{"name": "@oss/web-admin-v2", "version": "0.0.1", "description": "Line smart city", "productName": "LINE OSS", "author": "PNL", "private": true, "scripts": {"lint": "eslint --ext .js,.ts,.vue ./", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "format": "prettier .  --write"}, "dependencies": {"@overlapmedia/imagemapper": "^2.0.6", "@quasar/extras": "^1.16.4", "@vueuse/core": "^10.2.1", "amazon-cognito-identity-js": "^6.3.3", "aws-amplify": "^5.3.8", "axios": "^1.2.1", "chart.js": "^4.4.2", "dayjs": "^1.11.9", "decamelize": "^6.0.0", "deep-equal": "^2.2.3", "file-saver": "^2.0.5", "json-editor-vue": "^0.15.0", "lodash": "^4.17.21", "pinia": "^2.0.11", "quasar": "^2.6.0", "reconnecting-websocket": "^4.4.0", "uuidv4": "^6.2.13", "vue": "3.4.5", "vue-advanced-chat": "^2.1.0", "vue-chartjs": "^5.3.1", "vue-i18n": "next", "vue-pdf-embed": "^1.1.6", "vue-router": "^4.0.0", "vue3-google-map": "^0.21.0", "vue3-toastify": "^0.2.1"}, "devDependencies": {"@babel/preset-env": "^7.22.9", "@babel/preset-react": "^7.22.5", "@quasar/app-vite": "^1.3.0", "@storybook/addon-actions": "^7.1.1", "@storybook/addon-controls": "^7.1.1", "@storybook/addon-essentials": "^7.1.1", "@storybook/addon-interactions": "^7.1.1", "@storybook/addon-links": "^7.1.1", "@storybook/addon-mdx-gfm": "^7.1.1", "@storybook/addon-styling": "^1.3.5", "@storybook/blocks": "^7.1.1", "@storybook/testing-library": "^0.2.0", "@storybook/vue3": "^7.1.1", "@storybook/vue3-webpack5": "^7.1.1", "@types/deep-equal": "^1.0.4", "@types/file-saver": "^2.0.7", "@types/google.maps": "^3.58.1", "@types/node": "^12.20.21", "@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "@vue/compiler-sfc": "^3.3.4", "autoprefixer": "^10.4.2", "css-loader": "^6.8.1", "eslint": "^8.10.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.19.1", "eslint-plugin-n": "^15.0.0", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-storybook": "^0.6.13", "eslint-plugin-vue": "^9.0.0", "install-peers": "^1.0.4", "postcss": "^8.4.26", "prettier": "3.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "storybook": "^7.1.1", "tailwindcss": "^3.3.3", "typescript": "^5.1.6"}, "engines": {"node": "^18 || ^16 || ^14.19", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}