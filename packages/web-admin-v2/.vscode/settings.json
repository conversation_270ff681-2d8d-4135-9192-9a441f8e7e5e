{"editor.bracketPairColorization.enabled": true, "editor.guides.bracketPairs": true, "editor.formatOnSave": true, "editor.defaultFormatter": "dbaeumer.vscode-eslint", "editor.codeActionsOnSave": ["source.fixAll.eslint"], "eslint.validate": ["javascript", "javascriptreact", "typescript", "vue"], "typescript.tsdk": "node_modules/typescript/lib", "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "vue.codeActions.enabled": false}