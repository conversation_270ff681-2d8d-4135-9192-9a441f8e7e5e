# REQUIRED
# use /sst/oss/{stage}/Api/admin_api/url value from Parameter Store
VITE_AMPLIFY_ADMIN_API_ENDPOINT_URL=https://exw4kkiwkb.execute-api.ap-northeast-1.amazonaws.com

# REQUIRED
# {stage}-oss-admins_pool
VITE_AMPLIFY_AUTH_USER_POOL_WEB_CLIENT_ID=2lg68po1pevpthjkkbie7r7484

# REQUIRED
# {stage}-oss-admins_pool pool ID
VITE_AMPLIFY_AUTH_USER_POOL_ID=ap-northeast-1_jRk2bg3WZ

# OPTIONAL
VITE_AMPLIFY_SCENARIO_API_ENDPOINT_URL=''
VITE_AMPLIFY_BOSAI_API_ENDPOINT_URL=''
VITE_CQ_API_ENDPOINT_URL=''
VITE_SCENARIO_CLOUDFRONT_DOMAIN_NAME=''
VITE_DISTRIBUTION_RESOURCES_CLOUDFRONT_DOMAIN_NAME=''
VITE_USE_PAYMENT=0

VITE_GOOGLE_MAP_API_KEY=
VITE_I18N_ENABLE= # true to show I18n locale change button

VITE_OLD_ADMIN_URL=https://d34ytf9ha4ixq3.cloudfront.net

# endpoint for damage report feature
VITE_DAMAGE_REPORT_API_ENDPOINT_URL=https://api-damage.hts.id.vn
VITE_DAMAGE_REPORT_CHAT_WS_ENDPOINT=wss://ws-damage.hts.id.vn