import { defineStore } from "pinia"
import { computed, ref } from "vue"
import { GetAllScenarioMessagesIncludeTalk, GetAllScenarios, GetScenarioDataByDataType } from "../../../services/scenarios.service"
import { cloneDeep, every, isEmpty, get as getObj } from "lodash";
import { CreateDamageReports, DeleteDamageReportsByID, ExportReportToCSV, GetAllDamageReports, GetDamageReportsByID, GetSignedImagesInfo, GetTalkRelationship, QueryFilterDamageReports, UpdateDamageReportsByID, UploadImagesS3 } from "../../../services/damage-report.service"
import dayjs from "dayjs";
import { useI18n } from "vue-i18n";
import { useNotify } from "../../../composables/useNotify"
import { useThrottleFn } from "@vueuse/core";

export const useDamageReportStore = defineStore('damage-report', () => {

  const { t } = useI18n()
  const { alertError } = useNotify()
  // #region State
  const loadings = ref<any>({})
  const filterFormData = ref<any>({})
  const isShowFilterForm = ref(false)
  const lstScenarioOptions = ref<any[]>([])

  // table data
  const tableReportData = ref<any[]>([])
  const totalReportDataCount = ref(0)
  const lastEvaluatedKey = ref()
  const curFilterCondition = ref<any>(null)
  const isClearCacheReportData = ref(false)

  // Form options
  // TODO generate from scenario
  const scenarioOptions = ref<any[]>([])
  const currentActiveScenarioVersion = ref<any>()
  const statusOptions = computed(() => [{
    value: 'not-processed',
    label: t('status.not-processed'),
  }, {
    value: 'cancel',
    label: t('status.cancel'),
  }, {
    value: 'processing',
    label: t('status.processing'),
  }, {
    value: 'finished',
    label: t('status.finished'),
  }])
  const categoryOptionsDefault = computed(() => [
    {
      value: '河川',
      label: t('category.河川')
    },
    {
      value: '公園',
      label: t('category.公園')
    },
  ]
  )
  const subjectOptionsDictDefault = computed(() => ({
    河川: [{
      value: '河川',
      label: t('subject.河川.河川')
    }, {
      // "Others"
      value: 'その他',
      label: t('subject.河川.その他')
    },],
    公園: [{
      // "Playground equipments"
      value: '遊具',
      label: t('subject.公園.遊具')
    },
    {
      // "Lightings"
      value: '照明灯',
      label: t('subject.公園.照明灯')
    },
    {
      // "Resting facilities"
      value: '休憩施設',
      label: t('subject.公園.休憩施設')
    },
    {
      // "Water facilities"
      value: '水回り',
      label: t('subject.公園.水回り')
    },
    {
      // "Trees"
      value: '樹木',
      label: t('subject.公園.樹木')
    },
    {
      // "Others"
      value: 'その他',
      label: t('subject.公園.その他')
    },
    ],
  }))
  const feedbackOptionsDictDefault = computed(() => ({
    河川_河川: [
      {
        // 'Damaged',
        value: "破損",
        label: t('feedback.破損')
      },
      {
        // 'Trees and grass are overgrown',
        value: "木や草が茂っている",
        label: t('feedback.木や草が茂っている')
      },
      {
        // 'Oil/foam floating',
        value: "油・泡が浮いている",
        label: t('feedback.油・泡が浮いている')
      },
      {
        // 'Oil/foam floating',
        value: "その他",
        label: t('feedback.その他')
      },
    ],
    // 'Playground equipment': ['Damaged', 'Other'],
    公園_遊具: [{
      value: "破損",
      label: t('feedback.破損'),
    }, {
      value: "その他",
      label: t('feedback.その他')
    }],
    // Lighting: ['Not lit', 'Damaged', 'Other'],
    公園_照明灯: [{
      value: "不点灯",
      label: t('feedback.不点灯'),
    }, {
      value: "破損",
      label: t('feedback.破損'),
    }, {
      value: "その他",
      label: t('feedback.その他')
    }],
    // 'Benches, etc. (resting facilities)': ['Damaged', 'Other'],
    公園_休憩施設: [{
      value: "破損",
      label: t('feedback.破損'),
    }, {
      value: "その他",
      label: t('feedback.その他')
    }],
    /* 'Water facilities (waterworks, toilets, etc.)': [
      'Water leaks',
      'Water does not come out/flow',
      'Other',
      'Damage',
    ], */
    公園_水回り: [{
      value: "水漏れ",
      label: t('feedback.水漏れ'),
    }, {
      value: "水が出ない・流れない",
      label: t('feedback.水が出ない・流れない'),
    }, {
      value: "破損",
      label: t('feedback.破損'),
    }, {
      value: "その他",
      label: t('feedback.その他')
    }],
    // Trees: ['Damaged', 'Pest infestation', 'Other'],
    公園_樹木: [{
      value: "破損",
      label: t('feedback.破損'),
    }, {
      value: "害虫発生",
      label: t('feedback.害虫発生'),
    }, {
      value: "その他",
      label: t('feedback.その他')
    }],
  }))

  const categoryOptions = ref<any[]>([])
  const subjectOptionsDict = ref<any>({})
  const feedbackOptionsDict = ref<any>({})
  // #endregion

  // #region Actions
  // scenario
  const buildOptionsFromScenario = useThrottleFn(async () => {
    loadings.value.scenarioOptionsLoading = true
    if (scenarioOptions.value.length === 0) {
      const listScenario: any[] = []
      const scenarioData = await GetAllScenarios()
      const settingScenario = scenarioData?.items?.find(x => x.scenarioId === 'settings')

      if (settingScenario?.activeScenarioId) {
        const activeScenario = scenarioData?.items?.find(x => x.scenarioId === settingScenario?.activeScenarioId)
        const productionScenarioVersions = settingScenario?.envMapping?.production;
        currentActiveScenarioVersion.value = {
          scenarioId: settingScenario?.activeScenarioId,
          versionId: productionScenarioVersions
        }
        if (activeScenario) {
          for (const scenarioId in activeScenario.versions) {
            let el = activeScenario.versions[scenarioId]
            if (el?.specialTalks?.damageReport === true) {
              listScenario.push({
                value: `${el?.model?.meta?.name}#${scenarioId}`,
                label: el?.displayVersionName,
                production: scenarioId === productionScenarioVersions,
              })
            }
          }
        }
      }
      scenarioOptions.value = listScenario
    }
    loadings.value.scenarioOptionsLoading = false
  }, 500)

  async function buildCollectionData(scenario, talkDrVersionId?) {
    let talkRelation: any
    if (talkDrVersionId) {
      talkRelation = await GetTalkRelationship({
        scenario: scenario,
        talkDrVersionId: talkDrVersionId
      })
      // console.warn("exist", talkRelation);
    }
    else {
      // case admin create new => get active talk from active scenario
      console.warn(currentActiveScenarioVersion.value);
      const talkMsgList = await GetScenarioDataByDataType(currentActiveScenarioVersion.value.scenarioId, currentActiveScenarioVersion.value.versionId, "talk")
      const activeTalkVersionId = talkMsgList?.items?.find(x => x.dataId === "DAMAGE_REPORT_TALK")?.versionActive
      if (activeTalkVersionId) {
        talkRelation = await GetTalkRelationship({
          scenario: `${currentActiveScenarioVersion.value.scenarioId}#${currentActiveScenarioVersion.value.versionId}`,
          talkDrVersionId: activeTalkVersionId
        })
        if (talkRelation) {
          categoryOptions.value = Object.keys(talkRelation).map(key => {
            let categoryValue = talkRelation[key].text
            if (talkRelation[key]?.subjects) {
              subjectOptionsDict.value[categoryValue] = talkRelation[key].subjects.map(subj => {
                let subjectValue = subj[Object.keys(subj)[0]]?.text
                if (subj[Object.keys(subj)[0]]?.feedbacks) {
                  feedbackOptionsDict.value[`${categoryValue}_${subjectValue}`] = subj[Object.keys(subj)[0]]?.feedbacks?.map(feedback => {
                    let feedbackValue = feedback[Object.keys(feedback)[0]]?.text
                    return {
                      value: feedbackValue,
                      label: feedbackValue,
                    }
                  }).sort((a, b) => {
                    if (a.value < b.value) return -1;
                    if (a.value > b.value) return 1;
                    return 0;
                  })
                }
                return {
                  value: subjectValue,
                  label: subjectValue,
                }
              }).sort((a, b) => {
                if (a.value < b.value) return -1;
                if (a.value > b.value) return 1;
                return 0;
              })
            }
            return {
              value: categoryValue,
              label: categoryValue,
            }
          }).sort((a, b) => {
            if (a.value < b.value) return -1;
            if (a.value > b.value) return 1;
            return 0;
          })
          // console.warn({
          //   categoryOptions,
          //   subjectOptionsDict,
          //   feedbackOptionsDict,
          // });
        }
      }
      else {
        // Not found active talk version => fall back to default
        categoryOptions.value = categoryOptionsDefault.value
        subjectOptionsDict.value = subjectOptionsDictDefault.value
        feedbackOptionsDict.value = feedbackOptionsDictDefault.value
      }
    }
  }
  // crud report
  async function getNextDamageReports(startRow = 0, count = 0) {
    // console.log('getNextDamageReports', { startRow, count, lastKey: lastEvaluatedKey.value });
    loadings.value.queryDamageReport = true
    try {
      let res: any;
      if (curFilterCondition.value !== null) {
        // apply filter
        res = await QueryFilterDamageReports({
          ...curFilterCondition.value,
          lastEvaluatedKey: lastEvaluatedKey.value,
        })
      }
      else {
        res = await GetAllDamageReports({
          lastEvaluatedKey: lastEvaluatedKey.value,
        })
      }

      if (!res?.data) {
        return
      }

      if (!lastEvaluatedKey.value) {
        // first page query
        tableReportData.value = res.data
      }
      else {
        tableReportData.value.push(...res.data)
      }
      totalReportDataCount.value = res.total
      lastEvaluatedKey.value = res.lastEvaluatedKey

      // case 1 request not get all/enough data => loop to get more
      if (count === 0) {
        while (lastEvaluatedKey.value && (startRow + count > tableReportData.value.length)) {
          if (curFilterCondition.value !== null) {
            // apply filter
            res = await QueryFilterDamageReports({
              ...curFilterCondition.value,
              lastEvaluatedKey: lastEvaluatedKey.value,
            })
          }
          else {
            res = await GetAllDamageReports({
              lastEvaluatedKey: lastEvaluatedKey.value,
            })
          }
          if (!res?.data) {
            break
          }
          tableReportData.value.push(...res.data)
          totalReportDataCount.value = res.total
          lastEvaluatedKey.value = res.lastEvaluatedKey
        }
      }
    } catch (error) {
      if (error?.response?.data?.message) {
        alertError(error?.response?.data?.message)
      }
      else {
        alertError(t(`errors.${error.code}`))
      }
    }
    loadings.value.queryDamageReport = false
  }
  async function triggerFilter() {
    loadings.value.queryDamageReport = true
    const filter = cloneDeep(filterFormData.value)
    if (filter.modifiedAtFrom) {
      filter.fromDate = dayjs(filter.modifiedAtFrom, 'YYYY/MM/DD').startOf('day').toISOString()
      delete filter.modifiedAtFrom
    }
    if (filter.modifiedAtTo) {
      filter.toDate = (dayjs(filter.modifiedAtTo, 'YYYY/MM/DD').endOf('day')).toISOString()
      delete filter.modifiedAtTo
    }

    // if no filter condition
    if (every(filter, isEmpty)) {
      curFilterCondition.value = null
    }
    else {
      curFilterCondition.value = filter
    }

    // reset table data (to get new filtered data)
    lastEvaluatedKey.value = null
    tableReportData.value = []

    loadings.value.queryDamageReport = false
    return
  }

  async function createDamageReport(reportData) {
    loadings.value.createDamageReport = true
    const result = await CreateDamageReports(reportData).catch(handleError)
    loadings.value.createDamageReport = false
    return result
  }
  async function getDamageReportsByID(id: string) {
    loadings.value.crudDamageReport = true
    const result = await GetDamageReportsByID(id).catch(handleError)
    loadings.value.crudDamageReport = false
    return result
  }
  async function deleteDamageReportsByID(id: string) {
    loadings.value.crudDamageReport = true
    const result = await DeleteDamageReportsByID(id).catch(handleError)
    loadings.value.crudDamageReport = false
    return result
  }
  async function updateDamageReportsByID(id: string, reportData) {
    loadings.value.crudDamageReport = true
    const result = await UpdateDamageReportsByID(id, reportData).catch(handleError)
    loadings.value.crudDamageReport = false
    return result
  }

  async function uploadImagesS3(files: HTMLInputElement["files"]) {
    loadings.value.uploadImagesS3 = true
    const result = await UploadImagesS3(files).catch(handleError)
    loadings.value.uploadImagesS3 = false
    return result
  }
  async function getSignedImagesInfo(payload) {
    let result = await GetSignedImagesInfo(payload).catch(handleError)
    return result
  }
  // #endregion

  async function exportReportToCSV() {
    loadings.value.exportCSV = true
    const filter = cloneDeep(filterFormData.value)
    if (filter.modifiedAtFrom) {
      filter.fromDate = dayjs(filter.modifiedAtFrom, 'YYYY/MM/DD').toISOString()
      delete filter.modifiedAtFrom
    }
    if (filter.modifiedAtTo) {
      filter.toDate = (dayjs(filter.modifiedAtTo, 'YYYY/MM/DD').add(1, 'day')).toISOString()
      delete filter.modifiedAtTo
    }
    let result = await ExportReportToCSV(filter).catch(handleError)
    loadings.value.exportCSV = false
    return result
  }


  function handleError(error) {
    if (error?.response?.data?.message) {
      alertError(error?.response?.data?.message)
    }
    else {
      alertError(t(`errors.${error.code}`))
    }
  }

  return {
    // state
    loadings,

    isShowFilterForm,
    filterFormData,
    curFilterCondition,
    lstScenarioOptions,

    tableReportData,
    totalReportDataCount,
    lastEvaluatedKey,
    isClearCacheReportData,


    scenarioOptions,
    statusOptions,
    categoryOptions,
    subjectOptionsDict,
    feedbackOptionsDict,

    // getters

    // actions
    getNextDamageReports,
    triggerFilter,
    createDamageReport,
    getDamageReportsByID,
    updateDamageReportsByID,
    deleteDamageReportsByID,

    uploadImagesS3,
    getSignedImagesInfo,
    buildOptionsFromScenario,
    buildCollectionData,
    exportReportToCSV,
  }
})
