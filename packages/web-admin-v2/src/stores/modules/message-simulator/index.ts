import { Message, ActionItem, Action } from './../../../types/index'
import { defineStore } from 'pinia'
import { MessageTypes } from './message-simulator.constants'
import emojis from 'src/assets/emojis.json'
import { extend, LocalStorage } from 'quasar'
import { API } from 'aws-amplify'
import { PlatformAdminApi } from '../../../services/amplify.service'
import { generateUUID } from '../../../utils/uuidUtils'

export const useMessageSimulatorStore = defineStore({
  id: 'messageSimulatorStore',

  state: () => ({
    loadings: {},
    errors: {},
    messageTypes: MessageTypes,
    messageTypeSelected: 'text' as
      | 'text'
      | 'image'
      | 'imagemap'
      | 'flex',
    message: {
      id: '',
      contents: {
        type: 'text',
        text: '',
        originalContentUrl: '',
        previewImageUrl: '',
      },
    } as Message,
    imageMapActions: [] as Array<ActionItem>,
    imgSrc: '',
    imgType: '',
    imgUploadUrl: '',
    emojis,
    selectedEmojis: (LocalStorage.getItem('selectedEmojis') || []) as string[],
    imageMapsEditor: null,
    actions: [] as Array<Action>,
    selectMode: true,
    imageMapImageHeight: 0,
    imageMapImageWidthBase: 1040,
  }),

  getters: {
    messageJsonObject: (state) => {
      try {
        return state.message?.contents?.contents
      } catch (e) {
        return {}
      }
    },

    getMessageTypeObject: (state) => (type: string) => {
      return state.messageTypes.find((messageType) => messageType.id === type)
    },
    selectedAction: (state) => {
      return state.actions.find((action) => action.selected)
    },
    isCanAddAction: (state) => {
      return state.actions.length < 20
    },
  },

  actions: {
    resetMessage() {
      this.message = {
        id: '',
        contents: {
          type: 'text',
          text: '',
        },
      }
      this.imageMapsEditor = null
      this.selectMode = true
      this.actions = []
    },
    async getUploadUrl() {
      try {
        const key = generateUUID()
        const resp = await API.get(
          PlatformAdminApi,
          `/distribution/configs/resources/upload?key=${key}&contentType=${this.imgType}`,
          {}
        )
        if (resp && resp.urlLink) this.imgUploadUrl = resp.urlLink
        if (resp && resp.downloadUrlLink) {
          this.message.contents.originalContentUrl = resp.downloadUrlLink
          this.message.contents.previewImageUrl = resp.downloadUrlLink
        }
      } catch (e) {
        console.log('Failed to get upload url: ', e)
      }
    },
    async uploadImageToServer() {
      const resp = await fetch(this.imgUploadUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': this.imgType,
        },
        body: this.imgSrc,
      })
    },
    selectEmoji(emoji: string) {
      this.selectedEmojis.push(emoji)
      LocalStorage.set('selectedEmojis', this.selectedEmojis)
    },
    newImageMapAction() {
      const newAction: ActionItem = {
        actionType: 'メッセージアクション',
        text: '',
        x: 0,
        y: 0,
        width: 0,
        height: 0,
      }
      this.imageMapActions.push(newAction)
    },
    updateImageMapAction(newState: ActionItem, i: number) {
      this.imageMapActions[i] = { ...this.imageMapActions[i], ...newState }
    },
    deleteImageMapAction(i: number) {
      this.imageMapActions = this.imageMapActions.filter(
        (mapAction: ActionItem, index: number) => {
          return i !== index
        }
      )
    },
    resetMessageOnCancel() {
      this.messageTypeSelected = 'text' as
        | 'text'
        | 'image'
        | 'imagemap'
        | 'flex'
      this.resetMessage()
      this.imageMapActions = [] as Array<ActionItem>
      this.imgSrc = ''
      this.imgType = ''
      this.imgUploadUrl = ''
      this.imageMapActions = [] as Array<ActionItem>
    },

    updateActionArea(id: string, area: any) {
      this.actions = this.actions.map((action) => {
        if (action.id === id) {
          return extend(true, action, { area })
        }
        return action
      })
    },

    removeAction(id: string) {
      this.actions = this.actions.filter((action) => action.id !== id)
      this.imageMapsEditor?.removeComponent(id)
    },

    setActionSelected(id: string) {
      this.actions = this.actions.map((action) => {
        if (action.id === id) {
          return extend(true, action, { selected: true })
        }
        return extend(true, action, { selected: false })
      })
    },

    initActions(actions: Array<Action>) {
      this.actions = actions
      this.actions.forEach((action) => {
        console.log('🚀 ~ this.actions.forEach ~ action:', action)
        this.imageMapsEditor?.createRectangle(action?.area, action?.id)
      })
    },
    confirmMessage(type: string) {
      if (type === 'text' && !this.message.contents.text) {
        return false
      }
      if (type === 'image' && !this.message.contents.file) {
        return false
      }
      if (type === 'imagemap') {
        const actions = this.message.contents.actions
        let valid = true
        if (!this.message.contents.file) {
          return false
        }
        if (!actions || actions.length === 0) {
          valid = false
        } else {
          for (const action of actions) {
            if (action.type === 'message' && !action.text) {
              valid = false
              break
            }
            if (action.type === 'uri') {
              if (!action.linkUri) {
                valid = false
                break
              }
              const urlRegex = /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/
              const regex = new RegExp(urlRegex)
              if (action.linkUri.match(regex) === null) {
                valid = false
                break
              }
            }
          }
        }
        if (!valid) {
          return false
        }
      }
      if (type === 'flex') {
        if (!this.message.contents.contents) {
          return false
        }
        if (this.message.contents.isJson === false) {
          return false
        }
      }
      return true
    }
  },
})
