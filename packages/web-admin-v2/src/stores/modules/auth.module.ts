import {
  SET_IS_SIGNING_IN,
  SET_IS_SIGNING_OUT,
  SET_IS_REFRESHING_USER,
  SET_COGNITO_USER,
  SET_USER,
  RESET_USER,
  SET_SIGNIN_ERROR,
  SET_SIGNOUT_ERROR,
  SET_REFRESH_USER_ERROR,
  SET_FORGOT_PASSWORD_ERROR,
  SET_FORGOT_PASSWORD_SUBMIT_ERROR,
  SET_IS_ACCOUNT_UNUSABLE,
  SET_IS_USER_ABILITIES_DEFINED,
  SET_PERMISSIONS_FROM_TEAM,
} from '../mutation-types'
import { defineStore } from 'pinia'
import {
  AUTH_SIGNIN,
  FETCH_COMMON_SETTINGS,
  AUTH_REFRESH_USER,
} from '../action-types'
import {
  setAmplifyUser,
  AmplifyService,
  getCognitoUser,
  refreshUserInfo,
} from 'src/services/amplify.service'
import { useSettingsStore } from 'src/stores/modules/settings'
import { TABS_AND_SUBPAGES_RELATIONS } from 'src/constants/permission.constants'
export type InitUserPayload = {
  username: string
  accessToken: string
  idToken: string
  refreshToken: string
}

export const useAuthStore = defineStore({
  id: 'authStore',

  state: () => ({
    status: '',
    token: '',
    isSigningIn: true,
    isSigningOut: false,
    isRefreshingUser: false,
    cognitoUser: null,
    user: null,
    signinError: null,
    signoutError: null,
    refreshUserError: null,
    forgotPasswordError: null,
    forgotPasswordSubmitError: null,
    isAccountUnusable: false,
    isUserAbilitiesDefined: false,
    permissionsFromTeam: {},
  }),

  getters: {},

  actions: {
    async [AUTH_SIGNIN](payload: InitUserPayload) {
      this[SET_IS_REFRESHING_USER](true)
      try {
        const settingsStore = useSettingsStore()

        await setAmplifyUser(payload)
        AmplifyService.configure()

        this[SET_COGNITO_USER](await getCognitoUser())
        this[SET_USER](await refreshUserInfo())
        await settingsStore[FETCH_COMMON_SETTINGS]()
      } catch (error) {
      } finally {
        this[SET_IS_REFRESHING_USER](false)
      }
    },

    async [AUTH_REFRESH_USER]() {
      this[SET_IS_REFRESHING_USER](true)
      this[SET_REFRESH_USER_ERROR](null)
      try {
        const settingsStore = useSettingsStore()
        AmplifyService.configure()
        this[SET_COGNITO_USER](await getCognitoUser())
        this[SET_USER](await refreshUserInfo())
        await settingsStore[FETCH_COMMON_SETTINGS]()
        // Promise.all([
        //   await dispatch(AUTH_DEFINE_USER_ABILITY),
        //   await dispatch(FETCH_PERMISSIONS_FROM_TEAM),
        //   await dispatch(AUTH_GET_ATTR),
        // ])
      } catch (error: any) {
        this[SET_REFRESH_USER_ERROR](error)
      } finally {
        this[SET_IS_REFRESHING_USER](false)
      }
    },

    [SET_IS_SIGNING_IN](value) {
      this.isSigningIn = value
    },
    [SET_IS_SIGNING_OUT](value) {
      this.isSigningOut = value
    },
    [SET_IS_REFRESHING_USER](value) {
      this.isRefreshingUser = value
    },
    [SET_COGNITO_USER](value) {
      this.cognitoUser = value
    },
    [SET_USER](value) {
      this.user = value
    },

    [SET_SIGNIN_ERROR](value) {
      this.signinError = value
    },
    [SET_SIGNOUT_ERROR](value) {
      this.signoutError = value
    },
    [SET_REFRESH_USER_ERROR](value) {
      this.refreshUserError = value
    },
    [SET_FORGOT_PASSWORD_ERROR](value) {
      this.forgotPasswordError = value
    },
    [SET_FORGOT_PASSWORD_SUBMIT_ERROR](value) {
      this.forgotPasswordSubmitError = value
    },
    [SET_IS_ACCOUNT_UNUSABLE](value) {
      this.isAccountUnusable = value
    },
    [RESET_USER](state) {
      this.status = ''
      this.token = ''
      this.isSigningIn = false
      this.user = null
      this.isUserAbilitiesDefined = false
    },
    [SET_IS_USER_ABILITIES_DEFINED](value) {
      this.isUserAbilitiesDefined = value
    },
    [SET_PERMISSIONS_FROM_TEAM](value) {
      const tabsAndSubpagesPermissions = []
      for (const tab of Object.keys(TABS_AND_SUBPAGES_RELATIONS)) {
        for (const permission of value.permissions) {
          if (permission.indexOf(tab) >= 0) {
            tabsAndSubpagesPermissions.push(tab)
            for (const subpage of TABS_AND_SUBPAGES_RELATIONS[tab]) {
              tabsAndSubpagesPermissions.push(subpage)
            }
          }
        }
      }
      value.permissions = tabsAndSubpagesPermissions
      this.permissionsFromTeam = value
    },
  },
})
