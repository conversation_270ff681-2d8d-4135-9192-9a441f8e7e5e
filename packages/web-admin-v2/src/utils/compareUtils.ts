/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import moment from 'moment'

export const compareString = (data: any, keyword: any): any => {
  return data.toString().trim() === keyword.toString().trim()
}

export const includesKeyword = (data: any, keyword: any): boolean => {
  if (keyword) {
    return data.toString().trim().includes(keyword)
  }
  return true
}
// Greater than or equal with date
export const gteWithDate = (data: any, compareWithDate: any): boolean => {
  if (compareWithDate) {
    return data >= moment(compareWithDate).startOf('day').unix()
  }
  return true
}

// Less than or equal with date
export const lteWithDate = (data: any, compareWithDate: any): boolean => {
  if (compareWithDate) {
    return data <= moment(compareWithDate).endOf('day').unix()
  }
  return true
}
