import {
  DeliverySettings,
  CreateDelivery,
  DeliveryObj,
  DeliveryCustomNumberedDayOfWeek,
  DeliveryCustomNumberedDayOfWeekFrontend
} from '../types/index'

/**
 * Check if the CreateDelivery Setting is Biweekly or not
 * @param settings CreateDelivery Setting
 * @returns True if Biweekly, otherwise False.
 */
const checkIfBiweekly = (settings: DeliverySettings): boolean => {
  return settings.distributionType === 'repeating' &&
    settings.repeatingSettings.custom &&
    settings.repeatingSettings.custom.type !== undefined &&
    settings.repeatingSettings.custom.type === 'skip' &&
    settings.repeatingSettings.custom.dates !== undefined &&
    settings.repeatingSettings.custom.dates.length === 0
}

/**
 * Transform Custom NumberedDayOfWeek from API Schema to Frontend Schema
 * In Frontend, dayOfWeek is array.
 * @param numberedDayOfWeek NumberedDayOfWeek from API Schema
 * @returns NumberedDayOfWeek used in Frontend Schema
 */
const transformNumberedDayOfWeek = (numberedDayOfWeek: DeliveryCustomNumberedDayOfWeek[]): DeliveryCustomNumberedDayOfWeekFrontend[] => {
  const transformedList: DeliveryCustomNumberedDayOfWeekFrontend[] = [
    {
      number: 1,
      dayOfWeek: []
    },
    {
      number: 2,
      dayOfWeek: []
    },
    {
      number: 3,
      dayOfWeek: []
    },
    {
      number: 4,
      dayOfWeek: []
    },
    {
      number: 5,
      dayOfWeek: []
    },
  ]
  for (const data of numberedDayOfWeek) {
    const transformedData = transformedList.find(x => x.number === data.number)
    if (transformedData) {
      transformedData.dayOfWeek.push(data.dayOfWeek)
    } else {
      transformedList.push({
        number: data.number,
        dayOfWeek: [data.dayOfWeek]
      })
    }
  }

  // This can be commented out once settled.
  console.log('Converted NumberedDayOfWeek', { original: numberedDayOfWeek, transform: transformedList })
  return transformedList
}

/**
 * Final Format CreateDelivery Object according to the detail
 * @param createDeliveryState CreateDelivery Object before request submission
 * @returns Formatted CreateDelivery Object
 */
export const formatDeliveryObj = (createDeliveryState: CreateDelivery): CreateDelivery => {
  const createDelivery: CreateDelivery = { ...createDeliveryState }

  // Format If Biweekly
  if (checkIfBiweekly(createDelivery.settings)) {
    // Remove dates as validation error
    delete createDelivery.settings.repeatingSettings.custom.dates
  }

  return createDelivery
}

/**
 * Convert Segment Config Schema from API to Frontend Schema
 * @param resp API Delivery Response
 * @returns Frontend Used Delivery Response
 */
export const formatResponseToDeliveryObj = (resp: any): DeliveryObj => {
  const messages = 'messages' in resp ? resp.messages : []
  const settings = 'settings' in resp ? resp.settings : {}

  // Check if Custom Numbered Day of Week and with correct format (OSS-59)
  if ('repeatingSettings' in settings &&
    'period' in settings.repeatingSettings &&
    settings.repeatingSettings.period === 'Custom' &&
    'custom' in settings.repeatingSettings &&
    'numberedDayOfWeek' in settings.repeatingSettings.custom &&
    Array.isArray(settings.repeatingSettings.custom.numberedDayOfWeek)) {
    settings.repeatingSettings.custom.numberedDayOfWeek = transformNumberedDayOfWeek(settings.repeatingSettings.custom.numberedDayOfWeek)
  }

  return {
    messages,
    settings
  }
}
