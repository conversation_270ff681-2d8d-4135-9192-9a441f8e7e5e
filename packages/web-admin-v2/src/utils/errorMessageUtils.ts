/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

export const MULTIPLE_REQUEST_MESSAGE =
  '複数のリクエストが送信されました。予約が正しく行われているかご確認ください。'

export const englishToJapaneseErrorConverter = (
  error: any,
  defaultErrorMessage: string = null
): string => {
  if (!error) {
    return '不明なエラーが発生しました。管理者にお問い合わせ下さい。'
  } else if (error.message && error.message.includes('Network Error')) {
    return 'ネットワーク接続に問題があります。インターネット接続状態を確認のうえ、もう一度アクセスしてください。エラーが継続する場合は、管理者にお問い合わせ下さい。'
  } else if (error.response && error.response.status === 504) {
    return 'タイムアウトしました。もう一度件数の取得を行なってください。エラーが継続する場合は、管理者にお問い合わせ下さい。'
  }

  let errorMessage = defaultErrorMessage || '不明なエラーが発生しました。管理者にお問い合わせ下さい。'
  if (error.code) {
    errorMessage += `\nエラーコード: ${error.code}`
  }
  if (
    (error.response && error.response.data && error.response.data.message) ||
    error.message
  ) {
    errorMessage += `\nエラーメッセージ: ${
      error.response && error.response.data && error.response.data.message
        ? error.response.data.message
        : error.message
    }`
  }

  return errorMessage
}
