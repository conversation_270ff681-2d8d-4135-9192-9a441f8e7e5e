/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { API } from 'aws-amplify'

export const loadAllPages = async (
  method: string,
  apiName: string,
  path: string,
  response: { lastEvaluatedKey: any },
  payload: {
    [x: string]: any
    fromDate?: any
    toDate?: any
    dataType?: any
    logType?: any
    surveyId?: any
  }
): Promise<any> => {
  if (method === 'GET') {
    payload.lastEvaluatedKey = response.lastEvaluatedKey
      ? JSON.stringify(response.lastEvaluatedKey)
      : ''
    return await getRequestLoad(apiName, path, payload)
  } else if (method === 'POST') {
    payload.lastEvaluatedKey = response.lastEvaluatedKey
      ? response.lastEvaluatedKey
      : {}
    return await postRequestLoad(apiName, path, payload)
  }
}

export const getRequestLoad = async (
  apiName: any,
  path: any,
  payload: any
): Promise<any> => {
  return await API.get(apiName, path, {
    queryStringParameters: payload,
  })
}

export const postRequestLoad = async (
  apiName: any,
  path: any,
  payload: any
): Promise<any> => {
  return await API.post(apiName, path, {
    body: payload,
  })
}
