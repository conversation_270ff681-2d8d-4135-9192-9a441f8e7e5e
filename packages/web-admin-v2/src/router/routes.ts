import { RouteRecordRaw } from 'vue-router'
import { PAGES } from '../constants/pages.ts'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: PAGES.HOME,
    component: () => import('src/pages/HomePage.vue'),
  },
  {
    path: '/segment-delivery',
    name: PAGES.SEGMENT_DELIVERY,
    component: () => import('src/pages/admin/SegmentDelivery/index.vue'),
    children: [
      {
        path: '',
        name: PAGES.SEGMENT_DELIVERY_HOME,
        component: () =>
          import('src/pages/admin/SegmentDelivery/SegmentDeliveryPage.vue'),
        children: [
          {
            path: '',
            name: PAGES.SEGMENT_DELIVERY_HISTORY,
            component: () =>
              import(
                'src/pages/admin/SegmentDelivery/SegmentDeliveryHistoryPage.vue'
              ),
          },
          {
            path: 'manual-delivery',
            name: PAGES.SEGMENT_DELIVERY_MANUAL_DELIVERY,
            component: () =>
              import('src/pages/admin/SegmentDelivery/ManualDeliveryPage.vue'),
          },
          {
            path: 'external-configs',
            name: PAGES.SEGMENT_DELIVERY_EXTERNAL_CONFIGS,
            component: () =>
              import(
                'src/pages/admin/SegmentDelivery/ExternalDeliveryPage.vue'
              ),
            children: [
              {
                path: '',
                name: PAGES.SEGMENT_DELIVERY_EXTERNAL_CONFIGS_MAIL,
                component: () =>
                  import(
                    'src/pages/admin/SegmentDelivery/ExternalDeliveryMailPage.vue'
                  ),
              },
              {
                path: 'talk',
                name: PAGES.SEGMENT_DELIVERY_EXTERNAL_CONFIGS_TALK,
                component: () =>
                  import(
                    'src/pages/admin/SegmentDelivery/ExternalDeliveryTalkPage.vue'
                  ),
              },
            ],
          },
        ],
      },
      {
        path: ':id',
        name: PAGES.SEGMENT_DELIVERY_DETAILS,
        component: () =>
          import(
            'src/pages/admin/SegmentDelivery/SegmentDeliveryDetails.vue'
          ),
      },
      {
        path: 'create',
        name: PAGES.SEGMENT_DELIVERY_CREATION,
        component: () =>
          import(
            'src/pages/admin/SegmentDelivery/SegmentDeliveryCreationPage.vue'
          ),
      },
      {
        path: 'create/:id?/:editMode?',
        name: PAGES.SEGMENT_DELIVERY_CREATION,
        component: () =>
          import(
            'src/pages/admin/SegmentDelivery/SegmentDeliveryCreationPage.vue'
          ),
      },
      {
        path: 'condition/:id',
        name: PAGES.SEGMENT_DELIVERY_CONDITION,
        component: () =>
          import(
            'src/pages/admin/SegmentDelivery/SegmentDeliveryConditionPage.vue'
          ),
      },
      {
        path: 'remind',
        name: PAGES.SEGMENT_DELIVERY_REMIND,
        component: () =>
          import('src/pages/admin/SegmentDelivery/RemindDeliveryPage.vue'),
      },
    ],
  },

  {
    path: '/damage-report',
    name: PAGES.DAMAGE_REPORT,
    component: () => import('src/pages/admin/DamageReport/index.vue'),
    children: [
      {
        path: '',
        name: PAGES.DAMAGE_REPORT_PAGE,
        component: () => import('src/pages/admin/DamageReport/DamageReportListPage.vue')
      },
      {
        name: PAGES.DAMAGE_REPORT_DETAIL,
        path: ':id',
        component: () => import('src/pages/admin/DamageReport/DamageReportDetailPage.vue'),
      },
      {
        name: PAGES.DAMAGE_REPORT_MAPVIEW,
        path: 'map',
        component: () => import('src/pages/admin/DamageReport/DamageReportMapViewPage.vue'),
      }
    ]
  },
  // Always leave this as last one,
  {
    path: '/:catchAll(.*)*',
    component: () => import('src/pages/ErrorNotFound.vue'),
    meta: {
      layout: 'non-header-layout',
    },
  },
]

export default routes
