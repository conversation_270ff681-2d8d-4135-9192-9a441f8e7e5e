// app global css in SCSS form
@tailwind base;
@tailwind components;
@tailwind utilities;

body.body--dark {
  background: #000;
}

.second-menu-tabs {
  .q-tab__icon {
    width: 18px !important;
    height: 18px !important;
    font-size: 18px !important;
  }
}

.hidden-scrollbar {
  -ms-overflow-style: none; /* IE, Edge 対応 */
  scrollbar-width: none; /* Firefox 対応 */
}
.hidden-scrollbar::-webkit-scrollbar {
  /* Chrome, Safari 対応 */
  display: none;
}
