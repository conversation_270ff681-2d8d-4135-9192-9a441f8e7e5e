<template>
  <q-header class="bg-white text-black" height-hint="98">
    <q-toolbar class="bg-primary text-white shadow-4">
      <q-toolbar class="tw-max-w-screen-lsc tw-mx-auto q-py-xs">
        <q-btn
          v-for="menu in appMenus"
          :key="menu.to"
          stretch
          flat
          class="q-px-md"
          @click="onClickMenu(menu)"
        >
          <q-icon left size="18px" :name="menu.icon" />
          <div>{{ menu.title }}</div>
        </q-btn>
      </q-toolbar>
    </q-toolbar>
  </q-header>
</template>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { useSettingsStore } from 'src/stores/modules/settings'
import { useAuthStore } from 'src/stores/modules/auth.module'
import { useRouter } from 'vue-router'

const appMenus = [
  {
    to: 'ApplicantsPage',
    groupMenu: 'ApplicantsPage',
    icon: 'mdi-clipboard-list-outline',
    title: 'ホームに戻る',
    redirectToOld: true,
    redirectTo: '/',
  },
  {
    to: 'SegmentDeliveryPage',
    groupMenu: 'SegmentDeliveryPage',
    icon: 'mdi-send-clock-outline',
    title: '配信',
    redirectTo: '/segment-delivery',
  },
  {
    to: 'DamageReportPage',
    groupMenu: 'DamageReportPage',
    icon: 'mdi-alert-circle-outline',
    title: '損傷報告',
    redirectTo: '/damage-report',
  },
  // {
  //   to: 'FormCreatePage',
  //   groupMenu: 'FormCreatePage',
  //   icon: 'mdi-text-box-outline',
  //   title: '帳票作成',
  // },
  // {
  //   to: 'ScenarioSettingsPage',
  //   groupMenu: 'ScenarioSettingsPage',
  //   icon: 'mdi-graph-outline',
  //   title: 'シナリオ設定',
  // },
  // {
  //   to: 'CalendarPage',
  //   groupMenu: 'CalendarPage',
  //   icon: 'mdi-calendar-clock',
  //   title: 'カレンダー',
  // },
  // {
  //   to: 'BosaiSettingsPage',
  //   groupMenu: 'BosaiSettingsPage',
  //   icon: 'mdi-hard-hat',
  //   title: '防災',
  // },
  // {
  //   to: 'LogsPage',
  //   groupMenu: 'LogsPage',
  //   icon: 'mdi-semantic-web',
  //   title: '統計',
  // },
  // {
  //   to: 'LogsPage',
  //   groupMenu: 'LogsPage',
  //   icon: 'mdi-cart-outline',
  //   title: '決済',
  // },
  // {
  //   to: 'CommonSettings',
  //   groupMenu: 'CommonSettings',
  //   icon: 'mdi-cog-outline',
  //   title: 'システム設定',
  // },
  // {
  //   to: 'User',
  //   groupMenu: 'User',
  //   icon: 'mdi-account-cog-outline',
  //   title: 'ユーザ設定',
  // },
]
const authStore = useAuthStore()
const { user } = storeToRefs(authStore)

const settingsStore = useSettingsStore()
const { commonSettings } = storeToRefs(settingsStore)

const router = useRouter()

const onClickMenu = (menu: any) => {
  console.log('🚀 ~ file: TheHeader.vue:121 ~ onClickMenu ~ menu:', menu)
  if (menu.redirectToOld) {
    window.location.href = import.meta.env.VITE_OLD_ADMIN_URL + menu.redirectTo
  } else if (menu.redirectTo) {
    router.push(menu.redirectTo)
  }
}
</script>
