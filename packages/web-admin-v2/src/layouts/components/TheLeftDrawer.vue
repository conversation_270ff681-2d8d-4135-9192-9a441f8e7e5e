<template>
  <q-drawer
    v-model="leftDrawerOpen"
    :mini="leftDrawerMini"
    show-if-above
    bordered
    class="bg-white"
    :width="240"
  >
    <q-scroll-area class="fit">
      <div class="tw-flex tw-flex-col lg:tw-h-[calc(100vh-60px)] tw-h-screen">
        <div class="">
          <q-list>
            <q-item
              clickable
              v-ripple
              @click="leftDrawerMini = !leftDrawerMini"
            >
              <q-item-section></q-item-section>
              <q-item-section avatar>
                <q-icon
                  color="primary"
                  :name="leftDrawerMini ? 'arrow_forward' : 'arrow_back'"
                />
              </q-item-section>
            </q-item>
          </q-list>
        </div>
        <div
          class="tw-flex-1 tw-flex"
          :class="{ 'lg:tw-items-center': leftDrawerMini }"
        >
          <q-list padding class="tw-w-full">
            <q-item
              v-for="menuItem in menus"
              :key="menuItem.text"
              v-ripple
              clickable
              active-class="tw-bg-primary-100 text-orange-5 tw-font-semibold"
              :to="menuItem.to"
              exact
            >
              <q-item-section avatar>
                <q-icon
                  :size="leftDrawerMini ? '1.7rem' : ''"
                  :name="menuItem.icon"
                />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ menuItem.text }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
        <div class="tw-text-center tw-text-sm tw-text-gray-500">
          <span v-if="leftDrawerMini">PNL</span>
          <span v-else>PlayNext Lab @ 2023</span>
        </div>
      </div>
    </q-scroll-area>
  </q-drawer>
</template>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { useAppStore } from 'src/stores/app'
import { MenuItem } from 'src/types'
import { PAGES } from 'src/constants/pages'
import { useRouter } from 'vue-router'

const router = useRouter()
const appStore = useAppStore()
const { leftDrawerOpen, leftDrawerMini } = storeToRefs(appStore)

const menus: MenuItem[] = [{ icon: 'history', text: '変更履歴', to: '/' }]
</script>
