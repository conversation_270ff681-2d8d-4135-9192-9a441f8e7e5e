<template>
  <q-layout
    v-if="!isRefreshingUser && user"
    view="hhh lpR fff"
    class="bg-grey-1"
  >
    <TheHeader />
    <!-- <TheLeftDrawer /> -->
    <q-page-container class="tw-min-h-screen tw-flex tw-bg-gray-100">
      <router-view />
    </q-page-container>
    <q-footer
      class="tw-bg-gray-100 text-black text-center q-py-md text-weight-light"
    >
      © PlayNext Lab Corp. 2024 | Version 2
    </q-footer>
  </q-layout>
  <q-layout
    v-else-if="!user && !isRefreshingUser"
    view="hhh lpR fff"
    class="bg-grey-1"
  >
    <q-page-container class="tw-min-h-screen tw-flex tw-bg-gray-100">
      <q-spinner-cube class="tw-m-auto" size="100px" color="primary" />
    </q-page-container>
  </q-layout>
</template>

<script lang="ts" setup>
import { useQuasar, QSpinnerCube, setCssVar } from 'quasar'
import { AUTH_SIGNIN, AUTH_REFRESH_USER } from 'src/stores/action-types'
import { useRoute, useRouter } from 'vue-router'
import { onMounted, watch } from 'vue'
import { useAuthStore } from 'src/stores/modules/auth.module'
import { useSettingsStore } from 'src/stores/modules/settings'
import { storeToRefs } from 'pinia'
import TheHeader from './components/TheHeader.vue'
const authStore = useAuthStore()
const { isRefreshingUser, user } = storeToRefs(authStore)

const route = useRoute()
const router = useRouter()
const $q = useQuasar()

const init = async () => {
  $q.loading.show({
    spinnerColor: 'white',
    backgroundColor: 'black',
    message: '処理中ですしばらく、お待ちください。',
    messageColor: 'white',
  })
  const { query } = route
  const { username, idToken, refreshToken, accessToken, redirectUrl } = query
  if (username && idToken && refreshToken && accessToken) {
    await authStore[AUTH_SIGNIN]({
      username,
      idToken,
      refreshToken,
      accessToken,
      redirectUrl,
    })
    router.push({ path: redirectUrl })
  } else if (!user.value) {
    await authStore[AUTH_REFRESH_USER]()
  }
  $q.loading.hide()
}
onMounted(() => {
  init()
})

watch(
  () => route.query,
  (value) => {
    init()
  }
)

const settingsStore = useSettingsStore()
watch(
  () => settingsStore.commonSettings,
  (value) => {
    if (value) {
      const { themes } = value
      setCssVar('primary', themes?.primaryColor?.color)
      // update primary color of tailwind
      document.documentElement.style.setProperty(
        '--color-primary',
        themes?.primaryColor?.color
      )
    }
  }
)
</script>
