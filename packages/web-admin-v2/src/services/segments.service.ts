import { ListExternalConfigsPayload } from './../types/index'
/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { API } from 'aws-amplify'
import { PlatformAdminApi } from './amplify.service'

import { LoadHistoryPayload } from '../types/index'
import { formatResponseToDeliveryObj } from '../utils/deliveryUtils'

const HISTORY_ENDPOINT = '/distribution/history'
export const LoadHistory = async (payload: LoadHistoryPayload) => {
  try {
    return await API.get(
      PlatformAdminApi,
      HISTORY_ENDPOINT + `/${payload.fromDate}/${payload.toDate}`,
      {
        queryStringParameters: {
          continueToken: payload.continueToken,
        },
      }
    )
  } catch (err: any) {
    console.log('🚀 ~ file: segments.service.ts:356 ~ LoadHistory ~ err:', err)
    return false
  }
}

const CONFIGS_ENDPOINT = '/distribution/configs'
export const CreateSegmentDelivery = async (payload) => {
  try {
    return await API.post(PlatformAdminApi, CONFIGS_ENDPOINT, {
      body: payload,
    })
  } catch (err: any) {
    console.log(
      '🚀 ~ file: segments.service.ts:47 ~ CreateSegmentDelivery ~ err:',
      err
    )
    return false
  }
}

export const GetConfigByID = async (id: string) => {
  try {
    return formatResponseToDeliveryObj(await API.get(PlatformAdminApi, `${CONFIGS_ENDPOINT}/${id}`, {}))
  } catch (err: any) {
    console.log('🚀 ~ file: segments.service.ts:47 ~ CreateSegmentDelivery ~ err:', err)
    return false
  }
}

export const CheckSubjectCondition = async (payload) => {
  return await API.post(PlatformAdminApi, '/dist/segment-delivery/distributionconfig/extractionanalysis', {
    body: {
      extractionCondition: payload.subjectExtractionCondition,
      test: payload.subjectTest,
    },
  })
}

export const CheckBodyCondition = async (payload) => {
  return await API.post(PlatformAdminApi, '/dist/segment-delivery/distributionconfig/extractionanalysis', {
    body: {
      extractionCondition: payload.bodyExtractionCondition,
      test: payload.bodyTest,
    },
  })
}

const EXTERNAL_CONFIGS_ENDPOINT = '/distribution/configs/external'
export const LoadExternalConfigs = async (
  payload: ListExternalConfigsPayload
) => {
  try {
    return await API.get(PlatformAdminApi, EXTERNAL_CONFIGS_ENDPOINT, {
      queryStringParameters: payload,
    })
  } catch (err: any) {
    console.log(' LoadExternalConfigs ~ err:', err)
    return false
  }
}

const TALK_DISTRIBUTIONS_ENDPOINT = '/distribution/configs/talk'
export const LoadTalkConfigs = async (
  payload: any
) => {
  try {
    return await API.get(PlatformAdminApi, TALK_DISTRIBUTIONS_ENDPOINT, {})
  } catch (err: any) {
    console.log(' LoadExternalConfigs ~ err:', err)
    return false
  }
}
