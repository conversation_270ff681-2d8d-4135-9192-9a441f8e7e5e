/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { API } from 'aws-amplify'
import { BiApiName } from './amplify.service'

import { loadAllPages } from '@/utils/queryHelper'

export const GetFollowers = async (fromDate, toDate, dataType, response) => {
  const queryStringParameters = {
    fromDate,
    toDate,
    dataType,
  }

  return await loadAllPages(
    'GET',
    BiApiName,
    '/bi/lineAPI/getFriendsList',
    response,
    queryStringParameters
  )
}

export const GetDemographic = async () => {
  return API.get(BiApiName, '/bi/lineAPI/getDemoGraphic', {})
}

export const GetRichmenuActions = async (
  fromDate,
  toDate,
  dataType,
  response
) => {
  const queryStringParameters = {
    fromDate,
    toDate,
    dataType,
  }

  return await loadAllPages(
    'GET',
    BiApiName,
    '/bi/scenario/getRichmenuLogs',
    response,
    queryStringParameters
  )
}

export const GetSegmentActivations = async (
  fromDate,
  toDate,
  dataType,
  logType,
  response
) => {
  const queryStringParameters = {
    fromDate,
    toDate,
    logType,
    dataType,
  }

  return await loadAllPages(
    'GET',
    BiApiName,
    '/bi/segment/getSegmentLogs',
    response,
    queryStringParameters
  )
}

export const GetLiffActivations = async (
  fromDate,
  toDate,
  dataType,
  response
) => {
  const queryStringParameters = {
    fromDate,
    toDate,
    dataType,
  }

  return await loadAllPages(
    'GET',
    BiApiName,
    '/bi/survey/getSurveyLogs',
    response,
    queryStringParameters
  )
}

export const GetLiffDetail = async (
  fromDate,
  toDate,
  surveyId,
  dataType,
  response
) => {
  const queryStringParameters = {
    fromDate,
    toDate,
    surveyId,
    dataType,
  }

  return await loadAllPages(
    'GET',
    BiApiName,
    '/bi/survey/getSurveyDetailLogs',
    response,
    queryStringParameters
  )
}

export const GetSurveyConfigs = async (response) => {
  const queryStringParameters = {}

  return await loadAllPages(
    'GET',
    BiApiName,
    '/bi/survey/getSurveyConfigs',
    response,
    queryStringParameters
  )
}
