/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { API, Auth } from 'aws-amplify'
import { PlatformAdminApi, Prefix } from './amplify.service'

const BASE_PATH = '/' + Prefix + 'platform/access'
const API_NAME = PlatformAdminApi
const INIT = {}

class UserService {
  async get(endpoint, payload = null) {
    if (payload) {
      return await API.get(API_NAME, BASE_PATH + endpoint, {
        queryStringParameters: payload,
      })
    } else {
      return await API.get(API_NAME, BASE_PATH + endpoint, INIT)
    }
  }

  async post(endpoint, payload = null) {
    return await API.post(PlatformAdminApi, BASE_PATH + endpoint, {
      body: payload,
    })
  }

  async put(endpoint, payload = null) {
    return await API.put(PlatformAdminApi, BASE_PATH + endpoint, {
      body: payload,
    })
  }

  async delete(endpoint) {
    return await API.del(PlatformAdminApi, BASE_PATH + endpoint, INIT)
  }
}

export { UserService }
