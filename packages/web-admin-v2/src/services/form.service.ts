/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { API } from 'aws-amplify'
import { ApiName, PlatformAdminApi, Prefix } from './amplify.service'
const GET_SURVEY_LIST = '/survey/admin/configs'
const GET_ALL_SURVEY_CONFIGS = '/survey/admin/configs'
const UPDATE_SURVEY_CONFIG = '/survey/admin/configs'

export const GetSurveyList = async (payload) => {
  return await API.get(PlatformAd<PERSON><PERSON><PERSON>, GET_SURVEY_LIST, {
    queryStringParameters: {
      download: payload.download,
      lastEvaluatedKey: JSON.stringify(payload.lastEvaluatedKey),
    },
  })
}

export const GetAndStockSurveyList = async (download) => {
  const surveyConfigs: any[] = []
  let response: any = {}
  let lastEvaluatedKey: any = null
  do {
    response = await GetSurveyList({
      download,
      lastEvaluatedKey,
    })
    if (response && response.data) {
      lastEvaluatedKey = response.lastEvaluatedKey
      surveyConfigs.push(...response.data)
    }
  } while (response.lastEvaluatedKey)
  return surveyConfigs
}

export const GetAllFormConfigs = async () => {
  let result, lastEvaluatedKey
  do {
    const params = lastEvaluatedKey
      ? {
          queryStringParameters: {
            lastEvaluatedKey: JSON.stringify(lastEvaluatedKey),
          },
        }
      : {}
    const res = await API.get(PlatformAdminApi, GET_ALL_SURVEY_CONFIGS, params)
    lastEvaluatedKey = res.lastEvaluatedKey
    if (result && res.data) {
      result.data = result.data ? result.data.concat(res.data) : res.data
    } else {
      result = res
    }
  } while (lastEvaluatedKey)
  delete result.lastEvaluatedKey
  return result
}

export const GetFormConfigById = async (surveyId) => {
  return await API.get(
    PlatformAdminApi,
    GET_ALL_SURVEY_CONFIGS + '/' + surveyId,
    {}
  )
}

export const RegisterSurveyConfig = async (payload) => {
  return API.post(PlatformAdminApi, GET_ALL_SURVEY_CONFIGS, {
    body: payload,
  })
}

export const UpdateSurveyConfig = async (payload) => {
  return API.put(PlatformAdminApi, UPDATE_SURVEY_CONFIG, {
    body: payload,
  })
}

export const GetCategoriesTrees = async () => {
  return await API.get(
    PlatformAdminApi,
    '/survey/calendars/categories_tree?include_display_settings',
    {}
  )
}
