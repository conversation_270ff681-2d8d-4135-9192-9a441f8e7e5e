/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { API } from 'aws-amplify'
import { PlatformAdminApi, Prefix } from './amplify.service'

// S3への画像ファイルアップロード
export const getPresignedUrlOfReminderImage = async (payload) => {
  return await API.get(
    PlatformAdminApi,
    '/' + Prefix + '/reservation-reminders/presignedURL',
    {
      queryStringParameters: {
        key: payload.key,
        contentType: payload.contentType,
      },
    }
  )
}

const ReadFileAsync = async (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      resolve(reader.result)
    }
    reader.onerror = reject
    reader.readAsArrayBuffer(file)
  })
}

export const UploadReminderImageToS3 = async ({
  file,
  contentType,
  presignedUrl,
}) => {
  const blob: any = await ReadFileAsync(file)
    .then((response) => {
      return response
    })
    .catch((error) => {
      console.log(error)
      return error
    })

  const s3Response = await fetch(presignedUrl, {
    method: 'PUT',
    headers: {
      'Content-Type': contentType,
    },
    body: blob,
  })
    .then((response) => {
      return response
    })
    .catch((error) => {
      console.log(error)
      return error
    })
  return s3Response
}

// リマインド配信の実行ログを取得(予約リマインド配信・日付リマインド配信共通で使用可能)
export const getExecutionHistories = async (payload) => {
  return await API.get(
    PlatformAdminApi,
    '/' + Prefix + '/reservation-reminders/history',
    {
      queryStringParameters: {
        month: payload.month,
        partitionKey: payload.partitionKey,
        sortKey: payload.sortKey,
      },
    }
  )
}

// 予約リマインド配信設定を取得
export const getReminderConfiguration = async (payload) => {
  return await API.get(
    PlatformAdminApi,
    '/' + Prefix + '/reservation-reminders/settings/appointment',
    {
      queryStringParameters: {
        surveyId: payload.surveyId,
        categoryId: payload.categoryId,
        type: payload.type,
      },
    }
  )
}

// リマインド配信設定を更新(予約リマインド配信・日付リマインド配信共通で使用可能)
export const updateReminderConfiguration = async (payload) => {
  return await API.put(
    PlatformAdminApi,
    '/' + Prefix + '/reservation-reminders/settings',
    {
      body: payload,
    }
  )
}

// リマインド配信設定を作成(予約リマインド配信・日付リマインド配信共通で使用可能)
export const createReminderConfiguration = async (payload) => {
  return await API.post(
    PlatformAdminApi,
    '/' + Prefix + '/reservation-reminders/settings',
    {
      body: payload,
    }
  )
}

// 予約リマインド配信を削除
export const deleteReservationReminderConfiguration = async (
  configurationId
) => {
  return await API.del(
    PlatformAdminApi,
    '/' +
      Prefix +
      '/reservation-reminders/settings/' +
      encodeURIComponent(configurationId),
    {}
  )
}

// 日付リマインド配信設定を削除
export const deleteDateReminders = async (reminderIds) => {
  return await API.post(
    PlatformAdminApi,
    '/' + Prefix + '/reservation-reminders/reminders/delete',
    {
      body: reminderIds,
    }
  )
}

// 日付リマインドを検索
export const findDateReminders = async (
  surveyId: string,
  dateItemKey: string
) => {
  const basePath =
    '/' + Prefix + '/reservation-reminders/settings/date_relative'
  return await API.get(PlatformAdminApi, basePath, {
    queryStringParameters: {
      surveyId,
      dateItemKey,
    },
  })
}
