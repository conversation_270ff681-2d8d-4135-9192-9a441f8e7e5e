import { InitUserPayload } from './../stores/modules/auth.module'
import { Auth, Amplify } from 'aws-amplify'
import {
  CognitoUserPool,
  CognitoIdToken,
  CognitoAccessToken,
  CognitoRefreshToken,
  CognitoUser,
  CognitoUserSession,
} from 'amazon-cognito-identity-js'

export const Prefix = ''
export const ApiName = 'AdminApi'
export const PlatformAdminApi = 'PlatformAdminApi'
export const ScenarioApiName = 'ScenarioAdminApi'
export const BosaiApiName = 'BosaiAdminApi'
export const QuickSightApiName = 'QuickSightAdminApi'
export const DamageReportApiName = 'DamageReportAdminApi'
export const DamageReportChatApiName = 'DamageReportAdminChatApi'
export const BiApiName = 'BiApiName'
export const ServerlessMode = true

export const setAmplifyUser = async (payload: InitUserPayload) => {
  const idToken = {
    'cognito:username': payload.username,
  }
  const authResponse = {
    id_token: payload.idToken,
  }
  const accessToken = payload.accessToken
  const refreshToken = payload.refreshToken

  const userPool = new CognitoUserPool({
    UserPoolId: import.meta.env.VITE_AMPLIFY_AUTH_USER_POOL_ID,
    ClientId: import.meta.env.VITE_AMPLIFY_AUTH_USER_POOL_WEB_CLIENT_ID,
  })

  const cognitoIdToken = new CognitoIdToken({
    IdToken: authResponse.id_token,
  })

  const cognitoAccessToken = new CognitoAccessToken({
    AccessToken: accessToken,
  })

  const cognitoRefreshToken = new CognitoRefreshToken({
    RefreshToken: refreshToken,
  })

  const username = idToken['cognito:username']

  const user = new CognitoUser({
    Username: username,
    Pool: userPool,
  })

  user.setSignInUserSession(
    new CognitoUserSession({
      AccessToken: cognitoAccessToken,
      IdToken: cognitoIdToken,
      RefreshToken: cognitoRefreshToken,
    })
  )
}

const AmplifyService = {
  configure() {
    const endpointUrl = import.meta.env.VITE_AMPLIFY_ADMIN_API_ENDPOINT_URL
    const scenarioEndpointUrl = import.meta.env.VITE_AMPLIFY_SCENARIO_API_ENDPOINT_URL
    const quickSightEndpointUrl = import.meta.env.VITE_CQ_API_ENDPOINT_URL
    const biEndpointUrl = import.meta.env.VITE_CQ_API_ENDPOINT_URL
    const bosaiEndpointUrl = import.meta.env.VITE_AMPLIFY_BOSAI_API_ENDPOINT_URL
    const damageReportEndpointUrl = import.meta.env.VITE_DAMAGE_REPORT_API_ENDPOINT_URL

    Amplify.configure({
      Auth: {
        identityPoolId: import.meta.env.VITE_AMPLIFY_AUTH_IDENTITY_POOL_ID,
        userPoolId: import.meta.env.VITE_AMPLIFY_AUTH_USER_POOL_ID,
        userPoolWebClientId: import.meta.env
          .VITE_AMPLIFY_AUTH_USER_POOL_WEB_CLIENT_ID,
        identityPoolRegion: import.meta.env.VITE_DEPLOY_AWS_REGION,
        region: import.meta.env.VITE_AMPLIFY_AUTH_REGION,
      },
      API: {
        endpoints: [
          {
            name: BiApiName,
            endpoint: biEndpointUrl,
            region: import.meta.env.VITE_AMPLIFY_AUTH_REGION,
            custom_header: async () => {
              try {
                return {
                  Authorization: `Bearer ${(await Auth.currentSession())
                    .getIdToken()
                    .getJwtToken()}`,
                }
              } catch (e: any) {
                return {}
              }
            },
          },
          {
            name: PlatformAdminApi,
            endpoint: endpointUrl,
            region: import.meta.env.VITE_AMPLIFY_AUTH_REGION,
            custom_header: async () => {
              try {
                return {
                  Authorization: `Bearer ${(await Auth.currentSession())
                    .getIdToken()
                    .getJwtToken()}`,
                }
              } catch (e: any) {
                return {}
              }
            },
          },
          {
            name: ScenarioApiName,
            endpoint: scenarioEndpointUrl,
            region: import.meta.env.VITE_AMPLIFY_AUTH_REGION,
            custom_header: async () => {
              try {
                return {
                  Authorization: `Bearer ${(await Auth.currentSession())
                    .getIdToken()
                    .getJwtToken()}`,
                }
              } catch (e: any) {
                return {}
              }
            },
          },
          {
            name: BosaiApiName,
            endpoint: bosaiEndpointUrl,
            region: import.meta.env.VITE_AMPLIFY_AUTH_REGION,
            custom_header: async () => {
              try {
                return {
                  Authorization: `Bearer ${(await Auth.currentSession())
                    .getIdToken()
                    .getJwtToken()}`,
                }
              } catch (e: any) {
                return {}
              }
            },
          },
          {
            name: QuickSightApiName,
            endpoint: quickSightEndpointUrl,
            region: import.meta.env.VITE_AMPLIFY_AUTH_REGION,
          },
          {
            name: DamageReportApiName,
            endpoint: damageReportEndpointUrl,
            region: import.meta.env.VITE_AMPLIFY_AUTH_REGION,
            custom_header: async () => {
              try {
                return {
                  Authorization: `Bearer ${(await Auth.currentSession())
                    .getIdToken()
                    .getJwtToken()}`,
                }
              } catch (e: any) {
                return {}
              }
            },
          },
          {
            name: DamageReportChatApiName,
            endpoint: damageReportEndpointUrl,
            region: import.meta.env.VITE_AMPLIFY_AUTH_REGION,
            custom_header: async () => {
              try {
                return {
                  Authorization: `Bearer ${(await Auth.currentSession())
                    .getIdToken()
                    .getJwtToken()}`,
                }
              } catch (e: any) {
                return {}
              }
            },
          },
        ],
      },
    })
  },
}

class AppUser {
  public sub: any
  public username: string
  public email: string
  public groups: Array<any>

  constructor(sub, username, email, groups?) {
    this.sub = sub
    this.username = username
    this.email = email
    this.groups = groups || []
  }

  static fromCognitoUser = (cognitoUser) => {
    if (!cognitoUser) {
      return new AppUser('', '', [])
    }
    const session = cognitoUser.signInUserSession
    if (!session) return null
    const payload = session.idToken.payload
    return new AppUser(
      payload.sub,
      payload['cognito:username'],
      payload.email,
      payload['cognito:groups']
    )
  }
}

const getCognitoUser = async () => {
  return await Auth.currentAuthenticatedUser()
}

const refreshUserInfo = async () => {
  const user = await getCognitoUser()
  if (user) {
    return AppUser.fromCognitoUser(user)
  } else {
    return AppUser.fromCognitoUser(undefined)
  }
}

export {
  AmplifyService,
  Prefix as ADMIN_API,
  AppUser,
  refreshUserInfo,
  getCognitoUser,
}
