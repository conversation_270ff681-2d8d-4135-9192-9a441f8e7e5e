/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { API } from 'aws-amplify'
import { PlatformAdminApi, Prefix } from './amplify.service'

const SearchSystemLogs = async (startDate, endDate) => {
  const init: any = {
    queryStringParameters: {},
  }
  if (startDate) {
    init.queryStringParameters.start = startDate
  }
  if (endDate) {
    init.queryStringParameters.end = endDate
  }

  return await API.get(PlatformAdminApi, '/' + Prefix + '/system-logs', init)
}

interface IFrontendActionLog {
  type: 'download-system-log'
  path: string
  method: 'GET' | 'PUT' | 'POST' | 'DELETE' | 'PUT'
  params?: {
    parameter1?: string | number
    parameter2?: string | number
    parameter3?: string | number
    parameter4?: string | number
    parameter5?: string | number
  }
}
interface ITalkLogsActionLog {
  type: 'talklog-system-log'
  path: string
  method: 'GET' | 'PUT' | 'POST' | 'DELETE' | 'PUT'
  params?: {
    parameter1?: string | number
    parameter2?: string | number
    parameter3?: string | number
    parameter4?: string | number
    parameter5?: string | number
  }
}

const UploadFrontendActionLog = async (
  payload: IFrontendActionLog
): Promise<void> => {
  return await API.post(
    PlatformAdminApi,
    '/' + Prefix + '/system-logs/record',
    {
      body: payload,
    }
  )
}

const UploadFrontendTalkLog = async (
  payload: ITalkLogsActionLog
): Promise<void> => {
  return await API.post(
    PlatformAdminApi,
    '/' + Prefix + '/system-logs/record',
    {
      body: payload,
    }
  )
}
export {
  SearchSystemLogs,
  UploadFrontendActionLog,
  UploadFrontendTalkLog,
  IFrontendActionLog,
  ITalkLogsActionLog,
}
