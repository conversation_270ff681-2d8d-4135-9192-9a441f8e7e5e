/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { API } from 'aws-amplify'
import { ApiName, PlatformAdminApi } from './amplify.service'
import { generateUUID } from '@/utils/uuidUtils'

export const UploadSurveyImageToS3 = async (payload) => {
  let headerFlg = false
  const itemImgsChk = payload.surveySchema.filter(
    (obj) => obj.imageUrl && JSON.stringify(obj.imageUrl.file) === '{}'
  )
  const itemImgs = itemImgsChk != undefined ? itemImgsChk : []
  if (
    payload.headerImageUrl &&
    JSON.stringify(payload.headerImageUrl.file) === '{}'
  ) {
    itemImgs.push({ imageUrl: { file: null } })
    itemImgs[itemImgs.length - 1].imageUrl.file = payload.headerImageUrl.file
    headerFlg = true
  }
  if (itemImgs.length != 0) {
    for (let i = 0; itemImgs.length > i; i++) {
      const s3ToUrl = await upLoadLoopS3(itemImgs[i])
      if (itemImgs.length - 1 == i && headerFlg) {
        payload.headerImageUrl = s3ToUrl
      } else {
        itemImgs[i].imageUrl = s3ToUrl
      }
    }
  }
}

export const upLoadLoopS3 = async (imgs) => {
  const GET_PRESIGNED_URL = '/survey/admin/configs/img'
  const folname = 'resources'
  const url = generateUUID()
  const contentType = imgs.imageUrl.file.type

  // S3バケットの証明付きURLを取得
  const presignedUrlResponse = await API.get(
    PlatformAdminApi,
    GET_PRESIGNED_URL,
    {
      queryStringParameters: {
        folderName: folname,
        key: url,
        contentType,
      },
    }
  )

  const s3ToUrl = presignedUrlResponse.objUrl + folname + '/' + url
  let readError = false
  delete imgs.imageUrl.file.headerUrl
  const blob: any = await ReadFileAsync(imgs.imageUrl.file)
    .then((response) => {
      return response
    })
    .catch((error) => {
      readError = true
      return error
    })
  if (readError) {
    throw blob
  }

  // blobでファイルアップロード
  let uploadError = false
  const s3UploadUrl = presignedUrlResponse.urlLink
  const s3Response = await fetch(s3UploadUrl, {
    method: 'PUT',
    headers: {
      'Content-Type': contentType,
    },
    body: blob,
  })
    .then((response) => {
      return response
    })
    .catch((error) => {
      uploadError = true
      return error
    })
  if (uploadError) {
    throw s3Response
  }
  return s3ToUrl
}

export const ReadFileAsync = async (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      resolve(reader.result)
    }
    reader.onerror = reject
    reader.readAsArrayBuffer(file)
  })
}
