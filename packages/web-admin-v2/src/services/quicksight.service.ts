/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { API } from 'aws-amplify'
import { QuickSightApiName } from './amplify.service'

export const GetDashboardEmbedUrl = async (dashboardId, userSub, userEmail) => {
  function getDashboardUrlRequest(dashboardId, userSub, userEmail) {
    return API.post(QuickSightApiName, '/getdashboardurl', {
      body: {
        DashboardId: dashboardId,
        UserSub: userSub,
        UserEmail: userEmail,
      },
    })
  }

  const res = await getDashboardUrlRequest(dashboardId, userSub, userEmail)
  const body = JSON.parse(res.body)
  return body.link.EmbedUrl
}

export const GetButtonEmbedUrl = async (buttonId, userSub, userEmail) => {
  function getButtonUrlRequest(buttonId, userSub, userEmail) {
    return API.post(QuickSightApiName, '/getbuttonurl', {
      body: {
        ButtonId: buttonId,
        UserSub: userSub,
        UserEmail: userEmail,
      },
    })
  }

  const res = await getButtonUrlRequest(buttonId, userSub, userEmail)
  const body = JSON.parse(res.body)
  return body.link.EmbedUrl
}
