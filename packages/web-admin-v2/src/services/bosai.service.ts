/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { API } from 'aws-amplify'
import { BosaiApiName } from './amplify.service'
import { cloneDeep } from 'lodash'

import { loadAllPages } from '@/utils/queryHelper'

const shelterS3FolderName = 'shelter'
const shelterS3FileName = 'shelter_csv'

const ReadFileAsync = async (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = () => {
      resolve(reader.result)
    }

    reader.onerror = reject

    reader.readAsArrayBuffer(file)
  })
}

const GET_BOSAI_OPERATION_STATUS = '/bosai/shelter/asyncStatus'
export const GetBosaiOperationStatus = async (actionName) => {
  return API.get(BosaiApiName, GET_BOSAI_OPERATION_STATUS, {
    queryStringParameters: {
      actionName,
    },
  })
}

const GET_ALL_SHELTERS = '/bosai/db/shelter/scan'
export const GetAllShelters = async (responseJson) => {
  return await loadAllPages(
    'POST',
    BosaiApiName,
    GET_ALL_SHELTERS,
    responseJson,
    {}
  )
}

const SEARCH_SHELTERS = '/bosai/db/shelter/search'
export const SearchShelters = async (responseJson, searchPayload) => {
  return await loadAllPages(
    'POST',
    BosaiApiName,
    SEARCH_SHELTERS,
    responseJson,
    searchPayload
  )
}

const IMPORT_SHELTER_DATA = '/bosai/shelter/importCSV'
export const ImportShelterData = async () => {
  return API.post(BosaiApiName, IMPORT_SHELTER_DATA, {
    body: {
      key: shelterS3FolderName + '/' + shelterS3FileName,
    },
  })
}

const BATCH_WRITE_ITEM_ENDPOINT = '/bosai/db/shelter/batchWriteItem'
export const BatchWriteItem = async (putItems, deleteItems) => {
  const bodyBatchWriteItem = {
    putItems,
    deleteItems,
  }

  let errorBatchWriteItem = false
  const response = await API.post(BosaiApiName, BATCH_WRITE_ITEM_ENDPOINT, {
    body: bodyBatchWriteItem,
  })
    .then((response) => {
      return response
    })
    .catch((error) => {
      errorBatchWriteItem = true
      return error
    })
  if (errorBatchWriteItem) {
    throw response
  }
  if (response.result === 'ERROR') {
    throw new Error(response.error_message)
  }
  return response
}

const DELETE_ALL_ENDPOINT = '/bosai/db/shelter/deleteAll'
export const DeleteAllShelterData = async () => {
  let deleteAllError = false
  const response = await API.del(BosaiApiName, DELETE_ALL_ENDPOINT, {})
    .then((response) => {
      return response
    })
    .catch((error) => {
      deleteAllError = true
      return error
    })
  if (deleteAllError) {
    throw response
  }
  if (response.result === 'ERROR') {
    throw new Error(response.error_message)
  }
  return response
}

const DOWNLOAD_SHELTER_DATA = '/bosai/shelter/exportCSV'
export const DownloadSheltersAsCSV = async () => {
  const exportResponse = await API.get(BosaiApiName, DOWNLOAD_SHELTER_DATA, {})
  console.log(exportResponse)

  if (exportResponse.result == 'ERROR') {
    throw new Error(exportResponse.exception)
  }

  return exportResponse
}

const GET_CSV_UPLOAD_URL = '/bosai/shelter/csvUploadURL'
export const UploadCSVFileToS3 = async (fileData) => {
  let errorGettingPresignedURL = false
  const presignedUrlResponse = await API.get(BosaiApiName, GET_CSV_UPLOAD_URL, {
    queryStringParameters: {
      objectName: shelterS3FileName,
      folderName: shelterS3FolderName,
      contentType: fileData.type,
    },
  })
    .then((response) => {
      return response
    })
    .catch((error) => {
      errorGettingPresignedURL = true
      return error
    })
  if (errorGettingPresignedURL) {
    throw presignedUrlResponse
  }
  if (presignedUrlResponse.result === 'ERROR') {
    throw new Error(presignedUrlResponse.exception)
  }
  const s3UploadUrl = presignedUrlResponse.url
  const s3UploadFields = presignedUrlResponse.fields
  let errorReadingFile = false
  const blob: any = await ReadFileAsync(fileData)
    .then((response) => {
      return response
    })
    .catch((error) => {
      errorReadingFile = true
      return error
    })
  if (errorReadingFile) {
    throw blob
  }

  let errorImportingToS3 = false
  const form = new FormData()
  for (const field in s3UploadFields) {
    form.append(field, s3UploadFields[field])
  }
  form.append('file', new Blob([blob]))
  const s3Response = await fetch(s3UploadUrl, {
    method: 'POST',
    body: form,
  })
    .then((response) => {
      return response
    })
    .catch((error) => {
      errorImportingToS3 = true
      return error
    })
  if (errorImportingToS3) {
    throw s3Response
  }

  return s3Response
}
