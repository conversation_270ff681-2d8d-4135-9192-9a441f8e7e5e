/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { AbilityBuilder, Ability, defineAbility } from '@casl/ability'
import {
  ACCESS_TAB_PERMISSION,
  HIDE_BUTTON_PERMISSION,
  DISABLE_BUTTON_PERMISSION,
} from '@/constants/permission.constants'

export default defineAbility((can, cannot) => {
  can('access', 'signin')
})

export const GetUserAbilities = async (userGroups) => {
  /*
  let _pers = await GetAccessSettings()
  return _pers[userGroups]
  */
  // now only show pages using default values
  // TODO: update when group permission pattern changes
  // NOTE: AND logic with editable permission later
  // TODO: put into constant file
  /*
  const defaultAdminsAccess = [
    "ApplicantsPage",
    "SegmentDeliveryPage",
    "ConditionDetail",
    "DistributionDetail",
    "DistributionCreatePage",
    "DistributionEditPage",
    "DistributionCopyPage",
    "FormCreatePage",
    "FormCreateNewPage",
    "FormDetailPage",
    "CommonSettings",
    "AwsSettings",
    "LineSettings",
    "EmailSettings",
    // "CalendarSettings",
    "UserSettings",
    "User",
    "UserDetail",
    "Team",
    "Group",
    "GroupDetail",
    "Policy",
    "ScenarioSettingsPage",
    "ScenarioSettingsDetailPage",
    "ScenarioMindmapPage",
    "CalendarPage",
    "BosaiSettingsPage",
    "StatisticsPage",
    "LogsPage",
    // "PermissionSettings"
  ];
  const defaultMembersAccess = [
    "ApplicantsPage",
    "SegmentDeliveryPage",
    "ConditionDetail",
    "DistributionDetail",
    "DistributionCreatePage",
    "DistributionEditPage",
    "DistributionCopyPage",
    "FormCreatePage",
    "FormCreateNewPage",
    "FormDetailPage",
    "CommonSettings",
    "AwsSettings",
    "EmailSettings",
    "LineSettings",
    // "CalendarSettings",
    "UserSettings",
    "User",
    "UserDetail",
    "Team",
    "Group",
    "GroupDetail",
    "Policy",
    "ScenarioSettingsPage",
    "ScenarioSettingsDetailPage",
    "CalendarPage",
    "BosaiSettingsPage",
    "StatisticsPage",
    "LogsPage",
    // "PermissionSettings"
  ];
  const defaultGuestsAccess = [
    "ApplicantsPage",
    "SegmentDeliveryPage",
    "ConditionDetail",
    "DistributionDetail",
    "DistributionCreatePage",
    "DistributionEditPage",
    "DistributionCopyPage",
    "FormCreatePage",
    "FormCreateNewPage",
    "FormDetailPage",
    "CommonSettings",
    "AwsSettings",
    "LineSettings",
    "EmailSettings",
    // "CalendarSettings",
    "UserSettings",
    "User",
    "UserDetail",
    "Team",
    "Group",
    "GroupDetail",
    "Policy",
    "ScenarioSettingsPage",
    "ScenarioSettingsDetailPage",
    "CalendarPage",
    "BosaiSettingsPage",
    "StatisticsPage",
    "LogsPage",
    // "PermissionSettings"
  ];
  */
  /*
  const defaultAdminsClick = [
    "backendRequest",
  ];
  const defaultMembersClick = [
    "backendRequest",
  ];
  const defaultGuestsClick = [
    // "backendRequest",
  ];
  */

  /*
  const defaultAdminHideButton = [
    // NOTE: because there might be super_admin, also put admin's permission list
    // home
    // "Applicants_ContentFragment_ImportCSVAppending",
    // "Applicants_DataModal_Unicast",
    // "Applicants_DataModal_Save",

    // distribution
    // "SegmentDelivery_DistributionCreation_SaveDraft",
    // "SegmentDelivery_DistributionCreation_TestBroadcast",
    // "SegmentDelivery_DistributionCreation_SendDistribution",
    // "SegmentDelivery_DistributionDetail_DeleteDistribution",
    // "SegmentDelivery_DataModal_Save",
    // "SegmentDelivery_ConditionDetail_Clear",
    // "SegmentDelivery_ConditionDetail_Save",

    // form
    // "FormEditor_EndOfSurveyMessageModal_Save",

    // scenario
    // "ScenarioSettings_ScenarioEnvironmentCard_Activate", // show?
  ];
  const defaultMembersHideButton = [
    // home
    // "Applicants_ContentFragment_ImportCSVAppending",
    // "Applicants_DataModal_Unicast",
    // "Applicants_DataModal_Save",
    // "Applicants_NewSurveyModal_Save",
    // "Applicants_ContentFragment_MultipleUnicast",

    // distribution
    // "SegmentDelivery_DistributionCreation_SaveDraft",
    // "SegmentDelivery_DistributionCreation_TestBroadcast",
    // "SegmentDelivery_DistributionCreation_SendDistribution",
    "SegmentDelivery_DistributionDetail_DeleteDistribution",
    // "SegmentDelivery_DataModal_Save",
    // "SegmentDelivery_ConditionDetail_Clear",
    // "SegmentDelivery_ConditionDetail_Save",

    // form
    // "FormEditor_EndOfSurveyMessageModal_Save",

    // scenario
    "ScenarioSettings_ScenarioEnvironmentCard_Activate", // show?
    "ScenarioSettings_ScenarioEnvironmentCard_Delete",
    // "Components_BotDesigner_ItemProperties_Save",
    // "Components_BotDesigner_ItemProperties_Delete",
    // rich menu
    // "ScenarioSettings_RichMenuCreateModal_Save", // old
    // "ScenarioSettings_RichMenuDesigner_Save",
    "ScenarioSettings_RichMenuManageTable_SetDefault",
    "ScenarioSettings_RichMenuManageTable_SetBosai",
    "ScenarioSettings_RichMenuManageTable_Delete",

    // calendar
    // "Calendar_SearchFragment_CsvRegistration",
    // "Calendar_ImportCsvContent_SelectCsvFile",
    // "Calendar_ImportCsvContent_RegisterCsvFile",
    // "Calendar_Index_CalendarSettings",

    // bosai
    "Components_RichMenuDisplay_Activate",

    // system settings
    "AdminSettings_CommonSettings_Update",
    "AdminSettings_AwsSettings_UpdateMessagingAPISettingsPro",
    "AdminSettings_AwsSettings_UpdateMessagingAPISettingsSB",
    "AdminSettings_AwsSettings_UpdateDamageReportSettings",
    "AdminSettings_EmailSettings_Update",
    // "AdminSettings_AddCalendarCategoryModal_Save",
    // "AdminSettings_CalendarDisplaySettingsFragment_Update", // ???

    // user settings
    "UserSettings_AddUserDialog_Save",
    "UserSettings_UserDetail_ResetPassword",
    "UserSettings_UserDetail_DisableAccount",
    "UserSettings_UserDetail_DeleteUser",
    "UserSettings_UserDetail_RemoveTeam",
    "UserSettings_UserDetail_RemoveAccessLevel",
    "UserSettings_Policy_Save",
  ];
  const defaultGuestsHideButton = [
    // home
    "Applicants_ContentFragment_ImportCSVAppending",
    "Applicants_DataModal_Unicast",
    "Applicants_DataModal_Save",
    "Applicants_NewSurveyModal_Save",
    "Applicants_ContentFragment_MultipleUnicast",

    // distribution
    "SegmentDelivery_DistributionCreation_SaveDraft",
    "SegmentDelivery_DistributionCreation_TestBroadcast",
    "SegmentDelivery_DistributionCreation_SendDistribution",
    "SegmentDelivery_DistributionDetail_DeleteDistribution",
    "SegmentDelivery_DataModal_Save",
    "SegmentDelivery_ConditionDetail_Clear",
    "SegmentDelivery_ConditionDetail_Save",

    // form
    "FormEditor_EndOfSurveyMessageModal_Save",

    // scenario
    "ScenarioSettings_ScenarioEnvironmentCard_Activate", // show?
    "ScenarioSettings_ScenarioEnvironmentCard_Delete",
    "Components_BotDesigner_ItemProperties_Save",
    "Components_BotDesigner_ItemProperties_Delete",
    // rich menu
    "ScenarioSettings_RichMenuCreateModal_Save", // old
    "ScenarioSettings_RichMenuDesigner_Save",
    "ScenarioSettings_RichMenuManageTable_SetDefault",
    "ScenarioSettings_RichMenuManageTable_SetBosai",
    "ScenarioSettings_RichMenuManageTable_Delete",

    // calendar
    "Calendar_SearchFragment_CsvRegistration",
    "Calendar_ImportCsvContent_SelectCsvFile",
    "Calendar_ImportCsvContent_RegisterCsvFile",
    "Calendar_Index_CalendarSettings",

    // bosai
    "Components_RichMenuDisplay_Activate",

    // system settings
    "AdminSettings_CommonSettings_Update",
    "AdminSettings_AwsSettings_UpdateMessagingAPISettingsPro",
    "AdminSettings_AwsSettings_UpdateMessagingAPISettingsSB",
    "AdminSettings_AwsSettings_UpdateDamageReportSettings",
    "AdminSettings_EmailSettings_Update", // ??????
    // "AdminSettings_AddCalendarCategoryModal_Save",
    // "AdminSettings_CalendarDisplaySettingsFragment_Update",

    // user settings
    "UserSettings_AddUserDialog_Save",
    "UserSettings_UserDetail_ResetPassword",
    "UserSettings_UserDetail_DisableAccount",
    "UserSettings_UserDetail_DeleteUser",
    "UserSettings_UserDetail_RemoveTeam",
    "UserSettings_UserDetail_RemoveAccessLevel",
    "UserSettings_Policy_Save",
  ];

  const defaultAbilities = {
    admins: {
      can: {
        access: defaultAdminsAccess,
        click: defaultAdminsClick,
        hideButton: defaultAdminHideButton,
      },
      cannot: {
        displayButton: defaultAdminHideButton,
      }
    },
    members: {
      can: {
        access: defaultMembersAccess,
        click: defaultMembersClick,
        hideButton: defaultMembersHideButton,
      },
      cannot: {
        displayButton: defaultMembersHideButton,
      }
    },
    guests: {
      can: {
        access: defaultGuestsAccess,
        click: defaultGuestsClick,
        hideButton: defaultGuestsHideButton,
      },
      cannot: {
        displayButton: defaultGuestsHideButton,
      }
    },
  }
*/

  const defaultAbilities = {
    administrators: {
      access: [],
      hideButton: [],
      disableButton: [],
      click: ['backendRequest'],
    },
    admins: {
      access: [],
      hideButton: [],
      disableButton: [],
      click: ['backendRequest'],
    },
    members: {
      access: [],
      hideButton: [],
      disableButton: [],
      click: ['backendRequest'],
    },
    guests: {
      access: [],
      hideButton: [],
      disableButton: [],
      click: ['backendRequest'],
    },
    operators: {
      access: [],
      hideButton: [],
      disableButton: [],
      click: ['backendRequest'],
    },
  }

  for (const [k, v] of Object.entries(ACCESS_TAB_PERMISSION)) {
    for (const role of v) {
      defaultAbilities[role].access.push(k)
    }
  }
  for (const [k, v] of Object.entries(HIDE_BUTTON_PERMISSION)) {
    for (const role of v) {
      defaultAbilities[role].hideButton.push(k)
    }
  }
  for (const [k, v] of Object.entries(DISABLE_BUTTON_PERMISSION)) {
    for (const role of v) {
      defaultAbilities[role].disableButton.push(k)
    }
  }

  defaultAbilities.administrators = {
    access: [
      ...defaultAbilities.administrators.access,
      ...defaultAbilities.admins.access,
    ],
    hideButton: [
      ...defaultAbilities.administrators.hideButton,
      ...defaultAbilities.admins.hideButton,
    ],
    disableButton: [
      ...defaultAbilities.administrators.disableButton,
      ...defaultAbilities.admins.disableButton,
    ],
    click: [
      ...defaultAbilities.administrators.click,
      ...defaultAbilities.admins.click,
    ],
  }

  if (
    userGroups.some((value) => {
      return value.indexOf('Administrator:admins') >= 0
    })
  ) {
    return defaultAbilities.administrators
  }
  if (
    userGroups.some((value) => {
      return value.indexOf('admins') >= 0
    })
  ) {
    return defaultAbilities.admins
  }
  if (
    userGroups.some((value) => {
      return value.indexOf('members') >= 0
    })
  ) {
    return defaultAbilities.members
  }
  if (
    userGroups.some((value) => {
      return value.indexOf('guests') >= 0
    })
  ) {
    return defaultAbilities.guests
  }
  if (
    userGroups.some((value) => {
      return value.indexOf('operators') >= 0
    })
  ) {
    return defaultAbilities.operators
  }
  return {}
}

/*
export const ACTION_TYPES = {
  ACCESS: "access",
  READ: "read",
  CREATE: "create",
  UPDATE: "update",
  DELETE: "delete",
  SEND_MSG: "send_message",
  EXPORT_CSV: "export_csv"
};

export const USER_PERMISSIONS = {
  admins: [
    { access: "ApplicantsPage" },
    { access: "SegmentDeliveryPage" },
    { access: "ConditionDetail" },
    { access: "DistributionCreatePage" },
    { access: "FormCreatePage" },
    { access: "FormCreateNewPage" },
    { access: "FormDetailPage" },
    { access: "CommonSettings" },
    { access: "PermissionSettings" },
    { access: "AwsSettings" },
    { access: "LineSettings" },
    { access: "EmailSettings" },
    { access: "CalendarSettings" },
    { access: "UserSettings" },
    { access: "User" },
    { access: "UserDetail" },
    { access: "Team" },
    { access: "Group" },
    { access: "GroupDetail" },
    { access: "Policy" },
    { access: "ScenarioSettingsPage" },
    { access: "ScenarioSettingsDetailPage" },
    { access: "ScenarioMindmapPage" },
    { access: "BosaiSettingsPage" },
    { access: "StatisticsPage" },
    { access: "LogsPage" },
  ],
  members: [
    // { access: "ApplicantsPage" },
    { access: "SegmentDeliveryPage" },
    { access: "StatisticsPage" },
  ],
  viewers: [{ access: "ApplicantsPage" }],
  guest: [{ access: "ApplicantsPage" }],
};

export const PAGES = [
  { action: "access", name: "管理画面", value: "ApplicantsPage" },
  { action: "access", name: "セグメント配信", value: "SegmentDeliveryPage" },
  { action: "access", name: "セグメント配信条件設定", value: "ConditionDetail" },
  { action: "access", name: "新規配信作成", value: "DistributionCreatePage" },
  { action: "access", name: "帳票一覧", value: "FormCreatePage" },
  { action: "access", name: "帳票作成", value: "FormCreateNewPage" },
  { action: "access", name: "帳票編集", value: "FormDetailPage" },
  { action: "access", name: "一般設定", value: "CommonSettings" },
  { action: "access", name: "LINE設定", value: "AwsSettings" },
  { action: "access", name: "LINE設定", value: "LineSettings" },
  { action: "access", name: "メール設定", value: "EmailSettings" },
  { action: "access", name: "カレンダー設定", value: "CalendarSettings" },
  { action: "access", name: "ユーザ設定", value: "UserSettings" },
  { action: "access", name: "ユーザ", value: "User"},
  { action: "access", name: "ユーザ詳細", value: "UserDetail" },
  { action: "access", name: "チーム", value: "Team" },
  { action: "access", name: "グループ", value: "Group" },
  { action: "access", name: "グループ詳細", value: "GroupDetail" },
  { action: "access", name: "ポリシー設定", value: "Policy" },
  { action: "access", name: "シナリオ設定", value: "ScenarioSettingsPage" },
  { action: "access", name: "シナリオ設定編集", value: "ScenarioSettingsDetailPage" },
  { action: "access", name: "シナリオマインドマップ", value: "ScenarioMindmapPage" },
  { action: "access", name: "カレンダー", value: "CalendarPage" },
  { action: "access", name: "防災設定", value: "BosaiSettingsPage" },
  { action: "access", name: "統計", value: "StatisticsPage" },
  { action: "access", name: "ログ", value: "LogsPage" },
];

export const ACTIONS = [

];
*/
