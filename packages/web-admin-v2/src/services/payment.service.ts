/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import {
  Product,
  ProductCategory,
  Service,
  TaxRateSetting,
} from '@/store/modules/payment/payment.types'
import { readFileAsync } from '@/utils/fileImportHelper'
import { API } from 'aws-amplify'
import { PlatformAdminApi } from './amplify.service'

const basePath = '/payment-configs'
const endPoints = {
  services: basePath + '/services',
  products: (serviceId: string) =>
    `${basePath}/services/${encodeURIComponent(serviceId)}/products`,
  productCategories: (serviceId: string) =>
    `${basePath}/service/${encodeURIComponent(serviceId)}/product-categories`,
  taxSetting: basePath + '/tax-setting',
}

export const repeatQueryFunction = async <T>(
  callback: (payload?: { [key: string]: any }) => Promise<any>,
  payload: { [key: string]: any } = {}
): Promise<T[]> => {
  const data: T[] = []
  let response: any = {}
  let lastEvaluatedKey: any = null
  do {
    response = await callback({ ...payload, lastEvaluatedKey })
    if (response?.code !== 'success' || !response?.data) {
      throw response?.errorMessage || 'データ取得中にエラーが発生しました。'
    }
    lastEvaluatedKey = response.lastEvaluatedKey
    data.push(...response.data)
  } while (response.lastEvaluatedKey)
  return data
}

// サービス設定
type FetchPaymentServiceListPayload = {
  fields?: ''
  lastEvaluatedKey?: any
  reservationServiceType?: 0 | 1 | 2
}
export const FetchPaymentServiceList = async (
  payload: FetchPaymentServiceListPayload
) => {
  return {
    code: 'success',
    data: [],
  }
  // FIXME payment feature has been removed from the core of OSS
  // return await API.get(PlatformAdminApi, endPoints.services, {
  //   queryStringParameters: payload
  // });
}

export const FetchPaymentService = async (
  serviceSortKey: string,
  includeProducts = false
) => {
  return await API.get(
    PlatformAdminApi,
    `${endPoints.services}/${encodeURIComponent(serviceSortKey)}`,
    {
      queryStringParameters: {
        include_products: includeProducts,
      },
    }
  )
}

export const CreatePaymentService = async (payload: Service<number>) => {
  return await API.post(PlatformAdminApi, endPoints.services, {
    body: payload,
  })
}

export const UpdatePaymentService = async (payload) => {
  return await API.put(PlatformAdminApi, endPoints.services, {
    body: payload,
  })
}

export const DeletePaymentServices = async (payload: string[]) => {
  return await API.post(PlatformAdminApi, endPoints.services + '/delete', {
    body: {
      keys: payload,
    },
  })
}

// 商品設定
type FetchProductListPayload = {
  serviceId: string
  lastEvaluatedKey?: any
  status?: 0 | 1 | 2
}
export const FetchProductList = async (payload: FetchProductListPayload) => {
  const { serviceId, lastEvaluatedKey, status } = payload
  return await API.get(PlatformAdminApi, endPoints.products(serviceId), {
    queryStringParameters: {
      lastEvaluatedKey,
      status,
    },
  })
}

export const CreateProduct = async (
  payload: Product<number>,
  serviceId: string
) => {
  return await API.post(PlatformAdminApi, endPoints.products(serviceId), {
    body: payload,
  })
}

export const UpdateProduct = async (
  payload: Product<number>,
  serviceId: string
) => {
  return await API.put(PlatformAdminApi, endPoints.products(serviceId), {
    body: payload,
  })
}

export const DeleteProducts = async (payload: string[], serviceId: string) => {
  return await API.post(
    PlatformAdminApi,
    `${endPoints.products(serviceId)}/delete`,
    {
      body: {
        ids: payload,
      },
    }
  )
}

export const GetPresignedUrl = async (key: string, serviceId: string) => {
  const contentType = 'text/csv'
  return await API.get(
    PlatformAdminApi,
    `${endPoints.products(serviceId)}/presigned-url`,
    {
      queryStringParameters: {
        bucketKey: key,
        contentType,
      },
    }
  )
}

export const UploadCSVToS3 = async (file: Blob, presignedUrl: string) => {
  const contentType = 'text/csv'
  const blob: any = await readFileAsync(file)
    .then((response) => {
      return response
    })
    .catch((error) => {
      console.log(error)
      return error
    })

  const s3Response = await fetch(presignedUrl, {
    method: 'PUT',
    headers: {
      'Content-Type': contentType,
    },
    body: blob,
  })
    .then((response) => {
      return response
    })
    .catch((error) => {
      return error
    })
  return s3Response
}

export const ValidateProductsCSV = async (
  bucketKey: string,
  isSJISEncoding: boolean,
  serviceId: string
) => {
  return await API.post(
    PlatformAdminApi,
    `${endPoints.products(serviceId)}/validate-csv`,
    {
      body: {
        bucketKey,
        isSJISEncoding,
        serviceId,
      },
    }
  )
}

export const SaveProductsFromS3 = async (
  bucketKey: string,
  serviceId: string
) => {
  return await API.post(
    PlatformAdminApi,
    `${endPoints.products(serviceId)}/save-csv`,
    {
      body: {
        bucketKey,
      },
    }
  )
}

export const DonwloadProductsCsv = async (productIds: string[], serviceId) => {
  return await API.post(
    PlatformAdminApi,
    `${endPoints.products(serviceId)}/export-csv`,
    {
      body: {
        ids: productIds,
      },
    }
  )
}

export const UpdateProductOrders = async (
  productIds: { productId: string; order: number | null }[],
  serviceId
) => {
  return await API.put(
    PlatformAdminApi,
    `${endPoints.products(serviceId)}/order`,
    {
      body: {
        ids: productIds,
      },
    }
  )
}

// 商品分類設定
export const FetchProductCategoryList = async ({
  lastEvaluatedKey,
  serviceId,
}) => {
  return await API.get(
    PlatformAdminApi,
    endPoints.productCategories(serviceId),
    {
      queryStringParameters: {
        lastEvaluatedKey,
      },
    }
  )
}

export const CreateProductCategory = async (
  payload: ProductCategory,
  serviceId: string
) => {
  return await API.post(
    PlatformAdminApi,
    endPoints.productCategories(serviceId),
    {
      body: payload,
    }
  )
}

export const UpdateProductCategory = async (
  payload: ProductCategory,
  serviceId: string
) => {
  return await API.put(
    PlatformAdminApi,
    endPoints.productCategories(serviceId),
    {
      body: payload,
    }
  )
}

export const DeleteProductCategories = async (payload: string[], serviceId) => {
  return await API.post(
    PlatformAdminApi,
    endPoints.productCategories(serviceId) + '/delete',
    {
      body: {
        ids: payload,
      },
    }
  )
}

// 消費税設定
export const FetchTaxSettings = async () => {
  return await API.get(PlatformAdminApi, endPoints.taxSetting, {})
}

export const UpsertRateSettings = async (payload: TaxRateSetting[]) => {
  return await API.post(PlatformAdminApi, endPoints.taxSetting, {
    body: {
      rateSettings: payload,
    },
  })
}
