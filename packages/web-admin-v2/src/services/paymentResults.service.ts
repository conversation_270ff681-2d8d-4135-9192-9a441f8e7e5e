/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { API } from 'aws-amplify'
import { PlatformAdminApi } from './amplify.service'

const BASE_PATH = '/payment-results'

export const ExportCsvPaymentResults = async (payload: {
  selected: string[]
  surveyId: string
}): Promise<any> => {
  return API.post(PlatformAdminApi, `${BASE_PATH}/export_csv`, {
    body: payload,
  })
}

export const FetchSurveyPaymentResults = async (payload: {
  partitionKeys: Array<string>
}): Promise<any> => {
  return await API.post(PlatformAdminApi, `${BASE_PATH}/list`, {
    body: { keys: payload.partitionKeys },
  })
}

export const FetchSurveyPaymentResult = async (surveyResutlsPk: string) => {
  return await API.get(PlatformAdminApi, `${BASE_PATH}/${surveyResutlsPk}`, {})
}
