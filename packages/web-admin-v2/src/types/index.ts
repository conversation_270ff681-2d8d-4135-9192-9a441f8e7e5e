// {
//   "createdByName": "Admin",
//   "deliveryStartedAs": "auto-repeating",
//   "deliveriesCount": 0,
//   "createdAt": "2023-09-01T15:00:00Z",
//   "configurationId": "test-repeating-config",
//   "createdBy": "admin",
//   "state": "NOT_STARTED",
//   "distributionName": "Test repeating delivery for",
//   "distributionType": "repeating",
//   "id": "73e63a19-cbe1-4d53-890c-fd20c59232df",
//   "targetsCount": 0,
//   "targetsSelectionType": "broadcast"
// }
export interface DeliveryRow {
  id: string
  distributionName: string
  distributionType: string
  deliveryStartedAs: string
  deliveriesCount: number
  targetsCount: number
  targetsSelectionType: string
  state: string
  createdAt: string
  createdBy: string
  createdByName: string
}

export interface LoadHistoryPayload {
  fromDate: string
  toDate: string
  limit?: number
  continueToken?: string
}

export interface ListExternalConfigs {
  surveyId?: string
  limit?: number
  enabled?: boolean
  continueToken?: string
}

export interface TalkConfig {
  targetSelectionType: string
  createdAt: string
  updatedBy: string
  updatedAt: string
  enabled: boolean
  distributionName: string
  distributionType: string
  id: string
  isDraft: boolean
  environment?: string
  talkName?: string
  mailTriggerSettings: {
    condition: {
      bodyExtractionCondition: string
      bodyTest: string
      subjectTest: string
      subjectExtractionCondition: string
    }
    content: {
      bodyChangeCondition: string
      bodyTest: string
    }
  }
  talkSettings: {
    environment: string
    talkId: string
    talkName: string
    useDisasterRichmenu: boolean
  }
}
export interface MailConfig {
  targetSelectionType: string
  createdAt: string
  updatedBy: string
  updatedAt: string
  enabled: boolean
  distributionName: string
  distributionType: string
  id: string
  isDraft: boolean
  surveyConditions?: {
    conditions: Array<MailCondition>
    surveyId: string
  }
  mailTriggerSettings: {
    condition: {
      bodyExtractionCondition: string
      bodyTest: string
      subjectTest: string
      subjectExtractionCondition: string
    }
    content: {
      bodyChangeCondition: string
      bodyTest: string
    }
  }
}
interface MailCondition {
  conditionValues: Array<string>
  itemKey: string
}
export interface MailFilterCondition {
  distributionName: string
  surveyId: string
  enabled: boolean | undefined
}

export interface TalkFilterCondition {
  distributionName: string
  talkName: string
  enabled: boolean | undefined
}

export interface EditConfig {
  settings: {
    targetSelectionType: string
    createdAt: string
    updatedBy: string
    updatedAt: string
    enabled: boolean
    distributionName: string
    distributionType: string
    id: string
    isDraft: boolean
    mailTriggerSettings: {
      condition: {
        bodyExtractionCondition: string
        bodyTest: string
        subjectTest: string
        subjectExtractionCondition: string
      }
      content: {
        bodyChangeCondition: string
        bodyTest: string
      }
    }
    surveyConditions?: {
      conditions: Array<MailCondition>
      surveyId: string
    }
    talkSettings?: {
      environment: string
      talkId: string
      talkName: string
      useDisasterRichmenu: boolean
    }
    repeatingSettings?: {
      fromDate: string
      toDate: string
      fromTime: string
      period: string
      withExclude: boolean
      exclude: {
        daysOfWeek: Array<string>
        dates: Array<string>
      }
      daysOfWeek: Array<string>
      daysOfMonth: Array<string>
      custom: {
        type: string
        skip: {
          period: string
          length: number
        }
        numberedDayOfWeek: Array<any>
        dates: Array<string>
      }
    }
  }
  messages: [
    {
      contents: {
        type: string
        text: string
      }
      id: string
    },
  ]
}
export interface ActionItem {
  actionType: 'メッセージアクション' | 'URIアクション'
  text: string
  x: number
  y: number
  width: number
  height: number
}

export interface Action {
  type: string
  text?: string
  linkUri?: string
  area: {
    height: number
    width: number
    x: number
    y: number
  },
  selected?: boolean
  id?: string
}

export interface MessageContents {
  type: string
  text?: string
  template?: any

  // flex
  contents?: object
  isJson?: boolean

  // image
  originalContentUrl?: string
  previewImageUrl?: string

  // image map
  altText?: string
  baseUrl?: string
  actions?: Array<Action>
  baseSize?: {
    height: number
    width: number
  },
  file?: any
}

export interface Message {
  id: string
  altText: string
  type: string
  contents: MessageContents
}

export interface DeliveryCustomNumberedDayOfWeek {
  number: number
  dayOfWeek: number
}

export interface DeliveryCustomNumberedDayOfWeekFrontend {
  number: number
  dayOfWeek: number[]
}

export interface DeliveryRepeatSettings {
  fromDate: string
  toDate: string
  fromTime: string
  period: string
  withExclude: boolean
  exclude: {
    daysOfWeek: number[]
    dates: string[]
  };
  daysOfWeek: number[]
  daysOfMonth: number[]
  custom: {
    type: string
    skip: {
      period: string
      length: number
    }
    numberedDayOfWeek: any[]
    dates?: string[]
  };
}

export interface DeliverySettings {
  targetSelectionType: string
  distributionName: string
  distributionType: string
  repeatingSettings: DeliveryRepeatSettings
  postponedSettings: {
    date: string
    time: string
  }
  surveyConditions: {
    surveyId: string
    pickAll: boolean
    conditions: any[]
  }
  id?: number
  updatedAt?: string
}

export interface CreateDelivery {
  messages: any[],
  settings: DeliverySettings
}

export interface DeliveryObj {
  messages: any[]
  settings: DeliverySettings
}
