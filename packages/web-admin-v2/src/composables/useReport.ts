import { useQuasar } from "quasar"
import { computed, Ref } from "vue"
import { useI18n } from "vue-i18n"
import { useDamageReportStore } from "../stores/modules/damage-report"
import { useRouter } from "vue-router"
import { useNotify } from "./useNotify"
import { storeToRefs } from "pinia"


export function useReport(reportData: Ref<any>) {
  const { t } = useI18n()
  const $q = useQuasar()
  const router = useRouter()
  const reportStore = useDamageReportStore()
  const { alertRaw } = useNotify()
  const { isClearCacheReportData } = storeToRefs(reportStore)

  const isAllowCancel = computed(() => {
    return ['not-processed', 'processing'].includes(reportData.value?.status)
  })
  const isAllowUpdate = computed(() => {
    return ['not-processed', 'processing', 'finished'].includes(reportData.value?.status)
  })
  const isAllowDelete = computed(() => {
    return ['cancel'].includes(reportData.value?.status)
  })

  // #region action
  function cancelReport() {
    $q.dialog({
      title: t('dialog.CancelReport.title'),
      message: t('dialog.CancelReport.message', { newline: "<br>" }),
      html: true,
      cancel: {
        flat: true,
        label: t('dialog.CancelReport.cancel'),
      },
      ok: {
        pushed: true,
        color: 'negative',
        label: t('dialog.CancelReport.confirm'),
      },
    })
      .onOk(async () => {
        await reportStore
          .updateDamageReportsByID(reportData.value.id, {
            status: 'cancel',
          })
          .then((res) => {
            if (res) {
              setTimeout(() => {
                router.push('/damage-report')
              }, 1200)
            }
          })
          .catch((err) => {
            alertRaw(err.response?.data?.message || err.message, 'negative')
          })
      })
      .onCancel(() => { })
  }
  function deleteReport() {
    $q.dialog({
      title: t('dialog.DeleteRow.title'),
      message: t('dialog.DeleteRow.message', [1], 1),
      cancel: {
        flat: true,
        label: t('dialog.DeleteRow.cancel'),
      },
      ok: {
        pushed: true,
        color: 'negative',
        label: t('dialog.DeleteRow.confirm'),
      },
      persistent: true,
    })
      .onOk(async () => {
        await reportStore.deleteDamageReportsByID(reportData.value.id).then(() => {
          setTimeout(async () => {
            reportStore.triggerFilter()
            isClearCacheReportData.value = true
            router.push('/damage-report')
          }, 1200)
        })
          .catch((err) => {
            alertRaw(err.response?.data?.message || err.message, 'negative')
          })
      })
      .onCancel(() => {
        // console.log('>>>> Cancel')
      })
  }
  // #endregion

  return {
    isAllowCancel,
    isAllowUpdate,
    isAllowDelete,

    //action
    cancelReport,
    deleteReport,
  }
}