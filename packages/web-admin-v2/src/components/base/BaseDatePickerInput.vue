<template>
  <q-input filled v-model="date" >
    <template v-slot:append>
      <q-icon name="event" class="cursor-pointer">
        <q-popup-proxy cover transition-show="scale" transition-hide="scale">
          <q-date v-model="date" mask="YYYY-MM-DD" :options="isValidDate">
            <div class="row items-center justify-end">
              <q-btn v-close-popup label="Close" color="primary" flat />
            </div>
          </q-date>
        </q-popup-proxy>
      </q-icon>
    </template>
  </q-input>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { computed } from 'vue'
import { useSegmentsStore } from '../../stores/modules/segments'
import { storeToRefs } from 'pinia';

const segmentsStore = useSegmentsStore()
const { createDelivery } = storeToRefs(segmentsStore)
const props = defineProps({
  modelValue: {
    type: String,
    required: true,
  },
  isFromDate: {
    type: Boolean,
    required: false,
  },
  isToDate: {
    type: Boolean,
    required: false,
  },
})

const emit = defineEmits(['update:modelValue'])

const date = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
  },
})

const isValidDate = (date: string) => {
  if (props.isFromDate) {
    const toDateJS = dayjs(createDelivery.value.settings.repeatingSettings.toDate, 'YYYY-MM-DD')
    const selectedFromDate = dayjs(date, 'YYYY-MM-DD')
    const isValid = !toDateJS.isSame(selectedFromDate, 'day') && toDateJS.isAfter(selectedFromDate, 'day')
    if (!isValid) return isValid
  } else if (props.isToDate) {
    const fromDateJS = dayjs(createDelivery.value.settings.repeatingSettings.fromDate, 'YYYY-MM-DD')
    const selectedToDate = dayjs(date, 'YYYY-MM-DD')
    const isValid = !fromDateJS.isSame(selectedToDate, 'day') && fromDateJS.isBefore(selectedToDate, 'day')
    if (!isValid) return isValid
  }
  return segmentsStore.isValidDate(date)
}
</script>
