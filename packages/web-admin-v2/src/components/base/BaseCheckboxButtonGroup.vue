<template>
  <div class="tw-flex tw-flex-row tw-gap-3 tw-flex-wrap tw-justify-stretch">
    <q-btn
      v-for="(option, index) in options"
      :key="index"
      padding="5px 15px"
      :color="modelValue?.includes(option.value) ? 'primary' : 'white'"
      :text-color="modelValue?.includes(option.value) ? 'white' : 'black'"
      :label="option.label"
      class="tw-rounded-lg tw-w-16"
      @click="emit('update:modelValue', modelValue.includes(option.value) ? modelValue.filter((v) => v !== option.value) : [...modelValue, option.value])"
    />
  </div>
</template>

<script setup lang="ts">
defineProps({
  modelValue: {
    type: Array,
    required: true,
  },
  options: {
    type: Array,
    required: true,
  },
})
const emit = defineEmits(['update:modelValue'])
</script>
