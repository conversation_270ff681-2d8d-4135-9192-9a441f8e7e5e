<template>
  <BaseiPhone12Mockup>
    <BaseLineAppFrame>
      <div ref="surveyPreviewRef" class="tw-py-2 tw-mt-2 tw-pb-20 tw-flex tw-flex-col tw-gap-4">
        <template v-for="message in createDelivery?.messages" :key="message?.id">
          <LineMessageSimulatorPreview :type="message?.contents?.type" :message="message" class="tw-cursor-pointer"/>
        </template>
      </div>
    </BaseLineAppFrame>
  </BaseiPhone12Mockup>
</template>

<script setup lang="ts">
import BaseLineAppFrame from '../base/BaseLineAppFrame.vue'
import LineMessageSimulatorPreview from '../MessageSimulator/Preview/LineMessageSimulatorPreview.vue'
import BaseiPhone12Mockup from '../base/BaseiPhone12Mockup.vue'
import { storeToRefs } from 'pinia'
import { useSegmentsStore } from 'src/stores/modules/segments'
import { FLEXMESSAGE_TEMPLATE_ITEMS } from 'src/stores/modules/segments/segments.constants.ts'
const segmentssStore = useSegmentsStore()

const { createDelivery } = storeToRefs(segmentssStore)
</script>
