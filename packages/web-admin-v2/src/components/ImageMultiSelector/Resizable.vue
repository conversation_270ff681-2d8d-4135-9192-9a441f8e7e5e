<template>
  <div class="handler">
    <!-- resize handler -->
    <div
      v-if="item.resizable"
      class="select-areas-resize-handler w"
      :style="{
        position: 'absolute',
        cursor: 'w-resize',
        display: 'block',
        left: item.x + posImg.left - 6 + 'px',
        top: item.y + posImg.top + item.height / 2 - 4 + 'px',
        'z-index': item.z + 10,
      }"
      @mousedown.stop.prevent="startDrag(item, 'w')"
    ></div>
    <div
      v-if="item.resizable"
      class="select-areas-resize-handler sw"
      :style="{
        position: 'absolute',
        cursor: 'sw-resize',
        display: 'block',
        left: item.x + posImg.left - 4 + 'px',
        top: item.y + posImg.top + item.height - 6 + 'px',
        'z-index': item.z + 10,
      }"
      @mousedown.stop.prevent="startDrag(item, 'sw')"
    ></div>
    <div
      v-if="item.resizable"
      class="select-areas-resize-handler s"
      :style="{
        position: 'absolute',
        cursor: 's-resize',
        display: 'block',
        left: item.x + posImg.left + item.width / 2 - 4 + 'px',
        top: item.y + posImg.top + item.height - 6 + 'px',
        'z-index': item.z + 10,
      }"
      @mousedown.stop.prevent="startDrag(item, 's')"
    ></div>
    <div
      v-if="item.resizable"
      class="select-areas-resize-handler se"
      :style="{
        position: 'absolute',
        cursor: 'se-resize',
        display: 'block',
        left: item.x + posImg.left + item.width - 6 + 'px',
        top: item.y + posImg.top + item.height - 6 + 'px',
        'z-index': item.z + 10,
      }"
      @mousedown.stop.prevent="startDrag(item, 'se')"
    ></div>
    <div
      v-if="item.resizable"
      class="select-areas-resize-handler e"
      :style="{
        position: 'absolute',
        cursor: 'e-resize',
        display: 'block',
        left: item.x + posImg.left + item.width - 6 + 'px',
        top: item.y + posImg.top + item.height / 2 - 6 + 'px',
        'z-index': item.z + 10,
      }"
      @mousedown.stop.prevent="startDrag(item, 'e')"
    ></div>
    <div
      v-if="item.resizable"
      class="select-areas-resize-handler ne"
      :style="{
        position: 'absolute',
        cursor: 'ne-resize',
        display: 'block',
        left: item.x + posImg.left + item.width - 6 + 'px',
        top: item.y + posImg.top - 4 + 'px',
        'z-index': item.z + 10,
      }"
      @mousedown.stop.prevent="startDrag(item, 'ne')"
    ></div>
    <div
      v-if="item.resizable"
      class="select-areas-resize-handler n"
      :style="{
        position: 'absolute',
        cursor: 'n-resize',
        display: 'block',
        left: item.x + posImg.left + item.width / 2 - 4 + 'px',
        top: item.y + posImg.top - 4 + 'px',
        'z-index': item.z + 10,
      }"
      @mousedown.stop.prevent="startDrag(item, 'n')"
    ></div>
    <div
      v-if="item.resizable"
      class="select-areas-resize-handler nw"
      :style="{
        position: 'absolute',
        cursor: 'nw-resize',
        display: 'block',
        left: item.x + posImg.left - 4 + 'px',
        top: item.y + posImg.top - 4 + 'px',
        'z-index': item.z + 10,
      }"
      @mousedown.stop.prevent="startDrag(item, 'nw')"
    ></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'

interface LocalState {
  pos: any;
}

const props = defineProps({
  item: {
    type: Object,
    default: () => {
      return {
        id: 0,
        x: 0,
        y: 0,
        width: 0,
        height: 0,
        z: 0,
        resizable: false,
      }
    },
  },
  posImg: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits(['startDrag', 'doDrag'])

const pos = ref(null)

const startDrag = (item: any, type: any): void => {
  pos.value = type
  document.addEventListener('mousemove', doDrag)
  emit('startDrag', props.item)
}

const doDrag = (e: any): void => {
  emit('doDrag', props.item, pos.value, e)
}

onMounted(() => {
  if (props.item.resizable === false) {
    window.addEventListener(
      'mouseup',
      (document as any).removeEventListener('mousemove', doDrag)
    )
  }
})
</script>

<style lang="scss" scoped>
.select-areas-resize-handler {
  background-color: #00b900;
  height: 8px;
  width: 8px;
  overflow: hidden;
}
</style>
