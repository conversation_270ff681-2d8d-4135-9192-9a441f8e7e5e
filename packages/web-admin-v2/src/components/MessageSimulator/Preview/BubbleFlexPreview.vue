<style scoped>
@import url(https://static.line-scdn.net/line_flexible_msg/172927b9b3c/css/sp/main.css?26687621);

.fxC2 {
  color: #666666 !important;
}

.fxC1 {
  color: #aaaaaa !important;
}

.fxC0 {
  color: #999999 !important;
}

.LyGi {
  width: 100%;
  max-width: 500px;
  min-width: 0;
}

.LyMe {
  width: 100%;
  max-width: 300px;
  min-width: 0;
}

.LyKi {
  width: 100%;
  max-width: 260px;
  min-width: 0;
}

.LyMi {
  width: 100%;
  max-width: 160px;
  min-width: 0;
}

.LyNa {
  width: 100%;
  max-width: 120px;
  min-width: 0;
}

.bubble-flex-preview-container {
  width: 100%;
}

</style>

<template>
  <q-container class="bubble-flex-preview-container">
    <div v-if="messageJsonObject" class="single-bubble-flex-container tw-w-full" v-html="renderedBubbleFlex"></div>
  </q-container>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { render } from 'src/services/flexRender.ts'

const props = defineProps<{
  message: any
}>()

const messageJsonObject = computed(() => {
  return props.message?.contents?.contents
})

const renderedBubbleFlex = computed(() => render(messageJsonObject.value))
</script>
