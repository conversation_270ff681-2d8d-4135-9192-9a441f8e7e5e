<template>
  <img v-if="imgUrl" :src="imgUrl" alt="Uploaded Image" class="tw-w-full tw-rounded-xl" />
</template>

<script lang="ts" setup>
import { computed } from 'vue'
const props = defineProps<{
  message: any;
}>()

const imgUrl = computed(() => {
  const file = props.message?.contents?.file
  const baseUrl = props.message?.contents?.baseUrl

  // check file can create ObjectURL
  if (typeof file === 'object' && file instanceof Blob) {
    return URL.createObjectURL(file)
  }

  return `${baseUrl}/${props.message?.contents?.baseSize?.width}`
})
</script>
