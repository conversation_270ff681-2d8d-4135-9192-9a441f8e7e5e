<template>
  <div class="tw-grid tw-grid-cols-12 tw-max-h-[calc(100vh-160px)] tw-gap-4">
    <div class="tw-col-span-12">
      <div class="q-px-md">
        <div class="tw-w-full tw-mb-4">
          <q-file
            v-model="imageFile"
            class="tw-w-full"
            :label="!imageFile ? 'クリックして画像を開く' : '画像'"
            filled
            counter
            dense
            clearable
            accept="image/png, image/jpeg, image/jpg"
          >
            <template v-slot:prepend>
              <q-icon name="attach_file" />
            </template>

            <template v-slot:hint>
              画像形式：JPG, JPEG, PNG | ファイルサイズ: 10MB以下
              (LINEトーク上でプレビュー表示される画像は、システム内部で圧縮された画像になります)
              <p class="tw-mb-3 tw-text-red-400">
                {{ fileTypeErrorMessage ? fileTypeErrorMessage : "" }} <br />
                {{ fileSizeErrorMessage ? fileSizeErrorMessage : "" }}
              </p>
            </template>
          </q-file>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep, isNumber, update } from 'lodash'
import { storeToRefs } from 'pinia'
import ActionItemList from './ActionItemList.vue'
import { MessageContents } from '../../types/index.ts'
import { useMessageSimulatorStore } from '../../stores/modules/message-simulator'
import MultiSelectAreasImage from '../ImageMultiSelector/MultiSelectAreasImage.vue'
import ImageMultiSelectorArea from '../ImageMultiSelector/ImageMultiSelectorArea.vue'
import MessageSimulatorMessageImageMapAction from './MessageSimulatorMessageImageMapAction.vue'
const messageSimulatorStore = useMessageSimulatorStore()
const {
  message,
  imgSrc,
  imgType,
  actions,
  imageMapImageHeight,
  selectedAction,
} = storeToRefs(messageSimulatorStore)
import { computed, ref, watch, onMounted } from 'vue'
const fileTypeErrorMessage = ref<string>('')
const fileSizeErrorMessage = ref<string>('')
const props = defineProps<{
  modelValue: MessageContents;
}>()

const emits = defineEmits(['update:modelValue'])

const imageFile = computed({
  get: () => props.modelValue?.file,
  set: (value) => {
    const uploadedFile = value

    if (uploadedFile) {
      const fileType = uploadedFile.type
      const fileSize = uploadedFile.size
      if (
        fileType !== 'image/png' &&
        fileType !== 'image/jpg' &&
        fileType !== 'image/jpeg'
      ) {
        fileTypeErrorMessage.value = `ファイル形式：『${fileType}』がPNG・JPG・JPEGではありません。`
      } else {
        fileTypeErrorMessage.value = ''
      }
      console.log('file size: ', fileSize)
      if (fileSize > 10000000) {
        fileSizeErrorMessage.value = `ファイルサイズ：『${
          fileSize / 1000000
        }MB』が10MB以上です。`
      } else {
        fileSizeErrorMessage.value = ''
      }

      if (fileSizeErrorMessage.value !== '' || fileTypeErrorMessage.value !== '') return
    }
    emits('update:modelValue', {
      ...props.modelValue,
      file: value,
    })
  },
})
</script>
