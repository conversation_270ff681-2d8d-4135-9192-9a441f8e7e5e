<template>
  <BaseiPhone12Mockup>
    <BaseLineAppFrame>
      <div
        ref="surveyPreviewRef"
        class="tw-py-2 tw-mt-2  tw-pb-20"
      >
        <LineMessageSimulatorPreview
          :type="message?.contents?.type"
          :message="message"
        />
      </div>
    </BaseLineAppFrame>
  </BaseiPhone12Mockup>
</template>

<script setup lang="ts">
import BaseiPhone12Mockup from '../base/BaseiPhone12Mockup.vue'
import BaseLineAppFrame from '../base/BaseLineAppFrame.vue'
import LineMessageSimulatorPreview from './Preview/LineMessageSimulatorPreview.vue'
import { storeToRefs } from 'pinia'
import { useMessageSimulatorStore } from '../../stores/modules/message-simulator'
const messageSimulatorStore = useMessageSimulatorStore()

const { message, messageTypes, messageJsonObject } = storeToRefs(messageSimulatorStore)
</script>
