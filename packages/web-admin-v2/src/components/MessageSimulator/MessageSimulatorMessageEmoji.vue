<template>
  <div class="tw-flex tw-flex-row tw-flex-wrap tw-justify-between">
    <div
      v-for="(emoji, index) in emojis?.paths"
      :key="index"
      @click="onSelectEmoji(emoji.path)"
      class="tw-h-10 tw-w-10 tw-cursor-pointer hover:tw-scale-150 tw-transition-all tw-duration-200"
    >
      <q-img :src="`/` + emoji.path" spinner-color="white" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useMessageSimulatorStore } from 'src/stores/modules/message-simulator'
import { storeToRefs } from 'pinia'
const messageSimulatorStore = useMessageSimulatorStore()
const { emojis } = storeToRefs(messageSimulatorStore)
const emits = defineEmits(['selectEmoji'])
const onSelectEmoji = (emojiPath) => {
  const emoji = `<<$${emojiPath.split('/').pop().split('.')[0]}$>>`
  messageSimulatorStore.selectEmoji(emoji)
  emits('selectEmoji', emoji)
}
</script>
