<template>
  <q-dialog v-model="show">
    <q-card class="tw-w-full">
      <q-form @submit="onSubmit" class="q-gutter-md">
        <q-card-section class="row items-center tw-pb-0">
          <div class="text-h6 tw-font-semibold">
            {{ isEditData ? '配信編集' : '新規作成' }}
          </div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-separator />

        <q-card-section class="scroll tw-flex tw-flex-col tw-space-y-0">
          <div>
            <div>配信名</div>
            <q-input
              v-model="data.distributionName"
              lazy-rules
              :rules="data.deliveryTitleRules"
              dense
              outlined
              placeholder="配信名"
            />
          </div>
          <div>
            <div>環境を選択</div>
            <q-select
              v-model="data.selectedEnvironment"
              lazy-rules
              :rules="data.environmentRules"
              dense
              outlined
              placeholder="環境名"
              :options="data.listOfEnvironments"
              emit-value
              map-options
            />
          </div>
          <div>
            <div>トークを選択</div>
            <q-select
              v-model="data.selectedTalk"
              lazy-rules
              :rules="data.talkRules"
              dense
              outlined
              placeholder="トーク名"
              :options="talksToDisplay"
              option-value="dataId"
              option-label="name"
              emit-value
              map-options
              :loading="loadings['fetchScenarioTalksForTalkDistribution']"
              :disabled="loadings['fetchScenarioTalksForTalkDistribution']"
            />
          </div>
          <div>
            <q-checkbox
              v-model="data.useDisasterRichmenu"
              label="災害用リッチメニューを使用"
              class="tw-mb-3"
            />
          </div>
          <div>
            <div>有効 / 無効</div>
            <div class="q-gutter-sm">
              <q-radio v-model="data.enabled" :val="true" label="有効" />
              <q-radio v-model="data.enabled" :val="false" label="無効" />
            </div>
          </div>
        </q-card-section>
        <q-separator />

        <q-card-actions align="right" class="tw-px-4 tw-pb-6">
          <q-btn
            outline
            label="キャンセル"
            color="primary"
            class="!tw-px-4"
            v-close-popup
            :loading="loadings['saveConfigContent'] || loadings['CREATE_SEGMENT_DELIVERY']"
          />
          <q-btn
            :label="isEditData ? '保存' : '作成'"
            color="primary"
            type="submit"
            class="!tw-px-8"
            :loading="loadings['saveConfigContent'] || loadings['CREATE_SEGMENT_DELIVERY']"
          />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useQuasar } from 'quasar'
import { useSegmentsStore } from '../../stores/modules/segments'
import { storeToRefs } from 'pinia'
const $q = useQuasar()
const segmentsStore = useSegmentsStore()

const { talksForOutsideDistribution, loadings } = storeToRefs(segmentsStore)

interface LocalState {
  valid: boolean
  isEditData: boolean
  distributionConfigId: any
  distributionName: string
  enabled: boolean
  selectedTalk: any
  talkName: string
  selectedEnvironment: string
  useDisasterRichmenu: boolean
  deliveryTitleRules: any
  talkRules: any
  environmentRules: any
  listOfEnvironments: Array<any>
  createError: boolean
  updateError: boolean
}

const props = defineProps({
  visible: Boolean,
  close: Function,
  item: Object,
  source: String,
  getSurveyConditions: Function,
  distItem: Object,
  updateTargetEstimate: Function,
})

const emits = defineEmits(['close'])

const data = ref({
  valid: false,

  isEditData: false,

  distributionConfigId: null,
  distributionName: '',
  enabled: true,
  selectedTalk: null,
  talkName: null,
  selectedEnvironment: 'sandbox',
  useDisasterRichmenu: false,

  deliveryTitleRules: [(v) => !!v || '配信名は必須入力です。'],
  talkRules: [(v) => !!v || 'トークは必須入力です。'],
  environmentRules: [(v) => !!v || '環境は必須入力です。'],

  listOfEnvironments: [
    {
      label: '本番',
      value: 'production',
    },
    {
      label: 'サンドボックス',
      value: 'sandbox',
    },
  ],

  createError: false,
  updateError: false,
})

const talksToDisplay = computed(() => {
  return talksForOutsideDistribution.value[data.value.selectedEnvironment]
})

watch(
  () => talksToDisplay.value,
  () => {
    if (data.value.selectedTalk) {
      const talk = talksToDisplay.value.find(
        (talk) => talk.dataId === data.value.selectedTalk
      )
      if (!talk) {
        data.value.selectedTalk = null
      }
    }
  }
)

const show = computed({
  get(): boolean {
    return props.visible
  },
  set(value: boolean): void {
    if (!value) {
      emits('close')
    }
  },
})

watch(
  () => show.value,
  (value) => {
    if (value) {
      if (props.item) {
        data.value.isEditData = true
        data.value.distributionConfigId = props.item.distributionConfigId
        data.value.distributionName = props.item.distributionName
        data.value.enabled = props.item.enabled
        data.value.selectedTalk = props.item.talkSettings.talkId
        data.value.selectedEnvironment = props.item.environment
        data.value.useDisasterRichmenu = props.item.talkSettings.useDisasterRichmenu ?? false
      } else {
        data.value.isEditData = false
        data.value.distributionConfigId = null
        data.value.distributionName = ''
        data.value.enabled = true
        data.value.selectedTalk = null
        data.value.selectedEnvironment = 'sandbox'
        data.value.useDisasterRichmenu = false
      }
    }
    segmentsStore.fetchScenarioTalksForTalkDistribution()
  }
)

const isEditData = computed({
  get(): boolean {
    return !!props.item
  },
  set(value: boolean): void {
    data.value.isEditData = value
  },
})

const getTalkName = (talkId: string) => {
  const talk = talksToDisplay.value.find((talk) => talk.dataId === talkId)
  return talk ? talk.name : ''
}

function onSubmit() {
  const params: any = {
    settings: {
      isDraft: false,
      distributionName: data.value.distributionName,
      distributionType: 'talk',
      targetSelectionType: 'scenario',
      talkSettings: {
        environment: data.value.selectedEnvironment,
        talkName: getTalkName(data.value.selectedTalk),
        talkId: data.value.selectedTalk,
        useDisasterRichmenu: data.value.useDisasterRichmenu ?? false,
      },
      enabled: data.value.enabled,
      mailTriggerSettings: {
        condition: {
          subjectExtractionCondition: '',
          subjectTest: '',
          bodyExtractionCondition: '',
          bodyTest: '',
        },
      },
    },
  }

  if (data.value.isEditData) {
    params.distributionConfigId = data.value.distributionConfigId
  }

  if (data.value.isEditData) {
    const editParams = {
      settings: {
        ...props.item,
        distributionName: data.value.distributionName,
        distributionType: 'talk',
        targetSelectionType: 'scenario',
        enabled: data.value.enabled,
        talkSettings: {
          environment: data.value.selectedEnvironment,
          talkName: getTalkName(data.value.selectedTalk),
          talkId: data.value.selectedTalk,
          useDisasterRichmenu: data.value.useDisasterRichmenu ?? false,
        },
      }
    }
    update(editParams)
  } else {
    create(params)
  }
}

async function create(params: any) {
  const resp = await segmentsStore.CREATE_SEGMENT_DELIVERY(params)
  if (resp) {
    show.value = false
    segmentsStore.fetchTalkConfigs()
    $q.notify('配信データを追加しました。')
  } else {
    $q.notify('配信の作成に失敗しました。')
  }
}

async function update(params: any) {
  const resp = await segmentsStore.saveConfigContent(params)
  if (resp) {
    show.value = false
    segmentsStore.fetchTalkConfigs()
    $q.notify('配信データを追加しました。')
  } else {
    $q.notify('配信の作成に失敗しました。')
  }
}
</script>
