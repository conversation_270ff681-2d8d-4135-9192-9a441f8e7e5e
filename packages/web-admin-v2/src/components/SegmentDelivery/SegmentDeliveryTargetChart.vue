<template>
  <q-card flat bordered>
    <q-toolbar class="text-primary">
      <q-toolbar-title class="tw-text-base tw-font-semibold"
        >ターゲット統計</q-toolbar-title
      >
    </q-toolbar>
    <q-separator />
    <q-card-section>
      <div class="tw-relative tw-justify-center tw-items-center">
        <div v-show="loadings['getTargetStatistics']" class="tw-absolute tw-top-1/2 tw-w-full tw-text-center tw-text-3xl tw-text-primary-600" >loading...</div>
        <div v-show="!loadings['getTargetStatistics']" class="tw-absolute tw-top-1/2 tw-w-full tw-text-center tw-text-3xl tw-text-primary-600">{{createDelivery.settings.surveyConditions.pickAll ? 100 : targetUsers}}%</div>
        <Doughnut :data="createDelivery.settings.surveyConditions.pickAll ? sendAllData : data" :options="options" />
      </div>
      <div class="tw-text-center tw-pt-4 tw-font-semibold">
         {{createDelivery.settings.surveyConditions.pickAll ? `すべての友だちが対象になります` : `約${createDelivery.usersToSendTo}人`}}
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js'
import { storeToRefs } from 'pinia'
import { computed, ref } from 'vue'
import { Doughnut } from 'vue-chartjs'
import { useSegmentsStore } from '../../stores/modules/segments';

const segmentssStore = useSegmentsStore()
const { createDelivery, loadings } = storeToRefs(segmentssStore)
const data = {
  labels: ['配信対象', '未対象'],
  datasets: [
    {
      backgroundColor: ['#41B883', '#f0f0f0'],
      data: [createDelivery.value.usersToSendTo, createDelivery.value.totalUsers],
    },
  ],
}
const sendAllData = {
  labels: ['配信対象', '未対象'],
  datasets: [
    {
      backgroundColor: ['#41B883', '#f0f0f0'],
      data: [100, 0],
    },
  ],
}
const options = {
  responsive: true,
  maintainAspectRatio: false,
  // doughnut thickness
  cutout: '80%',
}
ChartJS.register(ArcElement, Tooltip, Legend)

const chartConfig = {
  type: 'doughnut',
  data,
  options,
}

const targetUsers = computed(() => {
  if (createDelivery.value.usersToSendTo === 1 && createDelivery.value.totalUsers === 0) return 100
  return Math.round((createDelivery.value.usersToSendTo / createDelivery.value.totalUsers) * 100)
})
</script>
