<template>
  <q-dialog v-model="show">
    <q-card class="tw-w-full !tw-max-w-4xl">
      <q-form @submit="onSubmit" class="q-gutter-md">
        <q-card-section class="row items-center tw-pb-0">
          <div class="text-h6 tw-font-semibold">
            {{ isEditData ? '配信編集' : '新規作成' }}
          </div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-separator />
        <q-card-section class="scroll tw-flex tw-flex-col tw-space-y-2">
          <div>
            <div>配信名</div>
            <q-input
              v-model="data.distributionName"
              lazy-rules
              :rules="data.deliveryTitleRules"
              dense
              outlined
              placeholder="配信名"
            />
          </div>

          <div class="tw-grid tw-grid-cols-2">
            <div class="">
              <div>有効 / 無効</div>
              <div class="q-gutter-sm">
                <q-radio v-model="data.enabled" :val="true" label="有効" />
                <q-radio v-model="data.enabled" :val="false" label="無効" />
              </div>
            </div>
            <div class="">
              <div>全ての友だち</div>
              <div class="q-gutter-sm">
                <q-radio v-model="data.sendAll" :val="true" label="はい" />
                <q-radio v-model="data.sendAll" :val="false" label="いいえ" />
              </div>
            </div>
          </div>
          <div v-if="!data.sendAll">
            <div>帳票</div>
            <q-select
              v-model="data.surveyConditions.surveyId"
              lazy-rules
              :rules="data.surveyRules"
              dense
              outlined
              :placeholder="compPlaceholder"
              use-input
              :options="surveyConfigsListFiltered"
              option-value="surveyId"
              option-label="surveyTitle"
              emit-value
              map-options
              :loading="loadings['fetchSurveyConfigsList']"
              :disable="loadings['fetchSurveyConfigsList']"
              input-debounce="0"
              @filter="filterSurvey"
            />

            <div>
              <q-card flat bordered class="tw-rounded-lg">
                <q-table
                  :rows="selectedSurveyConfigSelecatbleSchema"
                  :columns="surveyTableColumns"
                  row-key="name"
                  no-data-label="データがありません"
                  class="tw-shadow-none"
                  :loading="loadings['fetchSurveyConfig']"
                  :pagination="{ rowsPerPage: 10 }"
                  top
                >
                  <template v-slot:body-cell-options="props">
                    <q-td :props="props" class="!tw-whitespace-break-spaces">
                      <q-select
                        :model-value="
                          data.surveyConditions?.conditions?.find(
                            (c) => c.itemKey === props.row.itemKey
                          )?.conditionValues || []
                        "
                        @update:model-value="onUpdateCondition(props, $event)"
                        lazy-rules
                        dense
                        outlined
                        placeholder="帳票"
                        :disable="loadings['fetchSurveyConfig']"
                        :options="props.row.options"
                        :multiple="props.row.type === 'checkboxes'"
                        class="tw-max-w-xs"
                      />
                    </q-td>
                  </template>
                </q-table>
              </q-card>
            </div>
          </div>
        </q-card-section>
          <div class="tw-px-4 tw-flex tw-flex-row tw-justify-between">
            <q-btn
              v-if="selectedSurveyConfigSelecatbleSchema.length > 0"
              flat
              color="white"
              icon="rotate_left"
              text-color="primary"
              label="配信先対象をリセット"
              @click="resetSurveyConditions"
            />
          </div>
        <q-separator />

        <q-card-actions align="right" class="tw-px-4 tw-pb-6">
          <q-btn
            outline
            label="キャンセル"
            @click="onCancel"
            color="primary"
            class="!tw-px-4"
            :loading="segLoadings['saveConfigContent']"
            v-close-popup
          />
          <q-btn
            :label="isEditData ? '保存' : '作成'"
            color="primary"
            type="submit"
            class="!tw-px-8"
            :loading="segLoadings['saveConfigContent'] || segLoadings['CREATE_SEGMENT_DELIVERY']"
          />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useQuasar } from 'quasar'
import { useSegmentsStore } from '../../stores/modules/segments'
import { useFormsStore } from '../../stores/modules/forms'
import { storeToRefs } from 'pinia'
import _ from 'lodash'

const $q = useQuasar()
const segmentsStore = useSegmentsStore()
const formsStore = useFormsStore()
const { talksForOutsideDistribution, loadings: segLoadings } = storeToRefs(segmentsStore)
const {
  surveyConfigsListTypeSorted,
  loadings,
  selectedSurveyConfig,
  selectedSurveyConfigSelecatbleSchema,
  surveyConfigsListFiltered,
} = storeToRefs(formsStore)
interface LocalState {
  valid: boolean
  isEditData: boolean
  distributionConfigId: any
  distributionName: string
  enabled: boolean
  selectedTalk: any
  selectedEnvironment: string
  useDisasterRichmenu: boolean
  deliveryTitleRules: any
  talkRules: any
  environmentRules: any
  listOfEnvironments: Array<any>
  createError: boolean
  updateError: boolean
}

const props = defineProps({
  visible: Boolean,
  close: Function,
  item: Object,
  source: String,
  getSurveyConditions: Function,
  distItem: Object,
  updateTargetEstimate: Function,
})

// この値は保存されている設定を再ロードするためにある。編集画面に入った時に設定される。
let initSurveyId;

onMounted(() => {
  if (props !== undefined && props !== null && props.item) {
    data.value.surveyConditions.surveyId = props.item?.surveyConditions?.surveyId ? props.item.surveyConditions.surveyId : null
    data.value.surveyConditions.conditions = props.item?.surveyConditions?.conditions ? props.item.surveyConditions.conditions : []
    data.value.sendAll = !props.item?.surveyConditions?.surveyId
  }
})

const surveyTableColumns = [
  {
    name: 'itemKey',
    field: 'itemKey',
    label: 'アイテムキー',
    align: 'left',
    sortable: true,
    classes: 'tw-w-0',
  },
  {
    name: 'title',
    field: 'title',
    label: 'タイトル',
    align: 'left',
    sortable: true,
    classes: 'tw-w-0',
  },
  {
    name: 'type',
    field: 'type',
    label: 'ブロック種別',
    align: 'left',
    sortable: true,
    classes: 'tw-w-0',
  },
  {
    name: 'options',
    field: 'options',
    label: '配信条件',
    align: 'left',
    sortable: false,
  },
]

const emits = defineEmits(['close'])

const data = ref({
  valid: false,

  isEditData: false,

  distributionConfigId: null,
  distributionName: '',
  enabled: true,
  sendAll: true,
  selectedSurvey: null,
  selectedEnvironment: 'sandbox',
  useDisasterRichmenu: false,

  deliveryTitleRules: [(v) => !!v || '配信名は必須入力です。'],
  surveyRules: [(v) => !!v || '帳票は必須入力です。'],
  environmentRules: [(v) => !!v || '環境は必須入力です。'],

  surveyConditions: {
    surveyId: null,
    conditions: [],
  },
  createError: false,
  updateError: false,
})

const talksToDisplay = computed(() => {
  return talksForOutsideDistribution.value[data.value.selectedEnvironment]
})

const filterSurvey = (val, update) => {
  update(() => {
    if (val === '') {
      surveyConfigsListFiltered.value = surveyConfigsListTypeSorted.value
      return surveyConfigsListTypeSorted
    }
    const needle = val.toLowerCase()
    const _filtered = surveyConfigsListTypeSorted.value.filter(
      (v) => v.surveyTitle.toLowerCase().indexOf(needle) > -1
    )
    surveyConfigsListFiltered.value = _filtered
    return _filtered
  })
}

watch(
  () => data.value.surveyConditions.surveyId,
  async (selectedSurveyId) => {
    if (selectedSurveyId) {
      // 帳票データを取得
      await formsStore.fetchSurveyConfig(selectedSurveyId)

      // 新規作成の場合は条件をリセット
      if(!data.value.isEditData) {
        data.value.surveyConditions.conditions = []
        return
      }

      // 編集の場合は、帳票が変更された場合は条件をリセット, それ以外は再ロード。
      if(selectedSurveyId != initSurveyId) {
        data.value.surveyConditions.conditions = []
      } else {
        data.value.surveyConditions.conditions = _.cloneDeep(props.item?.surveyConditions?.conditions)
      }
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

const show = computed({
  get(): boolean {
    return props.visible
  },
  set(value: boolean): void {
    if (!value) {
      emits('close')
    }
  },
})

const compPlaceholder = computed(() => {
  return data.value.surveyConditions.surveyId ? '' : '帳票'
})

watch(
  () => show.value,
  async (value) => {
    if (value) {
      if (props.item) {
        data.value.isEditData = true
        data.value.distributionConfigId = props.item.distributionConfigId
        data.value.distributionName = props.item.distributionName
        data.value.enabled = props.item.enabled
        data.value.selectedSurvey = props.item.talkId
        data.value.selectedEnvironment = props.item.environment
        data.value.useDisasterRichmenu = props.item.useDisasterRichmenu
        await formsStore.fetchSurveyConfig(props.item?.surveyConditions?.surveyId)
        data.value.surveyConditions.surveyId = props.item?.surveyConditions?.surveyId ? props.item.surveyConditions.surveyId : null
        data.value.surveyConditions.conditions = props.item?.surveyConditions?.conditions ? _.cloneDeep(props.item.surveyConditions.conditions) : []
        data.value.sendAll = !props.item?.surveyConditions?.surveyId
        initSurveyId = data.value.surveyConditions.surveyId
      } else {
        data.value.isEditData = false
        data.value.distributionConfigId = null
        data.value.distributionName = ''
        data.value.enabled = true
        data.value.selectedSurvey = null
        data.value.selectedEnvironment = 'sandbox'
        data.value.useDisasterRichmenu = false
        data.value.surveyConditions.surveyId = null
        data.value.surveyConditions.conditions = []
        data.value.sendAll = true
        formsStore.clearSelectedSurveyConfig()
      }
    }
    await formsStore.fetchSurveyConfigsList('list')
})

const isEditData = computed({
  get(): boolean {
    return !!props.item
  },
  set(value: boolean): void {
    data.value.isEditData = value
  },
})

const onUpdateCondition = (props, value) => {
  const index = data.value.surveyConditions.conditions.findIndex(
    (c) => c.itemKey === props.row.itemKey
  )
  if (index === -1) {
    data.value.surveyConditions.conditions.push({
      itemKey: props.row.itemKey,
      conditionValues: typeof (value) === 'string' ? [value] : value,
    })
  } else {
    data.value.surveyConditions.conditions[index].conditionValues = typeof (value) === 'string' ? [value] : value
    console.log('🚀 ~ onUpdateCondition ~ value:', value)
    console.log('🚀 ~ onUpdateCondition ~ props.row.itemKey:', props.row.itemKey)
  }
}

function onSubmit() {
  // reset survey conditions if sendAll is true
  // 配信先設定が「全ての友だち」の場合は条件をリセット
  if (data.value.sendAll) {
    resetSurveyConditions()
  }

  const params: any = {
    settings: {
      targetSelectionType: data.value.sendAll ? 'broadcast' : 'surveyConditions',
      enabled: data.value.enabled,
      distributionName: data.value.distributionName,
      isDraft: false,
      distributionType: 'external',
      surveyConditions: {
        surveyId: data.value.surveyConditions.surveyId
          ? data.value.surveyConditions.surveyId
          : '',
        conditions: data.value.surveyConditions.conditions
          ? data.value.surveyConditions.conditions
          : []
      },
      mailTriggerSettings: {
        condition: {
          bodyExtractionCondition: '',
          bodyTest: '',
          subjectTest: '',
          subjectExtractionCondition: '',
        },
        content: {
          bodyChangeCondition: '',
          bodyTest: '',
        },
      },
    },
  }

  if (data.value.isEditData) {
    params.distributionConfigId = data.value.distributionConfigId
    const editParams: any = {
      settings: {
        ...props.item,
        distributionName: data.value.distributionName,
        distributionType: 'external',
        targetSelectionType: data.value.sendAll ? 'broadcast' : 'surveyConditions',
        enabled: data.value.enabled,
      }
    }
    if (data.value.sendAll) {
      editParams.settings.surveyConditions.conditions = []
      editParams.settings.surveyConditions.surveyId = ""
    } else {
      editParams.settings.surveyConditions = data.value.surveyConditions
    }
    update(editParams)
  } else {
    create(params)
  }
}

const resetSurveyConditions = () => {
  data.value.surveyConditions.conditions = []
}

const onCancel = () => {
  data.value = {
    valid: false,
    isEditData: false,
    distributionConfigId: null,
    distributionName: '',
    enabled: true,
    sendAll: true,
    selectedSurvey: null,
    selectedEnvironment: 'sandbox',
    useDisasterRichmenu: false,
    deliveryTitleRules: [(v) => !!v || '配信名は必須入力です。'],
    surveyRules: [(v) => !!v || '帳票は必須入力です。'],
    environmentRules: [(v) => !!v || '環境は必須入力です。'],
    surveyConditions: {
      surveyId: null,
      conditions: [],
    },
    createError: false,
    updateError: false,
  }
  formsStore.clearSelectedSurveyConfig()
}

async function create(params: any) {
  const resp = await segmentsStore.CREATE_SEGMENT_DELIVERY(params)
  if (resp) {
    show.value = false
    segmentsStore.fetchDeliveryExternalConfigs()
    $q.notify('配信データを追加しました。')
  } else {
    $q.notify('配信の作成に失敗しました。')
  }
}

async function update(params: any) {
  const resp = await segmentsStore.saveConfigContent(params)
  if (resp) {
    show.value = false
    segmentsStore.fetchDeliveryExternalConfigs()
    $q.notify('配信データを追加しました。')
  } else {
    $q.notify('配信の作成に失敗しました。')
  }
}
</script>
