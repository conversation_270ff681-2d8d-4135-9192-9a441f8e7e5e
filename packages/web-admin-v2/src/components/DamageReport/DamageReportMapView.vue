<template>
  <div>
    <GoogleMap
      ref="mapRef"
      :keyboard-shortcuts="false"
      :fullscreen-control="false"
      region="ja-Jpan"
      language="ja-Jpan"
      :disable-default-ui="true"
      :api-key="apiKey"
      mapId="DEMO_MAP_ID"
      style="width: 100%; height: calc(100svh - 200px)"
      :center="center"
      :zoom="14"
    >
      <CustomControl position="TOP_LEFT">
        <div
          class="tw-flex tw-items-center tw-gap-4 tw-ml-2 tw-mt-2 tw-bg-white tw-rounded-full"
          style="min-width: 360px"
        >
          <q-select
            v-model="searchMapSelect"
            input-debounce="0"
            class="tw-w-full"
            placeholder="Search"
            :options="optionsReport"
            use-input
            hide-selected
            clearable
            outlined
            denseopts
            fill-input
            dense
            rounded
            @filter="filterOptionsReport"
          />
        </div>
      </CustomControl>
      <MarkerCluster>
        <AdvancedMarker
          :options="report.markerOptions"
          v-for="(report, index) of listDamageReport"
        >
          <DamageReportMapInfoWindow
            class="tw-min-w-36"
            :options="report.infoWindowOptions"
            :data="report"
          >
          </DamageReportMapInfoWindow>
        </AdvancedMarker>
      </MarkerCluster>
    </GoogleMap>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import DamageReportMapInfoWindow from './DamageReportMapInfoWindow.vue'
import {
  GoogleMap,
  AdvancedMarker,
  InfoWindow,
  MarkerCluster,
  CustomControl,
} from 'vue3-google-map'
import { useDamageReportStore } from '../../stores/modules/damage-report'
import { storeToRefs } from 'pinia'
import { useGeolocation } from '@vueuse/core'

const apiKey = import.meta.env.VITE_GOOGLE_MAP_API_KEY
const mapRef = ref<any>(null)

const reportStore = useDamageReportStore()
const { tableReportData } = storeToRefs(reportStore)

const listDamageReport = computed(() => {
  return tableReportData.value
    ?.filter((x) => !!x.location)
    ?.map((x) => {
      return {
        ...x,
        markerOptions: {
          position: {
            lat: x?.location?.lat,
            lng: x?.location?.long,
          },
        },
        infoWindowOptions: {
          // headerDisabled: true,
          headerContent:
            x.location?.address ||
            `${x?.location?.lat?.toFixed(5)},
          ${x?.location?.long?.toFixed(5)}`,
        },
      }
    })
})

// cur Location as center
const { coords, locatedAt, pause } = useGeolocation()
const center = computed(() => {
  for (const element of tableReportData.value) {
    if (element?.location) {
      pause()
      return { lat: element.location?.lat, lng: element.location?.long }
    }
  }

  if (!locatedAt.value) {
    return { lat: 38.313858, lng: 140.346908 }
  }
  pause()
  return { lat: coords.value.latitude, lng: coords.value.longitude }
})

watch(
  () => mapRef.value?.ready,
  async (ready) => {
    if (!ready) return
    // try {
    // const request = {
    //   textQuery: 'Tacos in Mountain View',
    //   fields: ['displayName', 'location', 'businessStatus'],
    //   includedType: 'restaurant',
    //   locationBias: { lat: 37.4161493, lng: -122.0812166 },
    //   isOpenNow: true,
    //   language: 'en-US',
    //   maxResultCount: 8,
    //   minRating: 3.2,
    //   region: 'us',
    //   useStrictTypeFiltering: false,
    // }
    //   const { places } = await mapRef.value.api.places.Place.searchByText(
    //     request
    //   )
    //   console.log(places)
    // } catch (error) {}
    if (tableReportData.value?.length === 0) {
      reportStore.getNextDamageReports()
    }
  }
)

// select
const searchMapSelect = ref<any>()
const optionsReport = ref<any[]>([])
watch(searchMapSelect, () => {
  if (searchMapSelect.value?.value?.location) {
    mapRef.value.map.panTo({
      lat: searchMapSelect.value?.value?.location?.lat,
      lng: searchMapSelect.value?.value?.location?.long,
    })
  }
})

function filterOptionsReport(val, update, abort) {
  update(() => {
    if (!val) {
      optionsReport.value = listDamageReport.value.map((x: any) => {
        return {
          label: `${x.subject} - ${x?.location?.lat} - ${x?.location?.long}`,
          value: x,
        }
      })
    } else {
      const needle = val.toLowerCase()
      optionsReport.value = listDamageReport.value
        .filter((v) => {
          return v.subject?.toLowerCase()?.indexOf(needle) > -1
        })
        ?.map((x: any) => {
          return {
            label: `${x.subject} - ${x?.location?.lat} - ${x?.location?.long}`,
            value: x,
          }
        })
    }
  })
}
</script>

<style>
.q-menu::-webkit-scrollbar {
  width: 0.5em;
  height: 0.5em;
}

.q-menu::-webkit-scrollbar-track {
  background: #ffffff;
  border-radius: 100vw;
}

.q-menu::-webkit-scrollbar-thumb {
  background: #bcc0c4;
  border: #bcc0c4;
  border-radius: 100vw;
}

.q-menu::-webkit-scrollbar-thumb:hover {
  background: #aaaaaa;
}
</style>
