<template>
  <q-card flat bordered class="tw-rounded-lg tw-mt-2">
    <q-table
      ref="tableRef"
      class="my-sticky-header-table"
      no-data-label="データがありません"
      :rows="tableRows"
      :columns="columnsConfig"
      row-key="id"
      :filter="tableSearchKeyword"
      :loading="loadings.queryDamageReport"
      :rows-per-page-label="t('table.ui.RecordPerPage')"
      :pagination-label="formatPaginationLabel"
      selection="multiple"
      v-model:pagination="tablePagination"
      v-model:selected="tableSelectedRows"
      table-class="table-class"
      :selected-rows-label="formatSelectedLabel"
      @request="onRequest"
    >
      <template v-slot:top-right>
        <div class="tw-flex tw-items-center tw-gap-4">
          <q-btn
            :loading="loadings.queryDamageReport"
            outline
            :disable="!isAllowDelete"
            :color="isAllowDelete ? 'negative' : 'red-3'"
            class="ml-auto"
            icon="delete"
            @click="deleteSelectedRow"
          >
            <div>
              {{ t('btn.DeleteSelected') }}
            </div>
            <q-tooltip> {{ t('tips.btn.DeleteSelected') }} </q-tooltip>
          </q-btn>
          <q-btn
            :loading="loadings.queryDamageReport"
            outline
            color="primary"
            icon="o_file_download"
            :disabled="
              !curFilterCondition?.scenarioId || totalReportDataCount === 0
            "
            @click="exportCsv"
          >
            <div>
              {{ t('btn.Export_CSV') }}
            </div>
            <q-tooltip> {{ t('tips.btn.Export_CSV') }} </q-tooltip>
          </q-btn>
          <q-btn
            :loading="loadings.queryDamageReport"
            color="primary"
            @click="goToReportDetail('NULL')"
            icon="add"
          >
            <div>
              {{ t('btn.Add') }}
            </div>
          </q-btn>
          <q-btn
            :loading="loadings.queryDamageReport"
            color="primary"
            @click="toMapView"
            icon="o_map"
          >
            <div class="max-[700px]:tw-hidden tw-ml-1">
              {{ t('btn.Map') }}
            </div>
          </q-btn>
        </div>
      </template>

      <template v-slot:body-cell-category="{ row }">
        <td>
          <div class="row" style="max-width: 150px">
            <q-chip
              class="truncate-chip-labels"
              v-if="row?.category"
              color="primary"
              text-color="white"
              dense
              clickable
              @click="clickFilter('category', row?.category)"
            >
              <div class="ellipsis">
                {{ row?.category }}
                <!-- <q-tooltip>{{ row?.category }}</q-tooltip> -->
              </div>
            </q-chip>
          </div>
        </td>
      </template>
      <template v-slot:body-cell-subject="{ row }">
        <td>
          <div class="row" style="max-width: 150px">
            <q-chip
              class="truncate-chip-labels"
              v-if="row?.subject"
              color="primary"
              text-color="white"
              dense
              clickable
              @click="clickFilter('subject', row?.subject)"
            >
              <div class="ellipsis">
                {{ row?.subject }}
                <!-- <q-tooltip>{{ row?.subject }}</q-tooltip> -->
              </div>
            </q-chip>
          </div>
        </td>
      </template>
      <template v-slot:body-cell-status="{ row }">
        <td>
          <div class="row" style="max-width: 150px">
            <q-chip
              class="truncate-chip-labels"
              v-if="row?.status"
              color="primary"
              text-color="white"
              dense
              clickable
              @click="clickFilter('status', row?.status)"
            >
              <div class="ellipsis">
                {{ t('status.' + row?.status) }}
                <!-- <q-tooltip>{{ t('status.' + row?.status) }}</q-tooltip> -->
              </div>
            </q-chip>
          </div>
        </td>
      </template>
      <template v-slot:body-cell-actions="{ row }">
        <td class="tw-w-24">
          <q-btn size="sm" color="primary" @click="goToReportDetail(row.id)">{{
            t('btn.Detail')
          }}</q-btn>
          <q-icon
            name="o_mark_chat_unread"
            class="tw-ml-2 cursor-pointer"
            size="large"
            color="warning"
            ><q-tooltip
              :offset="[10, 10]"
              class="bg-white text-primary border"
              anchor="top middle"
              self="center middle"
              >{{ t('tips.icon.unread_messages') }}</q-tooltip
            >
          </q-icon>
        </td>
      </template>
      <template v-slot:header-selection="scope">
        <q-checkbox v-model="scope.selected" />
      </template>
      <template v-slot:body-selection="scope">
        <q-checkbox v-model="scope.selected" />
      </template>
    </q-table>
  </q-card>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { saveAs } from 'file-saver'
import { useRouter } from 'vue-router'
import { useDamageReportStore } from '../../stores/modules/damage-report'
import { storeToRefs } from 'pinia'
import { useI18n } from 'vue-i18n'
import { useQuasar } from 'quasar'
import { useNotify } from '../../composables/useNotify'
import { useDate } from '../../composables/useDate'

const $q = useQuasar()
const { t, locale } = useI18n()
const router = useRouter()
const reportStore = useDamageReportStore()
const { alertRaw } = useNotify()
const { formatDateJP } = useDate()
/* Table section */
const {
  tableReportData,
  filterFormData,
  curFilterCondition,
  isShowFilterForm,
  loadings,
  totalReportDataCount,
  lastEvaluatedKey,
  isClearCacheReportData,
} = storeToRefs(reportStore)
const tableRef = ref()
const tableSearchKeyword = ref('')
const tableSelectedRows = ref<any[]>([])
const tableSelectionMode = computed(() => {
  if (curFilterCondition.value?.status === 'cancel') {
    return 'multiple'
  }
  return 'none'
})

const tablePagination = ref({
  // sortBy: 'desc',
  descending: false,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
})

const columnsConfig = computed(() => [
  {
    name: 'id',
    required: true,
    label: t('table.header.ID'),
    align: 'left',
    field: (row) => row.id,
    format: (val) => `${val}`,
    style: 'padding: 8px',
    // sortable: true,
  },
  {
    name: 'userReport',
    align: 'left',
    label: t('table.header.Username'),
    field: (row) => row.userName,
    style: 'padding: 8px',
    // sortable: true,
  },
  {
    name: 'category',
    label: t('table.header.Category'),
    field: 'category',
    align: 'left',
  },
  {
    name: 'subject',
    label: t('table.header.Subject'),
    field: 'subject',
    align: 'left',
  },
  {
    name: 'status',
    label: t('table.header.Status'),
    field: 'status',
    align: 'left',
    format: (status) => {
      return t('status.' + status)
    },
  },
  {
    name: 'createdAt',
    label: t('table.header.CreatedAt'),
    align: 'left',
    // sortable: true,
    field: 'createdAt',
    format: formatDateJP,
  },
  {
    name: 'updatedAt',
    label: t('table.header.UpdatedAt'),
    align: 'left',
    // sortable: true,
    field: 'updatedAt',
    format: formatDateJP,
  },
  {
    name: 'actions',
    label: '',
  },
])

function formatSelectedLabel() {
  return tableSelectedRows.value.length === 0
    ? ''
    : t('table.ui.RecordSelected', { number: tableSelectedRows.value.length })
}
function formatPaginationLabel(firstIndex, endIndex, total) {
  return locale.value === 'ja'
    ? `${firstIndex}-${endIndex} 件目 / ${total}件`
    : `${firstIndex}-${endIndex} of ${total}`
}

onMounted(async () => {
  await tableRef.value.requestServerInteraction() // trigger onRequest
})

// #region emulate data from server
const tableRows = ref<any[]>([])
async function fetchFromServer(startRow, count, filter, sortBy, descending) {
  // count === 0 => rowsPerPage all
  if (
    isClearCacheReportData.value ||
    count === 0 ||
    startRow + count > tableReportData.value.length
  ) {
    // side-effect query and store data in pinia as cache
    await reportStore.getNextDamageReports(startRow, count)
    isClearCacheReportData.value = false
  }
  tablePagination.value.rowsNumber = totalReportDataCount.value
  const endRow = count === 0 ? totalReportDataCount.value : startRow + count
  return tableReportData.value.slice(startRow, endRow)
}
async function onRequest(props) {
  const { page, rowsPerPage, sortBy, descending } = props.pagination
  const filter = props.filter

  const startRow = (page - 1) * rowsPerPage
  const returnedData = await fetchFromServer(
    startRow,
    rowsPerPage, // get all rows if "All" (0) is selected
    filter,
    sortBy,
    descending
  )

  // clear out existing data and add new
  tableRows.value.splice(0, tableRows.value.length, ...returnedData)

  // local pagination object
  tablePagination.value.page = page
  tablePagination.value.rowsPerPage = rowsPerPage
  tablePagination.value.descending = descending
  // tablePagination.value.sortBy = sortBy
}
// #endregion
// table action go to page detail
function goToReportDetail(id?: string) {
  router.push({
    name: 'damage-report-detail',
    params: {
      id: id ?? 'NULL',
    },
  })
}
function toMapView() {
  router.push({
    name: 'damage-report-mapview',
  })
}

const isAllowDelete = computed(() => {
  return (
    tableSelectedRows.value.length > 0 &&
    tableSelectedRows.value.every((x) => x.status === 'cancel')
  )
})
async function deleteSelectedRow() {
  $q.dialog({
    title: t('dialog.DeleteRow.title'),
    message: t(
      'dialog.DeleteRow.message',
      [tableSelectedRows.value?.length],
      tableSelectedRows.value?.length
    ),
    cancel: {
      flat: true,
      label: t('dialog.DeleteRow.cancel'),
    },
    ok: {
      pushed: true,
      color: 'negative',
      label: t('dialog.DeleteRow.confirm'),
    },
    persistent: true,
  })
    .onOk(async () => {
      if (tableSelectedRows.value.length > 0) {
        let promises: any[] = []
        for (const row of tableSelectedRows.value) {
          if (row.id) {
            promises.push(reportStore.deleteDamageReportsByID(row.id))
          }
        }
        const results = await Promise.allSettled(promises)
        for (const res of results) {
          if (res.status === 'rejected') {
            alertRaw(t(`errors.${res.reason?.code}`))
          }
        }
        setTimeout(async () => {
          tableSelectedRows.value = []
          tableReportData.value = []
          lastEvaluatedKey.value = null
          await tableRef.value.requestServerInteraction() //trigger onRequest
        }, 2000)
      }
    })
    .onCancel(() => {})
}
function clickFilter(type, value) {
  filterFormData.value[type] = value
  isShowFilterForm.value = true
}
async function exportCsv() {
  const res = await reportStore.exportReportToCSV()
  if (res?.url) {
  }
  try {
    saveAs(res?.url, res?.fileName)
  } catch (error) {}
}

async function tableRequest() {
  await tableRef.value.requestServerInteraction() //trigger onRequest
}

watch(
  tableReportData,
  (oldVal, newVal) => {
    // reset selected when filter condition change
    tableSelectedRows.value = []
    tablePagination.value['page'] = 1
  },
  { deep: true }
)

defineExpose({
  tableRequest, //expose to run on filter form submitted
})
</script>

<!-- Sticky header -->
<style lang="sass">
.my-sticky-header-table
  /* height or max-height is important */
  max-height: calc(100svh - 150px)

  .q-table__top,
  .q-table__bottom,
  thead tr:first-child th
    /* bg color is important for th; just specify one */
    background-color: #fff

  thead tr th
    position: sticky
    z-index: 1
  thead tr:first-child th
    top: 0

  /* this is when the loading indicator appears */
  &.q-table--loading thead tr:last-child th
    /* height of all previous header rows */
    top: 55px

  /* prevent scrolling behind sticky top row on focus */
  tbody
    /* height of all previous header rows */
    scroll-margin-top: 55px
</style>

<!-- scrollbar -->
<style>
.table-class.q-table__middle::-webkit-scrollbar {
  width: 0.5em;
  height: 0.5em;
}

.table-class.q-table__middle::-webkit-scrollbar-track {
  background: #ffffff;
  border-radius: 100vw;
  /* sticky header margin */
  margin-top: 55px;
}

.table-class.q-table__middle::-webkit-scrollbar-thumb {
  background: #bcc0c4;
  border: #bcc0c4;
  border-radius: 100vw;
}

.table-class.q-table__middle::-webkit-scrollbar-thumb:hover {
  background: #aaaaaa;
}
.table-class > table > thead > tr > th {
  padding: 8px;
}
.table-class > table > tbody > tr > td {
  padding: 8px;
}
</style>
