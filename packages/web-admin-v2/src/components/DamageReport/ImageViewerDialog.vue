<template>
  <div class="q-pa-md q-gutter-sm">
    <q-dialog
      v-model="dialog"
      :maximized="false"
      transition-show="slide-up"
      transition-hide="slide-down"
    >
      <q-card
        class="tw-grid tw-w-full tw-h-full"
        style="grid-template-rows: max-content 1fr; max-width: 1280px"
      >
        <div
          class="tw-flex tw-items-center tw-justify-end tw-gap-2 tw-bg-gray-100 tw-p-1 tw-border-b"
        >
          <q-btn
            dense
            class="tw-bg-gray-100 tw-font-semibold"
            round
            icon="close"
            @click="dialog = !dialog"
          >
          </q-btn>
        </div>
        <div>
          <q-carousel
            v-model="imagesSlide"
            v-model:fullscreen="imagesSlideFullscreen"
            class="tw-w-full tw-h-full"
            control-color="primary"
            control-type="flat"
            dense
            animated
            arrows
            navigation
            infinite
            swipeable
          >
            <q-carousel-slide
              v-for="(img, index) of props.images"
              :key="img.url"
              class="tw-bg-contain tw-bg-no-repeat"
              :name="index"
              :img-src="img.url"
              @click="imagesSlideFullscreen = !imagesSlideFullscreen"
            />
          </q-carousel>
        </div>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const dialog = defineModel<boolean>()
const props = withDefaults(
  defineProps<{
    images: any[]
  }>(),
  {
    images: () => [],
  }
)
const imagesSlide = ref(0)
const imagesSlideFullscreen = ref(false)
</script>
