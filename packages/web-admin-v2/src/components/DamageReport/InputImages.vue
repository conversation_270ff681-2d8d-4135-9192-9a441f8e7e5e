<template>
  <div class="tw-w-full">
    <q-file
      class="tw-w-full"
      multiple
      dense
      borderless
      :label="t('form.label.upload')"
      accept=".jpg, image/*"
      :max-file-size="FILE_UPLOAD_SIZE_LIMIT"
      :loading="props.loading"
      v-model="imagesUpload"
      @update:model-value="changeImages"
    >
      <template v-slot:prepend>
        <q-icon name="attach_file" size="sm" />
      </template>
    </q-file>
    <q-carousel
      v-if="Array.isArray(props.images) && props.images?.length > 0"
      v-model="curImagesIndex"
      v-model:fullscreen="carouselFullscreen"
      height="150px"
      class="tw-w-full"
      control-color="primary"
      control-type="flat"
      animated
      arrows
      navigation
      infinite
      padding
      swipeable
    >
      <q-carousel-slide
        v-for="(img, index) of props.images"
        :key="img.url"
        class="tw-bg-contain tw-bg-no-repeat"
        :name="index"
        :img-src="img.url"
        @click="carouselFullscreen = !carouselFullscreen"
      />
      <template v-slot:control>
        <q-carousel-control
          position="top-right"
          :offset="[8, 8]"
          class="text-white rounded-borders"
          style="padding: 0px 8px"
        >
          <q-btn
            color="negative"
            round
            dense
            flat
            style="
              backdrop-filter: blur(4px);
              background: rgba(255, 255, 255, 0.4);
            "
            icon="delete"
            @click="deleteSelectedImages"
          >
          </q-btn>
        </q-carousel-control>
      </template>
    </q-carousel>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

const FILE_UPLOAD_SIZE_LIMIT = 10 * 1024 * 1024 // 10MB
const curImagesIndex = ref(0) // index
const carouselFullscreen = ref(false)
const imagesUpload = ref([])
const { t } = useI18n()

const props = withDefaults(
  defineProps<{
    images: any
    loading: boolean
  }>(),
  {
    images: {},
    loading: false,
  }
)

const emit = defineEmits(['deleteImage', 'change'])

async function changeImages(files) {
  emit('change', files)
  imagesUpload.value = []
}
function deleteSelectedImages() {
  emit('deleteImage', {
    index: curImagesIndex.value,
  })
  curImagesIndex.value =
    curImagesIndex.value - 1 < 0 ? 0 : curImagesIndex.value - 1
}
</script>
