<script setup lang="ts">
import { saveAs } from 'file-saver'
import { onBeforeUnmount, onMounted, ref } from 'vue'
import { register } from 'vue-advanced-chat'
import { ChatMessage, ChatRoom, ChatUser } from '../../types/chat'
import { useNotify } from '../../composables/useNotify'
import { useChat } from '../../composables/useChat'
import {
  CHAT_ACTIONS,
  CHAT_EVENTS,
  CHAT_ROLES,
} from '../../constants/chat.constants'
import WebsocketService from '../../services/websocket.service'
import { generateUUID } from '../../utils/uuidUtils'

const socket = new WebsocketService()
const socketLoaded = ref(false)
socket
  .initWebSocket()
  .then(() => {
    socketLoaded.value = true
  })
  .catch((err) => {
    console.error(err)
    alertError('WS_INIT_ERROR')
    socketLoaded.value = false
  })
const props = withDefaults(
  defineProps<{
    rooms: ChatRoom[]
    user: ChatUser
  }>(),
  {}
)
const chatWindow = ref()
const { alertError, alertUnknownError } = useNotify()
const {
  CHAT_UI_MESSAGE,
  CHAT_UI_ACTION_MESSAGE,
  convertWebSocketMessageToChatMessage,
  convertAPIMessagesToChatMessage,
  queryMessageFromAPI,
  updateMessageUnread,
  queryChatRoom,
  createNewRoom,
  processMessageFilesToS3,
  formatStringToJapanDateWithoutTime,
} = useChat()

const messages = ref<ChatMessage[]>([])
const messagesLoaded = ref(false)
const lastMessageFetchKey = ref()
const managerLatestTimeRead = ref(0)
const isSendingAction = ref(false)
const isRoomNotExist = ref(false)
const defaultTextareaValue = ref('') // add a default textarea value
const messageReset = ref('')

function typingMessage(event) {
  if (!event.message && isSendingAction.value === true) {
    defaultTextareaValue.value = ' '
    isSendingAction.value = false
  } else if (!event.message) {
    defaultTextareaValue.value = messageReset.value
  } else {
    messageReset.value = event.message
  }
}

async function sendMessage({ content, files, replyMessage, roomId }) {
  try {
    isSendingAction.value = true
    if (content && content.length > 5000) {
      alertError('chat.err.MAX_LENGTH_MESSAGE')
      if (!defaultTextareaValue.value) {
        defaultTextareaValue.value = content
      }
      defaultTextareaValue.value += ' '
      messageReset.value = defaultTextareaValue.value
      isSendingAction.value = false
      return
    }
    if (isRoomNotExist.value) {
      await createNewRoom(roomId)
      isRoomNotExist.value = false
    }

    const messageSent: any = {
      _id: generateUUID(),
      content,
    }
    if (files) {
      const { uploadSuccess, uploadsResult } = await processMessageFilesToS3(
        files
      )
      if (!uploadSuccess) {
        return
      }
      messageSent.files = uploadsResult
    }
    if (replyMessage) {
      messageSent.replyMessage = {
        content: replyMessage.content,
        senderId: replyMessage.senderId,
      }
      if (replyMessage.files) {
        messageSent.replyMessage.files = replyMessage?.files
      }
    }

    socket?.sendMessage({
      action: CHAT_ACTIONS.SEND,
      body: {
        event_name: CHAT_EVENTS.SEND_MESSAGE,
        payload: {
          ...messageSent,
          gid: props.rooms[0].roomId,
          timestamp: new Date().getTime(),
          sender: {
            id: props.user._id,
            name: props.user.username,
            role: CHAT_ROLES.MANAGER,
          },
        },
      },
    })
    messageReset.value = ' '
  } catch (error) {
    alertError(error.message)
  }
}

// Room first load (options.reset true) or load older messages of a conversation when the user scroll on top
async function fetchMessages({ room, options }) {
  if (!props.user._id) {
    return
  }
  messagesLoaded.value = false
  try {
    const { items, nextEvaluatedKey } = await queryMessageFromAPI(
      room.roomId,
      lastMessageFetchKey.value
    )
    lastMessageFetchKey.value = nextEvaluatedKey ?? null
    if (Array.isArray(items) || items?.length >= 0) {
      // first item is last recent message
      messages.value = [
        ...items
          .map((msg) =>
            convertAPIMessagesToChatMessage(msg, managerLatestTimeRead.value)
          )
          .reverse(),
        ...messages.value,
      ]
      if (items?.length && options?.reset) {
        await updateMessageUnread(room.roomId, props.user._id)
      }
    }
    if (!nextEvaluatedKey || items.length === 0) {
      messagesLoaded.value = true
    } else {
      messagesLoaded.value = false
    }
  } catch (error) {
    alertError(error.message)
  }
}

// Clicked to view or download a file
function openFile({ message, file }) {
  saveAs(file.file.url, file.file.file_name)
}

async function listenNewMessageFromWebsocket(message: any) {
  messages.value.push(convertWebSocketMessageToChatMessage(message))
  await updateMessageUnread(props.rooms[0].roomId, props.user._id)
}

function updateRealtimeManagerReadMessage(message) {
  messages.value = messages.value.map((item) => {
    item.seen = true
    return item
  })
}

async function initChat() {
  try {
    ;({
      isNewRoom: isRoomNotExist.value,
      roomManagerLastReadTime: managerLatestTimeRead.value,
    } = await queryChatRoom(props.rooms[0].roomId, props.user._id))

    socket?.sendMessage({
      action: CHAT_ACTIONS.PING,
      body: {
        event_name: CHAT_EVENTS.JOIN_GROUP,
        payload: {
          gid: props.rooms[0].roomId,
          sender: {
            id: props.user._id,
            name: props.user.username,
            role: CHAT_ROLES.MANAGER,
          },
        },
      },
    })
  } catch (error) {
    alertUnknownError()
  }
}
initChat().then(() => {
  setTimeout(() => {
    register() // register custom element chat
    const style = document.createElement('style')
    style.innerHTML =
      '.vac-room-header{ display:none } .vac-container-scroll{ margin-top: 0 !important} ::-webkit-scrollbar {  width: 0.5em;  height: 0.5em;} ::-webkit-scrollbar-track {   background: #ffffff;   border-radius: 100vw; }  ::-webkit-scrollbar-thumb {   background: #bcc0c4;   border: #bcc0c4;   border-radius: 100vw; }  ::-webkit-scrollbar-thumb:hover {   background: #aaaaaa; } .vac-reply-box { max-height: 200px; overflow: auto !important}'
    chatWindow.value.shadowRoot.appendChild(style)
    socket?.on(CHAT_EVENTS.SEND_MESSAGE, listenNewMessageFromWebsocket)
    socket?.on(CHAT_EVENTS.READ_MESSAGE, updateRealtimeManagerReadMessage)
  }, 0)
})

onBeforeUnmount(() => {
  socket?.close()
})
</script>

<template>
  <vue-advanced-chat
    v-if="user._id"
    ref="chatWindow"
    style="max-height: 100%; min-height: 100%"
    :current-user-id="user._id"
    responsive-breakpoint="400"
    :message-actions="JSON.stringify(CHAT_UI_ACTION_MESSAGE)"
    :text-messages="JSON.stringify(CHAT_UI_MESSAGE)"
    show-audio="false"
    show-new-messages-divider="true"
    show-reaction-emojis="false"
    media-preview-enabled="false"
    user-tags-enabled="false"
    accepted-files="image/png, image/jpeg, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.openxmlformats-officedocument.wordprocessingml.document,  application/msword , application/pdf, text/plain"
    :rooms="JSON.stringify(props.rooms)"
    :single-room="true"
    :rooms-loaded="true"
    :rooms-list-opened="false"
    show-add-room="false"
    :messages="JSON.stringify(messages)"
    :messages-loaded="messagesLoaded"
    :room-message="defaultTextareaValue"
    @typing-message="typingMessage($event.detail[0])"
    @send-message="sendMessage($event.detail[0])"
    @fetch-messages="fetchMessages($event.detail[0])"
    @open-file="openFile($event.detail[0])"
  />
  <div v-else>Unknown error</div>
</template>
