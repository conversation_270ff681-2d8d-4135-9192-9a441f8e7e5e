<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { AdvancedMarker, GoogleMap, InfoWindow } from 'vue3-google-map'
import DamageReportMapInfoWindow from './DamageReportMapInfoWindow.vue'

const props = withDefaults(
  defineProps<{
    reportData: any
    mode: 'view' | 'edit'
  }>(),
  {
    reportData: undefined,
    mode: 'view',
  }
)

const emit = defineEmits(['changeLocation'])
const { t } = useI18n()
const dialog = defineModel<boolean>()

// default tokyo
const TOKYO_COORDINATES = {
  lat: 35.6684103,
  lng: 139.5760601,
}
const center = ref(
  props.reportData?.location?.lat && props.reportData?.location?.long
    ? {
        lat: props.reportData?.location?.lat,
        lng: props.reportData?.location?.long,
      }
    : TOKYO_COORDINATES
)
const mapRef = ref()
const markerOptions = computed(() => {
  return {
    position:
      props.reportData?.location?.lat && props.reportData?.location?.long
        ? {
            lat: props.reportData?.location?.lat,
            lng: props.reportData?.location?.long,
          }
        : TOKYO_COORDINATES,
  }
})
const infoWindowOptions = {
  headerContent:
    props.reportData?.location?.address ||
    `${props.reportData?.location?.lat?.toFixed(5)},
    ${props.reportData?.location?.long?.toFixed(5)}`,
}
watch(
  () => mapRef.value?.ready,
  async (ready) => {
    if (!ready) return
    // geocoder.value = new mapRef.value.api.Geocoder()
    if (props.mode === 'edit') {
      mapRef.value.map?.addListener('click', onMapClick)
    }
  }
)
const renderCurMarker = ref(0)

// catch click event to set marker location
function onMapClick(mapsMouseEvent) {
  markerOptions.value.position = mapsMouseEvent.latLng.toJSON()
  // required GoogleMap geocoding api
  // geocoder.value
  //   .geocode({ location: mapsMouseEvent.latLng.toJSON() })
  //   .then((response) => {
  //     if (response.results[0]) {
  //       console.log('geocoder', response.results[0].formatted_address)
  //     } else {
  //       window.alert('No results found')
  //     }
  //   })
  //   .catch((e) => window.alert('Geocoder failed due to: ' + e))
  renderCurMarker.value++
}
function confirmPickLocation() {
  const newLocation = {
    lat: markerOptions.value.position.lat,
    long: markerOptions.value.position.lng,
    address: `${markerOptions.value.position.lat.toFixed(
      5
    )}, ${markerOptions.value.position.lng.toFixed(5)}`, // need geocoding api
  }
  emit('changeLocation', newLocation)
}

const apiKey = import.meta.env.VITE_GOOGLE_MAP_API_KEY
</script>

<template>
  <div class="q-pa-md q-gutter-sm">
    <q-dialog
      v-model="dialog"
      :maximized="false"
      transition-show="slide-up"
      transition-hide="slide-down"
    >
      <q-card
        class="tw-grid grid-rows-[max-content_1fr] tw-w-full tw-h-full"
        style="grid-template-rows: max-content 1fr; max-width: 1280px"
      >
        <div
          class="tw-flex tw-items-center tw-justify-end tw-gap-2 tw-bg-gray-100 tw-p-1 tw-border-b"
        >
          <q-btn
            v-if="mode === 'edit'"
            color="primary"
            rounded
            class="font-semibold"
            @click="confirmPickLocation"
          >
            {{ t('btn.Confirm') }}
          </q-btn>
          <q-btn
            dense
            class="tw-bg-gray-100 tw-font-semibold"
            round
            icon="close"
            @click="dialog = !dialog"
          >
          </q-btn>
        </div>
        <div>
          <GoogleMap
            ref="mapRef"
            :keyboard-shortcuts="false"
            :fullscreen-control="false"
            region="ja-Jpan"
            language="ja-Jpan"
            :disable-default-ui="true"
            :api-key="apiKey"
            map-id="DEMO_MAP_ID"
            style="width: 100%; height: 100%"
            :center="center"
            :zoom="14"
          >
            <AdvancedMarker :key="renderCurMarker" :options="markerOptions">
              <DamageReportMapInfoWindow
                class="tw-min-w-36"
                :options="infoWindowOptions"
                :data="reportData"
              >
              </DamageReportMapInfoWindow>
            </AdvancedMarker>
          </GoogleMap>
        </div>
      </q-card>
    </q-dialog>
  </div>
</template>

<style scoped>
.grid-rows-\[max-content_1fr\] {
  grid-template-rows: max-content 1fr;
}
.parent {
  display: grid;
  gap: 0.5rem;
  margin: 0.5rem 0;
  grid-template-columns: auto 1fr;
}
.input-group {
  display: grid;
  grid-column: 1/-1;
  grid-template-columns: subgrid;
  justify-items: start;
  align-items: start;
}
</style>

<style>
.gm-style-iw-ch {
  font-weight: 600 !important;
}
</style>
