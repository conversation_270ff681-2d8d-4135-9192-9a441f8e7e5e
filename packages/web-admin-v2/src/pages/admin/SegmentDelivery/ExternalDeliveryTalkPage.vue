<template>
  <div>
    <div>
      <div
        class="tw-w-full tw-pl-6 tw-pr-5 tw-py-4 tw-flex tw-flex-row tw-items-center tw-justify-between"
      >
        <div class="text-h4 flex flex-inline items-baseline">
          {{ externalDeliveryConfig.talkDelivery.filteredTalkData.length
          }}<small class="tw-text-sm">件</small>
        </div>
        <div>
          <div class="tw-flex tw-flex-inline tw-items-center tw-space-x-2">
            <div>
              <q-btn
                @click="segmentsStore.fetchTalkConfigs()"
                size="md"
                class="tw-ml-2"
                color="primary"
                label="データ更新"
                icon="refresh"
                push
                :loading="loadings['fetchTalkConfigs']"
              />
            </div>
            <q-separator vertical inset />
            <q-btn
              push
              icon="filter_list"
              label="条件検索"
              @click="showDetailSearch = !showDetailSearch"
            />
          </div>
        </div>
      </div>
      <q-separator />
      <template v-if="showDetailSearch">
        <q-card-section class="tw-px-10 tw-space-y-6 tw-bg-gray-50">
          <div class="tw-flex tw-flex-inline tw-items-center tw-space-x-4">
            <div class="tw-w-24 tw-mr-4 text-right">配信名</div>
            <div class="tw-flex-1">
              <q-input
                dense
                hide-bottom-space
                filled
                v-model="
                  externalDeliveryConfig.talkDelivery.filterCondition
                    .distributionName
                "
              />
            </div>
          </div>

          <div class="tw-flex tw-flex-inline tw-items-center tw-space-x-4">
            <div class="tw-w-24 tw-mr-4 text-right">トーク名</div>
            <q-select
              class="tw-flex-1"
              v-model="
                externalDeliveryConfig.talkDelivery.filterCondition.talkName
              "
              lazy-rules
              dense
              outlined
              placeholder="トーク名"
              :options="outsideTalks"
              option-value="name"
              option-label="name"
              emit-value
              map-options
              :loading="loadings['fetchScenarioTalksForTalkDistribution']"
              :disabled="loadings['fetchScenarioTalksForTalkDistribution']"
            />
          </div>

          <div class="tw-flex tw-flex-inline tw-items-center tw-space-x-4">
            <div class="tw-w-24 tw-mr-4 text-right">有効/無効</div>
            <div>
              <div class="q-gutter-sm">
                <q-radio
                  v-model="
                    externalDeliveryConfig.talkDelivery.filterCondition.enabled
                  "
                  :val="true"
                  label="有効"
                />
                <q-radio
                  v-model="
                    externalDeliveryConfig.talkDelivery.filterCondition.enabled
                  "
                  :val="false"
                  label="無効"
                />
              </div>
            </div>
          </div>

          <q-card-actions
            class="tw-w-full tw-justify-center tw-py-4 tw-flex tw-space-x-4"
          >
            <q-btn
              @click="segmentsStore.filterTalkDeliveries()"
              size="md"
              class="tw-w-44"
              color="primary"
              label="検索"
              icon="filter_alt"
              rounded
              push
            />
            <q-btn
              @click="segmentsStore.filterTalkDeliveries(true)"
              size="md"
              class="tw-w-44"
              label="リセット"
              icon="restart_alt"
              rounded
              push
            />
          </q-card-actions>
        </q-card-section>

        <q-separator />
      </template>
      <q-table
        :rows="externalDeliveryConfig.talkDelivery.filteredTalkData"
        :columns="externalDeliveryConfig.talkDelivery.tableColumns"
        row-key="name"
        no-data-label="データがありません"
        class="tw-shadow-none"
        :loading="loadings['fetchTalkConfigs']"
        :pagination="{ rowsPerPage: 10 }"
        top
      >
      <template v-slot:body-cell-distributionName="props">
          <q-td :props="props">
            <a :style="{ cursor: 'pointer' }" @click="onClickDeliveryName(props.row)">
              {{ props.row.distributionName }}
            </a>
          </q-td>
        </template>
        <template v-slot:body-cell-enabled="props">
          <q-td :props="props">
            <div :style="{ color: props.row.enabled ? 'green' : 'red' }">
              {{ props.row.enabled ? '有効' : '無効' }}
            </div>
          </q-td>
        </template>
        <template v-slot:body-cell-distributionCondition="props">
          <q-td :props="props">
            <router-link :to="`/segment-delivery/condition/${props.row.id}?type=talk`">
              <q-btn class="bg-primary text-white">設定</q-btn>
            </router-link>
          </q-td>
        </template>
        <template v-slot:body-cell-environment="props">
          <q-td :props="props">
            <div>
              {{
                props.row.environment === 'sandbox' ? 'サンドボックス' : '本番'
              }}
            </div>
          </q-td>
        </template>
      </q-table>
    </div>
  </div>
  <SegmentDeliveryTalkCreation
    :visible="showTalkModal"
    :item="editTalkConfig"
    @close="closeTalkCreation()"
  />
  <SegmentDeliveryMailCreation
    :visible="showMailModal"
    @close="showMailModal = false"
  />
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { useSegmentsStore } from '../../../stores/modules/segments'
import { storeToRefs } from 'pinia'
import { RouterLink, useRouter } from 'vue-router'
import SegmentDeliveryTalkCreation from '../../../components/SegmentDelivery/SegmentDeliveryTalkCreation.vue'
import SegmentDeliveryMailCreation from '../../../components/SegmentDelivery/SegmentDeliveryMailCreation.vue'
import { TalkConfig } from '../../../types/index'
const router = useRouter()

const segmentsStore = useSegmentsStore()
const { loadings, externalDeliveryConfig, talksForOutsideDistribution } =
  storeToRefs(segmentsStore)

const editTalkConfig = ref<TalkConfig | null>(null)
const onClickDeliveryName = (talkRow: TalkConfig) => {
  editTalkConfig.value = talkRow
  showTalkModal.value = true
}
const closeTalkCreation = () => {
  showTalkModal.value = false
  editTalkConfig.value = null
}

onMounted(async () => {
  await segmentsStore.fetchTalkConfigs()
  await segmentsStore.fetchScenarioTalksForTalkDistribution()
})
const showDetailSearch = ref(true)

const outsideTalks = computed(() => {
  return [
    ...talksForOutsideDistribution.value.production,
    ...talksForOutsideDistribution.value.sandbox,
  ]
})

const search = async () => {
  await segmentsStore.fetchDeliveryExternalConfigs()
  segmentsStore.filterDeliveryHistories()
}

const showTalkModal = ref(false)
const onCreateTalk = () => {
  showTalkModal.value = true
}

const showMailModal = ref(false)
const onCreateMail = () => {
  showMailModal.value = true
}
</script>
