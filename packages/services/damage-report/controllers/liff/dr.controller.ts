import { plainToInstance } from "class-transformer";
import { QueryDamageReportDto } from "damage-report/dto/query-damage-report.dto";
import { UpdateDamageReportLiffDto } from "damage-report/dto/update-damage-report.dto";
import { <PERSON>Helper } from "damage-report/helpers/dynamo.helper";
import { DamageReportLiffService } from "damage-report/services/damage-report-liff.service";
import type { Request, Response } from "lambda-api";
import { API } from "lambda-api"
import { requestContext } from "../../../common/liff-api/execution-context";

class Handler {
    constructor(public damageReportService: DamageReportLiffService) { }

    async queryDamageReport(req: Request, res: Response) {
        const payload = !req.body ? new QueryDamageReportDto() : plainToInstance(QueryDamageReportDto, req.body);
        payload.uid = requestContext.userId;

        const result = await this.damageReportService.queryByCreatedTimeLiff(payload);
        return res.json(result)
    }
    async queryDamageReportPublished(req: Request, res: Response) {
        const result = await this.damageReportService.queryDRPublished();
        return res.json(result)
    }
    async getDamageReport(req: Request, res: Response) {
        const result = await this.damageReportService.getDamageReportLiff(req.params.id, requestContext.userId);
        return res.json(result)
    }
    async updateDamageReport(req: Request, res: Response) {
        const id = req.params.id;
        const payload = plainToInstance(UpdateDamageReportLiffDto, req.body);

        if (!id && !payload.id) throw new Error("IDがありません");
        payload.id = id;
        payload.uid = requestContext.userId;

        const result = await this.damageReportService.updateDamageReport(payload);
        return res.json(result)
    }
}

export function damageReportLiffRoutersFactory(helper: { dynamo: DynamoHelper }) {
    const damageReportService = new DamageReportLiffService(helper.dynamo);
    const handler = new Handler(damageReportService);

    return (api: API) => {
        api.post('/query', handler.queryDamageReport.bind(handler));
        api.get('/query-published', handler.queryDamageReportPublished.bind(handler));
        api.put('/:id', handler.updateDamageReport.bind(handler));
        api.get('/:id', handler.getDamageReport.bind(handler));
    }
}