import { plainToInstance } from "class-transformer";
import { CreateDRTalkVersionDto } from "damage-report/dto/scenario/create-dr-talk-version.dto";
import { DeleteDRTalkVersionDto } from "damage-report/dto/scenario/delete-dr-talk-version.dto";
import { DeleteScenarioDto } from "damage-report/dto/scenario/delete-scenario.dto";
import { SaveDrTalkDto } from "damage-report/dto/scenario/save-dr-talk.dto";
import { UpdateDRTalkActiveDto } from "damage-report/dto/scenario/update-dr-talk.dto";
import { DynamoHelper } from "damage-report/helpers/dynamo.helper";
import { ScenarioService } from "damage-report/services/scenario.service";
import type { Request, Response } from "lambda-api";
import { API } from "lambda-api"

class Handler {
    constructor(public scenarioService: ScenarioService) { }
    async createDRScenarioVersion(req: Request, res: Response) {
        const payload = plainToInstance(CreateDRTalkVersionDto, req.body);
        const result = await this.scenarioService.createDRScenarioVersion(payload);
        return res.json(result)
    }
    async saveDRScenarioVersion(req: Request, res: Response) {
        const payload = plainToInstance(SaveDrTalkDto, req.body);
        const result = await this.scenarioService.saveTalk(payload);
        return res.json(result)
    }
    async setDRTalkVersionActive(req: Request, res: Response) {
        const payload = plainToInstance(UpdateDRTalkActiveDto, req.body);
        const result = await this.scenarioService.setDRTalkVersionActive(payload);
        return res.json(result)
    }
    async getAllScenarioDataOfDR(req: Request, res: Response) {
        const scenario = req.params.scenario;
        const dataId = req.query.dataId;
        const result = await this.scenarioService.getAllMessageOfDR(scenario, dataId);
        return res.json(result)
    }
    async deleteTalkDRVersion(req: Request, res: Response) {
        const payload = plainToInstance(DeleteDRTalkVersionDto, req.body);
        const result = await this.scenarioService.deleteTalkDRVersion(payload);
        return res.json(result)
    }
}
class HandlerScenario {
    constructor(public scenarioService: ScenarioService) { }

    async deleteScenario(req: Request, res: Response) {
        const payload = plainToInstance(DeleteScenarioDto, req.body);
        const result = await this.scenarioService.deleteScenario(payload);
        return res.json(result)
    }
}

export function talkRoutersFactory(helper: { dynamo: DynamoHelper }) {
    const scenarioService = new ScenarioService(helper.dynamo);
    const handler = new Handler(scenarioService);

    return (app: API) => {
        app.post('/create-talk-version', handler.createDRScenarioVersion.bind(handler));
        app.post('/set-talk-active', handler.setDRTalkVersionActive.bind(handler));
        app.post('/save-talk', handler.saveDRScenarioVersion.bind(handler));
        app.get('/talk-messages/:scenario', handler.getAllScenarioDataOfDR.bind(handler));
        app.delete('/talk-version', handler.deleteTalkDRVersion.bind(handler));
    }
}

export function scenarioVersionRoutersFactory(helper: { dynamo: DynamoHelper }) {
    const scenarioService = new ScenarioService(helper.dynamo);
    const handler = new HandlerScenario(scenarioService);

    return (app: API) => {
        app.delete('/', handler.deleteScenario.bind(handler));
    }
}
