import { plainToInstance } from "class-transformer";
import { validateSync } from "class-validator";
import { CreatePresignedLinkUploadDto } from "damage-report/dto/create-presigned-link-upload";
import { GetFileSignedUrlDto } from "damage-report/dto/get-file-signed.dto";
import { PreSignedChatDto } from "damage-report/dto/pre-signed-chat.dto";
import { GetTalkDRRelationshipDto } from "damage-report/dto/scenario/get-talk-dr-relationship.dto";
import { DynamoHelper } from "damage-report/helpers/dynamo.helper";
import { UtilsService } from "damage-report/services/utils.service";
import type { Request, Response } from "lambda-api";
import { API } from "lambda-api"

class HandlerUtils {
    utilsService: UtilsService;
    constructor(public dynamoHelper: DynamoHelper) { this.utilsService = new UtilsService(dynamoHelper) }
    async presignedLinkUpload(req: Request, res: Response) {
        const body = typeof req.body == 'string' ? JSON.parse(req.body) : req.body;
        if (!Array.isArray(body)) return res.json([]);

        const payloadsValidate = body.map(x => {
            const payload = plainToInstance(CreatePresignedLinkUploadDto, x);
            const errs = validateSync(payload);
            return errs.length == 0 ? payload : null
        }).filter(Boolean);

        const result = await this.utilsService.presignedLinkUpload(payloadsValidate)
        return res.json(result)
    }
    async getFileSignedUrl(req: Request, res: Response) {
        const body = typeof req.body == 'string' ? JSON.parse(req.body) : req.body;
        if (!Array.isArray(body)) return res.json([]);

        const payloadsValidate = body.map(x => {
            const payload = plainToInstance(GetFileSignedUrlDto, x);
            const errs = validateSync(payload);
            return errs.length == 0 ? payload : null
        }).filter(Boolean);

        const result = await this.utilsService.getFilesSignedUrl(payloadsValidate)
        return res.json(result)
    }
    async preSignedChat(req: Request, res: Response) {
        const payload = plainToInstance(PreSignedChatDto, req.body);
        const result = await this.utilsService.preSignedChat(payload);

        return res.json(result)
    }
    async getTalkDRRelationship(req: Request, res: Response) {
        const payload = plainToInstance(GetTalkDRRelationshipDto, req.body);
        const result = await this.utilsService.getTalkDRRelationship(payload);
        return res.json(result)
    }
    async getScenarioConfigForm(req: Request, res: Response) {
        const payload = plainToInstance(GetTalkDRRelationshipDto, req.body);
        const result = await this.utilsService.getTalkDRConfigForm(payload);
        return res.json(result)
    }
}

export function utilsRoutersFactory(helper: { dynamo: DynamoHelper }) {
    const handler = new HandlerUtils(helper.dynamo);

    return (app: API) => {
        app.post('/upload-files', handler.presignedLinkUpload.bind(handler));
        app.post('/signed-files', handler.getFileSignedUrl.bind(handler));
        app.post('/presigned-chat', handler.preSignedChat.bind(handler));
        app.post('/get-talk-relationship', handler.getTalkDRRelationship.bind(handler));
        app.post('/get-scenario-config-form', handler.getScenarioConfigForm.bind(handler));
    }
}
