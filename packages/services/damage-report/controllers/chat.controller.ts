import { plainToInstance } from "class-transformer";
import { __CHAT_UID_MANAGER } from "damage-report/constants.js";
import { BatchGetGroupChatDto, CreateGroupChatDto, GetGroupChatDto } from "damage-report/dto/get-group-chat.dto";
import { ListMessageDto } from "damage-report/dto/list-message.dto";
import { UpdateUnreadDto } from "damage-report/dto/update-unread.dto.js";
import { DynamoHelper } from "damage-report/helpers/dynamo.helper";
import { ChatService } from "damage-report/services/chat.service";
import type { Request, Response } from "lambda-api";
import { API } from "lambda-api"
import { requestContext as reqCtxLiff } from "../../common/liff-api/execution-context";

class Handler {
    constructor(public chatService: ChatService, public forLiff = false) { }

    async listMessage(req: Request, res: Response) {
        const payload = plainToInstance(ListMessageDto, req.body);
        const result = await this.chatService.listMessage(payload);
        return res.json(result)
    }
    async getGroupChat(req: Request, res: Response) {
        const payload = new GetGroupChatDto();
        payload.gid = req.query.gid;
        payload.uid = this.uid;

        const result = await this.chatService.getGroup(payload);
        return res.json(result)
    }

    async batchGetGroupChat(req: Request, res: Response) {
        const payload = new BatchGetGroupChatDto();
        payload.gid = req.body.gid;
        payload.uid = this.uid;

        const result = await this.chatService.batchGetGroup(payload);
        return res.json(result)
    }
    
    async createGroup(req: Request, res: Response) {
        const payload = new CreateGroupChatDto();
        payload.gid = req.query.gid;
        payload.uid = this.uid;

        const result = await this.chatService.createGroup(payload);
        return res.json(result)
    }
    async updateUnread(req: Request, res: Response) {
        const payload = plainToInstance(UpdateUnreadDto, req.body);
        payload.uid = this.uid;

        const result = await this.chatService.updateUnread(payload);
        return res.json(result)
    }

    get uid() { return this.forLiff ? reqCtxLiff.userId : __CHAT_UID_MANAGER }
}

export function chatRoutersFactory(helper: { dynamo: DynamoHelper }, forLiff = false) {
    const mapService = new ChatService(helper.dynamo);
    const handler = new Handler(mapService, forLiff);

    return (api: API) => {
        api.post('/list-message', handler.listMessage.bind(handler));
        api.get('/group', handler.getGroupChat.bind(handler));
        api.post('/batch-group', handler.batchGetGroupChat.bind(handler));
        api.post('/group', handler.createGroup.bind(handler));
        api.put('/unread', handler.updateUnread.bind(handler));
    }
}