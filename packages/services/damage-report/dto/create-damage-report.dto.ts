import { Type } from "class-transformer";
import { <PERSON>rrayMaxSize, IsArray, IsIn, IsNotEmpty, IsO<PERSON>al, MaxLength, ValidateNested } from "class-validator";
import { __DR_ARR_STATUS, EDRStatus } from "damage-report/constants";
import { DamageReportItem, DRImage, Location } from "damage-report/models/damage-report.model";
import moment from "moment";
import { v4 as uuidV4 } from "uuid";

export class CreateDamageReportDto extends DamageReportItem {
    @IsNotEmpty()
    category: string;

    @IsNotEmpty()
    scenarioId?: string;

    @IsNotEmpty()
    subject?: string;

    @IsOptional()
    @IsArray()
    @ArrayMaxSize(3)
    @ValidateNested()
    @Type(() => DRImage)
    images?: DRImage[];

    @IsOptional()
    @ValidateNested()
    @Type(() => Location)
    location?: ILocation;


    @IsOptional()
    @MaxLength(200)
    remarks?: string;

    @IsOptional()
    @IsArray()
    @ArrayMaxSize(3)
    @ValidateNested()
    @Type(() => DRImage)
    remarksImages?: DRImage[];

    @IsOptional()
    @IsIn(__DR_ARR_STATUS)
    status: EDRStatus = EDRStatus.NotProcessed;

    constructor() {
        super();
        this.createdAt = moment().unix();
        this.updatedAt = this.createdAt;
        this.id = uuidV4();
    }
}