import { IsDateString, IsIn, IsOptional } from "class-validator";
import { __DR_ARR_STATUS, EDRStatus } from "damage-report/constants";
import { QueryDamageReportDto } from "./query-damage-report.dto";

export class FilterDamageReportDto extends QueryDamageReportDto implements IQueryDR {
    @IsOptional()
    @IsDateString()
    fromDate: string;
    
    @IsOptional()
    @IsDateString()
    toDate: string;

    @IsOptional()
    userName: string;

    @IsOptional()
    @IsIn(__DR_ARR_STATUS)
    status: EDRStatus;

    @IsOptional()
    category: string;

    @IsOptional()
    subject: string;
    
    @IsOptional()
    scenarioId: string;

    @IsOptional()
    queryAll?: boolean;

    @IsOptional()
    timeMode: 'createdAt' | 'updatedAt'
}