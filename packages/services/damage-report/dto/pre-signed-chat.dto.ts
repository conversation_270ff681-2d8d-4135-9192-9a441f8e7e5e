import { IsIn, IsNotEmpty, IsNumber, IsOptional, ValidateIf } from "class-validator";

export class PreSignedChatDto {
    @IsOptional()
    @IsNumber()
    expiresIn: number;

    @ValidateIf(op => op.method == 'GET')
    @IsNotEmpty()
    key: string;

    @IsNotEmpty()
    fileName: string;

    @IsNotEmpty()
    @IsIn(['GET', 'PUT'])
    method: 'GET' | 'PUT';

    @IsNotEmpty()
    @IsIn(["image", "file"])
    type: 'image' | 'file'

    @IsOptional()
    contentType: string
}