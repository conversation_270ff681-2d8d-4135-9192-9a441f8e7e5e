import { Exclude, Type } from "class-transformer";
import { ArrayMaxSize, IsArray, IsBoolean, IsIn, IsNotEmpty, IsOptional, MaxLength, ValidateNested } from "class-validator";
import { __DR_ARR_STATUS, EDRStatus } from "damage-report/constants";
import { DamageReportItem, DRImage, Location } from "damage-report/models/damage-report.model";
import moment from "moment";

export class UpdateDamageReportDto extends DamageReportItem {

    @IsNotEmpty()
    id: string;

    @IsOptional()
    category: string;

    @IsOptional()
    @IsIn(__DR_ARR_STATUS)
    status: EDRStatus;

    @IsOptional()
    subject?: string;

    @IsOptional()
    @IsArray()
    @ArrayMaxSize(3)
    @ValidateNested()
    @Type(() => DRImage)
    images?: DRImage[];

    @IsOptional()
    @ValidateNested()
    @Type(() => Location)
    location?: ILocation;

    @IsOptional()
    @MaxLength(200)
    remarks?: string;

    @IsOptional()
    @MaxLength(200)
    feedback?: string;

    @IsOptional()
    @IsArray()    
    @ArrayMaxSize(3)
    @ValidateNested()
    @Type(() => DRImage)
    remarksImages?: DRImage[];

    @IsOptional()
    @IsBoolean()
    published?: boolean;

    constructor() {
        super();
        this.updatedAt = moment().unix()
    }
}

export class UpdateDamageReportLiffDto extends UpdateDamageReportDto {

    @IsOptional()
    @IsIn([EDRStatus.Cancel, EDRStatus.NotProcessed])
    status: EDRStatus;

    @Exclude()
    remarks?: string;

    @Exclude()
    remarksImages?: DRImage[];

    @Exclude()
    published?: boolean;
}