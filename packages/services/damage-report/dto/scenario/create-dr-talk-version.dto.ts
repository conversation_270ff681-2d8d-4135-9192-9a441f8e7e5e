import { Type } from "class-transformer";
import { <PERSON><PERSON><PERSON><PERSON>, IsNotEmpty } from "class-validator";
import { TalkDataItem } from "damage-report/models/scenario-data.model";

export class CreateDRTalkVersionDto {
    @IsNotEmpty()
    @Type(() => TalkDataItem)
    talkItem: TalkDataItem;

    @IsNotEmpty()
    @IsArray()
    @Type(() => TalkDataItem)
    otherItem: TalkDataItem[]
}