const LINEMESSAGING_CHANNEL_ACCESS_TOKEN = process.env.LINEMESSAGING_CHANNEL_ACCESS_TOKEN

const EXPIRES_IN = 60 * 60 * 24;
export class Utils {
    static async getLineUserProfile(userId: string, token: string) {
        const url = `https://api.line.me/v2/bot/profile/${userId}`;
        const res = await fetch(url, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });

        return await res.json() as ILineUserProfile
    }
    static dateToSecond(date?: Date) {
        const _date = date ?? new Date();
        return Math.floor(_date.getTime() / 1000)
    }
    static getExpiresInTime(time?: number) {
        return Math.min(time || EXPIRES_IN, EXPIRES_IN);
    }
    static trimFileName(name: string) {
        return name.replace(/\s+/g, "-");
    }
    /**
     * 
     * @param time default delay in 500ms
     * @returns 
     */
    static delay(time = 500) {
        return new Promise<void>((res) => {
            setTimeout(() => {
                res()
            }, time);
        })
    }

    static async sendLineChannelMesssages(userId: string, payload: any[]) {
        const url = `https://api.line.me/v2/bot/message/push`;
        const requestOptions: RequestInit = {
            method: "POST",
            body: JSON.stringify({
                to: userId,
                messages: payload
            }),
            redirect: "follow",
            headers: {
                'Authorization': `Bearer ${LINEMESSAGING_CHANNEL_ACCESS_TOKEN}`,
                'Content-Type': "application/json"
            }
          };
        const res = await fetch(url, requestOptions);
        return await res.json()
    }
}