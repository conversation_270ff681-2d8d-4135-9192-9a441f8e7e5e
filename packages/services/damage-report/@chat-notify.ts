export enum EHttpMethod {
    GET = 'GET',
    PUT = 'PUT',
    POST = 'POST',
}
export enum ERouteKey {
    SEND = 'sendMessage',
    MODIFY = 'modifyMessage',
    PING = 'pingMessage',
}

export enum EChatEvent {
    HANDSHAKE = 'handshake',
    JOIN = 'join',
    JOIN_GROUP = 'join-group',
    SEND_MESSAGE = 'send-message',
    PING = 'ping',
    REACT_MESSAGE = 'react-message',
    EDIT_MESSAGE = 'edit-message',
    RECALL_MESSAGE = 'recall-message',
    DELETE_MESSAGE = 'delete-message',
    NOTIFY = 'notify',
    READ_TIME_CHANGE = 'read-time-change',
    DAMAGE_REPORT_CREATE = 'damage-report-create',
    FINISH_STREAM_CALCULATE_TOTAL = 'finish-calculate-total',
}
export enum EChatRole {
    MANAGER = 'manager',
    LINE_USER = 'line-user',
}
export enum EMessageType {
    file = 'file',
    image = 'image',
}

/** Event */
export interface IEventBase {
    gid: string;
    messageId: string;
}
export interface IEditEvent extends IEventBase {
    contentEdit: string;
    timeEdited?: number;
    sender: ISender;
}
export interface IDeleteEvent extends IEventBase {
    sender: ISender;
}
export interface IRecallEvent extends IEventBase { }
export interface IChatReactEvent extends IChatReact, IEventBase { }
export interface IJoinEvent {
    gid?: string;
    sender: ISender;
}
export interface ISendMessageEvent extends IMessage {
    messageIdTemp: string;
}

export interface IEventAction<T> {
    event_name: EChatEvent;
    payload: T;
}
export interface IWSActionMessage<T> extends IEventAction<T> {
    action: ERouteKey;
}
export interface IHandlerEvent<T> {
    handler(data: {
        payload: T;
        connection_id?: string;
        gid?: string;
    }): Promise<void>;
}
export interface IChatReact {
    content: string;
    sender: ISender;
    timestamp: number;
}
export interface IFile {
    key: string;
    file_name: string;
    type: EMessageType;
    content_type: string;
    url?: string;
    thumbnail_url?: string;
}
export interface ISender {
    id: string;
    name: string;
    role: EChatRole;
}
export interface IMessage {
    /** gid = damage_report_id */
    gid: string;
    id: string;
    timestamp: number;
    sender: ISender;
    time_edited?: number;
    replyTo?: Partial<IMessage>;
    unavailable?: boolean;
    content?: string;
    react?: IChatReact[];
    files?: IFile[];
}
/** Connection Entity */
export interface IConnection {
    uid: string;
    uid_origin?: string;
    connection_id: string;
    time_connected?: number;
    time_updated?: number;
    event_name: EChatEvent;
    role?: EChatRole;
    content?: any;
    expire_at?: number;
}
export interface IConnectionGroup {
    connection_id: string;
    gid: string;
    uid: string;
    uid_origin: string;
    expire_at: number;
}
export interface IUser {
    id: string;
    username: string;
}
export interface IChatMember {
    id: string;
    gid: string;
    name?: string;
    latest_time_read: number;
    unread_count: number;
    unread_count_plus?: boolean;
    role: EChatRole;
    latest_read_user?: string;
    last_notify?: number;
}
export interface IGroup {
    id: string;
    name?: string;
    unread_count?: number;
    unread_count_plus?: boolean;
    members?: IChatMember[];
}
/** Websocket */
export interface IWebsocketAuthContext {
    sub: string;
    userName: string;
    email?: string;
    role: EChatRole;
    principalId: string;
}

// API Lambda handler
export interface IEventListMessage {
    gid: string;
    lastEvaluatedKey?: any;
    limit?: number;
    expiresIn?: number;
    atTime?: number;
}
export interface IEventListGroup {
    idMember?: string;
    role: EChatRole;
}
export interface IPreSignedUrl {
    expiresIn: number,
    key: string,
    file_name: string,
    method: 'GET' | 'PUT',
    type: EMessageType,
    content_type: string,
}
export interface IEventUpdateUnRead {
    uid: string;
    gid: string;
    latest_time_read: number;
}
export interface IEventCreateGroup {
    id: string;
    gid: string;
    role: EChatRole;
}
