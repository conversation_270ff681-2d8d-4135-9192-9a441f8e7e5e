
const ALLOWED_HEADERS = 'Content-Type, Authorization, Content-Length, X-Requested-With,x-amz-security-token,x-amz-date,x-dev-username'

const setHeadersOnResponseJson = (req: any, res: any) => {
    res.headers['access-control-allow-headers'] = ALLOWED_HEADERS
    res.headers['access-control-allow-origin'] = req.headers['origin'] || '*'
    res.headers['access-control-allow-credentials'] = !!req.headers['origin'];
    res.headers['Access-Control-Max-Age'] = 86400
    res.headers['access-control-allow-methods'] = 'GET, PUT, POST, DELETE, OPTIONS'
    res.headers['x-some-header'] = 'some-value'
}


export const handler: any = (evt, ctx) => {
    const response: any = {
        headers: {},
        body: '',
        statusCode: 200,
    }
    setHeadersOnResponseJson(evt, response);
    return Promise.resolve(response);
}
