import { PutCommand } from "@aws-sdk/lib-dynamodb";
import { SQSEvent, SQSRecord } from "aws-lambda";
import { EChatEvent } from "damage-report/@chat-notify";
import { __DR_GROUPID_KEY, __DR_SCENARIO_DATA_DATAID } from "damage-report/constants";
import { DynamoHelper } from "damage-report/helpers/dynamo.helper";
import { PushMessageHelper } from "damage-report/helpers/push-message.helper";
import { GeoService } from "damage-report/services/geo.service";
import { v4 as uuidV4 } from "uuid";
import { Utils } from "../utils";

const LINEMESSAGING_CHANNEL_ACCESS_TOKEN = process.env.LINEMESSAGING_CHANNEL_ACCESS_TOKEN;
const DAMAGE_REPORT_DATA_TABLE = process.env.DAMAGE_REPORT_DATA_TABLE;
export const handler = async (event: SQSEvent) => {
    try {
        const dynamoHelper = new DynamoHelper();
        const geoService = new GeoService(dynamoHelper);
        const pushMessageHelper = new PushMessageHelper(dynamoHelper.client);

        await Promise.all(event.Records.map(async record => {
            const damageItem = await handleRecord(record, dynamoHelper, geoService);

            // send notify to all admin
            delete damageItem.flows;
            await pushMessageHelper.toAllAdmin({
                event_name: EChatEvent.DAMAGE_REPORT_CREATE,
                payload: damageItem
            })
        }))
    } catch (error) {
        console.error("Error consume create damage report message ", error)
    }
    return console.log("Finished")
}

// create damage report item
async function handleRecord(record: SQSRecord, dynamoHelper: DynamoHelper, geoService: GeoService) {
    const message = JSON.parse(record.body) as ICreateDRMessage;
    if (!message.uid || !message.scenarioId) return;

    const damageItem = {} as IDamageReportItem;
    if (message.from == 'chatbot') {
        damageItem.groupId = __DR_GROUPID_KEY;
        damageItem.uid = message.uid;

        const profile = await Utils.getLineUserProfile(message.uid, LINEMESSAGING_CHANNEL_ACCESS_TOKEN);
        damageItem.userName = profile.displayName || "";
        damageItem.from = 'chatbot'
    }

    const payload = message.payload as IUserSessionDamageReport;
    const payloadVersion = message.payload as IUserSessionDamageReportVersion;

    if (Object.keys(payload).length === 0) {
        // empty request
        return
    }

    let category = payload.category?.general;
    let subject = payload.category?.detailed || '';
    let images: IDRImage[] = [];

    const isCustomScenario = message.talkId && message.talkId != __DR_SCENARIO_DATA_DATAID
    if (isCustomScenario) {
        //custom scenario data
        category = payloadVersion.category;
        subject = payloadVersion.subject || '';
        damageItem.feedback = payloadVersion.feedback;
        damageItem.talkDRId = message.talkId;

        if (payloadVersion.images?.length) {
            images = payloadVersion.images.map(x => {
                return {
                    fromChatBot: true,
                    key: '',
                    url: x
                } as IDRImage
            })
            damageItem.images = images
        }
    } else {
        // default chat bot
        if (payload.images) {
            Object.keys(payload.images).forEach(keyImage => {
                const image = {
                    fromChatBot: true,
                    key: "",
                    url: message.payload.images[keyImage]
                }
                images.push(image)
            })
            damageItem.images = images
        }
        damageItem.feedback = payload.userFeedback;
        // damageItem.flows = payload.workflows || [];
    }

    damageItem.category = category;
    damageItem.subject = subject;
    damageItem.scenarioId = message.scenarioId;

    const dateNow = Utils.dateToSecond();
    damageItem.createdAt = dateNow;
    damageItem.updatedAt = dateNow;

    let insertGeo = false;
    if (message.payload.location) {
        const location: ILocation = {
            address: message.payload.location.address,
            lat: message.payload.location.latitude,
            long: message.payload.location.longitude
        }
        damageItem.location = location;
        insertGeo = true
    }
    damageItem.status = "not-processed";
    damageItem.id = uuidV4();

    const otherPayloadData = processOtherField(payload, isCustomScenario)
    for (const key in otherPayloadData) {
        damageItem[key] = otherPayloadData[key]
    }

    await dynamoHelper.client.send(new PutCommand({
        TableName: DAMAGE_REPORT_DATA_TABLE,
        Item: damageItem
    }));

    if (insertGeo) await geoService.createItem(damageItem as any);

    return damageItem
}

function processOtherField(payload: any, isCustomScenario: boolean) {
    if (!payload || typeof (payload) !== 'object') {
        return {}
    }
    const knownKeys = ["id", "userName", "scenarioId", "status", "remarks", "remarksImages",
        "published", "createdAt", "updatedAt", "groupId", "uid", "from", "versionId",
        "category", "subject", "location", "images", "__images_key__", "__location_key__",
        "confirm_summary_report", "__field_label_mappings__",
        ...isCustomScenario ? [
            "feedback"
        ] : [
            "userFeedback"
        ]
    ]

    const result: any = {}
    for (const key in payload) {
        if (knownKeys.includes(key)) {
            continue
        }

        if (payload["__images_key__"]?.includes(key) && payload[key]) {
            // image process
            result[key] = payload[key]?.map(imageUrl => ({
                fromChatBot: true,
                key: "",
                url: imageUrl
            }))
        }
        else if (payload["__location_key__"]?.includes(key) && payload[key]) {
            // location process
            result[key] = {
                address: payload[key].address,
                lat: payload[key].latitude,
                long: payload[key].longitude
            }
        }
        else {
            result[key] = payload[key]
        }
    }
    return result
}

