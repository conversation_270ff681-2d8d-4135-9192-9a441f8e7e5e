import { CognitoJwtVerifier } from "aws-jwt-verify";
import { EChatRole } from "damage-report/@chat-notify";
import { __AUTH_WS_PRINCIPAL_ID } from "damage-report/constants";
const FROM_HEADER_MANAGER = 'manager';
const FROM_HEADER_LIFF = 'liff';
const LINELOGIN_CHANNEL_ID = process.env.LINELOGIN_CHANNEL_ID;

const verifier = CognitoJwtVerifier.create({
    userPoolId: process.env.COGNITO_POOL_ID!,
    tokenUse: "id",
    clientId: process.env.COGNITO_CLIENT_ID!,
});

// example: wss://xxxxxx?token=abc&from=manager
// example: wss://xxxxxx?token=abc&from=liff
export const handler = async (event: any) => {
    const queryStringParameters = event.queryStringParameters;
    let token = queryStringParameters['token'];

    if (!queryStringParameters['from'] || !token) throw new Error("権限がありません");
    const _token = getToken(token);
    // "methodArn": "arn:aws:execute-api:us-east-1:123456789012:abcdef123/default/$connect"
    const resource = event.methodArn;

    let payloadUser: any;
    if (queryStringParameters['from'] == FROM_HEADER_MANAGER) {
        payloadUser = await verifyFromManager(_token);
    } else if (queryStringParameters['from'] == FROM_HEADER_LIFF) {
        payloadUser = await verifyAccessToken(_token);
    }
    if (payloadUser) return generatePolicy(__AUTH_WS_PRINCIPAL_ID, 'Allow', resource, payloadUser);
    throw new Error("権限がありません");
}

async function verifyFromManager(token: string) {
    try {
        const data = await verifier.verify(token);
        return {
            sub: data.sub,
            userName: data['cognito:username'],
            email: data['email'],
            role: EChatRole.MANAGER
        }
    } catch {
        throw new Error("権限がありません");
    }
}
async function verifyFromLiff(token: string) {
    try {
        const url_line_verify = 'https://api.line.me/oauth2/v2.1/verify';
        const data: any = await fetch(url_line_verify, {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: new URLSearchParams({
                id_token: token,
                client_id: LINELOGIN_CHANNEL_ID!
            })
        }).then(res => res.json());
        if (!data.sub) throw new Error("権限がありません");
        return {
            sub: data.sub,
            userName: data.name,
            email: data.email,
            role: EChatRole.LINE_USER
        }
    } catch (error) {
        throw new Error("権限がありません");
    }
}
async function verifyAccessToken(token: string) {
    try {
        const url_line_verify = 'https://api.line.me/oauth2/v2.1/verify?access_token=' + token;
        const data: any = await fetch(url_line_verify).then(res => res.json());
        if (data.client_id != LINELOGIN_CHANNEL_ID) throw new Error("無効なトークン")
    
        const profile: any = await fetch('https://api.line.me/oauth2/v2.1/userinfo', {
            headers: {
                Authorization: `Bearer ${token}`
            }
        }).then(res => res.json());
    
        return {
            sub: profile.sub,
            userName: profile.name,
            email: profile.email,
            role: EChatRole.LINE_USER
        }
        
    } catch (error) {
        throw new Error("権限がありません")
    }
}
function getToken(token: string) {
    let _token = token;
    if (token.startsWith("Bearer ")) _token = token.substring(7, token.length);
    return _token
}
function generatePolicy(principalId: any, effect: 'Allow' | 'Deny', resource: string, context: any) {
    const authResponse: any = {};
    authResponse.principalId = principalId;

    if (effect && resource) {
        const policyDocument: any = {};
        policyDocument.Version = '2012-10-17';
        policyDocument.Statement = [];
        const statementOne: any = {};
        statementOne.Action = 'execute-api:Invoke';
        statementOne.Effect = effect;
        statementOne.Resource = resource;
        policyDocument.Statement[0] = statementOne;
        authResponse.policyDocument = policyDocument;
    }
    if (context) authResponse.context = context;
    return authResponse;
}