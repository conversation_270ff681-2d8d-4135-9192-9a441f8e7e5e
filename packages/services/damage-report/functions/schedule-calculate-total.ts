
import { QueryCommandInput } from "@aws-sdk/lib-dynamodb";
import { __DR_GROUPID_KEY } from "damage-report/constants";
import { DamageReportCacheTotalHelper } from "damage-report/helpers/dr-cache-total.helper";
import { <PERSON>Helper } from "damage-report/helpers/dynamo.helper";
import { TimeHelper } from "damage-report/helpers/time.helper";

const DAMAGE_REPORT_DATA_TABLE = process.env.DAMAGE_REPORT_DATA_TABLE;
const DAMAGE_REPORT_DATA_TABLE_INDEX_GROUPID_CREATED = process.env.DAMAGE_REPORT_DATA_TABLE_INDEX_GROUPID_CREATED;

export async function handler() {
    try {
        // get all data damage report
        const dynamoHelper = new DynamoHelper();
        const data = await getAllData(dynamoHelper);

        // re-calculator all total cache
        const cacheHelper = new DamageReportCacheTotalHelper(dynamoHelper);
        await cacheHelper.calculateTotal(data);

        console.log("Finish schedule calculate ", data.length)
    } catch (error) {
        console.error(error)
    }
}

async function getAllData(dynamoHelper: DynamoHelper) {
    const fromDateTime = TimeHelper.defaultFromDateQuery();
    const query: QueryCommandInput = {
        TableName: DAMAGE_REPORT_DATA_TABLE,
        IndexName: DAMAGE_REPORT_DATA_TABLE_INDEX_GROUPID_CREATED,
        KeyConditionExpression: "#groupId = :groupId AND #createdAt >= :createdAt",
        FilterExpression: "#markDelete <> :markDelete",
        ExpressionAttributeNames: {
            "#groupId": "groupId",
            "#createdAt": "createdAt",
            '#markDelete': 'markDelete'
        },
        ExpressionAttributeValues: {
            ":groupId": __DR_GROUPID_KEY,
            ":createdAt": fromDateTime,
            ':markDelete': true
        }
    };

    const results = await dynamoHelper.queryAll(query);
    return results as IDamageReportItem[]
}