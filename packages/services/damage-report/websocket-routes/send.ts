import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient } from "@aws-sdk/lib-dynamodb";
import { APIGatewayEvent } from "aws-lambda";
import { EChatEvent, ERouteKey, IHandlerEvent, ISendMessageEvent } from "damage-report/@chat-notify";
import { ChatMessageHelper } from "damage-report/helpers/chat-message.helper";
import { ChatNotifyHelper } from "damage-report/helpers/chat-notify.helper";
import { PushMessageHelper } from "damage-report/helpers/push-message.helper";
import { UtilsResponse } from "damage-report/utils/response";


const REGION = process.env.REGION || 'ap-northeast-1';
const dynamo = DynamoDBDocumentClient.from(new DynamoDBClient({ region: REGION }));
export const handler = async (event: APIGatewayEvent) => {
    try {
        const { data, connecttionId } = ChatNotifyHelper.handleCommonEvent<ISendMessageEvent>(event, ERouteKey.SEND);
        const isMemberInGroup = await ChatNotifyHelper.checkConnectionIdInGroup(connecttionId, data.payload.gid);
        if (!isMemberInGroup) return UtilsResponse.error("Member not exist in group");

        if (data.event_name == EChatEvent.SEND_MESSAGE) {
            await new HandlerSendEvent(dynamo).handler({ payload: data.payload })
        }

        return UtilsResponse.success()
    } catch (error) {
        return UtilsResponse.error(JSON.stringify(error));
    }
}


export class HandlerSendEvent implements IHandlerEvent<ISendMessageEvent> {
    constructor(public dynamo: DynamoDBDocumentClient) { }

    async handler({ payload }: { payload: ISendMessageEvent }) {
        const messageInstance = new ChatMessageHelper(payload.gid, this.dynamo);
        const connectionInstance = new PushMessageHelper(this.dynamo);
        const newMessage = await messageInstance.newMessage(payload);
        await connectionInstance.toGroup(payload.gid, { event_name: EChatEvent.SEND_MESSAGE, payload: newMessage });
    }
}
