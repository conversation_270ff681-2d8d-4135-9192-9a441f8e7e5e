import { DynamoD<PERSON>lient } from "@aws-sdk/client-dynamodb";
import { BatchWriteCommand, BatchWriteCommandInput, DynamoDBDocumentClient, PutCommand, QueryCommand } from "@aws-sdk/lib-dynamodb";
import { APIGatewayEvent, APIGatewayEventRequestContext } from "aws-lambda";
import { EChatEvent, IConnection, IWebsocketAuthContext } from "damage-report/@chat-notify";
import { __AUTH_WS_PRINCIPAL_ID, __CHAT_UID_TEMP } from "damage-report/constants";
import { PushMessageHelper } from "damage-report/helpers/push-message.helper";
import { UtilsResponse } from "damage-report/utils/response";

const REGION = process.env.REGION || 'ap-northeast-1';
const WS_CONNECTION_TABLE = process.env.WS_CONNECTION_TABLE!;
const WS_CONNECTION_ID_INDEX = process.env.WS_CONNECTION_ID_INDEX;
const CHAT_CONNECTION_GROUP_TABLE = process.env.CHAT_CONNECTION_GROUP_TABLE!;

const dynamo = DynamoDBDocumentClient.from(new DynamoDBClient({ region: REGION }));

export const handler = async (event: APIGatewayEvent) => {
    const _context = event.requestContext as APIGatewayEventRequestContext;
    const _connecttionId = _context.connectionId;
    const routeKey = _context.routeKey;

    if (!_connecttionId) return UtilsResponse.error("Not found connection id");
    const _item: IConnection = {
        uid: __CHAT_UID_TEMP,
        connection_id: _connecttionId,
        event_name: EChatEvent.HANDSHAKE,
        time_connected: Date.now()
    }
    if (routeKey == '$connect') {
        const authContext = _context.authorizer as IWebsocketAuthContext;
        if (authContext.principalId == __AUTH_WS_PRINCIPAL_ID && authContext.sub) {
            const pushHelper = new PushMessageHelper(dynamo);
            await pushHelper.join({
                payload: {
                    sender: {
                        id: authContext.sub,
                        name: authContext.userName,
                        role: authContext.role
                    }
                },
                connection_id: _connecttionId
            })
        } else {
            await dynamo.send(new PutCommand({
                TableName: WS_CONNECTION_TABLE,
                Item: _item
            }));
        }
    } else if (routeKey == '$disconnect') {
        try {
            const result = await dynamo.send(new QueryCommand({
                TableName: WS_CONNECTION_TABLE,
                IndexName: WS_CONNECTION_ID_INDEX,
                KeyConditionExpression: '#connection_id = :val',
                ExpressionAttributeNames: {
                    '#connection_id': 'connection_id'
                },
                ExpressionAttributeValues: {
                    ':val': _connecttionId
                }
            }));
            const result2 = await dynamo.send(new QueryCommand({
                TableName: CHAT_CONNECTION_GROUP_TABLE,
                KeyConditionExpression: '#connection_id = :val',
                ExpressionAttributeNames: {
                    '#connection_id': 'connection_id'
                },
                ExpressionAttributeValues: {
                    ':val': _connecttionId
                }
            }));

            const batchInput: BatchWriteCommandInput = {
                RequestItems: {}
            }
            let map1: any[] = [];
            let map2: any[] = [];
            if (result.Items && result.Items.length > 0) {
                map1 = result.Items.map(item => {
                    return { 'DeleteRequest': { 'Key': { uid: item.uid, connection_id: item.connection_id } } }
                });
                batchInput.RequestItems![WS_CONNECTION_TABLE] = map1
            }
            if (result2.Items && result2.Items.length > 0) {
                map1 = result2.Items.map(item => {
                    return { 'DeleteRequest': { 'Key': { gid: item.gid, connection_id: item.connection_id } } }
                });
                batchInput.RequestItems![CHAT_CONNECTION_GROUP_TABLE] = map1
            }

            if (map1.length || map2.length) await dynamo.send(new BatchWriteCommand(batchInput))
        } catch (error) {
            console.error("Error disconnection ", error)
        }
    }
    return UtilsResponse.success()
}