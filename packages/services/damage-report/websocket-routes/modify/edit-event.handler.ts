import { DynamoDBDocumentClient } from "@aws-sdk/lib-dynamodb";
import { EChatEvent, IEditEvent, IHandlerEvent } from "damage-report/@chat-notify";
import { ChatMessageHelper } from "damage-report/helpers/chat-message.helper";
import { PushMessageHelper } from "damage-report/helpers/push-message.helper";

export class HandlerEditEvent implements IHandlerEvent<IEditEvent> {
    constructor(public dynamo: DynamoDBDocumentClient) { }
    async handler({ payload }: { payload: IEditEvent }) {
        const messageInstance = new ChatMessageHelper(payload.gid, this.dynamo);
        const connectionInstance = new PushMessageHelper(this.dynamo);
        const timeEdited = messageInstance.editMessage(payload);
        connectionInstance.toGroup(payload.gid, { event_name: EChatEvent.EDIT_MESSAGE, payload: { ...payload, timeEdited } })
    }
}
