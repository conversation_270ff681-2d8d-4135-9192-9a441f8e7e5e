import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient } from "@aws-sdk/lib-dynamodb";
import { APIGatewayEvent } from "aws-lambda";
import { EChatEvent, ERoute<PERSON>ey } from "damage-report/@chat-notify";
import { ChatNotifyHelper } from "damage-report/helpers/chat-notify.helper";
import { UtilsResponse } from "damage-report/utils/response";
import { HandlerDeleteEvent } from "./delete-event.handler";
import { HandlerEditEvent } from "./edit-event.handler";
import { HandlerReactEvent } from "./react-event.handler";
import { HandlerRecallEvent } from "./recall-event.handler";

const REGION = process.env.REGION || 'ap-northeast-1';
const dynamo = DynamoDBDocumentClient.from(new DynamoDBClient({ region: REGION }));
export const handler = async (event: APIGatewayEvent) => {
    try {
        const { data } = ChatNotifyHelper.handleCommonEvent<any>(event, ERouteKey.MODIFY);
        const payload = data.payload;
        switch (data.event_name) {
            case EChatEvent.REACT_MESSAGE:
                await new HandlerReactEvent(dynamo).handler({ payload })
                break;
            case EChatEvent.EDIT_MESSAGE:
                await new HandlerEditEvent(dynamo).handler({ payload })
                break;
            case EChatEvent.DELETE_MESSAGE:
                await new HandlerDeleteEvent(dynamo).handler({ payload })
                break;
            case EChatEvent.RECALL_MESSAGE:
                await new HandlerRecallEvent(dynamo).handler({ payload })
                break;
            default:
                break;
        }
        return UtilsResponse.success()

    } catch (error) {
        return UtilsResponse.error(JSON.stringify(error));
    }
}