import { DynamoDBDocumentClient } from "@aws-sdk/lib-dynamodb";
import { IHandlerEvent, IChatReactEvent, EChatEvent } from "damage-report/@chat-notify";
import { Chat<PERSON>essageHelper } from "damage-report/helpers/chat-message.helper";
import { PushMessageHelper } from "damage-report/helpers/push-message.helper";

export class HandlerReactEvent implements IHandlerEvent<IChatReactEvent> {
    constructor(public dynamo: DynamoDBDocumentClient) { }
    async handler({ payload }: { payload: IChatReactEvent }) {
        const messageInstance = new ChatMessageHelper(payload.gid, this.dynamo);
        const connectionInstance = new PushMessageHelper(this.dynamo);
        const newReactData = messageInstance.reactMessage(payload);
        connectionInstance.toGroup(payload.gid, { event_name: EChatEvent.REACT_MESSAGE, payload: newReactData })
    }
}
