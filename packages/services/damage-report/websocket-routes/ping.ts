import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient, GetCommand } from "@aws-sdk/lib-dynamodb";
import { APIGatewayEvent } from "aws-lambda";
import { EChatEvent, ERoute<PERSON>ey } from "damage-report/@chat-notify";
import { __CHAT_UID_TEMP } from "damage-report/constants";
import { ChatNotifyHelper } from "damage-report/helpers/chat-notify.helper";
import { PushMessageHelper } from "damage-report/helpers/push-message.helper";
import { UtilsResponse } from "damage-report/utils/response";

const REGION = process.env.REGION || 'ap-northeast-1';
const WS_CONNECTION_TABLE = process.env.WS_CONNECTION_TABLE;
const dynamo = DynamoDBDocumentClient.from(new DynamoDBClient({ region: REGION }));
export async function handler(event: APIGatewayEvent) {
    try {
        const { data, connecttionId } = ChatNotifyHelper.handleCommonEvent<any>(event, ERouteKey.PING);
        const pushHelper = new PushMessageHelper(dynamo);

        switch (data.event_name) {
            case EChatEvent.JOIN:
            case EChatEvent.PING:
                const existItem = await getItemExist(connecttionId);
                if (!existItem) return;
                await pushHelper.join({ payload: data.payload, connection_id: connecttionId });
                break;
            case EChatEvent.JOIN_GROUP:
                await pushHelper.joinGroup({ payload: data.payload, connection_id: connecttionId });
                break;
            default:
                break;
        }
        return UtilsResponse.success()

    } catch (error) {
        return UtilsResponse.error(JSON.stringify(error));
    }
}
async function getItemExist(connectionId: string) {
    const result = await dynamo.send(new GetCommand({
        TableName: WS_CONNECTION_TABLE,
        Key: { uid: __CHAT_UID_TEMP, connection_id: connectionId }
    }));
    return result.Item
}