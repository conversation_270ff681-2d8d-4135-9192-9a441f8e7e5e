import { GetObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { Upload } from "@aws-sdk/lib-storage";
import { createPresignedPost, PresignedPostOptions } from "@aws-sdk/s3-presigned-post";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { writeToStream } from "@fast-csv/format";
import { CreatePresignedLinkUploadDto } from "damage-report/dto/create-presigned-link-upload";
import { GetFileSignedUrlDto } from "damage-report/dto/get-file-signed.dto";
import stream from "node:stream";

import { IFile } from "damage-report/@chat-notify";
import { PreSignedChatDto } from "damage-report/dto/pre-signed-chat.dto";
import { ApiHelper } from "damage-report/helpers/api.helper";
import { Utils } from "damage-report/utils";
import { v4 as uuidV4 } from "uuid";
import { <PERSON><PERSON><PERSON><PERSON> } from "damage-report/helpers/dynamo.helper";
import { __DR_SCENARIO_DATA_DATAID } from "damage-report/constants";
import { GetTalkDRRelationshipDto } from "damage-report/dto/scenario/get-talk-dr-relationship.dto";
import { GetCommand, QueryCommandInput } from "@aws-sdk/lib-dynamodb";
import { EDRFieldRequired, TalkDataItem } from "damage-report/models/scenario-data.model";

const MAIN_DAMAGE_REPORT_STORAGE = process.env.MAIN_DAMAGE_REPORT_STORAGE;
const TABLE_CHATBOT_SCENARIO_DATA = process.env.TABLE_CHATBOT_SCENARIO_DATA;

const expiresInPut = 60;
const expiresInGet = 60 * 60 * 24;
const MAX_FILE_SIZE_UPLOAD = 1024 * 1024 * 10;
const MAX_FILE_SIZE_UPLOAD_CHAT = MAX_FILE_SIZE_UPLOAD * 5;

export class UtilsService {
    s3Client: S3Client;
    constructor(
        public dynamoHelper: DynamoHelper
    ) {
        this.s3Client = new S3Client()
    }

    // signed file damage report
    async presignedLinkUpload(payloads: CreatePresignedLinkUploadDto[]) {
        const EXPIRESIN = 5 * 60;
        const result = await Promise.all(payloads.map(async data => {
            const key = data.fileName ? Utils.trimFileName(data.fileName) : uuidV4();
            const prefix = data.type == 'image' ? 'images' : 'files';
            const uuid = uuidV4();
            let fullKey = `${prefix}/${uuid}/${key}`;
            if (data.rootPrefix) fullKey = `${data.rootPrefix}/${fullKey}`;

            const params: PresignedPostOptions = {
                Bucket: MAIN_DAMAGE_REPORT_STORAGE,
                Key: fullKey,
                Expires: EXPIRESIN,
                Conditions: [
                    ["content-length-range", 0, MAX_FILE_SIZE_UPLOAD],
                    ["starts-with", "$Content-Type", "image/"]
                ]
            };
            const { url, fields } = await createPresignedPost(this.s3Client, params);
            return {
                url,
                fileName: data.fileName,
                fullKey,
                fields
            }
        }));
        return result
    }
    async getFilesSignedUrl(data: GetFileSignedUrlDto[]) {
        return await Promise.all(data.map(async item => await this.getFileSignedUrl(item)));
    }
    async getFileSignedUrl(data: GetFileSignedUrlDto) {
        const EXPIRESIN = data.expiresIn ?? 30 * 60;
        const prefix = data.type == 'image' ? 'images' : 'files';
        const fileName = data.fileName ? encodeURIComponent(data.fileName) : '';
        let fullKey = data.fullKey ?? `${prefix}/${fileName}`;

        const contentDisposition = data.type == 'image' ? 'inline' : `attachment; filename="${fileName}"`;
        const command = new GetObjectCommand({
            Bucket: MAIN_DAMAGE_REPORT_STORAGE,
            Key: fullKey,
            ResponseContentDisposition: contentDisposition
        });

        const url = await getSignedUrl(this.s3Client, command, {
            expiresIn: EXPIRESIN
        });
        return {
            url,
            fileName: fileName,
            key: fullKey,
            expiresIn: EXPIRESIN
        }
    }

    // signed in chat feature
    async signedFileChat(file: IFile, expiresIn?: number) {
        const _file_name = encodeURIComponent(file.file_name);
        const responseContentDisposition = `inline; filename="${_file_name}"`;

        const url = await getSignedUrl(
            this.s3Client,
            new GetObjectCommand({
                Bucket: MAIN_DAMAGE_REPORT_STORAGE,
                Key: file.key,
                ResponseContentDisposition: responseContentDisposition,
                ResponseContentType: file.content_type
            }),
            { expiresIn: Utils.getExpiresInTime(expiresIn) },
        );
        return url;
    }
    async preSignedChat(body: PreSignedChatDto) {
        await ApiHelper.validateData(body);

        const PREFIX_FILE = 'chat/files';
        const PREFIX_IMAGE = 'chat/images';

        const { key, expiresIn, method, contentType, type, fileName } = body;
        let _expiresIn = expiresIn;
        let command: any;
        let _key = key;
        let url = '';
        let fields;

        const _file_name = Utils.trimFileName(fileName);
        const _content_type = contentType || 'application/octet-stream';

        if (method == 'GET' && key) {
            const _file_name_encode = encodeURIComponent(_file_name);
            const responseContentDisposition = `inline; filename="${_file_name_encode}"`;
            _expiresIn = expiresIn > expiresInGet ? expiresInGet : expiresIn;

            command = new GetObjectCommand({
                Bucket: MAIN_DAMAGE_REPORT_STORAGE,
                Key: key,
                ResponseContentType: _content_type,
                ResponseContentDisposition: responseContentDisposition
            });
            url = await getSignedUrl(this.s3Client, command, { expiresIn: _expiresIn });
        } else if (method == 'PUT') {
            _expiresIn = expiresIn > expiresInPut ? expiresInPut : expiresIn;
            const uuid = uuidV4();
            _key = `${PREFIX_FILE}/${uuid}_${_file_name || ''}`;
            if (type == 'image') _key = `${PREFIX_IMAGE}/${uuid}_${_file_name || ''}`;

            const params: PresignedPostOptions = {
                Bucket: MAIN_DAMAGE_REPORT_STORAGE,
                Key: _key,
                Expires: _expiresIn,
                Conditions: [
                    ["content-length-range", 0, MAX_FILE_SIZE_UPLOAD_CHAT],
                    ['eq', "$Content-Type", _content_type]
                ]
            };
            const presignedPost = await createPresignedPost(this.s3Client, params);
            url = presignedPost.url;
            fields = presignedPost.fields
        }

        return {
            preSignedUrl: url,
            fields,
            key: _key
        }
    }


    // export files
    async exportCSV(data: TObject[], headers: string[] | boolean = true) {
        const timestamp = new Date().getTime();
        const fileName = `export-csv-${timestamp}.csv`;

        const fullKey = `tmp/${fileName}`;
        const passThroughStream = new stream.PassThrough({ encoding: 'utf8' });
        const upload = new Upload({
            client: this.s3Client,
            params: {
                Bucket: MAIN_DAMAGE_REPORT_STORAGE,
                Key: fullKey,
                Body: passThroughStream,
                ContentType: 'text/csv; charset=utf-8',
                Metadata: {
                    fileName
                }
            }
        })

        writeToStream(passThroughStream, data, { headers: headers });

        await upload.done();

        const getFileSigned = new GetFileSignedUrlDto();
        getFileSigned.type = 'file';
        getFileSigned.fullKey = fullKey;
        getFileSigned.fileName = fileName;

        return await this.getFileSignedUrl(getFileSigned);
    }

    async getTalkDRRelationship(payload: GetTalkDRRelationshipDto) {
        await ApiHelper.validateData(payload);

        const defaultTalkDRItem = await this.getScenarioDataItem(payload.scenario, __DR_SCENARIO_DATA_DATAID);
        if (!defaultTalkDRItem.versions?.length) throw new Error("どのバージョンのこのトークも存在しません");
        if (!defaultTalkDRItem.versions.find(x => x.id == payload.talkDrVersionId)) throw new Error("このバージョンが見つかりません");

        const talkDRData = await this.getScenarioDataItem(payload.scenario, payload.talkDrVersionId);
        const messagesIds = talkDRData.params.messages.filter(x => x.sender == "BOT").map(x => x.messageId);

        const queryAllMessage: QueryCommandInput = {
            TableName: TABLE_CHATBOT_SCENARIO_DATA,
            KeyConditionExpression: "#scenario = :scenario",
            ExpressionAttributeNames: {
                "#scenario": "scenario"
            },
            ExpressionAttributeValues: {
                ":scenario": payload.scenario
            }
        };

        const allMessagesInScenario = await this.dynamoHelper.queryAll(queryAllMessage) as TalkDataItem[];
        const messagesOfTalk = allMessagesInScenario.filter(x => messagesIds.includes(x.dataId));

        const categoryMessages = messagesOfTalk.filter(x => x.targetDataField == EDRFieldRequired.CATEGORY);
        const categoriesData = categoryMessages.map(x => this.getActionInMessage(x)).flat();

        const findActionInfoByParent = (parentId: string, fieldName: EDRFieldRequired): IActionMessageData[] => {
            const item = messagesOfTalk.find(x => x.dataId == parentId);
            if (!item) return [];

            const actions = this.getActionInMessage(item)
            if (item.targetDataField == fieldName) {
                return actions
            } else {
                for (let index = 0; index < actions.length; index++) {
                    const action = actions[index];
                    const _item = findActionInfoByParent(action.dataId, fieldName);
                    if (_item) return _item;
                }
            }
            return []
        }

        const results = categoriesData.reduce((res, curr) => {
            const allSubjectOfThisCategory = findActionInfoByParent(curr.dataId, EDRFieldRequired.SUBJECT);
            const allSubjectAndFeedbackDataOfThisCategory = allSubjectOfThisCategory.reduce((resFeedback, sub) => {
                const _feedbacks = findActionInfoByParent(sub.dataId, EDRFieldRequired.FEEDBACK);
                resFeedback.push({
                    [sub.dataId]: {
                        text: sub.text,
                        feedbacks: _feedbacks || []
                    }
                })
                return resFeedback
            }, [])

            res[curr.dataId] = {
                text: curr.text,
                subjects: allSubjectAndFeedbackDataOfThisCategory || []
            }
            return res
        }, {})

        return results
    }
    private async getScenarioDataItem(scenarioId: string, dataId: string): Promise<TalkDataItem | undefined> {
        const res = await this.dynamoHelper.client.send(new GetCommand({
            TableName: TABLE_CHATBOT_SCENARIO_DATA,
            Key: {
                scenario: scenarioId,
                dataId: dataId
            }
        }));
        return res.Item as TalkDataItem;
    }
    private getActionInMessage(message: TalkDataItem) {
        if (!message.params) {
            return
        }
        const result: IActionMessageData[] = []
        if (message.params.columnCount) {
            // carousel
            for (let indexColumn = 0; indexColumn < message.params.columnCount; indexColumn++) {
                for (let indexAction = 0; indexAction < message.params?.actionCount; indexAction++) {
                    const action = message.params[`action.${indexColumn}.${indexAction}`]
                    result.push({
                        id: `${message.dataId}___${indexColumn}_${indexAction}`,
                        dataId: action.data,
                        text: action.text,
                    })
                }
            }
        }
        else {
            // normal button
            for (let index = 0; index < message.params?.actionCount; index++) {
                const action = message.params[`actions.${index}`]
                result.push({
                    id: `${message.dataId}___${index}`,
                    dataId: action.data,
                    text: action.text,
                })
            }
        }
        return result
    }

    async getTalkDRConfigForm(payload: GetTalkDRRelationshipDto) {
        await ApiHelper.validateData(payload);

        const talkDRData = await this.getScenarioDataItem(payload.scenario, payload.talkDrVersionId);
        const messagesIds = talkDRData.params.messages.filter(x => x.sender == "BOT").map(x => x.messageId)
        const orderMessages = new Map(
            messagesIds.map((id, index) => {
                return [id, index];
            }),
        )

        const queryAllMessage: QueryCommandInput = {
            TableName: TABLE_CHATBOT_SCENARIO_DATA,
            KeyConditionExpression: "#scenario = :scenario",
            ExpressionAttributeNames: {
                "#scenario": "scenario"
            },
            ExpressionAttributeValues: {
                ":scenario": payload.scenario
            }
        };

        const allMessagesInScenario = await this.dynamoHelper.queryAll(queryAllMessage) as TalkDataItem[];
        const messagesOfTalk = allMessagesInScenario.filter(x => orderMessages.has(x.dataId)).sort((u, v) => {
            const uOrder = u.targetDataOrder ? Number(u.targetDataOrder) + messagesIds.length : orderMessages.get(u.dataId)
            const vOrder = v.targetDataOrder ? Number(v.targetDataOrder) + messagesIds.length : orderMessages.get(v.dataId)
            return uOrder - vOrder
        });
        const formConfigResult = this.genFormConfigFromMessages(messagesOfTalk, orderMessages)

        return Object.fromEntries(formConfigResult)
    }

    private genFormConfigFromMessages(messages: TalkDataItem[], orderMessages: Map<string, number>) {
        const messagesOfTalkMap = new Map(
            messages.map((obj) => {
                return [obj?.dataId, obj]
            }),
        )
        const metaData = ["images", "location", "confirm_summary_report", "end_talk"];
        const typeFilter = ["confirm", 'text']

        const listMsgs = messages.filter((x) => {
            return x.targetDataField && !metaData.includes(x.targetDataField) && !typeFilter.includes(x.dataType);
        }).map((x) => {
            return {
                ...x,
                configType: this.getConfigDataType(x),
            }
        })

        const result = new Map()
        for (const config of listMsgs) {
            if (config.configType === "collection") {
                const actions: any = this.getActionInMessage(config)
                result.set(config.targetDataField, {
                    type: "collection",
                    ...config.parentDataField
                        ? { parent: config.parentDataField }
                        : {},
                    key: config.targetDataField,
                    order: config.targetDataOrder || orderMessages.get(config.dataId),
                    label: config.nameLBD,
                    items: [
                        ...result.has(config.targetDataField) ? result.get(config.targetDataField)?.items : [],
                        ...actions.map((x) => {
                            return {
                                id: x.id,
                                value: x.text,
                            }
                        }),
                    ],
                })
            }
            else {
                result.set(config.targetDataField, {
                    label: config.nameLBD,
                    key: config.targetDataField,
                    type: config.configType,
                })
            }
        }
        return result
    }

    private getConfigDataType(config) {
        if (!config) {
            return
        }
        if (config.dataType === "text") {
            return "text"
        }
        else if (config.dataType === "buttons" || config.dataType === "carousel") {
            if (config.params) {
                for (const key in config.params) {
                    if (config.params[key]?.uri && ["https://line.me/R/nv/cameraRoll/single", "https://line.me/R/nv/camera/", "https://line.me/R/nv/cameraRoll/multi"].includes(config.params[key]?.uri)) {
                        return "images"
                    }
                    else if (config.params[key]?.uri === "https://line.me/R/nv/location") {
                        return "location"
                    }
                }
                return "collection"
            }
            else {
                return "unknown"
            }
        }
    }
    private traceParents(messagesOfTalkMap, root: TalkDataItem, childNode: TalkDataItem) {
        let foundInRoot: any
        const nodeToFind = []

        // find root
        if (root.params.columnCount) {
            // carousel
            for (let indexColumn = 0; indexColumn < root.params.columnCount; indexColumn++) {
                for (let indexActions = 0; indexActions < root.params.actionCount; indexActions++) {
                    const element = root.params[`action.${indexColumn}.${indexActions}`]
                    if (element.data === childNode.dataId) {
                        foundInRoot = {
                            id: `${root.dataId}___${indexColumn}_${indexActions}`,
                            value: element.text,
                        }
                        return foundInRoot
                    }
                    else {
                        nodeToFind.push({
                            index: `${indexColumn}_${indexActions}`,
                            data: messagesOfTalkMap.get(element.data),
                        })
                    }
                }
            }
        }
        else {
            for (let indexActions = 0; indexActions < root.params.actionCount; indexActions++) {
                const element = root.params[`actions.${indexActions}`]
                if (element.data === childNode.dataId) {
                    foundInRoot = {
                        id: `${root.dataId}___${indexActions}`,
                        value: element.text,
                    }
                    return foundInRoot
                }
                else {
                    nodeToFind.push({
                        index: indexActions,
                        data: messagesOfTalkMap.get(element.data),
                    })
                }
            }
        }

        // BFS search
        let foundIndex: null | string = null
        while (foundIndex === null && nodeToFind.length > 0) {
            const curNode = nodeToFind.pop()
            const curIndex = curNode.index
            const nodeData = curNode.data
            if (!nodeData) {
                continue
            }
            for (const key in nodeData.params) {
                if (nodeData.params[key]?.data === childNode.dataId) {
                    foundIndex = curIndex
                    break
                }
                else {
                    nodeToFind.push({
                        index: curIndex,
                        data: messagesOfTalkMap.get(nodeData.params[key]?.data),
                    })
                }
            }
        }
        if (foundIndex !== null) {
            return {
                id: `${root.dataId}___${foundIndex}`,
                value: root.params[`actions.${foundIndex}`]?.text,
            }
        }
    }
}

interface IActionMessageData {
    id: string
    dataId: string,
    text: string
}