import { GeoDataManager, GeoDataManagerConfiguration } from "damage-report/lib/dynamo-geo-handle";
import { <PERSON><PERSON>el<PERSON> } from "damage-report/helpers/dynamo.helper";
import { GeoPoint } from "damage-report/lib/dynamo-geo-handle/types";
import { DamageReportItem } from "damage-report/models/damage-report.model";
import { AttributeValue } from "@aws-sdk/client-dynamodb";

const DAMAGE_REPORT_GEO_TABLE = process.env.DAMAGE_REPORT_GEO_TABLE;
const DAMAGE_REPORT_GEO_INDEX_GEOHASH = process.env.DAMAGE_REPORT_GEO_INDEX_GEOHASH;

/**
 * this config must fixed after create data, change this value need recreating all data
 */
const HASH_KEY_LENGTH = 7;
export class GeoService {
    geoDataManager: GeoDataManager;
    constructor(public dynamoHelper: DynamoHelper) {
        const config = new GeoDataManagerConfiguration(
            dynamoHelper.client,
            dynamoHelper.dynamoClient,
            DAMAGE_REPORT_GEO_TABLE
        );
        config.hashKeyLength = HASH_KEY_LENGTH;
        config.geohashIndexName = DAMAGE_REPORT_GEO_INDEX_GEOHASH;
        this.geoDataManager = new GeoDataManager(config)
    }

    async createItem(damageItem: DamageReportItem) {
        const keyGeo = this.genKeyFromDamageData(damageItem);
        if (!keyGeo) return;
        await this.geoDataManager.putPoint({
            GeoPoint: keyGeo.GeoPoint,
            RangeKeyValue: keyGeo.RangeKeyValue
        })
    }
    async deleteItem(damageItem: DamageReportItem) {
        const keyGeo = this.genKeyFromDamageData(damageItem);
        if (!keyGeo) return;

        try {
            await this.geoDataManager.deletePoint({
                GeoPoint: keyGeo.GeoPoint,
                RangeKeyValue: keyGeo.RangeKeyValue
            })
        } catch (error) {
            console.error("error delete geo item ", error)
        }
    }
    async updateItem(damageItem: DamageReportItem, oldDamageItem?: DamageReportItem) {
        if (!damageItem) return;
        try {
            if (oldDamageItem?.location?.lat && oldDamageItem?.location?.long) {
                return await Promise.all([
                    this.deleteItem(oldDamageItem),
                    this.createItem(damageItem)
                ])
            }

            await this.createItem(damageItem)
        } catch (error) {
            console.error("error update geo item ", error)
        }
    }

    private genKeyFromDamageData(damageItem: DamageReportItem) {
        if (!damageItem.id || !damageItem.location?.lat || !damageItem.location?.long) return null;

        return {
            GeoPoint: {
                latitude: Number(damageItem.location.lat),
                longitude: Number(damageItem.location.long)
            },
            RangeKeyValue: { S: damageItem.id }
        } as {
            GeoPoint: GeoPoint,
            RangeKeyValue: AttributeValue
        }
    }
}