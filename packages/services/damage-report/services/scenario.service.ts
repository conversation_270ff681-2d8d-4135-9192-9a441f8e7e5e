import { GetCommand, QueryCommandInput, UpdateCommand } from "@aws-sdk/lib-dynamodb";
import { instanceToPlain, plainToInstance } from "class-transformer";
import { __DR_GROUPID_KEY, __DR_SCENARIO_DATA_DATAID } from "damage-report/constants";
import { CreateDRTalkVersionDto } from "damage-report/dto/scenario/create-dr-talk-version.dto";
import { DeleteDRTalkVersionDto } from "damage-report/dto/scenario/delete-dr-talk-version.dto";
import { DeleteScenarioDto } from "damage-report/dto/scenario/delete-scenario.dto";
import { SaveDrTalkDto } from "damage-report/dto/scenario/save-dr-talk.dto";
import { UpdateDRTalkActiveDto, UpdateDRTalkDto } from "damage-report/dto/scenario/update-dr-talk.dto";
import { ApiHelper } from "damage-report/helpers/api.helper";
import { <PERSON><PERSON><PERSON>per } from "damage-report/helpers/dynamo.helper";
import { <PERSON><PERSON><PERSON><PERSON> } from "damage-report/helpers/time.helper";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>equired, EScenarioDataType, ETalkVersionOf, IScenarioVersionInfo, TalkDataItem } from "damage-report/models/scenario-data.model";

const TABLE_CHATBOT_SCENARIO_DATA = process.env.TABLE_CHATBOT_SCENARIO_DATA;
const TABLE_CHATBOT_SCENARIO = process.env.TABLE_CHATBOT_SCENARIO;
const DAMAGE_REPORT_DATA_TABLE = process.env.DAMAGE_REPORT_DATA_TABLE;
const DAMAGE_REPORT_DATA_TABLE_INDEX_GROUPID_CREATED = process.env.DAMAGE_REPORT_DATA_TABLE_INDEX_GROUPID_CREATED;

const __DR_DR_FIELDS_REQUIRED_EVERY: EDRFieldRequired[] = [
    EDRFieldRequired.CATEGORY,
    EDRFieldRequired.SUBJECT,
    EDRFieldRequired.IMAGES,
    EDRFieldRequired.LOCATION,
    EDRFieldRequired.END_TALK
];

export class ScenarioService {
    constructor(public dynamoHelper: DynamoHelper) { }

    async createDRScenarioVersion(payload: CreateDRTalkVersionDto) {
        await ApiHelper.validateData(payload);

        payload.talkItem.versionOf = ETalkVersionOf.DAMAGE;
        const plainData = instanceToPlain(payload, { exposeUnsetFields: false });

        const existReportUseThisScenario = await this.checkExistReportUseScenario(payload.talkItem.scenario);
        if (existReportUseThisScenario) throw new Error("このシナリオは、既存の報告に使用されているので編集できません");

        await this.dynamoHelper.batchWriteItemV2(TABLE_CHATBOT_SCENARIO_DATA, [
            plainData.talkItem,
            ...(plainData.otherItem || [])
        ], [])
        const startMessageId = payload.talkItem.params.messages.find(x => x.sender == "BOT")?.messageId;
        let textMappingObject = payload.otherItem.find(x => x.dataType == EScenarioDataType.TEXT_MAPPING)?.textMapping;
        let textMapping = '';

        if (!textMappingObject) {
            const textMappingRecord = await this.getScenarioDataItem(payload.talkItem.scenario, EScenarioDataType.TEXT_MAPPING);
            if (textMappingRecord) textMappingObject = textMappingRecord.textMapping;
        }
        if (textMappingObject && startMessageId) {
            textMapping = Object.keys(textMappingObject).find(x => textMappingObject[x] == startMessageId);
        }

        // insert new version
        await this.updateDRTalk({
            scenario: payload.talkItem.scenario,
            talkDataId: payload.talkItem.dataId,
            name: payload.talkItem.params.name,
            startMessageId,
            textMapping
        });
    }
    async setDRTalkVersionActive(payload: UpdateDRTalkActiveDto) {
        await ApiHelper.validateData(payload);

        const existReportUseThisScenario = await this.checkExistReportUseScenario(payload.scenario);
        if (existReportUseThisScenario) throw new Error("このシナリオは、既存の報告に使用されているので編集できません");

        if (!payload.versionActive || payload.versionActive == __DR_SCENARIO_DATA_DATAID) {
            payload.versionActive = __DR_SCENARIO_DATA_DATAID;
            return await this.updateTalkDamageReport(payload, true);
        }
        
        const defaultTalkDRItem = await this.getTalkDamageReport(payload.scenario);
        if (!defaultTalkDRItem.versions?.length) throw new Error("このトークのバージョンは存在しません");
        if (!defaultTalkDRItem.versions.find(x => x.id == payload.versionActive)) throw new Error("指定されたIDに該当する報告は存在しません");

        // check version have all required fields
        const allMessage = await this.getAllMessageOfDR(payload.scenario, payload.versionActive);
        const passCheckRequired = __DR_DR_FIELDS_REQUIRED_EVERY.every(field => allMessage.find(m => m.targetDataField == field));
        if (!passCheckRequired) throw new Error("すべての必須フィールドを指定してください");

        return await this.updateTalkDamageReport(payload, true)
    }
    async getAllMessageOfDR(scenarioId: string, dataId?: string) {
        if (!scenarioId) return null;
        let talkDR = await this.getScenarioDataItem(scenarioId, dataId ?? __DR_SCENARIO_DATA_DATAID);

        if (talkDR?.versionActive && !dataId) talkDR = await this.getScenarioDataItem(scenarioId, talkDR.versionActive)
        if (!talkDR) throw new Error("損傷報告に該当するトークが見つかりません");
        const messages = talkDR.params.messages;

        const query: QueryCommandInput = {
            TableName: TABLE_CHATBOT_SCENARIO_DATA,
            KeyConditionExpression: "#scenario = :scenario",
            FilterExpression: '#dataType <> :textMapping AND #dataType <> :userMessage AND #dataType <> :talk',
            ExpressionAttributeNames: {
                '#scenario': 'scenario',
                '#dataType': 'dataType'
            },
            ExpressionAttributeValues: {
                ':scenario': scenarioId,
                ':textMapping': 'textMapping',
                ':userMessage': 'userMessage',
                ':talk': 'talk',
            }
        };

        const allRecords = await this.dynamoHelper.queryAll(query) as TalkDataItem[];

        const messageIdBot = messages.filter(x => x.sender == 'BOT').map(x => x.messageId);
        return allRecords.filter(x => messageIdBot.includes(x.dataId))
    }
    async getTalkDamageReport(scenarioId: string): Promise<TalkDataItem | undefined> {
        return await this.getScenarioDataItem(scenarioId, __DR_SCENARIO_DATA_DATAID);
    }
    async updateTalkDamageReport(payload: UpdateDRTalkDto, ignoreValidate = false) {
        if (!ignoreValidate) await ApiHelper.validateData(payload);

        const plainData = instanceToPlain(payload, { exposeUnsetFields: false }) as UpdateDRTalkDto;
        const expressionAttributeNames = {};
        const expressionAttributeValues = {};
        const arrExpression = [];
        const setExpression = (name: string, value: any) => {
            const nameExp = `#${name}`;
            const valExp = `:${name}`;

            expressionAttributeNames[nameExp] = name;
            expressionAttributeValues[valExp] = value;
            arrExpression.push(`${nameExp} = ${valExp}`)
        }

        if (payload.versions) setExpression('versions', plainData.versions);
        if (payload.versionActive) setExpression('versionActive', payload.versionActive == __DR_SCENARIO_DATA_DATAID ? null : payload.versionActive);

        if (!arrExpression.length) return null;
        const expression = "SET " + arrExpression.join(", ");

        await this.dynamoHelper.client.send(new UpdateCommand({
            TableName: TABLE_CHATBOT_SCENARIO_DATA,
            Key: { scenario: payload.scenario, dataId: __DR_SCENARIO_DATA_DATAID },
            UpdateExpression: expression,
            ExpressionAttributeNames: expressionAttributeNames,
            ExpressionAttributeValues: expressionAttributeValues
        }));
    }
    async getScenarioDataItem(scenarioId: string, dataId: string): Promise<TalkDataItem | undefined> {
        const res = await this.dynamoHelper.client.send(new GetCommand({
            TableName: TABLE_CHATBOT_SCENARIO_DATA,
            Key: {
                scenario: scenarioId,
                dataId: dataId
            }
        }));
        return res.Item as TalkDataItem;
    }
    async saveTalk(payload: SaveDrTalkDto) {
        await ApiHelper.validateData(payload);
        const talkDRId = payload.talkItem.dataId;
        payload.talkItem.versionOf = ETalkVersionOf.DAMAGE;
        if (talkDRId == __DR_SCENARIO_DATA_DATAID) throw new Error("デフォルトのトークは変更できません");

        const defaultTalkDRItem = await this.getTalkDamageReport(payload.talkItem.scenario);
        if (!defaultTalkDRItem.versions || !defaultTalkDRItem.versions.length) throw new Error("このトークのバージョンは存在しません");
        if (!defaultTalkDRItem.versions.find(x => x.id == talkDRId)) throw new Error("指定されたIDに該当する報告は存在しません");

        const existReportUseThisTalk = await this.checkExistReportUseTalkId(talkDRId);
        if (existReportUseThisTalk) throw new Error("このトークは、通報に使われているので編集できません");

        if (talkDRId == defaultTalkDRItem.versionActive) {
            const allMessageOfThisTalk = await this.getAllMessageOfDR(payload.talkItem.scenario, talkDRId);
            await this.checkVersionHadRequiredFields(payload.talkItem.scenario, talkDRId, [...payload.putItems, ...allMessageOfThisTalk])
        }
        const plainData = instanceToPlain(payload, { exposeUnsetFields: false }) as SaveDrTalkDto;

        const oldVersionsIdDelete = (payload.deleteItems || []).map(x => x.dataId);
        const startMessageId = payload.talkItem.params.messages.find(x => x.sender == "BOT")?.messageId;
        let textMappingObject = payload.putItems.find(x => x.dataType == EScenarioDataType.TEXT_MAPPING)?.textMapping;
        let textMapping = '';
        if (!textMappingObject) {
            const textMappingRecord = await this.getScenarioDataItem(payload.talkItem.scenario, EScenarioDataType.TEXT_MAPPING);
            if (textMappingRecord) textMappingObject = textMappingRecord.textMapping;
        }

        if (textMappingObject && startMessageId) {
            textMapping = Object.keys(textMappingObject).find(x => textMappingObject[x] == startMessageId);
        }

        await this.updateDRTalk({
            scenario: payload.talkItem.scenario,
            talkDataId: payload.talkItem.dataId,
            name: payload.talkItem.params.name,
            startMessageId,
            textMapping,
            _currentTalkDRItem: defaultTalkDRItem,
            oldVersionsIdDelete: oldVersionsIdDelete
        });
        await this.dynamoHelper.batchWriteItemV2(TABLE_CHATBOT_SCENARIO_DATA, [
            plainData.talkItem, ...plainData.putItems
        ], plainData.deleteItems)
    }
    async deleteTalkDRVersion(payload: DeleteDRTalkVersionDto) {
        await ApiHelper.validateData(payload);
        if (payload.versionId == __DR_SCENARIO_DATA_DATAID) throw new Error("このトークバージョンを削除できません");
        const existReportUseThisTalk = await this.checkExistReportUseTalkId(payload.versionId);
        if (existReportUseThisTalk) throw new Error("このトークバージョンを削除できません");

        const currentTalkDRItem = await this.getTalkDamageReport(payload.scenario);
        if (currentTalkDRItem.versionActive == payload.versionId) throw new Error("このトークバージョンを削除できません");

        const userMessage = await this.getScenarioDataItem(payload.scenario, EScenarioDataType.USER_MESSAGE);
        const textMapping = await this.getScenarioDataItem(payload.scenario, EScenarioDataType.TEXT_MAPPING);
        const currentTalkDelete = await this.getScenarioDataItem(payload.scenario, payload.versionId);
        const messageIds = currentTalkDelete.params.messages.map(x => x.messageId).filter(Boolean);

        userMessage.params = Object.keys(userMessage.params as Record<string, any>).reduce((res, key) => {
            if (!messageIds.includes(key)) res[key] = userMessage.params[key]
            return res
        }, {} as any);

        textMapping.textMapping = Object.keys(textMapping.textMapping as Record<string, any>).reduce((res, key) => {
            const _id = textMapping.textMapping[key];
            if (!messageIds.includes(_id)) res[key] = _id
            return res
        }, {} as any);

        currentTalkDRItem.versions = currentTalkDRItem.versions.filter(x => x.id != payload.versionId);
        await this.dynamoHelper.batchWriteItemV2(TABLE_CHATBOT_SCENARIO_DATA, [
            currentTalkDRItem,
            userMessage,
            textMapping
        ], [
            currentTalkDelete,
            ...messageIds.map(x => ({ scenario: payload.scenario, dataId: x }))
        ]);
        console.log("Delete DR version success")
    }
    async deleteScenario(payload: DeleteScenarioDto) {
        await ApiHelper.validateData(payload);
        const scenarioInfo = payload.getScenarioVersionData();
        if (!scenarioInfo) throw new Error("無効なペイロード");

        // get scenario version info
        const scenarioVersionInfo = await this.getScenarioVersionInfo(scenarioInfo.scenarioId, scenarioInfo.versionId);
        if (scenarioVersionInfo.specialTalks["damageReport"]) {
            const canNotDelete = await this.checkExistReportUseScenario(scenarioInfo.scenarioCurrent);
            if (canNotDelete) throw new Error("このシナリオは、通報に使われているので編集できません");
        }

        const plainData = instanceToPlain(payload, { exposeUnsetFields: false }) as DeleteScenarioDto;
        // delete all scenario data belong this scenario
        await this.dynamoHelper.batchWriteItem(TABLE_CHATBOT_SCENARIO_DATA, [], plainData.deleteItems);

        // update setting scenario remove version
        await this.dynamoHelper.client.send(new UpdateCommand({
            TableName: TABLE_CHATBOT_SCENARIO,
            Key: {
                scenarioId: scenarioInfo.scenarioId
            },
            UpdateExpression: "REMOVE versions.#v",
            ExpressionAttributeNames: {
                "#v": scenarioInfo.versionId
            }
        }));
        console.log("Delete scenario success")
    }

    // private
    private async checkExistReportUseTalkId(talkDRId: string) {
        if (!talkDRId || talkDRId == __DR_SCENARIO_DATA_DATAID) return true;
        const fromDateTime = TimeHelper.defaultFromDateQuery();
        const query: QueryCommandInput = {
            TableName: DAMAGE_REPORT_DATA_TABLE,
            IndexName: DAMAGE_REPORT_DATA_TABLE_INDEX_GROUPID_CREATED,
            KeyConditionExpression: "#groupId = :groupId AND #createdAt >= :createdAt",
            FilterExpression: "#markDelete <> :markDelete AND #talkDRId = :talkDRId",
            ExpressionAttributeNames: {
                "#groupId": "groupId",
                "#createdAt": "createdAt",
                '#markDelete': 'markDelete',
                '#talkDRId': 'talkDRId'
            },
            ExpressionAttributeValues: {
                ":groupId": __DR_GROUPID_KEY,
                ":createdAt": fromDateTime,
                ':markDelete': true,
                ':talkDRId': talkDRId
            }
        };

        const results = await this.dynamoHelper.queryTillGetExistItem(query);
        return results.length > 0
    }
    private async checkExistReportUseScenario(scenario: string) {
        const fromDateTime = TimeHelper.defaultFromDateQuery();
        const query: QueryCommandInput = {
            TableName: DAMAGE_REPORT_DATA_TABLE,
            IndexName: DAMAGE_REPORT_DATA_TABLE_INDEX_GROUPID_CREATED,
            KeyConditionExpression: "#groupId = :groupId AND #createdAt >= :createdAt",
            FilterExpression: "#markDelete <> :markDelete AND #scenarioId = :scenarioId",
            ExpressionAttributeNames: {
                "#groupId": "groupId",
                "#createdAt": "createdAt",
                '#markDelete': 'markDelete',
                '#scenarioId': 'scenarioId'
            },
            ExpressionAttributeValues: {
                ":groupId": __DR_GROUPID_KEY,
                ":createdAt": fromDateTime,
                ':markDelete': true,
                ':scenarioId': scenario
            }
        };

        const results = await this.dynamoHelper.queryTillGetExistItem(query);
        return results.length > 0
    }
    private async checkVersionHadRequiredFields(scenario: string, versionActive: string, messages: TalkDataItem[] = null) {
        const allMessage = messages ?? await this.getAllMessageOfDR(scenario, versionActive);
        const passCheckRequired = __DR_DR_FIELDS_REQUIRED_EVERY.every(field => allMessage.find(m => m.targetDataField == field));
        if (!passCheckRequired) throw new Error("すべての必須フィールドを指定してください");
        return passCheckRequired
    }
    private async updateDRTalk(data: {
        scenario: string,
        talkDataId: string,
        name: string,
        startMessageId: string,
        textMapping: string,
        _currentTalkDRItem?: TalkDataItem | null,
        oldVersionsIdDelete?: string[]
    }) {
        let {
            scenario,
            talkDataId,
            name,
            startMessageId,
            textMapping,
            _currentTalkDRItem = null,
            oldVersionsIdDelete = []
        } = data;

        if (oldVersionsIdDelete.includes(talkDataId)) oldVersionsIdDelete = [];

        const currentTalkDRItem = _currentTalkDRItem || await this.getTalkDamageReport(scenario);
        currentTalkDRItem.versions = [
            ...(currentTalkDRItem.versions || []),
            { id: talkDataId, name: name, startMessageId, textMapping }
        ].filter(x => !oldVersionsIdDelete.includes(x.id));
        
        // Dedup version, keep lastest
        const mapObj = new Map(
            currentTalkDRItem.versions?.map(obj => {
                return [obj.id, obj];
            }),
        )
        const uniqueIdsArrayVersion = Array.from(mapObj, ([name, value]) => ({
            ...value,
        }));

        const dataUpdate = {
            scenario: currentTalkDRItem.scenario,
            dataId: currentTalkDRItem.dataId,
            versions: uniqueIdsArrayVersion
        } as UpdateDRTalkDto;

        if (currentTalkDRItem.versionActive && oldVersionsIdDelete.includes(currentTalkDRItem.versionActive)) {
            dataUpdate.versionActive = talkDataId
        }

        const updateTalk = plainToInstance(UpdateDRTalkDto, dataUpdate);
        await this.updateTalkDamageReport(updateTalk)
    }
    private async getScenarioVersionInfo(scenarioId: string, versionScenario: string) {
        const resGetScenario = await this.dynamoHelper.client.send(new GetCommand({
            TableName: TABLE_CHATBOT_SCENARIO,
            Key: { scenarioId: scenarioId }
        }));

        const item = resGetScenario.Item;
        if (!item) throw new Error("シナリオが見つかりません")
        return item.versions[versionScenario] as IScenarioVersionInfo
    }
}
