
import { Batch<PERSON><PERSON><PERSON><PERSON>mand, GetCommand, QueryCommand, QueryCommandInput, UpdateCommand } from "@aws-sdk/lib-dynamodb";
import { EChatEvent, EChatRole, IChatMember, IGroup, IMessage } from "damage-report/@chat-notify";
import { __CHAT_UID_MANAGER } from "damage-report/constants";
import { BatchGetGroupChatDto, CreateGroupChatDto, GetGroupChatDto } from "damage-report/dto/get-group-chat.dto";
import { ListMessageDto } from "damage-report/dto/list-message.dto";
import { UpdateUnreadDto } from "damage-report/dto/update-unread.dto";
import { Api<PERSON>elper } from "damage-report/helpers/api.helper";
import { DynamoHelper } from "damage-report/helpers/dynamo.helper";
import { PushMessageHelper } from "damage-report/helpers/push-message.helper";
import { DamageReportItem } from "damage-report/models/damage-report.model";
import { UtilsService } from "./utils.service";
import { Utils } from "damage-report/utils";

const CHAT_MESSAGE_TABLE = process.env.CHAT_MESSAGE_TABLE;
const CHAT_MEMBER_TABLE = process.env.CHAT_MEMBER_TABLE;
const CHAT_MESSAGE_TABLE_TIMESTAMP_INDEX = process.env.CHAT_MESSAGE_TABLE_TIMESTAMP_INDEX;
const CHAT_MEMBER_TABLE_GID_INDEX = process.env.CHAT_MEMBER_TABLE_GID_INDEX;

const DAMAGE_REPORT_DATA_TABLE = process.env.DAMAGE_REPORT_DATA_TABLE;

export class ChatService {
    private utilsService: UtilsService;
    private pushMessageHelper: PushMessageHelper;
    constructor(public dynamoHelper: DynamoHelper) {
        this.utilsService = new UtilsService();
        this.pushMessageHelper = new PushMessageHelper(dynamoHelper.client)
    }

    async listMessage(payload: ListMessageDto) {
        await ApiHelper.validateData(payload);
        const group = await this.getGoupByGid(payload.gid);
        if (!group) return { items: [] }

        const input: QueryCommandInput = {
            TableName: CHAT_MESSAGE_TABLE,
            IndexName: CHAT_MESSAGE_TABLE_TIMESTAMP_INDEX,
            KeyConditionExpression: '#gid = :gid',
            ExpressionAttributeNames: {
                '#gid': 'gid',
            },
            ExpressionAttributeValues: {
                ':gid': payload.gid,
            },
            Limit: payload.limit || 20,
            ScanIndexForward: false,
        };

        if (payload.lastEvaluatedKey) input.ExclusiveStartKey = payload.lastEvaluatedKey;
        const result = await this.dynamoHelper.client.send(new QueryCommand(input));
        let items = [];
        if (result.Items?.length) {
            items = await Promise.all(result.Items.map(async message => {
                const _mesg = { ...message } as IMessage;
                if (_mesg.files) {
                    _mesg.files = await Promise.all(
                        _mesg.files.map(async (file) => {
                            const _attachment = { ...file };
                            _attachment.url = await this.utilsService.signedFileChat(file);
                            return _attachment;
                        }),
                    );
                }
                return _mesg;
            }))
        }
        return {
            items,
            lastEvaluatedKey: result.LastEvaluatedKey,
        }
    }
    async getGroup(data: GetGroupChatDto) {
        await ApiHelper.validateData(data);
        const groupMembers = await this.getGoupByGid(data.gid);

        if (!groupMembers) return null;

        const members = groupMembers.members as IChatMember[];
        const memberSelect = members.find(m => m.id == data.uid);
        const group: IGroup = {
            id: data.gid,
            name: 'group',
            unread_count: memberSelect?.unread_count || 0,
            unread_count_plus: memberSelect?.unread_count_plus || false,
            members: members
        };
        return group
    }
    async createGroup(data: CreateGroupChatDto) {
        await ApiHelper.validateData(data);

        const group = await this.getGroup(data);
        if (group) return group;

        const damageReport = await this.getDamageReport(data.gid);
        if (!damageReport || damageReport.from != 'chatbot') throw new Error("このグループを作成できません");


        const _data_manager: IChatMember = {
            id: __CHAT_UID_MANAGER,
            gid: data.gid,
            name: 'manager',
            latest_time_read: Date.now(),
            unread_count: 0,
            role: EChatRole.MANAGER,
        };

        const _data_line_user: IChatMember = {
            id: damageReport.uid,
            gid: data.gid,
            name: damageReport.userName,
            latest_time_read: Date.now(),
            unread_count: 0,
            role: EChatRole.LINE_USER,
        }

        await this.dynamoHelper.client.send(
            new BatchWriteCommand({
                RequestItems: {
                    [CHAT_MEMBER_TABLE]: [
                        {
                            PutRequest: {
                                Item: _data_line_user,
                            },
                        },
                        {
                            PutRequest: {
                                Item: _data_manager,
                            },
                        },
                    ],
                },
            }),
        );

        return {
            id: data.gid,
            name: "group",
            unread_count: 0,
            unread_count_plus: false,
            members: [_data_manager, _data_line_user],
        } as IGroup
    }
    async updateUnread(data: UpdateUnreadDto) {
        await ApiHelper.validateData(data);

        await this.dynamoHelper.client.send(new UpdateCommand({
            TableName: CHAT_MEMBER_TABLE,
            Key: { id: data.uid, gid: data.gid },
            UpdateExpression: 'SET #latest_time_read = :latest_time_read',
            ExpressionAttributeNames: {
                '#latest_time_read': 'latest_time_read',
            },
            ExpressionAttributeValues: {
                ':latest_time_read': data.latestTimeRead,
            },
        }));

        await this.pushMessageHelper.toGroup(data.gid, {
            event_name: EChatEvent.READ_TIME_CHANGE,
            payload: {
                uid: data.uid,
                gid: data.gid,
                latestTimeRead: data.latestTimeRead,
            },
        })
    }

    async batchGetGroup(data: BatchGetGroupChatDto) {
        await ApiHelper.validateData(data);
        const batchSize = 25;
        const result: Record<string,boolean> = {}
        for (let index = 0; index < data.gid.length; index += batchSize) {
            const batch: string[] = data.gid.slice(index, index + batchSize);
            await Promise.all(batch.map(async (gid) => {
                let res = await this.dynamoHelper.client.send(new QueryCommand({
                    TableName: CHAT_MEMBER_TABLE,
                    IndexName: CHAT_MEMBER_TABLE_GID_INDEX,
                    KeyConditionExpression: "#gid = :gid AND #uid = :uid",
                    ExpressionAttributeNames: {
                        "#gid": "gid",
                        "#uid": "id"
                    },
                    ExpressionAttributeValues: {
                        ":gid": gid,
                        ":uid": data.uid,
                    },
                    ProjectionExpression: "gid, last_notify, latest_time_read"
                }))
                if (res && res.Items && res.Items[0]) {
                    result[gid] = res.Items[0].last_notify === res.Items[0].latest_time_read;
                }
                else {
                    result[gid] = false
                }
            }))
            await Utils.delay(100);
        }
        return result
    }

    // protected
    protected async getGoupByGid(gid: string) {
        const damageReport = await this.getDamageReport(gid);
        if (!damageReport) return null;

        const res = await this.dynamoHelper.client.send(new QueryCommand({
            TableName: CHAT_MEMBER_TABLE,
            IndexName: CHAT_MEMBER_TABLE_GID_INDEX,
            KeyConditionExpression: '#gid = :gid',
            ExpressionAttributeNames: {
                '#gid': 'gid'
            },
            ExpressionAttributeValues: {
                ':gid': gid
            }
        }));

        if (!res.Items || !res.Items.length) return null;
        return {
            id: gid,
            name: 'group',
            members: res.Items
        } as IGroup
    }
    private async getDamageReport(id: string) {
        const res = await this.dynamoHelper.client.send(new GetCommand({
            TableName: DAMAGE_REPORT_DATA_TABLE,
            Key: { id }
        }));
        const item = res.Item as DamageReportItem;
        if (!item || item.markDelete) return null;
        return item
    }
}