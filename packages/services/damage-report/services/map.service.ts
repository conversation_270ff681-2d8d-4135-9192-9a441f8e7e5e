import { QueryRadiusDto, QueryRectangleDto } from "damage-report/dto/query-map.dto";
import { Api<PERSON>elper } from "damage-report/helpers/api.helper";
import { DynamoHelper } from "damage-report/helpers/dynamo.helper";
import { DamageReportService } from "./damage-report.service";

export class MapService {
    private damageReportService: DamageReportService
    constructor(public dynamoHelper: DynamoHelper) {
        this.damageReportService = new DamageReportService(dynamoHelper)
    }

    async queryRectangle(payload: QueryRectangleDto) {
        await ApiHelper.validateData(payload);
        if (!payload.checkMinMax()) throw new Error("無効なペイロード")

        const results = await this.damageReportService.geoService.geoDataManager.queryRectangle({
            MinPoint: payload.minPoint,
            MaxPoint: payload.maxPoint
        });
        if (!results || !results.length) return [];

        return this.damageReportService.getAllItemByListId(results.map(x => x.damageId))
    }

    async queryRadius(payload: QueryRadiusDto) {
        await ApiHelper.validateData(payload);

        const results = await this.damageReportService.geoService.geoDataManager.queryRadius({
            RadiusInMeter: payload.radiusInMeter,
            CenterPoint: payload.centerPoint
        });
        if (!results || !results.length) return [];

        return this.damageReportService.getAllItemByListId(results.map(x => x.damageId))
    }
}