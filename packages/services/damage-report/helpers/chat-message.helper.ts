import { Delete<PERSON>om<PERSON>, DynamoDBDocumentClient, <PERSON><PERSON><PERSON><PERSON>, <PERSON>C<PERSON><PERSON>, UpdateCommand } from "@aws-sdk/lib-dynamodb";
import { IChatReactEvent, IDeleteEvent, IEditEvent, IMessage, IRecallEvent, ISendMessageEvent } from "damage-report/@chat-notify";
import { v4 as uuidv4 } from "uuid";

const CHAT_MESSAGE_TABLE = process.env.CHAT_MESSAGE_TABLE;

export class ChatMessageHelper {
    dynamo: DynamoDBDocumentClient;
    constructor(private gid: string, dynamo: DynamoDBDocumentClient) {
        this.dynamo = dynamo;
    }
    async newMessage(payload: Partial<ISendMessageEvent>) {
        const id = uuidv4();
        const _payload: Partial<ISendMessageEvent> = {
            ...payload,
            timestamp: Date.now(),
            id
        }
        const { messageIdTemp, ...item } = _payload;
        item.gid = this.gid;
        await this.dynamo.send(new PutCommand({ TableName: CHAT_MESSAGE_TABLE, Item: item }));
        return _payload as ISendMessageEvent
    }
    async reactMessage(data: IChatReactEvent) {
        const message = await this.getMessageById(data.messageId);
        if (!message) return [];
        const { gid, messageId, ..._data } = data;
        _data.timestamp = Date.now();
        const newReactData = message.react && Array.isArray(message.react) ? [...message.react, _data] : [_data];
        await this.putMessage({ ...message, react: newReactData });
        return newReactData
    }
    async editMessage(data: IEditEvent) {
        const time_edited = Date.now();
        await this.dynamo.send(new UpdateCommand({
            TableName: CHAT_MESSAGE_TABLE,
            Key: { gid: data.gid, id: data.messageId },
            UpdateExpression: "SET #content = :content_edit, #time_edited = :time_edited",
            ExpressionAttributeNames: {
                "#content": "content",
                "#time_edited": "time_edited"
            },
            ExpressionAttributeValues: {
                ":content_edit": data.contentEdit,
                ":time_edited": time_edited
            }
        }));
        return time_edited
    }
    deleteMessage(data: IDeleteEvent) {
        return this.dynamo.send(new DeleteCommand({ TableName: CHAT_MESSAGE_TABLE, Key: { gid: data.gid, id: data.messageId } }))
    }
    async recallMessage(data: IRecallEvent): Promise<Partial<IMessage>> {
        await this.dynamo.send(new UpdateCommand({
            TableName: CHAT_MESSAGE_TABLE,
            Key: { gid: data.gid, id: data.messageId },
            UpdateExpression: "SET #content = :empty, #time_edited = :time_edited, #unavailable = :unavailable",
            ExpressionAttributeNames: {
                "#unavailable": "unavailable",
                "#content": "content",
                "#time_edited": "time_edited"
            },
            ExpressionAttributeValues: {
                ":empty": '',
                ":unavailable": true,
                ":time_edited": Date.now()
            }
        }));
        return { gid: data.gid, id: data.messageId, unavailable: true }
    }
    
    /** private */
    private async getMessageById(id: string): Promise<IMessage | undefined> {
        const result = await this.dynamo.send(new GetCommand({ TableName: CHAT_MESSAGE_TABLE, Key: { gid: this.gid, id } }));
        return result.Item as IMessage
    }
    private putMessage(newItem: Partial<IMessage>) {
        return this.dynamo.send(new PutCommand({ TableName: CHAT_MESSAGE_TABLE, Item: newItem }))
    }
}