import { validate, validateSync } from "class-validator";

export class <PERSON><PERSON><PERSON>el<PERSON> {
    static async validateData(data: any) {
        const errors = await validate(data);
        if (errors.length) throw new Error(errors[0].toString());
    }
    static validateDataSync(data: any) {
        const errors = validateSync(data);
        if (errors.length) throw new Error(errors[0].toString());
    }
}