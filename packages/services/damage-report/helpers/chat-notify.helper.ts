import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient, GetCommand, PutCommand, QueryCommand } from "@aws-sdk/lib-dynamodb";
import { APIGatewayEvent, APIGatewayEventRequestContext, APIGatewayProxyEvent } from "aws-lambda";
import { EHttpMethod, ERouteKey, IConnection, IWSActionMessage } from "damage-report/@chat-notify";


const WS_CONNECTION_TABLE = process.env.WS_CONNECTION_TABLE!;
const WS_CONNECTION_ID_INDEX = process.env.WS_CONNECTION_ID_INDEX!;
const CHAT_CONNECTION_GROUP_TABLE = process.env.CHAT_CONNECTION_GROUP_TABLE!;
const CHAT_MEMBER_TABLE = process.env.CHAT_MEMBER_TABLE!;
const REGION = process.env.REGION || 'ap-northeast-1';


const _dynamo = DynamoDBDocumentClient.from(new DynamoDBClient({ region: REGION }));

export class ChatNotifyHelper {
    static handleCommonEvent<T>(event: APIGatewayEvent, _routeKey: ERouteKey) {
        const _context = event.requestContext as APIGatewayEventRequestContext;
        const _connecttionId = _context.connectionId;
        const routeKey = _context.routeKey as ERouteKey;
        if (!_connecttionId || routeKey != _routeKey) throw new Error('error event');

        const _body = event.body ? typeof event.body == 'string' ? JSON.parse(event.body) : null : null;
        const final_data = typeof _body.body == 'string' ? JSON.parse(_body.body) : _body.body;
        if (!final_data) {
            console.error("Error no payload request")
            throw new Error('error event');
        }

        return {
            data: final_data as IWSActionMessage<T>,
            connecttionId: _connecttionId
        }
    }
    static async checkConnectionIdInGroup(connection_id: string, gid: string) {
        if (!connection_id || !gid) return false;
        const resCheckConnectionGroup = await ChatNotifyHelper.dynamo.send(new GetCommand({ TableName: CHAT_CONNECTION_GROUP_TABLE, Key: { connection_id, gid } }));
        if (resCheckConnectionGroup.Item) return true;

        const resQuery = await ChatNotifyHelper.dynamo.send(new QueryCommand({
            TableName: WS_CONNECTION_TABLE,
            IndexName: WS_CONNECTION_ID_INDEX,
            KeyConditionExpression: "#connection_id = :connection_id",
            ExpressionAttributeNames: {
                "#connection_id": "connection_id"
            },
            ExpressionAttributeValues: {
                ":connection_id": connection_id
            }
        }));
        if (resQuery.Items?.length == 1) {
            const _connection_record = resQuery.Items[0] as IConnection;
            const res = await ChatNotifyHelper.dynamo.send(new GetCommand({
                TableName: CHAT_MEMBER_TABLE,
                Key: { id: _connection_record.uid, gid }
            }));
            if (res.Item) {
                await ChatNotifyHelper.dynamo.send(new PutCommand({
                    TableName: CHAT_CONNECTION_GROUP_TABLE,
                    Item: {
                        connection_id,
                        gid,
                        uid: _connection_record.uid,
                        uid_origin: _connection_record.uid_origin,
                        expire_at: ChatNotifyHelper.expireAtCalculate(1)
                    }
                }))
                return true
            }
        }
        return false
    }
    static handleCommonApiGatewayInit(event: APIGatewayProxyEvent) {
        if (!event.pathParameters || !event.pathParameters['proxy']) throw new Error('error event');
        const proxy = event.pathParameters['proxy'];
        let body: any;
        if (event.body) {
            try {
                body = JSON.parse(event.body);
            } catch (error) {
                console.error("Error parse body")
            }
        }
        if (!body) throw new Error('error event');
        const isManager = event.path.includes("manager");

        return { proxy, body, method: event.httpMethod as EHttpMethod, isManager }
    }
    static get dynamo() {
        return _dynamo
    }
    static expireAtCalculate(days: number) {
        return Date.now() / 1000 + days * 24 * 60 * 60
    }
}