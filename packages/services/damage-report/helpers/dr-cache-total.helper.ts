import { Batch<PERSON>riteCommand } from "@aws-sdk/lib-dynamodb";
import { Utils } from "damage-report/utils";
import { <PERSON><PERSON><PERSON><PERSON> } from "./dynamo.helper";
import { __EMPTY_VALUE, __FILTER_CACHE_ALL, __KEY_CACHE_ALL, __ORDER_FIELD_BASED_ON_CATEGORY, __ORDER_FIELD_BASED_ON_SCENARIO, __ORDER_FIELD_BASED_ON_STATUS, __ORDER_FIELD_BASED_ON_SUBJECT } from "damage-report/constants";

const CACHE_TOTAL_DR_TABLE = process.env.CACHE_TOTAL_DR_TABLE;

export class DamageReportCacheTotalHelper {
    private allCacheItems: ICacheDRItem[] = [];
    private updatedAt = Date.now();

    constructor(public dynamoHelper: DynamoHelper) { }

    async getAllDataCache() {
        const result = await this.dynamoHelper.scanAll({
            TableName: CACHE_TOTAL_DR_TABLE
        });
        this.allCacheItems = result as ICacheDRItem[]
        return this.allCacheItems
    }
    async insertDR(data: IDamageReportItem) {
        if (!data) return;

        const arrMerge = [
            this.genStatus(data),
            this.genCategory(data),
            this.genScenario(data),
            this.genSubject(data),
            this.genUsername(data),
        ].flat();


        const arrMergeUpdate = arrMerge.map(item => {
            const itemMatch = this.allCacheItems.find(x => x.key == item.key && x.filter == item.filter);
            if (itemMatch) item.total += itemMatch.total;
            return item
        })

        arrMergeUpdate.push(this.updateAllTotalItem());
        // put or update arrMergeUpdate item to table
        await this.batchWriteItem(arrMergeUpdate)
    }
    async deleteDR(data: IDamageReportItem) {
        if (!data) return;
        const arrMerge = [
            this.genStatus(data),
            this.genCategory(data),
            this.genScenario(data),
            this.genSubject(data),
            this.genUsername(data),
        ].flat();

        const arrUpdate: ICacheDRItem[] = [];
        arrMerge.forEach(item => {
            const itemMatch = this.allCacheItems.find(x => x.key == item.key && x.filter == item.filter);
            if (itemMatch) {
                item.total = itemMatch.total - 1;
                arrUpdate.push(item)
            }
        })
        arrUpdate.push(this.updateAllTotalItem(false));
        await this.batchWriteItem(arrUpdate)
    }
    async updateDR(oldData: IDamageReportItem, data: IDamageReportItem) {
        if (!data && !oldData) return;
        if (data.markDelete && !oldData.markDelete) return await this.deleteDR(data)

        const arrTotalMinus: ICacheDRItem[][] = [];
        const arrTotalPlus: ICacheDRItem[][] = [];

        if (data.status != oldData.status) {
            const key = `status=${oldData.status}`;
            const minusData = this.allCacheItems.filter(x => x.key == key);
            arrTotalMinus.push(minusData);

            const plusData = this.genStatus(data);
            arrTotalPlus.push(plusData)
        }

        if (data.category != oldData.category) {
            const key = `category=${oldData.category}`;
            const minusData = this.allCacheItems.filter(x => x.key == key || x.filter.includes(key));
            arrTotalMinus.push(minusData);

            const plusData = this.genCategory(data);
            arrTotalPlus.push(plusData)
        }

        if (data.scenarioId != oldData.scenarioId) {
            const key = `scenarioId=${oldData.scenarioId}`;
            const minusData = this.allCacheItems.filter(x => x.key == key || x.filter.includes(key));
            arrTotalMinus.push(minusData);

            const plusData = this.genScenario(data);
            arrTotalPlus.push(plusData)
        }

        if (data.subject != oldData.subject) {
            const key = `subject=${oldData.subject}`;
            const minusData = this.allCacheItems.filter(x => x.key == key || x.filter.includes(key));
            arrTotalMinus.push(minusData);

            const plusData = this.genSubject(data);
            arrTotalPlus.push(plusData)
        }

        if (data.userName != oldData.userName) {
            const key = `userName=${oldData.userName}`;
            const minusData = this.allCacheItems.filter(x => x.key == key || x.filter.includes(key));
            arrTotalMinus.push(minusData);

            const plusData = this.genUsername(data);
            arrTotalPlus.push(plusData)
        }

        const arrFinalUpdate: ICacheDRItem[] = [];
        arrTotalMinus.flat().forEach(x => {
            arrFinalUpdate.push({
                ...x,
                total: x.total - 1,
                updatedAt: this.updatedAt
            })
        });
        arrTotalPlus.flat().forEach(item => {
            const itemMatch = this.allCacheItems.find(x => x.key == item.key && x.filter == item.filter);
            if (itemMatch) item.total += itemMatch.total;
            arrFinalUpdate.push(item)
        });

        await this.batchWriteItem(arrFinalUpdate)
    }
    async calculateTotal(data: IDamageReportItem[]) {
        if (!data?.length) return;

        const dataCache: ICacheDRItem[] = [
            {
                key: __KEY_CACHE_ALL,
                filter: __FILTER_CACHE_ALL,
                total: data.length,
                updatedAt: this.updatedAt
            }
        ];

        data.forEach(dr => {
            const arrMerge = [
                this.genStatus(dr),
                this.genCategory(dr),
                this.genScenario(dr),
                this.genSubject(dr),
                this.genUsername(dr),
            ].flat();

            arrMerge.forEach(cacheItem => {
                const _match = dataCache.find(x => x.key == cacheItem.key && x.filter == cacheItem.filter);
                if (_match) _match.total += 1
                else dataCache.push(cacheItem)
            })
        })

        await this.batchWriteItem(dataCache, 200)
    }

    /**
     * privates
     * @returns 
     */

    private genStatus(data: IDamageReportItem) {
        const arr: ICacheDRItem[] = [];
        const updatedAt = this.updatedAt;
        if (!data.status) return arr;

        const addFilterFn = (filterKey: string = __EMPTY_VALUE) => {
            arr.push({
                key: `status=${data.status}`,
                filter: filterKey,
                total: 1,
                updatedAt
            });
        }
        // default item
        addFilterFn();

        const arrFilterKey: string[] = [];
        // one condition
        __ORDER_FIELD_BASED_ON_STATUS.forEach(key => arrFilterKey.push(`${key}=${data[key] || __EMPTY_VALUE}`));

        // two condition
        const twoKeyFiled: Partial<Record<TFieldDR, TFieldDR[]>> = {
            'category': ['scenarioId', 'subject', 'userName'],
            'scenarioId': ['subject', 'userName'],
            'subject': ['userName']
        };
        Object.keys(twoKeyFiled).forEach(key => {
            const _arr = twoKeyFiled[key] as TFieldDR[];
            const _keyVal = `${key}=${data[key] || __EMPTY_VALUE}`;
            _arr.forEach(key2 => arrFilterKey.push(`${_keyVal}|${key2}=${data[key2] || __EMPTY_VALUE}`))
        })

        // three condition
        const threeCondition: Partial<Record<string, TFieldDR[]>> = {
            'category,scenarioId': ['subject', 'userName'],
            'scenarioId,subject': ['userName']
        }
        Object.keys(threeCondition).forEach(key => {
            const _arr = threeCondition[key] as TFieldDR[];
            const _arrKey = key.split(',') as TFieldDR[];
            const _filter1 = _arrKey.map(x => `${x}=${data[x] || __EMPTY_VALUE}`).join('|');
            _arr.forEach(key2 => arrFilterKey.push(`${_filter1}|${key2}=${data[key2] || __EMPTY_VALUE}`))
        })

        // four condition
        arrFilterKey.push(__ORDER_FIELD_BASED_ON_STATUS.map(x => `${x}=${data[x] || __EMPTY_VALUE}`).join('|'))

        // create each item from that key
        arrFilterKey.forEach(x => addFilterFn(x))
        return arr
    }
    private genCategory(data: IDamageReportItem) {
        const arr: ICacheDRItem[] = [];
        const updatedAt = this.updatedAt;
        if (!data.category) return arr;

        const addFilterFn = (filterKey: string = __EMPTY_VALUE) => {
            arr.push({
                key: `category=${data.category}`,
                filter: filterKey,
                total: 1,
                updatedAt
            });
        }
        // default item
        addFilterFn();

        const arrFilterKey: string[] = [];
        // one condition
        __ORDER_FIELD_BASED_ON_CATEGORY.forEach(key => arrFilterKey.push(`${key}=${data[key] || __EMPTY_VALUE}`));

        // two condition
        const twoKeyFiled: Partial<Record<TFieldDR, TFieldDR[]>> = {
            'scenarioId': ['subject', 'userName'],
            'subject': ['userName']
        };
        Object.keys(twoKeyFiled).forEach(key => {
            const _arr = twoKeyFiled[key] as TFieldDR[];
            const _keyVal = `${key}=${data[key] || __EMPTY_VALUE}`;
            _arr.forEach(key2 => arrFilterKey.push(`${_keyVal}|${key2}=${data[key2] || __EMPTY_VALUE}`))
        })

        // three condition
        const threeCondition: Partial<Record<string, TFieldDR[]>> = {
            'scenarioId,subject': ['userName']
        }
        Object.keys(threeCondition).forEach(key => {
            const _arr = threeCondition[key] as TFieldDR[];
            const _arrKey = key.split(',') as TFieldDR[];
            const _filter1 = _arrKey.map(x => `${x}=${data[x] || __EMPTY_VALUE}`).join('|');
            _arr.forEach(key2 => arrFilterKey.push(`${_filter1}|${key2}=${data[key2] || __EMPTY_VALUE}`))
        })

        // create each item from that key
        arrFilterKey.forEach(x => addFilterFn(x))
        return arr
    }
    private genScenario(data: IDamageReportItem) {
        const arr: ICacheDRItem[] = [];
        const updatedAt = this.updatedAt;
        if (!data.scenarioId) return arr;

        const addFilterFn = (filterKey: string = __EMPTY_VALUE) => {
            arr.push({
                key: `scenarioId=${data.scenarioId}`,
                filter: filterKey,
                total: 1,
                updatedAt
            });
        }
        // default item
        addFilterFn();

        const arrFilterKey: string[] = [];
        // one condition
        __ORDER_FIELD_BASED_ON_SCENARIO.forEach(key => arrFilterKey.push(`${key}=${data[key] || __EMPTY_VALUE}`));

        // two condition
        const twoKeyFiled: Partial<Record<TFieldDR, TFieldDR[]>> = {
            'subject': ['userName']
        };
        Object.keys(twoKeyFiled).forEach(key => {
            const _arr = twoKeyFiled[key] as TFieldDR[];
            const _keyVal = `${key}=${data[key] || __EMPTY_VALUE}`;
            _arr.forEach(key2 => arrFilterKey.push(`${_keyVal}|${key2}=${data[key2] || __EMPTY_VALUE}`))
        })

        // create each item from that key
        arrFilterKey.forEach(x => addFilterFn(x))
        return arr
    }
    private genSubject(data: IDamageReportItem) {
        const arr: ICacheDRItem[] = [];
        const updatedAt = this.updatedAt;
        if (!data.subject) return arr;

        const addFilterFn = (filterKey: string = __EMPTY_VALUE) => {
            arr.push({
                key: `subject=${data.subject}`,
                filter: filterKey,
                total: 1,
                updatedAt
            });
        }
        // default item
        addFilterFn();

        const arrFilterKey: string[] = [];
        // one condition
        __ORDER_FIELD_BASED_ON_SUBJECT.forEach(key => arrFilterKey.push(`${key}=${data[key] || __EMPTY_VALUE}`));
        // create each item from that key
        arrFilterKey.forEach(x => addFilterFn(x))

        return arr
    }
    private genUsername(data: IDamageReportItem) {
        if (!data.userName) return [];
        const arr: ICacheDRItem[] = [
            {
                key: `userName=${data.userName}`,
                filter: __EMPTY_VALUE,
                total: 1,
                updatedAt: this.updatedAt
            }
        ];
        return arr
    }

    private updateAllTotalItem(isPlus = true) {
        const item = this.allCacheItems.find(x => x.key == __KEY_CACHE_ALL && x.filter == __FILTER_CACHE_ALL);
        let total = 1;
        if (item) total = isPlus ? item.total + 1 : item.total - 1
        return {
            key: __KEY_CACHE_ALL,
            filter: __FILTER_CACHE_ALL,
            total,
            updatedAt: this.updatedAt
        } as ICacheDRItem
    }

    /** Batch write put item */
    private async batchWriteItem(data: ICacheDRItem[], delay = 100) {
        const delta = 25;
        for (let index = 0; index < data.length; index += delta) {
            const arr = [];
            for (let index2 = index; index2 < index + delta && index2 < data.length; index2++) {
                const element = data[index2];
                if (element) arr.push(element)
            }

            await Utils.delay(delay);
            const arrPut = arr.map(item => { return { PutRequest: { Item: item } } });
            await this.dynamoHelper.client.send(new BatchWriteCommand({
                RequestItems: {
                    [CACHE_TOTAL_DR_TABLE]: arrPut
                }
            }));
        }
    }
}