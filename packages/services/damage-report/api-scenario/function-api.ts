import 'reflect-metadata';

import { talkRoutersFactory, scenarioVersionRoutersFactory } from 'damage-report/controllers/scenario.controller';
import { DynamoHelper } from "damage-report/helpers/dynamo.helper";
import { apiHandlerFactory } from "../../common/admin-api/api-lambda";

export const apiHandler = apiHandlerFactory('/scenario', api => {
    const dynamoHelper = new DynamoHelper();

    api.register(talkRoutersFactory({dynamo: dynamoHelper}), { prefix: '/damage-report' });
    api.register(scenarioVersionRoutersFactory({dynamo: dynamoHelper}), { prefix: '/version' });
})