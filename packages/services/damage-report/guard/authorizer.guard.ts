
import { ERoleDamageReport } from "damage-report/models/roles";
import { Request, Response } from "lambda-api";
import { User } from "../../common/admin-api/utils/auth";
import { AccessDenied, BadRequest } from "../../common/admin-api/utils/exceptions";

export function AuthorizerGuard(listRole: ERoleDamageReport[], messageError?: string) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originFn = descriptor.value as Function;
        const _messageErr = messageError ?? 'Access to this resource is restricted to those groups';
        descriptor.value = function (req: Request, res: Response) {
            const user = req.user as User;
            if (!user) throw new BadRequest({ msg: 'Request is not authenticated' });

            const canAccess = listRole.every(x => user.groups.includes(x));
            if (!canAccess) throw new AccessDenied(_messageErr);

            return originFn.apply(this, [req, res])
        }
        return descriptor
    }
}