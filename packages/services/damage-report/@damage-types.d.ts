
declare type TObject = Record<string, any>;
declare interface IQueryDR {
    queryAll?: boolean
}
declare interface IResponseFilterDR {
    data: IDamageReportItem[],
    total: number,
    lastEvaluatedKey: any
}
declare interface ILocation {
    address?: string;
    lat?: number;
    long?: number;
}

interface IDamageReportFlow {
    scenarioId: string,
    message_chatbot_id: string,
    event_source: any
}
interface IUserSessionDamageReport {
    category: {
        general: string,
        detailed: string,
    },
    images: {
        detailed: string,
        general: string
    }
    location: {
        address: string,
        latitude: number,
        longitude: number
    },
    userFeedback: string,
    workflows: IDamageReportFlow[]
}
interface IUserSessionDamageReportVersion {
    category: string,
    subject: string,
    images: string[],
    location: {
        address: string,
        latitude: number,
        longitude: number
    },
    feedback: string
}
interface ICreateDRMessage {
    uid: string,
    from: 'chatbot' | 'manage' | 'liff',
    scenarioId: string,
    talkId: string,
    payload: IUserSessionDamageReport | IUserSessionDamageReportVersion
}

interface ILineUserProfile {
    userId: string,
    displayName: string,
    language: string,
}

declare interface IDamageReportItem {
    id?: string;
    uid?: string;
    groupId?: string;
    userName?: string;
    createdAt?: number;
    updatedAt?: number;
    updatedByUid?: string;
    images?: DRImage[];
    category?: string;
    subject?: string;
    location?: ILocation;
    feedback?: string;
    scenarioId?: string;
    flows?: IDamageReportFlow[]; // orders every flow
    remarks?: string;
    remarksImages?: DRImage[];
    status?: EDRStatus;

    from?: 'chatbot' | 'manager' | 'liff';
    published?: boolean,
    markDelete?: boolean,
    talkDRId?: string
}
declare type TFieldsDR = keyof IDamageReportItem;
declare interface IDRImage {
    key: string;
    keyThumbnail?: string;
    fromChatBot?: boolean;

    url?: string;
    urlThumbnail?: string;
}
declare interface ICacheDRItem {
    /**
     * rule 
     * sample: status=aaa
     */
    key: string,
    /**
     * filter: category=sdadsa|subject=sds...
     */
    filter: string,
    total: number,
    updatedAt: number
}
declare type TFieldDR = keyof IDamageReportItem;