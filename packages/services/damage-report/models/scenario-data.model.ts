export const DAMAGE_REPORT_TALK = "DAMAGE_REPORT_TALK"
export enum EScenarioDataType {
    INIT = '__INITIAL__',
    TALK = 'talk',
    TEXT_MAPPING = 'textMapping',
    USER_MESSAGE = 'userMessage',
}
export enum ETalkVersionOf {
    DAMAGE = 'damage-report',
    OTHER = 'other'
}
export enum EDRFieldRequired {
    CATEGORY = 'category', // button-template, carousel, flex
    SUBJECT = 'subject', // button-template, carousel, flex, text
    FEEDBACK = 'feedback', // button-template, carousel, flex, text 
    IMAGES = 'images', // button-template, carousel, flex, text 
    LOCATION = 'location', // button-template, carousel, flex, text 
    CONFIRM_SUMMARY_REPORT = 'confirm_summary_report', // confirm
    END_TALK = "end_talk" // text, complex
}
export interface IScenarioVersionInfo {
    displayVersionName: string,
    languages: any[],
    specialTalks: Record<string, boolean>
}

export interface ITalkDataVersionItem {
    id: string;
    name: string;
    startMessageId: string;
}
export interface ITalkDataItemParams {
    name: string,
    messages: Array<{
        messageId: string,
        sender: 'BOT' | 'USER',
    }>,
    linkToDataId?: string,
    text?: string,
    title?: string,
    actionCount?: number,
    columnCount?: number
}

export class TalkDataItem {
    /**
     * ex: test#uuid
     */
    scenario: string;
    dataId: string;
    dataType: EScenarioDataType;
    talk: string;
    textMapping?: Record<string, string>;

    nameLBD: string;
    originalLBD: string;
    params: ITalkDataItemParams;

    // For specify damage report use case
    versionOf: ETalkVersionOf;
    versions: ITalkDataVersionItem[];
    /**
     * uuid of dataId talk dr version
     * or falsy will use default template
     */
    versionActive: string;
    // damageReportFieldRequired: EDRFieldRequired;
    targetDataField?: string;
    targetDataOrder?: number
    parentDataField?: string;

    generation?: number
}