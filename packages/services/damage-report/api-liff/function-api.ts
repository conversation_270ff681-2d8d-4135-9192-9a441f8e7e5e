import 'reflect-metadata';

import { chatRoutersFactory } from 'damage-report/controllers/chat.controller';
import { damageReportLiffRoutersFactory } from 'damage-report/controllers/liff/dr.controller';
import { mapRoutersFactory } from 'damage-report/controllers/map.controller';
import { utilsRoutersFactory } from "damage-report/controllers/utils.controller";
import { DynamoHelper } from "damage-report/helpers/dynamo.helper";
import { apiHandlerFactory } from "../../common/liff-api/api-lambda";

export const apiHandler = apiHandlerFactory('/liff', api => {
    const dynamoHelper = new DynamoHelper();

    api.register(damageReportLiffRoutersFactory({ dynamo: dynamoHelper }), { prefix: '/damage-report' });
    api.register(mapRoutersFactory({ dynamo: dynamoHelper }, true), { prefix: '/map' });
    api.register(chatRoutersFactory({ dynamo: dynamoHelper }, true), { prefix: '/chat' });

    api.register(utilsRoutersFactory({ dynamo: dynamoHelper }), { prefix: '/utils' });
})