import 'reflect-metadata';

import { chatRoutersFactory } from 'damage-report/controllers/chat.controller';
import { damageReportRoutersFactory } from "damage-report/controllers/damage-report.controller";
import { mapRoutersFactory } from 'damage-report/controllers/map.controller';
import { utilsRoutersFactory } from "damage-report/controllers/utils.controller";
import { DynamoHelper } from "damage-report/helpers/dynamo.helper";
import { apiHandlerFactory } from "../../common/admin-api/api-lambda";

export const apiHandler = apiHandlerFactory('/admin', api => {
    const dynamoHelper = new DynamoHelper();

    api.register(damageReportRoutersFactory({ dynamo: dynamoHelper }), { prefix: '/damage-report' });
    api.register(mapRoutersFactory({ dynamo: dynamoHelper }), { prefix: '/map' });
    api.register(chatRoutersFactory({ dynamo: dynamoHelper }), { prefix: '/chat' });
    
    api.register(utilsRoutersFactory({ dynamo: dynamoHelper }), { prefix: '/utils' });
})