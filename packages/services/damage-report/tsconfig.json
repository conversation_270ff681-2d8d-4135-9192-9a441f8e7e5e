{"extends": "@tsconfig/node18/tsconfig.json", "compilerOptions": {"module": "ESNext", "baseUrl": ".", "noImplicitAny": false, "allowJs": true, "strictNullChecks": false, "strictPropertyInitialization": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "esModuleInterop": true, "moduleResolution": "bundler", "paths": {"@oss/shared/*": ["../../shared/lib/*"], "common": ["../common/*"], "damage-report/*": ["./*"]}}}