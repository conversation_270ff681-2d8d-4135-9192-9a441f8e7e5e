
// cache
export const __KEY_CACHE_ALL = "All_Damage_Report";
export const __FILTER_CACHE_ALL = "Filter_Damage_Report";
export const __EMPTY_VALUE = "_";

export const __DR_DEFAULT_YEAR_QUERY = 2024;
export const __DR_DEFAULT_PAGE_SIZE_QUERY = 200;
export enum EDRStatus {
    NotProcessed = "not-processed",
    Cancel = 'cancel',
    Processing = 'processing',
    Finished = 'finished',
}

export const __DR_GROUPID_MANAGER = "DR_GROUPID_MANAGER";
export const __DR_GROUPID_KEY = "DR_GROUPID_KEY";
export  const __DR_ARR_STATUS = [
    EDRStatus.NotProcessed,
    EDRStatus.Cancel,
    EDRStatus.Processing,
    EDRStatus.Finished,
];

// Chat
export const __CHAT_UID_MANAGER = 'uid-manager-common';
export const __CHAT_UID_TEMP = 'uid-temp';
export const __AUTH_WS_PRINCIPAL_ID = "damage_report_user_connection"

// cache total
export const __ORDER_FIELD_BASED_ON_STATUS: TFieldDR[] = [
    'category',
    'scenarioId',
    'subject',
    'userName'
];
export const __ORDER_FIELD_BASED_ON_CATEGORY: TFieldDR[] = [
    'scenarioId',
    'subject',
    'userName'
];
export const __ORDER_FIELD_BASED_ON_SCENARIO: TFieldDR[] = [
    'subject',
    'userName'
];
export const __ORDER_FIELD_BASED_ON_SUBJECT: TFieldDR[] = [
    'userName'
];

// Scenario
export const __DR_SCENARIO_DATA_DATAID = 'DAMAGE_REPORT_TALK'