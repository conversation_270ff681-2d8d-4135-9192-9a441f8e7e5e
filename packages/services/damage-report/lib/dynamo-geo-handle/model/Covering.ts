import { S2CellId } from "nodes2ts";
import { GeohashRange } from "./GeohashRange";
import { MaximumRangeException } from "../ExceptionGeo";

export class Covering {
    private readonly MAX_LENGTH_HASH_RANGE = 100;
    private cellIds: S2CellId[];

    constructor(cellIds: S2CellId[]) {
        this.cellIds = cellIds;
    }

    public getGeoHashRanges(hashKeyLength: number) {
        const ranges: GeohashRange[] = [];
        this.cellIds.forEach(outerRange => {
            const hashRange = new GeohashRange(outerRange.rangeMin().id, outerRange.rangeMax().id);
            const split = hashRange.trySplit(hashKeyLength);
            if ((split.length + ranges.length) >= this.MAX_LENGTH_HASH_RANGE) throw new MaximumRangeException();

            ranges.push(...split);
        });
        return ranges;
    }

    public getNumberOfCells() {
        return this.cellIds.length;
    }
}
