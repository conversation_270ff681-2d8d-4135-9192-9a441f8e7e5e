/*
 * Copyright 2010-2013 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 * 
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 * 
 *  http://aws.amazon.com/apache2.0
 * 
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */

import { AttributeValue, DeleteItemCommand, GetItemCommand, PutItemCommand, PutItemCommandInput, QueryCommand, QueryCommandInput, QueryCommandOutput, UpdateItemCommand } from "@aws-sdk/client-dynamodb";
import * as Long from "long";
import { GeoDataManagerConfiguration } from "../GeoDataManagerConfiguration";
import { GeohashRange } from "../model/GeohashRange";
import { S2Manager } from "../s2/S2Manager";
import {
    DeletePointInput,
    DeletePointOutput,
    GeoPoint,
    GetPointInput,
    GetPointOutput,
    PutPointInput,
    PutPointOutput,
    TDynamoGeoItemList,
    UpdatePointInput,
    UpdatePointOutput
} from "../types";
import { ScanCommand, ScanCommandInput } from "@aws-sdk/lib-dynamodb";

export class DynamoDBManager {
    private config: GeoDataManagerConfiguration;

    public constructor(config: GeoDataManagerConfiguration) {
        this.config = config;
    }

    /**
     * Query Amazon DynamoDB
     *
     * @param queryInput
     * @param hashKey
     *            Hash key for the query request.
     *
     * @param range
     *            The range of geohashs to query.
     *
     * @return The query result.
     */
    public async queryGeohash(queryInput: QueryCommandInput | undefined, hashKey: Long, range: GeohashRange): Promise<QueryCommandOutput[]> {
        const queryOutputs: QueryCommandOutput[] = [];

        const nextQuery = async (lastEvaluatedKey: any = null) => {
            const keyConditions: QueryCommandInput['KeyConditions'] = {};

            keyConditions[this.config.hashKeyAttributeName] = {
                ComparisonOperator: "EQ",
                AttributeValueList: [{ N: hashKey.toString(10) }]
            };

            const minRange: AttributeValue = { N: range.rangeMin.toString(10) };
            const maxRange: AttributeValue = { N: range.rangeMax.toString(10) };

            keyConditions[this.config.geohashAttributeName] = {
                ComparisonOperator: "BETWEEN",
                AttributeValueList: [minRange, maxRange]
            };

            const defaults: QueryCommandInput = {
                TableName: this.config.tableName,
                KeyConditions: keyConditions,
                IndexName: this.config.geohashIndexName,
                ConsistentRead: this.config.consistentRead,
                ReturnConsumedCapacity: "TOTAL",
                ExclusiveStartKey: lastEvaluatedKey
            };

            const queryOutput = await this.config.dynamoClient.send(new QueryCommand({ ...defaults, ...queryInput }));
            queryOutputs.push(queryOutput);
            if (queryOutput.LastEvaluatedKey) {
                return nextQuery(queryOutput.LastEvaluatedKey);
            }
        };

        await nextQuery();
        return queryOutputs;
    }

    public async scanWith2point(minPoint: GeoPoint, maxPoint: GeoPoint) {
        const checkMinMax = () => {
            if (!minPoint || !maxPoint) return false;
            return minPoint.longitude < maxPoint.longitude && minPoint.latitude < maxPoint.latitude
        }
        if (!checkMinMax()) throw new Error("無効な入力")

        const results = [];
        let lastEvaluatedKey: any;
        do {
            const scanInput: ScanCommandInput = {
                TableName: this.config.tableName,
                FilterExpression: '#lat BETWEEN :minLat AND :maxLat AND #long BETWEEN :minLong AND :maxLong',
                ExpressionAttributeNames: {
                    '#lat': 'lat',
                    '#long': 'long',
                },
                ExpressionAttributeValues: {
                    ':minLat': minPoint.latitude,
                    ':minLong': minPoint.longitude,
                    ':maxLat': maxPoint.latitude,
                    ':maxLong': maxPoint.longitude,
                }
            }
            if (lastEvaluatedKey) scanInput.ExclusiveStartKey = lastEvaluatedKey
            const res = await this.config.dynamoDBClient.send(new ScanCommand(scanInput));
            if (res.Items) results.push(...res.Items)
            lastEvaluatedKey = res.LastEvaluatedKey;

        } while (lastEvaluatedKey);

        return results as TDynamoGeoItemList
    }

    public getPoint(getPointInput: GetPointInput): Promise<GetPointOutput> {
        const geohash = S2Manager.generateGeohash(getPointInput.GeoPoint);
        const hashKey = S2Manager.generateHashKey(geohash, this.config.hashKeyLength);

        const getItemInput = getPointInput.GetItemInput;
        getItemInput.TableName = this.config.tableName;

        getItemInput.Key = {
            [this.config.hashKeyAttributeName]: { N: hashKey.toString(10) },
            [this.config.rangeKeyAttributeName]: getPointInput.RangeKeyValue
        };

        return this.config.dynamoClient.send(new GetItemCommand(getItemInput));
    }

    public putPoint(putPointInput: PutPointInput): Promise<PutPointOutput> {
        const geohash = S2Manager.generateGeohash(putPointInput.GeoPoint);
        const hashKey = S2Manager.generateHashKey(geohash, this.config.hashKeyLength);
        const putItemInput: PutItemCommandInput = {
            ...putPointInput.PutItemInput,
            TableName: this.config.tableName,
            Item: putPointInput.PutItemInput.Item || {}
        };

        putItemInput.Item[this.config.hashKeyAttributeName] = { N: hashKey.toString(10) };
        putItemInput.Item[this.config.rangeKeyAttributeName] = putPointInput.RangeKeyValue;
        putItemInput.Item[this.config.geohashAttributeName] = { N: geohash.toString(10) };
        putItemInput.Item['lat'] = { N: putPointInput.GeoPoint.latitude.toString() };
        putItemInput.Item['long'] = { N: putPointInput.GeoPoint.longitude.toString() };

        return this.config.dynamoClient.send(new PutItemCommand(putItemInput));
    }

    public updatePoint(updatePointInput: UpdatePointInput): Promise<UpdatePointOutput> {
        const geohash = S2Manager.generateGeohash(updatePointInput.GeoPoint);
        const hashKey = S2Manager.generateHashKey(geohash, this.config.hashKeyLength);

        updatePointInput.UpdateItemInput.TableName = this.config.tableName;

        if (!updatePointInput.UpdateItemInput.Key) {
            updatePointInput.UpdateItemInput.Key = {};
        }

        updatePointInput.UpdateItemInput.Key[this.config.hashKeyAttributeName] = { N: hashKey.toString(10) };
        updatePointInput.UpdateItemInput.Key[this.config.rangeKeyAttributeName] = updatePointInput.RangeKeyValue;

        // Geohash and geoJson cannot be updated.
        if (updatePointInput.UpdateItemInput.AttributeUpdates) {
            delete updatePointInput.UpdateItemInput.AttributeUpdates[this.config.geohashAttributeName];
        }

        return this.config.dynamoClient.send(new UpdateItemCommand(updatePointInput.UpdateItemInput));
    }

    public deletePoint(deletePointInput: DeletePointInput): Promise<DeletePointOutput> {
        const geohash = S2Manager.generateGeohash(deletePointInput.GeoPoint);
        const hashKey = S2Manager.generateHashKey(geohash, this.config.hashKeyLength);

        return this.config.dynamoClient.send(new DeleteItemCommand({
            TableName: this.config.tableName,
            Key: {
                [this.config.hashKeyAttributeName]: { N: hashKey.toString(10) },
                [this.config.rangeKeyAttributeName]: deletePointInput.RangeKeyValue
            }
        }));
    }
}
