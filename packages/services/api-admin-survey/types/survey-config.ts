/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
type BooleanValue = {
  value: boolean
}
export type FieldType = {
  itemKey: string,
  type: string,
  title: string,
  isAdminItem: boolean,
  isRequired: BooleanValue,
  description?: string,
  isSearchable: BooleanValue,
  isIndexable: BooleanValue,
  attributes?: {
    isSesshuKaisuu?: boolean
  },
  input?: string | number;
  default?: string | number;
  options?: string[],
};
type ReservationCategory = {
  name: string,
  children: {
    name: string, id: string, calendarId?: string
  }[]
}

export type ReservationFieldType = {
  isFixedLargeCategory: boolean,
  isFixedMediumCategory: boolean,
  isFixedSmallCategory: boolean,
  reservationCheckBox: boolean,
  reservationSupCheckBox: boolean,
  selectedLargeCategory?: ReservationCategory,
  selectedMediumCategory?: ReservationCategory,
  setLargeCategoryTitle?: string,
  setMediumCategoryTitle?: string,
  setSmallCategoryTitle?: string,
  type: 'reservation',
} & FieldType

export type RadioGroupHeaderFieldType = {
  sectionOptions: {
    groupheader: {
      value: string,
    },
    option: {
      value: string,
    }
  }[],
} & FieldType

export const PAY_TYPES = {
  CASHLESS: 0,
  CASH: 1
} as const;

export type SelectPayTypeFieldType = {
  payTypeOptions: {
    payType: typeof PAY_TYPES[keyof typeof PAY_TYPES],
    input: string
  }[]
} & FieldType

export type SurveyConfig = {
  surveyId: string,
  surveySchema: FieldType[],
  surveyStatus: 'disable' | 'enable',
  surveyTitle: string,
  surveyType: string,
  description: string,
  isAppending: BooleanValue,
  isDisplay?: boolean,
  vaccinationIntervalType?: {
    input: '日数' | '月数',
  },
  memberFormId?: string,
  usePayment: 0 | 1,  
}

export type CategoryPermissionsRecord = {
  partitionKey: string,
  permissions: LargeCategoryPermission[],
}

export type LargeCategoryPermission = {
  list: CategoryPermission[],
  categoryLarge: string
}

export type CategoryPermission =   { categoryId: string, teamIds: string[] }
