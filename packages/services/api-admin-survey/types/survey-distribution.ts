/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {SurveyConfig} from './survey-config.js';

export type ImageMapMessageActionPayload = {
  "type": "uri" | "message",
  "linkUri"? : string,
  "text"? : string,
  "area": {
    "width": number,
    "height": number,
    "x": number,
    "y": number,
  }
}

export type FlexMessageType = {
  type: "flex"
  altText: string,
  contents: any;
}

export type MessageBaseRecord = {
  primaryKey: string,
  type: string,
}
export type ImageMessageType = {
  type: "image",
  originalContentUrl: string,
  previewImageUrl: string,
}
export type TextMessageType = {
  type: "text",
  text: string,
  emojis: Array<any>,
}
export type VideoMessageType = {
  type: "video",
  originalContentUrl: string,
  previewImageUrl: string,
  trackingId?: string,
}
export type MessageType = ImageMessageType | TextMessageType | ImageMapMessageType | FlexMessageType | VideoMessageType | { type: string } & { [key: string]: any }

export type ImageMapMessageType = {
  type: "imagemap"
  baseUrl: string,
  altText: string,
  baseSize: {
    width: number,
    height: number
  },
  "actions": ImageMapMessageActionPayload[];
}

export type MessageRecord = {
  primaryKey?: string,
  contents: MessageType,
  type?: string,
  expireAt?: number,
}

export type DistributionDeliveryTarget ={
  userId: string,
  surveyResultsIds?: string[]
}

export type DeliveryTargetRequest = {
  id: string,
  partitionKey: string,
}

export type SearchCondition = {
  surveyId: string,
  filterCommon: {[key: string]: any[]},
  filterDate: {[key: string]: any},
  filterCategories?: string[],
  filterTeams?: { [key: string]: boolean }
}

export type RunDeliveryTask = {
  title: string,
  type: 'searchCondition' | 'target',
  searchCondition?: SearchCondition,
  users?: DeliveryTargetRequest[],
  messages: TextMessageType[],
  trackStatus: boolean,
  operationType: DistributionOperationTypes
}

export type RunDeliveryRequest = {
  isAppending: { value: boolean },
  surveyConfig: SurveyConfig,
} & RunDeliveryTask

export type DistributionManagerLambdaResponse = {
  result: 'ok' | 'error',
  distributionId: string,
}

export type DistributionOperationTypes = 'home-condition' | 'home-selected' | 'home-individual' | 'trigger-mail' |
  'manual-immediate' | 'manual-scheduled' | 'manual-recurring' | 'trigger-reminder'

export type HomeSearchLambdaRequest = {
  distributionId: string,
  title: string,
  userId: string,
  messageIds: string[],
  messages: TextMessageType[],
  search: SearchCondition,
  trackStatus: boolean,
  operationType: DistributionOperationTypes
}

export type HomeSelectedDeliveryRequest = {
  startedBy: string,
  title: string,
  trackStatus?: boolean,
  operationType: DistributionOperationTypes
  targets: DistributionDeliveryTarget[],
  messageIds?: string[],
  messages?: TextMessageType[],
}
