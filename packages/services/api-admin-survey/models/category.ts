/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import {
  Category,
  CategoryOrder,
  CategoryOrders,
} from '../types/categories.js';

export class CategoryModel {
  private category: Category;

  constructor(category: Category) {
    this.validateInitParams(category);
    this.category = category;
  }

  get id(): string {
    return this.category.id;
  }

  get largeCategoryOrder(): CategoryOrder {
    return this.category.largeCategoryOrder;
  }

  get mediumCategoryOrder(): CategoryOrder {
    return this.category.mediumCategoryOrder;
  }

  get smallCategoryOrder(): CategoryOrder {
    return this.category.smallCategoryOrder;
  }

  get updatedAt(): number {
    return this.category.updatedAt;
  }

  private validateInitParams(params: any) {
    const paramsKeys = Object.keys(params);
    const requiredParams = ['id', 'tag1', 'tag2', 'tag3'];
    requiredParams.forEach(param => {
      if (!paramsKeys.includes(param)) {
        throw new Error(`${params} is required paramter.`);
      }
    });
    const validParams = [...requiredParams, 'calendarId', 'largeCategoryOrder', 'mediumCategoryOrder', 'smallCategoryOrder', 'updatedAt'];
    paramsKeys.forEach(key => {
      if (!validParams.includes(key)) {
        throw new Error(`${key} is invalid parameter.`);
      }
    });
  }

  private getOrders(): CategoryOrders {
    return {
      largeCategoryOrder: this.category.largeCategoryOrder,
      mediumCategoryOrder: this.category.mediumCategoryOrder,
      smallCategoryOrder: this.category.smallCategoryOrder,
    }
  }

  private orderToNumberOrNull(order: CategoryOrder): number | null {
    return [null, undefined].includes(order) ? null : order;
  }

  private ordersToConnectedString(orders: CategoryOrders): string {
    return `${this.orderToNumberOrNull(orders.largeCategoryOrder)}`
      + `${this.orderToNumberOrNull(orders.mediumCategoryOrder)}`
      + `${this.orderToNumberOrNull(orders.smallCategoryOrder)}`;
  }

  isSameCategoryOrders(comparisonCategory: CategoryModel): boolean {
    const categoryOrders = this.ordersToConnectedString(this.getOrders());
    const comparisonOrders = this.ordersToConnectedString(comparisonCategory.getOrders());
    return categoryOrders === comparisonOrders;
  }
}