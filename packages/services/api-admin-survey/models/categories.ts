/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import {
  Category,
  CategoryRecord,
  LargeCategoryTree,
} from '../types/categories.js';
import { CategoryModel } from './category.js';
import { CategoriesOrder } from './categories-order.js';
import { CategoriesTreeModel } from './categories-tree.js';
import { uniqBy, cloneDeep } from 'lodash';

export class CategoriesModel {
  private categories: Category[];

  constructor(categories: Category[]) {
    this.validateInitParams(categories);
    this.categories = categories;
  }

  get length(): number {
    return this.categories.length;
  }

  get cache(): Category[] {
    return cloneDeep(this.categories);
  }

  private validateInitParams(params: any) {
    if (!Array.isArray(params)) {
      throw new Error(`Categories is invalid. The specified parameter is ${params}`)
    }
  }

  findById(categoryId: string): CategoryModel | null {
    const category = this.categories.find(category => category.id === categoryId);
    return category ? new CategoryModel(category) : null;
  }

  static toTree(categoryRecords: CategoryRecord[]): LargeCategoryTree[] {
    const categoriesTree:any[] = [];

    const tags1 =
      uniqBy(
        categoryRecords.map((c) => ({ name: c.tag1, order: c.largeCategoryOrder })),
        (c) => c.name
      )
      .sort(CategoriesOrder.compareCategoriesTreeForSort);
    tags1.forEach(({ name: tag1Name, order: tag1Order }) => {
      const node1:any = {
        name: tag1Name,
        order: tag1Order,
        children: [],
      };
      const tags2 = uniqBy(
        categoryRecords
          .filter((c) => c.tag1 === tag1Name)
          .map((c) => ({ name: c.tag2, order: c.mediumCategoryOrder }))
          .sort(CategoriesOrder.compareCategoriesTreeForSort),
        (c) => c.name
      );
      tags2.forEach(({ name: tag2Name, order: tag2Order }) => {
        const node2:any = {
          name: tag2Name,
          order: tag2Order,
          children: [],
        };
        const node2childCats = categoryRecords.filter((c) => c.tag1 === tag1Name && c.tag2 === tag2Name);
        node2childCats.forEach((c) => {
          if (!c.tag2) {
            node1.id = c.sortKey;
            node1.calendarId = c.calendarId;
            node1.updatedAt = c.updatedAt;
          } else if (!c.tag3) {
            node2.id = c.sortKey;
            node2.calendarId = c.calendarId;
            node2.updatedAt = c.updatedAt;
          } else {
            node2.children.push({
              name: c.tag3,
              id: c.sortKey,
              calendarId: c.calendarId,
              order: c.smallCategoryOrder,
              updatedAt: c.updatedAt,
            });
          }
        });
        node2.children.sort(CategoriesOrder.compareCategoriesTreeForSort);
        if (node2.name) {
          node1.children.push(node2);
        };
      });

      categoriesTree.push(node1);
    });

    return categoriesTree;
  }

  static sortByOrderOrName(categoryRecords: CategoryRecord[]) {
    const tree = CategoriesModel.toTree(categoryRecords);
    return new CategoriesTreeModel(tree).toCategories();
  }
}
