/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { DateModel, BirthdayModel }  from './date.js';

describe('BirthdayModel', () => {
  describe('calculateAge', () => {
    const mockDateFormat = 'yyyy-MM-dd';
    const mock = [
      {
        birthday: '2020-02-29',
        baseDate: '2024-02-29',
        expectedAge: 4,
        memo: '生年月日と基準日がうるう年の2月末',
      },
      {
        birthday: '2020-02-29',
        baseDate: '2023-02-28',
        expectedAge: 2,
        memo: '生年月日がうるう年の2月末で基準日が非うるう年の2月末',
      },
      {
        birthday: '2020-02-29',
        baseDate: '2024-03-01',
        expectedAge: 4,
        memo: '生年月日がうるう年の2月末で基準日がうるう年の3月頭',
      },
      {
        birthday: '2020-02-28',
        baseDate: '2024-02-29',
        expectedAge: 4,
        memo: '生年月日がうるう年の2月末-1日で基準日がうるう年の2月末',
      },
      {
        birthday: '2020-02-28',
        baseDate: '2023-02-28',
        expectedAge: 3,
        memo: '生年月日がうるう年の2月末-1日で基準日が非うるう年の2月末',
      },
      {
        birthday: '2020-02-28',
        baseDate: '2024-03-01',
        expectedAge: 4,
        memo: '生年月日がうるう年の2月末-1日で基準日がうるう年の3月頭',
      },
      {
        birthday: '2021-02-28',
        baseDate: '2024-02-29',
        expectedAge: 3,
        memo: '生年月日が非うるう年の2月末で基準日がうるう年の2月末',
      },
      {
        birthday: '2021-02-28',
        baseDate: '2023-02-28',
        expectedAge: 2,
        memo: '生年月日と基準日が非うるう年の2月末',
      },
      {
        birthday: '2021-02-28',
        baseDate: '2024-03-01',
        expectedAge: 3,
        memo: '生年月日が非うるう年で基準日がうるう年の3月頭',
      },
      {
        birthday: '2020-03-01',
        baseDate: '2024-02-29',
        expectedAge: 3,
        memo: '生年月日がうるう年の3月頭で基準日がうるう年の2月末',
      },
      {
        birthday: '2020-03-01',
        baseDate: '2023-02-28',
        expectedAge: 2,
        memo: '生年月日がうるう年の3月頭で基準日が非うるう年の2月末',
      },
      {
        birthday: '2020-03-01',
        baseDate: '2024-03-01',
        expectedAge: 4,
        memo: '生年月日と基準日がうるう年の3月頭',
      },
    ];

    mock.forEach(({ birthday, baseDate, expectedAge }) => {
      it(`誕生日が${birthday}で基準日が${baseDate}なので${expectedAge}歳になる`, () => {
        const _birthday = new BirthdayModel(birthday, mockDateFormat);
        const _baseDate = new DateModel(baseDate, mockDateFormat);
        const age = _birthday.calculateAge(_baseDate.getDate());
        expect(age).toBe(expectedAge);
      });
    });
  });
});