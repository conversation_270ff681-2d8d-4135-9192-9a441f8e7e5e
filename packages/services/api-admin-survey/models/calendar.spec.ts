/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { ReservableAgeModel } from './calendar.js';
import { DateModel, BirthdayModel }  from './date.js';
import { ControlTypeOfReservableAge, KeyOfControlType } from '../types/calendars.js';

const mockDateFormat = 'yyyy-MM-dd';
const mockForFullAge = [
  {
    birthday: '2020-02-29',
    baseDate: '2024-02-29',
    expectedAge: 4,
    memo: '生年月日と基準日がうるう年の2月末',
  },
  {
    birthday: '2020-02-29',
    baseDate: '2023-02-28',
    expectedAge: 2,
    memo: '生年月日がうるう年の2月末で基準日が非うるう年の2月末',
  },
  {
    birthday: '2020-02-29',
    baseDate: '2024-03-01',
    expectedAge: 4,
    memo: '生年月日がうるう年の2月末で基準日がうるう年の3月頭',
  },
  {
    birthday: '2020-02-28',
    baseDate: '2024-02-29',
    expectedAge: 4,
    memo: '生年月日がうるう年の2月末-1日で基準日がうるう年の2月末',
  },
  {
    birthday: '2020-02-28',
    baseDate: '2023-02-28',
    expectedAge: 3,
    memo: '生年月日がうるう年の2月末-1日で基準日が非うるう年の2月末',
  },
  {
    birthday: '2020-02-28',
    baseDate: '2024-03-01',
    expectedAge: 4,
    memo: '生年月日がうるう年の2月末-1日で基準日がうるう年の3月頭',
  },
  {
    birthday: '2021-02-28',
    baseDate: '2024-02-29',
    expectedAge: 3,
    memo: '生年月日が非うるう年の2月末で基準日がうるう年の2月末',
  },
  {
    birthday: '2021-02-28',
    baseDate: '2023-02-28',
    expectedAge: 2,
    memo: '生年月日と基準日が非うるう年の2月末',
  },
  {
    birthday: '2021-02-28',
    baseDate: '2024-03-01',
    expectedAge: 3,
    memo: '生年月日が非うるう年で基準日がうるう年の3月頭',
  },
  {
    birthday: '2020-03-01',
    baseDate: '2024-02-29',
    expectedAge: 3,
    memo: '生年月日がうるう年の3月頭で基準日がうるう年の2月末',
  },
  {
    birthday: '2020-03-01',
    baseDate: '2023-02-28',
    expectedAge: 2,
    memo: '生年月日がうるう年の3月頭で基準日が非うるう年の2月末',
  },
  {
    birthday: '2020-03-01',
    baseDate: '2024-03-01',
    expectedAge: 4,
    memo: '生年月日と基準日がうるう年の3月頭',
  },
];
const mockForSpecialSetting = [
  {
    birthday: '2020-02-28',
    baseDate: '2025-02-28',
    expectedAge: 5,
    memo: '基準日と誕生日と同じ',
  },
  {
    birthday: '2020-02-29',
    baseDate: '2025-03-01',
    expectedAge: 5,
    memo: '誕生日がうるう年で基準日が非うるう年',
  },
  {
    birthday: '2020-02-29',
    baseDate: '2025-02-28',
    expectedAge: 5,
    memo: '基準日が5歳になる誕生日の1日前',
  },
  {
    birthday: '2020-02-29',
    baseDate: '2025-02-27',
    expectedAge: 4,
    memo: '基準日が5歳になる誕生日の2日前',
  },
  {
    birthday: '2020-02-27',
    baseDate: '2025-02-28',
    expectedAge: 5,
    memo: '基準日が5歳になる誕生日の1日後',
  },
  {
    birthday: '2020-02-27',
    baseDate: '2032-02-24',
    expectedAge: 11,
    memo: '基準日が12歳になる誕生日の3日前',
  },
  {
    birthday: '2020-02-27',
    baseDate: '2032-02-25',
    expectedAge: 11,
    memo: '基準日が12歳になる誕生日の2日前',
  },
  {
    birthday: '2020-02-27',
    baseDate: '2032-02-26',
    expectedAge: 12,
    memo: '基準日が12歳になる誕生日の1日前',
  },
  {
    birthday: '2020-03-01',
    baseDate: '2025-02-28',
    expectedAge: 5,
    memo: '基準日(非うるう年の2月末)が5歳になる誕生日(うるう年3月頭)の1日前',
  },
  {
    birthday: '2020-03-02',
    baseDate: '2025-03-01',
    expectedAge: 5,
    memo: '基準日(非うるう年の3月頭)が5歳になる誕生日(うるう年)の1日前',
  },
  {
    birthday: '2020-03-01',
    baseDate: '2025-02-27',
    expectedAge: 4,
    memo: '基準日(非うるう年)が5歳になる誕生日(うるう年3月頭)の2日前',
  },
  {
    birthday: '2021-03-01',
    baseDate: '2026-02-28',
    expectedAge: 5,
    memo: '基準日(非うるう年)が5歳になる誕生日(非うるう年3月頭)の1日前',
  },
  {
    birthday: '2021-03-01',
    baseDate: '2026-02-27',
    expectedAge: 4,
    memo: '基準日(非うるう年)が5歳になる誕生日(非うるう年3月頭)の2日前',
  },
  {
    birthday: '2021-03-01',
    baseDate: '2026-02-26',
    expectedAge: 4,
    memo: '基準日(非うるう年)が5歳になる誕生日(非うるう年3月頭)の3日前',
  },
  {
    birthday: '2020-03-01',
    baseDate: '2032-02-29',
    expectedAge: 12,
    memo: '基準日(うるう年の2月末)が12歳になる誕生日(うるう年3月頭)の1日前',
  },
  {
    birthday: '2020-03-02',
    baseDate: '2032-03-01',
    expectedAge: 12,
    memo: '基準日(うるう年の3月頭)が12歳になる誕生日(うるう年)の1日前',
  },
  {
    birthday: '2020-03-01',
    baseDate: '2032-02-28',
    expectedAge: 11,
    memo: '基準日(うるう年)が12歳になる誕生日(うるう年3月頭)の2日前 '
  },
  {
    birthday: '2021-03-01',
    baseDate: '2033-02-28',
    expectedAge: 12,
    memo: '基準日(非うるう年)が12歳になる誕生日(非うるう年3月頭)の1日前',
  },
  {
    birthday: '2021-03-01',
    baseDate: '2033-02-27',
    expectedAge: 11,
    memo: '基準日(非うるう年)が12歳になる誕生日(非うるう年3月頭)の2日前',
  },
  {
    birthday: '2021-03-01',
    baseDate: '2033-02-26',
    expectedAge: 11,
    memo: '基準日(非うるう年)が12歳になる誕生日(非うるう年3月頭)の3日前',
  },
];

const mockReservableAgesByControlType = {
  fullAge: {
    start: 4,
    end: 4,
  },
  specialSetting: {
    start: 5,
    end: 11,
  }
}
const getMock = (type: KeyOfControlType) => {
  switch (type) {
    case 'allAge':
    case 'fullAge':
      return mockForFullAge;
    case 'specialSetting':
      return mockForSpecialSetting;
  }
}
const getResultString = (mockData: typeof mockForFullAge[0], controlType: KeyOfControlType, expected: string): string => {
  const { birthday, baseDate, expectedAge, memo } = mockData;
  switch (controlType) {
    case 'fullAge':
      return `誕生日が${birthday}, 基準日が${baseDate}なので${expectedAge}歳になる為${expected}`;
    case 'specialSetting':
      return `誕生日が${birthday}, 基準日が${baseDate}, ${memo}なので${expected}`;
  }
}
const comparison = (
  reservableAgeModel: ReservableAgeModel,
  controlType: KeyOfControlType,
  reservableAge: { start: number, end: number }
) => {
  const mock = getMock(controlType);
  mock.forEach((mock) => {
    const { birthday, baseDate, expectedAge } = mock;
    const _birthday = new BirthdayModel(birthday, mockDateFormat);
    const _baseDate = new DateModel(baseDate, mockDateFormat);
    const isReservable = expectedAge >= reservableAge.start && expectedAge <= reservableAge.end;
    const expected = isReservable ? '予約可能' : '予約不可';
    it(getResultString(mock, controlType, expected), () => {
      const isReservableAgeResult = reservableAgeModel.isReservableAge(_birthday, _baseDate);
      expect(isReservableAgeResult).toBe(isReservable);
    });
  });
}

describe('ReservableAgeModel', () => {
  describe('isReservableAge', () => {
    Object.keys(ControlTypeOfReservableAge).forEach((contorlType: KeyOfControlType) => {
      if (contorlType === 'allAge') {
        return;
      }
      describe(`[controlType] is "${contorlType}"`, () => {
        const reservableAgeModel = new ReservableAgeModel({
          controlType: ControlTypeOfReservableAge[contorlType],
          settings: [{
            start: mockReservableAgesByControlType[contorlType].start,
            end: mockReservableAgesByControlType[contorlType].end,
          }]
        });
        comparison(reservableAgeModel, contorlType, mockReservableAgesByControlType[contorlType]);
      });
    });
  });

  describe('getAgeByControlType', () => {
    const mock = [
      // 誕生日がうるう年の2/28
      {
        birthday: '2020-02-28',
        baseDate: '2022-02-26',
        expectedAge: 1,
        memo: '基準日が2歳になる誕生日の2日前',
      },
      {
        birthday: '2020-02-28',
        baseDate: '2022-02-27',
        expectedAge: 2,
        memo: '基準日が2歳になる誕生日の1日前',
      },
      {
        birthday: '2020-02-28',
        baseDate: '2022-02-28',
        expectedAge: 2,
        memo: '基準日が2歳になる誕生日の当日',
      },
      {
        birthday: '2020-02-28',
        baseDate: '2024-02-26',
        expectedAge: 3,
        memo: '基準日が4歳になる誕生日の2日前',
      },
      {
        birthday: '2020-02-28',
        baseDate: '2024-02-27',
        expectedAge: 4,
        memo: '基準日が4歳になる誕生日の1日前',
      },
      {
        birthday: '2020-02-28',
        baseDate: '2024-02-28',
        expectedAge: 4,
        memo: '基準日が4歳になる誕生日の当日',
      },
      // 誕生日がうるう年の2/29
      {
        birthday: '2020-02-29',
        baseDate: '2022-02-27',
        expectedAge: 1,
        memo: '基準日が2歳になる誕生日の2日前',
      },
      {
        birthday: '2020-02-29',
        baseDate: '2022-02-28',
        expectedAge: 2,
        memo: '基準日が2歳になる誕生日の1日前',
      },
      {
        birthday: '2020-02-29',
        baseDate: '2022-03-01',
        expectedAge: 2,
        memo: '基準日が2歳になる誕生日の当日',
      },
      {
        birthday: '2020-02-29',
        baseDate: '2024-02-27',
        expectedAge: 3,
        memo: '基準日が4歳になる誕生日の2日前',
      },
      {
        birthday: '2020-02-29',
        baseDate: '2024-02-28',
        expectedAge: 4,
        memo: '基準日が4歳になる誕生日の1日前',
      },
      {
        birthday: '2020-02-29',
        baseDate: '2024-02-29',
        expectedAge: 4,
        memo: '基準日が4歳になる誕生日の当日',
      },
      // 誕生日がうるう年の3/1
      {
        birthday: '2020-03-01',
        baseDate: '2022-02-27',
        expectedAge: 1,
        memo: '基準日が2歳になる誕生日の2日前',
      },
      {
        birthday: '2020-03-01',
        baseDate: '2022-02-28',
        expectedAge: 2,
        memo: '基準日が2歳になる誕生日の1日前',
      },
      {
        birthday: '2020-03-01',
        baseDate: '2022-03-01',
        expectedAge: 2,
        memo: '基準日が2歳になる誕生日の当日',
      },
      {
        birthday: '2020-03-01',
        baseDate: '2024-02-27',
        expectedAge: 3,
        memo: '基準日が4歳になる誕生日の3日前',
      },
      {
        birthday: '2020-03-01',
        baseDate: '2024-02-28',
        expectedAge: 3,
        memo: '基準日が4歳になる誕生日の2日前',
      },
      {
        birthday: '2020-03-01',
        baseDate: '2024-02-29',
        expectedAge: 4,
        memo: '基準日が4歳になる誕生日の1日前',
      },
      {
        birthday: '2020-03-01',
        baseDate: '2024-03-01',
        expectedAge: 4,
        memo: '基準日が4歳になる誕生日の当日',
      },
      // 誕生日が非うるう年の2/28
      {
        birthday: '2021-02-28',
        baseDate: '2022-02-26',
        expectedAge: 0,
        memo: '基準日が1歳になる誕生日の2日前',
      },
      {
        birthday: '2021-02-28',
        baseDate: '2022-02-27',
        expectedAge: 1,
        memo: '基準日が1歳になる誕生日の1日前',
      },
      {
        birthday: '2021-02-28',
        baseDate: '2022-02-28',
        expectedAge: 1,
        memo: '基準日が1歳になる誕生日の当日',
      },
      {
        birthday: '2021-02-28',
        baseDate: '2024-02-26',
        expectedAge: 2,
        memo: '基準日が3歳になる誕生日の2日前',
      },
      {
        birthday: '2021-02-28',
        baseDate: '2024-02-27',
        expectedAge: 3,
        memo: '基準日が3歳になる誕生日の1日前',
      },
      {
        birthday: '2021-02-28',
        baseDate: '2024-02-28',
        expectedAge: 3,
        memo: '基準日が3歳になる誕生日の当日',
      },
      // 誕生日が非うるう年の3/1
      {
        birthday: '2021-03-01',
        baseDate: '2022-02-27',
        expectedAge: 0,
        memo: '基準日が1歳になる誕生日の2日前',
      },
      {
        birthday: '2021-03-01',
        baseDate: '2022-02-28',
        expectedAge: 1,
        memo: '基準日が1歳になる誕生日の1日前',
      },
      {
        birthday: '2021-03-01',
        baseDate: '2022-03-01',
        expectedAge: 1,
        memo: '基準日が1歳になる誕生日の当日',
      },
      {
        birthday: '2021-03-01',
        baseDate: '2024-02-27',
        expectedAge: 2,
        memo: '基準日が3歳になる誕生日の3日前',
      },
      {
        birthday: '2021-03-01',
        baseDate: '2024-02-28',
        expectedAge: 2,
        memo: '基準日が3歳になる誕生日の2日前',
      },
      {
        birthday: '2021-03-01',
        baseDate: '2024-02-29',
        expectedAge: 3,
        memo: '基準日が3歳になる誕生日の1日前',
      },
      {
        birthday: '2021-03-01',
        baseDate: '2024-03-01',
        expectedAge: 3,
        memo: '基準日が3歳になる誕生日の当日',
      },
    ];
    describe('[controlType] is "specialSetting"', () => {
      const reservableAgeModel = new ReservableAgeModel({
        controlType: 2,
        settings: [{
          start: 1,
          end: 1,
        }]
      })
      mock.forEach(({ birthday, baseDate, expectedAge, memo }) => {
        it(`誕生日が${birthday}, 基準日が${baseDate}, ${memo}なので${expectedAge}歳になる`, () => {
          const birthdayModel = new BirthdayModel(birthday, mockDateFormat);
          const baseDateModel = new DateModel(baseDate, mockDateFormat);
          const age = reservableAgeModel.getAgeByControlType(birthdayModel, baseDateModel);
          expect(age).toBe(expectedAge);
        });
      });
    });
  })
});