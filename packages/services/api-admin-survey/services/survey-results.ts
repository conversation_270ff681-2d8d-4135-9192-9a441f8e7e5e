/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import AWS from 'aws-sdk';
import * as db from '../../common/admin-api/utils/db.js';
import _ from 'lodash';
import { parse } from 'json2csv';
import moment from 'moment';
moment.locale('ja');
import * as sConfigs from './survey.js';
import c from '../../common/admin-api/config/constants.js';
import { DDClient } from 'common/utils/index.js';
import { getEnvironmentCredentials } from '../../common/admin-api/utils/aws-helper.js';
import config from '../../common/admin-api/config/static.js';
import { getFormatedAnswerCode, prepareExportHeaders, sleep, randomString, generateUuid } from '../../common/admin-api/utils/util.js';
import { CsvToJson, SurveyResultsImportHelper } from '../utils/survey-results-import-helper.js';
import { SurveyResultsHelper } from './survey-results/search/sr_index/sr_helper.js';
import { ItemsMergeHelper } from './survey-results/search/all.js';
import { SurveyResult } from './survey-results/search/survey/results_model.js';
import SurveyResultsRollbackHelper from './survey-results/survey-results-rollback.js';
import platformAsyncService from '../../platform/services/platform-async-function.js';
import * as homeNotificationService from './home-notification.js';
import { loadScheduleOfCategory } from './calendars-schedules.js';
import {
  scanList,
  queryList,
  UpdateExpressionBuilder,
  updateOne,
  deleteOne,
  batchWriteItem,
  createOne,
  getOne,
} from '../../common/admin-api/utils/db.js';
import { tableSurveyResults, createLambdaClient } from '../../common/utils/aws-clients.js';
import * as memberConfig from '../../members/services/member.js';

import * as calendarExts from './calendars-ext.js';
import * as calendarUtils from './calendars-util.js';
import { SurveyResultsValidator } from './survey-results-validator.js';
import { attachCalendarInfoToGroupedItems } from './survey-results/search.js';

import { SurveyConfigModel } from '../models/survey-config.js';
import { CalendarModel } from '../models/calendar.js';
import { BirthdayModel, DateModel } from '../models/date.js';
import { ExportCsvColumnConfig, ExportCsvRowModel } from '../types/survey-results.js';
import * as process from "process";

const dynamoDbTable = () => {
  return config.get(c.TABLE_SURVEY_RESULTS);
};

const dynamoDbMembersTable = () => {
  return config.get(c.TABLE_MEMBER_RESULTS);
}

const surveyCalendarsTable = () => {
  return config.get(c.TABLE_SURVEY_CALENDARS);
};

const dynamoDbSurveyConfigTable = () => {
  return config.get(c.TABLE_SURVEY_CONFIGS);
};

const dynamoDbMemberConfigTable = () => {
  return config.get(c.TABLE_MEMBER_CONFIGS);
};

const displaySettingTable = () => {
  return config.get(c.DATABASE_TABLE);
};

const surveyResultsClient = () => {
  return tableSurveyResults(dynamoDbTable());
};

const formatUnixToYYYYMMDHHmmss = (value) => {
  return moment.unix(value).format('YYYY-MM-DD HH:mm:ss');
};

const createSurveyResultsHelper = async (surveyId) => {
  const surveyConfig = (await getSurveyConfig(surveyId))[0];
  return new SurveyResultsHelper(surveyConfig, {
    awsConfig: getEnvironmentCredentials(),
    resultsTable: config.get(c.TABLE_SURVEY_RESULTS),
  });
};

const convertToEachUser = (results) => {
  return results.reduce(
    (h, obj) =>
      Object.assign(h, {
        [obj.userId]: (h[obj.userId] || []).concat(obj),
      }),
    {}
  );
};

const groupByPartitionKey = (results) => {
  return results.reduce(
    (h, obj) =>
      Object.assign(h, {
        [obj.partitionKey]: (h[obj.partitionKey] || []).concat(obj),
      }),
    {}
  );
};

const create = async (items) => {
  for (let i = 0; i < items.length; ++i) {
    const item = items[i];
    const marshalled = AWS.DynamoDB.Converter.marshall(item, {
      convertEmptyValues: true,
    });
    await createOne({
      TableName: dynamoDbTable(),
      Item: marshalled,
    });
  }
};

const getAll = async () => {
  const tableName = dynamoDbTable();
  const list = await scanList({
    TableName: tableName,
  });
  return convertToEachUser(list);
};

const OBJECT_NOT_EXISTS:any = {};

const attachCalendarInfo = async (surveyResultsRecords) => {
  const surveyCalendarsTableName = surveyCalendarsTable();

  const itemsToAttachCalendarInfo = surveyResultsRecords.filter((element) => {
    return element.value && _.startsWith(element.value, 'category#') && element.value.includes('|');
  });
  const categoriesCache:any = {};
  const calendarsCache:any = {};
  for (let index = 0; index < itemsToAttachCalendarInfo.length; index++) {
    const element = itemsToAttachCalendarInfo[index];

    const paramsList = element.value.split('|');

    const categoryId = paramsList[0].split('_')[0];
    let category = categoriesCache[categoryId];
    if (category === OBJECT_NOT_EXISTS) {
      continue;
    }
    if (!category) {
      const dbCategoryQuery:any = {
        AttributesToGet: ['calendarId'],
        TableName: surveyCalendarsTableName,
        Key: {
          partitionKey: { S: 'categories' },
          sortKey: { S: categoryId },
        },
      };
      category = await getOne(dbCategoryQuery);
      categoriesCache[categoryId] = category || OBJECT_NOT_EXISTS;
    }

    const calendarId = category && category.calendarId;
    if (!calendarId) {
      continue;
    }
    let calendar = calendarsCache[calendarId];
    if (calendar === OBJECT_NOT_EXISTS) {
      continue;
    }
    if (!calendar) {
      const paramsComaList:any = {
        AttributesToGet: ['comaList'],
        TableName: surveyCalendarsTableName,
        Key: {
          partitionKey: { S: 'calendars' },
          sortKey: { S: calendarId },
        },
      };
      calendar = await getOne(paramsComaList);
      calendarsCache[calendarId] = calendar || OBJECT_NOT_EXISTS;
    }

    let reservationStartTime:any = null;
    if (calendar) {
      reservationStartTime = calendar.comaList[paramsList[2]].start;
    }

    element.reservation = {
      date: paramsList[1],
      time: reservationStartTime,
    };

    let reservationItem:any = [];
    if (paramsList[0]) {
      reservationItem = calendarExts.getReservationItemInfo(paramsList[0]);
    }
    element.reservationItem = reservationItem;
  }
};

const getByDbPatterns = async (surveyId, updateFrom, lastEvaluatedKey) => {
  const from = updateFrom || 0;
  const tableName = dynamoDbTable();
  const dbClient = new DDClient.Table(tableName, null, getEnvironmentCredentials());

  const result = await dbClient.queryPage({
    lastEvaluatedKey: lastEvaluatedKey,
    index: 'surveyId-partitionKey-index',
    filter: 'attribute_not_exists(deletedAt) and updatedAt > :f',
    query: 'surveyId = :v_id',
    mapping: {
      ':v_id': surveyId,
      ':f': from,
    },
  });

  if (result.items.length > 0) {
    await attachCalendarInfo(result.items);
  }
  return result;
};

const getWithDatabaseFilter = async (surveyId, filterCommon, lastEvaluatedKey) => {
  const helper = await createSurveyResultsHelper(surveyId);
  const indexedKeys = helper.getIndexedKeys();
  let indexedSearchArguments = _.pick(filterCommon, ...indexedKeys);
  indexedSearchArguments = _.pickBy(indexedSearchArguments, (value) => !_.isEmpty(value));
  if (!_.isEmpty(indexedSearchArguments)) {
    const searchKey = Object.keys(indexedSearchArguments)[0];
    const value = indexedSearchArguments[searchKey];
    const result = await helper.queryResultsByField(searchKey, value, lastEvaluatedKey);
    if (result.items.length > 0) {
      await attachCalendarInfo(result.items);
    }
    return result;
  }
  return await getByDbPatterns(surveyId, 0, lastEvaluatedKey);
};

const get = async (surveyId, updateFrom?, isMember = false) => {
  const from = updateFrom || 0;

  const tableName = isMember ? dynamoDbMembersTable() : dynamoDbTable();

  const filteredList = await queryList({
    TableName: tableName,
    IndexName: 'surveyId-updatedAt-index',
    FilterExpression: 'attribute_not_exists(deletedAt)',
    KeyConditionExpression: 'surveyId = :v_id and updatedAt > :from ',
    ExpressionAttributeValues: {
      ':v_id': { S: surveyId },
      ':from': { N: `${from}` },
    },
  });
  return groupByPartitionKey(filteredList);
};

const getSearchedAndSortedList = async (configs, commonSearchCriteria, isMember = false) => {
  const { filterCommon, filterDate, searchKeyword, sortBy, sortDesc } = commonSearchCriteria;

  // get all data
  const results = await get(configs.surveyId, null, isMember);

  const categoryList:any = {};

  // format
  const data:any[] = [];
  for (const partitionKey in results) {
    const row:any = {
      partitionKey: partitionKey,
    };
    const result = results[partitionKey][0];
    row.createdAt = result.createdAt;
    row.updatedAt = result.updatedAt;
    row.check = result.check || '未対応';
    if (configs && configs.isAppending && configs.isAppending.value === true) {
      row.note = result.note || '';
      if (result.answerCode) {
        row.answerCode = getFormatedAnswerCode(result.answerCode);
      } else {
        row.answerCode = '';
      }
    }
    row.userId = result.userId;
    row.id = result.userId;
    results[partitionKey].map((question) => {
      const _questionConfig = configs.surveySchema.find((obj) => obj.itemKey === question.itemKey);
      if (_questionConfig && _questionConfig.type === 'checkboxes') {
        if (!row[question.itemKey]) {
          row[question.itemKey] = [];
        }
        if (question.value) {
          row[question.itemKey].push(question.value);
        }
      } else if (_questionConfig && _questionConfig.type === 'reservation') {
        row.bunruiIdFull = question.value;
        row.bunrui_id_full = question.value;
        if (row && row.bunruiIdFull) {
          const bunruiIdIndex = row.bunruiIdFull.indexOf('|');
          if (bunruiIdIndex >= 0) {
            row.bunruiId = row.bunruiIdFull.substr(0, bunruiIdIndex);
            row.bunrui_id = row.bunruiIdFull.substr(0, bunruiIdIndex);

            const yoyakuNichiji = row.bunruiIdFull.substr(bunruiIdIndex + 1);
            const yoyakuNichijiIndex = yoyakuNichiji.indexOf('|');
            if (yoyakuNichijiIndex >= 0) {
              row.yoyakuNichi = yoyakuNichiji.substr(0, yoyakuNichijiIndex);
              row.yoyakuJi = yoyakuNichiji.substr(yoyakuNichijiIndex + 1);

              if (row.yoyakuNichi && row.yoyakuNichi.length === 8) {
                row.yoyakuNichiLabel =
                  row.yoyakuNichi.substr(0, 4) +
                  '-' +
                  row.yoyakuNichi.substr(4, 2) +
                  '-' +
                  row.yoyakuNichi.substr(6, 2);
              }

              if (row.yoyakuJi && row.yoyakuJi.length === 4) {
                row.yoyakuJiLabel = row.yoyakuJi.substr(0, 2) + ':' + row.yoyakuJi.substr(2, 2) + ':' + '00';
              }

              if (row.yoyakuNichiLabel && row.yoyakuJiLabel) {
                row.yoyakuNichijiLabel = row.yoyakuNichiLabel + ' ' + row.yoyakuJiLabel;
              }
            }
          }
        } else {
          row.bunruiId = null;
        }

        row[question.itemKey] = question.value;
      } else {
        if (_questionConfig) {
          if (_questionConfig.type !== 'linkbutton') {
            row[question.itemKey] = question.value;
          }
        }
      }
    });

    if (row.bunruiId) {
      if (categoryList.hasOwnProperty.call(row.bunruiId, 'key')) {
        row.bunrui = { ...categoryList[row.bunruiId.split('_')[0]] };
      } else {
        row.bunrui = await calendarExts.getSingleCategory(row.bunruiId);
      }
      if (row.bunrui) {
        categoryList[row.bunruiId] = { ...row.bunrui };

        row.tag1 = row.bunrui.tag1 ? row.bunrui.tag1 : '';
        row.tag2 = row.bunrui.tag2 ? row.bunrui.tag2 : '';
        row.tag3 = row.bunrui.tag3 ? row.bunrui.tag3 : '';
      }
    }

    _.size(row) > 0 ? data.push(row) : null;
  }

  // filter
  // search
  const filtered = data.filter((obj) => {
    let _compareResult = true;
    _compareResult = Object.entries(obj).some((value) => {
      //compare keyword
      const _compareKeyword =
        !searchKeyword ||
        (value[1] &&
          !['createdAt', 'updatedAt', 'id', 'check'].includes(value[0]) &&
          value[1].toString().toLowerCase().includes(searchKeyword.toLowerCase()));

      //compare multi choice
      const _compareMultiChoice = Object.entries(filterCommon).every((searchCondition: [string, any]) => {
        if (searchCondition[1] && searchCondition[1].length > 0) {
          //if condition is array
          if (typeof obj[searchCondition[0]] === 'object') {
            return obj[searchCondition[0]].some((itemValue) => searchCondition[1].includes(itemValue));
          } else {
            return searchCondition[1].includes(obj[searchCondition[0]]);
          }
        }
        return true;
      });

      //compare date
      const _compareDate = Object.entries(filterDate).every((dateCondition:[string,any]) => {
        if (dateCondition[1]) {
          return (
            //compare date from
            (!dateCondition[1].from ||
              obj[dateCondition[0]] >= moment(dateCondition[1].from, 'YYYY-MM-DD').startOf('day').unix()) &&
            //compare date to
            (!dateCondition[1].to ||
              obj[dateCondition[0]] <= moment(dateCondition[1].to, 'YYYY-MM-DD').endOf('day').unix())
          );
        }
        return true;
      });
      return _compareKeyword && _compareMultiChoice && _compareDate;
    });
    return _compareResult;
  });

  filtered.map(function (elem, index) {
    if (elem && (elem['isAppending'] === undefined || elem['isAppending'] === null)) {
      elem['isAppending'] = { value: false };
    }
    return elem;
  });

  // sort
  const sorted = filtered.sort((a, b) => {
    for (let i = 0; i < sortBy.length; i++) {
      const keyName = sortBy[i];
      const desc = sortDesc[i];

      if (a[keyName] === b[keyName]) {
        continue;
      }
      if (a[keyName] > b[keyName]) {
        return desc ? -1 : 1;
      } else {
        return desc ? 1 : -1;
      }
    }
  });

  return sorted;
};

const getByPartitionKey = async (partitionKey, enableConsistentRead = false) => {
  return await queryList({
    TableName: dynamoDbTable(),
    KeyConditionExpression: 'partitionKey = :pk ',
    ConsistentRead: enableConsistentRead,
    ExpressionAttributeValues: {
      ':pk': { S: partitionKey },
    },
  });
};

const getCategory = async (bunruiId) => {
  if (bunruiId === '' || bunruiId == null || bunruiId === undefined) {
    return null;
  }

  try {
    return await calendarExts.getSingleCategory(bunruiId.split('_')[0]);
  } catch (e: any) {
    return null;
  }
};

const getSurveyConfig = async (surveyId, isMember = false) => {
  const tableName = isMember ? dynamoDbMemberConfigTable() : dynamoDbSurveyConfigTable();
  return await queryList({
    TableName: tableName,
    KeyConditionExpression: 'surveyId = :s_id',
    ExpressionAttributeValues: {
      ':s_id': { S: surveyId },
    },
  });
};

const getReservationItem = async (surveyResults, config = null) => {
  const surveyId = surveyResults[0].partitionKey.split('#')[0];
  const surveyConfig = config || await sConfigs.getBySurveyId(surveyId);
  const reservationItemOfConfig = surveyConfig.surveySchema.find(schema => schema.type === 'reservation');
  if (!reservationItemOfConfig || !reservationItemOfConfig.itemKey) {
    return false;
  }
  const reservationItem = surveyResults.find(result => result.itemKey === reservationItemOfConfig.itemKey);
  if (!reservationItem) {
    return false;
  }
  // 予約済みデータかどうかチェック
  const isReservaiton = reservationItem.value.indexOf('|') !== -1;
  return isReservaiton ? reservationItem : false;
}

const deleteSurveyResultOfPreviousVersion = async (partitionKey, nextVersion, expectedResultsCount) => {
  const results = await getByPartitionKey(partitionKey, true);
  if (results.length === 0) {
    return {
      pk: partitionKey,
      isValid: false,
      validationError: 'No results found before deletion',
    };
  }
  const itemsToDelete:any[] = [];
  const itemsToLeft:any[] = [];
  results.forEach((r) => {
    if (r.batchPutVersion === nextVersion) {
      itemsToLeft.push(r);
    } else {
      itemsToDelete.push(r);
    }
  });
  const report:any = {
    pk: partitionKey,
    toDelete: [],
    toLeft: [],
    isValid: false,
    validationError: '',
  };
  report.toDelete = itemsToDelete.map((i) => ({
    sk: i.sortKey,
    ver: i.batchPutVersion,
  }));
  report.toLeft = itemsToLeft.map((i) => ({
    sk: i.sortKey,
    ver: i.batchPutVersion,
  }));

  const tableName = dynamoDbTable();

  for (let i = 0; i < itemsToDelete.length; i++) {
    const result = itemsToDelete[i];

    await deleteOne({
      TableName: tableName,
      Key: {
        partitionKey: { S: result.partitionKey },
        sortKey: { S: result.sortKey },
      },
    });
  }

  const resultsAfterDeletion = await getByPartitionKey(partitionKey, true);
  if (resultsAfterDeletion.length === 0) {
    report.isValid = false;
    report.validationError = 'No results';
  } else if (resultsAfterDeletion.length !== expectedResultsCount) {
    report.isValid = false;
    report.validationError =
      'Fetched results length [' +
      resultsAfterDeletion.length +
      '] ' +
      'did not match expected count ' +
      expectedResultsCount;
  } else {
    // resultsAfterDeletion = resultsAfterDeletion.slice(2);
    const config = await loadCachedConfig(resultsAfterDeletion[0].surveyId);
    const validator = new SurveyResultsValidator(_.cloneDeep(config), resultsAfterDeletion);
    report.isValid = validator.validate();
    report.validationError = validator.getErrorMessageAsString();
  }
  console.log(
    '[UPDATED_ITEM_VALIDATION_RESULT]',
    partitionKey,
    report.isValid ? 'OK' : 'ERROR',
    report.validationError
  );
  if (!report.isValid) {
    console.log('[DATA_LOSS_OLD_RESULTS_DELETION_DETECTED]', partitionKey, JSON.stringify(report.toDelete));
    console.log('[DATA_LOSS_NEW_RESULTS_LEFT_AFTER_DELETION]:', partitionKey, JSON.stringify(report.toLeft));
  }
  return report;
};

const surveyIdsCache:any = {};
const loadCachedConfig = async (surveyId) => {
  if (surveyIdsCache[surveyId]) {
    return surveyIdsCache[surveyId];
  }
  const config = await sConfigs.getBySurveyId(surveyId);
  surveyIdsCache[surveyId] = config;
  return config;
};

const deleteSurveyResultWithoutLatest = async (partitionKey, latestUpdatedAt) => {
  const results = await getByPartitionKey(partitionKey);
  if (results.length === 0) {
    return;
  }

  const tableName = dynamoDbTable();
  for (let i = 0; i < results.length; i++) {
    const result = results[i];
    if (result.updatedAt === latestUpdatedAt) {
      continue;
    }

    await deleteOne({
      TableName: tableName,
      Key: {
        partitionKey: { S: result.partitionKey },
        sortKey: { S: result.sortKey },
      },
    });
  }
};

/**
 * 同一パーティションキーで全項目のsortKeyとvalueを含む更新をする
 * 更新されなかったデータは削除する
 * @param {*} items
 */
const update = async (items) => {
  // 検索機能で利用するアイテム（アルファベットの昇順の最初のアイテム）の更新日の更新を一番最後に行うため
  // アルファベットの降順（Z ～ A）に並べ替え
  items = items.sort(function(a, b) {
    return (a.sortKey < b.sortKey) ? 1 : (a.sortKey > b.sortKey) ? -1 : 0;
  });

  for (let i = 0; i < items.length; i++) {
    const item = items[i];

    const Key:any = {
      partitionKey: { S: item.partitionKey },
      sortKey: { S: item.sortKey },
    };

    const updateItem = Object.assign({}, item);
    if (!item.reservationKeyOld) {
      delete updateItem.reservationKeyOld;
    }
    if (!item.reservationKeyNew) {
      delete updateItem.reservationKeyNew;
    }
    delete updateItem.partitionKey;
    delete updateItem.sortKey;

    if ('userId' in updateItem && (updateItem.userId === null || updateItem.userId === undefined)) {
      const splitPartitionKey = item.partitionKey.split('#');
      if (splitPartitionKey.length >= 2) {
        updateItem.userId = splitPartitionKey[1];
      } else {
        delete updateItem.userId;
      }
    }

    const updateBuilder = new UpdateExpressionBuilder(updateItem);
    updateBuilder.build();
    await updateOne({
      TableName: dynamoDbTable(),
      Key: Key,
      ExpressionAttributeNames: updateBuilder.getExpressionAttributeNames(),
      ExpressionAttributeValues: updateBuilder.getExpressionAttributeValues(),
      UpdateExpression: updateBuilder.getSetExpression(),
    });
  }

  // 更新されなかったデータは削除
  await deleteSurveyResultWithoutLatest(items[0].partitionKey, items[0].updatedAt);
};

/**
 * 同一パーティションキーで全項目のsortKeyとvalue以外を更新する
 * @param {*} items
 */
const updateItemsEntity = async (items) => {
  for (let i = 0; i < items.length; i++) {
    const item = items[i];

    const Key:any = {
      partitionKey: { S: item.partitionKey },
      sortKey: { S: item.sortKey },
    };

    const updateItem = Object.assign({}, item);
    if (!item.reservationKeyOld) {
      delete updateItem.reservationKeyOld;
    }
    if (!item.reservationKeyNew) {
      delete updateItem.reservationKeyNew;
    }
    delete updateItem.partitionKey;
    delete updateItem.sortKey;

    if ('userId' in updateItem && (updateItem.userId === null || updateItem.userId === undefined)) {
      const splitPartitionKey = item.partitionKey.split('#');
      if (splitPartitionKey.length >= 2) {
        updateItem.userId = splitPartitionKey[1];
      } else {
        delete updateItem.userId;
      }
    }

    const updateBuilder = new UpdateExpressionBuilder(updateItem);
    updateBuilder.build();
    await updateOne({
      TableName: dynamoDbTable(),
      Key: Key,
      ExpressionAttributeNames: updateBuilder.getExpressionAttributeNames(),
      ExpressionAttributeValues: updateBuilder.getExpressionAttributeValues(),
      UpdateExpression: updateBuilder.getSetExpression(),
    });
  }
};

const checkIsValidReservation = async (itemsToDelete) => {
  let someValid = false;
  // 分類アイテムのitemKeyを取得
  const surveyId = itemsToDelete[0].partitionKey.split('#')[0];
  const config:any = await sConfigs.getBySurveyId(surveyId);
  const reservationItem = config.surveySchema.find((schema) => schema.type === 'reservation');
  // 分類アイテムが存在しない場合はチェックしない
  if (!reservationItem || !reservationItem.itemKey) {
    return false;
  }

  for (const item of itemsToDelete) {
    // チェック対象のデータを取得
    const { partitionKey } = item;
    const results = await getByPartitionKey(partitionKey);

    // 予約が有効・かつ予約済のデータかチェック
    const reservationRecord = results.find((item) => item.itemKey === reservationItem.itemKey);
    if (!reservationRecord) {
      continue;
    }
    const { value, check } = reservationRecord;
    const isReservation = value.indexOf('|') !== -1;
    if (check !== '取り消し' && check !== 'キャンセル' && isReservation) {
      someValid = true;
      break;
    }
  }
  return someValid;
};

const deleteSurveyResult = async (itemsToDelete) => {
  // 有効な予約データかチェック。有効の場合は削除をしない。
  const isValidReservation = await checkIsValidReservation(itemsToDelete);
  if (isValidReservation) {
    return false;
  }

  // 物理削除
  const keys:any[] = [];
  const batchOperationResults:any = {};
  // 予約リマインド設定も同時に削除したいので分類アイテムのitemKeyをsurveyConfigが取得しておく
  const id = itemsToDelete[0]
    ? itemsToDelete[0].partitionKey || itemsToDelete[0].id
    : null;
  const surveyId = id ? id.split('#')[0] : id;
  const config:any = surveyId
    ? await sConfigs.getBySurveyId(surveyId)
    : null;
  const reservationItem = config
    ? config.surveySchema.find(schema => schema.type === 'reservation')
    : null;
  const reservationItemKey = reservationItem
    ? reservationItem.itemKey
    : null;

  for (let i = 0; i < itemsToDelete.length; ++i) {
    const item = itemsToDelete[i];
    const itemId = item['partitionKey'] || item['id'];
    batchOperationResults[itemId] = { key: itemId };
    const results = await getByPartitionKey(itemId);
    if (results.length === 0) {
      continue;
    }

    for (let i = 0; i < results.length; ++i) {
      const result = results[i];

      keys.push({
        pk: result.partitionKey,
        sk: result.sortKey,
      });
    }
  }

  // カレンダー予約の予約数を減らす
  const itemsToCancelReservations = itemsToDelete.filter((item) => {
    return item.calendarId && item.categoryId && item.reservation;
  });
  const cache:any = {};
  for (const item of itemsToCancelReservations) {
    const itemId = item['partitionKey'] || item['id'];
    const reservationItemId = item.reservationItemId;
    let itemInfo = cache[reservationItemId];
    if (!itemInfo) {
      itemInfo = (await calendarExts.getReservationItemInfo(reservationItemId))[0];
      cache[reservationItemId] = itemInfo;
    }
    let cost = 1;
    if (itemInfo) {
      cost = Number(itemInfo.cost);
    }
    const { date, slot } = item.reservation;
    const cancellationResult = await calendarExts.tryToPlaceReservation(item.calendarId, date, slot, cost * -1);
    batchOperationResults[itemId].reservations_cancelled = cancellationResult ? 1 : 0;
  }

  const client = surveyResultsClient();
  await client.deleteItems(keys);

  return Object.values(batchOperationResults).map((result:any) => {
    return {
      key: result.key,
      status: 'OK',
      reservations_cancelled: result.reservations_cancelled,
    };
  });
};

const deleteSingleSurveyResult = async (itemToDelete) => {
  //This should not be used for deleting survey results
  //Only for deleting one item in special circumstances
  if (!itemToDelete.partitionKey || !itemToDelete.sortKey) {
    return;
  }

  const client = surveyResultsClient();
  await client.deleteItems([
    {
      pk: itemToDelete.partitionKey,
      sk: itemToDelete.sortKey,
    },
  ]);
};

const deleteSurveyResults = async (surveysToDelete) => {
  // 物理削除
  const client = surveyResultsClient();
  const keys:any[] = [];
  for (let i = 0; i < surveysToDelete.length; ++i) {
    const item = surveysToDelete[i];
    const results = await getByPartitionKey(item.partitionKey);
    if (results.length === 0) {
      continue;
    }

    for (let i = 0; i < results.length; ++i) {
      const result = results[i];

      keys.push({
        pk: result.partitionKey,
        sk: result.sortKey,
      });
    }

    // カレンダー予約の予約数を減らす
    if (item.bunruiId && item.yoyakuNichi && item.yoyakuComa) {
      const calendarId = await getCalendarId(item.bunruiId.split('_')[0], true);
      const itemInfo = await calendarExts.getReservationItemInfo(item.bunruiId);
      let cost = 1;
      if (itemInfo.length > 0) {
        cost = Number(itemInfo[0].cost);
      }
      await calendarExts.tryToPlaceReservation(calendarId, item.yoyakuNichi, item.yoyakuComa, cost * -1);
    }
  }

  await client.deleteItems(keys);

  return true;
};

const resetUserId = async (partitionKey, userId) => {
  let res;
  const results = await getByPartitionKey(partitionKey);
  if (results.length === 0) {
    return false;
  }
  for (let index = 0; index < results.length; index++) {
    const element = results[index];

    const key:any = {
      partitionKey: { S: partitionKey },
      sortKey: { S: element.sortKey },
    };

    res = await db.updateOne({
      TableName: dynamoDbTable(),
      ExpressionAttributeNames: {
        '#uId': 'userId',
      },
      ExpressionAttributeValues: {
        ':uId': { S: userId },
      },
      Key: key,
      UpdateExpression: 'SET #uId = :uId',
    });
  }

  return res;
};

const createCSV = async (configs, commonSearchCriteria, headers: ExportCsvColumnConfig[], isMember = false) => {
  let sorted: ExportCsvRowModel[] = await getSearchedAndSortedList(configs, commonSearchCriteria, isMember);
  const pks = sorted.map((item) => item.partitionKey);

  if (sorted !== undefined) {
    sorted = sorted.map((_obj) => {
      const obj:any = {
        ..._obj,
        createdAt: formatUnixToYYYYMMDHHmmss(_obj.createdAt),
        updatedAt: formatUnixToYYYYMMDHHmmss(_obj.updatedAt),
      };

      for (const [key, value] of Object.entries(obj)) {
        if (Array.isArray(value)) {
          obj[key] = value.join(',');
        }
      }

      return obj;
    });
  }

  // format csv data
  const headerObj:any = {};
  headers.map((col) => {
    headerObj[col.value] = col.text;
  });
  sorted.unshift(headerObj);

  const fields = headers.map((col) => {
    return col.value;
  });
  const parseOptions:any = {
    fields,
    header: false,
    eol: '\r\n',
    excelStrings: true,
    withBOM: true,
  };

  const csvData = parse(sorted, parseOptions);

  const surveyTitle = configs?.surveyTitle || 'SurveyTitle';
  const surveyId = configs?.surveyId || 'SurveyId'

  const filename = `${surveyTitle}_${surveyId}_${moment().format('YYYYMMDD_HHmmss')}.csv`;

  const url = await putSurveyResultsCSV(csvData, filename);
  return {
    url,
    pks,
  };
};

const createCSVAppending = async (configs, headers: ExportCsvColumnConfig[], appendingData) => {
  // add/remove headers for CSVappending
  headers = prepareExportHeaders(headers);

  const result:any = {
    headers: headers,
    appendingData: appendingData,
  };

  let sorted = appendingData;

  if (sorted !== undefined && sorted.length > 0) {
    sorted.sort((a, b) => b.updatedAt - a.updatedAt);

    sorted = sorted.map((_obj) => {
      const obj:any = {
        ..._obj,
        createdAt: formatUnixToYYYYMMDHHmmss(_obj.createdAt),
        updatedAt: formatUnixToYYYYMMDHHmmss(_obj.updatedAt),
      };

      for (const [key, value] of Object.entries(obj)) {
        if (Array.isArray(value)) {
          obj[key] = value.join(',');
        }
      }

      return obj;
    });
  } else {
    sorted = [];
  }

  // format csv data
  const headerObj:any = {};
  headers.map((col) => {
    headerObj[col.value] = col.text;
  });
  sorted.unshift(headerObj);

  result['sorted'] = sorted;

  const fields = headers.map((col) => {
    return col.value;
  });
  const parseOptions:any = {
    fields,
    header: false,
    eol: '\r\n',
    excelStrings: true,
    withBOM: true,
  };

  const csvData = parse(sorted, parseOptions);

  const surveyId = configs ? (configs.surveyId || configs['memberSurveyId']) : 'SurveyId'
  const surveyTitle = configs.surveyTitle || 'SurveyTitle';

  const filename = `${surveyTitle}_${surveyId}_${moment().format('YYYYMMDD_HHmmss')}.csv`;

  result['url'] = await putSurveyResultsCSV(csvData, filename);

  return result;
};

const putSurveyResultsCSV = async (csvData, filename) => {
  const s3 = new AWS.S3();

  const url = `/resources/survey_results/${filename}`;

  const params:any = {
    Bucket: config.get(c.RESOURCES_BUCKET),
    Key: `public${url}`,
    Body: csvData,
  };

  await s3.putObject(params).promise();

  const presignedUrl = s3.getSignedUrl('getObject', {
    Bucket: config.get(c.RESOURCES_BUCKET),
    Key: `public${url}`,
    Expires: 60,
  });

  return presignedUrl;
};

const putSurveyResultsImportJSON = async (jsonData, bucketKey, contentType) => {
  const s3 = new AWS.S3();

  const params:any = {
    Bucket: config.get(c.RESOURCES_BUCKET),
    Key: bucketKey,
    Body: JSON.stringify(jsonData),
    ContentType: contentType,
    ContentEncoding: 'utf-8',
  };

  await s3.putObject(params).promise();
};

const createInsertDataForUpdateOptions = (oldRow, fromRegexp, to) => {
  // 必ずoldRowに存在する
  const options:any = {
    partitionKey: { S: oldRow.partitionKey },
    sortKey: { S: oldRow.sortKey.replace(fromRegexp, to) },
    surveyId: { S: oldRow.surveyId },
    userId: { S: oldRow.userId },
    itemKey: { S: oldRow.itemKey },
    userSearchKey: { S: oldRow.userSearchKey.replace(fromRegexp, to) },
    createdAt: { N: `${oldRow.createdAt}` },
    updatedAt: { N: `${oldRow.updatedAt}` },
  };
  // oldRowに必ず存在するわけではないが補完が必要
  const nullData = { NULL: true };
  if (oldRow.value) {
    options.value = { S: oldRow.value.replace(fromRegexp, to) };
  } else {
    options.value = nullData;
  }
  if ('note' in oldRow) {
    options.note = oldRow.note === null ? nullData : { S: oldRow.note };
  }
  if ('answerCode' in oldRow) {
    let data:any = null;
    const { answerCode } = oldRow;

    // answerCodeはstring, integer, nullのパターンがあり得るのでパターン毎の補完が必要
    if (answerCode === null) {
      data = nullData;
    } else if (typeof answerCode === 'string') {
      data = { S: answerCode };
    } else {
      data = { N: `${answerCode}` };
    }

    if (data) {
      options.answerCode = data;
    }
  }
  if ('check' in oldRow) {
    options.check = oldRow.check === null ? nullData : { S: oldRow.check };
  }
  return options;
};

const updateOptions = async (surveyId, updateOptions) => {
  let promises = [];
  const tableName = dynamoDbTable();

  for (const [itemKey, value] of Object.entries(updateOptions) as [string, any]) {
    const fromList = await queryList({
      TableName: dynamoDbTable(),
      IndexName: 'surveyId-sortKey-index',
      KeyConditionExpression: 'surveyId = :v_id and sortKey = :v_skey',
      ExpressionAttributeValues: {
        ':v_id': { S: surveyId },
        ':v_skey': { S: `${itemKey}#${value.from}` },
      },
    });

    const createParams:any = { RequestItems: {} };
    createParams.RequestItems[tableName] = [];
    const deleteParams:any = { RequestItems: {} };
    deleteParams.RequestItems[tableName] = [];
    const fromRegexp = new RegExp(value.from, 'g'); // global search for replace
    fromList.map(async (oldRow) => {
      const newRow = createInsertDataForUpdateOptions(oldRow, fromRegexp, value.to);
      createParams.RequestItems[tableName].push({
        PutRequest: {
          Item: newRow,
        },
      });
      deleteParams.RequestItems[tableName].push({
        DeleteRequest: {
          Key: {
            partitionKey: { S: oldRow.partitionKey },
            sortKey: { S: oldRow.sortKey },
          },
        },
      });

      // batchWriteItem is max 25 operations
      if (createParams.RequestItems[tableName].length === 25) {
        const createPromise = batchWriteItem({ ...createParams });
        const deletePromise = batchWriteItem({ ...deleteParams });
        promises.push(createPromise, deletePromise);

        createParams.RequestItems[tableName] = [];
        deleteParams.RequestItems[tableName] = [];
      }
    });

    // execute if any left
    if (createParams.RequestItems[tableName].length > 0) {
      const createPromise = batchWriteItem({ ...createParams });
      const deletePromise = batchWriteItem({ ...deleteParams });
      promises.push(createPromise, deletePromise);
    }
  }

  // execute until there is no UnprocesssedItems in response
  do {
    const results = await Promise.all(promises);

    promises = [];
    results.map((result) => {
      if (Object.keys(result.UnprocessedItems).length === 0) {
        return;
      }

      const params:any = { RequestItems: {} };
      params.RequestItems[tableName] = result.UnprocessedItems[tableName];
      const promise = batchWriteItem(params);
      promises.push(promise);
    });
  } while (promises.length !== 0);
};

const getOneItem = async (partitionKey, sortKey) => {
  const key:any = {
    partitionKey: { S: partitionKey },
    sortKey: { S: sortKey },
  };

  const params:any = {
    TableName: dynamoDbTable(),
    Key: key,
  };
  const res = await db.getOne(params);
  return res;
};

const getOneItemByExistsAnswerCode = async (partitionKey, sortKey) => {
  const key:any = {
    partitionKey: { S: partitionKey },
    sortKey: { S: sortKey },
  };

  const params:any = {
    TableName: dynamoDbTable(),
    Key: key,
    ProjectionExpression: 'answerCode',
  };
  const res = await db.getOne(params);
  return res;
};

const updateCounter = async (partitionKey, sortKey) => {
  const key:any = {
    partitionKey: { S: partitionKey },
    sortKey: { S: sortKey },
  };

  const res = await db.updateOne({
    TableName: dynamoDbTable(),
    ExpressionAttributeNames: {
      '#AN': 'answerCode',
    },
    ExpressionAttributeValues: {
      ':increase': { N: '1' },
    },
    Key: key,
    UpdateExpression: 'SET #AN = #AN + :increase',
  });
  return res;
};

const tryPlaceReservationIfPresents = async (items, configs, userId, costOfPayment?: number) => {
  const reservationName = 'reservation';
  const reservationTypeConfig = configs.surveySchema.find((item) => item.type === reservationName);
  if (!reservationTypeConfig) {
    return true;
  }

  const categoryItem = items.find((item) => item.itemKey === reservationTypeConfig.itemKey);
  if (!categoryItem || !categoryItem.value) {
    return true;
  }

  // コロナワクチンの帳表の場合は同時刻に登録済みかどうかのチェックを除外
  if (!configs.surveyType || (configs.surveyType && configs.surveyType !== 'corona')) {
    if (categoryItem && categoryItem.value.includes('|')) {
      const keys = categoryItem.sortKey.split('#');
      const itemKey = keys[0];

      const params:any = {
        TableName: dynamoDbTable(),
        IndexName: 'surveyId-sortKey-index',
        ExpressionAttributeValues: {
          ':sid': { S: categoryItem.surveyId },
          ':ik': { S: itemKey },
          ':sk': { S: categoryItem.value },
          ':uid': { S: userId },
        },
        ExpressionAttributeNames: { '#v': 'value' },
        KeyConditionExpression: 'surveyId = :sid and begins_with(sortKey,:ik)',
        FilterExpression: 'userId = :uid and #v = :sk',
      };
      const result = await db.queryList(params);
      if (Array.isArray(result) && result.length > 0) {
        return 'reserved';
      }
    }
  }

  const indexOf = categoryItem.value.indexOf('|');
  if (indexOf > 0) {
    const [categoryId, day, time] = categoryItem.value.split('|');
    const setCategoryId = categoryId.split('_')[0];
    const calendarId = await getCalendarId(setCategoryId, true);
    let cost = 1;
    if (costOfPayment) {
      cost = costOfPayment;
    } else {
      const itemInfo = await calendarExts.getReservationItemInfo(categoryId);
      if (itemInfo.length > 0) {
        cost = Number(itemInfo[0].cost);
      }
    }
    // 必要な予約枠数があるかどうか
    const reservationCheck = await calendarExts.checkReservationPossible(calendarId, day, time, cost);
    if (!reservationCheck) {
      return 'NoQuotas';
    }
    return await calendarExts.tryToPlaceReservation(calendarId, day, time, cost);
  }

  return true;
};

const cancelReservation = async (items, configs, lastest, costOfPayment?: number, rollbackHelper?: SurveyResultsRollbackHelper) => {
  const reservationName = 'reservation';
  const reservationTypeConfig = configs.surveySchema.find((item) => item.type === reservationName);
  if (!reservationTypeConfig) {
    return true;
  }
  // canceled
  const lastCategoryItem = lastest.find((item) => item.itemKey === reservationTypeConfig.itemKey);
  if (!lastCategoryItem || !lastCategoryItem.value || lastCategoryItem.check === 'キャンセル') {
    return true;
  }

  const categoryItem = items.find((item) => item.itemKey === reservationTypeConfig.itemKey);
  if (!categoryItem || !categoryItem.value) {
    return true;
  } else if (categoryItem.check === 'キャンセル') {
    //保存されている予約をキャンセルして、フロントからもらったの予約ではない。
    const indexOf = lastCategoryItem.value.indexOf('|');
    if (indexOf > 0) {
      const val = lastCategoryItem.value;
      // 元々の予約日時の予約数 -1
      const [categoryId, day, coma] = val.split('|');
      const setCategoryId = categoryId.split('_')[0];
      const calendarId = await getCalendarId(setCategoryId, true);
      let cost = 1;
      if (costOfPayment) {
        cost = costOfPayment;
      } else {
        const itemInfo = await calendarExts.getReservationItemInfo(categoryId);
        if (itemInfo.length > 0) {
          cost = Number(itemInfo[0].cost);
        }
      }
      // 減らす予約数があるかどうか
      const reservationCheck = await calendarExts.checkReservationPossible(calendarId, day, coma, cost * -1);
      if (!reservationCheck) {
        return false;
      }
      const updateReservationResult = await calendarExts.tryToPlaceReservation(calendarId, day, coma, cost * -1);
      if (rollbackHelper) {
        rollbackHelper.oldReservationInfo = { calendarId, date: day, timeSlot: coma, cost };
      }
      return updateReservationResult;
    }
    return true;
  }
  return true;
};

const updateReservationIfPresents = async (items, configs, lastest, costOfPayment?: number, rollbackHelper?: SurveyResultsRollbackHelper) => {
  const reservationName = 'reservation';
  const reservationTypeConfig = configs.surveySchema.find((item) => item.type === reservationName);
  if (!reservationTypeConfig) {
    return true;
  }

  const categoryItem = items.find((item) => item.itemKey === reservationTypeConfig.itemKey);
  if (!categoryItem || !categoryItem.value) {
    return true;
  } else if (categoryItem.check === 'キャンセル') {
    return true;
  }

  // canceled check
  const lastCategoryItem = lastest.find((item) => item.itemKey === reservationTypeConfig.itemKey);
  const isCanceled = !lastCategoryItem || !lastCategoryItem.value || lastCategoryItem.check === 'キャンセル';

  const _reservationKeyNew = categoryItem.reservationKeyNew; // 選択された予約日時
  const _reservationKeyOld = lastCategoryItem ? lastCategoryItem.value : undefined; // 元々の予約日時

  const _requestLatestUpdateTime = categoryItem.updatedAt;
  const existingDBItem = await getOneItem(
    categoryItem.partitionKey,
    categoryItem.itemKey + '#' + _reservationKeyOld
  );

  if (
    _requestLatestUpdateTime &&
    existingDBItem &&
    existingDBItem.updatedAt &&
    existingDBItem.updatedAt > _requestLatestUpdateTime
  ) {
    return 'DataConflicted';
  }

  // 予約日時が変更されている場合のみ
  if (_reservationKeyNew && (_reservationKeyNew !== _reservationKeyOld || isCanceled)) {
    // 新しく選択された予約日時の予約数 +1
    const indexOf = _reservationKeyNew.indexOf('|');
    if (indexOf > 0) {
      const [categoryId, day, time] = _reservationKeyNew.split('|');
      const setCategoryId = categoryId.split('_')[0];
      const calendarId = await getCalendarId(setCategoryId, true);
      let cost = 1;
      if (costOfPayment) {
        cost = costOfPayment;
      } else {
        const itemInfo = await calendarExts.getReservationItemInfo(categoryId);
        if (itemInfo.length > 0) {
          cost = Number(itemInfo[0].cost);
        }
      }
      // 必要な予約枠数があるかどうか
      const reservationCheck = await calendarExts.checkReservationPossible(calendarId, day, time, cost);
      if (!reservationCheck) {
        return 'NoQuotas';
      }

      let reservationCheckOld = true;
      let calendarIdOld:any = null;
      let dayOld:any = null;
      let timeOld:any = null;
      let costOld = 1;
      if (reservationCheck && _reservationKeyOld) {
        // 元々の予約日時の予約数 -1
        const indexOfOld = _reservationKeyOld.indexOf('|');
        if (indexOfOld > 0) {
          const [wkCategoryIdOld, wkDayOld, wkTimeOld] = _reservationKeyOld.split('|');
          const setCategoryIdOld = wkCategoryIdOld.split('_')[0];
          calendarIdOld = await getCalendarId(setCategoryIdOld, true);
          dayOld = wkDayOld;
          timeOld = wkTimeOld;
          if (costOfPayment) {
            costOld = costOfPayment;
          } else {
            const itemInfoOld = await calendarExts.getReservationItemInfo(wkCategoryIdOld);
            if (itemInfoOld.length > 0) {
              costOld = itemInfoOld[0].cost;
            }
          }
          // 減らす予約数があるかどうか
          reservationCheckOld = await calendarExts.checkReservationPossible(
            calendarIdOld,
            dayOld,
            timeOld,
            costOld * -1
          );
        }
      }

      if (!reservationCheckOld && !isCanceled) {
        return 'NoReservations';
      }

      let updateReservationResult = false;
      if (reservationCheck && (reservationCheckOld || isCanceled)) {
        // 予約数 +1
        updateReservationResult = await calendarExts.tryToPlaceReservation(calendarId, day, time, cost);
        // reservation from canceled
        if (isCanceled) {
          return updateReservationResult;
        }
        if (updateReservationResult && calendarIdOld && dayOld && timeOld) {
          // 予約数 -1
          updateReservationResult = await calendarExts.tryToPlaceReservation(
            calendarIdOld,
            dayOld,
            timeOld,
            costOld * -1
          );
        }
      }
      return updateReservationResult;
    }
    return true;
  } else {
    return true;
  }
};

const getCalendarId = async (calendarOrCategoryId, required = false) => {
  if (calendarOrCategoryId.startsWith('category')) {
    const category = await calendarExts.getSingleCategory(calendarOrCategoryId);
    required && calendarUtils.assertCategoryExists(category, calendarOrCategoryId);
    required &&
      calendarUtils.assertExists(category.calendarId, `No linked calendar for category [${calendarOrCategoryId}]`);

    if (!category.calendarId) {
      return null;
    }
    return category.calendarId;
  }
  return calendarOrCategoryId;
};

const getImportCsvAppendingPresignedUrl = async (bucketKey, contentType) => {
  const s3 = new AWS.S3();

  const presignedUrl = s3.getSignedUrl('putObject', {
    Bucket: config.get(c.RESOURCES_BUCKET),
    Key: bucketKey,
    ContentType: contentType,
    Expires: 5 * 60, // 5 minutes, but might take longer
  });

  return presignedUrl;
};

const checkImportStatusAndCallAppendingInvoker = async (username, surveyId, bucketKey, filename) => {
  const lambdaName = process.env.FUNCTION_ASYNC_JOBS_HANDLER; // self lambda

  const statusResult = await homeNotificationService.getHomeNotificationData(surveyId);

  const LAMBDA_TIME_OUT_MINUTES_SAFETY_NET = 30;
  if (
    statusResult.data &&
    statusResult.data.importCsvAppendingStatus &&
    statusResult.data.importCsvAppendingStatus.status &&
    statusResult.data.importCsvAppendingStatus.startedAt
  ) {
    let timeLimitForImportWaitExceeded = false;
    const startedTime = moment.unix(statusResult.data.importCsvAppendingStatus.startedAt);
    const nowTime = moment();
    const timeDifference = moment.duration(nowTime.diff(startedTime)).asMinutes();
    if (timeDifference > LAMBDA_TIME_OUT_MINUTES_SAFETY_NET) {
      timeLimitForImportWaitExceeded = true;
    }
    if (!timeLimitForImportWaitExceeded && statusResult.data.importCsvAppendingStatus.status !== 'FINISHED') {
      return {
        result: 'ERROR',
        errorMessage:
          'インポートのステータスが「処理中」です。' +
          'ステータスが「完了」に変化後、または今回のインポート開始時刻の30分後（' +
          startedTime.add(30, 'minutes').format('MM/DD HH:mm') +
          '）から再度インポートすることができます。' +
          'インポートのステータスを確認するには「通知情報」のボタンを押下してください。',
      };
    }
  }

  try {
    const lambda = createLambdaClient();
    const response = await lambda
      .invoke({
        FunctionName: lambdaName,
        InvocationType: 'Event',
        Payload: JSON.stringify({
          PlatformAsyncFunction: {
            MainAsyncLambdaImportInvoker: {
              username: username,
              surveyId: surveyId,
              bucketKey: bucketKey,
              filename: filename,
            },
          },
        }),
      })
      .promise();
  } catch (e: any) {
    console.log(e);
    throw e;
  }

  return {
    result: 'OK',
  };
};

const asyncImportCsvAppendingInvoker = async (username, surveyId, bucketKey, filename) => {
  const lambdaName = process.env.FUNCTION_ASYNC_JOBS_HANDLER || process.env.AWS_LAMBDA_FUNCTION_NAME; // self lambda

  try {
    const s3Response = await platformAsyncService.importCsvAppendingFetchFromS3(username, surveyId, bucketKey);

    const toUpdate = s3Response.body.toUpdate;
    const toCreate = s3Response.body.toCreate;

    if (
      (!toUpdate || !Array.isArray(toUpdate) || toUpdate.length === 0) &&
      (!toCreate || !Array.isArray(toCreate) || toCreate.length === 0)
    ) {
      return {
        result: 'ERROR',
        errorMessage: 'no [toUpdate] items and no [toCreate] items',
        surveyId: surveyId,
        bucketKey: bucketKey,
      };
    }

    const initialState:any = {
      result: 'OK',
      stats: {
        errorCount: 0,
        successCount: 0,
        total: 0,
      },
      filename: filename,
    };

    await platformAsyncService.importCsvAppendingStatusUpdate(username, surveyId, 'STARTED', initialState);
    await platformAsyncService.deletePreviousImportCsvAppendingResults(surveyId);

    const totalItems = toUpdate.length + toCreate.length;

    let numberOfLambdasToUse = 1000;
    const valuesPerLambda = Math.ceil(totalItems / numberOfLambdasToUse);
    const lambdaRanges:any[] = [];
    if (totalItems <= numberOfLambdasToUse) {
      numberOfLambdasToUse = totalItems;
    }
    for (let x = 0; x < numberOfLambdasToUse; x++) {
      const tempRange:any = {
        start: x * valuesPerLambda,
        end: (x + 1) * valuesPerLambda,
      };
      if (tempRange.end > totalItems) {
        tempRange.end = totalItems;
      }

      lambdaRanges.push(tempRange);
    }

    const params:any = {
      FunctionName: lambdaName,
      InvocationType: 'Event', // for async call
    };

    const lambda = createLambdaClient();
    let rampUpRatio = 0;
    for (let y = 0; y < numberOfLambdasToUse; y++) {
      const rowValues:any[] = [];
      const partitionKeyMap:any = {};
      const startIndex = lambdaRanges[y].start;
      const endIndex = lambdaRanges[y].end;
      for (let i = startIndex; i < endIndex; i++) {
        if (i < toUpdate.length) {
          rowValues.push(toUpdate[i].row);
          partitionKeyMap[toUpdate[i].row] = toUpdate[i].partitionKey;
        } else {
          rowValues.push(toCreate[i - toUpdate.length].row);
        }
      }
      if (y !== 0 && y % (10 * Math.pow(2, rampUpRatio)) === 0) {
        await sleep(60000);
        rampUpRatio++;
      }
      const response = await lambda
        .invoke({
          ...params,
          Payload: JSON.stringify({
            PlatformAsyncFunction: {
              ImportCsvAppending: {
                username: username,
                surveyId: surveyId,
                bucketKey: bucketKey,
                start: startIndex,
                end: endIndex,
                totalLambdas: numberOfLambdasToUse,
                totalItems: endIndex - (startIndex - 1),
                rows: rowValues,
                partitionKeyMap: partitionKeyMap,
              },
            },
          }),
        })
        .promise();
    }
  } catch (err: any) {
    await platformAsyncService.importCsvAppendingStatusUpdate(username, surveyId, 'ERROR', {
      result: 'ERROR',
      errorMessage: 'インポート準備ながらエラー発生しました。',
    });
    console.log('[ERROR] asyncImportCsvAppendingInvoker ' + err);
    return {
      result: 'ERROR',
      error: err,
    };
  }

  return {
    result: 'OK',
    surveyId: surveyId,
    bucketKey: bucketKey,
  };
};

const ALLOTED_UNPROCESSED_RETRIES = 100;
const batchPutSurveyResult = async (items) => {
  console.log('[STARTING_BATCH_PUT_SURVEY_RESULT] Starting batch put for items. Length: ' + items.length);

  const batchPutVersion = `${moment().unix()}__${randomString(7)}`;

  const itemsBeingPut:any[] = [];
  for (let i = 0; i < items.length; ++i) {
    const item = items[i];

    const updateItem = Object.assign({}, item);
    if (!item.reservationKeyOld) {
      delete updateItem.reservationKeyOld;
    }
    if (!item.reservationKeyNew) {
      delete updateItem.reservationKeyNew;
    }

    updateItem.batchPutVersion = batchPutVersion;

    items[i] = updateItem;

    const partitionKeyMapped = itemsBeingPut.find((obj) => obj.partitionKey === items[i].partitionKey);
    if (partitionKeyMapped === undefined) {
      itemsBeingPut.push({
        partitionKey: items[i].partitionKey,
        updatedAt: items[i].updatedAt,
      });
    }
  }

  let batch = [];
  const unprocessedElements:any[] = [];
  const unprocessedQuestions:any[] = [];
  let successCounter = 0;
  let currentBatchUnprocessed = 0;
  for (let x = 0; x < items.length; x++) {
    batch.push(items[x]);
    successCounter++;
    if (batch.length === 25 || x + 1 === items.length) {
      currentBatchUnprocessed = batch.length;

      let result:any = await batchWriteItem({
        RequestItems: {
          [dynamoDbTable()]: batch.map((i) => {
            const marshalled = AWS.DynamoDB.Converter.marshall(i, {
              convertEmptyValues: true,
            });

            return {
              PutRequest: {
                Item: marshalled,
              },
            };
          }),
        },
      });

      if (result.err) {
        console.log('[BATCH_WRITE_AWS_ERROR] Error returned from batchWriteItem');
        console.log(result.err);
      }

      let retryCounter = 1;
      while (
        result.UnprocessedItems &&
        result.UnprocessedItems[dynamoDbTable()] &&
        retryCounter < ALLOTED_UNPROCESSED_RETRIES
      ) {
        currentBatchUnprocessed = result.UnprocessedItems[dynamoDbTable()].length;
        const difference = _.random(1, 10);
        const sleepTime = 1000 - difference * 100 + 100 * retryCounter;
        console.log(
          `[UNPROCESSED_ITEMS]  (${retryCounter}) Number of unprocessed items in the batch:`,
          currentBatchUnprocessed,
          `, saved: ${successCounter - currentBatchUnprocessed}, waiting for ${sleepTime} ms before retry`
        );
        await sleep(sleepTime);
        try {
          result = await batchWriteItem({
            RequestItems: result.UnprocessedItems,
          });
        } catch (e: any) {
          console.log('[BATCH_WRITE_AWS_ERROR]', e.name, ' occured. Retrying...');
        }
        retryCounter++;
      }

      if (result.UnprocessedItems && result.UnprocessedItems[dynamoDbTable()]) {
        console.log('[UNPROCESSED_ITEMS_REMAINING] ImportCsvAppending Ended execution with unprocessed items......');
        console.log(result.UnprocessedItems);
        for (const item of result.UnprocessedItems[dynamoDbTable()]) {
          console.log('[UNPROCESSED_ITEMS_ERROR_START] found an item in unprocessed items list');
          console.log(item.PutRequest);
          const unmarshalled = AWS.DynamoDB.Converter.unmarshall(item.PutRequest.Item, {
            convertEmptyValues: true,
          });
          console.log(unmarshalled);
          if (!unprocessedElements.includes(unmarshalled.partitionKey)) {
            unprocessedElements.push(unmarshalled.partitionKey);
          }
          unprocessedQuestions.push({
            partitionKey: unmarshalled.partitionKey,
            itemKey: unmarshalled.sortKey.split('#')[0],
          });
        }
      }

      batch = [];
    }
  }

  const errorOccuredDeletingOldValues:any[] = [];
  const deletingErrorMap = new Map();
  const deletesReport:any = {};
  for (const itemsForClearing of itemsBeingPut) {
    try {
      //Expected number of results after deletion
      const expectedResultsCount = items.filter((item) => item.partitionKey === itemsForClearing.partitionKey).length;
      console.log('[SURVEY_EXPECTED_COUNT]', itemsForClearing.partitionKey, expectedResultsCount);
      const report = await deleteSurveyResultsWithoutLatestRetry(
        itemsForClearing.partitionKey,
        batchPutVersion,
        expectedResultsCount
      );
      if (!report.isValid) {
        deletesReport[itemsForClearing.partitionKey] = report;
      }
    } catch (error: any) {
      console.log('[ERROR] ImportCsvAppending DeleteOldValuesProcessing: ');
      console.log(error);
      errorOccuredDeletingOldValues.push(itemsForClearing.partitionKey);
      deletingErrorMap.set(itemsForClearing.partitionKey, error.message ? error.message : error);
    }
  }

  const totalErrorList:any[] = [];
  for (const elem of unprocessedElements) {
    if (!totalErrorList.includes(elem)) {
      totalErrorList.push(elem);
    }
  }
  for (const elem of errorOccuredDeletingOldValues) {
    if (!totalErrorList.includes(elem)) {
      totalErrorList.push(elem);
    }
  }
  for (const elem of Object.keys(deletesReport)) {
    if (!totalErrorList.includes(elem)) {
      totalErrorList.push(elem);
    }
  }

  return {
    result: 'OK',
    unprocessed_values: unprocessedElements,
    unprocessed_questions: unprocessedQuestions,
    delete_failed_values: errorOccuredDeletingOldValues,
    delete_failed_messages: deletingErrorMap,
    total_error_count: totalErrorList.length,
    deletesReport,
  };
};

const ALLOTED_DELETE_RETRIES = 10;
const deleteSurveyResultsWithoutLatestRetry = async (partitionKey, batchPutVersion, expectedResultsCount) => {
  let caughtError:any = {};
  let attemptedDeletes = 0;
  do {
    await sleep(500 * attemptedDeletes);
    try {
      return await deleteSurveyResultOfPreviousVersion(partitionKey, batchPutVersion, expectedResultsCount);
    } catch (error: any) {
      attemptedDeletes++;
      console.log(
        '[DELETE_SURVEY_RESULTS_WITHOUT_LATEST_RETRY_ERROR] An error occured when calling deleteSurveyResultWithoutLatest. Retrying ' +
          attemptedDeletes
      );
      console.log(error);
      caughtError = error;
    }
  } while (caughtError.retryable && attemptedDeletes < ALLOTED_DELETE_RETRIES);

  if (attemptedDeletes === ALLOTED_DELETE_RETRIES) {
    console.log(
      '[DELETE_SURVEY_RESULTS_WITHOUT_LATEST_RETRY] Reached max number of allowable retry' +
        ' attempts for deleteSurveyResultWithoutLatest'
    );
  }

  throw caughtError;
};

const getReservationData = async (surveyId, itemKey, userId) => {
  const params:any = {
    TableName: dynamoDbTable(),
    IndexName: 'userId-surveyId-index',
    ExpressionAttributeValues: {
      ':sid': { S: surveyId },
      ':ik': { S: itemKey },
      ':uid': { S: userId },
      ':cdel': { S: '取り消し' },
      ':ccan': { S: 'キャンセル' },
    },
    ExpressionAttributeNames: {
      '#c': 'check',
    },
    KeyConditionExpression: 'userId = :uid and surveyId = :sid',
    FilterExpression:
      'begins_with(sortKey,:ik) and not contains(#c,:cdel) and not contains(#c,:ccan) and attribute_not_exists(deletedAt)',
  };
  return await db.queryList(params);
};

const findIndexSearcheableField = (surveyConfig) => {
  const field = surveyConfig.surveySchema.filter((field) => {
    return field.isIndexable && field.isIndexable.value && field.isSearchable && field.isSearchable.value;
  })[0];
  return field || null;
};

const indexSearch = async (surveyConfig, fieldKey, queryQuestions) => {
  const helper = new SurveyResultsHelper(surveyConfig, {
    awsConfig: getEnvironmentCredentials(),
    resultsTable: dynamoDbTable(),
  });
  const indexableParameterFilter = queryQuestions.find((res) => res.itemKey === fieldKey);
  if (!indexableParameterFilter || !indexableParameterFilter.value) {
    return [];
  }
  const indexSearchResult = await helper.queryResultsByField(fieldKey, indexableParameterFilter.value);

  const surveyResults = SurveyResult.buildFromLines(indexSearchResult.items, surveyConfig);
  const filtered = surveyResults.filter((result) => {
    return (
      result.matches({
        logic: 'and',
        fields: queryQuestions,
      }) &&
      result.getStatusOfResultLine() !== '取り消し' &&
      result.getStatusOfResultLine() !== 'キャンセル'
    );
  });
  const searchResult = filtered.map((surveyResult) => {
    return {
      partitionKey: surveyResult.resultId,
      data: surveyResult.getLinedAnswers(),
    };
  });

  if (searchResult.length === 0 || searchResult[0].data.length === 0) {
    return searchResult;
  }

  const result = _.cloneDeep(searchResult);
  // Deleting isAdminItem in search results
  for (let index = 0; index < searchResult.length; index++) {
    const eSearchResult = searchResult[index];

    const data:any[] = [];
    for (let i = 0; i < surveyConfig.surveySchema.length; i++) {
      const eleSurveySchema = surveyConfig.surveySchema[i];
      for (let j = 0; j < eSearchResult.data.length; j++) {
        const eleSearchResult = eSearchResult.data[j];
        if (eleSurveySchema.itemKey === eleSearchResult.itemKey) {
          if (
            (surveyConfig.surveyType === 'corona' &&
              (eleSurveySchema.type === 'sesshuJisshiDate' || eleSurveySchema.type === 'sesshuVaccineMaker')) ||
            !eleSurveySchema.isAdminItem
          ) {
            data.push(eleSearchResult);
            break;
          }
        }
      }
    }
    result[index].data = data;
  }
  return result;
};

const fetchUsingSurveyIdSortKeyIndexWithMultipleSortKeys = async (surveyId, sortKeys) => {
  let filterExpression = '#sortKey IN (';
  const expressionNames:any = {
    '#sortKey': 'sortKey',
  };
  const expressionValues:any = {
    ':sid': { S: surveyId },
  };

  let count = 1;
  for (const sortKey of sortKeys) {
    filterExpression += ':key' + count;
    expressionValues[':key' + count] = { S: sortKey };

    if (count < sortKeys.length) {
      filterExpression += ', ';
    }

    count++;
  }
  filterExpression += ')';
  const params:any = {
    TableName: dynamoDbTable(),
    IndexName: 'surveyId-index',
    ExpressionAttributeValues: expressionValues,
    KeyConditionExpression: 'surveyId = :sid',
    ExpressionAttributeNames: expressionNames,
    FilterExpression: filterExpression,
  };

  const result = await db.queryList(params);
  return result;
};

const runValidationAndTransformationOnCsvFile = async (surveyId, bucketKey, isSJISEncoding, surveyConfig) => {
  //read the data from s3
  const s3response = await platformAsyncService.importValidationCSVAppendingFetchFromS3(bucketKey, isSJISEncoding);

  const parseResult:any = await CsvToJson(s3response, isSJISEncoding)
    .then((result) => {
      return result;
    })
    .catch((error) => {
      console.log('an error occured in papaparse');
      console.log(error);
      return;
    });

  if (parseResult == null) {
    return {
      result: 'ERROR',
      errorType: 'CSV_ERROR',
      errorMessage: 'CSVファイル読めませんでした。',
    };
  }

  if (parseResult.errors.length > 0) {
    let errorMessages = parseResult.errors;
    if (errorMessages.length > 10) {
      errorMessages = errorMessages.slice(0, 10);
      errorMessages.push('...', `${parseResult.errors.length}個のエラーが発生しました。`);
    }
    errorMessages.push('インプットファイルは.csvですか？');
    return {
      result: 'ERROR',
      errorType: 'CSV_ERROR',
      errorMessage: errorMessages,
    };
  }

  const calendarDisplay = await calendarExts.getGlobalDisplaySettings();

  let memberSurveyConfig:any = null;
  if (surveyConfig.memberFormId) {
    memberSurveyConfig = await memberConfig.getBySurveyId(surveyConfig.memberFormId);
  }

  const surveyResultsImportHelper = new SurveyResultsImportHelper(
    surveyConfig,
    parseResult.data,
    calendarDisplay,
    memberSurveyConfig
  );

  if (!surveyResultsImportHelper.validate()) {
    //for debugging
    surveyResultsImportHelper.printDebugErrors();
    return {
      result: 'ERROR',
      errorType: 'VALIDATION_ERROR',
      errorMessage: surveyResultsImportHelper.getErrors().join('\n'),
    };
  }

  let cleanedFileData = surveyResultsImportHelper.preparePayload();

  if (cleanedFileData.toCreate.length + cleanedFileData.toUpdate.length === 0) {
    return {
      result: 'ERROR',
      errorType: 'VALIDATION_ERROR',
      errorMessage: 'CSVにデータが1行もありません。',
    };
  }

  cleanedFileData = surveyResultsImportHelper.fixDatesForQuestions(cleanedFileData);

  const uniqueId = generateUuid();
  const bucketKeyOfTransform = `public/resources/survey_results/import_csv_appending/${surveyId}_json_${uniqueId}`;
  const transformFileContentType = 'application/json';

  try {
    await putSurveyResultsImportJSON(
      {
        params: {
          surveyId: cleanedFileData.surveyId,
        },
        body: {
          toUpdate: cleanedFileData.toUpdate,
          toCreate: cleanedFileData.toCreate,
        },
      },
      bucketKeyOfTransform,
      transformFileContentType
    );
  } catch (error: any) {
    console.log(error);
    return {
      result: 'ERROR',
      errorType: 'S3_ERROR',
      errorMessage: '選んだファイルをS3に保存できませんでした。',
    };
  }

  return {
    result: 'OK',
    jsonFileBucketKey: bucketKeyOfTransform,
    bunruiUpdatedFlag: cleanedFileData.status.bunruiUpdatedFlag,
  };
};

const getResultsByFormAndUserId = async (surveyFormId, userId) => {
  // Do a query by survey form ID and user ID
  const dbClient = new DDClient.Table(dynamoDbTable(), null, getEnvironmentCredentials());

  // Query params
  const params:any = {
    index: 'userId-surveyId-index',
    query: '#userId = :userIdValue and #surveyId = :surveyIdValue',
    mapping: {
      '#surveyId': 'surveyId',
      '#userId': 'userId',
      ':surveyIdValue': surveyFormId,
      ':userIdValue': userId,
    },
  };

  const queryResult = await dbClient.queryItems(params);

  const result:any = {};
  result['result'] = 'OK';

  // Merge results
  const mergeHelper = new ItemsMergeHelper();
  result['items'] = mergeHelper.groupAndMerge(queryResult);

  // Get calendar / reservation data
  result['items'] = await attachCalendarInfoToGroupedItems(result['items']);

  return result;
};

const getAllResultsByUserId = async (userId, surveyId) => {
  // Do a query by survey form ID and user ID
  const dbClient = new DDClient.Table(dynamoDbTable(), null, getEnvironmentCredentials());

  // Query params
  const params:any = {
    index: 'userId-surveyId-index',
    query: '#userId = :userIdValue and #surveyId = :surveyIdValue',
    filter: 'not contains(#c,:cdel) and not contains(#c,:ccan)',
    mapping: {
      '#userId': 'userId',
      '#surveyId': 'surveyId',
      '#c': 'check',
      ':userIdValue': userId,
      ':surveyIdValue': surveyId,
      ':cdel': '取り消し',
      ':ccan': 'キャンセル',
    },
  };

  const queryResult = await dbClient.queryItems(params);
  const mergeHelper = new ItemsMergeHelper();

  return {
    result: 'OK',
    items: mergeHelper.groupAndMerge(queryResult),
  };
};

const compartionPossibleStartDate = (date, _scheduleStartDate) => {
  const setDate = new Date(date);
  const scheduleStartDate = new Date(_scheduleStartDate);

  if (setDate > scheduleStartDate) {
    return setDate;
  } else {
    return scheduleStartDate;
  }
};

const getDayOffCount = (dayOffInfo, addDate) => {
  let dayOffCount = 0;
  const to = moment().add(addDate, "days").format("YYYYMMDD");
  for (const k in dayOffInfo) {
    const isDayOff = dayOffInfo[k] === 1;
    k < to && isDayOff && dayOffCount++;
  }
  return dayOffCount;
};

const getCalendarSchedules = async (from, to, categoryId) => {
  const schedules = await loadScheduleOfCategory(categoryId, from, to, {
    includeCalendarInfo: false,
  });
  return schedules;
};

const validateReservationPeriod = async (calendarInfo, categoryId) => {
  let reservationPossibleStartDate:any = null;
  let reservationPossibleEndDate:any = null;
  const controlType = calendarInfo.reservationControlType ? calendarInfo.reservationControlType : 0;
  if (controlType !== 0) {
    // 予約可能日設定：詳細設定
    // 予約可能開始日(reservationPossibleStart)が設定されている場合、営業日を絡めて開始日を算出
    if (calendarInfo.reservationPossibleStart || calendarInfo.reservationPossibleStart === 0) {
      const _from = moment().add(1, "days").format("YYYYMMDD");
      const addDays = 14 + calendarInfo.reservationPossibleStart;
      const _to = moment().add(addDays, "days").format("YYYYMMDD");
      const _scheduleData = await getCalendarSchedules(_from, _to, categoryId);

      let dayOffInfo = _scheduleData["dayOff"];
      if (dayOffInfo) {
        if (Object.keys(dayOffInfo).length === 0) {
          dayOffInfo = {};
          for (const key in _scheduleData["schedule"]) {
            dayOffInfo[key] = 0;
          }
        } else {
          for (const key in _scheduleData["schedule"]) {
            if (!(key in dayOffInfo)) {
              dayOffInfo[key] = 0;
            }
          }
        }
      }

      let setDate:any = null;
      const calcDays = calendarInfo.reservationPossibleStart;
      const dayOffCount = getDayOffCount(dayOffInfo, calcDays);
      const addedCalcDate = moment().add(calcDays, "days");
      for (const k in dayOffInfo) {
        // dayOffInfo[今日の日付 + calcDays] === 0(営業日の場合)：setDate = 今日の日付 + caldDays
        // dayOffInfo[今日の日付 + calcDays] === 1(休日の場合)：addedCalcDateの翌日で再度算出
        const copyiedDate = addedCalcDate.clone().format("YYYYMMDD");
        const isDayOff = dayOffInfo[copyiedDate] === 1;
        if (!isDayOff) {
          // 営業日の場合
          setDate = addedCalcDate.add(dayOffCount, "days").format("YYYY/MM/DD 00:00:00");
          break;
        } else {
          // 休日の場合
          addedCalcDate.add(1, "days");
        }
      }

      if (setDate) {
        // 日付の算出ができている場合
        if (calendarInfo.startDate) {
          // 予約制御が設定されている場合は日付を比較する
          reservationPossibleStartDate = compartionPossibleStartDate(setDate, calendarInfo.startDate);
        } else {
          // 予約制御が設定されていない場合は比較せずそのままセット
          reservationPossibleStartDate = new Date(setDate);
        }
      } else {
        // 日付の算出ができていない場合
        let wkDate = new Date();
        wkDate.setDate(wkDate.getDate() + calendarInfo.reservationPossibleStart);
        if (calendarInfo.startDate) {
          // 予約制御が設定されている場合は日付を比較する
          wkDate = compartionPossibleStartDate(wkDate, calendarInfo.startDate);
        }
        reservationPossibleStartDate = wkDate;
      }
    }

    // 予約可能終了日(reservationPossibleMonths)が設定されている場合、終了日を算出
    // 設定値が0の場合当月末まで、2の場合2か月後の月末まで
    // scheduleEndDateが設定されている場合はscheduleEndDateを参照
    if (calendarInfo.endDate) {
      // LINEのブラウザだと「yyyy-mm-dd」を日付に変換できない模様、yyyy/mm/ddに変換
      reservationPossibleEndDate = new Date(
        `${calendarInfo.endDate.replace(/-/g, "/")} 23:59:59`
      );
    } else if (
      calendarInfo.reservationPossibleMonths ||
      calendarInfo.reservationPossibleMonths === 0
    ) {
      // 予約可能制御日が未設定・かつ予約可能終了日(reservationPossibleMonths)が設定されている場合の終了日を算出
      // 設定値が0の場合当月末まで、2の場合2か月後の月末まで
      const nowDate = new Date();
      nowDate.setMonth(nowDate.getMonth() + (calendarInfo.reservationPossibleMonths + 1));
      const wkDate = new Date(`${nowDate.getFullYear()}/${("00" + (nowDate.getMonth() + 1)).slice(-2)}/01`);
      wkDate.setDate(wkDate.getDate() - 1);
      reservationPossibleEndDate = wkDate;
    }
  }
  return { reservationPossibleStartDate, reservationPossibleEndDate, controlType };
};

const setReservationPeriod = async (calendarInfo, yoyakuDate, currentCategoryKey, categoryId, checkCanceledRecord) => {
  const { reservationPossibleStartDate, reservationPossibleEndDate, controlType } = await validateReservationPeriod(calendarInfo, categoryId);
  let chkFrom:any = null;
  let chkTo:any = null;
  const from = reservationPossibleStartDate || new Date('2000/01/01');
  const to = reservationPossibleEndDate || new Date('2099/12/31');
  let dayCheckOnly = true;

  if (controlType !== 0) {
    dayCheckOnly = false;
    chkFrom = moment(from).format('YYYYMMDD');
    chkTo = moment(to).format('YYYYMMDD');

    // 選択した日付を変更するチェック、ステータスチェック
    if (currentCategoryKey) {
      const wkKey = currentCategoryKey.split("|"); // category#039070_2|20210415|5
      if (wkKey.length >= 2) {
        if (Number(chkFrom) <= Number(wkKey[1]) && Number(chkTo) >= Number(wkKey[1])) {
          dayCheckOnly = true;
        }
        if (checkCanceledRecord) {
          dayCheckOnly = false;
        }
      }
    }
    // 選択した日付が現在の予約可能期間内の日付の場合は当日の予約数チェックのみ
    if (!(Number(chkFrom) <= Number(yoyakuDate) && Number(chkTo) >= Number(yoyakuDate))) {
      dayCheckOnly = true;
    }
  }

  return { chkFrom, chkTo, dayCheckOnly };
};

const checkReservationPeriod = async (data, configs: SurveyConfigModel, calendar: CalendarModel) => {
  // if no userID, ignore
  if (!data.userId &&
    data.surveyConfig &&
    (!data.surveyConfig.memberFormId || data.surveyConfig.memberFormId === '')) {
    return false;
  }
  // get calendar infor
  const reservationItem = configs.getSchemaItemsByType('reservation')[0];
  const reservation = data.surveyResults.find((obj) => obj.itemKey === reservationItem.itemKey);
  const yoyakuDate = reservation.value.split('|')[1];
  const currentCategoryKey = (data.bunrui_id && data.bunrui_id.old) ? data.bunrui_id.old : null;
  // get reservationMaxCount, reservationMaxCountOfDay
  const calendarInfo = calendar.getCalendarInfo();
  // set reservation period
  const { chkFrom, chkTo, dayCheckOnly } = await setReservationPeriod(calendarInfo, yoyakuDate, currentCategoryKey, reservation.value.split('_')[0], data.changeStatus);

  // querry all survey results by userID
  const items = await getReservationData(data.surveyId, reservationItem.itemKey, data.userId ? data.userId : data.surveyResults[0].userId);

  // validate
  let dayCount = 1; // this result
  let allCount = 1; // this result
  for (const item of items) {
    if (item.partitionKey !== data.partitionKey && item.value && item.value.split('|')[1]) {
      if (!dayCheckOnly && Number(chkFrom) <= Number(item.value.split('|')[1]) && Number(chkTo) >= Number(item.value.split('|')[1])) {
        allCount += 1;
      }
      if (item.value.split('|')[1] === yoyakuDate) {
        dayCount += 1;
      }
    }
  }
  // check maximum resered all time
  if (
    calendarInfo.reservationMaxCount &&
    calendarInfo.reservationMaxCount !== 0 &&
    allCount > calendarInfo.reservationMaxCount
  ) {
    return true;
  }
  // check maximum reserd by one day
  if (
    calendarInfo.reservationMaxCountOfDay &&
    calendarInfo.reservationMaxCountOfDay !== 0 &&
    dayCount > calendarInfo.reservationMaxCountOfDay
  ) {
    return true;
  }
  return false;
};

const getTitleSettingBySurveyAndUser = async (surveyId, userId) => {
  // Do a query by survey form ID and user ID
  const dbClient = new DDClient.Table(displaySettingTable(), null, getEnvironmentCredentials());

  // Query params
  const params:any = {
    query: '#partitionKey = :pk',
    mapping: {
      '#partitionKey': 'partitionKey',
      ':pk': `${surveyId}#${userId}`,
    },
  };

  const queryResult = await dbClient.queryItems(params);

  return {
    result: 'OK',
    setting: queryResult.length > 0 ? queryResult[0].setting : {},
  };
};

const updateTitleSettingBySurveyAndUser = async (surveyId, userId, setting) => {
  // Do a query by survey form ID and user ID
  const dbClient = new DDClient.Table(displaySettingTable(), null, getEnvironmentCredentials());

  // check existed record
  const params:any = {
    query: '#partitionKey = :pk',
    mapping: {
      '#partitionKey': 'partitionKey',
      ':pk': `${surveyId}#${userId}`,
    },
  };
  try {
    const nowTime = moment().unix();
    const queryResult = await dbClient.queryItems(params);

    if (queryResult.length > 0) {
      // Update
      await dbClient.update({
        partitionKey: `${surveyId}#${userId}`,
        setting: setting,
        updatedAt: nowTime,
      });
    } else {
      // Create
      await dbClient.putItem({
        partitionKey: `${surveyId}#${userId}`,
        setting: setting,
        createdAt: nowTime,
        updatedAt: nowTime,
      });
    }

    return {
      result: 'OK',
      message: '表示設定情報を保存しました。',
    };
  } catch (error: any) {
    console.log('[ERROR]', error);
  }
};

const checkReservableAge = async (
  surveyConfig: SurveyConfigModel,
  surveyResults: { [key: string]: string }[],
  calendar: CalendarModel,
  reservationDate: DateModel,
) => {
  const reservableAge = calendar.getReservableAge();
  if (!reservableAge || reservableAge.controlTypeIs('allAge')) {
    return null;
  }
  const birthdayItems = surveyConfig.getSchemaItemsByType('birthday')
  if (birthdayItems.length === 0) {
    return '帳票に生年月日アイテムが存在しないため、予約可能年齢制限を行うことができません。'
  } else if (birthdayItems.length >= 2) {
    return '帳票に生年月日アイテムが2つ以上存在するため、予約可能年齢制限を行うことができません。';
  }
  const birthdayAnswer = surveyResults.find(answer => answer.itemKey === birthdayItems[0].itemKey);
  if (!birthdayAnswer?.value) {
    return '生年月日アイテムの回答が未入力のため、予約可能年齢制限を行うことができません。';
  }
  const birthday = new BirthdayModel(birthdayAnswer.value, 'yyyy-MM-dd');
  return reservableAge.isReservableAge(birthday, reservationDate) ? null : '予約可能な年齢ではありません。';
}

export {
  create,
  getAll,
  get,
  getByDbPatterns,
  getWithDatabaseFilter,
  getSearchedAndSortedList,
  getByPartitionKey,
  update,
  updateItemsEntity,
  createCSV,
  createCSVAppending,
  createInsertDataForUpdateOptions,
  updateOptions,
  deleteSurveyResult,
  deleteSingleSurveyResult,
  deleteSurveyResults,
  resetUserId,
  getSurveyConfig,
  deleteSurveyResultOfPreviousVersion,
  getCategory,
  getOneItemByExistsAnswerCode,
  updateCounter,
  tryPlaceReservationIfPresents,
  updateReservationIfPresents,
  cancelReservation,
  getImportCsvAppendingPresignedUrl,
  asyncImportCsvAppendingInvoker,
  batchPutSurveyResult,
  attachCalendarInfo,
  getReservationData,
  runValidationAndTransformationOnCsvFile,
  checkImportStatusAndCallAppendingInvoker,
  findIndexSearcheableField,
  indexSearch,
  fetchUsingSurveyIdSortKeyIndexWithMultipleSortKeys,
  checkReservationPeriod,
  getResultsByFormAndUserId,
  getTitleSettingBySurveyAndUser,
  updateTitleSettingBySurveyAndUser,
  getReservationItem,
  checkReservableAge,
};
