/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import c from '../../common/admin-api/config/constants.js';
import config from '../../common/admin-api/config/static.js';

import { queryList, queryRecent } from '../../common/admin-api/utils/db.js';
import {DistributionHistoryRecord} from '../types/survey-results.js';

const dynamoDbTable = () => {
  return config.get(c.TABLE_SURVEY_RESULTS_HISTORY);
};
function distributionHistoryTable() {
  return config.get(c.TABLE_DISTRIBUTION_HISTORY);
}

const getAllByPartitionKey = async (partitionKey) => {
  const params = {
    TableName: dynamoDbTable(),
    KeyConditionExpression: 'surveyResultId = :pkey',
    ExpressionAttributeValues: {
      ':pkey': { S: partitionKey },
    },
  };
  return await queryList(params);
};

const getRecentByPartitionKey = async (partitionKey: string, recentCount = c.SURVEY_RESULTS_HISTORY.recentFetchCountLimit) => {
  const params = {
    TableName: dynamoDbTable(),
    KeyConditionExpression: 'surveyResultId = :pkey',
    ExpressionAttributeValues: {
      ':pkey': { S: partitionKey },
    },
  };
  return await queryRecent(params, recentCount);
}

const getDistributionHistory = async (targetName: string, partitionKey: string) => {
  const table = distributionHistoryTable();
  const records: DistributionHistoryRecord[] = await queryList({
    TableName: table,
    KeyConditionExpression: 'partitionKey = :pk',
    FilterExpression: 'contains(surveyResultsKeys, :answerKey)',
    ExpressionAttributeValues: {
      ':pk': {S: `delivery#target#${targetName}`},
      ':answerKey': {S: partitionKey }
    },
    ScanIndexForward: false,
  });
  return records;
}

export {
  getAllByPartitionKey,
  getRecentByPartitionKey,
  getDistributionHistory
};
