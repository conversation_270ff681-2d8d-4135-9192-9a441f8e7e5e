/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import _ from 'lodash';
import moment from 'moment';

class SurveyResultsValidator {
  private schema: any;
  private schemaByGroupheader: any[];
  private schemaByConditions: any[];
  private errorMessage: any;
  constructor(surveyConfigs, surveyResults) {
    this.schema = surveyConfigs.surveySchema;
    this.schemaByGroupheader = [];
    this.schemaByConditions = [];
    this.errorMessage = {};

    this.putValueIntoSchema(surveyResults);

    this.generateSchemaConditionsFormat();
  }

  putValueIntoSchema(surveyResults) {
    const inputs = {};
    for (let i = 0; i < surveyResults.length; i++) {
      const input = surveyResults[i];
      const itemKey = input.itemKey;

      if (!(itemKey in inputs)) {
        inputs[itemKey] = input.value;
      } else {
        // if multi answer
        if (!Array.isArray(inputs[itemKey])) {
          inputs[itemKey] = [inputs[itemKey]];
        }
        inputs[itemKey].push(input.value);
      }
    }

    for (let i = 0; i < this.schema.length; i++) {
      const key = this.schema[i].itemKey;
      if (key in inputs) {
        let input = inputs[key];
        if (Array.isArray(input)) {
          input = input.filter((val) => val);
        }
        this.schema[i].input = input;
      }
    }
  }

  isValid() {
    return Object.keys(this.errorMessage).length === 0;
  }

  hasErrorByKey(key) {
    return Object.keys(this.errorMessage).includes(key);
  }

  getErrorMessage() {
    return this.errorMessage;
  }

  getErrorMessageAsString() {
    let result = '';
    const keys = Object.keys(this.errorMessage);
    for (let x = 0; x < keys.length; x++) {
      const key = keys[x];
      result += this.errorMessage[key];
      if (x !== keys.length - 1) {
        result += '、';
      }
    }
    return result;
  }

  putErrorMessage(key, message) {
    if (!(key in this.errorMessage)) {
      this.errorMessage[key] = [];
    }

    this.errorMessage[key].push(message);
  }

  validate() {
    return this.validateByConditions();
  }

  validateByConditions() {
    this.schemaByConditions.forEach((item) => {
      // commom validation
      this.validateCommon(item);

      // if common validation error, go next item
      if (this.hasErrorByKey(item.itemKey)) {
        return false;
      }

      {
        switch (item.type) {
          case 'groupheader':
            if (item.items && Array.isArray(item.items)) {
              item.items.forEach((subItem) => {
                // commom validation
                this.validateCommon(subItem);

                // if common validation error, go next item
                if (!this.hasErrorByKey(subItem.itemKey)) {
                  this.validateItem(subItem);
                }
              });
            }
            break;
          default:
            this.validateItem(item);
            break;
        }
      }
    });

    return this.isValid();
  }

  validateItem(item) {
    // each type validation
    switch (item.type) {
      case 'text':
        this.validateText(item);
        break;
      case 'checkboxes':
        this.validateCheckboxes(item);
        break;
      case 'dropdown':
        this.validateDropdown(item);
        break;
      case 'date':
        this.validateDate(item);
        break;
      case 'email':
        this.validateEmail(item);
        break;
      case 'phone':
        this.validatePhone(item);
        break;
      case 'number':
        this.validateNumber(item);
        break;
      case 'textarea':
        this.validateTextarea(item);
        break;
      case 'radio':
        this.validateRadio(item);
        break;
      case 'choicegroupheader':
        this.validateChoiceGroupheader(item);
        break;
      case 'postcode':
        this.validatePostcode(item);
        break;

      default:
        break;
    }
  }

  generateSchemaConditionsFormat() {
    const schemaOriginal = [...this.schema];
    let groupheaderItems:any = null;
    const choiceGroupheaderItems = {};

    schemaOriginal.forEach((item) => {
      // schemaByGroupheader
      if (item.type === 'groupheader') {
        this.schemaByGroupheader.push(item);

        if (groupheaderItems === null || groupheaderItems.length > 0) {
          groupheaderItems = [];

          item.items = groupheaderItems;
        }
      } else {
        // choiceGroupheaderItems
        if (item.type === 'choicegroupheader') {
          choiceGroupheaderItems[item.itemKey] = item;
        }

        if (groupheaderItems !== null && Array.isArray(groupheaderItems)) {
          groupheaderItems.push(item);
        } else {
          this.schemaByGroupheader.push(item);
        }
      }
    });

    const choiceGroupheaderArray = Object.values(choiceGroupheaderItems);

    this.schemaByGroupheader.forEach((item) => {
      if (item.type === 'groupheader') {
        let allowedAddingToConditionsFlag = false;

        choiceGroupheaderArray.forEach((choiceGroupheaderItem:any) => {
          if (choiceGroupheaderItem['input']) {
            const selectedGroupheaderValue = choiceGroupheaderItem.input;
            choiceGroupheaderItem.sectionOptions.forEach((sectionOption) => {
              if (selectedGroupheaderValue === sectionOption.option.value) {
                if (sectionOption.groupheader.value === item.title) {
                  allowedAddingToConditionsFlag = true;
                  return;
                }
              }
            });
          } else {
            allowedAddingToConditionsFlag = true;
            return;
          }
        });

        if (allowedAddingToConditionsFlag) {
          this.schemaByConditions.push(item);
        }
      } else {
        this.schemaByConditions.push(item);
      }
    });

    return this.schemaByConditions;
  }

  isInputEmpty(item) {
    if (!('input' in item) || item.input === '' || (Array.isArray(item.input) && item.input.length === 0)) {
      return true;
    }

    return false;
  }

  // common validatoin

  validateCommon(item) {
    // require
    this.validateRequired(item);
  }

  // each type validation

  validateText(item) {
    // length
    this.validateLength(item.itemKey, item.input, item.length);
  }

  validateEmail(item) {
    // format
    this.validateEmailString(item.itemKey, item.input, item.format);
  }

  validatePhone(item) {
    // phone string
    this.validatePhoneString(item.itemKey, item.input, item.format);
  }

  validateCheckboxes(item) {
    // options
    this.validateOptions(item.itemKey, item.input, item.options);
    // selection
    this.validateSelectionCount(item.itemKey, item.input, item.selection);
  }

  validateDropdown(item) {
    // options
    if (item.input) {
      this.validateOptions(item.itemKey, item.input, item.options);
    }
  }

  validateDate(item) {
    // date
    const format = item.includeYear ? 'YYYY-MM-DD' : 'MM-DD';
    this.validateDateString(item.itemKey, item.input, format);
  }

  validateNumber(item) {
    // number
    this.isNumber(item.itemKey, item.input);

    // range
    this.validateNumRange(item.itemKey, item.input, item.range);
  }

  validateTextarea(item) {
    // length
    this.validateLength(item.itemKey, item.input, item.length);
  }

  validateRadio(item) {
    // options
    this.validateOptions(item.itemKey, item.input, item.options);
  }

  validateChoiceGroupheader(item) {
    this.validateSectionOptions(item);
  }

  validatePostcode(item) {
    this.validatePostcodeString(item.itemKey, item.input);
  }

  // validation detail

  validateRequired(item) {
    let isRequired = false;
    if ('admin' in item.isRequired) {
      isRequired = item.isRequired.admin;
    } else {
      isRequired = item.isRequired.value;
    }

    if (isRequired) {
      if (this.isInputEmpty(item)) {
        this.putErrorMessage(item.itemKey, '必須項目です');
      }
    }
  }

  validateLength(key, input, length) {
    if (!input || !length) {
      return;
    }

    const min = parseInt(length.min);
    const max = parseInt(length.max);
    let localErrorMessage = '';
    if (min && input.length < min) {
      localErrorMessage += `${min}文字以上`;
    }
    if (max && input.length > max) {
      localErrorMessage += `${max}文字以内`;
    }
    if (localErrorMessage !== '') {
      localErrorMessage += 'で入力してください';
      this.putErrorMessage(key, localErrorMessage);
    }
  }

  validateEmailString(key, input, format) {
    if (!input) {
      return;
    }

    // from https://stackoverflow.com/questions/46155/how-to-validate-an-email-address-in-javascript
    const re =
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if (!re.test(String(input).toLowerCase())) {
      this.putErrorMessage(key, '正しい形式で入力してください');
    }
  }

  validatePhoneString(key, input, format) {
    if (!input) {
      return;
    }

    // const re = /^(0[5-9]0[0-9]{8}|0[1-9][1-9][0-9]{7})$/;
    const re = /^([0-9]{1,15})$/;
    if (!re.test(String(input))) {
      this.putErrorMessage(key, '正しい形式で入力してください');
    }
  }

  validatePostcodeString(key, input) {
    if (!input) {
      return;
    }

    const pattern = /^[0-9]{7}$/;
    if (!pattern.test(input)) {
      this.putErrorMessage(key, '正しい郵便番号を入力してください。');
      return;
    }
  }

  isNumber(key, input, errorMessage?) {
    if (!input) {
      return;
    }

    if (Number.isNaN(Number(input))) {
      this.putErrorMessage(key, errorMessage || '正しい形式で入力してください');
    }
  }

  validateNumRange(key, input, range) {
    if (!input || !range) {
      return;
    }

    const inputInt = parseInt(input);

    const min = parseInt(range.min);
    const max = parseInt(range.max);
    if (min && !max) {
      if (inputInt < min) {
        this.putErrorMessage(key, `${min}以上で入力してください`);
      }
    } else if (!min && max) {
      if (max < inputInt) {
        this.putErrorMessage(key, `${max}以下で入力してください`);
      }
    } else {
      if (inputInt < min || max < inputInt) {
        this.putErrorMessage(key, `${min}〜${max}の範囲で入力してください`);
      }
    }
  }

  validateSelectionCount(key, input, selection) {
    if (!input || !selection || this.isInputEmpty({ input: input })) {
      // [] is not falsy
      return;
    }

    const min = parseInt(selection.min);
    const max = parseInt(selection.max);
    const selectionCount = input.length;

    if (min && !max) {
      if (selectionCount < min) {
        this.putErrorMessage(key, `${min}個以上選択してください`);
      }
    } else if (!min && max) {
      if (max < selectionCount) {
        this.putErrorMessage(key, `${max}個以下で選択してください`);
      }
    } else if (min && max) {
      if (selectionCount < min || max < selectionCount) {
        this.putErrorMessage(key, `${min}〜${max}個選択してください`);
      }
    }
  }

  validateOptions(key, input, options) {
    if (!input || this.isInputEmpty({ input: input })) {
      // [] is not falsy
      return;
    }

    if (_.isString(input)) {
      input = [input];
    }
    for (let i = 0; i < input.length; i++) {
      if (!options.includes(input[i])) {
        this.putErrorMessage(key, '不正なデータです');
        return;
      }
    }
  }

  validateSectionOptions(choiceGroupheaderItem, errorMessage = null) {
    if (!choiceGroupheaderItem.input) {
      return;
    }

    let isValidated = false;
    choiceGroupheaderItem.sectionOptions.forEach((sectionOption) => {
      if (choiceGroupheaderItem.input === sectionOption.option.value) {
        isValidated = true;
        return;
      }
    });

    if (!isValidated) {
      this.putErrorMessage(
        choiceGroupheaderItem.key,
        errorMessage ||
          '不正なデータ：「' + choiceGroupheaderItem.title + '」の入力データが選択一覧に存在していない状態です。'
      );
    }
  }

  validateDateString(key, input, format) {
    if (!input) {
      return;
    }

    if (!moment(input, format).isValid()) {
      this.putErrorMessage(key, '正しい形式で入力してください');
    }
  }
}

export {
  SurveyResultsValidator,
};
