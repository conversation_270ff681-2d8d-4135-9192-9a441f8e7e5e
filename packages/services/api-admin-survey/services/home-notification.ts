/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import c from '../../common/admin-api/config/constants.js';
import config from '../../common/admin-api/config/static.js';
import { dynamoDbDocumentClient } from '../../common/utils/aws-clients.js';

const tableName = () => {
  return config.get(c.DATABASE_TABLE);
};

const getHomeNotificationData = async (surveyId) => {
  const client = dynamoDbDocumentClient();

  const icaPk = `latestImportCsvAppending#${surveyId}`;
  let ica:any = {};
  try {
    ica = await client
      .get({
        TableName: tableName(),
        Key: { partitionKey: icaPk },
      })
      .promise();

    ica = ica.Item || {};
    delete ica.partitionKey;
  } catch (error: any) {
    return {
      result: 'ERROR',
      errorMessage: error,
    };
  }

  const ecaPk = `latestExportCsvAppending#${surveyId}`;
  let eca:any = {};
  try {
    eca = await client
      .get({
        TableName: tableName(),
        Key: { partitionKey: ecaPk },
      })
      .promise();

    eca = eca.Item || {};
    delete eca.partitionKey;
  } catch (error: any) {
    return {
      result: 'ERROR',
      errorMessage: error,
    };
  }

  return {
    result: 'SUCCESS',
    data: {
      importCsvAppendingStatus: ica,
      exportCsvAppendingStatus: eca,
    },
  };
};

export {
  getHomeNotificationData,
};
