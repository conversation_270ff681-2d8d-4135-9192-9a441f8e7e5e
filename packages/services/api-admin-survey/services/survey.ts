/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */


import aws from 'aws-sdk';
import c from '../../common/admin-api/config/constants.js';
import { DDClient } from 'common/utils/index.js';
import config from '../../common/admin-api/config/static.js';
import * as logger from '../../common/admin-api/utils/logger.js';
import { getEnvironmentCredentials } from '../../common/admin-api/utils/aws-helper.js';
import { generateUuid } from '../../common/admin-api/utils/util.js';
import * as calendarCategoriesService from './calendars-categories.js';
import _ from 'lodash';

import { getOne, UpdateExpressionBuilder, updateOne, createOne, scanList } from '../../common/admin-api/utils/db.js';
import { getSurveyConfigById, dynamoDbTableSurveyConfig, updateMemberConfigOnSurveyConfigUpdate } from '../../platform/services/survey-member-common.js';
import {CategoryPermissionsRecord, SurveyConfig, SelectPayTypeFieldType} from '../types/survey-config.js';

const retryMax = 1;

const getSurveyResults = () => {
  return config.get(c.TABLE_SURVEY_RESULTS);
};


const endpointUrlBase = () => {
  return config.get(c.VUE_APP_LIFF_WEB_URL)
    ? config.get(c.VUE_APP_LIFF_WEB_URL) + '/'
    : 'https://liff.' + config.get(c.DEPLOY_ENV) + '.line-smartcity.com/';
};

const isString = (input) => {
  return typeof input == 'string' || input instanceof String;
};

const isArray = (input) => {
  return input instanceof Array;
};

const hasOptionByType = (type) => {
  return type === 'checkboxes' || type === 'dropdown' || type === 'radio';
};

const inputValidation = (input) => {
  // title validation
  if (!isString(input.surveyTitle)) {
    return false;
  } else if (input.surveyTitle.length <= 0) {
    return false;
  }

  // status validation
  if (input.surveyStatus !== 'enable' && input.surveyStatus !== 'disable') {
    return false;
  }

  // schema validation
  if (!isArray(input.surveySchema)) {
    return false;
  }
  for (const item of input.surveySchema) {
    switch (item.type) {
      case 'postcode':
        if (item.default && !validatePostcodeString(item.default)) return false;
        break;
      case 'phone':
        if (item.default && !validatePhoneString(item.default)) return false;
        break;
      case 'email':
        if (item.default && !validateEmailString(item.default)) return false;
        break;
    }
  }
  const filesSchema = input.surveySchema.find(item => item.type === 'files');
  if (filesSchema) {
    // ファイルアップロードの最大数を10個に制限
    if (!filesSchema.maxItems || filesSchema.maxItems <= 0 || filesSchema.maxItems > 10) {
      return false;
    }
  }

  const indexCount = input.surveySchema.filter(item => item.isIndexable.value).length;
  if (indexCount > 2) {
    return false;
  }

  if (!validateSelectPayTypeQuestion(input.surveySchema, !!input.usePayment)) {
    return false;
  }

  return true;
};


const validateSelectPayTypeQuestion = (surveySchema: SurveyConfig['surveySchema'], usePayment: boolean): boolean => {
  const selectPayTypeItems = surveySchema.filter(item => item.type === 'selectPayType') as SelectPayTypeFieldType[];
  const reservationItem = surveySchema.find(item => item.type === 'reservation');
  const selectProductsItem = surveySchema.find(item => item.type === 'selectProducts');
  if (selectPayTypeItems.length === 0) {
    return true;
  }
  if (!usePayment) {
    return false;
  }
  if (!selectProductsItem && !reservationItem) {
    return false;
  }
  if (selectPayTypeItems.length > 1) {
    return false;
  }
  const hasEmptyOption = selectPayTypeItems[0].payTypeOptions.some(item => !item.input);
  if (hasEmptyOption) {
    return false;
  }
  return true;
}

const getUpdateOptions = (input, latestData) => {
  const latestSchema = latestData.surveySchema;
  const updateOptions = {};
  let isValid = true;

  input.surveySchema.map((question) => {
    // only questions that have option
    if (!question.options) {
      return;
    }

    const tmp:any[] = [];
    const latestQuestion = latestSchema.find((q) => q.itemKey === question.itemKey);

    if (
      latestQuestion !== undefined &&
      latestQuestion &&
      latestQuestion.options !== undefined &&
      latestQuestion.options &&
      latestQuestion.options.length > 0
    ) {
      if (
        latestQuestion.type === 'sesshuVaccineMaker' ||
        (question.attributes && question.attributes.allowedEditElements === true) // radio, special item "1回目の接種ワクチンのメーカー"
      ) {
        return { updateOptions, isValid };
      }

      latestQuestion.options.map((oldVal, i) => {
        const newVal = question.options[i];
        if (oldVal !== newVal) {
          if (updateOptions[question.itemKey]) {
            isValid = false;
          }
          updateOptions[question.itemKey] = {
            from: oldVal,
            to: newVal,
          };
        }
      });
    }
  });
  return { updateOptions, isValid };
};

const loadPermissions = async (surveyId) => {
  const client = new DDClient.Table(
    config.get(c.DATABASE_TABLE),
    { pkField: 'partitionKey' },
    getEnvironmentCredentials()
  );
  const result: CategoryPermissionsRecord = await client.getItem({ pk: `surveyPermissions#${surveyId}` });
  if (result) {
    return result.permissions;
  }
  return null;
};

const savePermissions = async (surveyId, permissionsArray) => {
  const client = new DDClient.Table(
    config.get(c.DATABASE_TABLE),
    { pkField: 'partitionKey' },
    getEnvironmentCredentials()
  );
  await client.putItem({ permissions: permissionsArray }, { pk: `surveyPermissions#${surveyId}` });
};

const tryCreate = async (surveyConfigObj, params, retry) => {
  try {
    await createOne(params);
  } catch (err: any) {
    if (err.code === 'ConditionalCheckFailedException') {
      // when surveyId is already exists
      if (retry >= retryMax) {
        logger.appLogInfo('[Error]: Cannot be resolved surveyId duplication');
        throw err;
      }
      await tryCreate(surveyConfigObj, params, retry + 1);
    } else {
      throw err;
    }
  }

  return surveyConfigObj;
};

const create = async (username, input, retry = 0) => {
  const currentUnixtimeSec = Math.floor(new Date().getTime() / 1000);
  const tableName = dynamoDbTableSurveyConfig();
  const surveyId = generateUuid();
  const endpointURL = endpointUrlBase();

  const surveyConfigObj = {
    surveyId: surveyId,
    surveyTitle: input.surveyTitle,
    surveyType: input.surveyType,
    description: input.description,
    isAppending: input.isAppending,
    isSearchable: input.isSearchable,
    isEditDisabled: input.isEditDisabled,
    isDeleteAllEnabled: input.isDeleteAllEnabled,
    endOfSurveyMessage: input.endOfSurveyMessage,
    endOfSurveyMessageType: input.endOfSurveyMessageType,
    deliveryMessageSetting: input.deliveryMessageSetting,
    endpointURL: endpointURL,
    surveyStatus: input.surveyStatus,
    surveySchema: input.surveySchema,
    surveyTeams: input.surveyTeams,
    categoriesPermissions: input.categoriesPermissions,
    createdAt: currentUnixtimeSec,
    createdBy: username,
    updatedAt: currentUnixtimeSec,
    updatedBy: username,
    memberFormId: input.memberFormId ? input.memberFormId : '',
    vaccinationIntervalType: input.vaccinationIntervalType,
    doesCheckSameVaccineMaker: input.doesCheckSameVaccineMaker,
    headerImageUrl: input.headerImageUrl ? input.headerImageUrl : '',
    usePayment: input?.usePayment || 0
  };

  const marshalledItem = aws.DynamoDB.Converter.marshall(surveyConfigObj);

  const params = {
    TableName: tableName,
    Item: marshalledItem,
  };

  const result = await tryCreate(surveyConfigObj, params, retry);

  if (input.categoriesPermissions) {
    await savePermissions(surveyId, input.categoriesPermissions);
  }

  //populate categoriesTree for newly created survey schemas
  let categoriesTree:any = null;
  let selectProductsItem:any = null;
  for (const item of result.surveySchema) {
    // isNewItem is used in frontend only, just to check new item or not, for edit mode.
    // delete in backend, to avoid exception return on frontend, then flag (isNewItem) was changed.
    delete item.isNewItem;
    
    switch(item.type) {
      case 'reservation': {
        if (categoriesTree == null) {
          categoriesTree = await calendarCategoriesService.loadCategoriesTree({
            includeDisplaySettings: true,
          });
        }
        item.categories_tree = categoriesTree;
        break;
      }
      default: {
        continue;
      }
    }
  }


  return result;
};
// For additionType survey(追加型）add counter record in survey_result
const createSurveyResult = async (input, retry = 0) => {
  const tableName = getSurveyResults();

  const marshalledItem = aws.DynamoDB.Converter.marshall(input);
  const params = {
    TableName: tableName,
    Item: marshalledItem,
  };
  return await tryCreate(input, params, retry);
};

const recordToObj = (obj) => {
  if (!('categoriesPermissions' in obj)) {
    obj.categoriesPermissions = null;
  }
  if (!('endOfSurveyMessageType' in obj)) {
    obj.endOfSurveyMessageType = null;
  }
  if (!('deliveryMessageSetting' in obj)) {
    obj.deliveryMessageSetting = [];
  }
  return {
    surveyId: obj.surveyId,
    surveyTitle: obj.surveyTitle,
    surveyType: obj.surveyType ? obj.surveyType : 'normal',
    description: obj.description,
    isAppending: obj.isAppending,
    isSearchable: obj.isSearchable,
    isEditDisabled: obj.isEditDisabled,
    isDeleteAllEnabled: obj.isDeleteAllEnabled,
    endOfSurveyMessage: obj.endOfSurveyMessage,
    endpointURL: obj.endpointURL,
    surveyStatus: obj.surveyStatus,
    surveySchema: obj.surveySchema,
    surveyTeams: obj.surveyTeams,
    categoriesPermissions: obj.categoriesPermissions,
    endOfSurveyMessageType: obj.endOfSurveyMessageType,
    deliveryMessageSetting: obj.deliveryMessageSetting,
    createdAt: obj.createdAt,
    createdBy: obj.createdBy,
    updatedAt: obj.updatedAt,
    updatedBy: obj.updatedBy,
    memberFormId: obj.memberFormId ? obj.memberFormId : '',
    usePayment: obj.usePayment || 0,
  };
};

const LIST_ATTRIBUTES = ['surveyTitle', 'surveyId', 'surveyStatus', 'isAppending'];

const filterAttributes = (config, mode, listAttributes = LIST_ATTRIBUTES) => {
  if (mode === 'table') {
    return _.omit(config, 'surveySchema');
  }
  if (['list', 'allPermissions'].includes(mode)) {
    return _.pick(config, listAttributes);
  }
  return config;
};

const attachReservationAttributes = async (configs) => {
  const categories_tree_result = await calendarCategoriesService.loadCategoriesTree({
    includeDisplaySettings: true,
  });
  configs.forEach(function (element) {
    element.categoriesPermissions = [{ list: [], categoryLarge: '' }];
    if (element.surveySchema) {
      const schema = element.surveySchema;
      for (const surveyItemIndex in schema) {
        const surveyItem = schema[surveyItemIndex];
        if (surveyItem.type === 'reservation') {
          surveyItem.categories_tree = { ...categories_tree_result };
        }
      }
    }
  });
};

const formatUserTeams = (userGroup) => {
  return userGroup
    .split(',')
    .map(function (element) {
      let elTeam = '';
      if (element.includes(':') > 0) {
        // this might be 'Administrator' or auto-generated-teamId
        elTeam = element.split(':')[0];
      } else {
        // this can only be one of 'admins' 'members' 'guests' 'operators' (or even 'viewers')
        elTeam = element;
      }
      return elTeam;
    })
    .filter((team) => team.length > 0);
};

const filterByUserTeams = (configs, userTeams) => {
  const hasAdministrator = userTeams.some((team) => team === 'Administrator');
  const hasTeamId = userTeams.length > 0;
  // If user is Administrator OR does not have team id -> can access all survey
  if (hasAdministrator || !hasTeamId) {
    return configs;
  }

  return configs.filter(element => {
    // if the teamIds that can access this survey were specified (at least one) AND it's not only 'Administrator'
    if (
      element.surveyTeams &&
      element.surveyTeams.length > 0 &&
      !(element.surveyTeams.length === 1 && element.surveyTeams[0].teamId === 'Administrator')
    ) {
      const surveyTeamsArr = element.surveyTeams.map((x) => x.teamId);
      const overlapTeams = userTeams.filter((value) => surveyTeamsArr.includes(value));
      return overlapTeams.length > 0;
    }

    // if there is no surveyTeams settings, then anyone can access this survey
    return true;
  });
}

const getAll = async (userGroup, lastEvaluatedKey, mode = 'all') => {
  const tableName = dynamoDbTableSurveyConfig();
  const dbClient = new DDClient.Table(tableName, null, getEnvironmentCredentials());
  const briefMode = mode === 'list' || mode === 'table';
  const queryArgs:any = {
    limit: briefMode ? undefined : 50,
    index: briefMode ? 'surveyId-ligtweight-index' : undefined,
    lastEvaluatedKey: lastEvaluatedKey,
  };

  const scanResult = await dbClient.scanPage(queryArgs);

  // not show item that have been delete in 帳票作成ページ
  const displayItem = scanResult.items.filter(function (obj) {
    return obj.isDisplay === undefined || obj.isDisplay === true;
  });
  let result = displayItem.map((e) => recordToObj(e));
  if (mode !== 'all') {
    result.sort((a, b) => b.updatedAt - a.updatedAt);
  }

  if (mode === 'all') {
    await attachReservationAttributes(result);
  }

  // If mode is 'allPermissions' -> can access all permissions
  if (mode === 'allPermissions') {
    const listAttributes = ['surveyTeams', 'categoriesPermissions', 'updatedAt', 'surveyId', 'surveySchema'];
    return {
      items: result.map((c) => filterAttributes(c, mode, listAttributes)),
      lastEvaluatedKey: scanResult.lastEvaluatedKey,
    };
  }

  // Get user's teams
  const userTeamArr = formatUserTeams(userGroup);

  // Filter survey by teams
  result = filterByUserTeams(result, userTeamArr);

  return {
    items: result.map((c) => filterAttributes(c, mode)),
    lastEvaluatedKey: scanResult.lastEvaluatedKey,
  };
};

const getBySurveyId = async (surveyId, consistentRead = false): Promise<SurveyConfig> => {
  return await getSurveyConfigById(surveyId, consistentRead);
};

const getBySurveyIdAndGroup = async (userGroup, surveyId) => {
  const params = {
    TableName: dynamoDbTableSurveyConfig(),
    Key: {
      surveyId: { S: surveyId },
    },
  };
  // return await getOne(params);
  const survey = await getOne(params);
  const surveyObj = recordToObj(survey);

  // Get user's teams
  let hasTeamId = false;
  const elTeam = {};
  for (const element of userGroup.split(',')) {
    if (element.includes(':')) {
      elTeam[element.split(':')[0]] = element.split(':')[1];

      if (element.split(':')[0] !== 'Administrator') {
        hasTeamId = true;
      }
      // アドミン権限場合、スキップ
      if (element.split(':')[0] === 'Administrator') {
        hasTeamId = false;
        break;
      }
    } else {
      elTeam[element.split(':')[0]] = '';
    }
  };

  // If user does not have team id = user is one of 'admins' 'members' 'operators' 'guests' = can access all survey
  if (!hasTeamId) {
    return survey;
  }

  // otherwise:
  // Filter survey by teams
  let flg = false;
  // if the teamIds that can access this survey were specified (at least one) AND it's not only 'Administrator'
  if (
    surveyObj.surveyTeams &&
    surveyObj.surveyTeams.length > 0 &&
    !(surveyObj.surveyTeams.length === 1 && surveyObj.surveyTeams[0].teamId === 'Administrator')
  ) {
    const surveyTeamsArr = surveyObj.surveyTeams.map((x) => x.teamId);
    for (const key in elTeam) {
      if (surveyTeamsArr.includes(key)) {
        flg = true;
        break;
      }
    }
  } else {
    // old surveyObj will not have surveyTeams attribute -> accessible for everyone by default
    flg = true;
  }

  return flg ? survey : null;
};

const update = async (username, input) => {
  const currentUnixtimeSec = Math.floor(new Date().getTime() / 1000);
  const tableName = dynamoDbTableSurveyConfig();

  //Add default administrator team.
  const adminTeam = { teamName: 'アドミニストレータ', teamId: 'Administrator' };

  // Old survey type might not have {surveyTeams} attribute -> add it
  if (!('surveyTeams' in input)) {
    input.surveyTeams = [];
  }
  const hasAdminInside = input.surveyTeams.filter((item) => item.teamId === 'Administrator').length;
  if (hasAdminInside === 0) {
    input.surveyTeams.push(adminTeam);
  }

  for (const item of input.surveySchema) {
    // isNewItem is used in frontend only, just to check new item or not, for edit mode.
    // delete in backend, to avoid exception return on frontend, then flag (isNewItem) was changed.

    delete item.isNewItem;
  }

  const surveyConfigObj = {
    surveyTitle: input.surveyTitle,
    surveyType: input.surveyType ? input.surveyType : 'normal',
    description: input.description === null ? '' : input.description,
    isAppending: input.isAppending,
    isSearchable: input.isSearchable,
    isEditDisabled: input.isEditDisabled,
    isDeleteAllEnabled: input.isDeleteAllEnabled,
    endOfSurveyMessage: input.endOfSurveyMessage === null ? '' : input.endOfSurveyMessage,
    endOfSurveyMessageType: input.endOfSurveyMessageType === null ? '' : input.endOfSurveyMessageType,
    deliveryMessageSetting: input.deliveryMessageSetting ? input.deliveryMessageSetting : [],
    surveyStatus: input.surveyStatus,
    surveySchema: input.surveySchema,
    surveyTeams: input.surveyTeams,
    categoriesPermissions: input.categoriesPermissions,
    updatedAt: currentUnixtimeSec,
    updatedBy: username,
    memberFormId: input.memberFormId ? input.memberFormId : '',
    isDisplay: input.isDisplay !== undefined ? input.isDisplay : true,
    vaccinationIntervalType: input.vaccinationIntervalType,
    doesCheckSameVaccineMaker: input.doesCheckSameVaccineMaker,
    headerImageUrl: input.headerImageUrl,
  };

  // If survey config is being deleted, set memberFormId to empty
  if (!surveyConfigObj.isDisplay) {
    surveyConfigObj.memberFormId = '';
  }

  const Key = {
    surveyId: { S: input.surveyId },
  };

  const updateBuilder = new UpdateExpressionBuilder(surveyConfigObj);
  updateBuilder.build();
  const result = await updateOne({
    TableName: tableName,
    Key: Key,
    ExpressionAttributeNames: updateBuilder.getExpressionAttributeNames(),
    ExpressionAttributeValues: updateBuilder.getExpressionAttributeValues(),
    UpdateExpression: updateBuilder.getSetExpression(),
  });

  if (input.categoriesPermissions) {
    await savePermissions(input.surveyId, input.categoriesPermissions);
  }

  await updateMemberConfigOnSurveyConfigUpdate(input, username);

  return result;
};

const isExistOptionsForDistributionSurveyConditions = async (surveyConditions) => {
  if (!surveyConditions.surveyId) {
    return true;
  }

  const latestData:any = await getBySurveyId(surveyConditions.surveyId);

  if (!latestData) {
    return false;
  }

  const surveySchema = latestData.surveySchema;

  const result = surveyConditions.conditions.every((condition) => {
    const itemKey = condition.itemKey;
    const value = condition.conditionValues;
    const schema = surveySchema.find((item) => item.itemKey === itemKey);
    if (!schema) {
      return false;
    }
    if (!value || !hasOptionByType(schema.type)) {
      return true;
    }

    // type of having options
    if (!schema.options) {
      return false;
    }

    if (Array.isArray(value)) {
      const isAllExists = value.every((v) => schema.options.includes(v));
      if (!isAllExists) {
        return false;
      }
    } else {
      const isExist = schema.options.includes(value);
      if (!isExist) {
        return false;
      }
    }

    return true;
  });

  return result;
};

const isAppendingSurveyType = async (surveyConditions) => {
  // 追加型の帳票はセグメント配信できません!
  if (!surveyConditions.surveyId) {
    return false;
  }

  const latestData:any = await getBySurveyId(surveyConditions.surveyId);

  if (!latestData) {
    return false;
  }

  return 'isAppending' in latestData && latestData.isAppending.value;
};

const getAllSurveyConfigsNoFilter = async () => {
  const tableName = dynamoDbTableSurveyConfig();
  return await scanList({
    TableName: tableName,
  });
}

const validatePhoneString = (value) => {
  return /^\d{10,11}$/.test(value);
}

const validateEmailString = (value) => {
  return /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(value);
}

const validatePostcodeString = (value) => {
  return /^\d{7}$/.test(value);
}

export {
  create,
  getAll,
  getBySurveyId,
  getBySurveyIdAndGroup,
  update,
  inputValidation,
  getUpdateOptions,
  isExistOptionsForDistributionSurveyConditions,
  isAppendingSurveyType,
  loadPermissions,
  createSurveyResult,
  getAllSurveyConfigsNoFilter,
  validateSelectPayTypeQuestion,
};
