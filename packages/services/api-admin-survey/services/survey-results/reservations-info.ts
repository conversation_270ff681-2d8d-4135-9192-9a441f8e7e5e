/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { DDClient } from '../../../common/utils/index.js';
import _ from 'lodash';
const OBJECT_NOT_EXISTS = {};

class ReservationsInfoRepository {
  private calendarsTableName: any;
  private credentials: any;
  private categoriesCache: any;
  private calendarsCache: any;
  private reservationItemsCache: any;
  dbGateway: any;
  private rruUsed: number;
  constructor(calendarsTableName, credentials) {
    this.calendarsTableName = calendarsTableName;
    this.credentials = credentials;
    this.categoriesCache = {};
    this.calendarsCache = {};
    this.reservationItemsCache = {};
    this.dbGateway = null;
    this.rruUsed = 0;
  }

  async getOne(request) {
    const dbClient = new DDClient.Table(this.calendarsTableName, null, this.credentials);
    const result = await dbClient.queryItems(request);
    this.rruUsed += dbClient.getStats().totalOperationCapacity;
    return result[0];
  }

  async attachCalendarInfo(mergedRecords) {
    if (!this.dbGateway) {
      this.dbGateway = this.getOne;
    }
    const elementsWithCalendarInfo:any[] = [];
    mergedRecords.forEach((record) => {
      const fields = record.fields || [];
      fields.forEach((field) => {
        const value = field.value;
        if (value && _.startsWith(value, 'category#') && value.includes('|')) {
          elementsWithCalendarInfo.push(field);
        }
      });
    });
    for (let index = 0; index < elementsWithCalendarInfo.length; index++) {
      const element = elementsWithCalendarInfo[index];
      const paramsList = element.value.split('|');

      const reservationItemId = paramsList[0];
      const categoryId = reservationItemId.split('_')[0];
      let category = this.categoriesCache[categoryId];
      if (category === OBJECT_NOT_EXISTS) {
        continue;
      }
      if (!category) {
        category = await this.dbGateway({
          query: 'partitionKey = :pk and sortKey = :sk',
          mapping: {
            [':pk']: 'categories',
            [':sk']: categoryId,
          },
        });
        this.categoriesCache[categoryId] = category || OBJECT_NOT_EXISTS;
      }
      if (category) {
        element.category = {
          ..._.pick(category, 'tag1', 'tag2', 'tag3', 'calendarId'),
          id: category.sortKey,
        };
      }

      const calendarId = category && category.calendarId;
      if (!calendarId) {
        continue;
      }
      let calendar = this.calendarsCache[calendarId];
      if (calendar === OBJECT_NOT_EXISTS) {
        continue;
      }
      if (!calendar) {
        calendar = await this.dbGateway({
          query: 'partitionKey = :pk and sortKey = :sk',
          mapping: {
            [':pk']: 'calendars',
            [':sk']: calendarId,
          },
        });
        this.calendarsCache[calendarId] = calendar || OBJECT_NOT_EXISTS;
      }

      let reservationStartTime:any = null;
      if (calendar) {
        element.calendar = {
          name: calendar.name,
          id: calendar.sortKey,
        };
        reservationStartTime = paramsList[2] && calendar.comaList[paramsList[2]] ? calendar.comaList[paramsList[2]].start : '-';
      }
      element.reservation = {
        slot: paramsList[2],
        date: paramsList[1],
        time: reservationStartTime,
      };

      if (reservationItemId.indexOf('_') > -1) {
        let reservationItem = this.reservationItemsCache[reservationItemId];
        if (!reservationItem) {
          reservationItem = this.dbGateway({
            query: 'partitionKey = :pk and begins_with(sortKey, :cid)',
            mapping: {
              [':pk']: 'reservationItems',
              [':cid']: reservationItemId,
            },
          });
          this.reservationItemsCache[reservationItemId] = reservationItem || OBJECT_NOT_EXISTS;
        }
        if (reservationItem !== OBJECT_NOT_EXISTS) {
          element.reservationItem = {
            id: reservationItemId,
            ...reservationItem,
          };
        }
      }
    }
  }
}

export default ReservationsInfoRepository;
