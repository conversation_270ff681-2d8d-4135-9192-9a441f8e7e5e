/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { DDClient } from '../../../common/utils/index.js';
import c from '../../../common/admin-api/config/constants.js';
import cfg from '../../../common/admin-api/config/static.js';
import _ from 'lodash';
import hash from 'object-hash';
import { getEnvironmentCredentials } from '../../../common/admin-api/utils/aws-helper.js';
import { DateTime } from 'luxon';
import {CategoryPermissionsRecord} from '../../types/survey-config.js';

const surveyConfigsCache = {};
const configsCachedLoader = async (params, isMember = null) => {
  const key = JSON.stringify(params);
  const cachedValue = surveyConfigsCache[key];
  if (cachedValue && DateTime.now().toSeconds() - cachedValue.time < 10) {
    return cachedValue.value;
  }
  const newValue = await loadSurveyConfig(params, isMember);
  surveyConfigsCache[key] = {
    value: newValue,
    time: DateTime.now().toSeconds(),
  };
  return newValue;
};

const loadPermissions = async (surveyId) => {
  const client = new DDClient.Table(
    cfg.get(c.DATABASE_TABLE),
    { pkField: 'partitionKey' },
    getEnvironmentCredentials()
  );
  const result: CategoryPermissionsRecord = await client.getItem({ pk: `surveyPermissions#${surveyId}` });
  if (result) {
    return result.permissions;
  }
  return null;
};

const loadSurveyConfig = async (surveyId, isMember = null) => {
  const tableName = isMember ? cfg.get(c.TABLE_MEMBER_CONFIGS) : cfg.get(c.TABLE_SURVEY_CONFIGS);
  const client = new DDClient.Table(
    tableName,
    { pkField: 'surveyId' },
    getEnvironmentCredentials()
  );
  const config = await client.getItem({ pk: surveyId });
  if (!config) {
    throw `No SurveyConfig with id [${surveyId}]`;
  }
  const permissions = await loadPermissions(surveyId);
  const currentPermissions = permissions || config.categoriesPermissions;
  if (currentPermissions && _.isArray(currentPermissions)) {
    const categoryIdToTeamsMapping = {};
    currentPermissions.forEach((largeCategory) => {
      const list = largeCategory.list;
      if (list) {
        list.forEach((category) => {
          if (category && category.categoryId && category.categoryId !== '') {
            const categoryNormal = category.categoryId.split('_')[0];
            let teams = categoryIdToTeamsMapping[categoryNormal];
            if (!teams) {
              teams = [];
              categoryIdToTeamsMapping[categoryNormal] = teams;
            }
            teams.push(...(category.teamIds || []));
          }
        });
      }
    });
    Object.keys(categoryIdToTeamsMapping).forEach((key) => {
      categoryIdToTeamsMapping[key] = _.uniq(categoryIdToTeamsMapping[key]);
    });
    config.categoriesPermissions = categoryIdToTeamsMapping;
  }
  return config;
};

const requestObjectHash = (obj) => {
  return hash(obj, {
    algorithm: 'sha1',
    unorderedArrays: true,
  })
}

const limitResultsArraySize = (sourceArray = [], limit, startAfterPk) => {
  let indexToStart = 0;
  if (startAfterPk) {
    const indexOfLastPreviousResult = sourceArray.findIndex(v => v.partitionKey === startAfterPk);
    if (indexOfLastPreviousResult >= 0) {
      indexToStart = indexOfLastPreviousResult+1;
    }
  }
  const upperLimit = indexToStart + limit;
  if (indexToStart === 0 && upperLimit >= sourceArray.length) {
    return {
      slice: sourceArray,
    }
  }

  const slice = sourceArray.slice(indexToStart, upperLimit);
  return {
    slice,
    lastKeyOfSlice: slice.length > 0 ? slice[slice.length-1].partitionKey : undefined,
  }
}

export {
  configsCachedLoader as loadSurveyConfig,
  requestObjectHash,
  limitResultsArraySize
};
