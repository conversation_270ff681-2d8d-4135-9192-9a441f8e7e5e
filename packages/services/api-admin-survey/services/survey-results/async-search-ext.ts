/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { DDClient } from '../../../common/utils/index.js';
import c from '../../../common/admin-api/config/constants.js';
import cfg from '../../../common/admin-api/config/static.js';
import { createLambdaClient } from '../../../common/utils/aws-clients.js'
import { getEnvironmentCredentials } from '../../../common/admin-api/utils/aws-helper.js';
import console from "console";

const getClient = (isMember = false) => {
  const table = !isMember ? cfg.get(c.TABLE_SURVEY_RESULTS) : cfg.get(c.TABLE_MEMBER_RESULTS); 
  return new DDClient.Table(
    table,
    {pkField: 'partitionKey', skField: 'sortKey'},
    getEnvironmentCredentials()
  );
}

const getSearchStatus = async (searchId, isMember) => {
  return await getClient(isMember).getItem({pk: searchId, sk: 'index'});
}
const getPartialResult = async (searchId, partialResultId, isMember) => {
  return await getClient(isMember).getItem({pk: searchId, sk: partialResultId});
}
const updateSearchStatus = async (statusObject, isMember) => {
  return getClient(isMember).update(statusObject);
}
const invokeSearchSync = async (payload) => {
  const { Payload, FunctionError } = await createLambdaClient().invoke({
    FunctionName: cfg.get(c.FUNCTION_SURVEY_SEARCH),
    Payload: JSON.stringify(payload),
  }).promise();
  if (FunctionError) {
    console.log('FunctionError', FunctionError)
    throw new Error(FunctionError)
  }
  if (typeof Payload === 'string') {
    return JSON.parse(Payload);
  }
  return Payload;
}

export {
  getSearchStatus,
  getPartialResult,
  updateSearchStatus,
  invokeSearchSync
}
