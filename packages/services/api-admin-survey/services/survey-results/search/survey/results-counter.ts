/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { AwsCredentials } from '../types.js';
import { DDClient } from '../../../../../common/utils/index.js';
import { DateTime } from 'luxon';

export default class ResultsCounter {
  private _maxExecutionLengthMs = 20000;
  constructor(private databaseName: string, private credentials: AwsCredentials) {}

  set maxExecutionLengthMs(ms: number) {
    this._maxExecutionLengthMs = ms;
  }

  async countForSurvey(surveyId: string, lastEvaluatedKey?: any) {
    const client = new DDClient.Table(this.databaseName, null, this.credentials);
    let lastKey = lastEvaluatedKey;
    const startedAt = DateTime.now();
    let lastPartitionKey = lastEvaluatedKey ? lastEvaluatedKey.partitionKey : null;
    let counter = 0;
    let itemsCounter = 0;
    do {
      const page = await client.queryPage({
        query: 'surveyId = :surveyId',
        mapping: {
          ':surveyId': surveyId,
        },
        attributes: ['partitionKey'],
        index: 'surveyId-partitionKey-index',
        lastEvaluatedKey: lastKey,
      });
      lastKey = page.lastEvaluatedKey;
      itemsCounter += page.items.length;

      for (let i = 0; i < page.items.length; i++) {
        const pk = page.items[i].partitionKey;
        if (pk !== lastPartitionKey) {
          lastPartitionKey = pk;
          counter++;
        }
      }
    } while (lastKey && -startedAt.diffNow().milliseconds < this._maxExecutionLengthMs);

    return {
      uniqueKeys: counter,
      timeUsedMs: -startedAt.diffNow().milliseconds,
      itemsScanned: itemsCounter,
      rruUsed: client.getStats().totalOperationCapacity,
      lastEvaluatedKey: lastKey,
    };
  }
}
