/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import * as _ from 'lodash';
import { DateTime } from 'luxon';
import { MergedRecordType } from './results-parallel-loader.js';
import { ReservationLineModel } from './results_model.js';

export type ArgumentMappingType = { [key: string]: string | undefined };
export type ParameterMappingType = { [key: string]: any };
export type KeyValueType = string | number | boolean | null;

export type FieldsFilterValueType = string[] | ({ [key: string]: any } & { values: string[] }) | string;
export type FieldsFilter = { [key: string]: FieldsFilterValueType };
export type DatesFilter = { [key: string]: { from: string | null; to: string | null } };

export const NULL_VALUE_STR = '---===!NULL!===---';

export type ResultSearchRequest = {
  surveyId: string;
  filterCommon: FieldsFilter;
  filterDate: DatesFilter;
  lastEvaluatedKey?: any;
  sortBy?: string[];
  sortDesc?: boolean[];
  options?: {
    maxReturnedCount?: number;
    maxSearchTime?: number;
  };
};

export type ResultsMetaSearchResponse = {
  results_meta: any[];
  preLoadedItems: any[];
  lastEvaluatedKey?: any;
  rruUsed: number;
  timeUsedMs: number;
  returnedCount: number;
  totalCount: number;
};

export type ResultsFullSearchResponse = {
  items: any[];
  lastEvaluatedKey?: any;
  rruUsed: number;
  timeUsedMs: number;
  returnedCount: number;
  totalCount: number;
};

export type ExpressionType = {
  expressionString: string;
  argumentsMapping: ArgumentMappingType;
  valuesMapping: ParameterMappingType;
};

export class DatesFilterModel {
  constructor(private filter: DatesFilter) {

  }

  getValidFilter(): DatesFilter {
    const correctFilter = {};
    Object.keys(this.filter).forEach((key) => {
      if (!this.filter[key]) {
        return;
      }
      const params = this.filter[key];
      const correctParams = {};
      Object.entries(params)
        .filter(([x, val]) => !_.isEmpty(val))
        .forEach(([key, val]) => (correctParams[key] = val));
      if (!_.isEmpty(correctParams)) {
        correctFilter[key] = correctParams;
      }
    });
    return correctFilter;
  }

  getBetweenInUnix(key: string) {
    const pair = this.getValidFilter()[key];
    if (!pair) {
return null;
}
    return {
      from: startDayToUnixSeconds(pair.from),
      to: endDayToUnixSeconds(pair.to),
    };
  }

  hasValues(key?: string) {
    if (key) {
      return this.getBetweenInUnix(key) !== null;
    } else {
      return Object.keys(this.getValidFilter()).length > 0;
    }
  }

  getKeys() {
    return Object.keys(this.getValidFilter());
  }

  hasReservation() {
    return this.hasValues('appointment_date');
  }

  getReservation() {
    return this.getBetweenInUnix('appointment_date');
  }
}

type NormalizedFieldsFilter = {
  [key: string]: {
    values: string[];
    options: { [key: string]: string };
  };
};

export class FieldsFilterModel {
  private validFilter: NormalizedFieldsFilter;

  constructor(private rawFilter: FieldsFilter) {
    this.validFilter = this.getValidFilter(rawFilter);
  }

  private hasValue(value: any) {
    if (_.isNil(value)) {
return false;
}
    if (typeof value === 'string') {
      return value.length === 0;
    }
    return true;
  }

  adjustKeysTo(...keys: string[]) {
    Object.keys(this.validFilter).forEach((filterKey) => {
      if (!keys.includes(filterKey)) {
        delete this.validFilter[filterKey];
      }
    });
  }

  getValidFilter(filter: FieldsFilter): NormalizedFieldsFilter {
    if (!filter) return {};
    const correctFilter: NormalizedFieldsFilter = {};
    Object.keys(filter).forEach((key) => {
      const option = filter[key];
      if (!option) {
        return;
      }
      let values: string[] = [];
      const options = {};
      if (_.isPlainObject(option)) {
        values = option['values'] || [];
        Object.assign(options, _.omit(option as any, 'values'));
      } else {
        values = _.flatten(option as any);
      }
      _.remove(values, this.hasValue);
      if (values.length === 0) {
        return;
      }
      correctFilter[key] = {
        values,
        options,
      };
    });
    return correctFilter;
  }

  isEmpty() {
    return Object.keys(this.validFilter).length === 0;
  }

  getValues(key: string) {
    if (this.hasValues(key)) {
      return this.validFilter[key].values;
    }
    return [];
  }

  getOptionValue(key: string, optionName: string) {
    if (!this.hasValues(key)) {
      return null;
    }
    return this.validFilter[key].options[optionName];
  }

  hasValues(key: string) {
    return this.validFilter[key] && this.validFilter[key].values.length > 0;
  }

  getValuesForCategoryFilter(key: string) {
    const rawValues = this.getValues(key);
    const result: any = {
      tag1: undefined,
      tag2: undefined,
      tag3: undefined,
      id: undefined,
    };
    rawValues.forEach((rawParameter) => {
      const parts = rawParameter.split(':');
      const key = parts[0];
      if (key === 'id') {
        result[key] = parts[1].split('_')[0];
      } else {
        result[key] = parts[1];
      }
    });
    return result;
  }

  getKeys() {
    return Object.keys(this.validFilter);
  }

  ignore(key?: string) {
    if (!key) {
return this;
}
    const map = this.rawFilter;
    delete map[key];
    return new FieldsFilterModel(map);
  }
}

export const fixReservationDaysInterval = ({ from, to }: { from?: string; to?: string }) => {
  const fromStr = from || '1970-01-01';
  const toStr = to || '2050-01-01';
  return {
    from: DateTime.fromISO(fromStr).startOf('day').toSeconds(),
    to: DateTime.fromISO(toStr).endOf('day').toSeconds(),
  };
};

export class RequestExpressionsBuilder {
  private join(logic: 'OR' | 'AND', ...expressions: ExpressionType[]): ExpressionType {
    const expressionString = expressions
      .filter((subex) => !_.isEmpty(subex.expressionString))
      .map((e) => {
        if (e.expressionString.indexOf(' OR ') > -1) {
          return `(${e.expressionString})`;
        }
        return e.expressionString;
      })
      .join(` ${logic} `);
    const argumentsMapping = Object.assign({}, ...expressions.map((e) => e.argumentsMapping));
    const valuesMapping = Object.assign({}, ...expressions.map((e) => e.valuesMapping));
    return {
      expressionString,
      valuesMapping,
      argumentsMapping,
    };
  }

  public static get emptyExpression() {
    return {
      expressionString: '',
      argumentsMapping: {},
      valuesMapping: {},
    };
  }

  joinOr(...expressions: ExpressionType[]): ExpressionType {
    return this.join('OR', ...expressions);
  }

  joinAnd(...expressions: ExpressionType[]): ExpressionType {
    return this.join('AND', ...expressions);
  }

  makeOr(name: string, values?: KeyValueType[], mode = 'exact') {
    if (_.isNil(values)) {
      return RequestExpressionsBuilder.emptyExpression;
    }
    const expressions = values.map((value, index) => this.makeWithLogic(name, index, value, mode));
    return this.joinOr(...expressions);
  }

  makeOrEqual(name: string, values?: KeyValueType[]): ExpressionType {
    if (_.isNil(values)) {
      return RequestExpressionsBuilder.emptyExpression;
    }
    const expressions = values.map((value, index) => this.makeEqual(name, index, value));
    return this.joinOr(...expressions);
  }

  makeWithLogic(name: string, index: number, value?: KeyValueType, mode = 'exact') {
    if (_.isNil(value)) {
      return RequestExpressionsBuilder.emptyExpression;
    }
    if (value === NULL_VALUE_STR) {
      return {
        expressionString: `#${name}_${index} = :null OR attribute_not_exists(#${name}_${index})`,
        argumentsMapping: { [`#${name}_${index}`]: name },
        valuesMapping: { [':null']: null },
      };
    }
    let expressionString;
    switch (mode) {
      case 'contains': {
        expressionString = `contains(#${name}_${index}, :${name}_${index})`;
        break;
      }
      default: {
        expressionString = `#${name}_${index} = :${name}_${index}`;
        break;
      }
    }
    return {
      expressionString: expressionString,
      argumentsMapping: { [`#${name}_${index}`]: name },
      valuesMapping: { [`:${name}_${index}`]: value },
    };
  }

  makeEqual(name: string, index: number, value?: KeyValueType): ExpressionType {
    if (_.isNil(value)) {
      return RequestExpressionsBuilder.emptyExpression;
    }

    return {
      expressionString: `#${name}_${index} = :${name}_${index}`,
      argumentsMapping: { [`#${name}_${index}`]: name },
      valuesMapping: { [`:${name}_${index}`]: value },
    };
  }

  makeInterval(name: string, { from, to }: { from?: KeyValueType; to?: KeyValueType }): ExpressionType {
    if (_.isNil(from) && _.isNil(to)) {
      return RequestExpressionsBuilder.emptyExpression;
    }

    if (_.isNil(to)) {
      return {
        expressionString: `#${name} >= :${name}`,
        argumentsMapping: { [`#${name}`]: name },
        valuesMapping: { [`:${name}`]: from },
      };
    }
    if (_.isNil(from)) {
      return {
        expressionString: `#${name} <= :${name}`,
        argumentsMapping: { [`#${name}`]: name },
        valuesMapping: { [`:${name}`]: to },
      };
    }
    return {
      expressionString: `#${name} BETWEEN :${name}_from AND :${name}_to`,
      argumentsMapping: { [`#${name}`]: name },
      valuesMapping: { [`:${name}_from`]: from, [`:${name}_to`]: to },
    };
  }
}

const startDayToUnixSeconds = (stringDay: string | null) => {
  if (!stringDay) {
return 0;
}
  return DateTime.fromISO(stringDay).startOf('day').toSeconds();
};

const endDayToUnixSeconds = (stringDay: string | null) => {
  if (!stringDay) {
return 2531881600;
}
  return DateTime.fromISO(stringDay).endOf('day').toSeconds();
};

const SURROGATE_FIELDS = ['indexId', 'userSearchKey', 'sortKey', 'partitionKey', 'title'];
const INDIVIDUAL_FIELDS = ['itemKey', 'value', 'files'];
const COMMON_FIELDS_OMIT = [...SURROGATE_FIELDS, ...INDIVIDUAL_FIELDS];

export class ItemsMergeHelper {
  constructor() {}

  public groupAndMerge(items: Required<{ partitionKey: string }>[]) {
    const grouped = Object.entries(_.groupBy(items, 'partitionKey')).map(([pk, items]) => ({ id: pk, items }));
    return this.mergeRecords(grouped);
  }

  private mergeRecords(groupedLines: { id: string; items: any[] }[]): MergedRecordType[] {
    return groupedLines.map((group) => {
      const singleItem = _.omit(group.items[0], ...COMMON_FIELDS_OMIT);
      return {
        partitionKey: group.id,
        ...singleItem,
        fields: group.items.map((item) => _.pick(item, INDIVIDUAL_FIELDS)),
      };
    });
  }
}

class MergedResultsModel {
  private record: MergedRecordType;

  constructor(merged: MergedRecordType) {
    this.record = merged;
  }

  getFieldValue(key: string) {
    const field = this.record.fields.find((f) => f.itemKey === key);
    return field ? field.value : null;
  }

  isCheckIn(checkValues: string[]) {
    return checkValues.includes(this.record['check'] || '');
  }

  isUpdatedAtIn(from: number, to: number) {
    const value = this.record['updatedAt'] || 0;
    return value >= from && value <= to;
  }

  isCreatedAtIn(from: number, to: number) {
    const value = this.record['createdAt'] || 0;
    return value >= from && value <= to;
  }

  isFieldIn(itemKey: string, values: string[], mode = 'exact') {
    const fieldValue = this.getFieldValue(itemKey) || '';
    if (mode === 'exact') {
      return values.includes(fieldValue);
    }
    return values.some((value) => fieldValue.indexOf(value) > -1);
  }

  isAppointmentIn(itemKey: string, from: number, to: number) {
    const line = this.getFieldValue(itemKey);
    if (!line) {
return false;
}
    const itemModel = new ReservationLineModel({ value: line });
    if (!itemModel.hasValidDate) {
      return false;
    }
    return itemModel.timestamp >= from && itemModel.timestamp <= to;
  }

  isCategoryIn(itemKey: string, categoryIds: string[]) {
    const line = this.getFieldValue(itemKey);
    if (!line) {
return false;
}
    const itemModel = new ReservationLineModel({ value: line });
    if (!itemModel.hasValidCategory) {
      return false;
    }
    return categoryIds.includes(itemModel.categoryIdPrefix);
  }

  getCategoryId(itemKey: string) {
    const line = this.getFieldValue(itemKey);
    if (!line) {
return null;
}
    const itemModel = new ReservationLineModel({ value: line });
    if (!itemModel.hasValidCategory) {
      return null;
    }
    return itemModel.categoryIdPrefix;
  }
}

export class MergedResultsFilter {
  private allowedCategoriesIds: string[] | undefined;
  private _categoriesPredicate: (id: string) => boolean;

  constructor(private fields: FieldsFilterModel, private dates: DatesFilterModel, private reservationKey?: string) {}

  set categoriesPredicate(p: (id: string) => boolean) {
    this._categoriesPredicate = p;
  }

  setAllowedCategories(categoryIds?: string[]) {
    this.allowedCategoriesIds = categoryIds;
  }

  buildFilterPredicate() {
    return (item: MergedRecordType) => {
      const model = new MergedResultsModel(item);
      let matches = true;

      const fieldsCheck = this.fields.ignore('check').ignore(this.reservationKey).getKeys();
      fieldsCheck.forEach((key) => {
        const searchMode = this.fields.getOptionValue(key, 'mode') || 'exact';
        matches = matches && model.isFieldIn(key, this.fields.getValues(key), searchMode);
      });
      if (!matches) {
return false;
}

      if (this.fields.hasValues('check')) {
        matches = matches && model.isCheckIn(this.fields.getValues('check'));
      }
      if (!matches) {
return false;
}

      if (this.dates.hasValues('createdAt')) {
        const interval = this.dates.getBetweenInUnix('createdAt');
        if (interval) {
          matches = matches && model.isCreatedAtIn(interval.from, interval.to);
        }
      }
      if (!matches) {
return false;
}

      if (this.dates.hasValues('updatedAt')) {
        const interval = this.dates.getBetweenInUnix('updatedAt');
        if (interval) {
          matches = matches && model.isUpdatedAtIn(interval.from, interval.to);
        }
      }
      if (!matches) {
return false;
}

      if (this.reservationKey && this.dates.hasValues('appointment_date')) {
        const interval = this.dates.getBetweenInUnix('appointment_date');
        if (interval) {
          matches = matches && model.isAppointmentIn(this.reservationKey, interval.from, interval.to);
        }
      }
      if (!matches) {
return false;
}

      if (this.reservationKey && this.fields.hasValues(this.reservationKey) && this.allowedCategoriesIds) {
        matches = matches && model.isCategoryIn(this.reservationKey, this.allowedCategoriesIds);
      }

      if (this.reservationKey) {
        const categoryId = model.getCategoryId(this.reservationKey);
        if (this._categoriesPredicate && categoryId) {
          matches = matches && this._categoriesPredicate(categoryId);
        }
      }

      return matches;
    };
  }

  doFiltering(items: MergedRecordType[]) {
    return items.filter(this.buildFilterPredicate());
  }
}
