/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {
  ArgumentMappingType,
  DatesFilterModel,
  ExpressionType,
  FieldsFilterModel,
  NULL_VALUE_STR,
  ParameterMappingType,
  RequestExpressionsBuilder,
} from './results-query-helpers.js';
import { ReservationLineModel } from './results_model.js';
import * as _ from 'lodash';

export type ResultsQueryModel = {
  keyExpression: string;
  filterExpression?: string;
  argumentsMapping: ArgumentMappingType;
  valuesMapping: ParameterMappingType;
  indexName?: string;
  reverseScan?: boolean;
  lastEvaluatedKey?: any;
  sortKeyName?: string;
  paginate?: boolean;
  postDownloadFilter?: ResultsQueryModelPostFilterCallback;
  requiredAttributes?: string[];
  resultsCountLimit?: number;
};
export type ResultsQueryModelPostFilterCallback = (item: any) => boolean;
export type CategoryPredicate = (categoryId: string) => boolean;

export class ResultsQueryBuilder {
  private metadataOnlyMode = false;
  private _categoryPredicate: CategoryPredicate = (id) => true;

  constructor(
    public readonly surveyId: string,
    private fields: FieldsFilterModel,
    private dates: DatesFilterModel,
    public readonly lastEvaluatedKey?: any
  ) {}

  private hasSomethingForFieldSearch() {
    if (this.dates.hasValues('appointment_date')) {
      return true;
    }
    if (this.fields.ignore('check').isEmpty()) {
      return false;
    }
    return true;
  }

  set metadataOnly(flag: boolean) {
    this.metadataOnlyMode = flag;
  }
  set categoryPredicate(predicate: CategoryPredicate) {
    this._categoryPredicate = predicate;
  }

  public willUseSequenceSearch() {
    return this.hasSomethingForFieldSearch();
  }

  public canUseParallelSearch() {
    return (
      !this.hasSomethingForFieldSearch() && this.fields.hasValues('check') && this.fields.getValues('check').length > 1
    );
  }

  public willUseFieldSearch() {
    return !this.willUseSequenceSearch();
  }

  public updateAtPrioritySearch(lastEvaluatedKey?: any, reverse = true): ResultsQueryModel {
    let interval = this.dates.getBetweenInUnix('updatedAt');
    if (!interval) {
      interval = { from: 0, to: 2531881600 };
    }
    const filterExpression = this.createAndExpressionFor('check', 'createdAt');
    return {
      keyExpression: 'surveyId = :surveyId and updatedAt BETWEEN :upd_from AND :upd_to',
      valuesMapping: {
        [':surveyId']: this.surveyId,
        [':upd_from']: interval.from,
        [':upd_to']: interval.to,
        ...filterExpression.valuesMapping,
      },
      argumentsMapping: {
        ...filterExpression.argumentsMapping,
      },
      filterExpression: filterExpression.expressionString,
      reverseScan: reverse,
      indexName: 'surveyId-updatedAt-index',
      paginate: true,
      lastEvaluatedKey: lastEvaluatedKey || this.lastEvaluatedKey || undefined,
    };
  }

  private createCheckIndexReqeuest(value: string, lekSupport = false): ResultsQueryModel {
    const filterExpression = this.createAndExpressionFor(
      ...this.dates.getKeys(),
      ...this.fields.ignore('check').getKeys()
    );
    return {
      keyExpression: 'surveyId = :surveyId and #check = :check ',
      valuesMapping: {
        [':surveyId']: this.surveyId,
        ...filterExpression,
        [':check']: value,
      },
      argumentsMapping: {
        ['#check']: 'check',
      },
      filterExpression: filterExpression.expressionString || undefined,
      reverseScan: true,
      indexName: 'surveyId-check-index',
      paginate: lekSupport,
      lastEvaluatedKey: this.lastEvaluatedKey || undefined,
    };
  }

  public createAndExpressionFor(...keysToBuild: string[]): ExpressionType {
    const keys = [...keysToBuild];
    const builder = new RequestExpressionsBuilder();
    const expressions = keys.map((key) => {
      if (['createdAt', 'updatedAt'].includes(key)) {
        const dates = this.dates.getBetweenInUnix(key);
        if (dates) {
          return builder.makeInterval(key, dates);
        }
      } else if (key === 'check') {
        if (this.fields.hasValues(key)) {
          const statuses = this.fields.getValues(key);
          if (statuses.includes('未対応')) {
            statuses.push(NULL_VALUE_STR);
          }
          return builder.makeOr('check', statuses);
        }
      } else {
        if (this.fields.hasValues(key)) {
          const mode = this.fields.getOptionValue(key, 'mode') || 'exact';
          return builder.makeOr('value', this.fields.getValues(key), mode);
        }
      }
      return RequestExpressionsBuilder.emptyExpression;
    });
    return builder.joinAnd(...expressions);
  }

  public createResultsMetaAttributesRequest(rowsFilterItemKey: string, lastEvaluatedKey?: any): ResultsQueryModel {
    const filterExpression = this.createAndExpressionFor('createdAt', 'updatedAt', 'check');
    return {
      keyExpression: 'surveyId = :surveyId and begins_with(sortKey, :sk)',
      valuesMapping: {
        [':surveyId']: this.surveyId,
        ...filterExpression.valuesMapping,
        [':sk']: rowsFilterItemKey,
      },
      argumentsMapping: {
        ...filterExpression.argumentsMapping,
      },
      filterExpression: filterExpression.expressionString || undefined,
      reverseScan: false,
      indexName: 'surveyId-sortKey-index',
      paginate: true,
      lastEvaluatedKey: lastEvaluatedKey || this.lastEvaluatedKey || undefined,
    };
  }

  public createFilterExpressionOnlyRequest(lastEvaluatedKey?: any): ResultsQueryModel {
    const filterExpression = this.createAndExpressionFor('createdAt', 'updatedAt', 'check');
    return {
      keyExpression: 'surveyId = :surveyId',
      valuesMapping: {
        [':surveyId']: this.surveyId,
        ...filterExpression.valuesMapping,
      },
      argumentsMapping: {
        ...filterExpression.argumentsMapping,
      },
      filterExpression: filterExpression.expressionString || undefined,
      reverseScan: false,
      indexName: 'surveyId-partitionKey-index',
      paginate: true,
      sortKeyName: 'partitionKey',
      lastEvaluatedKey: lastEvaluatedKey || this.lastEvaluatedKey || undefined,
    };
  }

  /**
   *
   * @param key itemKey of type=reservation field
   * @param pageSizeLimit
   * @param categoriesToMatch
   */
  public createReservationDateBasedRequest = (
    key: string,
    pageSizeLimit = 250000,
    categoriesToMatch?: string[]
  ): ResultsQueryModel => {
    const filterExpression = this.createAndExpressionFor('createdAt', 'updatedAt', 'check');
    const lastEvaluatedKey = _.isPlainObject(this.lastEvaluatedKey) ? this.lastEvaluatedKey : undefined;
    return {
      keyExpression: 'surveyId = :surveyId AND begins_with(sortKey,:key)',
      valuesMapping: {
        [':surveyId']: this.surveyId,
        [':key']: key,
        ...filterExpression.valuesMapping,
      },
      argumentsMapping: {
        ...filterExpression.argumentsMapping,
        ['#value']: this.metadataOnlyMode ? 'value' : undefined,
      },
      lastEvaluatedKey,
      filterExpression: filterExpression.expressionString || undefined,
      reverseScan: false,
      paginate: true,
      indexName: 'surveyId-sortKey-index',
      resultsCountLimit: pageSizeLimit,
      requiredAttributes: this.metadataOnlyMode ? ['#value'] : undefined,
      postDownloadFilter: (item) => {
        const itemModel = new ReservationLineModel(item);
        let result = true;
        if (!itemModel.hasValidCategory) {
          return false;
        }

        result = result && this._categoryPredicate(itemModel.categoryIdPrefix);
        if (!result) {
          return false;
        }

        if (categoriesToMatch) {
          result = result && categoriesToMatch.includes(itemModel.categoryIdPrefix);
        }

        const interval = this.dates.getBetweenInUnix('appointment_date');
        if (interval && !itemModel.hasValidDate) {
          return false;
        }
        if (interval) {
          result = result && itemModel.timestamp >= interval.from && itemModel.timestamp <= interval.to;
        }
        return result;
      },
    };
  };

  public createParallelSearchQueryModel(key: string, value: string): ResultsQueryModel {
    const filterBy = ['check', 'createdAt', 'updatedAt'];
    const searchMode = this.fields.getOptionValue(key, 'mode') || 'exact';
    const useNonExactSearch = searchMode !== 'exact';
    if (useNonExactSearch) {
      filterBy.push(key);
      const filterExpression = this.createAndExpressionFor(...filterBy);
      return {
        keyExpression: 'surveyId = :surveyId AND begins_with(sortKey, :sortKey)',
        valuesMapping: {
          [':surveyId']: this.surveyId,
          [':sortKey']: `${key}#`,
          ...filterExpression.valuesMapping,
        },
        argumentsMapping: {
          ...filterExpression.argumentsMapping,
        },
        filterExpression: filterExpression.expressionString,
        reverseScan: false,
        indexName: 'surveyId-sortKey-index',
      };
    }

    const filterExpression = this.createAndExpressionFor(...filterBy, key);
    return {
      keyExpression: 'surveyId = :surveyId AND sortKey = :sortKey',
      valuesMapping: {
        [':surveyId']: this.surveyId,
        [':sortKey']: `${key}#${value}`,
        ...filterExpression.valuesMapping,
      },
      argumentsMapping: {
        ...filterExpression.argumentsMapping,
      },
      filterExpression: filterExpression.expressionString,
      reverseScan: false,
      indexName: 'surveyId-sortKey-index',
    };
  }

  public checkSearchParallelQueryModel() {
    return {
      values: this.fields.getValues('check'),
      factory: (val: string) => this.createCheckIndexReqeuest(val, false),
    };
  }

  public checkSearchSimpleQueryModel() {
    return this.createCheckIndexReqeuest(this.fields.getValues('check')[0], true);
  }
}
