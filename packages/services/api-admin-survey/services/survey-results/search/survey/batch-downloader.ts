/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { AwsCredentials } from '../types.js';
import { DDClient } from '../../../../../common/utils/index.js';
import { MergedRecordType } from './results-parallel-loader.js';
import { ItemsMergeHelper } from './results-query-helpers.js';
import { DateTime } from 'luxon';
// eslint-disable-next-line @typescript-eslint/no-var-requires
import PromisePool from 'es6-promise-pool';

export class BatchDownloader {
  constructor(private resultsTableName: string, private credentials: AwsCredentials) {}

  async download(partitionKeys: string[], pollSize?: number) {
    if (!pollSize) {
      pollSize = partitionKeys.length;
    }

    const client = new DDClient.Table(
      this.resultsTableName,
      { pkField: 'partitionKey', skField: 'sortKey' },
      this.credentials
    );

    const collector: MergedRecordType[] = [];
    const helper = new ItemsMergeHelper();

    const executeDownloadTask = async (partitionKey: string) => {
      const lines = await client.queryItems({
        query: 'partitionKey = :pk',
        mapping: {
          [':pk']: partitionKey,
        },
      });
      const records = helper.groupAndMerge(lines);
      records.forEach((t) => collector.push(t));
    };
    const copy = [...partitionKeys];
    const executor = new PromisePool(() => {
      const key = copy.pop();
      if (key) {
        return executeDownloadTask(key);
      }
      return;
    }, pollSize);
    const startAt = DateTime.now();
    await executor.start();

    return {
      timeUsedMs: -startAt.diffNow().milliseconds,
      rruUsed: client.getStats().totalOperationCapacity,
      totalCount: collector.length,
      items: collector,
    };
  }
}
