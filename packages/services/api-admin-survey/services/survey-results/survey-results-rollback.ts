/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { RecordSurveyResults } from '../../types/survey-results.js';
import { create, deleteSingleSurveyResult } from '../survey-results.js';
import { tryToPlaceReservation } from '../calendars-ext.js';

type ReservationInfo = {
  calendarId: string;
  date: string;
  timeSlot: string;
  cost: number;
}

export default class SurveyResultsRollbackHelper {
  private _oldSurveyResults: RecordSurveyResults[];
  private _newSurveyResults: RecordSurveyResults[];
  private _oldReservationInfo: ReservationInfo;

  constructor(oldSurveyResults: RecordSurveyResults[], newSurveyResults: RecordSurveyResults[]) {
    this._oldSurveyResults = oldSurveyResults;
    this._newSurveyResults = newSurveyResults;
  }

  set oldReservationInfo(payload: ReservationInfo) {
    this._oldReservationInfo = payload;
  }

  private async rollbackOldReservation() {
    const { calendarId, date, timeSlot, cost } = this._oldReservationInfo;
    await tryToPlaceReservation(calendarId, date, timeSlot, cost);
  }

  private async rollbackOldSurveyResults() {
    await create(this._oldSurveyResults);
  }

  private async rollbackNewSurveyResults() {
    await Promise.all(this._newSurveyResults.map(result => deleteSingleSurveyResult(result)));
  }

  private async rollbackSurveyResults() {
    await this.rollbackNewSurveyResults();
    await this.rollbackOldSurveyResults();
  }

  async runRollback() {
    const promises = [this.rollbackSurveyResults()];
    try {
      if (this._oldReservationInfo) {
        promises.push(this.rollbackOldReservation());
      }
      await Promise.all(promises);
      this.logRollbackInfo();
    } catch (e: any) {
      this.logRollbackInfo(e);
    }
  }

  private logRollbackInfo(error?: Error) {
    console.log({
      reseult: error ? 'ERROR' : 'SUCCESS',
      oldSurveyResults: this._oldSurveyResults,
      oldReservation: this._oldReservationInfo,
    });
    error && console.error(error);
  }
}
