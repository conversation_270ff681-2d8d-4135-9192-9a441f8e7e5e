/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import ReservationsInfoRepo from './reservations-info.js';
import { infoLog } from '../../../common/admin-api/utils/logger.js';
import config from '../../../common/admin-api/config/static.js';
import c from '../../../common/admin-api/config/constants.js';
import { getEnvironmentCredentials } from '../../../common/admin-api/utils/aws-helper.js';
import { DateTime } from 'luxon';
import { loadSurveyConfig } from './helpers.js';

import { QuickSearchManager, BatchDownloader } from './search/all.js';
import { default as ResultsCounter } from './search/survey/results-counter.js';
import * as calendarsExt from '../calendars-ext.js';
import { authContext } from '../../../common/admin-api/utils/auth.js';

const categoriesCache = {};
const categoriesLoaderCached = async (params) => {
  const key = JSON.stringify(params);
  const cachedValue = categoriesCache[key];
  if (cachedValue && DateTime.now().toSeconds() - cachedValue.time < 10) {
    return cachedValue.value;
  }
  const newValue = await categoriesLoader(params);
  categoriesCache[key] = {
    value: newValue,
    time: DateTime.now().toSeconds(),
  };
  return newValue;
};

const categoriesLoader = async ({ tag1, tag2, tag3, id }) => {
  if (id) {
    return [id];
  }
  return (await calendarsExt.filterCategories(tag1, tag2, tag3)).map((category) => category.sortKey);
};

const getUsingManager = async (
  surveyId,
  { filterCommon, filterDate, lastEvaluatedKey, sortBy, sortDesc, options },
  teams = authContext.teams
) => {
  infoLog(
    'getUsingFields(surveyId, filterCommon, filterDate, lastEvaluatedKey, sortBy, sortDesc, options, teams)',
    surveyId,
    filterCommon,
    filterDate,
    lastEvaluatedKey,
    sortBy,
    sortDesc,
    options,
    teams
  );
  const loader = new QuickSearchManager({
    surveyConfigTable: config.get(c.TABLE_SURVEY_CONFIGS),
    surveyResultsTable: config.get(c.TABLE_SURVEY_RESULTS),
    credentials: getEnvironmentCredentials(),
  });
  loader.setFromSearchRequest({
    surveyId,
    lastEvaluatedKey,
    sortBy,
    sortDesc,
    filterCommon: filterCommon || {},
    filterDate: filterDate || {},
    options: options || {},
  });
  loader.setPreloadConfig({ full: true });
  loader.setCategoriesLoader(categoriesLoaderCached);
  loader.setSurvey(surveyId, await loadSurveyConfig(surveyId));
  if (teams && !authContext.isAdmin) {
    loader.setAccessRules({ teams });
  }

  await loader.prepare();
  const results = await loader.searchAndGetResults();

  return {
    rruUsage: results.rruUsed,
    timeUsedMs: results.timeUsedMs,
    totalCount: results.totalCount,
    items: results.preLoadedItems,
    lastEvaluatedKey: results.lastEvaluatedKey,
  };
};

const getWithDatabaseFilter = async (surveyId, { filterCommon, filterDate, lastEvaluatedKey = undefined, sortBy, sortDesc }) => {
  const results = await getUsingManager(surveyId, { filterCommon, filterDate, lastEvaluatedKey, sortBy, sortDesc, options: {} });
  return results;
};

const searchResultsMeta = async (request, teams = authContext.teams) => {
  const manager = new QuickSearchManager({
    surveyConfigTable: config.get(c.TABLE_SURVEY_CONFIGS),
    surveyResultsTable: config.get(c.TABLE_SURVEY_RESULTS),
    credentials: getEnvironmentCredentials(),
  });
  manager.setCollectionTimeLimit(10);
  if (teams && !authContext.isAdmin) {
    manager.setAccessRules({ teams });
  }
  manager.setSurvey(request.surveyId, await loadSurveyConfig(request.surveyId));
  if (request.countOnly) {
    request.sortBy = [];
    (request.filterDate || {})['updatedAt'] = undefined;
    request.options = {
      ...(request.options || {}),
      maxReturnedCount: 9999999,
    };
  }
  manager.setMetadataAttributes(['partitionKey', 'updatedAt']);
  if (!request.options) {
    request.options = {};
  }
  const maxCount = request.options.maxReturnedCount || 10000;
  if (maxCount > 10000) {
    request.options.maxReturnedCount = 10000;
  }
  manager.setFromSearchRequest({
    ...request,
    filterCommon: request.filterCommon || {},
    filterDates: request.filterDate || {},
    options: request.options || {},
  });
  manager.setCategoriesLoader(categoriesLoaderCached);
  await manager.prepare();
  const results = await manager.searchAndGetResults();

  if (request.countOnly) {
    results.results_meta = [];
  }
  if (results.results_meta.length > 10000) {
    const slice = results.results_meta.slice(0, 10000);
    const lastEvaluatedKey = slice[slice.length - 1].partitionKey;
    results.results_meta = slice;
    results.lastEvaluatedKey = lastEvaluatedKey;
    results.returnedCount = slice.length;
  }
  return results;
};

const batchDownload = async (partitionKeys, isMember = false) => {
  const table = !isMember ? config.get(c.TABLE_SURVEY_RESULTS) : config.get(c.TABLE_MEMBER_RESULTS);
  const loader = new BatchDownloader(table, getEnvironmentCredentials());
  return await loader.download(partitionKeys);
};

const batchDownloadWithCalendarInfo = async (partitionKeys, isMember = false) => {
  const results = await batchDownload(partitionKeys, isMember);
  const table = !isMember ? config.get(c.TABLE_SURVEY_RESULTS) : config.get(c.TABLE_MEMBER_RESULTS);
  const repository = new ReservationsInfoRepo(table, getEnvironmentCredentials());
  await repository.attachCalendarInfo(results.items);
  return results;
};
const countForSurvey = async (surveyId, lastEvaluatedKey, maxExecutionLengthMs = 20000, isMember = false) => {
  const table = !isMember ? config.get(c.TABLE_SURVEY_RESULTS) : config.get(c.TABLE_MEMBER_RESULTS);
  const counter = new ResultsCounter(table, getEnvironmentCredentials());
  counter.maxExecutionLengthMs = maxExecutionLengthMs;
  return await counter.countForSurvey(surveyId, lastEvaluatedKey);
};

const attachCalendarInfoToGroupedItems = async (items, isMember = false) => {
  const table = !isMember ? config.get(c.TABLE_SURVEY_RESULTS) : config.get(c.TABLE_MEMBER_RESULTS);
  const repository = new ReservationsInfoRepo(table, getEnvironmentCredentials());
  await repository.attachCalendarInfo(items);
  return items;
};

export {
  getWithDatabaseFilter,
  searchResultsMeta,
  batchDownload,
  batchDownloadWithCalendarInfo,
  countForSurvey,
  attachCalendarInfoToGroupedItems,
};
