/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { debugLog } from '../../../common/admin-api/utils/logger.js';
import config from '../../../common/admin-api/config/static.js';
import c from '../../../common/admin-api/config/constants.js';
import { getEnvironmentCredentials } from '../../../common/admin-api/utils/aws-helper.js';
import { SurveyConfigModel } from './search/all.js';
import PromisePool from 'es6-promise-pool';
import { DateTime } from 'luxon';

import { DDClient } from '../../../common/utils/index.js';

const getReservationItemIds = async () => {
  const client = new DDClient.Table(config.get(c.TABLE_SURVEY_CONFIGS), null, getEnvironmentCredentials());
  const configs = await client.getAllItems();
  return configs
    .map((config) => {
      return { itemKey: new SurveyConfigModel(config).getReservationFieldKey(), surveyId: config.surveyId };
    })
    .filter((key) => !!key.itemKey);
};

const doParallelSearch = async () => {
  const start = DateTime.now();
  const surveyIds = await getReservationItemIds();
  debugLog('Survey configs finished', -start.diffNow('second').seconds, surveyIds.length, 'SurveyConfigs');

  const results = new Set();
  const task = async (key) => {
    const time = DateTime.now();
    const categoriesSet:any[] = await getReservationRecords(key.surveyId, key.itemKey);
    categoriesSet.forEach((s) => results.add(s));
    debugLog('Finished', key.surveyId, `${-time.diffNow('second').seconds} sec`, categoriesSet.length, results);
  };
  const executor = new PromisePool(() => {
    const key = surveyIds.pop();
    if (key) {
      return task(key);
    }
    return;
  }, 25);
  await executor.start();
  return Array.from(results);
};

const getReservationRecords = async (surveyId, reservationItemId) => {
  const client = new DDClient.Table(config.get(c.TABLE_SURVEY_RESULTS), null, getEnvironmentCredentials());
  const records = await client.queryItems({
    query: 'surveyId = :surveyId and begins_with(sortKey, :sortKey)',
    index: 'surveyId-sortKey-index',
    filter: 'attribute_exists(#value) AND size(#value) > :zero',
    mapping: {
      ':surveyId': surveyId,
      ':zero': 0,
      ':sortKey': `${reservationItemId}#`,
      '#value': 'value',
    },
    attributes: ['#value'],
  });
  const results = new Set();
  records.forEach((rec) => {
    let category = rec.value;
    if (category && typeof category === 'string' && category.includes('|')) {
      category = category.split('|')[0];
      category = category.split('_')[0];
      results.add(category);
    }
  });
  return Array.from(results);
};

export {
  doParallelSearch as collectAllCategoryIdsFromSurveyResults,
};
