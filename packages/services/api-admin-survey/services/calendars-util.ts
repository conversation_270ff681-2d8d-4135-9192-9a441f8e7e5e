/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import * as excep from '../../common/admin-api/utils/exceptions.js';

const assertCalendarExists = (check, calendarId) => {
  if (!check) {
    throw new excep.BadRequest({
      code: 'not_found',
      msg: `Calendar [${calendarId}] not found`,
      details: { id: calendarId },
    });
  }
};

const assertCategoryExists = (check, categoryId?) => {
  if (!check) {
    throw new excep.BadRequest({
      code: 'not_found',
      msg: `Category [${categoryId}] not found`,
      details: { id: categoryId },
    });
  }
};

const assertExists = (check, message, details?) => {
  if (!check) {
    throw new excep.BadRequest({
      msg: message,
      details: details,
    });
  }
};

const convertDeletionReport = (report, success = false) => {
  return report.map((d) => {
    if (d.canDelete) {
      return {
        date: d.date,
        code: success ? 'deleted' : 'delete',
      };
    } else {
      return {
        date: d.date,
        code: 'forbidden',
        details: {
          reservations: d.reservations,
        },
      };
    }
  });
};

export {
  assertCalendarExists,
  assertCategoryExists,
  assertExists,
  convertDeletionReport,
};
