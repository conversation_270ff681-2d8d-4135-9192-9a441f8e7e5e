/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import AWS from 'aws-sdk';
import { DDClient } from 'common/utils/index.js';
import { scanList } from '../../common/admin-api/utils/db.js';
import {getEnvironmentCredentials} from '../../common/admin-api/utils/aws-helper.js';
import c from '../../common/admin-api/config/constants.js';
import config from '../../common/admin-api/config/static.js';
import { DateTime } from 'luxon';
import {collectAllCategoryIdsFromSurveyResults} from './survey-results/categories-collect.js';
import {infoLog} from '../../common/admin-api/utils/logger.js';
import {CalendarRecord, CalendarRecordComaList, ScheduleDayRecord} from '../types/calendars.js';
import {DynamoBatchPutOperation} from '../../common/admin-api/utils/dynamo-batch-put.js';
import { CategoryRecord } from '../types/categories.js';

const getCurrentUnixTime = () => {
  return Math.round(new Date().getTime() / 1000);
};

const dynamoDbTable = () => {
  return config.get(c.TABLE_SURVEY_CONFIGS);
};

const getTable = () => {
  return new DDClient.Table(
    config.get(c.TABLE_SURVEY_CALENDARS),
    {
      pkField: 'partitionKey',
      skField: 'sortKey',
    },
    getEnvironmentCredentials()
  );
};

function getBatchPut() {
  return new DynamoBatchPutOperation(new AWS.DynamoDB.DocumentClient(getEnvironmentCredentials()));
}

const getDocumentClient = () => {
  return new AWS.DynamoDB.DocumentClient(getEnvironmentCredentials());
};

const getAllCategories = async (): Promise<CategoryRecord[]> => {
  return getTable().queryItems({
    query: 'partitionKey = :pk',
    mapping: { [':pk']: 'categories' },
  });
};

const getSingleCategory = async (categoryId: string): Promise<CategoryRecord> => {
  const _categoryId = categoryId.split('_')[0];
  return getTable().getItem({ pk: 'categories', sk: _categoryId });
};

const getSchedulesOfCalendar = async (calendarId, startDay, endDay) => {
  return getTable().queryItems({
    query: 'partitionKey = :calendarId',
    filter: '#date >= :startDay and #date <= :endDay',
    mapping: {
      [':calendarId']: calendarId,
      [':startDay']: startDay,
      [':endDay']: endDay,
      ['#date']: 'date',
    },
  });
};
const filterCategories = async (tag1?: string, tag2?: string, tag3?: string, searchBy?: string): Promise<CategoryRecord[]> => {
  const prefixParaName = 'prefix';
  const containsParaName = 'contains';
  const valuesMapping = {};
  const filter:any[] = [];

  if (tag1) {
    if (searchBy !== undefined) {
      if (searchBy === prefixParaName) {
        filter.push('begins_with(tag1, :tag1)');
      } else if (searchBy === containsParaName) {
        filter.push('contains(tag1, :tag1)');
      } else {
        filter.push('tag1 = :tag1');
      }
    } else {
      filter.push('tag1 = :tag1');
    }

    valuesMapping[':tag1'] = tag1;
  }

  if (tag2 !== undefined) {
    if (tag2 === '') {
      filter.push('attribute_not_exists(tag2)');
    } else {
      if (searchBy !== undefined) {
        if (searchBy === prefixParaName) {
          filter.push('begins_with(tag2, :tag2)');
        } else if (searchBy === containsParaName) {
          filter.push('contains(tag2, :tag2)');
        } else {
          filter.push('tag2 = :tag2');
        }
      } else {
        filter.push('tag2 = :tag2');
      }

      valuesMapping[':tag2'] = tag2;
    }
  }

  if (tag3 !== undefined) {
    if (tag3 === '') {
      filter.push('attribute_not_exists(tag3)');
    } else {
      if (searchBy !== undefined) {
        if (searchBy === prefixParaName) {
          filter.push('begins_with(tag3, :tag3)');
        } else if (searchBy === containsParaName) {
          filter.push('contains(tag3, :tag3)');
        } else {
          filter.push('tag3 = :tag3');
        }
      } else {
        filter.push('tag3 = :tag3');
      }

      valuesMapping[':tag3'] = tag3;
    }
  }

  return await getTable().queryItems({
    query: 'partitionKey = :pk',
    mapping: {
      ...valuesMapping,
      [':pk']: 'categories',
    },
    filter: filter.join(' and '),
  });
};

const saveCategory = async (category) => {
  const currentUnixTime = getCurrentUnixTime();
  await getTable().putItem({
    partitionKey: 'categories',
    sortKey: category.id,
    tag1: category.tag1,
    tag2: category.tag2,
    tag3: category.tag3,
    calendarId: category.calendarId,
    createdAt: category.createdAt,
    updatedAt: currentUnixTime,
    largeCategoryOrder: category.largeCategoryOrder,
    mediumCategoryOrder: category.mediumCategoryOrder,
    smallCategoryOrder: category.smallCategoryOrder,
  });
};

const updateCategoryOrder = async (category): Promise<CategoryRecord> => {
  const now = getCurrentUnixTime();
  return await getTable().update({
    partitionKey: 'categories',
    sortKey: category.id,
    updatedAt: now,
    largeCategoryOrder: category.largeCategoryOrder,
    mediumCategoryOrder: category.mediumCategoryOrder,
    smallCategoryOrder: category.smallCategoryOrder,
  });
}

const saveCategoriesBatch = async (categories) => {
  const now = DateTime.utc().toSeconds();
  const requests = categories.map((c) => {
    return {
      ...c,
      partitionKey: 'categories',
      sortKey: c.sortKey,
      updatedAt: now,
      createdAt: now,
    };
  });
  await getBatchPut().batchPut(config.get(c.TABLE_SURVEY_CALENDARS), requests);
  return requests;
};

const getAllTags = async () => {
  return await getTable().queryItems({
    query: 'partitionKey = :pk',
    mapping: { [':pk']: 'tags' },
  });
};

const fixedCategories = (results) => {
  const fixedCategories = {};
  results.forEach((obj) => {
    const surveySchema = obj.surveySchema;
    if (surveySchema.length > 0) {
      surveySchema.forEach((element) => {
        if (element.reservationCheckBox) {
          const { selectedLargeCategory } = element;
          const largeCategory = selectedLargeCategory ?
            selectedLargeCategory.name ? selectedLargeCategory.name : undefined
            : undefined;
          if (largeCategory) {
            if (!fixedCategories[largeCategory]) {
              fixedCategories[largeCategory] = [];
            }
            if (element.reservationSupCheckBox) {
              const { selectedMediumCategory } = element;
              const mediumCategory = selectedMediumCategory ?
                selectedMediumCategory.name ? selectedMediumCategory.name : undefined
                : undefined;
              (mediumCategory && !fixedCategories[largeCategory].includes(mediumCategory)) &&
              fixedCategories[largeCategory].push(mediumCategory);
            }
          }
        }
      });
    }
  });
  return fixedCategories;
};

const getFixedCategories = async () => {
  const tableName = dynamoDbTable();
  const list = await scanList({
    TableName: tableName,
  });
  return fixedCategories(list);
};

const getSurveyResultCategories = async () => {
  return await collectAllCategoryIdsFromSurveyResults();
};
const getAllCalendars = async () => {
  return await getTable().queryItems({
    query: 'partitionKey = :pk',
    mapping: { [':pk']: 'calendars' },
  });
};

const getCalendarInfo = async (calendarId): Promise<CalendarRecord> => {
  return await getTable().getItem({
    pk: 'calendars',
    sk: calendarId,
  });
};

const findCategoriesByCalendar = async (calendarId) => {
  return await getTable().queryItems({
    query: 'partitionKey = :pk',
    filter: 'calendarId = :calId',
    mapping: {
      [':pk']: 'categories',
      [':calId']: calendarId,
    },
  });
};

const saveGlobalDisplaySettings = async (settings) => {
  const currentUnixTime = getCurrentUnixTime();
  await getTable().putItem({
    partitionKey: 'settings',
    sortKey: 'global#display',
    title: settings.title,
    tag1: settings.tag1,
    tag2: settings.tag2,
    tag3: settings.tag3,
    reservationStart: settings.reservationStart,
    updatedAt: currentUnixTime,
  });
};

const getGlobalDisplaySettings = async () => {
  return await getTable().getItem({
    pk: 'settings',
    sk: 'global#display',
  });
};

const getScheduleForDays = async (calendarId: string, days: string[]): Promise<ScheduleDayRecord[]> => {
  const batchRequest = days.map((day) => {
    return {
      pk: calendarId,
      sk: `schedule#${day}`,
    };
  });
  return await getTable().batchGet(batchRequest);
};

const updateSchedule = async (newDays: ScheduleDayRecord[]) => {
  await getBatchPut().batchPut(config.get(c.TABLE_SURVEY_CALENDARS), newDays);
};

const saveCalendar = async (calendar) => {
  await getTable().putItem(calendar);
};

const updateComa = async (calendarId: string, comaList: CalendarRecordComaList, now: number) => {
  await getTable().update({
    partitionKey: 'calendars',
    sortKey: calendarId,
    comaList: comaList,
    updatedAt: now,
  });
};

const updateCategoryLink = async (categoryId, calendarId) => {
  await getTable().updateOrDeleteFields({
    partitionKey: 'categories',
    sortKey: categoryId,
    calendarId: calendarId || undefined,
  });
};

const getFutureSchedules = async (calendarId) => {
  const now = DateTime.local().toFormat('yyyyLLdd');
  return await getTable().queryItems({
    query: 'partitionKey = :pk and sortKey >= :now',
    mapping: {
      [':pk']: calendarId,
      [':now']: now,
    },
  });
};

const deleteCategory = async (categoryId) => {
  await getTable().deleteItem({ pk: 'categories', sk: categoryId });
};

const deleteCalendarWithSchedules = async (calendarId) => {
  const keysToDelete = await getTable().queryItems({
    query: 'partitionKey = :pk',
    mapping: {
      [':pk']: calendarId,
    },
    attributes: ['partitionKey', 'sortKey'],
  });
  // await getTable().deleteItems(keysToDelete.map((k) => ({pk: k.partitionKey, sk: k.sortKey})));
  await getTable().deleteItem({ pk: 'calendars', sk: calendarId });

  const categories = await findCategoriesByCalendar(calendarId);
  for (let i = 0; i < categories.length; i++) {
    await updateCategoryLink(categories[i].sortKey, undefined);
  }
};

const decrementReservation = async (calendarId, date, timeSlot) => {
  const params = {
    TableName: config.get(c.TABLE_SURVEY_CALENDARS),
    Key: { partitionKey: calendarId, sortKey: `schedule#${date}` },
    UpdateExpression: 'ADD reservationCounts.#timeSlot :dec',
    ConditionExpression: 'attribute_exists(reservationCounts.#timeSlot) and reservationCounts.#timeSlot > :zero',
    ExpressionAttributeNames: {
      ['#timeSlot']: timeSlot,
    },
    ExpressionAttributeValues: {
      [':dec']: -1,
      [':zero']: 0,
    },
  };

  try {
    await getDocumentClient().update(params).promise();
  } catch (e: any) {
    if (e.name === 'ConditionalCheckFailedException') {
      return false;
    }
    throw e;
  }
  return true;
};

const tryToPlaceReservation = async (calendarId, date, timeSlot, inc) => {
  let conditionExpression;
  if (inc > 0) {
    //Adding reservation
    conditionExpression =
      '(attribute_exists(reservationCounts.#timeSlot) and reservationCounts.#timeSlot < quotas.#timeSlot)' +
      ' or (attribute_not_exists(reservationCounts.#timeSlot) and quotas.#timeSlot > :zero)';
  } else {
    //Canceling reservation
    conditionExpression = '(attribute_exists(reservationCounts.#timeSlot) and reservationCounts.#timeSlot > :zero)';
  }

  const request = await getDocumentClient().update({
    TableName: config.get(c.TABLE_SURVEY_CALENDARS),
    Key: { partitionKey: calendarId, sortKey: `schedule#${date}` },
    UpdateExpression: 'ADD reservationCounts.#timeSlot :inc',
    ConditionExpression: conditionExpression,
    ExpressionAttributeNames: {
      ['#timeSlot']: timeSlot,
    },
    ExpressionAttributeValues: {
      [':inc']: inc,
      [':zero']: 0,
    },
  });
  try {
    await request.promise();
    infoLog(`PlaceReservation [Success] calendarId=${calendarId} date=${date} timeSlot=${timeSlot} inc=${inc}`);
    return true;
  } catch (e: any) {
    infoLog(
      `PlaceReservation [Fail] calendarId=${calendarId} date=${date} timeSlot=${timeSlot} inc=${inc}\n${JSON.stringify(
        e
      )}`
    );
    if (e.name === 'ConditionalCheckFailedException') {
      return false;
    }
    throw e;
  }
};

// 予約枠に空きがあるかチェック（incがマイナスの場合、減らす予約数があるか）
const checkReservationPossible = async (calendarId, date, timeSlot, inc) => {
  const checkData = await getTable().getItem({ pk: calendarId, sk: `schedule#${date}` });
  let quotas = 0;
  let reservationCounts = 0;
  if (checkData) {
    if (checkData.quotas) {
      quotas = checkData.quotas[timeSlot] ? Number(checkData.quotas[timeSlot]) : 0;
    }
    if (checkData.reservationCounts) {
      reservationCounts = checkData.reservationCounts[timeSlot] ? Number(checkData.reservationCounts[timeSlot]) : 0;
    }
  }
  infoLog({
    予約数: reservationCounts,
    予約枠: quotas,
    必要枠: inc,
    カレンダーID: calendarId,
    日付: date,
    時間枠: timeSlot,
  });
  if (inc > 0) {
    return quotas - reservationCounts >= Number(inc);
  } else {
    return reservationCounts + Number(inc) >= 0;
  }
};

const updateDayOff = async (calendarId, updateDate, dayOff, now) => {
  await getTable().update({
    partitionKey: calendarId,
    sortKey: `schedule#${updateDate}`,
    dayOff: dayOff,
    updatedAt: now,
  });
};

const getReservationControlInfo = async (calendarId) => {
  return await getTable().queryItems({
    query: 'partitionKey = :pk and begins_with(sortKey,:cid)',
    mapping: {
      [':pk']: 'reservationControl',
      [':cid']: calendarId,
    },
  });
};

const upsertReservationControlInfo = async (params) => {
  const currentUnixTime = getCurrentUnixTime();
  let deletedAt:any = null;
  if (params.delete) {
    deletedAt = params.deletedAt ? params.deletedAt : currentUnixTime;
  }
  await getTable().putItem({
    partitionKey: 'reservationControl',
    sortKey: params.sortKey,
    calendarId: params.calendarId,
    workId: params.workId,
    execDate: params.execDate,
    execTime: params.execTime,
    startDate: params.startDate,
    endDate: params.endDate,
    isExecuted: params.isExecuted,
    updatedAt: currentUnixTime,
    deletedAt: deletedAt,
  });
};

const execReservationControl = async () => {
  try {
    // 実行設定を取得
    const controls = await getTable().queryItems({
      query: 'partitionKey = :pk',
      filter: 'isExecuted = :done',
      mapping: {
        [':pk']: 'reservationControl',
        [':done']: 0,
      },
    });

    // 実行対象となる設定を取得
    const execList = {};
    const oldControl:any[] = [];
    for (const control of controls) {
      if (control.isExecuted === 0 && !control.deletedAt) {
        const execDate = new Date(`${control.execDate} ${control.execTime}`);
        const now = new Date();
        now.setHours(now.getHours() + 9);
        if (execDate <= now) {
          let isSet = false;
          if (execList[control.calendarId]) {
            // calendarId毎に複数ある場合、最新の実行時刻のものを優先
            isSet = execList[control.calendarId].execDate < execDate;
            // 実行する最新の設定より古い設定は実行済みにする為保持
            if (isSet) {
              oldControl.push(execList[control.calendarId].control);
            } else {
              oldControl.push(control);
            }
          } else {
            isSet = true;
          }
          if (isSet) {
            execList[control.calendarId] = {
              execDate: execDate,
              control: control,
              startDate: control.startDate,
              endDate: control.endDate,
            };
          }
        }
      }
    }

    const currentUnixTime = getCurrentUnixTime();
    for (const calendarId in execList) {
      const execParams = execList[calendarId];
      const targetCalendar = await getTable().getItem({
        pk: 'calendars',
        sk: calendarId,
      });
      // pk:calendarsのデータを更新
      targetCalendar['startDate'] = execParams.startDate;
      targetCalendar['endDate'] = execParams.endDate;
      targetCalendar['updatedAt'] = currentUnixTime;
      await getTable().putItem(targetCalendar);

      // pk:reservationControlのデータを実行済みにする
      execParams.control['isExecuted'] = 1; // 実行済み
      execParams.control['updatedAt'] = currentUnixTime;
      await getTable().putItem(execParams.control);
    }

    for (let i = 0; i < oldControl.length; i++) {
      // 実行した設定より過去分の設定を実行済みにする
      oldControl[i]['isExecuted'] = 1; // 実行済み
      oldControl[i]['updatedAt'] = currentUnixTime;
      await getTable().putItem(oldControl[i]);
    }

    return true;
  } catch (e: any) {
    console.log(e.message);
    return false;
  }
};

const updateCalendarOff = async (calendarId, calendarOff, now) => {
  await getTable().update({
    partitionKey: 'calendars',
    sortKey: calendarId,
    calendarOff: calendarOff,
    updatedAt: now,
  });
};

const getReservationItemInfo = async (categoryId) => {
  let params:any = null;
  // 分類ID「all」で全件取得
  if (categoryId === 'all') {
    params = {
      query: 'partitionKey = :pk',
      mapping: {
        [':pk']: 'reservationItems',
      },
    };
  } else {
    params = {
      query: 'partitionKey = :pk and begins_with(sortKey,:cid)',
      mapping: {
        [':pk']: 'reservationItems',
        [':cid']: categoryId,
      },
    };
  }
  return await getTable().queryItems(params);
};

const upsertReservationItemInfo = async (params) => {
  const currentUnixTime = getCurrentUnixTime();
  let deletedAt:any = null;
  if (params.delete) {
    deletedAt = params.deletedAt ? params.deletedAt : currentUnixTime;
  }
  await getTable().putItem({
    partitionKey: 'reservationItems',
    sortKey: params.sortKey,
    categoryId: params.categoryId,
    itemId: params.itemId,
    calendarId: params.calendarId,
    itemName: params.itemName,
    cost: params.cost,
    updatedAt: currentUnixTime,
    deletedAt: deletedAt,
  });
};

export {
  updateSchedule,
  getSchedulesOfCalendar,
  getAllCalendars,
  getAllCategories,
  getSingleCategory,
  filterCategories,
  saveCategory,
  updateCategoryOrder,
  getAllTags,
  getFixedCategories,
  getSurveyResultCategories,
  getCalendarInfo,
  getGlobalDisplaySettings,
  saveGlobalDisplaySettings,
  getScheduleForDays,
  findCategoriesByCalendar,
  saveCalendar,
  updateCategoryLink,
  deleteCalendarWithSchedules,
  deleteCategory,
  getFutureSchedules,
  saveCategoriesBatch,
  decrementReservation,
  updateComa,
  tryToPlaceReservation,
  updateDayOff,
  getReservationControlInfo,
  upsertReservationControlInfo,
  execReservationControl,
  updateCalendarOff,
  getReservationItemInfo,
  upsertReservationItemInfo,
  checkReservationPossible,
};
