import {Context} from "aws-lambda";
import * as PlatformAsyncFunctionController from './controllers/platform-async-function.js'
import '../common/admin-api/admin-lambda-init.js'
import {infoLog} from "../common/admin-api/utils/logger.js";
import {requestContext} from "../common/admin-api/utils/execution-context.js";


const PlatformAsyncFunctionMap = {
    ImportCsvAppending: PlatformAsyncFunctionController.importCsvAppendingHandler,
    MainAsyncLambdaImportInvoker: PlatformAsyncFunctionController.mainImportCsvLambdaInvoker,
    ExportCsvAppending: PlatformAsyncFunctionController.createCSVAppendingSearchCriteriaHandler,
};

const PlatformAsyncFunctionHandler = async (event, context) => {
    const eventName = Object.keys(event)[0];
    if (!(eventName in PlatformAsyncFunctionMap)) {
        infoLog('[ERROR] invoking PlatformAsyncFunction Handler without a supported event.');
    }

    infoLog(`async ${eventName} STARTED`, event);
    const response = await PlatformAsyncFunctionMap[eventName](event, context);
    infoLog('response', response);
    infoLog(`async ${eventName} FINISHED`);
    return response;
};


export const exportImportAsyncRunner = async (event: any, context: Context) => {
    requestContext.reset();
    if ('PlatformAsyncFunction' in event) {
        try {
            return await PlatformAsyncFunctionHandler(event.PlatformAsyncFunction, context);
        } catch (error: any) {
            infoLog('[ERROR]', error);
            return {result: 'ERROR'};
        }
    }
    throw new Error('Unknown event\n' + JSON.stringify(event, null, 2))
}
