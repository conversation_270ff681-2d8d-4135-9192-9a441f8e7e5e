/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *  - name: Survey
 *    description: 帳票管理API
 */

import { getUsername, getUserGroup } from '../../common/admin-api/utils/auth.js';
import _ from 'lodash';
import { admin as a, guest as g} from '../../common/admin-middlewares/access-guards.js';
import * as service from '../services/survey.js';
import * as surveyResultsService from '../services/survey-results.js';
import * as segmentDeliveryService from '../../distribution/old/services/segment-delivery.js';
import * as actionLogger from '../../platform/controllers/helpers/log-surveys.js';
import * as logger from '../../common/admin-api/utils/logger.js';
import { extractLastEvaluatedKey } from '../../platform/controllers/helpers/helpers.js';

/**
 * @openapi
 * components:
 *   schemas:
 *     survey:
 *       type: "object"
 *       properties:
 *         surveyId:
 *           type: "string"
 *         surveyStatus:
 *           type: "string"
 *         surveyTitle:
 *           type: "string"
 *         surveyType:
 *           type: "string"
 *         description:
 *           type: "string"
 *         endOfSurveyMessage:
 *           type: "string"
 *         endpointURL:
 *           type: "string"
 *
 */

/**
 * @openapi
 * /survey:
 *  post:
 *    summary: 帳票を作成する
 *    tags: [Survey]
 *    requestBody:
 *      required: true
 *      content:
 *        application/json:
 *          schema:
 *            $ref: '#/components/schemas/survey'
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 *              properties:
 *                result:
 *                  type: "string"
 *                data:
 *                  $ref: '#/components/schemas/survey'
 */
const create = async (req, res) => {
  const username = getUsername(req) || 'anonymous';
  const surveyConfigJson = req.body;

  if (!('categoriesPermissions' in surveyConfigJson)) {
    surveyConfigJson.categoriesPermissions = null;
  }
  if (!('endOfSurveyMessageType' in surveyConfigJson)) {
    surveyConfigJson.endOfSurveyMessageType = null;
  }
  if (!('deliveryMessageSetting' in surveyConfigJson)) {
    surveyConfigJson.deliveryMessageSetting = [];
  }

  const validationResult = service.inputValidation(surveyConfigJson);
  if (!validationResult) {
    logger.appLogInfo('[Error]: user request validation error');
    res.json({
      result: 'ERROR',
      errorMessage: 'バリデーションエラー',
    });
    return;
  }

  let result;
  try {
    result = await service.create(username, surveyConfigJson);
    await actionLogger.forConfig(surveyConfigJson).doLogCreate(result);
  } catch (e: any) {
    logger.appLogInfo(e);
    res.json({
      result: 'ERROR',
      errorMessage: 'Server Error',
    });
    return;
  }

  if (surveyConfigJson && surveyConfigJson.isAppending && surveyConfigJson.isAppending.value) {
    // insert survey_result
    const input = {
      partitionKey: result.surveyId,
      sortKey: 'counter',
      answerCode: 0,
    };
    try {
      await service.createSurveyResult(input);
    } catch (error: any) {
      logger.appLogInfo(error);
    }
  }

  res.json({
    result: 'OK',
    data: result,
  });
};

/**
 * @openapi
 * /survey:
 *  get:
 *    summary: 帳票一覧を返す
 *    tags: [Survey]
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 *              properties:
 *                result:
 *                  type: "string"
 *                lastEvaluatedKey:
 *                  type: "object"
 *                data:
 *                  type: "array"
 *                  items:
 *                    $ref: '#/components/schemas/survey'
 */
const getAll = async (req, res) => {
  try {
    const lastEvaluatedKey = req.query && req.query.lastEvaluatedKey ? extractLastEvaluatedKey(req) : undefined;
    const userGroup = getUserGroup(req);
    const mode = (req.query && req.query.download) || 'all'
    const result = await service.getAll(userGroup, lastEvaluatedKey, mode);
    res.json({
      result: 'OK',
      data: result.items,
      lastEvaluatedKey: result.lastEvaluatedKey,
    });
  } catch (err: any) {
    res.json({
      result: 'ERROR',
      errorMessage: err.message,
    });
  }
};

/**
 * @openapi
 * /survey/{surveyId}:
 *  get:
 *    summary: 帳票の詳細を返す
 *    tags: [Survey]
 *    parameters:
 *      - name: surveyId
 *        in: path
 *        description: 帳票ID
 *        required: true
 *        schema:
 *          type: string
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 *              properties:
 *                result:
 *                  type: "string"
 *                data:
 *                  type: "object"
 */
const get = async (req, res) => {
  const { surveyId } = req.params;
  const userGroup = getUserGroup(req);

  const result = await service.getBySurveyIdAndGroup(userGroup, surveyId);
  if (!result || Object.keys(result).length === 0) {
    return res.json({
      result: 'ERROR',
      errorMessage: '帳票の閲覧設定が他人によって変更された、またはデータが存在しません。',
    });
  }

  const permissions = await service.loadPermissions(surveyId);
  if (permissions) {
    result.categoriesPermissions = permissions;
  }
  res.json({
    result: 'OK',
    data: result,
  });
};

/**
 * @openapi
 * /survey/{surveyId}:
 *  put:
 *    summary: 帳票を更新する
 *    tags: [Survey]
 *    parameters:
 *      - name: surveyId
 *        in: path
 *        description: 帳票ID
 *        required: true
 *        schema:
 *          type: string
 *    requestBody:
 *      required: true
 *      content:
 *        application/json:
 *          schema:
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 *              properties:
 *                result:
 *                  type: "string"
 *                data:
 *                  type: "object"
 */
const update = async (req, res) => {
  const surveyConfigJson:any = req.body;
  if (!surveyConfigJson.categoriesPermissions) {
    delete surveyConfigJson.categoriesPermissions;
  }
  if (!surveyConfigJson.endOfSurveyMessageType) {
    delete surveyConfigJson.endOfSurveyMessageType;
  }
  if (!surveyConfigJson.deliveryMessageSetting) {
    delete surveyConfigJson.deliveryMessageSetting;
  }

  await actionLogger.forConfig(surveyConfigJson).doLogUpdate();

  const userGroup = getUserGroup(req);

  // 最新のデータ取得
  const surveyId = _.isObject(surveyConfigJson) && ('surveyId' in surveyConfigJson) ? (surveyConfigJson as any).surveyId : false;
  if (!surveyId) {
    return res.json({
      result: 'ERROR',
      errorMessage: '不正なデータです。',
    });
  }

  const latestData = await service.getBySurveyIdAndGroup(userGroup, surveyId);
  if (!latestData || Object.keys(latestData).length === 0) {
    return res.json({
      result: 'ERROR',
      errorMessage: '帳票の閲覧設定が他人によって変更されました、またはデータが存在しません。',
    });
  }

  // 排他エラーチェック
  if (surveyConfigJson.updatedAt !== latestData.updatedAt) {
    return res.json({
      result: 'ERROR',
      alreadyUpdated: true,
      errorMessage: '編集していたデータは、他の人によって変更されました。',
      data: latestData,
    });
  }

  // validation
  const validationResult = service.inputValidation(surveyConfigJson);
  if (!validationResult) {
    logger.appLogInfo('[Error]: user request validation error');
    res.json({
      result: 'ERROR',
      errorMessage: 'バリデーションエラー',
    });
    return;
  }

  // options の更新チェック
  const { updateOptions, isValid } = service.getUpdateOptions(surveyConfigJson, latestData);
  if (!isValid) {
    return res.json({
      result: 'ERROR',
      errorMessage: 'バリデーションエラー',
      details: {
        updateOptions: updateOptions,
        surveyConfigJson: surveyConfigJson,
        latestData: latestData,
      },
    });
  }

  for (const key in updateOptions) {
    if (Object.hasOwnProperty.call(updateOptions, key)) {
      const updateOption = updateOptions[key];
      for (let i = 0; i < surveyConfigJson.surveySchema.length; i++) {
        const surveyItem = surveyConfigJson.surveySchema[i];
        if (surveyItem.itemKey === key) {
          if (surveyItem.default) {
            // checkbox
            let newDefault = surveyItem.default;
            if (Array.isArray(surveyItem.default)) {
              newDefault = [];
              for (let j = 0; j < surveyItem.default.length; j++) {
                const val = surveyItem.default[j];
                if (surveyItem.options.find((e) => e === val)) {
                  newDefault.push(val);
                } else if (val === updateOption.from) {
                  newDefault.push(updateOption.to);
                }
              }
            } else if (surveyItem.default === updateOption.from) {
              // radio
              newDefault = updateOption.to;
            }
            surveyItem.default = newDefault;
          }

          if (surveyItem.input) {
            let newInput = surveyItem.input;
            if (Array.isArray(surveyItem.input)) {
              // checkbox
              newInput = [];
              for (let j = 0; j < surveyItem.input.length; j++) {
                const val = surveyItem.input[j];
                if (surveyItem.options.find((e) => e === val)) {
                  newInput.push(val);
                } else if (val === updateOption.from) {
                  newInput.push(updateOption.to);
                }
              }
            } else if (surveyItem.input === updateOption.from) {
              // radio
              newInput = updateOption.to;
            }
            surveyItem.input = newInput;
          }
        }
      }
    }
  }

  const username = getUsername(req) || 'anonymous';
  const configsPromise = service.update(username, surveyConfigJson);
  const resultsPromise = surveyResultsService.updateOptions(surveyId, updateOptions);
  const segmentDeliveryPromise = segmentDeliveryService.updateSurveyConfigOption(surveyId, updateOptions);

  const results = await Promise.all([configsPromise, resultsPromise, segmentDeliveryPromise]);

  res.json({
    result: 'OK',
    data: results[0],
  });
};

export const routes = (app) => {
  app.post('/', a(create));
  app.get('/', g(getAll));
  app.get('/:surveyId', g(get));
  app.put('/', a(update));
};
