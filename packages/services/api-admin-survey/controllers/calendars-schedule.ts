/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *  - name: CalendarsSchedule
 *    description: カレンダー管理API
 */

import * as services from '../services/calendars-schedules.js';
import * as validators from '../../platform/controllers/helpers/validators.js';
import { admin as a, member as m, guest as g } from '../../common/admin-middlewares/access-guards.js';

/**
 * @openapi
 * /calendars/schedules/{id}:
 *  get:
 *    summary: スケジュールを取得する
 *    tags: [CalendarsSchedule]
 *    parameters:
 *      - name: id
 *        in: path
 *        description: カレンダーID
 *        required: true
 *        schema:
 *          type: string
 */
const getSchedule = async (req, res) => {
  const { start_day, end_day, include_calendar_info } = req.query;
  const categoryId = decodeURIComponent(req.params['id']);
  validators.requireNotEmpty(categoryId, 'id');
  validators.requireMatchRegex(start_day, /\d{8}/, 'start_day');
  validators.requireMatchRegex(end_day, /\d{8}/, 'end_day');

  const schedules = await services.loadScheduleOfCategory(categoryId, start_day, end_day, {
    includeCalendarInfo: include_calendar_info !== undefined,
  });
  if (!schedules) {
    const msg = categoryId.startsWith('category')
      ? `Category [${categoryId}] has no linked calendar`
      : `Calendar [${categoryId}] was not found`;
    res.status(400).json({
      code: 'not_found',
      message: msg,
    });
  }
  res.status(200).json(schedules);
};

/**
 * @openapi
 * /calendars:
 *  get:
 *    summary: カレンダーの帳票一覧を取得する
 *    tags: [CalendarsSchedule]
 */

const getCalendars = async (req, res) => {
  res.status(200).json(await services.getCalendars());
};

/**
 * @openapi
 * /calendars/indo/{id}:
 *  get:
 *    summary:
 *    tags: [CalendarsSchedule]
 *    parameters:
 *      - name: id
 *        in: path
 *        description: カレンダーID
 *        required: true
 *        schema:
 *          type: string
 */
const getCalendarInfo = async (req, res) => {
  const id = decodeURIComponent(req.params['id']);
  validators.requireMatchRegex(id, /^(category|calendar)/, 'id');
  res.status(200).json(await services.loadCalendarInfo(id));
};

/**
 * @openapi
 * /calendars/info:
 *  post:
 *    summary:
 *    tags: [CalendarsSchedule]
 */
const upsertCalendar = async (req, res) => {
  const id = decodeURIComponent(req.params['id'] || '');
  const updated = await services.upsertCalendar(req.body, id);
  res.status(200).json(updated);
};

/**
 * @openapi
 * /calendars/schedules/{id}:
 *  post:
 *    summary: スケジュールを登録する
 *    tags: [CalendarsSchedule]
 *    parameters:
 *      - name: id
 *        in: path
 *        description: カレンダーID
 *        required: true
 *        schema:
 *          type: string
 */
const upsertSchedule = async (req, res) => {
  const id = decodeURIComponent(req.params['id']);
  validators.requireMatchRegex(id, /^(category|calendar)/, 'id');
  const patch = req.body.schedule;
  const coma = req.body.coma ? req.body.coma : [];
  const forceUpdate = req.query['mode'] === 'force_update';
  const report = await services.upsertSchedule(id, patch, coma, { force: forceUpdate });
  res.status(200).json(report);
};

/**
 * @openapi
 * /calendars/info/{id}:
 *  delete:
 *    summary:
 *    tags: [CalendarsSchedule]
 *    parameters:
 *      - name: id
 *        in: path
 *        description: カレンダーID
 *        required: true
 *        schema:
 *          type: string
 */
const deleteCalendar = async (req, res) => {
  const id = decodeURIComponent(req.params['id']);
  validators.requireMatchRegex(id, /^(category|calendar)/, 'id');
  const { mode } = req.query;
  const { ok, report } = await services.deleteCalendar(id, mode === 'force_delete');
  res.status(ok ? 200 : 400).json(report);
};

/**
 * @openapi
 * /calendars/create_comma_csv/{id}:
 *  post:
 *    summary:
 *    tags: [CalendarsSchedule]
 *    parameters:
 *      - name: id
 *        in: path
 *        description: カレンダーID
 *        required: true
 *        schema:
 *          type: string
 */
const createCommaCsv = async (req, res) => {
  const id = decodeURIComponent(req.params['id']);
  validators.requireMatchRegex(id, /^(calendar)/, 'id');

  const calendar = await services.loadCalendarInfo(id);

  const result = await services.createCSV(calendar);

  res.json(result);
};

/**
 * @openapi
 * /calendars/import_comma/{id}:
 *  post:
 *    summary:
 *    tags: [CalendarsSchedule]
 */
const importCommaValues = async (req, res) => {
  const calendarId = decodeURIComponent(req.params['id']);
  validators.requireMatchRegex(calendarId, /^(calendar)/, 'id');

  const coma = req.body.coma ? req.body.coma : [];

  const result = await services.updateCalendarComaList(calendarId, coma);

  res.json(result);
};

/**
 * @openapi
 * /calendars/update_day_off/{id}:
 *  get:
 *    summary:
 *    tags: [CalendarsSchedule]
 *    parameters:
 *      - name: id
 *        in: path
 *        description: カレンダーID
 *        required: true
 *        schema:
 *          type: string
 */
const updateDayOff = async (req, res) => {
  const calendarId = decodeURIComponent(req.params['id']);
  const updateDate = req.body.dt;
  const dayOff = req.body.dayOff;
  validators.requireMatchRegex(calendarId, /^(calendar)/, 'id');
  validators.requireMatchRegex(dayOff, /^(0|1)/, 'dayOff');
  validators.requireMatchRegex(updateDate, /^\d{8}$/, 'updateDate');
  await services.updateDayOff(calendarId, updateDate, dayOff);
  res.status(200).json({ result: 'OK' });
};

/**
 * @openapi
 * /calendars/control_info/{id}:
 *  get:
 *    summary:
 *    tags: [CalendarsSchedule]
 *    parameters:
 *      - name: id
 *        in: path
 *        description: カレンダーID
 *        required: true
 *        schema:
 *          type: string
 */
const getReservationControlInfo = async (req, res) => {
  const calendarId = decodeURIComponent(req.params['id']);
  res.status(200).json(await services.loadReservationControlInfo(calendarId));
};

/**
 * @openapi
 * /calendars/control_info:
 *  post:
 *    summary:
 *    tags: [CalendarsSchedule]
 */
const upsertReservationControlInfo = async (req, res) => {
  const updateData = req.body;
  res.status(200).json(await services.upsertReservationControlInfo(updateData));
};

/**
 * @openapi
 * /calendars/control_exec:
 *  post:
 *    summary:
 *    tags: [CalendarsSchedule]
 */
const execReservationControl = async (req, res) => {
  res.status(200).json(await services.execReservationControl());
};

/**
 * @openapi
 * /calendars/update_calendar_off/{id}:
 *  post:
 *    summary:
 *    tags: [CalendarsSchedule]
 *    parameters:
 *      - name: id
 *        in: path
 *        description: カレンダーID
 *        required: true
 *        schema:
 *          type: string
 */
const updateCalendarOff = async (req, res) => {
  const calendarId = decodeURIComponent(req.params['id']);
  const calendarOff = req.body.calendarOff;
  validators.requireMatchRegex(calendarId, /^(calendar)/, 'id');
  validators.requireMatchRegex(calendarOff, /^(0|1)/, 'calendarOff');
  await services.updateCalendarOff(calendarId, calendarOff);
  res.status(200).json({ result: 'OK' });
};

/**
 * @openapi
 * /calendars/download_calendar_csv/{id}:
 *  get:
 *    summary:
 *    tags: [CalendarsSchedule]
 *    parameters:
 *      - name: id
 *        in: path
 *        description: カレンダーID
 *        required: true
 *        schema:
 *          type: string
 */
const downloadCalendarCsv = async (req, res) => {
  const categoryId = decodeURIComponent(req.params['id']);
  const { startDay, endDay } = req.query;
  validators.requireNotEmpty(categoryId, 'id');
  validators.requireMatchRegex(startDay, /^\d{8}$/, 'startDay');
  validators.requireMatchRegex(endDay, /^\d{8}$/, 'endDay');
  const url = await services.downloadCalendarCsv(categoryId, startDay, endDay);
  res.json({
    result: 'OK',
    data: { url },
  });
};

/**
 * @openapi
 * /calendars/item_info/{id}:
 *  get:
 *    summary:
 *    tags: [CalendarsSchedule]
 */
const getReservationItemInfo = async (req, res) => {
  const categoryId = decodeURIComponent(req.params['id']);
  res.status(200).json(await services.loadReservationItemInfo(categoryId));
};

/**
 * @openapi
 * /calendars/item_info:
 *  post:
 *    summary:
 *    tags: [CalendarsSchedule]
 */
const upsertReservationItemInfo = async (req, res) => {
  const updateData = req.body;
  res.status(200).json(await services.upsertReservationItemInfo(updateData));
};

export const routes = (app) => {
  app.get('/get_calendars', g(getCalendars));
  app.post('/create_comma_csv/:id', m(createCommaCsv));
  app.post('/import_comma/:id', m(importCommaValues));
  app.get('/schedules/:id', g(getSchedule));
  app.post('/schedules/:id', m(upsertSchedule));
  app.get('/info/:id', g(getCalendarInfo));
  app.post('/info', a(upsertCalendar));
  app.post('/info/:id', m(upsertCalendar));
  app.delete('/info/:id', a(deleteCalendar));
  app.post('/update_day_off/:id', a(updateDayOff));
  app.get('/control_info/:id', g(getReservationControlInfo));
  app.post('/control_info', a(upsertReservationControlInfo));
  app.post('/control_exec', execReservationControl);
  app.post('/update_calendar_off/:id', a(updateCalendarOff));
  app.get('/download_calendar_csv/:id', m(downloadCalendarCsv));
  app.get('/item_info/:id', g(getReservationItemInfo));
  app.post('/item_info', a(upsertReservationItemInfo));
};
