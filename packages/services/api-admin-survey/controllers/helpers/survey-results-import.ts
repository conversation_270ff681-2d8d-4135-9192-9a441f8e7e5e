/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
const statusValidationErrorMsg = 'CSVインポートで「キャンセル」、「取り消し」から他のステータスへの変更、または他のステータスから「キャンセル」、「取り消し」への変更は出来ません。';
const statusValidator = (currentStatus, newStatus) => {
  if (!newStatus) {
    return {
      allowed: true,
    }
  }
  if (currentStatus === '取り消し' || newStatus === '取り消し') {
    return {
      allowed: false,
      message: statusValidationErrorMsg,
    }
  }
  if (currentStatus === 'キャンセル' || newStatus === 'キャンセル') {
    return {
      allowed: false,
      message: statusValidationErrorMsg,
    }
  }

  return {
    allowed: true,
    message: undefined,
  }
}

const getResultStatus = (surveyResultRecords) => {
  if (
    !surveyResultRecords ||
    !Array.isArray(surveyResultRecords) ||
    surveyResultRecords.length === 0) {
    return undefined;
  }

  return surveyResultRecords[0].check;
}

export {
  statusValidator,
  getResultStatus
}
