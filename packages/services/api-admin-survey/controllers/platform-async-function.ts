/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import {SurveyResultsHelper} from '../services/survey-results/search/sr_index/sr_helper.js';

import moment from 'moment';
import _ from 'lodash';

import service from '../../platform/services/platform-async-function.js';
import * as surveyResultsService from '../services/survey-results.js';
import * as surveyConfigService from '../services/survey.js';
import * as exportSurveyResultsService from './helpers/export-survey-results.js';
import {SurveyResultsValidator} from '../services/survey-results-validator.js';
import {SurveyResultsImportReportHelper} from '../utils/survey-results-import-helper.js';
import {SurveyResultsHistoryHelper} from '../utils/survey-results-history-helper.js';
import * as importHelpers from './helpers/survey-results-import.js';
import {infoLog} from '../../common/admin-api/utils/logger.js';
import {removeElementFromArray, sleep} from '../../common/admin-api/utils/util.js';

const STATUS_UPDATE_ITEM_COUNT_THRESHOLD = 100; // update status every 100 items

moment.locale('ja');

const importCsvAppendingHandler = async (event, context) => {
  const reportHelper = new SurveyResultsImportReportHelper();
  let toUpdate = [];
  let toCreate = [];
  let overallError = '';
  let toUpdateAndToCreateLength = 0;
  let expectedTotal = 0;
  let finishUpdateResult:any = null;

  if (!event.ImportCsvAppending.surveyId || !event.ImportCsvAppending.bucketKey) {
    infoLog('Missing {surveyId} or {bucketKey} attribute');
    await service.importCsvAppendingStatusUpdate('{username}', '{surveyId}', 'ERROR', {
      result: 'ERROR',
      errorMessage: 'Missing {surveyId} or {bucketKey} attribute',
    });
    return {
      result: 'ERROR',
      errorMessage: 'Missing {surveyId} or {bucketKey} attribute',
    };
  }

  const { username, surveyId, bucketKey, start, end } = event.ImportCsvAppending;
  infoLog(
    '[CSV_IMPORT_DESCRIPTION] Starting individual import function from CSV file index: ' + start + ' to ' + end
  );
  try {
    const s3Response = await service.importCsvAppendingFetchFromS3(username, surveyId, bucketKey);

    const s3SurveyId = s3Response.params.surveyId;
    toUpdate = s3Response.body.toUpdate;
    toCreate = s3Response.body.toCreate;
    expectedTotal = toUpdate.length + toCreate.length;

    const filteredResult = filterCsvAppendingDataBeforeImport(toUpdate, toCreate, start, end);

    toUpdate = filteredResult.toUpdate;
    toCreate = filteredResult.toCreate;
    toUpdateAndToCreateLength = filteredResult.toUpdate.length + filteredResult.toCreate.length;

    const importHandlerResponse = await importCsvAppendingSaveToDbHandler(
      username,
      s3SurveyId,
      filteredResult.toUpdate,
      filteredResult.toCreate,
      reportHelper,
      context
    );

    finishUpdateResult = await service.importCsvAppendingStatusUpdate(
      username,
      surveyId,
      'FINISHED',
      null,
      expectedTotal
    );
  } catch (error: any) {
    infoLog('[ERROR] ImportCsvAppending: ');
    infoLog(error);
    overallError =
      toUpdateAndToCreateLength === 0
        ? 'インポート項目が見つけられませんでした。'
        : error.code
        ? error.code
        : error.message
        ? error.message
        : error.errorMessage
        ? error.errorMessage
        : error.error_message
        ? error.error_message
        : error;
    await service.importCsvAppendingStatusUpdate(username, surveyId, 'ERROR', {
      result: 'ERROR',
      errorMessage: overallError,
    });
  }

  try {
    if (overallError !== '') {
      if (toUpdateAndToCreateLength === 0) {
        //could not read data for rows....
        reportHelper.addFromListOfRowNumbers(
          event.ImportCsvAppending.rows,
          event.ImportCsvAppending.partitionKeyMap,
          'error',
          overallError
        );
        await service.importCsvAppendingStatusUpdate(username, surveyId, 'UPDATED', {
          result: 'OK',
          stats: {
            total: event.ImportCsvAppending.rows.length,
            successCount: 0,
            errorCount: event.ImportCsvAppending.rows.length,
          },
        });
        finishUpdateResult = await service.importCsvAppendingStatusUpdate(
          username,
          surveyId,
          'FINISHED',
          null,
          expectedTotal
        );
      } else {
        //an unexpected error occured, no way to tell which values were saved correctly...
        reportHelper.addSeveralToReport(toUpdate, 'error', overallError);
        reportHelper.addSeveralToReport(toCreate, 'error', overallError);
        await service.importCsvAppendingStatusUpdate(username, surveyId, 'UPDATED', {
          result: 'OK',
          stats: {
            total: toUpdate.length + toCreate.length,
            successCount: 0,
            errorCount: toUpdate.length + toCreate.length,
          },
        });
        finishUpdateResult = await service.importCsvAppendingStatusUpdate(
          username,
          surveyId,
          'FINISHED',
          null,
          expectedTotal
        );
      }
    }

    if (finishUpdateResult && finishUpdateResult.result === 'OK' && finishUpdateResult.finishedImportWork) {
      //create the result report on finish
      const compileResult = await service.compileResultReportInS3(
        reportHelper,
        event.ImportCsvAppending.surveyId,
        event.ImportCsvAppending.totalLambdas
      );
      if (compileResult.result === 'CREATED') {
        await service.importCsvAppendingStatusUpdate(username, surveyId, 'REPORT', compileResult);
      }
    } else if (reportHelper.getRawReport().length > 0) {
      await service.saveImportReportToS3Partial(reportHelper.getRawReport(), event.ImportCsvAppending.surveyId);
    }
    //save counter that report was finished for instance
    await service.importCsvAppendingStatusUpdate(username, surveyId, 'REPORT-PARTIAL');
  } catch (error: any) {
    infoLog('[ERROR] importCsvAppendingHandler: An unexpected error occured saving result report');
    infoLog(error);
    const errorMessage = error.code
      ? error.code
      : error.message
      ? error.message
      : error.errorMessage
      ? error.errorMessage
      : error.error_message
      ? error.error_message
      : error;
    await service.importCsvAppendingStatusUpdate(username, surveyId, 'ERROR', {
      result: 'ERROR',
      errorMessage: errorMessage,
    });
  }

  return {
    result: 'FINISHED',
  };
};

const filterCsvAppendingDataBeforeImport = (toUpdate, toCreate, startIndex, endIndex) => {
  const toUpdateFiltered:any[] = [];
  const toCreateFiltered:any[] = [];
  for (let i = startIndex; i < endIndex; i++) {
    if (i < toUpdate.length) {
      toUpdateFiltered.push(toUpdate[i]);
    } else {
      toCreateFiltered.push(toCreate[i - toUpdate.length]);
    }
  }

  return {
    toUpdate: toUpdateFiltered,
    toCreate: toCreateFiltered,
  };
};

const handleBatchPutSurveyResult = (response, caseArray, reportHelper) => {
  if (
    response.unprocessed_values &&
    Array.isArray(response.unprocessed_values) &&
    response.unprocessed_values.length > 0
  ) {
    reportHelper.addFromListOfPartitionKeys(
      response.unprocessed_values,
      caseArray,
      'unprocessed',
      '更新時にエラーが発生しました。再度インポートを行なってください'
    );
  }
  if (
    response.delete_failed_values &&
    Array.isArray(response.delete_failed_values) &&
    response.delete_failed_values.length > 0
  ) {
    reportHelper.addFromListOfPartitionKeys(
      response.delete_failed_values,
      caseArray,
      'delete-error',
      '更新時にエラーが発生しました。再度インポートを行なってください'
    );
  }
  const dataLosses = Object.keys(response.deletesReport);
  if (dataLosses.length > 0) {
    reportHelper.addFromListOfPartitionKeys(dataLosses, caseArray, 'data-loss', 'データ欠損');
  }
};

const runBatchImportForCaseArray = async (username, surveyId, caseArray, surveyHelper, reportHelper, context, resultsHistoryHelper, importIsForUpdate) => {
  const TIMEOUT_THRESHOLD = 5 * 60 * 1000; // 5 minutes

  const batches:any[] = [];
  let oneBatch = [];
  for (let i = 0; i < caseArray.length; ++i) {
    const item = caseArray[i];

    oneBatch.push(item.surveyResult);
    if ((i + 1) % STATUS_UPDATE_ITEM_COUNT_THRESHOLD === 0) {
      batches.push(oneBatch);
      oneBatch = [];
    }
  }

  if (oneBatch.length > 0) {
    batches.push(oneBatch);
    oneBatch = [];
  }

  // Copy list of batches for possible error reporting
  const unsavedBatches = Array.from(batches);

  for (let i = 0; i < batches.length; ++i) {
    let successCount = 0;
    let errorCount = 0;
    const itemCount = batches[i].length;

    //logging for debug
    // for(let tempCase of batches[i]){
    //   var partitionKey = tempCase.length > 0 ? tempCase[0].partitionKey : null;
    //   if(partitionKey){
    //     infoLog("[CSV_IMPORT_BATCH_PROCESSING] About to attempt batch survey write for item with paritionKey "
    //       + partitionKey + ". Saving " + tempCase.length + " results");
    //   }
    // }
    const batch = batches[i].flat();

    // Check possible timeout
    if (context.getRemainingTimeInMillis() <= TIMEOUT_THRESHOLD) {
      // Report unsaved cases / items
      // Update error count
      // Stop batch import
      let itemErrorCount = 0;

      for (const notSavedBatch of unsavedBatches) {
        reportHelper.addFromListOfItems(
          notSavedBatch.flat(),
          caseArray,
          'timeout',
          'インポート機能がタイムアウトしました'
        );

        itemErrorCount += notSavedBatch.length;
      }

      await service.importCsvAppendingStatusUpdate(username, surveyId, 'UPDATED', {
        result: 'OK',
        stats: {
          total: itemErrorCount,
          successCount: 0,
          errorCount: itemErrorCount,
        },
      });

      break;
    }

    let haveDataLoss = false;
    try {
      surveyHelper.setIndexValues(batch);
      const response = await batchPutSurveyResultRetry(batch);

      successCount = itemCount - response.total_error_count;
      errorCount = response.total_error_count;
      const dataLosses = Object.keys(response.deletesReport);
      haveDataLoss = dataLosses.length > 0;

      await resultsHistoryHelper.saveCachedRecords(batch, response.unprocessed_questions, importIsForUpdate);

      handleBatchPutSurveyResult(response, caseArray, reportHelper);
    } catch (error: any) {
      infoLog('[ERROR] ImportCsvAppending Calling batch put survery result: ', error.stack);
      reportHelper.addFromListOfItems(
        batch,
        caseArray,
        'error',
        '更新時にエラーが発生しました。システム管理者にお問い合わせください'
      );
      successCount = 0;
      errorCount = itemCount;
    }

    // Batch was saved, so remove from unsaved list
    removeElementFromArray(unsavedBatches, batches[i]);

    await service.importCsvAppendingStatusUpdate(username, surveyId, 'UPDATED', {
      result: haveDataLoss ? 'ERROR' : 'OK',
      errorMessage: haveDataLoss
        ? 'データが欠損しております。必ず結果レポートを確認し、対象データをご確認頂き、管理者へお問い合わせください。'
        : undefined,
      stats: {
        total: successCount + errorCount,
        successCount: successCount,
        errorCount: errorCount,
      },
    });
  }
};

const importCsvAppendingSaveToDbHandler = async (username, surveyId, toUpdate, toCreate, reportHelper, context) => {
  let skippedCount = 0;

  const configs:any = await surveyConfigService.getBySurveyId(surveyId);

  // check if surveyId exists in DB
  if (!configs) {
    infoLog('[CSV_IMPORT_PRE_VALIDATION] Config was not found from getBySurveyId');
    reportHelper.addSeveralToReport(toUpdate, 'error', '帳票は見つけられません。');
    reportHelper.addSeveralToReport(toCreate, 'error', '帳票は見つけられません。');
    await service.importCsvAppendingStatusUpdate(username, surveyId, 'UPDATED', {
      result: 'OK',
      stats: {
        total: toUpdate.length + toCreate.length,
        successCount: 0,
        errorCount: toUpdate.length + toCreate.length,
      },
    });
    return {
      result: 'ERROR',
      errorMessage: '帳票は見つけられません。',
    };
  }

  reportHelper.setSurveyConfig(configs);

  // survey has to be append-type
  if (!('isAppending' in configs) || !configs.isAppending || !configs.isAppending.value) {
    infoLog('[CSV_IMPORT_PRE_VALIDATION] Config was not an appending type survey');
    reportHelper.addSeveralToReport(toUpdate, 'error', `帳票=${surveyId}は追加型ではないです。`);
    reportHelper.addSeveralToReport(toCreate, 'error', `帳票=${surveyId}は追加型ではないです。`);
    await service.importCsvAppendingStatusUpdate(username, surveyId, 'UPDATED', {
      result: 'OK',
      stats: {
        total: toUpdate.length + toCreate.length,
        successCount: 0,
        errorCount: toUpdate.length + toCreate.length,
      },
    });
    return {
      result: 'ERROR',
      errorMessage: `surveyId=${surveyId}は追加型ではないです。`,
    };
  }

  const resultsHistoryHelper = new SurveyResultsHistoryHelper();

  // check toUpdate items (old items that already exists in DB)
  if (toUpdate && Array.isArray(toUpdate) && toUpdate.length > 0) {
    const categoryList:any = {};

    // check if item exist in DB AND if it is the latest data
    for (let i = toUpdate.length - 1; i >= 0; i--) {
      const partitionKey = toUpdate[i].partitionKey;

      // check if item exist in DB
      const latestData = await surveyResultsService.getByPartitionKey(partitionKey);
      if (!latestData || Object.keys(latestData).length === 0) {
        reportHelper.addOneToReport(toUpdate[i], 'skip', `partitionKey=${partitionKey}が存在しません。`);
        toUpdate.splice(i, 1);
        continue;
      }

      // ========== Validate ==========
      const updatedRecord = toUpdate[i];
      updatedRecord.debug = {};
      const updatedRecordDebug = updatedRecord.debug;
      updatedRecordDebug.latestData = { ...latestData[0] };
      updatedRecordDebug.surveyId = updatedRecordDebug.latestData.surveyId;
      const surveyConfigList = await surveyResultsService.getSurveyConfig(updatedRecordDebug.surveyId);
      let surveyConfig:any = {};
      if (surveyConfigList.length > 0) {
        surveyConfig = surveyConfigList[0];
      }
      if (Object.keys(surveyConfig).length === 0) {
        surveyConfig.surveySchema = [];
      }

      updatedRecordDebug.surveyResult = updatedRecord.surveyResult;

      // Validate status was not updated from a valid state (未対応とか) to invalid state (キャンセルとか)
      // Or that status of キャンセル and 取り消し are not updated
      // As this will require updating the reservation which is not allowed during import
      const newStatus = importHelpers.getResultStatus(updatedRecord.surveyResult);
      const statusValidationResult = importHelpers.statusValidator(updatedRecordDebug.latestData.check, newStatus);
      if (!statusValidationResult.allowed) {
        reportHelper.addOneToReport(toUpdate[i], 'skip', statusValidationResult.message);
        toUpdate.splice(i, 1);
        continue;
      }

      // check if item is latest data
      if (toUpdate[i].updatedAt < latestData[0].updatedAt) {
        reportHelper.addOneToReport(
          toUpdate[i],
          'skip',
          `partitionKey=${partitionKey}は、他の人によって変更されました。`
        );
        toUpdate.splice(i, 1);
        continue;
      }

      // validate all items of this partitionKey
      const validator = new SurveyResultsValidator(configs, toUpdate[i].surveyResult);
      if (!validator.validate()) {
        reportHelper.addOneToReport(toUpdate[i], 'skip', validator.getErrorMessageAsString());
        toUpdate.splice(i, 1);
        continue;
      }

      // Look into updating data to see if reservation item is included
      // If it is, then check if a reservation was already made and perform a partial-skip by
      // overwriting the intended import value with the existing reservation record
      let reservationItemIncludedInImport = false;
      let foundErrorValidatingReservation = false;
      for (let innerIndex = updatedRecord.surveyResult.length - 1; innerIndex >= 0; innerIndex--) {
        const updatedItem = updatedRecord.surveyResult[innerIndex];
        const itemConfig = await getByItemKey(surveyConfig.surveySchema, updatedItem.itemKey);

        if (itemConfig) {
          switch (itemConfig.type) {
            case 'reservation': {
              reservationItemIncludedInImport = true;

              const existingReservationItem = latestData.find((obj) => obj.itemKey === updatedItem.itemKey);

              //If there is data existing for reservation item
              if (existingReservationItem) {
                const existingReservationValue = existingReservationItem.value;
                //元データに日時情報入っている場合、更新スキップ
                const existingCategoryIdIndex = existingReservationValue.indexOf('|');
                if (existingCategoryIdIndex > 0) {
                  reportHelper.addToReportIfValuesDiffer(
                    updatedItem.value,
                    existingReservationItem.value,
                    toUpdate[i],
                    'partial-skip',
                    'カテゴリIDか予約情報の設定が変更されているため、更新できませんでした。管理画面から個別に修正してください。'
                  );
                  existingReservationItem.updatedAt = updatedItem.updatedAt;
                  existingReservationItem.check = updatedItem.check;
                  existingReservationItem.note = updatedItem.note;
                  updatedRecord.surveyResult[innerIndex] = existingReservationItem;
                  continue;
                }
                //元データに付加情報入っている場合、更新スキップ
                const existingAdditionIndex = existingReservationValue.indexOf('_');
                if (existingAdditionIndex > 0) {
                  reportHelper.addToReportIfValuesDiffer(
                    updatedItem.value,
                    existingReservationItem.value,
                    toUpdate[i],
                    'partial-skip',
                    'カテゴリIDか予約情報の設定が変更されているため、更新できませんでした。管理画面から個別に修正してください。'
                  );
                  existingReservationItem.updatedAt = updatedItem.updatedAt;
                  existingReservationItem.check = updatedItem.check;
                  existingReservationItem.note = updatedItem.note;
                  updatedRecord.surveyResult[innerIndex] = existingReservationItem;
                  continue;
                }
              }

              // check existed or not
              let exist = false;
              const categoryIdFull = updatedItem.value;
              let categoryId = categoryIdFull;
              const categoryIdIndex = categoryId.indexOf('|');
              if (categoryIdIndex > 0) {
                categoryId = categoryId.substring(0, categoryIdIndex).split('_')[0];
              }
              if (categoryId) {
                categoryId = categoryId.split('_')[0];

                //removing reservation date time on update if previous not existing
                updatedItem.sortKey = updatedItem.sortKey.split('|')[0].split('_')[0];
                updatedItem.userSearchKey = updatedItem.userSearchKey.split('|')[0].split('_')[0];
                updatedItem.value = categoryId;

                if (categoryList[categoryId]) {
                  exist = true;
                } else {
                  const categoryData = await surveyResultsService.getCategory(categoryId);
                  if (categoryData) {
                    exist = true;
                    categoryList[categoryId] = categoryData;
                  }
                }
                if (exist === false) {
                  foundErrorValidatingReservation = true;
                  reportHelper.addOneToReport(toUpdate[i], 'skip', `categoryId=${categoryId}が存在しません。`);
                  toUpdate.splice(i, 1);
                  continue;
                }
              }
            }
              break;
            default:
              break;
          }
        }

        if (foundErrorValidatingReservation) {
          break;
        }
      }

      if (foundErrorValidatingReservation) {
        continue;
      }

      if (!reservationItemIncludedInImport) {
        //Import CSV file did not include reservation Item
        //If the reservation item in dynamodb has
        //日時情報 or 付加情報, throw an error
        const reservationConfig = surveyConfig.surveySchema.find((obj) => obj.type === 'reservation');
        if (reservationConfig) {
          const existingReservationItem = latestData.find((obj) => obj.itemKey === reservationConfig.itemKey);
          if (existingReservationItem) {
            //need to make sure the reservation is not deleted when updating other records
            existingReservationItem.updatedAt = toUpdate[i].updatedAt;
            if (
              toUpdate[i].surveyResult &&
              Array.isArray(toUpdate[i].surveyResult) &&
              toUpdate[i].surveyResult.length > 0
            ) {
              existingReservationItem.check = toUpdate[i].surveyResult[0].check;
              existingReservationItem.note = toUpdate[i].surveyResult[0].note;
            }
            updatedRecord.surveyResult.push(existingReservationItem);
          }
        }
      }

      // assign 'answerCode' value directly from backend (ignore values from CSV)
      toUpdate[i].surveyResult.forEach((item) => (item.answerCode = latestData[0].answerCode));

      // assign 'target' checkmark value as false by default (this will unmark all selected items in HOME)
      toUpdate[i].surveyResult.forEach((item) => (item.target = false));

      // append new update time
      const now = Math.floor(Date.now() / 1000);
      toUpdate[i].surveyResult.forEach((item) => {
        item.updatedAt = now;
        if (!item.check) {
          item['check'] = '未対応';
        }
      });

      resultsHistoryHelper.addUpdateSurveyResultToCache(username, toUpdate[i].surveyResult, latestData, configs);
    }
    // end of check for toUpdate items
  }

  // check toCreate items (= new items)
  if (toCreate && Array.isArray(toCreate) && toCreate.length > 0) {
    for (let i = toCreate.length - 1; i >= 0; i--) {
      // validate all items of this partitionKey
      const validator = new SurveyResultsValidator(configs, toCreate[i].surveyResult);
      if (!validator.validate()) {
        reportHelper.addOneToReport(toCreate[i], 'skip', validator.getErrorMessageAsString());
        toCreate.splice(i, 1);
        continue;
      }

      // (category validation)
      // note: currently, there should only be 1 category item per surveyconfig (aka configsCategory.length <= 1)
      let foundErrorValidatingReservation = false;
      const configsCategory = configs.surveySchema.filter((schemaItem) => schemaItem.type === 'reservation');
      for (let j = 0; j < configsCategory.length; ++j) {
        const toCreateCategoryItems = toCreate[i].surveyResult.filter(
          (item) => item.itemKey === configsCategory[j].itemKey
        );
        for (let k = 0; k < toCreateCategoryItems.length; ++k) {
          const categoryIdFull = toCreateCategoryItems[k].value;

          let categoryId = categoryIdFull;
          const categoryIdIndex = categoryId.indexOf('|');
          if (categoryIdIndex > 0) {
            categoryId = categoryId.substring(0, categoryIdIndex).split('_')[0];
          }

          if (categoryId && categoryId.length > 0) {
            categoryId = categoryId.split('_')[0];

            //removing reservation date/time information on create
            toCreateCategoryItems[k].sortKey = toCreateCategoryItems[k].sortKey.split('|')[0].split('_')[0];
            toCreateCategoryItems[k].userSearchKey = toCreateCategoryItems[k].userSearchKey.split('|')[0].split('_')[0];
            toCreateCategoryItems[k].value = categoryId;
            const categoryData = await surveyResultsService.getCategory(categoryId);
            if (!categoryData) {
              foundErrorValidatingReservation = true;
              reportHelper.addOneToReport(toCreate[i], 'skip', `categoryId=${categoryId}が存在しません。`);
              toCreate.splice(i, 1);
              continue;
            }
          }
          if (foundErrorValidatingReservation) {
            break;
          }
        }
        if (foundErrorValidatingReservation) {
          break;
        }
      }

      if (foundErrorValidatingReservation) {
        continue;
      }

      // append create time and update time
      const now = Math.floor(Date.now() / 1000);
      toCreate[i].surveyResult.forEach((item) => {
        item.createdAt = now;
        item.updatedAt = now;
        if (!item.check) {
          item['check'] = '未対応';
        }
      });

      resultsHistoryHelper.addCreateSurveyResultToCache(username, toCreate[i].surveyResult, configs);
    }
    // end of check for toCreate items
  }

  skippedCount = reportHelper.fetchCountOfReportByResultStatus('skip');
  if (skippedCount > 0) {
    await service.importCsvAppendingStatusUpdate(username, surveyId, 'UPDATED', {
      result: 'OK',
      stats: {
        total: skippedCount,
        successCount: 0,
        errorCount: skippedCount,
      },
    });
  }

  const surveyHelper = new SurveyResultsHelper(configs, {resultsTable: ''});

  // Running import on toUpdate Items
  if (toUpdate.length > 0) {
    await runBatchImportForCaseArray(username, surveyId, toUpdate, surveyHelper, reportHelper, context, resultsHistoryHelper, true);
  }

  //Running import on toCreate items
  if (toCreate.length > 0) {
    await runBatchImportForCaseArray(username, surveyId, toCreate, surveyHelper, reportHelper, context, resultsHistoryHelper, false);
  }

  return {
    result: 'SUCCESS',
    data: {
      toUpdate: toUpdate,
      toCreate: toCreate,
    },
  };
};

const ALLOTED_SAVE_RETRY_ATTEMPTS = 10;
const batchPutSurveyResultRetry = async (batch) => {
  let caughtError:any = {};
  let attemptedUpdates = 0;
  do {
    try {
      const difference = _.random(1, 10);
      await sleep(500 * (attemptedUpdates + difference));

      const response = await surveyResultsService.batchPutSurveyResult(batch);
      return response;
    } catch (err: any) {
      attemptedUpdates++;
      infoLog(
        '[BATCH_PUT_SURVEY_RESULT_RETRY_ERROR] An error occured when calling batchPutSurveyResult. Retrying ' +
          attemptedUpdates
      );
      infoLog(err);
      caughtError = err;
    }
    //try again for ThrottlingException, etc...
  } while (caughtError.retryable && attemptedUpdates < ALLOTED_SAVE_RETRY_ATTEMPTS);

  if (attemptedUpdates === ALLOTED_SAVE_RETRY_ATTEMPTS) {
    infoLog(
      '[BATCH_PUT_SURVEY_RESULT_RETRY] Reached max number of allowable retry attempts for batch survey result'
    );
  }

  throw caughtError;
};

const getByItemKey = async (surveySchema, itemKey) => {
  if (!surveySchema || surveySchema.length === 0) {
    return null;
  }
  if (!itemKey) {
    return null;
  }

  for (const itemIndex in surveySchema) {
    const item = surveySchema[itemIndex];
    if (item.itemKey === itemKey) {
      return item;
    }
  }

  return null;
};

//////////////////////////////////////////////

const mainImportCsvLambdaInvoker = async (event, context) => {
  if (
    !event.MainAsyncLambdaImportInvoker.username ||
    !event.MainAsyncLambdaImportInvoker.surveyId ||
    !event.MainAsyncLambdaImportInvoker.bucketKey ||
    !event.MainAsyncLambdaImportInvoker.filename
  ) {
    infoLog('Missing {username} or {surveyId} or {bucketKey} or {filename} attribute');
    return {
      result: 'ERROR',
      errorMessage: 'Missing {username} or {surveyId} or {bucketKey} or {filename} attribute',
    };
  }

  const username = event.MainAsyncLambdaImportInvoker.username || 'anonymous';
  const surveyId = event.MainAsyncLambdaImportInvoker.surveyId;
  const bucketKey = event.MainAsyncLambdaImportInvoker.bucketKey;
  const filename = event.MainAsyncLambdaImportInvoker.filename;

  let response:any = null;
  try {
    response = await surveyResultsService.asyncImportCsvAppendingInvoker(username, surveyId, bucketKey, filename);
  } catch (error: any) {
    return {
      result: 'ERROR',
      errorMessage: error,
    };
  }

  return response;
};

// create url
const createCSVAppendingSearchCriteriaHandler = async (event, context) => {
  //bucketKeyFilename is the csv file saved in s3 to append to. Only exists when appending
  const {
    username = 'unknown',
    surveyId = 'unknown',
    searchCriteria = {},
    allColumns = [],
    bucketKeyFilename,
  } = event.ExportCsvAppending;

  const defaultStatusParams:any = {
    username: username,
    surveyId: surveyId,
    searchCriteria: searchCriteria,
    allColumns: allColumns,
    stats: {
      errorCount: 0,
      successCount: 0,
      total: 0,
    },
    errorMessage: '',
    downloadUrl: '',
  };
  const requiredAttributes = ['username', 'surveyId', 'searchCriteria', 'allColumns'];
  for (const attribute of requiredAttributes) {
    if (!(attribute in event.ExportCsvAppending)) {
      infoLog(`Missing required attribute: ${attribute}`);
      await exportSurveyResultsService.exportCsvAppendingStatusUpdate({
        ...defaultStatusParams,
        status: 'ERROR',
        errorMessage: `Missing required attribute: ${attribute}`,
      });
      return {
        result: 'ERROR',
        errorMessage: `Missing required attribute: ${attribute}`,
      };
    }
  }

  const surveyConfig = await surveyConfigService.getBySurveyId(surveyId);

  let result:any = {};
  try {
    result = await exportSurveyResultsService.asyncCreateCSVAppendingSearchCriteria(
      surveyConfig,
      searchCriteria,
      allColumns,
      bucketKeyFilename
    );
  } catch (error: any) {
    //need to add a try catch inside asyncCreateCSVAppendingSearchCriteria and try to pass back the number of
    //items that failed exporting. If there is a partitionKey in searchCriteria as well, we can try to continue
    infoLog('[ERROR] asyncCreateCSVAppendingSearchCriteria ' + error);
    infoLog(error);
    infoLog(error.stack);
    const errorMessage = error.code ? error.code : error.message ? error.message : '不明なエラー発生しました。';
    await exportSurveyResultsService.exportCsvAppendingStatusUpdate({
      ...defaultStatusParams,
      status: 'ERROR',
      errorMessage: errorMessage,
    });

    return {
      result: 'ERROR',
    };
  }

  if (result.exportFinished) {
    // save FINISHED status to DB
    await exportSurveyResultsService.exportCsvAppendingStatusUpdate({
      ...defaultStatusParams,
      status: 'FINISHED',
      stats: {
        errorCount: 0,
        successCount: result.numberOfCasesExported,
        total: result.numberOfCasesExported,
      },
      downloadUrl: result.url,
    });

    return {
      result: 'SUCCESS',
    };
  } else {
    //if export is not finished, call the same lambda with new search conditions
    try {
      //need to add code to update stats here
      await exportSurveyResultsService.exportCsvAppendingStatusUpdate({
        ...defaultStatusParams,
        status: 'UPDATED',
        stats: {
          errorCount: 0,
          successCount: result.numberOfCasesExported,
          total: result.numberOfCasesExported,
        },
      });
      await exportSurveyResultsService.runExportCsvAppendingLambdaAsync(
        username,
        surveyId,
        result.nextSearchCriteria,
        allColumns,
        result.bucketKeyFilename
      );
    } catch (error: any) {
      infoLog('[ERROR] an error occured trying to run the lambda for a second execution');
      infoLog(error);
      return {
        result: 'ERROR',
      };
    }

    return {
      result: 'SUCCESS',
    };
  }
};

export {
  importCsvAppendingHandler,
  mainImportCsvLambdaInvoker,
  createCSVAppendingSearchCriteriaHandler,
};
