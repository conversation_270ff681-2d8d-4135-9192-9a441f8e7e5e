/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *  - name: CalendarsCategories
 *    description: カレンダー管理API
 */

import * as services from '../services/calendars-categories.js';
import * as excep from '../../common/admin-api/utils/exceptions.js';
import { admin as a, member as m, guest as g } from '../../common/admin-middlewares/access-guards.js';
import cfg from '../../common/admin-api/config/static.js';
import c from '../../common/admin-api/config/constants.js';
import * as  locks from './helpers/dblocks.js';

const OPERATION_NAME_FOR_LOCK = 'categories';
const TEMPORARY_LOCKED_RESPONSE = {
  code: 'temporary_locked',
  result: 'ERROR',
  errorMessage: 'レコード更新中のためリクエストの処理をブロックしました。',
}

/**
 * @openapi
 * /calendars/categories:
 *  get:
 *    summary:
 *    tags: [CalendarsCategories]
 */
const getCategories = async (req, res) => {
  const filter = req.query;
  const doesSort = req.query.does_sort === 'true';
  const categories = await services.loadCategories(filter, doesSort);
  res.status(200).json(categories);
};

/**
 * @openapi
 * /calendars/categories/{id}:
 *  get:
 *    summary:
 *    tags: [CalendarsCategories]
 *    parameters:
 *      - name: id
 *        in: path
 *        description: カテゴリーID
 *        required: true
 *        schema:
 *          type: string
 */
const getSingleCategory = async (req, res) => {
  const id = decodeURIComponent(req.params.id || '');
  const categories = await services.loadCategories({ id });
  if (categories.length === 0) {
    res.status(400).json({
      code: 'not_found',
      message: `Category [${id}] was not found`,
    });
  } else {
    res.status(200).json(categories[0]);
  }
};

/**
 * @openapi
 * /calendars/categories/{id}:
 *  put:
 *    summary:
 *    tags: [CalendarsCategories]
 *    parameters:
 *      - name: id
 *        in: path
 *        description: カテゴリーID
 *        required: true
 *        schema:
 *          type: string
 */
const putCategory = async (req, res) => {
  const lockHelper = new locks.DbLockHelper(cfg.get(c.TABLE_SURVEY_CALENDARS), OPERATION_NAME_FOR_LOCK);
  if (await lockHelper.isLocked()) {
    return res.status(409).json(TEMPORARY_LOCKED_RESPONSE);
  }
  const id = decodeURIComponent(req.params['id'] || '');
  const result = await services.updateCategory(req.body, id);
  res.status(200).json(result);
};

/**
 * @openapi
 * /calendars/categories:
 *  post:
 *    summary:
 *    tags: [CalendarsCategories]
 */
const createCategories = async (req, res) => {
  const lockHelper = new locks.DbLockHelper(cfg.get(c.TABLE_SURVEY_CALENDARS), OPERATION_NAME_FOR_LOCK);
  if (await lockHelper.isLocked()) {
    return res.status(409).json(TEMPORARY_LOCKED_RESPONSE);
  }
  const report = await services.createCategories(req.body);
  res.status(200).json(report);
};

/**
 * @openapi
 * /calendars/categories_tree:
 *  get:
 *    summary:
 *    tags: [CalendarsCategories]
 */
const getCategoriesTree = async (req, res) => {
  const { include_display_settings } = req.query;
  const categories = await services.loadCategoriesTree({
    includeDisplaySettings: include_display_settings !== undefined,
  });
  res.status(200).json(categories);
};

/**
 * @openapi
 * /calendars/display_settings:
 *  get:
 *    summary:
 *    tags: [CalendarsCategories]
 */
const getDisplaySettings = async (req, res) => {
  const labels = await services.loadGlobalDisplaySettings();
  res.status(200).json(labels);
};

/**
 * @openapi
 * /calendars/display_settings:
 *  put:
 *    summary:
 *    tags: [CalendarsCategories]
 */
const putDisplaySettings = async (req, res) => {
  const result = await services.updateGlobalDisplaySettings(req.body);
  res.status(200).json(result);
};

/**
 * @openapi
 * /calendars/categories/{id}:
 *  delete:
 *    summary:
 *    tags: [CalendarsCategories]
 */
const deleteCategory = async (req, res) => {
  const lockHelper = new locks.DbLockHelper(cfg.get(c.TABLE_SURVEY_CALENDARS), OPERATION_NAME_FOR_LOCK);
  if (await lockHelper.isLocked()) {
    return res.status(409).json(TEMPORARY_LOCKED_RESPONSE);
  }
  const id = decodeURIComponent(req.params['id'] || '');
  const deletedCategory = await services.deleteCategory(id);
  res.status(200).json(deletedCategory);
};

/**
 * @openapi
 * /calendars/fixed_categories:
 *  get:
 *    summary:
 *    tags: [CalendarsCategories]
 */
const getFixedCategories = async (req, res) => {
  const fixedCategories = await services.getFixedCategories();
  console.log(fixedCategories);
  res.status(200).json(fixedCategories);
};

/**
 * @openapi
 * /calendars/survey_result_categories:
 *  get:
 *    summary:
 *    tags: [CalendarsCategories]
 */
const getSurveyResultCategories = async (req, res) => {
  const surveyResultCategories = await services.getSurveyResultCategories();
  res.status(200).json(surveyResultCategories);
};

/**
 * @openapi
 * /calendars/create_categories_csv:
 *  post:
 *    summary:
 *    tags: [CalendarsCategories]
 */
const createCategoriesCsv = async (req, res) => {
  const categories = await services.getCategoriesCSV();

  const url = await services.createCategoriesCSV(categories);

  res.json({
    result: 'OK',
    data: { url },
  });
};

/**
 * @openapi
 * /calendars/categories_order:
 *  put:
 *    summary:
 *    tags: [CalendarsCategories]
 */
const updateCategoriesOrder = async (req, res) => {
  const lockHelper = new locks.DbLockHelper(cfg.get(c.TABLE_SURVEY_CALENDARS), OPERATION_NAME_FOR_LOCK);
  if (await lockHelper.isLocked()) {
    return res.status(200).json(TEMPORARY_LOCKED_RESPONSE);
  }
  // NOTE:APIGatewayの30秒制限にかからないように非同期で実行
  (async () => {
    lockHelper.setStaleAfterMsecFromMinutes(10);
    if (await lockHelper.tryToLock()) {
      try {
        await services.updateCategoriesOrder(req.body);
      } catch (error: any) {
        console.error(error);
      } finally {
        await lockHelper.releaseToLock();
      }
    } else {
      throw new excep.BadRequest({
        code: TEMPORARY_LOCKED_RESPONSE.code,
        msg: TEMPORARY_LOCKED_RESPONSE.errorMessage,
      });
    }
  })();
  res.status(200).json({
    result: 'OK',
    message: '更新処理を受け付けました。',
  });
};
export const routes = (app) => {
  app.get('/categories', g(getCategories));
  app.put('/categories/:id', a(putCategory));
  app.get('/categories/:id', g(getSingleCategory));
  app.post('/categories', a(createCategories));
  app.get('/categories_tree', g(getCategoriesTree));
  app.get('/display_settings', g(getDisplaySettings));
  app.put('/display_settings', a(putDisplaySettings));
  app.delete('/categories/:id', a(deleteCategory));
  app.get('/fixed_categories', g(getFixedCategories));
  app.get('/survey_result_categories', g(getSurveyResultCategories));
  app.post('/create_categories_csv', m(createCategoriesCsv));
  app.put('/categories_order', a(updateCategoriesOrder))
};
