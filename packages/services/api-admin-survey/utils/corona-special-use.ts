/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import {
  create,
  findIndexSearcheableField,
  indexSearch,
  deleteSingleSurveyResult,
  getByPartitionKey,
} from '../services/survey-results.js';
import * as exts from '../services/calendars-ext.js';
import _ from 'lodash';
import moment from 'moment';
import { SurveyTransactionHelper } from './survey-transaction-helper.js';
import { SurveyResultsHistoryHelper } from './survey-results-history-helper.js';
import { SurveyConfigModel } from '../models/survey-config.js';
import { RadioGroupHeaderFieldType } from '../types/survey-config.js';

const VACCINATION_DOSE_TITLE = '接種回数';
const QUESTIONS_TO_TRIGGER_INPUT = ['接種実施日', '接種ワクチンのメーカー'];
const QUESTIONS_TO_TRIGGER_TYPE = ['sesshuJisshiDate', 'sesshuVaccineMaker'];
const QUESTION_TITLE_MATCHING = {
  接種実施日: '1回目の接種日',
  接種ワクチンのメーカー: '1回目の接種ワクチンのメーカー',
};
const VACCINATION_INTERVAL_TYPES = {
  DAYS: '日数',
  MONTHS: '月数',
}
const PREVIOUS_VACCINE_ITEM_TYPES = {
  MAKER: 'previousVaccineMaker',
  DATE: 'previousVaccineDate',
}
const VaccinationTemplateVersion = {
  NTimes: 'NTimes',
  OneAndTwoTimes: 'OneAndTwoTimes',
  Other: 'Other'
} as const;
type VaccinationTemplateVersion = typeof VaccinationTemplateVersion[keyof typeof VaccinationTemplateVersion];

const conditionsCheck = async (originalResults, surveyConfig) => {
  let itemKeysOfTrigger = [];
  for (const questionTitle of QUESTIONS_TO_TRIGGER_INPUT) {
    const item = surveyConfig.surveySchema.find((elem) => elem.title === questionTitle);
    if (item) {
      itemKeysOfTrigger.push(item.itemKey);
    }
  }
  if (itemKeysOfTrigger.length !== 2) {
    //no titles found in config, return
    itemKeysOfTrigger = [];
    for (const questionType of QUESTIONS_TO_TRIGGER_TYPE) {
      const subItem = surveyConfig.surveySchema.find((elem) => elem.type === questionType);
      if (subItem) {
        itemKeysOfTrigger.push(subItem.itemKey);
      }
    }
  }

  //Filter search key items
  const itemsToSearch:any[] = [];
  for (const question of surveyConfig.surveySchema) {
    if (question.isSearchable && question.isSearchable.value) {
      const item = originalResults.find((elem) => elem.itemKey === question.itemKey);
      if (item) {
        itemsToSearch.push(item);
      }
    }
  }

  const vaccinationDoseQuestion = surveyConfig.surveySchema.find((elem) => elem.title === VACCINATION_DOSE_TITLE);
  if (vaccinationDoseQuestion === undefined) {
    //if no 接種回数, can't determine 1回目 or 2回目, return
    return false;
  }

  let results = [];
  const indexSearcheableField = findIndexSearcheableField(surveyConfig);
  if (indexSearcheableField) {
    results = await indexSearch(surveyConfig, indexSearcheableField.itemKey, itemsToSearch);
  } else {
    //This is if there is no index searchable field but corona should always have one
    return false;
  }
  return { itemKeysOfTrigger, vaccinationDoseQuestion, results };
};

const autoReservation = async (autoReservationDate, value, categoryItem, updatedDate) => {
  try {
    let checkCategory = categoryItem.value.split('|')[0].includes('_')
    ? categoryItem.value.split('|')[0]
    : categoryItem.value.split('|')[0] + '_0';
    const slot = value.split('|')[2] ? value.split('|')[2] : null;
    const firstTimeCategoryInfo = await exts.getSingleCategory(value.split('|')[0]);
    const categoryInfo = await exts.getSingleCategory(checkCategory.split('|')[0]);
    let reserveCategoryId = categoryInfo.calendarId;
    if (categoryInfo && (!reserveCategoryId ||
      (reserveCategoryId && !reserveCategoryId.includes('calendar')) ||
      !categoryInfo.tag3 || (categoryInfo.tag3 && categoryInfo.tag3 === '')) &&
      firstTimeCategoryInfo.tag3 && firstTimeCategoryInfo.tag3 !== '') {
      // 2回目の指定された大分類・中分類と1回目の指定された小分類というカテゴリを探す
      const reserveCategory = await exts.filterCategories(categoryInfo.tag1, categoryInfo.tag2, firstTimeCategoryInfo.tag3);
      checkCategory = reserveCategory.length > 0 ? reserveCategory[0].sortKey + '_0' : checkCategory;
      reserveCategoryId = reserveCategory.length > 0 ? reserveCategory[0].calendarId : null;
    }
    const calendarInfo = reserveCategoryId ? await exts.getCalendarInfo(reserveCategoryId) : null;
    const date = moment(autoReservationDate).format('YYYYMMDD');
    const calendarOfAutoReservationDay = await exts.getScheduleForDays(reserveCategoryId, [date]);
    if (
      !calendarInfo ||
      !calendarOfAutoReservationDay ||
      (calendarOfAutoReservationDay && calendarOfAutoReservationDay.length === 0) ||
      calendarOfAutoReservationDay[0].dayOff === 1
    ) {
      return false;
    }
    const reservationItem = _.cloneDeep(categoryItem);
    let _val = '';
    let _slot:any = null;
    if (Object.keys(calendarOfAutoReservationDay[0].quotas).length === 0) {
      // no slot
      return false;
    } else if (
      slot &&
      calendarOfAutoReservationDay[0].quotas[slot] &&
      (!calendarOfAutoReservationDay[0].reservationCounts[slot] ||
        (calendarOfAutoReservationDay[0].reservationCounts[slot] &&
          calendarOfAutoReservationDay[0].quotas[slot] > calendarOfAutoReservationDay[0].reservationCounts[slot]))
    ) {
      // existed slot
      _val = `${checkCategory}|${date}|${slot}`;
      reservationItem['sortKey'] = `${categoryItem.itemKey}#${_val}`;
      reservationItem['userSearchKey'] = `${categoryItem.itemKey}#${_val}`;
      reservationItem['value'] = _val;
      _slot = slot;
    } else {
      // not existed coma or full reserved
      let canReservation = false;
      for (const i in Object.keys(calendarOfAutoReservationDay[0].quotas)) {
        _slot = Object.keys(calendarOfAutoReservationDay[0].quotas)[i];
        if (
          calendarOfAutoReservationDay[0].quotas[_slot] !== 0 &&
          (!calendarOfAutoReservationDay[0].reservationCounts[_slot] ||
            (calendarOfAutoReservationDay[0].reservationCounts[_slot] &&
              calendarOfAutoReservationDay[0].quotas[_slot] > calendarOfAutoReservationDay[0].reservationCounts[_slot]))
        ) {
          // can auto reservation with other slot
          canReservation = true;
          _val = `${checkCategory}|${date}|${_slot}`;
          reservationItem['sortKey'] = `${categoryItem.itemKey}#${_val}`;
          reservationItem['userSearchKey'] = `${categoryItem.itemKey}#${_val}`;
          reservationItem['value'] = _val;
          break;
        } else {
          continue;
        }
      }
      if (!canReservation) {
        // have not slot, which can be reservation
        return false;
      }
    }

    reservationItem['updatedAt'] = updatedDate;

    const surveyTransactionHelper = new SurveyTransactionHelper(10);
    surveyTransactionHelper.addSurveyResultsItem(reservationItem);
    surveyTransactionHelper.deleteSurveyResultsItem(categoryItem);
    await surveyTransactionHelper.addSurveyCalendarReservation(reservationItem);
    surveyTransactionHelper.debugTransactionRecord();
    await surveyTransactionHelper.executeTransaction(moment().unix());

    return calendarInfo && 'comaList' in calendarInfo && calendarInfo.comaList[_slot]
      ? `2回目の予約が「${categoryInfo.tag1}」>「${categoryInfo.tag2}」>「${
          reserveCategoryId === categoryInfo.calendarId ? categoryInfo.tag3 : firstTimeCategoryInfo.tag3}」の${
          moment(autoReservationDate).format('YYYY/MM/DD')} ${calendarInfo.comaList[_slot].start}-${
          calendarInfo.comaList[_slot].end
        }で取れました。`
      : '2回目の予約は成功しました。結果は2回目レコードを確認してください。';
  } catch (error: any) {
    console.log(error);
    return false;
  }
};

const autoReservationCheck = async (secondDoseCase, surveyConfig, originalResults, itemKeysOfTrigger, updatedDate) => {
  if (itemKeysOfTrigger.length !== 2) {
    return;
  }
  for (const key of itemKeysOfTrigger) {
    const item = originalResults.find((elem) => elem.itemKey === key);
    if (!item) {
      return;
    }
  }
  const matchingQuestion = surveyConfig.surveySchema.find((elem) => elem.type === 'reservation');
  const existingReserved = secondDoseCase.data.find((elem) => elem.itemKey === matchingQuestion.itemKey);
  const categoryCheck = originalResults.find((elem) => elem.itemKey === matchingQuestion.itemKey);
  if (existingReserved.value.includes('|') || !existingReserved.value.includes('category')) {
    return;
  } else {
    // check vaccination interval
    const _firstVaccineDate = originalResults.find((obj) => obj.itemKey === itemKeysOfTrigger[0]);
    const _firstTimeVaccine = originalResults.find((obj) => obj.itemKey === itemKeysOfTrigger[1]);
    if (!_firstVaccineDate || !_firstTimeVaccine) {
      return;
    }
    let _intervalTimes = 0;
    const _vaccine = surveyConfig.surveySchema.find((item) => item.itemKey === itemKeysOfTrigger[1]);
    const _interval = _vaccine.sectionOptions.find((ops) => ops.option.value === _firstTimeVaccine.value);
    if (_interval && _interval.groupheader && _interval.groupheader.value) {
      _intervalTimes = _interval.groupheader.value + 1;
    }
    const autoReservationDate = moment(_firstVaccineDate.value).add(_intervalTimes, 'days').format('YYYY-MM-DD');
    // check can be reservation
    return await autoReservation(autoReservationDate, categoryCheck.value, existingReserved, updatedDate);
  }
};

const tryToFillInSecondVaccinationElements = async (
  request,
  originalResults,
  surveyConfig,
  partitionKeyOfOriginal,
  autoReservation
) => {
  //Ignore editing of cancelled and deleted items. Although you can't edit deleted items anyway but
  if (originalResults[0].check === 'キャンセル' || originalResults[0].check === '取り消し') {
    return;
  }

  const _conditionsCheck = await conditionsCheck(originalResults, surveyConfig);
  if (!_conditionsCheck) {
    return;
  }
  const { itemKeysOfTrigger, vaccinationDoseQuestion, results } = _conditionsCheck;

  if (itemKeysOfTrigger.length === 0) {
    //no titles found in config, return
    return;
  }

  const surveyResultItemsOfOriginal = {};
  for (const key of itemKeysOfTrigger) {
    const item = originalResults.find((elem) => elem.itemKey === key);
    if (item) {
      surveyResultItemsOfOriginal[item.title] = item;
    }
  }

  //Check to make sure saved survey results are not 2回目 themselves
  const vaccinationDoseFromOriginal = originalResults.find((elem) => elem.itemKey === vaccinationDoseQuestion.itemKey);
  if (vaccinationDoseFromOriginal && vaccinationDoseFromOriginal.value === '2回目') {
    //survey results being saved are for second dose, no need to alter values here in this case
    return;
  }

  let secondDoseCase:any = null;
  for (const result of results) {
    const vaccinationDoseItem = result.data.find((item) => item.itemKey === vaccinationDoseQuestion.itemKey);
    if (
      vaccinationDoseItem &&
      vaccinationDoseItem.value === '2回目' &&
      result.partitionKey !== partitionKeyOfOriginal
    ) {
      secondDoseCase = result;
    }
  }

  //unable to find 2回目 case, return
  if (secondDoseCase === null) {
    return;
  }
  if (!_.isArray(secondDoseCase.data) || secondDoseCase.data.length <= 0) {
    return;
  }

  const oldResults = await getByPartitionKey(secondDoseCase.partitionKey);

  const updatedDate = Math.floor(Date.now() / 1000);
  const resultsToSave:any[] = [];

  for (let x = 0; x < QUESTIONS_TO_TRIGGER_INPUT.length; x++) {
    const savedItemOriginal = surveyResultItemsOfOriginal[QUESTIONS_TO_TRIGGER_INPUT[x]];
    if (savedItemOriginal) {
      const matchingQuestionTitle = QUESTION_TITLE_MATCHING[QUESTIONS_TO_TRIGGER_INPUT[x]];
      const matchingQuestion = surveyConfig.surveySchema.find((elem) => elem.title === matchingQuestionTitle);

      if (!matchingQuestion) {
        continue;
      }

      const existingItem = secondDoseCase.data.find((elem) => elem.itemKey === matchingQuestion.itemKey);
      if (existingItem) {
        //if existingItems value is different, copy, change value/sortKey/userSearchKey and create + delete old
        if (existingItem.value !== savedItemOriginal.value) {
          const itemToCreate = _.cloneDeep(existingItem);
          itemToCreate['sortKey'] = `${matchingQuestion.itemKey}#${savedItemOriginal.value}`;
          itemToCreate['userSearchKey'] = `${matchingQuestion.itemKey}#${savedItemOriginal.value}`;
          itemToCreate['value'] = savedItemOriginal.value;
          itemToCreate['updatedAt'] = updatedDate;

          await deleteSingleSurveyResult(existingItem);
          resultsToSave.push(itemToCreate);
        }
      } else {
        //create the new item
        const itemToCreate = _.cloneDeep(secondDoseCase.data[0]);
        itemToCreate['sortKey'] = `${matchingQuestion.itemKey}#${savedItemOriginal.value}`;
        itemToCreate['userSearchKey'] = `${matchingQuestion.itemKey}#${savedItemOriginal.value}`;
        itemToCreate['value'] = savedItemOriginal.value;
        itemToCreate['title'] = matchingQuestionTitle;
        itemToCreate['itemKey'] = matchingQuestion.itemKey;
        itemToCreate['updatedAt'] = updatedDate;

        delete itemToCreate.indexId;
        delete itemToCreate.indexSearchKey;

        resultsToSave.push(itemToCreate);
      }
    } else {
      //item was deleted from first dose
      //need to delete from second dose if exists
      const matchingQuestionTitle = QUESTION_TITLE_MATCHING[QUESTIONS_TO_TRIGGER_INPUT[x]];
      const matchingQuestion = surveyConfig.surveySchema.find((elem) => elem.title === matchingQuestionTitle);

      if (!matchingQuestion) {
        continue;
      }

      const existingItem = secondDoseCase.data.find((elem) => elem.itemKey === matchingQuestion.itemKey);
      if (existingItem) {
        await deleteSingleSurveyResult(existingItem);
      }
    }
  }

  //Update the remaining results of the second dose case (need to fetch again to get admin only values)
  const secondReservationResultsBeforeUpdate = await getByPartitionKey(secondDoseCase.data[0].partitionKey);
  for (const secondDoseResult of secondReservationResultsBeforeUpdate) {
    const itemInResultsToSaveArray = resultsToSave.find((elem) => elem.itemKey === secondDoseResult.itemKey);

    if (!itemInResultsToSaveArray) {
      const itemToUpdate = _.cloneDeep(secondDoseResult);
      itemToUpdate['updatedAt'] = updatedDate;
      resultsToSave.push(itemToUpdate);
    }
  }

  await create(resultsToSave);

  const fillSecondVaccinationResult:any = { result: 'OK' };
  if (autoReservation) {
    fillSecondVaccinationResult.autoReservationResult = await autoReservationCheck(
      secondDoseCase,
      surveyConfig,
      originalResults,
      itemKeysOfTrigger,
      updatedDate
    );
  }

  const mappedItemsForFrontEndDisplay = {
    updatedAt: updatedDate,
    partitionKey: secondDoseCase.data[0].partitionKey,
  };
  const secondReservationResults = await getByPartitionKey(secondDoseCase.data[0].partitionKey);
  for (const secondReservationResult of secondReservationResults) {
    const surveyQuestion = surveyConfig.surveySchema.find((item) => item.itemKey === secondReservationResult.itemKey);
    if (surveyQuestion && surveyQuestion.type === 'checkboxes') {
      if (!mappedItemsForFrontEndDisplay[secondReservationResult.itemKey]) {
        mappedItemsForFrontEndDisplay[secondReservationResult.itemKey] = secondReservationResult.value ? [secondReservationResult.value] : [];
      } else if (secondReservationResult.value) {
        mappedItemsForFrontEndDisplay[secondReservationResult.itemKey].push(secondReservationResult.value);
      }
    } else {
      mappedItemsForFrontEndDisplay[secondReservationResult.itemKey] = secondReservationResult.value;
    }
  }
  fillSecondVaccinationResult.secondCaseResults = mappedItemsForFrontEndDisplay;

  const resultsHistoryHelper = new SurveyResultsHistoryHelper();
  await resultsHistoryHelper.logSurveyResultsUpdated(request, secondReservationResults, oldResults, surveyConfig);

  return fillSecondVaccinationResult;
};

const checkFirstTimeInterval = async (originalResults, surveyConfig) => {
  const _conditionsCheck = await conditionsCheck(originalResults, surveyConfig);
  if (!_conditionsCheck) {
    return false;
  }
  const { itemKeysOfTrigger, vaccinationDoseQuestion, results } = _conditionsCheck;

  if (itemKeysOfTrigger.length !== 2) {
    return false;
  }

  const firstDoseCase:any[] = [];
  for (const result of results) {
    const vaccinationDoseItem = result.data.find((item) => item.itemKey === vaccinationDoseQuestion.itemKey);
    if (vaccinationDoseItem && vaccinationDoseItem.value === '1回目') {
      firstDoseCase.push(result);
    }
  }
  if (firstDoseCase.length !== 1) {
    // nodata check + duplicate & incorrect data check
    return false;
  }
  // check vaccination interval
  const _firstVaccineDate = firstDoseCase[0].data.find((obj) => obj.itemKey === itemKeysOfTrigger[0]);
  const _firstTimeVaccine = firstDoseCase[0].data.find((obj) => obj.itemKey === itemKeysOfTrigger[1]);
  if (!_firstVaccineDate || !_firstTimeVaccine) {
    return false;
  }
  let _intervalTimes = 0;
  const _vaccine = surveyConfig.surveySchema.find((item) => item.itemKey === itemKeysOfTrigger[1]);
  const _interval = _vaccine.sectionOptions.find((ops) => ops.option.value === _firstTimeVaccine.value);
  if (_interval && _interval.groupheader && _interval.groupheader.value) {
    _intervalTimes = _interval.groupheader.value;
  }
  const firstVaccineDate = moment(_firstVaccineDate.value, 'YYYY-MM-DD');

  const reservation = surveyConfig.surveySchema.find((item) => item.type === 'reservation');
  const _reservationDate = originalResults.find((elem) => elem.itemKey === reservation.itemKey);
  const _secondVaccineDate = _reservationDate.value.split('|')[1];
  const secondVaccineDate = moment(_secondVaccineDate, 'YYYY-MM-DD');

  if (secondVaccineDate.diff(firstVaccineDate, 'days') < _intervalTimes + 1) {
    return true;
  }
  return false;
};

const checkVaccinationIntervalByType = (surveyResults, surveyConfig: SurveyConfigModel, selectedReservationDate): boolean => {
  if (!selectedReservationDate) {
    return false;
  }
  // Assuming "vaccinationIntervalType", "previousVaccineMaker", vaccination interval, "previousVaccinDate" are always present in "新型コロナワクチン接種(N回目)"
  // Get the value of the "vaccinationIntervalType"
  const intervalType = surveyConfig.getVaccinationIntervalType();
  // Get the value of "previousVaccineMaker"
  const previousVaccineMakerItem = surveyConfig.getSchemaItemsByType(PREVIOUS_VACCINE_ITEM_TYPES.MAKER)[0] as RadioGroupHeaderFieldType;
  const previousVaccineMakerInput =
    surveyResults.find((result) => result.itemKey === previousVaccineMakerItem.itemKey).value;
  // Get the value of the vaccination interval
  const intervalOption = previousVaccineMakerItem.sectionOptions.find((obj) => obj.option.value === previousVaccineMakerInput);
  const intervalTimes = intervalOption?.groupheader?.value;
  // Get the value of "previousVaccinDate"
  const previousVaccineDateItem = surveyConfig.getSchemaItemsByType(PREVIOUS_VACCINE_ITEM_TYPES.DATE)[0];
  const previousVaccineDateInput =
    surveyResults.find((result) => result.itemKey === previousVaccineDateItem.itemKey).value;


  const _selectedReservationDate = moment(selectedReservationDate, 'YYYY-MM-DD');
  const previousVaccineDate = moment(previousVaccineDateInput, 'YYYY-MM-DD');
  // Check interval
  if (intervalType === VACCINATION_INTERVAL_TYPES.DAYS) {
    return _selectedReservationDate.diff(previousVaccineDate, 'days') < Number(intervalTimes) + 1;
  }
  if (intervalType === VACCINATION_INTERVAL_TYPES.MONTHS) {
    const previousVaccineDay = moment(previousVaccineDate).format('DD');
    const dateOfIntervalTimesLater = previousVaccineDate.add(intervalTimes, 'months');
    const intervalTimesLaterDay = moment(dateOfIntervalTimesLater).format('DD');

    if (intervalTimesLaterDay === previousVaccineDay) { // Nヶ月後の日と前回接種の日が同じ
      return _selectedReservationDate.isBefore(dateOfIntervalTimesLater);
    } else {
      return _selectedReservationDate.isSameOrBefore(dateOfIntervalTimesLater);
    }
  }

  return false;
}

const checkVaccinationInterval = async (
  version: VaccinationTemplateVersion,
  surveyResults: { [key: string]: string },
  surveyConfig: SurveyConfigModel,
  vaccinationDate: string
) => {
  switch(version) {
    case VaccinationTemplateVersion.OneAndTwoTimes: {
      // For the "新型コロナワクチン接種(1・2回目)" template
      const resultForV1 = await checkFirstTimeInterval(surveyResults, surveyConfig.getCached());
      if (resultForV1) {
        return '１回目と２回目接種間隔が規定の接種間隔よりも短いです。';
      }
      break;
    }
    case VaccinationTemplateVersion.NTimes: {
      // For the "新型コロナワクチン接種(N回目)" template
      const resultForV2 = checkVaccinationIntervalByType(surveyResults, surveyConfig, vaccinationDate);
      if (resultForV2) {
        return '前回接種日と予約日との間隔が規定の接種間隔期間を満たしていません。';
      }
      break;
    }
    default:
      break;
  }
  return null;
}

const getVaccinationTemplateVersion = (
  surveyConfig: SurveyConfigModel, surveyResults: { [key: string]: string }[]
): VaccinationTemplateVersion => {
  if (surveyConfig.getType() !== 'corona') {
    return VaccinationTemplateVersion.Other;
  }

  // 1・2回目テンプレート
  const countVaccineItemV1 = surveyConfig.getVaccineCountItemV1();
  if (countVaccineItemV1) {
    const vaccinationTime = surveyResults.find(result => result.itemKey === countVaccineItemV1.itemKey)?.value;
    const isSecondTime = vaccinationTime === '2回目';
    // かつ2回目接種
    if (isSecondTime) {
      return VaccinationTemplateVersion.OneAndTwoTimes;
    }
  }

  // N回目テンプレート かつ 2回目以降
  const countVaccineItemV2 = surveyConfig.getSchemaItemsByType('countVaccines')[0];
  if (Number(countVaccineItemV2?.default > 1)) {
    return VaccinationTemplateVersion.NTimes;
  }

  return VaccinationTemplateVersion.Other;
}

export {
  tryToFillInSecondVaccinationElements,
  checkFirstTimeInterval,
  checkVaccinationIntervalByType,
  checkVaccinationInterval,
  getVaccinationTemplateVersion,
};
