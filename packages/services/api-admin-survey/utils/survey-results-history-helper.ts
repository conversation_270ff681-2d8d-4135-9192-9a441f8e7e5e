/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import AWS from 'aws-sdk';
import moment from 'moment';
import { getEnvironmentCredentials } from '../../common/admin-api/utils/aws-helper.js';
import c from '../../common/admin-api/config/constants.js';
import config from '../../common/admin-api/config/static.js';

import { getUsername } from '../../common/admin-api/utils/auth.js';
import { infoLog } from '../../common/admin-api/utils/logger.js';

import * as calendarScheduleService from '../services/calendars-schedules.js';

class SurveyResultsHistoryHelper {
  private surveyResultsHistoryTable: string;
  private errorLogHeader: string;
  private cachedToUpdateMap: any;
  private cachedToCreateMap: any;
  constructor() {
    this.surveyResultsHistoryTable = config.get(c.TABLE_SURVEY_RESULTS_HISTORY);
    this.errorLogHeader = '[ERROR] SurveyResultsHistoryHelper';

    this.cachedToUpdateMap = new Map();
    this.cachedToCreateMap = new Map();
  }

  getDocumentClient() {
    return new AWS.DynamoDB.DocumentClient(getEnvironmentCredentials());
  }

  getTimeStampInMillis() {
    return Date.now();
  }

  addUpdateSurveyResultToCache(username, surveyResults, oldSurveyResults, surveyConfig) {
    try {
      const partitionKey = surveyResults[0].partitionKey;
      const toUpdateLogRecord:any = {
        itemKeyToValueMapping: {},
        totalSurveyResultsToUpdate: surveyResults.length,
        username: username,
        partitionKey: partitionKey
      }

      for (const question of surveyConfig.surveySchema) {
        const newResult = surveyResults.find((elem) => elem.itemKey === question.itemKey);
        const oldResult = oldSurveyResults.find((elem) => elem.itemKey === question.itemKey);
        if ((newResult && oldResult === undefined) ||
        (newResult && oldResult && newResult.value !== oldResult.value)) {
          //In new data but not old OR value is different = create OR update
          toUpdateLogRecord.itemKeyToValueMapping[question.itemKey] = { title: question.title, value: newResult.value };
        } else if (newResult === undefined && oldResult) {
          //In old data but not new = deleted
          toUpdateLogRecord.itemKeyToValueMapping[question.itemKey] = { title: question.title, value: c.SURVEY_RESULTS_HISTORY.deleted };
        }
      }

      if (surveyResults[0].check !== oldSurveyResults[0].check) {
        toUpdateLogRecord.check = surveyResults[0].check;
      }
      if (surveyResults[0].note !== oldSurveyResults[0].note && (surveyResults[0].note || oldSurveyResults[0].note)) {
        toUpdateLogRecord.note = surveyResults[0].note;
      }

      this.cachedToUpdateMap.set(partitionKey, toUpdateLogRecord);
    } catch (err: any) {
      infoLog(this.errorLogHeader, err.stack);
    }
  }

  async saveCachedRecords(surveyResultArray, unprocessedSurveyResults, isUpdate) {
    try {
      const partitionKeyList = new Set();
      for (const surveyResult of surveyResultArray) {
        partitionKeyList.add(surveyResult.partitionKey);
      }
      if (isUpdate) {
        for (const partitionKey of Array.from(partitionKeyList)) {
          const updateRecord = this.cachedToUpdateMap.get(partitionKey);
          const unprocessedItemsList = unprocessedSurveyResults.filter((elem) => elem.partitionKey === partitionKey);
          if (updateRecord && unprocessedItemsList.length === 0) {
            //no problems occured saving data, save record normally
            await this.saveCachedRecord(updateRecord, c.SURVEY_RESULTS_HISTORY.updated);
          } else if (updateRecord && unprocessedItemsList.length < updateRecord.totalSurveyResultsToUpdate) {
            //Some items were unprocessed, remove from updateRecord and save
            for (const unprocessed of unprocessedItemsList) {
              delete updateRecord.itemKeyToValueMapping[unprocessed.itemKey];
            }
            await this.saveCachedRecord(updateRecord, c.SURVEY_RESULTS_HISTORY.updated);
          } else {
            //if unprocessedItemsList.length === updateRecord.totalSurveyResultsToUpdate, nothing was updated
            await this.saveEmptyCachedRecord(updateRecord, c.SURVEY_RESULTS_HISTORY.updated);
          }
        }
      } else {
        for (const partitionKey of Array.from(partitionKeyList)) {
          const createRecord = this.cachedToCreateMap.get(partitionKey);
          const unprocessedItemsList = unprocessedSurveyResults.filter((elem) => elem.partitionKey === partitionKey);
          if (createRecord && unprocessedItemsList.length === 0) {
            //no problems occured saving data, save record normally
            await this.saveCachedRecord(createRecord, c.SURVEY_RESULTS_HISTORY.created);
          } else if (createRecord && unprocessedItemsList.length < createRecord.totalSurveyResultsToUpdate) {
            //Some items were unprocessed, remove from createRecord and save
            for (const unprocessed of unprocessedItemsList) {
              delete createRecord.itemKeyToValueMapping[unprocessed.itemKey];
            }
            await this.saveCachedRecord(createRecord, c.SURVEY_RESULTS_HISTORY.created);
          } else {
            //if unprocessedItemsList.length === createRecord.totalSurveyResultsToUpdate, nothing was created
            await this.saveEmptyCachedRecord(createRecord, c.SURVEY_RESULTS_HISTORY.created);
          }
        }
      }
    } catch (err: any) {
      infoLog(this.errorLogHeader, err.stack);
    }
  }

  async saveCachedRecord(cachedRecord, logType) {
    const logRecord:any = {
      surveyResultId: cachedRecord.partitionKey,
      logType: logType,
      user: cachedRecord.username,
      createdAt: this.getTimeStampInMillis()
    }

    let details = '';
    for (const key of Object.keys(cachedRecord.itemKeyToValueMapping)) {
      details += this.formatResultForDetails(cachedRecord.itemKeyToValueMapping[key].title,
      cachedRecord.itemKeyToValueMapping[key].value);
    }
    if (cachedRecord.check !== null && cachedRecord.check !== undefined) {
      details += this.formatResultForDetails('ステータス', cachedRecord.check);
    }
    if (cachedRecord.note !== null && cachedRecord.note !== undefined) {
      details += this.formatResultForDetails('管理メモ', cachedRecord.note);
    }
    if (details === '') {
      details = '-';
    }
    logRecord.details = details;

    const dbClient = this.getDocumentClient();
    await dbClient
      .put({
        TableName: this.surveyResultsHistoryTable,
        Item: logRecord,
      })
      .promise();
  }

  async saveEmptyCachedRecord(cachedRecord, logType) {
    const logRecord = {
      surveyResultId: cachedRecord.partitionKey,
      logType: logType,
      user: cachedRecord.username,
      createdAt: this.getTimeStampInMillis(),
      details: '-',
    }

    const dbClient = this.getDocumentClient();
    await dbClient
      .put({
        TableName: this.surveyResultsHistoryTable,
        Item: logRecord,
      })
      .promise();
  }

  addCreateSurveyResultToCache(username, surveyResults, surveyConfig) {
    try {
      const partitionKey = surveyResults[0].partitionKey;
      const toCreateLogRecord:any = {
        itemKeyToValueMapping: {},
        totalSurveyResultsToUpdate: surveyResults.length,
        username: username,
        partitionKey: partitionKey,
      }

      for (const schemaQuestion of surveyConfig.surveySchema) {
        const result = surveyResults.find((elem) => elem.itemKey === schemaQuestion.itemKey);
        if (result) {
          toCreateLogRecord.itemKeyToValueMapping[schemaQuestion.itemKey] = { title: schemaQuestion.title, value: result.value };
        }
      }

      if (surveyResults[0].check) {
        toCreateLogRecord.check = surveyResults[0].check;
      }
      if (surveyResults[0].note) {
        toCreateLogRecord.note = surveyResults[0].note;
      }

      this.cachedToCreateMap.set(partitionKey, toCreateLogRecord);
    } catch (err: any) {
      infoLog(this.errorLogHeader, err.stack);
    }
  }

  async logUserIdReset(req, partitionKey) {
    try {
      const logRecord = {
        surveyResultId: partitionKey,
        logType: c.SURVEY_RESULTS_HISTORY.idReset,
        details: '-',
      };
      await this.saveDatabaseRecord(req, logRecord);
    } catch (err: any) {
      infoLog(this.errorLogHeader, err.stack);
    }
  }

  async logUnicastMessage(req, partitionKey, result, title, messages) {
    try {
      const logRecord:any = {
        surveyResultId: partitionKey,
        logType: c.SURVEY_RESULTS_HISTORY.unicast,
      };
      logRecord.details = this.getUnicastMessageDetails(result, title, messages);
      await this.saveDatabaseRecord(req, logRecord);
    } catch (err: any) {
      infoLog(this.errorLogHeader, err.stack);
    }
  }

  getUnicastMessageDetails(result, title, messages) {
    let details = '';
    details += this.formatResultForDetails('結果', result === 'SUCCESS' ? '成功' : '失敗');
    details += this.formatResultForDetails('タイトル', title);
    for (let x = 0; x < messages.length; x++) {
      const message = messages[x];
      details += this.formatResultForDetails(`メッセージ${x + 1}`, message.type === 'text' ? message.text : '画像');
    }

    return details;
  }

  async logSurveyResultsCreated(req, surveyResults, surveyConfig) {
    try {
      const logRecord:any = {
        surveyResultId: surveyResults[0].partitionKey,
        logType: c.SURVEY_RESULTS_HISTORY.created,
      };
      logRecord.details = await this.getSurveyCreateDetails(surveyResults, surveyConfig);
      await this.saveDatabaseRecord(req, logRecord);
    } catch (err: any) {
      infoLog(this.errorLogHeader, err.stack);
    }
  }

  async getSurveyCreateDetails(surveyResults, surveyConfig) {
    let details = '';
    for (const schemaQuestion of surveyConfig.surveySchema) {
      const result = surveyResults.find((elem) => elem.itemKey === schemaQuestion.itemKey);
      if (result) {
        details += this.formatResultForDetails(schemaQuestion.title, result.value);
      }
    }

    const reservationItem = surveyConfig.surveySchema.find((elem) => elem.type === 'reservation');
    if (reservationItem) {
      const result = surveyResults.find((elem) => elem.itemKey === reservationItem.itemKey);
      if (result && result.value && result.value.indexOf('|') > -1) {
        details += await this.getReservationDateTimeInfo(result);
      }
    }
    if (surveyResults[0].check) {
      details += this.formatResultForDetails('ステータス', surveyResults[0].check);
    }
    if (surveyResults[0].note) {
      details += this.formatResultForDetails('管理メモ', surveyResults[0].note);
    }

    return details;
  }

  async logSurveyResultsUpdated(req, surveyResults, oldSurveyResults, surveyConfig) {
    try {
      const logRecord:any = {
        surveyResultId: surveyResults[0].partitionKey,
        logType: c.SURVEY_RESULTS_HISTORY.updated,
      }
      logRecord.details = await this.getSurveyUpdateDetails(surveyResults, oldSurveyResults, surveyConfig);
      await this.saveDatabaseRecord(req, logRecord);
    } catch (err: any) {
      infoLog(this.errorLogHeader, err.stack);
    }
  }

  async getSurveyUpdateDetails(surveyResults, oldSurveyResults, surveyConfig) {
    let details = '';

    for (const question of surveyConfig.surveySchema) {
      const newResult = surveyResults.find((elem) => elem.itemKey === question.itemKey);
      const oldResult = oldSurveyResults.find((elem) => elem.itemKey === question.itemKey);
      if ((newResult && oldResult === undefined) ||
      (newResult && oldResult && newResult.value !== oldResult.value)) {
        //In new data but not old OR value is different = create OR update
        details += this.formatResultForDetails(question.title, newResult.value);
      } else if (newResult === undefined && oldResult) {
        //In old data but not new = deleted
        details += this.formatResultForDetails(question.title, c.SURVEY_RESULTS_HISTORY.deleted);
      }
    }

    const reservationItem = surveyConfig.surveySchema.find((elem) => elem.type === 'reservation');
    if (reservationItem) {
      const newReservation = surveyResults.find((elem) => elem.itemKey === reservationItem.itemKey);
      const oldReservation = oldSurveyResults.find((elem) => elem.itemKey === reservationItem.itemKey);
      if ((newReservation && oldReservation === undefined)
      || (newReservation && oldReservation && newReservation.value !== oldReservation.value)
      && newReservation.value.indexOf('|') > -1) {
        details += await this.getReservationDateTimeInfo(newReservation);
      }
    }

    if (surveyResults[0].check !== oldSurveyResults[0].check) {
      details += this.formatResultForDetails('ステータス', surveyResults[0].check);
    }
    if (surveyResults[0].note !== oldSurveyResults[0].note && (surveyResults[0].note || oldSurveyResults[0].note)) {
      details += this.formatResultForDetails('管理メモ', surveyResults[0].note);
    }

    if (details === '') {
      return '-';
    }
    return details;
  }

  async getReservationDateTimeInfo(reservationItem) {
    let result = '';
    const splitReservationValue = reservationItem.value.split('|');
    const calendarInfo = await calendarScheduleService.loadCalendarInfo(splitReservationValue[0]);
    result += moment(splitReservationValue[1]).format('YYYY-MM-DD');
    if (calendarInfo && calendarInfo.comaList && splitReservationValue[2] in calendarInfo.comaList && calendarInfo.comaList[splitReservationValue[2]].start) {
      result += ' ' + calendarInfo.comaList[splitReservationValue[2]].start;
    }

    return this.formatResultForDetails('予約日時', result);
  }

  formatResultForDetails(title, value) {
    return `[${title}]\n${value}\n`;
  }

  async saveDatabaseRecord(req, record) {
    const userName = getUsername(req) || 'anonymous';
    const dbClient = this.getDocumentClient();
    record.user = userName;
    record.createdAt = this.getTimeStampInMillis();
    await dbClient
      .put({
        TableName: this.surveyResultsHistoryTable,
        Item: record,
      })
      .promise();
  }
}
export {
  SurveyResultsHistoryHelper,
};
