/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import Papa from 'papaparse';
import moment from 'moment';
import _ from 'lodash';

moment.locale('ja');

//for papaparse
const DELIMITER = ',';
const ENCODING = 'SJIS';

// 2-type of items: update item OR create item
// update item must have PARTITION_KEY, USER_ID, CREATED_AT, UPDATED_AT
// create item must NOT have (OR have empty value of) PARTITION_KEY, CREATED_AT, UPDATED_AT

// csv file headers (fields name)
const PARTITION_KEY = 'パーティションキー'; // valid input: ({surveyId}#{userId}#{timestamp in ms NOT s}) OR empty
const USER_ID = 'ユーザーID'; // valid input: can be anything
const ANSWER_CODE = '#'; // valid input: can be anything
const CREATED_AT = '作成日'; // valid input: YYYY-MM-DD HH:mm:ss OR empty
const UPDATED_AT = '更新日'; // valid input: YYYY-MM-DD HH:mm:ss OR empty
const CHECK = '状態'; // valid input: CHECK_LIST
const NOTE = '管理者メモ'; // valid input: can be anything
const MEMBER_TYPE = '会員種別';

// validation constants
const EN = 'en';
const JA = 'ja';

const REQUIRED_HEADERS = [
  { value: 'partitionKey', text: PARTITION_KEY },
  { value: 'userId', text: USER_ID },
  { value: 'answerCode', text: ANSWER_CODE },
];
const MEMBER_SURVEY_DYNAMIC_COLUMNS = [{ value: 'memberType', text: MEMBER_TYPE }];

const ITEM_TYPE_NOT_REQUIRED = ['guide'];
const PARTITION_KEY_PARTS = 3;
const LANGUAGE_HEADERS = [
  { ja: PARTITION_KEY, en: 'partitionKey' },
  { ja: USER_ID, en: 'userId' },
  { ja: NOTE, en: 'note' },
  { ja: CREATED_AT, en: 'createdAt' },
  { ja: UPDATED_AT, en: 'updatedAt' },
  { ja: CHECK, en: 'check' },
];
const CHECK_LIST = ['未対応', '対応中', '対応済', '完了']; // -> for 更新型 = will NOT be used. Because cannot import 更新型 survey
const CHECK_LIST_APPENDING = ['未対応', '対応中', '対応済', '完了', '保留', 'キャンセル', '取り消し'];

const convertUnixToYYYYMMDDHHmmss = (value) => {
  return moment.unix(value).format('YYYYMMDDHHmmss');
};
const convertYYYYMMDDHHmmssToUnix = (value) => {
  return moment(value).unix();
};
const validYYYYMMDDHHmmss = (value) => {
  return moment(value, 'YYYY-MM-DD HH:mm:ss', true).isValid();
};

const convertPapaparseCSVError = (error) =>  {
  const row = error.row + 1;
  let japaneseError = '不明な CSV エラーが発生しました。';
  switch (error.type) {
    case 'Quotes':
      japaneseError = '第 (' + row + ') 行目 - CSV クォーテーションエラーが発生しました。';
      break;
    case 'Delimiter':
      japaneseError = '第 (' + row + ') 行目 - CSV デリミターエラーが発生しました。';
      break;
    case 'FieldMismatch':
      japaneseError = '第 (' + row + ') 行目 - 項目のデータ型が一致されてません。';
      break;
    default:
      japaneseError = '第 (' + row + ') 行目 - 不明な CSV エラーが発生しました。';
      break;
  }
  return japaneseError;
};

const CsvToJson = (file, isSJISEncoding) => {
  return new Promise((resolve, reject) => {
    const config: Papa.ParseLocalConfig = {
      header: false,
      skipEmptyLines: 'greedy',
      delimiter: DELIMITER,
      complete(result) {
        result.errors = result.errors.map((error) => convertPapaparseCSVError(error) as never);
        resolve(result);
      },
      error(error) {
        reject(error);
      },
    };
    if (isSJISEncoding) {
      config['encoding'] = ENCODING;
    }
    Papa.parse(file, config);
  });
};

class SurveyResultsImportReportHelper {
  private reportList: any[];
  private surveyConfig: any;
  private indexableValues: any;
  private questionKeyToTitle: any;
  constructor() {
    this.reportList = [];
    this.surveyConfig = null;
    this.indexableValues = [];
    this.questionKeyToTitle = {};
  }

  getRawReport() {
    return this.reportList;
  }

  getReportHeaders() {
    const commonHeaders = [
      {
        value: 'csvRow',
        text: 'CSV内行数',
      },
      {
        value: 'importResult',
        text: 'インポート結果',
      },
      {
        value: 'errorMessage',
        text: 'エラー詳細',
      },
      {
        value: 'partitionKey',
        text: 'パーティションキー',
      },
    ];

    for (const indexElem of this.indexableValues) {
      commonHeaders.push({
        value: indexElem,
        text: this.questionKeyToTitle[indexElem],
      });
    }

    return commonHeaders;
  }

  setSurveyConfig(config) {
    this.surveyConfig = config;
    for (const question of config.surveySchema) {
      if (question.isIndexable && question.isIndexable.value) {
        this.indexableValues.push(question.itemKey);
        this.questionKeyToTitle[question.itemKey] = question.title;
      }
    }
  }

  addToReportIfValuesDiffer(valueA, valueB, surveyCase, result, errorMessage) {
    if (valueA !== valueB) {
      this.addOneToReport(surveyCase, result, errorMessage);
    }
  }

  addFromPartitionKey(partitionKey, listOfCases, result, errorMessage = null) {
    const foundCase = listOfCases.find((entry) => {
      return (
        entry.partitionKey === partitionKey ||
        (entry.surveyResult &&
          Array.isArray(entry.surveyResult) &&
          entry.surveyResult.length > 0 &&
          entry.surveyResult[0].partitionKey === partitionKey)
      );
    });
    if (foundCase) {
      this.addOneToReport(foundCase, result, errorMessage);
    }
  }

  addFromListOfPartitionKeys(paritionKeyList, listOfCases, result, errorMessage = null) {
    for (const partitionKey of paritionKeyList) {
      const foundCase = listOfCases.find((entry) => {
        return (
          entry.partitionKey === partitionKey ||
          (entry.surveyResult &&
            Array.isArray(entry.surveyResult) &&
            entry.surveyResult.length > 0 &&
            entry.surveyResult[0].partitionKey === partitionKey)
        );
      });
      if (foundCase) {
        this.addOneToReport(foundCase, result, errorMessage);
      }
    }
  }

  addFromListOfItems(listOfItems, listOfCases, result, errorMessage = null) {
    const uniquePartitionKeys:any[] = [];
    for (const item of listOfItems) {
      if (!uniquePartitionKeys.includes(item.partitionKey)) {
        uniquePartitionKeys.push(item.partitionKey);
      }
    }
    this.addFromListOfPartitionKeys(uniquePartitionKeys, listOfCases, result, errorMessage);
  }

  //This method is only called if data cannot be fetched from S3
  //Not able to add index values at this point
  addFromListOfRowNumbers(rowNumberList, partitionKeyMap, result, errorMessage = null) {
    for (const rowNum of rowNumberList) {
      this.reportList.push({
        csvRow: rowNum,
        importResult: result,
        errorMessage: errorMessage ? errorMessage : '',
        partitionKey: partitionKeyMap[rowNum] ? partitionKeyMap[rowNum] : '',
      });
    }
  }

  addOneToReport(surveyCase, result, errorMessage = null) {
    //set basic values
    const entry = {
      csvRow: surveyCase.row,
      importResult: result,
      errorMessage: errorMessage ? errorMessage : '',
    };
    //attempt to set partitionKey
    if ('partitionKey' in surveyCase) {
      entry['partitionKey'] = surveyCase.partitionKey;
    }

    //attempt to set indexed items
    if (this.surveyConfig && surveyCase.surveyResult && Array.isArray(surveyCase.surveyResult)) {
      for (const question of surveyCase.surveyResult) {
        if (this.indexableValues.includes(question.itemKey)) {
          entry[question.itemKey] = question.value;
        }
      }
    }
    this.reportList.push(entry);
  }

  addSeveralToReport(casesArray, result, errorMessage = null) {
    for (const surveyCase of casesArray) {
      this.addOneToReport(surveyCase, result, errorMessage);
    }
  }

  fetchCountOfReportByResultStatus(resultToSearch) {
    return this.reportList.filter((elem) => elem.importResult === resultToSearch).length;
  }
}

class SurveyResultsImportHelper {
  private surveyId: any;
  private schema: any;
  private memberFormSchema: any;
  private formData: any;
  private jsonData: any;
  private schemaByGroupheader: any[];
  private schemaByBunki: any[];
  private calendarDisplay: any;
  private headers: any[];
  private records: any[];
  private errors: any[];
  private debugErrors: any[];
  private schemaRecordsProcess: any[];
  private countVaccines: number;
  constructor(formData, jsonData, calendarDisplay, memberForm = null) {
    this.surveyId = formData.surveyId;

    this.schema = formData.surveySchema.filter((item) => !ITEM_TYPE_NOT_REQUIRED.includes(item.type));

    this.memberFormSchema = memberForm
      ? memberForm.surveySchema.filter((item) => !ITEM_TYPE_NOT_REQUIRED.includes(item.type))
      : null;

    this.formData = formData;
    this.jsonData = jsonData;

    this.schemaByGroupheader = [];
    this.schemaByBunki = [];

    this.calendarDisplay = calendarDisplay; //calendar category tag names for global settings

    this.headers = []; // will be filled later
    this.records = []; // will be filled later

    this.errors = [];
    this.debugErrors = []; //used to log to cloudwatch

    this.countVaccines = formData.surveySchema.find((item) => item.type === 'countVaccines')?.default || 0;
  }

  getErrors() {
    return this.errors;
  }

  printDebugErrors() {
    console.log('[IMPORT_DEBUG] Starting logging for survey results import validation errors');
    for (const error of this.debugErrors) {
      console.log(error);
    }
    console.log('[IMPORT_DEBUG] Logging finished');
  }

  putErrorMessage(lineNumber, title, message, debugMessage = '') {
    this.errors.push(`${lineNumber} [${title}] ${message}`);
    this.debugErrors.push(`Item in ${lineNumber}'s [${title}] threw an error:\n${debugMessage}`);
  }

  isValid() {
    return this.errors.length === 0;
  }

  cleanString(raw) {
    let s = _.trim(raw);
    if (s.startsWith('"=')) {
      s = s.slice(2);
    }
    s = _.trimStart(s, '=');
    s = _.trim(s, '"');

    return s;
  }

  hasValidUpdateFields(lang, item, lineNumber:string|number = '') {
    // partitionKey, userId, createdAt, updatedAt all 4 has to be in item
    const fields = [PARTITION_KEY, USER_ID, CREATED_AT, UPDATED_AT];
    if (lang === EN) {
      // convert fields to EN
      for (const header of LANGUAGE_HEADERS) {
        for (let i = 0; i < fields.length; ++i) {
          if (header[JA] === fields[i]) {
            fields[i] = header[EN];
          }
        }
      }
    }
    for (const field of fields) {
      if (!(field in item) || item[field].length === 0) {
        this.debugErrors.push(`The item in line [${lineNumber}] is not valid for update.\n
              Expected csv to contain a value for ${field}`);
        return false;
      }
    }
    return true;
  }

  hasValidCreateFields(item, lineNumber = '') {
    // partitionKey, createdAt, updatedAt all 3 has to be EMPTY/UNDEFINED in item
    const fields = [PARTITION_KEY, CREATED_AT, UPDATED_AT];
    for (const field of fields) {
      if (field in item && item[field].length > 0) {
        this.debugErrors.push(`The item in line [${lineNumber}] is not valid for create.\n
              Expected value of ${field} to be empty but was ${item[field]}`);
        return false;
      }
    }
    return true;
  }

  validateFormData() {
    if (
      !this.surveyId ||
      this.surveyId.length === 0 ||
      !this.schema ||
      !Array.isArray(this.schema) ||
      this.schema.length === 0
    ) {
      this.errors.push('帳票を選択してください。');
      this.debugErrors.push(`Selected surveyId was invalid. SurveyId: ${this.surveyId}\n
            or surveySchema was empty`);
      return;
    }
  }

  validateHeaders() {
    // data is still in json
    const incorrectHeaders:any[] = [];

    if (this.jsonData.length === 0) {
      this.errors.push('csvファイルにヘッダーが必須です。');
      this.debugErrors.push('The imported csv file did not contain any rows');
      return;
    }

    // save to class object
    this.headers = this.jsonData[0].map((key) => this.cleanString(key));

    for (const requiredHeader of REQUIRED_HEADERS) {
      if (!this.headers.includes(requiredHeader.text)) {
        incorrectHeaders.push(requiredHeader.text);
        this.debugErrors.push(`The required header ${requiredHeader.text} was not present in csv header row`);
      }
    }

    let indexShift = REQUIRED_HEADERS.length;
    if (this.memberFormSchema) {
      //There is a member survey attached to this survey, skip the member columns plus extra member columns
      indexShift += this.memberFormSchema.length + MEMBER_SURVEY_DYNAMIC_COLUMNS.length;
    }
    for (let i = 0; i < this.schema.length; ++i) {
      const question = this.schema[i];

      switch (question.type) {
        case 'reservation': {

          const expectedLargeCategory = question.setLargeCategoryTitle? question.setLargeCategoryTitle :
            this.calendarDisplay.tag1 && this.calendarDisplay.tag1 !== '' ? this.calendarDisplay.tag1 : '大項目';
          const expectedMediumCategory = question.setMediumCategoryTitle? question.setMediumCategoryTitle :
          this.calendarDisplay.tag2 && this.calendarDisplay.tag2 !== '' ? this.calendarDisplay.tag2 : '中項目';
          const expectedSmallCategory = question.setSmallCategoryTitle? question.setSmallCategoryTitle :
          this.calendarDisplay.tag3 && this.calendarDisplay.tag3 !== '' ? this.calendarDisplay.tag3 : '小項目';

          if (this.headers[indexShift + i + 1] !== expectedLargeCategory){
            incorrectHeaders.push(this.headers[indexShift + i + 1]);
            this.debugErrors.push(`The expected large category value and the value in the header are different\n
                  Expected [${expectedLargeCategory}]\n
                  Headers [${this.headers[indexShift + i + 1]}]`);
          }
          if (this.headers[indexShift + i + 2] !== expectedMediumCategory) {
            incorrectHeaders.push(this.headers[indexShift + i + 2]);
            this.debugErrors.push(`The expected medium category value and the value in the header are different\n
                  Expected [${expectedMediumCategory}]\n
                  Headers [${this.headers[indexShift + i + 2]}]`);
          }
          if (this.headers[indexShift + i + 3] !== expectedSmallCategory) {
            incorrectHeaders.push(this.headers[indexShift + i + 3]);
            this.debugErrors.push(`The expected small category value and the value in the header are different\n
                  Expected [${expectedSmallCategory}]\n
                  Headers [${this.headers[indexShift + i + 3]}]`);
          }

          indexShift += 3;

          if (this.headers[indexShift + i + 1] === '予約項目') {
            ++indexShift;
          }
          if (this.headers[indexShift + i + 1] === '予約時間') {
            ++indexShift;
          }
          if (this.headers[indexShift + i + 1] === 'LINE/外部') {
            ++indexShift;
          }
          break;
        }
        case 'linkbutton':
          // 無視出来ない
          --indexShift;
          break;

        case 'groupheader':
          // 無視出来ない
          --indexShift;
          break;

        default:
          if (this.headers[indexShift + i] !== question.title) {
            if (this.headers[indexShift + i] !== undefined) {
              incorrectHeaders.push(this.headers[indexShift + i]);
            }
            this.debugErrors.push(`The CSV header differed from the expected survey config schema title\n
                  Expected the header to be ${question.title} but header was ${this.headers[indexShift + i]}`);
          }
          break;
      }
    }

    if (incorrectHeaders.length > 0){
      let incorrectHeadersMessage = '1行目：タイトル '
      incorrectHeaders.forEach(header =>
          incorrectHeadersMessage += `「${header}」`
      )
      incorrectHeadersMessage += 'が不正です。'
      this.errors.push(incorrectHeadersMessage)
    }
  }

  convertJsonToListOfObjects() {
    // i = 0 is for headers (already assigned)
    for (let i = 1; i < this.jsonData.length; ++i) {
      const lineNumber = `第${i}行目：`;
      if (this.headers.length !== this.jsonData[i].length) {
        this.errors.push(`${lineNumber} length mismatch. Check for comma characters.`);
        this.debugErrors.push(`The number of columns in line ${lineNumber} mismatches the headers\n
              Length of headers: [${this.headers.length}]. Length of ${i}: [${this.jsonData[i].length}].`);
        return;
      }

      const item = {};
      for (let j = 0; j < this.jsonData[i].length; ++j) {
        item[this.headers[j]] = this.cleanString(this.jsonData[i][j]);
      }

      // because surveyAnswer value is assigned at the very end, even if there is a header name = "surveyAnswer", it will be overwritten!
      const surveyAnswer:any[] = [];
      let indexShift = REQUIRED_HEADERS.length;
      if (this.memberFormSchema) {
        //There is a member survey attached to this survey, skip the member columns plus extra member columns
        indexShift += this.memberFormSchema.length + MEMBER_SURVEY_DYNAMIC_COLUMNS.length;
      }
      for (let j = 0; j < this.schema.length; ++j) {
        const cleanedString = this.cleanString(this.jsonData[i][indexShift + j]);

        const question = this.schema[j];
        switch (question.type) {
          case 'reservation':
            indexShift += 3;

            if (this.headers[indexShift + j + 1] === '予約項目') {
              ++indexShift;
            }
            if (this.headers[indexShift + j + 1] === '予約時間') {
              ++indexShift;
            }
            if (this.headers[indexShift + j + 1] === 'LINE/外部') {
              ++indexShift;
            }

            // category answer (if exist) has to be sent to backend = 'category#....'
            surveyAnswer.push(cleanedString);

            break;

          case 'linkbutton':
            // 無視出来ない
            --indexShift;
            surveyAnswer.push('');
            break;

          case 'groupheader':
            // 無視出来ない
            --indexShift;
            surveyAnswer.push('');
            break;

          default:
            if (this.schema[j].type === 'checkboxes') {
              if (cleanedString.length === 0) {
                surveyAnswer.push([]);
              } else {
                surveyAnswer.push(cleanedString.split(','));
              }
            } else {
              surveyAnswer.push(cleanedString);
            }
            break;
        }
      }
      item['surveyAnswer'] = surveyAnswer;
      // console.log(surveyAnswer)
      this.records.push(item);
    }
  }

  validateRecords() {
    // all non-empty user id has to be unique -> no. same user can answer several times
    // should check for partitionKey!
    const pkIndex = new Map();
    this.schemaRecordsProcess = [];
    for (let i = 0; i < this.records.length; ++i) {
      this.schemaRecordsProcess = [...this.schema];

      const lineNumber = `第${i + 2}行目：`;
      const item = this.records[i];

      if (!this.hasValidCreateFields(item, lineNumber) && !this.hasValidUpdateFields(JA, item, lineNumber)) {
        this.errors.push(
          `${lineNumber} 新規の場合は[${PARTITION_KEY}], [${CREATED_AT}], [${UPDATED_AT}]が空でなければいけません。`
        );
        this.errors.push(
          `${lineNumber} 更新の場合は[${PARTITION_KEY}], [${CREATED_AT}], [${UPDATED_AT}]が最新のデータでなければいけません。`
        );
        return;
      }

      // validation for non-survey-question fields
      if (CHECK in item && item[CHECK].length > 0 && !CHECK_LIST_APPENDING.includes(item[CHECK])) {
        this.errors.push(`${lineNumber} [${CHECK}]の値が指定とは異なります。`);
        this.debugErrors.push(`The check/状態 value of item in ${lineNumber} is invalid. Value: ${item[CHECK]}`);
        return;
      }

      // special check for update item
      if (this.hasValidUpdateFields(JA, item, lineNumber)) {
        if (pkIndex.has(item[PARTITION_KEY])) {
          this.errors.push(
            `${lineNumber} 同じ[${PARTITION_KEY}]を持つアイテムが第${pkIndex.get(item[PARTITION_KEY])}行目にあります。`
          );
          this.debugErrors.push(`The partitionKey [${item[PARTITION_KEY]}] of item on line ${lineNumber}\n
                was already found in the csv file on line ${pkIndex.get(item[PARTITION_KEY])}`);
          return;
        }
        pkIndex.set(item[PARTITION_KEY], i + 1);

        const pk = item[PARTITION_KEY].split('#');
        if (pk.length !== PARTITION_KEY_PARTS) {
          this.errors.push(`${lineNumber} [${PARTITION_KEY}]が間違いました。`);
          this.debugErrors.push(`Expected the partitionKey [${item[PARTITION_KEY]}] of item on line [${lineNumber}]\n
                to be of length ${PARTITION_KEY_PARTS} but was ${pk.length}`);
          return;
        }

        if (pk[0] !== this.surveyId) {
          this.errors.push(`${lineNumber} [${PARTITION_KEY}]の[surveyId]が間違いました。`);
          this.debugErrors.push(`Expected the surveyId value of partitionKey of item on line [${lineNumber}]\n
                to be ${this.surveyId} but was ${pk[0]}`);
          return;
        }
        /*if(pk[1] !== item[USER_ID]) {
          this.errors.push(`${lineNumber} [${PARTITION_KEY}]の[userId]が間違いました。`);
          return;
        }*/

        if (!validYYYYMMDDHHmmss(item[CREATED_AT])) {
          this.errors.push(
            `${lineNumber} [${CREATED_AT}]のフォーマットが違います。YYYY-MM-DD HH:mm:ss形式で作成してください。`
          );
          this.debugErrors.push(`Expected the ${CREATED_AT} value of item on line [${lineNumber}]\n
                to be YYYY-MM-DD HH:mm:ss but was ${item[CREATED_AT]}`);
          return;
        }
        if (!validYYYYMMDDHHmmss(item[UPDATED_AT])) {
          this.errors.push(
            `${lineNumber} [${UPDATED_AT}]のフォーマットが違います。YYYY-MM-DD HH:mm:ss形式で作成してください。`
          );
          this.debugErrors.push(`Expected the ${UPDATED_AT} value of item on line [${lineNumber}]\n
                to be YYYY-MM-DD HH:mm:ss but was ${item[UPDATED_AT]}`);
          return;
        }

        /* no need to check the pk timestamp:
        // there will always be delta time for data transmission between front-end and back-end
        const pkUnixTimestamp = Math.floor(Number(pk[2]) / 1000);
        const createdTimestamp = convertYYYYMMDDHHmmssToUnix(item[CREATED_AT]);
        if(Math.abs(pkUnixTimestamp - createdTimestamp) > 1) {
          console.log(pkUnixTimestamp, createdTimestamp)
          this.errors.push(`${lineNumber} [${PARTITION_KEY}]の[timestamp]が[${CREATED_AT}]と一致しません。`);
          return;
        }
        */
      }

      // merge input into schema
      {
        const formData = this.formData;

        for (let j = 0; j < this.schemaRecordsProcess.length; ++j) {
          const schemaItem = this.schemaRecordsProcess[j];
          schemaItem.input = item.surveyAnswer[j];
          schemaItem.lineNumber = lineNumber;

          if (formData && formData.surveySchema && Array.isArray(formData.surveySchema)) {
            formData.surveySchema.forEach((item, index) => {
              if (schemaItem.itemKey === item.itemKey) {
                formData.surveySchema[index] = schemaItem;
              }
            });
          }

          if (schemaItem.type === 'linkbutton') {
            continue;
          }
        }
      }


      // validation for survey-question fields
      for (let j = 0; j < this.schemaRecordsProcess.length; ++j) {
        const schemaItem = this.schemaRecordsProcess[j];

        if (schemaItem.type === 'linkbutton') {
          continue;
        }

        // check isRequired first
        this.validateCommon(schemaItem);
        if (!this.isValid()) {
          return;
        }
        this.validateItem(schemaItem);

        if (!this.isValid()) {
          return;
        }
      }

      if (!this.isValid()) {
        return;
      }
    }
  }

  validateItem(item) {
    const obj = item;

    switch (obj.type) {
      case 'text':
        this.validateText(obj);
        break;
      case 'checkboxes':
        this.validateCheckboxes(obj);
        break;
      case 'dropdown':
        this.validateDropdown(obj);
        break;
      case 'date':
        this.validateDate(obj);
        break;
      case 'birthday':
      case 'sesshuJisshiDate':
      case 'previousVaccineDate':
        this.validateDateWithYear(obj);
        break;
      case 'email':
        this.validateEmail(obj);
        break;
      case 'phone':
        this.validatePhone(obj);
        break;
      case 'number':
        this.validateNumber(obj);
        break;
      case 'textarea':
        this.validateTextarea(obj);
        break;
      case 'radio':
        this.validateRadio(obj);
        break;
      case 'postcode':
        this.validatePostcode(obj);
        break;
      case 'choicegroupheader':
      case 'sesshuVaccineMaker':
      case 'previousVaccineMaker':
        this.validateChoiceGroupHeader(obj);
        break;
      case 'countVaccines':
        this.validateCountVaccines(obj);
        break;
      default:
        break;
    }
  }

  validate() {
    this.validateFormData();
    if (!this.isValid()) {
      return false;
    }

    this.validateHeaders();
    if (!this.isValid()) {
      return false;
    }

    this.convertJsonToListOfObjects();
    if (!this.isValid()) {
      return false;
    }

    this.validateRecords();
    if (!this.isValid()) {
      return false;
    }

    return true;
  }

  preparePayload() {
    this.records = this.records.map(item => {
      if ((CREATED_AT in item) && item[CREATED_AT].length > 0) {
        item[CREATED_AT] = convertYYYYMMDDHHmmssToUnix(item[CREATED_AT]);
      }
      if (UPDATED_AT in item && item[UPDATED_AT].length > 0) {
        item[UPDATED_AT] = convertYYYYMMDDHHmmssToUnix(item[UPDATED_AT]);
      }
      for (const header of LANGUAGE_HEADERS) {
        if (header[JA] in item) {
          item[header[EN]] = item[header[JA]];
          delete item[header[JA]];
        }
      }
      return item;
    });

    // if now is declared here, cannot create item with same userId in 1 import (only the last one will be created? because partitionkey will be equal)
    // solve: use {now + i} instead. now is in ms, 20000 items -> delta t = 20s. User probably will not import in 20s interval. For now.
    const now = Date.now();

    const toUpdate:any[] = [];
    const toCreate:any[] = [];
    const status = {
      bunruiUpdatedFlag: false,
    };
    for (let i = 0; i < this.records.length; ++i) {
      const answerItem = this.records[i];
      const surveyResult:any[] = [];

      for (let j = 0; j < this.schema.length; ++j) {
        // if result is filled by matching the order of headers with order of questions
        // order of answerItem.answer has to match the order of questionItem!
        const generatedUserId = `I${convertUnixToYYYYMMDDHHmmss(Math.floor(now / 1000))}n${i + 1}`;
        const questionItem = this.schema[j];

        if (questionItem.type === 'reservation' || questionItem.type === 'linkbutton') {
          status.bunruiUpdatedFlag = true;
        }

        if (questionItem.type === 'checkboxes') {
          for (const option of questionItem.options) {
            if (this.hasValidUpdateFields(EN, answerItem, i + 2)) {
              surveyResult.push({
                answerCode: answerItem.answerCode,
                check: answerItem.check,
                createdAt: answerItem.createdAt,
                itemKey: questionItem.itemKey,
                note: answerItem.note,
                partitionKey: answerItem.partitionKey,
                sortKey: `${questionItem.itemKey}#${option}`,
                surveyId: this.surveyId,
                // target: true, // is this necessary?
                title: questionItem.title,
                updatedAt: answerItem.updatedAt,
                userId: answerItem.userId,
                userSearchKey: answerItem.surveyAnswer[j].includes(option)
                  ? `${questionItem.itemKey}#${option}#${option}`
                  : `${questionItem.itemKey}#${option}#`,
                value: answerItem.surveyAnswer[j].includes(option) ? option : '',
              });
            } else {
              surveyResult.push({
                answerCode: answerItem.answerCode,
                check: answerItem.check,
                itemKey: questionItem.itemKey,
                note: answerItem.note,
                partitionKey:
                  answerItem.userId.length > 0
                    ? `${this.surveyId}#${answerItem.userId}#${now + i}`
                    : `${this.surveyId}#${generatedUserId}#${now + i}`,
                sortKey: `${questionItem.itemKey}#${option}`,
                surveyId: this.surveyId,
                userId: answerItem.userId.length > 0 ? answerItem.userId : generatedUserId,
                userSearchKey: answerItem.surveyAnswer[j].includes(option)
                  ? `${questionItem.itemKey}#${option}#${option}`
                  : `${questionItem.itemKey}#${option}#`,
                value: answerItem.surveyAnswer[j].includes(option) ? option : '',
              });
            }
          }
        } else {
          if (answerItem.surveyAnswer[j].length === 0) {
            // don't include empty answer value to payload
            continue;
          }
          if (this.hasValidUpdateFields(EN, answerItem, i + 2)) {
            surveyResult.push({
              answerCode: answerItem.answerCode,
              check: answerItem.check,
              createdAt: answerItem.createdAt,
              itemKey: questionItem.itemKey,
              note: answerItem.note,
              partitionKey: answerItem.partitionKey,
              sortKey: `${questionItem.itemKey}#${answerItem.surveyAnswer[j]}`,
              surveyId: this.surveyId,
              // target: true, // is this necessary?
              title: questionItem.title,
              updatedAt: answerItem.updatedAt,
              userId: answerItem.userId,
              userSearchKey: `${questionItem.itemKey}#${answerItem.surveyAnswer[j]}`,
              value: answerItem.surveyAnswer[j],
            });
          } else {
            surveyResult.push({
              answerCode: answerItem.answerCode,
              check: answerItem.check,
              itemKey: questionItem.itemKey,
              note: answerItem.note,
              partitionKey:
                answerItem.userId.length > 0
                  ? `${this.surveyId}#${answerItem.userId}#${now + i}`
                  : `${this.surveyId}#${generatedUserId}#${now + i}`,
              sortKey: `${questionItem.itemKey}#${answerItem.surveyAnswer[j]}`,
              surveyId: this.surveyId,
              userId: answerItem.userId.length > 0 ? answerItem.userId : generatedUserId,
              userSearchKey: `${questionItem.itemKey}#${answerItem.surveyAnswer[j]}`,
              value: answerItem.surveyAnswer[j],
            });
          }
        }
      }
      if (this.hasValidUpdateFields(EN, answerItem, i + 2)) {
        toUpdate.push({
          partitionKey: answerItem.partitionKey,
          updatedAt: answerItem.updatedAt,
          surveyResult: surveyResult,
          row: i + 2,
        });
      } else {
        toCreate.push({
          surveyResult: surveyResult,
          row: i + 2,
        });
      }
    }
    // console.log("toUpdate", toUpdate)
    // console.log("toCreate", JSON.stringify(toCreate, null, 2))
    return {
      surveyId: this.surveyId,
      toUpdate: toUpdate,
      toCreate: toCreate,
      status: status,
    };
  }

  // each type validation

  validateText(item) {
    // length
    this.validateLength(item.lineNumber, item.title, item.input, item.length);
  }

  validateEmail(item) {
    // format
    this.validateEmailString(item.lineNumber, item.title, item.input);
  }

  validatePhone(item) {
    // phone string
    this.validatePhoneString(item.lineNumber, item.title, item.input);
  }

  validateCheckboxes(item) {
    // options
    this.validateOptions(item.lineNumber, item.title, item.input, item.options);
    if (!this.isValid()) {
      return;
    }
    // selection
    this.validateSelectionCount(item.lineNumber, item.title, item.input, item.selection);
  }

  validateDropdown(item) {
    // options
    if (item.input) {
      this.validateOptions(item.lineNumber, item.title, item.input, item.options);
    }
  }

  validateDate(item) {
    // date
    const validPattern = item.includeYear ? ['YYYY/M/D', 'YYYY-MM-DD', 'YYYYMMDD'] : ['MM-DD'];
    this.validateDateString(item.lineNumber, item.title, item.input, validPattern);
  }

  validateDateWithYear(item) {
    //birthday, sesshuJisshiDate, previousVaccineDate
    if (item.type === 'previousVaccineDate') {
      const isValidInput = this.validationForFirstTimeVaccination(item);
      if (!isValidInput) {
        this.putErrorMessage(
          item.lineNumber,
          item.title,
          `1回目接種の場合は[${item.title}]を入力しないでください。`,
          `The input ${item.input} is not allowed if first time vaccination. Title is ${item.title}`
        );
        return;
      }
    }

    const validPattern = ['YYYY/M/D', 'YYYY-MM-DD', 'YYYYMMDD'];
    this.validateDateString(item.lineNumber, item.title, item.input, validPattern);
  }

  validateNumber(item) {
    // number
    this.isNumber(item.lineNumber, item.title, item.input);

    // range
    this.validateNumRange(item.lineNumber, item.title, item.input, item.range);
  }

  validateTextarea(item) {
    // length
    this.validateLength(item.lineNumber, item.title, item.input, item.length);
  }

  validateRadio(item) {
    // options
    this.validateOptions(item.lineNumber, item.title, item.input, item.options);
  }

  validatePostcode(item) {
    this.validatePostcodeString(item.lineNumber, item.title, item.input);
  }

  validateChoiceGroupHeader(item) {
    //IsRequired Validation check should have been triggered already,
    //so empty input is okay at this stage
    if (this.isInputEmpty(item)) {
      return;
    }

    if (item.type === 'previousVaccineMaker') {
      const isValidInput = this.validationForFirstTimeVaccination(item);
      if (!isValidInput) {
        this.putErrorMessage(
          item.lineNumber,
          item.title,
          `1回目接種の場合は[${item.title}]を入力しないでください。`,
          `The input ${item.input} is not allowed if first time vaccination. Title is ${item.title}`
        );
        return;
      }
    }

    //Make sure input value is a valid section group choice
    let validOption = undefined;
    if (item.sectionOptions && Array.isArray(item.sectionOptions)) {
      validOption = item.sectionOptions.find((o) => o.option && o.option.value === item.input);
    }

    if (validOption === undefined){
      this.putErrorMessage(
        item.lineNumber,
        item.title,
        '不正なデータです',
        `The input ${item.input} is not a valid option in survey config choice group header with title ${item.title}`
      );
      return;
    }
  }

  validationForFirstTimeVaccination(item) {
    if (Number(this.countVaccines) === 1) {
      const isInputEmpty = this.isInputEmpty(item);
      return isInputEmpty
    }
    return true;
  }

  // validation detail
  isInputEmpty(item) {
    if (!('input' in item) || item.input === '' || (Array.isArray(item.input) && item.input.length === 0)) {
      return true;
    }

    return false;
  }

  validateCountVaccines(item) {
    const isValid = Number(item.input) === Number(this.countVaccines);
    if (!isValid) {
      this.putErrorMessage(
        item.lineNumber,
        item.title,
        `接種回数は[${this.countVaccines}]を入力する必要があります`,
        `The input ${item.input} is not a valid in survey config for item with title ${item.title}`
      );
    }
    return;
  }

  // common validation

  validateCommon(item) {
    // require
    this.validateRequired(item);
  }

  validateRequired(item) {
    if ('admin' in item.isRequired) {
      if (item.isRequired.admin === true) {
        if (this.isInputEmpty(item)) {
          this.putErrorMessage(
            item.lineNumber,
            item.title,
            '必須項目です。',
            `Expected input to not be empty for required admin ${item.title}`
          );
          return;
        }
      }
    } else {
      if (item.isRequired.value) {
        if (this.isInputEmpty(item)) {
          this.putErrorMessage(
            item.lineNumber,
            item.title,
            '必須項目です。',
            `Expected input to not be empty for required ${item.title}`
          );
          return;
        }
      }
    }
  }

  validateLength(lineNumber, title, input, length) {
    if (!input || !length) {
      return;
    }

    const min = parseInt(length.min);
    const max = parseInt(length.max);
    let localErrorMessage = '';
    if (min && input.length < min) {
      localErrorMessage += `${min}文字以上`;
    }
    if (max && input.length > max) {
      localErrorMessage += `${max}文字以内`;
    }
    if (localErrorMessage.length > 0) {
      localErrorMessage += 'で入力してください';
      this.putErrorMessage(
        lineNumber,
        title,
        localErrorMessage,
        `Expected length of input to be between ${min} and ${max} but was ${input.length}`
      );
      return;
    }
  }

  validateEmailString(lineNumber, title, input) {
    if (!input) {
      return;
    }

    // from https://stackoverflow.com/questions/46155/how-to-validate-an-email-address-in-javascript
    const re =
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if (!re.test(String(input).toLowerCase())) {
      this.putErrorMessage(
        lineNumber,
        title,
        '正しい形式で入力してください',
        `Item did not pass email validation check ${input}`
      );
      return;
    }
  }

  validatePhoneString(lineNumber, title, input) {
    if (!input) {
      return;
    }

    const re = /^([0-9]{1,15})$/;
    if (!re.test(String(input))) {
      this.putErrorMessage(
        lineNumber,
        title,
        '正しい形式で入力してください',
        `Item did not pass phone number validation check ${input}`
      );
      return;
    }
  }

  validatePostcodeString(lineNumber, title, input) {
    if (!input) {
      return;
    }

    const pattern = /^[0-9]{7}$/;
    if (!pattern.test(input)) {
      this.putErrorMessage(
        lineNumber,
        title,
        '正しい郵便番号を入力してください。',
        `Item did not pass post code validation check ${input}`
      );
      return;
    }
  }

  isNumber(lineNumber, title, input, errorMessage?) {
    if (!input) {
      return;
    }

    if (Number.isNaN(Number(input))) {
      this.putErrorMessage(
        lineNumber,
        title,
        errorMessage || '正しい形式で入力してください',
        `Item did not pass number validation check ${input}`
      );
      return;
    }
  }

  validateNumRange(lineNumber, title, input, range) {
    if (!input || !range) {
      return;
    }

    const inputInt = parseInt(input);

    const min = parseInt(range.min);
    const max = parseInt(range.max);
    if (min && !max) {
      if (inputInt < min) {
        this.putErrorMessage(
          lineNumber,
          title,
          `${min}以上で入力してください`,
          `Item did not pass min number range check. Expected ${inputInt} to be greater than ${min}`
        );
        return;
      }
    } else if (!min && max) {
      if (max < inputInt) {
        this.putErrorMessage(
          lineNumber,
          title,
          `${max}以下で入力してください`,
          `Item did not pass max number range check. Expected ${inputInt} to be less than ${max}`
        );
        return;
      }
    } else {
      if (inputInt < min || max < inputInt) {
        this.putErrorMessage(
          lineNumber,
          title,
          `${min}〜${max}の範囲で入力してください`,
          `Item did not pass number range check. Expected ${inputInt} to be between ${min} and ${max}`
        );
        return;
      }
    }
  }

  validateSelectionCount(lineNumber, title, input, selection) {
    if (!input || !selection || this.isInputEmpty({ input: input })) {
      // [] is not falsy
      return;
    }

    const min = parseInt(selection.min);
    const max = parseInt(selection.max);
    const selectionCount = input.length;

    if (min && !max) {
      if (selectionCount < min) {
        this.putErrorMessage(
          lineNumber,
          title,
          `${min}個以上選択してください`,
          `Item did not pass min selection check. Expected ${selectionCount} to be greater than ${min}`
        );
        return;
      }
    } else if (!min && max) {
      if (max < selectionCount) {
        this.putErrorMessage(
          lineNumber,
          title,
          `${max}個以下で選択してください`,
          `Item did not pass max selection check. Expected ${selectionCount} to be less than ${max}`
        );
        return;
      }
    } else if (min && max) {
      if (selectionCount < min || max < selectionCount) {
        this.putErrorMessage(
          lineNumber,
          title,
          `${min}〜${max}個選択してください`,
          `Item did not pass selection check. Expected ${selectionCount} to be between ${min} and ${max}`
        );
        return;
      }
    }
  }

  validateOptions(lineNumber, title, input, options) {
    if (!input || this.isInputEmpty({ input: input })) {
      // [] is not falsy
      return;
    }

    //if (_.isString(input)) {
    if (typeof input == 'string') {
      input = [input];
    }
    for (let i = 0; i < input.length; i++) {
      if (!options.includes(input[i])) {
        this.putErrorMessage(
          lineNumber,
          title,
          '不正なデータです',
          `The input ${input[i]} is not a valid option in survey config options for item with title ${title}`
        );
        return;
      }
    }
  }

  validateDateString(lineNumber, title, input, validFormatList) {
    if (!input) {
      return;
    }
    const validDataFormatPattern = Array.isArray(validFormatList) ? validFormatList : Array.of(validFormatList);
    const isValidDataString = validDataFormatPattern.some((pattern) => {
      return moment(input, pattern, true).isValid();
    })
    if (!isValidDataString) {
      this.putErrorMessage(
        lineNumber,
        title,
        '正しい形式で入力してください',
        `Expected ${input} to be a valid date string but was not.`
      );
    }
  }

  fixDatesForQuestions(fileData) {
    const dateTypes = ['birthday', 'date', 'sesshuJisshiDate', 'previousVaccineDate'];
    const surveyConfig = this.formData;
    const dateItemKeys:any[] = [];
    surveyConfig.surveySchema.forEach(function (data) {
      // 生年月日項目を対象とする
      if (dateTypes.includes(data.type)) {
        dateItemKeys.push(data.itemKey);
      }
    });

    if (dateItemKeys.length > 0) {
      // 整形
      this.resetDateValue(fileData.toCreate, dateItemKeys);
      this.resetDateValue(fileData.toUpdate, dateItemKeys);
      return fileData;
    } else {
      return fileData;
    }
  }

  resetDateValue(targetData, dateItemKeys) {
    if (targetData.length > 0) {
      targetData.forEach(function (data) {
        const surveyResult = data.surveyResult;
        surveyResult.forEach(function (result) {
          if (dateItemKeys.includes(result.itemKey)) {
            // warning: Special countermeasure for Japan old summer time daylight saving
            const correctJapaneseOldSummertime = '1948-05-02';
            const isJapaneseOldSummertime = (t) => {
              const tMoment = moment(t, 'YYYYMMDD');
              const correctJapaneseOldSummertimeMoment = moment(correctJapaneseOldSummertime, 'YYYYMMDD');

              return tMoment.isSame(correctJapaneseOldSummertimeMoment) ? true : false;
            };

            if (isJapaneseOldSummertime(result.value)) {
              result.value = correctJapaneseOldSummertime;
            } else {
              result.value = moment(result.value).format('YYYY-MM-DD');
            }
            result.sortKey = `${result.itemKey}#${result.value}`;
            result.userSearchKey = `${result.itemKey}#${result.value}`;
          }
        });
      });
    }
  }
}

export {
  CsvToJson,
  SurveyResultsImportHelper,
  SurveyResultsImportReportHelper,
};
