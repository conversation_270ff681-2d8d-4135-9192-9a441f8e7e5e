import {Handler} from "aws-lambda";
import {handler} from "./search/handler.js";
import {onLambdaFinishedError, onLambdaRequestStartCallback} from "../common/logs/logger.js";
import console from "console";

export const request: Handler = async (evt, ctx) => {
    onLambdaRequestStartCallback()
    console.log('search started')
    try {
        return handler(evt, ctx);
    } catch (e) {
        console.log('search error', e)
        onLambdaFinishedError(e);
    }
}
