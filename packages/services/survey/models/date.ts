/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { DateTime, Settings } from 'luxon';
Settings.defaultLocale = 'ja';

type CalculateDateParams = {
  years?: number,
  days?: number,
}

export class DateModel {
  protected date: DateTime;
  private _isInLeapYear: boolean;
  constructor(date: string, format: string) {
    this.date = DateTime.fromFormat(date, format).startOf('day');
    if (!this.date.isValid) {
      throw new Error('invalid_date_format');
    }
    this._isInLeapYear = this.date.isInLeapYear;
  }

  get isInLeapYear() {
    return this._isInLeapYear
  }

  getDate() {
    return this.date;
  }

  toFormat(format: string) {
    return this.date.toFormat(format);
  }

  plus(params: CalculateDateParams) {
    const format = 'yyyyMMdd';
    const calculatedDate = this.date.plus(params).toFormat(format);
    return new DateModel(calculatedDate, format);
  }
}

export class BirthdayModel extends DateModel {
  constructor(birthday: string, format: string) {
    super(birthday, format);
  }

  calculateAge(baseDate: DateTime = DateTime.now()): number {
    const endOfFebruaryInLeapYear = '0229';
    const isBirthdayEndOfFebruaryInLeapYear = this.toFormat('MMdd') === endOfFebruaryInLeapYear;
    const doesSpecialCalcForLeapYear = !baseDate.isInLeapYear && isBirthdayEndOfFebruaryInLeapYear;
    // 誕生日がうるう年の2月29日かつ計算の基準日がうるう年ではない場合は、誕生日 + 1日 = 3月1日を誕生日として計算をする
    const targetBirthday = doesSpecialCalcForLeapYear ? this.plus({ days: 1 }) : this;
    const diff = baseDate.diff(targetBirthday.getDate(), 'years').toObject().years;
    return diff ? Math.floor(diff) : 0;
  }

  minus(params: CalculateDateParams): BirthdayModel {
    const format = 'yyyyMMdd';
    const calculatedDate = this.date.minus(params).toFormat(format);
    return new BirthdayModel(calculatedDate, format);
  }
}