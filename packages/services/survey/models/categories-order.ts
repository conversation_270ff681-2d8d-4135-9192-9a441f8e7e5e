/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import {
  CategoryOrder,
} from '../types/categories';
import _ from "lodash";

type CategoryForSort = {
  name: string,
  order: CategoryOrder,
}

export class CategoriesOrder {
  static isSpecifiedOrder(categoryOrder: CategoryOrder): boolean {
    return !_.isNil(categoryOrder);
  }

  static compareCategoriesTreeForSort(categoryA: CategoryForSort, categoryB: CategoryForSort) {
    const isSpecifiedOrderToA = CategoriesOrder.isSpecifiedOrder(categoryA.order);
    const isSpecifiedOrderToB = CategoriesOrder.isSpecifiedOrder(categoryB.order);

    if (!isSpecifiedOrderToA && !isSpecifiedOrderToB) {
      // 両方並び順が指定されていない場合はタグ名の昇順にする
      if (categoryA.name === categoryB.name) {
        return 0;
      } else {
        return categoryA.name > categoryB.name ? 1 : -1;
      }
    } else if (isSpecifiedOrderToA && !isSpecifiedOrderToB) {
      // 片方だけ並び順が指定されている場合は、指定されていない方を後にする
      return -1;
    } else if (!isSpecifiedOrderToA && isSpecifiedOrderToB) {
      return 1;
    } else {
      // 両方とも並び順が指定されている場合は、指定通りの並び順にする
      return (categoryA.order || 0) > (categoryB.order || 0) ? 1 : -1;
    }
  }
}