/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {Settings} from 'luxon';
import {LogLevel, setLogLevel} from './support/logger.js';
import * as _ from 'lodash';

export const TryQuickSearch = {request: "TryQuickSearchRequest", response: 'TryQuickSearchResponse'}
export const AnswersCountStart = {request: "AnswersCountStartRequest", response: 'AnswersCountStartResponse'}
export const StartAsyncSearch = {request: "StartAsyncSearchRequest", response: 'StartAsyncSearchResponse'}
export const DistributionTargetedSearch = {request: "DistributionTargetedSearchRequest", response: 'DistributionTargetedSearchResponse'}

Settings.defaultZone = 'Asia/Tokyo';

export const contextInfo = {
  functionName: process.env.AWS_LAMBDA_FUNCTION_NAME || '',
  cwLogGroup: process.env.AWS_LAMBDA_LOG_GROUP_NAME || '',
  cwLogStream: process.env.AWS_LAMBDA_LOG_STREAM_NAME || '',
}
setLogLevel(process.env.LOG_LEVEL as LogLevel || 'info');

export const getContextInfo = () => {
  return _.clone(contextInfo);
}
