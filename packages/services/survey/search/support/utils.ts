/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import {DateTime} from 'luxon';
import {SurveyResult} from '../common/results_model.js';
import Timeout = NodeJS.Timeout;

export const currentDateTimeISO = () => {
  return DateTime.now().toISO({includeOffset: true})
}

const parseDateTime = (dateTime: any) => {
  if (DateTime.isDateTime(dateTime)) {
    return dateTime;
  }
  if (typeof dateTime === 'number') {
    return DateTime.fromMillis(dateTime);
  }
  if (typeof dateTime === 'string') {
    return DateTime.fromISO(dateTime);
  }
  return undefined;
}

export const countDurationMs = (nextTime: any, firstTime: any = DateTime.now()) => {
  const moment1:DateTime | undefined = parseDateTime(nextTime);
  if (!moment1) {
    return -1;
  }
  const moment2 = parseDateTime(firstTime);
  if (!moment2) {
    return -1;
  }
  return Math.abs(moment1.diff(moment2, "milliseconds").milliseconds);
}

export const unixTimeAfterHours = (hours: number) => {
  return Math.floor(DateTime.now().plus({hours: hours}).toSeconds());
}

export class MultiMap<T> {
  private map: { [key: string]: T[] } = {};

  public put = (key: string, value: T) => {
    const bucket = this.map[key];
    if (bucket) {
      bucket.push(value);
    } else {
      this.map[key] = [value];
    }
  }

  public putAll = (key: string, values: T[]) => {
    values.forEach(v => this.put(key, v));
  }

  public get = (key: string) => {
    const bucket = this.map[key];
    if (bucket) {
      return [...bucket];
    } else {
      return [];
    }
  }

  public keys() {
    return Object.keys(this.map);
  }
}

export class Timer {
  private started = false;
  private _finished = false;
  private timerId: Timeout;

  constructor(private milliseconds: number) {
  }

  public start(cb?: (elapsed: number) => void) {
    this.timerId = setTimeout(
      () => {
        this._finished = true;
        if (cb) {
          cb(this.milliseconds);
        }
      }, this.milliseconds) as never;
    this.started = true;
  }

  get isTimeout() {
    return this._finished;
  }

  public async wait() {
    this.started = true;
    return new Promise<void>((resolve) => setTimeout(() => {
      this._finished = true;
      resolve();
    }, this.milliseconds))
  }

  public cancel() {
    if (this.timerId) {
      clearTimeout(this.timerId);
    }
    this._finished = true;
  }
}

const durationCounters: { [key: string]: DurationCounter } = {};

export class DurationCounter {
  private startedAt: DateTime;

  constructor() {
    this.start();
  }

  start() {
    this.startedAt = DateTime.now();
  }

  elapsedSeconds() {
    return DateTime.now().diff(this.startedAt, "seconds").seconds;
  }

  elapsedMilliseconds() {
    return DateTime.now().diff(this.startedAt, "milliseconds").milliseconds;
  }
}

export const startDurationCounter = () => {
  return new DurationCounter();
}

export const runTimer = (params: { seconds?: number, milliseconds?: number }, cb?: () => void) => {
  const millis = params.milliseconds ? params.milliseconds : (params.seconds ? params.seconds * 1000 : 0);
  const timer = new Timer(millis);
  timer.start(cb);
  return timer;
}

export const waitFor = async (params: { seconds?: number, milliseconds?: number }) => {
  const millis = params.milliseconds ? params.milliseconds : (params.seconds ? params.seconds * 1000 : 0);
  return new Timer(millis).wait();
}

export const takeFromQueue = (queue: any[], limit: number) => {
  const nextPortion: SurveyResult[] = [];
  while ((nextPortion.length < limit) && (queue.length > 0)) {
    const next = queue.shift();
    if (next) {
      nextPortion.push(next);
    }
  }
  return nextPortion;
}

export function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0, v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};
