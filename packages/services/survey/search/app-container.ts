/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {StartAsyncSearchInput} from './async-search/async-search.js';
import {AsyncSearchExtFunctions} from './async-search/ext.js';
import {SearchManager} from './async-search/search-manager.js';
import {CacheableStorage, LongStorageAdapterImpl, UserIdGroupingCache} from './results-storage/cacheable-storage.js';
import {ResultsCachingService} from './results-storage/results-caching-service.js';
import {StorageController, SurveyResultRecord} from './common/types.js';
import {UniqueResultsFilter} from './async-search/unique-results-filter.js';

export class AppContainer {
  storage: AsyncSearchExtFunctions;
  searchManager: SearchManager;
  storageController: StorageController
  isMember: boolean;

  constructor(
    private searchId: string,
    private event: StartAsyncSearchInput,
  ) {
  }

  get useS3Storage() {
    return !!this.event.s3;
  }

  get s3Config() {
    return {
      folderKey: this.event.s3 ? this.event.s3.folderKey : this.searchId,
      resultsPerFile: this.event.s3 ? this.event.s3.resultsPerFile : 1000,
    }
  }

  async initialize(isMember = false) {
    this.isMember = isMember;
    this.storage = this.getStorage();
    this.searchManager = await this.createSearchManager();
    this.storageController = this.createStorageController();
  }

  private getStorage() {
    const storageImplementantion = !this.isMember ? createDbAccessFunctions() : createMemberDBAccessFunctions();
    if (this.useS3Storage) {
      storageImplementantion.setS3StorageFolder(this.s3Config.folderKey);
      if (!process.env.S3_BUCKET_NAME) {
        throw new Error('S3_BUCKET_NAME env variable is not specified');
      }
    }
    return storageImplementantion;
  }

  private createStorageController() {
    if (this.event.groupBy) {
      const filter = (r: SurveyResultRecord) => {
        if (this.event.ignoreNonLineUsers) {
          return r.userId && r.userId.startsWith('U');
        }
        return true;
      }
      return new CacheableStorage(
        new UserIdGroupingCache(),
        new LongStorageAdapterImpl(this.searchId, this.storage),
        this.s3Config.resultsPerFile,
        this.event.countOnly || false,
        filter,
      )
    }
    const dbMinFlushItems = this.useS3Storage ? this.s3Config.resultsPerFile : 50;
    const dbChunkMaxLength = this.useS3Storage ? this.s3Config.resultsPerFile : 3000;
    const controller = new ResultsCachingService(this.searchId, {
      dbMinFlushItems, dbChunkMaxLength
    }, this.storage);
    controller.setStoreCountOnly(this.event.countOnly);
    controller.setSelectedAttributes(['updatedAt']);
    controller.startAutoFlushing();
    if (this.useS3Storage){
      controller.setSelectedAttributes(['updatedAt', 'userId'])
    }
    if (this.event.uniqueBy) {
      controller.setFilter(new UniqueResultsFilter(this.event.uniqueBy));
    }
    return  controller;
  }

  private async createSearchManager() {
    const sm = new SearchManager(this.storage);
    sm.setSurvey(this.event.input.surveyId);
    await sm.prepare();

    sm.setFieldsFilter(this.event.input.filterCommon);
    sm.setDatesFilter(this.event.input.filterDate);
    sm.setCategoriesLimiter(this.event.input.filterCategories);
    sm.setTeamsLimiter(this.event.input.filterTeams);
    sm.setCountOnly(this.event.countOnly);
    sm.setSearchTimeoutSec(11*60);
    if (this.event.maxResultsCount) {
      sm.setResultsLimit(this.event.maxResultsCount);
    } else if (this.useS3Storage || this.event.countOnly) {
      sm.setResultsLimit(-1);
    } else {
      sm.setResultsLimit(10000);
      sm.setSearchTimeoutSec(8*60);
    }
    return sm;
  }


}

export async function createAppContainer(searchId: string, event: StartAsyncSearchInput) {
  const container = new AppContainer(searchId, event);
  await container.initialize(event.input.isMember);
  return container;
}

export function createDbAccessFunctions() {
  return new AsyncSearchExtFunctions( {
    surveyConfigsTable: process.env.TABLE_SURVEY_CONFIGS || '',
    surveyResultsTable: process.env.TABLE_SURVEY_RESULTS || '',
    resultsCacheTable: process.env.TABLE_SURVEY_RESULTS || '',
    s3BucketName: process.env.S3_BUCKET_NAME,
    surveyCalendarsTable: '',
    lambdaName: process.env.AWS_LAMBDA_FUNCTION_NAME || '',
  });
}

export function createMemberDBAccessFunctions() {
  return new AsyncSearchExtFunctions( {
    surveyConfigsTable: process.env.TABLE_MEMBER_CONFIGS || '',
    surveyResultsTable: process.env.TABLE_MEMBER_RESULTS || '',
    resultsCacheTable: process.env.TABLE_MEMBER_RESULTS || '',
    s3BucketName: process.env.S3_BUCKET_NAME,
    surveyCalendarsTable: '',
    lambdaName: process.env.AWS_LAMBDA_FUNCTION_NAME || '',
  });
}
