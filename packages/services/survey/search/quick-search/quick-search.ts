/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {DatesFilter, FieldsFilter} from "../common/results-query-helpers.js";
import {currentDateTimeISO, generateUUID, unixTimeAfterHours} from '../support/utils.js';
import { createDbAccessFunctions, createMemberDBAccessFunctions} from '../app-container.js';
import {StartAsyncSearch} from '../app.js';

type QuickSearchRequest = {
  surveyId: string,
  userId: string,
  countOnly?: boolean,
  filterCommon: FieldsFilter,
  filterDate: DatesFilter,
  filterCategories?: string[],
  filterTeams?: string[],
  isMember: boolean,
}

const fixRequest = (event: QuickSearchRequest) => {
  event.filterDate = event.filterDate || {}
  event.filterCommon = event.filterCommon || {}
}

const handler = async (request: QuickSearchRequest) => {
  fixRequest(request);
  const isMember = request.isMember;
  const searchId = generateUUID();
  const storage = !isMember ? createDbAccessFunctions() : createMemberDBAccessFunctions();
  const initialIndex = await storage.initialSetIndex(searchId, {
    status: 'not_started',
    chunks: [],
    totalCount: 0,
    searchId: searchId,
    input: request,
    createdAt: currentDateTimeISO(),
    expireAt: unixTimeAfterHours(7*24),
  });

  await storage.doAsyncLambdaCall({
    [StartAsyncSearch.request]: {
      searchId,
      userId: request.userId,
      countOnly: !!request.countOnly,
      input: request,
    }
  })
  return initialIndex;
}

export default handler;
