/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {SurveyConfig, SurveyConfigModel, MemberConfig, MemberConfigModel} from "./config_model.js";
import * as _ from 'lodash';
import {DateTime} from "luxon";

export type SurveyResultLine = {
  itemKey: string,
  surveyId: string,
  sortKey: string,
  userSearchKey: string,
  partitionKey: string,
  updatedAt: number,
  createdAt: number,
  value: string,
  userId: string,
  indexId?: string,
  indexSearchKey?: string,
  check?: string,
}

export type MemberResultLine = {
  itemKey: string,
  surveyId: string,
  sortKey: string,
  userSearchKey: string,
  partitionKey: string,
  updatedAt: number,
  createdAt: number,
  value: string,
  userId: string,
  indexId?: string,
  indexSearchKey?: string,
  check?: string,
}

export type ResultsFilter = {
  logic: 'and' | 'or',
  fields: {
    itemKey: string,
    value: string,
  }[]
}

export class SurveyResult {
  readonly answers: SurveyResultLine[];
  readonly configModel: SurveyConfigModel;

  constructor(answerLines: SurveyResultLine[], config: SurveyConfig) {
    this.answers = answerLines;
    this.configModel = new SurveyConfigModel(config);
  }

  merge(other: SurveyResult) {
    other.answers.forEach((a) => {
      const ik = a.itemKey;
      if (this.hasMatchingField({itemKey: ik, value: a.value})) {
        return;
      }
      this.answers.push(a);
    })
  }

  matches(filter: ResultsFilter) {
    if (filter.logic === 'or') {
      return !!filter.fields.find(this.hasMatchingField.bind(this));
    } else {
      return filter.fields.every(this.hasMatchingField.bind(this));
    }
  }

  getStatusOfResultLine() {
    if (this.answers.length > 0) {
      return this.answers[0].check;
    } else {
      return null;
    }
  }

  getAnswersByItemId(itemKey: string) {
    return this.answers.filter((answer) => answer.itemKey === itemKey)
  }

  private hasMatchingField(field: { itemKey: string, value: string }) {
    const lines = this.getAnswersByItemId(field.itemKey);
    if (lines.length === 0) {
      return false;
    }
    if (lines.length === 1) {
      return (field.value === lines[0].value)
    }
    return lines.some((answer) => field.value === answer.value)
  }

  getValue(itemId: string) {
    const lines = this.getAnswersByItemId(itemId);
    if (this.configModel.isMultiValue(itemId)) {
      return lines.map((answer) => answer.value || '')[0];
    }
    if (lines.length === 1) {
      return lines[0].value;
    }
    return undefined;
  }

  getValues(itemId: string) {
    const lines = this.getAnswersByItemId(itemId);
    return lines.map((answer) => answer.value || '');
  }

  getLinedAnswers() {
    return _.sortBy(this.answers, _.memoize((answer) => this.configModel.getOrdinal(answer.itemKey)));
  }

  get uid() {
    return this.answers[0].userId;
  }

  get resultId() {
    return this.answers[0].partitionKey;
  }

  get updatedAt() {
    return this.answers[0].updatedAt;
  }

  get check() {
    return this.answers[0].check;
  }

  makeTitledJson() {
    const result = {};
    this.configModel.getAllKeys().forEach((key) => {
      const values = this.getValues(key);
      if (values.length > 0) {
        result[this.configModel.getTitle(key)] = values.join(',')
      }
    })
    return result;
  }

  static buildFromLines(answers: SurveyResultLine[], config: SurveyConfig) {
    const map = _.groupBy(answers, (answer) => answer.partitionKey);
    return Object.values(map).map((linesOfSurvey) => new SurveyResult(linesOfSurvey, config));
  }
}

export class ReservationLineModel {
  private readonly _categoryId: string;
  private readonly _date: string;

  constructor(private line: any) {
    if (!line) {
      return;
    }
    let value: string;
    if (typeof line === "string") {
      value = line;
    } else {
      value = line.value;
    }
    const parts = value.split('|')
    this._categoryId = parts[0];
    this._date = parts[1];
  }

  get hasValidDate() {
    return this._date && this._date.length === 8;
  }

  get hasValidCategory() {
    return !!this._categoryId;
  }

  get rawLine() {
    return this.line;
  }

  get categoryIdPrefix() {
    return this._categoryId.split('_')[0];
  }

  get itemKey() {
    return this.line['itemKey'] as string
  }

  get categoryId() {
    return this._categoryId;
  }

  get datetime() {
    return DateTime.fromFormat(this._date, 'yyyyMMdd').startOf('day');
  }

  get dateStr() {
    return this._date;
  }

  get timestamp() {
    return this.datetime.toSeconds();
  }

}

export class MemberResult {
  readonly answers: MemberResultLine[];
  readonly configModel: MemberConfigModel;

  constructor(answerLines: MemberResultLine[], config: MemberConfig) {
    this.answers = answerLines;
    this.configModel = new MemberConfigModel(config);
  }

  matches(filter: ResultsFilter) {
    if (filter.logic === 'or') {
      return !!filter.fields.find(this.hasMatchingField.bind(this));
    } else {
      return filter.fields.every(this.hasMatchingField.bind(this));
    }
  }

  getStatusOfResultLine() {
    if (this.answers.length > 0) {
      return this.answers[0].check;
    } else {
      return null;
    }
  }

  getAnswersByItemId(itemKey: string) {
    return this.answers.filter((answer) => answer.itemKey === itemKey)
  }

  private hasMatchingField(field: { itemKey: string, value: string }) {
    const lines = this.getAnswersByItemId(field.itemKey);
    if (lines.length === 0) {
      return false;
    }
    if (lines.length === 1) {
      return (field.value === lines[0].value)
    }
    return lines.some((answer) => field.value === answer.value)
  }

  getValue(itemId: string) {
    const lines = this.getAnswersByItemId(itemId);
    if (this.configModel.isMultiValue(itemId)) {
      return lines.map((answer) => answer.value || '');
    }
    if (lines.length === 1) {
      return lines[0].value;
    }
    return undefined;
  }

  getLinedAnswers() {
    return _.sortBy(this.answers, _.memoize((answer) => this.configModel.getOrdinal(answer.itemKey)));
  }

  get uid() {
    return this.answers[0].userId;
  }

  get resultId() {
    return this.answers[0].partitionKey;
  }

  static buildFromLines(answers: MemberResultLine[], config: MemberConfig) {
    const map = _.groupBy(answers, (answer) => answer.partitionKey);
    return Object.values(map).map((linesOfSurvey) => new MemberResult(linesOfSurvey, config));
  }
}
