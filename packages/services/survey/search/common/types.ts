/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {Credentials} from "aws-sdk";
import {CredentialsOptions} from "aws-sdk/lib/credentials";

export type AwsCredentials = { credentials?: Credentials | CredentialsOptions | null, region?: string } | undefined;

export type QueryExecutor = {
  start(): Promise<any>
}

export type OptionsMap = {[key: string]: any}

export type TeamsFilter = { [key: string]: boolean };

export interface Stoppable {
  stop();
}

export type MergedRecordType = Required<{
  partitionKey: string,
  fields: any[],
}>

export type AsyncSearchIndexDbRecord = {
  partitionKey: string,
  sortKey: string,
  searchId: string,
  totalCount: number,
  status: string,
  chunks: string[],
  s3BucketName?: string,
  s3folderKey?: string,
  expireAt: number,
  errorMessage?: string,
  errorDetails?: any,
  startedAt: string,
  finishedAt: string,
  debug?: any,
}

export type SurveyResultRecord = {
  userId: string,
  partitionKey: string,
  updatedAt: number,
}
export type StorageIs = {
  addToTotalCounter(id: string, n: number): Promise<any>
  addChunk(id: string, items: any[]): Promise<string>
}
export type S3Config = {
  folderKey: string,
}

export interface StorageController {
  store(results: SurveyResultRecord[]);
  flushRest();
  stop();
}
