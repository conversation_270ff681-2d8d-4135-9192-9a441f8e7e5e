/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
type FieldType = {
  itemKey: string,
  type: string,
  title: string,
  isAdminItem: boolean,
  isRequired: {
    value: boolean,
  },
  description?: string,
  isSearchable: {
    value: boolean,
  },
  isIndexable: {
    value: boolean,
  },
};

type PaymentFieldType = FieldType & {
  input: string, // service sort key
}
type ReservationCategory = {
  name: string,
  children: {
    name: string, id: string, calendarId?: string
  }[]
}
export type ReservationFieldType = {
  isFixedLargeCategory: boolean,
  isFixedMediumCategory: boolean,
  isFixedSmallCategory: boolean,
  reservationCheckBox: boolean,
  reservationSupCheckBox: boolean,
  selectedLargeCategory?: ReservationCategory,
  selectedMediumCategory?: ReservationCategory,
  setLargeCategoryTitle?: string,
  setMediumCategoryTitle?: string,
  setSmallCategoryTitle?: string,
  type: 'reservation',
} & FieldType

export type SurveyConfig = {
  surveyId: string,
  surveySchema: FieldType[],
  categoriesPermissions?: { [key: string]: string[] }
}

export type MemberConfig = {
  memberSurveyId: string,
  surveySchema: FieldType[]
}

const MultiValueType = 'checkboxes';

export class SurveyConfigModel {

  private readonly config: SurveyConfig;
  private readonly schema: FieldType[];

  constructor(surveyConfig: SurveyConfig) {
    this.config = surveyConfig;
    this.schema = surveyConfig.surveySchema || [];
  }

  public get originalConfig() {
    return this.config;
  }

  getField(itemKey: string) {
    return this.schema.find((field) => field.itemKey === itemKey);
  }

  isMultiValue(itemId: string) {
    const field = this.getField(itemId)
    return field ? field.type === MultiValueType : false;
  }

  isScalarValue(itemId: string) {
    return !this.isMultiValue(itemId);
  }

  exists(itemId: string) {
    return !!this.getField(itemId);
  }

  getOrdinal(itemId: string) {
    const field = this.getField(itemId)
    if (!field) return -1;
    return this.schema.indexOf(field);
  }

  isIndexable(itemId: string) {
    const field = this.getField(itemId)
    return field && field.isIndexable ? field.isIndexable.value : false;
  }

  isSearcheable(itemId: string) {
    const field = this.getField(itemId)
    return field && field.isSearchable ? field.isSearchable.value : false;
  }

  getSchema() {
    return this.schema;
  }

  getTitle(itemKey: string) {
    const d = this.getField(itemKey);
    if (!d) {
      return '';
    }
    return d.title;
  }

  getSurveyId() {
    return this.config.surveyId;
  }

  getDefinitionOfType(type: string) {
    return this.schema.filter((item) => item.type === type);
  }

  hasReservation() {
    return !!this.getDefinitionOfType('reservation')[0]
  }

  getReservationDefinition(): ReservationFieldType {
    const def = this.getDefinitionOfType('reservation')[0];
    if (!def) {
      throw "Category is not defined in this form. Check existence, using hasReservation()";
    }
    return def as ReservationFieldType;
  }

  getReservationFieldKey() {
    if (this.hasReservation()) {
      const def = this.getReservationDefinition();
      return def ? def.itemKey : undefined;
    }
    return undefined;
  }

  getAllKeys() {
    return this.schema.map((field) => {
      return field.itemKey;
    });
  }

  getAllowedTeamsOfCategory(categoryId: string) {
    if (!this.config.categoriesPermissions) {
      return [];
    }
    return this.config.categoriesPermissions[categoryId] || [];
  }

  hasAccessLimitationsForSomeCategory() {
    const configPermissionsMap = this.config.categoriesPermissions;
    if (!configPermissionsMap) {
      return false;
    }
    return Object.keys(configPermissionsMap)
      .some((categoryId) => {
        const teams = configPermissionsMap[categoryId];
        return teams && teams.length > 0;
      });
  }

}


export class ConfigSchemaFieldModel {
  constructor(private fieldModel: FieldType) {
  }

  get isAdminItem() {
    return !!this.fieldModel.isAdminItem;
  }
  get isIndexable() {
    return this.fieldModel.isIndexable && this.fieldModel.isIndexable.value;
  }
  get isRequired() {
    return this.fieldModel.isRequired && this.fieldModel.isRequired.value;
  }
  get itemKey() {
    return this.fieldModel.itemKey;
  }
  get isVirtual() {
    return ["selectProducts"].includes(this.fieldModel.type) 
  }
}


export class MemberConfigModel {

  private readonly config: MemberConfig;
  private readonly schema: FieldType[];

  constructor(surveyConfig: MemberConfig) {
    this.config = surveyConfig;
    this.schema = surveyConfig.surveySchema || [];
  }

  getField(itemKey: string) {
    return this.schema.find((field) => field.itemKey === itemKey);
  }

  isMultiValue(itemId: string) {
    const field = this.getField(itemId)
    return field ? field.type === MultiValueType : false;
  }

  isScalarValue(itemId: string) {
    return !this.isMultiValue(itemId);
  }

  exists(itemId: string) {
    return !!this.getField(itemId);
  }

  getOrdinal(itemId: string) {
    const field = this.getField(itemId)
    if (!field) return -1;
    return this.schema.indexOf(field);
  }

  isIndexable(itemId: string) {
    const field = this.getField(itemId)
    return field && field.isIndexable ? field.isIndexable.value : false;
  }

  isSearcheable(itemId: string) {
    const field = this.getField(itemId)
    return field && field.isSearchable ? field.isSearchable.value : false;
  }

  getSchema() {
    return this.schema;
  }

  getSurveyId() {
    return this.config.memberSurveyId;
  }

}
