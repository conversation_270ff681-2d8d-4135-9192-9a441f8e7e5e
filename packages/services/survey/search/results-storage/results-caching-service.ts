/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import * as _ from "lodash";
import * as log from '../support/logger.js';
import {setInterval} from "timers";
import {trace} from "../support/logger.js";
import {takeFromQueue} from "../support/utils.js";
import {Stoppable} from "../common/types.js";

export type CachingPolicy = {
  dbChunkMaxLength: number,
  dbMinFlushItems: number,
}
export type SearchState = {
  status: string,
  totalCount: number,
  chunkIds: string[],
}

export type StorageIs = {
  addToTotalCounter(id: string, n: number): Promise<any>
  addChunk(id: string, items: any[]): Promise<string>
}

export interface ResultsFilter {
  accept(record: any): boolean;
}

export class ResultsCachingService implements Stoppable {
  private cachedItems: any[] = [];
  private counter = 0;
  private selectedAttributes: string[] = [];
  private storeCountOnly = false;
  private readonly state: SearchState;
  private isInitialFlushDone = false;
  private isAutoflushActive = false;
  private isStopped = false;
  private filter: ResultsFilter
  private flushImmediatly = false;


  constructor(
    private searchId: string,
    private cachingPolicy: CachingPolicy,
    private storage: StorageIs) {
    this.state = {
      totalCount: 0,
      chunkIds: [],
      status: 'progress'
    }

    if (cachingPolicy.dbChunkMaxLength <= 0) {
      throw new Error('dbChunkMaxLength must be > 0');
    }
    this.filter = {
      accept(record: any): boolean {
        return true;
      }
    }
  }

  public setCachingPolicy(c: CachingPolicy) {
    if (c.dbChunkMaxLength <= 0) {
      throw new Error('dbChunkMaxLength must be > 0');
    }
    this.cachingPolicy = c;
  }

  public setFlushImmediatly(f: boolean) {
    this.flushImmediatly = f;
  }

  public setFilter(f: ResultsFilter) {
    this.filter = f;
  }

  public setStoreCountOnly(co?: boolean) {
    this.storeCountOnly = !!co;
  }

  public setSelectedAttributes(attrs: string[]) {
    this.selectedAttributes = attrs;
  }

  public startAutoFlushing() {
    if (this.isAutoflushActive || this.isStopped || this.flushImmediatly) {
      return;
    }
    log.trace('autoflush', 'running')
    const flushingTimeoutHandler = setInterval(() => {
      if (this.isStopped) {
        clearInterval(flushingTimeoutHandler);
        this.isAutoflushActive = false;
        log.trace('autoflush', 'finished')
        return;
      }
      if (!this.hasMinimumToFlush()) {
        log.trace('autoflush', `too few items to flush (${this.cachedItems.length}). Skip.`)
        return;
      }
      log.trace('autoflush', 'flush once')
      this.flushOnce().then((left) => {
        if (left <= 0) {
          clearInterval(flushingTimeoutHandler);
          this.isAutoflushActive = false;
          log.trace('autoflush', 'stopped')
        }
      });
    }, this.storeCountOnly ? 2500 : 2000);
    this.isAutoflushActive = true;
  }

  updateCounter = async (addToCounter: number) => {
    const updated = await this.storage.addToTotalCounter(this.searchId,addToCounter);
    log.trace('updateCounter', 'new total count', updated.Attributes.totalCount)
  }

  saveNextChunk = async (chunk: any[]) => {
    if (this.storeCountOnly) {
      await this.updateCounter(chunk.length);
      return;
    }

    const metaToSave = chunk.map(m => {
      if (this.selectedAttributes.length > 0) {
        return _.pick(m, ['partitionKey', ...this.selectedAttributes]);
      }
      return m;
    });
    const id = await this.storage.addChunk(this.searchId, metaToSave);
    this.state.chunkIds.push(id);
    this.state.totalCount += chunk.length;
  };

  hasMinimumToFlush() {
    return this.cachedItems.length >= this.cachingPolicy.dbMinFlushItems
  }

  public store = async (items: any[]) => {
    const filtered = items.filter(this.filter.accept);
    filtered.forEach(i => this.cachedItems.push(i));
    log.trace('store', `${items.length}->${filtered.length} items after filtering to store`)
    if (!this.isInitialFlushDone && this.hasMinimumToFlush()) {
      this.isInitialFlushDone = true
      await this.flushOnce();
    }
    if (this.flushImmediatly && this.hasMinimumToFlush()) {
      await this.flushOnce();
    }
    this.startAutoFlushing();
  };

  async flushOnce() {
    if (this.storeCountOnly) {
      const nextPortionLength = this.cachedItems.length;
      this.cachedItems.splice(0, nextPortionLength)
      log.trace('flushOnce(count)',
        `cache length ${this.cachedItems.length}`,
        `interval to flush [${this.counter} -> ${this.counter + nextPortionLength})(${nextPortionLength} items)`
      )
      this.counter += nextPortionLength;
      if (nextPortionLength > 0) {
        await this.updateCounter(nextPortionLength);
      }
      return this.cachedItems.length;
    }

    const maxInChunk = this.storeCountOnly ? Number.MAX_SAFE_INTEGER : this.cachingPolicy.dbChunkMaxLength
    const portion = takeFromQueue(this.cachedItems, maxInChunk);
    const start = this.counter;
    const end = this.counter + portion.length
    this.counter = end;
    log.trace('flushOnce',
      `cache length ${this.cachedItems.length}`,
      `interval to flush [${start} -> ${end})(${portion.length} items)`
    )
    if (portion.length > 0) {
      await this.saveNextChunk(portion);
    }
    return this.cachedItems.length;
  }

  async flushRest() {
    trace('flushRest', this.cachedItems.length, 'items left')

    if (this.storeCountOnly) {
      await this.flushOnce();
      return;
    }
    while (this.cachedItems.length > 0) {
      await this.flushOnce();
    }
  }

  stop() {
    log.info('results-caching-service -> stop');
    this.isStopped = true;
    this.cachedItems.splice(0, this.cachedItems.length);
  }
}
