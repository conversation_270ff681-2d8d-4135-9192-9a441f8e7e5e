/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import 'source-map-support/register';
import TryQuickSearchHandler from './quick-search/quick-search.js';
import AsyncSearchHandler from './async-search/async-search.js';
import {AnswersCountStart, StartAsyncSearch, TryQuickSearch} from './app.js';
import * as log from './support/logger.js'

const runOperation = async (event: any) => {
  if (event[TryQuickSearch.request]) {
    return {
      [TryQuickSearch.response]: await TryQuickSearchHandler(event[TryQuickSearch.request]),
    };
  }
  if (event[StartAsyncSearch.request]) {
    return {
      [StartAsyncSearch.response]: await AsyncSearchHandler(event[StartAsyncSearch.request]),
    }
  }
  if (event[AnswersCountStart.request]) {
    log.info(AnswersCountStart, 'is not implemented yet')
    return {
      [AnswersCountStart.response]: {},
    };
  }
  throw new Error(`No supported operations among ${Object.keys(event)}`);
}

const handler = async (event: any, ctx: any) => {
  log.info(JSON.stringify(event, null, 2));
  try {
    const response = await runOperation(event);
    log.debug('Response:', response);
    return response;
  } catch (e: any) {
    log.info('[ERROR]', e);
    return {
      Error: {
        message: e.message,
        code: e.code || 'survey.search.generic',
        stack: e.stack,
      }
    }
  }
}

export {
  handler,
}
