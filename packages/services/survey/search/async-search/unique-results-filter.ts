/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import _ = require('lodash');
import {ResultsFilter} from '../results-storage/results-caching-service.js';

export class UniqueResultsFilter implements ResultsFilter {
  uniqueSet: Set<string>;

  constructor(private uniqueKey: string) {
    this.uniqueSet = new Set();
  }

  accept = (record: any): boolean => {
    if (_.isNil(record)) {
      return false;
    }
    const keyValue = record[this.uniqueKey];
    if (!keyValue) {
      return false;
    }
    const presents = this.uniqueSet.has(keyValue);
    if (presents) {
      return false;
    }
    this.uniqueSet.add(keyValue);
    return true;
  }


}
