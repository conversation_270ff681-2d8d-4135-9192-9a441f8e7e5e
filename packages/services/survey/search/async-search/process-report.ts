/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {StartAsyncSearchInput} from './async-search.js';
import {AsyncSearchIndexDbRecord} from '../common/types.js';

export interface ProcessReport {
  searchId: string,
  totalCount: number,
  chunksCount: number,
  s3BucketName?: string,
  chunks: string[],
  status: string,
  errorMessage?: string,
  errorDetails?: any,
  startedAt: string,
  finishedAt: string,
  searchRequest: StartAsyncSearchInput,
  debug: any,
}


export const buildFromIndexRecord = (request: StartAsyncSearchInput, indexRecord: AsyncSearchIndexDbRecord): ProcessReport => {
  return {
    status: indexRecord.status,
    searchId: indexRecord.searchId,
    s3BucketName: indexRecord.s3BucketName,
    startedAt: indexRecord.startedAt,
    finishedAt: indexRecord.finishedAt,
    chunks: indexRecord.chunks,
    totalCount: indexRecord.totalCount,
    chunksCount: indexRecord.chunks.length,
    errorDetails: indexRecord.errorDetails,
    errorMessage: indexRecord.errorMessage,
    searchRequest: request,
    debug: indexRecord.debug,
  }
}
