/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {Query, Table} from 'common/utils/db/table';
import  {getEnvironmentCredentials} from 'common/utils/aws-helper.js'
import * as AWS from 'aws-sdk';
import * as log from '../support/logger.js';
import {unixTimeAfterHours} from '../support/utils.js';
import {BatchDownloader} from './batch-downloader.js';
import {MergedResultsModel} from './merged-results-filter.js';
import {createDbClient, createS3Client} from '../app-aws.js';

import * as _ from 'lodash';
import {StorageIs} from '../common/types.js';
import console from "console";
import {logger} from "../../../common/logs/logger.js";

export class AsyncSearchExtFunctions implements StorageIs {
  s3StorageFolder: string | undefined;
  dbClient: AWS.DynamoDB.DocumentClient;
  chunkCounter = 0;
  constructor(private resources: {
    surveyResultsTable: string,
    surveyConfigsTable: string,
    surveyCalendarsTable: string,
    resultsCacheTable: string,
    s3BucketName?: string,
    lambdaName: string,
  }) {
    this.dbClient = createDbClient();
  }

  public setS3StorageFolder(folderKey: string) {
    this.s3StorageFolder = folderKey;
  }

  get s3BucketName() {
    return this.resources.s3BucketName;
  }

  public getConfig = async (surveyId) => {
    const table = new Table(this.resources.surveyConfigsTable, null, getEnvironmentCredentials());
    logger.debug("getConfig", surveyId, 'from', this.resources.surveyConfigsTable)
    return await table.getItem({pk: surveyId});
  }

  public doResultsQuery = async (q: Query) => {
    const table = new Table(this.resources.surveyResultsTable, null, getEnvironmentCredentials());
    return await table.queryPage(q);
  }

  public getIndex = async (searchId: string) => {
    const client = this.dbClient;
    const res = await client.get({
      TableName: this.resources.resultsCacheTable,
      Key: {
        partitionKey: searchId,
        sortKey: 'index'
      }
    }).promise();
    return res ? res.Item : undefined;
  }

  public initialSetIndex = async (searchId, params: any) => {
    const table = new Table(this.resources.surveyResultsTable, {
      pkField: 'partitionKey',
      skField: 'sortKey'
    }, getEnvironmentCredentials());
    const existed = await table.queryItems({
      query: 'partitionKey = :pk',
      mapping: {':pk': searchId},
      // attributes: ['partitionKey', 'sortKey'],
    });
    if (existed.length > 0) {
      const chunks = existed.filter(v => v.sortKey !== 'index');
      await table.deleteItems(chunks.map(c => ({pk: c.partitionKey, sk: c.sortKey})));
    }
    await table.putItem(params, {
      pk: searchId,
      sk: 'index'
    });
    return params;
  };

  public addToTotalCounter = async (searchId: string, addToCounter: number) => {
    const client = this.dbClient;
    return client.update({
      TableName: this.resources.resultsCacheTable,
      Key: {
        partitionKey: searchId,
        sortKey: 'index',
      },
      UpdateExpression: 'SET totalCount=totalCount+:itemsLen',
      ExpressionAttributeValues: {
        ':itemsLen': addToCounter,
      },
      ReturnValues: 'ALL_NEW',
    }).promise();
  }

  public updateIndex = async (searchId: string, params: any) => {
    const table = new Table(this.resources.surveyResultsTable, null, getEnvironmentCredentials());
    return await table.update(params, {
      pk: searchId,
      sk: 'index'
    });
  };

  public addChunk = async (searchId, items: any[]) => {
    const chunkId = this.generateChunkId(searchId, items.length);
    log.trace('addChunk', 'pk', searchId, 'sortKey', chunkId, items.length, 'items');
    if (this.useS3()) {
      await this.addChunkToS3(chunkId, items);
    } else {
      await this.addChunkToDb(searchId, items, chunkId);
    }
    await this.dbClient.update({
      TableName: this.resources.resultsCacheTable,
      Key: {
        partitionKey: searchId,
        sortKey: 'index',
      },
      UpdateExpression: `SET totalCount=totalCount+:itemsLen, chunks = list_append(chunks, :chunkId)`,
      ExpressionAttributeValues: {
        ':itemsLen': items.length,
        ':chunkId': [chunkId],
      }
    }).promise();
    return chunkId;
  }

  private addChunkToDb = async (searchId: string, items: any[], chunkId: string) => {
    await this.dbClient.put({
      TableName: this.resources.resultsCacheTable,
      Item: {
        partitionKey: searchId,
        sortKey: chunkId,
        count: items.length,
        data: items,
        expireAt: unixTimeAfterHours(3),
      },
    }).promise();
  }

  private addChunkToS3 = async (chunkId: string, items: any[]) => {
    const s3Client = createS3Client();
    await s3Client.putObject({
      Key: chunkId,
      Body: JSON.stringify(items),
      Bucket: this.resources.s3BucketName as string,
    }).promise();
  }

  public useS3() {
    return !!this.s3StorageFolder
  }

  generateChunkId(searchId: string, itemsCount: number) {
    if (!this.useS3()) {
      return this.generateUUID();
    }
    const s3filename = _.padStart(`${this.chunkCounter++}`, 3, '0') + `_chunk_${itemsCount}items.json`;
    return `temp/${this.s3StorageFolder}/${s3filename}`
  }

  generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0, v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  };

  public doAsyncLambdaCall = async (payload: any, lambdaName: string = this.resources.lambdaName) => {
    const client = new AWS.Lambda(getEnvironmentCredentials());
    await client.invoke({
      FunctionName: lambdaName,
      InvocationType: "Event",
      Payload: JSON.stringify(payload),
    }).promise()
  };

  public async debugGetFirst100Results(searchId: string) {
    const client = this.dbClient;
    const res = await client.query({
      TableName: this.resources.resultsCacheTable,
      KeyConditionExpression: 'partitionKey = :pk',
      ExpressionAttributeValues: {
        ':pk': searchId,
      }
    }).promise();
    if (!res.Items) throw "error";
    const index = res.Items.find(i => i.sortKey === 'index');
    if (!index) throw "error"
    let counter = 100;
    const mergedResults:MergedResultsModel[] = [];
    for await (const chunkId of index.chunks) {
      const chunk = res.Items.find(i => i.sortKey === chunkId);
      if (!chunk) {
        continue;
      }
      const ids = chunk.data.slice(0, counter).map(id => id.partitionKey);
      counter -= ids.length;
      const downloader = new BatchDownloader(this.doResultsQuery);
      const results = await downloader.download(ids);
      results.map(r => new MergedResultsModel(r)).forEach(f => mergedResults.push(f));
      if (counter <= 0) {
        break;
      }
    }
    return mergedResults;
  }
}
