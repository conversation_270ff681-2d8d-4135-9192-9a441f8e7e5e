/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {SearchManager} from "./search-manager.js";
import {waitFor} from "../support/utils.js";

export type FullScanProcessStorage = (items: any[]) => Promise<any>

export class FullScanSearchProcess {

  constructor(private manager: SearchManager, private store: FullScanProcessStorage) {
  }

  async start() {
    const requestBuilder = await this.manager.getRequestBuilder();
    const q = requestBuilder.createFilterExpressionOnlyRequest();
    const mainScanner = this.manager.createExecutorForFullScan(q);
    mainScanner.setMaxResultsCount(this.manager.maxResultsCount);
    mainScanner.setTimeoutSeconds(this.manager.searchTimeoutSec);

    const saver = async () => {
      while (!mainScanner.isFinished || mainScanner.hasSomeResults()) {
        const nextChunk = mainScanner.takeSome(this.manager.isCountOnlyMode ? 3000 : 1000);
        if (nextChunk.length > 0) {
          await this.store(nextChunk.map(r => r.answers[0]))
          await waitFor({milliseconds: 100});
        } else {
          await waitFor({milliseconds: this.manager.isCountOnlyMode ? 1000 : 500});
        }
      }
    }
    await Promise.all([mainScanner.start(), saver()]);
  }
}
