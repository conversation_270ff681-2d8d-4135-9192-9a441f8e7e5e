/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import {DatesFilterModel, FieldsFilterModel} from "../common/results-query-helpers.js";
import {ReservationLineModel} from "../common/results_model.js";
import {MergedRecordType} from "../common/types.js";
import * as _ from 'lodash';


export class MergedResultsModel {
  private record: MergedRecordType;

  constructor(merged: MergedRecordType) {
    this.record = merged;
  }

  getFieldValue(key: string) {
    const field = this.record.fields.find((f) => f.itemKey === key);
    return field ? field.value : null;
  }

  isCheckIn(checkValues: string[]) {
    return (checkValues.includes(this.record['check'] || ''));
  }

  isUpdatedAtIn(from: number, to: number) {
    const value = this.record['updatedAt'] || 0;
    return value >= from && value <= to;
  }

  isCreatedAtIn(from: number, to: number) {
    const value = this.record['createdAt'] || 0;
    return value >= from && value <= to;
  }

  isFieldIn(itemKey: string, values: string[], mode = 'exact') {
    const fieldValue = this.getFieldValue(itemKey) || ''
    if (mode === 'exact') {
      return values.includes(fieldValue);
    }
    return values.some((value) => fieldValue.indexOf(value) > -1);
  }

  isAppointmentIn(itemKey: string, from: number, to: number) {
    const line = this.getFieldValue(itemKey);
    if (!line) return false;
    const itemModel = new ReservationLineModel({value: line});
    if (!itemModel.hasValidDate) {
      return false;
    }
    return itemModel.timestamp >= from && itemModel.timestamp <= to;
  }

  isCategoryIn(itemKey: string, categoryIds: string[]) {
    const line = this.getFieldValue(itemKey);
    if (!line) return false;
    const itemModel = new ReservationLineModel({value: line});
    if (!itemModel.hasValidCategory) {
      return false;
    }
    return categoryIds.includes(itemModel.categoryIdPrefix);
  }

  getCategoryId(itemKey: string) {
    const line = this.getFieldValue(itemKey);
    if (!line) return null;
    const itemModel = new ReservationLineModel({value: line});
    if (!itemModel.hasValidCategory) {
      return null;
    }
    return itemModel.categoryIdPrefix;
  }

  get check() {
    return this.record['check'];
  }
  get partitionKey() {
    return this.record['partitionKey'];
  }
  get values() {
    return this.record.fields.map(v => v.value).filter(v => !_.isNil(v)).join(',')
  }
}

export class MergedResultsFilter {
  private allowedCategoriesIds: string[] | undefined;
  private _categoriesPredicate: (id: string) => boolean;

  constructor(private fields: FieldsFilterModel, private dates: DatesFilterModel, private reservationKey?: string) {
  }

  set categoriesPredicate(p: (id: string) => boolean) {
    this._categoriesPredicate = p;
  }

  setAllowedCategories(categoryIds?: string[]) {
    this.allowedCategoriesIds = categoryIds;
  }

  buildFilterPredicate() {
    return (item: MergedRecordType) => {
      const model = new MergedResultsModel(item);
      let matches = true;

      const fieldsCheck = this.fields.ignore('check').ignore(this.reservationKey).getKeys();
      fieldsCheck.forEach((key) => {
        const searchMode = this.fields.getOptionValue(key, 'mode') || 'exact';
        matches = matches && model.isFieldIn(key, this.fields.getValues(key), searchMode);
      })
      if (!matches) return false;

      if (this.fields.hasValues('check')) {
        matches = matches && model.isCheckIn(this.fields.getValues('check'));
      }
      if (!matches) return false;

      if (this.dates.hasValues('createdAt')) {
        const interval = this.dates.getBetweenInUnix('createdAt');
        if (interval) {
          matches = matches && model.isCreatedAtIn(interval.from, interval.to);
        }
      }
      if (!matches) return false;

      if (this.dates.hasValues('updatedAt')) {
        const interval = this.dates.getBetweenInUnix('updatedAt');
        if (interval) {
          matches = matches && model.isUpdatedAtIn(interval.from, interval.to);
        }
      }
      if (!matches) return false;

      if (this.reservationKey && this.dates.hasValues('appointment_date')) {
        const interval = this.dates.getBetweenInUnix('appointment_date');
        if (interval) {
          matches = matches && model.isAppointmentIn(this.reservationKey, interval.from, interval.to)
        }
      }
      if (!matches) return false;

      if (this.reservationKey
        && this.fields.hasValues(this.reservationKey)
        && this.allowedCategoriesIds) {
        matches = matches && model.isCategoryIn(this.reservationKey, this.allowedCategoriesIds);
      }

      if (this.reservationKey) {
        const categoryId = model.getCategoryId(this.reservationKey);
        if (this._categoriesPredicate && categoryId) {
          matches = matches && this._categoriesPredicate(categoryId);
        }
      }

      return matches;
    }
  }

  doFiltering(items: MergedRecordType[]) {
    return items.filter(this.buildFilterPredicate());
  }
}
