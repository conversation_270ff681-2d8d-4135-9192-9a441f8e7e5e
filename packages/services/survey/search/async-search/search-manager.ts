/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {TeamsFilter} from '../common/types.js';
import {ResultsQueryBuilder, ResultsQueryModel} from './query-builder.js';
import {DatesFilter, DatesFilterModel, FieldsFilter, FieldsFilterModel} from '../common/results-query-helpers.js';
import {ConfigSchemaFieldModel, SurveyConfig, SurveyConfigModel} from '../common/config_model.js';
import {AsyncSearchExtFunctions} from './ext.js';
import {SingleKeyQueryExecutor} from './single-key-query-executor.js';
import {InMemoryCategoryAccessFilter, InMemoryFilter} from './in-memory-filter.js';
import {FullScanExecutor} from './fullscan-executor.js';
import {AccessManager} from './access-manager.js';
import * as _ from 'lodash';
import {MergedResultsFilter} from './merged-results-filter.js';
import {BatchDownloader} from './batch-downloader.js';


export class SearchManager {
  public fields: FieldsFilterModel;
  public dates: DatesFilterModel;
  public categories: string[] | undefined;
  private surveyId: string;
  private surveyConfig: SurveyConfigModel;
  private lastEvaluatedKey: any;
  private teamsFilter: TeamsFilter = {};
  private countOnly = false;
  private resultsLimit: number | undefined = undefined;
  private accessManager: AccessManager;
  private timeoutSec: number = 14*60

  constructor(private extServices: AsyncSearchExtFunctions) {
  }


  setFieldsFilter(commonFilter: FieldsFilter) {
    this.fields = new FieldsFilterModel(commonFilter || {});
  }

  setDatesFilter(datesFilter: DatesFilter) {
    this.dates = new DatesFilterModel(datesFilter || {});
  }

  setCategoriesLimiter(categoriesIds?: string[]) {
    this.categories = categoriesIds;
  }

  setTeamsLimiter(userTeams?: TeamsFilter) {
    this.teamsFilter = userTeams || {}
  }

  setCountOnly(co?: boolean) {
    this.countOnly = !!co;
  }

  setResultsLimit(limit: number) {
    this.resultsLimit = limit === -1 ? Number.MAX_SAFE_INTEGER : limit;
  }

  setSearchTimeoutSec(timeout: number) {
    this.timeoutSec = timeout;
  }

  setSurvey(surveyId: string, surveyConfig?: any) {
    this.surveyId = surveyId;
    if (surveyConfig) {
      this.surveyConfig = new SurveyConfigModel(surveyConfig);
    }
  }

  public hasLimitsByCategory() {
    return !!this.categories && this.categories.length > 0;
  }

  public get isCountOnlyMode() {
    return this.countOnly;
  }

  public get maxResultsCount() {
    return _.isNil(this.resultsLimit) ? Number.MAX_SAFE_INTEGER : this.resultsLimit;
  }

  public get searchTimeoutSec() {
    return this.timeoutSec;
  }

  public hasAccessRules() {
    return Object.values(this.teamsFilter).some(t => t === false);
  }

  getConfigModel() {
    return this.surveyConfig;
  }

  setLastEvaluatedKey(key: any) {
    this.lastEvaluatedKey = key;
  }

  public async prepare() {
    if (!this.surveyConfig) {
      const configJson: SurveyConfig = await this.extServices.getConfig(this.surveyId);
      this.surveyConfig = new SurveyConfigModel(configJson);
    }
  }

  public isAccessableCategory = (categoryId: string) => {
    const val = this.teamsFilter[categoryId];
    return val === undefined || val === true;
  }

  public async getRequestBuilder() {
    return new ResultsQueryBuilder(this.surveyId, this.fields, this.dates, this.lastEvaluatedKey);
  }

  public createExecutorForSimpleKey(key: string, queryModel: ResultsQueryModel) {
    return new SingleKeyQueryExecutor(key, queryModel, () => true, this.extServices.doResultsQuery);
  }

  public createBatchDownloader() {
    return new BatchDownloader(this.extServices.doResultsQuery);
  }

  public createExecutorNoParametersIndexCount(key: string, queryModel: ResultsQueryModel) {
    const accessFilter = key === this.surveyConfig.getReservationFieldKey()
      ? new InMemoryCategoryAccessFilter(this.teamsFilter).predicate
      : () => true;
    return new SingleKeyQueryExecutor(key, queryModel, accessFilter, this.extServices.doResultsQuery);
  }

  public createImMemoryFilterPredicate() {
    const inMemoryFilter = new InMemoryFilter(this.dates, this.categories);
    return inMemoryFilter.predicate;
  }

  public createExecutorForCategoryKey(key: string, queryModel: ResultsQueryModel) {
    return new SingleKeyQueryExecutor(key, queryModel, this.createImMemoryFilterPredicate(), this.extServices.doResultsQuery);
  }

  public createExecutorForFullScan(queryModel: ResultsQueryModel) {
    return new FullScanExecutor(queryModel, this, this.extServices.doResultsQuery);
  }

  public createMergedItemsFilterPredicate() {
    return new MergedResultsFilter(this.fields, this.dates, this.surveyConfig.getReservationFieldKey()).buildFilterPredicate()
  }

  public selectKeyForPriorityIndexSearch(): string | null {
    const indexFields = this.surveyConfig.getSchema().filter((field) => {
      const model = new ConfigSchemaFieldModel(field);
      return !model.isAdminItem && model.isIndexable;
    }).map(f => f.itemKey);
    const fieldsInFilter = this.fields.getKeys();
    const intersection = _.intersection(indexFields, fieldsInFilter);
    if (intersection.length === 0) {
      return null;
    }
    return intersection.sort()[0];
  }

  public selectKeyForIndexSearch() {
    if (this.hasAccessRules()) {
      return this.surveyConfig.getReservationFieldKey();
    }
    const indexField = this.surveyConfig.getSchema().find((field) => {
      const fieldModel = new ConfigSchemaFieldModel(field);
      return !fieldModel.isAdminItem && fieldModel.isIndexable && fieldModel.isRequired && !fieldModel.isVirtual;
    });
    if (indexField) {
      return indexField.itemKey;
    }
    const requiredField = this.surveyConfig.getSchema().find((field) => {
      const fieldModel = new ConfigSchemaFieldModel(field);
      return !fieldModel.isAdminItem && fieldModel.isRequired && !fieldModel.isVirtual;
    });
    if (requiredField) {
      return requiredField.itemKey;
    }
    const nonAdminItem = this.surveyConfig.getSchema().find((field) => {
      const fieldModel = new ConfigSchemaFieldModel(field);
      return !fieldModel.isAdminItem && !fieldModel.isVirtual;
    });
    if (nonAdminItem) {
      return nonAdminItem.itemKey;
    }
    return null;
  }

  public createAccessManager() {
    if (!this.accessManager) {
      this.accessManager = new AccessManager(this.teamsFilter,
        this.surveyConfig.getReservationFieldKey(),
        this.createImMemoryFilterPredicate(),
        {query: this.extServices.doResultsQuery})
    }
    return this.accessManager;
  }

  public getExtServices() {
    return this.extServices;
  }

  public finishAll() {
    if (this.accessManager) {
      this.accessManager.stop();
    }
  }
}
