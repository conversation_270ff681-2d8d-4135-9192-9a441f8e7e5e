/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {ResultsQueryModel} from "./query-builder.js";
import {SingleKeyQueryExecutor, SingleKeyQueryExecutorMethod} from "./single-key-query-executor.js";
import {ReservationLineModel} from "../common/results_model.js";
import * as log from '../support/logger.js';
import { startDurationCounter} from "../support/utils.js";

/* eslint-disable @typescript-eslint/no-var-requires */
const PromisePool = require('es6-promise-pool');

export type AccessManagerStorageIs = {
  query: SingleKeyQueryExecutorMethod,
}
type Mapping = { [key: string]: boolean }

export class AccessManager {
  private mapping: Mapping = {};
  private preLoaded = false;
  private stopped = false;
  private preLoadExecutor: SingleKeyQueryExecutor;

  constructor(private categoryAccessMap: Mapping,
              private categoryItemKey: string | undefined,
              private rowPredicate: undefined | ((item: any) => boolean),
              private storage: AccessManagerStorageIs) {
  }

  public async preLoadPkCategoryMapping(key: string, q: ResultsQueryModel) {
    if (this.stopped) return;

    log.trace('preLoadPkCategoryMapping', 'started');
    const dc = startDurationCounter();
    this.preLoadExecutor = new SingleKeyQueryExecutor(key, q, this.rowPredicate || (() => true), this.storage.query);
    await this.preLoadExecutor.start();
    this.preLoadExecutor.resultsQueue.forEach(i => {
      const reservation = new ReservationLineModel(i);
      if (reservation.hasValidCategory) {
        this.mapping[i.partitionKey] = this.categoryAccessMap[reservation.categoryIdPrefix];
      }
    });
    log.trace('preLoadPkCategoryMapping', this.stopped ? 'stopped' : 'finished', dc.elapsedMilliseconds(), 'ms elapsed')
    this.preLoaded = true;
  }

  public get isPreloaded() {
    return this.preLoaded;
  }

  private isCategoryAllowed(category) {
    return this.categoryAccessMap[category] !== false;
  }

  private isPkAllowed(pk: string) {
    const res: boolean | undefined = this.mapping[pk];
    return res !== false;
  }

  public hasAccessUsingCached(partitionKey) {
    return this.isPkAllowed(partitionKey);
  }

  public async hasAccess(partitionKey: string) {
    if (this.preLoaded) {
      return this.isPkAllowed(partitionKey);
    }
    const response = await this.storage.query({
      query: 'partitionKey = :pk and begins_with(sortKey, :sk)',
      mapping: {
        ':pk': partitionKey,
        ':sk': this.categoryItemKey,
      }
    });
    if (response.items.length === 0) {
      return true;
    }
    const categoryRecord = response.items[0];
    if (!categoryRecord.value) {
      return true;
    }
    const category = new ReservationLineModel(categoryRecord);
    if (!category.hasValidCategory) {
      return true;
    }
    return this.isCategoryAllowed(category.categoryIdPrefix);
  }

  public async filterMany(items: any[]) {
    const allowed: any[] = [];
    const tasks = items.map(i => async () => {
      if (await this.hasAccess(i.partitionKey)) {
        allowed.push(i);
      }
    });
    const executor = new PromisePool(() => {
      const task = tasks.pop();
      if (task) {
        return task();
      }
      return;
    }, 25)
    await executor.start();
    return allowed;
  }

  public stop() {
    this.stopped = true;
    if (this.preLoadExecutor) {
      this.preLoadExecutor.stop();
    }
  }

}