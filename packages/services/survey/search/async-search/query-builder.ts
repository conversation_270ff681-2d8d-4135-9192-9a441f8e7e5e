/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {
  ArgumentMappingType,
  DatesFilterModel,
  ExpressionType,
  FieldsFilterModel,
  NULL_VALUE_STR,
  ParameterMappingType,
  RequestExpressionsBuilder
} from "../common/results-query-helpers.js";

export type ResultsQueryModel = {
  keyExpression: string,
  filterExpression?: string,
  argumentsMapping: ArgumentMappingType,
  valuesMapping: ParameterMappingType,
  indexName?: string,
  lastEvaluatedKey?: any,
  requiredAttributes?: string[],
}

export class ResultsQueryBuilder {
  private metadataOnlyMode = false;

  constructor(
    public readonly surveyId: string,
    private fields: FieldsFilterModel,
    private dates: DatesFilterModel,
    public readonly lastEvaluatedKey?: any) {
  }

  private createAndExpressionFor(...keysToBuild: string[]): ExpressionType {
    const keys = [...keysToBuild];
    const builder = new RequestExpressionsBuilder();
    const expressions = keys.map((key) => {
      if (['createdAt', 'updatedAt'].includes(key)) {
        const dates = this.dates.getBetweenInUnix(key)
        if (dates) {
          return builder.makeInterval(key, dates);
        }
      } else if (key === 'check') {
        if (this.fields.hasValues(key)) {
          const statuses = this.fields.getValues(key);
          if (statuses.includes('未対応')) {
            statuses.push(NULL_VALUE_STR);
          }
          return builder.makeOr('check', statuses);
        }
      } else {
        if (this.fields.hasValues(key)) {
          const mode = this.fields.getOptionValue(key, 'mode') || 'exact';
          return builder.makeOr('value', this.fields.getValues(key), mode);
        }
      }
      return RequestExpressionsBuilder.emptyExpression;
    });
    return builder.joinAnd(...expressions);
  }

  public createFilterExpressionOnlyRequest(): ResultsQueryModel {
    const filterExpression = this.createAndExpressionFor(
      'createdAt', 'updatedAt', 'check',
    )
    return {
      keyExpression: 'surveyId = :surveyId',
      valuesMapping: {
        [':surveyId']: this.surveyId,
        ...filterExpression.valuesMapping,
      },
      argumentsMapping: {
        ...filterExpression.argumentsMapping,
      },
      filterExpression: filterExpression.expressionString || undefined,
      indexName: 'surveyId-partitionKey-index',
      lastEvaluatedKey: this.lastEvaluatedKey,
    }
  }

  public createNoParametersIndexCount(itemKey: string): ResultsQueryModel {
    const filterBy = ['check', 'createdAt', 'updatedAt'];
    const filterExpression = this.createAndExpressionFor(...filterBy);
    return {
      keyExpression: 'surveyId = :surveyId AND begins_with(sortKey, :sortKey)',
      valuesMapping: {
        [':surveyId']: this.surveyId,
        [':sortKey']: `${itemKey}#`,
        ...filterExpression.valuesMapping,
      },
      argumentsMapping: {
        ...filterExpression.argumentsMapping,
        ['#value']: 'value'
      },
      filterExpression: filterExpression.expressionString,
      indexName: 'surveyId-sortKey-index',
      requiredAttributes: ['partitionKey', 'itemKey', '#value', 'userId'],
    }
  }

  public createNoValueSearchQueryModel(itemKey: string): ResultsQueryModel {
    const filterBy = ['check', 'createdAt', 'updatedAt'];
    const filterExpression = this.createAndExpressionFor(...filterBy);
    return {
      keyExpression: 'surveyId = :surveyId AND begins_with(sortKey, :sortKey)',
      valuesMapping: {
        [':surveyId']: this.surveyId,
        [':sortKey']: `${itemKey}#`,
        ...filterExpression.valuesMapping,
      },
      argumentsMapping: {
        ...filterExpression.argumentsMapping,
      },
      filterExpression: filterExpression.expressionString,
      indexName: 'surveyId-sortKey-index',
    }
  }


  public createParallelSearchQueryModel(key: string, value: string): ResultsQueryModel {
    const filterBy = ['check', 'createdAt', 'updatedAt'];
    const searchMode = this.fields.getOptionValue(key, 'mode') || 'exact';
    const useNonExactSearch = searchMode !== 'exact'
    if (useNonExactSearch) {
      filterBy.push(key);
      const filterExpression = this.createAndExpressionFor(...filterBy);
      return {
        keyExpression: 'surveyId = :surveyId AND begins_with(sortKey, :sortKey)',
        valuesMapping: {
          [':surveyId']: this.surveyId,
          [':sortKey']: `${key}#`,
          ...filterExpression.valuesMapping,
        },
        argumentsMapping: {
          ...filterExpression.argumentsMapping,
        },
        filterExpression: filterExpression.expressionString,
        indexName: 'surveyId-sortKey-index',
      }
    }

    const filterExpression = this.createAndExpressionFor(...filterBy, key);
    return {
      keyExpression: 'surveyId = :surveyId AND sortKey = :sortKey',
      valuesMapping: {
        [':surveyId']: this.surveyId,
        [':sortKey']: `${key}#${value}`,
        ...filterExpression.valuesMapping,
      },
      argumentsMapping: {
        ...filterExpression.argumentsMapping,
      },
      filterExpression: filterExpression.expressionString,
      indexName: 'surveyId-sortKey-index',
    }
  }

}
