/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {DatesFilterModel} from "../common/results-query-helpers.js";
import {ReservationLineModel} from "../common/results_model.js";
import {TeamsFilter} from "../common/types.js";

type TestableItem = {
  partitionKey: string,
  itemKey: string,
  value?: string,
}

export class InMemoryFilter {
  private appointmentDates: null | { from: number; to: number };
  constructor(
    private dates: DatesFilterModel,
    private categoriesFilter?: string[],
  ) {
    this.appointmentDates = this.dates.getBetweenInUnix('appointment_date');
  }

  public predicate = (item: TestableItem) => {
    const itemModel = new ReservationLineModel(item);
    if (!itemModel.hasValidCategory) {
      return false;
    }
    if (this.categoriesFilter && !this.categoriesFilter.includes(itemModel.categoryIdPrefix)) {
      return false;
    }
    if (this.appointmentDates && !itemModel.hasValidDate) {
      return false;
    }
    if (this.appointmentDates && (itemModel.timestamp < this.appointmentDates.from || itemModel.timestamp > this.appointmentDates.to)){
      return false;
    }
    return true;
  };
}


export class InMemoryCategoryAccessFilter {
  constructor(private map: TeamsFilter) {
  }

  public predicate = (item: TestableItem) => {
    const itemModel = new ReservationLineModel(item);
    if (!itemModel.hasValidCategory) {
      return true;
    }
    if (this.map[itemModel.categoryIdPrefix] === false) {
      return false;
    }
    return true;
  }
}