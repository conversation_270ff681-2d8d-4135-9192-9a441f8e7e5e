/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {DatesFilter, DatesFilterModel, FieldsFilter, FieldsFilterModel} from '../common/results-query-helpers.js';
import {SurveyConfigModel} from '../common/config_model.js';
import {IndexSearchProcess} from './index-search-process.js';
import {countDurationMs, currentDateTimeISO, unixTimeAfterHours} from '../support/utils.js';
import * as log from '../support/logger.js'
import {FullScanSearchProcess} from './fullscan-search-process.js';
import {AsyncSearchExtFunctions} from './ext.js';
import {SearchManager} from './search-manager.js';
import {PriorityIndexSearchProcess} from './priority-index-search-process.js';
import {AsyncSearchIndexDbRecord, StorageController} from '../common/types.js';
import {buildFromIndexRecord} from './process-report.js';
import {AppContainer, createAppContainer} from '../app-container.js';
import {getContextInfo} from '../app.js';

export type S3StorageInput = {
  folderKey: string,
  resultsPerFile: number
}

export type StartAsyncSearchInput = {
  searchId: string,
  userId: string,
  countOnly?: boolean
  input: {
    surveyId: string,
    filterCommon: FieldsFilter,
    filterDate: DatesFilter,
    filterCategories?: string[],
    filterTeams?: { [key: string]: boolean }
    isMember: boolean
  },
  s3?: S3StorageInput,
  uniqueBy?: string,
  groupBy?: string,
  maxResultsCount?: number,
  ignoreNonLineUsers?: boolean
}

const canTryQuickSearch = (request: StartAsyncSearchInput, surveyConfig: SurveyConfigModel) => {
  const fieldsModel = new FieldsFilterModel(request.input.filterCommon);
  const hasFields = fieldsModel
    .ignore('check')
    .ignore(surveyConfig.getReservationFieldKey())
    .getKeys().length > 0;

  const datesModel = new DatesFilterModel(request.input.filterDate);
  const hasReservation = datesModel.hasReservation();

  const hasCategoriesLimitation = !!request.input.filterCategories;
  return hasFields || hasReservation || hasCategoriesLimitation;
}

const handler = async (event: StartAsyncSearchInput) => {
  const searchId = event.searchId;
  const appContainer = await createAppContainer(searchId, event);

  log.info(`[${searchId}]`, 'Async search started', event, currentDateTimeISO());
  try {
    await handlerInternal(appContainer, event);
    log.info(`[${searchId}]`, 'Async search finished', currentDateTimeISO());
    return await buildProcessReport(searchId, event, appContainer);
  } catch (e: any) {
    await appContainer.storage.updateIndex(searchId, {
      status: 'error',
      errorMessage: e.message,
      finishedAt: currentDateTimeISO(),
      expireAt: unixTimeAfterHours(24 * 7),
      errorDetails: {
        errorTime: currentDateTimeISO(),
        message: e.message,
        stack: e.stack,
      }
    });
    return await buildProcessReport(searchId, event, appContainer);
  } finally {
    log.info('Final status', await appContainer.storage.getIndex(searchId));
  }
}

const buildProcessReport = async(searchId: string, event: StartAsyncSearchInput, appContainer: AppContainer) => {
  const indexRecord = await appContainer.storage.getIndex(searchId);
  return buildFromIndexRecord(event, indexRecord as AsyncSearchIndexDbRecord)
}

const fixRequest = (event: StartAsyncSearchInput) => {
  event.input.filterDate = event.input.filterDate || {}
  event.input.filterCommon = event.input.filterCommon || {}
}

const selectProcessAndExec = async (
  event: StartAsyncSearchInput,
  storage: AsyncSearchExtFunctions,
  searchManager: SearchManager,
  storageService: StorageController,
) => {
  if (canTryQuickSearch(event, searchManager.getConfigModel())) {
    log.info('[async-search]', 'Index search: started', currentDateTimeISO())
    let process;
    if (searchManager.selectKeyForPriorityIndexSearch()) {
      process = new PriorityIndexSearchProcess(searchManager, storageService.store);
    } else {
      process = new IndexSearchProcess(searchManager, storageService.store);
    }
    await process.start();
    log.info('[async-search]', 'Index search: finished', currentDateTimeISO())
  } else {
    const key = searchManager.selectKeyForIndexSearch();
    if (event.countOnly && key) {
      log.info('[async-search]', 'Full count: started', currentDateTimeISO())
      const process = new IndexSearchProcess(searchManager, storageService.store);
      await process.indexCount(key);
      log.info('[async-search]', 'Full count: finished', currentDateTimeISO())
    } else {
      log.info('[async-search]', 'Full table search', currentDateTimeISO())
      const process = new FullScanSearchProcess(searchManager, storageService.store);
      await process.start();
      log.info('[async-search]', 'Full table search: finished', currentDateTimeISO())
    }
  }
}

const handlerInternal = async (appContainter: AppContainer, event: StartAsyncSearchInput) => {
  fixRequest(event);

  const storage = appContainter.storage;
  const index = await storage.getIndex(event.searchId)
  const contextInfo = getContextInfo();
  if (!index) {
    await storage.initialSetIndex(event.searchId, {
      status: 'in_progress',
      createdAt: currentDateTimeISO(),
      startedAt: currentDateTimeISO(),
      chunks: [],
      totalCount: 0,
      searchId: event.searchId,
      input: event.input,
      errorDetails: null,
      s3BucketName: storage.s3BucketName,
      errorMessage: "",
      expireAt: unixTimeAfterHours(7*24),
      debug: {
        cwLogGroup: contextInfo.cwLogGroup,
        cwLogStream: contextInfo.cwLogStream,
        functionName: contextInfo.functionName,
      }
    })
  } else {
    await storage.updateIndex(event.searchId, {
      status: 'in_progress',
      startedAt: currentDateTimeISO(),
      totalCount: 0,
      chunks: [],
      errorDetails: null,
      s3BucketName: storage.s3BucketName,
      errorMessage: "",
      debug: {
        cwLogGroup: contextInfo.cwLogGroup,
        cwLogStream: contextInfo.cwLogStream,
        functionName: contextInfo.functionName,
      }
    });
  }
  const startedAt = currentDateTimeISO();

  try {
    await selectProcessAndExec(event, storage, appContainter.searchManager, appContainter.storageController);
    await appContainter.storageController.flushRest();
    await storage.updateIndex(event.searchId, {
      status: 'finished',
      finishedAt: currentDateTimeISO(),
      processTimeMs: countDurationMs(startedAt),
    });
  } finally {
    appContainter.storageController.stop();
    appContainter.searchManager.finishAll();
  }
}

export default handler;
