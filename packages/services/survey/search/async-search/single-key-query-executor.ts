/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {ResultsQueryModel} from "./query-builder.js";
import {DateTime} from "luxon";
import * as log from '../support/logger.js';
import {ItemsRequestResultPage, OnePageQuery} from "common/utils/db/table";
import * as _ from "lodash";
import {startDurationCounter} from "../support/utils.js";
import {SurveyResult} from "../common/results_model.js";

export type SingleKeyQueryExecutorMethod = (q: OnePageQuery) => Promise<ItemsRequestResultPage>;

export class SingleKeyQueryExecutor {
  private startedAt: DateTime
  private finishedAt: DateTime
  private started = false;
  private stopped = false;
  private _resultsQueue: any[] = [];
  private _collectedPks: Set<string> = new Set();

  constructor(
    private key: string,
    private request: ResultsQueryModel,
    private rowPredicate: (item: any) => boolean,
    private query: SingleKeyQueryExecutorMethod
  ) {
  }

  private async doWork() {
    const ddtQuery: OnePageQuery = {
      index: this.request.indexName,
      filter: this.request.filterExpression || undefined,
      query: this.request.keyExpression,
      mapping: {...this.request.valuesMapping, ...this.request.argumentsMapping},
      attributes: _.union(this.request.requiredAttributes || [])
    }

    let lastEvaluatedKey = this.request.lastEvaluatedKey;
    let totalExtractedCounter = 0;
    const du = startDurationCounter();
    do {
      const dc = startDurationCounter();
      const result = await this.query({
        ...ddtQuery,
        lastEvaluatedKey,
      });
      const msForDb = dc.elapsedMilliseconds();
      lastEvaluatedKey = result.lastEvaluatedKey;
      totalExtractedCounter += result.items.length;
      let counter = 0;
      result.items.filter(this.rowPredicate).forEach(v => {
        if (!this._collectedPks.has(v.partitionKey)) {
          this._resultsQueue.push(v);
          this._collectedPks.add(v.partitionKey);
          counter++;
        }
      });
      const msForFilter = dc.elapsedMilliseconds() - msForDb;
      log.trace(`<${this.key}> scan`, `${result.items.length} extracted, ${counter} added, ${totalExtractedCounter} total extracted, `
        + `${this.collectedPks.size} total collected, ${dc.elapsedMilliseconds()} msec for request, ` +
        `${msForDb} for DB request, ${msForFilter} for filtering`)
    } while (!!lastEvaluatedKey && !this.stopped);
    log.debug(`<${this.key}> scan finished`, `${totalExtractedCounter} total extracted`, `${du.elapsedMilliseconds()}ms elapsed`);
  }

  public async start() {
    if (this.started) return;
    this.started = true;
    this.startedAt = DateTime.now();
    const timer = startDurationCounter();
    log.trace(`<${this.key}> scan`, 'started')
    try {
      await this.doWork();
    } catch (e: any) {
      log.info(e)
    } finally {
      this.finishedAt = DateTime.now();
      log.trace(`<${this.key}> scan`, 'finished after', timer.elapsedSeconds(), 'sec', this.collectedPks.size, 'items collected')
    }
  }

  public get resultsQueue() {
    return this._resultsQueue;
  }

  public get collectedPks() {
    return this._collectedPks;
  }

  public get collectedPksArray() {
    return Array.from(this._collectedPks);
  }

  get isFinished() {
    return !!this.finishedAt;
  }

  public takeSome(limit = 100) {
    const nextPortion: SurveyResult[] = [];
    while ((nextPortion.length < limit) && (this._resultsQueue.length > 0)) {
      const next = this._resultsQueue.shift();
      if (next) {
        nextPortion.push(next);
      }
    }
    return nextPortion;
  }

  public hasSomeResults() {
    return this._resultsQueue.length > 0
  }

  public stop() {
    this.stopped = true;
  }
}
