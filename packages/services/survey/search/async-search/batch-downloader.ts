/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import {MergedRecordType} from "../common/types.js";
import * as _ from "lodash";
import {ItemsRequestResultPage, Query} from "@lsc/backend-shared-utils/lib/db/table";
// eslint-disable-next-line @typescript-eslint/no-var-requires
const PromisePool = require('es6-promise-pool');

const SURROGATE_FIELDS = ['indexId', 'userSearchKey', 'sortKey', 'partitionKey', 'title'];
const INDIVIDUAL_FIELDS = ['itemKey', 'value']
const COMMON_FIELDS_OMIT = [...SURROGATE_FIELDS, ...INDIVIDUAL_FIELDS];

export class ItemsMergeHelper {

  public groupAndMerge(items: Required<{ partitionKey: string }>[]) {
    const grouped = Object
      .entries(_.groupBy(items, 'partitionKey'))
      .map(([pk, items]) => ({id: pk, items}));
    return this.mergeRecords(grouped);
  }

  private mergeRecords(groupedLines: { id: string, items: any[] }[]): MergedRecordType[] {
    return groupedLines.map((group) => {
      const singleItem = _.omit(group.items[0], ...COMMON_FIELDS_OMIT);
      return {
        partitionKey: group.id,
        ...singleItem,
        fields: group.items.map((item) => _.pick(item, INDIVIDUAL_FIELDS)),
      }
    })
  }
}

export class BatchDownloader {

  constructor(private doQuery: (q: Query) => Promise<ItemsRequestResultPage>) {}

  async download(partitionKeys: string[], pollSize?:number) {
    if (partitionKeys.length === 0) {
      return [];
    }
    if (!pollSize) {
      pollSize = partitionKeys.length;
    }

    const collector: MergedRecordType[] = [];
    const helper = new ItemsMergeHelper();

    const executeDownloadTask = async (partitionKey: string) => {
      const response = await this.doQuery({
        query: 'partitionKey = :pk',
        mapping: {
          [':pk']: partitionKey,
        },
      });
      const records = helper.groupAndMerge(response.items);
      records.forEach(t => collector.push(t));
    }
    const copy = [...partitionKeys];
    const executor = new PromisePool(() => {
      const key = copy.pop();
      if (key) {
        return executeDownloadTask(key);
      }
      return;
    }, pollSize);
    await executor.start();

    return collector;
  }

}
