/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {SearchManager} from "./search-manager.js";
import {SingleKeyQueryExecutor} from "./single-key-query-executor.js";
import * as _ from "lodash";
import {MultiMap, waitFor} from "../support/utils.js";
import {AccessManager} from "./access-manager.js";

/* eslint-disable @typescript-eslint/no-var-requires */
const PromisePool = require('es6-promise-pool');

export type IndexSearchProcessStorage = (items: any[]) => Promise<any>

const nop = () => {
  /* no operation */
}

export class IndexSearchProcess {

  private collectedRecords: any[] = [];

  constructor(private manager: SearchManager, private store: IndexSearchProcessStorage) {
  }

  private async filterAccess(accessManager: AccessManager, sourcePks: string[]) {
    let pointer = 0;
    const result: string[] = [];
    const taskFactory = async (pk: string) => {
      if (await accessManager.hasAccess(pk)) {
        result.push(pk);
      }
    }
    const executor = new PromisePool(() => {
      if (accessManager.isPreloaded) {
        return;
      }
      const task = sourcePks[pointer++];
      if (task) {
        return taskFactory(task);
      }
      return;
    }, 20)
    await executor.start();

    if (pointer < sourcePks.length && accessManager.isPreloaded) {
      for (let i = pointer; i < sourcePks.length; i++) {
        if (accessManager.hasAccessUsingCached(sourcePks[i])) {
          result.push(sourcePks[i]);
        }
      }
    }

    return result;
  }

  async indexCount(itemKey: string) {
    const rb = await this.manager.getRequestBuilder();
    const q = rb.createNoParametersIndexCount(itemKey);
    const mainScanner = this.manager.createExecutorNoParametersIndexCount(itemKey, q);

    const saver = async () => {
      while (!mainScanner.isFinished || mainScanner.hasSomeResults()) {
        const nextChunk = mainScanner.takeSome(1000000);
        if (nextChunk.length > 0) {
          await this.store(nextChunk)
          await waitFor({milliseconds: 10});
        } else {
          await waitFor({milliseconds: 1000});
        }
      }
    }

    await Promise.all([mainScanner.start(), saver()]);
  }

  async start() {
    const reservationKey = this.manager.getConfigModel().getReservationFieldKey();
    const parallelSearchKeys = this.manager.fields.ignore('check').ignore(reservationKey).getKeys();
    const requestBuilder = await this.manager.getRequestBuilder();

    const executors: SingleKeyQueryExecutor[] = [];
    const executorsMap: MultiMap<SingleKeyQueryExecutor> = new MultiMap();
    parallelSearchKeys.forEach((key) => {
      const orValues = this.manager.fields.getValues(key);
      orValues.map((v) => {
        const q = requestBuilder.createParallelSearchQueryModel(key, v);
        const e = this.manager.createExecutorForSimpleKey(key, q);
        executorsMap.put(key, e);
        executors.push(e);
      });
    });
    const needFilterByReservation = reservationKey && (
      this.manager.dates.hasReservation() || this.manager.hasLimitsByCategory()
    );
    if (needFilterByReservation) {
      const q = requestBuilder.createNoValueSearchQueryModel(reservationKey as string);
      const e = this.manager.createExecutorForCategoryKey(reservationKey as string, q);
      executorsMap.put(reservationKey as string, e);
      executors.push(e);
    }
    let accessManager: AccessManager | undefined;
    if (this.manager.hasAccessRules() && reservationKey) {
      accessManager = this.manager.createAccessManager();
      accessManager.preLoadPkCategoryMapping(reservationKey,
        requestBuilder.createNoValueSearchQueryModel(reservationKey)
      ).then(nop);
    }

    const tasks = [...executors];
    const executor = new PromisePool(() => {
      const task = tasks.pop();
      if (task) {
        return task.start();
      }
      return;
    }, 10)
    await executor.start();

    const andOperands: { [key: string]: string[] } = {};
    executorsMap.keys().forEach((k) => {
      andOperands[k] = _.union(...executorsMap.get(k).map(k => k.collectedPksArray));
    });
    const commonKeys = _.intersection(...Object.values(andOperands));

    let filtredKeys: string[] = commonKeys;
    if (accessManager) {
      filtredKeys = await this.filterAccess(accessManager, commonKeys);
    }

    const keySet = new Set();
    filtredKeys.forEach(k => keySet.add(k));
    const filteredRecords: any[] = []
    executors.forEach((e) => {
      e.resultsQueue
        .filter(r => keySet.has(r.partitionKey))
        .forEach(r => {
          keySet.delete(r.partitionKey);
          filteredRecords.push(r);
        })
    });
    filteredRecords.forEach(p => this.collectedRecords.push(p));
    if (this.store) {
      await this.store(this.collectedRecords);
    }
  }
}
