/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import ApiBuilder from 'lambda-api';
import installMiddlewares from '../api-reception/middlewares/install';

const v1 = (app, opts) => {
  installMiddlewares(app);
  app.register(require('../api-reception/controllers/survey-configs').routes, {
    prefix: '/db/survey-configs',
  });
  app.register(require('../api-reception/controllers/survey-results').routes, {
    prefix: '/db/survey-results',
  });
  app.register(require('../api-reception/controllers/survey-calendars').install, {
    prefix: '/db/calendars',
  });
  app.register(require('../api-reception/controllers/authorization').routes, {
    prefix: '/db/login',
  });
  app.register(require('../api-reception/controllers/errors-test').routes, {
    prefix: '/db/errorsTest',
  });
};

export default async function (config) {
  const staticConfig = await import('../api-reception/config/static');
  staticConfig.default.merge(config);

  const api = ApiBuilder();

  // API versions
  api.register(v1, {prefix: '/survey/api/v1'});

  // current API version
  api.register(v1);

  return api;
};
