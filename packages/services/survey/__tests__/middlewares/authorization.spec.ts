/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

jest.mock('axios');
import axios from 'axios';
import * as authorization from '../../../api-reception/middlewares/authorization'

describe('getAndVerifyUser', () => {
  it('return verified UID, if verify success', async () => {
    const lineVerifySuccessResponse = { data: { "sub": "U1234567890abcdef1234567890abcdef" } };
    (axios.post as jest.Mock).mockResolvedValue(lineVerifySuccessResponse);
    const req = { headers: [], auth: { type: 'Bearer', value: 'value' }}

    const result = await authorization.getAndVerifyUser(req, {})
    expect(result).toBe('U1234567890abcdef1234567890abcdef');
  });

  it('return 403, if verify failed', async () => {
    const lineVerifyFailResponse = { status: 403, data: {"error": "invalid_request" }};
    (axios.post as jest.Mock).mockRejectedValue({ response: lineVerifyFailResponse });
    const req = { headers: [], auth: { type: 'Bearer', value: 'value' }};
    const res = {
      json: jest.fn(),
      status: jest.fn().mockReturnThis(),
    };

    await authorization.getAndVerifyUser(req, res)
    expect(res.status).toBeCalledWith(403);
    expect(res.json).toBeCalledWith(lineVerifyFailResponse.data);
  });

  it('return 403, if verify failed with unexpected error', async () => {
    (axios.post as jest.Mock).mockRejectedValue({ message: 'some error' });
    const req = { headers: [], auth: { type: 'Bearer', value: 'value' }};
    const res = {
      json: jest.fn(),
      status: jest.fn().mockReturnThis(),
    };

    await authorization.getAndVerifyUser(req, res)
    expect(res.status).toBeCalledWith(403);
    expect(res.json).toBeCalledWith({});
  });
});



