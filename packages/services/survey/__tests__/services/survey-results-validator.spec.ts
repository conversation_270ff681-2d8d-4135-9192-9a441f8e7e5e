/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
// @ts-nocheck
import { SurveyResultsValidator } from '../../services/survey-results-validator';
import { PAY_TYPES } from '../../types/payment';

type TSurveyConfigTestData = {
  surveySchema?: any[],
}

describe('SurveyResultsValidator', () => {
  describe('text', () => {
    let config: TSurveyConfigTestData = { surveySchema: [] };
    let input: any[] = [];

    beforeEach(() => {
      config = {
        surveySchema: [
          {
            itemKey: 'd0e5d09a',
            type: 'text',
            title: '氏名',
            description: 'お名前を記入してください',
            isRequired: {
              value: true,
            },
            length: {
              min: '2',
              max: '5',
            },
          },
        ],
      };

      input = [
        {
          value: 'テスト',
          partitionKey: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb#userD',
          sortKey: 'd0e5d09a#テスト',
          userId: 'userD',
          surveyId: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb',
          itemKey: 'd0e5d09a',
        },
      ];
    });

    it('valid input', () => {
      const validator = new SurveyResultsValidator(config, input);
      expect(validator.validate()).toBe(true);
    });

    describe('required', () => {
      it('input does not exit', () => {
        input[0].value = '';
        input[0].sortKey = 'd0e5d09a#';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          d0e5d09a: ['必須項目です'],
        });
      });

      it('not required, input exist', () => {
        config.surveySchema[0].isRequired.value = false;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('not required, input does not exist', () => {
        config.surveySchema[0].isRequired.value = false;
        config.surveySchema[0].length.min = '0';
        input[0].value = '';
        input[0].sortKey = 'd0e5d09a#';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });
    });

    describe('length', () => {
      it('not exist', () => {
        delete config.surveySchema[0].length;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('only min, valid input', () => {
        delete config.surveySchema[0].length.max;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('only min, shorter input', () => {
        delete config.surveySchema[0].length.max;
        input[0].value = 'a';
        input[0].sortKey = 'd0e5d09a#a';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          d0e5d09a: ['2文字以上で入力してください'],
        });
      });

      it('only max, valid input', () => {
        delete config.surveySchema[0].length.min;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('only max, longer input', () => {
        delete config.surveySchema[0].length.min;
        input[0].value = '1234567890';
        input[0].sortKey = 'd0e5d09a#1234567890';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          d0e5d09a: ['5文字以内で入力してください'],
        });
      });
    });
  });

  describe('checkboxes', () => {
    let config: TSurveyConfigTestData = {};
    let input: any[] = [];

    beforeEach(() => {
      config = {
        surveySchema: [
          {
            itemKey: '28dcd81d',
            type: 'checkboxes',
            title: '情報区分',
            description: '必要な情報を選択してください',
            options: ['防犯・防災', 'イベント', '健康', '福祉', '子育て'],
            isRequired: {
              value: true,
            },
            selection: {
              min: '1',
              max: '3',
            },
          },
        ],
      };

      input = [
        {
          value: 'イベント',
          partitionKey: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb#userD',
          sortKey: '28dcd81d#イベント',
          userId: 'userD',
          surveyId: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb',
          itemKey: '28dcd81d',
        },
        {
          value: '防犯・防災',
          partitionKey: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb#userD',
          sortKey: '28dcd81d#防犯・防災',
          userId: 'userD',
          surveyId: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb',
          itemKey: '28dcd81d',
        },
        {
          value: '',
          partitionKey: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb#userD',
          sortKey: '28dcd81d#健康',
          userId: 'userD',
          surveyId: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb',
          itemKey: '28dcd81d',
        },
        {
          value: '',
          partitionKey: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb#userD',
          sortKey: '28dcd81d#福祉',
          userId: 'userD',
          surveyId: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb',
          itemKey: '28dcd81d',
        },
        {
          value: '',
          partitionKey: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb#userD',
          sortKey: '28dcd81d#子育て',
          userId: 'userD',
          surveyId: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb',
          itemKey: '28dcd81d',
        },
      ];
    });

    it('valid input', () => {
      const validator = new SurveyResultsValidator(config, input);
      expect(validator.validate()).toBe(true);
    });

    describe('required', () => {
      it('input does not exit', () => {
        input[0].value = '';
        input[1].value = '';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          '28dcd81d': ['必須項目です'],
        });
      });

      it('not required, input exist', () => {
        config.surveySchema[0].isRequired.value = false;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('not required, input does not exist', () => {
        config.surveySchema[0].isRequired.value = false;
        config.surveySchema[0].selection.min = '0';
        input[0].value = '';
        input[1].value = '';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });
    });

    describe('selecion', () => {
      it('shorter input', () => {
        config.surveySchema[0].selection.min = '2';
        input[1].value = '';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          '28dcd81d': ['2〜3個選択してください'],
        });
      });

      it('longer input', () => {
        input[2].value = '健康';
        input[3].value = '福祉';
        input[4].value = '子育て';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          '28dcd81d': ['1〜3個選択してください'],
        });
      });

      it('not exist', () => {
        delete config.surveySchema[0].selection;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('only min, valid input', () => {
        delete config.surveySchema[0].selection.max;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('only min, shorter input', () => {
        config.surveySchema[0].isRequired.value = false;
        delete config.surveySchema[0].selection.max;
        input[0].value = '';
        input[1].value = '';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          '28dcd81d': ['1個以上選択してください'],
        });
      });

      it('only max, valid input', () => {
        delete config.surveySchema[0].selection.min;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('only max, longer input', () => {
        delete config.surveySchema[0].selection.min;
        input[2].value = '健康';
        input[3].value = '福祉';
        input[4].value = '子育て';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          '28dcd81d': ['3個以下で選択してください'],
        });
      });
    });
  });

  describe('dropdown', () => {
    let config: TSurveyConfigTestData = {};
    let input: any[] = [];

    beforeEach(() => {
      config = {
        surveySchema: [
          {
            itemKey: 'g3seyqkx',
            type: 'dropdown',
            title: '年代',
            description: '年代を選択してください',
            options: ['20才未満', '20〜29歳', '30〜39歳', '40〜49歳', '50歳以上'],
            isRequired: {
              value: true,
            },
          },
        ],
      };

      input = [
        {
          value: '20才未満',
          partitionKey: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb#userD',
          sortKey: 'g3seyqkx#20才未満',
          userId: 'userD',
          surveyId: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb',
          itemKey: 'g3seyqkx',
        },
      ];
    });

    it('valid input', () => {
      const validator = new SurveyResultsValidator(config, input);
      expect(validator.validate()).toBe(true);
    });

    describe('required', () => {
      it('input does not exit', () => {
        input[0].value = '';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          g3seyqkx: ['必須項目です'],
        });
      });

      it('not required, input exist', () => {
        config.surveySchema[0].isRequired.value = false;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('not required, input does not exist', () => {
        config.surveySchema[0].isRequired.value = false;
        input[0].value = '';
        input[0].sortKey = 'g3seyqkx#';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });
    });

    describe('options', () => {
      it('input does not exist in options', () => {
        input[0].value = 'aaaaa';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          g3seyqkx: ['不正なデータです'],
        });
      });
    });
  });

  describe('date', () => {
    let config: TSurveyConfigTestData = {};
    let input: any[] = [];

    beforeEach(() => {
      config = {
        surveySchema: [
          {
            itemKey: 'y2fqwjha',
            type: 'date',
            title: '誕生日',
            description: '誕生日を入力してください',
            includeYear: true,
            isRequired: {
              value: true,
            },
          },
        ],
      };

      input = [
        {
          value: '2020-08-01',
          partitionKey: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb#userD',
          sortKey: 'y2fqwjha#2020-08-01',
          userId: 'userD',
          surveyId: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb',
          itemKey: 'y2fqwjha',
        },
      ];
    });

    it('valid input', () => {
      const validator = new SurveyResultsValidator(config, input);
      expect(validator.validate()).toBe(true);
    });

    describe('required', () => {
      it('input does not exit', () => {
        input[0].value = '';
        input[0].sortKey = 'y2fqwjha#';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          y2fqwjha: ['必須項目です'],
        });
      });

      it('not required, input exist', () => {
        config.surveySchema[0].isRequired.value = false;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('not required, input does not exist', () => {
        config.surveySchema[0].isRequired.value = false;
        input[0].value = '';
        input[0].sortKey = 'y2fqwjha#';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });
    });

    describe('format', () => {
      it('MM-DD format', () => {
        config.surveySchema[0].includeYear = false;
        input[0].value = '12-31';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('MM-DD format, the day does not exits', () => {
        config.surveySchema[0].includeYear = false;
        input[0].value = '12-32';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          y2fqwjha: ['正しい形式で入力してください'],
        });
      });

      it('not YYYY-MM-DD format', () => {
        input[0].value = 'aaaaa';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          y2fqwjha: ['正しい形式で入力してください'],
        });
      });

      it('the day does not exits', () => {
        input[0].value = '2020-06-31';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          y2fqwjha: ['正しい形式で入力してください'],
        });
      });
    });
  });

  describe('email', () => {
    let config: TSurveyConfigTestData = {};
    let input: any[] = [];

    beforeEach(() => {
      config = {
        surveySchema: [
          {
            itemKey: 'x2r43nhp',
            type: 'email',
            title: 'メールアドレス',
            description: 'メールアドレスを記入してください',
            isRequired: {
              value: true,
            },
            format: {},
          },
        ],
      };

      input = [
        {
          value: '<EMAIL>',
          partitionKey: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb#userD',
          sortKey: 'x2r43nhp#<EMAIL>',
          userId: 'userD',
          surveyId: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb',
          itemKey: 'x2r43nhp',
        },
      ];
    });

    it('valid input', () => {
      const validator = new SurveyResultsValidator(config, input);
      expect(validator.validate()).toBe(true);
    });

    describe('required', () => {
      it('input does not exit', () => {
        input[0].value = '';
        input[0].sortKey = 'x2r43nhp#';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          x2r43nhp: ['必須項目です'],
        });
      });

      it('not required, input exist', () => {
        config.surveySchema[0].isRequired.value = false;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('not required, input does not exist', () => {
        config.surveySchema[0].isRequired.value = false;
        input[0].value = '';
        input[0].sortKey = 'x2r43nhp#';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });
    });

    describe('format', () => {
      it('not email format', () => {
        input[0].value = 'aaaaa';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          x2r43nhp: ['正しい形式で入力してください'],
        });
      });

      it('the email format include @', () => {
        input[0].value = 'foo.bar@example';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          x2r43nhp: ['正しい形式で入力してください'],
        });
      });
    });
  });

  describe('phone', () => {
    let config: TSurveyConfigTestData = {};
    let input: any[] = [];

    beforeEach(() => {
      config = {
        surveySchema: [
          {
            itemKey: 'f94w63nt',
            type: 'phone',
            title: '連絡先電話番号',
            description: '電話番号を記入してください',
            isRequired: {
              value: true,
            },
            format: {},
          },
        ],
      };

      input = [
        {
          value: '09012345678',
          partitionKey: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb#userD',
          sortKey: 'f94w63nt#09012345678',
          userId: 'userD',
          surveyId: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb',
          itemKey: 'f94w63nt',
        },
      ];
    });

    it('valid input', () => {
      const validator = new SurveyResultsValidator(config, input);
      expect(validator.validate()).toBe(true);
    });

    describe('required', () => {
      it('input does not exit', () => {
        input[0].value = '';
        input[0].sortKey = 'f94w63nt#';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          f94w63nt: ['必須項目です'],
        });
      });

      it('not required, input exist', () => {
        config.surveySchema[0].isRequired.value = false;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('not required, input does not exist', () => {
        config.surveySchema[0].isRequired.value = false;
        input[0].value = '';
        input[0].sortKey = 'f94w63nt#';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });
    });

    describe('format', () => {
      it('not phone format', () => {
        input[0].value = 'aaaaa';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          f94w63nt: ['正しい形式で入力してください'],
        });
      });

      it('input include -', () => {
        input[0].value = '090-1234-5678';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          f94w63nt: ['正しい形式で入力してください'],
        });
      });
    });
  });

  describe('number', () => {
    let config: TSurveyConfigTestData = {};
    let input: any[] = [];

    beforeEach(() => {
      config = {
        surveySchema: [
          {
            itemKey: 'k869he52',
            type: 'number',
            title: '家族の人数',
            description: '家族の人数を記入してください',
            isRequired: {
              value: true,
            },
            range: {
              min: '1',
              max: '999',
            },
          },
        ],
      };

      input = [
        {
          value: '3',
          partitionKey: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb#userD',
          sortKey: 'k869he52#09012345678',
          userId: 'userD',
          surveyId: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb',
          itemKey: 'k869he52',
        },
      ];
    });

    it('valid input', () => {
      const validator = new SurveyResultsValidator(config, input);
      expect(validator.validate()).toBe(true);
    });

    describe('required', () => {
      it('input does not exit', () => {
        input[0].value = '';
        input[0].sortKey = 'k869he52#';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          k869he52: ['必須項目です'],
        });
      });

      it('not required, input exist', () => {
        config.surveySchema[0].isRequired.value = false;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('not required, input does not exist', () => {
        config.surveySchema[0].isRequired.value = false;
        input[0].value = '';
        input[0].sortKey = 'k869he52#';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });
    });

    describe('format', () => {
      it('not number', () => {
        input[0].value = 'aaaaa';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          k869he52: ['正しい形式で入力してください'],
        });
      });
    });

    describe('range', () => {
      it('range does not exist', () => {
        delete config.surveySchema[0].range;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('lower', () => {
        input[0].value = '0';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          k869he52: ['1〜999の範囲で入力してください'],
        });
      });

      it('higer', () => {
        input[0].value = '1000';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          k869he52: ['1〜999の範囲で入力してください'],
        });
      });

      it('only range.min, lower', () => {
        delete config.surveySchema[0].range.max;
        input[0].value = '0';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          k869he52: ['1以上で入力してください'],
        });
      });

      it('only range.max, higher', () => {
        delete config.surveySchema[0].range.min;
        input[0].value = '1000';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          k869he52: ['999以下で入力してください'],
        });
      });
    });
  });
  describe('textarea', () => {
    let config: TSurveyConfigTestData = {};
    let input: any[] = [];

    beforeEach(() => {
      config = {
        surveySchema: [
          {
            itemKey: 'u8q34drh',
            type: 'textarea',
            title: 'その他',
            description: 'ご意見をお聞かせください',
            isRequired: {
              value: true,
            },
            length: {
              min: '10',
              max: '400',
            },
          },
        ],
      };

      input = [
        {
          value: '私はこの地方にいるものではありません、\n東京の方に住っております。',
          partitionKey: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb#userD',
          sortKey: 'u8q34drh#私はこの地方にいるものではありません、\n東京の方に住っております。',
          userId: 'userD',
          surveyId: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb',
          itemKey: 'u8q34drh',
        },
      ];
    });

    it('valid input', () => {
      const validator = new SurveyResultsValidator(config, input);
      expect(validator.validate()).toBe(true);
    });

    describe('required', () => {
      it('input does not exit', () => {
        input[0].value = '';
        input[0].sortKey = 'u8q34drh#';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          u8q34drh: ['必須項目です'],
        });
      });

      it('not required, input exist', () => {
        config.surveySchema[0].isRequired.value = false;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('not required, input does not exist', () => {
        config.surveySchema[0].isRequired.value = false;
        config.surveySchema[0].length.min = '0';
        input[0].value = '';
        input[0].sortKey = 'u8q34drh#';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });
    });

    describe('length', () => {
      it('not exist', () => {
        delete config.surveySchema[0].length;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('only min, valid input', () => {
        delete config.surveySchema[0].length.max;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('only min, shorter input', () => {
        config.surveySchema[0].length.min = '100';
        delete config.surveySchema[0].length.max;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          u8q34drh: ['100文字以上で入力してください'],
        });
      });

      it('only max, valid input', () => {
        delete config.surveySchema[0].length.min;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('only max, longer input', () => {
        delete config.surveySchema[0].length.min;
        config.surveySchema[0].length.max = '10';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          u8q34drh: ['10文字以内で入力してください'],
        });
      });
    });
  });

  describe('radio', () => {
    let config: TSurveyConfigTestData = {};
    let input: any[] = [];

    beforeEach(() => {
      config = {
        surveySchema: [
          {
            itemKey: 'x798h2uv',
            type: 'radio',
            title: '防災情報',
            description: '',
            options: ['有り', '無し'],
            isRequired: {
              value: true,
            },
          },
        ],
      };

      input = [
        {
          value: '有り',
          partitionKey: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb#userD',
          sortKey: 'x798h2uv#有り',
          userId: 'userD',
          surveyId: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb',
          itemKey: 'x798h2uv',
        },
      ];
    });

    it('valid input', () => {
      const validator = new SurveyResultsValidator(config, input);
      expect(validator.validate()).toBe(true);
    });

    describe('required', () => {
      it('input does not exit', () => {
        input[0].value = '';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          x798h2uv: ['必須項目です'],
        });
      });

      it('not required, input exist', () => {
        config.surveySchema[0].isRequired.value = false;

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });

      it('not required, input does not exist', () => {
        config.surveySchema[0].isRequired.value = false;
        input[0].value = '';
        input[0].sortKey = 'x798h2uv#';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(true);
      });
    });

    describe('options', () => {
      it('input does not exist in options', () => {
        input[0].value = 'aaaaa';

        const validator = new SurveyResultsValidator(config, input);
        expect(validator.validate()).toBe(false);
        expect(validator.getErrorMessage()).toEqual({
          x798h2uv: ['不正なデータです'],
        });
      });
    });
  });

  describe('selectPayType', () => {
    let config: TSurveyConfigTestData = {};
    let input: any[] = [];

    beforeEach(() => {
      config = {
        surveySchema: [
          {
            itemKey: 'x798h2uv',
            type: 'selectPayType',
            title: '支払方法選択',
            description: '',
            payTypeOptions: [
              {
                payType: PAY_TYPES.CASHLESS,
                input: 'キャッシュレス',
              },
              {
                payType: PAY_TYPES.CASH,
                input: '現金',
              },
            ],
            isRequired: {
              value: true,
            },
          },
        ],
      };

      input = [
        {
          value: '現金',
          partitionKey: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb#userD',
          sortKey: 'x798h2uv#現金',
          userId: 'userD',
          surveyId: 'sdasda-bc89-fsfsf-b1c1-dfgdvbcvb',
          itemKey: 'x798h2uv',
        },
      ];
    });

    it('should be true', () => {
      const validator = new SurveyResultsValidator(config, input);
      validator.validate();
      expect(validator.isValid()).toBe(true);
      console.log(validator.getErrorMessage());
    });

    it('should be false. Because there is no value', () => {
      input[0].value = '';
      const validator = new SurveyResultsValidator(config, input);
      validator.validate();
      expect(validator.isValid()).toBe(false);
    });

    it('should be false. Because there is no value', () => {
      input[0].value = 'to be error';
      const validator = new SurveyResultsValidator(config, input);
      validator.validate();
      expect(validator.isValid()).toBe(false);
    });
  });
});
