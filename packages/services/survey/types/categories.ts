/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

export type CategoryOrder = number | null | undefined;

export type CategoryOrders = {
  largeCategoryOrder?: CategoryOrder,
  mediumCategoryOrder?: CategoryOrder,
  smallCategoryOrder?: CategoryOrder,
}

export type CategoryNames = {
  tag1: string,
  tag2: string,
  tag3: string,
}

export type CategoryRecord = {
  partitionKey: 'categories',
  sortKey: string,
  calendarId?: string,
  createdAt?: number,
  updatedAt?: number,
} & CategoryNames & CategoryOrders;

export type Category = {
  id: string,
  calendarId?: string,
} & CategoryNames & CategoryOrders;

type SmallCategoryTree = {
  name: string,
  id: string,
  calendarId?: string,
  order: CategoryOrder,
}

type MediumCategoryTree = {
  name: string,
  id?: string,
  calendarId?: string,
  order: CategoryOrder,
  children: SmallCategoryTree[],
}

export type LargeCategoryTree = {
  name: string;
  id?: string,
  calendarId?: string,
  order: CategoryOrder,
  children: MediumCategoryTree[];
}
