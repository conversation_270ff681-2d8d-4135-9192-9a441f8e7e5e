/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

export const TAX_TYPES = {
  OUTER: 0,
  INNTER: 1,
} as const;

export type TaxType = typeof TAX_TYPES[keyof typeof TAX_TYPES];

export const CALCULATION_TYPES = {
  FOR_EACH_ITEMS: 0,
  TOTAL: 1,
} as const;

export type CalculationType = typeof CALCULATION_TYPES[keyof typeof CALCULATION_TYPES];

export const ROUNDING_TYPES = {
  ROUND_DOWN: 0,
  ROUND_UP: 1,
  ROUND: 2,
} as const;

export type RoundingType = typeof ROUNDING_TYPES[keyof typeof ROUNDING_TYPES];

export const RESERVE_SERVICE_TYPES = {
  TRUE: 0,
  FALSE: 1,
} as const;

export type ReserveServiceType = typeof RESERVE_SERVICE_TYPES[keyof typeof RESERVE_SERVICE_TYPES];

export const RESERVE_COST_TYPES = {
  FIXED: 0,
  ADDITION: 1,
} as const;

export type ReserveCostType = typeof RESERVE_COST_TYPES[keyof typeof RESERVE_COST_TYPES];


export type RateSetting = {
  value: number;
  start: string;
  end: string;
};

export const PAYMENT_METHODS = {
  NULL: '',
  CREDIT: 'credit',
  PAYPAY: 'paypay',
  LINEPAY: 'linepay',
  CASH: 'cash'
} as const;

export type PaymentMethodsType = typeof PAYMENT_METHODS[keyof typeof PAYMENT_METHODS];

export const PAYMENT_STATUSES = {
  NULL: -1,
  COMPLETE: 0,
  NOT_APPLICABLE: 1,
  REFUNDED: 2,
  CREDIT_COMPLETE: 3,
  CREDIT_CANCEL: 4,
} as const;

export type PaymentStatusType = typeof PAYMENT_STATUSES[keyof typeof PAYMENT_STATUSES];

export const TAXABLE_TYPES = {
  TAXABLE: 0, // 課税対象
  TAX_FREE: 1, // 非課税
} as const;

export type TaxableType = typeof TAXABLE_TYPES[keyof typeof TAXABLE_TYPES];

export const PRODUCT_STATUSES = {
  SALE: 0,
  STOP: 1,
} as const;

export const PAY_TYPES = {
  CASHLESS: 0,
  CASH: 1,
} as const;

export type PayType = typeof PAY_TYPES[keyof typeof PAY_TYPES];

export type Product = {
  productId: string;
	productName: string;
	price: number;
	productCategories: {
    id: string;
    name: string
  }[];
	taxableType: TaxableType;
	order: number;
	status: number;
};

export type PaymentDetail = {
  detailId: number;
  productCategoryId: string;
  productCategoryName: string;
  productId: string;
  productName: string;
  price: number;
  count: number;
  tax: number;
  amount: number;
  amountWithTax: number;
  taxableType: TaxableType;
  reservationPaymentItemId?: string;
};

export interface ProcessPaymentResultReqBody {
  pay_method: PaymentMethodsType;
  merchant_id: string;
  service_id: string;
  cust_code: string;
  sps_cust_no: string;
  sps_payment_no: string;
  order_id: string;
  item_id: string;
  pay_item_id: string;
  item_name: string;
  tax: string;
  amount: string;
  pay_type: string;
  auto_charge_type: string;
  service_type: string;
  div_settele: string;
  last_charge_month: string;
  camp_type: string;
  tracking_id: string;
  terminal_type: string;
  free1: string;
  free2: string;
  free3: string;
  dtl_rowno: string[];
  dtl_item_id: string[];
  dtl_item_name: string[];
  dtl_item_count: string[];
  dtl_tax: string[];
  dtl_amount: string[];
  dtl_free1: string[];
  dtl_free2: string[];
  dtl_free3: string[];
  request_date: string; // yyyyMMddHHmmss
  res_pay_method: PaymentMethodsType;
  res_result: 'OK' | 'NG';
  res_tracking_id: string;
  res_sps_cust_no: string;
  res_sps_payment_no: string;
  res_payinfo_key: string;
  res_payment_date: string;
  res_err_code: string;
  res_date: string; // yyyyMMddHHmmss
  limit_second: string;
  sps_hashcode: string;
}
