/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

export type ScheduleDaySlots = {
  [key: string]: string | number,
}
export type ScheduleDay = {
  date: string,
  dayOff: number,
  slots: ScheduleDaySlots,
}
export type ComaItem = {
  id: string,
  times: {
    start: string,
    end: string,
  }
}
export type DuplicateComa = {
  id: string,
  code: string,
  message: string,
}
export type InvalidDays = {
  date: string,
  code: string,
  message: string,
  details: any,
}
export type ScheduleDayRecord = {
  partitionKey: string,
  sortKey: string,
  date: string,
  quotas: ScheduleDaySlots,
  updatedAt: number,
  createdAt: number,
  reservationCounts: any,
  dayOff: number,
}
export type CalendarRecordComaList = {
  [key: string]: { start: string, end: string }
}
export const ControlTypeOfReservableAge = {
  allAge: 0,
  fullAge: 1,
  specialSetting: 2,
} as const;
export type KeyOfControlType = keyof typeof ControlTypeOfReservableAge;
type ControlTypeOfReservableAge = typeof ControlTypeOfReservableAge[keyof typeof ControlTypeOfReservableAge];
export type ReservableAge = {
  controlType: ControlTypeOfReservableAge,
  settings: {
    start: number,
    end: number,
  }[],
}
export type CalendarRecord = {
  partitionKey: 'calendars',
  sortKey: string,
  name: string,
  createdAt: number,
  updatedAt: number,
  startDate: string,
  endDate: string,
  reservationControlType: number,
  reservationMaxCountOfDay: number,
  reservationPossibleStart: number,
  reservationCancelLimit: number,
  reservationPossibleMonths: number,
  reservationMaxCount: number,
  reservableAge: ReservableAge,
  comaList: CalendarRecordComaList,
  calendarOff: number,
}
