/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { v4 as uuidv4 } from 'uuid';
import * as _ from 'lodash';

const getLineMessagingCredentials = () => {
  return {
    accessToken: process.env.LINEMESSAGING_CHANNEL_ACCESS_TOKEN,
    channelSecret: process.env.LINEMESSAGING_CHANNEL_SECRET,
  };
};

const getCurrentTime = () => {
  return require('moment-timezone')().utcOffset('+0900').format('YYYYMMDDTHHmmssZZ');
};

const getCurrentDate = () => {
  return require('moment-timezone')().utcOffset('+0900').format('YYYYMMDD');
};

const getRegion = () => {
  return process.env.AWS_REGION || 'ap-northeast-1';
};

const isLocalRun = () => {
  return !process.env.AWS_LAMBDA_FUNCTION_NAME;
};

const isAwsEnvironment = () => {
  return !isLocalRun() && process.env.AWS_SAM_LOCAL !== 'true';
};
const isSamLocalTestEnvironment = () => {
  return process.env.AWS_SAM_LOCAL === 'true';
};

const getLogLevel = () => {
  return process.env.AWS_LAMBDA_LOG_LEVEL || 'info';
};
const hasTestRoute = () => {
  return process.env.AWS_LAMBDA_HAS_TEST_ROUTE === 'true' || false;
};
const isDebugLogging = () => {
  return getLogLevel() === 'debug';
};
const isProductionEnv = () => {
  return process.env.AWS_LAMBDA_PRODUCTION_MODE === 'true' || false;
};

const appLogDebug = (...args) => {
  if (getLogLevel() === 'debug') {
    console.log(...args);
  }
};
const appLogInfo = (...args) => {
  console.log(...args);
};

const getNextUuid = () => {
  return uuidv4();
};

const decodeBase64 = (base64str) => {
  return Buffer.from(base64str, 'base64').toString('utf8');
};

const randomIntBetween = (start, end) => {
  return _.random(start, end);
};

const extractVersionPrefix = (event) => {
  let pathPattern = event.resource;
  pathPattern = pathPattern.replace(/{proxy\+}/, '___proxy___');
  pathPattern = pathPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  pathPattern = pathPattern.replace('___proxy___', '.+');
  const pathRegex = new RegExp(pathPattern);
  const prefix = event.path.replace(pathRegex, '');
  if (prefix) {
    return prefix.substring(1);
  }
  return '';
};
//Fortmat AnswerCode 1->000001
const getFormatedAnswerCode = (answerCode) => {
  if (Number(answerCode) > 999999) {
    return answerCode;
  }
  const str = '' + answerCode;
  const pad = '000000';
  const result = pad.substring(0, pad.length - str.length) + str;
  return result;
};

const findWithAttr = (array, attr, value) => {
  for (let i = 0; i < array.length; i += 1) {
    if (array[i][attr] === value) {
      return i;
    }
  }
  return -1;
};

const sleep = (ms) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export {
  getRegion,
  isAwsEnvironment,
  isSamLocalTestEnvironment,
  isLocalRun,
  getLogLevel,
  isProductionEnv,
  isDebugLogging,
  appLogInfo,
  appLogDebug,
  hasTestRoute,
  getLineMessagingCredentials,
  extractVersionPrefix,
  getCurrentTime,
  getCurrentDate,
  getNextUuid,
  decodeBase64,
  randomIntBetween,
  getFormatedAnswerCode,
  findWithAttr,
  sleep,
};
