/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import config from '../../api-reception/config/static';
import c from '../../api-reception/config/constants';
import * as AWS from 'aws-sdk';

const getRegion = () => {
  return config.opt(c.AWS_REGION).orFirst(process.env.AWS_REGION, 'ap-northeast-1');
};

const initializeAwsLocal = (profileName?) => {
  if (!profileName) {
    profileName = process.env.AWS_PROFILE || 'lsc-dev';
  }
  AWS.config.credentials = new AWS.SharedIniFileCredentials({
    profile: profileName,
  });
  AWS.config.region = getRegion();
};

const isAwsEnvironment = () => {
  // check whether it is SAM local-api Docker env
  if (process.env.AWS_SAM_LOCAL === 'true') {
    return false;
  }
  return !!process.env.AWS_LAMBDA_LOG_STREAM_NAME;
};

const getLocalCredentials = (profileName) => {
  return {
    credentials: new AWS.SharedIniFileCredentials({
      profile: profileName,
    }),
    region: getRegion(),
  };
};
const isLocalRun = () => {
  return !!process.env.IS_LOCAL;
};

const getEnvironmentCredentials = (profileName = 'lsc-dev') => {
  if (isAwsEnvironment() || isLocalRun()) {
    return undefined;
  }
  if (config.isLocalRuntime && profileName) {
    return getLocalCredentials(profileName);
  }
  if (process.env.AWS_ACCESS_KEY_ID) {
    return {
      credentials: new AWS.Credentials({
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      }),
      region: getRegion(),
    };
  }
  return getLocalCredentials(profileName);
};

export {
  getRegion,
  initializeAwsLocal,
  isAwsEnvironment,
  getEnvironmentCredentials,
};
