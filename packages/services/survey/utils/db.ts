/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import * as AWS from 'aws-sdk';
import { getEnvironmentCredentials } from './aws-helper';
import { DDClient } from '../../common/utils/index.js';

const getDbClient = () => {
  return new AWS.DynamoDB(getEnvironmentCredentials());
};

const getDynamoDbPatternsClient = (tableName) => {
  return new DDClient.Table(tableName, null, getEnvironmentCredentials());
};

const queryList = async <T = any> (params: AWS.DynamoDB.QueryInput) => {
  const client = getDbClient();
  const json: T[] = [];
  let request;
  do {
    const lastKey = request ? request.LastEvaluatedKey : undefined;
    request = await client
      .query({
        ...params,
        ExclusiveStartKey: lastKey,
      })
      .promise();
    request.Items.map((map) => {
      json.push(AWS.DynamoDB.Converter.unmarshall(map) as T);
    });
  } while (request.LastEvaluatedKey !== undefined);
  return json;
};

const queryOnePage = async <T = any> (params: AWS.DynamoDB.QueryInput) => {
  const client = getDbClient();
  const resultsArray: T[] = [];

  const request = await client
    .query({
      ...params,
    })
    .promise();
  if (request.Items) {
    request.Items.map((map) => {
      resultsArray.push(AWS.DynamoDB.Converter.unmarshall(map) as T);
    });
  }

  return {
    items: resultsArray,
    lastEvaluatedKey: request.LastEvaluatedKey,
  };
};

const scanList = async (params) => {
  const client = getDbClient();
  const json: any[] = [];
  let request;
  do {
    const lastKey = request ? request.LastEvaluatedKey : undefined;
    request = await client
      .scan({
        ...params,
        ExclusiveStartKey: lastKey,
      })
      .promise();
    request.Items.map((map) => {
      json.push(AWS.DynamoDB.Converter.unmarshall(map));
    });
  } while (request.LastEvaluatedKey !== undefined);
  return json;
};

const scanOnePage = async (params) => {
  const client = getDbClient();
  const resultsArray: any[] = [];

  const request = await client
    .scan({
      ...params,
    })
    .promise();
  request.Items?.map((map) => {
    resultsArray.push(AWS.DynamoDB.Converter.unmarshall(map));
  });

  return {
    items: resultsArray,
    lastEvaluatedKey: request.LastEvaluatedKey,
  };
};

const getMany = async (params) => {
  const clinet = getDbClient();
  const batchResult = await clinet.batchGetItem(params).promise();
  const jsonResults: any[] = [];
  Object.keys(batchResult?.Responses || {}).forEach((table) => {
    const items = batchResult.Responses ? batchResult.Responses[table] : [];
    items.forEach((item) => {
      jsonResults.push(AWS.DynamoDB.Converter.unmarshall(item));
    });
  });
  return jsonResults;
};

const getOne = async <T = any> (params: AWS.DynamoDB.GetItemInput): Promise<T | undefined> => {
  const client = getDbClient();
  const result = await client.getItem(params).promise();
  if (result.Item) {
    return AWS.DynamoDB.Converter.unmarshall(result.Item) as T;
  }
  return undefined;
};

const updateOne = async <T = any> (params: AWS.DynamoDB.UpdateItemInput) => {
  const client = getDbClient();
  const res = await client
    .updateItem({
      ReturnValues: 'ALL_NEW',
      ...params,
    })
    .promise();
  if (res.Attributes) {
    return AWS.DynamoDB.Converter.unmarshall(res.Attributes);
  }
  return undefined;
};

const createOne = async <T = any> (params: AWS.DynamoDB.PutItemInput) => {
  const client = getDbClient();
  const res = await client
    .putItem({
      ...params,
    })
    .promise();
  if (res.Attributes) {
    return AWS.DynamoDB.Converter.unmarshall(res.Attributes);
  }
  return undefined;
};

const deleteOne = async (params) => {
  const client = getDbClient();
  await client
    .deleteItem({
      ...params,
    })
    .promise();
};

class UpdateExpressionBuilder {
  private setExpressions: any[];
  private deleteExpressions: any[];
  private expressionAttributeNames: any;
  private expressionAttributeValues: any;
  private ignoredKeys: any[];
  private deleteValueIfUndefined: boolean;
  private source: any;
  constructor(source, opts:any = {}) {
    this.setExpressions = [];
    this.deleteExpressions = [];
    this.expressionAttributeNames = {};
    this.expressionAttributeValues = {};

    this.ignoredKeys = [];
    this.deleteValueIfUndefined = false;

    if (typeof source === 'string') {
      this.source = JSON.parse(source);
    } else {
      this.source = source;
    }
    if (opts.ignoredKeys) {
      this.ignoredKeys.push(...opts.ignoredKeys);
    }
    if (opts.deleteValueIfUndefined !== undefined) {
      this.deleteValueIfUndefined = opts.deleteValueIfUndefined;
    }
  }

  build() {
    Object.keys(this.source)
      .filter((key) => !this.ignoredKeys.includes(key))
      .forEach((key) => {
        const value = this.source[key];
        if (value === undefined) {
          if (this.deleteValueIfUndefined) {
            this.deleteExpressions.push(`#${key}`);
            this.expressionAttributeNames[`#${key}`] = key;
          }
          return;
        }
        this.setExpressions.push(`#${key} = :${key}`);
        this.expressionAttributeNames[`#${key}`] = key;

        let marshalled;
        if (Array.isArray(value)) {
          marshalled = AWS.DynamoDB.Converter.input(value, {
            convertEmptyValues: true,
          });
        } else if (typeof value === 'number' || typeof value === 'boolean' || typeof value === 'string') {
          marshalled = AWS.DynamoDB.Converter.input(value, {
            convertEmptyValues: true,
          });
        } else {
          marshalled = {
            M: AWS.DynamoDB.Converter.marshall(value, {
              convertEmptyValues: true,
            }),
          };
        }
        this.expressionAttributeValues[`:${key}`] = marshalled;
      });
    if (this.setExpressions.length === 0) {
      return this.deleteExpressions.length !== 0;
    } else {
      return true;
    }
  }

  getSetExpression() {
    let expr = '';
    if (this.setExpressions.length > 0) {
      expr += `SET ${this.setExpressions.join(', ')}`;
    }
    if (this.deleteExpressions.length > 0) {
      expr += ` REMOVE ${this.deleteExpressions.join(', ')}`;
    }
    return expr.trim();
  }
  getExpressionAttributeNames() {
    return this.expressionAttributeNames;
  }
  getExpressionAttributeValues() {
    return this.expressionAttributeValues;
  }

  printDebug() {
    console.log('SetExpr:', this.getSetExpression());
    console.log('ExAtNa:', this.getExpressionAttributeNames());
    console.log('ExAtVal:', JSON.stringify(this.getExpressionAttributeValues(), null, 2));
  }
}

class Transaction {
  private client: AWS.DynamoDB;
  private transactItems: AWS.DynamoDB.TransactWriteItemList;

  constructor() {
    this.client = getDbClient();
    this.transactItems = [];
  }

  public get getItems() {
    return this.client.transactGetItems;
  }

  public addUpdate(item: AWS.DynamoDB.TransactWriteItem) {
    this.transactItems.push(item);
  }

  public async run() {
    return await this.client.transactWriteItems({
      TransactItems: this.transactItems,
    }).promise();
  }
}

export {
  getDbClient,
  queryList,
  queryOnePage,
  scanList,
  scanOnePage,
  getMany,
  getOne,
  updateOne,
  UpdateExpressionBuilder,
  createOne,
  deleteOne,
  getDynamoDbPatternsClient,
  Transaction,
};
