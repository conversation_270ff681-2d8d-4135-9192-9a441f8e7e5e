import {APIGatewayProxyHandlerV2, Context, Hand<PERSON>} from 'aws-lambda'
import {greetUs} from "./test.js";
import {SurveyConfig} from '@oss/shared/types.js'
export const restApi: APIGatewayProxyHandlerV2<void> = async (evt, ctx) => {
    return {
        body: JSON.stringify({
            result: 'OK'
        }),
        statusCode: 200,
        isBase64Encoded: false,
        headers: {'content-type': 'application/json'}
    }
}

export const test = async (evt: any, ctx: Context) => {
    const sampleConfig : SurveyConfig = {
        id: 'xxx',
        name: 'myConfig'
    }
    return {
        result: greetUs(evt),
        sampleConfig,
    }
}