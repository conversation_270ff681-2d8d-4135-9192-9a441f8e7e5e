/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import * as AWS from 'aws-sdk';
import { getEnvironmentCredentials } from '../utils/aws-helper';
import { getNextUuid } from '../utils/util';

const sendSQSForLogicalDeleteSurveyResults = async (userId, partitionKey, check) => {
  const client = new AWS.SQS(getEnvironmentCredentials());
  await client.sendMessage({
    QueueUrl: process.env.ASYNC_OPERATIONS_QUEUE,
    MessageGroupId: 'SurveyAccessor-LogicalDelete-SurveyResults-MessageGroupId',
    MessageDeduplicationId: getNextUuid(),
    MessageBody: JSON.stringify({
      userId: userId,
      surveyResultsPartitionKey: partitionKey,
      check: check,
    }),
  }).promise();
};

export {
    sendSQSForLogicalDeleteSurveyResults,
};
