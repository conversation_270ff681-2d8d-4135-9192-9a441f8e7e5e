/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import _, { isEmpty, pick } from 'lodash';

export interface DynamoItem<T = string, U = string> {
  partitionKey: T;
  sortKey?: U;
  createdAt?: number;
  updatedAt?: number;
}

export class BaseService {
  protected queryParams(tableName: string, partitionKey: string, sortKey?: string) {
    const params: any = {
      TableName: tableName,
      Key: {
        partitionKey: { S: partitionKey },
      },
    };
    if (sortKey) {
      params.Key.sortKey = {
        S: sortKey,
      };
    }
    return params;
  }

  protected queryListParams(tableName: string, partitionKey: string, sortKey?: string) {
    const params: any = {
      TableName: tableName,
      KeyConditionExpression: '#pk = :pk',
      ExpressionAttributeValues: {
      ':pk': { S: partitionKey },
      },
      ExpressionAttributeNames: {
        '#pk': 'partitionKey',
      }
    };
    if (sortKey) {
      params.KeyConditionExpression += ' and #sk = :sk';
      params.ExpressionAttributeNames['#sk'] = 'sortKey';
      params.ExpressionAttributeValues[':sk'] = { S: sortKey };
    }
    return params;
  }

  protected filterData<T>(data: T[], fields?: string[], preferFields?: string[]): Partial<T>[];
  protected filterData<T>(data: T, fields?: string[], preferFields?: string[]): Partial<T>;
  protected filterData<T>(data: T, fields?: string[], preferFields?: string[]): any {
    if (Array.isArray(data)) {
      return data.map((item) => this.filterData(item, fields, preferFields));
    }

    const dataKeys = _.isPlainObject(data) ? Object.keys(data as any) : [];

    let filters: any[] = [];
    if (fields && !isEmpty(fields)) {
      filters = fields.filter((field) => dataKeys.includes(field));
    }
    if (!isEmpty(preferFields)) {
      filters = [...filters, ...(preferFields || [])];
    }
    if (filters.length === 0) {
      return null;
    }

    return pick(data, filters);
  }
}
