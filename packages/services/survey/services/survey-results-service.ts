/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import * as AWS from 'aws-sdk'
import * as db from '../utils/db.js';
import {getEnvironmentCredentials} from '../utils/aws-helper.js';
import config from '../../api-reception/config/static.js';
import c from '../../api-reception/config/constants.js';
import MESSAGES from '../../api-reception/config/messages.js';
import * as calendars from './calendars-service.js';
import * as calendarsExt from './calendars-ext.js';
import * as notifications from './push-notifications-manager.js';
import {getFormatedAnswerCode} from '../utils/util.js';
import {sendMessages} from '../utils/line.js';
import {SurveyResultsHelper} from './helpers/sr_helper.js';
import {SurveyTransactionHelper} from './helpers/survey-transaction-helper.js';
import * as ResultsControlService from '../services/results-control-service.js';
import {SurveyResult} from './helpers/survey/results_model.js';
import * as _ from 'lodash';
import {sendSQSForLogicalDeleteSurveyResults} from './sqs-service.js';
import {ItemsMergeHelper} from './helpers/item-merge-helper.js';
import {SurveyResultsHistoryHelper} from './helpers/survey-results-history-helper.js';
import SurveyResultsRollbackHelper from './helpers/survey-results-rollback.js';
import {buildCalendar, CalendarModel} from '../models/calendar.js';
import {BirthdayModel} from '../models/date.js';
import {SurveyConfig} from '../types/survey-config.js';
import {TemporarySurveyResults} from '../types/survey-results.js';
import * as coronaExamination from './helpers/corona-shinken.js';
import console from "console";
import PaymentResultsService, { PaymentResult }  from 'payment/api/service/user/payment-results-service.js';
import * as timeUtils from '../../common/utils/time-utils.js';
import { notificationReceiptHelper } from 'payment/api/service/helpers/payment-results-helper.js';

export class SurveyResultsService {
  constructor(private conf: {
    surveyResultsTable?: string,
    surveyConfigTable?: string,
  } = {}) {
  }
  getTableName() {
    return this.conf.surveyResultsTable || config.get(c.TABLE_SURVEY_RESULTS);
  }

  dynamoDbSurveyConfigTable() {
    return this.conf.surveyConfigTable || config.get(c.TABLE_SURVEY_CONFIGS );
  }

  dynamoDbMemberConfigTable() {
    return config.get(c.TABLE_MEMBER_CONFIGS);
  }

  async query(queryParams) {
    const params: AWS.DynamoDB.QueryInput = {
      TableName: this.getTableName(),
      ExpressionAttributeValues: queryParams.expressionValues,
      ExpressionAttributeNames: queryParams.expressionNames,
      KeyConditionExpression: queryParams.keyExpression,
      FilterExpression: queryParams.filterExpression,
    };
    return await db.queryList(params);
  }

  async queryByIndex(queryParams) {
    const params = {
      TableName: this.getTableName(),
      IndexName: queryParams.IndexName,
      ExpressionAttributeValues: queryParams.expressionValues,
      KeyConditionExpression: queryParams.keyExpression,
    };
    return await db.queryList(params);
  }

  async getAllUserResults(surveyId, userId) {
    const params = {
      index: 'surveyId-partitionKey-index',
      query: 'surveyId = :surveyId and begins_with(partitionKey, :pk)',
      mapping: {
        [':surveyId']: surveyId,
        [':pk']: `${surveyId}#${userId}`,
      },
    };
    return await db.getDynamoDbPatternsClient(this.getTableName()).queryItems(params);
  }

  async getAllSurveyResultsBySurveyIdPartitionKey(surveyId, partitionKey) {
    const params = {
      index: 'surveyId-partitionKey-index',
      query: 'surveyId = :surveyId and partitionKey = :pk',
      mapping: {
        [':surveyId']: surveyId,
        [':pk']: partitionKey,
      },
    };
    return await db.getDynamoDbPatternsClient(this.getTableName()).queryItems(params);
  }

  async scan(queryParams) {
    const params: any = {
      TableName: this.getTableName(),
      ExpressionAttributeValues: queryParams.expressionValues,
      FilterExpression: queryParams.keyExpression,
    };
    if (queryParams.expressionNames) {
      params.ExpressionAttributeNames = queryParams.expressionNames;
    }
    return await db.scanList(params);
  }

  async querySurveyIdSortKeyIndex(queryParams) {
    const params: any = {
      TableName: this.getTableName(),
      IndexName: 'surveyId-index',
      KeyConditionExpression: 'surveyId = :sid',
      ExpressionAttributeValues: queryParams.expressionValues,
      FilterExpression: queryParams.filterExpression,
    };
    if (queryParams.expressionNames) {
      params.ExpressionAttributeNames = queryParams.expressionNames;
    }
    return await db.queryList(params);
  }

  async getSurveyResult(partitionKey) {
    if (!partitionKey) {
      return [];
    }
    const params = {
      keyExpression: 'partitionKey = :pk',
      expressionValues: { ':pk': { S: partitionKey } },
    };
    return await this.query(params);
  }

  async getTemporarySurveyResults(orderId: string) {
    if (!orderId) {
      return null;
    }
    return await this.getOne<TemporarySurveyResults>('temporary', orderId);
  }

  async getOne<T = any>(partitionKey: string, sortKey: string, option?: Omit<AWS.DynamoDB.GetItemInput, 'Key' | 'TableName'>) {
    const Key = {
      partitionKey: { S: partitionKey },
      sortKey: { S: sortKey },
    }
    const params: AWS.DynamoDB.GetItemInput = {
      TableName: this.getTableName(),
      Key: Key,
    }
    if (option) {
      Object.assign(params, option);
    }

    return await db.getOne<T>(params)
  }

  async getOneItem<T = any>(partitionKey, sortKey) {
    const Key = {
      partitionKey: { S: partitionKey },
      sortKey: { S: sortKey },
    };

    const params = {
      TableName: this.getTableName(),
      Key: Key,
      ProjectionExpression: 'answerCode',
    };
    const res = await db.getOne<T>(params);
    return res;
  }

  async updateCounter(partitionKey, sortKey) {
    const Key = {
      partitionKey: { S: partitionKey },
      sortKey: { S: sortKey },
    };

    const res = await db.updateOne({
      TableName: this.getTableName(),
      ExpressionAttributeNames: {
        '#AN': 'answerCode',
      },
      ExpressionAttributeValues: {
        ':increase': { N: '1' },
      },
      Key: Key,
      UpdateExpression: 'SET #AN = #AN + :increase',
    });
    return res;
  }

  async putItems(items) {
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      const marshalled = AWS.DynamoDB.Converter.marshall(item, {
        convertEmptyValues: true,
      });
      await db.createOne({
        TableName: this.getTableName(),
        Item: marshalled,
      });
    }
  }

  async generateCounterForAppending(configs) {
    let answerCode = 0;
    if (configs.isAppending && configs.isAppending.value) {
      const partitionKey = configs.surveyId;
      const sortKey = 'counter';

      //Check Counter existing or not
      const currentCounter = await this.getOneItem(partitionKey, sortKey);
      if (currentCounter === undefined) {
        //not existing => insert first counter
        const items = [
          {
            partitionKey: partitionKey,
            sortKey: sortKey,
            answerCode: 1,
          },
        ];
        await this.putItems(items);
        answerCode = 1;
      } else {
        // update counter -> get lastest counter(answerCode)
        const response = await this.updateCounter(partitionKey, sortKey);
        if (response && response.answerCode) {
          answerCode = response.answerCode;
        }
      }
    }

    return answerCode;
  }

  async checkForAlreadyExistingReservation(reservationItem) {
    const keys = reservationItem.sortKey.split('#');
    const itemKey = keys[0].split('_')[0];
    const sortKey = keys[2];
    let additionalExpressionAttributeValues = {};
    let filterExpression = '';
    const isActualReservation = sortKey.indexOf('|') !== -1;

    if (isActualReservation) {
      const [cId, day, timeSlot] = sortKey.split('|');
      additionalExpressionAttributeValues = {
        ':cid': { S: keys[1] + '#' + keys[2].substring(0, 6) },
        ':rdinfo': { S: '|' + day + '|' + timeSlot },
      };
      filterExpression =
        'userId = :uid and contains(#v,:cid) and contains(#v,:rdinfo) and not contains(#c,:cdel) and not contains(#c,:ccan)';
    } else {
      additionalExpressionAttributeValues = {
        ':sk': { S: sortKey },
      };
      filterExpression = 'userId = :uid and contains(#v,:sk) and not contains(#c,:cdel) and not contains(#c,:ccan)';
    }

    const params = {
      TableName: this.getTableName(),
      IndexName: 'surveyId-sortKey-index',
      ExpressionAttributeValues: {
        ':sid': { S: reservationItem.surveyId },
        ':ik': { S: itemKey },
        ':uid': { S: reservationItem.userId },
        ':cdel': { S: '取り消し' },
        ':ccan': { S: 'キャンセル' },
        ...additionalExpressionAttributeValues,
      },
      ExpressionAttributeNames: {
        '#v': 'value',
        '#c': 'check',
      },
      KeyConditionExpression: 'surveyId = :sid and begins_with(sortKey,:ik)',
      FilterExpression: filterExpression,
    };
    const result = await db.queryList(params);
    if (Array.isArray(result) && result.length > 0) {
      return 'reserved';
    }
  }

  getCategoryIdFromReservationAnswer(reservationAnswer: { [key: string]: string }): string {
    const [categoryId] = reservationAnswer.value.split('|');
    return categoryId;
  }

  getBirthdayFromAnswerOrError(
    surveyConfig: SurveyConfig,
    surveyResults: { itemKey: string, value: any }[],
    calendar: CalendarModel
  ): BirthdayModel | null {
    // 予約可能年齢が有効かチェック
    const reservableAge = calendar.getReservableAge();
    if (!reservableAge || reservableAge.controlTypeIs('allAge')) {
      return null;
    }
    // 生年月日アイテムの数をチェック
    const birthdayItems = surveyConfig.surveySchema.filter(item => item.type === 'birthday');
    if (birthdayItems.length !== 1) {
      // 帳票の設定が間違っている扱い
      throw 'InvalidSurvey';
    }
    // 生年月日の回答値が存在するかチェック
    const birthdayAnswer = surveyResults.find(answer => answer.itemKey === birthdayItems[0].itemKey);
    if (!birthdayAnswer?.value) {
      throw 'MissingBirthdayAnswer';
    }

    return new BirthdayModel(birthdayAnswer.value, 'yyyy-MM-dd');
  }

  private setOrderId(paymentResult, surveyResults) {
    if (paymentResult) {
      surveyResults.forEach(r => r.orderId = paymentResult.orderId);
    }
  }

  async runSurveyResultsTransaction(
    surveyConfig: SurveyConfig,
    surveyResults,
    oldResults,
    deleteOldSurveyResults,
    requestStartTime,
    paymentResult: PaymentResult
  ) {
    //Look for a reservation in the results to save
    const reservationQuestion = surveyConfig.surveySchema.find((obj) => obj.type === 'reservation');
    let reservationItem:any = null;
    if (reservationQuestion) {
      reservationItem = surveyResults.find((obj) => obj.itemKey === reservationQuestion.itemKey);
    }

    // NOTE: 決済結果が存在する場合はorderIdを帳票結果にセット
    let oldPaymentResult: PaymentResult = null;
    const isExistOldResults = oldResults.length > 0;
    if (isExistOldResults && surveyConfig.usePayment === 1) {
      const result = await PaymentResultsService.getPaymentResultBySortKey(oldResults[0].partitionKey);
      oldPaymentResult = result.payload.data;
    }
    this.setOrderId(oldPaymentResult || paymentResult, surveyResults);

    // コロナワクチンの帳表の場合は同時刻に登録済みかどうかのチェックを除外
    if (
      (!surveyConfig.surveyType || (surveyConfig.surveyType && surveyConfig.surveyType !== 'corona')) &&
      reservationItem
    ) {
      const reservationCheckResponse = await this.checkForAlreadyExistingReservation(reservationItem);
      if (reservationCheckResponse === 'reserved') {
        return {
          result: 'ERROR',
          code: 'quota_exceeded',
          errorMessage: '指定された日付・時間は既に予約済みです。お手数ですが、他の日付・時間を選んでください。',
        };
      }
    }

    //Generate the transaction log for new surveyResults
    //Skip generating transaction for Update Type
    const isUpdateSurveyType = !('isAppending' in surveyConfig) || !surveyConfig.isAppending.value;
    try {
      await ResultsControlService.createTransactionRecord(
        surveyResults[0].userId,
        surveyResults[0].partitionKey,
        'started',
        !isUpdateSurveyType
      );
    } catch (err: any) {
      //An error occured creating transaction log for new surveyResults, likely ConditionExpression
      console.error('[ERROR] ' + err.stack);
      return {
        result: 'ERROR',
        code: 'unable_to_start_new_results_transaction',
        errorMessage:
          '予約が混雑しており登録失敗しました。暫く時間をおいた後に、始めの手順から予約をしてください。',
        errorDetails: err.message,
      };
    }

    // Update the items of surveyResults expect reservation
    // 予約以外の帳票結果を更新
    try {
      console.log('surveyResult items: ', surveyResults.filter((obj) => obj.value)) // valueがある回答結果のみログに表示
      if (deleteOldSurveyResults) {

        //console.log('before updateItems', surveyResults)
        // Set the items partition key to the old partition key
        // NOTE: There is no time, so update properly when refactoring.
        /*    surveyResults = surveyResults.map((obj) => {
          obj.partitionKey = oldResults[0].partitionKey;
          return obj;
        }); */

        // NOTE: Temporal workaround. It is very important for every surveyResults to include type on DB.
        // めも: これは一時的なワークアラウンドで、全ての帳票結果にtypeを含めることが重要
        // add type temporal obj itemKey: type
        // 一時的にobjにitemKey: typeを追加
        const keyTypesMap = surveyConfig.surveySchema.reduce((map, schemaItem) => {
          map[schemaItem.itemKey] = schemaItem.type;
          return map;
          }, {});
        const keyTypes = surveyResults.map((obj) => keyTypesMap[obj.itemKey] || null);

        console.log('keyTypes: ', keyTypes)

        await this.updateItems(
          surveyResults.filter((obj) => !reservationQuestion || obj.itemKey !== reservationQuestion.itemKey),
          oldResults,
          keyTypes,
          surveyConfig.usePayment === 1
        );
      } else {
        await this.putItems(
          surveyResults.filter((obj) => !reservationQuestion || obj.itemKey !== reservationQuestion.itemKey)
        );
      }
    } catch (err: any) {
      //An error occured adding the new surveyResults to DB, likely Throttling
      console.error('[ERROR] ' + err.stack);
      return {
        result: 'ERROR',
        code: 'throttling_on_new_results_save',
        errorMessage:
          '予約が混雑しており登録失敗しました。暫く時間をおいた後に、始めの手順から予約をしてください。',
        errorDetails: err.message,
      };
    }

    //Run Transaction for creating reservation or updating transaction logs if needed
    try {
      const surveyTransactionHelper = new SurveyTransactionHelper();
      if (surveyConfig.usePayment === 1) {
        if (oldPaymentResult) {
          // NOTE: 決済帳票かつ前回の帳票結果が存在する場合は前回の決済結果を基にして新しく決済結果を作る
          const now = timeUtils.nowUnixSec();
          surveyTransactionHelper.addPaymentResultsItem({
            ...oldPaymentResult,
            sortKey: surveyResults[0].partitionKey,
            createdAt: now,
            updatedAt: now,
          });
        } else if (paymentResult) {
          // NOTE: 決済帳票かつ決済結果が渡された場合は渡された値で決済結果を作る(合計金額が0円の場合のみこのパターンになる)
          surveyTransactionHelper.addPaymentResultsItem(paymentResult);
        }
      }
      
      let needCalendarReservation = !!reservationItem;

      // special case for corona vaccination with examinations
      const isCoronaExaminationSurvey = coronaExamination.isCoronaExaminationSurvey(surveyConfig);
      // we don't need to reserve timeslot, if an applicant does not want any examination (or does not need to take them)
      if (isCoronaExaminationSurvey) {
        needCalendarReservation = needCalendarReservation && coronaExamination.isSomeExaminationsSelected(surveyConfig, surveyResults)
      }

      if (needCalendarReservation) {
        surveyTransactionHelper.addSurveyResultsItem(reservationItem);
        if (isCoronaExaminationSurvey) {
          surveyTransactionHelper.addCoronaExaminationLineReservation(surveyConfig, surveyResults);
        }
        const categoryId = this.getCategoryIdFromReservationAnswer(reservationItem);
        const calendar = await buildCalendar(categoryId);
        const birthday = this.getBirthdayFromAnswerOrError(surveyConfig, surveyResults, calendar) || undefined;
        await surveyTransactionHelper.addSurveyCalendarReservation(reservationItem, calendar, birthday);
      }
      //Set newResults to finished
      surveyTransactionHelper.addTransactionLogRecord(
        surveyResults[0].userId,
        surveyResults[0].partitionKey,
        'finished'
      );
      //console.log("Passed surveyTransactionHelper.addTransactionLogRecord")
      if (isExistOldResults && !isUpdateSurveyType) {
        surveyTransactionHelper.addTransactionLogRecord(oldResults[0].userId, oldResults[0].partitionKey, 'started');
      }
      surveyTransactionHelper.debugTransactionRecord();
      await surveyTransactionHelper.executeTransaction(requestStartTime);
    } catch (err: any) {
      if (!deleteOldSurveyResults) {
        //The transaction did not finish correctly, if new items where put, attempt to delete.
        //If unable to delete here because of throttling, automatic cleaner function will delete later.
        try {
          await this.deleteSurveyResult(surveyResults[0].partitionKey);
        } catch (internalError: any) {
          console.error(internalError.stack);
          console.error(
            'Unable to delete survey results immediately on unfinished transaction. ' +
            'Results should be deleted by automatic cleaner.'
          );
        }
      }
      //Calendar quotas filled up before user pressed 確定 in LIFF, not a processing error that requires logging
      if (err.name === 'TransactionCanceledException' && err.stack.includes('ConditionalCheckFailed')) {
        return {
          result: 'ERROR',
          code: 'quota_exceeded',
          errorMessage:
            '指定された日付・時間にあっている枠が埋まっています。お手数ですが、他の日付・時間を選んでください。',
        };
      } else if (err.name === 'TransactionCanceledException' && err.stack.includes('ValidationError')) {
        console.error('[ERROR] ' + err.stack);
        return {
          result: 'ERROR',
          code: 'validation_error',
          errorMessage: '入力内容の保存に失敗しました。管理者にお問い合わせください。尚、やり直す場合はブラウザを閉じてください。',
        };
      } else if (c.TRANSACTION_ERROR_VALUES.includes(err)) {
        return {
          result: 'ERROR',
          code: c.TRANSACTION_ERROR_MESSAGES[err].code,
          errorMessage: c.TRANSACTION_ERROR_MESSAGES[err].message,
        };
      } else {
        console.error(
          '[ERROR] ' +
          (err === 'out_of_time' ? 'Transaction did not complete within alloted time interval.' : err.stack)
        );
        return {
          result: 'ERROR',
          code: 'error_on_transaction',
          errorMessage: reservationItem
            ? '予約が混雑しており登録失敗しました。暫く時間をおいた後に、始めの手順から予約をしてください。'
            : '現時点では混雑しており登録失敗しました。暫く時間をおいた後に始めの手順から予約をしてください。',
          errorDetails: err.message,
        };
      }
    }

    const resultsHistoryHelper = new SurveyResultsHistoryHelper();
    if (isUpdateSurveyType) {
      if (oldResults.length === 0) {
        await resultsHistoryHelper.logSurveyResultsCreated(surveyResults, surveyConfig);
      } else {
        await resultsHistoryHelper.logSurveyResultsUpdated(surveyResults, oldResults, surveyConfig);
      }
    } else {
      await resultsHistoryHelper.logSurveyResultsCreated(surveyResults, surveyConfig);
    }

    try {
      if (oldResults.length > 0 && !isUpdateSurveyType) {
        await sendSQSForLogicalDeleteSurveyResults(
          oldResults[0].userId,
          oldResults[0].partitionKey,
          oldResults[0].check
        );
      }
    } catch (err: any) {
      console.error('[ERROR] Unable to push to SNS topic to delete old surveyResults ' + err.stack);
      console.error(err);
    }

    const newConfig = await this.checkAndGenerateSchemaForConditions(surveyResults, surveyConfig);
    _.remove(newConfig.surveySchema, function (o: any) {
      return o.type.trim() === 'guide';
    });

    await this.sendBackNotification(surveyResults, newConfig);
    if (paymentResult) {
      await notificationReceiptHelper.sendNotification({
        orderId: paymentResult.orderId,
        serviceName: paymentResult.serviceName,
        payMethod: paymentResult.payMethod,
        surveyResultsPk: paymentResult.sortKey,
        userId: paymentResult.userId,
      });
    }
    
    return {
      result: 'OK',
      answerCode:
        surveyConfig.isAppending && surveyConfig.isAppending.value
          ? getFormatedAnswerCode(surveyResults[0].answerCode)
          : null,
    };
  }

  async cancelUsingTransaction(surveyResults, configs, originalCheck, requestStartTime, rollbackHelper: SurveyResultsRollbackHelper) {
    const reservationQuestion = configs.surveySchema.find((item) => item.type === 'reservation');

    //Update Items except for reservation
    await this.updateItemsWithoutDelete(
      surveyResults.filter((obj) => !reservationQuestion || obj.itemKey !== reservationQuestion.itemKey)
    );

    let reservationItem:any = null;
    let cancelReservationInCalendars = false;
    if (reservationQuestion) {
      reservationItem = surveyResults.find(obj => obj.itemKey === reservationQuestion.itemKey);
      cancelReservationInCalendars = originalCheck !== 'キャンセル';
    }

    if (reservationItem || cancelReservationInCalendars) {
      try {
        const surveyTransactionHelper = new SurveyTransactionHelper();
        if (reservationItem) {
          surveyTransactionHelper.addSurveyResultsItem(reservationItem);
        }
        if (cancelReservationInCalendars) {
          await surveyTransactionHelper.cancelSurveyCalendarReservation(reservationItem, undefined, rollbackHelper);
        }
        surveyTransactionHelper.debugTransactionRecord();
        await surveyTransactionHelper.executeTransaction(requestStartTime);
      } catch (err: any) {
        if (err === 'cancellation_exceeded') {
          console.error(
            '[ERROR] cancellation_exceeded キャンセルしたら、カレンダーの予約枠数は0未満になります。キャンセルできません。'
          );
          return {
            result: 'ERROR',
            errorMessage: '管理者にお問い合わせください。',
          };
        }
        //Calendar quotas filled up before user pressed 確定 in LIFF, not a processing error that requires logging
        if (err.name === 'TransactionCanceledException' && err.stack.includes('ConditionalCheckFailed')) {
          return {
            result: 'ERROR',
            code: 'quota_exceeded',
            errorMessage: '指定された日付・時間にあっている枠がキャンセルできない。管理者にお問い合わせください。',
          };
        } else if (err.name === 'TransactionCanceledException' && err.stack.includes('ValidationError')) {
          console.error('[ERROR] ' + err.stack);
          return {
            result: 'ERROR',
            code: 'validation_error',
            errorMessage: '入力内容の保存に失敗しました。管理者にお問い合わせください。',
          };
        } else {
          console.error(
            '[ERROR] ' +
            (err === 'out_of_time' ? 'Transaction did not complete within alloted time interval.' : err.stack)
          );
          return {
            result: 'ERROR',
            code: 'error_on_transaction',
            errorMessage: reservationItem
              ? '予約が混雑しており登録失敗しました。暫く時間をおいた後に、始めの手順から予約をしてください。なお、この画面は閉じてください。'
              : '現時点では混雑しており登録失敗しました。暫く時間をおいた後に、始めの手順から予約をしてください。なお、この画面は閉じてください。',
            errorDetails: err.message,
          };
        }
      }
    }

    return { result: 'OK' };
  }

  /**
   * Run processing for updating items
   * @param {*} surveyConfig Survey config
   * @param {*} surveyResults List of surveyResults to save
   * @param {*} oldResults List of old surveyResults to update (could be empty array)
   * @param requestStartTime
   * @returns results object
   */
  async processCreatingSurveyResults(surveyConfig, surveyResults, oldResults, requestStartTime, paymentResult?: PaymentResult) {
    //Set CreatedAt, UpdatedAt and Index values for new surveyResults
    const results = await this.getSurveyResult(surveyResults[0].partitionKey);
    //console.log('results', JSON.stringify(results, null, 2));
    //console.log('surveyResults', JSON.stringify(surveyResults, null, 2));
    const now = Math.floor(Date.now() / 1000);
    const createdAt = results.length !== 0 ? results[0].createdAt : now;
    const updatedAt = now;

    for (const element of surveyResults) {
      delete element.isLiffMode;
      element.createdAt = createdAt;
      element.updatedAt = updatedAt;
    }

    const cacheHelper = new SurveyResultsHelper(surveyConfig, {});
    await cacheHelper.setIndexValues(surveyResults);

    const answerCode = await this.generateCounterForAppending(surveyConfig);
    // Add answerCode for save database
    if (answerCode > 0) {
      surveyResults.forEach(function (element) {
        element.answerCode = answerCode;
      });
    }

    //更新型帳票のため。フロントからpayloadにパーティションキー入ってないので、元データ上書きします。
    if (oldResults.length === 0) {
      oldResults = results;
    }

    //新たな回答にステータスが全項目に書いているチェック
    const elementWithCheck = surveyResults.find((elem) => elem.check);
    surveyResults.forEach(function (element) {
      element.check = elementWithCheck ? elementWithCheck.check : '未対応';
    });

    return await this.runSurveyResultsTransaction(
      surveyConfig,
      surveyResults,
      oldResults,
      oldResults.length > 0,
      requestStartTime,
      paymentResult
    );
  }

  async sendBackNotification(items, configs) {
    const reservationTypeConfig = configs.surveySchema.find((item) => item.type === 'reservation');
    if (!reservationTypeConfig) {
      return true;
    }

    const calendarsManager = new notifications.CalendarReporterManager({
      getCategory: calendarsExt.getSingleCategory,
      getTitles: calendarsExt.getGlobalDisplaySettings,
      getCalendarInfo: calendarsExt.getCalendarInfo,
    });

    const bottomTextType = configs.endOfSurveyMessageType;
    let surveyMessages = [{ type: "text", text: MESSAGES.DEFAULT_END_OF_SURVEY_MESSAGE }];
    if ('endOfSurveyMessage' in configs) {
      if (Array.isArray(configs.endOfSurveyMessage)) {
        surveyMessages = configs.endOfSurveyMessage;
      } else {
        surveyMessages = [{ type: "text", text: configs.endOfSurveyMessage }];
      }
    }
    if (surveyMessages) {
      surveyMessages.forEach((message) => {
        if (message.type === "text" && message.text) {
          message.text = message.text.replace('\nブラウザを閉じてください。', '');
        }
      });
    }

    let calendarMessages: any[] = [];
    const categoryItem = items.find((item) => item.itemKey === reservationTypeConfig.itemKey);
    if (categoryItem.check !== 'キャンセル') {
      const [categoryId] = categoryItem.value.split('|');
      const calendarInfo = await calendarsManager.getCalendarInfoByCategoryId(categoryId);
      if (calendarInfo && calendarInfo.calendarMessage) {
        if (Array.isArray(calendarInfo.calendarMessage)) {
          calendarMessages = calendarInfo.calendarMessage;
        } else {
          calendarMessages = [{ type: "text", text: calendarInfo.calendarMessage }];
        }
      }
    }
    const reservationReportsManager = new notifications.ReservationReporterManager(calendarsManager, configs, {
      surveyMessages: surveyMessages,
      calendarMessages: calendarMessages,
    });
    const messages = await reservationReportsManager.buildFullReport(items, bottomTextType);
    const user = items[0].userId;
    await sendMessages(user, messages);
  }

  async getSurveyConfig(memberSurveyId, isMember = false) {
    const tableName = isMember ? this.dynamoDbMemberConfigTable() : this.dynamoDbSurveyConfigTable();
    return await db.queryList({
      TableName: tableName,
      KeyConditionExpression: 'surveyId = :s_id',
      ExpressionAttributeValues: {
        ':s_id': { S: memberSurveyId },
      },
    });
  }

  async getTotalMemberSurveySubId() {
    const params = {
      TableName: this.dynamoDbSurveyConfigTable(),
    };
    const list = await db.scanList(params);

    return {
      // list: list,
      length: list.length,
    };
  }

  async getLastResultNumber(memberSurveyId) {
    const result = await this.getSurveyConfig(memberSurveyId, true);

    let surveyConfig = { lastResultNumber: 0 };
    if (result && Array.isArray(result) && result.length > 0) {
      surveyConfig = result[0];
    }

    return {
      // surveyConfig: surveyConfig,
      lastResultNumber: surveyConfig.lastResultNumber,
    };
  }

  async updateConfigLastResultNumber(items) {
    //const tableName = this.dynamoDbSurveyConfigTable();
    const tableName = this.dynamoDbMemberConfigTable();

    const surveyConfigObj = {
      lastResultNumber: items.lastResultNumber,
    };

    // If memberSurveyId is not set, use surveyId
    if (!items.memberSurveyId) {
      items.memberSurveyId = items.surveyId;
    }
    const Key = {
      surveyId: { S: items.memberSurveyId },
    };

    const updateBuilder = new db.UpdateExpressionBuilder(surveyConfigObj);
    updateBuilder.build();
    return await db.updateOne({
      TableName: tableName,
      Key: Key,
      ExpressionAttributeNames: updateBuilder.getExpressionAttributeNames(),
      ExpressionAttributeValues: updateBuilder.getExpressionAttributeValues(),
      UpdateExpression: updateBuilder.getSetExpression(),
    });
  }

  async updateItems(items: Array<{ partitionKey: string; sortKey: string; [key: string]: any }>, oldItems: Array<{ partitionKey: string; sortKey: string; [key: string]: any }>, keyTypes, usePayment = false) {
    const deletedItems = [];

    console.log(`Update items | `, items);
    // Loop through each item and update it
    // 各アイテムをループして更新
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      //console.log("keyTypes", keyTypes[i]);

      //console.log("keyTypes", keyTypes);

      console.log(`[item] SortKey: ${item.sortKey} | Type: ${JSON.stringify(keyTypes[i] ?? 'null')}`);

      const Key = {
        partitionKey: { S: item.partitionKey },
        sortKey: { S: item.sortKey },
      };

      const updateItem = Object.assign({}, item);
      delete updateItem.partitionKey;
      delete updateItem.sortKey;
      for (const key of Object.keys(updateItem)) {
        if (updateItem[key] === null || updateItem[key] === undefined) {
          delete updateItem[key];
        }
      }

      // Create update builder to prepare for DynamoDB update
      // DynamoDBの更新の準備のために更新ビルダーを作成
      const updateBuilder = new db.UpdateExpressionBuilder(updateItem);
      updateBuilder.build();
      const updateParams = {
        TableName: this.getTableName(),
        Key: Key,
        ExpressionAttributeNames: updateBuilder.getExpressionAttributeNames(),
        ExpressionAttributeValues: updateBuilder.getExpressionAttributeValues(),
        UpdateExpression: updateBuilder.getSetExpression(),
      };
      await db.updateOne(updateParams); // Actual update to DynamoDB | DynamoDBへの実際の更新

      // if memberNumber replace previous entry (since all memberNumber are update type.)
      // メンバー番号が更新型のため、前のエントリを置き換える (全てのメンバー番号が更新型)
      // TODO: WIP　未完成
      if (keyTypes[i] === 'memberNumber') {
        //const oldItem = oldItems.find((oldItem) => oldItem.sortKey === item.sortKey);
      }

      // if item is a choicegroupheader then set all itemKey to null except for the one passed in
      // アイテムがchoicegroupheaderの場合、渡されたものを除いてすべてのitemKeyをnullに設定
      if (keyTypes[i] === 'choicegroupheader') {
        const itemKey = item.itemKey;
        const sortKey = item.sortKey;

        console.log(`[choicegroupheader] sortKey: ${itemKey} | value: ${item.value}`);
        

        // Query all items where partitionKey matches and sortKey begins with the itemKey
        // パーティションキーが一致し、sortKeyがitemKeyで始まるすべてのアイテムをクエリ
        const queryParams = {
          TableName: this.getTableName(),
          KeyConditionExpression: 'partitionKey = :partitionKey AND begins_with(sortKey, :itemKey)',
          ExpressionAttributeValues: {
            ':partitionKey': { S: item.partitionKey },
            ':itemKey': { S: itemKey },
          },
        };
        const itemsWithSameKey = await db.queryList(queryParams);

        // Update all items with the same itemKey to set their value to null, except the one with the passed sortKey
        // 渡されたsortKeyを除いて、同じitemKeyを持つすべてのアイテムの値をnullに設定する
        for (const otherItem of itemsWithSameKey) {
          if (otherItem.sortKey !== sortKey) {
            const updateParams = {
              TableName: this.getTableName(),
              Key: {
                partitionKey: { S: otherItem.partitionKey },
                sortKey: { S: otherItem.sortKey },
              },
              ExpressionAttributeNames: {
                '#value': 'value',
              },
              ExpressionAttributeValues: {
                ':nullValue': { NULL: true },
              },
              UpdateExpression: 'SET #value = :nullValue',
            };
            await db.updateOne(updateParams);
          }
        }
      }


    }
    
    // delete old records, ONLY if the partition key has changed（決済？）
    // 古いレコードを削除する、パーティションキーが変更された場合のみ (決済？)
    if (usePayment && oldItems && items[0]?.partitionKey !== oldItems[0]?.partitionKey) {
      await this.deleteSurveyResult(oldItems[0].partitionKey);
    }else{
      // 更新されなかったデータは削除
      await this.deleteSurveyResultWithoutLatest(items[0].partitionKey, items[0].updatedAt);
    }
  }

  async updateItemsWithoutDelete(items) {
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      const Key = {
        partitionKey: { S: item.partitionKey },
        sortKey: { S: item.sortKey },
      };

      const updateItem = Object.assign({}, item);
      delete updateItem.partitionKey;
      delete updateItem.sortKey;
      for (const key of Object.keys(updateItem)) {
        if (updateItem[key] === null || updateItem[key] === undefined) {
          delete updateItem[key];
        }
      }

      const updateBuilder = new db.UpdateExpressionBuilder(updateItem);
      updateBuilder.build();
      const updateParams = {
        TableName: this.getTableName(),
        Key: Key,
        ExpressionAttributeNames: updateBuilder.getExpressionAttributeNames(),
        ExpressionAttributeValues: updateBuilder.getExpressionAttributeValues(),
        UpdateExpression: updateBuilder.getSetExpression(),
      };
      await db.updateOne(updateParams);
    }
  }

  async deleteSurveyResult(partitionKey) {
    const results = await this.getSurveyResult(partitionKey);
    if (results.length === 0) {
      return;
    }

    const tableName = this.getTableName();
    for (let i = 0; i < results.length; i++) {
      const result = results[i];
      await db.deleteOne({
        TableName: tableName,
        Key: {
          partitionKey: { S: result.partitionKey },
          sortKey: { S: result.sortKey },
        },
      });
    }
  }

  async updateUserIdToSurveyResultByPks(postData) {
    let res;
    for (let i = 0; i < postData.keys.length; i++) {
      const Key = {
        partitionKey: { S: postData.keys[i].partitionKey },
        sortKey: { S: postData.keys[i].sortKey },
      };
      try {
        res = await db.updateOne({
          TableName: this.getTableName(),
          ExpressionAttributeNames: {
            '#uId': 'userId',
          },
          ExpressionAttributeValues: {
            ':uIdVal': { S: postData.userId },
            ':uPre': { S: 'I' },
            ':empty': { S: '' },
          },
          Key: Key,
          UpdateExpression: 'SET #uId = :uIdVal',
          ConditionExpression: 'begins_with(#uId, :uPre) or #uId = :empty',
        });
      } catch (e: any) {
        console.error(e.name, e.constructor.name, JSON.stringify(e, null, 2));
        if (e.name !== 'ConditionalCheckFailedException') {
          throw e;
        }
      }
    }
    return res;
  }

  async deleteSingleSurveyResult(surveyResultItem) {
    const tableName = this.getTableName();
    await db.deleteOne({
      TableName: tableName,
      Key: {
        partitionKey: { S: surveyResultItem.partitionKey },
        sortKey: { S: surveyResultItem.sortKey },
      },
    });
  }

  async deleteSurveyResultWithoutLatest(partitionKey, latestUpdatedAt) {
    const results = await this.getSurveyResult(partitionKey);
    if (results.length === 0) {
      return;
    }

    const tableName = this.getTableName();
    for (let i = 0; i < results.length; i++) {
      const result = results[i];
      if (result.updatedAt === latestUpdatedAt) {
        continue;
      }
      await db.deleteOne({
        TableName: tableName,
        Key: {
          partitionKey: { S: result.partitionKey },
          sortKey: { S: result.sortKey },
        },
      });
    }
  }

  findIndexSearcheableField(surveyConfig) {
    const field = surveyConfig.surveySchema.filter((field) => {
      return field.isIndexable && field.isIndexable.value && field.isSearchable && field.isSearchable.value;
    })[0];
    return field || null;
  }

  async indexSearch(surveyConfig, fieldKey, queryParams, userId: string): Promise<any> {
    const helper = new SurveyResultsHelper(surveyConfig, {
      awsConfig: getEnvironmentCredentials(),
      resultsTable: this.getTableName(),
    });
    const indexableParameterFilter = queryParams.surveyResults.find((res) => res.itemKey === fieldKey);
    if (!indexableParameterFilter || !indexableParameterFilter.value) {
      return [];
    }
    const indexSearchResult = await helper.queryResultsByField(fieldKey, indexableParameterFilter.value);

    const surveyResults = SurveyResult.buildFromLines(indexSearchResult.items, surveyConfig);
    const filtered = surveyResults.filter((result) => {
      const isMatch = result.matches({
        logic: 'and',
        fields: queryParams.surveyResults,
      }) && result.getStatusOfResultLine() !== '取り消し' && result.answers.length > 0;
      // NOTE: 決済帳票の場合はLINEユーザーIDが一致とorderIdが一致しているレコードにフィルタリング
      if (surveyConfig.usePayment === 1) {
        return isMatch && result.orderId === queryParams.orderId && result.uid === userId;
      }
      return isMatch;
    });
    const searchResult = filtered.map((surveyResult) => {
      return {
        partitionKey: surveyResult.resultId,
        data: surveyResult.getLinedAnswers(),
      };
    });

    if (searchResult.length === 0 || searchResult[0].data.length === 0) {
      return searchResult;
    }

    const result = _.cloneDeep(searchResult);
    const countVaccinesItem = surveyConfig.surveySchema.find(item => item.type === 'countVaccines');
    // Deleting isAdminItem in search results
    for (let index = 0; index < searchResult.length; index++) {
      const eSearchResult = searchResult[index];

      const data: any[] = [];
      for (let i = 0; i < surveyConfig.surveySchema.length; i++) {
        const eleSurveySchema = surveyConfig.surveySchema[i];
        for (let j = 0; j < eSearchResult.data.length; j++) {
          const eleSearchResult = eSearchResult.data[j];
          if (eleSurveySchema.itemKey === eleSearchResult.itemKey) {
            if (eleSurveySchema.type === 'checkboxes' && !eleSurveySchema.isAdminItem && eleSearchResult.value) {
              data.push(eleSearchResult);
            }
            else if (
              (surveyConfig.surveyType === 'corona' &&
                (!countVaccinesItem &&
                  (eleSurveySchema.type === 'sesshuJisshiDate' || eleSurveySchema.type === 'sesshuVaccineMaker')) ||
                (countVaccinesItem && eleSurveySchema.type === 'sesshuJisshiDate')) ||
              !eleSurveySchema.isAdminItem && eleSurveySchema.type !== 'checkboxes'
            ) {
              data.push(eleSearchResult);
              break;
            }
          }
        }
      }
      result[index].data = data;
    }
    return result;
  }

  async getReservationData(surveyId, itemKey, userId): Promise<any> {
    const params = {
      TableName: this.getTableName(),
      IndexName: 'userId-surveyId-index',
      ExpressionAttributeValues: {
        ':sid': { S: surveyId },
        ':ik': { S: itemKey },
        ':uid': { S: userId },
        ':cdel': { S: '取り消し' },
        ':ccan': { S: 'キャンセル' },
      },
      ExpressionAttributeNames: {
        '#c': 'check',
      },
      KeyConditionExpression: 'userId = :uid and surveyId = :sid',
      FilterExpression:
        'begins_with(sortKey,:ik) and not contains(#c,:cdel) and not contains(#c,:ccan) and attribute_not_exists(deletedAt)',
    };
    return await db.queryList(params);
  }

  async getMergedResultsByUserId(surveyId, lineUserId, surveyConfig) {
    const params = {
      TableName: this.getTableName(),
      IndexName: 'userId-surveyId-index',
      ExpressionAttributeValues: {
        ':sid': { S: surveyId },
        ':uid': { S: lineUserId },
        ':cdel': { S: '取り消し' },
      },
      ExpressionAttributeNames: {
        '#c': 'check',
      },
      KeyConditionExpression: 'userId = :uid and surveyId = :sid',
      FilterExpression:
        'not contains(#c,:cdel) and attribute_not_exists(deletedAt)',
    };
    const results = await db.queryList(params);

    const mergeHelper = new ItemsMergeHelper();

    const mergedResults = mergeHelper.groupAndMerge(results);

    return await calendars.addCalendarInfoForReservations(surveyConfig, mergedResults);

  }

  async isFirstTimeVaccination(surveyResults) {
    const findIdx = _.findIndex(surveyResults, function (o: any) {
      return o.value && o.value.trim() === '1回目';
    });
    return findIdx > -1;
  }

  async isVaccinationReport(surveyConfig) {
    return surveyConfig.surveyType === 'corona';
  }

  spliceChoiceGroupFromSurveySchema(surveySchema, findChoiceGrpHeadArr, surveyResults) {
    const choiceGroupHeaderName = 'choicegroupheader';
    const groupHeaderName = 'groupheader';
    // 分岐で選択しないグループヘッダを検索する
    const findChoiceGrpHeadExistInResult = _.find(surveyResults, function (r) {
      return r.itemKey === findChoiceGrpHeadArr.itemKey;
    });
    let findSectionOptionsNotSelectedArr = findChoiceGrpHeadArr.sectionOptions;
    if (findChoiceGrpHeadExistInResult) {
      findSectionOptionsNotSelectedArr = _.filter(findChoiceGrpHeadArr.sectionOptions, function (o) {
        return o.option.value.trim() !== findChoiceGrpHeadExistInResult.value;
      });
    }
    // 分岐で選択しないグループヘッダとそのグループヘッダのアイテムを削除する
    for (let j = 0; j < findSectionOptionsNotSelectedArr.length; j++) {
      const groupHeaderIndex = _.findIndex(surveySchema, function (o: any) {
        return (
          o.type.trim() === groupHeaderName && o.title.trim() === findSectionOptionsNotSelectedArr[j].groupheader.value
        );
      });
      if (groupHeaderIndex === -1) {
        return;
      }
      let nextGroupHeaderIndex = _.findIndex(
        surveySchema,
        function (o: any) {
          return o.type.trim() === groupHeaderName || o.type.trim() === choiceGroupHeaderName;
        },
        groupHeaderIndex + 1
      );
      if (nextGroupHeaderIndex === -1) {
        nextGroupHeaderIndex = surveySchema.length;
      }
      surveySchema.splice(groupHeaderIndex, nextGroupHeaderIndex - groupHeaderIndex);
    }
  }

  generateSchemaForConditions(surveyResults, surveyConfig) {
    const choiceGroupHeaderName = 'choicegroupheader';
    const groupHeaderName = 'groupheader';
    const newSurveyConfig = _.cloneDeep(surveyConfig);
    const newSurveySchema = newSurveyConfig.surveySchema;
    const findChoiceGrpHeadArr = _.filter(newSurveySchema, function (o) {
      return o.type.trim() === choiceGroupHeaderName;
    });
    // 分岐でない場合
    if (findChoiceGrpHeadArr.length > 0) {
      for (let i = 0; i < findChoiceGrpHeadArr.length; i++) {
        this.spliceChoiceGroupFromSurveySchema(newSurveySchema, findChoiceGrpHeadArr[i], surveyResults);
      }
    }
    // ワクチンだけではなく、全て帳票アイテムの、説明文は全て表示しない
    _.remove(newSurveyConfig.surveySchema, function (o: any) {
      return o.type.trim() === 'guide' || o.type.trim() === groupHeaderName
    });

    return newSurveyConfig;
  }

  async checkAndGenerateSchemaForConditions(surveyResults, surveyConfig) {
    const newConfig = this.generateSchemaForConditions(surveyResults, surveyConfig);
    const countVaccinesItem = newConfig.surveySchema.find(item => item.type === 'countVaccines');

    if (countVaccinesItem) { // ワクチンN回目テンプレートで1回目の接種の場合
      const isFirstVaccination = Number(countVaccinesItem.default) === 1;
      const previousVaccineItemTypes = ['previousVaccineMaker', 'previousVaccineDate'];
      if (isFirstVaccination) {
        _.remove(newConfig.surveySchema, (o: any) => previousVaccineItemTypes.includes(o.type.trim()));
      }
    } else {
      const isFirstVaccination = await this.isFirstTimeVaccination(surveyResults);
      const isVacRpt = await this.isVaccinationReport(surveyConfig);
      if (isFirstVaccination && isVacRpt) {
        _.remove(newConfig.surveySchema, function (o: any) {
          return o.type.trim() === 'sesshuJisshiDate';
        });
      }
    }

    return newConfig;
  }

  async getLinkedMemberOfSurveyResults(surveyId, userId) {
    const params = {
      TableName: this.getTableName(),
      IndexName: 'userId-surveyId-index',
      ExpressionAttributeValues: {
        ':sid': { S: surveyId },
        ':uid': { S: userId },
        ':cdel': { S: MESSAGES.TORIKESHI_TEXT },
        ':ccan': { S: MESSAGES.CANCEL_TEXT },
      },
      ExpressionAttributeNames: {
        '#c': 'check',
      },
      KeyConditionExpression: 'surveyId = :sid and userId = :uid',
      FilterExpression: 'not contains(#c,:cdel) and not contains(#c,:ccan) and attribute_not_exists(deletedAt)',
    };
    return await db.queryList(params);
  }

  async createTemporarySurveyResultsWithValidate(temporarySurveyResults: TemporarySurveyResults, surveyConfig: SurveyConfig) {
    const reservationQuestion = surveyConfig.surveySchema.find(item => item.type === 'reservation');
    const reservationAnswer = reservationQuestion ? temporarySurveyResults.values[reservationQuestion?.itemKey] : null

    if (reservationAnswer) {
      const reservationCheckResponse = await this.checkForAlreadyExistingReservation({
        ...reservationAnswer,
        userId: temporarySurveyResults.userId,
        surveyId: temporarySurveyResults.surveyResultsPk.split('#')[0]
      });
      if (reservationCheckResponse === 'reserved') {
        return {
          result: 'ERROR',
          code: 'quota_exceeded',
          errorMessage: '指定された日付・時間は既に予約済みです。お手数ですが、他の日付・時間を選んでください。',
        };
      }
      try {
        const categoryId = this.getCategoryIdFromReservationAnswer(reservationAnswer);
        const calendar = await buildCalendar(categoryId);
        const birthday = this.getBirthdayFromAnswerOrError(
          surveyConfig,
          Object.keys(temporarySurveyResults.values).map(itemKey => ({ ...temporarySurveyResults.values[itemKey], itemKey })),
          calendar
        );
        const [_, date, timeSlot] = reservationAnswer.value.split('|');
        await calendarsExt.validateReservable(calendar, date, timeSlot, temporarySurveyResults.reservationCost, birthday);
      } catch (e: any) {
        if (c.TRANSACTION_ERROR_VALUES.includes(e)) {
          return {
            result: 'ERROR',
            code: c.TRANSACTION_ERROR_MESSAGES[e].code,
            errorMessage: c.TRANSACTION_ERROR_MESSAGES[e].message,
          };
        } else {
          return {
            result: 'ERROR',
            code: 'server_error',
            errorMessage: e.mesage
          }
        }
      }
    }

    return await this.createTemporarySurveyResults(temporarySurveyResults);
  }

  async createTemporarySurveyResults(temporarySurveyResults: TemporarySurveyResults) {
    const marshalled = AWS.DynamoDB.Converter.marshall(temporarySurveyResults, {
      convertEmptyValues: true,
    });
    await db.createOne({
      TableName: this.getTableName(),
      Item: marshalled,
    });
    return { result: 'OK' };
  }

  private async queryBySurveyIdOrderIdIndex(surveyId: string, orderId: string) {
    const params = {
      TableName: this.getTableName(),
      IndexName: 'surveyId-orderId-index',
      KeyConditionExpression: 'surveyId = :sid and orderId = :oid',
      ExpressionAttributeValues: {
        [':sid']: { S: surveyId },
        [':oid']: { S: orderId },
      },
    };
    return await db.queryList(params);
  }

  async getActiveResultsBySurveyIdAndOrderId(surveyConfig: SurveyConfig, orderId: string, userId: string) {
    const deactiveStatus = '取り消し';
    const adminItemKeys = surveyConfig.surveySchema
      .filter(item => item.isAdminItem)
      .map(item => item.itemKey);
    const results = await this.queryBySurveyIdOrderIdIndex(surveyConfig.surveyId, orderId);
    return results.filter(r => r.check !== deactiveStatus && !adminItemKeys.includes(r.itemKey) && r.userId === userId);
  }
}

export const surveyResultsService = new SurveyResultsService();
