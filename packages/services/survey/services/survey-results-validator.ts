/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import * as _ from 'lodash';
import moment from 'moment';

class SurveyResultsValidator {
  private schema: any;
  private isLiffMode: any;
  private schemaByGroupheader: any[];
  private errorMessage: any;
  schemaByConditions: any[];
  private overrideRequiredCheck: boolean;
  private surveyResults: any;
  constructor(surveyConfigs, surveyResults, options:any = {}) {
    this.schema = surveyConfigs.surveySchema;
    this.isLiffMode = surveyResults[0].isLiffMode !== undefined ? surveyResults[0].isLiffMode : false;
    this.schemaByGroupheader = [];
    this.schemaByConditions = [];
    this.errorMessage = {};
    this.surveyResults = surveyResults;

    this.overrideRequiredCheck = false;
    if ('overrideRequiredCheck' in options) {
      this.overrideRequiredCheck = options.overrideRequiredCheck;
    }

    // put value into schema
    const inputs = {};
    for (let i = 0; i < surveyResults.length; i++) {
      const input = surveyResults[i];
      const itemKey = input.itemKey;

      if (!(itemKey in inputs)) {
        inputs[itemKey] = input.value;
      } else {
        // if multi answer
        if (!Array.isArray(inputs[itemKey])) {
          inputs[itemKey] = [inputs[itemKey]];
        }
        inputs[itemKey].push(input.value);
      }
    }

    for (let i = 0; i < this.schema.length; i++) {
      const key = this.schema[i].itemKey;
      if (key in inputs) {
        let input = inputs[key];
        if (Array.isArray(input)) {
          input = input.filter((val) => val);
        }
        this.schema[i].input = input;
      }
    }

    this.generateSchemaConditionsFormat();
  }

  isValid() {
    return Object.keys(this.errorMessage).length === 0;
  }

  hasErrorByKey(key) {
    return Object.keys(this.errorMessage).includes(key);
  }

  getErrorMessage() {
    return this.errorMessage;
  }

  putErrorMessage(key, message) {
    if (!(key in this.errorMessage)) {
      this.errorMessage[key] = [];
    }

    this.errorMessage[key].push(message);
  }

  getSchema() {
    return this.schema;
  }

  validate() {
    return this.validateByConditions();
  }

  validateByConditions() {
    this.schemaByConditions.forEach((item) => {
      // commom validation
      this.validateCommon(item);

      // if common validation error, go next item
      if (this.hasErrorByKey(item.itemKey)) {
        return;
      }

      {
        switch (item.type) {
          case 'groupheader':
            if (item.items && Array.isArray(item.items)) {
              item.items.forEach((subItem) => {
                // commom validation
                this.validateCommon(subItem);

                // if common validation error, go next item
                if (!this.hasErrorByKey(subItem.itemKey)) {
                  this.validateItem(subItem);
                }
              });
            }
            break;
          default:
            this.validateItem(item);
            break;
        }
      }
    });

    return this.isValid();
  }

  validateItem(item) {
    // each type validation
    switch (item.type) {
      case 'text':
        this.validateText(item);
        break;
      case 'checkboxes':
        this.validateCheckboxes(item);
        break;
      case 'dropdown':
        this.validateDropdown(item);
        break;
      case 'date':
        this.validateDate(item);
        break;
      case 'email':
        this.validateEmail(item);
        break;
      case 'phone':
        this.validatePhone(item);
        break;
      case 'number':
        this.validateNumber(item);
        break;
      case 'textarea':
        this.validateTextarea(item);
        break;
      case 'radio':
        this.validateRadio(item);
        break;
      case 'choicegroupheader':
        this.validateChoiceGroupheader(item);
        break;
      case 'reservation':
        this.validateReservation(item);
        break;
      case 'selectPayType':
        this.validatePayType(item);
        break;
      case 'files':
        this.validateFiles(item);
        break;
      default:
        break;
    }
  }

  generateSchemaConditionsFormat() {
    const schemaOriginal = [...this.schema];
    let groupheaderItems: any[] | null = null;
    const choiceGroupheaderItems = {};

    schemaOriginal.forEach((item) => {
      // schemaByGroupheader
      if (item.type === 'groupheader') {
        this.schemaByGroupheader.push(item);

        if (groupheaderItems === null || groupheaderItems.length > 0) {
          groupheaderItems = [];

          item.items = groupheaderItems;
        }
      } else {
        // choiceGroupheaderItems
        if (item.type === 'choicegroupheader') {
          choiceGroupheaderItems[item.itemKey] = item;
        }

        if (groupheaderItems !== null && Array.isArray(groupheaderItems)) {
          groupheaderItems.push(item);
        } else {
          this.schemaByGroupheader.push(item);
        }
      }
    });

    const choiceGroupheaderArray = Object.values(choiceGroupheaderItems) as any[];

    this.schemaByGroupheader.forEach((item) => {
      if (item.type === 'groupheader') {
        let allowedAddingToConditionsFlag = false;

        choiceGroupheaderArray.forEach((choiceGroupheaderItem) => {
          if (choiceGroupheaderItem['input']) {
            const selectedGroupheaderValue = choiceGroupheaderItem.input;
            choiceGroupheaderItem.sectionOptions.forEach((sectionOption) => {
              if (selectedGroupheaderValue === sectionOption.option.value) {
                if (sectionOption.groupheader.value === item.title) {
                  allowedAddingToConditionsFlag = true;
                  return;
                }
              }
            });
          } else {
            allowedAddingToConditionsFlag = true;
            return;
          }
        });

        if (allowedAddingToConditionsFlag) {
          this.schemaByConditions.push(item);
        }
      } else {
        this.schemaByConditions.push(item);
      }
    });

    return this.schemaByConditions;
  }

  isInputEmpty(item) {
if (!('input' in item) || item.input === '' || (Array.isArray(item.input) && item.input.length === 0)) {
      return true;
    }

    return false;
  }

  // common validatoin

  validateCommon(item) {
    // require
    if (!this.overrideRequiredCheck){
      this.validateRequired(item);
    }
  }

  // each type validation

  validateText(item) {
    // length
    this.validateLength(item.itemKey, item.input, item.length);
  }

  validateEmail(item) {
    // format
    this.validateEmailString(item.itemKey, item.input, item.format);
  }

  validatePhone(item) {
    // phone string
    this.validatePhoneString(item.itemKey, item.input, item.format);
  }

  validateCheckboxes(item) {
    // options
    this.validateOptions(item.itemKey, item.input, item.options);

    // 管理項目はLiffから選択個数はチェックしない
    if (item.isAdminItem !== undefined && item.isAdminItem && this.isLiffMode) {
      return;
    }
    // selection
    this.validateSelectionCount(item.itemKey, item.input, item.selection);
  }

  validateDropdown(item) {
    // options
    if (item.input) {
      this.validateOptions(item.itemKey, item.input, item.options);
    }
  }

  validateDate(item) {
    // date
    const format = item.includeYear ? 'YYYY-MM-DD' : 'MM-DD';
    this.validateDateString(item.itemKey, item.input, format);
  }

  validateNumber(item) {
    // number
    this.isNumber(item.itemKey, item.input);

    // range
    this.validateNumRange(item.itemKey, item.input, item.range);
  }

  validateTextarea(item) {
    // length
    this.validateLength(item.itemKey, item.input, item.length);
  }

  validateRadio(item) {
    // options
    this.validateOptions(item.itemKey, item.input, item.options);
  }

  validateChoiceGroupheader(item) {
    this.validateSectionOptions(item);
  }

  validateReservation(item) {
    const re = /^(category|calendar)#.*\|\d{8}\|\d+$/;
    if (!re.test(String(item.input))) {
      this.putErrorMessage(item.itemKey, '正しい形式で入力してください');
    }

    //TODO check date/time format and whether the date is future
  }

  validatePayType(item) {
    if (this.isInputEmpty(item)) {
      this.putErrorMessage(item.itemKey, '必須項目です');
    } else {
      const validInputs = item.payTypeOptions.map(o => o.input);
      if (!validInputs.includes(item.input)) {
        this.putErrorMessage(item.itemKey, `${item.title}は${validInputs.join('か')}を入力してください`);
      }
    }
  }

  // validation detail

  validateRequired(item) {
    // console.log("validateRequired, item")
    // console.log("REQ", item)

    if (item.isRequired.value || item.isRequiredForUser.value) {
      if (this.isInputEmpty(item)) {
        this.putErrorMessage(item.itemKey, item.isRequired.errorMessage || '必須項目です');
      }
    }
  }

  validateLength(key, input, length) {
    if (!input || !length) {
      return;
    }

    const min = parseInt(length.min);
    const max = parseInt(length.max);
    let localErrorMessage = '';
    if (min && input.length < min) {
      localErrorMessage += `${min}文字以上`;
    }
    if (max && input.length > max) {
      localErrorMessage += `${max}文字以内`;
    }
    if (localErrorMessage !== '') {
      localErrorMessage += 'で入力してください';
      this.putErrorMessage(key, length.errorMessage || localErrorMessage);
    }
  }

  validateEmailString(key, input, format) {
    if (!input) {
      return;
    }
    const re =
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if (!re.test(String(input).toLowerCase())) {
      this.putErrorMessage(key, format.errorMessage || '正しい形式で入力してください');
    }
  }

  validatePhoneString(key, input, format) {
    if (!input) {
      return;
    }

    // const re = /^(0[5-9]0[0-9]{8}|0[1-9][1-9][0-9]{7})$/;
    const re = /^([0-9]{1,15})$/;
    if (!re.test(String(input))) {
      this.putErrorMessage(key, format.errorMessage || '正しい形式で入力してください');
    }
  }

  isNumber(key, input, errorMessage?) {
    if (!input) {
      return;
    }

    if (Number.isNaN(Number(input))) {
      this.putErrorMessage(key, errorMessage || '正しい形式で入力してください');
    }
  }

  validateNumRange(key, input, range) {
    if (!input || !range) {
      return;
    }

    const inputInt = parseInt(input);

    const min = parseInt(range.min);
    const max = parseInt(range.max);
    if (min && !max) {
      if (inputInt < min) {
        this.putErrorMessage(key, range.errorMessage || `${min}以上で入力してください`);
      }
    } else if (!min && max) {
      if (max < inputInt) {
        this.putErrorMessage(key, range.errorMessage || `${max}以下で入力してください`);
      }
    } else {
      if (inputInt < min || max < inputInt) {
        this.putErrorMessage(key, range.errorMessage || `${min}〜${max}の範囲で入力してください`);
      }
    }
  }

  validateSelectionCount(key, input, selection) {
    if (!input || !selection) {
      return;
    }

    const min = parseInt(selection.min);
    const max = parseInt(selection.max);
    // NOTE: 文字列の場合はチェックボックスが1つだけ選択されている状態なので1を代入する
    const selectionCount = _.isString(input) ? 1 : input.length;

    if (min && !max) {
      if (selectionCount < min) {
        this.putErrorMessage(key, selection.errorMessage || `${min}個以上選択してください`);
      }
    } else if (!min && max) {
      if (max < selectionCount) {
        this.putErrorMessage(key, selection.errorMessage || `${max}個以下で選択してください`);
      }
    } else if (min && max) {
      if (selectionCount < min || max < selectionCount) {
        this.putErrorMessage(key, selection.errorMessage || `${min}〜${max}個選択してください`);
      }
    }
  }

  validateOptions(key, input, options, errorMessage?) {
    if (!input) {
      return;
    }

    if (_.isString(input)) {
      input = [input];
    }
    for (let i = 0; i < input.length; i++) {
      if (!options.includes(input[i])) {
        this.putErrorMessage(key, errorMessage || '不正なデータです');
        return;
      }
    }
  }

  validateSectionOptions(choiceGroupheaderItem, errorMessage = null) {
    if (!choiceGroupheaderItem.input) {
      return;
    }

    let isValidated = false;
    choiceGroupheaderItem.sectionOptions.forEach((sectionOption) => {
      if (choiceGroupheaderItem.input === sectionOption.option.value) {
        isValidated = true;
        return;
      }
    });

    if (!isValidated) {
      this.putErrorMessage(
        choiceGroupheaderItem.key,
        errorMessage ||
          '不正なデータ：「' + choiceGroupheaderItem.title + '」の入力データが選択一覧に存在していない状態です。'
      );
    }
  }

  validateDateString(key, input, format, errorMessage?) {
    if (!input) {
      return;
    }

    if (!moment(input, format).isValid()) {
      this.putErrorMessage(key, errorMessage || '正しい形式で入力してください');
    }
  }

  validateFiles(item) {
    if(item.isRequired.value || item.isRequiredForUser.value) {
      const filesInput = this.surveyResults.find((i) => i.itemKey === item.itemKey);
      if (filesInput && filesInput.files && filesInput.files.length === 0) {
        this.putErrorMessage(item.itemKey, '必須項目です');
      }
    }
  }
}

export {
  SurveyResultsValidator,
};
