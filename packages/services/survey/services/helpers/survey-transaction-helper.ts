/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import * as AWS from 'aws-sdk';
import moment from 'moment';
import { getEnvironmentCredentials } from '../../utils/aws-helper.js';
import c from '../../../api-reception/config/constants.js';
import config from '../../../api-reception/config/static.js';
import * as calendars from '../calendars-service.js';
import * as calendarsExt from '../calendars-ext.js';
import { sleep } from '../../utils/util.js';
import { CalendarModel } from '../../models/calendar.js';
import { BirthdayModel } from '../../models/date.js';
import SurveyResultsRollbackHelper from './survey-results-rollback.js';
import { SurveyConfig } from '../../types/survey-config.js';
import { collectQuotaTransactionParams } from './corona-shinken.js';
import { PaymentResult } from 'payment/api/service/user/payment-results-service.js';

class SurveyTransactionHelper {
    protected surveyResultsTable: any;
    protected transactionArgs: any;
    private lineUsersTable: any;
    private surveyCalendarsTable: any;
    protected paymentResultsTable: any;
    private MAX_TIME_FOR_TRANSACTION_SUCCESS: number;

    constructor() {
        (this.lineUsersTable = config.get(c.TABLE_LINE_USERS)),
            (this.surveyCalendarsTable = config.get(c.TABLE_SURVEY_CALENDARS));
        (this.surveyResultsTable = config.get(c.TABLE_SURVEY_RESULTS)),
            (this.transactionArgs = {
                TransactItems: [],
            });
        this.paymentResultsTable = process.env.TABLE_PAYMENT_RESULTS;
        this.MAX_TIME_FOR_TRANSACTION_SUCCESS = 25;
    }

    getDocumentClient() {
        return new AWS.DynamoDB.DocumentClient(getEnvironmentCredentials());
    }

    addSurveyResultsItem(reservationItem) {
        this.transactionArgs.TransactItems.push({
            Put: {
                TableName: this.surveyResultsTable,
                Item: reservationItem,
            },
        });
    }

    async cancelSurveyCalendarReservation(reservationItem, paymentCost?: number, rollbackHelper?: SurveyResultsRollbackHelper) {
        const [categoryId, date, timeSlot] = reservationItem.value.split('|');
        const calendarId = await calendars.getCalendarId(categoryId, true);
        const itemInfo = await calendarsExt.getReservationItemInfo(categoryId);
        const cost = paymentCost ? paymentCost : itemInfo.length > 0 ? Number(itemInfo[0].cost) : 1;
        // 減らす予約数があるかどうか
        const reservationCheck = await calendarsExt.checkReservationPossible(calendarId, date, timeSlot, cost * -1);
        if (!reservationCheck) {
            throw 'cancellation_exceeded';
        }

        this.pushReservationTransaction(calendarId, date, timeSlot, cost * -1);
        rollbackHelper && (rollbackHelper.oldReservationInfo = { calendarId, date, timeSlot, cost });
    }

    async addSurveyCalendarReservation(reservationItem, calendar: CalendarModel, birthday?: BirthdayModel, costOfPayment?: number) {
        let cost = 1;
        if (costOfPayment) {
            cost = costOfPayment;
        } else {
            const [categoryId] = reservationItem.value.split('|');
            const itemInfo = await calendarsExt.getReservationItemInfo(categoryId);
            itemInfo.length > 0 && (cost = Number(itemInfo[0].cost));
        }

        return await this.execAddSurveyCalendarReservation(reservationItem, calendar, cost, birthday);
    }

    pushReservationTransaction(calendarId, date, timeSlot, cost) {
        this.transactionArgs.TransactItems.push({
            Update: {
                TableName: this.surveyCalendarsTable,
                Key: { partitionKey: calendarId, sortKey: `schedule#${date}` },
                UpdateExpression: 'ADD reservationCounts.#timeSlot :inc',
                ConditionExpression:
                    cost > 0
                        ? '(attribute_exists(reservationCounts.#timeSlot) and reservationCounts.#timeSlot < quotas.#timeSlot)' +
                        ' or (attribute_not_exists(reservationCounts.#timeSlot) and quotas.#timeSlot > :zero)'
                        : '(attribute_exists(reservationCounts.#timeSlot) and reservationCounts.#timeSlot > :zero)',
                ExpressionAttributeNames: {
                    ['#timeSlot']: timeSlot,
                },
                ExpressionAttributeValues: {
                    [':inc']: cost,
                    [':zero']: 0,
                },
            },
        });
    }

    addCoronaExaminationLineReservation(surveyConfig: SurveyConfig, surveyResults: any) {
        const transactionParams = collectQuotaTransactionParams(surveyConfig, surveyResults);
        console.log('coronaExaminationTransactionParams', transactionParams);
        if (!transactionParams) return;
        this.transactionArgs.TransactItems.push({
            Update: {
                TableName: this.surveyCalendarsTable,
                Key: { partitionKey: transactionParams.partitionKey, sortKey: transactionParams.sortKey },
                UpdateExpression: transactionParams.updateExpression,
                ConditionExpression: transactionParams.conditionExpression,
                ExpressionAttributeNames: transactionParams.namesMapping,
                ExpressionAttributeValues: transactionParams.valuesMapping,
            }
        })
    }

    addTransactionLogRecord(partitionKey, sortKey, status) {
        const expressionAttributeNames = {
            '#status': 'status',
            '#transaction': 'transactionKey',
        };

        const expressionAttributeValues = {
            ':status': status,
            ':transaction': sortKey,
        };

        let updateExpression = 'SET #transaction = :transaction, #status = :status';

        if (status === 'started') {
            const dateTime = moment();
            updateExpression += ', #created = :created, #expiry = :expire';
            expressionAttributeNames['#created'] = 'createdAt';
            expressionAttributeNames['#expiry'] = 'expireAt';
            expressionAttributeValues[':created'] = dateTime.unix();
            expressionAttributeValues[':expire'] = dateTime.add(1, 'week').unix();
        }

        this.transactionArgs.TransactItems.push({
            Update: {
                TableName: this.lineUsersTable,
                Key: { partitionKey: partitionKey, sortKey: sortKey },
                UpdateExpression: updateExpression,
                ExpressionAttributeNames: expressionAttributeNames,
                ExpressionAttributeValues: expressionAttributeValues,
            },
        });
    }

    debugTransactionRecord() {
        console.log('Transaction contents');
        this.transactionArgs.TransactItems.forEach((b) => {
            console.log(b);
        });
    }

    async executeTransaction(requestStartTime = null) {
        do {
            try {
                await this.getDocumentClient().transactWrite(this.transactionArgs).promise();
                return;
            } catch (err: any) {
                //A TransactionConflict occured, likely another Transaction updating surveyCalendars
                if (err.name === 'TransactionCanceledException' && err.stack.includes('TransactionConflict')) {
                    console.log('[INFO] Received TransactionConflict exception, OK to retry.');
                    await sleep(500);
                } else {
                    throw err;
                }
            }
        } while (
            requestStartTime === null ||
            moment.duration(moment().diff(requestStartTime)).seconds() < this.MAX_TIME_FOR_TRANSACTION_SUCCESS
        );

        console.log('[INFO] Execute transaction was not run successfully within time limit.');
        throw 'out_of_time';
    }

    async addPaymentResultsItem(paymentResults: PaymentResult) {
        this.transactionArgs.TransactItems.push({
            Put: {
                TableName: this.paymentResultsTable,
                Item: paymentResults,
            }
        });
    }

    protected async execAddSurveyCalendarReservation(reservationItem, calendar: CalendarModel, cost: number, birthday?: BirthdayModel) {
        const [_, date, timeSlot] = reservationItem.value.split('|');
        await calendarsExt.validateReservable(calendar, date, timeSlot, cost, birthday);
        const calendarInfo = calendar.getCalendarInfo();
        if (!calendarInfo) throw new Error('[execAddSurveyCalendarReservation] No calendar info found: ' + `${calendar.categoryId}`)
        const { sortKey: calendarId } = calendarInfo;
        this.pushReservationTransaction(calendarId, date, timeSlot, cost);
    }
}

export {
    SurveyTransactionHelper,
};
