/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { SESClient, SendEmailCommand, SendEmailCommandInput } from '@aws-sdk/client-ses';
import { isEmpty } from 'lodash';
import { SurveyConfig } from "./../../types/survey-config.js";
import { infoLog } from './../../utils/logger.js';
import { Settings } from "luxon";
import { loadMessage } from '../helpers/notification-templates/index.js';

Settings.defaultZoneName = 'Asia/Tokyo';

const SOURCE_ADDRESS_PARAM_NAME = 'AWS_SES_EMAIL_DOMAIN';
const SES_REGION_PARAM_NAME = 'SES_REGION'

const EMAIL_TITLE = '通知先メールアドレス'

export const handlers = {
    sendSes: async (params: SendEmailCommandInput) => {
        const ses = new SESClient({
            region: process.env[SES_REGION_PARAM_NAME],
        });
        const command = new SendEmailCommand(params);
        try {
            const response = await ses.send(command);  // This is where you pass the command object
            infoLog("Email sent successfully:", response);
        } catch (error) {
            infoLog("Error sending email:", error);
        }
    },
};

function lookForEmailInConfig(configs: SurveyConfig): string | undefined {
    const emailField = configs.surveySchema.find(v => v.title === EMAIL_TITLE);
    if (!emailField) {
        infoLog(`Survey configには「${EMAIL_TITLE}」っていうemailを登録している項目はありません。`);
        return undefined;
    }

    const email = emailField.default + '' || emailField.input + '';
    if (!email || isEmpty(email.trim())) {
        infoLog(`Survey configには送信先のemailは指定されていません。`);
        return undefined;
    }

    return email;
}

async function sendNotification(configs: SurveyConfig) {
    const sourceAddressDomain = process.env[SOURCE_ADDRESS_PARAM_NAME];
    if (!sourceAddressDomain) {
        infoLog(`送信元のemailドメインは指定されていません。`);
        return;
    }

    const sourceAddress = `no-reply@${sourceAddressDomain}`;

    const { subject, body, to } = await loadMessage(process.env.OSS_ENV_NAME, {
        config: configs,
    });

    if (isEmpty(to)) {
        // if no email was provided, try the old method an look for it in the config
        const email = lookForEmailInConfig(configs);
        if (email) {
            to.push(email);
        } else {
            infoLog(`通知先のemailは指定されていません。`);
            return;
        }
    }
    if (isEmpty(subject)) {
        infoLog(`通知メールの件名は指定されていません。`);
        return;
    }
    if (isEmpty(body)) {
        infoLog(`通知メールの本文は指定されていません。`);
        return;
    }

    infoLog(`「${subject}」の通知メールを${sourceAddress}から${to}に送信します。\n\n`, body);

    const req: SendEmailCommand = {
        Source: sourceAddress,
        Message: {
            Subject: { Data: subject },
            Body: { Text: { Data: body } },
        },
        Destination: {
            ToAddresses: to
        }
    };
    await handlers.sendSes(req);

    infoLog(`通知メールを${to}に送信しました。`);
}

export async function notifyNewApplication(configs: SurveyConfig) {
    try {
        await sendNotification(configs);
    } catch (e) {
        infoLog(`通知メール送信は失敗しました。`, e);
    }
}
