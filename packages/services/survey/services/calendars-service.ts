/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import * as _ from 'lodash';
import { DateTime, Duration } from 'luxon';
import * as exts from './calendars-ext';
import * as utils from './calendars-util';
import { CategoriesOrder } from '../models/categories-order';

const buildScheduleMap = (scheduleDays, startDay, endDay) => {
  const scheduleMap = {};
  scheduleDays.forEach((day) => {
    const quotasSlots = Object.keys(day.quotas);
    const reservationSlots = Object.keys(day.reservationCounts);
    const slots = _.union(quotasSlots, reservationSlots);
    const dayMap = {};
    slots.forEach((slot) => {
      const reservationsCount = day.reservationCounts[slot] || 0;
      const quotas = day.quotas[slot] || 0;
      dayMap[slot] = [quotas, reservationsCount];
    });
    scheduleMap[day.date] = dayMap;
  });

  let currentDay = DateTime.fromISO(startDay);
  const lastDay = DateTime.fromISO(endDay);
  while (currentDay <= lastDay) {
    const day = currentDay.toFormat('yyyyLLdd');
    if (!scheduleMap[day]) {
      scheduleMap[day] = {};
    }
    currentDay = currentDay.plus(Duration.fromObject({ days: 1 }));
  }
  return scheduleMap;
};

const buildDayOffMap = (scheduleDays) => {
  const dayOffMap = {};
  scheduleDays.forEach((day) => {
    dayOffMap[day.date] = day.dayOff;
  });
  return dayOffMap;
};

const loadSchedule = async (calendarId, startDay, endDay) => {
  const scheduleDays = await exts.getSchedulesOfCalendar(calendarId, startDay, endDay);
  const scheduleMap = buildScheduleMap(scheduleDays, startDay, endDay);
  const dayOffMap = buildDayOffMap(scheduleDays);

  return { scheduleMap: scheduleMap, dayOffMap: dayOffMap };
};

const loadScheduleOfCategory = async (categoryId, startDay, endDay, options) => {
  const opts = {
    includeCalendarInfo: false,
    ...options,
  };

  let calendarId = categoryId;
  if (calendarId.startsWith('category')) {
    const setCategoryId = categoryId.split('_')[0];
    const category = await exts.getSingleCategory(setCategoryId);
    if (!category) {
      throw 'Category ' + setCategoryId + ' not found';
    }

    if (!category.calendarId) {
      return null;
    }

    calendarId = category.calendarId;
  }
  console.log(categoryId, calendarId, startDay, endDay, options);
  const calendarInfo = await exts.getCalendarInfo(calendarId);
  const schedule = await loadSchedule(calendarId, startDay, endDay);
  const result = {
    schedule: schedule.scheduleMap,
    dayOff: schedule.dayOffMap,
    comaList: calendarInfo.comaList ? calendarInfo.comaList : {},
  };
  if (opts.includeCalendarInfo) {
    result['id'] = calendarInfo.sortKey;
    result['name'] = calendarInfo.name;
  }

  return result;
};
const loadCategories = async (tagFilters) => {
  const filter: any = {};
  filter.tag1 = tagFilters.tag1;
  if (tagFilters.tag2 !== undefined) {
    filter.tag2 = tagFilters.tag2;
  }
  if (tagFilters.tag3 !== undefined) {
    filter.tag3 = tagFilters.tag3;
  }

  const categories = await exts.filterCategories(filter.tag1, filter.tag2, filter.tag3);

  return categories.map((category) => {
    return {
      id: category.sortKey,
      tag1: category.tag1,
      tag2: category.tag2,
      tag3: category.tag3,
      calendarId: category.calendarId,
    };
  });
};

const loadCategoriesTree = async () => {
  const categories = await exts.filterCategories();
  const roots: any[] = [];

  const tags1 = _.uniqBy(
    categories.map((c) => ({ name: c.tag1, order: c.largeCategoryOrder })),
    (c) => c.name
  ).sort(CategoriesOrder.compareCategoriesTreeForSort);
  tags1.forEach(({ name: tag1Name, order: tag1Order }) => {
    const node1: any = {
      name: tag1Name,
      order: tag1Order,
      children: [],
    };
    const tags2 = _.uniqBy(
      categories
        .filter((c) => c.tag1 === tag1Name)
        .map((c) => ({ name: c.tag2, order: c.mediumCategoryOrder }))
        .sort(CategoriesOrder.compareCategoriesTreeForSort),
      (c) => c.name
    );
    tags2.forEach(({ name: tag2Name, order: tag2Order }) => {
      const node2: any = {
        name: tag2Name,
        order: tag2Order,
        children: [],
      };
      const node2childCats = categories.filter((c) => c.tag1 === tag1Name && c.tag2 === tag2Name);
      node2childCats.forEach((c) => {
        if (!c.tag2) {
          node1.id = c.sortKey;
          node1.calendarId = c.calendarId;
        } else if (!c.tag3) {
          node2.id = c.sortKey;
          node2.calendarId = c.calendarId;
        } else {
          node2.children.push({
            name: c.tag3,
            id: c.sortKey,
            calendarId: c.calendarId,
            order: c.smallCategoryOrder,
          });
        }
      });
      node2.children.sort(CategoriesOrder.compareCategoriesTreeForSort);
      node1.children.push(node2);
    });

    roots.push(node1);
  });

  return roots;
};

const loadGlobalDisplaySettings = async () => {
  const settings = await exts.getGlobalDisplaySettings();
  return _.omit(settings, ['partitionKey', 'sortKey', 'createdAt', 'updatedAt']);
};

const loadTagLabels = async () => {
  return _.pick(await loadGlobalDisplaySettings(), ['tag1', 'tag2', 'tag3']);
};

const getCalendarId = async (calendarOrCategoryId, required = false) => {
  const categoryId = calendarOrCategoryId.split('_')[0];
  if (categoryId.startsWith('category')) {
    const category = await exts.getSingleCategory(categoryId);
    required && utils.assertCategoryExists(category, categoryId);
    required && utils.assertExists(category.calendarId, `No linked calendar for category [${categoryId}]`);

    if (!category.calendarId) {
      return null;
    }
    return category.calendarId;
  }
  return calendarOrCategoryId;
};

const checkReservationPossibleWithoutRegistering = async (categoryId, date, timeSlot) => {
  const calendarId = await getCalendarId(categoryId, true);
  const itemInfo = await exts.getReservationItemInfo(categoryId);
  let cost = 1;
  if (itemInfo.length > 0) {
    cost = Number(itemInfo[0].cost);
  }
  // 必要な予約枠数があるかどうか
  const reservationCheck = await exts.checkReservationPossible(calendarId, date, timeSlot, cost);
  if (!reservationCheck) {
    return 'NoQuotas';
  }
};

const registerReservation = async (categoryId, date, timeSlot) => {
  const calendarId = await getCalendarId(categoryId, true);
  const itemInfo = await exts.getReservationItemInfo(categoryId);
  let cost = 1;
  if (itemInfo.length > 0) {
    cost = Number(itemInfo[0].cost);
  }
  // 必要な予約枠数があるかどうか
  const reservationCheck = await exts.checkReservationPossible(calendarId, date, timeSlot, cost);
  if (!reservationCheck) {
    return 'NoQuotas';
  }
  return await exts.tryToPlaceReservation(calendarId, date, timeSlot, cost);
};

const removeReservation = async (categoryOrCalendarId, date, timeSlot) => {
  const calendarId = await getCalendarId(categoryOrCalendarId, true);
  const itemInfo = await exts.getReservationItemInfo(categoryOrCalendarId);
  let cost = 1;
  if (itemInfo.length > 0) {
    cost = Number(itemInfo[0].cost);
  }
  // 減らす予約数があるかどうか
  const reservationCheck = await exts.checkReservationPossible(calendarId, date, timeSlot, cost * -1);
  if (!reservationCheck) {
    return false;
  }
  return await exts.tryToPlaceReservation(calendarId, date, timeSlot, cost * -1);
};

const getCalendars = async () => {
  return await exts.getAllCalendars();
};

const loadCalendarInfo = async (categoryId) => {
  const calendarId = await getCalendarId(categoryId, true);

  return await exts.getCalendarInfo(calendarId);
};

const loadReservationItemInfo = async (categoryId) => {
  const data = await exts.getReservationItemInfo(categoryId);
  return data;
};

const addCalendarInfoForReservations = async (surveyConfig, results) => {
  const reservationQuestion = surveyConfig.surveySchema.find((obj) => obj.type === 'reservation');
  if (!reservationQuestion) {
    return results;
  }

  for (const result of results) {
    const reservationItem = result.fields.find((obj) => obj.itemKey === reservationQuestion.itemKey);
    if (reservationItem) {
      const categoryValue = reservationItem.value.split('|')[0].split('_')[0];
      result.categoryInfo = await exts.getSingleCategory(categoryValue);
      result.calendarInfo = await loadCalendarInfo(categoryValue);
    }
  }

  return results;
};

const canBeReserveCalendar = async (calendarId) => {
  let canBeReserve = false;
  const data = await exts.getCalendarReservationInfo(calendarId);
  if (!data || data.length === 0) {
    // 指定されたカレンダーは予約可能日がありません
    return canBeReserve;
  }
  for (const schedule of data) {
    const quotas = schedule.quotas;
    const reservationCounts = schedule.reservationCounts;
    let sub_check = false;
    for (const slot of Object.keys(quotas)) {
      if (
        quotas[slot] > 0 &&
        (!reservationCounts[slot] ||
          (reservationCounts[slot] && Number(reservationCounts[slot]) < Number(quotas[slot])))
      ) {
        // 予約可能枠がある
        sub_check = true;
        break;
      } else {
        continue;
      }
    }
    if (sub_check) {
      canBeReserve = true;
      break;
    } else {
      continue;
    }
  }
  return canBeReserve;
};

export {
  loadScheduleOfCategory,
  loadCategories,
  loadTagLabels,
  loadCategoriesTree,
  loadGlobalDisplaySettings,
  loadCalendarInfo,
  registerReservation,
  removeReservation,
  checkReservationPossibleWithoutRegistering,
  getCalendars,
  getCalendarId,
  loadReservationItemInfo,
  addCalendarInfoForReservations,
  canBeReserveCalendar,
};
