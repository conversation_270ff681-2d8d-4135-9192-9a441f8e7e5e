import {Request, Response} from "lambda-api";
import {InstantConfig, MessagesValidator} from "./helpers/request-validators.js";
import {DeliveryTarget, Message, SurveyAnswersFilter} from "@oss/shared/lib/distribution/api-types.js";
import {createConfig, saveSelectedTargets, updateMessages} from "./repo/repo-configs.js";
import {DistributionConfigParams} from "./repo/repo-configs-defs.js";
import {NOTIFICATONS, notify, RunDistributionCommandEvent} from "../module.js";
import {DistributionType, TargetsSelectionType} from "@oss/shared/lib/distribution/constants.js";
import messages from "../../api-reception/config/messages.js";

export async function runInstant(req: Request, res: Response) {
    const {
        settings,
        messages
    } = req.body
    const config = InstantConfig.parse(settings)
    const validatedMessages = MessagesValidator.min(1).parse(messages) as Message[]

    const {id} = await createConfig(config as DistributionConfigParams)
    await updateMessages(id, validatedMessages)

    const notification: RunDistributionCommandEvent = {
        RunConfiguration: {
            configurationId: id
        }
    }
    await notify(NOTIFICATONS.types.RUN_CONFIGURATION, notification)
    res.json({
        id,
        ...config,
        messages,
    })
}

type RunBatchSelectedRequest = {
    messages: any[],
    title: string,
    users: {id: string, partitionKey: string}[]
}

export async function runBatchSelected(req: Request, res: Response) {
    const body: RunBatchSelectedRequest = req.body;
    const targets: DeliveryTarget[] = body.users.map(v => ({answerId: v.partitionKey, lineUid: v.id}));
    const messageContents: any[] = body.messages;
    const title: string = body.title;

    const config: DistributionConfigParams = {
        targetSelectionType: TargetsSelectionType.SELECTED,
        distributionName: title,
        distributionType: DistributionType.INSTANT,
        messageIds: messageContents.map((c,i) => `message${i}`),
        enabled: true
    }

        const messages: Message[] = messageContents.map((c, i) => {
            return {
                id: config.messageIds[i],
                contents: c,
            }
        })

    const {id} = await createConfig(config);
    await updateMessages(id, messages);
    await saveSelectedTargets(id, targets);

    const notification: RunDistributionCommandEvent = {
        RunConfiguration: {
            configurationId: id
        }
    }
    await notify(NOTIFICATONS.types.RUN_CONFIGURATION, notification)

    res.json({
        id,
        ...config,
        messages,
    })
}

type RunWithFilterRequest = {
    searchCondition: SurveyAnswersFilter,
    surveyConfig: {
        surveyId: string,
        surveyTitle: string,
    },
    title: string,
    messages: any[],
}

export async function runInstantWithFilter(req: Request, res: Response) {
    const body: RunWithFilterRequest = req.body
    const config: DistributionConfigParams = {
        targetSelectionType: TargetsSelectionType.SURVEY_CONDITIONS,
        distributionName: body.title,
        distributionType: DistributionType.INSTANT,
        messageIds: body.messages.map((c,i) => `message${i}`),
        enabled: true,
        surveyConditions: {
            surveyId: body.surveyConfig.surveyId,
            filter: body.searchCondition,
            conditions: []
        }
    }

    const messages: Message[] = body.messages.map((c, i) => {
        return {
            id: config.messageIds[i],
            contents: c,
        }
    })

    const { id } = await createConfig(config)
    await updateMessages(id, messages);

    const notification: RunDistributionCommandEvent = {
        RunConfiguration: {
            configurationId: id
        }
    }
    await notify(NOTIFICATONS.types.RUN_CONFIGURATION, notification)

    res.json({
        id,
        ...config,
        messages,
    })
}
