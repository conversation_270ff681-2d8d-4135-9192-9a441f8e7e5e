import {Request, Response} from "lambda-api";
import {SendLineMessage} from "./helpers/request-validators.js";
import {LineMessage, LineRequest} from "../line/sender.js";
import {lambdaEnv} from "../../common/lambda-env.js";
import {ENV} from "../module.js";

export async function sendLineMessage(req: Request, res: Response) {
    const sendRequest = SendLineMessage.parse(req.body)

    const sender = new LineRequest({channelAccessToken: lambdaEnv.getEnv(ENV.LINE_MESSAGING_ACCESS_TOKEN)})
    const result = await sender.push(sendRequest.lineUid, sendRequest.messages as LineMessage[])
    // TODO send notification
    if (result.isOk) {
        res.json({
            requestId: result.requestId,
        })
        return
    }

    if (result.error?.statusCode === 400) {
        res.status(400).json({
            requestId: result.requestId,
            responseCode: result.error.statusCode,
            errorMessage: result.error.message,
            errorDetails: result.error.details
        })
        return
    }

    res.status(500).json({
        requestId: result.requestId,
        responseCode: result.error?.statusCode,
        errorMessage: result.error?.message,
        errorDetails: result.error?.details
    })
}


