import { Request, Response } from "lambda-api";
import { z } from "zod";
import { DistributionType } from "@oss/shared/lib/distribution/constants.js";
import { listConfigs, listDrafts as loadDrafts } from "./repo/repo-configs.js";
import _ from "lodash";
import { findFutureTime } from "./helpers/recurrent-calc.js";
import { DateTime } from "luxon";

const repeatingFilterValidator = z.object({
    nameContains: z.string().trim().optional(),
    surveyId: z.string().optional(),
    broadcast: z.coerce.boolean().default(false),
    limit: z.coerce.number().default(1000),
    continueToken: z.string().optional(),
    toDate: z.string().regex(/\d\d\d\d-\d\d-\d\d/).optional(),
})
const postponedFilterValidator = z.object({
    surveyId: z.string().optional(),
    broadcast: z.coerce.boolean().default(false),
    limit: z.coerce.number().default(1000),
    continueToken: z.string().optional(),
    fromDate: z.string().regex(/\d\d\d\d-\d\d-\d\d/).optional(),
    toDate: z.string().regex(/\d\d\d\d-\d\d-\d\d/).optional(),
})
const draftFilterValidator = z.object({
    limit: z.coerce.number().default(1000),
    continueToken: z.string().optional(),
    fromDate: z.string().regex(/\d\d\d\d-\d\d-\d\d/).optional(),
    toDate: z.string().regex(/\d\d\d\d-\d\d-\d\d/).optional(),
})
const externalFilterValidator = z.object({
    surveyId: z.string().optional(),
    broadcast: z.string().optional(),
    limit: z.coerce.number().default(1000),
    continueToken: z.string().optional(),
    enabled: z.coerce.boolean().optional()
})


export async function listRepeating(req: Request, res: Response) {
    const query = repeatingFilterValidator.parse(req.query)
    let list = await listConfigs(DistributionType.REPEATING, {
        surveyId: query.surveyId,
        broadcast: query.broadcast,
    })
    if (query.nameContains) {
        list = list.filter(l => l.distributionName.includes(query.nameContains))
    }
    const resultItems = list
        .map(config => {
            const futureTime = findFutureTime(config.repeatingSettings)
            return { config, futureTime }
        }).filter(({ futureTime }) => {
            if (!futureTime) return false
            if (query.toDate && (futureTime.date > query.toDate)) return false
            return true
        }).map(({ config, futureTime }) => {
            const { date, time } = futureTime
            config['nextDeliveryDateTime'] = date + ' ' + time
            return config
        })

    res.json({
        items: resultItems,
        continueToken: undefined
    })
}

export async function listPostponed(req: Request, res: Response) {
    const query = postponedFilterValidator.parse(req.query)
    const dateTimeNow = DateTime.now().setZone('Asia/TOkyo')
    const today = dateTimeNow.toISODate()
    let list = await listConfigs(DistributionType.POSTPONED, {
        surveyId: query.surveyId,
        broadcast: query.broadcast,
    })
    list = list.filter(config => {
        const settingDateTime = `${config.postponedSettings.date}T${config.postponedSettings.time}:00.000+09:00`
        if (config.postponedSettings.date < today) return false
        if (query.toDate && query.toDate < config.postponedSettings.date) return false
        if (query.fromDate && query.fromDate === today) return new Date(dateTimeNow.toISO()) < new Date(settingDateTime)
        return true
    })
    list.forEach(config => {
        const nextDeliveryDateTime = config.postponedSettings.date + ' ' + config.postponedSettings.time
        config['nextDeliveryDateTime'] = nextDeliveryDateTime
    })
    res.json({
        items: list,
        continueToken: undefined
    })
}

export async function listExternal(req: Request, res: Response) {
    const query = externalFilterValidator.parse(req.query)
    let list = await listConfigs(DistributionType.EXTERNAL, {
        surveyId: query.surveyId,
        broadcast: query.broadcast === 'true',
    })
    if (_.isBoolean(query.enabled)) {
        list = list.filter(l => l.enabled === query.enabled)
    }
    res.json({
        items: list,
        continueToken: undefined
    })
}

export async function listTalks(req: Request, res: Response) {
    const query: Record<string, any> = req.query
    let list = await listConfigs(DistributionType.TALK, {})
    res.json({
        items: list,
        continueToken: undefined
    })
}

export async function listDrafts(req: Request, res: Response) {
    const query = draftFilterValidator.parse(req.query)
    let list = await loadDrafts()
    list = list.filter(config => {
        if (query.fromDate && config.updatedAt < query.fromDate) return false
        if (query.toDate && query.toDate < config.updatedAt) return false
        return true
    })
    if (list === null) {
        return
    }
    res.json({
        items: list,
        continueToken: undefined
    })
}





