import {DateTime, Duration} from "luxon";
import _ from "lodash";
import {DeliveryHistoryDetails, DeliveryHistoryShort} from "@oss/shared/lib/distribution/api-types.js";
import {DynamoDBDocumentClient} from "@aws-sdk/lib-dynamodb";
import {DynamoDBClient} from "@aws-sdk/client-dynamodb";
import {ClientConfiguration, createAwsClient, lambdaEnv} from "../../../common/lambda-env.js";
import {S3Client} from "@aws-sdk/client-s3";

export const LISTING_INDEX = 'gsi-listing'
export function getHistoryTable() {
    return lambdaEnv.getEnv('DATABASE_HISTORY')
}

export type DeliveryHistoryRecord = DeliveryHistoryShort & {
    partitionKey: string,
    sortKey: string,
    gsiListingPk: string,
    gsiListingSk: string,
}

export type DeliveryHistoryDetailsRecord =  {
    id: string,
    partitionKey: string,
    sortKey: string,
    error?: {
        message: string,
        description: string,
        diagnostic: string
    },
}

export type UserDeliveryHistoryRecord = {
    partitionKey: string, // === userId
    sortKey: string, // === deliveryId
    userId: string,
    relatedSurveyResultsIds?: string[],
    isDelivered: boolean,
    distributionName: string,
    configurationId: string,
    deliveryId: string,
    deliveryAt: string,
}

const RecordSupportFields =
    ['partitionKey', 'sortKey', 'gsiListingPk', 'gsiListingSk'] as const

/**
 *
 * @param from 2016-05-25T09:24:15Z
 * @param to 2016-05-25T09:24:15Z
 */
export function splitSearchJob(from: string, to: string) {
    const fromDt = DateTime.fromISO(from)
    const toDt = DateTime.fromISO(to)

    type elem = {
        pk: string,
        skFrom: string,
        skTo: string
    }
    const months: elem[] = []

    const startElem:elem = {
        pk: fromDt.toFormat('yyyy-MM'),
        skFrom: from,
        skTo: toDt.hasSame(fromDt, 'month') ? to : fromDt.endOf('month').toUTC().toISO()
    }

    const endElem: elem = {
        pk: toDt.toFormat('yyyy-MM'),
        skFrom: toDt.startOf('month').toUTC().toISO(),
        skTo: to
    }

    months.push(startElem)
    if (startElem.pk !== endElem.pk) {
        months.push(endElem)
    }

    let currentDt = fromDt.plus(Duration.fromObject({month: 1}))
    while (currentDt.startOf('month') < toDt.startOf('month')) {
        months.push({
            pk: currentDt.toFormat('yyyy-MM'),
            skFrom: currentDt.startOf('month').toISO(),
            skTo: currentDt.endOf('month').toISO()
        })
        currentDt = currentDt.plus(Duration.fromObject({ month: 1}))
    }

    return _.uniq(months)
}

export function mapRecordShort(r: DeliveryHistoryRecord): DeliveryHistoryShort {
    return _.omit(r, RecordSupportFields);
}
export function mapDetails(r: DeliveryHistoryDetailsRecord): DeliveryHistoryDetails {
    return _.omit(r, RecordSupportFields);
}

export const continueTokenUtils = {
    encode: (token: any) => {
        return token ? encodeURI(JSON.stringify(token)) : undefined
    },
    decode: (token: string | undefined) => {
        return token ? JSON.parse(decodeURI(token)) : undefined
    }
}

export const awsClients = {
    get dynamoDb() {
        return createAwsClient(config => DynamoDBDocumentClient.from(new DynamoDBClient(config)))
    },
    get s3() {
        return createAwsClient(config => new S3Client(config))
    }
}

