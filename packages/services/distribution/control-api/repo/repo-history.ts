import {GetCommand, PutCommand, QueryCommand, UpdateCommand} from "@aws-sdk/lib-dynamodb";
import {
    awsClients,
    DeliveryHistoryDetailsRecord,
    DeliveryHistoryRecord,
    getHistoryTable,
    LISTING_INDEX,
    mapDetails,
    mapRecordShort
} from "./repo-history-defs.js";
import {DeliveryHistoryDetails, DeliveryHistoryShort} from "@oss/shared/lib/distribution/api-types.js";
import {DateTime} from "luxon";
import {DeliveryState, DistributionType} from "@oss/shared/lib/distribution/constants.js";
import * as excep from '../../../common/admin-api/utils/exceptions.js';

/**
 *
 */
export async function scanMonth(from: string, to: string, params: {
    filter?: {
        filterExpression: string,
        filterValues: Record<string, any>
    },
    lek?: any
}) {
    let eav = {
        ':sgh': "segment-dist",
        ...(params.filter?.filterValues || {}),
        ':from': DateTime.fromISO(from).startOf('day').toUTC().toISO(),
        ':to': DateTime.fromISO(to).endOf('day').toUTC().toISO()
    }
    const {Items, LastEvaluatedKey} = await awsClients.dynamoDb.send(new QueryCommand({
        TableName: getHistoryTable(),
        KeyConditionExpression: 'gsiListingPk = :sgh AND gsiListingSk BETWEEN :from AND :to',
        FilterExpression: params.filter?.filterExpression || undefined,
        ExpressionAttributeValues: eav,
        IndexName: LISTING_INDEX,
        ExclusiveStartKey: params.lek
    }))
    return {
        items: Items.map(r => mapRecordShort(r as DeliveryHistoryRecord)),
        lek: LastEvaluatedKey
    }
}

export async function scanRecordsBetween(from: string, to: string, limit = 1000, startAfter?: any) {
    const betweenDateWrongErrMsg = "開始日の設定が正しくありません。"
    if (new Date(to) < new Date(from)) {
        throw new excep.BadRequest({ code: 'between_date_wrong', msg: betweenDateWrongErrMsg });
      }
    let lek = startAfter
    let items: DeliveryHistoryShort[] = []
    do {
        const res = await scanMonth(from, to, { lek })
        items = items.concat(res.items)
        lek = res.lek
    } while (lek && items.length <= limit)
    return {
        items,
        lek
    }
}

export async function getHistoryRecord(historyId: string) {
    const { Item } = await awsClients.dynamoDb.send(new GetCommand({
        TableName: getHistoryTable(),
        Key: {
            partitionKey: historyId,
            sortKey: 'default'
        }
    }))
    return Item ? mapRecordShort(Item as DeliveryHistoryRecord) : null;
}

export async function getHistoryDetails(historyId: string) {
    const { Item } = await awsClients.dynamoDb.send(new GetCommand({
        TableName: getHistoryTable(),
        Key: {
            partitionKey: historyId,
            sortKey: 'default'
        }
    }))
    if (!Item) return null;
    const { Item : Details } = await awsClients.dynamoDb.send(new GetCommand({
        TableName: getHistoryTable(),
        Key: {
            partitionKey: historyId,
            sortKey: 'details'
        }
    }))
    const Logs = [] // TODO fetch logs
    return {
        Item: mapRecordShort(Item as DeliveryHistoryRecord),
        Details: Details ? mapDetails(Details as DeliveryHistoryDetailsRecord) : {},
        Logs
    }
}

export async function createHistoryRecord(object: DeliveryHistoryShort, details: DeliveryHistoryDetails = {}) {
    const [reminderIndexPk, ] = object.configurationId.split('#')
    const historyIndexPk = object.distributionType === DistributionType.REMINDER ? reminderIndexPk : 'segment-dist'
    const record: DeliveryHistoryRecord = {
        ...object,
        partitionKey: object.id,
        sortKey: 'default',
        gsiListingPk: historyIndexPk,
        gsiListingSk: object.createdAt
    }
    await awsClients.dynamoDb.send(new PutCommand({
        TableName: getHistoryTable(),
        Item: record
    }))

    await awsClients.dynamoDb.send(new PutCommand({
        TableName: getHistoryTable(),
        Item: {
            partitionKey: object.id,
            sortKey: 'details',
            id: object.id,
            ...details,
        } as DeliveryHistoryDetailsRecord
    }))
}


export async function updateFinished(id: string) {
    const timestamp = DateTime.now().toUTC().toISO()
    await awsClients.dynamoDb.send(new UpdateCommand({
        TableName: getHistoryTable(),
        Key: {
            partitionKey: id,
            sortKey: 'default',
        },
        UpdateExpression: "SET #state = :state, finishedAt = :finishedAt",
        ExpressionAttributeValues: {
            ':state': DeliveryState.FINISHED,
            ':finishedAt': timestamp
        },
        ExpressionAttributeNames: {
            '#state': 'state'
        }
    }))
}

export async function updateStarted(id: string) {
    const timestamp = DateTime.now().toUTC().toISO()
    await awsClients.dynamoDb.send(new UpdateCommand({
        TableName: getHistoryTable(),
        Key: {
            partitionKey: id,
            sortKey: 'default',
        },
        UpdateExpression: "SET #state = :state, startedAt = :startedAt",
        ExpressionAttributeValues: {
            ':state': DeliveryState.IN_PROGRESS,
            ':startedAt': timestamp
        },
        ExpressionAttributeNames: {
            '#state': 'state'
        }
    }))
}

export async function updateDeliveriesCount(id: string, increment: number) {
    await awsClients.dynamoDb.send(new UpdateCommand({
        TableName: getHistoryTable(),
        Key: {
            partitionKey: id,
            sortKey: 'default',
        },
        UpdateExpression: "ADD deliveriesCount :increment ",
        ExpressionAttributeValues: {
            ':increment': increment
        }
    }))
}
export async function setTargetsCount(id: string, targets: number) {
    await awsClients.dynamoDb.send(new UpdateCommand({
        TableName: getHistoryTable(),
        Key: {
            partitionKey: id,
            sortKey: 'default',
        },
        UpdateExpression: "SET targetsCount = :targetsCount",
        ExpressionAttributeValues: {
            ':targetsCount': targets
        }
    }))
}

export async function updateWithError(id: string, params: {
    errorMessage: string,
    event?: string,
    timestamp?: string
}) {
    const timestamp = params.timestamp || DateTime.now().toUTC().toISO()
    await awsClients.dynamoDb.send(new UpdateCommand({
        TableName: getHistoryTable(),
        Key: {
            partitionKey: id,
            sortKey: 'default',
        },
        UpdateExpression: "SET #state = :state, finishedAt = :finishedAt, errorMessage = :errorMessage",
        ExpressionAttributeValues: {
            ':state': DeliveryState.ERROR,
            ':finishedAt': timestamp,
            ':errorMessage': params.errorMessage || ''
        },
        ExpressionAttributeNames: {
            '#state': 'state'
        }
    }))
    await awsClients.dynamoDb.send(new UpdateCommand({
        TableName: getHistoryTable(),
        Key: {
            partitionKey: id,
            sortKey: 'details'
        },
        UpdateExpression: "SET #error = :error",
        ExpressionAttributeValues: {
            ':error': { message: params.errorMessage },
        },
        ExpressionAttributeNames: {
            '#error': 'error',
        }
    }))
}

export async function updateUserHistory(params: {
    userId: string,
    relatedSurveyResultsIds?: string[],
    isDelivered: boolean,
    distributionName: string,
    configurationId: string,
    deliveryId: string,
    deliveryAt?: string,
}){
    const timestamp = params.deliveryAt || DateTime.now().toUTC().toISO()
    await awsClients.dynamoDb.send(new PutCommand({
        TableName: getHistoryTable(),
        Item: {
            partitionKey: params.userId,
            sortKey: params.deliveryId,
            deliveryAt: params.deliveryAt || timestamp,
            ...params,
        }
    }))
}
