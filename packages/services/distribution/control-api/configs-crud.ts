import {Request, Response} from "lambda-api";
import * as validators from './helpers/request-validators.js'
import {DistributionType, TargetsSelectionType} from "@oss/shared/lib/distribution/constants.js";
import moment from 'moment';
moment.locale('ja');
import {
    createConfig,
    getConfig,
    getMessages,
    removeConfig,
    removeMessages,
    updateConfig,
    updateMessages
} from "./repo/repo-configs.js";
import {DistributionConfigParams} from "./repo/repo-configs-defs.js";
import {DistributionConfig, Message} from "@oss/shared/lib/distribution/api-types.js";
import {getPostponedScheduler, getRepeatingScheduler} from "../schedule/segment-delivery-scheduler-client.js";
import {findFutureTime} from "./helpers/recurrent-calc.js";
import {deleteSchedule} from "../schedule/aws-scheduler-client.js";
import {authContext} from "../../common/admin-api/utils/auth.js";
import {getDistributionResourcesUploadLink} from "./helpers/file-upload.js";
import { lambdaEnv } from "../../common/lambda-env.js";
import {ENV} from "../module.js";
import { BadRequest } from "../../common/utils/exceptions.js";

function validateMessages(messages: any) {
    return validators.MessagesValidator.min(1).parse(messages) as Message[]
}

async function saveMessages(id: string, messages: Message[]) {
    await updateMessages(id, messages)
}

async function upsertCommon(settings: DistributionConfigParams, id?: string) {
    if(settings.targetSelectionType === TargetsSelectionType.SURVEY_CONDITIONS && !settings.surveyConditions.surveyId){
        throw new Error(`When targetSelectionType is ${TargetsSelectionType.SURVEY_CONDITIONS}, surveyId is required.`)
    }
    if (id) {
        return updateConfig(id, settings)
    } else {
        settings.createdBy = authContext.user?.name as string
        return createConfig(settings)
    }

}

async function createRepeating(setting: any, id?: string) {
    const body = validators.RepeatingConfig.parse(setting)
    // validate
    const now = moment()
    const fromDatetime = moment(`${body.repeatingSettings.fromDate} ${body.repeatingSettings.fromTime}:00`, 'YYYY-MM-DD HH:mm:ss')
    const isInFuture = fromDatetime.isAfter(now);
    if (!isInFuture) {
        throw new BadRequest({ msg: '現在時刻より前の日時は設定できません。', code: 'BAD_REQUEST' });
    }

    const config = await upsertCommon(body as DistributionConfigParams, id)
    if (config.isDraft) {
        return config;
    }

    const result = findFutureTime(config.repeatingSettings);
    if (result) {
        const {date, time} = result
        await getRepeatingScheduler().update(config.id, date, time, {description: config.distributionName})
    } else {
        await getRepeatingScheduler().remove(config.id)
    }

    return config
}

async function createPostponed(settings: any, id?: string) {
    const body = validators.PostponedConfig.parse(settings)
    const now = moment()
    const postponedDatetime = moment(`${body.postponedSettings.date} ${body.postponedSettings.time}:00`, 'YYYY-MM-DD HH:mm:ss')
    const isInFuture = postponedDatetime.isAfter(now);
    if (!isInFuture) {
        throw new BadRequest({ msg: '現在時刻より前の日時は設定できません。', code: 'BAD_REQUEST' });
    }
    if (body.targetSelectionType === TargetsSelectionType.SURVEY_CONDITIONS) {
        // check presence of surveyConditions
    } else {
        // check absence of surveyConditions
    }

    const config = await upsertCommon(body as DistributionConfigParams, id)
    if (config.isDraft) {
        return config;
    }
    await getPostponedScheduler().update(config.id, body.postponedSettings.date, body.postponedSettings.time, {
        description: config.distributionName
    })
    return config
}

async function createExternal(settings: any, id?: string) {
    validators.ExternalConfig.parse(settings)
    return upsertCommon(settings as DistributionConfigParams, id)
}

async function createTalk(settings: any, id?: string) {
    validators.TalkConfig.parse(settings)
    return upsertCommon(settings as DistributionConfigParams, id)
}

export async function create(req: Request, res: Response) {
    const {
        settings,
        messages,
    } = req.body
    const {distributionType, targetSelectionType} = validators.ConfigRequiredFields.parse(settings)
    let newConfig: DistributionConfig
    if (distributionType === DistributionType.POSTPONED) {
        newConfig = await createPostponed(settings)
        const validatedMessages = validateMessages(messages)
        await updateMessages(newConfig.id, validatedMessages)
    } else if (distributionType === DistributionType.REPEATING) {
        newConfig = await createRepeating(settings)
        const validatedMessages = validateMessages(messages)
        await updateMessages(newConfig.id, validatedMessages)
    } else if (distributionType === DistributionType.EXTERNAL) {
        newConfig = await createExternal(settings)
    } else if (distributionType === DistributionType.TALK) {
        newConfig = await createTalk(settings)
    } else {
        throw new Error(`Not supported config type ${distributionType}`)
    }

    res.json(newConfig)
}

export async function update(req: Request, res: Response) {
    const {
        settings,
        messages
    } = req.body
    const id = req.params.id
    const {distributionType, targetSelectionType} = validators.ConfigRequiredFields.parse(settings)
    let newConfig: DistributionConfig
    if (distributionType === DistributionType.POSTPONED) {
        newConfig = await createPostponed(settings, id)
        const validatedMessages = validateMessages(messages)
        await updateMessages(id, validatedMessages)
    } else if (distributionType === DistributionType.REPEATING) {
        newConfig = await createRepeating(settings, id)
        const validatedMessages = validateMessages(messages)
        await updateMessages(id, validatedMessages)
    } else if (distributionType === DistributionType.EXTERNAL) {
        newConfig = await createExternal(settings, id)
    } else if (distributionType === DistributionType.TALK) {
        newConfig = await createTalk(settings, id)
    } else {
        throw new Error(`Not supported config type ${distributionType}`)
    }

    res.json(newConfig)
}

export async function remove(req: Request, res: Response) {
    const id = req.params.id;
    const config = await getConfig(id)
    await removeConfig(id)
    await removeMessages(id)

    if ([DistributionType.REPEATING, DistributionType.POSTPONED].includes(config.distributionType)) {
        await deleteSchedule(id, config.distributionType)
    }
    // TODO send notification
    res.sendStatus(204)
}

export async function get(req: Request, res: Response) {
    const config = await getConfig(req.params.id)
    const messages = await getMessages(req.params.id)
    if (config) {
        res.json({
            settings: config,
            messages
        })
    } else {
        res.sendStatus(404)
    }
}

export async function getImageUploadUrl(req: Request, res: Response) {
    const {key, contentType} = validators.UploadUrlRequest.parse(req.query)
    const urlLink = await getDistributionResourcesUploadLink(key, contentType)
    const downloadUrlLink = `https://${lambdaEnv.getEnv(ENV.RESOURCES_CLOUDFRONT_DOMAIN)}/dist-resources/${key}`
    res.json({urlLink, downloadUrlLink})
}
