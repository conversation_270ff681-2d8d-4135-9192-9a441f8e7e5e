import axios from "axios";
import {v4} from "uuid";
import {DateTime} from "luxon";

const PUSH_ENDPOINT = 'https://api.line.me/v2/bot/message/push'
const BROADCAST_ENDPOINT = 'https://api.line.me/v2/bot/message/broadcast'
const MULTICAST_ENDPOINT = 'https://api.line.me/v2/bot/message/multicast'

export type LineMessage = {
    type: string,
    [key: string]: any
}

type ResponseInfo = {
    isOk: boolean,
    wasAlreadyAccepted: boolean
    requestId: string,
    retryToken: string,
    acceptedRequestId?: string,
    error?: {
        isRetryable: boolean,
        isTimeout: boolean,
        message: string,
        statusCode: number,
        details?: any
    },
    requestTimestamp: string,
}

export type RequestResult = ResponseInfo & {

    request: {
        targets?: string[],
        messages: LineMessage[],
    }
}


export class LineRequest {
    constructor(private config: {
        channelAccessToken: string
    }) {
    }

    async push(lineUid: string, messages: LineMessage[], retryToken = v4()): Promise<RequestResult> {
        const info = await this.doRequest(PUSH_ENDPOINT, {
            to: lineUid,
            messages,
        }, {retryToken})
        return {
            ...info,
            request: {
                targets: [lineUid],
                messages
            }
        }
    }

    async multicast(lineUids: string[], messages: LineMessage[], retryToken = v4()): Promise<RequestResult> {
        const info = await this.doRequest(MULTICAST_ENDPOINT, {
            to: lineUids,
            messages,
        }, {retryToken})
        return {
            ...info,
            request: {
                targets: lineUids,
                messages
            }
        }
    }

    async broadcast(messages: LineMessage[], retryToken = v4()): Promise<RequestResult> {
        const info = await this.doRequest(BROADCAST_ENDPOINT, {
            messages,
        }, {retryToken})
        return {
            ...info,
            request: {
                messages
            }
        }
    }


    private async doRequest(url: string, requestBody: any, opts: {
        retryToken: string
    }): Promise<ResponseInfo> {
        const requestTimestamp = DateTime.now().toUTC().toISO()
        const retryToken = opts?.retryToken || v4()
        try {
            const res = await axios.post(url, requestBody, {
                validateStatus: () => true,
                headers: {
                    "X-Line-Retry-Key": retryToken,
                    "Content-Type": 'application/json',
                    Authorization: `Bearer ${this.config.channelAccessToken}`,
                }
            })
            const result: ResponseInfo = {
                isOk: res.status === 200 || res.status === 409,
                wasAlreadyAccepted: res.status === 409,
                requestId: res.headers['x-line-request-id'],
                acceptedRequestId: res.headers['x-line-accepted-request-id'],
                retryToken,
                requestTimestamp,
            }
            if (!result.isOk) {
                result.error = {
                    isRetryable: Math.round(res.status / 100) === 5,
                    isTimeout: false,
                    statusCode: res.status,
                    message: res.data['message'],
                    details: res.data['details']
                }
            }
            return result
        } catch (error: any) {
            return {
                isOk: false,
                wasAlreadyAccepted: false,
                requestTimestamp,
                retryToken,
                requestId: '',
                error: {
                    isRetryable: true,
                    isTimeout: error.code === 'ECONNABORTED',
                    statusCode: 0,
                    message: error.message
                },
            }
        }
    }
}
