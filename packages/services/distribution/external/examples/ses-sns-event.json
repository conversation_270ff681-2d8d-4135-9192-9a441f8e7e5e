{"Records": [{"EventSource": "aws:sns", "EventVersion": "1.0", "EventSubscriptionArn": "arn:aws:sns:ap-northeast-1:342975433460:mydev-oss-received-emails:c1ac47e4-1142-4da9-907f-00e81ec6897f", "Sns": {"Type": "Notification", "MessageId": "48a71836-1247-5a87-a07e-45c2a7314536", "TopicArn": "arn:aws:sns:ap-northeast-1:342975433460:mydev-oss-received-emails", "Subject": "Amazon SES Email Receipt Notification", "Message": "{\"notificationType\":\"Received\",\"mail\":{\"timestamp\":\"2023-12-14T07:48:06.319Z\",\"source\":\"<EMAIL>\",\"messageId\":\"rtlr2dpe3s493jkkvberi3noll6fo54jhvin8ig1\",\"destination\":[\"<EMAIL>\"],\"headersTruncated\":false,\"headers\":[{\"name\":\"Return-Path\",\"value\":\"<<EMAIL>>\"},{\"name\":\"Received\",\"value\":\"from mail-oa1-f52.google.com (mail-oa1-f52.google.com [*************]) by inbound-smtp.ap-northeast-1.amazonaws.com with SMTP id rtlr2dpe3s493jkkvberi3noll6fo54jhvin8ig1 for <EMAIL>; Thu, 14 Dec 2023 07:48:06 +0000 (UTC)\"},{\"name\":\"Received-SPF\",\"value\":\"pass (spfCheck: domain of company.co.jp designates ************* as permitted sender) client-ip=*************; envelope-from=<EMAIL>; helo=mail-oa1-f52.google.com;\"},{\"name\":\"Authentication-Results\",\"value\":\"amazonses.com; spf=pass (spfCheck: domain of company.co.jp designates ************* as permitted sender) client-ip=*************; envelope-from=<EMAIL>; helo=mail-oa1-f52.google.com; dkim=pass header.i=@company.co.jp; dmarc=none header.from=company.co.jp;\"},{\"name\":\"X-SES-RECEIPT\",\"value\":\"AEFBQUFBQUFBQUFGZENpeXgwR1NRRGRKVG1kKzBkMmlHaUJzUm9uRnFnRlQwMzVYVk1TbGFROXFoMHpId2tJaEFlalVkOGFhcDdRNDNnaHRLdUF2c2E1SlhubTMramxHNC95bjIvcm9iZ3BWZUw5TVpCTGRWUFBkUHg3a2tmeWR1S3NGSlVNTTRieWcxZWo0TjBxekhsUjZBWlB2dG1QdkdscG1YcURrcVVRTGFGSi9nK09pTitqaFZla21aQnVNZHE5ckFKdzlLTGlLU3pnVElsbmoxM0xFcHBLQ0RLczNVTFJKc3hObGd0STJzcUppMXAva2wwQ2lRVURzNDRrOUM3d0pOczZueEZQbTZ2TkNYbklpVlZVaXNXcXJPWHZ4QTMvenYxTitJOERNTnQwRElhV1ZLY2xBTzFsbUMrR2h2OU5sNU9jK1hFOWM9\"},{\"name\":\"X-SES-DKIM-SIGNATURE\",\"value\":\"a=rsa-sha256; q=dns/txt; b=QGyZCSz/BA5KVHlzrsWpEX/gDu8fL4E9llR+X9NcrxEL77EA2N9Vea27FzY8HNssdKOs6PODJdRGaC+ZsgJVV+/SJr5KGcV0umRhEAu3QaEEZPrlprh+v76aBEdYXJveSpO2fUXJzmiX0mTDQ5JIp/QHem68Z23OY1Id/bCM6sg=; c=relaxed/simple; s=zh4gjftm6etwoq6afzugpky45synznly; d=amazonses.com; t=1702540086; v=1; bh=Gl7oD8tndaSCWIf1xVfzCmYUZMKOBri3jQAlVKVXDkY=; h=From:To:Cc:Bcc:Subject:Date:Message-ID:MIME-Version:Content-Type:X-SES-RECEIPT;\"},{\"name\":\"Received\",\"value\":\"by mail-oa1-f52.google.com with SMTP id 586e51a60fabf-1f060e059a3so5895216fac.1 for <<EMAIL>>; Wed, 13 Dec 2023 23:48:05 -0800 (PST)\"},{\"name\":\"DKIM-Signature\",\"value\":\"v=1; a=rsa-sha256; c=relaxed/relaxed; d=company.co.jp; s=google; t=1702540084; x=1703144884; darn=nikita-mydev-oss.line-smartcity.com; h=to:subject:message-id:date:from:mime-version:from:to:cc:subject:date:message-id:reply-to; bh=Gl7oD8tndaSCWIf1xVfzCmYUZMKOBri3jQAlVKVXDkY=; b=wlFczHnTiz+Z9fIgw1k95t6Qw/xywxIQr/CTAF+jCLPeyaIXmpc3mpcOhdG1Y+0bFQAyzCmZ1/j28dBMURmMfyzKE7pmYM59VaHliiu2NefmLx7gB9umSnLmGUN8XPRoEBuMrUQsYeqko/2DNKy+K7QYa7p9zjCI9kuA6TfxawP4e+RwmYJFANSXcggKdtoKb1jPhxGZDbNDe5Q4cvZlFIJnL8N4OTYKrsbZrth3pIpDOfLGDsLnqUVpFihOXfblP2qp8Z5r0UiFvo5TbQndX3BpSlaTQqYGmjZgXBZJ3j9FjXSalE6X8KXiYoPl1hWhvbPfv6uyIch25l20bbE2vA==\"},{\"name\":\"X-Google-DKIM-Signature\",\"value\":\"v=1; a=rsa-sha256; c=relaxed/relaxed; d=1e100.net; s=20230601; t=1702540084; x=1703144884; h=to:subject:message-id:date:from:mime-version:x-gm-message-state :from:to:cc:subject:date:message-id:reply-to; bh=Gl7oD8tndaSCWIf1xVfzCmYUZMKOBri3jQAlVKVXDkY=; b=rPNcfywshVvv8IaTR0IS+eWuelNxwgjikgQ+b/ToixZAnqCM/LFfiuNOKj6T0U1PxE dZOCYAPgHQrGUtTB8KpWJNWV4ceAqg3sWXqN365+o40mRsIR1zFx5Pb9BcAJ0fb7vAhI WPNypGK51DORp06Cjra0z7HhnQAQHoqaAUcgjHF1E/ZpPOwoCf82jvUbHcVI/v9ISJW6 DQ/qwVzECpzLr85WVo7qeAJk1o42H4kInarsR2mo/Z/rR2+BO6EDBlFMntgXhRJtvp98 M19NstWJg8aGUy7MtZWw15r9AlCbgygfYYDHxFxJitvs/qryiAvmiRfn7ThRVRGB0vnD 9AVw==\"},{\"name\":\"X-Gm-Message-State\",\"value\":\"AOJu0YyrfssDN1vEQMkJjUStekMi80csp6GNhFLTwtq6juUh7tAr0gA5 twKkiQst6Q33awkf34Ua1DqFbt/reUQB9vv8AF3qcvC1p2PSV1y4\"},{\"name\":\"X-Google-Smtp-Source\",\"value\":\"AGHT+IEFl7LAGZ9h4Jip3C+7uKxo5votQp10He8SFPHpRikrEByJ+fvk6+aHVf7LKzDXayIQ0lkEjQ/q6MuwDbF13Yc=\"},{\"name\":\"X-Received\",\"value\":\"by 2002:a05:6870:ac10:b0:1fb:75c:3ffc with SMTP id kw16-20020a056870ac1000b001fb075c3ffcmr12202037oab.92.1702540084278; Wed, 13 Dec 2023 23:48:04 -0800 (PST)\"},{\"name\":\"MIME-Version\",\"value\":\"1.0\"},{\"name\":\"From\",\"value\":\"Nikita Brazhnikov <<EMAIL>>\"},{\"name\":\"Date\",\"value\":\"Thu, 14 Dec 2023 16:47:53 +0900\"},{\"name\":\"Message-ID\",\"value\":\"<CA+Zs+mv60JyCQ8tA17xiUCx4G_i9JAgQ=<EMAIL>>\"},{\"name\":\"Subject\",\"value\":\"Test delivery\"},{\"name\":\"To\",\"value\":\"<EMAIL>\"},{\"name\":\"Content-Type\",\"value\":\"multipart/alternative; boundary=\\\"000000000000f08bd2060c7380b8\\\"\"}],\"commonHeaders\":{\"returnPath\":\"<EMAIL>\",\"from\":[\"Nikita Brazhnikov <<EMAIL>>\"],\"date\":\"Thu, 14 Dec 2023 16:47:53 +0900\",\"to\":[\"<EMAIL>\"],\"messageId\":\"<CA+Zs+mv60JyCQ8tA17xiUCx4G_i9JAgQ=<EMAIL>>\",\"subject\":\"Test delivery\"}},\"receipt\":{\"timestamp\":\"2023-12-14T07:48:06.319Z\",\"processingTimeMillis\":421,\"recipients\":[\"<EMAIL>\"],\"spamVerdict\":{\"status\":\"DISABLED\"},\"virusVerdict\":{\"status\":\"DISABLED\"},\"spfVerdict\":{\"status\":\"PASS\"},\"dkimVerdict\":{\"status\":\"PASS\"},\"dmarcVerdict\":{\"status\":\"GRAY\"},\"action\":{\"type\":\"S3\",\"topicArn\":\"arn:aws:sns:ap-northeast-1:342975433460:mydev-oss-received-emails\",\"bucketName\":\"mydev-oss-distribution-public-resources-aomwp\",\"objectKeyPrefix\":\"emails/<EMAIL>\",\"objectKey\":\"emails/<EMAIL>/rtlr2dpe3s493jkkvberi3noll6fo54jhvin8ig1\"}}}", "Timestamp": "2023-12-14T07:48:06.769Z", "SignatureVersion": "1", "Signature": "b4KeyqjB626V0+m6g1xSRMUADvfF9RwM4EtVxOeMGJahj4T+am4IozYtsKijjpvMnOvhZFz6swPPAw9tGEebp5NTKgeK75HwGNJNOWx5GH7OZL65CCC2Tp0Z5ZkyOXiWmnAqTRZEjsc5T9XrMZX75Up0wUSSnmAfzSnKaaEO1wlYqKGTeWMRIptykCgBquyEKrv6ivLHvi5atmIeUv57woqxxz7nVSaF+a7td8m0H78W6fY11i3FMBHjMbO/z5tpA2qs7JEI7mXJvX5/pJQYJm5Xs7ni+JPCh6bgoZLGxPfhCLlYSf5I0HY/d6s3AjVf/WdU0NJ4X8BVgXQOqlw2kg==", "SigningCertUrl": "https://sns.ap-northeast-1.amazonaws.com/SimpleNotificationService-01d088a6f77103d0fe307c0069e40ed6.pem", "UnsubscribeUrl": "https://sns.ap-northeast-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:ap-northeast-1:342975433460:mydev-oss-received-emails:c1ac47e4-1142-4da9-907f-00e81ec6897f", "MessageAttributes": {}}}]}