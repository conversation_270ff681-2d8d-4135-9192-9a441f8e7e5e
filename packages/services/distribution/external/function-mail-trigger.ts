import console from "console";
import {SESMessage, SNSEvent} from "aws-lambda";
import {simpleParser} from 'mailparser'
import {GetObjectCommand, PutObjectCommand, S3Client} from "@aws-sdk/client-s3";
import {lambdaEnv} from "../../common/lambda-env.js";
import {ENV, NOTIFICATONS, notify, RunDistributionCommandEvent} from "../module.js";
import {listConfigs} from '../control-api/repo/repo-configs.js'
import {DistributionType} from "@oss/shared/lib/distribution/constants.js";
import {DistributionConfig} from "@oss/shared/lib/distribution/api-types.js";
import {matches} from 'common/utils/mail/analyser.js'
import transform from 'common/utils/mail/transformer.js'
import {InvokeCommand, LambdaClient} from "@aws-sdk/client-lambda";
import util from "util";

async function getEnabledConfigs() {
    const external = await listConfigs(DistributionType.EXTERNAL, {})
    const talks = await listConfigs(DistributionType.TALK, {})
    return external.concat(...talks).filter(c => c.enabled)
}

async function getEnabledTalks() {
    const talks = await listConfigs(DistributionType.TALK, {})
    return talks.filter(c => c.enabled)
}

function isMatching(config: DistributionConfig, mailContent: { subject: string; text: string }) {
    // データ移行後mailTriggeredSettingsが存在しないデータがあれば以下のコメントアウトを解除
    // if (!config.mailTriggerSettings) {
    //     return false
    // }
    const condition = config.mailTriggerSettings.condition
    console.log('testing', config.id, JSON.stringify(condition))
    return matches({subject: mailContent.subject, body: mailContent.text},
        condition.subjectExtractionCondition,
        condition.bodyExtractionCondition)
}

function createDistributionEvent(config: DistributionConfig, mailText: string, subject: string): RunDistributionCommandEvent {
    const sedCommands = config.mailTriggerSettings.content?.bodyChangeCondition
    const messageText = sedCommands ? transform(sedCommands)(mailText) : mailText
    return {
        RunConfiguration: {
            configurationId: config.id,
            messages: [{
                type: 'text',
                text: messageText,
                originalText: mailText,
                subject: subject
            }]
        }
    };
}

async function createRunScenarioCalls(config: DistributionConfig) {
    const chatbotFunctionPayload = JSON.stringify({
        event_type: 'talk_distribution',
        talkId: config.talkSettings.talkId,
        talkName: config.talkSettings.talkName,
        useDisasterRichmenu: config.talkSettings.useDisasterRichmenu,
    })
    const chatbotFunction = config.talkSettings.environment === 'production'
        ? lambdaEnv.getEnv(ENV.LAMBDA_CHATBOT_PROD)
        : lambdaEnv.getEnv(ENV.LAMBDA_CHATBOT_SANDBOX)
    console.log(`Calling ${chatbotFunction} with talk ${config.talkSettings.talkId}(${config.id}). Payload\n ${chatbotFunctionPayload}`)
    await new LambdaClient({}).send(new InvokeCommand({
        FunctionName: chatbotFunction,
        Payload: new util.TextEncoder().encode(chatbotFunctionPayload),
        InvocationType: 'Event'
    }))
}

export async function handler(event: SNSEvent) {
    const snsEvent = event.Records[0].Sns
    if (snsEvent.Subject === 'Amazon SES Email Receipt Subscription Notification') {
        console.log('Subscription notification service message, ignore it')
        return;
    }

    const message = JSON.parse(snsEvent.Message) as SESMessage;
    const messageId = message.mail.messageId
    const subject = message.mail.commonHeaders.subject
    const fromAddress = message.receipt.recipients[0]
    console.log(messageId, subject, fromAddress)


    // --- for debug ---
    await new S3Client({}).send(new PutObjectCommand({
        Bucket: lambdaEnv.getEnv(ENV.BUCKET_PRIVATE_STORAGE),
        Key: `sns/${messageId}.json`,
        ContentType: 'text/json',
        Body: JSON.stringify(event)
    }))
    // --- END ---

    // TODO SPF header check (see aws_back/distribution_engine/manager/lambda/src/utils/mail.ts)
    // TODO whitelisted emails check

    const emailFromS3 = await new S3Client({}).send(new GetObjectCommand({
        Bucket: lambdaEnv.getEnv(ENV.BUCKET_PRIVATE_STORAGE),
        Key: `emails/${fromAddress}/${messageId}`
    }))
    const body = await emailFromS3.Body.transformToString('utf-8')

    const parsedMail = await simpleParser(body)
    const text = parsedMail.text
    console.log("SUBJECT: " + parsedMail.subject )
    console.log("======= TEXT========\n", text, "\n=========")

    const externalConfigs = await getEnabledConfigs();
    // filter matching configurations
    const matchingConfigs = externalConfigs.filter(c => isMatching(c, {subject, text}))

    // process mail triggered distributions
    // create message text and combine it into event bus payload
    const mailTriggeredDeliveries = matchingConfigs.filter(c => c.distributionType === 'external')
    const distrTasks: RunDistributionCommandEvent[] = mailTriggeredDeliveries.map(c => createDistributionEvent(c, text, subject))
    await Promise.all(distrTasks.map(t => notify(NOTIFICATONS.types.RUN_CONFIGURATION, t)))
    console.log(mailTriggeredDeliveries.length, 'distributions has been run')

    // process talks
    const talks = matchingConfigs.filter(c => c.distributionType === 'talk')
    const talksTasks = talks.map(c => createRunScenarioCalls(c))
    await Promise.all(talksTasks)
    console.log(talksTasks.length, 'talks has been run')
}
