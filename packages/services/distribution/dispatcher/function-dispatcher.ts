import '../module.js'
import {ENV, RunDistributionCommandEvent} from "../module.js";
import {EventBusEvent} from "../delivery-engine/types.js";
import {SFNClient, StartExecutionCommand} from '@aws-sdk/client-sfn'
import {lambdaEnv} from "../../common/lambda-env.js";
import console from "console";
import {getConfig} from "../control-api/repo/repo-configs.js"
import {DistributionType} from "@oss/shared/lib/distribution/constants.js";
import {findFutureTime, findFutureTimeStartingTomorrow} from "../control-api/helpers/recurrent-calc.js";
import {getRepeatingScheduler} from "../schedule/segment-delivery-scheduler-client.js";
import {DateTime} from "luxon";


export async function handler(event: any) {
    console.log("New run request" + JSON.stringify(event))
    // accept notifications about new instant distributions
    // and run them
    const {detail} = event as EventBusEvent<RunDistributionCommandEvent>

    const configurationId = detail.RunConfiguration.configurationId.replace('--', '#')
    const configuration = await getConfig(configurationId);
    await new SFNClient().send(new StartExecutionCommand({
        stateMachineArn: lambdaEnv.getEnv(ENV.MACHINE_ARN_SEARCH),
        input: JSON.stringify(detail.RunConfiguration)
    }));

    if (configuration.distributionType === DistributionType.REPEATING) {
        // note: we should ensure that the next time is on the next day or later.
        // the same configuration is not allowed to run twice on the same day.
        // detail._meta?.scheduledTime contains the time when the distribution was scheduled to run
        const nextTime = findFutureTimeStartingTomorrow(configuration.repeatingSettings, DateTime.fromISO(detail._meta?.scheduledTime))
        if (nextTime) {
            await getRepeatingScheduler().update(configurationId, nextTime.date, nextTime.time)
        }
    }
}
