import {DateTime} from "luxon";
import {
    ActionAfterCompletion,
    CreateScheduleCommand,
    DeleteScheduleCommand,
    GetScheduleCommand, GetScheduleCommandOutput,
    SchedulerClient,
    UpdateScheduleCommand,
} from "@aws-sdk/client-scheduler";
import {NOTIFICATONS} from "../module.js";
import {CreateScheduleCommandInput} from "@aws-sdk/client-scheduler/dist-types/commands/CreateScheduleCommand.js";
import _ from "lodash";

export type SchedulerParams = CreateScheduleCommandInput

type ScheduleProps = {
    eventBridge?: {
        eventBusArn: string,
        source: string,
        detailType: string,
    },
    lambda?: {
        lambdaArn: string,
    }
    roleArn: string,
    dlqArn?: string,
    cron?: string,
    startFrom?: string,
    endAt?: string,
    runAt?: string
    name: string,
    group?: string,
    description?: string,
    payload: Record<string, any>,
}

export class SchedulerBuilder {
    constructor(private props: ScheduleProps) {
    }

    setEventBridgeMessageProps(source: string, detailType: string) {
        if (!this.props.eventBridge) {
            this.props.eventBridge = {eventBusArn: '', detailType: '', source: ''}
        }
        this.props.eventBridge.source = source;
        this.props.eventBridge.detailType = detailType;
        return this;
    }
    setEventBusArn(arn: string) {
        if (!this.props.eventBridge) {
            this.props.eventBridge = {eventBusArn: '', detailType: '', source: ''}
        }
        this.props.eventBridge.eventBusArn = arn;
        return this;
    }
    setSchedulerExecutionRoleArn(arn: string) {
        this.props.roleArn = arn;
        return this;
    }
    setDlqArn(arn: string) {
        this.props.dlqArn = arn;
        return this;
    }
    setCron(expression: string) {
        this.props.cron = expression;
        return this;
    }
    setStartFrom(iso: string) {
        this.props.startFrom = iso;
        return this;
    }
    setEndAt(iso: string | undefined) {
        this.props.endAt = iso;
        return this;
    }
    setRunTime(time: string) {
        this.props.runAt = time;
        return this;
    }
    setDescription(description: string) {
        this.props.description = description;
        return this;
    }
    setName(name: string) {
        this.props.name = name;
        return this;
    }
    setGroup(group: string) {
        this.props.group = group;
        return this;
    }
    setLambda(arn: string) {
        if (!this.props.lambda) {
            this.props.lambda = {lambdaArn: ''}
        }
        this.props.lambda.lambdaArn = arn;
        return this;
    }
    setPayload(p: Record<string, any>) {
        this.props.payload = p;
        return this;
    }

    clone() {
        return new SchedulerBuilder(this.getProps())
    }

    getProps() {
        return _.cloneDeep(this.props)
    }

    /**
     * Converts the props of a schedule into a CreateScheduleCommandInput object.
     * @returns {CreateScheduleCommandInput} An object representing the input for the CreateScheduleCommand.
     */
    convertToCommandInput(): CreateScheduleCommandInput {
        const details = {
            ...this.props.payload,
            _meta: {
                scheduledTime: '<aws.scheduler.scheduled-time>',
                scheduleArn: '<aws.scheduler.schedule-arn>'
            }
        }

        const targetArn = this.props.lambda?.lambdaArn || this.props.eventBridge?.eventBusArn
        const runAt = this.props.runAt ? DateTime.fromISO(this.props.runAt) : null

        const result: CreateScheduleCommandInput = {
            ScheduleExpression: runAt ? `at(${runAt.toFormat("yyyy-MM-dd'T'HH:mm:ss")})` : this.props.cron,
            Name: this.props.name,
            GroupName: this.props.group,
            Description: this.props.description,
            FlexibleTimeWindow: {
                Mode: 'OFF'
            },
            StartDate: this.props.startFrom ? DateTime.fromISO(this.props.startFrom).toJSDate() : undefined,
            EndDate: this.props.endAt ? DateTime.fromISO(this.props.endAt).toJSDate() : undefined,
            ActionAfterCompletion: ActionAfterCompletion.DELETE,
            Target: {
                Arn: targetArn,
                RoleArn: this.props.roleArn,
                Input: JSON.stringify(details),
            },
        }
        if (this.props.eventBridge?.eventBusArn) {
            result.Target.EventBridgeParameters = {
                Source: NOTIFICATONS.source,
                DetailType: NOTIFICATONS.types.RUN_CONFIGURATION
            }
        }
        if (this.props.dlqArn) {
            result.Target.DeadLetterConfig = {Arn: this.props.dlqArn}
        }
        return result
    }
}


export async function createSchedule(params: SchedulerParams) {
    const client = new SchedulerClient({})
    const {ScheduleArn} = await client.send(new CreateScheduleCommand(params))
    return ScheduleArn as string
}

export async function updateSchedule(params: SchedulerParams) {
    const client = new SchedulerClient({})
    await client.send(new UpdateScheduleCommand(params))
}

/**
 * Retrieves the schedule for a specified ID and optional, group. Returns null, if not found.
 *
 * @param {string} id - The ID of the schedule to retrieve.
 * @param {string} [group] - Optional. The group name of the schedule.
 * @return {Promise<GetScheduleCommandOutput>} - A promise that resolves with the retrieved schedule object or null if not found.
 * @throws {Error} - An error occurred during the retrieval process.
 */
export async function getSchedule(id: string, group?: string): Promise<GetScheduleCommandOutput> {
    const client = new SchedulerClient({})
    try {
        return await client.send(new GetScheduleCommand({Name: id, GroupName: group}))
    } catch (e: any) {
        if (e.name === 'ResourceNotFoundException') {
            return null
        }
        throw e;
    }
}

/**
 * Deletes a schedule.
 * @param {string} id - The ID of the schedule to delete.
 * @param {string} [group] - The group name of the schedule to delete.
 * @return {Promise<boolean>} - A promise that resolves to true if the schedule was deleted successfully, or false if the schedule was not found. Throws an error if there was an issue
 * deleting the schedule.
 */
export async function deleteSchedule(id: string, group?: string): Promise<boolean> {
    const client = new SchedulerClient({})
    try {
        await client.send(new DeleteScheduleCommand({Name: id, GroupName: group}))
        return true
    } catch (e: any) {
        if (e.name === 'ResourceNotFoundException') {
            return false
        }
        throw e;
    }
}
