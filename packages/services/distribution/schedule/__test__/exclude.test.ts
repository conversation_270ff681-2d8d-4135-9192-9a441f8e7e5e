import { RecurringSettings, getNextDate } from "./testbase.js";


describe(('Exclude [Daily]'), () => {
    const config: RecurringSettings = {
        "fromDate": "2025-03-22",
        "toDate": "2026-03-31",
        "fromTime": "06:30",
        "period": "Daily",
        "withExclude": true,
        "exclude": {
            "daysOfWeek": [2, 5], // 火曜日と金曜日
            "dates": ["2025-03-25", "2025-03-26", "2025-03-27", "2025-03-28", "2025-03-29", "2025-03-30", "2025-03-31"]
        }
    }
    it('1回目の配信 - 1', async () => {
        const nextDate = getNextDate(config, '2025-03-23T23:59:59+09:00');
        expect(nextDate).toBe('2025-03-24');
    });
    it('1回目の配信 - 2', async () => {
        const nextDate = getNextDate(config, '2025-03-24T00:00:00+09:00');
        expect(nextDate).toBe('2025-03-24');
    });
    it('2回目の配信（指定日と2025/4/1(火)は除外される）', async () => {
        const nextDate = getNextDate(config, '2025-03-24T06:31:00+09:00');
        expect(nextDate).toBe('2025-04-02');
    });
    it('3回目の配信', async () => {
        const nextDate = getNextDate(config, '2025-04-02T06:31:00+09:00');
        expect(nextDate).toBe('2025-04-03');
    });
    it('4回目の配信（2025/4/4(金)は除外される）', async () => {
        const nextDate = getNextDate(config, '2025-04-03T06:31:00+09:00');
        expect(nextDate).toBe('2025-04-05');
    });
})

describe(('Exclude [Weekly]'), () => {
    const config: RecurringSettings = {
        "fromDate": "2025-03-22",
        "toDate": "2026-03-31",
        "fromTime": "06:30",
        "period": "Weekly",
        "withExclude": true,
        "daysOfWeek": [2, 5], // 火曜日と金曜日
        "exclude": {
            "daysOfWeek": [],
            "dates": ["2025-03-25"]
        }
    }
    it('1回目の配信 - 1 - 2025/3/25は除外', async () => {
        const nextDate = getNextDate(config, '2025-03-21T23:59:59+09:00');
        expect(nextDate).toBe('2025-03-28');
    });
    it('1回目の配信 - 2 - 2025/3/25は除外', async () => {
        const nextDate = getNextDate(config, '2025-03-25T00:00:00+09:00');
        expect(nextDate).toBe('2025-03-28');
    });
    it('2回目の配信', async () => {
        const nextDate = getNextDate(config, '2025-03-28T06:31:00+09:00');
        expect(nextDate).toBe('2025-04-01');
    });
    it('2回目の配信', async () => {
        const nextDate = getNextDate(config, '2025-03-28T06:31:00+09:00');
        expect(nextDate).toBe('2025-04-01');
    });
    it('火曜日は除外 - 1', async () => {
        const config: RecurringSettings = {
            "fromDate": "2025-03-22",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "Weekly",
            "withExclude": true,
            "daysOfWeek": [2, 5], // 火曜日と金曜日
            "exclude": {
                "daysOfWeek": [2], // 火曜日
                "dates": []
            }
        }
        const nextDate = getNextDate(config, '2025-03-21T06:31:00+09:00');
        expect(nextDate).toBe('2025-03-28');
    });
    it('火曜日は除外 - 2', async () => {
        const config: RecurringSettings = {
            "fromDate": "2025-03-22",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "Weekly",
            "withExclude": true,
            "daysOfWeek": [2, 5], // 火曜日と金曜日
            "exclude": {
                "daysOfWeek": [2], // 火曜日
                "dates": []
            }
        }
        const nextDate = getNextDate(config, '2025-03-28T06:31:00+09:00');
        expect(nextDate).toBe('2025-04-04');
    });
})

describe(('Exclude [Monthly]'), () => {
    const config: RecurringSettings = {
        "fromDate": "2025-03-22",
        "toDate": "2026-03-31",
        "fromTime": "06:30",
        "period": "Monthly",
        "withExclude": true,
        "daysOfMonth": [
            5,
            10,
            15,
            20,
            25,
            30
        ],
        "exclude": {
            "daysOfWeek": [3, 7],
            "dates": ["2025-05-10", "2025-05-20", "2025-05-25", "2025-06-05", "2025-06-20"]
        }
    }
    it('次の日曜日（2025/04/20）は除外', async () => {
        const nextDate = getNextDate(config, '2025-04-19T23:59:59+09:00');
        expect(nextDate).toBe('2025-04-25');
    });
    it('次の指定日（2025/05/10）は除外', async () => {
        const nextDate = getNextDate(config, '2025-05-05T23:59:59+09:00');
        expect(nextDate).toBe('2025-05-15');
    });
    it('次の日曜日（2025/06/15）と次の指定日（2025/06/20）と次の水曜日（2025/06/25）は除外', async () => {
        const nextDate = getNextDate(config, '2025-06-14T23:59:59+09:00');
        expect(nextDate).toBe('2025-06-30');
    });
})

describe(('Exclude [LastDay]'), () => {
    describe(('Period: LastDay'), () => {
        const config: RecurringSettings = {
            "fromDate": "2025-03-22",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "LastDay",
            "withExclude": true,
            "exclude": {
                "daysOfWeek": [3, 5],
                "dates": ["2025-03-31", "2025-06-30"]
            }
        }
        it('指定日(2025-03-31), 水曜日(2025-04-30)は除外', async () => {
            const nextDate = getNextDate(config, '2025-03-21');
            expect(nextDate).toBe('2025-05-31');
        });
        it('指定日(2025-06-30)は除外', async () => {
            const nextDate = getNextDate(config, '2025-06-29T00:00:00+09:00');
            expect(nextDate).toBe('2025-07-31');
        });
    })
})

describe(('Exclude [Custom]'), () => {
    describe(('skip days'), () => {
        const config: RecurringSettings = {
            "fromDate": "2025-04-01",
            "toDate": "2026-05-31",
            "fromTime": "06:30",
            "period": "Custom",
            "withExclude": true,
            "daysOfWeek": [],
            "exclude": {
                "daysOfWeek": [2, 4],
                "dates": ["2025-04-09"]
            },
            custom: {
                type: 'skip',
                skip: {
                    period: 'days',
                    length: 2
                }
            }
        }
        it('2025-04-01 -> 2025-04-05', async () => {
            const nextDate = getNextDate(config, '2025-04-01T06:29:59');
            expect(nextDate).toEqual('2025-04-05');
        });
        it('2025-04-05 -> 2025-04-07', async () => {
            const nextDate = getNextDate(config, '2025-04-05T06:30:00');
            expect(nextDate).toEqual('2025-04-07');
        });
        it('2025-04-07 -> 2025-04-11', async () => {
            const nextDate = getNextDate(config, '2025-04-05T06:30:00');
            expect(nextDate).toEqual('2025-04-07');
        });
    })
    describe(('skip weeks'), () => {
        const config: RecurringSettings = {
            "fromDate": "2025-04-01",
            "toDate": "2026-05-31",
            "fromTime": "06:30",
            "period": "Custom",
            "withExclude": true,
            "daysOfWeek": [],
            "exclude": {
                "daysOfWeek": [],
                "dates": ["2025-04-08"]
            },
            custom: {
                type: 'skip',
                skip: {
                    period: 'weeks',
                    length: 2
                }
            }
        }
        it('2025-04-01 -> 2025-04-01', async () => {
            const nextDate = getNextDate(config, '2025-04-01T06:29:59');
            expect(nextDate).toEqual('2025-04-01');
        });
        it('2025-04-01 -> 2025-04-15（2025-04-08は除外）', async () => {
            const nextDate = getNextDate(config, '2025-04-01T06:30:00');
            expect(nextDate).toEqual('2025-04-15');
        });
        it('2025-04-01 -> return null（火曜日は除外）', async () => {
            const config: RecurringSettings = {
                "fromDate": "2025-04-01",
                "toDate": "2026-05-31",
                "fromTime": "06:30",
                "period": "Custom",
                "withExclude": true,
                "daysOfWeek": [],
                "exclude": {
                    "daysOfWeek": [2],
                    "dates": []
                },
                custom: {
                    type: 'skip',
                    skip: {
                        period: 'weeks',
                        length: 2
                    }
                }
            }
            const nextDate = getNextDate(config, '2025-04-01T06:30:00');
            expect(nextDate).toBeNull();
        });
    })
    describe(('skip months'), () => {
        const config: RecurringSettings = {
            "fromDate": "2025-04-01",
            "toDate": "2026-05-31",
            "fromTime": "06:30",
            "period": "Custom",
            "withExclude": true,
            "daysOfWeek": [],
            "exclude": {
                "daysOfWeek": [5],
                "dates": ["2025-12-01"]
            },
            custom: {
                type: 'skip',
                skip: {
                    period: 'months',
                    length: 2
                }
            }
        }
        it('2025-04-01 -> 2025-04-01', async () => {
            const nextDate = getNextDate(config, '2025-04-01T06:29:59');
            expect(nextDate).toEqual('2025-04-01');
        });
        it('2025-04-01 -> 2025-06-01', async () => {
            const nextDate = getNextDate(config, '2025-04-01T06:30:00');
            expect(nextDate).toEqual('2025-06-01');
        });
        it('2025-06-01 -> 2025-10-01（金曜日（2025-08-01）は除外）', async () => {
            const nextDate = getNextDate(config, '2025-06-01T06:30:00');
            expect(nextDate).toEqual('2025-10-01');
        });
        it('2025-10-01 -> 2026-02-01（2025-12-01は除外）', async () => {
            const nextDate = getNextDate(config, '2025-10-01T06:30:00');
            expect(nextDate).toEqual('2026-02-01');
        });
    })
})