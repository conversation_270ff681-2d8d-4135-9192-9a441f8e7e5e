import { RecurringSettings, getNextDate } from "./testbase.js";


describe(('Period: Monthly'), () => {
    it('1回目の配信 - 1', async () => {
        const config: RecurringSettings = {
            "fromDate": "2025-03-22",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "Monthly",
            "withExclude": false,
            "daysOfMonth": [
                3,
                12
            ],
            "exclude": {
                "daysOfWeek": [],
                "dates": []
            }
        }
        const nextDate = getNextDate(config, '2025-03-21');
        expect(nextDate).toBe('2025-04-03');
    });

    it('1回目の配信 - 2', async () => {
        const config: RecurringSettings = {
            "fromDate": "2025-03-22",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "Monthly",
            "withExclude": false,
            "daysOfMonth": [
                3,
                12
            ],
            "exclude": {
                "daysOfWeek": [],
                "dates": []
            }
        }
        const nextDate = getNextDate(config, '2025-03-21T23:59:59+09:00');
        expect(nextDate).toBe('2025-04-03');
    });
    it('2回目の配信 - 時間の境界1', async () => {
        const config: RecurringSettings = {
            "fromDate": "2025-03-22",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "Monthly",
            "withExclude": false,
            "daysOfMonth": [
                3,
                12
            ],
            "exclude": {
                "daysOfWeek": [],
                "dates": []
            }
        }
        const nextDate = getNextDate(config, '2025-04-03T06:29:59+09:00');
        expect(nextDate).toBe('2025-04-03');
    });
    it('2回目の配信 - 時間の境界2', async () => {
        const config: RecurringSettings = {
            "fromDate": "2025-03-22",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "Monthly",
            "withExclude": false,
            "daysOfMonth": [
                3,
                12
            ],
            "exclude": {
                "daysOfWeek": [],
                "dates": []
            }
        }
        const nextDate = getNextDate(config, '2025-04-03T06:30:00+09:00');
        expect(nextDate).toBe('2025-04-12');
    });
    it('2回目の配信 - 時間の境界3', async () => {
        const config: RecurringSettings = {
            "fromDate": "2025-03-22",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "Monthly",
            "withExclude": false,
            "daysOfMonth": [
                3,
                12
            ],
            "exclude": {
                "daysOfWeek": [],
                "dates": []
            }
        }
        const nextDate = getNextDate(config, '2025-04-03T06:31:00+09:00');
        expect(nextDate).toBe('2025-04-12');
    });
    it('2回目の配信 - 1回目の翌日から', async () => {
        const config: RecurringSettings = {
            "fromDate": "2025-03-22",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "Monthly",
            "withExclude": false,
            "daysOfMonth": [
                3,
                12
            ],
            "exclude": {
                "daysOfWeek": [],
                "dates": []
            }
        }
        const nextDate = getNextDate(config, '2025-04-04T00:00:00+09:00');
        expect(nextDate).toBe('2025-04-12');
    });
    it('3回目(初回から一ヶ月後)の配信', async () => {
        const config: RecurringSettings = {
            "fromDate": "2025-03-22",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "Monthly",
            "withExclude": false,
            "daysOfMonth": [
                3,
                12
            ],
            "exclude": {
                "daysOfWeek": [],
                "dates": []
            }
        }
        const nextDate = getNextDate(config, '2025-04-13T00:00:00+09:00');
        expect(nextDate).toBe('2025-05-03');
    });
    it('4回目(初回から一ヶ月後)の配信', async () => {
        const config: RecurringSettings = {
            "fromDate": "2025-03-22",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "Monthly",
            "withExclude": false,
            "daysOfMonth": [
                3,
                12
            ],
            "exclude": {
                "daysOfWeek": [],
                "dates": []
            }
        }
        const nextDate = getNextDate(config, '2025-05-04T00:00:00+09:00');
        expect(nextDate).toBe('2025-05-12');
    });
    it('31日が存在する月', async () => {
        const config: RecurringSettings = {
            "fromDate": "2025-03-22",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "Monthly",
            "withExclude": false,
            "daysOfMonth": [
                31
            ],
            "exclude": {
                "daysOfWeek": [],
                "dates": []
            }
        }
        const nextDate = getNextDate(config, '2025-03-21T00:00:00+09:00');
        expect(nextDate).toBe('2025-03-31');
    });
    it('31日が存在しない月', async () => {
        const config: RecurringSettings = {
            "fromDate": "2025-03-22",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "Monthly",
            "withExclude": false,
            "daysOfMonth": [
                31
            ],
            "exclude": {
                "daysOfWeek": [],
                "dates": []
            }
        }
        const nextDate = getNextDate(config, '2025-04-21T00:00:00+09:00');
        expect(nextDate).toBe('2025-04-30');
    });

    // // 以下を実行すると結果が返ってきません
    it('存在しない日付を指定した場合（0未満）', async () => {
        const config: RecurringSettings = {
            "fromDate": "2025-03-22",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "Monthly",
            "withExclude": false,
            "daysOfMonth": [
                -1
            ],
            "exclude": {
                "daysOfWeek": [],
                "dates": []
            }
        }
        expect(() => getNextDate(config, '2025-03-30T00:00:00+09:00')).toThrow(/Invalid day of month value: -1/);
    });
    it('存在しない日付を指定した場合（0）', async () => {
        const config: RecurringSettings = {
            "fromDate": "2025-03-22",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "Monthly",
            "withExclude": false,
            "daysOfMonth": [
                0
            ],
            "exclude": {
                "daysOfWeek": [],
                "dates": []
            }
        }
        expect(() => getNextDate(config, '2025-03-30T00:00:00+09:00')).toThrow(/Invalid day of month value: 0/);
    });
    it('存在しない日付を指定した場合（32）', async () => {
        const config: RecurringSettings = {
            "fromDate": "2025-03-22",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "Monthly",
            "withExclude": false,
            "daysOfMonth": [
                32
            ],
            "exclude": {
                "daysOfWeek": [],
                "dates": []
            }
        }
        expect(() => getNextDate(config, '2025-03-30T00:00:00+09:00')).toThrow(/Invalid day of month value: 32/);
    });
})