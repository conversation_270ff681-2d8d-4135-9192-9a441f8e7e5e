import { RecurringSettings, getNextDate } from "./testbase.js";


describe(('Work period'), () => {
    describe(('Before work period start'), () => {
        it('should return some date', async () => {
            const config: RecurringSettings = {
                "fromDate": "2025-04-01",
                "toDate": "2026-03-31",
                "fromTime": "06:30",
                "period": "Weekly",
                "withExclude": true,
                "daysOfWeek": [
                    3,
                    6
                ],
                "exclude": {
                    "daysOfWeek": [],
                    "dates": [
                        "2025-05-03",
                        "2025-05-04",
                        "2025-05-05",
                        "2026-01-01",
                        "2026-01-02",
                        "2026-01-03"
                    ]
                }
            }
            const nextDate = getNextDate(config, '2025-03-01');
            expect(nextDate).not.toBeNull();
        });
    })
    describe(('After work period end'), () => {
        const config: RecurringSettings = {
            "fromDate": "2025-04-01",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "Weekly",
            "withExclude": true,
            "daysOfWeek": [
                3,
                6
            ],
            "exclude": {
                "daysOfWeek": [],
                "dates": [
                    "2025-05-03",
                    "2025-05-04",
                    "2025-05-05",
                    "2026-01-01",
                    "2026-01-02",
                    "2026-01-03"
                ]
            }
        }
        it('should return null', async () => {
            const nextDate = getNextDate(config, '2026-03-31T07:00:00');
            expect(nextDate).toBeNull();
        });
    })

    describe('In work period', () => {
        const config: RecurringSettings = {
            "fromDate": "2025-04-01",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "Weekly",
            "withExclude": true,
            "daysOfWeek": [
                3,
                6
            ],
            "exclude": {
                "daysOfWeek": [],
                "dates": [
                    "2025-05-03",
                    "2025-05-04",
                    "2025-05-05",
                    "2026-01-01",
                    "2026-01-02",
                    "2026-01-03"
                ]
            }
        }
        it('should return not null', async () => {
            const nextDate = getNextDate(config, '2025-04-01T:06:30:00');
            expect(nextDate).not.toBeNull();
        });
    })

    describe('In the moment of work period start', () => {
        const config: RecurringSettings = {
            "fromDate": "2025-04-01",
            "toDate": "2025-04-02",
            "fromTime": "06:30",
            "period": "Weekly",
            "withExclude": false,
            "daysOfWeek": [2],
            "exclude": {
                "daysOfWeek": [],
                "dates": [
                ]
            }
        }
        it('should return null', async () => {
            const nextDate = getNextDate(config, '2025-04-01T06:30:00');
            expect(nextDate).toBeNull();
        });
    })

    describe('Around the period start', () => {
        const config: RecurringSettings = {
            "fromDate": "2025-04-01",
            "toDate": "2025-04-02",
            "fromTime": "06:30",
            "period": "Daily",
            "withExclude": false,
            "daysOfWeek": [],
            "exclude": {
                "daysOfWeek": [],
                "dates": [
                ]
            }
        }
        it('in 6:30 it should be 04-02', async () => {
            const nextDate = getNextDate(config, '2025-04-01T06:30:00');
            expect(nextDate).toEqual('2025-04-02');
        });
        it('in 6:29 it should should be 04-01', async () => {
            const nextDate = getNextDate(config, '2025-04-01T06:29:00');
            expect(nextDate).toEqual('2025-04-01');
        });

    })

    describe('Around the period end', () => {
        const config: RecurringSettings = {
            "fromDate": "2025-04-01",
            "toDate": "2025-04-02",
            "fromTime": "06:30",
            "period": "Daily",
            "withExclude": false,
            "daysOfWeek": [],
            "exclude": {
                "daysOfWeek": [],
                "dates": [
                ]
            }
        }
        it('at 04-02 6:31 it should be null', async () => {
            const nextDate = getNextDate(config, '2025-04-02T06:31:00');
            expect(nextDate).toBeNull();
        });
        it('at 04-02 6:30 it should be null', async () => {
            const nextDate = getNextDate(config, '2025-04-02T06:30:00');
            expect(nextDate).toBeNull();
        });
        it('at 04-02 6:29 it should be 04-02', async () => {
            const nextDate = getNextDate(config, '2025-04-02T06:29:00');
            expect(nextDate).toEqual('2025-04-02');
        });

    })

    describe('When no hits in the period', () => {
        const config: RecurringSettings = {
            "fromDate": "2025-04-01",
            "toDate": "2025-04-05",
            "fromTime": "06:30",
            "period": "Weekly",
            "withExclude": false,
            "daysOfWeek": [7],
            "exclude": {
                "daysOfWeek": [],
                "dates": [
                ]
            }
        }
        it('on 04-01 it should be null', async () => {
            const nextDate = getNextDate(config, '2025-03-31');
            expect(nextDate).toBeNull();
        });
        it('on 04-01 it should be null', async () => {
            const nextDate = getNextDate(config, '2025-04-01');
            expect(nextDate).toBeNull();
        });
        it('at 04-02 6:30 it should be null', async () => {
            const nextDate = getNextDate(config, '2025-04-05');
            expect(nextDate).toBeNull();
        });
        it('at 04-02 6:29 it should be 04-02', async () => {
            const nextDate = getNextDate(config, '2025-04-06');
            expect(nextDate).toBeNull();
        });

    })
})