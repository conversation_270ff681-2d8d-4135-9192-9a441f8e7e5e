import { RecurringSettings, getNextDate } from "./testbase.js";


describe(('Period: Daily'), () => {
    const config: RecurringSettings = {
        "fromDate": "2025-03-22",
        "toDate": "2026-03-31",
        "fromTime": "06:30",
        "period": "Daily",
        "withExclude": false,
        "exclude": {
            "daysOfWeek": [],
            "dates": []
        }
    }
    it('1回目の配信 - 1', async () => {
        const nextDate = getNextDate(config, '2025-03-21');
        expect(nextDate).toBe('2025-03-22');
    });
    it('1回目の配信 - 2 ', async () => {
        const nextDate = getNextDate(config, '2025-03-21T00:00:00+09:00');
        expect(nextDate).toBe('2025-03-22');
    });
    it('2回目の配信 - 境界1', async () => {
        const nextDate = getNextDate(config, '2025-03-22T06:29:00+09:00');
        expect(nextDate).toBe('2025-03-22');
    });
    it('2回目の配信 - 境界2 ', async () => {
        const nextDate = getNextDate(config, '2025-03-22T06:30:00+09:00');
        expect(nextDate).toBe('2025-03-23');
    });
    it('2回目の配信 - 境界3 ', async () => {
        const nextDate = getNextDate(config, '2025-03-22T06:31:00+09:00');
        expect(nextDate).toBe('2025-03-23');
    });
})