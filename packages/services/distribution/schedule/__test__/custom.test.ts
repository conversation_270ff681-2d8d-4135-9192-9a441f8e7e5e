import { RecurringSettings, getNextDate } from "./testbase.js";


describe(('Custom [dates]'), () => {
    describe(('Selected dates inside period'), () => {
        const config: RecurringSettings = {
            "fromDate": "2025-04-01",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "Custom",
            "withExclude": false,
            "daysOfWeek": [],
            custom: {
                type: 'dates',
                dates: [
                    "2025-05-04",
                    "2025-05-03"
                ]
            }

        }
        it('should return 2025-05-03', async () => {
            const nextDate = getNextDate(config, '2025-05-01');
            expect(nextDate).toEqual('2025-05-03');
        });
        it('should return 2025-05-04', async () => {
            const nextDate = getNextDate(config, '2025-05-03T06:30:00');
            expect(nextDate).toEqual('2025-05-04');
        });
    })
    describe(('Selected dates have one outside period'), () => {
        const config: RecurringSettings = {
            "fromDate": "2025-04-01",
            "toDate": "2026-03-31",
            "fromTime": "06:30",
            "period": "Custom",
            "withExclude": false,
            "daysOfWeek": [],
            custom: {
                type: 'dates',
                dates: [
                    "2025-02-04",
                    "2027-05-03"
                ]
            }
        }
        it('from outside should return null', async () => {
            const nextDate = getNextDate(config, '2025-01-01');
            expect(nextDate).toBeNull();
        });
        it('from inside should return null', async () => {
            const nextDate = getNextDate(config, '2025-05-01');
            expect(nextDate).toBeNull();
        });
    })
})

describe(('Custom [numberedDayOfWeek]'), () => {
    describe(('Selected dates inside period'), () => {
        const config: RecurringSettings = {
            "fromDate": "2025-04-01",
            "toDate": "2025-05-31",
            "fromTime": "06:30",
            "period": "Custom",
            "withExclude": false,
            "daysOfWeek": [],
            custom: {
                type: 'numberedDayOfWeek',
                numberedDayOfWeek: [
                    { dayOfWeek: 2, number: 1 },
                    { dayOfWeek: 5, number: 2 }]
            }
        }
        it('03-01 -> 04-01', async () => {
            const nextDate = getNextDate(config, '2025-03-01');
            expect(nextDate).toEqual('2025-04-01');
        });
        it('04-01 -> 04-11', async () => {
            const nextDate = getNextDate(config, '2025-04-01T06:30:00');
            expect(nextDate).toEqual('2025-04-11');
        });
        it('04-11 -> 05-06', async () => {
            const nextDate = getNextDate(config, '2025-04-11T06:30:00');
            expect(nextDate).toEqual('2025-05-06');
        });
        it('05-06 -> 05-09', async () => {
            const nextDate = getNextDate(config, '2025-05-06T06:30:00');
            expect(nextDate).toEqual('2025-05-09');
        });
    })
})

describe(('Custom [skip][days]'), () => {
    describe(('Selected dates inside period'), () => {
        const config: RecurringSettings = {
            "fromDate": "2025-04-01",
            "toDate": "2026-05-31",
            "fromTime": "06:30",
            "period": "Custom",
            "withExclude": false,
            "daysOfWeek": [],
            custom: {
                type: 'skip',
                skip: {
                    period: 'days',
                    length: 2
                }
            }
        }
        it('2025-04-01 -> 2025-04-01', async () => {
            const nextDate = getNextDate(config, '2025-04-01T06:29:59');
            expect(nextDate).toEqual('2025-04-01');
        });
        it('2025-04-01 -> 2025-04-03', async () => {
            const nextDate = getNextDate(config, '2025-04-01T06:30:00');
            expect(nextDate).toEqual('2025-04-03');
        });
        it('2025-04-03 -> 2025-04-05', async () => {
            const nextDate = getNextDate(config, '2025-04-03T06:30:00');
            expect(nextDate).toEqual('2025-04-05');
        });
    })
    describe(('Selected dates have one outside period'), () => {
        const config: RecurringSettings = {
            "fromDate": "2025-04-01",
            "toDate": "2026-05-31",
            "fromTime": "06:30",
            "period": "Custom",
            "withExclude": false,
            "daysOfWeek": [],
            custom: {
                type: 'skip',
                skip: {
                    period: 'days',
                    length: 2
                }
            }
        }
        it('from outside should return 2025-04-01', async () => {
            const nextDate = getNextDate(config, '2025-03-01T00:00:00');
            expect(nextDate).toEqual('2025-04-01');
        });
        it('from outside should return null', async () => {
            const nextDate = getNextDate(config, '2026-06-01T00:00:00');
            expect(nextDate).toBeNull();
        });
    })
})

describe(('Custom [skip][weeks]'), () => {
    describe(('Selected dates inside period'), () => {
        const config: RecurringSettings = {
            "fromDate": "2025-04-01",
            "toDate": "2026-05-31",
            "fromTime": "06:30",
            "period": "Custom",
            "withExclude": false,
            "daysOfWeek": [],
            custom: {
                type: 'skip',
                skip: {
                    period: 'weeks',
                    length: 2
                }
            }
        }
        it('2025-04-01 -> 2025-04-01', async () => {
            const nextDate = getNextDate(config, '2025-04-01T06:29:59');
            expect(nextDate).toEqual('2025-04-01');
        });
        it('2025-04-01 -> 2025-04-15', async () => {
            const nextDate = getNextDate(config, '2025-04-01T06:30:00');
            expect(nextDate).toEqual('2025-04-15');
        });
        it('2025-04-15 -> 2025-04-29', async () => {
            const nextDate = getNextDate(config, '2025-04-15T06:30:00');
            expect(nextDate).toEqual('2025-04-29');
        });
    })
    describe(('Selected dates have one outside period'), () => {
        const config: RecurringSettings = {
            "fromDate": "2025-04-01",
            "toDate": "2026-05-31",
            "fromTime": "06:30",
            "period": "Custom",
            "withExclude": false,
            "daysOfWeek": [],
            custom: {
                type: 'skip',
                skip: {
                    period: 'weeks',
                    length: 2
                }
            }
        }
        it('from outside should return 2025-04-01', async () => {
            const nextDate = getNextDate(config, '2025-03-01T00:00:00');
            expect(nextDate).toEqual('2025-04-01');
        });
        it('from outside should return null', async () => {
            const nextDate = getNextDate(config, '2026-06-01T00:00:00');
            expect(nextDate).toBeNull();
        });
    })
})

describe(('Custom [skip][months]'), () => {
    describe(('Selected dates inside period'), () => {
        const config: RecurringSettings = {
            "fromDate": "2025-04-01",
            "toDate": "2026-05-31",
            "fromTime": "06:30",
            "period": "Custom",
            "withExclude": false,
            "daysOfWeek": [],
            custom: {
                type: 'skip',
                skip: {
                    period: 'months',
                    length: 2
                }
            }
        }
        it('2025-04-01 -> 2025-04-01', async () => {
            const nextDate = getNextDate(config, '2025-04-01T06:29:59');
            expect(nextDate).toEqual('2025-04-01');
        });
        it('2025-04-01 -> 2025-06-01', async () => {
            const nextDate = getNextDate(config, '2025-04-01T06:30:00');
            expect(nextDate).toEqual('2025-06-01');
        });
        it('2025-06-01 -> 2025-08-01', async () => {
            const nextDate = getNextDate(config, '2025-06-01T06:30:00');
            expect(nextDate).toEqual('2025-08-01');
        });
    })
    describe(('Selected dates have one outside period'), () => {
        const config: RecurringSettings = {
            "fromDate": "2025-04-01",
            "toDate": "2026-05-31",
            "fromTime": "06:30",
            "period": "Custom",
            "withExclude": false,
            "daysOfWeek": [],
            custom: {
                type: 'skip',
                skip: {
                    period: 'months',
                    length: 2
                }
            }
        }
        it('from outside should return 2025-04-01', async () => {
            const nextDate = getNextDate(config, '2025-03-01T00:00:00');
            expect(nextDate).toEqual('2025-04-01');
        });
        it('from outside should return null', async () => {
            const nextDate = getNextDate(config, '2026-06-01T00:00:00');
            expect(nextDate).toBeNull();
        });
    })
})