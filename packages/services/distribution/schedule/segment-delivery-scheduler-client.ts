import {DateTime} from "luxon";
import {
    ActionAfterCompletion,
    CreateScheduleCommand,
    DeleteScheduleCommand,
    GetScheduleCommand,
    SchedulerClient,
    UpdateScheduleCommand,
} from "@aws-sdk/client-scheduler";
import {lambdaEnv} from "../../common/lambda-env.js";
import {ENV, NOTIFICATONS} from "../module.js";
import {CreateScheduleCommandInput} from "@aws-sdk/client-scheduler/dist-types/commands/CreateScheduleCommand.js";
import {DistributionType} from "@oss/shared/lib/distribution/constants.js";
import _ from "lodash";
import {createSchedule, deleteSchedule, getSchedule, updateSchedule} from "./aws-scheduler-client.js";
import console from "console";

type SchedulerParams = CreateScheduleCommandInput

export function getPostponedScheduler() {
    const groupName = `${lambdaEnv.getEnv(ENV.ENV_NAME)}-distribution-${DistributionType.POSTPONED}`
    return new SegmentDeliveryScheduler(groupName)
}

export function getRepeatingScheduler() {
    const groupName = `${lambdaEnv.getEnv(ENV.ENV_NAME)}-distribution-${DistributionType.REPEATING}`
    return new SegmentDeliveryScheduler(groupName)
}

export class SegmentDeliveryScheduler {
    constructor(private group: string) {
    }

    async create(configurationId: string, date: string, time: string, opts: {
        description?: string,
    } = {}) {
        const dt = DateTime.fromISO(`${date}T${time}`).toUTC()
        const schedule = buildSchedule(configurationId, dt, {
            RunConfiguration: {
                id: configurationId
            }
        })
        schedule.GroupName = this.group
        schedule.Description = opts.description
        return createSchedule(schedule);
    }

    async update(configurationId: string, date: string, time: string, opts: {
        description?: string,
    } = {}) {
        const dt = DateTime.fromISO(`${date}T${time}`).toUTC()
        const currentSchedule = await getSchedule(configurationId, this.group)
        const base = buildSchedule(configurationId, dt, createRunConfigurationEvent(configurationId))
        base.GroupName = this.group
        base.Description = opts.description || currentSchedule?.Description
        if (currentSchedule) {
            await updateSchedule(base)
            return currentSchedule.Arn
        } else {
            return createSchedule(base);
        }
    }

    async remove(configurationId: string) {
        return deleteSchedule(configurationId, this.group)
    }

}

function createRunConfigurationEvent(configurationId: string) {
    return {
        RunConfiguration: {
            configurationId
        }
    }
}

function buildSchedule(id: string, daytime: DateTime, payload: Record<string, any>): CreateScheduleCommandInput {
    const targetBusArn = lambdaEnv.getEnv(ENV.EVENTBUS_DISTRIBUTIONS_ARN)
    const schedulerRoleArn = lambdaEnv.getEnv(ENV.SCHEDULER_ROLE_ARN)
    const details = {
        ...payload,
        _meta: {
            scheduledTime: '<aws.scheduler.scheduled-time>',
            scheduleArn: '<aws.scheduler.schedule-arn>'
        }
    }
    const dlqArn = lambdaEnv.getEnv(ENV.SYSTEM_DLQ_ARN);
    const result: CreateScheduleCommandInput = {
        ScheduleExpression: `at(${daytime.toFormat("yyyy-MM-dd'T'HH:mm:ss")})`,
        Name: id,
        Description: `[${id}] distribution`,
        FlexibleTimeWindow: {
            Mode: 'OFF'
        },
        ActionAfterCompletion: ActionAfterCompletion.DELETE,
        Target: {
            Arn: targetBusArn,
            RoleArn: schedulerRoleArn,
            Input: JSON.stringify(details),
            EventBridgeParameters: {
                Source: NOTIFICATONS.source,
                DetailType: NOTIFICATONS.types.RUN_CONFIGURATION
            },
        },
    }
    if (dlqArn) {
        result.Target.DeadLetterConfig = {Arn: dlqArn}
    }
    return result
}
