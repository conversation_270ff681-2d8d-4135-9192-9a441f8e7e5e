/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import {DateTime, Duration} from 'luxon';
import * as _ from 'lodash';

export type DateValue = string | DateTime;

export interface NextDayCalculator {
    getNextDay(base: DateValue): string | null;

    isDateSatisfied(date: DateValue): boolean;
}

export const getDateTime = (val: DateValue) => {
    if (DateTime.isDateTime(val)) {
        return val;
    }
    const parsed = DateTime.fromISO(val);
    if (parsed.isValid) {
        return parsed;
    }
    throw 'Unparseable base date value ' + val;
};
const formatResult = (val: DateValue) => {
    return getDateTime(val).toFormat('yyyyLLdd');
};

export class DailyCalc implements NextDayCalculator {
    getNextDay(base: DateValue): string {
        const result = getDateTime(base).plus(Duration.fromObject({days: 1}));
        return formatResult(result);
    }

    isDateSatisfied(date: DateValue): boolean {
        return true;
    }
}

export type WeeklyCalcParams = {
    daysOfWeek: number[];
};

export class WeeklyCalc implements NextDayCalculator {
    readonly daysOfWeek: number[];

    constructor(params: number[]) {
        if (params.length === 0) {
            throw 'Can not perform weekly schedule calculation without at least 1 day specified';
        }
        params.forEach((v) => {
            if (v < 1 || v > 7) {
                throw 'Invalid day of week value: ' + v;
            }
        });
        this.daysOfWeek = params;
    }

    getNextDay(base: DateValue): string {
        const baseDate = getDateTime(base);
        const baseDayOfWeek = baseDate.weekday;
        let nextWeek = true;
        let nextWeekDay = Math.min(...this.daysOfWeek);
        const nextDays = this.daysOfWeek.filter((v) => v > baseDayOfWeek);
        if (nextDays.length > 0) {
            nextWeek = false;
            nextWeekDay = Math.min(...nextDays);
        }
        let result = baseDate;
        if (nextWeek) {
            result = result.plus({week: 1});
        }
        result = result.set({weekday: nextWeekDay});
        return formatResult(result);
    }

    isDateSatisfied(date: DateValue): boolean {
        const dateVal = getDateTime(date);
        return this.daysOfWeek.includes(dateVal.weekday);
    }
}

export type MonthlyCalcParams = {
    dates: [];
};

export class MonthlyCalc implements NextDayCalculator {
    readonly days: number[];

    constructor(dates: number[]) {
        if (dates.length === 0) {
            throw 'Can not perform monthly schedule calculation without at least 1 date specified';
        }
        dates.forEach((v) => {
            if (v < 1 || v > 31) {
                throw 'Invalid day of month value: ' + v;
            }
        });
        this.days = dates;
    }

    getNextDay(base: DateValue): string | null {
        const baseDate = getDateTime(base);
        const currentDay = baseDate.day;
        const nextDays = this.days.filter((v) => v > currentDay).filter((v) => v <= baseDate.daysInMonth);
        const found = Math.min(...nextDays);
        let result;
        if (found === Infinity) {
            for (let i = 1; i < 6; i++) {
                const nextBase = baseDate.plus({month: i}).set({day: 1});
                const dayInNextMonth = Math.min(...this.days.filter((v) => v <= nextBase.daysInMonth));
                if (dayInNextMonth !== Infinity) {
                    result = nextBase.set({day: dayInNextMonth});
                }
            }
        } else {
            result = baseDate.set({day: found});
        }
        if (!result) {
            return null;
        }
        let nextMonth = false;
        let resultDay;
        if (nextDays.length > 0) {
            resultDay = Math.min(...nextDays);
        } else {
            resultDay = Math.min(...this.days);
            nextMonth = true;
        }
        let resultDate = baseDate.set({day: resultDay});
        if (nextMonth) {
            resultDate = resultDate.plus({month: 1});
        }
        return formatResult(resultDate);
    }

    isDateSatisfied(date: DateValue): boolean {
        const dateVal = getDateTime(date);
        return this.days.includes(dateVal.day);
    }
}

export class EndOfMonthCalc implements NextDayCalculator {
    getNextDay(base: DateValue): string {
        const baseDate = getDateTime(base);
        const nextEoM = baseDate.plus({day: 1}).endOf('month');
        return formatResult(nextEoM);
    }

    isDateSatisfied(date: DateValue): boolean {
        const dateVal = getDateTime(date);
        return dateVal.day === dateVal.endOf('month').day;
    }
}

export type SkipPeriodCalcParams = {
    period: string;
    length: number;
    startDate: DateValue;
    dayToKeep?: number;
};

export class SkipPeriodCalc implements NextDayCalculator {
    private params: SkipPeriodCalcParams;

    constructor(params: SkipPeriodCalcParams) {
        this.params = params;
    }

    getNextDay(base: DateValue): string | null {
        const baseDate = getDateTime(base);
        let next: DateTime;
        const period = this.params.period.toLowerCase();
        if (period.startsWith('month')) {
            let search = true;
            let counter = 1;
            const dayToKeep = this.params.dayToKeep || baseDate.day;
            do {
                next = baseDate.plus({month: (this.params.length) * counter}).set({day: dayToKeep});
                if (next.day === dayToKeep) {
                    search = false;
                }
            } while (search && ++counter <= 24);

            if (search) {
                return null;
            }
        } else {
            next = baseDate.plus({[period]: this.params.length});
        }

        return formatResult(next);
    }

    isDateSatisfied(date: DateValue): boolean {
        const baseDate = getDateTime(date);
        const startDate = getDateTime(this.params.startDate);
        const period = this.params.period.toLowerCase();
        if (baseDate.hasSame(startDate, 'day')) {
            return true;
        }
        let next = startDate;
        if (period.startsWith('month')) {
            let found = false;
            let counter = 1;
            const dayToKeep = startDate.day;
            do {
                next = startDate.plus({month: (this.params.length + 1) * counter}).set({day: dayToKeep});
                found = next.hasSame(baseDate, 'day');
                counter++;
            } while (!found && next < baseDate);

            return found;
        } else {
            let counter = 1;
            let found = false;
            do {
                next = startDate.plus({[period]: (this.params.length + 1) * counter});
                found = next.hasSame(baseDate, 'day');
                counter++;
            } while (!found && next < baseDate);
            return found;
        }
    }
}

export class SelectedDatesCalc implements NextDayCalculator {
    readonly dates: DateTime[];

    constructor(dates: string[]) {
        this.dates = dates.map((v) => DateTime.fromISO(v));
    }

    getNextDay(base: DateValue): string | null {
        const baseDate = getDateTime(base);
        const candidate = this.dates.filter((d) => d > baseDate.endOf('day')).sort()[0];
        return candidate ? formatResult(candidate) : null;
    }

    isDateSatisfied(date: DateValue): boolean {
        const dateVal = getDateTime(date);
        return !!this.dates.find((d) => d.hasSame(dateVal, 'day'));
    }
}

export type NumberedDayOfWeekCalcParams = {
    days: { dayOfWeek: number; number: number }[];
};

export class NumberedDayOfWeekCalc implements NextDayCalculator {
    private params: NumberedDayOfWeekCalcParams;

    constructor(params: NumberedDayOfWeekCalcParams) {
        this.params = params;
    }

    private static findThDateInMonth(baseDate: DateTime, weekDay: number, ordinalNumber: number): DateTime | null {
        let current = baseDate.startOf('month');
        let counter = 0;
        while (current.month === baseDate.month) {
            if (current.weekday === weekDay) {
                counter++;
                if (counter === ordinalNumber) {
                    return current;
                }
            }

            current = current.plus({days: 1});
        }
        return null;
    }

    getNextDay(base: DateValue): string | null {
        const baseDate = getDateTime(base);

        let monthOffset = 0;
        let candidate: DateTime | null = null;
        while (!candidate && monthOffset <= 12) {
            candidate = this.params.days
                .map(({dayOfWeek, number}) => {
                    return NumberedDayOfWeekCalc.findThDateInMonth(baseDate.plus({month: monthOffset}), dayOfWeek, number);
                })
                .filter((v) => !_.isNil(v))
                .filter((v: DateTime) => v > baseDate.endOf('day'))
                .sort()[0];
            monthOffset++;
        }
        if (!candidate) {
            return null;
        }
        return formatResult(candidate);
    }

    isDateSatisfied(date: DateValue): boolean {
        const dateVal = getDateTime(date);
        const candidatesOfThisMonth = this.params.days
            .map((p) => {
                return NumberedDayOfWeekCalc.findThDateInMonth(dateVal, p.dayOfWeek, p.number);
            })
            .filter((v) => !_.isNil(v));
        return !!candidatesOfThisMonth.find((d) => d!.hasSame(dateVal, 'day'));
    }
}

export type DaysFilterParams = {
    excludeDates: string[];
    excludeDaysOfWeek: number[];
};

export class DaysFilter {
    readonly dates: DateTime[];
    readonly daysOfWeek: number[];

    constructor(params: DaysFilterParams) {
        this.daysOfWeek = params.excludeDaysOfWeek;
        this.dates = params.excludeDates.map(getDateTime);
    }

    matches(date: DateValue) {
        const dateVal = getDateTime(date);
        if (this.daysOfWeek.length > 0) {
            if (this.daysOfWeek.includes(dateVal.weekday)) {
                return true;
            }
        }
        if (this.dates.length > 0) {
            if (this.dates.find((d) => d.hasSame(dateVal, 'day'))) {
                return true;
            }
        }
        return false;
    }
}

export class DateFrame {
    readonly from: DateTime;
    readonly till: DateTime;

    constructor(dateFrom: DateValue, dateTill: DateValue) {
        this.from = getDateTime(dateFrom);
        this.till = getDateTime(dateTill).endOf('day');
    }

    isInside(dateParam: DateValue) {
        const date = getDateTime(dateParam);
        return date >= this.from && date <= this.till;
    }

    isBefore(dateParam: DateValue) {
        const date = getDateTime(dateParam);
        return date < this.from;
    }

    isAfter(dateParam: DateValue) {
        const date = getDateTime(dateParam);
        return date > this.till;
    }

    getLengthDays() {
        return this.till.diff(this.from, 'days').days;
    }
}
