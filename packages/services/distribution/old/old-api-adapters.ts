import * as newTypes from "@oss/shared/lib/distribution/api-types.js";
import {DistributionConfig} from "@oss/shared/lib/distribution/api-types.js";
import {
    DeliveryHistoryType,
    DeliveryHistoryWithDetailsType,
    MailTriggerConfigType,
    TalkConfigType,
    UpsertDistributionConfigRequest,
    UpsertDistributionConfigResponse
} from "./api-types.js";
import {DateTime} from "luxon";
import {
    DeliveryStartedAs,
    DeliveryState,
    DistributionType,
    TargetsSelectionType
} from "@oss/shared/lib/distribution/constants.js";
import {DeliveryStateValue, MessageRecord, MessageType, SurveyConditions} from "./types.js";
import _ from "lodash";
import {DistributionConfigParams} from "../control-api/repo/repo-configs-defs.js";
import {
    createConfig,
    getConfig,
    listConfigs,
    removeConfig,
    removeMessages,
    updateConfig,
    updateMessages
} from "../control-api/repo/repo-configs.js";
import {getPostponedScheduler, getRepeatingScheduler} from "../schedule/segment-delivery-scheduler-client.js";
import {findFutureTime} from "../control-api/helpers/recurrent-calc.js";
import console from "console";
import {deleteSchedule} from "../schedule/aws-scheduler-client.js";
import {NOTIFICATONS, notify, RunDistributionCommandEvent} from "../module.js";

function isoToFormats(isoStr?: string) {
    if (!isoStr) {
        return {
            unix: undefined,
            local: undefined,
            utc: undefined
        }
    }
    const dt = DateTime.fromISO(isoStr)
    return {
        unix: dt.toUnixInteger(),
        local: dt.toLocal().toISO(),
        utc: dt.toUTC().toISO()
    }
}

function convertState(r: newTypes.DeliveryHistoryShort): DeliveryStateValue {
    const state = r.state
    switch (state) {
        case DeliveryState.ERROR:
            return "ERROR"
        case DeliveryState.FINISHED:
            return "FINISHED"
        case DeliveryState.NOT_STARTED:
            return "NEW"
        case DeliveryState.IN_PROGRESS:
            return "QUEUED"
        default:
            return "QUEUED"
    }
}

function convertStartType(r: newTypes.DeliveryHistoryShort) {
    if ([DeliveryStartedAs.MANUAL_HOME_CONDITIONS, DeliveryStartedAs.MANUAL_HOME_SELECTION].includes(r.deliveryStartedAs)) {
        return 'HOME'
    }
    if (r.distributionType === DistributionType.REPEATING) {
        return 'RECURRING'
    }
    if (r.distributionType === DistributionType.INSTANT) {
        return 'IMMEDIATE'
    }
    if (r.distributionType === DistributionType.POSTPONED) {
        return 'ONETIME'
    }
    return 'IMMEDIATE'
}

function convertConditions(config: newTypes.DistributionConfig): SurveyConditions {
    if (config.targetSelectionType === TargetsSelectionType.BROADCAST) {
        return {
            pickAll: true,
            surveyId: ''
        }
    }
    if (config.targetSelectionType === TargetsSelectionType.SURVEY_CONDITIONS
    && config.surveyConditions
    ) {
        return {
            conditions: config.surveyConditions.conditions,
            pickAll: false,
            surveyId: config.surveyConditions.surveyId,
            surveyTitle: config.surveyConditions['surveyTitle']
        }
    }
    return {
        pickAll: false,
        conditions: [],
        surveyId: '',
    }
}

function convertConditionsOldToNew(sc: SurveyConditions) {
    if (!sc) return {
        conditions: undefined,
        targetSelectionType: TargetsSelectionType.BROADCAST,
    }
    const newC: newTypes.SurveyConditions = {
        surveyId: sc.surveyId,
        conditions: sc.conditions.map(c => ({
            itemKey: c.itemKey, conditionValues: _.flatten([c.conditionValues]).filter(f => !!f)
        })),
    }
    newC['surveyTitle'] = sc.surveyTitle
    return {
        conditions: newC,
        targetSelectionType: sc.pickAll ? TargetsSelectionType.BROADCAST : TargetsSelectionType.SURVEY_CONDITIONS,
    }
}

export function mergeConfigAndHistory(config: newTypes.DistributionConfig, messages: newTypes.Message[], history?: newTypes.DeliveryHistoryShort): UpsertDistributionConfigResponse {
    const shallowConfig = mapConfigRecordShallow(config)

    const mappedMessages: MessageRecord[] = messages.map(m => {
        return {
            type: m.contents['type'],
            primaryKey: m.id,
            contents: m.contents as MessageType,
        }
    })
    const mergedConfig: DeliveryHistoryWithDetailsType = {
        ...shallowConfig,
        messages: mappedMessages as never,
    };
    if (config.distributionType === DistributionType.POSTPONED) {
        const scheduledStartAt = isoToFormats(`${config.postponedSettings.date}T${config.postponedSettings.time}`)
        mergedConfig.scheduledStartAt = scheduledStartAt.unix
        mergedConfig.scheduledStartAtStr = scheduledStartAt.utc
    } else {
        const scheduledStartAt = isoToFormats(DateTime.now().plus({day: 1}).toISO())
        mergedConfig.scheduledStartAt = scheduledStartAt.unix
        mergedConfig.scheduledStartAtStr = scheduledStartAt.utc
    }
    if (history) { // it means, the history record has been requested, not config
        const mappedHistoryRecord = mapHistoryRecord(history)
        Object.assign(mergedConfig, _.pick(mappedHistoryRecord,
            ['deliveryTargetsCount', 'deliverySuccessCount', "deliveryFailureCount", "completedAt", "completedAtStr", "startedAt", "startedAtStr", "state"]))
    }
    return mergedConfig;
}

export function mapConfigRecordForHistoryScreens(config: newTypes.DistributionConfig, history: newTypes.DeliveryHistoryShort[]) {
    const type = config.distributionType;
    if (type === DistributionType.POSTPONED) {
        if (history.some(h => h.configurationId === config.id)) {
            return undefined
        }
    }
    return mapConfigRecordShallow(config)
}

export function mapConfigRecordShallow(config: newTypes.DistributionConfig): (DeliveryHistoryType | undefined) {
    const type = config.distributionType;
    const startType = type === DistributionType.POSTPONED ? 'ONETIME' : (type === DistributionType.REPEATING ? 'RECURRING' : 'IMMEDIATE')
    const created = isoToFormats(config.createdAt)
    return {
        createdAt: created.unix,
        createdBy: config.createdBy,
        deliveryFailureCount: 0,
        deliverySuccessCount: 0,
        deliveryTargetsCount: 0,
        distributionId: config.id,
        dryRun: false,
        errorMessage: "",
        id: config.id,
        messages: [],
        surveyConditions: convertConditions(config),
        recurringSettings: config.repeatingSettings,
        name: config.distributionName,
        startType,
        startedAt: 0,
        startedAtStr: "",
        scheduledStartAtStr: type === DistributionType.POSTPONED ? `${config.postponedSettings.date}T${config.postponedSettings.time}:00Z`: '',
        state: config.isDraft ? 'DRAFT' : 'SCHEDULED',
        type: "distributionDelivery#v2",
        updatedAt: 0,
        updatedAtStr: ""

    }
}

export function mapHistoryRecord(newRecord: newTypes.DeliveryHistoryShort): DeliveryHistoryType {
    const completed = isoToFormats(newRecord.finishedAt)
    const started = isoToFormats(newRecord.startedAt)
    const created = isoToFormats(newRecord.createdAt)
    return {
        id: newRecord.id,
        name: newRecord.distributionName,
        completedAt: completed.unix,
        completedAtStr: completed.local,
        startedAt: started.unix,
        startedAtStr: started.local,
        createdAt: created.unix,
        createdBy: newRecord.createdBy,
        type: 'distributionDelivery#v2',
        deliveryFailureCount: newRecord.targetsCount - newRecord.deliveriesCount,
        deliverySuccessCount: newRecord.deliveriesCount,
        deliveryTargetsCount: newRecord.targetsCount,
        distributionId: newRecord.configurationId,
        dryRun: false,
        errorMessage: "",
        messages: [],
        startType: convertStartType(newRecord),
        state: convertState(newRecord),
        updatedAt: 0,
        updatedAtStr: ""

    }
}

export async function upsertOldConfig(c: UpsertDistributionConfigRequest, isDraft = false, configId?: string) {
    const params: DistributionConfigParams = {
        isDraft,
        targetSelectionType: c.surveyConditions.pickAll ? TargetsSelectionType.BROADCAST : TargetsSelectionType.SURVEY_CONDITIONS,
        enabled: true,
        distributionType: c.startType === 'RECURRING' ? DistributionType.REPEATING : (c.startType === 'ONETIME' ? DistributionType.POSTPONED : DistributionType.INSTANT),
        distributionName: c.name,
        messageIds: [],
    }
    if (!c.surveyConditions.pickAll) {
        params.surveyConditions = {
            surveyId: c.surveyConditions.surveyId,
            conditions: c.surveyConditions.conditions.map(c => ({
                itemKey: c.itemKey,
                conditionValues: _.flatten([c.conditionValues]).filter(f => !!f)
            })).filter(v => v.conditionValues.length > 0)
        };
        params.surveyConditions['surveyTitle'] = c.surveyConditions.surveyTitle
    }

    if (params.distributionType === DistributionType.POSTPONED) {
        if (c.scheduledStartAt) {
            const str = DateTime.fromSeconds(c.scheduledStartAt).toLocal().toFormat("yyyy-MM-dd'T'HH:mm")
            c.scheduledStartDate = str.split('T')[0]
            c.scheduledStartTime = str.split('T')[1]
        }
        params.postponedSettings = {
            date: c.scheduledStartDate,
            time: c.scheduledStartTime,
        }
    }
    if (params.distributionType === DistributionType.REPEATING) {
        params.repeatingSettings = c.recurringSettings
    }

    c.messages.forEach((m, i) => {
        params.messageIds.push(`message_${i}`)
    })
    console.log('Updating config', JSON.stringify(params, null, 2))
    const config = configId ? await updateConfig(configId, params) : await createConfig(params)
    console.log('Updating messages')
    const newMessages: newTypes.Message[] = c.messages.map((c, i) => {
        return {
            id: params.messageIds[i],
            contents: c.contents
        }
    })
    await updateMessages(config.id, newMessages);
    if (!isDraft) {
        switch (params.distributionType) {
            case DistributionType.POSTPONED:
                await getPostponedScheduler().update(config.id, config.postponedSettings.date, config.postponedSettings.time, {
                    description: config.distributionName
                })
                break;
            case DistributionType.REPEATING:
                const result = findFutureTime(config.repeatingSettings);
                if (result) {
                    const {date, time} = result
                    await getRepeatingScheduler().update(config.id, date, time, {description: config.distributionName})
                }
                break;
            case DistributionType.INSTANT:
                const notification: RunDistributionCommandEvent = {
                    RunConfiguration: {
                        configurationId: config.id
                    }
                }
                await notify(NOTIFICATONS.types.RUN_CONFIGURATION, notification)
                break;
        }
    }

    return {
        result: 'SUCCESS',
        item: mergeConfigAndHistory(config, newMessages),
    }
}

export async function deleteConfiguration(id: string) {
    const config = await getConfig(id)
    await removeConfig(id)
    await removeMessages(id)

    if ([DistributionType.REPEATING, DistributionType.POSTPONED].includes(config.distributionType)) {
        await deleteSchedule(id, config.distributionType)
    }
}

function mapNewToOldMail(c: DistributionConfig): MailTriggerConfigType {
    const createdAtNum = DateTime.fromISO(c.createdAt)
    const updatedAtNum = DateTime.fromISO(c.updatedAt)
    return {
        enabled: c.enabled,
        type: 'distributionConfig',
        name: c.distributionName,
        id: c.id,
        surveyConditions: c.targetSelectionType === 'broadcast' ? null : convertConditions(c),
        distributionCondition: c.mailTriggerSettings,
        createdAt: createdAtNum.toSeconds(),
        updatedAt: updatedAtNum.toSeconds(),
    }
}

function mapNewToOldTalk(c: DistributionConfig): TalkConfigType {
    const createdAtNum = DateTime.fromISO(c.createdAt)
    const updatedAtNum = DateTime.fromISO(c.updatedAt)
    return {
        enabled: c.enabled,
        type: 'distributionConfig',
        name: c.distributionName,
        id: c.id,
        surveyConditions: null,
        distributionCondition: c.mailTriggerSettings,
        talkId: c.talkSettings.talkId,
        talkName: c.talkSettings.talkName,
        useDisasterRichmenu: c.talkSettings.useDisasterRichmenu,
        createdAt: createdAtNum.toSeconds(),
        updatedAt: updatedAtNum.toSeconds(),
        environment: c.talkSettings.environment,
    }
}

export async function listExternalConfigurations() {
    const configs = await listConfigs(DistributionType.EXTERNAL, {})
    const mailTriggering = configs.filter(c => c.targetSelectionType !== 'scenario')
    return mailTriggering.map(mapNewToOldMail)
}

export async function listTalks() {
    const configs = await listConfigs(DistributionType.EXTERNAL, {})
    const talks = configs.filter(c => c.targetSelectionType === 'scenario')
    return talks.map(mapNewToOldTalk)
}

export async function getOldExternalDistribution(id: string) {
    const config = await getConfig(id);
    if (config.targetSelectionType === TargetsSelectionType.SCENARIO) {
        return mapNewToOldTalk(config)
    } else {
        return mapNewToOldMail(config)
    }
}

function timestampToIso(seconds: number | undefined) {
    if (seconds === undefined) return undefined
    return DateTime.fromSeconds(seconds).toUTC().toISO()
}

export async function upsertTalk(c: TalkConfigType, configId?: string) {
    const params: DistributionConfigParams = {
        isDraft: false,
        createdAt: timestampToIso(c.createdAt),
        mailTriggerSettings: c.distributionCondition,
        talkSettings: {
            talkId: c.talkId,
            talkName: c.talkName,
            useDisasterRichmenu: c.useDisasterRichmenu,
            environment: c.environment,
            bosaiTrigger: true,
        },
        distributionType: DistributionType.EXTERNAL,
        distributionName: c.name,
        enabled: c.enabled,
        updatedAt: timestampToIso(c.updatedAt),
        targetSelectionType: TargetsSelectionType.SCENARIO,
    };
    const config = configId ? await updateConfig(configId, params) : await createConfig(params)
    return getOldExternalDistribution(config.id)
}

export async function upsertMail(c: MailTriggerConfigType, configId?: string) {
    const cond = convertConditionsOldToNew(c.surveyConditions)
    const params: DistributionConfigParams = {
        isDraft: false,
        createdAt: timestampToIso(c.createdAt),
        mailTriggerSettings: c.distributionCondition,
        distributionType: DistributionType.EXTERNAL,
        distributionName: c.name,
        enabled: c.enabled,
        updatedAt: timestampToIso(c.updatedAt),
        targetSelectionType: cond.targetSelectionType,
        surveyConditions: cond.conditions,
    };
    const config = configId ? await updateConfig(configId, params) : await createConfig(params)
    return getOldExternalDistribution(config.id)
}
