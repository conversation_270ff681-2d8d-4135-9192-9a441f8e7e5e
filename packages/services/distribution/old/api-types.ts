import {
    DeliveryStateValue,
    DistributionDeliveryV2Fields,
    DistributionDeliveryV2Record,
    MessageRecord,
    MessageType, SurveyConditions
} from "./types.js";
import {MailTriggerSettings, Message} from "@oss/shared/lib/distribution/api-types.js";

export type DeliveryHistoryType = DistributionDeliveryV2Fields & { messages: string[] }
export type DeliveryHistoryWithDetailsType = DistributionDeliveryV2Fields & {
    messages: MessageRecord[]
}
export type UpsertDistributionConfigRequest = DistributionDeliveryV2Fields & {
    scheduledStartDate?: string,
    scheduledStartTime?: string,
    messages: MessageRecord[],
}
export type UpsertDistributionConfigResponse = UpsertDistributionConfigRequest
export type UpdateDistributionConfigResponse = {
    result: 'ERROR' | 'SUCCESS',
    message?: string,
    updatedItems?: any
}

export type MailTriggerConfigType = {
    enabled: boolean,
    surveyConditions: SurveyConditions,
    updatedAt: number,
    createdAt: number,
    distributionCondition: MailTriggerSettings,
    id: string,
    name: string,
    type: 'distributionConfig'
}

export type TalkConfigType = {
    environment: 'production' | 'sandbox',
    enabled: boolean,
    surveyConditions: null,
    updatedAt: number,
    createdAt: number,
    distributionCondition: MailTriggerSettings,
    id: string,
    name: string,
    talkId: string,
    talkName: string,
    useDisasterRichmenu: boolean,
    type: 'distributionConfig'
}
