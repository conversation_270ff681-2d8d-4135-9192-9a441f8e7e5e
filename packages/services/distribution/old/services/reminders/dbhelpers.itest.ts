/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { dbTest } from "../../../../common/utils/index.js";
import { createExampleSchema } from "common/utils/index.js/lib/test-support/test-schema-builder";
import config from '../../../../common/admin-api/config/static.js';
import c from '../../../../common/admin-api/config/constants.js';
import { repeatQuery } from "../../../../common/admin-api/utils/db.js";
import { getAllConfigurations } from "./dbhelpers.js";

config.merge({ [c.TABLE_REMINDERS]: 'reminders' });

dbTest.useTestDatabase(async (c) => {
    await c.createTable(createExampleSchema('reminders'))
});
const table = dbTest.useTestTable('reminders');

describe('getConfigurations', () => {
    beforeAll(async () => {
        const data:any[] = [];
        for (let i = 0; i < 100; ++i) {
            data.push({
                partitionKey: 'configuration',
                sortKey: 'config_' + i,
                reminderType: 'date_relative',
            })
        }
        await table.batchInsert(data)
    })

    describe('#repeatQuery', () => {


        it('should get 100 objects', async () => {
            const res = await repeatQuery((lek) => {
                return table.documentClient.query({
                    TableName: 'reminders',
                    KeyConditionExpression: 'partitionKey = :pk',
                    FilterExpression: 'reminderType = :reminderType',
                    ExpressionAttributeValues: {
                        ':pk': 'configuration',
                        ':reminderType': 'date_relative',
                    },
                    ExclusiveStartKey: lek,
                    Limit: 10,
                }).promise();
            })

            expect(res).toHaveLength(100);
        })
    })

    describe('#getAllConfigurations', () => {
        it('should get 100 configurations', async () => {
            const res = await getAllConfigurations("date_relative");
            expect(res).toHaveLength(100);
        })
    })
})
