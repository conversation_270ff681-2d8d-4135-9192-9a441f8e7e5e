import {createSchedule, deleteSchedule, SchedulerBuilder} from "../../../schedule/aws-scheduler-client.js";
import {lambdaEnv} from "common/lambda-env.js";
import {ENV} from "../../../module.js";
import {DateTime} from "luxon";
import {makeReminderId} from "./dbhelpers.js";

function getRemindersGroup() {
    return `${lambdaEnv.getEnv(ENV.ENV_NAME)}-reminders`
}

export function createSchedulerBuilder(id: string) {
    return new SchedulerBuilder({
        name: id,
        payload: {},
        group: getRemindersGroup(),
        roleArn: lambdaEnv.getEnv(ENV.SCHEDULER_ROLE_ARN),
        dlqArn: lambdaEnv.getEnv(ENV.SYSTEM_DLQ_ARN),
        lambda: {lambdaArn: lambdaEnv.getEnv(ENV.LAMBDA_REMINDERS_ADAPTER)},
    })
}

export type ReminderDailySchedule = {
    sendTime: string,
    reminderLocalId: string,
    fromDate?: string,
    toDate?: string,
}

export async function deleteReminderSchedules(ids: string[]) {
    await Promise.all(ids.map(id => deleteSchedule(id, getRemindersGroup())))
}

export async function updateReminderSchedules(reminderConfigId: string, reminders: ReminderDailySchedule[]) {
    const now = DateTime.now().toUTC();
    const reminderUpdates = reminders.map(async (reminder) => recreateSchedule(reminder, reminderConfigId, now));
    await Promise.all(reminderUpdates);
}

export const makeScheduleId = (configId: string, reminderLocalId: string) => {
    return makeReminderId(configId, reminderLocalId).replace('#', '--');
}

function cronExpressionEveryDay(isoTime: string) {
    const dt = DateTime.fromISO(isoTime).toUTC()
    return `cron(${dt.minute} ${dt.hour} * * ? *)`;
}

async function recreateSchedule(reminder: ReminderDailySchedule, reminderConfigId: string, now: DateTime) {
    let startFrom = reminder.fromDate ? DateTime.fromISO(reminder.fromDate) : now
    if (startFrom.minus({minute: 5}) < now) {
        startFrom = now;
    }

    let endAt = reminder.toDate ? DateTime.fromISO(reminder.toDate) : undefined
    if (endAt <= now) {
        endAt = undefined
    }

    const scheduleId = makeScheduleId(reminderConfigId, reminder.reminderLocalId);
    await deleteSchedule(scheduleId, getRemindersGroup());
    const scheduler = createSchedulerBuilder(scheduleId)
        .setCron(cronExpressionEveryDay(reminder.sendTime))
        .setEndAt(endAt ? endAt.toUTC().toISO() : undefined)
        .setStartFrom(startFrom.toUTC().toISO())
        .setPayload({
            RunReminder: {
                reminderId: makeReminderId(reminderConfigId, reminder.reminderLocalId),
            }
        })
    ;
    await createSchedule(scheduler.convertToCommandInput());
}
