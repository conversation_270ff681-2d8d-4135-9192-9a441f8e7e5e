/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import _ from 'lodash';
import c from '../../../common/admin-api/config/constants.js';
import config from '../../../common/admin-api/config/static.js';
import {
    DateRelativeReminderItemResponseModel,
    ReminderConfigurationCreateRequest,
    ReminderConfigurationRecord,
    ReminderSettingsType,
    SingleReminderRequest
} from '../../../platform/types/reminders.js';
import {AccessDenied, BadRequest} from '../../../common/admin-api/utils/exceptions.js';
import {requestContext} from '../../../common/admin-api/utils/execution-context.js';
import {getCurrentTimeISO, toYYYYhpMMhpDD} from '../../../common/admin-api/utils/util.js';
import {buildS3UploadLink} from '../../../common/utils/aws-clients.js';
import {deleteMessagesOfReminders, downloadMessages, saveMessagesAndGetIds} from './distribution_resources.js';
import {
    ConfigurationUpdateHelper,
    deleteConfigurationRecord,
    deleteRemindersSettings,
    deleteState,
    mapRequestToRecordSetting,
    reassignReminderLocalIdsIfNeed
} from './reminders/config.js';
import {
    CONFIGURATION_PK,
    createNewConfigurationSk,
    findConfigById,
    findConfigBySurveyAndCategory,
    findConfigBySurveyAndItemKey,
    findDateReminder,
    getAllConfigurations,
    getHistoryRecord,
    makeReminderId,
    saveConfig
} from './reminders/dbhelpers.js';
import {HistoryHelper, mapHistory} from './reminders/history.js';
import {ReminderPermissionsHelper} from './reminders/permissions.js';
import {deleteReminderSchedules, makeScheduleId, updateReminderSchedules} from "./reminders/scheduler-reminders.js";
import {lambdaEnv} from "../../../common/lambda-env.js";
import {ENV} from "../../module.js";
import console from "console";

export async function loadHistoryByMonth(month: string) {
    const user = requestContext.user;
    const historyHelper = new HistoryHelper({month});
    await historyHelper.runSearch();
    if (!user.isSystemAdministrator()) {
        const surveys = historyHelper.getSurveysList();
        const permissionsHelper = new ReminderPermissionsHelper(user, surveys);
        await permissionsHelper.prepareAccessMap();
        await permissionsHelper.prepareCategoriesAccessMap();
        historyHelper.constraints.allowedSurveyIds = permissionsHelper.getAllowedSurveyIds();
        historyHelper.constraints.forbiddenCategories = permissionsHelper.getForbiddenCategories()
    }
    return await historyHelper.filterAndMap();
}

export async function getSingleHistoryRecord(partitionKey: string, sortKey: string) {
    const record = await getHistoryRecord({partitionKey, sortKey});
    await accessCheck(record.surveyId, record.categoryId, 'Has not access to this history record');
    return record ? mapHistory(record) : null;
}

export async function accessCheck(surveyId: string, categoryId: string, message: string) {
    const user = requestContext.user;
    if (user.isSystemAdministrator()) {
        return true;
    }
    if (!categoryId) {
        return false;
    }
    const helper = new ReminderPermissionsHelper(user, [surveyId]);
    await helper.prepareAccessMap();
    await helper.prepareCategoriesAccessMap();
    if (!helper.hasAccessToCategory(categoryId) || !helper.hasAccessToSurvey(surveyId)) {
        throw new AccessDenied(message);
    }
}

export async function getConfiguration(request: {
    surveyId?: string,
    categoryId?: string,
    dateItemKey?: string,
    id?: string,
    type?: ReminderSettingsType,
}) {
    let record: ReminderConfigurationRecord;
    if (request.id) {
        record = await findConfigById(request.id);
    } else {
        if (!request.surveyId) {
            throw new Error('survey id is required for search');
        }
        if (request.dateItemKey) {
            record = await findDateReminder(request.surveyId, request.dateItemKey);
        } else {
            record = await findConfigBySurveyAndCategory(request.surveyId, request.categoryId, 'appointment');
        }
    }
    if (!record) {
        return null;
    }
    const surveyId = request.surveyId || record.surveyId;
    const categoryId = record.reminderType === 'appointment' ? (request.categoryId || record.categoryId) : undefined;
    await accessCheck(surveyId, categoryId, 'Can not preview this configuration')
    return configRecordToApiResponse(record);
}

export async function findDateReminders() {
    const allConfigurations = await getAllConfigurations('date_relative');

    const surveys = _.uniq(allConfigurations.map(c => c.surveyId));
    console.log(allConfigurations.map(v => v.name))
    const permissionsHelper = new ReminderPermissionsHelper(requestContext.user, surveys);
    await permissionsHelper.prepareAccessMap();
    const allowedSurveys = permissionsHelper.getAllowedSurveyIds();
    _.remove(allConfigurations, (a) => !allowedSurveys.includes(a.surveyId));

    const reminders: DateRelativeReminderItemResponseModel[] = [];
    for await (const config of allConfigurations) {
        const surveyName = permissionsHelper.getSurveyTitle(config.surveyId) || '';
        const dateItemName = permissionsHelper.getSurveyItemName(config.surveyId, config.dateItemKey);
        for await (const reminder of config.settings) {
            const messages = await downloadMessages(reminder.messages);
            reminders.push({
                dateItemKey: config.dateItemKey,
                messages,
                monthesAfter: reminder.monthsAfter,
                daysAfter: reminder.daysAfter,
                yearsAfter: reminder.yearsAfter,
                configurationId: config.sortKey,
                surveyId: config.surveyId,
                reminderId: makeReminderId(config.sortKey, reminder.reminderLocalId),
                surveyName,
                dateItemName,
            });
        }
        ;
    }
    ;
    return reminders;
}

async function mapSettingsToDb(config: ReminderConfigurationCreateRequest) {
    const mappedReminders = config.settings.map((s, i) => mapRequestToRecordSetting(config, s, i))
    await Promise.all(mappedReminders.map(async (r) => {
        r.messages = await saveMessagesAndGetIds(r.messages);
    }))
    return mappedReminders;
}

export async function createConfigCommon(config: ReminderConfigurationCreateRequest) {
    const user = requestContext.user;
    await accessCheck(config.surveyId, config.categoryId, 'Not enough permissions to create reminder for this survey or category');
    const type = config.reminderType || (config.dateItemKey ? 'date_relative' : 'appointment')
    const reminderConfigId = config.id || createNewConfigurationSk()
    const record: ReminderConfigurationRecord = {
        partitionKey: CONFIGURATION_PK,
        sortKey: reminderConfigId,
        name: config.name,
        createdAt: getCurrentTimeISO(),
        updatedAt: getCurrentTimeISO(),
        createdBy: user.name,
        updatedBy: user.name,
        settings: await mapSettingsToDb(config),
        reminderType: type,
        dateItemKey: config.dateItemKey,
        conditions: config.conditions,
        surveyId: config.surveyId,
        fromDate: config.fromDate || toYYYYhpMMhpDD(),
        toDate: config.toDate || '2099-12-31',
        categoryId: config.categoryId || undefined,
    }
    reassignReminderLocalIdsIfNeed(record.settings);

    const existed = await findConfigById(record.sortKey);
    if (existed) {
        throw new BadRequest({msg: 'Reminder with this id is already exists'});
    }
    await saveConfig(record);
    await updateReminderSchedules(reminderConfigId, record.settings.map(r => ({
        sendTime: r.sendTime,
        reminderLocalId: r.reminderLocalId,
        fromDate: record.fromDate,
        toDate: record.toDate,
    })))
    return configRecordToApiResponse(record);
}

export async function createConfig(config: ReminderConfigurationCreateRequest) {
    const theSameParametersExisted = config.reminderType === 'appointment'
        ? await findConfigBySurveyAndCategory(config.surveyId, config.categoryId)
        : await findConfigBySurveyAndItemKey(config.surveyId, config.dateItemKey);
    if (theSameParametersExisted) {
        throw new BadRequest({msg: `Reminder with the same survey and category/itemKey is already exists`});
    }
    return await createConfigCommon(config);
}

export async function upsertDateRelativeReminder(config: ReminderConfigurationCreateRequest) {
    const targetConfig = await findConfigBySurveyAndItemKey(
        config.surveyId,
        config.dateItemKey,
    );

    const isAddingNewReminders = !targetConfig || (config.settings.length > targetConfig.settings.length)
    if (isAddingNewReminders) {
        await assertRemindersCountLimit();
    }

    if (!targetConfig) {
        return await createConfigCommon(config);
    } else {
        return await updateConfig(config, targetConfig);
    }
}

export async function upsertAppointmentReminder(config: ReminderConfigurationCreateRequest) {
    const targetConfig = await findConfigBySurveyAndCategory(config.surveyId, config.categoryId);
    if (!targetConfig) {
        return await createConfigCommon(config);
    }
    return await updateConfig(config, targetConfig);
}

export async function updateConfig(config: ReminderConfigurationCreateRequest, targetConfig: ReminderConfigurationRecord) {
    await accessCheck(config.surveyId, config.categoryId, 'Can not create configuration, using provided parameters');
    await accessCheck(targetConfig.surveyId, targetConfig.categoryId, 'Can not update this configuration');

    const updateHelper = new ConfigurationUpdateHelper(config, targetConfig);
    if (!await updateHelper.checkCanUpdate()) {
        throw new Error('The reminder you want to update or delete is running now. Try later');
    }
    const newRecord = await updateHelper.runUpdate();
    await updateReminderSchedules(newRecord.sortKey, newRecord.settings.map(r => ({
        reminderLocalId: r.reminderLocalId,
        sendTime: r.sendTime,
        fromDate: newRecord.fromDate,
        toDate: newRecord.toDate,
    })));
    return configRecordToApiResponse(newRecord);
}

export async function deleteReminders(ids: string[]) {
    await deleteRemindersSettings(ids);
    await deleteReminderSchedules(ids);
    return ids.map(id => ({id, code: 'success'}));
}

export async function deleteConfiguration(configurationId: string) {
    const config = await findConfigById(configurationId);
    const reminderIds = config.settings.map( s => makeReminderId(config.sortKey, s.reminderLocalId))
    const schedulerIds = config.settings.map( s => makeScheduleId(config.sortKey, s.reminderLocalId))

    await deleteConfigurationRecord(configurationId);
    await deleteReminderSchedules(schedulerIds)
    await deleteMessagesOfReminders(config.settings);
    await Promise.all(reminderIds.map(deleteState));

    return configRecordToApiResponse(config);
}

async function mapSettingsToApiResponse(record: ReminderConfigurationRecord): Promise<SingleReminderRequest[]> {
    return Promise.all(record.settings.map(async (s) => {
        return {
            reminderLocalId: s.reminderLocalId,
            sendTime: s.sendTime,
            daysBefore: s.daysBefore,
            daysAfter: s.daysAfter,
            monthsAfter: s.monthsAfter,
            yearsAfter: s.yearsAfter,
            messages: await downloadMessages(s.messages),
        } as SingleReminderRequest
    }))
}

async function configRecordToApiResponse(record: ReminderConfigurationRecord) {
    return {
        id: record.sortKey,
        name: record.name,
        surveyId: record.surveyId,
        dateItemKey: record.dateItemKey,
        settings: await mapSettingsToApiResponse(record),
        reminderType: record.reminderType,
        categoryId: record.categoryId,
        fromDate: record.fromDate,
        toDate: record.toDate,
        conditions: record.conditions,
    }
}

export async function getPresignedUrlOfReminderImage(query) {
    const {key, contentType} = query;
    const presignedUrl = await buildS3UploadLink('putObject', {
        Bucket: lambdaEnv.getEnv(ENV.BUCKET_DIST_RESOURCES),
        Key: key,
        CacheControl: 'no-cache',
        Expires: 3600,
        ContentType: contentType
    });
    console.log(lambdaEnv.getEnv(ENV.RESOURCES_CLOUDFRONT_DOMAIN))
    const url = 'https://' + lambdaEnv.getEnv(ENV.RESOURCES_CLOUDFRONT_DOMAIN) + '/' + key;
    return {presignedUrl, url}
}

export async function assertRemindersCountLimit() {
    const allDateRemindrsConfig = await getAllConfigurations('date_relative');
    const totalCount = _.chain(allDateRemindrsConfig).map(v => v.settings).flatten().value().length;
    const limit = _.toNumber(config.getOrDefaults(c.DATE_REMINDERS_TOTAL_COUNT_LIMIT, '100'));
    if (totalCount >= limit) {
        throw new BadRequest({msg: '配信設定が100件を超過するため保存できませんでした。日付配信の全体で100件以下になるように設定してください。'})
    }
}
