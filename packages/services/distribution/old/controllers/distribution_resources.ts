/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *  - name: DistributionResources
 *    description: 配信リソース管理API
 */

import * as service from '../services/distribution_resources.js';
import { member as m, guest as g } from '../../../common/admin-middlewares/access-guards.js';

import { LoggerService } from '../../../platform/services/logger-service.js';
const loggerService = new LoggerService('distribution_resources');

/**
 * @openapi
 * /distribution_resources/distributionResource:
 *  get:
 *    summary:
 *    tags: [DistributionResources]
 */
const getDistributionResource = async (req, res) => {
  await loggerService.createLog(req);

  if ('primaryKey' in req.query) {
    const primaryKey = req.query.primaryKey;
    const getCall = await service.getDistributionResource(primaryKey);
    res.json(getCall);
  } else {
    res.json({
      result: 'ERROR',
      message: 'Missing {primaryKey} query parameter.',
    });
  }
};

/**
 * @openapi
 * /distribution_resources/presignedURL:
 *  get:
 *    summary:
 *    tags: [DistributionResources]
 */
const getPresignedUrl = async (req, res) => {
  const key = req.query.key;
  const contentType = req.query.contentType;

  if (key && contentType) {
    const getCall = await service.getPresignedUrl(key, contentType);
    res.json(getCall);
  } else {
    res.json({
      result: 'ERROR',
      message: 'Missing {key} and {contentType} query parameters.',
    });
  }
};

/**
 * @openapi
 * /distribution_resources/distributionResource/all:
 *  get:
 *    summary:
 *    tags: [DistributionResources]
 */
const getAllDistributionResources = async (req, res) => {
  await loggerService.createLog(req);

  const getCall = await service.getAllDistributionResources();
  res.json(getCall);
};

/**
 * @openapi
 * /distribution_resources/distributionResource/queryByType:
 *  get:
 *    summary:
 *    tags: [DistributionResources]
 */
const queryByTypeDistributionResources = async (req, res) => {
  await loggerService.createLog(req);

  if ('type' in req.query) {
    const itemType = req.query.type;
    const queryCall = await service.queryByTypeDistributionResources(itemType);
    res.json(queryCall);
  } else {
    res.json({
      result: 'ERROR',
      message: 'Missing {type} query parameter.',
    });
  }
};

/**
 * @openapi
 * /distribution_resources/distributionResource/create:
 *  post:
 *    summary:
 *    tags: [DistributionResources]
 */

const createDistributionResource = async (req, res) => {
  await loggerService.createLog(req);

  if ('contents' in req.body) {
    const contents = req.body.contents;

    let createCall;

    if ('itemType' in req.body) {
      createCall = await service.createDistributionResource(contents, req.body.itemType);
    } else {
      createCall = await service.createDistributionResource(contents);
    }

    res.json(createCall);
  } else {
    res.json({
      result: 'ERROR',
      message: 'Missing {contents} in request JSON body.',
    });
  }
};

/**
 * @openapi
 * /distribution_resources/distributionResource/update:
 *  post:
 *    summary:
 *    tags: [DistributionResources]
 */

const updateDistributionResource = async (req, res) => {
  await loggerService.createLog(req);

  if ('primaryKey' in req.body && 'contents' in req.body) {
    const primaryKey = req.body.primaryKey;
    const contents = req.body.contents;

    let updateCall;

    if ('itemType' in req.body) {
      updateCall = await service.updateDistributionResource(primaryKey, contents, req.body.itemType);
    } else {
      updateCall = await service.updateDistributionResource(primaryKey, contents);
    }

    res.json(updateCall);
  } else {
    res.json({
      result: 'ERROR',
      message: 'Missing either {primaryKey} or {contents} in request JSON body.',
    });
  }
};

/**
 * @openapi
 * /distribution_resources/distributionResource:
 *  delete:
 *    summary:
 *    tags: [DistributionResources]
 */
const deleteDistributionResource = async (req, res) => {
  await loggerService.createLog(req);

  if ('primaryKey' in req.query) {
    const primaryKey = req.query.primaryKey;
    const deleteCall = await service.deleteDistributionResource(primaryKey);
    res.json(deleteCall);
  } else {
    res.json({
      result: 'ERROR',
      message: 'Missing {primaryKey} query parameter.',
    });
  }
};

export const routes = (app) => {
  // Distribution resources
  app.get('/distributionResource', g(getDistributionResource));
  app.get('/presignedURL', g(getPresignedUrl));
  app.get('/distributionResource/all', g(getAllDistributionResources));
  app.get('/distributionResource/queryByType', g(queryByTypeDistributionResources));
  app.post('/distributionResource/create', m(createDistributionResource));
  app.post('/distributionResource/update', m(updateDistributionResource));
  app.delete('/distributionResource', m(deleteDistributionResource));
}
