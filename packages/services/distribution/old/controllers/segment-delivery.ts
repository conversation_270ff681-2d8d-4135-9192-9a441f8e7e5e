/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *  - name: SegmentDelivery
 *    description: セグメント配信管理API(deprecated)
 */

import {ClientError} from '../../../common/admin-api/utils/exceptions.js';
import * as service from '../services/segment-delivery.js';
import * as surveyConfigService from '../../../api-admin-survey/services/survey.js';
import {guest as g, member as m} from '../../../common/admin-middlewares/access-guards.js';
import {LoggerService} from '../../../platform/services/logger-service.js';
import {extractLastEvaluatedKey} from '../../../platform/controllers/helpers/helpers.js';
import {cloneDeep} from 'lodash';
import {
    getOldExternalDistribution,
    listExternalConfigurations,
    listTalks,
    upsertMail,
    upsertTalk
} from "../old-api-adapters.js";
import {MailTriggerConfigType, TalkConfigType} from "../api-types.js";
import {Request, Response} from "lambda-api";
import {MailTriggerSettings} from "@oss/shared/lib/distribution/api-types.js";
import {getConfig, updateConfig} from "../../control-api/repo/repo-configs.js";

const loggerService = new LoggerService('segment-delivery');

/**
 * @openapi
 * /segment-delivery/distributionconfig:
 *  get:
 *    summary: 任意の配信設定を取得する
 *    tags: [SegmentDelivery]
 */
const getDistributionConfig = async (req, res) => {
    await loggerService.createLog(req);

    const distributionConfigId = req.query.distributionConfigId;
    res.json(await getOldExternalDistribution(distributionConfigId));
};

/**
 * @openapi
 * /segment-delivery/distributionconfig/all:
 *  get:
 *    summary: すべての配信設定を取得する
 *    tags: [SegmentDelivery]
 */
const getDistributionConfigs = async (req, res) => {
    await loggerService.createLog(req);

    const lastEvaluatedKey = extractLastEvaluatedKey(req);
    const getCall = await service.getDistributionConfigs({lastEvaluatedKey});
    res.json(getCall);
};

/**
 * @openapi
 * /segment-delivery/distributionconfig/allMail:
 *  get:
 *    summary: すべてのメール配信設定を取得する
 *    tags: [SegmentDelivery]
 */
const getDistributionConfigsMail = async (req, res) => {
    await loggerService.createLog(req);
    const items = await listExternalConfigurations();
    res.json({items});
};

/**
 * @openapi
 * /segment-delivery/distributionconfig/allTalk:
 *  get:
 *    summary: すべてのトーク配信設定を取得する
 *    tags: [SegmentDelivery]
 */
const getDistributionConfigsTalk = async (req, res) => {
    await loggerService.createLog(req);

    const items = await listTalks()
    res.json({items});
};

/**
 * @openapi
 * /segment-delivery/distributionconfig/create:
 *  put:
 *    summary: 配信設定を追加する
 *    tags: [SegmentDelivery]
 */
const createDistributionConfig = async (req: Request, res: Response) => {
    await loggerService.createLog(req);

    const distributionConfig: MailTriggerConfigType | TalkConfigType = cloneDeep(req.body);

    if (!distributionConfig.name || !distributionConfig.enabled) {
        throw new ClientError('配信作成のためパラメータが不足しています。');
    }

    const isBroadcast = !(distributionConfig.surveyConditions && distributionConfig.surveyConditions.surveyId);
    const distributionCondition = {
        condition: {
            subjectExtractionCondition: null,
            subjectTest: null,
            bodyExtractionCondition: null,
            bodyTest: null,
        },
        content: {
            bodyChangeCondition: null,
            bodyTest: null,
        },
    };
    const surveyConditions = isBroadcast ? null : distributionConfig.surveyConditions;
    if (!isBroadcast) {
        const isAppendingSurveyType = await surveyConfigService.isAppendingSurveyType(surveyConditions);
        if (isAppendingSurveyType) {
            throw new ClientError('追加型の帳票は配信できません。');
        }
        const isExistOptions = await surveyConfigService.isExistOptionsForDistributionSurveyConditions(surveyConditions);
        if (!isExistOptions) {
            throw new ClientError('帳票データが更新されています。ページを再読み込みしてください。');
        }
    }

    const configBase = {
        ...distributionConfig,
        distributionCondition: distributionCondition,
        surveyConditions: surveyConditions || null,
    }
    if (configBase['talkId']) {
        res.json(await upsertTalk(configBase as TalkConfigType))
    } else {
        res.json(await upsertMail(configBase as MailTriggerConfigType))
    }
};

/**
 * @openapi
 * /segment-delivery/distributionconfig/update:
 *  put:
 *    summary: 配信設定を更新する
 *    tags: [SegmentDelivery]
 */

const updateDistributionConfig = async (req, res) => {
    await loggerService.createLog(req);
    const id: string = req.body.id
    if (!id) {
        throw new ClientError('配信更新の必須値が存在しません。');
    }

    const distributionConfig = cloneDeep(req.body);
    const isBroadcast = !(distributionConfig.surveyConditions && distributionConfig.surveyConditions.surveyId);
    const surveyConditions = isBroadcast ? null : req.body.surveyConditions;
    if (!isBroadcast) {
        const isAppendingSurveyType = await surveyConfigService.isAppendingSurveyType(surveyConditions);
        if (isAppendingSurveyType) {
            throw new ClientError('追加型の帳票は配信できません。');
        }
        const isExistOptions = await surveyConfigService.isExistOptionsForDistributionSurveyConditions(surveyConditions);
        if (!isExistOptions) {
            throw new ClientError('帳票データが更新されています。ページを再読み込みしてください。');
        }
    }

    distributionConfig.surveyConditions = surveyConditions;

    if (distributionConfig['talkId']) {
        res.json(await upsertTalk(distributionConfig as TalkConfigType, id))
    } else {
        res.json(await upsertMail(distributionConfig as MailTriggerConfigType, id))
    }
};

/**
 * @openapi
 * /segment-delivery/distributionconfig/delete:
 *  post:
 *    summary: 配信設定を削除する
 *    tags: [SegmentDelivery]
 */
const deleteDistributionConfig = async (req, res) => {
    await loggerService.createLog(req);

    const distributionConfigId = req.distributionConfigId;
    const deleteCall = await service.deleteDistributionConfig(distributionConfigId);
    res.json(deleteCall);
};

/**
 * @openapi
 * /segment-delivery/distributionconfig/distributioncondition/update:
 *  put:
 *    summary: 配信条件を更新する
 *    tags: [SegmentDelivery]
 */
const updateDistributionConfigDistributionCondition = async (req, res) => {
    await loggerService.createLog(req);

    const distributionConfigId = req.body.distributionConfigId;
    const distributionConfigDistributionCondition = req.body.distributionConfigDistributionCondition;
    const updateCall = await service.updateDistributionConfigDistributionCondition(
        distributionConfigId,
        distributionConfigDistributionCondition
    );
    res.json(updateCall);
};

/**
 * @openapi
 * /segment-delivery/distributionconfig/surveyconditions/update:
 *  get:
 *    summary: 帳票設定を更新する
 *    tags: [SegmentDelivery]
 */
const updateDistributionConfigSurveyConditions = async (req, res) => {
    await loggerService.createLog(req);

    const distributionConfigId = req.body.distributionConfigId;
    const isBroadcast = !(req.body.surveyConditions && req.body.surveyConditions.surveyId);
    const surveyConditions = isBroadcast ? undefined : req.body.surveyConditions;

    if (!isBroadcast) {
        const isAppendingSurveyType = await surveyConfigService.isAppendingSurveyType(surveyConditions);
        if (isAppendingSurveyType) {
            throw new ClientError('追加型の帳票は配信できません。');
        }
        const isExistOptions = await surveyConfigService.isExistOptionsForDistributionSurveyConditions(surveyConditions);
        if (!isExistOptions) {
            throw new ClientError('帳票データが更新されています。ページを再読み込みしてください。');
        }
    }
    const updateCall = await service.updateDistributionConfigSurveyConditions(distributionConfigId, surveyConditions);
    res.json(updateCall);
};

/**
 * @openapi
 * /segment-delivery/distributionconfig/extractionanalysis:
 *  post:
 *    summary:
 *    tags: [SegmentDelivery]
 */
const regexExtractionAnalysis = async (req, res) => {
    await loggerService.createLog(req);

    const regex = req.body.extractionCondition;
    const test = req.body.test;
    const regexAnalysisCall = await service.regexExtractionAnalysis(regex, test);
    res.json(regexAnalysisCall);
};

/**
 * @openapi
 * /segment-delivery/distributionconfig/bodytransform:
 *  post:
 *    summary:
 *    tags: [SegmentDelivery]
 */
const regexBodyTransform = async (req, res) => {
    await loggerService.createLog(req);

    const regex = req.body.changeCondition;
    const test = req.body.test;
    const regexTransformCall = await service.regexBodyTransform(regex, test);
    res.json(regexTransformCall);
};

/**
 * @openapi
 * /segment-delivery/distributionconfig/distributioncondition/condition/update:
 *  post:
 *    summary:
 *    tags: [SegmentDelivery]
 */
const updateDistributionConditionCondition = async (req, res) => {
    await loggerService.createLog(req);

    const distributionConfigId = req.body.distributionConfigId;
    const subjectExtractionCondition = req.body.subjectExtractionCondition || '';
    const subjectTest = req.body.subjectTest || '';
    const bodyExtractionCondition = req.body.bodyExtractionCondition || '';
    const bodyTest = req.body.bodyTest || '';
    const conditions: MailTriggerSettings['condition'] = {
        subjectExtractionCondition,
        subjectTest,
        bodyExtractionCondition,
        bodyTest
    }
    const config = await getConfig(distributionConfigId);
    config.mailTriggerSettings = config.mailTriggerSettings || {}
    config.mailTriggerSettings.condition = conditions
    await updateConfig(distributionConfigId, config)
    res.json({
        distributionCondition: {
            condition: conditions
        }
    });
};

const updateDistributionConditionContent = async (req, res) => {
    await loggerService.createLog(req);

    const distributionConfigId = req.body.distributionConfigId;
    const bodyChangeCondition = req.body.bodyChangeCondition || '';
    const bodyTest = req.body.bodyTest || '';
    const config = await getConfig(distributionConfigId);
    const settings: MailTriggerSettings = config.mailTriggerSettings || {}
    settings.content = {
        bodyChangeCondition,
        bodyTest
    }
    await updateConfig(distributionConfigId, config)
    res.json({
        distributionCondition: {
            content: settings.content
        }
    });
};

/**
 * @openapi
 * /segment-delivery/distributiondelivery/all:
 *  get:
 *    summary:
 *    tags: [SegmentDelivery]
 */
const getDistributionDeliveries = async (req, res) => {
    await loggerService.createLog(req);

    const getCall = await service.getDistributionDeliveries();
    res.json(getCall);
};

/**
 * @openapi
 * /segment-delivery/distributiondelivery/resend:
 *  post:
 *    summary:
 *    tags: [SegmentDelivery]
 */
const resendDistributionDelivery = async (req, res) => {
    await loggerService.createLog(req);

    const distributionDeliveryId = req.body.distributionDeliveryId;
    const resendCall = await service.resendDistributionDelivery(distributionDeliveryId);
    res.json(resendCall);
};

export const routes = (app) => {
    // Distribution Configs
    app.get('/distributionconfig', g(getDistributionConfig));
    app.get('/distributionconfig/all', g(getDistributionConfigs));
    app.get('/distributionconfig/allMail', g(getDistributionConfigsMail));
    app.get('/distributionconfig/allTalk', g(getDistributionConfigsTalk));
    app.put('/distributionconfig/create', m(createDistributionConfig));
    app.put('/distributionconfig/update', m(updateDistributionConfig));
    app.post('/distributionconfig/delete', m(deleteDistributionConfig));
    app.put('/distributionconfig/distributioncondition/update', m(updateDistributionConfigDistributionCondition));
    app.put('/distributionconfig/surveyconditions/update', m(updateDistributionConfigSurveyConditions));
    app.post('/distributionconfig/extractionanalysis', g(regexExtractionAnalysis));
    app.post('/distributionconfig/bodytransform', g(regexBodyTransform));
    app.post('/distributionconfig/distributioncondition/condition/update', m(updateDistributionConditionCondition));
    app.post('/distributionconfig/distributioncondition/content/update', m(updateDistributionConditionContent));
    // Distribution Deliveries
    app.get('/distributiondelivery/all', g(getDistributionDeliveries));
    app.post('/distributiondelivery/resend', m(resendDistributionDelivery));
};
