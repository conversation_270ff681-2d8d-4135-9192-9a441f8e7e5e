/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {Request, Response} from 'lambda-api';
import _ from 'lodash';
import {ReminderConfigurationCreateRequest, SingleReminderRequest} from '../../../../platform/types/reminders.js';
import {BadRequest} from '../../../../common/admin-api/utils/exceptions.js';
import {requestContext} from '../../../../common/admin-api/utils/execution-context.js';
import {
    hasValue,
    isValid<PERSON><PERSON>ber,
    requireHasValue,
    requireMatchRegex,
    requireNotEmpty
} from '../../../../platform/controllers/helpers/validators.js';
import {UpsertDistributionConfigRequest} from "../../api-types.js";
import * as surveyConfigService from "../../../../api-admin-survey/services/survey.js";

export function validateLoadExecutionHistory(req: Request) {
    const {month, partitionKey, sortKey} = req.query;
    const byId = !!(partitionKey && sortKey);
    if (!byId && month) {
        requireMatchRegex(month, /^\d{6}$/, 'month');
    }
    return {
        month: month || '',
        partitionKey: partitionKey || '',
        sortKey: sortKey || '',
    }
}

export function validateAppointmentRequestParams(params: {
    surveyId: string,
    categoryId: string,
    id: string,
    dateItemKey: string
}) {
    if (params.id) {
        return params;
    }
    requireNotEmpty(params.surveyId, 'surveyId');
    return params;
}

export function validateDateRelativeRequestParams(params: {
    surveyId: string,
    id: string,
    dateItemKey: string
}) {
    if (params.id) {
        return params;
    }
    requireNotEmpty(params.surveyId, 'surveyId');
    requireNotEmpty(params.dateItemKey, 'dateItemKey')
    return params;
}

export function validateConfigurationBody(json: ReminderConfigurationCreateRequest) {
    const req = _.cloneDeep(json);
    const messages: any[] = [];
    requireNotEmpty(req.reminderType, 'reminderType');
    requireNotEmpty(req.surveyId, 'surveyId');
    const isDateReminder = req.reminderType === 'date_relative';
    if (isDateReminder) {
        requireNotEmpty(req.dateItemKey, 'dateItemKey');
        messages.push(...validateDateRelativeReminder(req));
    } else {
        if (!requestContext.user.isSystemAdministrator()) {
            requireNotEmpty(req.categoryId, 'categoryId');
        }
        validateAppointmentReminder(req);
    }
    if (messages.length > 0) {
        const {reminderLocalId, message} = messages[0];
        throw new BadRequest({msg: message, details: {reminderLocalId}});
    }
    return req;
}

function validateAppointmentReminder(json: ReminderConfigurationCreateRequest) {
    json.settings.forEach((s, i) => {
        const reminderName = `setting[${s.reminderLocalId || i}]`;
        requireHasValue(s.daysBefore, `${reminderName} must have daysBefore parameter specified`);
        requireHasValue(s.sendTime, `${reminderName} must have sendTime parameter specified`);
    });
}

function validateDateRelativeReminder(json: ReminderConfigurationCreateRequest) {
    const messages: any[] = [];

    json.settings.forEach((s, i) => {
        const reminderId = hasValue(s.reminderLocalId) ? s.reminderLocalId : `${i}`;
        const errorMessage = validateSingleDateRelativeReminder(s);
        if (errorMessage) {
            messages.push({
                reminderLocalId: reminderId,
                message: errorMessage,
            })
        }
    })

    return messages;
}

function validateSingleDateRelativeReminder(r: SingleReminderRequest) {
    if (!isValidNumber(r.yearsAfter)) return '年数に数字を指定してください。';
    if (!isValidNumber(r.monthsAfter)) return '月数に数字を指定してください。';
    if (!isValidNumber(r.daysAfter)) return '日数に数字を指定してください。';
    if (Math.abs(r.yearsAfter) > 100) return '年数に１００以下を指定してください。';
    if (Math.abs(r.monthsAfter) > 11) return '月数に１１以下を指定してください。';
    if (r.yearsAfter !== 0 || r.monthsAfter !== 0) {
        if (Math.abs(r.daysAfter) > 15) return '年数または月数に1以上を指定する場合は日数は15以下を指定してください。';
    } else {
        if (Math.abs(r.daysAfter) > 1000) return '日数に１０００以下を指定してください。'
    }
    for (let i = 0; i < r.messages.length; ++i) {
        const m = r.messages[i];
        if (m.type === 'text' && _.toLength(m.text) > 3000) return `${i}番目のメッセージの内容は３０００字以下を入力してください。`;
    }
}

export async function validateNewDelivery(req: Request, res: Response) {
    const distributionDeliveryToSave: UpsertDistributionConfigRequest = req.body;

    const isAppendingSurveyType = await surveyConfigService.isAppendingSurveyType(
        distributionDeliveryToSave.surveyConditions
    );
    if (isAppendingSurveyType) {
        res.json({
            result: 'ERROR',
            message: '追加型の帳票は配信できません。',
        });
        return false
    }

    const isExistOptions = await surveyConfigService.isExistOptionsForDistributionSurveyConditions(
        distributionDeliveryToSave.surveyConditions
    );
    if (!isExistOptions) {
        res.json({
            result: 'ERROR',
            message: '帳票データが更新されています。ページを再読み込みしてください。',
        });
        return false
    }

    return true
}
