/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { Request, Response } from 'lambda-api';
import * as service from '../services/reminders.js';
import { deleteConfiguration, deleteReminders, findDateReminders, getPresignedUrlOfReminderImage } from '../services/reminders.js';
import { guest as g, member as m } from '../../../common/admin-middlewares/access-guards.js';
import { sendDataResponse } from './helpers/responses.js';
import {
  validateAppointmentRequestParams,
  validateConfigurationBody,
  validateDateRelativeRequestParams,
  validateLoadExecutionHistory
} from './helpers/validators.js';
import { requireNotEmptyValueOrStruct, requireParameter } from '../../../platform/controllers/helpers/validators.js';

export const loadExecutionHistory = async (req: Request, res: Response) => {
  const {month, partitionKey, sortKey} = validateLoadExecutionHistory(req);
  if (partitionKey && sortKey) {
    const record = await service.getSingleHistoryRecord(partitionKey, sortKey);
    res.json([record]);
    return;
  }
  const history = await service.loadHistoryByMonth(month);
  res.json(history);
}
export const findAppointmentReminderConfiguration = async (req: Request, res: Response) => {
  const {id, categoryId, surveyId} = validateAppointmentRequestParams({
    id: req.query.id,
    categoryId: req.query.categoryId,
    surveyId: req.query.surveyId,
    dateItemKey: null,
  });
  const record = await service.getConfiguration({
    id, categoryId, surveyId, type: 'appointment'
  });
  sendDataResponse(res, record);
}
export const updateReminderConfiguration = async (req: Request, res: Response) => {
  const patch = validateConfigurationBody(req.body);
  let updated;
  if (patch.reminderType==='date_relative') {
    updated = await service.upsertDateRelativeReminder(patch);
  } else {
    updated = await service.upsertAppointmentReminder(patch);
  }
  sendDataResponse(res, updated);
}
export const createReminderConfiguration = async (req: Request, res: Response) => {
  const body = validateConfigurationBody(req.body);
  let result;
  if (body.reminderType==='date_relative') {
    result = await service.upsertDateRelativeReminder(body);
  } else {
    result = await service.createConfig(body)
  }
  sendDataResponse(res, result);
}
export const deleteDateReminder = async (req: Request, res: Response) => {
  const reminderId = decodeURIComponent(requireParameter(req, 'reminderId'));
  const result = await deleteReminders([reminderId]);
  res.json(result);
}
export const deleteDateReminders = async (req: Request, res: Response) => {
  const ids = requireNotEmptyValueOrStruct(req.body.ids, '"ids" array is empty or null');
  const result = await deleteReminders(ids);
  sendDataResponse(res, result);
}
export const findDateReminderSetting = async (req: Request, res: Response) => {
  const {surveyId, dateItemKey, id} = validateDateRelativeRequestParams({
    surveyId: req.query.surveyId,
    dateItemKey: req.query.dateItemKey,
    id: req.query.id,
  });
  const result = await service.getConfiguration({
    id, surveyId, type: 'date_relative', dateItemKey
  });
  sendDataResponse(res, result);
}
export const getDateReminders = async (req: Request, res: Response) => {
  const result = await findDateReminders();
  res.json(result);
}

export const deleteReminderConfiguration = async (req: Request, res: Response) => {
  const id = requireParameter(req, 'id');
  const result = await deleteConfiguration(id);
  result ? res.json(result) : res.status(404).json({code: 'not_found'});
}

const getImageUrl = async (req, res) => {
  const result = await getPresignedUrlOfReminderImage(req.query);
  res.status(200).json(result);
};


export const routes = (app) => {
  app.get('/history', g(loadExecutionHistory));
  app.get('/settings/appointment', g(findAppointmentReminderConfiguration));
  app.put('/settings', m(updateReminderConfiguration));
  app.post('/settings', m(createReminderConfiguration));
  app.delete('/reminders/:reminderId', m(deleteDateReminder));
  app.delete('/settings/:id', m(deleteReminderConfiguration));
  app.post('/reminders/delete', m(deleteDateReminders));
  app.get('/settings/date_relative', g(findDateReminderSetting));
  app.get('/reminders/date_relative', g(getDateReminders));
  app.get('/presignedURL', g(getImageUrl));
}
