import {S3StorageInput} from "../../../survey/search/async-search/async-search.js";
import {DatesFilter, FieldsFilter} from "../../../survey/search/common/results-query-helpers.js";
import {InvokeCommand, LambdaClient} from "@aws-sdk/client-lambda";
import * as util from "util";
import {InvalidExecutionState} from "../types.js";
import {SurveyConditions} from "@oss/shared/lib/distribution/api-types.js";
import * as _ from 'lodash'
import {isEmptyStructOrString} from "../../../common/utils/assertions.js";

export type SearchLambdaRequest = {
    searchId: string,
    userId: string,
    countOnly?: boolean
    input: {
        surveyId: string,
        filterCommon: FieldsFilter,
        filterDate: DatesFilter,
        filterCategories?: string[],
        filterTeams?: { [key: string]: boolean }
    },
    ignoreNonLineUsers?: boolean,
    groupBy?: string,
    s3?: S3StorageInput
}

export interface SearchLambdaResponse {
    searchId: string,
    totalCount: number,
    chunksCount: number,
    s3BucketName?: string,
    chunks: string[],
    status: string,
    errorMessage?: string,
    errorDetails?: any,
    startedAt: string,
    finishedAt: string,
    searchRequest: SearchLambdaRequest,
    debug: any,
}


export async function callSearchAndWait(request: SearchLambdaRequest, functionName: string): Promise<SearchLambdaResponse> {
    const client = new LambdaClient({})
    const cmd = new InvokeCommand({
        FunctionName: functionName,
        Payload: new util.TextEncoder().encode(JSON.stringify({
            StartAsyncSearchRequest: request,
        }))
    })
    const {Payload} = await client.send(cmd)
    const payloadStr = new util.TextDecoder().decode(Payload)
    const payloadObj = JSON.parse(payloadStr)
    return payloadObj['StartAsyncSearchResponse'] as SearchLambdaResponse;
}

export function assertResponseIsOk(response: SearchLambdaResponse) {
    if (response.status === 'error') {
        throw new InvalidExecutionState(`Error response from the search function: ${response.errorMessage}`)
    }
}


export class SearchRequestHelper {
    constructor(private distributionId: string, private segment: SurveyConditions) {
    }

    convertConditions() {
        if (!this.segment) {
            return {};
        }
        const conditions = this.segment.conditions || [];
        _.remove(conditions, isEmptyStructOrString);
        _.remove(conditions, (c) => isEmptyStructOrString(c.conditionValues));

        const filterCommon: FieldsFilter = {};
        conditions.forEach(c => {
            const conditionValues = _.flatten([c.conditionValues]).filter(v => !isEmptyStructOrString(v));
            if (conditionValues.length > 0) {
                filterCommon[c.itemKey] = conditionValues;
            }
        });
        return filterCommon;
    }

    public hasValidConditions() {
        if (!this.segment) {
            return false;
        }
        return !isEmptyStructOrString(this.convertConditions());
    }

    buildRequest(options: {
        resultsPerFile?: number,
        folder?: string,
    } = {}): SearchLambdaRequest {
        const opts = {
            resultsPerFile: 500,
            folder: `distribution/${this.distributionId}`,
            ...options,
        }


        const request: SearchLambdaRequest = {
            s3: {
                resultsPerFile: opts.resultsPerFile,
                folderKey: opts.folder,
            },
            searchId: this.distributionId,
            countOnly: false,
            input: {
                surveyId: this.segment.surveyId,
                filterCommon: {},
                filterDate: {},
            },
            userId: `distribution#${this.distributionId}`,
            ignoreNonLineUsers: true,
            groupBy: 'userId'
        }

        if (this.segment.filter) {
            request.input.filterCommon = this.segment.filter.filterCommon || {}
            request.input.filterDate = this.segment.filter.filterDate || {}
            request.input.filterTeams = this.segment.filter.filterTeams
            request.input.filterCategories = this.segment.filter.filterCategories
        } else {
            request.input.filterCommon = this.convertConditions();
        }

        return request
    }
}
