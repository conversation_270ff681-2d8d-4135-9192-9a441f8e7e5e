import {ChunkData, ChunksFileStruct, SingleDeliveryTarget} from "../types.js";
import {GetObjectCommand, S3Client} from "@aws-sdk/client-s3";
import console from "console";
import _ from "lodash";


export class Chunk<PERSON>elper {
    file: ChunksFileStruct

    constructor(private chunkId: string, private downloader: (chunkKey: string) => Promise<ChunksFileStruct>) {
    }

    async download() {
        this.file = await this.downloader(this.chunkId);
    }

    getChunk(chunk: ChunkData): SingleDeliveryTarget[] {
        if (!this.file) {
            return [];
        }
        return this.file.slice(chunk.chunkOffset, chunk.chunkOffset + chunk.chunkSize);
    }
}

export async function downloadChunkJson(bucket: string, key: string) {
    const client = new S3Client({})
    try {
        const {Body} = await client.send(new GetObjectCommand({
            Bucket: bucket,
            Key: key,
            ResponseContentType: 'text/json'
        }))
        return JSON.parse(await Body.transformToString()) as ChunksFileStruct
    } catch (e) {
        if (e.code === 'NoSuchKey') {
            return null
        }
        throw e;
    }
}

