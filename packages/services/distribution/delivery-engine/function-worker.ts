import {Context} from "aws-lambda";
import {DeliveryProcessState, MappedJob, RetryableError} from "./types.js";
import {LineMessage, LineRequest} from "../line/sender.js";
import {lambdaEnv} from "../../common/lambda-env.js";
import {ENV} from "../module.js";
import {getMessages} from "../control-api/repo/repo-configs.js";
import {downloadChunkJson} from "./helpers/s3chunks.js";
import _ from "lodash";
import {Job} from "sst/constructs";

type Input = {
    Job: JobTask,
    RetryId: {
        id: string
    }
}

export type JobTask = {
    sharedParams: DeliveryProcessState['searchResult']['sharedParams'],
    job: MappedJob,
    deliveryConfig: DeliveryProcessState['deliveryConfig']
}

export type JobResult = {
    chunkId: string,
    result: 'delivered' | 'failure' | 'no_targets',
    details: {
        requestId: string,
        deliveredCount: number,
        error?: {
            retryId: string,
            message: string,
        }
    }
}

export async function handler(rawEvent: Input, context: Context): Promise<JobResult> {
    const event = rawEvent.Job
    const lineMessages = event.deliveryConfig.messages.map(m => m as LineMessage)

    if (event.job.chunkId.startsWith('err_')) {
        throw new RetryableError('Test error for chunk ' + event.job.chunkId)
    }
    const targets = await downloadChunkJson(event.sharedParams.bucketName, event.job.chunkId);
    if (!targets) {
        return {
            chunkId: event.job.chunkId,
            result: 'no_targets',
            details: {
                requestId: '',
                deliveredCount: 0
            }
        }
    }

    const usersList = _.uniq(targets.map(t => t.userId))

    const lineRequest = new LineRequest({channelAccessToken: lambdaEnv.getEnv(ENV.LINE_MESSAGING_ACCESS_TOKEN)})
    const result = await lineRequest.multicast(usersList, lineMessages, rawEvent.RetryId.id)

    if (result.isOk) {
        return {
            chunkId: event.job.chunkId,
            result: 'delivered',
            details: {
                requestId: result.requestId,
                deliveredCount: usersList.length,
            }
        }
    }

    console.log(JSON.stringify(result, null, 2))
    console.log(JSON.stringify(lineMessages, null, 2))

    if (!result.error.isRetryable) {
        return {
            chunkId: event.job.chunkId,
            result: 'failure',
            details: {
                requestId: result.requestId,
                error: {
                    message: result.error.message,
                    retryId: result.retryToken,
                },
                deliveredCount: 0,
            }
        }
    }

    throw new RetryableError(result.error.message);
}
