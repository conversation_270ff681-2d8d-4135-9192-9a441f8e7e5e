import {DeliveryProcessState} from "./types.js";
import {lambdaEnv} from "../../common/lambda-env.js";
import {ENV} from "../module.js";
import {assertResponseIsOk, callSearchAndWait, SearchRequestHelper} from "./helpers/targetsSearch.js";
import {setTargetsCount, updateDeliveriesCount} from "../control-api/repo/repo-history.js";


export async function handler(event: DeliveryProcessState): Promise<DeliveryProcessState['searchResult']> {

    const requestHelper = new SearchRequestHelper(
        event.deliveryConfig.historyRecordId,
        event.deliveryConfig.targets.searchConditions)
    const searchResult = await callSearchAndWait(requestHelper.buildRequest(), lambdaEnv.getEnv(ENV.SEARCH_FUNCTION))
    assertResponseIsOk(searchResult)
    await setTargetsCount(event.deliveryConfig.historyRecordId, searchResult.totalCount)
    return {
        jobs: searchResult.chunks.map(c => ({ chunkId: c })),
        sharedParams: {
            bucketName: lambdaEnv.getEnv(ENV.SEARCH_RESULTS_BUCKET)
        }
    }
}
