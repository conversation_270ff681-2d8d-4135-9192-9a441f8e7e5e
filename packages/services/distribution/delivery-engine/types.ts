import {LineMessageStruct, Message, SurveyConditions} from "@oss/shared/lib/distribution/api-types.js";
import {EventBridgeEvent} from "aws-lambda";
import {TargetsSelectionType} from "@oss/shared/lib/distribution/constants.js";
import {MessageType} from "../../api-admin-survey/types/survey-distribution.js";

export type MappedJob = {
    chunkId: string,
}

export type MappedResult = {
    chunkId: string,
    resolution: 'delivered' | 'no_targets' | 'failure',
    details?: {
        delivered: number
    },
    error?: {
        lineRequestId?: string,
        lineRetryToken: string,
        errorType: string,
        errorMessage: string,
        workerLambdaExecutionId: string,
    }
}

export type DeliveryProcessState = {
    executionId: string,
    startArgs: { // input arguments wrapper
        configurationId: string,
        messages?: LineMessageStruct[]
    },
    deliveryConfig?: { // Starter task result
        configurationId: string,
        historyRecordId: string,
        targets: {
            type: TargetsSelectionType,
            broadcast?: boolean, // if send to all friends
            useSelectedTargets?: boolean // if there is a list of targets, mapped to the config
            searchConditions?: SurveyConditions
        },
        messages: LineMessageStruct[]
    },
    searchResult?: {
        jobs: MappedJob[],
        sharedParams: {
            bucketName: string
        }
    },
    deliveryResult?: { // Simple delivery task result
        resolution: 'delivered' | 'no_targets' | 'failure',
        result: { // exists if resolution is delivered or failure
            request?: {
                retryToken: string,
                requestId: string,
            },
            stats?: {
              targets: number,
              delivered: number,
            },
            failure?: {
                message: string
                details: any
            }
        },

    },
    parallelDeliveryResult?: MappedResult[]
    processError?: { // Delivery error task result
        Error: string,
        Cause: string,
    }

}

export class InvalidExecutionState extends Error {
    constructor(message: string) {
        super(message);
    }
}

export class RetryableError extends Error {
    constructor(message) {
        super(message);
    }
}

export type ChunkData = {
    id: string,
    fileKey: string,
    chunkOffset: number,
    chunkSize: number,
}

export type ChunksFileStruct = SingleDeliveryTarget[]

export type SingleDeliveryTarget = {
    userId: string,
    partitionKey: string,
    surveyResultsIds?: string[],
    segmentIds?: string[],
}

export type EventBusEvent<Payload> = EventBridgeEvent<string, Payload>

