// the [START] step
import {getConfig, getMessages} from '../control-api/repo/repo-configs.js'
import {DeliveryStartedAs, DeliveryState, DistributionType} from "@oss/shared/lib/distribution/constants.js";
import {createHistoryRecord} from "../control-api/repo/repo-history.js";
import {DateTime} from "luxon";
import {DeliveryProcessState} from "./types.js";
import {v4} from "uuid";

export async function handler(event: DeliveryProcessState): Promise<DeliveryProcessState['deliveryConfig']> {
    const configurationId = event.startArgs.configurationId.replace('--', '#')
    if (!configurationId) {
        throw new Error('[configurationId] property is empty')
    }
    const settings = await getConfig(configurationId)
    if (!settings) {
        throw new Error(`No configuration found with ID=${configurationId}`)
    }
    let messages = event.startArgs.messages;
    if (!messages) {
        const dbMessages = await getMessages(configurationId)
        if (dbMessages) {
            messages = dbMessages.map(m => m.contents)
        }
    }
    const now = DateTime.now().toUTC().toISO()
    const historyId = [DistributionType.INSTANT, DistributionType.POSTPONED].includes(settings.distributionType) ? configurationId : v4()
    await createHistoryRecord({
        id: historyId,
        startedAt: now,
        state: DeliveryState.IN_PROGRESS,
        distributionType: settings.distributionType,
        deliveryStartedAs: DeliveryStartedAs.UNSPECIFIED,
        targetsSelectionType: settings.targetSelectionType,
        configurationId: configurationId,
        createdAt: now,
        deliveriesCount: 0,
        targetsCount: 0,
        createdByName: '', // TODO
        createdBy: '', // TODO
        distributionName: settings.distributionName
    }, {
        stateMachine: {
            executionId: event.executionId
        },
        messages
    })
    return {
        configurationId: configurationId,
        historyRecordId: historyId,
        targets: {
            type: settings.targetSelectionType,
            broadcast: settings.targetSelectionType === 'broadcast',
            useSelectedTargets: settings.targetSelectionType === 'selected',
            searchConditions: settings.surveyConditions
        },
        messages: messages
    }
}

