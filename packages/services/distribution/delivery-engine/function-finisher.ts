import {getHistoryRecord, updateFinished, updateWithError} from "../control-api/repo/repo-history.js";
import {DeliveryProcessState} from "./types.js";
import {DeliveryResultEventPayload, NOTIFICATONS, notify} from "../module.js";
import {nowISO} from "../../common/utils/datetime.js";

export async function handler(event: DeliveryProcessState) {
    const historyId = event.deliveryConfig.historyRecordId;

    if (event.deliveryResult) {
        // handle simple flow
        await finish(event)
        return
    }
    if (event.parallelDeliveryResult) {
        const failedChunks = event.parallelDeliveryResult.filter(f => f.resolution === 'failure')
        // TODO store failed chunks
        if (failedChunks.length > 0) {
            await finish(event, `${failedChunks[0].error.errorMessage}`)
        } else {
            await finish(event)
        }
        return
    }
    if (event.processError) {
        // handle global process error
        const cause = JSON.parse(event.processError.Cause) as {
            errorType: string,
            errorMessage: string,
        };
        await finish(event, cause.errorMessage)
    }
}

async function finish(event: DeliveryProcessState, errorMessage?: string) {
    const historyId = event.deliveryConfig.historyRecordId
    const history = await getHistoryRecord(historyId)
    if (errorMessage) {
        await updateWithError(historyId, {errorMessage})
    } else {
        await updateFinished(historyId)
    }
    const notification: DeliveryResultEventPayload = {
        finishedAt: nowISO(),
        deliveredCount: history.deliveriesCount,
        targetsCount: history.targetsCount,
        failedCount: history.targetsCount - history.deliveriesCount,
        executionId: event.executionId, // TODO
        distributionType: history.distributionType,
        message: errorMessage || 'success',
        result: errorMessage ? 'error' : 'success',
        configurationId: history.configurationId,
        historyId
    }
    await notify(NOTIFICATONS.types.DELIVERY_FINISHED, notification)
}
