import {findConfigById} from "../old/services/reminders/dbhelpers.js";
import console from "console";
import {ConditionsToSearchParamsConverter, <PERSON>minderHelper, ReminderStatusHelper} from "./reminder-helpers.js";
import {getReminderStatus, loadMessages, updateReminderStateRecord, upsertHistoryRecord} from "./ext.js";
import {
    ReminderExecutionHistoryRecord,
    ReminderExecutionState,
    ReminderStatusRecord
} from "../../platform/types/reminders.js";
import {createConfig, updateMessages} from "../control-api/repo/repo-configs.js";
import {DistributionType, TargetsSelectionType} from "@oss/shared/lib/distribution/constants.js";
import {CreateInitialHistory} from "./history-statuses.js";
import {
    DeliveryResultEvent,
    DeliveryResultEventPayload,
    NOTIFICATONS,
    notify,
    RunDistributionCommandEvent
} from "../module.js";


type ReminderSchedulerEvent = {
    RunReminder: {
        reminderId: string,
    }
}

export async function handler(event: ReminderSchedulerEvent | DeliveryResultEvent) {
    if (event['RunReminder']) {
        await handleSchedulerCall(event as ReminderSchedulerEvent)
    } else {
        const payload = (event as DeliveryResultEvent).detail
        await handleReminderDeliveryFinished(payload)
    }
}

async function handleReminderDeliveryFinished(payload: DeliveryResultEventPayload) {
    console.log(JSON.stringify(payload, null, 2))

    // look for a state record, that stores a key for history record
    const reminderStatus = await getReminderStatus(payload.configurationId);
    if (!reminderStatus) {
        console.log('No status record for reminder with ID=', payload.configurationId)
        return
    }

    // update history record
    const resultState:ReminderExecutionState  = payload.result === 'success' ? "SUCCESS" : 'ERROR'
    const patch: Partial<ReminderExecutionHistoryRecord> = {
        state: resultState,
        deliveredCount: payload.deliveredCount,
        message: payload.message,
        targetsCount: payload.targetsCount,
        failedCount: payload.targetsCount - payload.deliveredCount,
        finishedAt: payload.finishedAt,
        distributionId: payload.executionId,
        distribution: {
            distributionId: payload.executionId,
            errorMessage: resultState === 'ERROR' ? payload.message : undefined
        }
    }
    await upsertHistoryRecord(patch, reminderStatus.lastRunHistoryKey)

    // update state record
    await updateReminderStateRecord({
        state: "WAITING",
        lastRunResult: {
            state: resultState,
            distributionId: patch.distributionId,
            finishedAt: patch.finishedAt,
            deliveredCount: patch.deliveredCount,
            failedCount: patch.failedCount,
            targetsCount: patch.targetsCount,
            startedAt: reminderStatus.latestStartTime,
        }
    }, payload.configurationId)
}



export async function handleSchedulerCall(event: ReminderSchedulerEvent) {
    console.log(JSON.stringify(event, null, 2))
    // fetch reminder
    const reminderId = event.RunReminder.reminderId
    const [configurationId, localReminderId] = reminderId.split('#')
    const remindersConfig = await findConfigById(configurationId);
    if (!remindersConfig) {
        console.log('No configuration found for configurationId=', configurationId)
        return
    }
    const reminder = remindersConfig.settings.find(c => c.reminderLocalId === localReminderId)
    if (!reminder) {
        console.log('No configuration reminder with localId=', localReminderId, 'found in configuration with id=', configurationId);
        return
    }


    const reminderHelper = new ReminderHelper({
        reminderId,
        configuration: remindersConfig,
        parameters: reminder
    })
    const shouldRunNow = await isShouldRunNow(reminderHelper)
    if (!shouldRunNow) {
        return;
    }

    const appointmentDays = reminderHelper.getTargetDates();
    if (appointmentDays.length === 0) {
        return null;
    }

    const filterDate = reminderHelper.isAppointmentReminder() ? {
        appointment_date: {
            from: appointmentDays[0],
            to: appointmentDays[0],
        }
    } : {}
    const filterCommon = reminderHelper.isDateRelativeReminder() ? {
        [remindersConfig.dateItemKey]: appointmentDays,
    } : {}
    filterCommon['check'] = ['未対応', '対応中', '対応済', '完了', '保留']

    if (remindersConfig.conditions) {
        const conditions = new ConditionsToSearchParamsConverter(remindersConfig.conditions)
            .convertConditions()
        Object.assign(filterCommon, conditions);
    }
    const filterCategories =
        (reminderHelper.isAppointmentReminder() && remindersConfig.categoryId)
            ? [remindersConfig.categoryId]
            : null;

    const {id: engineConfigurationId} = await createConfig({
        distributionType: DistributionType.REMINDER,
        distributionName: remindersConfig.name,
        enabled: true,
        messageIds: reminder.messages,
        targetSelectionType: TargetsSelectionType.SURVEY_CONDITIONS,
        surveyConditions: {
            surveyId: remindersConfig.surveyId,
            conditions: [],
            filter: {
                filterCommon,
                filterCategories,
                filterDate
            }
        }
    }, reminderId)

    const messages = await loadMessages(reminder.messages);
    await updateMessages(reminderId, messages.map(((m, i) => {
        return {
            id: reminder.messages[i],
            contents: m,
        }
    })))

    const remindersHistory = new CreateInitialHistory(reminderHelper.getSettings())
    await remindersHistory.loadReferences()
    await remindersHistory.run()

    // send run distribution notification
    const runConfigEvent: RunDistributionCommandEvent = {
        RunConfiguration: {
            configurationId: engineConfigurationId
        }
    }
    await notify(NOTIFICATONS.types.RUN_CONFIGURATION, runConfigEvent)
}

async function isShouldRunNow(helper: ReminderHelper) {
    if (!helper.isInActiveInterval()) {
        console.log('Is not in active interval')
        return false;
    }
    // if (!helper.isAfterFirstRunDay()) {
    //     console.log('Is not after first run day')
    //     return false;
    // }
    if (!helper.hasAnyTargetDates()) {
        console.log('No target dates exists')
        return false;
    }
    // const status = await getReminderStatus(helper.getSettings().reminderId);
    // if (status) {
    //     const result = checkReminderStateAndLimit(status);
    //     if (result) {
    //         console.log('Can start now', status);
    //     }
    //     return result;
    // }
    return true;
}

function checkReminderStateAndLimit(status: ReminderStatusRecord) {
    const statusHelper = new ReminderStatusHelper(status);
    if (!statusHelper.isActiveNow()) {
        return false;
    }
    if (statusHelper.isRunning()) {
        return false;
    }
    if (statusHelper.isAlreadyRunToday()) return false;
    return true;
}
