/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */


import {AWS} from "../../common/utils/aws-helper.js";
import {lambdaEnv} from "../../common/lambda-env.js";
import {ENV} from "../module.js";
import {
  CategoryRecord,
  ReminderExecutionHistoryRecord,
  RemindersTableKey,
  ReminderStatusRecord, SurveyConfigRecord
} from "../../platform/types/reminders.js";
import {MessageRecord} from "../../api-admin-survey/types/survey-distribution.js";
import {updateOperation} from "../../common/utils/index.js";


function dbClient() {
  return new AWS.DynamoDB.DocumentClient({});
}

export async function getReminderStatus(reminderId: string) {
  const dbResponse = await dbClient().get({
    TableName: lambdaEnv.getEnv(ENV.DATABASE_REMINDERS),
    Key: {
      partitionKey: 'status',
      sortKey: reminderId,
    },
  }).promise();
  return dbResponse.Item as ReminderStatusRecord
}

export async function loadMessages(messageIds: string[]) {
  return Promise.all(messageIds.map(async (id) => {
    return dbClient().get({
      TableName: lambdaEnv.getEnv(ENV.DATABASE_MESSAGES),
      Key: {
        primaryKey: id,
      }
    }).promise().then(v => v.Item as MessageRecord).then(v => v.contents)
  }))
}
export async function loadDependencyDetails(surveyId: string, categoryId: string) {
  const [survey, category] = await Promise.all([loadSurveyConfig(surveyId), loadCategory(categoryId)]);
  return {
    survey, category
  }
}
export async function loadSurveyConfig(surveyId: string) {
  return (await dbClient().get({
    TableName: lambdaEnv.getEnv(ENV.DATABASE_SURVEY_CONFIGS),
    Key: {
      surveyId: surveyId,
    }
  }).promise()).Item as SurveyConfigRecord;
}
export async function loadCategory(categoryId: string) {
  if (!categoryId) {
    return null;
  }
  return (await dbClient().get({
    TableName: lambdaEnv.getEnv(ENV.DATABASE_CALENDARS),
    Key: {
      partitionKey: 'categories',
      sortKey: categoryId,
    }
  }).promise()).Item as CategoryRecord;
}
export async function  upsertHistoryRecord(patch: Partial<ReminderExecutionHistoryRecord>, key: RemindersTableKey) {
  const dbResponse = await updateOperation(dbClient()).update({
    tableName: lambdaEnv.getEnv(ENV.DATABASE_REMINDERS),
    key: key,
    patch: patch
  });
  return dbResponse as ReminderExecutionHistoryRecord
}
export async function updateReminderStateRecord(patch: Partial<ReminderStatusRecord>, sortKey: string, condition?: {[key :string]: any}) {
  const dbResponse = await updateOperation(dbClient()).update({
    tableName: lambdaEnv.getEnv(ENV.DATABASE_REMINDERS),
    key: {
      partitionKey: 'status',
      sortKey: sortKey,
    },
    patch,
    conditionCheck: condition,
  });
  return dbResponse as ReminderStatusRecord;
}
