import {apiHandlerFactory} from "common/admin-api/api-lambda.js";
import * as segmentDeliveryNew from './old/controllers/new-segment-delivery.js'
import * as segmentDelivery from './old/controllers/segment-delivery.js'
import * as broadcastMessages from './old/controllers/broadcast_messages.js'
import * as distResources from './old/controllers/distribution_resources.js'
import * as reminders from './old/controllers/reminders.js'
import {listDrafts, listExternal, listPostponed, listRepeating, listTalks} from "./control-api/configs-list.js";
import * as configCrud from "./control-api/configs-crud.js";
import {getDetails, listHistory} from "./control-api/history.js";
import {runBatchSelected, runInstant, runInstantWithFilter} from "./control-api/run-instant.js";
import {sendLineMessage} from "./control-api/send-message.js";


// old API
export const main = apiHandlerFactory('/dist', api => {
    api.register(segmentDeliveryNew.routes, { prefix: '/new-segment-delivery' })
    api.register(segmentDelivery.routes, { prefix: '/segment-delivery' })
    api.register(broadcastMessages.routes, { prefix: '/broadcast_messages' })
    api.register(distResources.routes, { prefix: '/distribution_resources' })
    api.register(reminders.routes, { prefix: '/reservation-reminders' })
})

// new API
export const handler = apiHandlerFactory('/distribution', api => {

    // history listing
    api.get('/history/:fromDate/:toDate', listHistory)
    // history detailed
    api.get('/history/:id', getDetails)

    // distribution configs listing
    api.get('/configs/postponed', listPostponed)
    api.get('/configs/repeating', listRepeating)
    api.get('/configs/external', listExternal)
    api.get('/configs/talk', listTalks)
    api.get('/configs/drafts', listDrafts)

    // distribution configs CRUD
    api.get('/configs/:id', configCrud.get)
    api.post('/configs', configCrud.create)
    api.put('/configs/:id', configCrud.update)
    api.delete('/configs/:id', configCrud.remove)

    api.get('/configs/resources/upload', configCrud.getImageUploadUrl)

    // create and run instant delivery
    api.post('/instant', runInstant)
    api.post('/instant/selected', runBatchSelected)
    api.post('/instant/filter', runInstantWithFilter)

    // send LINE message directly
    api.post('/message', sendLineMessage)

})
