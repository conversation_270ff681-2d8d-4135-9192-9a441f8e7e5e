/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import config from '../../api-reception/config/static.js';
import c from '../../api-reception/config/constants.js';
import * as db from '../../survey/utils/db.js';
import { getEnvironmentCredentials } from '../../survey/utils/aws-helper.js';

import { SurveyResultsService } from './../../survey/services/survey-results-service.js';
import memberConfigsService from './member-config-service.js';
import { MemberResultsHelper } from './../../survey/services/helpers/sr_helper.js';
import { MemberResult } from './../../survey/services/helpers/survey/results_model.js';
import { ItemsMergeHelper } from './../../survey/services/helpers/item-merge-helper.js';

class MemberResultsService extends SurveyResultsService {
  private surveyResultsService: SurveyResultsService;
  constructor() {
    super();
    this.surveyResultsService = new SurveyResultsService();
  }

  getTableName() {
    return config.get(c.TABLE_MEMBER_RESULTS);
  }

  async tryPlaceReservationIfPresents(items, configs) {
    // NOT IMPLEMENTED
  }

  async indexSearch(memberConfig, fieldKey, queryParams) {
    const helper = new MemberResultsHelper(memberConfig, {
      awsConfig: getEnvironmentCredentials(),
      resultsTable: this.getTableName(),
    });
    const indexableParameterFilter = queryParams.surveyResults.find((res) => res.itemKey === fieldKey);

    if (!indexableParameterFilter || !indexableParameterFilter.value) {
      return [];
    }

    const indexSearchResult = await helper.queryResultsByField(fieldKey, indexableParameterFilter.value);

    const mergeHelper = new ItemsMergeHelper();

    const surveyResults = MemberResult.buildFromLines(
      indexSearchResult.items,
      memberConfig
    );

    const mergedResults = mergeHelper.groupAndMerge(indexSearchResult.items);

    const filtered = mergedResults.filter((item) => {
      const result = surveyResults.find(i => i.answers[0].partitionKey === item.partitionKey)
      if (result.matches({
          logic: 'and',
          fields: queryParams.surveyResults,
        })){
        return item;
      }
    });

    return filtered;
  }

  async getReservationData(surveyId, itemKey, userId) {
    // NOT IMPLEMENTED
  }

  async checkNumberingExist(memberSurveyId, memberNumberString) {
    let memberNumberExistFlag = true;

    let memberConfig:any = await this.getSurveyConfig(memberSurveyId, true);

    let memberResults = null;
    let surveySchema = null;
    let memberNumberConfig = null;
    let sortKey = '';

    if (memberConfig) {
      if (Array.isArray(memberConfig) && memberConfig.length > 0) {
        memberConfig = memberConfig[0];
      }

      const memberSurveySubIdString = ('' + memberConfig.memberSurveySubId).padStart(4, '0');
      surveySchema = memberConfig.surveySchema;

      memberNumberConfig = surveySchema.find((schema) => schema.type === 'memberNumber');
      
      //console.log('memberNumberConfig', memberNumberConfig); 

      const memmberNum = memberNumberString.length <= 7 ? memberNumberString : memberNumberString.substring(4);

      sortKey = memberNumberConfig.itemKey + '#' + memmberNum;

      //console.log('sortkey', sortKey);

      //console.log('sortKey', sortKey);
      //console.log('surveyId', memberConfig.surveyId);

      if (memberNumberConfig) {
        const query_params = {
          IndexName: 'surveyId-sortKey-index',
          keyExpression: 'surveyId = :surveyId and sortKey = :sortKey',
          expressionValues: {
            ':surveyId': { S: memberConfig.surveyId },
            ':sortKey': { S: sortKey },
          },
        };

        memberResults = await this.queryByIndex(query_params);
        if (Array.isArray(memberResults) && memberResults.length === 0) {
          memberNumberExistFlag = false;
        }
      }
    }

    return {
      memberNumberExistFlag,
    };
  }

  async findByUserIdAndSurveyId(userId, surveyId) {
    const mergeHelper = new ItemsMergeHelper();

    const query_params = {
      IndexName: 'userId-surveyId-index',
      keyExpression: 'userId = :userId and surveyId = :surveyId',
      expressionValues: {
        ':userId': {S: userId},
        ':surveyId': {S: surveyId}
      },
    }

    const queryResult = await this.queryByIndex(query_params)

    return mergeHelper.groupAndMerge(queryResult);
  }

  async findByPartitionKey(partitionKey) {
    const query_params = {
      keyExpression: 'partitionKey = :partitionKey',
      expressionValues: {
        ':partitionKey': {S: partitionKey}
      }
    }

    return await this.query(query_params);
  };

  async updateUserIdOfFetchedResults(mergedMemberResults, userId){
    for (const mergedResult of mergedMemberResults) {
      if (mergedResult.userId && mergedResult.userId[0] !== 'U'){
        const savedMemberResults = await this.findByPartitionKey(mergedResult.partitionKey);

        await this.updateItemsWithoutDelete(savedMemberResults.map(obj => {
          return {
            partitionKey: obj.partitionKey,
            sortKey: obj.sortKey,
            userId: userId,
          }
        }));

        //update the userId of all surveyResults where userId is equal to original I, mergedResult.userId
        //to keep the symbolic link between surveyResults and this memberResults
        await this.updateUserIdOfLinkedSurveyResults(
          mergedResult.surveyId,
          mergedResult.userId,
          userId
        );
      }
    }
  };

  async updateUserIdOfLinkedSurveyResults(memberConfigId, oldUserId, newUserId) {
    // Get member config
    try {
      const memberConfig = await memberConfigsService.getSurveyConfig(memberConfigId);

      for (const linkedSurveyConfigId of memberConfig.linkedForms) {
        // Update all survey results that match the oldUserId and linked survey config id
        const surveyResults = await this.surveyResultsService.queryByIndex({
          IndexName: 'userId-surveyId-index',
          keyExpression: 'userId = :userIdValue and surveyId = :surveyIdValue',
          expressionValues: {
            ':userIdValue': { S: oldUserId },
            ':surveyIdValue': { S: linkedSurveyConfigId }
          }
        });

        if (surveyResults.length > 0) {
          await this.surveyResultsService.updateItemsWithoutDelete(surveyResults.map(obj => {
            return {
              partitionKey: obj.partitionKey,
              sortKey: obj.sortKey,
              userId: newUserId,
            }
          }));
        }
      }
    } catch (error: any) {
      // Ignore errors
      console.error('[ERROR]:' + error.stack);
    }
  }

  async getResultsBySurveyItemKeys(surveyId, itemKeys) {
    // Due to KeyConditionExpression limitation, we must query for each itemId

    const results = [];

    for (const itemKey of itemKeys) {
      const params = {
        TableName: this.getTableName(),
        IndexName: 'surveyId-sortKey-index',
        ExpressionAttributeValues: {
          ':sid': { S: surveyId },
          ':ik': { S: itemKey },
        },
        KeyConditionExpression: 'surveyId = :sid AND begins_with(sortKey,:ik)',
      };

      const queryResults = await db.queryList(params);
      results.push(...queryResults);
    }

    return results;
  }
}

const instance = new MemberResultsService();
export default instance;