/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import * as db from '../../survey/utils/db.js';
import config from '../../api-reception/config/static.js';
import c from '../../api-reception/config/constants.js';

import { SurveyConfigsService } from './../../survey/services/survey-configs-service.js';
import { NotFound } from '../../common/utils/exceptions.js';

class MemberConfigsService extends SurveyConfigsService {
  getTableName() {
    return config.get(c.TABLE_MEMBER_CONFIGS);
  }

  generateKeyParams(surveyId) {
    return {
      TableName: this.getTableName(),
      Key: {
        surveyId: { S: surveyId },
      },
    };
  }

  async getSurveyConfig(surveyId) {
    if (!surveyId) {
      throw new NotFound('無効なURL');
    }

    const config = await db.getOne(this.generateKeyParams(surveyId));

    if (!config || config.surveyStatus !== 'enable') {
      throw new NotFound('無効な帳票ID');
    }

    return config;
  }
}

const instance = new MemberConfigsService();
export default instance;