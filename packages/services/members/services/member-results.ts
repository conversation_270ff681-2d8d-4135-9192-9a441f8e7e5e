/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import AWS from 'aws-sdk';
import * as db from '../../common/admin-api/utils/db.js';
import _ from 'lodash';
import moment from 'moment';
moment.locale('ja');
import c from '../../common/admin-api/config/constants.js';
import { DDClient } from 'common/utils/index.js';
import { getEnvironmentCredentials } from '../../common/admin-api/utils/aws-helper.js';
import config from '../../common/admin-api/config/static.js';
import { getFormatedAnswerCode } from '../../common/admin-api/utils/util.js';
import { MemberResultsHelper } from '../../api-admin-survey/services/survey-results/search/sr_index/sr_helper.js';
import { ItemsMergeHelper } from '../../api-admin-survey/services/survey-results/search/all.js';
import * as surveyService from '../../api-admin-survey/services/survey-results.js';
import {
  scanList,
  queryList,
  UpdateExpressionBuilder,
  updateOne,
  deleteOne,
  batchWriteItem,
  createOne,
} from '../../common/admin-api/utils/db.js';
import { tableSurveyResults } from '../../common/utils/aws-clients.js';

import { SurveyResultsHistoryHelper } from '../../api-admin-survey/utils/survey-results-history-helper.js';

const dynamoDbTable = () => {
  return config.get(c.TABLE_MEMBER_RESULTS);
};

const dynamoDbSurveyConfigTable = () => {
  return config.get(c.TABLE_MEMBER_CONFIGS);
};

const memberResultsClient = () => {
  return tableSurveyResults(dynamoDbTable());
};

const getLinkedMemberOfSurveyResults = async (memberFormId, userIds) => {
  const dbClient = new DDClient.Table(dynamoDbTable(), null, getEnvironmentCredentials());

  const items:any[] = [];

  for (const userId of userIds) {
    // Query params
    const params = {
      index: 'userId-surveyId-index',
      query: '#userId = :userIdValue and #surveyId = :surveyIdValue',
      mapping: {
        '#surveyId': 'surveyId',
        '#userId': 'userId',
        ':surveyIdValue': memberFormId,
        ':userIdValue': userId,
      },
    };

    const queryResult = await dbClient.queryItems(params);

    items.push(...queryResult);
  }

  // Merge results
  const mergeHelper = new ItemsMergeHelper();
  return mergeHelper.groupAndMerge(items);
};

const getSurveyConfig = async (surveyId) => {
  return await queryList({
    TableName: dynamoDbSurveyConfigTable(),
    KeyConditionExpression: 'surveyId = :s_id',
    ExpressionAttributeValues: {
      ':s_id': { S: surveyId },
    },
  });
};

const createSurveyResultsHelper = async (surveyId) => {
  const surveyConfig = (await getSurveyConfig(surveyId))[0];
  return new MemberResultsHelper(surveyConfig, {
    awsConfig: getEnvironmentCredentials(),
    resultsTable: dynamoDbTable(),
  });
};

const convertToEachUser = (results) => {
  return results.reduce(
    (h, obj) =>
      Object.assign(h, {
        [obj.userId]: (h[obj.userId] || []).concat(obj),
      }),
    {}
  );
};

const groupByPartitionKey = (results) => {
  return results.reduce(
    (h, obj) =>
      Object.assign(h, {
        [obj.partitionKey]: (h[obj.partitionKey] || []).concat(obj),
      }),
    {}
  );
};

const create = async (items) => {
  for (let i = 0; i < items.length; ++i) {
    const item = items[i];
    const marshalled = AWS.DynamoDB.Converter.marshall(item, {
      convertEmptyValues: true,
    });
    await createOne({
      TableName: dynamoDbTable(),
      Item: marshalled,
    });
  }
};

const getAll = async () => {
  const tableName = dynamoDbTable();
  const list = await scanList({
    TableName: tableName,
  });
  return convertToEachUser(list);
};

const getByDbPatterns = async (surveyId, updateFrom, lastEvaluatedKey, searchKeyword) => {
  const from = updateFrom || 0;
  const tableName = dynamoDbTable();
  const dbClient = new DDClient.Table(tableName, null, getEnvironmentCredentials());

  const queryParams = {
    lastEvaluatedKey: lastEvaluatedKey,
    index: 'surveyId-updatedAt-index',
    filter: 'attribute_not_exists(deletedAt)',
    query: 'surveyId = :v_id and updatedAt > :f',
    mapping: {
      ':v_id': surveyId,
      ':f': from,
    },
  };

  if (searchKeyword !== undefined) {
    queryParams.filter += ' AND begins_with(#value, :searchKeyword)';
    queryParams.mapping[':searchKeyword'] = searchKeyword;
    queryParams.mapping['#value'] = 'value';
  }

  const result = await dbClient.queryPage(queryParams);

  if (result.items.length > 0 && searchKeyword) {
    const partitionKey = result.items[0].partitionKey;
    const keywordResult = await getByPartitionKey(partitionKey);
    return { items: keywordResult, lastEvaluatedKey: result.lastEvaluatedKey };
  }

  return result;
};

const getWithDatabaseFilter = async (surveyId, filterCommon, lastEvaluatedKey, searchKeyword) => {
  const helper = await createSurveyResultsHelper(surveyId);
  const indexedKeys = helper.getIndexedKeys();
  let indexedSearchArguments = _.pick(filterCommon, ...indexedKeys);
  indexedSearchArguments = _.pickBy(indexedSearchArguments, (value: any) => !_.isEmpty(value));
  if (!_.isEmpty(indexedSearchArguments)) {
    const searchKey = Object.keys(indexedSearchArguments)[0];
    const value = indexedSearchArguments[searchKey];
    return await helper.queryResultsByField(searchKey, value, lastEvaluatedKey);
  }
  return await getByDbPatterns(surveyId, 0, lastEvaluatedKey, searchKeyword);
};

const get = async (surveyId, updateFrom?) => {
  const from = updateFrom || 0;

  const filteredList = await queryList({
    TableName: dynamoDbTable(),
    IndexName: 'surveyId-updatedAt-index',
    FilterExpression: 'attribute_not_exists(deletedAt)',
    KeyConditionExpression: 'surveyId = :v_id and updatedAt > :from ',
    ExpressionAttributeValues: {
      ':v_id': { S: surveyId },
      ':from': { N: `${from}` },
    },
  });
  return groupByPartitionKey(filteredList);
};

const getSearchedAndSortedList = async (configs, searchCriteria) => {
  const { filterCommon, filterDate, searchKeyword, sortBy, sortDesc } = searchCriteria;

  // get all data
  const results = await get(configs.surveyId);

  const categoryList:any = {};

  // format
  const data:any[] = [];
  for (const partitionKey in results) {
    const row:any = {
      partitionKey: partitionKey,
      fields: [],
    };
    const result = results[partitionKey][0];
    row.createdAt = result.createdAt;
    row.updatedAt = result.updatedAt;
    row.check = result.check || '未対応';
    if (configs && configs.isAppending && configs.isAppending.value === true) {
      row.note = result.note || '';
      if (result.answerCode) {
        row.answerCode = getFormatedAnswerCode(result.answerCode);
      } else {
        row.answerCode = '';
      }
    }
    row.userId = result.userId;
    row.id = result.userId;
    results[partitionKey].map((question) => {
      const _questionConfig = configs.surveySchema.find((obj) => obj.itemKey === question.itemKey);
      if (_questionConfig && _questionConfig.type === 'checkboxes') {
        if (!row.fields[question.itemKey]) {
          row.fields.push({ itemKey: question.itemKey, value: null });
        }
        if (question.value) {
          row.fields.push({ itemKey: question.itemKey, value: question.value });
        }
      } else if (_questionConfig && _questionConfig.type === 'reservation') {
        row.bunruiIdFull = question.value;
        if (row && row.bunruiIdFull) {
          const bunruiIdIndex = row.bunruiIdFull.indexOf('|');
          if (bunruiIdIndex >= 0) {
            row.bunruiId = row.bunruiIdFull.substr(0, bunruiIdIndex);

            const reservationDate = row.bunruiIdFull.substr(bunruiIdIndex + 1);
            const reservationDateIndex = reservationDate.indexOf('|');
            if (reservationDateIndex >= 0) {
              row.yoyakuNichi = reservationDate.substr(0, reservationDateIndex);
              row.yoyakuJi = reservationDate.substr(reservationDateIndex + 1);

              if (row.yoyakuNichi && row.yoyakuNichi.length === 8) {
                row.yoyakuNichiLabel =
                  row.yoyakuNichi.substr(0, 4) +
                  '-' +
                  row.yoyakuNichi.substr(4, 2) +
                  '-' +
                  row.yoyakuNichi.substr(6, 2);
              }

              if (row.yoyakuJi && row.yoyakuJi.length === 4) {
                row.yoyakuJiLabel = row.yoyakuJi.substr(0, 2) + ':' + row.yoyakuJi.substr(2, 2) + ':' + '00';
              }

              if (row.yoyakuNichiLabel && row.yoyakuJiLabel) {
                row.yoyakuNichijiLabel = row.yoyakuNichiLabel + ' ' + row.yoyakuJiLabel;
              }
            }
          }
        } else {
          row.bunruiId = null;
        }

        row.fields.push({ itemKey: question.itemKey, value: question.value });
      } else {
        if (_questionConfig) {
          if (_questionConfig.type !== 'linkbutton') {
            row.fields.push({ itemKey: question.itemKey, value: question.value });
          }
        }
      }
    });

    if (row.bunruiId) {
      row.bunrui = { ...categoryList[row.bunruiId] };
      if (row.bunrui) {
        categoryList[row.bunruiId] = { ...row.bunrui };

        if (row.bunrui.tag1) {
          row.bunruiTag1 = row.bunrui.tag1;
        }
        if (row.bunrui.tag2) {
          row.bunruiTag2 = row.bunrui.tag2;
        }
        if (row.bunrui.tag3) {
          row.bunruiTag3 = row.bunrui.tag3;
        }
      }
    }

    _.size(row) > 0 ? data.push(row) : null;
  }

  // filter
  // search
  const filtered = data.filter((obj) => {
    let _compareResult = true;
    _compareResult = Object.entries(obj).some((value) => {
      //compare keyword
      const _compareKeyword =
        !searchKeyword ||
        (value[1] &&
          !['createdAt', 'updatedAt', 'id', 'check'].includes(value[0]) &&
          value[1].toString().toLowerCase().includes(searchKeyword.toLowerCase()));

      //compare multi choice
      const _compareMultiChoice = Object.entries(filterCommon).every((searchCondition:[string, any]) => {
        if (searchCondition[1] && searchCondition[1].length > 0) {
          //if condition is array
          if (typeof obj[searchCondition[0]] === 'object') {
            return obj[searchCondition[0]].some((itemValue) => searchCondition[1].includes(itemValue));
          } else {
            return searchCondition[1].includes(obj[searchCondition[0]]);
          }
        }
        return true;
      });

      //compare date
      const _compareDate = Object.entries(filterDate).every((dateCondition: [string, any]) => {
        if (dateCondition[1]) {
          return (
            //compare date from
            (!dateCondition[1].from ||
              obj[dateCondition[0]] >= moment(dateCondition[1].from, 'YYYY-MM-DD').startOf('day').unix()) &&
            //compare date to
            (!dateCondition[1].to ||
              obj[dateCondition[0]] <= moment(dateCondition[1].to, 'YYYY-MM-DD').endOf('day').unix())
          );
        }
        return true;
      });
      return _compareKeyword && _compareMultiChoice && _compareDate;
    });
    return _compareResult;
  });

  filtered.map(function (elem, index) {
    if (elem && (elem['isAppending'] === undefined || elem['isAppending'] === null)) {
      elem['isAppending'] = { value: false };
    }
    return elem;
  });

  // sort
  const sorted = filtered.sort((a, b) => {
    for (let i = 0; i < sortBy.length; i++) {
      const keyName = sortBy[i];
      const desc = sortDesc[i];

      if (a[keyName] === b[keyName]) {
        continue;
      }
      if (a[keyName] > b[keyName]) {
        return desc ? -1 : 1;
      } else {
        return desc ? 1 : -1;
      }
    }
  });

  return sorted;
};

const getByPartitionKey = async (partitionKey) => {
  return await queryList({
    TableName: dynamoDbTable(),
    KeyConditionExpression: 'partitionKey = :pk ',
    ExpressionAttributeValues: {
      ':pk': { S: partitionKey },
    },
  });
};

const getTotalMemberSurveySubId = async () => {
  const params:any = {
    TableName: dynamoDbSurveyConfigTable(),
  };
  const list = await scanList(params);

  return {
    length: list.length,
  };
};

const getLastResultNumber = async (surveyId) => {
  const result = await getSurveyConfig(surveyId);

  let surveyConfig:any = { lastResultNumber: 0 };
  if (result && Array.isArray(result) && result.length > 0) {
    surveyConfig = result[0];
  }

  return {
    lastResultNumber: surveyConfig.lastResultNumber,
  };
};

const checkSaibanExist = async (surveyId, saibanString, memberConfig = null) => {
  let saibanExistFlag = true;

  if (!memberConfig) {
    memberConfig = await getSurveyConfig(surveyId);
  }
  let memberResults:any = null;
  let surveySchema:any = null;
  let saibanConfig:any = null;
  let userSearchKey = '';

  if (memberConfig) {
    if (Array.isArray(memberConfig) && memberConfig.length > 0) {
      memberConfig = memberConfig[0];
    }

    surveySchema = memberConfig.surveySchema;

    saibanConfig = surveySchema.find((schema) => schema.type === 'memberNumber');

    userSearchKey = saibanConfig.itemKey + '#' + saibanString.slice(4);

    //console.log('userSearchKey###', userSearchKey);

    if (saibanConfig) {
      const query_params:any = {
        TableName: dynamoDbTable(),
        IndexName: 'surveyId-userSearchKey-index',
        FilterExpression: 'surveyId = :surveyId and userSearchKey = :userSearchKey',
        ExpressionAttributeValues: {
          ':surveyId': { S: memberConfig.surveyId },
          ':userSearchKey': { S: userSearchKey },
        },
      };

      memberResults = await scanList(query_params);
      if (Array.isArray(memberResults) && memberResults.length === 0) {
        saibanExistFlag = false;
      }
    }
  }

  return {
    saibanExistFlag,
  };
};

const deleteSurveyResultWithoutLatest = async (partitionKey, latestUpdatedAt) => {
  const results = await getByPartitionKey(partitionKey);
  if (results.length === 0) {
    return;
  }

  const tableName = dynamoDbTable();
  for (let i = 0; i < results.length; i++) {
    const result = results[i];
    if (result.updatedAt === latestUpdatedAt) {
      continue;
    }

    await deleteOne({
      TableName: tableName,
      Key: {
        partitionKey: { S: result.partitionKey },
        sortKey: { S: result.sortKey },
      },
    });
  }
};

const update = async (items) => {
  for (let i = 0; i < items.length; i++) {
    const item = items[i];

    const Key:any = {
      partitionKey: { S: item.partitionKey },
      sortKey: { S: item.sortKey },
    };

    const updateItem = Object.assign({}, item);
    if (!item.reservationKeyOld) {
      delete updateItem.reservationKeyOld;
    }
    if (!item.reservationKeyNew) {
      delete updateItem.reservationKeyNew;
    }
    delete updateItem.partitionKey;
    delete updateItem.sortKey;

    if ('userId' in updateItem && (updateItem.userId === null || updateItem.userId === undefined)) {
      const splitPartitionKey = item.partitionKey.split('#');
      if (splitPartitionKey.length >= 2) {
        updateItem.userId = splitPartitionKey[1];
      } else {
        delete updateItem.userId;
      }
    }
    
    const updateBuilder = new UpdateExpressionBuilder(updateItem);
    updateBuilder.build();
    await updateOne({
      TableName: dynamoDbTable(),
      Key: Key,
      ExpressionAttributeNames: updateBuilder.getExpressionAttributeNames(),
      ExpressionAttributeValues: updateBuilder.getExpressionAttributeValues(),
      UpdateExpression: updateBuilder.getSetExpression(),
    });
  }

  // 更新されなかったデータは削除
  await deleteSurveyResultWithoutLatest(items[0].partitionKey, items[0].updatedAt);
};

const updateConfigMemberSurveySubId = async (username, input) => {
  const currentUnixtimeSec = Math.floor(new Date().getTime() / 1000);
  const tableName = dynamoDbSurveyConfigTable();

  const surveyConfigObj:any = {
    updatedAt: currentUnixtimeSec,
    updatedBy: username,

    memberSurveySubId: input.memberSurveySubId,
  };

  if (!input.surveyId) {
    input.surveyId = input.surveyId;
  }

  const Key:any = {
    surveyId: { S: input.surveyId },
  };

  const updateBuilder = new UpdateExpressionBuilder(surveyConfigObj);
  updateBuilder.build();
  return await updateOne({
    TableName: tableName,
    Key: Key,
    ExpressionAttributeNames: updateBuilder.getExpressionAttributeNames(),
    ExpressionAttributeValues: updateBuilder.getExpressionAttributeValues(),
    UpdateExpression: updateBuilder.getSetExpression(),
  });
};

const updateConfigLastResultNumber = async (username, input) => {
  const currentUnixtimeSec = Math.floor(new Date().getTime() / 1000);
  const tableName = dynamoDbSurveyConfigTable();

  const surveyConfigObj:any = {
    updatedAt: currentUnixtimeSec,
    updatedBy: username,

    lastResultNumber: input.lastResultNumber,
  };

  if (!input.surveyId) {
    input.surveyId = input.surveyId;
  }
  const Key:any = {
    surveyId: { S: input.surveyId },
  };

  const updateBuilder = new UpdateExpressionBuilder(surveyConfigObj);
  updateBuilder.build();
  return await updateOne({
    TableName: tableName,
    Key: Key,
    ExpressionAttributeNames: updateBuilder.getExpressionAttributeNames(),
    ExpressionAttributeValues: updateBuilder.getExpressionAttributeValues(),
    UpdateExpression: updateBuilder.getSetExpression(),
  });
};

const deleteSurveyResult = async (configs, searchCriteria, allColumns) => {
  // 物理削除

  const client = memberResultsClient();

  const keys:any[] = [];

  for (let i = 0; i < allColumns.length; ++i) {
    const item = allColumns[i];
    const results = await getByPartitionKey(item.partitionKey);
    if (results.length === 0) {
      continue;
    }

    for (let i = 0; i < results.length; ++i) {
      const result = results[i];

      keys.push({
        pk: result.partitionKey,
        sk: result.sortKey,
      });
    }
  }

  await client.deleteItems(keys);

  return allColumns;
};

const updateUserIdOfLinkedSurveyResult = async (userId, oldUserId, memberConfigId, req) => {
  const config = (await getSurveyConfig(memberConfigId))[0];
  const resultsHistoryHelper = new SurveyResultsHistoryHelper();
  if (!config.linkedForms || config.linkedForms.length === 0) {
    return;
  }
  let results = [];
  for (const id of config.linkedForms) {
    const _results = await surveyService.getResultsByFormAndUserId(id, oldUserId);
    results = [...results, ..._results.items];
  }
  if (results.length > 0) {
    for (const result of results) {
      if (!result.check || (result.check && result.check !== '取り消し')) {
        await surveyService.resetUserId(result.partitionKey, userId);
        await resultsHistoryHelper.logUserIdReset(req, result.partitionKey);
      } else {
        continue;
      }
    }
  }
  return;
};

const resetUserId = async (req, partitionKey, userId) => {
  let res;
  const results = await getByPartitionKey(partitionKey);
  if (results.length === 0) {
    return false;
  }
  const oldUserId = results[0].userId;
  const memberConfig = results[0].surveyId;
  for (let index = 0; index < results.length; index++) {
    const element = results[index];

    const key:any = {
      partitionKey: { S: partitionKey },
      sortKey: { S: element.sortKey },
    };

    res = await db.updateOne({
      TableName: dynamoDbTable(),
      ExpressionAttributeNames: {
        '#uId': 'userId',
      },
      ExpressionAttributeValues: {
        ':uId': { S: userId },
      },
      Key: key,
      UpdateExpression: 'SET #uId = :uId',
    });
  }
  if (res) {
    console.log('Update userId of survey results associated with member', userId, oldUserId, memberConfig);
    await updateUserIdOfLinkedSurveyResult(userId, oldUserId, memberConfig, req);
  }
  return res;
};

const createInsertDataForUpdateOptions = (oldRow, fromRegexp, to) => {
  const options:any = {
    partitionKey: { S: oldRow.partitionKey },
    sortKey: { S: oldRow.sortKey.replace(fromRegexp, to) },
    surveyId: { S: oldRow.surveyId },
    userId: { S: oldRow.userId },
    itemKey: { S: oldRow.itemKey },
    userSearchKey: { S: oldRow.userSearchKey.replace(fromRegexp, to) },
    createdAt: { N: `${oldRow.createdAt}` },
    updatedAt: { N: `${oldRow.updatedAt}` },
  };
  if (oldRow.value) {
    options.value = { S: oldRow.value.replace(fromRegexp, to) };
  } else {
    options.value = { NULL: true };
  }
  return options;
};

const updateOptions = async (surveyId, updateOptions) => {
  let promises = [];
  const tableName = dynamoDbTable();

  for (const [itemKey, value] of Object.entries(updateOptions)) {
    const fromList = await queryList({
      TableName: dynamoDbTable(),
      IndexName: 'surveyId-sortKey-index',
      KeyConditionExpression: 'surveyId = :v_id and sortKey = :v_skey',
      ExpressionAttributeValues: {
        ':v_id': { S: surveyId },
        ':v_skey': { S: `${itemKey}#${(value as any).from}` },
      },
    });

    const createParams:any = { RequestItems: {} };
    createParams.RequestItems[tableName] = [];
    const deleteParams:any = { RequestItems: {} };
    deleteParams.RequestItems[tableName] = [];
    const fromRegexp = new RegExp((value as any).from, 'g'); // global search for replace
    fromList.map(async (oldRow) => {
      const newRow = createInsertDataForUpdateOptions(oldRow, fromRegexp, (value as any).to);
      createParams.RequestItems[tableName].push({
        PutRequest: {
          Item: newRow,
        },
      });
      deleteParams.RequestItems[tableName].push({
        DeleteRequest: {
          Key: {
            partitionKey: { S: oldRow.partitionKey },
            sortKey: { S: oldRow.sortKey },
          },
        },
      });

      // batchWriteItem is max 25 operations
      if (createParams.RequestItems[tableName].length === 25) {
        const createPromise = batchWriteItem({ ...createParams });
        const deletePromise = batchWriteItem({ ...deleteParams });
        promises.push(createPromise, deletePromise);

        createParams.RequestItems[tableName] = [];
        deleteParams.RequestItems[tableName] = [];
      }
    });

    // execute if any left
    if (createParams.RequestItems[tableName].length > 0) {
      const createPromise = batchWriteItem({ ...createParams });
      const deletePromise = batchWriteItem({ ...deleteParams });
      promises.push(createPromise, deletePromise);
    }
  }

  // execute until there is no UnprocesssedItems in response
  do {
    const results = await Promise.all(promises);

    promises = [];
    results.map((result) => {
      if (Object.keys(result.UnprocessedItems).length === 0) {
        return;
      }

      const params:any = { RequestItems: {} };
      params.RequestItems[tableName] = result.UnprocessedItems[tableName];
      const promise = batchWriteItem(params);
      promises.push(promise);
    });
  } while (promises.length !== 0);
};

const getOneItemByExistsAnswerCode = async (partitionKey, sortKey) => {
  const key:any = {
    partitionKey: { S: partitionKey },
    sortKey: { S: sortKey },
  };

  const params:any = {
    TableName: dynamoDbTable(),
    Key: key,
    ProjectionExpression: 'answerCode',
  };
  const res = await db.getOne(params);
  return res;
};

const updateCounter = async (partitionKey, sortKey) => {
  const key:any = {
    partitionKey: { S: partitionKey },
    sortKey: { S: sortKey },
  };

  const res = await db.updateOne({
    TableName: dynamoDbTable(),
    ExpressionAttributeNames: {
      '#AN': 'answerCode',
    },
    ExpressionAttributeValues: {
      ':increase': { N: '1' },
    },
    Key: key,
    UpdateExpression: 'SET #AN = #AN + :increase',
  });
  return res;
};

const getResultsBySurveyItemKeys = async (surveyId, itemKeys) => {
  // Due to KeyConditionExpression limitation, we must query for each itemId

  const results:any[] = [];

  for (const itemKey of itemKeys) {
    const params:any = {
      TableName: dynamoDbTable(),
      IndexName: 'surveyId-sortKey-index',
      ExpressionAttributeValues: {
        ':sid': { S: surveyId },
        ':ik': { S: itemKey },
      },
      KeyConditionExpression: 'surveyId = :sid AND begins_with(sortKey,:ik)',
    };

    const queryResults = await db.queryList(params);
    results.push(...queryResults);
  }

  return results;
};

export {
  create,
  getAll,
  get,
  getSearchedAndSortedList,
  getByDbPatterns,
  getWithDatabaseFilter,
  getByPartitionKey,
  update,
  updateOptions,
  deleteSurveyResult,
  resetUserId,
  getSurveyConfig,
  getOneItemByExistsAnswerCode,
  updateCounter,

  getTotalMemberSurveySubId,
  getLastResultNumber,
  updateConfigMemberSurveySubId,
  updateConfigLastResultNumber,

  getLinkedMemberOfSurveyResults,
  checkSaibanExist,
  getResultsBySurveyItemKeys,
};
