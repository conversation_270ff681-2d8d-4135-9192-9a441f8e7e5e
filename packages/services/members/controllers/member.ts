/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *  - name: Member
 *    description: 会員情報帳票管理API
 */

import { getUsername, getUserGroup } from '../../common/admin-api/utils/auth.js';
import _ from 'lodash';
import { admin as a, guest as g} from '../../common/admin-middlewares/access-guards.js';
import * as service from '../services/member.js';
import * as memberResultsService from '../services/member-results.js';
import * as logger from '../../common/admin-api/utils/logger.js';
import { extractLastEvaluatedKey } from '../../platform/controllers/helpers/helpers.js';

/**
 * @openapi
 * /memberConfigs:
 *  post:
 *    summary: 会員情報を作成する
 *    tags: [Member]
 */
const create = async (req, res) => {
  const username = getUsername(req) || 'anonymous';
  const memberConfigJson = req.body;

  let validationResult = service.inputValidation(memberConfigJson);
  if (!validationResult) {
    logger.appLogInfo('[Error]: user request validation error');
    res.json({
      result: 'ERROR',
      errorMessage: 'バリデーションエラー',
    });
    return;
  }

  validationResult = await service.linkedSurveyConfigsValidation(memberConfigJson);
  if (!validationResult) {
    logger.appLogInfo('[Error]: Trying to link same survey config to multiple member forms.');
    res.json({
      'result': 'ERROR',
      'errorMessage': '帳票連携で指定された帳票が他の会員帳票に連携済みのため登録できませんでした。連携を削除してください。'
    });
    return;
  }

  let result;
  try {
    result = await service.create(username, memberConfigJson);
  } catch (e: any) {
    logger.appLogInfo(e);
    res.json({
      result: 'ERROR',
      errorMessage: 'Server Error',
    });
    return;
  }

  if (memberConfigJson && memberConfigJson.isAppending && memberConfigJson.isAppending.value) {
    // insert survey_result
    const input:any = {
      partitionKey: result.surveyId,
      sortKey: 'counter',
      answerCode: 0,
    };
    try {
      await service.createSurveyResult(input);
    } catch (error: any) {
      logger.appLogInfo(error);
    }
  }

  res.json({
    result: 'OK',
    data: result,
  });
};

/**
 * @openapi
 * /memberConfigs:
 *  get:
 *    summary: 会員一覧を取得する
 *    tags: [Member]
 */
const getAll = async (req, res) => {
  const lastEvaluatedKey = req.query && req.query.lastEvaluatedKey ? extractLastEvaluatedKey(req) : undefined;
  const userGroup = getUserGroup(req);
  const result = await service.getAll(userGroup, { lastEvaluatedKey });
  res.json({
    result: 'OK',
    data: result.items,
    lastEvaluatedKey: result.lastEvaluatedKey,
  });
};

/**
 * @openapi
 * /memberConfigs/{memberSurveyId}:
 *  get:
 *    summary: 会員詳細を取得する
 *    tags: [Member]
 */
const get = async (req, res) => {
  const { memberSurveyId } = req.params;
  const userGroup = getUserGroup(req);

  const result = await service.getBySurveyIdAndGroup(userGroup, memberSurveyId);
  if (!result || Object.keys(result).length === 0) {
    return res.json({
      result: 'ERROR',
      errorMessage: '帳票の閲覧設定が他人によって変更された、またはデータが存在しません。',
    });
  }
  res.json({
    result: 'OK',
    data: result,
  });
};

/**
 * @openapi
 * /memberConfigs/{memberSurveyId}:
 *  put:
 *    summary: 会員情報を更新する
 *    tags: [Member]
 */
const update = async (req, res) => {
  const memberConfigJson = req.body;
  const userGroup = getUserGroup(req);

  // 最新のデータ取得
  const memberSurveyId =
    isObject(memberConfigJson) && 'surveyId' in memberConfigJson ? (memberConfigJson as any).surveyId : false;
  if (!memberSurveyId) {
    return res.json({
      result: 'ERROR',
      errorMessage: '不正なデータです。',
    });
  }

  const latestData = await service.getBySurveyIdAndGroup(userGroup, memberSurveyId);
  if (!latestData || Object.keys(latestData).length === 0) {
    return res.json({
      result: 'ERROR',
      errorMessage: '帳票の閲覧設定が他人によって変更された、またはデータが存在しません。',
    });
  }

  // 排他エラーチェック
  if (memberConfigJson.updatedAt !== latestData.updatedAt) {
    return res.json({
      result: 'ERROR',
      alreadyUpdated: true,
      errorMessage: '編集していたデータは、他の人によって変更されました。',
      data: latestData,
    });
  }

  // validation
  const validationResult = service.inputValidation(memberConfigJson);
  if (!validationResult) {
    logger.appLogInfo('[Error]: user request validation error');
    res.json({
      result: 'ERROR',
      errorMessage: 'バリデーションエラー',
    });
    return;
  }

  // options の更新チェック
  const { updateOptions, isValid } = service.getUpdateOptions(memberConfigJson, latestData);
  if (!isValid) {
    return res.json({
      result: 'ERROR',
      errorMessage: 'バリデーションエラー',
    });
  }

  for (const key in updateOptions) {
    if (Object.hasOwnProperty.call(updateOptions, key)) {
      const updateOption = updateOptions[key];
      for (let i = 0; i < memberConfigJson.surveySchema.length; i++) {
        const surveyItem = memberConfigJson.surveySchema[i];
        if (surveyItem.itemKey === key) {
          if (surveyItem.default) {
            // checkbox
            let newDefault = surveyItem.default;
            if (Array.isArray(surveyItem.default)) {
              newDefault = [];
              for (let j = 0; j < surveyItem.default.length; j++) {
                const val = surveyItem.default[j];
                if (surveyItem.options.find((e) => e === val)) {
                  newDefault.push(val);
                } else if (val === updateOption.from) {
                  newDefault.push(updateOption.to);
                }
              }
            } else if (surveyItem.default === updateOption.from) {
              // radio
              newDefault = updateOption.to;
            }
            surveyItem.default = newDefault;
          }

          if (surveyItem.input) {
            let newInput = surveyItem.input;
            if (Array.isArray(surveyItem.input)) {
              // checkbox
              newInput = [];
              for (let j = 0; j < surveyItem.input.length; j++) {
                const val = surveyItem.input[j];
                if (surveyItem.options.find((e) => e === val)) {
                  newInput.push(val);
                } else if (val === updateOption.from) {
                  newInput.push(updateOption.to);
                }
              }
            } else if (surveyItem.input === updateOption.from) {
              // radio
              newInput = updateOption.to;
            }
            surveyItem.input = newInput;
          }
        }
      }
    }
  }

  const username = getUsername(req) || 'anonymous';
  const configsPromise = service.update(username, memberConfigJson, memberSurveyId);
  const resultsPromise = memberResultsService.updateOptions(memberSurveyId, updateOptions);

  const results = await Promise.all([configsPromise, resultsPromise]);

  res.json({
    result: 'OK',
    data: results[0],
  });
};

export const routes = (app) => {
  app.post('/', a(create));
  app.get('/', g(getAll));
  app.get('/:memberSurveyId', g(get));
  app.put('/', a(update));
};

// util
const isObject = (value: any): boolean => {
  return value && typeof value === 'object' && !Array.isArray(value);
};