/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *  - name: MemberResult
 *    description: 会員情報回答管理API
 */

import { getUsername } from '../../common/admin-api/utils/auth.js';
import { MemberResultsHelper } from '../../api-admin-survey/services/survey-results/search/sr_index/sr_helper.js';

import {requestContext} from '../../common/admin-api/utils/execution-context.js';
import {LoggerService} from '../../platform/services/logger-service.js';


import moment from 'moment';
import { isEmpty } from 'lodash';
import * as service from '../services/member-results.js';
import * as serviceSurveyResults from '../../api-admin-survey/services/survey-results.js';
import * as memberConfigService from '../services/member.js';
import { admin as a, guest as g, kengenCheck} from '../../common/admin-middlewares/access-guards.js';
import { SurveyResultsValidator } from '../../api-admin-survey/services/survey-results-validator.js';
import actionLog from '../../platform/controllers/helpers/log-actions-common.js';

const loggerService = new LoggerService('member-results');

/**
 * @openapi
 * /member-results/{surveyId}:
 *  post:
 *    summary: 会員情報結果を取得する
 *    tags: [Member]
 */
const getList = async (req, res) => {
  const { surveyId } = req.params;
  const { filterCommon, searchKeyword, lastEvaluatedKey } = req.body;

  const configs = await memberConfigService.getBySurveyId(surveyId);

  await actionLog('会員:閲覧(帳票)', {
    type: 'カレンダー',
    parameter1: configs.surveyTitle,
  });

  if (isEmpty(filterCommon) == 0 && (searchKeyword == undefined || searchKeyword == '')) {
    const result = await service.getByDbPatterns(surveyId, 0, lastEvaluatedKey);
    res.json({
      result: 'OK',
      data: result,
    });
  } else {
    const result = await service.getWithDatabaseFilter(surveyId, filterCommon, lastEvaluatedKey, searchKeyword);
    res.json({
      result: 'OK',
      data: result,
    });
  }
};

/**
 * @openapi
 * /member-results/{surveyId}/delete_survey_result:
 *  post:
 *    summary: 帳票結果を削除する
 *    tags: [MemberResult]
 */
const deleteSurveyResult = async (req, res) => {
  const { surveyId } = req.params;
  const { surveyTitle, filterCommon, filterDate, itemsPerPage, page, searchKeyword, sortBy, sortDesc, allColumns } =
    req.body;

  const configs = await memberConfigService.getBySurveyId(surveyId);
  await actionLog('会員:削除', {
    type: 'カレンダー',
    parameter1: configs.surveyTitle,
    parameter2: allColumns[0] ? allColumns[0].userId : '',
    parameter3: req.body.allColumns,
  });

  const searchCriteria:any = { filterCommon, filterDate, searchKeyword, sortBy, sortDesc };

  await service.deleteSurveyResult(configs, searchCriteria, allColumns);

  const sorted = await service.getSearchedAndSortedList(configs, searchCriteria);

  // paging
  const beginIndex = itemsPerPage * (page - 1);
  const paged = sorted.slice(beginIndex, beginIndex + itemsPerPage);

  res.json({
    result: 'OK',
    data: paged,
    totalCount: sorted.length,
  });
};

/**
 * @openapi
 * /member-results/{surveyId}/create_csv:
 *  post:
 *    summary: 結果CSVデータを生成する
 *    tags: [MemberResult]
 */
const createCsv = async (req, res) => {
  const { surveyId } = req.params;
  const { filterCommon, filterDate, searchKeyword, sortBy, sortDesc, headers, isMember } = req.body;
  const searchCriteria:any = { filterCommon, filterDate, searchKeyword, sortBy, sortDesc };

  const configs = await memberConfigService.getBySurveyId(surveyId);

  const { url } = await serviceSurveyResults.createCSV(configs, searchCriteria, headers, isMember);

  res.json({
    result: 'OK',
    data: { url },
  });
};

/**
 * @openapi
 * /member-results/{surveyId}/create_csv_appending:
 *  post:
 *    summary: 結果CSVデータを生成する
 *    tags: [MemberResult]
 */
const createCsvAppending = async (req, res) => {
  const { surveyId } = req.params;
  const { headers, appendingData } = req.body;

  const configs = await memberConfigService.getBySurveyId(surveyId);

  let exports = '全ての会員';
  const memberNumberSchema = configs.surveySchema.find((schema) => schema.type === 'memberNumber');
  if (appendingData && appendingData.length !== 0 && memberNumberSchema) {
    const { itemKey } = memberNumberSchema;
    exports = appendingData.map((data) => `${data.userId} ${data[itemKey]}`).join('\n');
  }

  const createCSVAppendingResult = await serviceSurveyResults.createCSVAppending(configs , headers, appendingData);

  await actionLog('会員:csvエクスポート', {
    type: 'カレンダー',
    parameter1: configs.surveyTitle,
    parameter2: exports,
  });

  res.json({
    result: 'OK',
    data: createCSVAppendingResult,
  });
};

/**
 * @openapi
 * /member-results/{surveyId}/update_from/{updateFrom}:
 *  get:
 *    summary: 任意の日付以降の更新情報を取得する
 *    tags: [MemberResult]
 */
const getListUpdatedFrom = async (req, res) => {
  const { surveyId, updateFrom } = req.params;

  const result = await service.get(surveyId, updateFrom);
  res.json({
    result: 'OK',
    data: result,
  });
};

/**
 * @openapi
 * /member-results/{surveyId}/checkSaibanExist:
 *  post:
 *    summary: 採番が存在するかどうか確認する
 *    tags: [MemberResult]
 */
const checkSaibanExist = async (req, res) => {
  const params = req.query;

  const checkMemberNumberExistResult = await service.checkSaibanExist(params.memberSurveyId, params.saibanString);

  return res.json({
    result: 'OK',
    data: checkMemberNumberExistResult,
  });
};

/**
 * @openapi
 * /member-results/{surveyId}/checkSaibanExistForUpdate:
 *  post:
 *    summary: 採番が存在するかどうか確認する
 *    tags: [MemberResult]
 */
const checkSaibanExistForUpdate = async (req, res) => {
  const params = req.query;

  if (params.saibanString === params.exceptSaibanString) {
    return res.json({
      result: 'OK',
      data: {
        saibanExistFlag: false,
      },
    });
  }

  const checkMemberNumberExistResult = await service.checkSaibanExist(params.memberSurveyId, params.saibanString);

  return res.json({
    result: 'OK',
    data: checkMemberNumberExistResult,
  });
};

/**
 * @openapi
 * /member-results/linkMember:
 *  post:
 *    summary:
 *    tags: [MemberResult]
 */
const getLinkedMemberOfSurveyResults = async (req, res) => {
  const { memberFormId, userIds } = req.body;
  const results = await service.getLinkedMemberOfSurveyResults(memberFormId, userIds);
  res.json({
    result: 'OK',
    items: results,
  });
};

/**
 * @openapi
 * /member-results/getTotalMemberSurveySubId:
 *  get:
 *    summary: 帳票回答数の合計値を取得する
 *    tags: [MemberResult]
 */
const getTotalMemberSurveySubId = async (req, res) => {
  const result = await service.getTotalMemberSurveySubId();
  res.json({
    result: 'OK',
    data: result,
  });
};

/**
 * @openapi
 * /member-results:
 *  put:
 *    summary: 会員情報を更新する
 *    tags: [MemberResult]
 */
const update = async (req, res) => {
  const errString = 'ERROR';

  // 受け取る
  const surveyResults = req.body.data || req.body;

  // 最新のデータ取得
  const partitionKey = Array.isArray(surveyResults) && surveyResults[0].partitionKey;
  if (!partitionKey) {
    return res.json({
      result: 'ERROR',
      errorMessage: '不正なデータです。',
    });
  }

  const latestData = await service.getByPartitionKey(partitionKey);
  if (!latestData || Object.keys(latestData).length === 0) {
    return res.json({
      result: 'ERROR',
      errorMessage: 'データが存在しません。',
    });
  }

  // 排他エラーチェック
  if (surveyResults[0].updatedAt !== latestData[0].updatedAt) {
    return res.json({
      result: 'ERROR',
      alreadyUpdated: true,
      errorMessage: '編集していたデータは、他の人によって変更されました。',
      data: latestData,
    });
  }

  // validation
  const surveyId = surveyResults[0].surveyId;
  const configs:any = await memberConfigService.getBySurveyId(surveyId);
  const validator = new SurveyResultsValidator(configs, surveyResults);
  if (!validator.validate()) {
    return res.json({
      result: 'ERROR',
      needRefresh: true,
      errorMessage: '帳票データが、他の人によって変更されました。',
    });
  }

  // Duplicate key check
  const duplicateKeyCheck = await checkDuplicateKey(configs, surveyResults);

  if (!duplicateKeyCheck) {
    return res.status(200).json({ result: errString, errorMessage: '入力された会員情報は既に登録済みとなります。' });
  }

  const isUpdateCheckMemberNumberExist = await updateCheckMemberNumberExistsAutomatic({ configs, surveyResults });
  if (isUpdateCheckMemberNumberExist) {
    return res.status(200).json({
      result: errString,
      errorMessage: '入力された会員番号は、既に使用されております。\n別の番号を入力してください。',
    });
  }

  // 保存
  const now = Math.floor(Date.now() / 1000);
  surveyResults.forEach((r) => (r.updatedAt = now));

  const helper = new MemberResultsHelper(configs, {resultsTable: ''});
  helper.setIndexValues(surveyResults);

  await service.update(surveyResults);

  await actionLog('会員:変更', {
    type: 'カレンダー',
    parameter1: configs.surveyTitle,
    parameter2: surveyResults[0] ? surveyResults[0].userId : '',
    parameter3: surveyResults,
  });

  return {
    result: 'OK',
    data: surveyResults,
  };
};

const getByItemKey = async (surveySchema, itemKey) => {
  if (!surveySchema || surveySchema.length === 0) {
    return null;
  }
  if (!itemKey) {
    return null;
  }

  for (const itemIndex in surveySchema) {
    const item = surveySchema[itemIndex];
    if (item.itemKey === itemKey) {
      return item;
    }
  }
  return null;
};

/**
 * @openapi
 * /member-results/{surveyId}/import_csv_appending:
 *  post:
 *    summary: 会員情報をCSVアップロードする
 *    tags: [MemberResult]
 */
const importCsvAppending = async (req, res) => {
  const surveyId = req.params.surveyId;

  const configs:any = await memberConfigService.getBySurveyId(surveyId);

  // check if surveyId exists in DB
  if (!configs) {
    return res.status(200).json({ result: 'ERROR', errorMessage: 'no survey config in DB.' });
  }

  const toUpdate = req.body.toUpdate;
  const toCreate = req.body.toCreate;

  if (
    (!toUpdate || !Array.isArray(toUpdate) || toUpdate.length === 0) &&
    (!toCreate || !Array.isArray(toCreate) || toCreate.length === 0)
  ) {
    return res.json({
      result: 'ERROR',
      errorMessage: 'no [toUpdate] items and no [toCreate] items', // OR "不正なデータ"
    });
  }

  // check toUpdate items (old items that already exists in DB)
  if (toUpdate && Array.isArray(toUpdate) && toUpdate.length > 0) {
    const categoryList:any = {};

    // check if item exist in DB AND if it is the latest data
    for (let i = 0; i < toUpdate.length; ++i) {
      const partitionKey = toUpdate[i].partitionKey;

      // check if item exist in DB
      const latestData = await service.getByPartitionKey(partitionKey);
      if (!latestData || Object.keys(latestData).length === 0) {
        return res.json({
          result: 'ERROR',
          errorMessage: `partitionKey=${partitionKey}が存在しません。`,
        });
      }

      // ========== Validate ==========
      const updatedRecord = toUpdate[i];
      updatedRecord.debug = {};
      const updatedRecordDebug = updatedRecord.debug;
      updatedRecordDebug.latestData = { ...latestData[0] };
      updatedRecordDebug.surveyId = updatedRecordDebug.latestData.surveyId;
      const surveyConfigList = await service.getSurveyConfig(updatedRecordDebug.surveyId);
      let surveyConfig:any = {};
      if (surveyConfigList.length > 0) {
        surveyConfig = surveyConfigList[0];
      }
      if (Object.keys(surveyConfig).length === 0) {
        surveyConfig.surveySchema = [];
      }

      updatedRecordDebug.surveyResult = updatedRecord.surveyResult;

      for (const updatedItemIndex in updatedRecord.surveyResult) {
        const updatedItem = updatedRecord.surveyResult[updatedItemIndex];
        const itemConfig = await getByItemKey(surveyConfig.surveySchema, updatedItem.itemKey);

        if (itemConfig) {
          switch (itemConfig.type) {
            case 'reservation': {
              // check existed or not
              let exist = false;
              const categoryIdFull = updatedItem.value;
              let categoryId = categoryIdFull;
              const categoryIdIndex = categoryId.indexOf('|');
              if (categoryIdIndex > 0) {
                categoryId = categoryId.substring(0, categoryIdIndex);
              }
              if (categoryId) {
                if (categoryList[categoryId]) {
                  exist = true;
                } else {
                  const categoryData = await serviceSurveyResults.getCategory(categoryId);
                  if (categoryData) {
                    exist = true;
                    categoryList[categoryId] = categoryData;
                  }
                }
                if (exist === false) {
                  return res.json({
                    result: 'ERROR',
                    errorMessage: `categoryId=${categoryIdFull}が存在しません。`,
                  });
                }
              }

              break;
            }
            default:
              break;
          }
        }
      }

      // ========== Others ==========

      // check if item is latest data
      if (toUpdate[i].updatedAt < latestData[0].updatedAt) {
        return res.json({
          result: 'ERROR',
          alreadyUpdated: true,
          errorMessage: `partitionKey=${partitionKey}は、他の人によって変更されました。`,
          data: latestData,
        });
      }

      // validate all items of this partitionKey
      const validator = new SurveyResultsValidator(configs, toUpdate[i].surveyResult);
      if (!validator.validate()) {
        return res.json({
          result: 'ERROR',
          needRefresh: true,
          errorMessage: '帳票データが、他の人によって変更されました。', // invalid survey answer: survey has been updated OR invalid text length, email address etc. -> should also be in front-end check
          validatorErrorMessage: validator.getErrorMessage(),
        });
      }

      // assign 'answerCode' value directly from backend (ignore values from CSV)
      toUpdate[i].surveyResult.forEach((item) => (item.answerCode = latestData[0].answerCode));

      // assign 'target' checkmark value as false by default (this will unmark all selected items in HOME)
      toUpdate[i].surveyResult.forEach((item) => (item.target = false));

      // append new update time
      const now = Math.floor(Date.now() / 1000);
      toUpdate[i].surveyResult.forEach((item) => (item.updatedAt = now));
    }
    // end of check for toUpdate items
  }

  // check toCreate items (= new items)
  if (toCreate && Array.isArray(toCreate) && toCreate.length > 0) {
    for (let i = 0; i < toCreate.length; ++i) {
      // validations
      // (category validation)
      // note: currently, there should only be 1 category item per surveyconfig (aka configsCategory.length <= 1)
      const configsCategory = configs.surveySchema.filter((schemaItem) => schemaItem.type === 'reservation');
      for (let j = 0; j < configsCategory.length; ++j) {
        const toCreateCategoryItems = toCreate[i].surveyResult.filter(
          (item) => item.itemKey === configsCategory[j].itemKey
        );
        for (let k = 0; k < toCreateCategoryItems.length; ++k) {
          const categoryIdFull = toCreateCategoryItems[k].value;

          let categoryId = categoryIdFull;
          const categoryIdIndex = categoryId.indexOf('|');
          if (categoryIdIndex > 0) {
            categoryId = categoryId.substring(0, categoryIdIndex);
          }

          if (categoryId && categoryId.length > 0) {
            const categoryData = await serviceSurveyResults.getCategory(categoryId);
            if (!categoryData) {
              return res.json({
                result: 'ERROR',
                errorMessage: `categoryId=${categoryIdFull}が存在しません。`,
              });
            }
          }
        }
      }

      // validate all items of this partitionKey
      const validator = new SurveyResultsValidator(configs, toCreate[i].surveyResult);
      if (!validator.validate()) {
        return res.json({
          result: 'ERROR',
          needRefresh: true,
          errorMessage: '帳票データが、他の人によって変更されました。', // invalid survey answer
          validatorErrorMessage: validator.getErrorMessage(),
        });
      }

      // append create time and update time
      const now = Math.floor(Date.now() / 1000);
      toCreate[i].surveyResult.forEach((item) => {
        item.createdAt = now;
        item.updatedAt = now;
      });
    }
    // end of check for toCreate items
  }

  const helper = new MemberResultsHelper(configs, {resultsTable: ''});

  // save to DB iff all data is correct
  if (toUpdate && Array.isArray(toUpdate) && toUpdate.length > 0) {
    for (const item of toUpdate) {
      helper.setIndexValues([item.surveyResult]);
      await service.update(item.surveyResult);
    }
  }
  if (toCreate && Array.isArray(toCreate) && toCreate.length > 0) {
    for (const item of toCreate) {
      helper.setIndexValues([item.surveyResult]);
      await service.create(item.surveyResult);
    }
  }

  return {
    result: 'OK',
    data: {
      toUpdate: toUpdate,
      toCreate: toCreate,
    },
  };
};

/**
 * @openapi
 * /member-results/{surveyId}/putItem:
 *  post:
 *    summary: 結果を登録する
 *    tags: [MemberResult]
 */
const createSurveyResultAppending = async (req, res) => {
  const errString = 'ERROR';
  const sortKeyCounter = 'counter';
  const surveyResults = req.body.data ? req.body.data : req.body;
  let answerCode = 0;

  try {
    let lineUid = '';
    if (surveyResults[0] && surveyResults[0].userId) {
      lineUid = surveyResults[0].userId;
    }

    // check if surveyId exists in DB
    const surveyId = req.params.surveyId;
    const configs:any = await memberConfigService.getBySurveyId(surveyId);

    if (!configs) {
      return res.status(200).json({ result: errString, errorMessage: 'no survey config in DB.' });
    }

    // Duplicate key check
    const duplicateKeyCheck = await checkDuplicateKey(configs, surveyResults);

    if (!duplicateKeyCheck) {
      return res.status(200).json({ result: errString, errorMessage: '入力された会員情報は既に登録済みとなります。' });
    }

    // 採番
      // 採番
      const memberNumberConfig = configs.surveySchema.find((schema) => schema.type === 'memberNumber');
      let memberNumberValue = '';

      // 自動採番
      if (memberNumberConfig && memberNumberConfig.isAutomaticNumbering) {        
        const serviceResult = await service.getLastResultNumber(surveyId);
        configs.lastResultNumber = serviceResult.lastResultNumber + 1;

        const username = getUsername(req) || 'anonymous';
        await service.updateConfigLastResultNumber(username, configs);

        // 採番
        const memberNumberConfig = configs.surveySchema.find((schema) => schema.type === 'memberNumber');
        if (memberNumberConfig && memberNumberConfig.isAutomaticNumbering) {
          surveyResults.forEach((surveyResult) => {
            if (surveyResult.itemKey === memberNumberConfig.itemKey) {
              const memberSurveySubId = ('' + configs.memberSurveySubId).padStart(4, '0');
              const lastResultNumber = ('' + configs.lastResultNumber).padStart(7, '0');
              surveyResult.value = memberSurveySubId + lastResultNumber;
              memberNumberValue = surveyResult.value;

              return; // forEach
            }
          });

          surveyResults.forEach((surveyResult) => {
            const surveyResultkeys = Object.keys(surveyResult);
            surveyResultkeys.forEach((surveyResultkey) => {
              if (typeof surveyResult[surveyResultkey] === 'string') {
                surveyResult[surveyResultkey] = surveyResult[surveyResultkey].replace(/自動採番/g, memberNumberValue);
              }
            });
          });
        }
      } else {
        // 手動採番

        // パラメータの手動採番を取得
        surveyResults.forEach((surveyResult) => {
          if (surveyResult.itemKey === memberNumberConfig.itemKey) {
            memberNumberValue = surveyResult.value;

            return; // forEach
          }
        });

        const checkMemberNumberExistResult = await service.checkSaibanExist(
          configs.surveyId,
          memberNumberValue,
          configs
        );
        if (checkMemberNumberExistResult && checkMemberNumberExistResult.saibanExistFlag) {
          const validator = new SurveyResultsValidator(configs, surveyResults);
          const errorMessage = validator.getErrorMessage();

          // important! return error and do not continue next commands.
          return res.status(200).json({
            result: errString,
            errorMessage: '入力された会員番号は、既に使用されております。\n別の番号を入力してください。',
            validatorErrorMessage: errorMessage,
            needRefresh: false,
          });
        }
      }

    const validator = new SurveyResultsValidator(configs, surveyResults);
    if (!validator.validate()) {
      const errorMessage = validator.getErrorMessage();
      res.status(200).json({
        result: errString,
        errorMessage: '情報が更新されています。お手数ですが、再読み込みをしてください。',
        validatorErrorMessage: errorMessage,
        needRefresh: true,
      });
    }

    const momentNow = moment();
    const unixNow = momentNow.unix();
    const generatedUserId = `I${momentNow.format('YYYYMMDDHHmmss')}n1`;
    const generatedPartitionKey = `${surveyId}#${generatedUserId}#${momentNow.toDate().getTime()}`;

    const currentCounter = await service.getOneItemByExistsAnswerCode(surveyId, sortKeyCounter);
    if (currentCounter === undefined) {
      //not existing => insert first counter
      const items = [
        {
          partitionKey: surveyId,
          sortKey: sortKeyCounter,
          answerCode: 1,
        },
      ];
      await service.create(items);
      answerCode = 1;
    } else {
      // update counter -> get latest counter(answerCode)
      const response = await service.updateCounter(surveyId, sortKeyCounter);
      if (response && response.answerCode) {
        answerCode = response.answerCode;
      }
    }

    // set partitionKey, userId, createdAt, updatedAt, answerCode, check, target
    for (let i = 0; i < surveyResults.length; i++) {
      surveyResults[i].partitionKey = generatedPartitionKey;
      surveyResults[i].surveyId = surveyId;
      surveyResults[i].userId = generatedUserId;
      surveyResults[i].createdAt = unixNow;
      surveyResults[i].updatedAt = unixNow;
      surveyResults[i].answerCode = answerCode;
      surveyResults[i].check = '未対応';
      surveyResults[i].target = false;
    }

    const helper = new MemberResultsHelper(configs, {resultsTable: ''});
    helper.setIndexValues(surveyResults);

    await service.create(surveyResults);

    await actionLog('会員:新規追加', {
      type: 'カレンダー',
      parameter1: configs.surveyTitle,
      parameter2: lineUid,
      parameter3: generatedUserId,
      parameter4: surveyResults,
    });

    res.status(200).json({
      result: 'OK',
      data: surveyResults,
      currentCounter: currentCounter,
    });
  } catch (error: any) {
    res.status(200).json({
      result: errString,
      errorMessage: error.message,
      errorDetail: error,
    });
  }
};

/**
 * @openapi
 * /member-results/reset-user-id:
 *  post:
 *    summary: 該当のUIDをリセットする
 *    tags: [MemberResult]
 */
const resetUserId = async (req, res) => {
  const errString = 'ERROR';
  const partitionKey = req.body.partitionKey;
  const userId = req.body.userId;
  const result = await service.resetUserId(req, partitionKey, userId);
  if (!result) {
    return res.status(400).json({ result: errString, errorMessage: 'not found survey result in DB.' });
  }
  return res.status(200).json({
    result: 'OK',
  });
};

async function getExistingResultsGrouped(config, itemsToCheck, excludedPartitionKey) {
  // Retrieve all results peratining to member config
  // and are related to the items to check via itemId
  const results = await service.getResultsBySurveyItemKeys(config.surveyId, Object.keys(itemsToCheck));

  // Group results by partitionKey and set the results value
  // for each item key
  const groupedResults:any = {};

  for (const res of results) {
    // Ignore if excluded partition key
    if (res.partitionKey === excludedPartitionKey) {
      continue;
    }

    // Check if partition key exists on groupedResults
    // and create it if needed
    if (!Object.prototype.hasOwnProperty.call(groupedResults, res.partitionKey)) {
      groupedResults[res.partitionKey] = {};
    }

    // Save result value using itemKey, grouped by partitionKey
    groupedResults[res.partitionKey][res.itemKey] = res.value;
  }

  return Object.values(groupedResults);
}

async function checkDuplicateKey(config, results) {
  // Validate results that have the duplicateKeyCheck flag.
  // For the same form config, no other result may have the
  // same set of values for the result to be created or updated
  // Return true if no duplication was detected and
  // the create / update operation may proceed.
  let checkResult = true;

  function getDuplicateKeySchemaItems(config) {
    // Aux function to aggregate the items to be checked
    // and the corresponding values
    const itemsToCheck:any = {};

    for (const item of config.surveySchema) {
      if (item.duplicateKeyCheck) {
        // Set item id and the value, if it exists
        const itemResult = results.find((r) => r.itemKey === item.itemKey);

        if (itemResult) {
          itemsToCheck[itemResult.itemKey] = itemResult.value;
        }
      }
    }

    return itemsToCheck;
  }

  function checkAllValuesMatch(itemsToCheck, existingResults) {
    for (const result of existingResults) {
      let itemValuesMatch = true;

      for (const [key, value] of Object.entries(itemsToCheck)) {
        if (result[key] !== value) {
          itemValuesMatch = false;
        }
      }

      if (itemValuesMatch) {
        return true;
      }
    }

    return false;
  }

  // Check is only valid if config has at least 1 item
  // with the "duplicateKeyCheck" flag
  const schemaItemsToCheck = getDuplicateKeySchemaItems(config);

  if (Object.keys(schemaItemsToCheck).length > 0) {
    // Do validation

    // New or to be updated partition key
    // This should be excluded from the search of existing results
    const excludedPartitionKey = results[0].partitionKey;

    // Get all results that pertain to the passed member config
    // and group them by partition key
    // Only results that match the itemKey of the items to check
    // should be considered
    const existingResults = await getExistingResultsGrouped(config, schemaItemsToCheck, excludedPartitionKey);

    // For each existing result, compare to values to check
    // If ALL of the match, the create or update operation
    // cannot proceed
    const allValuesMatch = checkAllValuesMatch(schemaItemsToCheck, existingResults);

    if (allValuesMatch) {
      checkResult = false;
    }
  }

  return checkResult;
}

async function updateCheckMemberNumberExistsAutomatic(payload) {
  let isMemberNumberExisted = true;

  const { configs, surveyResults } = payload;

  // 採番
  const memberNumberConfig = configs.surveySchema.find((schema) => schema.type === 'memberNumber');
  let memberNumberValue = '';
  let isMemberNumberChanged = true;

  if (memberNumberConfig) {
    // 自動採番
    if (memberNumberConfig.isAutomaticNumbering) {
      isMemberNumberExisted = false;
    } else {
      // 手動採番
      // パラメータの手動採番を取得
      surveyResults.forEach((surveyResult) => {
        if (surveyResult.itemKey === memberNumberConfig.itemKey) {
          memberNumberValue = surveyResult.value;

          if (surveyResult.value === surveyResult.originalValue) {
            isMemberNumberChanged = false;
          }

          return; // forEach
        }
      });

      if (isMemberNumberChanged) {
        const checkMemberNumberExistResult = await service.checkSaibanExist(
          configs.surveyId,
          memberNumberValue,
          configs
        );
        if (checkMemberNumberExistResult && checkMemberNumberExistResult.saibanExistFlag) {
          isMemberNumberExisted = true;
        } else {
          isMemberNumberExisted = false;
        }
      } else {
        isMemberNumberExisted = false;
      }
    }
  }

  return isMemberNumberExisted;
}

export const routes = (app) => {
  app.post('/linkMember', g(getLinkedMemberOfSurveyResults));
  app.post('/:surveyId', g(getList));
  app.post('/:surveyId/create_csv', kengenCheck('amo', createCsv));
  app.post('/:surveyId/create_csv_appending', kengenCheck('amo', createCsvAppending));
  app.put('/', kengenCheck('amo', update));
  app.get('/:surveyId/update_from/:updateFrom', g(getListUpdatedFrom));
  app.post('/:surveyId/delete_survey_result ', a(deleteSurveyResult));
  app.post('/:surveyId/import_csv_appending', kengenCheck('amo', importCsvAppending));
  app.post('/reset-user-id', kengenCheck('amo', resetUserId));
  app.post('/:surveyId/putItem', kengenCheck('amo', createSurveyResultAppending));
  app.any('/getTotalMemberSurveySubId', a(getTotalMemberSurveySubId));
  app.get('/:surveyId/checkSaibanExist', kengenCheck('amo', checkSaibanExist));
  app.get('/:surveyId/checkSaibanExistForUpdate', kengenCheck('amo', checkSaibanExistForUpdate));
};
