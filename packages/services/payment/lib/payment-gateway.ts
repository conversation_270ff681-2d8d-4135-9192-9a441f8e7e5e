import AWS from 'aws-sdk';
import {getEnvironmentCredentials} from 'common/utils/aws-helper.js';
import {CANCEL_ERROR_CODES} from "./constants.js";

const PAYMENT_GATEWAY_OPERATION_NAMES = {
    CANCEL_PAYEMNT: 'CANCEL_PAYEMNT',
}

export type GatewayResponseCancelPayment = {
    result: 'OK'
} | {
    result: 'ERROR',
    errorMessage: string,
    code: CANCEL_ERROR_CODES,
}

export async function cancelPayment(servicePk: string, productAnswerItemSk: string, paymentGatewayLambda: string): Promise<GatewayResponseCancelPayment> {
    const lambdaClient = new AWS.Lambda(getEnvironmentCredentials());
    const {Payload} = await lambdaClient.invoke({
        FunctionName: paymentGatewayLambda,
        Payload: JSON.stringify({
            [PAYMENT_GATEWAY_OPERATION_NAMES.CANCEL_PAYEMNT]: {
                partitionKey: servicePk,
                sortKey: productAnswerItemSk,
            }
        }),
    }).promise();
    if (typeof Payload === 'string') {
        return JSON.parse(Payload);
    }
    return Payload;
}