/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import * as https from 'https';
import { hashUtils } from '../../../../common/utils/index.js';
import { appConfig, ENV_NAMES } from '../../configs.js';
import { XMLParser } from 'fast-xml-parser';

export class RequestHelper {
  requestToSoftBankPayment<T>(xml: string): Promise<T> {
    const xmlParser = new XMLParser({
      ignoreAttributes: false,
    });
    return new Promise((resolve, reject) => {
      const req = https.request(
        appConfig.get(ENV_NAMES.PAYMENT_API_TYPE_ENDPOINT_URL),
        {
          method: 'POST',
          headers: {
            'Content-Type': 'text/xml',
          },
          auth: `${appConfig.get(ENV_NAMES.PAYMENT_API_AUTH_ID)}:${appConfig.get(ENV_NAMES.PAYMENT_API_AUTH_PASS)}`
        },
        (res) => {
          let body = '';
          res.setEncoding('utf8');
          res.on('data', (chunk) => body += chunk);
          res.on('end', () => resolve(xmlParser.parse(body as string)));
          res.on('error', (error) => reject(error));
        }
      );
      req.write(xml);
      req.end();
    });
  }

  validateEnvs(): void {
    const emptyEnvs = [];
    Object.values(ENV_NAMES).forEach(envName => {
      if (envName === ENV_NAMES.DATABASE_PAYMENT_RESULTS) {
        return;
      }
      const envValue = appConfig.get(envName);
      if (!envValue) {
        emptyEnvs.push(envName);
      }
    });
    if (emptyEnvs.length > 0) {
      throw new Error(`The environment variable is not set. Unset values: ${emptyEnvs.join(', ')}`);
    }
  }

  generateHashCode<T>(payload: T, ...ingore: string[]) {
    const linkedString = Object.entries(payload).reduce((str, [key, value]) => {
      if (ingore.includes(key) || !value) {
        return str;
      }
      return str += value;
    }, '');
    return hashUtils.toSHA1(linkedString);
  }

  protected logPayload<T>(payload: T, actionName: string): void {
    console.log(JSON.stringify({ actionName, payload }, null, 2));
  }
}
