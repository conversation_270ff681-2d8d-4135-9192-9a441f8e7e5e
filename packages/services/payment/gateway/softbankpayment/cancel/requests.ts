/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import * as timeUtils from '../../../../common/utils/datetime.js';
import { appConfig, ENV_NAMES } from '../../configs.js';
import { RequestHelper } from '../shared/requests.js';
import { PAYMENT_METHODS } from '../../types/payment-results.js';
import type { PaymentResultsRecord } from '../../types/payment-results.js';

// NOTE: SoftBankPayment側で定義されているキャンセルリクエストのリクエストID
const REQUEST_ID = {
  [PAYMENT_METHODS.CREDIT]: 'ST02-00303-101',
  [PAYMENT_METHODS.LINEPAY]: 'ST02-00306-310',
  [PAYMENT_METHODS.PAYPAY]: 'ST02-00303-311',
} as const;

type CancelPayload = {
  spsApiRequestId: typeof REQUEST_ID[keyof typeof REQUEST_ID],
  merchantId: string;
  serviceId: string;
  trackingId: string;
  processingDateTime?: string;
  requestDate: string; // yyyyMMddHHmmss
  hashkey: string;
  spsHashCode: string;
}

class CancelRequestHelper extends RequestHelper {
  generatePayload(paymentResults: PaymentResultsRecord): CancelPayload {
    const now = timeUtils.toYYYYMMDDHHmmss();
    const basePayload = {
      spsApiRequestId: REQUEST_ID[paymentResults.payMethod],
      merchantId: appConfig.get(ENV_NAMES.VUE_APP_PAYMENT_MERCHANT_ID),
      serviceId: paymentResults.serviceId,
      trackingId: paymentResults.trackingId,
      processingDateTime: paymentResults.payMethod === PAYMENT_METHODS.CREDIT ? now : undefined,
      requestDate: now,
      hashkey: appConfig.get(ENV_NAMES.VUE_APP_PAYMENT_API_KEY),
    };

    return {
      ...basePayload,
      spsHashCode: this.generateHashCode(basePayload, 'spsApiRequestId'),
    };
  }

  payloadToXml(payload: CancelPayload) {
    const processingDateTimeElement = payload.processingDateTime ?
      `    <processing_datetime>${payload.processingDateTime}</processing_datetime>\n` : '';
    const postData =
      '<?xml version="1.0" encoding="Shift_JIS"?>\n' +
      `<sps-api-request id="${payload.spsApiRequestId}">\n` +
      `    <merchant_id>${payload.merchantId}</merchant_id>\n` +
      `    <service_id>${payload.serviceId}</service_id>\n` +
      `    <tracking_id>${payload.trackingId}</tracking_id>\n` +
      processingDateTimeElement +
      `    <request_date>${payload.requestDate}</request_date>\n` +
      `    <hashkey>${payload.hashkey}</hashkey>\n` +
      `    <sps_hashcode>${payload.spsHashCode}</sps_hashcode>\n` +
      '</sps-api-request>';
    this.logPayload(postData, 'cancel');
    return postData;
  }
}

const instance = new CancelRequestHelper();
export default instance;