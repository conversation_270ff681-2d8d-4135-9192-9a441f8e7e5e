/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { PAYMENT_METHODS } from '../../types/payment-results.js';
import { mergeErrorCode } from '../shared/error-code.js';

const COMMON_ERROR_CODE_NAME = {
  CANCELED: 'CANCELED',
  OUTSIDE_CANCELABLE_PERIOD: 'OUTSIDE_CANCELABLE_PERIOD',
  NOT_CANCEL_POSSIBLE_STATE: 'NOT_CANCEL_POSSIBLE_STATE',
} as const

// NOTE: SoftBankPayment側で定義されているエラ－のコード群
// 支払方法別の機能コード(支払方法を区別するためのコード)
const FUCTION_CODE = {
  [PAYMENT_METHODS.CREDIT]: 101,
  [PAYMENT_METHODS.LINEPAY]: 310,
  [PAYMENT_METHODS.PAYPAY]: 311,
}
// 支払方法別の種別コード(どんなエラーかを区別するためのコード)
export const KINDS_CODE = {
  [PAYMENT_METHODS.CREDIT]: {
    CARD_CENTER_SIDE_ERROR: 21, // クレジットカードセンター側のエラ－
    CANCELED_CREDIT: 45, // 与信取消済み
    [COMMON_ERROR_CODE_NAME.CANCELED]: 46, // 返金済み
    [COMMON_ERROR_CODE_NAME.OUTSIDE_CANCELABLE_PERIOD]: 56, // キャンセル可能期間外
    [COMMON_ERROR_CODE_NAME.NOT_CANCEL_POSSIBLE_STATE]: 59 // 不当な状態のトランザクションに対し返金要求をした場合に発生
  },
  [PAYMENT_METHODS.LINEPAY]: {
    LINE_PAY_SIDE_ERROR: 20, // LINE Pay側のエラー
    [COMMON_ERROR_CODE_NAME.OUTSIDE_CANCELABLE_PERIOD]: 22, // キャンセル可能期間外
    [COMMON_ERROR_CODE_NAME.CANCELED]: 71, // 返金済み
  },
  [PAYMENT_METHODS.PAYPAY]: {
    // NOTE: PayPayは返金期間外のエラ－コードが定義されていない
    PAYPAY_SIDE_ERROR: 20, // PayPay側のエラー
    [COMMON_ERROR_CODE_NAME.CANCELED]: 71, // 返金済み
  }
} as const;
// 支払方法共通のコード
const COMMON_CODE = 999;

export const getCreditErrorCode = (kindsCode: number) => {
  return mergeErrorCode(
    FUCTION_CODE[PAYMENT_METHODS.CREDIT],
    kindsCode,
    COMMON_CODE
  );
}

export const getLinePayErrorCode = (kindsCode: number) => {
  return mergeErrorCode(
    FUCTION_CODE[PAYMENT_METHODS.LINEPAY],
    kindsCode,
    COMMON_CODE
  );
}

export const getPayPayErrorCode = (kindsCode: number) => {
  return mergeErrorCode(
    FUCTION_CODE[PAYMENT_METHODS.PAYPAY],
    kindsCode,
    COMMON_CODE
  );
}