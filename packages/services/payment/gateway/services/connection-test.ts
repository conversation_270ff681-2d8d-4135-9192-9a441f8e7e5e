import * as https from 'https';
import instance from "../repositories/payment-results.js";


export async function connectionTest() {
    const internet = await internetConnectionTest();
    console.log(internet.success ? `Internet connection is OK, IP address is ${internet.message}` : `Internet connection is failed. ${internet.message}`);
    const dynamo = await dynamoConnectionTest();
    console.log(dynamo.success ?  `DynamoDB connection is OK: [${dynamo.message}]` : `DynamoDB connection is failed: [${dynamo.message}]`);
    return {
        internet,
        dynamo,
    }
}

type ConnectionTestResult = {
    success: boolean;
    message: string;
}

async function internetConnectionTest(): Promise<ConnectionTestResult> {
    return new Promise((resolve, reject) => {
        try {
            let result = ''
            const req = https.request('https://api.ipify.org?format=json', {
                method: 'GET',
                timeout: 4000,
                headers: {
                    'Accept': 'application/json'
                },
            }, (res) => {
                res.setEncoding('utf8');
                res.on('data', (chunk) => result += chunk);
                res.on('end', () => resolve({
                    success: res.statusCode === 200,
                    message: JSON.parse(result)['ip'],
                }));
            });
            req.end();
        } catch (e: any) {
            resolve({
                success: false,
                message: e.message,
            })
        }
    })
}

async function dynamoConnectionTest(): Promise<ConnectionTestResult> {
    return instance.test();
}