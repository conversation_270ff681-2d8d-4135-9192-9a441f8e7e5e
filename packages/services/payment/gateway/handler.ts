/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { OPERATION_NAMES } from './configs.js';
import cancelPaymentResultsService from './services/cancel-payment-results.js';
import { validateCancelPaymentEventRequest } from './validators/event-validator.js';
import type { CancelPaymentEventRequest } from './validators/event-validator.js';
import {connectionTest} from "./services/connection-test.js";

const handler = async (event: any) => {
  console.log('gateway call',JSON.stringify(event, null, 2))
  try {
    if (event[OPERATION_NAMES.CANCEL_PAYEMNT]) {
      const request = event[OPERATION_NAMES.CANCEL_PAYEMNT] as CancelPaymentEventRequest;
      validateCancelPaymentEventRequest(request);
      return await cancelPaymentResultsService.cancelPayment(request.partitionKey, request.sortKey);
    }
    if (event[OPERATION_NAMES.CONNECTION_TEST]) {
      return await connectionTest()
    }
    throw new Error(`No supported operations among ${Object.keys(event)}`);
  } catch (e: any) {
    console.error(e);
    return {
      result: 'ERROR',
      errorMessage: e.message || e.errorMessage,
      code: e.code,
    };
  }
}

export { handler };