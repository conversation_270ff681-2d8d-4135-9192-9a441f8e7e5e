/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import {updateOperation} from "common/utils/db/update-operation.js";
import * as config from "../../common/utils/config.js";
import * as AWS from "aws-sdk";
import {getEnvironmentCredentials} from "../../common/utils/aws-helper.js";

const appConfig = new config.AppConfig();
appConfig.mapFromProcessEnv();

const awsClients = {
  get dynamoDbDocument() {
    return new AWS.DynamoDB.DocumentClient(getEnvironmentCredentials());
  },
  get dynamoDbUpdateHelper() {
    return updateOperation(awsClients.dynamoDbDocument);
  },
  get config() {
    return getEnvironmentCredentials();
  }
}

const ENV_NAMES = {
  DATABASE_PAYMENT_RESULTS: 'DATABASE_PAYMENT_RESULTS',
  VUE_APP_PAYMENT_MERCHANT_ID: 'VUE_APP_PAYMENT_MERCHANT_ID',
  PAYMENT_API_AUTH_ID: 'PAYMENT_API_AUTH_ID',
  PAYMENT_API_AUTH_PASS: 'PAYMENT_API_AUTH_PASS',
  VUE_APP_PAYMENT_API_KEY: 'VUE_APP_PAYMENT_API_KEY',
  PAYMENT_API_TYPE_ENDPOINT_URL: 'PAYMENT_API_TYPE_ENDPOINT_URL'
} as const;

const TABLE_NAMES = {
  [ENV_NAMES.DATABASE_PAYMENT_RESULTS]: appConfig.get(ENV_NAMES.DATABASE_PAYMENT_RESULTS),
} as const;

const OPERATION_NAMES = {
  CANCEL_PAYEMNT: 'CANCEL_PAYEMNT',
  CONNECTION_TEST: 'CONNECTION_TEST',
} as const;

export {
  awsClients as AWS,
  appConfig,
  TABLE_NAMES,
  ENV_NAMES,
  OPERATION_NAMES,
}