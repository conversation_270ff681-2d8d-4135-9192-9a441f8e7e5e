/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import * as timeUtils from '../../../common/utils/time-utils.js';
import { TABLE_NAMES, AWS } from '../configs.js';
import { UpdatePaymentResultsPayload, PaymentResultsRecord } from '../types/payment-results.js';

type GetOneAttribute = keyof PaymentResultsRecord;

class PaymentResultsRepository {
  private table = TABLE_NAMES.DATABASE_PAYMENT_RESULTS;
  private client = AWS.dynamoDbDocument;
  
  async getOne(partitionKey: string, sortKey: string, attributes: GetOneAttribute[] = []) {
    const result = await this.client.get({
      TableName: this.table,
      Key: {
        partitionKey,
        sortKey,
      },
      AttributesToGet: attributes,
    }).promise();
    return result.Item as PaymentResultsRecord;
  }

  async update(patch: UpdatePaymentResultsPayload) {
    return await AWS.dynamoDbUpdateHelper.update({
      tableName: this.table,
      key: {
        partitionKey: patch.partitionKey,
        sortKey: patch.sortKey,
      },
      patch: {
        ...patch,
        updatedAt: timeUtils.nowUnixSec(),
      }
    });
  }

  async test() {
    try {
      await this.client.scan({
        TableName: this.table,
        Limit: 1,
      }).promise();
      return {
        success: true,
        message: `Can read from ${this.table}`,
      }
    } catch (e: any) {
      return {
        success: false,
        message: e.message,
      }
    }
  }
}

const instance = new PaymentResultsRepository();
export default instance;
