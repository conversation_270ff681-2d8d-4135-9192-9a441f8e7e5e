/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import Ajv from "ajv";

export type CategoryCreateRequest = {
    name: string
}
export type CategoryUpdateRequest = {
    id: string,
    updatedAt: number
} & CategoryCreateRequest;

export type CategoryProps = {
    service: string,
    name: string,
}
export type CategoryNamedRef = {
    id: string,
    name: string,
    updatedAt?: number,
}
export type RecordCategory = {
    partitionKey: string,
    sortKey: string,
    createdAt: number,
    updatedAt: number
} & CategoryProps


const updateRequestSchema = {
    required: ['name', 'updatedAt'],
    type: 'object',
    properties: {
        name: { type: 'string', minLength: 1},
        updatedAt: { type: 'number'}
    }
}
const createRequestSchema = {
    required: ['name'],
    type: 'object',
    properties: {
        id: { type: 'string', minLength: 1},
        name: { type: 'string', minLength: 1},
    }
}
const deleteAllSchema = {
    required: ['ids'],
    type: 'object',
    properties: {
        ids: { type: 'array', items: {type: 'string'}, minItems: 1 },
    }
}

const compiler = new Ajv();
export const validators = {
    createRequest: compiler.compile(createRequestSchema),
    updatedRequet: compiler.compile(updateRequestSchema),
    deleteRequest: compiler.compile(deleteAllSchema)
}