/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import Ajv from "ajv";
import { validateIgnoreStringForSjis } from '../utils/validation-helper';

const ajv = new Ajv();
ajv.addFormat('serviceName', {
    validate(value: string): boolean {
        return validateIgnoreStringForSjis(value) && !value.includes('（') && !value.includes('）');
    }
});

export const CALC_TYPE_DETAILS = 0;
export const CALC_TYPE_TOTAL = 1;

export const ROUNDING_TYPE_FLOOR = 0
export const ROUNDING_TYPE_CEIL = 1
export const ROUNDING_TYPE_ROUND = 2

export const TAX_TYPE_OUTER = 0;
export const TAX_TYPE_INNER = 1;

export const RESERVATION_SERVICE_TRUE = 0
export const RESERVATION_SERVICE_FALSE = 1

export const RESERVATION_COST_TYPE_FIXED = 0
export const RESERVATION_COST_TYPE_TOTAL = 1


export type CalculationType = 0 | 1
export type RoundingType = 0 | 1 | 2
export type ReservationServiceType = 0 | 1
export type TaxType = 0 | 1
export type ReservationCostType = 0 | 1


export type ServiceApiType = Omit<ServiceRecord, 'partitionKey' | 'createdAt'> & {
    products: any[],
}

export type ServiceRecord = {
    partitionKey: string
    sortKey: string,
    serviceId: string,
    serviceName: string,
    receiptCreatorName: string,
    receiptDisplayAddress: string,
    taxType: TaxType,
    calculationType: CalculationType,
    roundingType: RoundingType,
    reservationServiceType: ReservationServiceType,
    reservationCostType: ReservationCostType,
    purchaseLimit: number,
    linkedCalendarIds: string[],
    linkedSurveyIds: string[],
    updatedAt: number
    createdAt: number
}

export type DeleteServiceRequest = {keys: string[]}

export type CreateServiceRequest = Omit<ServiceApiType, 'partitionKey' | 'sortKey' | 'updatedAt'>
export type UpdateServiceRequest = Partial<CreateServiceRequest> & {
    sortKey: string,
    updatedAt: number,
}
type CreateServiceRequestKeys = keyof CreateServiceRequest;
const createServiceRequestRequired: CreateServiceRequestKeys[] = [
    'calculationType', 'purchaseLimit',
    'serviceId', 'serviceName', 'reservationServiceType', 'roundingType',
    'receiptCreatorName', 'receiptDisplayAddress', 'taxType'
]
const createServiceValidator = {
    required: createServiceRequestRequired,
    type: 'object',
    properties: {
        serviceId: { type: 'string', minLength: 3 },
        serviceName: { type: 'string', minLength: 1, format: 'serviceName' },
        receiptCreatorName: { type: 'string', minLength: 1 },
        receiptDisplayAddress: { type: 'string', minLength: 1 },
        taxType: { enum: [TAX_TYPE_OUTER, TAX_TYPE_INNER] },
        calculationType: { enum: [CALC_TYPE_DETAILS, CALC_TYPE_TOTAL] },
        reservationCostType: { enum: [RESERVATION_COST_TYPE_FIXED, RESERVATION_COST_TYPE_TOTAL] },
        roundingType: {enum: [ROUNDING_TYPE_ROUND, ROUNDING_TYPE_CEIL, ROUNDING_TYPE_FLOOR]},
        purchaseLimit: { type: 'number', minimum: 0 },
        reservationServiceType: { enum: [RESERVATION_SERVICE_FALSE, RESERVATION_SERVICE_TRUE]}
    }
}
const updateServiceValidator = {
    type: 'object',
    properties: {
        ...createServiceValidator.properties,
        sortKey: { type: 'string', minLength: 1},
        updatedAt: { type: "number", minimum: 1 }
    },
    required: ['updatedAt', 'sortKey'],
}
const deleteServiceValidator = {
    type: 'object',
    properties: {
        keys: { type: 'array', items: { type: "string"}, minItems: 1}
    },
    required: ['keys']
}
const listServicesQueryValidator = {
    type: 'object',
    properties: {
        lastEvaluatedKey: { type: 'string'},
        reservationServiceType: { enum: ["0","1"] },
        fields: {type: 'string'},
    }
}

export const validators = {
    createServiceRequest: ajv.compile(createServiceValidator),
    updateServiceRequest: ajv.compile(updateServiceValidator),
    deleteServicesRequest: ajv.compile(deleteServiceValidator),
    listServicesQuery: ajv.compile(listServicesQueryValidator),
};