/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import Ajv from "ajv"

export type RecordReservationPaymentItemProps = {
    calendarId: string,
    calendarCategoryId: string,    
    cost: number,
    isDeleted: boolean,
    deletedAt: number,
    productId: string,
    serviceId: string,
    serviceProductId: string,
}
export type RecordReservationPaymentItem = RecordReservationPaymentItemProps & {
    createdAt: number,
    updatedAt: number,
    partitionKey: string,
    sortKey: string,
}
export type RecordCalendarLink = {
    partitionKey: string,
    sortKey: string,
    usePaymentService: 0 | 1,
    paymentServiceSortKey: string
}

export type ResponseReservationPaymentItem = {
    id: string,
    calendarId: string,
    calendarCategoryId: string,
    cost: number,
    isDeleted: boolean,
    productId: string,
    productName: string,
    productCategories: {id: string, name: string}[],
    price: number,
    taxableType: number,
    status: number
}

export type ResponseCategoryReservations = {
    serviceId: string,
    serviceSortKey: string,
    serviceName: string,
    taxType: number,
    calculationType: number,
    reservationCostType: number,
    roundingType: number,
    purchaseLimit: number,
    reservationPaymentItems: ResponseReservationPaymentItem[],    
}
export type ResponseReservationPaymentItemShort = {
    id: string,
    updatedAt: number
} & RecordReservationPaymentItemProps

export type RequestCreateUpdateReservation = {
    id: string,
    calendarId: string,
    categoryId: string,
    cost: number,
    isDeleted: boolean,
    serviceId: string,
    productId: string,
    deletedAt: number,
    updatedAt: number,
}
const notEmptyString = { type: 'string', minLength: 1 }
const createReservationPaymentSchema = {
    type: 'object',
    required: ['id', 'calendarId', 'categoryId', 'cost', 'isDeleted', "productId", "deletedAt"],
    properties: {
        id: notEmptyString,
        calendarId: notEmptyString,
        categoryId: notEmptyString,
        cost: { type: 'number'},
        isDeleted: { type: 'boolean'},
        productId: notEmptyString,
        deletedAt: { oneOf: [{ type: 'null'}, { type: 'integer'}]},
    },
}
const compiler = new Ajv();
export const validators = {
    createUpdateRequestValidator: compiler.compile({
        type: 'array',
        items: createReservationPaymentSchema,
    }),
    listQueryValidator: compiler.compile({
        type: 'object',
        properties: {
            include_deleted: { enum: ['true', 'false']},
            include_products: { enum: ['true', 'false']}
        }
    })
}