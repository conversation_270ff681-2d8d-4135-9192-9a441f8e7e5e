/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import Ajv from "ajv";
import { CALCULATION_TYPES, ROUNDING_TYPES, TAX_TYPES } from "./payment.js";
import {generateRandomStringFromChars} from "../../../common/utils/uniq-id.js";

export const STATUS_FINISHED = 0 // 決済完了
export const STATUS_NOT_SUBJECT = 1 // 決済対象外
export const STATUS_RETURNED = 2 // 返金済み
export const STATUS_СREDIT_FINISHED = 3 // 与信完了
export const STATUS_CREDIT_CANCELLED = 4 // 与信取消

export const PAYMENT_METHOD_CREDIT = "credit";
export const PAYMENT_METHOD_PAYPAY = "paypay";
export const PAYMENT_METHOD_LINEPAY = "linepay";
export const PAYMENT_METHOD_CASH = "cash";

export const PAY_TYPES = {
    CASHLESS: 0,
    CASH: 1,
}

export const TAXABLE_TYPES = {
    TAXABLE: 0,
    TAX_FREE: 1,
}

export const PAYMENT_GATEWAY_OPERATION_NAMES = {
    CANCEL_PAYEMNT: 'CANCEL_PAYEMNT',
}

export type PaymentStatus = 0 | 1 | 2

export type PurchaseDetails = {
    detailId: number, // 購入明細ID
    productCategoryId: string, // 商品分類ID
    productCategoryName: string, // 商品分類名
    productId: string, // 商品ID
    productName: string, // 商品名
    price: number, // 明細価格
    count: number,  // 購入数
    tax: number, // 明細毎の税額
    amount: number, // 明細毎の合計金額(税込み)
    taxableType: number, // 課税(区分)
    reservationPaymentItemId: string // 予約項目(商品用)レコードのsortKey
}
export type PaymentResults = {
    orderId: string,
    payMethod: string, // 支払方法
    trackingId: number,// 決済APIから返却される12桁の数値, キャンセルの際に利用するのでLIFFから予約する際は必須
    serviceId: string,
    serviceName: string,
    status: PaymentStatus,
    details: PurchaseDetails[],
    reservationCost: number,
    tax: number,
    amount: number,
    completedDate: number,
    canceledDate: number,
    taxRate: number,
    taxType: number,
    calculationType: number,
    roundingType: number,
    surveyId: string,
    userId: string,
}
export type RecordPaymentResults = PaymentResults & {
    partitionKey: string, // サービス・商品レコードのsortKey
    sortKey: string, // surveyResultsのpartitionKey
    createdAt: number,
    updatedAt: number
}
export type ExportCsvRequest = {
    surveyId: string
    selected: string[]
}
export type CancelPaymentRequest = {
    partitionKey: string;
    sortKey: string;
}

const notEmptyString = { type: 'string', minLength: 1 }
const someNumber = { type: 'number' }
const someString = { type: 'string' }
const someNullableString = { type: 'string',  nullable: true }

type PurchaseDetailsKeys = Partial<keyof PurchaseDetails>

const purchaseDetailsProperties: { [key in keyof PurchaseDetails]: any } = {
    detailId: someNumber,
    productCategoryId: someString,
    productCategoryName: someString,
    amount: someNumber,
    count: someNumber,
    price: someNumber,
    tax: someNumber,
    taxableType: { enum: [TAX_TYPES.INNTER, TAX_TYPES.OUTER] },
    productId: notEmptyString,
    productName: someString,
    reservationPaymentItemId: notEmptyString
}


const paymentResultProperties: { [key in keyof Partial<PaymentResults>]: any } = {
    amount: someNumber,
    calculationType: { enum: [CALCULATION_TYPES.FOR_EACH_ITEMS, CALCULATION_TYPES.TOTAL] },
    canceledDate: someNumber,
    status: { enum: [STATUS_FINISHED, STATUS_NOT_SUBJECT, STATUS_RETURNED] },
    completedDate: someNumber,
    orderId: notEmptyString,
    payMethod: { enum: [PAYMENT_METHOD_CREDIT, PAYMENT_METHOD_LINEPAY, PAYMENT_METHOD_PAYPAY] },
    reservationCost: someNumber,
    roundingType: { enum: [ ROUNDING_TYPES.ROUND, ROUNDING_TYPES.ROUND_DOWN, ROUNDING_TYPES.ROUND_UP ]},
    serviceId: notEmptyString,
    serviceName: someString,
    surveyId: notEmptyString,
    tax: someNumber,
    taxRate: someNumber,
    taxType: { enum: [ TAX_TYPES.INNTER, TAX_TYPES.OUTER]},
    trackingId: someNumber,
    userId: notEmptyString,
}

const createPaymentDataRequestSchema = {
    type: 'object',
    required: [
        'serviceId', 'status', 'details', 'tax', 'amount', 'taxRate',
        'taxType', 'calculationType', 'roundingType', 'surveyId'
    ],
    properties: {
        ...paymentResultProperties,
        details: { type: 'array', items: {
            type: 'object',
            required: [
                'productId', 'detailId','amount','count','price',
                'tax', 'taxableType'
            ] as PurchaseDetailsKeys[],
            properties: purchaseDetailsProperties,
        }},
    }
}
const updatePaymentDataRequestSchema = {
    type: 'object',
    required: createPaymentDataRequestSchema.required.concat(['updatedAt']),
    properties: createPaymentDataRequestSchema.properties,
}

const compiler = new Ajv();
export const validators = {
    listRequest: compiler.compile({
        type: "object",
        required: ['keys'],
        properties: {
            keys: { type: 'array', items: { type: "string" } }
        }
    }),
    exportCsvRequest: compiler.compile({
        type: 'object',
        required: ['selected'],
        properties: { selected: { type: 'array', items: { type: "string" } } }
    }),
    createPaymentResult: compiler.compile(createPaymentDataRequestSchema),
    updatePaymentResult: compiler.compile(updatePaymentDataRequestSchema),
}

export function generateOrderId() {
    return generateRandomStringFromChars(10)
}