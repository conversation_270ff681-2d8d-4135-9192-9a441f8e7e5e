/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { RecordPaymentResults } from "../../../types/model-results.js";
import { createPaymentResult, updatePaymentResult } from "../payment-results.js";

export async function createFromSurveyResult(payment: RecordPaymentResults) {
    return await createPaymentResult(payment);
}

export const paymentResultService = {
    create: createFromSurveyResult,
    update: updateForSurveyResult,
}

export async function updateForSurveyResult(payment: RecordPaymentResults) {
    try {
        return {
            code: 'success',
            message: '決済結果の更新は完成しました。',
            data: await updatePaymentResult(payment)
        };
    } catch (e: any) {
        let code = e.code;
        let message = '決済結果の保存は失敗しました。'
        if (code === 'not_found') {
            message = '決済結果は見つかりませんでした。'
        }
        if (code === 'exclusion_error') {
            message = '決済結果は他のユーザーによって変更されました。'
        }
        if (code === 'is_cancelled') {
            code = 'success'
            message = 'キャンセル済みの決済結果データのため、更新を実施しません。'
        }
        return {
            code,
            message
        }
    }
}