/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import _ from 'lodash';
import { findBySortKey } from '../../repo/admin-products';
import { ServiceException } from '../../utils/exceptions';
import * as servicesRepo from '../../repo/admin-services';
import * as categoriesRepo from '../../repo/admin-product-categories';
import { listAll, getCalendarServiceLink, getOne, update, create, getForProduct } from '../../repo/admin-reservations';
import { RecordReservationPaymentItem, RecordReservationPaymentItemProps, RequestCreateUpdateReservation, ResponseCategoryReservations, ResponseReservationPaymentItem, ResponseReservationPaymentItemShort } from '../../types/model-reservations';
import { repeatRequest } from '../../utils/repository-helpers';
import * as timeUtils from "@oss/services/common/utils/datetime";

export async function loadProductReservationItems(serviceId: string, productId: string) {
     return await getForProduct(serviceId, productId);
}

export async function loadReservationItems(categoryId: string, includeDeleted = false) {
    const allItems = await listAll(categoryId, true);
    const maxSerialNumber = allItems
        .map(i => i.sortKey)
        .map(pk => pk.split('_')[1])
        .filter(serial => !!serial)
        .map(value => parseInt(value, 10))
        .sort((a, b) => a - b).reverse()[0]

    const items = includeDeleted ? allItems : allItems.filter(i => i.isDeleted !== true);
    const productIds = _.uniq(items.map(i => i.productId));
    const idNames = await Promise.all(productIds.map(findBySortKey))
        .then(v => v.filter(t => !!t).map(t => ({ id: t.sortKey, name: t.productName })))
        .then(v => v.reduce((acc, curr) => {
            acc[curr.id] = curr.name
            return acc
        }, {}))

    return {
        items: items.map(r => {
            return {
                id: r.sortKey,
                calendarId: r.calendarId,
                calendarCategoryId: r.sortKey.split('_')[0],
                cost: r.cost,
                isDeleted: !!r.isDeleted,
                deletedAt: r.deletedAt || null,
                serviceId: r.serviceId,
                productId: r.productId,
                productName: idNames[r.productId] || '',
                updatedAt: r.updatedAt,
            }
        }),
        maxSerialNumber,
    }
}

function hasValue(value: any) {
    return !(_.isNil(value) || value === "")
}

export async function getCategoryItems(categoryId: string): Promise<ResponseCategoryReservations> {
    const serviceSortKey = await getCalendarServiceLink(categoryId);
    if (!serviceSortKey) {
        throw new ServiceException('not_found', 'カレンダーに決済サービスが設定されていません。')
    }
    const reservationItems = await listAll(categoryId);
    const service = await servicesRepo.getBySortKey(serviceSortKey);

    const productIds = _.uniq(reservationItems.map(i => `${service.sortKey}_${i.productId}`));
    const products = await Promise.all(productIds.map(findBySortKey));
    const serviceCategories = await repeatRequest(lek => categoriesRepo.list(service.sortKey, lek));

    const rpitems: ResponseReservationPaymentItem[] = reservationItems.map(i => {
        const product = products.find(p => p.productId === i.productId);
        if (!product) {
            return null;
        }
        const productCategoriesIds = product.productCategories;
        const categories = serviceCategories.filter(c => productCategoriesIds.includes(c.sortKey));
        return {
            id: i.sortKey,
            calendarId: i.calendarId,
            calendarCategoryId: categoryId,
            cost: i.cost,
            isDeleted: i.isDeleted || false,
            productId: i.productId,
            productName: product.productName,
            price: product.price,
            productCategories: categories.map(c => ({ id: c.sortKey, name: c.name })),
            status: product.status,
            taxableType: product.taxableType,
        }
    }).filter(hasValue);

    return {
        serviceId: service.serviceId,
        serviceName: service.serviceName,
        calculationType: service.calculationType,
        roundingType: service.roundingType,
        taxType: service.taxType,
        reservationCostType: service.reservationCostType,
        serviceSortKey: service.sortKey,
        purchaseLimit: service.purchaseLimit,
        reservationPaymentItems: rpitems,
    }
}

export async function createUpdateOne(item: RequestCreateUpdateReservation) {
    const existed = await getOne(item.id);
    const record: RecordReservationPaymentItemProps = {
        calendarCategoryId: item.categoryId,
        serviceId: item.serviceId,
        serviceProductId: `${item.serviceId}_${item.productId}`,
        calendarId: item.calendarId,
        cost: item.cost,
        deletedAt: item.deletedAt,        
        isDeleted: item.isDeleted || false,
        productId: item.productId,
    }
    let updatedRecord: RecordReservationPaymentItem
    if (existed) {
        updatedRecord = await update(item.id, record, item.updatedAt);
    } else {
        updatedRecord = await create(item.id, record);
    }
    return {
        id: updatedRecord.sortKey,
        calendarCategoryId: updatedRecord.calendarCategoryId,
        serviceProductId: updatedRecord.serviceProductId,
        serviceId: updatedRecord.serviceId,
        calendarId: updatedRecord.calendarId,
        cost: updatedRecord.cost,
        deletedAt: updatedRecord.deletedAt,
        isDeleted: updatedRecord.isDeleted,
        productId: updatedRecord.productId,
        updatedAt: updatedRecord.updatedAt,
    } as ResponseReservationPaymentItemShort
}

export async function createUpdateMany(items: RequestCreateUpdateReservation[]) {
    const allIdsUnique = _.uniq(items.map(i => i.id)).length === items.length;
    if (!allIdsUnique) {
        throw new ServiceException('invalid_params', '重複なID');
    }
    const allProductsUnique = _.uniq(items.map(i => i.productId)).length === items.length;
    if (!allProductsUnique) {
        throw new ServiceException('invalid_params', '重複な商品ID');
    }
    return Promise.all(items.map(createUpdateOne));
}

export async function rogicalDeleteAll(categoryId: string) {
    const existed = await loadReservationItems(categoryId);
    const now = timeUtils.nowUnixSec();
    return await Promise.all(existed.items.map(async (item) => {
        await update(item.id, {
            isDeleted: true,
            deletedAt: now,
            updatedAt: now,
        })
    }));
}