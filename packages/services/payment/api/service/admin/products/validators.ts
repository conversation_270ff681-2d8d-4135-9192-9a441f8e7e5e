/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { ServiceException } from '../../../utils/exceptions';
import { countProducts, list } from '../../../repo/admin-products';
import { RecordProduct } from '../../../types/model-products';

export const PRODUCT_REGISTRATION_LIMIT = 1000;

export async function assertCanAddMoreProducts(serviceId: string, newProductsCount = 1) {
    const currentCount = await countProducts(serviceId)
    if ( currentCount + newProductsCount > PRODUCT_REGISTRATION_LIMIT) {
        throw new ServiceException('too_much', `サービス毎の商品登録数の上限を超えました。上限は${PRODUCT_REGISTRATION_LIMIT}件です。`)
    }
}

export async function checkDuplicateById(serviceId: string, products: RecordProduct[]) {
    const existed = await list({serviceId});
    const existedIds = existed.items.map(({sortKey}) => sortKey);
    if (existedIds.length === 0) {
        return;
    }
    const duplicateIds = products.reduce((ids: string[], {sortKey}) => {
        existedIds.includes(sortKey) && ids.push(sortKey);
        return ids;
    }, []);
    if (duplicateIds.length > 0) {
        throw new ServiceException('duplicate_id', `次の商品IDの商品は登録済みです。[${duplicateIds.map(id => `"${id}"`).join(', ')}]`);
    }
}