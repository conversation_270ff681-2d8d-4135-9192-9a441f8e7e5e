/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { isEmpty, isString } from 'lodash';
import * as db from '../../utils/db';
import config from '../../config/static';
import c from '../../config/constants';
import type { CalculationType, Product, RoundingType, TaxType, ReserveServiceType, ReserveCostType, TaxableType } from '../../types/payment';
import { BaseService, DynamoItem } from '../base-service.js';
import * as AWS from 'aws-sdk';

const PARTITION_KEYS = {
  TAX_SETTING: 'taxSetting',
  PRODUCT_CATEGORIES: 'productCategories',
  PRODUCTS: 'products',
  SERVICES: 'services',
} as const;

export type RateSetting = {
  value: number;
  applyDate: string;
};

export interface TaxSetting extends DynamoItem<typeof PARTITION_KEYS.TAX_SETTING> {
  sortKey: string;
  rateSettings: RateSetting[];
}

export interface ProductCategory extends DynamoItem<typeof PARTITION_KEYS.PRODUCT_CATEGORIES> {
  sortKey: string;
  service: string;
  name: string;
}

export interface ProductRecord extends DynamoItem<typeof PARTITION_KEYS.PRODUCTS> {
  sortKey: string;
  productId: string;
  productName: string;
  price: number;
  productCategories: string[];
  taxableType: TaxableType;
  order: number;
  status: number;
}

export interface PaymentService extends DynamoItem<typeof PARTITION_KEYS.SERVICES> {
  sortKey: string;
  serviceId: string;
  serviceName: string;
  receiptCreatorName: string;
  receiptDisplayAddress: string;
  taxType: TaxType;
  calculationType: CalculationType;
  roundingType: RoundingType;
  reservationServiceType: ReserveServiceType;
  reservationCostType: ReserveCostType;
  purchaseLimit: number;
  linkedCaledarIds: string[];
  linkedSurveyIds: string[];
  products: Product[];
}

class PaymentConfigsService extends BaseService {
  private getTableName(configName: string = c.TABLE_PAYMENT_CONFIGS) {
    return config.get(configName) || process.env[configName];
  }


  public async getProductCategories(sortKey: string, lastEvaluatedKey?: string) {
    const queryInput = {
      ...this.queryParams(this.getTableName(), PARTITION_KEYS.PRODUCT_CATEGORIES, sortKey),
    };
    if (lastEvaluatedKey) {
      queryInput.ExclusiveStartKey = lastEvaluatedKey;
    }
    const result = await db.queryOnePage<ProductCategory>(queryInput);
    return {
      data: result.items,
      lastEvaluatedKey: result.lastEvaluatedKey,
    };
  }

  public async getAllProductCategories(serviceSortKey: string) {
    const queryInput = this.queryListParams(this.getTableName(), PARTITION_KEYS.PRODUCT_CATEGORIES);
    queryInput['FilterExpression'] = '#service = :service';
    queryInput.ExpressionAttributeNames['#service'] = 'service';
    queryInput.ExpressionAttributeValues[':service'] = { S: serviceSortKey };
    return await db.queryList(queryInput) as ProductCategory[];
  }

  public async getServices(fields?: string, reservationServiceType = 2, lastEvaluatedKey?: string) {
    const queryInput = {
      ...this.queryParams(this.getTableName(), PARTITION_KEYS.SERVICES),
    };
    if (lastEvaluatedKey) {
      queryInput.ExclusiveStartKey = lastEvaluatedKey;
    }
    const { items, lastEvaluatedKey: resultLastEvaluatedKey } = await db.queryOnePage<PaymentService>(queryInput);

    let filters: string[] | undefined;
    if (isString(fields) && !isEmpty(fields)) {
      filters = fields.split(',');
    }
    let data = this.filterData(items, filters, ['sortKey', 'serviceId', 'serviceName']);
    if (reservationServiceType !== 2) {
      data = data.filter((service) => service.reservationServiceType === reservationServiceType);
    }

    return {
      data,
      lastEvaluatedKey: resultLastEvaluatedKey,
    };
  }

  public async getService(sortKey: string, includeProducts = false) {
    // const queryInput = this.queryParams(this.getTableName(), PARTITION_KEYS.SERVICES, sortKey);
    const queryInput = {
      TableName: this.getTableName(),
      Key: {
        partitionKey: { S: PARTITION_KEYS.SERVICES },
        sortKey: { S: sortKey },
      }
    }
    const service = await db.getOne<PaymentService>(queryInput);
    if (includeProducts) {
      service.products = await this.getAllProducts(sortKey);
    }

    return {
      data: service,
    };
  }

  public async getAllProducts(serviceSortKey: string, isIncludeDeleted = false) {
    const queryInput = {
      TableName: this.getTableName(),
      KeyConditionExpression: '#pk = :pk and begins_with(#sk, :sk)',
      ExpressionAttributeNames: {
        '#pk': 'partitionKey',
        '#sk': 'sortKey',
      },
      ExpressionAttributeValues: {
        ':pk': { S: PARTITION_KEYS.PRODUCTS },
        ':sk': { S: serviceSortKey },
      },
    }
    if (!isIncludeDeleted) {
      queryInput['FilterExpression'] = '#status = :status';
      queryInput.ExpressionAttributeNames['#status'] = 'status';
      queryInput.ExpressionAttributeValues[':status'] = { N: '0' };
    }
    const products = await db.queryList(queryInput) as ProductRecord[];
    const productCategories: ProductCategory[] = await this.getAllProductCategories(serviceSortKey);
    
    return products.map(p => {
      const categories = p.productCategories.map(id => {
        const category = productCategories.find(c => c.sortKey === id);
        return {
          id: category.sortKey,
          name: category.name,
        }
      });
      return {
        ...p,
        productCategories: categories,
      }
    });
  }

  public async getOneProduct(productId: string) {
    return await db.getOne<ProductRecord>({
      Key: {
        partitionKey: { S: PARTITION_KEYS.PRODUCTS },
        sortKey: { S: productId },
      },
      TableName: this.getTableName()
    });
  }

  public async filterService(serviceId: string) {
    const queryInput: AWS.DynamoDB.QueryInput = {
      ...this.queryParams(this.getTableName(), PARTITION_KEYS.SERVICES),
      FilterExpression: 'serviceId = :serviceId',
      ExpressionAttributeValues: {
        ':serviceId': { S: serviceId },
      },
      Limit: 1,
    };

    const paymentServices = await db.queryList<PaymentService>(queryInput);
    if (!paymentServices || paymentServices.length === 0) {
      return null;
    }
    return paymentServices.pop();
  }

  public async getTaxSetting() {
    const params = this.queryParams(
        this.getTableName(),
        PARTITION_KEYS.TAX_SETTING,
        'default'
    )
    console.log(JSON.stringify(params))
    const results = await db.getOne<TaxSetting>({
      Key: {
        partitionKey: { S: PARTITION_KEYS.TAX_SETTING},
        sortKey: {S: 'default'}
      },
      TableName: this.getTableName()
    });
    return results?.rateSettings;
  }
}

const instance = new PaymentConfigsService();
export default instance;
