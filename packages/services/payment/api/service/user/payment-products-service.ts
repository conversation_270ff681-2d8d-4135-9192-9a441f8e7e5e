/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import * as db from '../../utils/db.js';
import config from '../../config/static.js';
import c from '../../config/constants.js';
import { BaseService, DynamoItem } from '../base-service.js';
import paymentConfigs from './payment-configs-service.js';

const PARTITION_KEYS = {
  CALENDARS: 'calendars',
  CATEGORIES: 'categories',
  RESERVATION_PAYMENT_ITEMS: 'reservationPaymentItems',
} as const;

export interface ReservationPaymentItem extends DynamoItem<typeof PARTITION_KEYS.RESERVATION_PAYMENT_ITEMS> {
  sortKey: string;
  caledarId: string;
  calendarCategoryId: string;
  cost: number;
  isDeleted: boolean;
  deletedAt: number | null;
  productId: string;
}

export class PaymentProductsError extends Error {
  public code: string;
  public status: number;

  constructor(status: number, code: string, message?: string) {
    super(message);
    this.status = status;
    this.code = code;
    this.message = message;
  }
}

class PaymentProductsService extends BaseService {
  private getTableName(configName: string = c.TABLE_SURVEY_CALENDARS) {
    return config.get(configName);
  }

  public async getReservationPaymentProducts(categoryId: string) {
    const { calendarId } = await this.getCategory(categoryId);
    const { paymentServiceSortKey } = await this.getCalendar(calendarId);
    const { data: service } = await paymentConfigs.getService(paymentServiceSortKey);
    const reservationPaymentItems = await this.getReservationPaymentItems(categoryId);
    const products = await paymentConfigs.getAllProducts(paymentServiceSortKey);
    const mergedItems = reservationPaymentItems.map(item => {
      const product = products.find(p => p.sortKey === `${paymentServiceSortKey}_${item.productId}`);
      if (!product) return null;
      return {
        ...item,
        productCategories: product.productCategories || [],
        productName: product.productName || 0,
        price: product.price || 0,
        taxableType: product.taxableType || 0,
        status: product.status || 0,
      }
    }).filter(item => item !== null);

    return {
      code: 'success',
      data: {
        serviceSortKey: service.sortKey,
        serviceId: service.serviceId,
        serviceName: service.serviceName,
        taxType: service.taxType,
        calculationType: service.calculationType,
        roundingType: service.roundingType,
        reservationCostType: service.reservationCostType,
        purchaseLimit: service.purchaseLimit,
        reservationPaymentItems: mergedItems,
      },
    };
  }

  private async getCategory(categoryId: string) {
    const category = await db.getOne<{ calendarId?: string }>({
      TableName: this.getTableName(),
      Key: {
        partitionKey: { S: PARTITION_KEYS.CATEGORIES },
        sortKey: {
          S: categoryId,
        }
      }
    });
    if (!category) {
      throw new PaymentProductsError(400, 'invalid_id', '[categoryId]が無効です。');
    }
    if (!category.calendarId) {
      throw new PaymentProductsError(406, 'category_not_set', '[categoryId]はカレンダーに設定されていません。');
    }
    return category;
  }

  private async getCalendar(calendarId: string) {
    const calendar = await db.getOne<{ paymentServiceSortKey?: string }>({
      TableName: this.getTableName(),
      Key: {
        partitionKey: { S: PARTITION_KEYS.CALENDARS },
        sortKey: { S: calendarId }
      }
    });
    console.log('calendar', calendarId, calendar);
    if (!calendar) {
      throw new PaymentProductsError(409, 'missing_calendar', '[categoryId]のカレンダーが存在しません。');
    }
    if (!calendar.paymentServiceSortKey) {
      throw new PaymentProductsError(404, 'not_found', 'カレンダーに決済サービスが設定されていません。');
    }
    return calendar;
  }

  private async getReservationPaymentItems(categoryId: string) {
    const queryParams = {
      TableName: this.getTableName(),
      KeyConditionExpression: '#pk = :pk and begins_with(#sk, :sk)',
      FilterExpression: '#del <> :del',
      ExpressionAttributeNames: {
        '#pk': 'partitionKey',
        '#sk': 'sortKey',
        '#del': 'isDeleted',
      },
      ExpressionAttributeValues: {
        ':pk': { S: PARTITION_KEYS.RESERVATION_PAYMENT_ITEMS },
        ':sk': { S: categoryId },
        ':del': { BOOL: true },
      },
    }
    return await db.queryList<ReservationPaymentItem>(queryParams);
  }
}

const instance = new PaymentProductsService();
export default instance;
