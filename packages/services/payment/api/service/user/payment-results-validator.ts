/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import type { PaymentResult } from './payment-results-service.js';
import type {
  TaxableType
} from '../../types/payment.js';
import  {
  PAYMENT_STATUSES,
  TAX_TYPES,
  CALCULATION_TYPES,
  ROUNDING_TYPES,
  TAXABLE_TYPES,
} from '../../types/payment.js';
import { PaymentResultsFalsificationValidator } from '../helpers/payment-results-falsification-validator.js';

const SERVICE_ID_LENGTH = {
  MIN: 3,
  MAX: 3,
}

const SERVICE_NAME_LENGTH = {
  MIN: 1,
  MAX: 40,
}

const ORDER_ID_LENGTH = {
  MIN: 15,
  MAX: 15,
}

const TAX_RANGE = {
  MIN: 0,
}

const AMOUNT_RANGE = {
  MIN: 0,
}

const TAX_RATE_RANGE = {
  MIN: 0,
  MAX: 100,
}

const PRODUCT_ID_LENGTH = {
  MIN: 1,
  MAX: 20,
}

const PRODUCT_NAME_LENGTH = {
  MIN: 1,
  MAX: 40,
}

export class PaymentResultsValidator {
  private paymentResult: PaymentResult;
  private userId: string;
  private errorMessage: { [index: string]: any };

  constructor(paymentResult: PaymentResult, userId: string) {
    this.paymentResult = paymentResult;
    this.userId = userId;
    this.errorMessage = {};
  }

   async validate() {
    return await this.validateByConditions();
  }

  private async validateByConditions() {
    this.validateKey(this.paymentResult.partitionKey, 'partitionKey');
    this.validateKey(this.paymentResult.sortKey, 'sortKey');
    this.validateServiceId();
    this.validateServiceName();
    this.validateUserId();
    this.validateOrderId();
    this.validateStatus();
    this.validateReservationCost();
    this.validateTax();
    this.validateAmount();
    this.validateTaxRate();
    this.validateTaxType();
    this.validateCalculationType();
    this.validateRoundingType();
    this.validateDetails();
    const falsificationValidator = new PaymentResultsFalsificationValidator({ ...this.paymentResult, serviceSortKey: this.paymentResult.partitionKey });
    const result = await falsificationValidator.validate();
    if (!result) {
      this.errorMessage = { ...this.errorMessage, ...falsificationValidator.getErrorMessage() };
    }
    return this.isValid();
  }

  isValid() {
    return Object.keys(this.errorMessage).length === 0;
  }

  getErrorMessage() {
    return this.errorMessage;
  }

  private validateString(value: string, minLength?: number, maxLength?: number) {
    const isValidMin = typeof minLength === 'number' ? this.validateStringMinLength(value, minLength) : true;
    const isValidMax = typeof maxLength === 'number' ? this.validateStringMaxLength(value, maxLength) : true;
    return !!value && isValidMin && isValidMax;
  }

  private validateStringMinLength(value: string, length: number) {
    return value.length >= length;
  }

  private validateStringMaxLength(value: string, length: number) {
    return value.length <= length;
  }

  private validatePositiveNumber(value: number) {
    const pattern = /^([1-9]\d*|0)$/;
    const isValidMin = value >= 0;
    return pattern.test(String(value)) && isValidMin;
  }

  private setErrorMessage(value: any, itemName) {
    this.errorMessage[itemName] = `It is an invalid value. The specified value is [${value}]`;
  }

  private validateKey(value: string, itemName: string) {
    if (!this.validateString(value)) {
      this.setErrorMessage(value, itemName);
    }
  }

  private validateServiceId() {
    const value = this.paymentResult.serviceId;
    if (!this.validateString(value, SERVICE_ID_LENGTH.MIN, SERVICE_ID_LENGTH.MAX)) {
      this.setErrorMessage(value, 'serviceId');
    }
  }

  private validateServiceName() {
    const value = this.paymentResult.serviceName;
    if (!this.validateString(value, SERVICE_NAME_LENGTH.MIN, SERVICE_NAME_LENGTH.MAX)) {
      this.setErrorMessage(value, 'serviceName');
    }
  }

  private validateUserId() {
    const value = this.userId;
    const startString = 'U';
    if (!this.validateString(value, 1) || !value.startsWith(startString)) {
      this.setErrorMessage(value, 'userId');
    }
  }

  private validateOrderId() {
    const value = this.paymentResult.orderId;
    if (!this.validateString(value, ORDER_ID_LENGTH.MIN, ORDER_ID_LENGTH.MAX)) {
      this.setErrorMessage(value, 'orderId');
    }
  }

  private validateStatus() {
    const value = this.paymentResult.status;
    if (!Object.values(PAYMENT_STATUSES).includes(value)) {
      this.setErrorMessage(value, 'status');
    }
  }

  private validateReservationCost() {
    const value = this.paymentResult.reservationCost;
    if (!this.validatePositiveNumber(value)) {
      this.setErrorMessage(value, 'reservationCost');
    }
  }

  private validateTax() {
    const value = this.paymentResult.reservationCost;
    if (typeof value !== 'number' || value < TAX_RANGE.MIN) {
      this.setErrorMessage(value, 'tax');
    }
  }

  private validateAmount() {
    const value = this.paymentResult.amount;
    if (typeof value !== 'number' || value < AMOUNT_RANGE.MIN) {
      this.setErrorMessage(value, 'tax');
    }
  }

  private validateTaxRate() {
    const value = this.paymentResult.taxRate
    if (typeof value !== 'number' || value < TAX_RATE_RANGE.MIN || value > TAX_RATE_RANGE.MAX) {
      this.setErrorMessage(value, 'taxRate');
    }
  }

  private validateTaxType() {
    const value = this.paymentResult.taxType;
    if (!Object.values(TAX_TYPES).includes(value)) {
      this.setErrorMessage(value, 'taxType');
    }
  }

  private validateCalculationType() {
    const value = this.paymentResult.calculationType;
    if (!Object.values(CALCULATION_TYPES).includes(value)) {
      this.setErrorMessage(value, 'calculationType');
    }
  }

  private validateRoundingType() {
    const value = this.paymentResult.roundingType;
    if (!Object.values(ROUNDING_TYPES).includes(value)) {
      this.setErrorMessage(value, 'roundingType');
    }
  }

  private validateDetails() {
    const details = this.paymentResult.details
    if (!Array.isArray(details)) {
      this.errorMessage['details'] = 'details must be an array.';
    } else if (details.length < 1) {
      this.errorMessage['details'] = 'At least one detail element is required.';
    } else {
      this.paymentResult.details.forEach((d, i) => {
        this.validateDetailId(d.detailId, i);
        this.validateProductId(d.productId, i);
        this.validateProductName(d.productName, i);
        this.validatePrice(d.price, i);
        this.validateCount(d.count, i);
        this.validateDetailTax(d.tax, i);
        this.validateDetailAmount(d.amount, i);
        this.validateTaxableType(d.taxableType, i);
      });
    }
  }

  private validateDetailId(value: number, index: number) {
    if (!this.validatePositiveNumber(value)) {
      this.setErrorMessage(value, `details[${index}].detailId`);
    }
  }

  private validateProductId(value: string, index: number) {
    if (!this.validateString(value, PRODUCT_ID_LENGTH.MIN, PRODUCT_ID_LENGTH.MAX)) {
      this.setErrorMessage(value, `details[${index}].productId`);
    }
  }

  private validateProductName(value: string, index: number) {
    if (!this.validateString(value, PRODUCT_NAME_LENGTH.MIN, PRODUCT_NAME_LENGTH.MAX)) {
      this.setErrorMessage(value, `details[${index}].productName`);
    }
  }

  private validatePrice(value: number, index: number) {
    if (!this.validatePositiveNumber(value)) {
      this.setErrorMessage(value, `details[${index}].price`);
    }
  }

  private validateCount(value: number, index: number) {
    if (!this.validatePositiveNumber(value)) {
      this.setErrorMessage(value, `details[${index}].count`);
    }
  }

  private validateDetailTax(value: number, index: number) {
    if (!this.validatePositiveNumber(value)) {
      this.setErrorMessage(value, `details[${index}].tax`);
    }
  }

  private validateDetailAmount(value: number, index: number) {
    if (!this.validatePositiveNumber(value)) {
      this.setErrorMessage(value, `details[${index}].amount`);
    }
  }

  private validateTaxableType(value: TaxableType, index: number) {
    if (!Object.values(TAXABLE_TYPES).includes(value)) {
      this.setErrorMessage(value, `details[${index}].taxableType`);
    }
  }
}
