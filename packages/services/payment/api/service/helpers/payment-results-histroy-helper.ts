/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import {
  PAYMENT_STATUSES,
  TAXABLE_TYPES,
  TAX_TYPES,
  CALCULATION_TYPES,
  ROUNDING_TYPES
} from '../../types/payment';

import { PaymentResult } from '../user/payment-results-service';
import * as datetime from "../../../../common/utils/datetime";

const OUT_PUT_FIELDS = {
  serviceName: 'serviceName',
  orderId: 'orderId',
  details: 'details',
  tax: 'tax',
  amount: 'amount',
  taxType: 'taxType',
  taxRate: 'taxRate',
  calculationType: 'calculationType',
  roundingType: 'roundingType',
  status: 'status',
  completedDate: 'completedDate',
  canceledDate: 'canceledDate',
} as const;

const FIELD_NAMES = {
  [OUT_PUT_FIELDS.serviceName]: 'サービス名',
  [OUT_PUT_FIELDS.orderId]: '受付番号',
  [OUT_PUT_FIELDS.details]: '商品明細',
  [OUT_PUT_FIELDS.tax]: '税額',
  [OUT_PUT_FIELDS.amount]: '合計金額',
  [OUT_PUT_FIELDS.taxType]: '税区分',
  [OUT_PUT_FIELDS.taxRate]: '税率',
  [OUT_PUT_FIELDS.calculationType]: '消費税計算方法',
  [OUT_PUT_FIELDS.roundingType]: '端数処理区分',
  [OUT_PUT_FIELDS.status]: '決済ステータス',
  [OUT_PUT_FIELDS.completedDate]: '決済日時',
  [OUT_PUT_FIELDS.canceledDate]: 'キャンセル日時',
} as const;

const TAX_TYPE_TEXTS = {
  [TAX_TYPES.OUTER]: '外',
  [TAX_TYPES.INNTER]: '内',
} as const;

const CALCULATION_TYPE_TEXTS = {
  [CALCULATION_TYPES.FOR_EACH_ITEMS]: '明細ごと',
  [CALCULATION_TYPES.TOTAL]: '合計',
} as const;

const ROUNDING_TYPE_TEXTS = {
  [ROUNDING_TYPES.ROUND]: '四捨五入',
  [ROUNDING_TYPES.ROUND_DOWN]: '切捨て',
  [ROUNDING_TYPES.ROUND_UP]: '切上げ'
}

const PAYMENT_STATUS_TEXTS = {
  [PAYMENT_STATUSES.COMPLETE]: '決済完了',
  [PAYMENT_STATUSES.NOT_APPLICABLE]: '決済対象外',
  [PAYMENT_STATUSES.REFUNDED]: '返金済み',
  [PAYMENT_STATUSES.CREDIT_COMPLETE]: '与信完了',
  [PAYMENT_STATUSES.CREDIT_CANCEL]: '与信取消',
} as const;

const TAXABLE_TYPE_TEXTS = {
  [TAXABLE_TYPES.TAXABLE]: '課税',
  [TAXABLE_TYPES.TAX_FREE]: '非課税',
}

class PaymentResultsHistoryHelper {
  formatPaymentResultsForDetails(paymentResuls: Partial<PaymentResult>) {
    return Object.keys(OUT_PUT_FIELDS).reduce((str: string, field) =>   str += this.getFormatedValue(paymentResuls, field), '');
  }

  private getFormatedValue(paymentResuls: Partial<PaymentResult>, field: string) {
    const value = paymentResuls[field];
    if (value === null || value === undefined) {
      return '';
    }
    const fieldName = FIELD_NAMES[field];
    switch (field) {
      case OUT_PUT_FIELDS.details: {
        return this.detailsToText(value);
      }
      case OUT_PUT_FIELDS.taxType: {
        return this.formatFieldAndValue(fieldName, this.taxTypeToText(value));
      }
      case OUT_PUT_FIELDS.calculationType: {
        return this.formatFieldAndValue(fieldName, this.calcTypeToText(value));
      }
      case OUT_PUT_FIELDS.roundingType: {
        return this.formatFieldAndValue(fieldName, this.roundingTypeToText(value));
      }
      case OUT_PUT_FIELDS.status: {
        return this.formatFieldAndValue(fieldName, this.statusToText(value));
      }
      case OUT_PUT_FIELDS.completedDate:
      case OUT_PUT_FIELDS.canceledDate: {
        return this.formatFieldAndValue(fieldName, this.formatDate(value));
      }
      default: {
        return this.formatFieldAndValue(fieldName, value);
      }
    }
  }

  private formatFieldAndValue(fieldName: string, value: any) {
    return `[${fieldName}]\n${value}\n`;
  }

  private taxTypeToText(type: number) {
    return TAX_TYPE_TEXTS[type];
  }

  private calcTypeToText(type: number) {
    return CALCULATION_TYPE_TEXTS[type];
  }

  private roundingTypeToText(type: number) {
    return ROUNDING_TYPE_TEXTS[type];
  }

  private statusToText(status: number) {
    return PAYMENT_STATUS_TEXTS[status];
  }

  private formatDate(value: number) {
    const date = datetime.fromUnixSec(value);
    return date.toFormat('yyyy/MM/dd HH:mm:ss');
  }

  private detailsToText(details: PaymentResult['details']) {
    return details.reduce((str: string, d) => {
      return str += `[商品明細${d.detailId}]\n${d.productName} 数量${d.count} ${d.amount}円 ${this.taxableTypeToText(d.taxableType)}\n`;
    }, '');
  }

  private taxableTypeToText(type: number) {
    return TAXABLE_TYPE_TEXTS[type];
  }
}

const instance = new PaymentResultsHistoryHelper();
export default instance;