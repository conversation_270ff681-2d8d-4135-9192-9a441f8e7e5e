/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { DateTime } from 'luxon';
import  {
  TAXABLE_TYPES,
  PRODUCT_STATUSES,
} from '../../types/payment.js';
import paymentConfigsService, { RateSetting } from '../user/payment-configs-service.js';
import { PaymentResultsCalculateHelper } from './payment-results-calculate-helper.js';
import { PaymentOrder } from '@oss/services/survey/types/survey-results.js';


export class PaymentResultsFalsificationValidator {
  private errorMessage: { [index: string]: any };

  constructor(private order: PaymentOrder) {
    this.errorMessage = {};
  }

  async validate() {
    await this.validateOrder();
    return this.validateByConditions();
  }

  validateByConditions() {
    return this.isValid();
  }

  isValid() {
    return Object.keys(this.errorMessage).length === 0;
  }

  getErrorMessage() {
    return this.errorMessage;
  }

  async validateOrder() {
    // NOTE: 消費税率・消費税区分・消費税計算区分・端数処理区分がDBのデータと一致しているかチェック。合計/明細の消費税・金額の計算値が間違っていないかチェック。
    await Promise.all([
      this.validateTaxRate(),
      this.validateServiceSettings(),
      this.validateCalculationResults(),
    ]);
  }

  private async validateTaxRate() {
    const applyTaxRate = await this.getApplyTaxRate();
    if (this.order.taxRate !== applyTaxRate) {
      this.errorMessage.taxRate = '消費税率が更新されています。';
    }
  }

  private async getApplyTaxRate() {
    const rateSettings = await paymentConfigsService.getTaxSetting();
    const applyingIndex = this.getApplyingIndex(rateSettings);
    const isExistApplyingRate = applyingIndex !== -1;
    return isExistApplyingRate ? rateSettings[applyingIndex].value : null;
  }

  private getApplyingIndex(taxRateSettings: RateSetting[]): number {
    const now = DateTime.now();
    let findIndex = -1;
    let findDate: DateTime = null;
    taxRateSettings.forEach((s, i) => {
      const applyDate = DateTime.fromFormat(s.applyDate, 'yyyy-MM-dd');
      const isFuture = applyDate > now;
      if (isFuture) {
        return;
      }

      if (findDate === null || findDate < applyDate) {
        findDate = applyDate;
        findIndex = i;
      }
    });
    return findIndex;
  }

  private async validateServiceSettings() {
    const { serviceSortKey, taxType, calculationType, roundingType } = this.order;
    const service = await paymentConfigsService.getService(serviceSortKey);
    if (service.data.taxType !== taxType) {
      this.errorMessage.taxType = '税区分が更新されています。';
    }
    if (service.data.calculationType !== calculationType) {
      this.errorMessage.calculationType = '消費税計算方法が更新されています。';
    }
    if (service.data.roundingType !== roundingType) {
      this.errorMessage.roundingType = '端数処理区分が更新されています。';
    }
  }

  private async validateCalculationResults() {
    const { amount, tax, details, taxRate, taxType, calculationType, roundingType, serviceSortKey } = this.order;
    const paymentResultsCalculateHelper = new PaymentResultsCalculateHelper(taxRate, taxType, calculationType, roundingType);

    const totalAmount = paymentResultsCalculateHelper.calculateTotalAmount(details);
    if (amount !== totalAmount) {
      this.errorMessage.totalAmount = '合計金額が正しくありません。';
    }

    const totalTax = paymentResultsCalculateHelper.calculateTotalTax(details);
    if (tax !== totalTax) {
      this.errorMessage.totalTax = '消費税額が正しくありません。';
    }

    for (const index in details) {
      const { amount, amountWithTax, tax, count, price, productId, productName, taxableType } = details[index];
      const product = await paymentConfigsService.getOneProduct(`${serviceSortKey}_${productId}`);
      if (!product || product.status === PRODUCT_STATUSES.STOP) {
        this.errorMessage[`details[${index}]`] = `"${productName}"は削除されたか販売停止中です。`;
        continue;
      }

      if (product.price !== price || product.taxableType !== taxableType || product. productName !== productName) {
        this.errorMessage[`details[${index}]`] = `"${productName}"の商品情報が更新されています。`;
        continue;
      }

      const detailAmount = price * count;
      const detailTax = taxableType === TAXABLE_TYPES.TAX_FREE ? 0 : paymentResultsCalculateHelper.calculateTax(detailAmount);
      const totalAmount = paymentResultsCalculateHelper.calculateDetailAmountWithTax(detailAmount, detailTax);
      if (amount !== detailAmount || tax !== detailTax || amountWithTax !== totalAmount) {
        this.errorMessage[`details[${index}]`] = '明細金額が正しくありません。';
      }
    }
  }
}
