/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { RecordReservationPaymentItem, RecordReservationPaymentItemProps } from '../types/model-reservations';
import { repeatQuery, typeItems } from '../utils/repository-helpers';
import { ServiceException } from '../utils/exceptions';
import * as timeUtils from "@oss/services/common/utils/datetime";
import {config} from "../config/payment-config";
import {AWS, getEnvironmentCredentials} from "../../../common/utils/aws-helper";
import {updateOperation} from "../../../common/utils";
import {CalendarRecord} from "../../../api-admin-survey/types/calendars";

const PK_RESERVATION_PAYMENT_ITEMS = 'reservationPaymentItems';
const dynamoDbDocument = new AWS.DynamoDB.DocumentClient(getEnvironmentCredentials());

export function itemKey(sortKey: string) {
    return {
        partitionKey: PK_RESERVATION_PAYMENT_ITEMS,
        sortKey,
    }
}

export async function list(categoryId: string, includeDeleted = false, lastEvaluatedKey?: any) {
    const filterExpression = includeDeleted ? undefined : 'isDeleted = :false'
    return dynamoDbDocument.query({
        TableName: config.tableCalendars,
        KeyConditionExpression: 'partitionKey = :pk and begins_with(sortKey, :sk)',
        FilterExpression: filterExpression,
        ExpressionAttributeValues: {
            ':pk': 'reservationPaymentItems',
            ':sk': categoryId,
            ':false': filterExpression ? false : undefined
        },
        ExclusiveStartKey: lastEvaluatedKey,
    }).promise()
        .then(r => typeItems<RecordReservationPaymentItem>(r));
}

export async function listAll(categoryId: string, includeDeleted = false) {
    return repeatQuery(lek => list(categoryId, includeDeleted, lek))
}

export async function getOne(sortKey: string) {
    return dynamoDbDocument.get({
        TableName: config.tableCalendars,
        Key: itemKey(sortKey),
    }).promise().then(r => r.Item as RecordReservationPaymentItem)
}

export async function update(sortKey: string, patch: Partial<RecordReservationPaymentItem>, versionMark?: number) {
    return updateOperation(dynamoDbDocument).update({
        tableName: config.tableCalendars,
        key: itemKey(sortKey),
        patch: {
            updatedAt: timeUtils.nowUnixSec(),
            ...patch,
        },
        versionCheck: {
            updatedAt: versionMark,
        }
    }).then(updated => updated as RecordReservationPaymentItem);
}

export async function create(sortKey: string, props: RecordReservationPaymentItemProps) {
    const now = timeUtils.nowUnixSec();
    const record: RecordReservationPaymentItem = {
        ...props,
        ...itemKey(sortKey),
        updatedAt: now,
        createdAt: now,
    }
    await dynamoDbDocument.put({
        TableName: config.tableCalendars,
        Item: record
    }).promise();
    return record;
}

export async function getCalendarServiceLink(categoryId: string) {
    const category = await dynamoDbDocument.get({
        TableName: config.tableCalendars,
        Key: {
            partitionKey: 'categories',
            sortKey: categoryId,
        }
    }).promise().then(r => r.Item);
    if (!category) {
        throw new ServiceException('not_found', `分類 ID=[${categoryId}]は存在しておりません。`);
    }
    if (!category.calendarId) {
        throw new ServiceException('not_found', `分類 ID=[${categoryId}]はカレンダーと紐づいておりません。`);
    }
    const calendar = await dynamoDbDocument.get({
        TableName: config.tableCalendars,
        Key: {
            partitionKey: 'calendars',
            sortKey: category.calendarId,
        }
    }).promise().then(r => r.Item as CalendarRecord);

    if (!calendar) {
        throw new ServiceException('not_found', `カレンダーID=[${category.calendarId}]は存在しておりません。`);
    }
    return calendar.paymentServiceSortKey;
}

type ServiceCategoryMapping = {
    partitionKey: string,
    sortKey: string,
    paymentServiceSortKey: string
}

export async function getForProduct(serviceId: string, productId: string, includeDeleted = false) {
    const filter = includeDeleted ? undefined : `attribute_not_exists(isDeleted) OR isDeleted = :false`
    return repeatQuery((lek: any) => dynamoDbDocument.query({
        TableName: config.tableCalendars,
        KeyConditionExpression: 'serviceProductId = :serviceProductId',
        FilterExpression: filter,
        IndexName: 'serviceProductId-sortKey-index',
        ExpressionAttributeValues: {
            ':serviceProductId': `${serviceId}_${productId}`,
            ':false': includeDeleted ? undefined : false,
        },
        ExclusiveStartKey: lek,
    }).promise()).then(r => r as RecordReservationPaymentItem[]);
}