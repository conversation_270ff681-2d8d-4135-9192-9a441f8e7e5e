/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { RecordPaymentResults } from '../types/model-results'
import { buildConditions } from '@oss/services/common/utils/db/update-operation';
import { isConditionException } from '../utils/repository-helpers'
import { ServiceException } from "../utils/exceptions";
import {AWS, getEnvironmentCredentials} from "../../../common/utils/aws-helper";
import {config} from "../config/payment-config";

const awsConfig = getEnvironmentCredentials();
const dynamoDbDocument = new AWS.DynamoDB.DocumentClient(awsConfig);

export async function getOne(partitionKey: string, sortKey: string) {
    return dynamoDbDocument.get({
        TableName: config.tablePaymentResults,
        Key: {
            partitionKey,
            sortKey,
        }
    }).promise().then(v => v.Item as RecordPaymentResults)
}

export async function findBySortKey(sortKey: string) {
    return dynamoDbDocument.query({
        TableName: config.tablePaymentResults,
        KeyConditionExpression: 'sortKey = :sk',
        ExpressionAttributeValues: {
            ':sk': sortKey,
        },
        IndexName: 'sortKey-index'
    }).promise()
        .then(({ Items }) => Items as RecordPaymentResults[]);
}

export async function createDownloadLink(forKey: string, contentType) {
    const client = new AWS.S3(awsConfig);
    return client.getSignedUrlPromise('getObject', {
        Bucket: config.bucketImportExport,
        Key: forKey,
        ResponseContentType: contentType,
        Expires: 900,
    });
}

export async function saveToS3(key: string, data: string) {
    const client = new AWS.S3(awsConfig);
    await client.putObject({
        Bucket: config.bucketImportExport,
        Key: key,
        Body: data,
    }).promise();
}

export async function create(record: RecordPaymentResults) {
    return dynamoDbDocument.put({
        Item: record,
        TableName: config.tablePaymentResults,
    }).promise();
}

export async function update(record: RecordPaymentResults, versionMark?: number) {
    try {
        const conditions = buildConditions({ updatedAt: versionMark });
        await dynamoDbDocument.put({
            Item: record,
            TableName: config.tablePaymentResults,
            ConditionExpression: conditions.expression,
            ExpressionAttributeNames: conditions.ean,
            ExpressionAttributeValues: conditions.eav
        }).promise();
    } catch (e) {
        if (isConditionException(e)) {
            throw new ServiceException('exclusion_error', '他のユーザーによって変更されました。')
        }
        throw e;
    }
}