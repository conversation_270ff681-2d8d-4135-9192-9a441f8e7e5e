/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import _ from 'lodash';
import * as shiftjis from "shiftjis";
import { config } from '../config/payment-config';
import { ServiceException } from '../utils/exceptions';
import { isConditionException, repeatQuery, repeatRequest, typeItems } from '../utils/repository-helpers';
import { RecordProduct, Status } from '../types/model-products';
import AWS from 'aws-sdk';
import {infoLog} from "../../../common/admin-api/utils/logger";
import {getEnvironmentCredentials} from "../../../common/utils/aws-helper";
import {updateOperation} from "../../../common/utils";

const awsConfig = getEnvironmentCredentials();
const dynamoDbDocument = new AWS.DynamoDB.DocumentClient(awsConfig);

export function productKey(serviceId: string, productId: string) {
    return {
        partitionKey: 'products',
        sortKey: `${serviceId}_${productId}`
    }
}
export async function list(filter: {
    serviceId: string,
    status?: Status
}, lastEvaluatedKey?: any) {
    const ean = {};
    const eav = {
        ':pk': 'products',
        ':sk': filter.serviceId,
    };
    let fe: string = undefined;
    if (!_.isNil(filter.status)) {
        fe = '#status = :status'
        ean['#status'] = 'status';
        eav[':status'] = filter.status;
    }
    const response = await dynamoDbDocument.query({
        TableName: config.tablePaymentConfigs,
        KeyConditionExpression: 'partitionKey = :pk and begins_with(sortKey, :sk)',
        FilterExpression: fe,
        ExpressionAttributeValues: eav,
        ExpressionAttributeNames: _.isEmpty(ean) ? undefined : ean,
        ExclusiveStartKey: lastEvaluatedKey,
    }).promise();

    return {
        items: response.Items as RecordProduct[],
        lastEvaluatedKey: response.LastEvaluatedKey,
    }
}

export async function create(record: RecordProduct, allowOverwrite = false) {
    try {
        await dynamoDbDocument.put({
            TableName: config.tablePaymentConfigs,
            Item: record,
            ConditionExpression: allowOverwrite ? undefined : 'attribute_not_exists(sortKey)'
        }).promise();
        return true;
    } catch (e) {
        if (isConditionException(e)) {
            return false;
        }
        throw e;
    }
}

export async function update(serviceId: string,
                             productId: string,
                             patch: Partial<RecordProduct>,
                             versionMark?: number) {
    try {
        const response = await updateOperation(dynamoDbDocument).update({
            tableName: config.tablePaymentConfigs,
            key: productKey(serviceId, productId),
            patch,
            versionCheck: {
                updatedAt: versionMark,
            }
        });
        return response as RecordProduct
    } catch (e) {
        if (isConditionException(e)) {
            throw new ServiceException('exclusion_error', '他のユーザーによって変更されました。')
        }
        throw e;
    }
}

export async function findBySortKey(sk: string) {
    return dynamoDbDocument.get({
        TableName: config.tablePaymentConfigs,
        Key: {
            partitionKey: 'products',
            sortKey: sk
        },
    }).promise().then(v => v.Item as RecordProduct);
}

export async function findByName(serviceId: string, name: string) {
    return repeatQuery(lek =>
        dynamoDbDocument.query({
            TableName: config.tablePaymentConfigs,
            KeyConditionExpression: 'partitionKey = :pk AND begins_with(sortKey, :sk)',
            FilterExpression: 'productName = :name',
            ExpressionAttributeValues: {
                ':pk': 'products',
                ':sk': serviceId,
                ':name': name,
            },
            ExclusiveStartKey: lek,
        }).promise().then(r => typeItems<RecordProduct>(r))
    );
}

export async function createUploadLink(forKey: string, contentType: string) {
    const client = new AWS.S3(awsConfig);
    return client.getSignedUrlPromise('putObject', {
        Bucket: config.bucketImportExport,
        Key: forKey,
        ContentType: contentType,
        Expires: 900,
    });
}

export async function createDownloadLink(forKey: string, contentType) {
    const client = new AWS.S3(awsConfig);
    return client.getSignedUrlPromise('getObject', {
        Bucket: config.bucketImportExport,
        Key: forKey,
        ResponseContentType: contentType,
        Expires: 900,
    });
}

export async function saveToS3(key: string, data: string) {
    const client = new AWS.S3(awsConfig);
    await client.putObject({
        Bucket: config.bucketImportExport,
        Key: key,
        Body: data,
    }).promise();
}

export async function deleteFile(key: string) {
    const client = new AWS.S3(awsConfig);
    await client.deleteObject({
        Bucket: config.bucketImportExport,
        Key: key
    }).promise();
}

export async function downloadTextFile(key: string, isShiftJis = false) {
    const client = new AWS.S3(awsConfig);
    try {
        const res = await client.getObject({
            Bucket: config.bucketImportExport,
            Key: key,
            ResponseContentEncoding: 'application/octet-stream'
        }).promise();

        const buffer = res.Body;
        if (isShiftJis) {
            return shiftjis.decode(buffer);
        } else {
            return buffer.toString('utf-8')
        }
    } catch (e) {
        infoLog('downloadTextFile', e);
        // TODO if not found, return null. Re-throw an exception otherwise
        return null;
    }
}

export async function deleteProductRecord(serviceId: string, productId: string) {
    return dynamoDbDocument.delete({
        TableName: config.tablePaymentConfigs,
        Key: productKey(serviceId, productId),
    }).promise();
}

export async function countProducts(serviceId: string) {
    const items = await repeatRequest((lek) => {
        return dynamoDbDocument.query({
            TableName: config.tablePaymentConfigs,
            KeyConditionExpression: 'partitionKey = :pk and begins_with(sortKey, :sk)',
            ExpressionAttributeValues: {
                ':pk': "products",
                ':sk': serviceId,
            },
            ExclusiveStartKey: lek,
            ProjectionExpression: 'partitionKey, sortKey'
        }).promise().then(v => ({ items: v.Items, lastEvaluatedKey: v.LastEvaluatedKey }));
    });
    return items.length;
}