/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { ServiceRecord, ReservationServiceType } from "../types/model-services.js";
import { config } from '../config/payment-config.js';
import { updateOperation } from "@oss/services/common/utils/db/update-operation.js";
import { BatchDeleteOperation } from '@oss/services/common/utils/db/batch-delete-operation.js';
import _ from "lodash";
import {Table} from "../../../common/utils/db/table.js";
import {getEnvironmentCredentials, AWS} from "../../../common/utils/aws-helper.js";

export const SERVICES_PK = 'services'

const awsConfig = getEnvironmentCredentials();
const dynamoDbDocument = new AWS.DynamoDB.DocumentClient(awsConfig);

function tableKey(serviceId?: string) {
    return {
        partitionKey: SERVICES_PK,
        sortKey: serviceId
    }
}

export async function deleteSingle(sortKey: string) {
    return await dynamoDbDocument.delete({
        TableName: config.tablePaymentConfigs,
        Key: tableKey(sortKey),
    }).promise();
}

export async function deleteSelected(serviceIds: string[]) {
    const op = new BatchDeleteOperation<string>(dynamoDbDocument, config.tablePaymentConfigs, tableKey)
    op.setItems(serviceIds);
    await op.process();
    return op.report;
}

export async function update(id: string, patch: Partial<ServiceRecord>, versionMark: number) {
    console.log('update', id, patch, versionMark, config.tablePaymentConfigs)
    return await updateOperation(dynamoDbDocument).update({
        tableName: config.tablePaymentConfigs,
        key: tableKey(id),
        patch,
        versionCheck: {
            updatedAt: versionMark,
        }
    }) as ServiceRecord
}

export async function find(filter: {
                               reservationServiceType?: ReservationServiceType
                           },
                           lastEvaluatedKey: any,
) {
    const client = new Table(config.tablePaymentConfigs, null, awsConfig);
    const mapping = {
        ':pk': tableKey().partitionKey,
    };
    const filterExpr = !_.isNil(filter.reservationServiceType)
        ? 'reservationServiceType = :reservationServiceType'
        : undefined
    if (filterExpr) {
        mapping[':reservationServiceType'] = filter.reservationServiceType;
    }

    const result = await client.queryPage({
        query: 'partitionKey = :pk',
        filter: filterExpr,
        mapping,
        lastEvaluatedKey,
    });
    return {
        items: result.items as ServiceRecord[],
        lastEvaluatedKey: result.lastEvaluatedKey,
    }
}
export async function getBySortKey(id: string) {
    const response = await dynamoDbDocument.get({
        TableName: config.tablePaymentConfigs,
        Key: tableKey(id),
    }).promise();
    return response.Item as ServiceRecord;
}
export async function findByServiceId(serviceId: string) {
    const response = await dynamoDbDocument.query({
        TableName: config.tablePaymentConfigs,
        KeyConditionExpression: 'partitionKey = :pk AND begins_with(sortKey, :sk)',
        ExpressionAttributeValues: {
            ':pk': SERVICES_PK,
            ':sk': `${serviceId}`
        }
    }).promise();
    return _.isEmpty(response.Items) ? null : response.Items[0] as ServiceRecord;
}
export async function create(record: ServiceRecord) {
    const response = await dynamoDbDocument.put({
        TableName: config.tablePaymentConfigs,
        Item: record,
        ConditionExpression: 'attribute_not_exists(serviceId)',
        ReturnValues: 'ALL_OLD'
    }).promise();
    return response.Attributes as ServiceRecord
}
