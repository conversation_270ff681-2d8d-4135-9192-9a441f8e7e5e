/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import * as timeUtils from "@oss/services/common/utils/datetime";
import { TaxSettingsRateSetting, TaxSettingsRecord } from '../types/model-taxes';
import {config} from "../config/payment-config";
import {AWS, getEnvironmentCredentials} from "../../../common/utils/aws-helper";
import {updateOperation} from "../../../common/utils";

const PK = {
    partitionKey: 'taxSetting',
    sortKey: 'default'
}
const dynamoDbDocument = new AWS.DynamoDB.DocumentClient(getEnvironmentCredentials());

export async function get() {
    const record = await dynamoDbDocument
        .get({
            TableName: config.tablePaymentConfigs,
            Key: PK,
        })
        .promise();
    return record.Item as TaxSettingsRecord;
}

export async function upsert(settings: TaxSettingsRateSetting[]) {
    const now = timeUtils.nowUnixSec();
    const existed = await get();
    if (!existed) {
        const record: TaxSettingsRecord = {
            ...PK,
            createdAt: now,
            updatedAt: now,
            rateSettings: settings,
        }
        await dynamoDbDocument.put({
            TableName: config.tablePaymentConfigs,
            Item: record,
        }).promise();
        return record;
    }
    return await updateOperation(dynamoDbDocument).update({
        tableName: config.tablePaymentConfigs,
        key: PK,
        patch: {
            updatedAt: now,
            rateSettings: settings
        }
    }) as TaxSettingsRecord;
}
