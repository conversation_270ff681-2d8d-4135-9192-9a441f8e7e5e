import { Request, Response, NextFunction } from "lambda-api";
import StaticConfig from '../config/static.js'
import Constants from '../config/constants.js'
import axios from "axios";

export const requireLiffAuth = (req: Request, res: Response, next: NextFunction) => {
    if (!req.userId) {
        res.status(401).json({ error: 'Unauthorized' })
        return
    }
    next()
}

export const buildAuthentication = () => {
    return async (req: Request, res: Response, next: NextFunction) => {
        let userId = null;
        if (StaticConfig.isDevelopmentEnv) {
            userId = req.headers['x-auth-id'];
        }
        if (!userId) {
            userId = await tryToAuthFromHeader(req)
        }
        req.userId = userId
        next()
    }
}


type VerificationError = {
    error: string,
    error_description: string
}
type VerifiedToken = {
    iss: string,
    sub: string,
    aud: string,
    exp: number,
    iat: number,
    auth_time: number, nonce?: string,
    amr: string[],
    name?: string,
    picture?: string,
    email?: string,
}

type VerificationResponse = {
    error: VerificationError,
    token: undefined
} | {
    error: undefined,
    token: VerifiedToken
}

type VerificationApiResponse = VerifiedToken | VerificationError

async function tryToAuthFromHeader(req: Request) {
    const authHeader = req.auth
    if (!authHeader || authHeader.type !== 'Bearer') {
        return undefined
    }
    const tokenString = authHeader.value
    const { error, token } = await verifyToken(tokenString)
    if (error) {
        console.log(error)
        return null;
    }
    return token.sub

}

const verifyToken = async (token: string): Promise<VerificationResponse> => {
    const channelId = StaticConfig.get(Constants.LINELOGIN_CHANNEL_ID)
    if (!channelId) {
        console.log('LINE channelId is not configured')
        return {
            error: {
                error: 'internal',
                error_description: 'channelId is not configured'
            },
            token: undefined
        }
    }

    const params = new URLSearchParams();
    params.append('id_token', token);
    params.append('client_id', channelId);
    try {
        const result = await axios.post('https://api.line.me/oauth2/v2.1/verify', params, {
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            validateStatus: () => true,
        });
        const data: VerificationApiResponse = result.data
        if ((data as VerificationError).error) {
            return { error: data as VerificationError, token: undefined }
        } else {
            return { token: data as VerifiedToken, error: undefined }
        }
    } catch (error: any) {
        return {
            error: {
                error: 'network',
                error_description: error.message,
            },
            token: undefined
        }
    }
};