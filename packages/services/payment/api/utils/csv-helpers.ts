/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import _ from 'lodash';
import * as timeUtils from '@oss/services/common/utils/time-utils';

type ValueMapping<T> = {
    csv: string,
    db: T,
}

export class ValuesMap<T = any> {
    values: ValueMapping<T>[] = []

    dbValue(csvValue: string, defaultValue: T = undefined) {
        const value = this.values.filter(v => v.csv === csvValue).map(v => v.db)[0]
        if (_.isNil(value)) {
            return defaultValue;
        }
        return value;
    }
    csvValue(dbValue: T, defaultValue = '') {
        const value = this.values.filter(v => v.db === dbValue).map(v => v.csv)[0]
        if (_.isNil(value)) {
            return defaultValue;
        }
        return value;
    }

    map(csv: string, db: T) {
        this.values.push({csv, db});
        return this;
    }
    fromMap(mapping: {[key: string]: T}) {
        _.toPairs(mapping).forEach(v => this.map(v[0], v[1]))
        return this;
    }

    static map<T = any>(csv: string, db:T ) {
        return new ValuesMap().map(csv, db);
    }
    static fromMap<T = any>(mapping: {[key: string]: T}) {
        return new ValuesMap().fromMap(mapping);
    }
}

function timestampFormatter(opts: {format?: string, defaultValue?: string} = {
    defaultValue: '',
    format: 'yyyy-MM-dd HH:mm:ss'
}) {
    return (value: number) => {
        if (_.isNil(value)) return opts.defaultValue;
        return timeUtils.fromUnixSec(value).toFormat(opts.format)
    }
}

export const formatters = {
    timestamp: timestampFormatter,
    enum: ValuesMap.fromMap,
}