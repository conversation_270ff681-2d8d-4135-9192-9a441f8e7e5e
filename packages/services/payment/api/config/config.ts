/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import c from '../config/constants.js';

import mts from 'moment-timezone';
mts.tz.setDefault('Asia/Tokyo');

export const isValue = (val) => {
  return !(val === null || val === undefined || val === '');
};
export const getFirstNonEmpty = (...values) => {
  return values.find(isValue);
};

export class Optional {
  private getter: any;
  constructor(getter) {
    this.getter = getter;
  }
  or(defaultValue) {
    return this.orFirst(defaultValue);
  }
  orFirst(...defaultValues) {
    return getFirstNonEmpty(this.getter(), ...defaultValues);
  }
}

export const compose = (...optionsMaps) => {
  let result = {};

  optionsMaps.forEach((map) => {
    result = Object.assign(result, map);
  });

  return result;
};

export class Config {
  private map: Record<string, any>;
  constructor(map: Record<string, any>) {
    this.map = map || {};
  }

  merge(update: Config) {
    const map = update.map ? update.map : update;
    this.map = compose(this.map, map);
  }

  addAll(map: Record<string, any>) {
    Object.assign(this.map, map)
  }

  get all() {
    return this.map;
  }

  opt(key) {
    return new Optional(() => this.map[key]);
  }

  get(key) {
    return this.map[key];
  }

  getBoolean(key) {
    const val = this.get(key);
    return val === 'true';
  }

  getOrDefaults(key, ...defaultValues) {
    return getFirstNonEmpty(this.map[key], ...defaultValues);
  }

  print() {
    return JSON.stringify(this.map, null, 2);
  }
}

export class AppConfig extends Config {
  constructor(...args) {
    super(args.length === 0 ? {} : args[0]);
  }

  get environmentName() {
    return this.get(c.DEPLOY_ENV);
  }

  get isLocalRuntime() {
    return this.runtimeEnv === 'local';
  }

  get isProductionEnv() {
    return this.environmentType === 'production';
  }

  get isDevelopmentEnv() {
    return this.environmentType === 'development';
  }

  get isTestEnv() {
    return this.environmentType === 'test';
  }

  get runtimeEnv() {
    let defaultRuntimeEnv = 'local';
    if (process.env.AWS_LAMBDA_FUNCTION_NAME) {
      defaultRuntimeEnv = 'aws';
    }

    return defaultRuntimeEnv;
  }

  get isAwsRuntime() {
    return this.runtimeEnv === 'aws';
  }

  get environmentType() {
    if (this.get(c.ENVIRONMENT_TYPE)) {
      return this.get(c.ENVIRONMENT_TYPE);
    }
    if (
      this.getBoolean(c.DEVELOPMENT_MODE) || this.isLocalRuntime
    ) {
      return 'development';
    }
    if (this.get(c.DEVELOPMENT_MODE) === 'test') {
      return 'test';
    }
    return 'production';
  }
}
