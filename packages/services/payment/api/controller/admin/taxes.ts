/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { API, HandlerFunction } from 'lambda-api';
import { member } from '@oss/services/common/admin-middlewares/access-guards';
import {  getValidBody, respond } from '../../utils/controller-helpers';
import { TaxSettingsRequest, validators } from '../../types/model-taxes';
import * as taxService from '../../service/admin/payment-taxes';

const get: HandlerFunction = async (req, res) => {
  
  const taxSettings = await taxService.getTaxSettings();
  if (!taxSettings) {
    respond(res).notFound('消費税設定が見つかりませんでした。');
  } else {
    respond(res).success<TaxSettingsRequest>({
      rateSettings: taxSettings,
    });
  }
};

const upsert: HandlerFunction = async (req, res) => {
  const body = getValidBody<TaxSettingsRequest>(req, validators.taxSettingsRequest);
  // await paymentActionLog('消費税設定更新',
  //   body.rateSettings.forEach(s => `適用日: ${s.applyDate}, 税率: ${s.value}`)); // FIXME logging
    
  const updatedSettings = await taxService.upsertTaxSettings(body.rateSettings);
  respond(res).success<TaxSettingsRequest>({
    rateSettings: updatedSettings,
  });
};

export function routes(router: API) {
  router.get('/tax-setting', get);
  router.post('/tax-setting', member(upsert));
};
