/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { API, HandlerFunction } from "lambda-api";
import { member } from '@oss/services/common/admin-middlewares/access-guards';
import { extractLastEvaluatedKey, getValidBody, respond } from '../../utils/controller-helpers';
import { CategoryCreateRequest, CategoryUpdateRequest, validators } from '../../types/model-product-categories';
import * as service from '../../service/admin/payment-product-categories';

const list: HandlerFunction = async (req, res) => {
    const serviceSortKey = decodeURIComponent(req.params.key);
    const lastEvaluatedKey = extractLastEvaluatedKey(req);
    const result = await service.findAll(serviceSortKey, lastEvaluatedKey);
    respond(res).successWithData(result.items, result.lastEvaluatedKey);
}
const create: HandlerFunction = async (req, res) => {
    const serviceSortKey = decodeURIComponent(req.params.key);
    const request = getValidBody<CategoryCreateRequest>(req, validators.createRequest);
    const result = await service.createNew(serviceSortKey, request)
    
    respond(res).successWithData(result)
    // await paymentActionLog('商品分類作成', serviceSortKey, result.id, result.name); // FIXME logging
}
const update: HandlerFunction = async (req, res) => {
    const serviceSortKey = decodeURIComponent(req.params.key);
    const request = getValidBody<CategoryUpdateRequest>(req, validators.updatedRequet);

    respond(res).successWithData(await service.updateExisted(serviceSortKey, request.id, request));
    // await paymentActionLog('商品分類更新', serviceSortKey, request.id, request.name); // FIXME logging
}
const del: HandlerFunction = async (req, res) => {
    const serviceSortKey = decodeURIComponent(req.params.key);
    const request = getValidBody<{ids: string[]}>(req, validators.deleteRequest);
    respond(res).successWithBody({
        ids: await service.deleteAll(serviceSortKey, request.ids)
    })

    // await paymentActionLog('商品分類一括削除', serviceSortKey, request.ids); // FIXME logging
}

export function routes(api: API) {
    api.get('/service/:key/product-categories', list);
    api.post('/service/:key/product-categories', member(create));
    api.put('/service/:key/product-categories', member(update));
    api.post('/service/:key/product-categories/delete', member(del))
}