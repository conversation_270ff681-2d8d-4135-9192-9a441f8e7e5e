/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { API, HandlerFunction } from "lambda-api";
import { admin } from '@oss/services/common/admin-middlewares/access-guards.js';
import { getValidBody, getValidQuery, respond } from '../../utils/controller-helpers.js';
import { RequestCreateUpdateReservation, validators } from '../../types/model-reservations.js';
import { getCategoryItems, loadReservationItems, createUpdateMany } from "../../service/admin/payment-reservations.js";

const getOne: HandlerFunction = async (req, res) => {
    const categoryId = decodeURIComponent(req.params.categoryId);
    respond(res).successWithData(await getCategoryItems(categoryId))
}
const list: HandlerFunction = async (req, res) => {
    const { include_deleted } = getValidQuery(req, validators.listQueryValidator);
    const categoryId = decodeURIComponent(req.params.categoryId);
    const { items, maxSerialNumber } = await loadReservationItems(categoryId, include_deleted === 'true');
    respond(res).successWithBody({
        data: items,
        maxSerialNumber,
    })
}
const createUpdate: HandlerFunction = async (req, res) => {
    const request = getValidBody<RequestCreateUpdateReservation[]>(req, validators.createUpdateRequestValidator);

    respond(res).successWithData(await createUpdateMany(request));
    // await actionLog('カレンダー', '商品用予約項目更新', request.length); // FIXME logging
}

export function routes(api: API) {
    api.get('/reservation-payment-products/:categoryId', getOne);
    api.get('/reservation-payment-items/:categoryId', list);
    api.post('/calender-payment-items', admin(createUpdate));
}