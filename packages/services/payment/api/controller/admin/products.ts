/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { API, HandlerFunction } from "lambda-api";
import { member } from '@oss/services/common/admin-middlewares/access-guards';
import { extractLastEvaluatedKey, getValidBody, respond } from '../../utils/controller-helpers';
import { RequestChangeOrder, RequestCreateProductItem, RequestMassDelete, RequestUpdateProductItem, Status, validators } from '../../types/model-products';
import * as service from '../../service/admin/payment-products.js';

const list: HandlerFunction = async (req, res) => {
    const serviceSortKey = decodeURIComponent(req.params.key);
    const lek = extractLastEvaluatedKey(req);
    const serviceType = parseInt(req.query.status || "2");

    const  {items, lastEvaluatedKey } = await service.find(serviceSortKey, {
        status: serviceType < 2 ? serviceType as Status : undefined
    },lek)
    respond(res).success(items, lastEvaluatedKey);
}

const create: HandlerFunction = async (req, res) => {
    const request = getValidBody<RequestCreateProductItem>(req, validators.createRequest)    
    const serviceSortKey = decodeURIComponent(req.params.key);
    // await paymentActionLog('商品作成', serviceSortKey, null, request); // FIXME logging

    respond(res).successWithData(await service.createFromDto(serviceSortKey, request));
}

const update: HandlerFunction = async (req, res) => {
    const request = getValidBody<RequestUpdateProductItem>(req, validators.updateRequest);
    const serviceSortKey = decodeURIComponent(req.params.key);
    // await paymentActionLog('商品更新', serviceSortKey, request.productId, request); // FIXME logging

    respond(res).successWithData(await service.updateFromDto(serviceSortKey, request));
}

const validateImportCsv: HandlerFunction = async (req, res) => {
    const {bucketKey, isSJISEncoding} = req.body;
    const serviceSortKey = decodeURIComponent(req.params.key);
    const  { resultKey, messages }= await service.validateImportCsv(serviceSortKey, bucketKey, isSJISEncoding)
    if (messages) {
        respond(res).successWithBody({
            code: 'validationError',
            errorMessages: messages,
        })
    } else {
        respond(res).successWithBody({ bucketKey: resultKey })
    }
}

const getImportCsvUrl: HandlerFunction = async (req, res) => {
    const {bucketKey, contentType} = req.query;    
    respond(res).successWithBody({
        url: await service.createImportLink({ bucketKey, contentType })
    })
}

const importCsv: HandlerFunction = async (req, res) => {
    const { bucketKey } = req.body;
    const serviceSortKey = decodeURIComponent(req.params.key);
    await service.importFromS3Json(serviceSortKey, bucketKey)
    respond(res).success()
}

const exportCsv: HandlerFunction = async (req, res) => {
    const request = getValidBody<{ids: string[]}>(req, validators.exportRequest);
    const serviceSortKey = decodeURIComponent(req.params.key);
    // await paymentActionLog('商品CSVエクスポート', serviceSortKey, request.ids); // FIXME logging

    const url = await service.exportToCsv(serviceSortKey, request.ids);
    respond(res).successWithBody({
        url,
    })
}
const saveOrder: HandlerFunction = async (req, res) => {
    const request = getValidBody<RequestChangeOrder>(req, validators.reorderRequest);
    const serviceSortKey = decodeURIComponent(req.params.key);
    // await paymentActionLog('商品並び順更新', serviceSortKey, request.ids.length) // FIXME logging

    respond(res).success(await service.updateOrder(serviceSortKey, request.ids))
}

const deleteProducts: HandlerFunction = async (req, res) => {
    const request = getValidBody<RequestMassDelete>(req, validators.massDeleteRequest);
    const serviceSortKey = decodeURIComponent(req.params.key);
    // await paymentActionLog('商品削除', serviceSortKey, request.ids) // FIXME logging
    const result = await service.massDeletion(serviceSortKey, request.ids);
    respond(res).successWithBody({
        ids: result,
    })
}

export function routes(api: API) {
    api.get('/services/:key/products', list);
    api.post('/services/:key/products', member(create));
    api.put('/services/:key/products', member(update));
    api.get('/services/:key/products/presigned-url', member(getImportCsvUrl));
    api.post('/services/:key/products/validate-csv', member(validateImportCsv));
    api.post('/services/:key/products/save-csv', member(importCsv));
    api.post('/services/:key/products/export-csv', member(exportCsv));
    api.put('/services/:key/products/order', member(saveOrder));
    api.post('/services/:key/products/delete', member(deleteProducts));
}