/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *   - name: PaymentConfig
 *     description: 決済基盤設定API
 */

import type { Request, Response } from 'lambda-api';
import paymentConfigsService from '../../service/user/payment-configs-service.js';
import paymentProductsService from '../../service/user/payment-products-service.js';
import {BadRequest, ServerError} from "../../../../common/utils/exceptions.js";
import loggerService from "../../../../common/logs/logger-service.js";

/**
 * @openapi
 * /payment/configs/services:
 *  get:
 *    summary: サービス・商品一覧
 *    description: 管理画面で設定されたサービスや商品の一覧を取得します。
 *    tags: [PaymentConfig]
 *    parameters:
 *      - in: query
 *        name: fields
 *        required: false
 *        schema:
 *          type: string
 *      - in: query
 *        name: reservationServiceType
 *        required: false
 *        schema:
 *          type: string
 *      - in: query
 *        name: lastEvaluatedKey
 *        required: false
 *        schema:
  *         type: string
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 *              properties:
 *                code:
 *                  type: "string"
 *                data:
 *                  type: "object"
 *                lastEvaluatedKey:
 *                  type: "string"
 */
const getServices = async (req: Request, res: Response) => {
  await loggerService.logApiEvent({
    logType: 'payment-config',
    endpointName: 'services',
    action: 'getServices',
  });

  const { lastEvaluatedKey: queryLastEvaluatedKey, fields, reservationServiceType: reservationServiceTypeStr } = req.query;

  let reservationServiceType;
  if (reservationServiceTypeStr) {
    const num = parseInt(reservationServiceTypeStr, 10);
    if (!Number.isNaN(num)) {
      reservationServiceType = num;
    }
  }

  const { data, lastEvaluatedKey } = await paymentConfigsService.getServices(fields, reservationServiceType, queryLastEvaluatedKey);
  res.status(200).json({
    code: 'success',
    data,
    lastEvaluatedKey,
  });
};

/**
 * @openapi
 * /payment/configs/services/{key}:
 *  get:
 *    summary: サービス・商品取得
 *    description: 管理画面で設定されたサービスや商品を取得します。
 *    tags: [PaymentConfig]
 *    parameters:
 *      - in: path
 *        name: key
 *        required: true
 *        schema:
 *          type: string
 *      - in: query
 *        name: include_products
 *        required: false
 *        schema:
 *          type: string
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 *              properties:
 *                code:
 *                  type: "string"
 *                data:
 *                  type: "object"
 */
const getService = async (req: Request, res: Response) => {
  await loggerService.logApiEvent({
    logType: 'payment-config',
    endpointName: 'services',
    action: 'getService',
  });

  const { key } = req.params;
  const { include_products } = req.query;

  // NOTE: keyのバリデーションが必要？

  const includeProducts = include_products === 'true';
  const { data } = await paymentConfigsService.getService(decodeURIComponent(key), includeProducts);
  res.status(200).json({
    code: 'success',
    data,
  });
};

/**
 * @openapi
 * /payment/configs/service/{key}/product-categories:
 *  get:
 *    summary: 商品分類取得
 *    description: 管理画面で設定された商品分類を取得します。
 *    tags: [PaymentConfig]
 *    parameters:
 *      - in: path
 *        name: key
 *        required: true
 *        schema:
 *          type: string
 *      - in: query
 *        name: lastEvaluatedKey
 *        required: false
 *        schema:
 *          type: string
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 *              properties:
 *                code:
 *                  type: "string"
 *                data:
 *                  type: "object"
 *                lastEvaluatedKey:
 *                  type: "string"
 */
const getProductCategories = async (req: Request, res: Response) => {
  await loggerService.logApiEvent({
    logType: 'payment-config',
    endpointName: 'product-categories',
    action: 'getProductCategories',
  });

  const { key } = req.params;
  const { lastEvaluatedKey: queryLastEvaluatedKey } = req.query;

  // NOTE: ここはkeyのバリデーションがいらない？

  const { data, lastEvaluatedKey } = await paymentConfigsService.getProductCategories(decodeURIComponent(key), queryLastEvaluatedKey);
  res.status(200).json({
    code: 'success',
    data,
    lastEvaluatedKey
  });
};

/**
 * @openapi
 * /payment/configs/tax-setting:
 *  get:
 *    summary: 消費税設定取得
 *    description: 管理画面で設定された消費税設定を取得します。
 *    tags: [PaymentConfig]
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: object
 *              properties:
 *                code:
 *                  type: string
 *                data:
 *                  type: object
 *                message: string
 */
 const getTaxRateSettings = async (req: Request, res: Response) => {
  await loggerService.logApiEvent({
    logType: 'payment-config',
    endpointName: 'tax-setting',
    action: 'getTaxRateSettings',
  });

  const rateSettings = await paymentConfigsService.getTaxSetting();
  const response = rateSettings
    ? { code: 'success', data: { rateSettings } }
    : { code: 'not_found', message: '消費税設定が見つかりませんでした。' };
  res.status(200).json(response);
};

const getReservationPaymentProducts = async (req, res) => {
  const key = 'category_id';
  const categoryId = decodeURIComponent(req.params[key]);

  const result = await paymentProductsService.getReservationPaymentProducts(categoryId);


  res.status(200).json(result);
};

const w = (handler) => {
  return async (req, res) => {
    try {
      return await handler(req, res);
    } catch (e: any) {
      if (e.constructor.name === 'NotFound') {
        throw new BadRequest({ cause: e, code: e?.code });
      } else {
        throw new ServerError({ cause: e });
      }
    }
  };
};

export function routes(app: any) {
  app.get('/services', w(getServices));
  app.get('/services/:key', w(getService));
  app.get('/services/:key/product-categories', w(getProductCategories));
  app.get('/tax-setting', w(getTaxRateSettings));
  app.get('/reservation-payment-products/:category_id', w(getReservationPaymentProducts))
}
