import {apiHandlerFactory} from "../../common/admin-api/api-lambda.js";
import {Config} from "./config/config.js";
import * as staticConfig from "./config/static.js";
import {loadFromEnvironment} from "../../common/admin-api/config/config-loaders.js";
import {API} from "lambda-api";

import { routes as Services } from "../api/controller/admin/services.js";
import { routes as Reservations} from "../api/controller/admin/reservations.js";
import { routes as Results} from "../api/controller/admin/results.js";
import { routes as Taxes} from "../api/controller/admin/taxes.js";
import { routes as Products} from "../api/controller/admin/products.js";
import { routes as ProductCategories} from "../api/controller/admin/product-categories.js";

export const main = apiHandlerFactory(undefined, app => {
    const config = new Config(loadFromEnvironment());
    staticConfig.default.merge(config);

    app.register(registerApi, {prefix: '/v1'})
    app.register(registerApi);
});

function registerApi(app: API) {
    app.register(Services, {
        prefix: '/payment/configs'
    });
    app.register(Taxes, {
        prefix: '/payment/configs'
    });
    app.register(Products, {
        prefix: '/payment/configs'
    });
    app.register(ProductCategories, {
        prefix: '/payment/configs'
    });
    app.register(Results, { prefix: '/payment/results' });
    app.register(Reservations, { prefix: '/calendars' });
}