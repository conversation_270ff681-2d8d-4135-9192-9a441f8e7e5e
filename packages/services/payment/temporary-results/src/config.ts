/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import * as config from "@oss/services/common/utils/config";
import {AWS, getEnvironmentCredentials} from "../../../common/utils/aws-helper";

const dynamoDbDocument = new AWS.DynamoDB.DocumentClient(getEnvironmentCredentials());

const ENV_NAMES = {
  TABLE_SURVEY_RESULTS: 'TABLE_SURVEY_RESULTS',
  VITE_USE_PAYMENT: 'VITE_USE_PAYMENT',
} as const;

const appConfig = new config.AppConfig();

type AppConfig = config.AppConfig;

export {
  appConfig,
  dynamoDbDocument,
  ENV_NAMES,
  AppConfig,
}