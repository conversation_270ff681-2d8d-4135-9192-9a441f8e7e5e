{"name": "lsc-temporary-results-manager", "version": "0.0.1", "description": "", "main": "handler.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 0", "lint": "eslint .", "format": "prettier --write .", "fix-format-lint": "npm run format && npm run lint", "build": "webpack --mode production", "local-run": "ts-node src/localRunner.ts", "get-lambda-env": "ts-node src/createEnvFile.ts", "precommit": "lint-staged", "transpile-watch": "tsc --watch", "typecheck": "tsc --noEmit", "run-script": "ts-node"}, "author": "", "license": "Apache-2.0", "dependencies": {"@lsc/backend-shared-utils": "file:../../../shared/utils", "js-yaml": "^3.14.0", "lodash": "^4.17.21", "source-map-support": "^0.5.21"}, "devDependencies": {"@lsc/lambda-dev-utils": "file:../../../shared/lambda-dev-utils", "@types/lodash": "^4.14.172", "@types/node": "~12.12.22", "@typescript-eslint/eslint-plugin": "^4.31.0", "@typescript-eslint/parser": "^4.31.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-header": "^3.1.1", "eslint-plugin-import": "^2.22.0", "glob-parent": "^5.1.2", "prettier": "^2.3.1", "validator": "^13.7.0"}, "nodemonConfig": {"delay": 2000, "watch": ["src/*", "env.yaml"], "execMap": {"js": "node", "ts": "ts-node"}}}