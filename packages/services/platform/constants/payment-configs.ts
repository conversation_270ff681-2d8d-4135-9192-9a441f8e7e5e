/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

export const TAX_TYPES = {
  OUTER: 0,
  INNTER: 1,
} as const;

export const CALCULATION_TYPES = {
  FOR_EACH_ITEMS: 0,
  TOTAL: 1,
} as const;

export const ROUNDING_TYPES = {
  ROUND_DOWN: 0,
  ROUND_UP: 1,
  ROUND: 2,
} as const;