/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import config from '../common/admin-api/config/static';
import { requestContext } from '../common/admin-api/utils/execution-context';

const hasDebugOutput = () => {
  return [].includes(config.logOutputLevel);
};

const debug = (...values) => {
  if (hasDebugOutput()) {
    console.log(...values);
  }
};

const logLambdaApiResponse = (event, response) => {
  const statusCode = response.statusCode;
  const path = event.path;
  const method = event.httpMethod;
  const user = requestContext.userId;

  const line = `${statusCode} - ${method} ${path} (${user || 'anonymous'})`;

  if (statusCode === 200 && response.body) {
    const isRequestMarkedAsError = requestContext.isRequestFailed;
    const head = response.body.substr(0, 25);
    if (isRequestMarkedAsError || head.includes('ERROR')) {
      const json = JSON.parse(response.body);
      console.log(`${line} HAS_ERROR\n${JSON.stringify(json, null, 2)}`);
      return;
    }
  }
  console.log(line);
};

const logLambdaApiResponseBody = (response) => {
  const body = response.body;
  if (!body) {
    console.log('=== no response body ===');
    return;
  }
  try {
    const json = JSON.parse(body);
    console.log('Response body\n' + JSON.stringify(json, null, 2));
  } catch (e: any) {
    console.log('Response body\n' + body);
  }
};

export {
  debug,
  logLambdaApiResponse,
  logLambdaApiResponseBody,
};
