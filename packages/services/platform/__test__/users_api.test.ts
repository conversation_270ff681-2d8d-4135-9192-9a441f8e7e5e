/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { createGetEvent, handlerFactory, createPostEvent, createDeleteEvent } from './testResourcesFactory.js';
import defaults from '../../common/admin-api/config/defaults.js';
import c from '../../common/admin-api/config/constants.js';

const printBody = (body) => {
  try {
    console.log(JSON.stringify(JSON.parse(body), null, 2));
  } catch (e: any) {
    console.log(body);
  }
};

const handler = handlerFactory('lsc-dev').create(defaults, {
  [c.ADMIN_USER_POOL_ID]: 'ap-northeast-1_Nrfte9gsx',
  [c.DEPLOY_ENV]: 'lsc-dev-haoran',
  [c.ENVIRONMENT_TYPE]: 'test',
});

let response;

response = handler(createGetEvent('/v1/users', {}));

response
  .then((r) => {
  })
  .catch((e) => {
    console.log(e);
  });
