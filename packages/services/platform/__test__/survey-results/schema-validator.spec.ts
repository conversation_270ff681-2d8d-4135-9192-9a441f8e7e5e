/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import schema from './survey-schema-vaccine.json';
import validResult from './survey-result-vaccine-correct.json';
import invalidResult from './survey-result-vaccine-incorrect.json';
import { SurveyResultsValidator } from '../../../api-admin-survey/services/survey-results-validator.js';
import _ from 'lodash';

describe('schema', () => {
  describe('if has all required records', () => {
    it('should return true', () => {
      let validator = new SurveyResultsValidator(_.cloneDeep(schema), validResult);
      console.log(JSON.stringify(validator.getErrorMessage()));
      expect(validator.validate()).toBe(true);
    });
    it('should return false', () => {
      let validator = new SurveyResultsValidator(_.cloneDeep(schema), invalidResult);
      expect(validator.validate()).toBe(false);
    });
  });
});
