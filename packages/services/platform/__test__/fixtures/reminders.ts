import { ReminderConfigurationCreateRequest, SingleReminderRequest } from '../../types/reminders.js';
import {testHelpers} from 'common/utils/index.js';

const {
    nextGuid, nowISO, clone
} = testHelpers.helpers();

const templateReservationReminderCreate: ReminderConfigurationCreateRequest = {
    id: '5DF243BD-26DE-404F-8C2C-5742DFFB8A24',
    name: 'template',
    fromDate: '2022-05-20',
    toDate: '2099-12-31',
    dateItemKey: '12345',
    conditions: [
        {itemKey: '123', conditionValues: ['1', '2']}
    ],
    surveyId: '10C66270-CF64-4AF8-A2DE-CDD021B17036',
    reminderType: 'date_relative',
    settings: [],
}

const templateSingleReminderCreate: SingleReminderRequest = {
    messages: [
        { type: 'text', text: 'message 1 text'},
        { type: 'text', text: 'message 2 text'},
    ],
    reminderLocalId: '',
    sendTime: '12:00',
    daysAfter: 0,
    yearsAfter: 0,
    monthsAfter: 0
}

export const templates = {
        create: {
            reservationReminder: templateReservationReminderCreate,
            singleReminder: templateSingleReminderCreate,
        }    
}
