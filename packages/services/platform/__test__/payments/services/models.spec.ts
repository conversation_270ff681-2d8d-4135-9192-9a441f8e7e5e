import { validators, ServiceApiType, CreateServiceRequest, CALC_TYPE_DETAILS, RESERVATION_SERVICE_TRUE, ROUNDING_TYPE_ROUND, TAX_TYPE_OUTER, RESERVATION_COST_TYPE_FIXED } from '../../../payments/services/models.js';

describe('services validators', () => {
    describe('createServiceRequestValidator', () => {
        const sample: CreateServiceRequest = {
            calculationType: CALC_TYPE_DETAILS,
            linkedCalendarIds: [],
            linkedSurveyIds: [],
            purchaseLimit: 1,
            receiptCreatorName: 'receiptCreatorName',
            receiptDisplayAddress: 'address',
            reservationServiceType: RESERVATION_SERVICE_TRUE,
            reservationCostType: RESERVATION_COST_TYPE_FIXED,
            roundingType: ROUNDING_TYPE_ROUND,
            taxType: TAX_TYPE_OUTER,
            serviceName: 'service_name',
            serviceId: 'service_id',
            products: []
        }
        type Case = {
            title: string,
            sample: { [key in keyof CreateServiceRequest]: any },
            isValid?: boolean,
        }
        const cases: Case[] = [
            {
                title: 'valid',
                sample,
                isValid: true,
            },
            {
                title: 'valid with linkedCalendarIds or linkedSurveyIds abscent',
                sample: { ...sample, linkedCalendarIds: undefined, linkedSurveyIds: undefined },
                isValid: true,
            },
            {
                title: 'calculationType has wrong value',
                sample: { ...sample, calculationType: 4 }
            },
            {
                title: 'serviceId is empty',
                sample: { ...sample, serviceId: undefined }
            },
            {
                title: 'serviceName is empty',
                sample: { ...sample, serviceName: '' }
            },
            {
                title: 'taxType has wrong value',
                sample: { ...sample, taxType: -1 },
            }
        ]
        test.each(cases.map(c => [c.title, c.sample, c.isValid || false]))
            ('%s', (_, sample, isValid) => {
                expect(validators.createServiceRequest(sample)).toEqual(isValid)
            })
    })
})