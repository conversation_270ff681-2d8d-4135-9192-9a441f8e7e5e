/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { dbTest } from "common/utils/index.js";
import c from '../../../../common/admin-api/config/constants.js';
import config from '../../../../common/admin-api/config/static.js';
import { ServiceRecord } from "../../../payments/services/models.js";
import { find } from '../../../payments/services/repository.js';

const tableName = 'test-services'
config.merge({[c.TABLE_PAYMENT_CONFIGS]: tableName})

dbTest.useTestDatabase(async c => {
    await c.createTable(dbTest.schema.createExampleSchema(tableName));
});
const table = dbTest.useTestTable(tableName);

describe('#find', () => {
    beforeAll(done => {
        table.collectionFromYaml<ServiceRecord>(__dirname, 'resources', 'services.yaml')
        .then(table.batchInsert)
        .then(() => done()).catch(done.fail);
    })
    it('should fetch all', async () => {
        const items = await find({}, null).then(v => v.items)
        expect(items).toHaveLength(3);
    });
    it('should fetch reservation services only', async () => {
        const items = await find({reservationServiceType: 1}, null).then(v => v.items)
        expect(items).toHaveLength(2);
        expect(items).toSatisfyAll(i => i.reservationServiceType === 1)
    });
    it('should fetch not reservation services only', async () => {
        const items = await find({reservationServiceType: 0}, null).then(v => v.items)
        expect(items).toHaveLength(1);
        expect(items).toSatisfyAll(i => i.reservationServiceType === 0)
    });
})
