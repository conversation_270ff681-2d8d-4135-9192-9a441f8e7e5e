/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import _ from 'lodash';
import {ApiHelper,buildApiInstance} from '../test-helpers/contollers-test-helpers.js';
import controller from '../../../payments/products/controller.js';
import * as service from '../../../payments/products/service.js';
import { mocked } from 'ts-jest/utils';
import { RequestChangeOrder } from '../../../payments/products/models.js';
import { loadJsonFromTest } from '../test-helpers/common-test-helpers.js';
jest.mock('../../../payments/products/service');
const serviceMock = mocked(service);


const samples = loadJsonFromTest('requests.json', __dirname);

let api: ApiHelper
beforeEach(() => {
    jest.resetAllMocks()
    api = buildApiInstance(controller);
});

describe('GET /services/:key/products', () => {
    beforeEach(async () => {
        serviceMock.find.mockReset();
        serviceMock.find.mockResolvedValue({
            items: [
                {} as never
            ],
            lastEvaluatedKey: undefined
        })
        await api.get('/services/001/products', {status: "1", lastEvaluatedKey: { pk: 'value'}})
    })
    it('should respond successfull', () => {
        expect(api.last.isOk).toBeTruthy()
    })
    it('should return products', () => {
        expect(api.last.data).toHaveLength(1)
    });
    it('should pass params to mock service', () => {
        expect(serviceMock.find).toHaveBeenCalledWith('001', { status: 1}, { pk: 'value'})
    });
})

describe('POST /services/:key/products', () => {
    const sample = samples.createProduct;
    beforeEach(async () => {
        serviceMock.createFromDto.mockReset();
        serviceMock.createFromDto.mockResolvedValue(sample)
        await api.post('/services/001/products', sample);
    });
    
    it('should pass validation', () => {
        expect(api.last.isOk).toBeTruthy();
    })
    it('should call service', () => {
        expect(serviceMock.createFromDto).toBeCalledWith('001', expect.objectContaining(sample))
    })
    it('should return inserted value', () => {
        expect(api.last.data).toEqual(expect.objectContaining(sample));
    })
})

describe('PUT /services/:key/products', () => {
    const sample = samples.updateProduct;
    beforeEach(async () => {
        serviceMock.updateFromDto.mockReset();
        serviceMock.updateFromDto.mockResolvedValue(sample)
        await api.put('/services/001/products', sample);
    });
    
    it('should pass validation', () => {
        expect(api.last.isOk).toBeTruthy();
    })
    it('should call service', () => {
        expect(serviceMock.updateFromDto).toBeCalledWith('001', expect.objectContaining(sample))
    })
    it('should return inserted value', () => {
        expect(api.last.data).toEqual(expect.objectContaining(sample));
    })
})

describe('GET /services/:key/products/presigned-url', () => {
    beforeEach(async () => {
        serviceMock.createImportLink.mockResolvedValue('https://s3_bucket_link')
        await api.get('/services/001/products/presigned-url', {
            bucketKey: 'bucket',
            contentType: 'text/csv'
        });
    });
    
    it('should pass validation', () => {
        expect(api.last.isOk).toBeTruthy();
    })
    it('should call service', () => {
        expect(serviceMock.createImportLink).toBeCalledWith({bucketKey: 'bucket', contentType: 'text/csv'});
    })
    it('should return inserted value', () => {
        expect(api.last.body).toEqual(expect.objectContaining({
            url: 'https://s3_bucket_link'
        }));
    })
})

describe('POST /services/:key/products/validate-csv', () => {
    beforeEach(async () => {
        serviceMock.validateImportCsv.mockResolvedValue({
            resultKey: '/somefile.json'
        })
        await api.post('/services/001/products/validate-csv', {
            bucketKey: '/file.csv',
            isSJISEncoding: true,
        });
    });
    
    it('should pass validation', () => {
        expect(api.last.isOk).toBeTruthy();
    })
    it('should call service', () => {
        expect(serviceMock.validateImportCsv).toBeCalledWith('001', '/file.csv', true);
    })
    it('should return inserted value', () => {
        expect(api.last.body).toEqual(expect.objectContaining({
            bucketKey: '/somefile.json'
        }));
    })
})

describe('POST /services/:key/products/save-csv', () => {
    beforeEach(async () => {
        serviceMock.importFromS3Json.mockResolvedValue();
        await api.post('/services/001/products/save-csv', {
            bucketKey: '/file.json',
            fileName: 'somefile.csv',
        });
    });
    
    it('should pass validation', () => {
        expect(api.last.isOk).toBeTruthy();
    })
    it('should call service', () => {
        expect(serviceMock.importFromS3Json).toBeCalledWith('001', '/file.json');
    })
})

describe('POST /services/:key/products/order', () => {
    beforeEach(async () => {
        serviceMock.updateOrder.mockResolvedValue([{productId: '001'} as any]);
        await api.put('/services/001/products/order', {
            ids: [{order: 1, productId: '001'}]
        } as RequestChangeOrder);
    });
    
    it('should pass validation', () => {
        expect(api.last.isOk).toBeTruthy();
    })
    it('should call service', () => {
        expect(serviceMock.updateOrder).toBeCalledWith('001', [{order: 1, productId: '001'}]);
    })
    it('should return array updated elements', () => {
        expect(api.last.data).toEqual([{productId: '001'}])
    })
})