/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { columns, parseCsvAndValidateFormat, validateExisted, exportToCsv } from '../../../payments/products/csv-import-export.js';
import { ProductFullProps, RecordProduct } from '../../../payments/products/models.js';
import { loadTextFromTest, loadYamlFromTest } from '../test-helpers/common-test-helpers.js';

const serviceId = '001-xxxxxx';

function loadAndValidate(filename: string) {
    const csvString = loadTextFromTest(`import/${filename}`, __dirname);
    return parseCsvAndValidateFormat(serviceId, csvString);
}
function loadProductsFromYaml(filename: string) {
    return loadYamlFromTest(`export/${filename}`, __dirname);
}

describe("parse import csv", () => {
    describe("valid.csv", () => {
        const { data, report, records } = loadAndValidate("valid.csv");

        it('should be correct record 1', () => {
            const record = records[0];
            expect(record).toEqual(expect.objectContaining({
                order: 0,
                price: 100,
                productCategories: ["category#001", "category#002"],
                productId: '001',
                productName: '商品1',
                status: 0,
                taxableType: 0,
                serviceId: '001'
            } as ProductFullProps))
        })

        it('should be correct record 1', () => {
            const record = records[1];
            expect(record).toEqual(expect.objectContaining({
                order: 1,
                price: 200,
                productCategories: [],
                productId: '002',
                productName: '商品2',
                status: 1,
                taxableType: 1
            } as ProductFullProps))
        })
    })
})

describe("validate import csv", () => {
    describe('valid csv', () => {

        const { data, report } = loadAndValidate("valid.csv");

        it('should be no errors', () => {
            expect(report.csvErrors).toHaveLength(0);
            expect(report.recordErrors).toHaveLength(0);
            expect(report.isOk).toBeTruthy();
        });
        it('should receive 2 records', () => {
            expect(data).toHaveLength(2)
        });
        it('should contain all colums', () => {
            const singleRecord = data[0];
            const recordColumns = Object.keys(singleRecord).sort();
            expect(recordColumns).toEqual(columns.sort())
        });
    });

    describe('invalid csv', () => {
        const { report } = loadAndValidate('invalid.csv');

        it('should return parsing error', () => {
            expect(report.isOk).toBeFalsy();
            expect(report.csvErrors).toBeTruthy();
            expect(report.errorMessage).toEqual('不正なCSVフォーマット。')
        })
    })

    describe('no required column error', () => {
        const { report } = loadAndValidate('no_column.csv');
        it('should return validation error', () => {
            expect(report.isOk).toBeFalsy();
            expect(report.recordErrors).toBeTruthy();
        })
    })

    describe('value format problem', () => {
        const { report } = loadAndValidate('incorrect_value_format.csv');
        it('should be invalid', () => {
            expect(report.isOk).toBeFalsy();
        })
        it('should record 2 errors', () => {
            expect(report.recordErrors).toHaveLength(2)
        })
    })

})

describe('validateExisted', () => {
    describe('valid case', () => {
        const step1 = loadAndValidate("valid.csv");
        const { records, report } = validateExisted(
            step1.records,
            step1.report,
            [{ id: 'category#001', name: '' }, { id: 'category#002', name: '' }],
            [])
        it('should be valid', () => {
            expect(report.isOk).toBeTruthy();
        });
    })
    describe('category not exists', () => {
        const step1 = loadAndValidate("valid.csv");
        const { records, report } = validateExisted(
            step1.records,
            step1.report,
            [{ id: 'category#001', name: '' }],
            [])
        it('should not be valid', () => {
            expect(report.isOk).toBeFalsy();
        });
    })
    describe('duplicate name', () => {
        const step1 = loadAndValidate("valid.csv");
        const { records, report } = validateExisted(
            step1.records,
            step1.report,
            [{ id: 'category#001', name: '' }, { id: 'category#002', name: '' }],
            [{ productId: '003', productName: '商品1' }])
        it('should not be valid', () => {
            expect(report.isOk).toBeFalsy();
        });
    })
})

describe('export products', () => {
    const yamlRecords = loadProductsFromYaml('valid.yaml')
    const csvString = exportToCsv('001', yamlRecords as RecordProduct[], false);
    const reImportedExport = parseCsvAndValidateFormat(serviceId, csvString);
    const importedValidCsv = loadAndValidate('valid.csv');
    it('should be valid', () => {
        expect(reImportedExport.report.isOk).toBeTruthy();
    });
    it('record 0 should be equal to parsed', () => {
        expect(reImportedExport.records[0]).toEqual(
            expect.objectContaining(importedValidCsv.records[0]))
    })
    it('record 1 should be equal to parsed', () => {
        expect(reImportedExport.records[1]).toEqual(
            expect.objectContaining(importedValidCsv.records[1]))
    })
})