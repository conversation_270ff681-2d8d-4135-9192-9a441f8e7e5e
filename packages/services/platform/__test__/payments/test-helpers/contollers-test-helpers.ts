/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import ApiBuilder, { API } from 'lambda-api';
import { ApiResponse } from '../../../payments/shared/controller-helpers.js';
import paths from 'path'
import { User } from '../../../../common/admin-api/utils/auth.js';

type LambdaResponse = {
    headers: { [key: string]: string },
    body: string,
    statusCode: number,
}

export class TestApiResponse<T = any> {
    constructor(private res: LambdaResponse) { }

    get body() {
        return JSON.parse(this.res.body) as ApiResponse<T>
    }
    get isOk() {
        return this.res.statusCode === 200 && this.body.code === 'success'
    }
    get data() {
        return this.body.data
    }
    get isInvalidParams() {
        return this.body.code === "invalid_params"
    }
}

class ConfigurableApiApp<T> {
    private lastResponse: T
    private api: API
    private rootPath = '/'
    private user: User
    constructor(private responseParser: (lambdaApiResponse: LambdaResponse) => T) {
        this.api = ApiBuilder();
        this.api.use((req, res, next) => {
            req.user = this.user;
            next();
        })
    }

    addController(registrar: (api: API) => any, prefix = '/') {
        this.api.register(registrar, { prefix })
    }

    authorizeAll(username: string, roles?: string[]) {
        this.user = new User(username, roles);
     }
    authorizeAllAdministrator(username?: string) { 
        this.user = new User(username, ['Administrator:admins']);
    }

    setRequestsRootPath(rootPath = '/') {
        this.rootPath = rootPath;
    }

    async get(path: string, query = {}) {
        const res = await this.api.run({
            path: paths.join(this.rootPath, path),
            queryStringParameters: query,
            httpMethod: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        } as never, {} as never);
        this.lastResponse = this.responseParser(res);
    }
    async post(path: string, body: any) {
        const res = await this.api.run({
            path: paths.join(this.rootPath, path),
            httpMethod: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(body),
        } as never, {} as never);
        this.lastResponse = this.responseParser(res);
    }
    async put(path: string, body: any) {
        const res = await this.api.run({
            path: paths.join(this.rootPath, path),
            httpMethod: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(body),
        } as never, {} as never);
        this.lastResponse = this.responseParser(res);
    }
    async del(path: string) {
        const res = await this.api.run({
            path: paths.join(this.rootPath, path),
            httpMethod: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        } as never, {} as never);
        this.lastResponse = this.responseParser(res);
    }

    get last() {
        return this.lastResponse;
    }
}

export function buildApiInstance(controller: (api: API) => any) {
    const api = new ConfigurableApiApp<TestApiResponse>(r => new TestApiResponse(r));
    api.addController(controller);
    api.authorizeAllAdministrator();
    return api;
}

export type ApiHelper = ConfigurableApiApp<TestApiResponse>