import ApiBuilder, { API } from 'lambda-api';
import registrar from '../../../payments/taxes/controller.js';
import { mocked } from 'ts-jest/utils';
import * as service from '../../../payments/taxes/service.js';
import { TaxSettingsRequest } from '../../../payments/taxes/models.js';
import { User } from '../../../../common/admin-api/utils/auth.js';
import { VALIDATION_ERROR_DEFAULT_MESSAGE } from '../../../payments/shared/controller-helpers.js';

jest.mock('../../../payments/taxes/service');
const serviceMock = mocked(service);


let api: API;
let user: User;
beforeEach(() => {
    api = ApiBuilder();
    api.use((req, res, next) => {
        req.user = user;
        next();
    })
    api.register(registrar, { prefix: '/payment-configs' });
    user = undefined;
});

describe('GET /payment-configs/task-setting', () => {
    it('should return valid object', async () => {
        serviceMock.getTaxSettings.mockResolvedValue([
            {
                value: 10,
                applyDate: '',
            },
        ]);
        const response = await api.run(
            {
                path: '/payment-configs/tax-setting',
                httpMethod: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            } as never,
            {} as never
        );
        expect(JSON.parse(response.body)).toEqual(
            expect.objectContaining({
                code: 'success',
                data: {
                    rateSettings: [
                        {
                            value: 10,
                            applyDate: '',
                        },
                    ],
                } as TaxSettingsRequest,
            })
        );
    });
    it('should return not found', async () => {
        serviceMock.getTaxSettings.mockResolvedValue(null);
        const response = await api.run(
            {
                path: '/payment-configs/tax-setting',
                httpMethod: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            } as never,
            {} as never
        );
        expect(JSON.parse(response.body)).toEqual(
            expect.objectContaining({
                code: 'not_found',
                message: expect.any(String),
            })
        );
    });
});
describe('POST /payment-configs/task-setting', () => {
    it('should call the service', async () => {
        const call = serviceMock.upsertTaxSettings.mockResolvedValue([
            { applyDate: '2019-10-01', value: 10 }
        ]);
        const response = await api.run({
            path: '/payment-configs/tax-setting',
            httpMethod: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                rateSettings: [
                    { applyDate: '2019-10-01', value: 10 }
                ]
            } as TaxSettingsRequest)
        } as never, {} as never)
        expect(JSON.parse(response.body)).toEqual(expect.objectContaining({
            code: 'success'
        }));
        expect(call).toHaveBeenCalledWith([{ applyDate: '2019-10-01', value: 10 }])
    });

    it('should return validation failed error', async () => {
        const response = await api.run({
            path: '/payment-configs/tax-setting',
            httpMethod: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                rateSettings: [
                    { applyDate: 'xxxxxx', value: 0 }
                ]
            } as TaxSettingsRequest)
        } as never, {} as never)
        expect(JSON.parse(response.body)).toEqual(expect.objectContaining({
            error: VALIDATION_ERROR_DEFAULT_MESSAGE
        }));
    })
    it('should allow admin only', async () => {
        user = new User('someuser', ['xxx:members']);
        const response = await api.run({
            path: '/payment-configs/tax-setting',
            httpMethod: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                rateSettings: [
                    { applyDate: 'xxxxxx', value: 0 }
                ]
            } as TaxSettingsRequest)
        } as never, {} as never)
        expect(response.statusCode).toEqual(500);
    })
});
