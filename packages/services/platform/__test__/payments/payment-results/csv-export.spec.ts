/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { loadJsonFromTest, loadTextFromTest } from '../test-helpers/common-test-helpers.js';
import { exportToCsvString } from '../../../payments/payment-results/csv-export.js';

describe('result CSV string', () => {
    const exampleCsv = loadTextFromTest('export_valid_example.csv', __dirname);
    const sourceJson = loadJsonFromTest('export_source.json', __dirname);
    const exportCsv = exportToCsvString([sourceJson]);
    it('should be equal to the example', () => {
        const normalizedExample  = exampleCsv.replace(/(\r\n|\n|\r)/gm, "").trim();
        const normalizedExport = exportCsv.replace(/(\r\n|\n|\r)/gm, "").trim();
        expect(normalizedExport).toEqual(normalizedExample);
    })
});
