/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { mocked } from 'ts-jest/utils';
import * as products from '../../../payments/products/repository.js';
import * as repo from '../../../payments/product-categories/repository.js';
import * as services from '../../../payments/services/repository.js';
import { deleteAll } from '../../../payments/product-categories/service.js';

jest.mock('../../../payments/product-categories/repository');
jest.mock('../../../payments/products/repository');
jest.mock('../../../payments/services/repository');

const mockedRepo = mocked(repo);
const mockedProducts = mocked(products);
const mockedServices = mocked(services);

describe('deleteAll', () => {
    beforeEach(() => {
        jest.resetAllMocks();
        mockedProducts.list.mockResolvedValue({
            items: [
                { productId: '001' , productCategories: []} as never,
                { productId: '002' , productCategories: ['cat1', 'cat2']} as never,
            ],
            lastEvaluatedKey: undefined
        });
        mockedRepo.del.mockResolvedValue(true);
        mockedServices.getBySortKey.mockResolvedValue({} as any);
    })
    describe('all deleting categories are not in use', () => {
        it('should delete cat3, cat4', async () => {
            const res = await deleteAll('001', ['cat3', 'cat4']);
            expect(mockedRepo.del).toHaveBeenCalledTimes(2);
            expect(res).toContainEqual(expect.objectContaining({
                id: 'cat3',
                code: 'success',
            }))
            expect(res).toContainEqual(expect.objectContaining({
                id: 'cat4',
                code: 'success',
            }))
        })
    })
    describe('some deleting categories are in use', () => {
        it('should delete cat1, cat4', async () => {
            const res = await deleteAll('001', ['cat1', 'cat4']);
            expect(mockedRepo.del).toHaveBeenCalledTimes(1);
            expect(res).toContainEqual(expect.objectContaining({
                id: 'cat1',
                code: 'error',
                details: expect.objectContaining({
                    productIds: ['002']
                })
            }))
            expect(res).toContainEqual(expect.objectContaining({
                id: 'cat4',
                code: 'success',
            }))
        })
    })
})