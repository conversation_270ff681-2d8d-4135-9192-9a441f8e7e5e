/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { dbTest } from 'common/utils/index.js'
import { getForProduct, list } from '../../../payments/reservations/repository.js'
import { staticConfig } from '../../../../common/admin-api/config/static.js'
import c from '../../../../common/admin-api/config/constants.js'
import { RecordReservationPaymentItem } from '../../../payments/reservations/models.js';

const calendarsTable = 'calendarsTable';
staticConfig.merge({
    [c.TABLE_SURVEY_CALENDARS]: calendarsTable
})
const database = dbTest.useTestDatabase(async (c) => {
    await c.createTable(dbTest.schema.createTableSchema({
        name: calendarsTable,
        partitionKey: { name: 'partitionKey' },
        sortKey: { name: 'sortKey' },
        indexes: [
            {
                name: 'serviceProductId-sortKey-index',
                partitionKey: { name: 'serviceProductId' },
                sortKey: { name: 'sortKey' },
            }
        ]
    }));
});
beforeEach(async () => await database.recreateDatabase())

const table = dbTest.useTestTable(calendarsTable);

describe('#list', () => {
    describe('if no reservationPaymentItems', () => {
        it('should fetch empty list', async () => {
            await expect(list('category#001', true).then(r => r.Items))
                .resolves.toHaveLength(0);
        });
    });
    describe('if there are reservation payment items', () => {
        beforeEach(async () => {
            await table.tableClient.putItem({
                partitionKey: 'reservationPaymentItems',
                sortKey: 'category#001',
                isDeleted: false,
            } as RecordReservationPaymentItem)
            await table.tableClient.putItem({
                partitionKey: 'reservationPaymentItems',
                sortKey: 'category#001_1',
                isDeleted: true,
            } as RecordReservationPaymentItem)
        })
        it('should fetch one', async () => {
            await expect(list('category#001', false).then(r => r.Items))
                .resolves.toHaveLength(1)
        })
        it('should fetch with deleted', async () => {
            await expect(list('category#001', true).then(r => r.Items))
                .resolves.toHaveLength(2)
        })
    })
})

describe('#getLinkedCategoryIdsByService', () => {
    beforeEach(async () => {
        await table.tableClient.putItem({
            partitionKey: 'reservationPaymentItems',
            sortKey: 'category#001_1',
            serviceProductId: '001#fsdofjoj_002',
            isDeleted: false,
        } as RecordReservationPaymentItem)
        await table.tableClient.putItem({
            partitionKey: 'reservationPaymentItems',
            sortKey: 'category#001_3',
            serviceProductId: '001#fsdofjoj_002',
            isDeleted: true,
        } as RecordReservationPaymentItem)
    })
    describe('without deleted', () => {
        it('should fetch one', async () => {
            await expect(getForProduct('001#fsdofjoj', '002')).resolves
                .toMatchObject([{
                    sortKey: 'category#001_1',
                }])
        })
    })

    describe('with deleted', () => {
        it('should fetch two', async () => {
            await expect(getForProduct('001#fsdofjoj', '002', true)).resolves
                .toMatchObject([{
                    sortKey: 'category#001_1',
                },
                {
                    sortKey: 'category#001_3',
                }])
        })
    })

})
