/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import {
  createGetEvent,
  createPutEvent,
  handlerFactory,
  createPostEvent,
  createDeleteEvent,
} from './testResourcesFactory.js';
import defaults from '../../common/admin-api/config/defaults.js';
import c from '../../common/admin-api/config/constants.js';

const printBody = (body) => {
  try {
    console.log(JSON.stringify(JSON.parse(body), null, 2));
  } catch (e: any) {
    console.log(body);
  }
};

const handler = handlerFactory('lsc-dev').create(defaults, {
  [c.DEPLOY_ENV]: 'lsc-dev-makoto',
  [c.ENVIRONMENT_TYPE]: 'development',
});

let response;

response = handler(
  createPostEvent('/v1/survey/', {
    surveyTitle: 'メールマガジン配信設定',
    description: 'テスト',
    surveyStatus: 'enable',
    surveySchema: [
      {
        itemKey: '3f9be10b',
        title: '情報区分',
        description: '情報区分を選択してください',
        surveyStatus: 'enable',
        type: 'checkboxes',
        options: [
          '防犯・防災その他緊急お知らせ情報',
          'まちづくり情報（自治会・市民活動等）',
          'ふれあい文化センター情報',
          'イベント情報（スポーツ・その他の行事等）',
          '生活・環境情報（生活情報・ごみ・リサイクル等）',
          '福祉情報（高齢・介護・障がい・一人親など）',
          '健康情報（健康増進・各種予防情報等）',
          '生涯学習情報（図書館情報・講座・講習会等',
          '子育て情報（乳幼児・未就学児・小中学生等）',
          '都市情報（道路などの工事・コミュニティバス等）',
          '上記以外の行政情報（議会・選挙・その他）',
        ],
        isRequired: {
          value: true,
        },
        selection: {
          min: null,
          max: null,
        },
      },
    ],
  })
);

response
  .then((r) => {
    console.log(JSON.stringify({ ...r, body: undefined }, null, 2));
    console.log('======== JSON body =========== ');
    printBody(r.body);
  })
  .catch((e) => {
    console.log(e);
  });
