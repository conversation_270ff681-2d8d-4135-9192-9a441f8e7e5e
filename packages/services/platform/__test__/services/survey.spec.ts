/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import * as surveyConfigService from '../../../api-admin-survey/services/survey.js';
import { FieldType, SelectPayTypeFieldType, PAY_TYPES } from '../../../api-admin-survey/types/survey-config.js';

type Mock<T, U> = {
  title: string,
  result: T,
  params: U
}

const eachTest = <T, U>(mocks: Mock<T, U>[], callback: (params: any) => any) => {
  mocks.forEach(({ title, result, params }) => {
    it(title, () => {
      const callbackResult = callback(params);
      expect(callbackResult).toBe(result);
    });
  });
}

describe('#validateSelectPayTypeQuestion', () => {
  type MockParams = {
    surveySchema: Array<Partial<FieldType> | Partial<SelectPayTypeFieldType>>,
    usePayment: boolean,
  };
  const baseItem = {
    title: 'test',
    type: 'selectPayType',
    description: 'test',
    payTypeOptions: [
      { payType: PAY_TYPES.CASHLESS, input: 'キャッシュレス' },
      { payType: PAY_TYPES.CASH, input: '現金' },
    ]
  };
  const baseParams: MockParams = {
    surveySchema: [baseItem, { type: 'reservation' }],
    usePayment: true,
  }
  const mocks: Mock<boolean, MockParams>[] = [
    // NOTE: 正しい設定
    {
      title: 'should be true.',
      result: true,
      params: baseParams
    },
    // NOTE: 「決済機能を利用する」がOFFの為エラ－になる
    {
      title: 'should be false. Because "決済機能を利用する" is OFF.',
      result: false,
      params: {
        ...baseParams,
        usePayment: false,
      }
    },
    // NOTE: 「商品選択（カレンダー予約なし）」「分類」どちらも未設定の為エラ－になる
    {
      title: 'should be false. Because neither "selectProductsItem" and "reservationItem" are set yet.',
      result: false,
      params: {
        ...baseParams,
        surveySchema: [baseItem]
      }
    },
    // NOTE: 「支払方法選択」が2つ設定されている為エラ－になる
    {
      title: 'should be false. Because two "selectPayTypeItems" are set.',
      result: false,
      params: {
        ...baseParams,
        surveySchema: [baseItem, baseItem, { type: 'reservation' }],
      }
    },
    // NOTE: 「支払方法選択」の選択肢が未入力の為エラーになる
    {
      title: 'should be false. Because the option is not entered.',
      result: false,
      params: {
        ...baseParams,
        surveySchema: [
          {
            ...baseItem,
            payTypeOptions: [
              { payType: PAY_TYPES.CASHLESS, input: 'キャッシュレス' },
              { payType: PAY_TYPES.CASH, input: '' },
            ]
          },
          { type: 'reservation' }
        ],
      }
    },
  ];

  eachTest(mocks, (params: MockParams) => surveyConfigService.validateSelectPayTypeQuestion(params.surveySchema as any, params.usePayment));
});