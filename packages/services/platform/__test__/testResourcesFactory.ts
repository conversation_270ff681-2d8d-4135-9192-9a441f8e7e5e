/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import createApp from '../createApp.js';
import { compose } from '../../common/admin-api/config/config.js';
import * as awsHelpers from '../../common/admin-api/utils/aws-helper.js';
import ApiBuilder from 'lambda-api';

class HandlerFactory {
  private profileName: any;
  private configs: any;
  constructor(profileName, ...configs) {
    this.profileName = profileName;
    this.configs = compose(...configs);
  }

  create(...configs) {
    return async (event, context : any= {}) => {
      awsHelpers.initializeAwsLocal(this.profileName);
      const app = await createApp(compose(this.configs, ...configs));
      return await app.run(event, context);
    };
  }
}

class MockedHandlerFactory {
  private configs: any;
  constructor(...configs) {
    this.configs = compose(...configs);
  }

  create(path, controller) {
    const staticConfig = require('../../common/admin-api/config/static.js');
    staticConfig.merge(this.configs);
    return async (event, context:any = {}) => {
      const api = ApiBuilder();
      api.register(controller, { prefix: path });
      return await api.run(event, context);
    };
  }
}

const handlerFactory = (awsProfile, ...configs) => {
  return new HandlerFactory(awsProfile, ...configs);
};

const createGetEvent = (path, query = {}) => {
  return {
    path: path,
    httpMethod: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  };
};

const createPutEvent = (path, body = {}) => {
  return {
    path: path,
    httpMethod: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body,
  };
};

const createPostEvent = (path, body = {}) => {
  return {
    path: path,
    httpMethod: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body,
  };
};
const createDeleteEvent = (path) => {
  return {
    path: path,
    httpMethod: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  };
};

export {
  handlerFactory,
  createGetEvent,
  createPutEvent,
  createPostEvent,
  createDeleteEvent,
  MockedHandlerFactory,
};
