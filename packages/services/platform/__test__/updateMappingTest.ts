/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { createPutEvent, handlerFactory } from './testResourcesFactory.js';
import defaults from '../../common/admin-api/config/defaults.js';
import c from '../../common/admin-api/config/constants.js';

const handler = handlerFactory('lsc-dev').create(defaults, {
  [c.TABLE_CHATBOT_SCENARIO]: 'lsc-dev-chatbot-scenario',
});

const response = handler(
  createPutEvent('/v1/scenarios/mapping', {
    scenarioId: 'Awesome Scenario',
    mapping: {
      production: 'ver1234',
      sandbox: 'ver5678!!!',
    },
  }),
  {}
);

response
  .then((r) => {
    console.log(JSON.stringify(r, null, 2));
  })
  .catch((e) => {
    console.log(e);
  });
