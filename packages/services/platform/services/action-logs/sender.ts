/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { getEnvironmentCredentials } from '../../../common/admin-api/utils/aws-helper.js';
import { infoLog } from '../../../common/admin-api/utils/logger.js';

import { DateTime } from 'luxon';


import AWS from 'aws-sdk'
import ctx from '../../../common/admin-api/config/static.js'
import c from '../../../common/admin-api/config/constants.js'
import { requestContext } from '../../../common/admin-api/utils/execution-context.js';

const prepareRecord = (name, endpoint, params, ...rest) => {
  const {parameterMain, type, parameter1, parameter2, parameter3, parameter4, parameter5} = params;
  let newParameter4 = parameter4;
  if (rest.length > 0) {
    newParameter4 = [parameter4, ...rest].join('\n\n');
  }
  return {
    time: Math.floor(DateTime.now().toSeconds()),
    actionEndpoint: endpoint,
    actorName: params.user || requestContext.userId || 'system',
    action: name,
    type,
    parameterMain,
    parameter1,
    parameter2,
    parameter3,
    parameter4: newParameter4,
    parameter5
  };
}

const serializeRecord = (record) => {
  return JSON.stringify(record);
}

const logAction = async (name, path, params, ...rest) => {
  const client = new AWS.Firehose(getEnvironmentCredentials());
  const message = prepareRecord(name,path,params,...rest);

  const deliveryStreamName = ctx.get(c.ACTION_LOGS_DELIVERY_STREAM);
  if (!deliveryStreamName || ctx.isDevelopmentEnv) {
    infoLog('[ActionLogs]', message);
  }
  if (ctx.isTestEnv) {
    return;
  }
  if (deliveryStreamName) {
    await client.putRecord(
      {DeliveryStreamName: deliveryStreamName, Record: {Data: serializeRecord(message)}}
    ).promise();
  }
}

const logActionSafe = async (name, path, {
  user,
  parameterMain,
  type,
  parameter1,
  parameter2,
  parameter3,
  parameter4,
  parameter5
} : {
  user?,
  parameterMain?,
  type?,
  parameter1?,
  parameter2?,
  parameter3?,
  parameter4?,
  parameter5?
}, ...rest) => {
  try {
    await logAction(name, path, {type, user, parameterMain, parameter1, parameter2, parameter3, parameter4, parameter5}, ...rest);
  } catch (e: any) {
    infoLog('[ActionLogs] action recording failed', e);
  }
}


export default logActionSafe
