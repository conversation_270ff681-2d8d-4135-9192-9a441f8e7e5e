/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import config from '../../common/admin-api/config/static.js';
import _ from 'lodash';
import { SystemConfigurationError } from '../../common/admin-api/utils/exceptions.js';
import * as awsClients from '../../common/utils/aws-clients.js';
import aws from 'aws-sdk';

const functionListCache = {
    value: null,
};
let functionMappingCache: Record<string, string> | undefined = undefined;

let damageReportStackLambdaCache: StackLambdas | undefined = undefined;

const getClient = () => {
    return awsClients.createLambdaClient();
};

class LambdaNotFoundException extends SystemConfigurationError {
    private code: string;

    constructor(lambda) {
        super(`Lambda ${lambda} was not found`);
        this.code = 'LambdaNotFound';
    }
}

const loadList = async () => {
    const list: any[] = [];
    let nextMarker = undefined;
    do {
        const response = await getClient()
            .listFunctions({
                FunctionVersion: 'ALL',
                MaxItems: 60,
                Marker: nextMarker,
            })
            .promise();
        list.push(
            ...response.Functions.map((f) => {
                return f.FunctionName;
            })
        );
        nextMarker = response.NextMarker;
    } while (nextMarker);
    return list;
};

const getFunctionsList = async () => {
    if (!functionListCache.value) {
        functionListCache.value = await loadList();
    }
    return functionListCache.value;
};
/**
 * SST stores full names of functions in SSM Parameter Store. Keys have common prefix `/sst/oss/${envName}/Function/`
 * This function loads all parameters from the path and creates a mapping of function name to full name.
 * There are only those functions that are explicitly defined in the stack. Implicitly created functions are not included.
 * @returns 
 */
const getFunctionKeyNameMapping = async () => {
    if (functionMappingCache) {
        return functionMappingCache;
    }
    const envName = process.env.SST_STAGE;
    const ssm = awsClients.parameterStoreClient()
    const params: aws.SSM.GetParametersByPathRequest = {
        Path: `/sst/oss/${envName}/Function/`,
        Recursive: true,
        WithDecryption: true,
    };

    const allParameters: aws.SSM.Parameter[] = [];
    let nextToken: string | undefined;

    do {
        const response = await ssm.getParametersByPath({ ...params, NextToken: nextToken }).promise();
        if (response.Parameters) {
            allParameters.push(...response.Parameters);
        }
        nextToken = response.NextToken;
    } while (nextToken);

    functionMappingCache = {}
    allParameters.map((p) => {
        const name = p.Name.split('/').slice(-2)[0];
        return {
            name,
            value: p.Value,
        }
    }
    ).reduce((acc, p) => {
        acc[p.name] = p.value;
        return acc;
    }, functionMappingCache);
    return functionMappingCache;
}

const getLambdaName = async (coreName) => {
    const mapping = await getFunctionKeyNameMapping();
    const result = mapping[coreName];
    if (result) {
        return result;
    }
    throw new LambdaNotFoundException(coreName);
};

const updateLambdaEnvironmentByFullName = async (lambdaFullName: string, variables: Record<string, string>) => {
    await doUpdateEnvironments(lambdaFullName, variables);
}

const updateLambdaEnvironment = async (lambdaName: string, variables: Record<string, string>) => {
    const lambdaFullName = await getLambdaName(lambdaName);
    await doUpdateEnvironments(lambdaFullName, variables);
}
const doUpdateEnvironments = async (lambdaFullName: string, variables: Record<string, string>) => {
    const currentFunction = await getClient()
        .getFunctionConfiguration({
            FunctionName: lambdaFullName,
        })
        .promise();

    let currentVariables = {};
    if (currentFunction.Environment && currentFunction.Environment.Variables) {
        currentVariables = currentFunction.Environment.Variables;
    }

    const newVariables = {
        ...currentVariables,
        ...variables,
    };

    //console.log(`Updating lambda ${lambdaFullName} with new environment variables`, newVariables);

    await getClient()
        .updateFunctionConfiguration({
            FunctionName: lambdaFullName,
            Environment: {
                Variables: newVariables,
            },
        })
        .promise();
};

const resourcegroupstaggingapi = new aws.ResourceGroupsTaggingAPI();

/**
 * Damage Report stack uses many lambda functions, but the most of them are created implicitly.
 * We have to use tag filtering to get all lambdas of the damage reporting stack.
 * @returns 
 */
async function getLambdasOfDamageReportStack() {
    if (damageReportStackLambdaCache) {
        return damageReportStackLambdaCache;
    }
    const res = await getLambdasOfStack('damage-report');
    damageReportStackLambdaCache = res;
    return res;
}

async function getLambdasOfStack(stackName: string) {
    // use filter resources by tags to get all lambdas of the stack
    const params = {
        ResourceTypeFilters: ['lambda'],
        TagFilters: [{
            Key: 'aws:cloudformation:stack-name',
            Values: [`mydev-oss-${stackName}`],
        }],
        PaginationToken: undefined
    };

    const list: ResourceTagMapping[] = [];
    while (true) {
        const resources = await resourcegroupstaggingapi.getResources(params).promise();

        resources.ResourceTagMappingList.forEach(r => {
            list.push(r)
        });

        if (!resources.PaginationToken) {
            break;
        }

        params.PaginationToken = resources.PaginationToken;
    }
    // there are not only functions, but also other resources in the list
    // we need to filter only functions
    const filteredList = list.filter(r => r.ResourceARN.includes(':function:'));
    return new StackLambdas(stackName, filteredList);
}

type ResourceTagMapping = {
    ResourceARN: string;
    Tags: {
        Key: string,
        Value: string
    }[]
}

export class StackLambdas {
    constructor(private stackName: string, private resourcesList: ResourceTagMapping[]) {
    }

    /**
     * Return the name of first matched Lambda or undefined
     * @param idPrefix
     */
    findLambdaNameByIdPrefix(idPrefix: string) {
        const matched = this.resourcesList.find(r => r.Tags.find(t => t.Key == 'aws:cloudformation:logical-id' && t.Value.startsWith(idPrefix)));
        if (matched) {
            return matched.ResourceARN.split(':').slice(-1)[0];
        }
        return undefined
    }
    listLambdaNames(...excludedPrefixes: string[]) {
        const excluded = excludedPrefixes.map(p => p.toLowerCase());
        return this.resourcesList
            .filter(r => !excluded.some(p => r.Tags.find(t => t.Key == 'aws:cloudformation:logical-id' && t.Value.toLowerCase().startsWith(p))))
            .map(r => r.ResourceARN.split(':').slice(-1)[0]);
    }
}

export {
    updateLambdaEnvironment,
    updateLambdaEnvironmentByFullName,
    getLambdaName,
    getLambdasOfStack,
    getLambdasOfDamageReportStack
};
