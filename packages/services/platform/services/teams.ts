/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import * as exceptions from '../../common/admin-api/utils/exceptions.js';
import _ from 'lodash';
import { DateTime } from 'luxon';
import c from '../../common/admin-api/config/constants.js';
import config from '../../common/admin-api/config/static.js';
import { isMemberOfAdministratorsTeam } from '../../common/admin-api/utils/auth.js';
import { dynamoDbDocumentClient } from '../../common/utils/aws-clients.js';

const prefix = 'permission#';

const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

const getCurrentDateTime = () => {
  return DateTime.utc().toSeconds();
};

const checkIsExisted = async (params) => {
  const existedList = await getTeamList([], true);
  if (params.teamId) {
    const find = _.find(existedList, (obj) => {
      if (params.teamId !== obj.teamId && params.teamName === obj.teamName) {
        return obj;
      }
    });
    return find ? true : false;
  } else {
    const find = _.find(existedList, { teamName: params.teamName });
    return find ? true : false;
  }
};

const tableName = () => {
  return config.get(c.DATABASE_TABLE);
};

const getPartitionKey = (teamId) => {
  return prefix + teamId;
};

const getTeamList = async (ownGroups, all = false) => {
  const client = dynamoDbDocumentClient();
  const document = await client
    .scan({
      TableName: tableName(),
      IndexName: 'teamId-teamName-index',
    })
    .promise();

  const response = document.Items.filter((item) => {
    return item.partitionKey.startsWith(prefix) && item.teamId !== 'Administrator';
  });

  const isAdministrator = isMemberOfAdministratorsTeam(ownGroups);

  if (isAdministrator || all) {
    return response;
  } else {
    return response.filter((item) =>
      ownGroups.some((value) => {
        return value.indexOf(item.teamId) >= 0;
      })
    );
  }
};

const getTeamDetail = async (teamId) => {
  const pk = getPartitionKey(teamId);
  const client = dynamoDbDocumentClient();
  const document = await client
    .query({
      TableName: tableName(),
      KeyConditionExpression: 'partitionKey = :pk',
      ExpressionAttributeValues: {
        ':pk': pk,
      },
    })
    .promise();

  const response = document.Items || {};
  return response[0];
};

const putTeam = async (params) => {
  const isExisted = await checkIsExisted(params);
  if (isExisted) {
    throw new exceptions.BadRequest({
      code: 'already_exists',
      msg: '入力されたチーム名は、既に使用されております。別の名前を入力してください。'
    });
  }

  const teamId = params.teamId ? params.teamId : generateUUID();
  const pk = getPartitionKey(teamId);

  const merged:any = {
    ...params,
    ...{
      partitionKey: pk,
      teamId: teamId,
      updatedAt: getCurrentDateTime(),
    },
  };
  merged.createdAt = params.createdAt ? params.createdAt : getCurrentDateTime();

  const client = dynamoDbDocumentClient();
  await client
    .put({
      TableName: tableName(),
      Item: merged,
    })
    .promise();

  const response:any = {};
  response.item = merged;
  response.status = 200;

  return response;
};

const deleteTeam = async (teamId) => {
  const detail = await getTeamDetail(teamId);
  const pk = getPartitionKey(teamId);
  const client = dynamoDbDocumentClient();
  await client
    .delete({
      TableName: tableName(),
      Key: { partitionKey: pk },
    })
    .promise();

  const response:any = {};
  response.item = detail;
  response.status = 200;

  return response;
};

const getOwnPermissions = async (ownGroups) => {
  const teamIds:any[] = [];
  const teamNames:any[] = [];
  const groups:any[] = [];
  const permissions = new Set();

  await Promise.all(
    ownGroups.map(async (item) => {
      const [teamId, level] = item.split(':');
      const detail = await getTeamDetail(teamId);
      if (detail) {
        teamIds.push(detail.teamId);
        teamNames.push(detail.teamName);
        groups.push(detail.teamName + ':' + level);
        detail.permissions.map((permission) => {
          permissions.add(permission);
        });
      } else {
        groups.push(item);
      }
    })
  );

  groups.sort((a, b) => b - a);

  const isAdministrator = isMemberOfAdministratorsTeam(ownGroups);

  const response:any = {
    teamIds: teamIds,
    teamNames: teamNames,
    groups: groups,
    permissions: [...permissions],
    isAdministrator: isAdministrator,
  };

  return response;
};

export {
  getTeamList,
  getTeamDetail,
  putTeam,
  deleteTeam,
  getOwnPermissions,
};
