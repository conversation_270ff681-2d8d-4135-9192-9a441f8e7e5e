/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { AwsClientMock } from '../spec/testResourcesFactory.js';
import config from '../../common/admin-api/config/static.js';
import cs from '../../common/admin-api/config/constants.js';
jest.mock('./aws-clients');
import { createLambdaClient as lambdaClient } from '../../common/utils/aws-clients.js';

import { getLambdaName, updateLambdaEnvironment } from './lambda-operations.js';

beforeEach(() => {
  AwsClientMock.reset(lambdaClient);
});

describe('getLambdaName', () => {
  it('find Function for lsc-dev environment', async () => {
    config.merge({ [cs.DEPLOY_ENV]: 'lsc-dev' });
    AwsClientMock.build(lambdaClient)
      .method('listFunctions')
      .returns({
        Functions: [
          { FunctionName: 'lsc-dev-jonh-Function-XXXX' },
          { FunctionName: 'lsc-dev-Function-XXXX' },
          { FunctionName: 'lsc-dev-scenario-Function-XXXX' },
        ],
      });

    expect(await getLambdaName('Function')).toBe('lsc-dev-scenario-Function-XXXX');
  });
});

describe('updateLambdaEnvironment', () => {
  it('calls updateFunctionConfiguration with merged environment variables as argument', async () => {
    config.merge({ [cs.DEPLOY_ENV]: 'lsc-dev' });

    AwsClientMock.build(lambdaClient)
      .method('listFunctions')
      .returns({
        Functions: [{ FunctionName: 'lsc-dev-Function-XXXX' }],
      });
    const getFnConf = AwsClientMock.build(lambdaClient)
      .method('getFunctionConfiguration')
      .returns({
        Environment: {
          Variables: {
            VALUE_1: 'VALUE_1',
            OVERRIDE_IT: 'OVERRIDE_IT',
          },
        },
      })
      .mock();
    const updateFnConf = AwsClientMock.build(lambdaClient).method('updateFunctionConfiguration').mock();

    await updateLambdaEnvironment('Function', {
      VALUE_2: 'VALUE_2',
      OVERRIDE_IT: 'OVERRIDEN',
    });

    expect(getFnConf).toBeCalledWith(expect.objectContaining({ FunctionName: 'lsc-dev-scenario-Function-XXXX' }));
    expect(updateFnConf).toBeCalledWith(
      expect.objectContaining({
        FunctionName: 'lsc-dev-scenario-Function-XXXX',
        Environment: {
          Variables: {
            VALUE_1: 'VALUE_1',
            VALUE_2: 'VALUE_2',
            OVERRIDE_IT: 'OVERRIDEN',
          },
        },
      })
    );
  });
});
