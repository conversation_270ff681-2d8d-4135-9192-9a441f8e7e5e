/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import * as AWS from 'aws-sdk';
import moment from 'moment';
import * as systemLogs from './system-logs.js';

describe('system-logs internal function', () => {
  describe('normalizeParams', () => {
    const now = moment();
    const before31Days = moment().add(-30, 'days');
    const targets = [
      {
        params: {
          start: '2021-08-10',
          end: '2021-08-11',
        },
        expects: {
          start: {
            year: 2021,
            month: 7,
            date: 10,
          },
          end: {
            year: 2021,
            month: 7,
            date: 11,
          },
        },
      },
      {
        params: {
          start: '2021-04-01',
        },
        expects: {
          start: {
            year: 2021,
            month: 3,
            date: 1,
          },
          end: {
            year: 2021,
            month: 4,
            date: 1,
          },
        },
      },
      {
        params: {
          end: '2021-05-31',
        },
        expects: {
          start: {
            year: 2021,
            month: 4,
            date: 1,
          },
          end: {
            year: 2021,
            month: 4,
            date: 31,
          },
        },
      },
      {
        params: {},
        expects: {
          start: {
            year: before31Days.year(),
            month: before31Days.month(),
            date: before31Days.date(),
          },
          end: {
            year: now.year(),
            month: now.month(),
            date: now.date(),
          },
        },
      },
    ];
    targets.forEach((target) => {
      const { params, expects } = target;
      it(Object.keys(params).join(', ') || 'empty', () => {
        const { start, end } = systemLogs._internal.normalizeParams(params);

        expect(start).not.toBeUndefined();
        expect(end).not.toBeUndefined();

        expect(moment.isMoment(start)).toBeTruthy();
        expect(moment.isMoment(end)).toBeTruthy();

        expect(start.year()).toBe(expects.start.year);
        expect(start.month()).toBe(expects.start.month);
        expect(start.date()).toBe(expects.start.date);

        expect(end.year()).toBe(expects.end.year);
        expect(end.month()).toBe(expects.end.month);
        expect(end.date()).toBe(expects.end.date);

        if (!params.start || !params.end) {
          expect(start.diff(end, 'days')).toBe(-30);
        }
      });
    });
  });
  describe('validateDateRange', () => {
    const targets = [
      {
        params: {
          start: '2021-08-10',
          end: '2021-08-11',
        },
        expects: true,
      },
      {
        params: {
          start: '2021-03-01',
          end: '2021-04-01',
        },
        expects: false,
      },
      {
        params: {
          start: '2021-04-01',
          end: '2021-03-01',
        },
        expects: false,
      },
    ];
    targets.forEach((target) => {
      const { start, end } = target.params;

      let ok = true;
      try {
        systemLogs._internal.validateDateRange(moment(start), moment(end));
      } catch (e: any) {
        ok = false;
      }

      it(`validate: ${start} to ${end}`, () => {
        expect(ok).toBe(target.expects);
      });
    });
  });
  describe('toYYYYMM', () => {
    const now = moment();
    it('check format', () => {
      const yyyymm = systemLogs._internal.toYYYYMM(now);

      expect(yyyymm).not.toBeUndefined();
      expect(typeof yyyymm).toBe('string');

      const elements = yyyymm.split('-');
      expect(elements.length).toBe(2);
      expect(now.year()).toBe(parseInt(elements[0], 10));
      expect(now.month()).toBe(parseInt(elements[1], 10) - 1);
    });
  });
  describe('targetMonths', () => {
    const targets = [
      {
        params: {
          start: '2021-08-10',
          end: '2021-08-11',
        },
        expects: ['2021-08'],
      },
      {
        params: {
          start: '2021-04-25',
          end: '2021-05-10',
        },
        expects: ['2021-04', '2021-05'],
      },
      {
        params: {
          start: '2021-01-31',
          end: '2021-03-01',
        },
        expects: ['2021-01', '2021-02', '2021-03'],
      },
    ];

    targets.forEach((target) => {
      const { params, expects } = target;
      it(`${params.start} to ${params.end}`, () => {
        const results = systemLogs._internal.targetMonths(moment(params.start), moment(params.end));

        expect(results).not.toBeUndefined();
        expect(Array.isArray(results)).toBeTruthy();

        expect(results).toEqual(expects);
      });
    });
  });
  describe('findObjects', () => {
    const s3 = new AWS.S3();
    s3.listObjectsV2 = jest.fn().mockImplementation((params) => ({
      promise() {
        const { Prefix } = params;
        return Promise.resolve({
          Contents: [
            {
              Key: `${Prefix}-01.csv`,
            },
            {
              Key: `${Prefix}-02.csv`,
            },
            {
              Key: `${Prefix}-03.csv`,
            },
          ],
        });
      },
    }));

    const prefix = 'operations-log-';
    const targets = [['2021-08'], ['2021-07', '2021-08']];

    for (const target of targets) {
      it(`target: ${target.join(', ')}`, async () => {
        const result = await systemLogs._internal.findObjects(s3, 'dummy-bucket', prefix, target);

        expect(Array.isArray(result)).toBeTruthy();
        expect(result.length).toBe(target.length * 3);

        const resultKeys = result.map((r) => r.Key);
        for (let t = 0; t < target.length; t++) {
          for (let i = 1; i <= 3; i++) {
            expect(resultKeys).toContain(`${prefix}${target[t]}-0${i}.csv`);
          }
        }
      });
    }
  });
  describe('extractObjects', () => {
    const targets = [
      {
        params: {
          start: '2021-07-25',
          end: '2021-08-05',
        },
        expects: {
          start: '2021-07-25',
          end: '2021-08-05',
        },
      },
      {
        params: {
          start: '2021-07-10',
          end: '2021-08-02',
        },
        expects: {
          start: '2021-07-20',
          end: '2021-08-02',
        },
      },
      {
        params: {
          start: '2021-07-28',
          end: '2021-08-30',
        },
        expects: {
          start: '2021-07-28',
          end: '2021-08-25',
        },
      },
    ];

    const url = 'https://example.com';
    const s3 = new AWS.S3();
    s3.getSignedUrlPromise = jest.fn().mockImplementation(() => Promise.resolve(url));

    const prefix = 'operations-log-';
    const now = moment().toJSON();
    const objects:any[] = [];

    // NOTE: 2021-07-20 から 2021-08-25 までのテストデータを生成
    for (let m = 7; m <= 8; m++) {
      const start = m === 7 ? 20 : 1;
      const end = m === 8 ? 25 : 31;
      for (let d = start; d <= end; d++) {
        objects.push({
          Key: `${prefix}2021-0${m}-${d.toString(10).padStart(2, '0')}.csv`,
          LastModified: now,
          Size: 0,
        });
      }
    }

    for (const target of targets) {
      const { params, expects } = target;
      it(`${params.start} to ${params.end}`, async () => {
        const startMomentDate = moment(expects.start);
        const endMomentDate = moment(expects.end);
        const result = await systemLogs._internal.extractObjects(
          s3,
          'dummy-bucket',
          prefix,
          objects,
          startMomentDate,
          endMomentDate
        );
        expect(Array.isArray(result)).toBeTruthy();

        const days = Math.abs(startMomentDate.diff(endMomentDate, 'days', true)) + 1;
        expect(result.length).toBe(days);

        for (let i = 0; i < days; i++) {
          const date = moment(expects.start).add(i, 'days').format('YYYY-MM-DD');
          expect(result[i].filename).toBe(`${prefix}${date}.csv`);
          expect(result[i].modified).toBe(now);
          expect(result[i].size).toBe(0);
          expect(result[i].url).toBe(url);
        }

        expect(result[0].filename).toBe(`${prefix}${expects.start}.csv`);
        expect(result[result.length - 1].filename).toBe(`${prefix}${expects.end}.csv`);
      });
    }
  });
});
