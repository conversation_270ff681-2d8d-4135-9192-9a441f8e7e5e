/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import AWS from 'aws-sdk';
import moment from 'moment';
import * as externalLog from '../controllers/helpers/log-external.js'

const filenamePrefix = 'operations-log-';

const normalizeParams = (params) => {
  const { start, end } = params;

  let mStart;
  let mEnd;
  if (!start && end) {
    mEnd = moment(end);
    mStart = moment(mEnd).add(-30, 'days');
  } else if (start && !end) {
    mStart = moment(start);
    mEnd = moment(mStart).add(30, 'days');
  } else if (!start && !end) {
    mEnd = moment();
    mStart = moment(mEnd).add(-30, 'days');
  } else {
    mStart = moment(start);
    mEnd = moment(end);
  }

  return {
    start: mStart,
    end: mEnd,
  };
};

const validateDateRange = (startMomentDate, endMomentDate) => {
  if (startMomentDate.diff(endMomentDate, 'days') < -30) {
    throw new Error('search days must not over 31 days');
  } else if (startMomentDate.diff(endMomentDate, 'days') > 0) {
    throw new Error('start date must not over end date');
  }
};

const toYYYYMM = (momentDate) => {
  return momentDate.format('YYYY-MM');
};

const targetMonths = (startMomentDate, endMomentDate) => {
  const result:any[] = [];
  if (startMomentDate.month() === endMomentDate.month()) {
    const target = toYYYYMM(startMomentDate);
    result.push(target);
  } else {
    const diff = Math.ceil(Math.abs(endMomentDate.diff(startMomentDate, 'month', true)));
    for (let i = 0; i <= diff; i++) {
      const target = moment(startMomentDate).date(1).add(i, 'month');
      result.push(toYYYYMM(target));
    }
  }
  return result;
};

const findObjects = async (s3, bucket, prefix, targetsYYYYMM) => {
  const baseParams = {
    Bucket: bucket,
    Prefix: prefix,
  };

  const result:any[] = [];
  for (const targetYYYYMM of targetsYYYYMM) {
    const param = { ...baseParams, Prefix: baseParams.Prefix + targetYYYYMM };
    const list = await s3.listObjectsV2(param).promise();
    result.push(...list.Contents);
  }
  return result;
};

const extractObjects = async (s3, bucket, prefix, objects, startMomentDate, endMomentDate) => {
  const result:any[] = [];

  const targets = [...objects].sort((a, b) => (a.Key > b.Key ? 1 : -1));

  let startFound = false;
  for (const target of targets) {
    const { Key, LastModified, Size } = target;
    const keyDateStr = Key.replace(prefix, '').replace('.csv', '');
    const keyMomentDate = moment(keyDateStr);

    if (keyMomentDate > endMomentDate) {
      break;
    }

    if (!startFound) {
      if (startMomentDate <= keyMomentDate) {
        startFound = true;
      } else {
        continue;
      }
    }

    const url = await s3.getSignedUrlPromise('getObject', {
      Bucket: bucket,
      Key,
      Expires: 3600, // 1 hour
    });

    result.push({
      filename: Key.split('/').pop(),
      modified: LastModified,
      size: Size,
      url,
    });
  }

  return result;
};

const listObjects = async (params) => {
  const { LOG_BUCKET, LOG_BUCKET_PREFIX } = process.env;
  if (!LOG_BUCKET) {
    throw new Error("'LOG_BUCKET' environment variable is undefined");
  }
  if (!LOG_BUCKET_PREFIX) {
    throw new Error("'LOG_BUCKET_PREFIX' environment variable is undefined");
  }

  const { start, end } = normalizeParams(params);
  validateDateRange(start, end);
  const targetsYYYYMM = targetMonths(start, end);
  const prefix = `${LOG_BUCKET_PREFIX}${filenamePrefix}`;

  const s3 = new AWS.S3();

  const objects = await findObjects(s3, LOG_BUCKET, prefix, targetsYYYYMM);
  return await extractObjects(s3, LOG_BUCKET, prefix, objects, start, end);
};

const saveExternalLog = async (body: any) => {
  if (!externalLog.isExternalActionLogData(body)) {
    return {
      result: 'ERROR',
      message: 'Invalid format',
    };
  }
  await externalLog.create().trySaveItem(body);
  return {
    result: 'OK'
  };
}

const internal = {
  normalizeParams,
  validateDateRange,
  toYYYYMM,
  targetMonths,
  findObjects,
  extractObjects,
}

export {
  listObjects,
  saveExternalLog,
  internal as _internal
};
