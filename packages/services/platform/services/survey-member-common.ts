/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import c from '../../common/admin-api/config/constants.js';
import config from '../../common/admin-api/config/static.js';

import { getOne } from '../../common/admin-api/utils/db.js';

import { UpdateExpressionBuilder, updateOne } from '../../common/admin-api/utils/db.js';

import NodeCache from 'node-cache';
import {SurveyConfig} from '../../api-admin-survey/types/survey-config.js';

const dynamoDbTableSurveyConfig = () => {
  return config.get(c.TABLE_SURVEY_CONFIGS);
};

const dynamoDbTableMemberConfig = () => {
  return config.get(c.TABLE_MEMBER_CONFIGS);
};

const configsCache = new NodeCache({
  stdTTL: 5,
  useClones: true,
});

const getSurveyConfigById = async (surveyId, consistentRead = false): Promise<SurveyConfig> => {
  if (!surveyId) {
    return;
  }
  let target:SurveyConfig = configsCache.get(surveyId);
  if (!target) {
    const params = {
      TableName: dynamoDbTableSurveyConfig(),
      Key: {
        surveyId: { S: surveyId },
      },
      ConsistentRead: consistentRead,
    };
    target = await getOne(params) as SurveyConfig;
    configsCache.set(surveyId, target);
  }
  return target;
};

const getMemberConfigById = async (memberConfigId) => {
  const params = {
    TableName: dynamoDbTableMemberConfig(),
    Key: {
      surveyId: { S: memberConfigId },
    },
  };
  return await getOne(params);
};

const fetchTeamListFromLinkedSurveyConfigs = async (linkedSurveyFormIds) => {
  const teamList:any[] = [];

  const uniqueTeams = {};

  for (const surveyId of linkedSurveyFormIds) {
    const surveyConfig:any = await getSurveyConfigById(surveyId);

    if (surveyConfig) {
      for (const team of surveyConfig.surveyTeams) {
        uniqueTeams[team.teamId] = team.teamName;
      }
    }
  }

  for (const teamId in uniqueTeams) {
    teamList.push(
      {
        teamId: teamId,
        teamName: uniqueTeams[teamId]
      }
    );
  }
  // if memberConfig don't have any linked surveyConfigs, set Administrator permission (only admins can see it)
  if (linkedSurveyFormIds.length === 0 && teamList.length === 0) {
    teamList.push(
      {
        teamId: 'Administrator',
        teamName: 'アドミニストレータ',
      }
    )
  }

  return teamList;
};

const updateMemberConfig = async (username, input, surveyId, updateTeamListFromLinked = false) => {
  const currentUnixtimeSec = Math.floor(new Date().getTime() / 1000);

  //Add default administrator team.
  const adminTeam = { teamName: 'アドミニストレータ', teamId: 'Administrator' };

  // Old survey type might not have {surveyTeams} attribute -> add it
  if (!('surveyTeams' in input)) {
    input.surveyTeams = [];
  }
  const hasAdminInside = input.surveyTeams.filter((item) => item.teamId === 'Administrator').length;
  if (hasAdminInside === 0) {
    input.surveyTeams.push(adminTeam);
  }

  if (!input.linkedForms) {
    input.linkedForms = [];
  }

  const teamList = updateTeamListFromLinked ? await fetchTeamListFromLinkedSurveyConfigs(input.linkedForms) : input.surveyTeams;

  const surveyConfigObj = {
    surveyTitle: input.surveyTitle,
    surveyType: input.surveyType ? input.surveyType : 'normal',
    description: input.description === null ? '' : input.description,
    isAppending: input.isAppending,
    isSearchable: input.isSearchable,
    endOfSurveyMessage: input.endOfSurveyMessage === null ? '' : input.endOfSurveyMessage,
    surveyStatus: input.surveyStatus,
    surveySchema: input.surveySchema,
    surveyTeams: teamList,
    updatedAt: currentUnixtimeSec,
    updatedBy: username,
    linkedForms: input.linkedForms,
    headerImageUrl: input.headerImageUrl,
  };

  const Key = {
    surveyId: { S: surveyId },
  };

  const updateBuilder = new UpdateExpressionBuilder(surveyConfigObj);
  updateBuilder.build();
  return await updateOne({
    TableName: dynamoDbTableMemberConfig(),
    Key: Key,
    ExpressionAttributeNames: updateBuilder.getExpressionAttributeNames(),
    ExpressionAttributeValues: updateBuilder.getExpressionAttributeValues(),
    UpdateExpression: updateBuilder.getSetExpression(),
  });
};

const updateTeamListDict = (teamList, teamsToAdd) => {
  for (const team of teamsToAdd) {
    teamList[team.teamId] = team.teamName;
  }
};

const compareTeamLists = (list1, list2) => {
  // True if both lists contains same teams
  // False otherwise
  let result = true;

  // Check if lists have same size and same team ids
  if (list1.length === list2.length) {
    for (const team1 of list1) {
      const hasTeam = list2.some((team2) => {
        return team1.teamId === team2.teamId;
      });

      if (!hasTeam) {
        result = false;
        break;
      }
    }
  } else {
    result = false;
  }

  return result;
};

const updateTeamsOfLinkedMemberForm = async (memberConfig, linkedSurveyId, linkedTeamList) => {
  // Update member form team list based on new team list and existing lists from linked survey forms
  // Make a dictionary of unique teams than update member config
  const teamList = {}

  if (linkedTeamList.length > 0) {
    updateTeamListDict(teamList, linkedTeamList);
  }

  for (const surveyConfigId of memberConfig.linkedForms) {
    // Ignore the team list from the linked survey config that triggered this update
    if (surveyConfigId !== linkedSurveyId) {
      const surveyConfig:any = await getSurveyConfigById(
        surveyConfigId
      );
      if (surveyConfig) {
        updateTeamListDict(teamList, surveyConfig.surveyTeams);
      }
    }
  }

  // Make new team list, set it to member form and update
  const newTeamList:any[] = [];

  for (const teamId in teamList) {
    newTeamList.push(
      {
        teamId: teamId,
        teamName: teamList[teamId]
      }
    );
  }

  return newTeamList;
};

const updateMemberConfigOnSurveyConfigUpdate = async (surveyConfigInput, username) => {
  if (surveyConfigInput.memberFormId && surveyConfigInput.memberFormId.length > 0) {
    // If form is not "deleted", use its team list as it is
    // If isDisplay is being set to false, update linked member form as if the team list was empty
    const memberConfig = await getMemberConfigById(surveyConfigInput.memberFormId);

    const surveyDeleted = surveyConfigInput.isDisplay !== undefined && !surveyConfigInput.isDisplay;
    const teamList = surveyDeleted ? [] : surveyConfigInput.surveyTeams;

    const updatedTeamList = await updateTeamsOfLinkedMemberForm(
      memberConfig,
      surveyConfigInput.surveyId,
      teamList,
    );

    if (surveyDeleted) {
      // Remove this survey config ID from the list of linked forms in the member config object
      memberConfig.linkedForms = memberConfig.linkedForms.filter((surveyId) => {
        return surveyId !== surveyConfigInput.surveyId;
      });
    }

    // Updated member config if team list was changed or if survey config is being deleted
    // "deleted" -> 「isDisplay」 is false
    if (!compareTeamLists(memberConfig.surveyTeams, updatedTeamList) || surveyDeleted) {
      memberConfig.surveyTeams = updatedTeamList;
      await updateMemberConfig(username, memberConfig, surveyConfigInput.memberFormId);
    }
  }
};

export {
  dynamoDbTableSurveyConfig,
  dynamoDbTableMemberConfig,
  fetchTeamListFromLinkedSurveyConfigs,
  getSurveyConfigById,
  getMemberConfigById,
  updateMemberConfig,
  updateTeamsOfLinkedMemberForm,
  updateMemberConfigOnSurveyConfigUpdate,
};
