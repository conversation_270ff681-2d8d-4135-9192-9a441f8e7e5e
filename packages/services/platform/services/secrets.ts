/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import aws from 'aws-sdk';
import c from '../../common/admin-api/config/constants.js';
import * as awsHelper from '../../common/admin-api/utils/aws-helper.js';
import _ from 'lodash';
import config from '../../common/admin-api/config/static.js';
import { SystemConfigurationError } from '../../common/admin-api/utils/exceptions.js';

const getSecretManagerClient = () => {
  return new aws.SecretsManager(awsHelper.getEnvironmentCredentials());
};

const getSecretId = () => {
  return config.opt(c.SECRET_ID).or(config.get(c.DEPLOY_ENV));
};

const get = async () => {
  const result = await getSecretManagerClient()
    .getSecretValue({
      SecretId: getSecretId(),
    })
    .promise();

  try {
    return JSON.parse(result.SecretString || '');
  } catch (e: any) {
    throw new SystemConfigurationError(`Can not parse Secrets Manager
     value as JSON`);
  }
};

const upsert = async (values) => {
  const prevSecret = await get();

  const newSecretValue = _.omitBy(
    {
      ...prevSecret,
      ...values,
    },
    (value) => value === null
  );

  await getSecretManagerClient()
    .putSecretValue({
      SecretId: getSecretId(),
      SecretString: JSON.stringify(newSecretValue),
    })
    .promise();

  return newSecretValue;
};

const rewrite = async (newSecret) => {
  await getSecretManagerClient()
    .putSecretValue({
      SecretId: getSecretId(),
      SecretString: JSON.stringify(newSecret),
    })
    .promise();
};

export {
  upsert,
  get,
  rewrite,
};
