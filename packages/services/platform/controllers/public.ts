/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *  - name: Public
 *    description: 基本設定管理API
 */

import * as secrets from '../services/secrets.js';
import * as buildInfo from '../../common/admin-api/utils/build-properties.js';
import {lambdaEnv} from "../../common/lambda-env.js";

/**
 * @openapi
 * /public/configuration:
 *  get:
 *    summary: 基本設定情報を返す
 *    tags: [Public]
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 */
const get = async (req, res) => {
  const s = {} // TODO load URLs from the Parameter Store

  res.json({
    endpoints: {
      admin: s['VUE_APP_AMPLIFY_ADMIN_API_ENDPOINT_URL'],
      scenario: s['VUE_APP_AMPLIFY_SCENARIO_API_ENDPOINT_URL'],
      quicksight: s['VUE_APP_CQ_API_ENDPOINT_URL'],
    },
    login: {
      cognitoUserPoolId: lambdaEnv.getEnv('ADMIN_USERPOOL_ID'),
      cognitoUserPoolClientId: lambdaEnv.getEnv('ADMIN_USERPOOL_CLIENT_ID'),
    },
    build: {

    },
    versions: {
      general: s['GENERAL_APP_VERSION'],
    },
  });
};

export const routes = (app) => {
  app.get('/configuration', get);
}
