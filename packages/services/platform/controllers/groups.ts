/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *  - name: Groups
 *    description: ユーザー権限グループ管理API
 */

import aws from 'aws-sdk';
import config from '../../common/admin-api/config/static.js';
import c from '../../common/admin-api/config/constants.js';
import { BadRequest, ServerError } from '../../common/admin-api/utils/exceptions.js';
import { admin, member, guest, compose } from '../../common/admin-middlewares/access-guards.js';
import { LoggerService } from '../services/logger-service.js';
const loggerService = new LoggerService('user-settings');

const w = (handler) => {
  return async (req, res) => {
    try {
      return await handler(req, res);
    } catch (e: any) {
      if (e.constructor.name === 'NotFound') {
        throw new BadRequest({ cause: e, code: e.code });
      } else {
        throw new ServerError({ cause: e });
      }
    }
  };
};

const m = compose(member, w);
const g = compose(guest, w);
const a = compose(admin, w);

const getClient = () => {
  return new aws.CognitoIdentityServiceProvider();
};
const getUserPoolId = () => {
  return config.get(c.ADMIN_USER_POOL_ID);
};

/**
 * @openapi
 * /groups:
 *  get:
 *    summary: 権限グループ一覧を返す
 *    tags: [Groups]
 */
const list = async (req, res) => {
  await loggerService.createLog(req);
  const params:any = {
    UserPoolId: getUserPoolId(),
    Limit: 60,
  };

  let response:any = {};
  let groups = [];

  do {
    response = await getClient().listGroups(params).promise();
    params.NextToken = response.NextToken;
    groups = groups.concat(response.Groups);
  } while (response.NextToken);

  response.Groups = groups;
  res.json(response);
};

/**
 * @openapi
 * /groups/{groupName}:
 *  get:
 *    summary: 権限グループ詳細を返す
 *    tags: [Groups]
 *    parameters:
 *      - name: groupName
 *        in: path
 *        description: Name of group
 *        required: true
 *        schema:
 *          type: string
 */
const details = async (req, res) => {
  await loggerService.createLog(req);

  const response = await getClient()
    .getGroup({
      UserPoolId: getUserPoolId(),
      GroupName: req.params.group,
    })
    .promise();

  res.json(response);
};

/**
 * @openapi
 * /groups:
 *  post:
 *    summary: 権限グループを作成する
 *    tags: [Groups]
 */
const create = async (req, res) => {
  await loggerService.createLog(req);

  const response = await getClient()
    .createGroup({
      ...req.body,
      UserPoolId: getUserPoolId(),
    })
    .promise();

  res.json(response);
};

/**
 * @openapi
 * /groups/{groupName}:
 *  delete:
 *    summary: 権限グループを削除する
 *    tags: [Groups]
 *    parameters:
 *      - name: groupName
 *        in: path
 *        description: Name of group
 *        required: true
 *        schema:
 *          type: string
 */
const deleteGroup = async (req, res) => {
  await loggerService.createLog(req);

  await getClient()
    .deleteGroup({
      UserPoolId: getUserPoolId(),
      GroupName: req.group,
    })
    .promise();

  res.sendStatus(200);
};

/**
 * @openapi
 * /groups/{groupName}/users:
 *  get:
 *    summary: 権限グループに所属するユーザー一覧を返す
 *    tags: [Groups]
 *    parameters:
 *      - name: groupName
 *        in: path
 *        description: Name of group
 *        required: true
 *        schema:
 *          type: string
 */
const users = async (req, res) => {
  await loggerService.createLog(req);

  const response = await getClient()
    .listUsersInGroup({
      UserPoolId: getUserPoolId(),
      GroupName: req.params.group,
      Limit: 60,
    })
    .promise();

  res.json(response);
};

/**
 * @openapi
 * /groups/{groupName}:
 *  put:
 *    summary: 権限グループ詳細を更新
 *    tags: [Groups]
 *    parameters:
 *      - name: groupName
 *        in: path
 *        description: Name of group
 *        required: true
 *        schema:
 *          type: string
 */
const update = async (req, res) => {
  await loggerService.createLog(req);

  await getClient()
    .updateGroup({
      Precedence: req.body.Precedence,
      Description: req.body.Description,
      GroupName: req.params.group,
      UserPoolId: getUserPoolId(),
    })
    .promise();

  res.sendStatus(200);
};

export const routes = (app) => {
  app.get('/', g(list));
  app.post('/', a(create));
  app.get('/:group', g(details));
  app.put('/:group', a(update));
  app.get('/:group/users', g(users));
  app.delete('/:group', a(deleteGroup));
};
