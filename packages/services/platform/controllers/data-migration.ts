/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import service from '../services/data-migration.js';
import * as surveyConfigService from '../../api-admin-survey/services/survey.js';
import config from '../../common/admin-api/config/static.js';
import { infoLog } from '../../common/admin-api/utils/logger.js';
import { MigrationError } from '../../common/admin-api/utils/exceptions.js';

const operationKey = `runAnswerCodeRestoration#${config.environmentName}`;
const runAnswerCodeRestoration = async (event, context) => {
    try {
        if (!('ContinueSurveyConfig' in event || 'ContinueSurveyResult' in event)) {
            //Do not call start if midway through migration
            await startDataMigration(operationKey);
        }

        //Fetch update type survey configs with reservation
        const configs = await getSurveyConfigsForProcessing(event.ContinueSurveyConfig);

        const migrationResult = await service.runAnswerCodeRestoration(configs, event, context);
        infoLog('migrationResult', migrationResult);

        if (migrationResult.result === 'CONTINUE') {
            await service.invokeMigrationLambda('RunAnswerCodeRestoration', {
                ContinueSurveyConfig: migrationResult.ContinueSurveyConfig,
                ContinueSurveyResult: migrationResult.ContinueSurveyResult
            })
        } else {
            await service.updateMigrationStatus(operationKey, 'finished');
        }
        return { result: 'OK' };
    } catch (error: any) {
        if (error instanceof MigrationError && error.code === 'migration_finished') {
            return { result: 'OK', details: 'Migration Already Completed' };
        } else {
            infoLog('[ERROR] runAnswerCodeRestoration データマイグレーション処理ながらエラー発生しました。', error);
            await service.updateMigrationStatus(operationKey, 'error', { errorMessage: error.message });
            return { result: 'ERROR', errorMessage: error.message };
        }
    }
}

const startDataMigration = async (dataMigrationOperationKey) => {
    const existingMigrationStatus = await service.getMigrationStatus(dataMigrationOperationKey);
    if (existingMigrationStatus === undefined || existingMigrationStatus.status === 'error') {
        await service.updateMigrationStatus(dataMigrationOperationKey, 'started', { errorMessage: '' });
    } else if (existingMigrationStatus.status === 'finished') {
        throw new MigrationError('migration_finished', 'Migration has already been completed for this environment. ');
    } else if (existingMigrationStatus.status === 'started') {
        throw new MigrationError('migration_started', 'Migration is currently running for this environment.');
    }
}

const getSurveyConfigsForProcessing = async (configIdForResume) => {
  let configs = await surveyConfigService.getAllSurveyConfigsNoFilter();
  configs = configs.filter((elem) => elem.isAppending && elem.isAppending.value && elem.surveySchema && elem.surveySchema.find((e) => e.type === 'reservation'));
  configs.sort((a, b) => a.surveyId - b.surveyId);


  if (configIdForResume) {
    const indexOfResume = configs.findIndex((elem) => elem.surveyId === configIdForResume);
    configs = configs.slice(indexOfResume);
  }

  return configs;
}

const makeShortMigrationExecutor = (migrationName, executor) => {
  return async (event, context) => {
    const opKey = `${migrationName}#${config.environmentName}`;
    infoLog(`[MIGRATION] ${opKey} launch request`);
    try {
      await startDataMigration(opKey);
      infoLog(`[MIGRATION] ${opKey} started`);
      await executor(event, context);
      await service.updateMigrationStatus(opKey, 'finished');
      infoLog(`[MIGRATION] ${opKey} finished`);
      return {result: 'OK'};
    } catch (error: any) {
      if (error instanceof MigrationError && error.code === 'migration_finished') {
        infoLog(`[MIGRATION] ${opKey} is already completed`);
        return {result: 'OK', details: 'Migration Already Completed'};
      } else if (error instanceof MigrationError && error.code === 'migration_started') {
        infoLog(`[MIGRATION] ${opKey} is currently running`);
        return {result: 'OK', details: 'Migration is currently running'};
      } else {
        infoLog(`[MIGRATION][ERROR] ${opKey} データマイグレーション処理ながらエラー発生しました。`, error);
        await service.updateMigrationStatus(opKey, 'error', {errorMessage: error.message});
        return {result: 'ERROR', errorMessage: error.message};
      }
    }
  }
}

const runImportCognitoUsers = async (event, context) => {
  const executor = await import('../migrations/download_cognito_users2.js');
  return await makeShortMigrationExecutor('ImportCognitoUsers', executor.default)(event, context);
}

const runDisasterDistributionUpdate = async (event, context) => {
  const executor = await import('../migrations/update_disaster_distributions.js');
  return await makeShortMigrationExecutor('UpdateDisasterDistributions', executor.default)(event, context);
};

export {
  runAnswerCodeRestoration,
  runImportCognitoUsers,
  runDisasterDistributionUpdate,
};
