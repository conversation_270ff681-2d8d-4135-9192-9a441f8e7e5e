/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import * as controller from './public.js';
import { MockedHandlerFactory, createGetEvent } from '../spec/testResourcesFactory.js';

import * as secretsService from '../services/secrets.js';
import {mocked} from 'ts-jest/utils';
jest.mock('../services/secrets');
const secrets = mocked(secretsService);

describe('GET /public/configuration', () => {
  beforeEach(() => {
    secrets.get.mockImplementation(() => Promise.resolve({
      VUE_APP_AMPLIFY_ADMIN_API_ENDPOINT_URL: 'https://v1',
      VUE_APP_AMPLIFY_SCENARIO_API_ENDPOINT_URL: 'https://v2',
      VUE_APP_CQ_API_ENDPOINT_URL: 'https://v3',
    }));
  });
  it('success', async () => {
    const app = new MockedHandlerFactory({}).create('/public', controller.routes);
    const response = await app(createGetEvent('/public/configuration'));

    expect(response.statusCode).toBe(200);
    expect(JSON.parse(response.body)).toMatchObject({
      endpoints: {
        admin: 'https://v1',
        scenario: 'https://v2',
        quicksight: 'https://v3',
      },
      build: expect.anything(),
    });
  });
});
