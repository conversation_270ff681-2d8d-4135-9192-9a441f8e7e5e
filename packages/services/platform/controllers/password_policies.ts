/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *  - name: PasswordPolicy
 *    description: パスワードポリシー管理API
 */

import { createCognitoIdp } from '../../common/utils/aws-clients.js';
import { admin as a, guest as g } from '../../common/admin-middlewares/access-guards.js';
import config from '../../common/admin-api/config/static.js';
import c from '../../common/admin-api/config/constants.js';
import _ from 'lodash';
import { ConfigurationMiss, BadRequest, ServerError } from '../../common/admin-api/utils/exceptions.js';

import { LoggerService } from '../services/logger-service.js';
const loggerService = new LoggerService('user-settings');

const w = (handler) => {
  return async (req, res) => {
    try {
      return await handler(req, res);
    } catch (e: any) {
      if (e.code === 'ResourceNotFoundException') {
        throw new ConfigurationMiss({ cause: e, code: e.code });
      } else if (e.constructor.name === 'NotFound') {
        throw new BadRequest({ cause: e, code: e.code });
      } else {
        throw new ServerError({ cause: e });
      }
    }
  };
};

const getClient = () => {
  return createCognitoIdp();
};
const getUserPoolId = () => {
  return config.get(c.ADMIN_USER_POOL_ID);
};

/**
 * @openapi
 * /pw_policies:
 *  post:
 *    summary: パスワードポリシーを更新する
 *    tags: [PasswordPolicy]
 *    requestBody:
 *      required: true
 *      content:
 *        application/json:
 *          schema:
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 */
const updatePasswordPolicies = w(async (req, res) => {
  await loggerService.createLog(req);

  const newPolicies = req.body;
  await getClient()
    .updateUserPool({
      UserPoolId: getUserPoolId(),
      Policies: {
        PasswordPolicy: newPolicies,
      },
    })
    .promise();

  res.sendStatus(200);
});

/**
 * @openapi
 * /pw_policies:
 *  get:
 *    summary: 登録されているパスワードポリシーを取得する
 *    tags: [PasswordPolicy]
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 */
const getPasswordPolicies = w(async (req, res) => {
  await loggerService.createLog(req);

  const response = await getClient()
    .describeUserPool({
      UserPoolId: getUserPoolId(),
    })
    .promise();

  const policies = _.get(response, 'UserPool.Policies.PasswordPolicy');
  res.json(policies);
});

export const routes = (app) => {
  app.post('/', a(updatePasswordPolicies));
  app.get('/', g(getPasswordPolicies));
};
