/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import * as controller from './settings.js';
import {
  handlerFactory,
  createGetEvent,
  createPutEvent,
  MockedHandlerFactory,
} from '../spec/testResourcesFactory.js';

describe('PUT /settings', () => {
  it('success', async () => {
    const mock = jest.fn().mockReturnValueOnce(Promise.resolve());
    controller.setService({
      updateSettings: mock,
    });

    const mf = new MockedHandlerFactory({});
    let app = mf.create('/settings', controller.routes);

    const response = await app(createPutEvent('/settings', { key: 'value' }));
    // const response = {}
    expect(response.statusCode).toBe(200);
    expect(mock).toHaveBeenCalledWith(expect.objectContaining({ key: 'value' }));
  });
});

describe('GET /settings', () => {
  it('success', async () => {
    const mock = jest.fn().mockReturnValueOnce(Promise.resolve({ key: 'value' }));
    controller.setService({
      getSettings: mock,
    });

    const mf = new MockedHandlerFactory({});
    let app = mf.create('/settings', controller.routes);

    const response = await app(createGetEvent('/settings'));
    expect(response.statusCode).toBe(200);
    expect(JSON.parse(response.body)).toMatchObject({ key: 'value' });
  });
});
