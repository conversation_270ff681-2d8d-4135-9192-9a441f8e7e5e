/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */
import config from '../../common/admin-api/config/static.js';
import cs from '../../common/admin-api/config/constants.js';
config.merge({
  [cs.ENVIRONMENT_TYPE]: 'test',
});
import * as controller from './user_settings.js';
const {
  handlerFactory,
  createGetEvent,
  createPutEvent,
  MockedHandlerFactory,
} = require('../spec/testResourcesFactory.js');

import * as userSettings from '../services/user_settings.js';
import {mocked} from 'ts-jest/utils';
jest.mock('../services/user_settings');
const mockedService = mocked(userSettings);

describe('PUT /permission', () => {
  it('update 2 groups', async () => {
    mockedService.updateGroupSettings.mockImplementation((group, alias, perm) => {
      return Promise.resolve({
        key: group,
      });
    });

    const mf = new MockedHandlerFactory({});
    mf.authenticate('alex', ['admins']);
    let app = mf.create('/s', controller.routes);

    const response = await app(
      createPutEvent('/s/permission', {
        members: { key: 'members' },
        guests: { key: 'guests' },
      })
    );
    expect(JSON.parse(response.body)).toMatchObject({
      myGroup: 'admins',
      byGroup: {
        members: {
          key: 'members',
        },
        guests: {
          key: 'guests',
        },
      },
    });
    expect(response.statusCode).toBe(200);
    expect(mockedService.updateGroupSettings).toHaveBeenNthCalledWith(
      1,
      'members',
      'permissions',
      expect.objectContaining({ key: 'members' })
    );
    expect(mockedService.updateGroupSettings).toHaveBeenNthCalledWith(
      2,
      'guests',
      'permissions',
      expect.objectContaining({ key: 'guests' })
    );
  });
  it('fails if member', async () => {
    const mf = new MockedHandlerFactory({});
    mf.authenticate('alex', ['members']);
    let app = mf.create('/s', controller.routes);
    const response = await app(
      createPutEvent('/s/permission', {
        members: { key: 'members' },
        guests: { key: 'guests' },
      })
    );
    console.log(JSON.stringify(response, null, 2));
    expect(response.statusCode).toBe(403);
  });
});

describe('GET /permission', () => {
  it('user with members group call', async () => {
    mockedService.getGroupSettings.mockReturnValue(
      Promise.resolve({
        key: 'value',
      })
    );
    const mf = new MockedHandlerFactory({});
    mf.authenticate('alex', ['members']);
    let app = mf.create('/front-settings', controller.routes);

    const response = await app(createGetEvent('/front-settings/permission'));
    expect(response.statusCode).toBe(200);
    expect(JSON.parse(response.body)).toMatchObject({
      myGroup: 'members',
      byGroup: {
        members: {
          key: 'value',
        },
      },
    });
    expect(mockedService.getGroupSettings).toBeCalledWith('members', 'permissions');
  });

  it('user with admins group call', async () => {
    mockedService.getGroupSettings.mockReturnValue(
      Promise.resolve({
        admins: {
          key: 'admins',
        },
        members: {
          key: 'members',
        },
      })
    );
    const mf = new MockedHandlerFactory({});
    mf.authenticate('alex', ['admins']);
    let app = mf.create('/front-settings', controller.routes);

    const response = await app(createGetEvent('/front-settings/permission'));
    expect(response.statusCode).toBe(200);
    expect(JSON.parse(response.body)).toMatchObject({
      myGroup: 'admins',
      byGroup: {
        admins: {
          key: 'admins',
        },
        members: {
          key: 'members',
        },
      },
    });
    expect(mockedService.getGroupSettings).toBeCalledWith('*', 'permissions');
  });
});
