/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *  - name: Users
 *    description: 管理画面ユーザー管理API
 */

import {createCognitoIdp} from '../../common/utils/aws-clients.js';
import config from '../../common/admin-api/config/static.js';
import c from '../../common/admin-api/config/constants.js';
import _ from 'lodash';
import {isMemberOfAdministratorsTeam} from '../../common/admin-api/utils/auth.js';
import {AccessDenied, BadRequest} from '../../common/admin-api/utils/exceptions.js';
import {admin as a, guest as g} from '../../common/admin-middlewares/access-guards.js';
import {LoggerService} from '../services/logger-service.js';
import {
  deleteHashUsernameMapping,
  filterAndGetPage,
  getUserAndRoles,
  getUsernameByHash,
  updateHashUsernameMapping,
  UsersEditAccessHelper
} from '../services/users.js';
import * as actionLogs from './helpers/log-users.js';
import {requireUser} from "../../common/admin-middlewares/authentication";

const loggerService = new LoggerService('user-settings');

const getClient = () => {
  return createCognitoIdp();
};
const getUserPoolId = () => {
  return config.get(c.ADMIN_USER_POOL_ID);
};

const requireUsernameByHash = async (hash) => {
  const username = await getUsernameByHash(hash);
  if (!username) {
    throw new BadRequest({msg: `No username with hash '${hash}' found`});
  }
  return username;
}

async function createAccessHelper(request: any, targetUsername: string) {
  const executor = requireUser(request);
  const target = await getUserAndRoles({username: targetUsername});
  return new UsersEditAccessHelper(executor, target);
}

/**
 * @openapi
 * /users:
 *  get:
 *    summary: ユーザー一覧を返す
 *    tags: [Users]
 */
const getUserList = async (req, res) => {
  await loggerService.createLog(req);
  const params:any = {
    UserPoolId: getUserPoolId(),
    Limit: 60,
  };

  let response:any = {};
  let users = [];

  do {
    response = await getClient().listUsers(params).promise();
    params.PaginationToken = response.PaginationToken;
    users = users.concat(response.Users);
  } while (response.PaginationToken);

  const ownGroups = req.user.groups;
  const isAdministrator = isMemberOfAdministratorsTeam(ownGroups);

  await Promise.all(
    users.map(async (user) => {
      const userGroups = await getClient()
        .adminListGroupsForUser({
          UserPoolId: getUserPoolId(),
          Username: user.Username,
          Limit: 60,
        })
        .promise();

      if (userGroups.Groups.length > 0) {
        const teams = userGroups.Groups.filter((group) => {
          if (group.GroupName.includes(':')) {
            const teamId = group.GroupName.split(':')[0];
            return teamId ? true : false;
          }
        });

        user.Teams = _.map(teams, 'GroupName');
      }
    })
  );

  if (!isAdministrator) {
    const listable = new Set();
    users.map((user) => {
      if (user.Teams) {
        user.Teams.map((team) => {
          const teamId = team.split(':')[0];
          const found = ownGroups.some((obj) => obj.includes(teamId));
          if (found) {
            listable.add(user);
          }
        });
      }
    });

    response.Users = [...listable];
  } else {
    response.Users = users;
  }
  res.json(response);
};

/**
 * @openapi
 * /users/{username}:
 *   get:
 *     description: ユーザー詳細を取得する
 *     tags: [Users]
 *     parameters:
 *       - name: username
 *         in: path
 *         description: Name of user to fetch
 *         required: true
 *         schema:
 *           type: string
 */
const getUserDetail = async (req, res) => {
  await loggerService.createLog(req);
  const username = await requireUsernameByHash(req.params.username);
  const response = await getClient()
    .adminGetUser({
      UserPoolId: getUserPoolId(),
      Username: username,
    })
    .promise();

  const ownGroups = req.user.groups;
  const isAdministrator = isMemberOfAdministratorsTeam(ownGroups);

  if (!isAdministrator) {
    let NextToken = undefined;
    const groups:any[] = [];
    do {
      const userGroups = await getClient()
        .adminListGroupsForUser({
          UserPoolId: getUserPoolId(),
          Username: username,
          Limit: 60,
          NextToken,
        })
        .promise();
      NextToken = userGroups.NextToken;
      groups.push(...userGroups.Groups);
    } while (NextToken);

    if (groups.length > 0) {
      const isSameGroup = groups.some((group) => {
        if (group.GroupName.includes(':')) {
          const teamId = group.GroupName.split(':')[0];
          return ownGroups.some((obj) => obj.includes(teamId));
        }
      });

      if (!isSameGroup) {
        throw new AccessDenied('Access to this resource is restricted to groups: ' + ownGroups.join(','));
      }
    }
  }

  res.json(response);
};

/**
 * @openapi
 * /users:
 *  post:
 *    summary: 新規ユーザーを作成する
 *    tags: [Users]
 *    requestBody:
 *      required: true
 *      content:
 *        application/json:
 *          schema:
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 */
const createUser = async (req, res) => {
  await loggerService.createLog(req);
  const params = req.body;

  await actionLogs.forUser(params.Username, actionLogs.getEmailFromRequest(params)).logCreateUser();

  await updateHashUsernameMapping(params.Username);
  const response = await getClient()
    .adminCreateUser({
      ...params,
      UserPoolId: getUserPoolId(),
    })
    .promise();

  res.json(response);
};

/**
 * @openapi
 * /users/{username}/enable:
 *  post:
 *    summary: ユーザーを有効化する
 *    tags: [Users]
 *    parameters:
 *      - name: username
 *        in: path
 *        description: Name of user to enable
 *        required: true
 *        schema:
 *          type: string
 *
 */
const enableUser = async (req, res) => {
  await loggerService.createLog(req);
  const username = await requireUsernameByHash(req.params.username);

  const accessHelper = await createAccessHelper(req, username);
  if (!accessHelper.canEnable()) {
    throw new AccessDenied('Not enough permissions');
  }

  await actionLogs.forUser(username).logChangeState(true)

  await getClient()
    .adminEnableUser({
      UserPoolId: getUserPoolId(),
      Username: username,
    })
    .promise();
  const user = await getClient()
    .adminGetUser({
      UserPoolId: getUserPoolId(),
      Username: username,
    })
    .promise();

  res.json({
    Enabled: true,
    UserLastModifiedDate: user.UserLastModifiedDate,
    Details: user,
  });
};

/**
 * @openapi
 * /users/{username}/disable:
 *  post:
 *    summary: ユーザーを無効化する
 *    tags: [Users]
 *    parameters:
 *      - name: username
 *        in: path
 *        description: Name of user to disable
 *        required: true
 *        schema:
 *          type: string
 *
 */
const disableUser = async (req, res) => {
  await loggerService.createLog(req);
  const username = await requireUsernameByHash(req.params.username);

  const accessHelper = await createAccessHelper(req, username);
  if (!accessHelper.canDisable()) {
    throw new AccessDenied('Not enough permissions');
  }

  await actionLogs.forUser(username).logChangeState(false)

  await getClient()
    .adminDisableUser({
      UserPoolId: getUserPoolId(),
      Username: username,
    })
    .promise();
  const user = await getClient()
    .adminGetUser({
      UserPoolId: getUserPoolId(),
      Username: username,
    })
    .promise();

  res.json({
    Enabled: false,
    UserLastModifiedDate: user.UserLastModifiedDate,
    Details: user,
  });
};

/**
 * @openapi
 * /users/{username}:
 *  delete:
 *    summary: ユーザーを削除する
 *    tags: [Users]
 *    parameters:
 *      - name: username
 *        in: path
 *        description: Name of user to delete
 *        required: true
 *        schema:
 *          type: string
 *
 */
const deleteUser = async (req, res) => {
  await loggerService.createLog(req);
  const username = await requireUsernameByHash(req.params.username);

  const accessHelper = await createAccessHelper(req, username);
  if (!accessHelper.canDelete()) {
    throw new AccessDenied('Not enough permissions');
  }

  await actionLogs.forUser(username).logDeleteUser();

  await getClient()
    .adminDeleteUser({
      UserPoolId: getUserPoolId(),
      Username: username,
    })
    .promise();

  await deleteHashUsernameMapping(req.params.username)

  res.sendStatus(200);
};

/**
 * @openapi
 * /users/{username}/password/reset:
 *  post:
 *    summary: ユーザーのパスワードをリセットする
 *    tags: [Users]
 *    parameters:
 *      - name: username
 *        in: path
 *        description: Name of user to reset
 *        required: true
 *        schema:
 *          type: string
 *
 */
const resetUserPassword = async (req, res) => {
  await loggerService.createLog(req);
  const username = await requireUsernameByHash(req.params.username);

  const accessHelper = await createAccessHelper(req, username);
  if (!accessHelper.canResetPassword()) {
    throw new AccessDenied('Not enough permissions');
  }

  await actionLogs.forUser(username).logResetPassword();

  await getClient()
    .adminResetUserPassword({
      UserPoolId: getUserPoolId(),
      Username: username,
    })
    .promise();

  res.sendStatus(200);
};

/**
 * @openapi
 * /users/{username}/groups:
 *  get:
 *    summary: ユーザーが所属する権限グループを取得する
 *    tags: [Users]
 *    parameters:
 *      - name: username
 *        in: path
 *        description: Name of user
 *        required: true
 *        schema:
 *          type: string
 *
 */
const getUserGroups = async (req, res) => {
  await loggerService.createLog(req);
  const username = await requireUsernameByHash(req.params.username);
  let NextToken = undefined;
  const groups:any[] = [];
  do {
    const response = await getClient()
      .adminListGroupsForUser({
        UserPoolId: getUserPoolId(),
        Username: username,
        Limit: 60,
        NextToken,
      })
      .promise();
    NextToken = response.NextToken;
    groups.push(...response.Groups);
  } while (NextToken);

  const ownGroups = req.user.groups;
  const isAdministrator = isMemberOfAdministratorsTeam(ownGroups);

  let listable = groups;
  if (!isAdministrator) {
    listable = [];
    if (groups.length > 0) {
      groups.map((group) => {
        let compareName = group.GroupName;
        if (compareName.includes(':')) {
          const teamId = compareName.split(':')[0];
          compareName = teamId;
        }
        const found = ownGroups.some((obj) => obj.includes(compareName));
        if (found) {
          listable.push(group);
        }
      });
    }
  }
  res.json({ Groups: listable });
};

/**
 * @openapi
 * /users/{username}/groups/{group}:
 *  post:
 *    summary: ユーザーに権限グループを付与する
 *    tags: [Users]
 *    parameters:
 *      - name: username
 *        in: path
 *        description: Name of user
 *        required: true
 *        schema:
 *          type: string
 *      - name: group
 *        in: path
 *        description: Name of group
 *        required: true
 *        schema:
 *          type: string
 *
 */
const addToGroup = async (req, res) => {
  await loggerService.createLog(req);

  const username = await requireUsernameByHash(req.params.username);
  const accessHelper = await createAccessHelper(req, username);
  if (!accessHelper.canAddGroup(req.params.group)) {
    throw new AccessDenied('Not enough permissions');
  }

  await actionLogs.forUser(username).logAddGroupMembership(req.params.group);

  await getClient()
    .adminAddUserToGroup({
      UserPoolId: getUserPoolId(),
      GroupName: req.params.group,
      Username: username,
    }).promise();

  res.sendStatus(200);
};

/**
 * @openapi
 * /users/{username}/groups/{group}:
 *  delete:
 *    summary: ユーザーに付与した権限グループを削除する
 *    tags: [Users]
 *    parameters:
 *      - name: username
 *        in: path
 *        description: Name of user
 *        required: true
 *        schema:
 *          type: string
 *      - name: group
 *        in: path
 *        description: Name of group
 *        required: true
 *        schema:
 *          type: string
 *
 */
const removeFromGroup = async (req, res) => {
  await loggerService.createLog(req);

  const username = await requireUsernameByHash(req.params.username);

  const accessHelper = await createAccessHelper(req, username);
  if (!accessHelper.canRemoveGroup(req.params.group)) {
    throw new AccessDenied('Not enough permissions');
  }

  await actionLogs.forUser(username).logRemoveGroupMembership(req.params.group);

  await getClient()
    .adminRemoveUserFromGroup({
      UserPoolId: getUserPoolId(),
      GroupName: req.params.group,
      Username: username,
    })
    .promise();

  res.sendStatus(200);
};

const search = async (req, res) => {
  await loggerService.createLog(req);
  const requestBody = req.body;
  const page:any = await filterAndGetPage(
    {
      ...requestBody.filter,
      user: requireUser(req),
    },
    requestBody.limit,
    requestBody.paginationToken
  );
  const  { paginationToken, items } = page;
  res.status(200).json({
    Users: items,
    paginationToken,
  });
};

export const routes = (app) => {
  app.get('/', g(getUserList));
  app.get('/:username', g(getUserDetail));
  app.post('/', a(createUser));
  app.post('/search', g(search));
  app.post('/:username/enable', a(enableUser));
  app.post('/:username/disable', a(disableUser));
  app.delete('/:username', a(deleteUser));
  app.post('/:username/password/reset', g(resetUserPassword));
  app.get('/:username/groups', g(getUserGroups));
  app.post('/:username/groups/:group', a(addToGroup));
  app.delete('/:username/groups/:group', a(removeFromGroup));
};
