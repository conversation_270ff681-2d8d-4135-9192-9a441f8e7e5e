/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */
import {
  MockedHandlerFactory,
  createGetEvent,
  createPutEvent,
  createPostEvent,
  AwsClientMock,
}  from '../spec/testResourcesFactory.js';
import cs from '../../common/admin-api/config/constants.js';

const { routes } = require('./custom-message.js');

jest.mock('../services/aws-clients');
import * as mockedAws from '../../common/utils/aws-clients.js';

const factory = new MockedHandlerFactory({ [cs.ADMIN_USER_POOL_ID]: '12345' });

describe('getMessageTemplates', () => {
  const objFromUser = {
    UserPoolId: '12345',
  };
  const objToAws = {
    UserPoolId: '12345',
  };
  const objFromAws = {
    UserPool: {
      AdminCreateUserConfig: {
        AllowAdminCreateUserOnly: true,
        InviteMessageTemplate: {
          EmailMessage: 'STRING_VALUE_MESSAGE',
          EmailSubject: 'STRING_VALUE_SUBJECT',
          SMSMessage: 'STRING_VALUE_SMS',
        },
        UnusedAccountValidityDays: 30,
      },
      VerificationMessageTemplate: {
        DefaultEmailOption: 'CONFIRM_WITH_CODE',
        EmailMessage: 'STRING_VALUE_CODE_MESSAGE',
        EmailMessageByLink: 'STRING_VALUE_LINK_MESSAGE',
        EmailSubject: 'STRING_VALUE_CODE_SUBJECT',
        EmailSubjectByLink: 'STRING_VALUE_LINK_SUBJECT',
        SmsMessage: 'STRING_VALUE_SMS',
      },
    },
  };
  const objToUser = {
    InviteMessageTemplate: {
      EmailMessage: 'STRING_VALUE_MESSAGE',
      EmailSubject: 'STRING_VALUE_SUBJECT',
      SMSMessage: 'STRING_VALUE_SMS',
    },
    VerificationMessageTemplate: {
      DefaultEmailOption: 'CONFIRM_WITH_CODE',
      EmailMessage: 'STRING_VALUE_CODE_MESSAGE',
      EmailMessageByLink: 'STRING_VALUE_LINK_MESSAGE',
      EmailSubject: 'STRING_VALUE_CODE_SUBJECT',
      EmailSubjectByLink: 'STRING_VALUE_LINK_SUBJECT',
      SmsMessage: 'STRING_VALUE_SMS',
    },
  };
  const fn = AwsClientMock.build(mockedAws.createCognitoIdp).method('describeUserPool').returns(objFromAws).mock();

  it('should return user pool message template (both invite and verification) json', async () => {
    const handler = factory.create('/messageTemplates', routes);
    const response = await handler(createGetEvent('/messageTemplates', objFromUser));
    expect(response.statusCode).toBe(200);
    expect(JSON.parse(response.body)).toMatchObject(objToUser);
    expect(fn).toBeCalledWith(objToAws);
  });
});

describe('updateInviteMessageTemplate', () => {
  const objFromUser = {
    UserPoolId: '12345',
    InviteMessageTemplate: {
      EmailMessage: 'STRING_VALUE_MESSAGE',
      EmailSubject: 'STRING_VALUE_SUBJECT',
      SMSMessage: 'STRING_VALUE_SMS',
    },
  };
  const objToAws = {
    UserPoolId: '12345',
    AdminCreateUserConfig: {
      AllowAdminCreateUserOnly: true,
      InviteMessageTemplate: {
        EmailMessage: 'STRING_VALUE_MESSAGE',
        EmailSubject: 'STRING_VALUE_SUBJECT',
        SMSMessage: 'STRING_VALUE_SMS',
      },
    },
  };

  it('should send update request for invitation message template', async () => {
    const fn = AwsClientMock.build(mockedAws.createCognitoIdp).method('updateUserPool').mock();
    const handler = factory.create('/updateInvite', routes);
    const response = await handler(createPostEvent('/updateInvite', objFromUser));
    expect(response.statusCode).toBe(200);
    expect(fn).toBeCalledWith(expect.objectContaining(objToAws));
  });
});

describe('updateVerificationMessageTemplate', () => {
  const objFromUser = {
    UserPoolId: '12345',
    VerificationMessageTemplate: {
      DefaultEmailOption: 'CONFIRM_WITH_CODE',
      EmailMessage: 'STRING_VALUE_CODE_MESSAGE',
      EmailMessageByLink: 'STRING_VALUE_LINK_MESSAGE',
      EmailSubject: 'STRING_VALUE_CODE_SUBJECT',
      EmailSubjectByLink: 'STRING_VALUE_LINK_SUBJECT',
      SmsMessage: 'STRING_VALUE_SMS',
    },
  };
  const objToAws = {
    UserPoolId: '12345',
    VerificationMessageTemplate: {
      DefaultEmailOption: 'CONFIRM_WITH_CODE',
      EmailMessage: 'STRING_VALUE_CODE_MESSAGE',
      EmailMessageByLink: 'STRING_VALUE_LINK_MESSAGE',
      EmailSubject: 'STRING_VALUE_CODE_SUBJECT',
      EmailSubjectByLink: 'STRING_VALUE_LINK_SUBJECT',
      SmsMessage: 'STRING_VALUE_SMS',
    },
  };

  it('should send request for verification message template update', async () => {
    const fn = AwsClientMock.build(mockedAws.createCognitoIdp).method('updateUserPool').mock();
    const handler = factory.create('/updateVerification', routes);
    const response = await handler(createPostEvent('/updateVerification', objFromUser));
    expect(response.statusCode).toBe(200);
    expect(fn).toBeCalledWith(expect.objectContaining(objToAws));
  });
});
