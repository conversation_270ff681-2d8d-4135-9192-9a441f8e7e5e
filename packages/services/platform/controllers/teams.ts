/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *  - name: Teams
 *    description: チーム管理API
 */

import * as service from '../services/teams.js';
import { createCognitoIdp } from '../../common/utils/aws-clients.js';
import config from '../../common/admin-api/config/static.js';
import c from '../../common/admin-api/config/constants.js';
import { TEAM_ROLES } from '../../common/admin-api/utils/auth.js';
import {admin, compose, guest, member} from '../../common/admin-middlewares/access-guards.js';
import { LoggerService } from '../services/logger-service.js';
const loggerService = new LoggerService('user-settings');

const m = compose(member);
const a = compose(admin);
const g = compose(guest);

const getClient = () => {
  return createCognitoIdp();
};

const getUserPoolId = () => {
  return config.get(c.ADMIN_USER_POOL_ID);
};

/**
 * @openapi
 * /teams:
 *  get:
 *    summary: チーム一覧を返す
 *    tags: [Teams]
 */
const getTeamList = async (req, res) => {
  await loggerService.createLog(req);
  const groups = req.user.groups;
  const listed = await service.getTeamList(groups);
  res.json(listed);
};

/**
 * @openapi
 * /teams/{teamId}:
 *  get:
 *    summary: チーム詳細を返す
 *    tags: [Teams]
 *    parameters:
 *      - name: teamId
 *        in: path
 *        description: Team ID
 *        required: true
 *        schema:
 *          type: string
 */
const getTeamDetail = async (req, res) => {
  await loggerService.createLog(req);
  const teamId = req.params.teamId;
  const details = await service.getTeamDetail(teamId);
  res.json(details[0]);
};

/**
 * @openapi
 * /teams:
 *  post:
 *    summary: チームを作成する
 *    tags: [Teams]
 *    requestBody:
 *      required: true
 *      content:
 *        application/json:
 *          schema:
 */
const createTeam = async (req, res) => {
  await loggerService.createLog(req);
  const params = req.body;
  const created = await service.putTeam(params);

  if (created) {
    await Promise.all(
      TEAM_ROLES.map(async (group) => {
        await getClient()
          .createGroup({
            GroupName: created.item.teamId + ':' + group,
            UserPoolId: getUserPoolId(),
          })
          .promise();
      })
    );
  }

  res.json(created);
};

/**
 * @openapi
 * /teams/{teamId}:
 *  put:
 *    summary: チームを更新する
 *    tags: [Teams]
 *    parameters:
 *      - name: teamId
 *        in: path
 *        description: Team ID
 *        required: true
 *        schema:
 *          type: string
 *    requestBody:
 *      required: true
 *      content:
 *        application/json:
 *          schema:
 */
const updateTeam = async (req, res) => {
  await loggerService.createLog(req);
  const teamId = req.params.teamId;
  const params = { ...{ teamId: teamId }, ...req.body };
  const updated = await service.putTeam(params);
  res.json(updated);
};

/**
 * @openapi
 * /teams/{teamId}:
 *  delete:
 *    summary: チームを削除する
 *    tags: [Teams]
 *    parameters:
 *      - name: teamId
 *        in: path
 *        description: Team ID
 *        required: true
 *        schema:
 *          type: string
 */
const deleteTeam = async (req, res) => {
  await loggerService.createLog(req);
  const teamId = req.params.teamId;
  const deleted = await service.deleteTeam(teamId);

  if (deleted) {
    const groupDeleteTask = async (roleName) => {
      try {
        await getClient()
          .deleteGroup({
            GroupName: `${teamId}:${roleName}`,
            UserPoolId: getUserPoolId(),
          })
          .promise();
      } catch (e: any) {
        if (e.code === 'ResourceNotFoundException') {
          // ignore
        } else {
          throw e;
        }
      }
    };

    await Promise.all(TEAM_ROLES.map(groupDeleteTask));
  }

  res.json(deleted);
};

/**
 * @openapi
 * /teams/own:
 *  get:
 *    summary: 自身が保持する権限を返す
 *    tags: [Teams]
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 */
const getOwnPermissions = async (req, res) => {
  await loggerService.createLog(req);
  const groups = req.user.groups;
  const own = await service.getOwnPermissions(groups);
  res.json(own);
};

export const routes = (app) => {
  app.get('/', getTeamList);
  app.get('/:teamId', getTeamDetail);
  app.post('/', createTeam);
  app.put('/:teamId', updateTeam);
  app.delete('/:teamId', deleteTeam);
  app.get('/own', getOwnPermissions);
};
