/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *  - name: CustomMessage
 *    description: メッセージテンプレート管理API
 */

import { createCognitoIdp } from '../../common/utils/aws-clients.js';
import config from '../../common/admin-api/config/static.js';
import c from '../../common/admin-api/config/constants.js';
import _ from 'lodash';
import { member as m, guest as g } from '../../common/admin-middlewares/access-guards.js';
import { ConfigurationMiss, BadRequest, ServerError } from '../../common/admin-api/utils/exceptions.js';


const w = (handler) => {
  return async (req, res) => {
    try {
      return await handler(req, res);
    } catch (e: any) {
      if (e.code === 'ResourceNotFoundException') {
        throw new ConfigurationMiss({ cause: e, code: e.code });
      } else if (e.constructor.name === 'NotFound') {
        throw new BadRequest({ cause: e, code: e.code });
      } else {
        throw new ServerError({ cause: e });
      }
    }
  };
};

const getClient = () => {
  return createCognitoIdp();
};
const getUserPoolId = () => {
  return config.get(c.ADMIN_USER_POOL_ID);
};

/**
 * HTML タグを除去する
 * 
 * @param input 
 * @returns 
 */
const stripHtmlTags = (input: string): string => input.replace(/<\/?[^>]+(>|$)/g, "");

/**
 * @openapi
 * /custom-message:
 *  post:
 *    summary: 各種システムからのメッセージを更新する
 *    tags: [CustomMessage]
 */
const updateMessageTemplates = w(async (req, res) => {

  // InviteMessageTemplate の EmailSubject から HTML タグを除去
  if (req.body.InviteMessageTemplate?.EmailSubject) {
    req.body.InviteMessageTemplate.EmailSubject = stripHtmlTags(req.body.InviteMessageTemplate.EmailSubject);
  }

  await getClient()
    .updateUserPool({
      UserPoolId: getUserPoolId(),
      AutoVerifiedAttributes: req.body.AutoVerifiedAttributes,
      AdminCreateUserConfig: {
        AllowAdminCreateUserOnly: true,
        InviteMessageTemplate: req.body.InviteMessageTemplate,
      },
      VerificationMessageTemplate: req.body.VerificationMessageTemplate,
    })
    .promise();

  res.sendStatus(200);
});

/**
 * @openapi
 * /custom-message:
 *  get:
 *    summary: 各種システムからのメッセージを取得する
 *    tags: [CustomMessage]
 */
const getMessageTemplates = w(async (req, res) => {
  const response = await getClient()
    .describeUserPool({
      UserPoolId: getUserPoolId(),
    })
    .promise();

  const autoVerifiedAttributes = _.get(response, 'UserPool.AutoVerifiedAttributes');
  const inviteMessageTemplate = _.get(response, 'UserPool.AdminCreateUserConfig.InviteMessageTemplate');
  const verificationMessageTemplate = _.get(response, 'UserPool.VerificationMessageTemplate');

  res.json({
    AutoVerifiedAttributes: autoVerifiedAttributes,
    InviteMessageTemplate: inviteMessageTemplate,
    VerificationMessageTemplate: verificationMessageTemplate,
  });
});

export const routes = (app) => {
  app.get('/', g(getMessageTemplates));
  app.post('/', m(updateMessageTemplates));
};
