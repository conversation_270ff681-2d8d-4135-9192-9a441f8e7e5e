/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *  - name: Lambda
 *    description: Lambda関数管理API
 */

import AWS from 'aws-sdk';
import { admin as a, guest as g } from '../../common/admin-middlewares/access-guards.js';

const getClient = () => {
  return new aws.Lambda();
};

/**
 * @openapi
 * components:
 *   schemas:
 *     lambda:
 *       type: "object"
 *       properties:
 *         Name:
 *           type: "string"
 *         LastModified:
 *           type: "string"
 *         Environment:
 *           type: "object"
 */

const mapFunctionConfiguration = (f) => {
  let variables = {};
  if (f.Environment && f.Environment.Variables) {
    variables = f.Environment.Variables;
  }
  return {
    Name: f.FunctionName,
    LastModified: f.LastModified,
    Environment: variables,
  };
};

/**
 * @openapi
 * /lambda:
 *  get:
 *    summary: Lambda関数の詳細を返す
 *    tags: [Lambda]
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "array"
 *              items:
 *                $ref: '#/components/schemas/lambda'
 */
const list = async (req, res) => {
  const response = await getClient()
    .listFunctions({
      FunctionVersion: 'ALL',
      MaxItems: 60,
    })
    .promise();
  res.json(response.Functions.map(mapFunctionConfiguration));
};

/**
 * @openapi
 * /lambda/{lambdaName}:
 *  get:
 *    summary: Lambda関数一覧を返す
 *    tags: [Lambda]
 *    parameters:
 *      - name: lambdaName
 *        in: path
 *        description: Name of Lambda
 *        required: true
 *        schema:
 *          type: string
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              $ref: '#/components/schemas/lambda'
 */
const get = async (req, res) => {
  const response = await getClient()
    .getFunctionConfiguration({
      FunctionName: req.params.name,
    })
    .promise();
  res.json(mapFunctionConfiguration(response));
};

/**
 * @openapi
 * /lambda/{lambdaName}/variables:
 *  get:
 *    summary: Lambda関数の環境変数を更新する
 *    tags: [Lambda]
 *    parameters:
 *      - name: lambdaName
 *        in: path
 *        description: Name of Lambda
 *        required: true
 *        schema:
 *          type: string
 *    requestBody:
 *      required: true
 *      content:
 *        application/json:
 *          schema:
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              $ref: '#/components/schemas/lambda'
 */
const update = async (req, res) => {
  const currentFunction = await getClient()
    .getFunctionConfiguration({
      FunctionName: req.params.name,
    })
    .promise();
  let currentVariables = {};
  if (currentFunction.Environment && currentFunction.Environment.Variables) {
    currentVariables = currentFunction.Environment.Variables;
  }

  const newValues = req.body;

  const newVariables = {
    ...currentVariables,
    ...newValues,
  };

  const response = await getClient()
    .updateFunctionConfiguration({
      FunctionName: req.params.name,
      Environment: {
        Variables: newVariables,
      },
    })
    .promise();

  res.json(mapFunctionConfiguration(response));
};

export const routes = (app) => {
  app.get('/', g(list));
  app.get('/:name', g(get));
  app.put('/:name/variables', a(update));
};
