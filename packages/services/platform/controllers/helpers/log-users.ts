/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { requestContext } from '../../../common/admin-api/utils/execution-context.js';

import send from '../../services/action-logs/sender.js';
import { LoggerConfigException, wrapSafe } from '../../services/action-logs/utils.js';
import { getTeamDetail } from '../../services/teams.js';
import { getUserEmail } from '../../services/users.js';

class UsersActionLogger {
  private user: any;
  private info: any;
  private userInfoLoader: any;
  private request: any;
  private actor: any;
  constructor(user, email) {
    this.user = user;
    this.info = {
      name: user,
      email: email,
      group: '',
    };
  }

  setUserInfoLoader(loader) {
    this.userInfoLoader = loader;
  }

  setRequest(request) {
    this.request = request;
  }

  setActor(actor) {
    this.actor = actor;
  }

  async preLoadInfo(group = '') {
    this.info = await this.userInfoLoader(this.user, group);
    if (!this.info) {
      throw new LoggerConfigException('Could not find any info about user ' + this.user);
    }
  }

  async doLogAction(name, { parameterMain, type, parameter1, parameter2, parameter3, parameter4 } : {
    parameterMain?, type?, parameter1?, parameter2?, parameter3?, parameter4?, parameter5?
  }) {
    const user = this.actor;
    const endpoint = `${this.request.method} ${this.request.path}`;
    await send(name, endpoint, {
      user,
      parameterMain,
      type,
      parameter1,
      parameter2,
      parameter3,
      parameter4,
    });
  }

  async logCreateUser() {
    await wrapSafe(async () => {
      await this.doLogAction('ユーザ:新規追加', {
        type: 'ユーザー設定',
        parameter1: this.info.name,
        parameter2: this.info.email,
        parameter3: this.info.group,
      });
    });
  }

  async logGetUser() {
    await wrapSafe(async () => {
      await this.preLoadInfo();
      await this.doLogAction('ユーザ:承認ステータス変更', {
        parameterMain: this.info.name,
        parameter1: this.info.email,
      });
    });
  }

  async logChangeState(enabled) {
    await wrapSafe(async () => {
      await this.preLoadInfo();
      await this.doLogAction(enabled ? 'ユーザ:アカウント有効化' : 'ユーザ:アカウント無効化', {
        type: 'ユーザー設定',
        parameter1: this.info.name,
        parameter2: this.info.email,
      });
    });
  }

  async logAddGroupMembership(group) {
    await wrapSafe(async () => {
      await this.preLoadInfo(group);
      const isAdmin = group === 'Administrator:admins';
      if (isAdmin) {
        await this.doLogAction('ユーザ:チーム追加(管理者)', {
          type: 'ユーザー設定',
          parameter1: this.info.name,
        });
      } else {
        await this.doLogAction('ユーザ:チーム追加', {
          type: 'ユーザー設定',
          parameter1: this.info.name,
          parameter2: this.info.email,
          parameter3: this.info.group,
        });
      }
    });
  }

  async logRemoveGroupMembership(group) {
    await wrapSafe(async () => {
      await this.preLoadInfo(group);
      const isAdmin = group === 'Administrator:admins';
      if (isAdmin) {
        await this.doLogAction('ユーザ:チーム退会(管理者)', {
          type: 'ユーザー設定',
          parameter1: this.info.name,
          parameter2: this.info.email,
          parameter3: this.info.group,
        });
      } else {
        await this.doLogAction('ユーザ:チーム退会', {
          type: 'ユーザー設定',
          parameter1: this.info.name,
          parameter2: this.info.email,
          parameter3: this.info.group,
        });
      }
    });
  }

  async logDeleteUser() {
    await wrapSafe(async () => {
      await this.preLoadInfo();
      await this.doLogAction('ユーザ:アカウント削除', {
        type: 'ユーザー設定',
        parameter1: this.info.name,
        parameter2: this.info.email,
      });
    });
  }

  async logResetPassword() {
    await wrapSafe(async () => {
      await this.preLoadInfo();
      await this.doLogAction('ユーザ:パスワードリセット', {
        type: 'ユーザー設定',
        parameter1: this.info.name,
        parameter2: this.info.email,
      });
    });
  }
}

const userInfoLoader = async (userId, groupId = '') => {
  const email = await getUserEmail(userId);
  let groupName = '';
  if (groupId) {
    const [teamId, kengen] = groupId.split(':');
    const team = await getTeamDetail(teamId);
    if (team) {
      groupName = `${team.teamName}:${kengen}`;
    }
  }
  return {
    name: userId,
    email,
    group: groupName,
  };
};

const createForUser = (userId, email = '') => {
  const logger = new UsersActionLogger(userId, email);
  logger.setActor(requestContext.userId);
  logger.setRequest(requestContext.currentRequest);
  logger.setUserInfoLoader(userInfoLoader);
  return logger;
};

export const getEmailFromRequest = (request) => {
  const attrs = request.UserAttributes || [];
  return attrs.filter((r) => r.Name === 'email').map((r) => r.Value)[0];
}

export {
  createForUser as forUser,
};
