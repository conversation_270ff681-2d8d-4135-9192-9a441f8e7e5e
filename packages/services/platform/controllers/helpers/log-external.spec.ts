/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import * as actionLogs from './log-external.js';
import { requestContext } from '../../../common/admin-api/utils/execution-context.js';

describe('log-external', () => {
  beforeAll(() => {
    requestContext.reset();
    requestContext.userId = 'testUser';
  });
  it('create can create base instance', () => {
    const log = actionLogs.create();
    expect(log.userId).toEqual(requestContext.userId)
  });
  it('isExternalActionLogData validate collect log format', () => {
    expect(actionLogs.isExternalActionLogData({
      type: 'download-system-log',
      path: '/test-path1',
      method: 'GET',
    })).toBeTruthy();

    expect(actionLogs.isExternalActionLogData({
      type: 'invalid-type',
      path: '/test-path1',
      method: 'POST',
    })).toBeFalsy();

    expect(actionLogs.isExternalActionLogData({
      type: 'download-system-log',
      path: '/test-path1',
      method: 'invalid',
    })).toBeFalsy();

    expect(actionLogs.isExternalActionLogData({
      path: '/test-path1',
      method: 'POST',
    })).toBeFalsy();

    expect(actionLogs.isExternalActionLogData({
      type: 'download-system-log',
      method: 'POST',
    })).toBeFalsy();

    expect(actionLogs.isExternalActionLogData({
      type: 'invalid-type',
      path: '/test-path1',
    })).toBeFalsy();
  });
  it('will call send if collect data format', () => {
    const mockSend = jest.fn(async (name: any, path: any, { user, parameterMain, type, parameter1, parameter2, parameter3, parameter4, parameter5 }: {
      user?: any;
      parameterMain?: any;
      type?: any;
      parameter1?: any;
      parameter2?: any;
      parameter3?: any;
      parameter4?: any;
      parameter5?: any;
    }, ...rest: any[]) => { })

    const collectFormatData = {
      type: 'download-system-log',
      path: '/test-path1',
      method: 'POST',
      params: {
        parameter1: 'ファイル名:test'
      }
    }
    const log = actionLogs.create();
    log.injectMockSend(mockSend);
    log.trySaveItem(collectFormatData);
    const { name, type } = log.mapLogTypeToLabel();
    expect(mockSend).toHaveBeenCalled();
    expect(mockSend.mock.calls[0][0]).toEqual(name);
    expect(mockSend.mock.calls[0][1]).toEqual(`${collectFormatData.method} ${collectFormatData.path}`);
    expect(mockSend.mock.calls[0][2]['user']).toEqual(requestContext.userId);
    expect(mockSend.mock.calls[0][2]['type']).toEqual(type);
    expect(mockSend.mock.calls[0][2]['parameter1']).toEqual(collectFormatData.params.parameter1);

    mockSend.mockClear();
    const invalidFormatData = {
      type: 'invalid',
    };
    log.trySaveItem(invalidFormatData);
    expect(mockSend.mock.calls.length).toBe(0)
  })
});
