/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import _ from 'lodash';
import send from '../../services/action-logs/sender.js';
import { requestContext } from '../../../common/admin-api/utils/execution-context.js';
import { wrapSafe } from '../../services/action-logs/utils.js';
import { getBySurveyId } from '../../../api-admin-survey/services/survey.js';
import isObjectsEqual from 'deep-equal';
import { loadCategoriesTree } from '../../../api-admin-survey/services/calendars-categories.js';
import { getTeamList } from '../../services/teams.js';

const DEFAULT_END_OF_SURVEY_MESSAGE = 'ありがとうございます。\nご登録を承りました。';
const autoReplyMessageTypes = {
  1: '予約内容を全て送信（管理項目は除く）',
  2: 'インデックスキー＋分類＋予約日時を送信',
  3: '予約内容を送信しない',
};

class SurveyConfigsActionLogger {
  private config: any;
  private request: any;
  private _userId: any;
  constructor(config) {
    this.config = config;
  }

  setRequest(request) {
    this.request = request;
  }

  set userId(user) {
    this._userId = user;
  }

  get userId() {
    return this._userId;
  }

  formatSchema() {
    return this.config.surveySchema.map(this.__formatItem).join('\n');
  }

  formatCanViewTeams() {
    return (this.config.surveyTeams || [])
      .filter((t) => t.teamId !== 'Administrator')
      .map((t) => t.teamName)
      .join(',');
  }

  flattenObject(obj, path = []) {
    let result = {};
    if (Array.isArray(obj)) {
      obj.forEach((o, i) => {
        result = { ...result, ...this.flattenObject(o, [...path, i.toString()]) };
      });
    } else if (_.isObject(obj)) {
      Object.keys(obj).forEach((key) => {
        result = { ...result, ...this.flattenObject(obj[key], [...path, key]) };
      });
    } else {
      result[path.join('.')] = obj;
    }
    return result;
  }

  categoryTree(obj) {
    const flattenObj = this.flattenObject(obj);
    const result = {};
    Object.keys(flattenObj)
      .filter((key) => {
        const value = flattenObj[key];
        return _.isString(value) && value.startsWith('category#');
      })
      .forEach((key) => {
        const paths:any[] = [];
        const pathElem = _.split(key, '.') || [];

        let workingObj = obj;
        pathElem.forEach((elem) => {
          workingObj = _.get(workingObj, elem);
          if (workingObj.name) {
            paths.push(workingObj.name);
          }
          if (workingObj.id) {
            result[workingObj.id] = paths.join('>');
          }
        });
      });
    return result;
  }

  async formatCanViewCategories() {
    const teamList = await getTeamList([], true);
    const teams = teamList.reduce((obj, team) => {
      const { teamId, teamName } = team;
      obj[teamId] = teamName;
      return obj;
    }, {});

    const permissions = this.config.categoriesPermissions || [];
    let categories;
    if (permissions.length > 0) {
      const { tree } = await loadCategoriesTree();
      categories = this.categoryTree(tree);
    }

    const results:any[] = [];
    for (const permission of permissions) {
      const { list } = permission;
      for (const team of list) {
        const { categoryId, teamIds } = team;
        const teamNames = (teamIds || []).map((teamId) => teams[teamId]);
        results.push(
          `${categories[categoryId]}: ${teamNames.join(',') || '-'}`
        );
      }
    }

    return results.join('\n');
  }

  formatOptions() {
    const isDeleteAllEnabled = _.get(this.config, 'isDeleteAllEnabled.value');
    const isEditDisabled = _.get(this.config, 'isEditDisabled.value');
    const isAppending = _.get(this.config, 'isAppending.value');
    const isSearchable = _.get(this.config, 'isSearchable.value');
    const surveyType = _.get(this.config, 'surveyType');
    let endOfSurveyMessage = _.get(this.config, 'endOfSurveyMessage');
    const endOfSurveyMessageType = _.get(this.config, 'endOfSurveyMessageType');
    const deliveryMessageSetting = _.get(this.config, 'deliveryMessageSetting');

    const result:any[] = [];
    if (isDeleteAllEnabled) {
      result.push(`全件削除許可: ${isDeleteAllEnabled}`);
    }
    if (isEditDisabled) {
      result.push(`編集・予約キャンセル無効: ${isEditDisabled}`);
    }
    if (isAppending) {
      result.push(`追加型: ${isAppending}`);
    }
    if (isSearchable) {
      result.push(`参照・変更帳票: ${isSearchable}`);
    }
    if (surveyType === 'corona' && this.config.endOfSurveyMessage === undefined) {
      endOfSurveyMessage = DEFAULT_END_OF_SURVEY_MESSAGE;
    } else if (endOfSurveyMessage === '') {
      endOfSurveyMessage = '-';
    }
    result.push(`メッセージ送信設定: ${endOfSurveyMessage}`);
    if (surveyType === 'corona') {
      result.push(`メッセージ送信種別: ${autoReplyMessageTypes[endOfSurveyMessageType] || autoReplyMessageTypes[1]}`);
    }
    if (deliveryMessageSetting) {
      result.push(
        `配信メッセージ定型文: ${deliveryMessageSetting.length > 0 ? JSON.stringify(deliveryMessageSetting) : '-'}`
      );
    }

    return result.join('\n');
  }

  __formatItem(item) {
    return `${item.itemKey} '${item.title}' (${item.type})`;
  }

  async countDifference() {
    const current:any = await getBySurveyId(this.config.surveyId);
    const addedItems:any[] = [];
    const removedItems:any[] = [];
    const changedItems:any[] = [];

    const surveyType = _.get(this.config, 'surveyType');
    const endOfSurveyMessageType = _.get(this.config, 'endOfSurveyMessageType');

    if (current.endOfSurveyMessage !== this.config.endOfSurveyMessage) {
      changedItems.push(`メッセージ送信設定: ${this.config.endOfSurveyMessage}`);
    }
    if (surveyType === 'corona' && current.endOfSurveyMessageType !== this.config.endOfSurveyMessageType) {
      changedItems.push(
        `メッセージ送信種別: ${autoReplyMessageTypes[endOfSurveyMessageType] || autoReplyMessageTypes[1]}`
      );
    }

    if (current.deliveryMessageSetting !== this.config.deliveryMessageSetting) {
      // NOTE: 一意に特定するキーが無いので、JSONにして全部出力する
      changedItems.push(
        `配信メッセージ定型文: ${
          this.config.deliveryMessageSetting && this.config.deliveryMessageSetting.length > 0
            ? JSON.stringify(this.config.deliveryMessageSetting)
            : '-'
        }`
      );
    }

    current.surveySchema.forEach((oldItem) => {
      const newItem = this.config.surveySchema.find((i) => i.itemKey === oldItem.itemKey);
      if (!newItem) {
        removedItems.push(this.__formatItem(oldItem));
        return;
      }
      if (!isObjectsEqual(newItem, oldItem)) {
        changedItems.push(this.__formatItem(newItem));
      }
    });

    const oldKeys = current.surveySchema.map((i) => i.itemKey);
    this.config.surveySchema
      .filter((i) => !oldKeys.includes(i.itemKey))
      .map((i) => this.__formatItem(i))
      .forEach((i) => addedItems.push(i));

    const result:any[] = [];
    if (addedItems.length > 0) {
      result.push(`追加: \n${addedItems.join('\n')}`);
    }
    if (changedItems.length > 0) {
      if (result.length > 0) {
        result.push('\n');
      }
      result.push(`更新: \n${changedItems.join('\n')}`);
    }
    if (removedItems.length > 0) {
      if (result.length > 0) {
        result.push('\n');
      }
      result.push(`削除: \n${removedItems.join('\n')}`);
    }
    return result.join('\n\n');
  }

  async doLogAction(name, { parameterMain, type, parameter1, parameter2, parameter3, parameter4, parameter5 } : {
    parameterMain?, type?, parameter1?, parameter2?, parameter3?, parameter4?, parameter5?
  }) {
    const user = this.userId;
    const endpoint = `${this.request.method} ${this.request.path}`;
    await send(name, endpoint, {
      user,
      parameterMain,
      type,
      parameter1,
      parameter2,
      parameter3,
      parameter4,
      parameter5,
    });
  }

  async doLogCreate(result) {
    await wrapSafe(async () => {
      await this.doLogAction('新規追加', {
        type: '帳票作成',
        parameter1: result.surveyId,
        parameter2: this.config.surveyTitle,
        parameter3: this.formatOptions(),
        parameter4: this.formatSchema(),
        parameter5: `帳票閲覧権限: ${this.formatCanViewTeams() || '-'}${
          this.config.categoriesPermissions ? '\n分類閲覧権限:\n' + (await this.formatCanViewCategories()) : '-'
        }`,
      });
    });
  }

  async doLogUpdate() {
    await wrapSafe(async () => {
      await this.doLogAction('更新', {
        type: '帳票作成',
        parameter1: this.config.surveyId,
        parameter2: this.config.surveyTitle,
        parameter3: await this.countDifference(),
        parameter4: `帳票閲覧権限: ${this.formatCanViewTeams() || '-'}${
          this.config.categoriesPermissions ? '\n分類閲覧権限:\n' + (await this.formatCanViewCategories()) : '-'
        }`,
      });
    });
  }
}

const loggerForConfig = (config) => {
  const logger = new SurveyConfigsActionLogger(config);
  logger.setRequest(requestContext.currentRequest);
  logger.userId = requestContext.userId;
  return logger;
};

export {
  loggerForConfig as forConfig,
};
