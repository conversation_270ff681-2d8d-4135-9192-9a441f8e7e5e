/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */


import { requestContext } from '../../../common/admin-api/utils/execution-context.js';
import send from '../../services/action-logs/sender.js';
import {lambdaEnv} from "../../../common/lambda-env.js";


const doLogAction = async (name, user, endpoint, body) => {
  await send(name, endpoint, {
    ...body,
    user,
  });
}

const recordCurrentRequest = async (actionName, parameters = null) => {
  const request = requestContext.currentRequest;
  if (!request) {
    return;
  }
  const user = requestContext.userId;
  const endpoint = `${request.method} ${request.path}`;

  let logParameters = parameters;
  if (!logParameters) {
    const requestBody = request.body;
    if (typeof requestBody !== 'string') {
      logParameters = { parameterMain: JSON.stringify(requestBody) };
    }
  } else {
    Object.keys(logParameters).forEach((key) => {
      if (typeof logParameters[key] !== 'string') {
        logParameters[key] = JSON.stringify(logParameters[key]);
      }
    });
  }

  await doLogAction(actionName, user, endpoint, logParameters);
}

export default recordCurrentRequest;
