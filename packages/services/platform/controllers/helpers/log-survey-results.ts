/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import _ from 'lodash';
import send from '../../services/action-logs/sender.js';
import { requestContext } from '../../../common/admin-api/utils/execution-context.js';
import { LoggerConfigException, wrapSafe } from '../../services/action-logs/utils.js';
import { SurveyConfigModel } from '../../../api-admin-survey/services/survey-results/search/survey/config_model.js';
import { SurveyResult } from '../../../api-admin-survey/services/survey-results/search/survey/results_model.js';
import { getBySurveyId } from '../../../api-admin-survey/services/survey.js';

class SurveyResultsActionLogger {
  private userId: any;
  private surveyResults: any;
  private configLoader: any;
  private request: any;
  private surveyConfigId: any;
  private surveyConfig: any;
  constructor() {}

  setResults(surveyResults) {
    this.surveyResults = surveyResults;
  }

  setConfigLoader(configLoader) {
    this.configLoader = configLoader;
  }

  setRequest(request) {
    this.request = request;
  }

  setSurveyConfigId(id) {
    this.surveyConfigId = id;
  }

  setUserId(userId) {
    this.userId = userId;
  }

  getSurveyId() {
    if (this.surveyConfigId) {
      return this.surveyConfigId;
    }
    if (this.surveyResults && this.surveyResults[0]) {
      if (this.surveyResults[0].surveyId) {
        return this.surveyResults[0].surveyId;
      }
      if (this.surveyResults[0].partitionKey) {
        return this.surveyResults[0].partitionKey.split('#')[0];
      }
    }
    throw new LoggerConfigException('No survey config id');
  }

  async loadSurveyConfig() {
    if (this.surveyConfig) {
      return this.surveyConfig;
    }
    const surveyId = this.getSurveyId();
    const config = await this.configLoader(surveyId);
    if (!config) {
      throw new LoggerConfigException(`No survey config with id = ${surveyId}`);
    }
    this.surveyConfig = config;
    return config;
  }

  getResultsPk() {
    if (this.surveyResults && this.surveyResults[0]) {
      return this.surveyResults[0].partitionKey;
    }
    throw new LoggerConfigException('No survey results');
  }

  async doLogAction(name, { parameterMain, type, parameter1, parameter2, parameter3, parameter4, parameter5 } : {
    parameterMain?, type?, parameter1?, parameter2?, parameter3?, parameter4?, parameter5?
  }) {
    const user = this.userId;
    const endpoint = `${this.request.method} ${this.request.path}`;
    await send(name, endpoint, {
      user,
      parameterMain,
      type,
      parameter1,
      parameter2,
      parameter3,
      parameter4,
      parameter5,
    });
  }

  makeSurveyResultsValuesReport() {
    const results = new SurveyResult(this.surveyResults, this.surveyConfig);
    const config = new SurveyConfigModel(this.surveyConfig);
    const lines:any[] = [];
    const sortedAnswers = results.getLinedAnswers();
    sortedAnswers.forEach((answer) => {
      const fieldConfig = config.getField(answer.itemKey);
      if (!fieldConfig) {
        return;
      }
      const values = results.getValue(answer.itemKey) || '';
      const valuesStr = _.isArray(values) ? values.join(',') : values;
      lines.push(`${fieldConfig.title}: ${valuesStr}`);
    });
    if (sortedAnswers.length > 0 && sortedAnswers[0].note !== undefined) {
      // NOTE: 管理者メモは全ての回答に同じ値が入っている
      lines.push(`管理者メモ: ${sortedAnswers[0].note || '-'}`);
    }
    return lines.join('\n');
  }

  printRequestBody() {
    const body = this.request.body;
    return JSON.stringify(body, null, 2);
  }

  async logPutItem() {
    await wrapSafe(async () => {
      await this.loadSurveyConfig();
      await this.doLogAction('新規追加', {
        type: 'ホーム',
        parameter1: this.surveyConfig.surveyTitle,
        parameter2: this.getResultsPk(),
        parameter3: this.makeSurveyResultsValuesReport(),
      });
    });
  }

  async logDeleteItem() {
    await wrapSafe(async () => {
      await this.loadSurveyConfig();
      await this.doLogAction('選択削除', {
        type: 'ホーム',
        parameter1: this.surveyConfig.surveyTitle,
        parameter2: this.surveyResults.map((result) => result.partitionKey).join('\n'),
        parameter3: JSON.stringify(this.surveyResults),
      });
    });
  }

  async logUpdateItem() {
    await wrapSafe(async () => {
      await this.loadSurveyConfig();
      await this.doLogAction('更新', {
        type: 'ホーム',
        parameter1: this.surveyConfig.surveyTitle,
        parameter2: this.getResultsPk(),
        parameter3: this.makeSurveyResultsValuesReport(),
      });
    });
  }

  async logFindItems() {
    await wrapSafe(async () => {
      await this.loadSurveyConfig();
      await this.doLogAction('閲覧(帳票)', {
        type: 'ホーム',
        parameter1: this.surveyConfig.surveyTitle,
      });
    });
  }

  async logResetUserId(partitionKey) {
    await wrapSafe(async () => {
      await this.loadSurveyConfig();
      await this.doLogAction('ユーザーIDリセット', {
        type: 'ホーム',
        parameter1: this.surveyConfig.surveyTitle,
        parameter2: partitionKey,
      });
    });
  }

  async logCreateCsv(pks) {
    await wrapSafe(async () => {
      await this.loadSurveyConfig();
      await this.doLogAction('CSVエクスポート', {
        type: 'ホーム',
        parameter1: this.surveyConfig.surveyTitle,
        parameter2: pks.join('\n'),
      });
    });
  }

  async logCreateCsvAppending(appendingData) {
    await wrapSafe(async () => {
      await this.loadSurveyConfig();
      await this.doLogAction('CSVエクスポート', {
        type: 'ホーム',
        parameter1: this.surveyConfig.surveyTitle,
        parameter2: appendingData.map((data) => data.partitionKey).join('\n'),
      });
    });
  }

  async logAsyncCsvAppendingInvoker(filename) {
    await wrapSafe(async () => {
      await this.loadSurveyConfig();
      await this.doLogAction('CSVインポート', {
        type: 'ホーム',
        parameter1: this.surveyConfig.surveyTitle,
        parameter2: filename,
      });
    });
  }

  async logAsyncMultipleUnicastInvoker(body, status) {
    await wrapSafe(async () => {
      await this.loadSurveyConfig();
      await this.doLogAction('一括LINEユニキャスト配信', {
        type: 'ホーム',
        parameter1: this.surveyConfig.surveyTitle,
        parameter2: status.partitionKey,
        parameter3: body.title,
        parameter4: body.users ? body.users.map((user) => user.id).join('\n') : JSON.stringify(body.searchCondition),
        parameter5: body.messages.map((message) => message.text).join('\n----\n'),
      });
    });
  }

  async logHomeNotificationData() {
    await wrapSafe(async () => {
      await this.loadSurveyConfig();
      await this.doLogAction('通知情報（エクスポート）', {
        type: 'ホーム',
        parameter1: this.surveyConfig.surveyTitle,
      });
    });
  }

  async logDownloadHistoryLogs(partitionKey) {
    await wrapSafe(async () => {
      await this.loadSurveyConfig();
      await this.doLogAction('帳票操作ログダウンロード', {
        type: 'ホーム',
        parameter1: this.surveyConfig.surveyTitle,
        parameter2: partitionKey
      });
    });
  }
}

const createActionLoggerForConfig = (surveyConfigId) => {
  const logger = new SurveyResultsActionLogger();
  logger.setSurveyConfigId(surveyConfigId);
  logger.setRequest(requestContext.currentRequest);
  logger.setUserId(requestContext.userId);
  logger.setConfigLoader(getBySurveyId);
  return logger;
};

const createActionLoggerForResults = (surveyResults) => {
  const logger = new SurveyResultsActionLogger();
  logger.setResults(surveyResults);
  logger.setRequest(requestContext.currentRequest);
  logger.setUserId(requestContext.userId);
  logger.setConfigLoader(getBySurveyId);
  return logger;
};

export {
  createActionLoggerForResults as forResults,
  createActionLoggerForConfig as forConfig,
};
