/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import createApp from '../createApp.js';
import { compose } from '../../common/admin-api/config/config.js';
import * as awsHelpers from '../../common/admin-api/utils/aws-helper.js';
import { User } from '../../common/admin-api/utils/auth.js';
import ApiBuilder from 'lambda-api';
import * as errors from '../../common/admin-middlewares/errors.js';
import config from '../../common/admin-api/config/static.js';

class HandlerFactory {
  private profileName: any;
  private configs: any;
  constructor(profileName, ...configs) {
    this.profileName = profileName;
    this.configs = compose(...configs);
  }

  create(...configs) {
    return async (event, context:any = {}) => {
      awsHelpers.initializeAwsLocal(this.profileName);
      const app = await createApp(compose(this.configs, ...configs));
      return await app.run(event, context);
    };
  }
}

class MockedHandlerFactory {
  private configs: any;
  private user: any;
  constructor(...configs) {
    this.configs = compose(...configs);
    this.user = null;
  }

  authenticate(username, groups = []) {
    this.user = {
      username,
      groups,
    };
  }

  create(path, controller) {
    const staticConfig = config;
    staticConfig.merge(this.configs);
    return async (event, context:any = {}) => {
      const api = ApiBuilder();
      if (this.user) {
        api.use((req, res, next) => {
          req.user = new User(this.user.username, this.user.groups);
          next();
        });
      }
      api.use(errors.defaultHandler);
      api.register(controller, { prefix: path });
      return await api.run(event, context);
    };
  }
}

const handlerFactory = (awsProfile, ...configs) => {
  return new HandlerFactory(awsProfile, ...configs);
};

const createGetEvent = (path, query = null) => {
  return {
    path: path,
    httpMethod: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    queryStringParameters: query,
  };
};

const createPutEvent = (path, body = {}) => {
  return {
    path: path,
    httpMethod: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body,
  };
};

const createPostEvent = (path, body = {}) => {
  return {
    path: path,
    httpMethod: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body,
  };
};
const createDeleteEvent = (path) => {
  return {
    path: path,
    httpMethod: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  };
};

class AwsClientMock {
  private client: any;
  private fn: jest.Mock<any, any>;
  private returnValue: any;
  constructor(factoryMethod) {
    this.client = factoryMethod;
    if (!this.client.__awsClientMockMethods) {
      this.client.__awsClientMockMethods = {};
      this.client.mockImplementation(() => this.client.__awsClientMockMethods);
    }
  }

  method(methodName) {
    this.fn = jest.fn().mockReturnValueOnce({ promise: () => Promise.resolve(this.returnValue) });
    this.client.__awsClientMockMethods[methodName] = this.fn;
    return this;
  }

  returns(returnValue) {
    this.returnValue = returnValue;
    return this;
  }

  mock() {
    return this.fn;
  }

  static build(factoryMethod) {
    return new AwsClientMock(factoryMethod);
  }

  static reset(factoryMethod) {
    delete factoryMethod.__awsClientMockMethods;
    factoryMethod.mockReset && factoryMethod.mockReset();
  }
}

export {
  AwsClientMock,
  handlerFactory,
  createGetEvent,
  createPutEvent,
  createPostEvent,
  createDeleteEvent,
  MockedHandlerFactory,
};
