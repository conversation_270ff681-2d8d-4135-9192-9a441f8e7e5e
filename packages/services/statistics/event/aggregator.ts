import {createDefaultAwsDynamoClient} from "../../common/lambda-env";
import {PutCommand} from "@aws-sdk/lib-dynamodb";
import {LogDetail} from "@oss/shared/platform/LogEvents";
import {isEmpty} from "lodash";

const db = createDefaultAwsDynamoClient();
const TABLES = {
    [LogDetail.SURVEY]: process.env.TABLE_SURVEY_LOGS,
    [LogDetail.DISTRIBUTION]: process.env.TABLE_DISTRIBUTION_LOGS,
}

export async function logModule(module: LogDetail, data: any) {
    const tableName: string = TABLES[module] || '';
    if (isEmpty(tableName)) {
        throw new Error(`Logging table for module ${module} is not found`);
    }

    await db.send(new PutCommand(({
        TableName: tableName,
        Item: data
    })));
}