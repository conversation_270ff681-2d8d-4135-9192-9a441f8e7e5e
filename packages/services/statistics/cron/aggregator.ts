import {RichMenuData, StatisticAggregationJob, SurveyConfig} from "./types";
import {DateTime} from "luxon";
import axios, {HttpStatusCode} from "axios";
import {createDefaultAwsDynamoClient} from "../../common/lambda-env";
import {
    GetCommand,
    QueryCommand,
    QueryCommandOutput,
    ScanCommand,
    ScanCommandOutput,
    UpdateCommand
} from "@aws-sdk/lib-dynamodb";
import {TimeUtil} from "../time";

import {EventType, Period} from "@oss/shared/statistics/types";

type HandleResult = { continue: false } | { continue: true } & StatisticAggregationJob

const lineAccessToken = process.env.LINE_MESSAGING_CHANNEL_ACCESS_TOKEN;
const statisticLogTable = process.env.TABLE_BI_LOGS;
const chatbotScenarioLogs = process.env.TABLE_CHAT_BOT_SCENARIO_LOGS;

const surveyLogs = process.env.TABLE_SURVEY_LOGS;
const surveyConfigs = process.env.TABLE_SURVEY_CONFIGS;
const surveyResults = process.env.TABLE_SURVEY_RESULTS;

const db = createDefaultAwsDynamoClient();

export async function handleAggregationEvent(data: StatisticAggregationJob, timing: () => number): Promise<HandleResult> {
    const aggregator = new Aggregator(data, timing);

    switch (data.event) {
        case EventType.LINE_FRIENDS: {
            await aggregator.aggregateLineFriends();
            break;
        }
        case EventType.LINE_RICH_MENU: {
            await aggregator.aggregateRichMenus();
            break;
        }
        case EventType.SURVEY_LOGS: {
            await aggregator.aggregateSurveyLogs();
            break;
        }
        case EventType.SURVEY_LOGS_DETAIL: {
            await aggregator.aggregateSurveyDetailLogs();
            break;
        }
        case EventType.SEGMENT_DELIVERY_LOGS: {
            // TODO
            break;
        }
    }

    return aggregator.response;
}

class Aggregator {

    private result: HandleResult = {
        continue: false
    };
    private readonly isTimeout: () => boolean

    constructor(
        private readonly data: StatisticAggregationJob, timing: () => number
    ) {
        this.isTimeout = () => timing() < 5 * 60 * 1000; // 5 minutes;
    }

    async aggregateLineFriends() {
        const url = 'https://api.line.me/v2/bot/insight/followers?date=' + TimeUtil.asDefaultFromISO(this.data.date);
        try {
            const result = await axios.get(url, {
                headers: {
                    'Authorization': 'Bearer ' + lineAccessToken
                }
            });

            if (result.status !== HttpStatusCode.Ok) {
                throw new Error('LINE api request error: ' + result.statusText + ' ' + JSON.stringify(result.data || {}));
            }

            await updateLog(this.data, result.data);
        } catch (error: any) {
            console.error('Failed collect LINE friends statistics: ', error);
        }
    }

    async aggregateRichMenus() {
        let menuData: RichMenuData = {
            richmenuDetailCount: {},
            richmenuTotalCount: 0
        }

        if (this.data.continue) {
            menuData = await getLog(this.data) as RichMenuData ?? menuData;
        }

        try {
            const [timestampFrom, timestampUntil] = this.getTimestampRange();

            // Message fetching
            if (!this.data.metadata?.messageFetchingDone) {
                const query = {
                    TableName: chatbotScenarioLogs,
                    FilterExpression: 'attribute_exists(richMenu)',
                    KeyConditionExpression: 'logType = :logType AND createdAt BETWEEN :timestamp1 AND :timestamp2',
                    ExclusiveStartKey: this.data.metadata?.messageLastEvaluated,
                    ExpressionAttributeValues: {
                        ':logType': 'message',
                        ':timestamp1': timestampFrom,
                        ':timestamp2': timestampUntil,
                    }
                };

                do {
                    const queryOutput = await db.send(new QueryCommand(query));
                    for (const item of queryOutput.Items) {
                        const text = item.message.text;
                        menuData.richmenuDetailCount[text] = (menuData.richmenuDetailCount[text] ?? 0) + 1;
                    }

                    if (this.isTimeout()) {
                        await updateLog(this.data, menuData);
                        this.result = {
                            ...this.data,
                            continue: true,
                            metadata: {
                                messageFetchingDone: !queryOutput.LastEvaluatedKey,
                                messageLastEvaluated: queryOutput.LastEvaluatedKey
                            }
                        }

                        return;
                    }

                    if (queryOutput.LastEvaluatedKey) {
                        query.ExclusiveStartKey = queryOutput.LastEvaluatedKey;
                    } else {
                        break;
                    }
                } while (true);
            }

            // Postback fetching
            {
                const query = {
                    TableName: chatbotScenarioLogs,
                    FilterExpression: 'attribute_exists(richMenu)',
                    KeyConditionExpression: 'logType = :logType AND createdAt BETWEEN :timestamp1 AND :timestamp2',
                    ExclusiveStartKey: this.data.metadata?.postbackLastEvaluated,
                    ExpressionAttributeValues: {
                        ':logType': 'postback',
                        ':timestamp1': timestampFrom,
                        ':timestamp2': timestampUntil,
                    }
                };

                do {
                    const queryOutput = await db.send(new QueryCommand(query));
                    for (const item of queryOutput.Items) {
                        const text = item.postback.data;
                        menuData.richmenuDetailCount[text] = (menuData.richmenuDetailCount[text] ?? 0) + 1;
                    }

                    if (this.isTimeout()) {
                        await updateLog(this.data, menuData);
                        this.result = {
                            ...this.data,
                            continue: true,
                            metadata: {
                                messageFetchingDone: true,
                                postbackLastEvaluated: queryOutput.LastEvaluatedKey
                            }
                        }

                        return;
                    }

                    if (queryOutput.LastEvaluatedKey) {
                        query.ExclusiveStartKey = queryOutput.LastEvaluatedKey;
                    } else {
                        break;
                    }
                } while (true);
            }

            menuData.richmenuTotalCount = Object.values(menuData.richmenuDetailCount)
                .reduce((acc, val) => acc + val, 0);
            await updateLog(this.data, menuData);
            console.info('Rich menu processing done: ', menuData);
        } catch (error) {
            console.error('Error during processing rich menu data', error);
        }
    }

    async aggregateSurveyLogs() {
        const [from, to] = this.getTimestampRange();
        let data = {};

        if (this.data.continue) {
            data = await getLog(this.data) ?? data;
        }

        let lastSurveyId = this.data.metadata?.lastSurveyId;
        let lastAction = this.data.metadata?.lastAction;
        let lastEvaluatedKey = this.data.metadata?.lastEvaluatedKey;
        try {
            for (const survey of await getSurveyConfigs()) {
                if (lastSurveyId) {
                    // Skip until last survey ID
                    if (survey.surveyId !== lastSurveyId) {
                        continue;
                    }

                    lastSurveyId = null;
                }

                const actionData = data[survey.surveyId] ?? {};
                for (const action of ['getSurveyConfig', 'createSurveyResult', 'getSurveyResult', 'updateSurveyResult']) {
                    let response: QueryCommandOutput
                    if (lastAction) {
                        // Skip for last action
                        if (lastAction !== action) {
                            continue;
                        }

                        lastAction = null;
                    }

                    if (this.isTimeout()) {
                        this.result = {
                            ...this.data,
                            continue: true,
                            metadata: {
                                lastSurveyId: survey.surveyId,
                                lastAction: action,
                                lastEvaluatedKey: null,
                            }
                        };

                        return;
                    }

                    actionData.surveyTitle = survey.surveyTitle;
                    if (typeof actionData[action] === 'undefined') {
                        actionData[action] = 0;
                    }

                    do {
                        response = await db.send(new QueryCommand({
                            TableName: surveyLogs,
                            Select: 'COUNT',
                            IndexName: 'surveyId-createdAt-index',
                            KeyConditionExpression: 'surveyId = :surveyId AND createdAt BETWEEN :timestamp1 AND :timestamp2',
                            FilterExpression: 'action = :surveyAction',
                            ExclusiveStartKey:  response?.LastEvaluatedKey ?? lastEvaluatedKey,
                            ExpressionAttributeValues: {
                                ':surveyId': survey.surveyId,
                                ':timestamp1': from,
                                ':timestamp2': to,
                                ':surveyAction': action
                            },
                        }));

                        actionData[action] += response.Count;

                        if (this.isTimeout()) {
                            data[survey.surveyId] = actionData;
                            await updateLog(this.data, data);
                            if (!!response.LastEvaluatedKey) {
                                this.result = {
                                    ...this.data,
                                    continue: true,
                                    metadata: {
                                        lastSurveyId: survey.surveyId,
                                        lastAction: action,
                                        lastEvaluatedKey: response.LastEvaluatedKey,
                                    }
                                }

                                return;
                            }
                        }
                    } while (!!response.LastEvaluatedKey);
                }

                data[survey.surveyId] = actionData;
            }

            await updateLog(this.data, data);
        } catch (error) {
            console.error('Unable to collect survey logs statistics', error)
        }
    }

    async aggregateSurveyDetailLogs() {
        const [from, to] = this.getTimestampRange();
        let data = {};

        if (this.data.continue) {
            data = await getLog(this.data) ?? data;
        }

        let lastSurveyId = this.data.metadata?.lastSurveyId;
        let lastItemKey = this.data.metadata?.lastItemKey;
        let lastSurveySchemaOption = this.data.metadata?.lastSurveySchemaOption;
        const lastEvaluatedKey = this.data.metadata?.lastEvaluatedKey;
        try {
            for (const survey of await getSurveyConfigs()) {
                if (lastSurveyId) {
                    if (lastSurveyId !== survey.surveyId) {
                        continue;
                    }

                    lastSurveyId = null;
                }

                const actionData = data[survey.surveyId] ?? {
                    surveyTitle: survey.surveyTitle
                }

                for (const surveySchema of survey.surveySchema) {
                    if (lastItemKey) {
                        if (lastItemKey !== surveySchema.itemKey) {
                            continue;
                        }

                        lastItemKey = null;
                    }

                    if (surveySchema.options) {
                        const optionsData = actionData[surveySchema.itemKey] ?? {
                            title: surveySchema.title ?? surveySchema.itemKey
                        };

                        for (const option of surveySchema.options) {
                            if (lastSurveySchemaOption) {
                                if (lastSurveySchemaOption !== option) {
                                    continue;
                                }

                                lastSurveySchemaOption = null;
                            }

                            if (this.isTimeout()) {
                                actionData[surveySchema.itemKey] = optionsData;
                                data[survey.surveyId] = actionData;
                                await updateLog(this.data, data);

                                this.result = {
                                    ...this.data,
                                    continue: true,
                                    metadata: {
                                        lastSurveyId: survey.surveyId,
                                        lastItemKey: surveySchema.itemKey,
                                        lastSurveySchemaOption: option
                                    }
                                }

                                return;
                            }

                            if (!optionsData[option]) {
                                optionsData[option] = 0;
                            }

                            let response: QueryCommandOutput;

                            do {
                                response = await db.send(new QueryCommand({
                                    TableName: surveyResults,
                                    Select: 'COUNT',
                                    IndexName: 'surveyId-updatedAt-index',
                                    KeyConditionExpression: 'surveyId = :surveyId AND updatedAt BETWEEN :time1 AND :time2',
                                    FilterExpression: 'itemKey = :itemKey AND value = :schemaOption',
                                    ExclusiveStartKey: response?.LastEvaluatedKey ?? lastEvaluatedKey,
                                    ExpressionAttributeValues: {
                                        ':surveyId': survey.surveyId,
                                        ':time1': from,
                                        ':time2': to,
                                        ':itemKey': surveySchema.itemKey,
                                        ':schemaOption': option
                                    }
                                }));

                                optionsData[option] += response.Count;
                                if (this.isTimeout() && !!response.LastEvaluatedKey) {
                                    this.result = {
                                        ...this.data,
                                        continue: true,
                                        metadata: {
                                            lastSurveyId: survey.surveyId,
                                            lastItemKey: surveySchema.itemKey,
                                            lastSurveySchemaOption: option,
                                            lastEvaluatedKey: response.LastEvaluatedKey
                                        }
                                    }

                                    return;
                                }
                            } while (!!response.LastEvaluatedKey);
                        }

                        actionData[surveySchema.itemKey] = optionsData;
                    }

                }

                data[survey.surveyId] = actionData;
            }

            await updateLog(this.data, data);
        } catch (error) {
            console.error('Unable to collect survey detail statistics', error);
        }
    }

    private getTimestampRange(period: Period = this.data.period) {
        let from = DateTime.fromISO(this.data.date).toUTC()
        const until = DateTime.fromISO(this.data.date).toUTC()
            .set({
                hour: 23,
                minute: 59,
                second: 59,
                millisecond: 999
            });

        switch (period) {
            case Period.DAILY: {
                from = from.set({
                    hour: 0,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                });

                break;
            }
            case Period.WEEKLY: {
                from = from.set({
                    hour: 0,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                }).minus({ week: 1 });

                break;
            }
            case Period.MONTHLY: {
                from = from.set({
                    day: 1,
                    hour: 0,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                });

                break;
            }
        }

        return [
            from.toMillis(),
            until.toMillis()
        ];
    }

    get response(): HandleResult {
        return this.result;
    }
}

async function updateLog(data: StatisticAggregationJob, log: any) {
    await db.send(new UpdateCommand({
        TableName: statisticLogTable,
        Key: {
            DataType: data.period,
            Date: TimeUtil.asDefaultFromISO(data.date)
        },
        UpdateExpression: `SET ${data.event} = :map`,
        ExpressionAttributeValues: {
            ':map': log
        }
    }))
}

async function getLog(data: StatisticAggregationJob) {
    const { Item } = await db.send(new GetCommand({
        TableName: statisticLogTable,
        Key: {
            DataType: data.period,
            Date: TimeUtil.asDefaultFromISO(data.date),
        }
    }));

    return !Item ? undefined : Item[data.event];
}

async function getSurveyConfigs() {
    const result = new Array<SurveyConfig>();
    const query = {
        TableName: surveyConfigs
    }

    let response: ScanCommandOutput;
    do {
        response = await db.send(new ScanCommand({
            ...query,
            ExclusiveStartKey: response?.LastEvaluatedKey
        }));

        result.push(...(response.Items as SurveyConfig[]));
    } while(!!response.LastEvaluatedKey)
    return result;
}