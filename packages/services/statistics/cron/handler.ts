import AWS from "aws-sdk";
import {Context} from "aws-lambda";
import {StatisticAggregationJob} from "./types";
import {DateTime} from "luxon";
import {getEnvironmentCredentials} from "../../common/utils/aws-helper";
import {LambdaResponse, OK} from "@oss/shared/platform/types";
import {handleAggregationEvent} from "./aggregator";
import {TimeUtil} from "../time";

import {EventType, Period} from "@oss/shared/statistics/types";

const now = DateTime.now();
const yesterday = TimeUtil.now()
    .minus({ day: 1 })
    .toISO( { suppressMilliseconds: true });
const lambda = new AWS.Lambda(getEnvironmentCredentials());

export async function main(event: any, context: Context): Promise<LambdaResponse> {
    console.info('Begin statistic aggregation', event);

    async function invokeItself(payload: any) {
        await lambda.invoke({
            FunctionName: context.functionName,
            InvocationType: 'Event',
            Payload: JSON.stringify(payload),
        }).promise();
    }

    if (isRecallPayload(event)) {
        try {
            const response = await handleAggregationEvent(event, context.getRemainingTimeInMillis);
            if (response.continue) {
                await invokeItself(response);
            }
        } catch (error: any) {
            console.error('Unable to handle aggregation', error)
            return {
                result: 'FAIL',
                message: error?.message ??
                    `An error occurred attempting to accumulate statistics for BI event ${event.period}|${event.date}|${event.event}`
            }
        }
    } else {
        await doDailyAggregation(invokeItself);
        if (now.weekday === 1) {
            await doWeeklyAggregation(invokeItself);
        }

        if (now.day === 1) {
            await doMonthlyAggregation(invokeItself);
        }
    }

    console.info('Statistic aggregation is done');
    return OK;
}

function isRecallPayload(value: any): value is StatisticAggregationJob {
    return value && value.event && value.period && value.date;
}

async function doDailyAggregation(lambda: Lambda) {
    for (const event of Object.values(EventType)) {
        await lambda({
            event,
            period: Period.DAILY,
            date: yesterday
        } satisfies StatisticAggregationJob);
    }
}

const WEEKLY_AGGREGATIONS = [
    EventType.LINE_FRIENDS,
    EventType.LINE_RICH_MENU,
    EventType.SEGMENT_DELIVERY_LOGS
];

async function doWeeklyAggregation(lambda: Lambda) {
    for (const event of WEEKLY_AGGREGATIONS) {
        await lambda({
            event,
            period: Period.WEEKLY,
            date: yesterday
        } satisfies StatisticAggregationJob);
    }
}

async function doMonthlyAggregation(lambda: Lambda) {
    for (const event of WEEKLY_AGGREGATIONS) {
        await lambda({
            event,
            period: Period.MONTHLY,
            date: yesterday
        } satisfies StatisticAggregationJob);
    }
}

type Lambda = (payload: any) => Promise<void>;