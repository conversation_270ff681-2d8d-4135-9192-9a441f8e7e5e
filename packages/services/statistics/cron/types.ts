import {EventType, Period} from "@oss/shared/statistics/types";

export type StatisticAggregationJob = {
    event: EventType,
    period: Period,
    date: string,
    continue?: boolean,
    metadata?: Record<string, any>
}

export type RichMenuData = {
    richmenuTotalCount: number,
    richmenuDetailCount: Record<string, number>
}

export type SurveyConfig = {
    surveyId: string,
    surveyTitle: string,
    surveySchema: Array<{
        itemKey: string,
        title: string,
        options?: Array<string>
    }>
}