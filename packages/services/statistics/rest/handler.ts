import {apiHandlerFactory} from "../../common/admin-api/api-lambda";
import * as line from './controllers/line';
import * as scenario from './controllers/scenario';
import * as segment from './controllers/segment';
import * as survey from './controllers/survey';

export const main = apiHandlerFactory('/bi', app => {
    app.register(line.routes, { prefix: '/lineAPI' });
    app.register(scenario.routes, { prefix: '/scenario' });
    app.register(segment.routes, { prefix: '/segment' });
    app.register(survey.routes, { prefix: '/survey' });
});