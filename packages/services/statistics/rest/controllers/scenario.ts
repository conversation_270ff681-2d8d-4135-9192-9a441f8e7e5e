import {API, Request} from "lambda-api";
import {restApi} from "../utils";
import {EventType} from "@oss/shared/statistics/types";
import * as common from "./common";

export function routes(app: API) {
    app.get('/getRichmenuLogs', restApi(ScenarioController.getRichMenus));
}

namespace ScenarioController {

    export async function getRichMenus(req: Request) {
        const results = await common.fetchByQuery(req.query);

        return {
            result: 'OK',
            status_code: 200,
            lastEvaluatedKey: results.LastEvaluatedKey,
            items: results.Items.map(v => {
                return {
                    'Date': v.Date,
                    'RichmenuLogs': v[EventType.LINE_RICH_MENU] || {}
                }
            })
        }
    }
}