import {API, Request} from "lambda-api";
import {restApi} from "../utils";
import {z} from "zod";
import * as common from "./common";
import {EventType} from "@oss/shared/statistics/types";
import {ScanCommand} from "@aws-sdk/lib-dynamodb";
import {getLastEvaluatedKey} from "./common";

export function routes(app: API) {
    app.get('/getSurveyLogs', restApi(SurveyController.getSurveyLogs));
    app.get('/getSurveyDetailLogs', restApi(SurveyController.getSurveyDetailLogs));
    app.get('/getSurveyConfigs', restApi(SurveyController.getSurveyConfigs));
}

namespace SurveyController {

    const surveyTable = process.env.TABLE_SURVEY_CONFIGS;
    const surveyDetailValidator = common.statisticValidator.extend({
        surveyId: z.string()
    })

    export async function getSurveyLogs(req: Request) {
        const results = await common.fetchByQuery(req.query);

        return {
            result: 'OK',
            status_code: 200,
            lastEvaluatedKey: results.LastEvaluatedKey,
            items: results.Items.map(v => {
                return {
                    'Date': v.Date,
                    'SurveyLogs': v[EventType.SURVEY_LOGS] || {}
                }
            })
        }
    }

    export async function getSurveyDetailLogs(req: Request) {
        const { params, lastKey } = common.parseQuery(req.query, surveyDetailValidator);
        const results = await common.fetchStatisticLog(params.dataType, params.fromDate, params.toDate, lastKey);

        return {
            result: 'OK',
            status_code: 200,
            lastEvaluatedKey: results.LastEvaluatedKey,
            items: results.Items.map(v => {
                const data = v[EventType.SURVEY_LOGS_DETAIL];
                const result = data && data[params.surveyId] ?
                    data[params.surveyId] : {};

                return {
                    'Date': v.Date,
                    'SurveyId': params.surveyId,
                    'DetailSurveyLogs': result
                }
            })
        }
    }

    export async function getSurveyConfigs(req: Request) {
        const lastKey = getLastEvaluatedKey(req.query.lastEvaluatedKey);
        const results = await common.db.send(new ScanCommand({
            TableName: surveyTable,
            FilterExpression: 'surveyStatus = :filter_status',
            ExpressionAttributeValues: {
                ':filter_status': 'enable'
            },
            ExclusiveStartKey: lastKey
        }));

        return {
            result: 'OK',
            status_code: 200,
            lastEvaluatedKey: results.LastEvaluatedKey,
            surveyList: results.Items.reduce((acc, value) => {
                acc[value.surveyId] = {
                    surveyTitle: value.surveyTitle
                }

                return acc;
            }, {})
        }
    }
}