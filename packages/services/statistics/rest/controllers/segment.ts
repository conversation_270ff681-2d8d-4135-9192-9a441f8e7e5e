import {API, Request} from "lambda-api";
import {restApi} from "../utils";
import {z} from "zod";
import {EventType} from "@oss/shared/statistics/types";
import * as common from "./common";

export function routes(app: API) {
    app.get('/getSegmentLogs', restApi(SegmentController.getSegmentLogs));
}

const requestValidator = common.statisticValidator.extend({
    logType: z.string()
});

namespace SegmentController {

    export async function getSegmentLogs(req: Request) {
        const { params, lastKey } = common.parseQuery(req.query, requestValidator);
        const results = await common.fetchStatisticLog(params.dataType, params.fromDate, params.toDate, lastKey);
        const key = {
            segment: 'segmentLogs',
            user: 'userCount',
            success: 'successLogs'
        }[params.logType];

        return {
            result: 'OK',
            status_code: 200,
            lastEvaluatedKey: results.LastEvaluatedKey,
            items: results.Items.map(v => {
                const data = v[EventType.SEGMENT_DELIVERY_LOGS];
                const result = {};
                if (data && data[key]) {
                    result[key] = data[key];
                }

                return {
                    'Date': v.Date,
                    'SegmentLogs': result
                }
            })
        }
    }
}