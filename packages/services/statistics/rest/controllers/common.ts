import {createDefaultAwsDynamoClient} from "../../../common/lambda-env";
import {z, ZodObject, ZodRawShape} from "zod";
import {dateYYYYMMDD} from "../../../common/validator/date";
import {Period} from "@oss/shared/statistics/types";
import {QueryCommand} from "@aws-sdk/lib-dynamodb";
import {NativeAttributeValue} from "@aws-sdk/util-dynamodb";

export const db = createDefaultAwsDynamoClient();
export const statisticLogTable = process.env.TABLE_BI_LOGS;

export const statisticValidator = z.object({
    fromDate: dateYYYYMMDD,
    toDate: dateYYYYMMDD,
    dataType: z.nativeEnum(Period),
    lastEvaluatedKey: z.string().optional()
})

export async function fetchStatisticLog(dataType: string, from: string, until: string, lastKey?: Record<string, NativeAttributeValue>) {
    return await db.send(new QueryCommand({
        TableName: statisticLogTable,
        KeyConditionExpression: 'DataType = :type AND #column_date BETWEEN :from AND :until',
        ExpressionAttributeNames: {
            "#column_date": "Date"
        },
        ExpressionAttributeValues: {
            ':type': dataType,
            ':from': from,
            ':until': until
        },
        ExclusiveStartKey: lastKey
    }));
}

export async function fetchByQuery(query: any, validator: ZodObject<any> = statisticValidator) {
    const { params, lastKey } = parseQuery(query, validator);
    return fetchStatisticLog(params.dataType, params.fromDate, params.toDate, lastKey);
}

export function parseQuery<T extends ZodRawShape>(query: any, validator: ZodObject<T>) {
    const params = validator.parse(query);
    const lastKey = getLastEvaluatedKey(params.lastEvaluatedKey);

    return {
        params, lastKey
    }
}

export function getLastEvaluatedKey(key?: string) {
    if (!!key) {
        try {
            return JSON.parse(key);
        } catch (ignored) {}
    }

    return undefined;
}