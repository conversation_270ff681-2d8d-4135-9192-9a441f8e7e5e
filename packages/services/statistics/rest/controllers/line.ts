import {API, Request} from "lambda-api";
import {restApi} from "../utils";
import {EventType} from "@oss/shared/statistics/types";
import axios, {HttpStatusCode} from "axios";
import * as common from './common';

export function routes(app: API) {
    app.get('/getFriendsList', restApi(LineController.getFriendsList));
    app.get('/getMessageDelivered', restApi(LineController.getMessageDelivered));
    app.get('/getDemoGraphic', restApi(LineController.getDemographic));
}

namespace LineController {
    export async function getFriendsList(req: Request) {
        const results = await common.fetchByQuery(req.query);

        return {
            result: 'OK',
            status_code: 200,
            lastEvaluatedKey: results.LastEvaluatedKey,
            items: results.Items.map(v => {
                return {
                    'Date': v.Date,
                    'FriendsList': v[EventType.LINE_FRIENDS] || {}
                }
            })
        }
    }

    export async function getMessageDelivered(req: Request) {
        // Not implemented in LSC neither
        return {
            result: 'OK',
            status_code: 200
        }
    }

    export async function getDemographic(req: Request) {
        const response = await axios.get('https://api.line.me/v2/bot/insight/demographic', {
            headers: {
                'Authorization': 'Bearer ' + process.env["LINE_MESSAGING_CHANNEL_ACCESS_TOKEN"]
            }
        });

        if (response.status !== HttpStatusCode.Ok) {
            console.error("Error calling LINE API", response.data);
            return {
                result: 'ERROR',
                status_code: HttpStatusCode.InternalServerError
            }
        }

        return {
            result: 'OK',
            status_code: HttpStatusCode.Ok,
            demographic: response.data
        }
    }
}