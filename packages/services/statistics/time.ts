import {DateTime} from "luxon";

export namespace TimeUtil {

    export const TIME_ZONE = 'Asia/Tokyo';

    export function now() {
        return DateTime.now()
            .setZone(TIME_ZONE);
    }

    export function asDefault(date: DateTime = now()) {
        return date.toFormat('yyyyMMdd');
    }


    export function asDefaultFromISO(iso: string) {
        return asDefault(DateTime.fromISO(iso).setZone(TIME_ZONE));
    }
}