"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os
import json
from flask import jsonify, request

from app import app
from async_process.async_handler import AsyncHandler
asyncHandler = AsyncHandler()

from util import (
    DecimalEncoder, DecimalDecoder, get_now_timestamp_jst_timezone
)

@app.route('/bosai/shelter/asyncStatus', methods=['GET'])
def async_status_check():
    """API endpoint that retrieves the status of a certain async operation

    :returns str: JSON of the async operation status
    ---
    tags:
      - BosaiShelterAsync
    requestBody:
      content:
        application/json:
          schema:
    responses:
      '200':
        content:
          application/json:
            schema:
              type: "object"
    """
    try:
        if not request.args.get("actionName"):
            return jsonify(result="ERROR",
                error_message="パラメータが足りません。「actionName」必須です。")

        work_status = asyncHandler.get_async_work_data(request.args.get("actionName"))

        result = {
            "result": "OK",
            "data": work_status
        }
        return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)
    except ClientError as client_error:
        return jsonify(
            result="ERROR",
            error_message=client_error.response['Error']['Message'])