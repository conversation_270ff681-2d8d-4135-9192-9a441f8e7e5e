{"name": "@oss/services", "version": "0.0.0", "description": "microservices backends", "main": "index.js", "type": "module", "author": "PNL", "license": "MIT", "scripts": {"test": "jest"}, "dependencies": {"@aws-sdk/client-apigatewaymanagementapi": "3.699.0", "@aws-sdk/client-dynamodb": "^3.352.0", "@aws-sdk/client-eventbridge": "^3.454.0", "@aws-sdk/client-lambda": "^3.352.0", "@aws-sdk/client-s3": "^3.360.0", "@aws-sdk/client-scheduler": "^3.465.0", "@aws-sdk/client-ses": "^3.750.0", "@aws-sdk/client-sfn": "^3.462.0", "@aws-sdk/lib-dynamodb": "^3.454.0", "@aws-sdk/lib-storage": "3.712.0", "@aws-sdk/s3-presigned-post": "3.360.0", "@aws-sdk/s3-request-presigner": "^3.359.0", "@aws-sdk/util-dynamodb": "^3.352.0", "@fast-csv/format": "5.0.2", "@types/uuid": "^9.0.7", "arraybuffer-to-buffer": "^0.0.7", "aws-jwt-verify": "4.0.1", "aws-xray-sdk-core": "^3.10.3", "axios": "^1.4.0", "class-transformer": "0.5.1", "class-validator": "0.14.1", "concatjson": "^2.0.1", "date-fns": "4.1.0", "date-fns-timezone": "^0.1.4", "deep-equal": "^2.2.1", "encoding-japanese": "^2.0.0", "es6-promise-pool": "^2.5.0", "fast-csv": "^5.0.2", "inclusion": "^1.0.1", "js-yaml": "^4.1.0", "json2csv": "^5.0.7", "jsonwebtoken": "^9.0.0", "lambda-api": "^1.0.2", "lodash": "^4.17.21", "mailparser": "^3.6.5", "moment-timezone": "^0.5.43", "node-cache": "^5.1.2", "nodes2ts": "3.0.0", "normalize-url": "^8.0.0", "papaparse": "^5.4.1", "reflect-metadata": "0.2.2", "sed-lite": "^1.0.0", "shiftjis": "^1.0.0", "source-map-support": "^0.5.21", "timsort": "^0.3.0", "uuid": "^9.0.1", "uuid-to-hex": "^1.1.1", "zod": "^3.22.4"}, "devDependencies": {"@babel/preset-env": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@types/aws-lambda": "^8.10.119", "@types/jest": "^29.5.9", "@types/lodash": "^4.14.195", "@types/mailparser": "^3.4.4", "@types/moment": "^2.13.0", "command-exists": "^1.2.9", "docker-compose": "^0.24.1", "jest": "^29.7.0", "ts-jest": "^29.1.1"}}