/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import {createEmptyFileIfNotExists, createReportFileName, deleteDirContent, getTmpDir} from "./utils.js";
import {Downloader, getTargetPrefixes, LogFilesIterator, uploadTheReportCsv} from "./export-import.js";
import {getAwsCredentials} from "./app.js";
import {KinesisLogFileReader, LogFileCsvModel} from "./parser.js";
import * as path from "path";
import {LogFileRecord} from "./types.js";
import {DateTime} from "luxon";

export type ReportBuilderOptions = {
  sourceBucketName: string,
  sourceBucketPrefix: string,
  destBucketName: string,
  destBucketPrefix: string,
  tmpDir: string,
}

export class DailyReportBuilder {
  readonly opts: ReportBuilderOptions
  private downloader: Downloader;
  private cachedReportCsv: string;
  private dayStartUnixSec: number;
  private dayEndUnixSec: number;

  constructor(private dateIsoString: string, options: ReportBuilderOptions) {
    this.opts = {
      ...options,
    }
    this.opts.tmpDir = getTmpDir(this.opts.tmpDir);
    this.downloader = new Downloader({
      temporaryFolder: this.opts.tmpDir,
      awsCredentials: getAwsCredentials(),
      sourceBucketName: this.opts.sourceBucketName,
    });
    this.cachedReportCsv = path.join(this.opts.tmpDir, 'tmpReport.csv');
    this.dayStartUnixSec = DateTime.fromISO(this.dateIsoString).startOf('day').toSeconds();
    this.dayEndUnixSec = DateTime.fromISO(this.dateIsoString).endOf('day').toSeconds();
  }

  log(...args: any[]) {
    console.log(...args);
  }

  onlyTargetDateFilter = (record) => {
    const row = record as LogFileRecord;
    if (!row.time) {
      return true
    }
    if (row.time >= this.dayStartUnixSec && row.time <= this.dayEndUnixSec) {
      return true;
    }
    return false;
  }

  isCorrect = (record) => {
    return record && record.time;
  }

  async aggregateLogFile(key: string) {
    const cachedFile = await this.downloader.downloadFile(key);
    if (!cachedFile) return;
    try {
      const reader = this.downloader.createReader(cachedFile);

      const logFileReader = new KinesisLogFileReader(reader);
      let cachedRecords;
      try {
        cachedRecords = await logFileReader.readAll();
        cachedRecords = cachedRecords.filter(this.isCorrect).filter(this.onlyTargetDateFilter);
      } catch (e) {
        this.log('Unexpected log file format', e.message);
        return;
      }

      const csvModel = new LogFileCsvModel(this.cachedReportCsv);
      csvModel.addMany(cachedRecords);

      await csvModel.flushOnDisc();
    } finally {
      await this.downloader.deleteQuite(cachedFile);
    }
  }

  async run() {

    this.log('Clear tmp directory', this.opts.tmpDir);
    await deleteDirContent(this.opts.tmpDir);
    createEmptyFileIfNotExists(this.cachedReportCsv);

    const prefixesToScan = await getTargetPrefixes(this.dateIsoString, this.opts.sourceBucketPrefix);
    this.log('Collecting log files');
    const logsFileIterator = new LogFilesIterator({
      sourceBucketPrefixes: prefixesToScan,
      sourceBucketName: this.opts.sourceBucketName,
      awsCredentials: getAwsCredentials(),
    });
    let nextFile;
    let counter = 0;
    do {
      nextFile = await logsFileIterator.getNext();
      if (nextFile) {
        this.log('Processing', nextFile);
        await this.aggregateLogFile(nextFile);
        this.log('Finished', nextFile);
        counter++;
      }
    } while (nextFile);

    this.log(`${counter} log files processed`);
    this.log('Uploading the result csv')
    const s3Path = await uploadTheReportCsv(this.cachedReportCsv, {
      bucketName: this.opts.destBucketName,
      prefix: this.opts.destBucketPrefix,
      filename: createReportFileName(this.dateIsoString),
    })
    this.log('Uploaded to', s3Path);
  }

}
