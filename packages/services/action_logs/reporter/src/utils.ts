/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

// import { rimraf } from 'rimraf';
import rimraf from 'rimraf';
import path from 'path';
import { DateTime, Duration } from 'luxon';
import fs from 'fs';

export const isAwsEnvironment = () => {
  // check whether it is SAM local-api Docker env
  if (process.env.AWS_SAM_LOCAL === 'true') {
    return false;
  }
  return !!process.env.AWS_LAMBDA_LOG_STREAM_NAME;
};

export const getTmpDir = (suggested: string) => {
  if (isAwsEnvironment() && !suggested.startsWith('/tmp')) {
    return path.join('/tmp', suggested);
  }
  return suggested;
};

export const deleteDirContent = async (dir: string) => {
  // AWS Lambda の /tmp は削除可能にする
  if (!isAwsEnvironment() && path.isAbsolute(dir) && !path.normalize(dir).startsWith(process.cwd()) && !dir.startsWith('/tmp')) {
    throw new Error("fail-safe: can not delete any dir, that is not under the process's working directory");
  }
  await new Promise((resolve) => {
    rimraf(path.join(dir, '*'), { disableGlob: false }, resolve);
  });
  // await rimraf(path.join(dir, '*'));
};

export const yesterdayDateStr = () => {
  return DateTime.local()
    .minus(Duration.fromObject({ day: 1 }))
    .toISODate();
};

export const createReportFileName = (targetDateIso: string = yesterdayDateStr()) => {
  const dt = DateTime.fromISO(targetDateIso);
  return `operations-log-${dt.toFormat('yyyy-MM-dd')}.csv`;
};

export const createEmptyFileIfNotExists = (target: string) => {
  fs.mkdirSync(path.dirname(target), { recursive: true });
  fs.closeSync(fs.openSync(target, 'w'));
};
