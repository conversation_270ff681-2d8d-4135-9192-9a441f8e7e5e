/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import AWS from 'aws-sdk';

export const ENV_VARIABLES = {
  SOURCE_BUCKET: 'SOURCE_BUCKET',
  DEST_BUCKET: 'DEST_BUCKET',
  SOURCE_BUCKET_PREFIX: 'SOURCE_BUCKET_PREFIX',
  DEST_BUCKET_PREFIX: 'DEST_BUCKET_PREFIX',
}

export const requireEnv = (name: string, defaultValue?: string) => {
  const value = process.env[name] || defaultValue;
  if (!value) {
    throw new Error('Unresolved environment variable ' + name);
  }
  return value;
}

export const debugLog = (...args: any[]) => {
  // TODO log level check
  infoLog(...args);
}

export const infoLog = (...args: any[]) => {
  console.log(...args.map(printIt));
};

const printIt = (arg) => {
  if (typeof arg !== 'object') {
    return arg;
  }
  if (arg.toString === Object.prototype.toString) {
    try {
      return JSON.stringify(arg, null, 2);
    } catch (e) {
      return arg;
    }
  } else {
    return arg;
  }
};

// let defaultProfile = 'oss-local';
let defaultProfile = '';

export const setDefaultProfile = (profile: string) => {
  defaultProfile = profile;
}

export const isAwsEnvironment = () => {
  // check whether it is SAM local-api Docker env
  if (process.env.IS_SST_DEV) {
    return true;
  }
  return !!process.env.AWS_LAMBDA_LOG_STREAM_NAME;
};

export const getAwsCredentials = (profileName: string | undefined = defaultProfile || process.env.AWS_PROFILE) => {
  if (isAwsEnvironment()) {
    return undefined;
  }
  if (profileName) {
    return {
      credentials: new AWS.SharedIniFileCredentials({
        profile: profileName
      }),
      region: process.env.AWS_REGION || 'ap-northeast-1',
    };
  }
  return {
    credentials: new AWS.Credentials({
      accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || ''
    }),
    region: process.env.AWS_REGION || 'ap-northeast-1'
  }
}
