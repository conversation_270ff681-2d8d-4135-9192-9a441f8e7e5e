/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import AWS from 'aws-sdk';
import {getAwsCredentials} from "./app.js";
import {DateTime, Duration} from "luxon";
import {AwsCredentials} from "./types.js";
import * as _ from "lodash";
import * as fs from "fs";
import * as path from "path";

/**
 * The implementation takes the folder corresponding to the last hour of the previous day,
 * because the Kinesis Firehose flushes the next hour's events into the previous hour's file
 * @param targetDateIso ISO date string, target date
 * @param rootPrefix root folder prefix (default is empty)
 */
export const getTargetPrefixes = (targetDateIso: string, rootPrefix?: string) => {
  const start = DateTime.fromISO(targetDateIso).startOf('day').toUTC();
  const prefixes: string[] = [];
  for (let i = -1; i < 25; i++) {
    const hour = start.plus(Duration.fromObject({hour: i}));
    const t = [
      hour.year,
      _.padStart(hour.month + '', 2, '0'),
      _.padStart(hour.day + '', 2, '0'),
      _.padStart(hour.hour + '', 2, '0'),
    ].join('/');
    prefixes.push(rootPrefix ? path.join(rootPrefix, t) : t);
  }
  return prefixes;
}

export class LogFilesIterator {
  buffer: string[] = [];
  readonly client: AWS.S3;
  usedPrefixes: string[] = [];
  marker: string | undefined = undefined;

  constructor(private opts: {
    sourceBucketName: string,
    sourceBucketPrefixes: string[],
    awsCredentials?: AwsCredentials,
    maxPageSize?: number,
  }) {
    this.client = new AWS.S3(opts.awsCredentials || getAwsCredentials())
    this.usedPrefixes.push(...opts.sourceBucketPrefixes);
  }

  private async fillBuffer() {
    if (this.usedPrefixes.length === 0) {
      return null;
    }
    const nextPage = await this.client.listObjects({
      Bucket: this.opts.sourceBucketName,
      Prefix: this.usedPrefixes[0],
      Marker: this.marker,
      MaxKeys: this.opts.maxPageSize || 100,
    }).promise();

    if (nextPage.Contents) {
      nextPage.Contents.forEach((f) => {
        if (f.Key) {
          this.buffer.push(f.Key);
        }
      });
      this.buffer.sort();
    }
    this.marker = nextPage.NextMarker;
    if (!nextPage.NextMarker) {
      this.usedPrefixes.shift();
    }
    if (this.buffer.length === 0) {
      await this.fillBuffer();
    }
  }

  async getNext() {
    if (this.buffer.length === 0) {
      await this.fillBuffer();
    }
    const next = this.buffer.shift();
    return next ? next : null;
  }

}

export class Downloader {
  constructor(private opts: {
    sourceBucketName: string,
    awsCredentials?: AwsCredentials,
    temporaryFolder: string
  }) {
  }

  async deleteQuite(pathToCache: string) {
    try {
      await fs.promises.unlink(pathToCache);
    } catch (ignored) {
      // Ignore error
    }
  }

  createReader(keyOrLocalPath: string) {
    let localPath = keyOrLocalPath;
    if (!keyOrLocalPath.startsWith('/')) {
      localPath = path.join(this.opts.temporaryFolder, keyOrLocalPath);
    }
    return fs.createReadStream(localPath, {encoding: "utf8"})
  }

  async downloadFile(key: string): Promise<string | null> {
    const pathToCache = path.join(this.opts.temporaryFolder, key);
    await fs.promises.mkdir(path.dirname(pathToCache), {recursive: true});
    if (fs.existsSync(pathToCache)) {
      return pathToCache;
    }
    const client = new AWS.S3(this.opts.awsCredentials);
    const s3Object = client.getObject({
      Bucket: this.opts.sourceBucketName,
      Key: key
    });
    const dest = fs.createWriteStream(pathToCache, {encoding: "utf8"})
    return new Promise((resolve, reject) => {
      s3Object.createReadStream()
        .on('end', () => {
        resolve(pathToCache);
      })
        .on('error', (err) => {
          this.deleteQuite(key);
          if (err['code'] === 'NoSuchKey') {
            resolve(null);
          } else {
            reject(err);
          }
        }).pipe(dest);
    })
  }
}

export const uploadTheReportCsv = async (reportFile: string, opts: {
  bucketName: string,
  prefix: string,
  filename: string,
}) => {

  const readStream = fs.createReadStream(reportFile);
  const key = path.join(opts.prefix, opts.filename);

  const client = new AWS.S3(getAwsCredentials());
  await client.putObject({
    Bucket: opts.bucketName,
    Key: key,
    Body: readStream,
  } ).promise();

  return `s3://${opts.bucketName}/${key}`;
};
