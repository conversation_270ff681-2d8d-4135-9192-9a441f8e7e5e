/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import {Readable} from "stream";
import * as cj from 'concatjson'
import {CsvReportLine, LogFileRecord} from "./types.js";
import * as _ from "lodash";
import {DateTime} from "luxon";
import * as csv from 'fast-csv';
import {sort} from 'timsort';
import * as fs from "fs";
import {createEmptyFileIfNotExists} from "./utils.js";

function noop() {
  // nothing
}

export class KinesisLogFileReader {
  private callback: (value: any) => any;
  private error: (err: any) => any;
  private finished = false;
  private readonly stream: Readable

  constructor(sourceStream: Readable, private opts: {
    stopOnError: boolean
  } = {stopOnError: false}) {
    this.callback = noop;
    this.error = noop;

    this.stream = sourceStream.pipe(cj.parse());
    this.stream.on('data', (o) => {
      this.callback(o);
      this.stream.pause();
    });
    this.stream.on('error', (err) => {
      this.finished = true;
      if (opts.stopOnError) {
        this.callback(null);
      } else {
        this.error(err);
      }
      this.stream.destroy();
    });
    this.stream.on('end', () => {
      if (!this.finished) {
        this.callback(null);
        this.finished = true;
      }
    });
  }

  async readNext() {
    if (this.finished) {
      return null;
    }
    return new Promise<any>((resolve, reject) => {
      this.callback = resolve;
      this.error = reject;
      this.stream.resume();
    })
  }

  async readAll() {
    const records: any[] = [];
    let record;
    do {
      record = await this.readNext();
      records.push(record);
    } while (record);
    return records;
  }

}

export class LogToCsvConverter {

  convert(raw: any): CsvReportLine | null {
    if (!_.isPlainObject(raw)) {
      return null;
    }
    const row = raw as LogFileRecord;

    const time = this.toTimeStr(row.time);
    const action = row.action;
    if (!time || !action) {
      return null;
    }

    const data = this.mergeLogRecordParameters(row);
    if (Array.isArray(data)) {
      const [type, data1, data2, data3, data4, data5] = data;
      return {
        time,
        api: row.actionEndpoint || '',
        user: row.actorName || '',
        action,
        type,
        data1,
        data2,
        data3,
        data4,
        data5,
      }
    }
    return {
      time,
      api: row.actionEndpoint || '',
      user: row.actorName || '',
      action,
      type: '-',
      data1: data,
      data2: '',
      data3: '',
      data4: '',
      data5: '',
    }
  }

  toTimeStr(timestamp?: number | string) {
    if (!timestamp) return null;
    let dt: DateTime;
    if (typeof timestamp === 'number') {
      if (timestamp / 10000000000 > 1) {
        dt = DateTime.fromMillis(timestamp);
      } else {
        dt = DateTime.fromSeconds(timestamp);
      }
    } else {
      dt = DateTime.fromISO(timestamp);
    }
    if (!dt.isValid) {
      return null;
    }
    return dt.setZone('Asia/Tokyo').toFormat('yyyy/MM/dd HH:mm:ss')
  }

  mergeLogRecordParameters(log: LogFileRecord) {
    // NOTE: 部分的にログ出力項目を増やしているのでparameterMainの有無で確認
    if (log.parameterMain && log.parameterMain !== "") {
      const rows: any[] = [];
      rows.push(log.parameterMain);
      rows.push(log.parameter1);
      rows.push(log.parameter2);
      rows.push(log.parameter3);
      rows.push(log.parameter4);
      rows.push(log.parameter5);
      return rows.filter((v) => !_.isEmpty(v)).join('\n\n');
    }

    return [
      log.type || '-',
      log.parameter1 || '',
      log.parameter2 || '',
      log.parameter3 || '',
      log.parameter4 || '',
      log.parameter5 || '',
    ]
  }
}

export class LogFileCsvModel {
  readonly table: LogFileRecord[]

  constructor(
    private fileToCache: string,
    private converter: LogToCsvConverter = new LogToCsvConverter()) {
    this.table = [];
  }

  addMany(records: any[]) {
    records.forEach((r) => {
      this.table.push(r as LogFileRecord);
    })
  }

  sortByTime() {
    sort(this.table, (a, b) => {
      return (a.time || -1) - (b.time || -1)
    });
  }

  async flushOnDisc(): Promise<void> {
    const exists = fs.existsSync(this.fileToCache);
    if (!exists) {
      createEmptyFileIfNotExists(this.fileToCache);
    }
    const isEmpty = fs.statSync(this.fileToCache).size === 0;
    if (isEmpty) {
      // NOTE: 空ファイルにのみBOMを挿入する
      fs.writeFileSync(this.fileToCache, '\ufeff');
    }

    const writeTo = fs.createWriteStream(this.fileToCache, {
      flags: 'a',
      encoding: 'utf-8',
    });
    const csvStream = csv.format(isEmpty ? {
      headers: ['操作日時', 'ユーザー名', 'API', '種別', '操作', '情報1', '情報2', '情報3', '情報4', '情報5'],
      delimiter: ','
    } : {headers: false, delimiter: ','});
    csvStream.pipe(writeTo);

    if (!isEmpty) {
      writeTo.write('\n');
    }
    return new Promise<void>((resolve, reject) => {
      this.table.forEach((r) => {
        const csvModel = this.converter.convert(r);
        if (csvModel) {
          csvStream.write([
            csvModel.time,
            csvModel.user,
            csvModel.api,
            csvModel.type,
            csvModel.action,
            csvModel.data1,
            csvModel.data2,
            csvModel.data3,
            csvModel.data4,
            csvModel.data5,
          ]);
        }
      })
      csvStream.on('end', () => {
        writeTo.close();
      });
      csvStream.on('error', reject)
      writeTo.on('close', () => {
        resolve();
      });
      csvStream.end();
    })
  }
}
