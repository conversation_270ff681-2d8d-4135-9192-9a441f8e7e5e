/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import {Credentials} from "aws-sdk";
import {CredentialsOptions} from "aws-sdk/lib/credentials";

export type AwsCredentials = { credentials?: Credentials | CredentialsOptions | null, region?: string };

export type LogFileRecord = {
  time?: number,
  actionEndpoint?: string,
  actorName?: string,
  type?: string,
  action?: string,
  parameterMain?: string,
  parameter1?: string,
  parameter2?: string,
  parameter3?: string,
  parameter4?: string,
  parameter5?: string,
}

export type CsvReportLine = {
  time: string,
  user: string,
  api: string,
  type: string,
  action: string,
  data1: string,
  data2: string,
  data3: string,
  data4: string,
  data5: string,
}
