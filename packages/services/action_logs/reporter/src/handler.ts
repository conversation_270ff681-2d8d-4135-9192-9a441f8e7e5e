/*
 * Copyright 2022 LINE Fukuoka Corporation
 *
 * LINE Corporation licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import 'source-map-support/register.js';
import {DailyReportBuilder} from "./report-builder.js";
import {ENV_VARIABLES, infoLog, requireEnv} from "./app.js";
import {Settings} from "luxon";
import {yesterdayDateStr} from "./utils.js";

Settings.defaultZoneName = 'Asia/Tokyo';

export const handler = async (event: any) => {
  infoLog('Started on event', event);

  const reportTargetDate = event.AggregateDate || yesterdayDateStr();
  infoLog('Building daily actions report for', reportTargetDate);

  await new DailyReportBuilder(reportTargetDate, {
    destBucketName: requireEnv(ENV_VARIABLES.DEST_BUCKET),
    destBucketPrefix: requireEnv(ENV_VARIABLES.DEST_BUCKET_PREFIX),
    sourceBucketName: requireEnv(ENV_VARIABLES.SOURCE_BUCKET),
    sourceBucketPrefix: requireEnv(ENV_VARIABLES.SOURCE_BUCKET_PREFIX),
    tmpDir: '/tmp',
  }).run();

  infoLog(`Finished building report for ${reportTargetDate}`);
}
