import {APIGatewayProxyHandlerV2} from "aws-lambda";
import * as surveyResults from './controllers/survey-results.js'
import * as surveyConfigs from './controllers/survey-configs.js'
import * as surveyCalendars from './controllers/survey-calendars.js'
import * as memberConfigs from './controllers/member-configs.js'
import * as memberResults from './controllers/member-results.js'
import {apiHandlerFactory} from "./controllers/helpers/liffApiHandler.js";

export const main: APIGatewayProxyHandlerV2 = apiHandlerFactory('/survey/api/v1/db', controller => {
    controller.register(surveyResults.routes, { prefix: '/survey-results'})
    controller.register(surveyConfigs.routes, { prefix: '/survey-configs'})
    controller.register(surveyCalendars.install, { prefix: '/calendars'})

    // register member survey
    controller.register(memberConfigs.routes, { prefix: '/member-configs' })
    controller.register(memberResults.routes, { prefix: '/member-results' })
})
