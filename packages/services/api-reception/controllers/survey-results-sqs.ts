/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { surveyConfigsService } from '../../survey/services/survey-configs-service.js';
import { surveyResultsService } from '../../survey/services/survey-results-service.js';
import * as ResultsControlService from '../../survey/services/results-control-service.js';
import { SurveyTransactionHelper } from '../../survey/services/helpers/survey-transaction-helper.js';
import { SurveyResultsHistoryHelper } from '../../survey/services/helpers/survey-results-history-helper.js';


export type CleanerFuncPayload = {
    userId: string;
    surveyResultsPartitionKey: string;
    check: boolean;
}

export const handleLogicalDeleteSurveyResults = async (event: CleanerFuncPayload) => {
    const { userId, surveyResultsPartitionKey, check } = event;
    if (!userId || !surveyResultsPartitionKey || !check) {
        return { result: 'ERROR', errorMessage: 'Required parameters not found' };
    }

    const results = await surveyResultsService.getSurveyResult(surveyResultsPartitionKey);
    if (results.length === 0){
        return { result: 'ERROR', errorMessage: 'Unable to find survey results' };
    }

    const surveyConfig = await surveyConfigsService.getSurveyConfig(results[0].surveyId);
    if (!surveyConfig) {
        return { result: 'ERROR', errorMessage: 'Unable to find survey config' };
    }

    const originalResultsCheck = results[0].check;

    const updatedAt = Math.floor(Date.now() / 1000);
    results.forEach(obj => {
        obj.userId = userId;
        obj.check = check;
        obj.updatedAt = updatedAt;
    });

    try {
        const response = await runSurveyResultsDeleteTransaction(surveyConfig, results, originalResultsCheck);
        if (response.result !== 'ERROR') {
            const resultsHistoryHelper = new SurveyResultsHistoryHelper();
            await resultsHistoryHelper.logSurveyResultsUpdateOnlyStatus(surveyResultsPartitionKey, check);
        }

        return response;
    } catch (err:any) {
        console.log('[ERROR] Error Running transaction method on old survey results ' + err.stack);
        return { result: 'ERROR', errorMessage: err.message };
    }
};

const runSurveyResultsDeleteTransaction = async (surveyConfig, surveyResults, originalResultsCheck) => {
    const alreadyCanceledReservationStatuses = ['キャンセル', '取り消し'];
    const transactionRecord = await ResultsControlService.getTransactionLog(surveyResults[0].userId, surveyResults[0].partitionKey);
    if (!transactionRecord || transactionRecord.status === 'finished') {
        return { result: 'ERROR', errorMessage: 'Running SQS Logical Delete on already finished transaction.' };
    }

    //Look for a reservation in the results to save
    const reservationQuestion = surveyConfig.surveySchema.find(obj => obj.type === 'reservation');
    let reservationItem:any = null;
    if (reservationQuestion){
        reservationItem = surveyResults.find(obj => obj.itemKey === reservationQuestion.itemKey);
    }

    await surveyResultsService.updateItemsWithoutDelete(surveyResults.filter(obj => !reservationQuestion || obj.itemKey !== reservationQuestion.itemKey ));

    if (reservationItem && reservationItem.value.split('|').length === 3){
        try {
        //Build transaction
        const surveyTransactionHelper = new SurveyTransactionHelper();
        surveyTransactionHelper.addSurveyResultsItem(reservationItem);
        if (!alreadyCanceledReservationStatuses.includes(originalResultsCheck)) {
            await surveyTransactionHelper.cancelSurveyCalendarReservation(reservationItem);
        }
        surveyTransactionHelper.addTransactionLogRecord(surveyResults[0].userId, surveyResults[0].partitionKey, 'finished');

        surveyTransactionHelper.debugTransactionRecord();
        await surveyTransactionHelper.executeTransaction();
        } catch (err: any) {
            if (err === 'cancellation_exceeded'){
                return {
                    result: 'ERROR',
                    errorMessage: 'Unable to cancel reservation as cancelling with current cost will put reservation count under 0',
                }
            }
            console.log('[ERROR] sqsHandlerTransaction ' + (err === 'out_of_time' ? 'Transaction did not complete within alloted time interval.' : err.stack));
            return { result: 'ERROR', errorMessage: err.message };
        }
    } else {
        if (reservationItem){
            //No reservation to cancel, just update like so
            await surveyResultsService.updateItemsWithoutDelete([reservationItem]);
        }
        await ResultsControlService.createTransactionRecord(surveyResults[0].userId, surveyResults[0].partitionKey, 'finished');
    }

    return { result: 'OK' };
}
