/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *  - name: Calendars
 *    description: カレンダーAPI
 */

import * as services from '../../survey/services/calendars-service.js';
import * as validators from './helpers/validators.js';

/**
 * @openapi
 * /db/calendars/schedule/{category_id}:
 *  get:
 *    summary: スケジュールを取得する
 *    tags: [Calendars]
 *    parameters:
 *      - name: category_id
 *        in: path
 *        description: カレンダーID
 *        required: true
 *        schema:
 *          type: string
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 */
const getSchedule = async (req, res) => {
  const { start_day, end_day, include_calendar_info } = req.query;
  const categoryId = decodeURIComponent(req.params['category_id']);
  validators.requireNotEmpty(categoryId, 'category id');
  validators.requireMatchRegex(start_day, /\d{8}/, 'start_day');
  validators.requireMatchRegex(end_day, /\d{8}/, 'end_day');

  const schedules = await services.loadScheduleOfCategory(categoryId, start_day, end_day, {
    includeCalendarInfo: include_calendar_info !== undefined,
  });
  res.status(200).json(schedules);
};

/**
 * @openapi
 * /db/calendars/info/{category_id}:
 *  get:
 *    summary: カレンダー情報を取得する
 *    tags: [Calendars]
 *    parameters:
 *      - name: category_id
 *        in: path
 *        description: カレンダーID
 *        required: true
 *        schema:
 *          type: string
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 */
const getCalendar = async (req, res) => {
  const categoryId = decodeURIComponent(req.params['category_id']);
  validators.requireNotEmpty(categoryId, 'category id');

  const calendar = await services.loadCalendarInfo(categoryId);

  res.status(200).json(calendar);
};

/**
 * @openapi
 * /db/calendars/categories:
 *  get:
 *    summary:
 *    tags: [Calendars]
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 */
const getCategories = async (req, res) => {
  const filter = req.query;
  const categories = await services.loadCategories(filter);
  res.status(200).json(categories);
};

/**
 * @openapi
 * /db/calendars/categories_tree:
 *  get:
 *    summary:
 *    tags: [Calendars]
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 */
const getCategoriesTree = async (req, res) => {
  const categories = await services.loadCategoriesTree();
  res.status(200).json(categories);
};

/**
 * @openapi
 * /db/calendars/tag_labels:
 *  get:
 *    summary:
 *    tags: [Calendars]
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 */
const getTagLabels = async (req, res) => {
  const labels = await services.loadTagLabels();
  res.status(200).json(labels);
};

/**
 * @openapi
 * /db/calendars/display_settings:
 *  get:
 *    summary:
 *    tags: [Calendars]
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 */
const getDisplaySettings = async (req, res) => {
  const labels = await services.loadGlobalDisplaySettings();
  res.status(200).json(labels);
};

/**
 * @openapi
 * /db/calendars/getCalendars:
 *  get:
 *    summary:
 *    tags: [Calendars]
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 */
const getCalendars = async (req, res) => {
  res.status(200).json(await services.getCalendars());
};

/**
 * @openapi
 * /db/calendars/item_info/{category_id}:
 *  get:
 *    summary:
 *    tags: [Calendars]
 *    parameters:
 *      - name: category_id
 *        in: path
 *        description: カテゴリーID
 *        required: true
 *        schema:
 *          type: string
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 */
const getReservationItemInfo = async (req, res) => {
  const categoryId = decodeURIComponent(req.params['id']);
  res.status(200).json(await services.loadReservationItemInfo(categoryId));
};

/**
 * @openapi
 * /db/calendars/canBeReserve/{calendar_id}:
 *  get:
 *    summary:
 *    tags: [Calendars]
 *    parameters:
 *      - name: calendar_id
 *        in: path
 *        description: カレンダーID
 *        required: true
 *        schema:
 *          type: string
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "boolean"
 */
const canBeReserveCalendar = async (req, res) => {
  const calendarId = decodeURIComponent(req.params['calendar_id']);
  validators.requireNotEmpty(calendarId, 'calendar id');

  const canBeReserve = await services.canBeReserveCalendar(calendarId);

  res.status(200).json(canBeReserve);
};

export function install(app, opts) {
  app.get('/schedule/:category_id', getSchedule);
  app.get('/info/:category_id', getCalendar);
  app.get('/canBeReserve/:calendar_id', canBeReserveCalendar);
  app.get('/categories', getCategories);
  app.get('/tag_labels', getTagLabels);
  app.get('/categories_tree', getCategoriesTree);
  app.get('/display_settings', getDisplaySettings);
  app.get('/getCalendars', getCalendars);
  app.get('/item_info/:id', getReservationItemInfo);
}
