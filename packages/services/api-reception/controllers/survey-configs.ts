/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *  - name: SurveyConfigs
 *    description: 帳票API
 */

import { surveyConfigsService } from '../../survey/services/survey-configs-service.js';
import * as surveyCalendarService from '../../survey/services/calendars-service.js';
import { BadRequest, ServerError, NotFound } from '../../survey/utils/exceptions.js';
import { ReservationFieldType } from '../../survey/types/survey-config.js';
import loggerService from "../../../services/common/logs/logger-service.js";
import memberResultsService from 'members/services/member-results-service.js';

const w = (handler) => {
  return async (req, res) => {
    try {
      return await handler(req, res);
    } catch (e: any) {
      if (e.constructor.name === 'NotFound') {
        throw new BadRequest({ cause: e, code: e.code });
      } else {
        throw new ServerError({ cause: e });
      }
    }
  };
};

/**
 * @openapi
 * /db/survey-configs/getAllSearchable:
 *  get:
 *    summary: 検索可能な帳票設定の一覧を返す
 *    description: SURVEY_CONFIGSテーブルより、検索可能な帳票設定の一覧を返します
 *    tags: [SurveyConfigs]
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 *              properties:
 *                result:
 *                  type: "string"
 *                data:
 *                  type: array
 *                  items:
 *                    $ref: '#/definitions/SurveyConfig'
 */
const getSearchableSurveyConfigs = async (req, res) => {
  await loggerService.logApiEvent({
    logType: 'config',
    endpointName: 'getAllSearchable',
    action: 'getSurveyConfig',
  });

  const results:any[] = await surveyConfigsService.getSearchableSurveyConfigs();
  let categories_tree_result:any = null;
  let catgories_tree_display:any = null;
  for (const config of results) {
    const surveySchemaList = config.surveySchema;
    for (const item in surveySchemaList) {
      const tempItem = surveySchemaList[item];
      if (tempItem.type === 'reservation') {
        if (categories_tree_result == null) {
          categories_tree_result = await surveyCalendarService.loadCategoriesTree();
          catgories_tree_display = await surveyCalendarService.loadGlobalDisplaySettings();
        }
        surveySchemaList[item].categories_tree = {
          tree: categories_tree_result,
          display: catgories_tree_display,
        };
      }
    }
    config.surveySchema = surveySchemaList;
  }

  res.status(200).json({ result: 'OK', data: results });
};

/**
 * @openapi
 * /db/survey-configs/getItem:
 *  get:
 *    summary: 帳票設定を返す
 *    description:
 *      surveyIdの値に基づき、SURVEY_CONFIGSテーブルより該当の帳票設定を返します
 *    tags: [SurveyConfigs]
 *    parameters:
 *      - in: query
 *        name: surveyId
 *        required: true
 *        schema:
 *          type: string
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 *              properties:
 *                result:
 *                  type: "string"
 *                data:
 *                  $ref: '#/definitions/SurveyConfig'
 *                hasMember:
 *                  type: boolean
 *                memberFormId:
 *                  type: string
 */
const getSurveyConfig = async (req, res) => {
  const surveyId = req.query.surveyId;
  const userId = req.userId;
  await loggerService.logApiEvent({
    logType: 'config',
    endpointName: 'getItem',
    action: 'getSurveyConfig',
    surveyId: surveyId,
    headers: req.query,
  });

  const config = await surveyConfigsService.getSurveyConfig(surveyId);
  let categories_tree_result:any = null;
  let catgories_tree_display:any = null;
  const surveySchemaList = config.surveySchema;
  for (const item in surveySchemaList) {
    const tempItem = surveySchemaList[item];
    if (tempItem.type === 'reservation') {
      if (categories_tree_result == null) {
        categories_tree_result = await surveyCalendarService.loadCategoriesTree();
        catgories_tree_display = await surveyCalendarService.loadGlobalDisplaySettings();
      }
      (surveySchemaList[item] as ReservationFieldType).categories_tree = {
        tree: categories_tree_result,
        display: catgories_tree_display,
      };
    }
  }

  let hasMember = false
  let memberFormId = null
  if (userId && ('memberFormId' in config) && config.memberFormId) {
    memberFormId = config.memberFormId
    const memberResults = await memberResultsService.getLinkedMemberOfSurveyResults(memberFormId, userId);
    hasMember = (memberResults.length > 0)
  }

  config.surveySchema = surveySchemaList;
  res.status(200).json({ result: 'OK', data: config,
    hasMember,
    memberFormId,
  });
};


/**
 * @openapi
 * /db/survey-configs/getSurveyConfigsList:
 *  post:
 *    summary: 帳票設定一覧を返す
 *    description:
 *      SURVEY_CONFIGSテーブルより指定した帳票設定を返します
 *    tags: [SurveyConfigs]
 *    requestBody:
 *      required: true
 *      content:
 *        application/json:
 *          schema:
 *            type: object
 *            properties:
 *              surveyIdList:
 *                type: array
 *                items:
 *                  type: string
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 *              properties:
 *                result:
 *                  type: "string"
 *                items:
 *                  type: array
 *                  items:
 *                    $ref: '#/definitions/SurveyConfig'
 */
const getSurveyConfigsList = async (req, res) => {
  const surveyConfigIdList = req.body.surveyIdList;
  if (!surveyConfigIdList || !Array.isArray(surveyConfigIdList)){
    return res.status(200).json({ result: 'ERROR', errorMessage: '渡されたパラメータが期待されたものと一致しません。「surveyIdList」が必須です。' });
  }
  const results: any[] = [];
  for (const surveyId of surveyConfigIdList){
    try {
      const config = await surveyConfigsService.getSurveyConfig(surveyId);
      results.push(config);
    } catch (err: any) {
      if (!(err instanceof NotFound)){
        throw err;
      }
    }
  }

  return res.status(200).json({ result: 'OK', items: results });
};

export function routes (app, opts) {
  app.get('/getItem', w(getSurveyConfig));
  app.post('/getSurveyConfigsList', w(getSurveyConfigsList));
  app.get('/getAllSearchable', w(getSearchableSurveyConfigs));
}
