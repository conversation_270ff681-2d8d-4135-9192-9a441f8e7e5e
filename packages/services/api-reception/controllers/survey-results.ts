/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *  - name: SurveyResults
 *    description: 帳票回答API
 */

import * as _ from 'lodash';
import {cloneDeep, groupBy} from 'lodash';
import moment from 'moment';
import config from '../../api-reception/config/static.js';
import c from '../config/constants.js';
import {surveyConfigsService} from '../../survey/services/survey-configs-service.js';
import {surveyResultsService} from '../../survey/services/survey-results-service.js';
import {surveyResultsHelper} from '../../survey/services/helpers/survey-results-helper.js';
import {SurveyResultsValidator} from '../../survey/services/survey-results-validator.js';
import {requestContext} from '../../survey/utils/execution-context.js';
import {findWithAttr} from '../../survey/utils/util.js';
import * as validators from './helpers/validators.js';
import * as ResultsControlService from '../../survey/services/results-control-service.js';
import {checkUserId} from './helpers/access.js';
import * as locks from './helpers/dblocks.js';
import {logPutItemsStart} from './helpers/logging.js';
import loggerService from "../../common/logs/logger-service";
import { notifyNewApplication } from './../../survey/services/helpers/apply-notification.js';
import AWS from 'aws-sdk';
interface SurveyResult {
  userId: string;
  surveyId: string;
  value: string;
  partitionKey: string;
  sortKey: string;
  itemKey: string;
  userSearchKey: string;
  check: string;
  note?: string;
}


const getSurveyResult = async (req, res) => {
  const queryParams = req.body;
  let userId = req.userId;
  let surveyId = queryParams.surveyId;

  if (!surveyId && _.get(queryParams, 'expressionValues[:pk][S]')) {
    const keys = queryParams.expressionValues[':pk'].S.split('#');
    surveyId = keys[0];
    userId = keys[1];
  }

  let results = await surveyResultsService.getAllUserResults(surveyId, userId);
  results = surveyResultsHelper.omitNote(results);

  if (results.length > 0) {
    const configs = await surveyConfigsService.getSurveyConfig(surveyId);

    if (!configs) {
      return res.status(200).json({ result: 'ERROR', errorMessage: 'no survey config.' });
    }
  }

  await loggerService.logApiEvent({
    logType: 'result',
    endpointName: 'query',
    action: 'getSurveyResult',
    surveyId: surveyId,
    userId: userId,
    body: req.body,
  });
  res.status(200).json({ result: 'OK', data: results });
};

const bucketName = () => {
  return config.get(c.RESOURCES_BUCKET);
};

const getSurveyResultFilter = async (req, res) => {
  const { queryParams, requestingUserId } = req.body;
  checkUserId(req, requestingUserId);
  const accessCheckResult = await ResultsControlService.isSearchAllowed(requestingUserId, queryParams.surveyId);
  if (!accessCheckResult) {
    res.status(200).json({
      result: 'ERROR',
      errorMessage:
        '検索失敗の上限を超えたため、あなたのアクセスをロックしました。明日までお待ちいただき、再度検索を行ってください。',
    });
    return;
  }

  let results;
  // for Reservation Search LIFF
  try {
    const surveyConfig = await surveyConfigsService.getSurveyConfig(queryParams.surveyId);
    const surveySchema = surveyConfig?.surveySchema;
    const isTemplateForPayment = surveyConfig?.usePayment === 1;

    if (isTemplateForPayment && queryParams.surveyResults.length === 0) {
      // NOTE: 決済帳票かつ帳票に照会キーが設定されていない場合(=queryParams.surveyResults.lengthが0)はsurveyIdとorderIdで検索をする。
      const results = await surveyResultsService.getActiveResultsBySurveyIdAndOrderId(surveyConfig, queryParams.orderId, requestingUserId);
      const resultsOfRemovedNote = surveyResultsHelper.omitNote(results);
      const groupByPartitionKey = groupBy(resultsOfRemovedNote, 'partitionKey');
      const returnData = Object.entries(groupByPartitionKey).map(([pkey, results]) => {
        return {
          partitionKey: pkey,
          data: results,
        }
      });
      return res.status(200).json({ result: 'OK', data: returnData });
    }

    const indexSearcheableField = surveyResultsService.findIndexSearcheableField(surveyConfig);
    if (indexSearcheableField) {
      console.log('before: surveyResultsService.indexSearch')
      const results = await surveyResultsService.indexSearch(surveyConfig, indexSearcheableField.itemKey, queryParams, requestingUserId);
      console.log('after: surveyResultsService.indexSearch')
      const filteredResults = await validators.filterOutInvalidTransactionResults(requestingUserId, results);

      if (validators.userAllowedToFetchSearchResults(filteredResults, requestingUserId)) {
        if (filteredResults.length === 0) {
          await ResultsControlService.recordSearchAttemptFailure(requestingUserId, queryParams.surveyId);
        } else {
          filteredResults.forEach((each) => {
            each.data = surveyResultsHelper.omitNote(each.data || []);
          });

        }
        res.status(200).json({ result: 'OK', data: filteredResults });
      } else {
        await ResultsControlService.recordSearchAttemptFailure(requestingUserId, queryParams.surveyId);
        res.status(200).json({
          result: 'ERROR',
          errorMessage:
            'ご利用のLINE アカウントでは、対象データを照会することは出来ません。\n以前のアカウントで再度照会いただくか、お電話にてお問い合わせください。',
        });
      }
      return;
    }

    const paramsCount = queryParams.surveyResults.length;
    // NOTE: 決済帳票ではない場合のみここでレスポンスを返す。照会キーが設定されてない場合(paramsCount === 0となる)があるため。
    if (paramsCount === 0 && !isTemplateForPayment) {
      res.status(200).json({ result: 'ERROR', errorMessage: '管理者にお問い合わせください' });
      return;
    }

    let filterExpression = '#sortKey IN (';
    const expressionNames = {
      '#sortKey': 'sortKey',
    };
    const expressionValues = {
      ':sid': {S: queryParams.surveyId},
    };

    const categoryValuesFound: string[] = [];
    const categoryKeysFound: string[] = [];
    for (const queryItem of queryParams.surveyResults) {
      if (queryItem.value.includes('category')) {
        categoryValuesFound.push(queryItem);
        categoryKeysFound.push(queryItem.itemKey);
      }
    }

    let count = 1;
    for (const result of queryParams.surveyResults) {
      if (!categoryKeysFound.includes(result.itemKey)) {
        filterExpression += ':key' + count;
        expressionValues[':key' + count] = {S: result.itemKey + '#' + result.value};

        if (count < queryParams.surveyResults.length - categoryValuesFound.length) {
          filterExpression += ', ';
        }

        count++;
      }
    }
    filterExpression += ')';

    let categoryCount = 1;
    if (categoryValuesFound.length > 0) {
      for (const categoryVal of categoryValuesFound) {
        expressionValues[':cat' + categoryCount] = {S: categoryVal};
        filterExpression += ' OR begins_with(#sortKey, :cat' + categoryCount + ')';
        categoryCount++;
      }
    }

    const doesSearchForPayment = paramsCount === 0 && isTemplateForPayment;
    results = await surveyResultsService.querySurveyIdSortKeyIndex({
      filterExpression: doesSearchForPayment ? undefined : filterExpression,
      expressionValues: expressionValues,
      expressionNames: doesSearchForPayment ? undefined : expressionNames,
    });

    const partitionKeys: string[] = [];
    for (let i = 0; i < results.length; i++) {
      const result = results[i];
      if (!partitionKeys.includes(result.partitionKey)) {
        partitionKeys.push(result.partitionKey);
      }
    }

    results = [];
    for (let i = 0; i < partitionKeys.length; i++) {
      const partitionKey = partitionKeys[i];
      const params = {
        keyExpression: 'partitionKey = :pk',
        expressionValues: {':pk': {S: partitionKey}},
      };

      // NOTE: 決済帳票の場合はLINEユーザーIDが一致とorderIdが一致の条件を追加
      if (isTemplateForPayment && queryParams.orderId) {
        params.expressionValues[':oid'] = { S: queryParams.orderId };
        params['expressionNames'] = { '#oid': 'orderId' };
        params['filterExpression'] = '#oid = :oid';

        params.expressionValues[':uid'] = { S: requestingUserId };
        params['expressionNames']['#uid'] = 'userId';
        params['filterExpression'] += ' AND #uid = :uid';
      }
      const queryResults = await surveyResultsService.query(params);
      if (queryResults.length === 0) {
        continue;
      }

      // 値チェック（完全一致）
      const resultsToIgnore = ['取り消し'];
      let validate = true;
      const expectedNumberOfCheckboxes = new Map();
      const numberOfCheckboxesSentFromFrontEnd = new Map();

      for (let j = 0; j < queryParams.surveyResults.length; j++) {
        const surveyResult = queryParams.surveyResults[j];
        const _schemaQuestion = surveySchema.find((obj) => obj.itemKey === surveyResult.itemKey);
        if (_schemaQuestion && _schemaQuestion.type === 'checkboxes') {
          if (!expectedNumberOfCheckboxes.has(_schemaQuestion.itemKey)) {
            const expectedCount = queryResults.filter((obj) => obj.itemKey === _schemaQuestion.itemKey && obj.value);
            expectedNumberOfCheckboxes.set(_schemaQuestion.itemKey, expectedCount.length);
          }

          const valueContained = queryResults.find(
            (obj) =>
              (!('deletedAt' in obj) || !obj.deletedAt) &&
              'check' in obj &&
              !resultsToIgnore.includes(obj.check) &&
              obj.itemKey === surveyResult.itemKey &&
              obj.value === surveyResult.value
          );

          if (valueContained === undefined) {
            validate = false;
          } else {
            if (numberOfCheckboxesSentFromFrontEnd.has(_schemaQuestion.itemKey)) {
              numberOfCheckboxesSentFromFrontEnd.set(
                _schemaQuestion.itemKey,
                numberOfCheckboxesSentFromFrontEnd.get(_schemaQuestion.itemKey) + 1
              );
            } else {
              numberOfCheckboxesSentFromFrontEnd.set(_schemaQuestion.itemKey, 1);
            }
          }
        } else {
          const matchResult = queryResults.find(
            (queryResult) =>
              (!('deletedAt' in queryResult) || !queryResult.deletedAt) &&
              'check' in queryResult &&
              !resultsToIgnore.includes(queryResult.check) &&
              surveyResult.itemKey === queryResult.itemKey &&
              surveyResult.value === queryResult.value
          );
          if (matchResult === undefined) {
            validate = false;
          }
        }

        if (!validate) {
          break;
        }
      }

      //check that all checkboxes saved in the db and in the search; matched
      for (const [mapKey, mapValue] of expectedNumberOfCheckboxes) {
        if (
          !numberOfCheckboxesSentFromFrontEnd.has(mapKey) ||
          numberOfCheckboxesSentFromFrontEnd.get(mapKey) !== mapValue
        ) {
          validate = false;
        }
      }

      if (validate) {
        //sort queryResults according to survey schema
        let queryResultsFilterd = queryResults;
        let sortedResults = queryResultsFilterd;
        let  checkAdminItem: string[] = [];
        if (surveySchema) {
          checkAdminItem = surveySchema.filter((obj) => obj.isAdminItem === true).map(obj => obj.itemKey);
        }
        //skip admin item
        if (queryResults.length > 0 && checkAdminItem && checkAdminItem.length > 0) {
          queryResultsFilterd = queryResults.filter((obj) => !checkAdminItem.includes(obj.itemKey))
        }
        if (surveySchema) {
          sortedResults = queryResultsFilterd.sort((a, b) => {
            const indexA = findWithAttr(surveySchema, 'itemKey', a.itemKey);
            const indexB = findWithAttr(surveySchema, 'itemKey', b.itemKey);
            return indexA - indexB;
          });
          sortedResults = sortedResults.filter((obj) => obj.value);
        }
        results.push({ partitionKey: partitionKey, data: sortedResults });
      }
    }
    if (validators.userAllowedToFetchSearchResults(results, requestingUserId)) {
      if (results.length === 0) {
        await ResultsControlService.recordSearchAttemptFailure(requestingUserId, queryParams.surveyId);
      } else {
        results.forEach((each) => {
          each.data = surveyResultsHelper.omitNote(each.data || []);
        });
      }
      res.status(200).json({ result: 'OK', data: results });
    } else {
      await ResultsControlService.recordSearchAttemptFailure(requestingUserId, queryParams.surveyId);
      res.status(200).json({
        result: 'ERROR',
        errorMessage:
          'ご利用のLINE アカウントでは、対象データを照会することは出来ません。\n以前のアカウントで再度照会いただくか、お電話にてお問い合わせください。',
      });
    }

    await loggerService.logApiEvent({
      logType: 'result',
      endpointName: 'filterQuery',
      action: 'getSurveyResultFilter',
      surveyId: queryParams.surveyId,
      userId: '',
      body: req.body,
    });

    return;
  } catch (error: any) {
    await ResultsControlService.recordSearchAttemptFailure(requestingUserId, queryParams.surveyId);
    console.log(error);
    res.status(200).json({ result: 'ERROR', errorMessage: 'an error occured during processing' });
  }
};


const setUpDataForCreateSurveyResults = async (requestBody) => {
  const surveys = requestBody.data;

  // check the last element is oldPartitionKey or not
  const {[surveys.length - 1]: lastElement} = surveys;
  let oldPartitionKey:any = null;

  if (lastElement.oldPartitionKey) {
    oldPartitionKey = lastElement.oldPartitionKey;
    // delete last element(oldPartitionKey)
    surveys.pop();
  }
  const surveyId = surveys[0].surveyId;
  const configs = await surveyConfigsService.getSurveyConfigWithoutError(surveyId);

  const oldResults = await surveyResultsService.getSurveyResult(oldPartitionKey);
  const originalOldResultCheck = oldResults.length > 0? cloneDeep(oldResults[0].check) : null;
  const originalOldResultUserId = oldResults.length > 0? cloneDeep(oldResults[0].userId) : null;
  const originalOldResultOrderId = oldResults.length > 0 ? cloneDeep(oldResults[0].orderId) : null;
  const oldNote = oldResults.length > 0 ? cloneDeep(oldResults[0].note): null;
  if (configs && configs.isAppending && configs.isAppending.value) {
    surveys.forEach((element) => {
      element.note = oldNote;
      originalOldResultOrderId && (element.orderId = originalOldResultOrderId);
    });
  }

  oldResults.forEach((obj) => {
    if (requestBody.subData && requestBody.subData.checkStatus){
      obj.check = requestBody.subData.checkStatus;
    }
    if (requestBody.subData && requestBody.subData.items && requestBody.subData.items[0] && requestBody.subData.items[0].userId){
      obj.userId = requestBody.subData.items[0].userId;
    }
  })

  const surveyResults = surveys;

  return {
    originalOldResultUserId: originalOldResultUserId,
    originalOldResultCheck: originalOldResultCheck,
    surveyResults: surveyResults,
    oldResults: oldResults,
    configs: configs,
  }
}

const createSurveyResult = async (req, res) => {
  const v = await setUpDataForCreateSurveyResults(req.body);
  const {oldResults, originalOldResultCheck, originalOldResultUserId, configs} = v;
  let {surveyResults} = v

  const requestStartTime = moment();

  if (!configs || ('surveyStatus' in configs && configs.surveyStatus === 'disable')
    || (oldResults.length > 0 && originalOldResultCheck === '取り消し')) {
    requestContext.markError();
    return res.status(200).json({
      result: 'ERROR',
      code: !configs ? 'no_config' : 'surveyStatus' in configs && configs.surveyStatus === 'disable' ? 'disabled_config' : 'statuses_conflict',
      errorMessage: !configs ? 'no survey config.' :
        'surveyStatus' in configs && configs.surveyStatus === 'disable' ? '現在LINEからの予約を受け付けておりません。' :
          'この予約はすでに送信されています。' + c.CLOSE_GUIDANCE,
    });
  }

  if (originalOldResultUserId && originalOldResultUserId !== surveyResults[0].userId) {
    return res.status(200).json({
      result: 'ERROR',
      code: 'userid_mismatch',
      errorMessage: '別のアカウントによって更新されましたので更新ができませんでした。' + c.CLOSE_GUIDANCE,
    });
  }

  //Get the admin items from old survey results to save as new survey results
  surveyResults = [...surveyResults, ...surveyResultsHelper.getAndFormatAdminItemsFromSurveyResults(oldResults, configs, surveyResults[0])]
  surveyResults = surveyResultsHelper.fillCountVaccinesAnswerInResults(surveyResults, configs);

  const validator = new SurveyResultsValidator(configs, surveyResults);
  // const prValidator = paymentResult ? new PaymentResultsValidator(paymentResult, req.userId) : null;
  // const isValidPaymentResult = prValidator ? await prValidator.validate() : true; // TODO
  if (validator.validate()) {
    const response = await surveyResultsService.processCreatingSurveyResults(configs, surveyResults, oldResults, requestStartTime);
    await notifyNewApplication(configs as any);
    return res.status(200).json(response);
  } else {
    const validatorErrorMessage = validator.getErrorMessage();
    requestContext.markError();
    res.status(200).json({
      result: 'ERROR',
      errorMessage: '情報が更新されています。' + c.CLOSE_GUIDANCE,
      validatorErrorMessage: validatorErrorMessage,
      paymentValidatorErrorMessage: {},
      validator: {
        schema: validator.getSchema(),
        schemaByBunki: validator.schemaByConditions,
      },
      needRefresh: true,
    });
  }
};

const createSurveyResultTransaction = async (req, res) => {
  logPutItemsStart(req.body);
  const key = `createSurveyResult#${req.userId}`;
  const lockForInMilliseconds = 15 * 60 * 1000; //15 minutes in milliseconds
  if (await locks.tryToLockSurveyResult(key, lockForInMilliseconds)) {
    try {
      await createSurveyResult(req, res);
    } finally {
      await locks.releaseSurveyResult(key);
    }
  } else {
    res.status(409).json({
      code: 'temporary_locked',
      result: 'ERROR',
      errorMessage: '予約更新は処理中のため二重リクエストが弾き返された。',
    });
  }
};

const getBySurveyIdUserIdIndex = async (req, res) => {
  const {surveyId} = req.query;
  const lineUserId = req.userId;
  if (!surveyId || !lineUserId) {
    return res.status(200).json({
      result: 'ERROR',
      errorMessage: '渡されたパラメータが期待されたものと一致しません。「surveyId」と「lineUserId」が必須です。'
    });
  }

  const surveyConfig = await surveyConfigsService.getSurveyConfig(surveyId);

  let results = await surveyResultsService.getMergedResultsByUserId(surveyId, lineUserId, surveyConfig);
  results = surveyResultsHelper.omitNote(results);

  return res.status(200).json({result: 'OK', items: results});
}

const getBySurveyIdPartitionKeyIndex  = async (req, res) => {
  const { surveyId, partitionKey } = req.query;

  if (!surveyId || !partitionKey) {
    return res.status(200).json({ result: 'ERROR', errorMessage: '渡されたパラメータが期待されたものと一致しません。「surveyId」と「partitionKey」が必須です。' });
  }

  let results = await surveyResultsService.getAllSurveyResultsBySurveyIdPartitionKey(surveyId, partitionKey);
  results = surveyResultsHelper.omitNote(results);

  return res.status(200).json({ result: 'OK', data: results });
}

const downloadFileUrl = async (req, res) => {
  const { key } = req.query;
  const s3 = new AWS.S3();
  const url = await s3.getSignedUrlPromise('getObject', {
    Bucket: bucketName(),
    Key: `${key}`,
    Expires: 60 * 15, // 15 minute expiry time
  });
  res.json({
    result: 'OK',
    data: url,
  });
};


const checkReservation = async (req, res) => {
  const postData = req.body;
  const from = postData.from; // チェックする期間FROM
  const to = postData.to; // チェックする期間TO
  const targetDay = postData.targetDay; // 指定された予約日
  const maxCount = postData.maxCount; // 期間内で予約可能な件数
  const maxCountOfDay = postData.maxCountOfDay; // 1日に予約可能な件数
  const dayCheckOnly = postData.dayCheckOnly; // 同日の予約数チェックのみ

  const reservationData = await surveyResultsService.getReservationData(
      postData.surveyId,
      postData.itemKey,
      postData.userId
  );
  let reservationCount = 0;
  let reservationCountOfDay = 0;
  reservationData.forEach((data) => {
    const wkValue = data.value; //category#036366_1|20210222|1
    const values = wkValue.split('|');
    if (values.length >= 2) {
      // 予約範囲期間内での予約数チェック
      if (!dayCheckOnly && Number(from) <= Number(values[1]) && Number(to) >= Number(values[1])
          && data.partitionKey !== postData.partitionKey) {
        reservationCount += 1;
      }
      // 同日の予約数チェック
      if (Number(targetDay) === Number(values[1])
          && data.partitionKey !== postData.partitionKey) {
        reservationCountOfDay += 1;
      }
    }
  });

  if (reservationCount >= Number(maxCount) && Number(maxCount) !== 0) {
    requestContext.markError();
    res.status(200).json({
      result: 'ERROR',
      code: 'reservation_max_error',
      errorMessage: '予約期間での予約件数を超えています',
      checkDetail: `予約件数：${reservationCount + 1}件目 予約可能件数：${maxCount}件`,
      reservationCount: reservationCount,
      reservationCountOfDay: reservationCountOfDay,
    });
    return;
  }

  if (reservationCountOfDay >= Number(maxCountOfDay) && Number(maxCountOfDay) !== 0) {
    requestContext.markError();
    res.status(200).json({
      result: 'ERROR',
      code: 'reservation_max_of_day_error',
      errorMessage: '同日で予約できる件数を超えています',
      checkDetail: `予約件数：${reservationCountOfDay + 1}件目 予約可能件数：${maxCountOfDay}件`,
      reservationCount: reservationCount,
      reservationCountOfDay: reservationCountOfDay,
    });
    return;
  }
  res.status(200).json({
    result: 'OK',
    reservationCount: reservationCount,
    reservationCountOfDay: reservationCountOfDay,
  });
};


export function routes (app, opts) {
  app.post('/query', getSurveyResult);
  app.post('/filterQuery', getSurveyResultFilter);
  app.get('/getBySurveyIdUserIdIndex', getBySurveyIdUserIdIndex);
  app.post('/checkReservation', checkReservation);
  app.get('/getBySurveyIdPartitionKeyIndex', getBySurveyIdPartitionKeyIndex);
  app.get('/downloadUrl', downloadFileUrl);
}
