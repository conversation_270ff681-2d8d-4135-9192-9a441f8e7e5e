/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *  - name: MemberConfig
 *    description: 会員情報帳票API
 */

import memberConfigService from '../../members/services/member-config-service.js';
import loggerService from '../../common/logs/logger-service.js';
import { BadRequest, ServerError } from '../../common/utils/exceptions.js';

const w = (handler) => {
  return async (req, res) => {
    try {
      return await handler(req, res);
    } catch (e: any) {
      if (e.constructor.name === 'NotFound') {
        throw new BadRequest({ cause: e, code: e.code });
      } else {
        throw new ServerError({ cause: e });
      }
    }
  };
};

/**
 * @openapi
 * /db/member-configs/getAllSearchable:
 *  get:
 *    summary: 検索可能な帳票設定の一覧を返す
 *    description: SURVEY_CONFIGSテーブルより、検索可能な帳票設定の一覧を返します
 *    tags: [MemberConfig]
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 *              properties:
 *                result:
 *                  type: "string"
 *                data:
 *                  type: array
 *                  items:
 *                    $ref: '#/definitions/SurveyConfig'
 */
const getSearchableMemberConfigs = async (req, res) => {
  await loggerService.logApiEvent({
    logType: 'config',
    endpointName: 'getAllSearchable',
    action: 'getMemberConfig',
  });

  const results = await memberConfigService.getSearchableSurveyConfigs();

  res.status(200).json({ result: 'OK', data: results });
};

/**
 * @openapi
 * /db/member-configs/getItem:
 *  get:
 *    summary: 帳票設定を返す
 *    description: memberSurveyIdの値に基づき、SURVEY_CONFIGSテーブルより該当の帳票設定を返します
 *    tags: [MemberConfig]
 *    parameters:
 *     - in: query
 *       name: memberSurveyId
 *       required: true
 *       schema:
 *         type: string
 *    responses:
 *      '200':
 *        content:
 *          application/json:
 *            schema:
 *              type: "object"
 *              properties:
 *                result:
 *                  type: "string"
 *                data:
 *                  $ref: '#/definitions/SurveyConfig'
 */
const getMemberConfig = async (req, res) => {
  const memberSurveyId = req.query.memberSurveyId;

  await loggerService.logApiEvent({
    logType: 'config',
    endpointName: 'getItem',
    action: 'getMemberConfig',
    memberSurveyId: memberSurveyId,
    headers: req.query,
  });

  const config = await memberConfigService.getSurveyConfig(memberSurveyId);

  res.status(200).json({ result: 'OK', data: config });
};

export function routes(app, opts) {
  app.get('/getItem', w(getMemberConfig));
  app.get('/getAllSearchable', w(getSearchableMemberConfigs));
}