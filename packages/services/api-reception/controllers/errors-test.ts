/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

const someNestedFunc = async () => {
  return new Promise((resolve, reject) => reject(new Error('some error')));
};

const someFunc = async () => {
  return await Promise.all([someNestedFunc()]);
};

const getException = async (req, res) => {
  await someFunc();

  res.sendStatus(200);
};

const get200Error = async (req, res) => {
  res.status(200).json({
    result: 'ERROR',
    message: 'Some message',
  });
};

export function routes (app, opts) {
  app.get('/', getException);
  app.get('/200', get200Error);
  return app;
}
