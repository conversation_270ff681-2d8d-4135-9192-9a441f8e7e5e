/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

/**
 * @openapi
 * tags:
 *  - name: SurveyResults
 *    description: 帳票回答API
 */

import { Request } from 'lambda-api';
import _ from 'lodash';
import moment from 'moment';
import config from '../../api-reception/config/static.js';
import { timeUtils, uniq } from '../../common/utils/index.js';
import * as calendarExt from '../../survey/services/calendars-ext.js';
import * as calendarService from '../../survey/services/calendars-service.js';
import { surveyResultsHelper } from '../../survey/services/helpers/survey-results-helper.js';
import { surveyConfigsService } from '../../survey/services/survey-configs-service.js';
import { surveyResultsService } from '../../survey/services/survey-results-service.js';
import { SurveyResultsValidator } from '../../survey/services/survey-results-validator.js';
import { requestContext } from '../../survey/utils/execution-context.js';
import c from '../config/constants.js';
import * as locks from './helpers/dblocks.js';
import { logPutItemsStart } from './helpers/logging.js';
import { PaymentFormBuilder } from './helpers/payment-form-builder.js';

import { tryToRefundPayment } from 'api-admin-survey/services/payment.js';
import { SurveyResultsHelper } from 'api-admin-survey/services/survey-results/search/sr_index/sr_helper.js';
import { CANCEL_ERROR_CODES } from 'payment/lib/constants.js';
import loggerService from "../../common/logs/logger-service.js";
import { buildS3UploadLink } from '../../common/utils/aws-clients.js';
import { SurveyResultsHistoryHelper } from '../../survey/services/helpers/survey-results-history-helper.js';
import SurveyResultsRollbackHelper from '../../survey/services/helpers/survey-results-rollback.js';
import { CalculationType, PAYMENT_METHODS, PaymentDetail, PayType, RoundingType, TaxType } from "../../survey/types/payment.js";
import { TemporarySurveyResults } from '../../survey/types/survey-results.js';

import { CashPaymentHelper } from 'payment/api/service/helpers/cash-payment-helper.js';
import { PaymentResult } from 'payment/api/service/user/payment-results-service.js';
import { PaymentResultsValidator } from 'payment/api/service/user/payment-results-validator.js';
import { PAY_TYPES, PAYMENT_STATUSES } from 'payment/api/types/payment.js';
import { notifyNewApplication } from './../../survey/services/helpers/apply-notification.js';


interface SurveyResult {
  userId: string;
  surveyId: string;
  value: string;
  partitionKey: string;
  sortKey: string;
  itemKey: string;
  userSearchKey: string;
  check: string;
  note?: string;
}

interface SurveyResultsBody {
  data: SurveyResult[];
}

interface PaymentOrder {
  serviceSortKey: string;
  serviceId: string;
  serviceName: string;
  reservationCost: number;
  details: PaymentDetail[];
  tax: number;
  amount: number;
  taxRate: number;
  taxType: TaxType;
  calculationType: CalculationType;
  roundingType: RoundingType;
  selectedPayType?: PayType,
}

interface SurveyResultsWithPaymentBody extends SurveyResultsBody {
  order: PaymentOrder;
}

const setUpDataForCreateSurveyResults = async (requestBody) => {
  const surveys = requestBody.data;

  // check the last element is oldPartitionKey or not
  const { [surveys.length - 1]: lastElement } = surveys;
  let oldPartitionKey: any = null;

  if (lastElement.oldPartitionKey) {
    oldPartitionKey = lastElement.oldPartitionKey;
    // delete last element(oldPartitionKey)
    surveys.pop();
  }

  const surveyId = surveys[0].surveyId;
  const configs = await surveyConfigsService.getSurveyConfigWithoutError(surveyId);

  const oldResults = await surveyResultsService.getSurveyResult(oldPartitionKey);
  const originalOldResultCheck = oldResults.length > 0 ? _.cloneDeep(oldResults[0].check) : null;
  const originalOldResultUserId = oldResults.length > 0 ? _.cloneDeep(oldResults[0].userId) : null;
  const originalOldResultOrderId = oldResults.length > 0 ? _.cloneDeep(oldResults[0].orderId) : null;
  const oldNote = oldResults.length > 0 ? _.cloneDeep(oldResults[0].note) : null;
  if (configs && configs.isAppending && configs.isAppending.value) {
    surveys.forEach((element) => {
      element.note = oldNote;
      originalOldResultOrderId && (element.orderId = originalOldResultOrderId);
    });
  }

  oldResults.forEach((obj) => {
    if (requestBody.subData && requestBody.subData.checkStatus) {
      obj.check = requestBody.subData.checkStatus;
    }
    if (requestBody.subData && requestBody.subData.items && requestBody.subData.items[0] && requestBody.subData.items[0].userId) {
      obj.userId = requestBody.subData.items[0].userId;
    }
  })

  return {
    originalOldResultUserId: originalOldResultUserId,
    originalOldResultCheck: originalOldResultCheck,
    surveyResults: surveys,
    oldResults: oldResults,
    configs: configs,
  }
}

const createSurveyResult = async (req, res) => {
  const v = await setUpDataForCreateSurveyResults(req.body);
  const { oldResults, originalOldResultCheck, originalOldResultUserId, configs } = v;
  let { surveyResults } = v
  const paymentResult = req.body.order ? setUpDataForCreatePaymentResults(req.body, req.userId) : undefined;


  const requestStartTime = moment();

  if (!configs || ('surveyStatus' in configs && configs.surveyStatus === 'disable')
    || (oldResults.length > 0 && originalOldResultCheck === '取り消し')) {
    requestContext.markError();
    return res.status(200).json({
      result: 'ERROR',
      code: !configs ? 'no_config' : 'surveyStatus' in configs && configs.surveyStatus === 'disable' ? 'disabled_config' : 'statuses_conflict',
      errorMessage: !configs ? 'no survey config.' :
        'surveyStatus' in configs && configs.surveyStatus === 'disable' ? '現在LINEからの予約を受け付けておりません。' :
          'この予約はすでに送信されています。' + c.CLOSE_GUIDANCE,
    });
  }

  if (originalOldResultUserId && originalOldResultUserId !== surveyResults[0].userId) {
    return res.status(200).json({
      result: 'ERROR',
      code: 'userid_mismatch',
      errorMessage: '別のアカウントによって更新されましたので更新ができませんでした。' + c.CLOSE_GUIDANCE,
    });
  }

  if (paymentResult) {
    const { partitionKey } = paymentResult;
    const reservationQuestion = configs.surveySchema.find(item => item.type === 'reservation');
    const reservationAnswer = surveyResults.find(r => r.itemKey === reservationQuestion?.itemKey);
    if (reservationAnswer) {
      const categoryId = reservationAnswer.value.split('|')[0];
      const calendarId = await calendarService.getCalendarId(categoryId);
      if (!calendarId) {
        return res.status(200).json({
          result: 'error',
          errorMessage: '予約が混雑しており登録失敗しました。暫く時間をおいた後に、始めの手順から予約をしてください。',
        });
      }
      const calendar = await calendarExt.getCalendarInfo(calendarId);
      if (calendar.paymentServiceSortKey !== partitionKey) {
        requestContext.markError();
        return res.status(200).json({
          result: 'error',
          errorMessage: '情報が更新されています。' + c.CLOSE_GUIDANCE,
        });
      }
    }
  }

  //Get the admin items from old survey results to save as new survey results
  surveyResults = [...surveyResults, ...surveyResultsHelper.getAndFormatAdminItemsFromSurveyResults(oldResults, configs, surveyResults[0])]
  surveyResults = surveyResultsHelper.fillCountVaccinesAnswerInResults(surveyResults, configs);

  const validator = new SurveyResultsValidator(configs, surveyResults);
  const prValidator = paymentResult ? new PaymentResultsValidator(paymentResult, req.userId) : null;

  const isValidPaymentResult = prValidator ? await prValidator.validate() : true;
  if (validator.validate() && isValidPaymentResult) {
    const response = await surveyResultsService.processCreatingSurveyResults(configs, surveyResults, oldResults, requestStartTime, paymentResult);
    await notifyNewApplication(configs as any);
    return res.status(200).json(response);
  } else {
    const validatorErrorMessage = validator.getErrorMessage();
    requestContext.markError();
    res.status(200).json({
      result: 'ERROR',
      errorMessage: '情報が更新されています。' + c.CLOSE_GUIDANCE,
      validatorErrorMessage: validatorErrorMessage,
      paymentValidatorErrorMessage: {},
      validator: {
        schema: validator.getSchema(),
        schemaByBunki: validator.schemaByConditions,
      },
      needRefresh: true,
    });
  }
};

const createSurveyResultTransaction = async (req, res) => {
  logPutItemsStart(req.body);
  const key = `createSurveyResult#${req.userId}`;
  const lockForInMilliseconds = 15 * 60 * 1000; //15 minutes in milliseconds
  if (await locks.tryToLockSurveyResult(key, lockForInMilliseconds)) {
    try {
      await createSurveyResult(req, res);
    } finally {
      await locks.releaseSurveyResult(key);
    }
  } else {
    res.status(409).json({
      code: 'temporary_locked',
      result: 'ERROR',
      errorMessage: '予約更新は処理中のため二重リクエストが弾き返された。',
    });
  }
};

const bucketName = () => {
  return config.get(c.RESOURCES_BUCKET);
};

const getPresignedUrl = async (req, res) => {
  const contentType = req.query.contentType;
  const key = req.query.key;
  const response:any = {};
  const bucket = bucketName()
  const params = { 
    Bucket:bucket,
    Key: key,
    ContentType: contentType,
    ACL: 'public-read',
    Expires: 60 * 60 * 24,
  };
  try {
    const s3Link = await buildS3UploadLink('putObject', params);
    response.params = params;
    response.urlLink = s3Link;
    response.objUrl = s3Link.slice(0, s3Link.indexOf("/", 8)) + '/';
    response.result = 'SUCCESS';
  } catch (error: any) {
    console.log(error)
    response.result = 'ERROR';
    response.message = 'An error occured attempting to get presigned link';
  }
  res.json(response);
}

const cancelSurveyResultTransaction = async (req, res) => {
  const key = `cancelSurveyResult#${req.body.userId}`;
  if (await locks.tryToLockSurveyResult(key, 5000)) {
    try {
      await cancelSurveyResult(req, res);
    } finally {
      await locks.releaseSurveyResult(key);
    }
  } else {
    res.status(409).json({
      code: 'temporary_locked',
      result: 'ERROR',
      errorMessage: 'レコード更新中のためリクエストの処理をブロックしました。',
    });
  }
};

const cancelSurveyResult = async (req, res) => {
  const { surveyId, partitionKey, userId } = req.body;
  const requestStartTime = moment();

  // validation
  const configs = await surveyConfigsService.getSurveyConfig(surveyId);

  if (!configs) {
    requestContext.markError();
    return res.status(200).json({ result: 'ERROR', errorMessage: 'no survey config.' });
  }

  const results = await surveyResultsService.getSurveyResult(partitionKey);

  const validator = new SurveyResultsValidator(configs, results, {
    overrideRequiredCheck: true,
  });
  if (validator.validate()) {

    if (results.length === 0) {
      //No elements found to cancel, incorrect partitionKey sent from front end
      requestContext.markError();
      return res.status(200).json({ result: 'ERROR', errorMessage: 'キャンセルしたい項目見つけられません。' });
    }

    //Check if existing results are already deleted
    if (['取り消し', 'キャンセル'].includes(results[0].check)) {
      requestContext.markError();
      return res.status(200).json({
        result: 'ERROR',
        code: 'statuses_conflict',
        errorMessage:
          'この予約はすでに送信されています。変更されたい場合は、ブラウザを閉じてから、もう一度やりなおしてください。',
      });
    }

    const rollbackHelper = new SurveyResultsRollbackHelper(_.cloneDeep(results));
    const originalCheck = results[0].check;

    const now = Math.floor(Date.now() / 1000);
    for (const item of results) {
      item.check = 'キャンセル';
      if (userId) {
        item.userId = userId;
      }
      item.updatedAt = now;
    }

    const transactionResponse = await surveyResultsService.cancelUsingTransaction(results, configs, originalCheck, requestStartTime, rollbackHelper);

    const helper = new SurveyResultsHelper(configs as any, { resultsTable: '' });
    const paymentConfig = await helper.getPaymentConfig(results)!
    if (transactionResponse.result === 'OK') {
      const refundRes = await tryToRefundPayment(paymentConfig.serviceSortKey, results[0].partitionKey);
      // ignore not found error, because it means there was no payment at all for this answer
      if (refundRes.isError && (refundRes.code !== CANCEL_ERROR_CODES.NOT_FOUND)) {
        console.log('Refund failed', JSON.stringify(refundRes, null, 2));
        requestContext.markError();
        res.status(200).json({
          result: 'ERROR',
          errorMessage: `返金できませんでした。原因：code=${refundRes.code}, message=[${refundRes.message}]`,
        });
        return;

      }
    }

    await loggerService.logApiEvent({
      logType: 'result',
      endpointName: 'cancelItem',
      action: 'cancelSurveyResult',
      surveyId: results[0].surveyId,
      userId: results[0].userId,
      body: req.body,
    });

    if (transactionResponse.result === 'OK') {
      const resultsHistoryHelper = new SurveyResultsHistoryHelper();
      await resultsHistoryHelper.logSurveyResultsUpdateOnlyStatus(results[0].partitionKey, 'キャンセル');
    }

    return res.status(200).json(transactionResponse);
  } else {
    const validatorErrorMessage = validator.getErrorMessage();
    requestContext.markError();
    return res.status(200).json({
      result: 'ERROR',
      errorMessage: '情報が更新されています。お手数ですが、再読み込みをしてください。',
      validatorErrorMessage: validatorErrorMessage,
      validator: {
        schema: validator.getSchema(),
        schemaByBunki: validator.schemaByConditions,
      },
      needRefresh: true,
    });
  }
};

const updateUserIdByPks = async (req, res) => {
  const postData = req.body;
  const resultsHistoryHelper = new SurveyResultsHistoryHelper();

  const partitionKeys: string[] = [];
  if (postData && postData.keys.length > 0) {
    for (let index = 0; index < postData.keys.length; index++) {
      const element: string = postData.keys[index].partitionKey;
      if (!partitionKeys.includes(element)) {
        partitionKeys.push(element);
      }
    }
  }

  const actuallyUpdatedPartitionKeys = new Set();
  if (partitionKeys.length > 0) {
    for (let index = 0; index < partitionKeys.length; index++) {
      const elementPartitionKey = partitionKeys[index];
      const results = await surveyResultsService.getSurveyResult(elementPartitionKey);

      if (results.length > 0 && results[0].surveyId) {
        if (results[0].userId !== postData.userId) {
          actuallyUpdatedPartitionKeys.add(elementPartitionKey);
        }
        const configs = await surveyConfigsService.getSurveyConfig(results[0].surveyId);

        if (configs) {
          for (let index = 0; index < configs.surveySchema.length; index++) {
            const element = configs.surveySchema[index];
            if (element.isAdminItem) {
              const oldItem = results.find((e) => e.itemKey === element.itemKey);
              if (!oldItem) {
                continue;
              }

              const item = {
                partitionKey: elementPartitionKey,
                sortKey: oldItem.sortKey,
              };
              postData.keys.push(item);
            }
          }
        }
      }
    }
  }
  await surveyResultsService.updateUserIdToSurveyResultByPks(postData);
  for (const partitionKey of Array.from(actuallyUpdatedPartitionKeys)) {
    await resultsHistoryHelper.logUserIdSet(partitionKey, postData.userId);
  }
  res.status(200).json({ result: 'OK' });
};

const deleteSurveyResult = async (req, res) => {
  const surveyItem = req.body;
  await surveyResultsService.deleteSingleSurveyResult(surveyItem);
  res.status(200).json({ result: 'OK' });
};

const setUpDataForCreateTemporarySurveyResults = async (requestBody: SurveyResultsWithPaymentBody, createdAt: number, userId: string) => {
  const { data, order } = requestBody;
  const { serviceId } = order;

  if (data.length === 0) {
    return null;
  }

  const surveyId = data[0].surveyId;
  const configs = await surveyConfigsService.getSurveyConfigWithoutError(surveyId);
  // NOTE: 15桁にしたいので${serviceId(3桁)}-${11桁の半角英数}
  const orderId = `${serviceId}-${uniq.generateRandomStringFromChars(11, '0123456789abcdefghijklmnopqrstuvwxyz')}`;

  const temporarySurveyResults: TemporarySurveyResults = {
    partitionKey: 'temporary',
    sortKey: orderId,
    surveyResultsPk: data[0].partitionKey,
    userId: userId,
    status: data[0].check,
    values: data.reduce((obj, { itemKey, sortKey, userSearchKey, value }) => {
      obj[itemKey] = {
        sortKey,
        userSearchKey,
        value,
      };
      return obj;
    }, {}),
    reservationCost: order.reservationCost,
    order: {
      orderId,
      serviceId,
      serviceSortKey: order.serviceSortKey,
      serviceName: order.serviceName,
      details: order.details,
      tax: order.tax,
      amount: order.amount,
      taxRate: order.taxRate,
      taxType: order.taxType,
      calculationType: order.calculationType,
      roundingType: order.roundingType,
      selectedPayType: order.selectedPayType,
    },
    createdAt,
  };

  return {
    temporarySurveyResults,
    configs,
  }
}

const createTemporarySurveyResult = async (req: Request, res) => {
  const requestStartTime = moment();
  const { liffBaseUrl } = req.body;

  // make temporary order record
  const v = await setUpDataForCreateTemporarySurveyResults(req.body, requestStartTime.unix(), req.userId);

  // Stop if survey is not found or disabled
  if (!v || !v.configs || ('surveyStatus' in v.configs && v.configs.surveyStatus === 'disable')) {
    requestContext.markError();
    const configs = v?.configs;
    const message = !configs ? 'no survey config.' :
      'surveyStatus' in configs && configs.surveyStatus === 'disable' ? '現在LINEからの予約を受け付けておりません。' :
        'この予約はすでに送信されています。変更されたい場合は、ブラウザを閉じてから、もう一度やりなおしてください。';
    return res.status(200).json({
      message: message,
      result: 'ERROR',
      code: !configs ? 'no_config' : 'surveyStatus' in configs && configs.surveyStatus === 'disable' ? 'disabled_config' : 'statuses_conflict',
      errorMessage: message,
    });
  }
  const { temporarySurveyResults, configs } = v;
  const { order } = temporarySurveyResults;

  // validations if using calendars
  const reservationQuestion = configs.surveySchema.find(item => item.type === 'reservation');
  const reservationAnswer = temporarySurveyResults.values[reservationQuestion?.itemKey];
  if (reservationAnswer) {
    const categoryId = reservationAnswer.value.split('|')[0];
    const calendarId = await calendarService.getCalendarId(categoryId);
    if (!calendarId) {
      return res.status(200).json({
        result: 'error',
        errorMessage: '予約が混雑しており登録失敗しました。暫く時間をおいた後に、始めの手順から予約をしてください。',
      });
    }
    const calendar = await calendarExt.getCalendarInfo(calendarId);
    if (calendar.paymentServiceSortKey !== order.serviceSortKey) {
      requestContext.markError();
      return res.status(200).json({
        result: 'error',
        errorMessage: '情報が更新されています。お手数ですが、最初からやり直してください。',
      });
    }
  }

  // validate answers
  const srValidator = new SurveyResultsValidator(configs, req.body.data, {
    overrideRequiredCheck: true,
  });
  if (srValidator.validate()) {

    let callbackUrl = 'https://' + req.requestContext.domainName;
    // if (callbackUrl.endsWith('amazonaws.com')) {
    //   callbackUrl += '/Prod'
    // }
    const paymentFormBuilder = new PaymentFormBuilder({
      merchantId: process.env.PAYMENT_MERCHANT_ID,
      paymentApiKey: process.env.PAYMENT_SHA1_API_KEY,
      paymentApiUrlBase: callbackUrl,
      liffWebUrlBase: liffBaseUrl,
    });
    // save answers
    const createResult = await surveyResultsService.createTemporarySurveyResultsWithValidate(temporarySurveyResults, v.configs);

    if (createResult.result === "ERROR") {
      return res.status(200).json(createResult);
    }

    return res.status(200).json({
      code: 'success',
      orderId: order.orderId,
      form: paymentFormBuilder.buildPaymentRequest(v.configs.surveyId, temporarySurveyResults)
    });
  } else {
    const validatorErrorMessage = srValidator.getErrorMessage();
    requestContext.markError();

    const message = '情報が更新されています。お手数ですが、最初からやり直してください。';
    res.status(200).json({
      code: 'error',
      message,
      payload: {
        result: 'ERROR',
        errorMessage: message,
        validatorErrorMessage: validatorErrorMessage,
        needRefresh: true,
      },
    });
  }
};

const createTemporarySurveyResultTransaction = async (req, res) => {
  logPutItemsStart(req.body);
  const key = `createSurveyResult#${req.userId}`;
  const lockForInMilliseconds = 15 * 60 * 1000; //15 minutes in milliseconds
  if (await locks.tryToLockSurveyResult(key, lockForInMilliseconds)) {
    try {
      await createTemporarySurveyResult(req, res);
    } finally {
      await locks.releaseSurveyResult(key);
    }
  } else {
    res.status(409).json({
      code: 'temporary_locked',
      result: 'ERROR',
      errorMessage: '予約更新は処理中のため二重リクエストが弾き返された。',
    });
  }
};

const setUpDataForCreatePaymentResults = (requestBody: SurveyResultsWithPaymentBody, userId: string): PaymentResult => {
  const data = requestBody.data;
  const surveyResultItem = [...data].pop();
  const surveyResultsPartitionKey = surveyResultItem?.partitionKey;
  const surveyId = surveyResultItem?.surveyId;

  const order = requestBody.order;
  const { serviceSortKey, serviceId, serviceName, reservationCost, tax, amount, taxRate, taxType, calculationType, roundingType, selectedPayType }  = order;
  const details: PaymentDetail[] = order.details || [];
  const now = timeUtils.nowUnixSec();

  return {
    partitionKey: serviceSortKey,
    sortKey: surveyResultsPartitionKey,
    serviceId,
    serviceName,
    surveyId,
    status: PAYMENT_STATUSES.NOT_APPLICABLE,
    userId,
    orderId: `${serviceId}-${uniq.generateRandomStringFromChars(11, '0123456789abcdefghijklmnopqrstuvwxyz')}`,
    reservationCost,
    details,
    tax,
    amount,
    taxRate,
    taxType,
    selectedPayType,
    payMethod: selectedPayType === PAY_TYPES.CASH ? 'cash' : undefined,
    calculationType,
    roundingType,
    createdAt: now,
    updatedAt: now
  };
}

export function routes(app) {
  app.post('/putItem', createSurveyResultTransaction);
  app.get('/presignedURL', getPresignedUrl);
  app.post('/putUserId', updateUserIdByPks);
  app.delete('/deleteItem', deleteSurveyResult);
  app.post('/cancelItem', cancelSurveyResultTransaction);
  app.post('/temprary', createTemporarySurveyResultTransaction);
}
