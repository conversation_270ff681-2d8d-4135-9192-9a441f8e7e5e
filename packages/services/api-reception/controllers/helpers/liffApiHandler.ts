import momentTimezone from "moment-timezone";
import {setDefaultLocaleEn, setDefaultTimezoneJapan} from "../../../common/utils/time-utils.js";
import {AppConfig as Config, compose} from "../../config/config.js";
import defaultConfig from "../../config/defaults.js";
import {loadFromEnvironment} from "../../config/config-loaders.js";
import {default as staticConfig} from "../../config/static.js";
import createAPI, {API} from "lambda-api";
import {APIGatewayProxyHandlerV2} from "aws-lambda";
import {logger, onLambdaRequestStartCallback} from "../../../common/logs/logger.js";
import {APIGatewayProxyStructuredResultV2} from "aws-lambda/trigger/api-gateway-proxy.js";
import {setHeadersOnResponseJson} from "../../../common/admin-middlewares/cors.js";
import {methodsCors, optionsCors} from '../../middlewares/cors.js'

momentTimezone.tz.setDefault('Asia/Tokyo')
setDefaultTimezoneJapan();
setDefaultLocaleEn();

type HandlerFactoryOptions = {

}
const defaultHandlerFactoryOptions: HandlerFactoryOptions = {

}

const lazyInitialize = () => {
    const config = new Config(compose(defaultConfig, loadFromEnvironment()));
    staticConfig.merge(config);
};
lazyInitialize();

export const apiHandlerFactory = (base: string, routeConfiguration: (api: API) => void, options: HandlerFactoryOptions = defaultHandlerFactoryOptions): APIGatewayProxyHandlerV2<void> => {
    const api = createAPI({ base } );

    api.options(optionsCors)
    api.use(methodsCors);
    api.use(require('../../middlewares/errors.js').defaultHandler);
    api.use(async (req, res, next) => {
        if (req.query.stop) {
            res.status(401).json('Tet');
        } else {
            next();
        }
    });
    api.use(require('../../middlewares/authorization.js').build());

    routeConfiguration(api);
    return async (event, ctx) => {
        onLambdaRequestStartCallback()
        logger.debug(staticConfig.print())
        const result: APIGatewayProxyStructuredResultV2 = await api.run(event, ctx) ;
        setHeadersOnResponseJson(event, result)
        return result;
    }
}
