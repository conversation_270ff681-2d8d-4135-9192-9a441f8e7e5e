import crypto from "crypto";
import {TemporarySurveyResults} from "../../../survey/types/survey-results.js";
import { z } from 'zod'
import { DateTime}  from 'luxon'

const ConfigValidator = z.object({
    merchantId: z.string().nonempty(),
    liffWebUrlBase: z.string().nonempty(),
    paymentApiUrlBase: z.string().nonempty(),
    paymentApiKey: z.string().nonempty(),
})

export type PaymentFormBuilderConfig = {
    merchantId: string;
    liffWebUrlBase: string;
    paymentApiUrlBase: string;
    paymentApiKey: string,
}

function joinObjectValues(object: Record<string, any>) {
    return Object.values(object).reduce((accumulator, value) => {
        return accumulator + (value instanceof Array ? value.map(joinObjectValues).join("") : value);
    }, '')
}


export class PaymentFormBuilder {
    private readonly config: PaymentFormBuilderConfig;

    constructor(config: PaymentFormBuilderConfig) {
        this.config = ConfigValidator.parse(config);
    }

    public buildPaymentRequest(surveyId: string, tempResults: TemporarySurveyResults) {
        const order = tempResults.order;
        const serviceId = tempResults.order.serviceId

        function prepareOrderDetails() {
            return {
                details: order.details.map(detail => ({
                    dtl_rowno: detail.detailId,
                    dtl_item_id: detail.productId,
                    dtl_item_name: detail.productName,
                    dtl_item_count: detail.count,
                    dtl_tax: detail.tax,
                    dtl_amount: detail.amountWithTax,
                    dtl_free1: '',
                    dtl_free2: '',
                    dtl_free3: ''
                }))
            }
        }

        const payload: Record<string, any> = {
            pay_method: '',
            merchant_id: this.config.merchantId,
            service_id: serviceId,
            cust_code: tempResults.userId,
            sps_cust_no: '',
            sps_payment_no: '',
            order_id: order.orderId,
            item_id: order.serviceSortKey,
            pay_item_id: '',
            item_name: order.serviceName,
            tax: order.tax,
            amount: order.amount,
            pay_type: '0',
            auto_charge_type: '',
            service_type: '0',
            div_settele: '',
            last_charge_month: '',
            camp_type: '',
            tracking_id: '',
            terminal_type: '0',
            success_url: `${this.config.liffWebUrlBase}/completedPayment/${surveyId}`,
            cancel_url: `${this.config.liffWebUrlBase}/canceledPayment`,
            error_url: `${this.config.liffWebUrlBase}/errorPayment`,
            pagecon_url: `${this.config.paymentApiUrlBase}/survey/api/v1/payment/results/accept`,
            free1: '',
            free2: '',
            free3: '',
            free_csv: '',
            ...prepareOrderDetails(),
            request_date: DateTime.now().toFormat('yyyyMMddHHmmss'),
            limit_second: '',
        };

        const hash = crypto.createHash('sha1');
        hash.update(joinObjectValues(payload));
        hash.update(this.config.paymentApiKey);
        payload.sps_hashcode = hash.digest('hex');
        return payload;
    }
}