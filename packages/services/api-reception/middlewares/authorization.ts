/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import config from '../config/static.js';
import constants from '../config/constants.js';
import axios from 'axios';
import { infoLog } from '../../survey/utils/logger.js';
import { surveyResultsService } from '../../survey/services/survey-results-service.js';
import { requestContext } from '../../survey/utils/execution-context.js';

const verifyToken = async (token: string) => {
  const params = new URLSearchParams();
  params.append('id_token', token);
  params.append('client_id', config.get(constants.LINELOGIN_CHANNEL_ID));
  const req = axios.post('https://api.line.me/oauth2/v2.1/verify', params, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  });
  return (await req).data;
};

const getAndVerifyUser = async (req, res) => {
  let userId:any = null;
  if (config.environmentType !== 'production') {
    userId = req.headers['user-id'] || 'anonymous';
  }

  const auth = req.auth;
  const idToken = auth.type === 'Bearer' ? auth.value : null;
  if (!idToken && !userId) {
    infoLog('Authentication token missed');
    res.status(403).json({ error_description: 'Authentication token missed' });
    return;
  }
  if (idToken) {
    try {
      const result = await verifyToken(idToken);
      userId = result.sub;
    } catch (e: any) {
      const { status, data } = e.response || {};
      infoLog('Token validation failed', e.message, { status, data });
      res.status(403).json(data || {});
      return;
    }
  }
  // access auth with userId
  if (req.body) {
    let paramsUserId;
    // getSurveyResult
    let partitionKey =
      req.body.expressionValues && req.body.expressionValues[':pk'] && req.body.expressionValues[':pk'].S;
    // createSurveyResult
    if (!partitionKey) {
      partitionKey = Array.isArray(req.body) && req.body[0] && req.body[0].partitionKey;
    }
    if (partitionKey) {
      const params = {
        keyExpression: 'partitionKey = :pk',
        expressionValues: { ':pk': { S: partitionKey } },
      };

      const queryResults = await surveyResultsService.query(params);

      if (queryResults.length > 0) {
        paramsUserId = queryResults[0].userId ? queryResults[0].userId : '';
      }
    }

    if (paramsUserId && paramsUserId !== userId) {
      res.status(403).json({ message: "Could not load the user's profile" });
      return;
    }
  }

  infoLog(userId ? `Authorized, uid [${userId}]` : 'Not authorized');
  return userId;
};

const build = () => async (req, res, next) => {
  if (req.path.indexOf('/login/') !== -1) {
    next();
  } else {
    let userId;
    if (config.isDevelopmentEnv) {
      userId = req.headers['__test__user'];
    }
    if (!userId) {
      userId = await getAndVerifyUser(req, res);
    }
    if (userId) {
      req.userId = userId;
      requestContext.userId = userId;
      next();
    }
  }
};

export {
  build,
  getAndVerifyUser,
  verifyToken
};
