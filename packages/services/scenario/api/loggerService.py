"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import sys
import os
import json
import time
import logging
import traceback

import boto3

from util import DecimalEncoder, DecimalDecoder

lambdaClient = boto3.client('lambda') # Lambda reference

class Logger():
    """Class for BI logging.
    """
    logType = None

    def __init__(self, logType):
        self.log_type = logType

    def logErrorEvent(self, errorMessage, withStackTrace=True, functionName=''):
        """Logs an error that occured while calling scenario API.

        :param str errorMessage: The error message to log
        :param boolean withStackTrace: Include the stack trace in the logs
            (Only needed for none exception logs)
        :param str functionName: The name of the function that threw the error
            (Only needed for none exception logs)
        """
        try:
            if withStackTrace:
                error_message_to_log = '"[ERROR]" ' + errorMessage + '\n'
                trace_back_text = traceback.format_exc()
                logging.error(error_message_to_log + trace_back_text)
            else:
                error_message_to_log = '"[ERROR]" ' + errorMessage + ' in function ' + functionName
                logging.error(error_message_to_log)
        except:
            exc_type, exc_value, exc_traceback = sys.exc_info()
            traceback.print_exception(exc_type, exc_value, exc_traceback, limit=2, file=sys.stdout)

    def logApiEvent(self, apiName=None, header=None, body=None, userName=None):
        """Logs an event generated by the scenario API.

        :param str apiName: The api the event is associated with.
        :param dict header: API request header.
        :param str/dict body: API request body.
        :param str userName: User who called API from LineSmartCity website.
        """
        try:
            payload = {
                'logType': self.log_type,
                'endpointName': apiName,
                'createdAt': int(round(time.time() * 1000)),
                'userName': userName,
            }

            if header:
                payload['headers'] = header
            if body:
                payload['body'] = json.loads(body, cls=DecimalDecoder)
            if userName:
                payload['userName'] = userName

            lambdaClient.invoke(
                FunctionName=os.environ['LOG_LAMBDA_ARN'],
                InvocationType='Event',
                Payload=json.dumps(payload, cls=DecimalEncoder).encode('utf-8'),
            )
        except:
            self.logErrorEvent("An exception occured calling the logging to DynamoDB event.")
