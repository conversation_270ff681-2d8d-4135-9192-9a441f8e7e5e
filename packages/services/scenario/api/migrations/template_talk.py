"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os
import boto3
import copy
import json

from util import DecimalDecoder
from boto3.dynamodb.conditions import Attr

dynamodb = boto3.resource('dynamodb') # DynamoDB reference

TABLE_CHATBOT_SCENARIO_DATA = os.environ['TABLE_CHATBOT_SCENARIO_DATA']

template_scenario_names_to_talk_id = {
    "防災": "BOSAI_FLOW_TALK",
    "防災検索": "BOSAI_FLOW_SEARCH_TALK",
    "損傷報告": "DAMAGE_REPORT_TALK",
}

dir_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
template_scenario_names_to_talk_json_file = {
    "防災": os.path.join(os.path.join(dir_path, 'bosai'), 'scenario.json'),
    "防災検索": os.path.join(os.path.join(dir_path, 'bosai'), 'scenario_search.json'),
    "損傷報告": os.path.join(os.path.join(dir_path, 'damage_report'), 'scenario.json'),
}
template_scenario_names_to_template_json_talk = {
    "防災": "bosai_talk",
    "防災検索": "bosai_talk",
    "損傷報告": "damage_report_talk"
} 
template_scenario_names_to_talk_data = {
    "防災": None,
    "防災検索": None,
    "損傷報告": None,
}

def start_migration():
    print("starting Template Talk migration")
    table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)

    fetch_data = True
    itemsToMigrate = []
    func_kwargs = {
        "Select": "ALL_ATTRIBUTES",
        "FilterExpression": Attr('dataType').eq('talk'),
    }
    while fetch_data:
        response = table.scan(**func_kwargs)
        itemsToMigrate.extend(response["Items"])
        if "LastEvaluatedKey" in response:
            func_kwargs["ExclusiveStartKey"] = response["LastEvaluatedKey"]
        else:
            fetch_data = False

    itemsToMigrate = filter(lambda talk: talk.get('params') and talk.get('params').get('name') in template_scenario_names_to_talk_id, itemsToMigrate)

    for talk in itemsToMigrate:
        print(f"Fixing item {talk['dataId']} of {talk['scenario']}")
        talkName = talk.get('params').get('name')
        originalTalkData = None
        if template_scenario_names_to_talk_data.get(talkName):
            originalTalkData = template_scenario_names_to_talk_data.get(talkName)
        else:
            data = None
            with open(template_scenario_names_to_talk_json_file.get(talkName), 'r') as read_file:
                data = read_file.read()
            json_obj = json.loads(data, cls=DecimalDecoder)
            template_scenario_names_to_talk_data[talkName] = json_obj.get(template_scenario_names_to_template_json_talk.get(talkName))
            originalTalkData = template_scenario_names_to_talk_data.get(talkName)
        
        if template_scenario_names_to_talk_id.get(talkName) == talk['dataId']:
            #Only need to make sure the talk contains all the messages
            needUpdating = False
            for message in originalTalkData['messages']:
                foundInSavedItem = filter(lambda m: m.get('messageId') == message.get('messageId'), talk.get('params').get('messages'))
                if not foundInSavedItem:
                    needUpdating = True
                    talk.get('params').get('messages').append(message)
            if needUpdating:
                table.put_item(
                    Item = talk
                )
        else:
            #Need to copy the contents of the talk
            #Make sure the talk contains all template messages
            #Save a NEW item in the DB with the default talk dataId
            #Delete old item with random dataId
            newTalk = copy.deepcopy(talk)
            newTalk['dataId'] = template_scenario_names_to_talk_id.get(talkName)
            for message in originalTalkData['messages']:
                foundInSavedItem = list(filter(lambda m: m.get('messageId') == message.get('messageId'), talk.get('params').get('messages')))
                if not foundInSavedItem:
                    newTalk.get('params').get('messages').append(message)
            table.put_item(
                Item = newTalk
            )
            table.delete_item(
                Key= {
                    'scenario': talk.get('scenario'),
                    'dataId' : talk.get('dataId')
                }
            )
