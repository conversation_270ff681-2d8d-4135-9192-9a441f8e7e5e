"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os
import boto3
import copy
import json

from util import DecimalDecoder
from boto3.dynamodb.conditions import Attr, Key

dynamodb = boto3.resource('dynamodb') # DynamoDB reference

TABLE_CHATBOT_SCENARIO_DATA = os.environ['TABLE_CHATBOT_SCENARIO_DATA']
table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)

data_to_update = [
    "EARTHQUAKE_WHEREABOUTS_1",
    "EARTHQUAKE_WHEREABOUTS_1_AREA_1",
    "EARTHQUAKE_WHEREABOUTS_1_AREA_2",
    "EARTHQUAKE_WHEREABOUTS_1_AREA_3",
    "EARTHQUAKE_WHEREABOUTS_1_AREA_4",
    "EARTHQUAKE_WHEREABOUTS_1_AREA_5",
    "EARTHQUAKE_WHEREABOUTS_1_AREA_6",
    "EARTHQUAKE_WHEREABOUTS_1_AREA_7",
    "EARTHQUAKE_WHEREABOUTS_2",
    "EARTHQUAKE_WHEREABOUTS_2_AREA_1",
    "EARTHQUAKE_WHEREABOUTS_2_AREA_2",
    "EARTHQUAKE_WHEREABOUTS_2_AREA_3",
    "EARTHQUAKE_WHEREABOUTS_2_AREA_4",
    "EARTHQUAKE_WHEREABOUTS_2_AREA_5",
    "EARTHQUAKE_WHEREABOUTS_3",
    "EARTHQUAKE_WHEREABOUTS_3_AREA_1",
    "EARTHQUAKE_WHEREABOUTS_3_AREA_2",
    "EARTHQUAKE_WHEREABOUTS_3_AREA_3",
]

def query_all_scenario_data_table_by_scenario(scenarioName):
    fetch_data = True
    itemsToReturn = []    
    func_kwargs = {
        "Select": "ALL_ATTRIBUTES",
        "KeyConditionExpression": Key('scenario').eq(scenarioName),
    }
    while fetch_data:
        response = table.query(**func_kwargs)
        itemsToReturn.extend(response["Items"])
        if "LastEvaluatedKey" in response:
            func_kwargs["ExclusiveStartKey"] = response["LastEvaluatedKey"]
        else:
            fetch_data = False

    return itemsToReturn


def start_migration():
    print("starting Template Earthquake migration")
    fetch_data = True
    earthQuakeTalks = []
    func_kwargs = {
        "Select": "ALL_ATTRIBUTES",
        "FilterExpression": Attr('dataId').eq("BOSAI_EARTHQUAKE_TALK"),
    }
    while fetch_data:
        response = table.scan(**func_kwargs)
        earthQuakeTalks.extend(response["Items"])
        if "LastEvaluatedKey" in response:
            func_kwargs["ExclusiveStartKey"] = response["LastEvaluatedKey"]
        else:
            fetch_data = False

    elementsToUpdate = []
    for talk in earthQuakeTalks:
        print(f"Fixing earthquake template talk of {talk['scenario']}")
        messages = query_all_scenario_data_table_by_scenario(talk['scenario'])

        #Get all messages that need name updating
        for message in messages:
            if message.get("dataId") in data_to_update:
                message["nameLBD"] = message["nameLBD"].replace("細かい", "詳細")
                elementsToUpdate.append(message)

    with table.batch_writer() as batch:
        for elem in elementsToUpdate:
            batch.put_item(Item=elem)



        


