"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
from flask import Flask
import awsgi
import json
from flask_cors import CORS

# Flask app setup
app = Flask(__name__)
app.config['JSON_AS_ASCII'] = False
CORS(app)

cognitoUserName = 'anonymous'
cognitoUserGroups = 'none'

from services import (scenarioTable, scenarioTableData, userSessionTable, lbd_import,
                      lbd_export, special_flows, richMenuService, trash_spreadsheet, 
                      stampTitle, zipcodes, aggregateTalkLog)
from migrations.migration import run_migration

def lambda_handler(event, context):
    """AWS Lambda handler method

    :param event: Lambda trigger event.
    :param context: AWS Lambda context.
    """
    global cognitoUserName
    global cognitoUserGroups

    if event.get('DataMigrationFunction'):
        dataMigrations = event.get('DataMigrationFunction').keys()
        for migration in dataMigrations:
            run_migration(migration)
        return


    if event.get('requestContext') and event.get('requestContext').get('authorizer') and  \
        event.get('requestContext').get('authorizer').get('claims'):
        cognitoInfo = event.get('requestContext').get('authorizer').get('claims')
        cognitoUserName = cognitoInfo.get('cognito:username')
        cognitoUserGroups = cognitoInfo.get('cognito:groups')

    return awsgi.response(app, event, context, base64_content_types={"image/png"})
