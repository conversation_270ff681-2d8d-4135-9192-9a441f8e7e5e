"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os
import json

from util import DecimalDecoder, replace_instances_in_item
from special_flow_util import update_special_flow

import boto3
from boto3.dynamodb.conditions import Key
from botocore.exceptions import ClientError

dynamodb = boto3.resource('dynamodb') # DynamoDB reference
TABLE_CHATBOT_SCENARIO_DATA = os.environ['TABLE_CHATBOT_SCENARIO_DATA']

def create_scenario(scenario, version, language="", search_only=False):
    """Creates the special Bosai scenario and save it on the
    database.

    :param str scenario: The parent scenario name (from LBD)
    :param str version: The scenario version
    :param str language: ISO 639-1 language code
    :param bool search_only: True if search only (simplified) version. False if not.

    :returns str: SUCCESS or ERROR if there were any exceptions
    """
    # Set variables depedning on type of scenario
    # Disaster mode or search only
    json_name = ''
    special_talk = ''
    talk_id = ''

    if search_only:
        json_name = 'scenario_search.json'
        special_talk = '防災検索'
        talk_id = 'BOSAI_FLOW_SEARCH_TALK'
    else:
        json_name = 'scenario.json'
        special_talk = '防災'
        talk_id = 'BOSAI_FLOW_TALK'

    # read file
    dir_path = os.path.dirname(os.path.realpath(__file__))
    bosai_json = os.path.join(dir_path, json_name)

    data = None

    with open(bosai_json, 'r') as bosai_file:
        data = bosai_file.read()

    # parse file
    try:
        json_obj = json.loads(data, cls=DecimalDecoder)
        bosai_messages = json_obj['bosai_scenario']
        bosai_talk = json_obj['bosai_talk']
    except json.JSONDecodeError as js_error:
        return "ERROR", str(js_error.msg)

    scenario_id = scenario + '#' + version
    if language:
        scenario_id += '#' + language

    return update_special_flow(
        scenario_id,
        bosai_messages,
        bosai_talk,
        special_talk,
        talk_id
    )

def create_named_scenario(scenario, version, disaster_name, language="", search_only=False):
    """Creates an specific Disaster scenario and save it on the
    database.

    :param str scenario: The parent scenario name (from LBD)
    :param str version: The scenario version
    :param str language: ISO 639-1 language code
    :param bool search_only: True if search only (simplified) version. False if not.
    :param str 

    :returns str: SUCCESS or ERROR if there were any exceptions
    """
    # Set variables depending on type of scenario
    # Disaster mode or search only
    json_name = ''
    special_talk = ''
    talk_id = ''

    json_name = 'individual_scenario.json'
    special_talk = f"{disaster_name}_防災"
    talk_id = f"{disaster_name}_DISASTER_FLOW_TALK"

    # read file
    dir_path = os.path.dirname(os.path.realpath(__file__))
    disaster_json = os.path.join(dir_path, json_name)

    data = None

    with open(disaster_json, 'r') as disaster_file:
        data = disaster_file.read()

    # parse file
    try:
        json_obj = json.loads(data, cls=DecimalDecoder)
        messages = json_obj['individual_disaster_scenario']
        talk = json_obj['disaster_talk']
        dynamic_flow = json_obj['flow_mapping']
    except json.JSONDecodeError as js_error:
        return "ERROR", str(js_error.msg)

    replace_key = '{REPLACE}'

    messages = replace_instances_in_item(messages, replace_key, disaster_name)
    talk = replace_instances_in_item(talk, replace_key, disaster_name)
    dynamic_flow = replace_instances_in_item(dynamic_flow, replace_key, disaster_name)

    scenario_id = scenario + '#' + version
    if language:
        scenario_id += '#' + language

    dynamic_result = save_dynamic_flow_to_scenario(scenario_id, dynamic_flow)
    if not dynamic_result:
        return "ERROR", "save_dynamic_flow_to_scenario"

    return update_special_flow(
        scenario_id,
        messages,
        talk,
        special_talk,
        talk_id
    )

def save_dynamic_flow_to_scenario(scenario, dynamic_flow):
    try:
        table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)
        func_kwargs = {
            "Key": {
                'scenario': scenario,
                'dataId' : 'dynamicFlows'
            },
            "UpdateExpression": "SET dynamicFlows = list_append(if_not_exists(dynamicFlows, :empty_list), :new_list)",
            "ExpressionAttributeValues": {
                ":empty_list": [],
                ":new_list": [dynamic_flow]
            }
        }
        print(func_kwargs)

        response = table.update_item(**func_kwargs)
        return True
    except ClientError as client_error:
        print(f"[ERROR] Unable to update dynamic flow item of scenario {scenario} | {dynamic_flow.get('name')}")
        print(client_error)
        return False

def _create_rain_typhoon_scenario(scenario, version, language=""):
    """Creates the special Rain/Typhoon Disaster scenario and save it on the
    database.

    :param str scenario: The parent scenario name (from LBD)
    :param str version: The scenario version
    :param str language: ISO 639-1 language code

    :returns str: SUCCESS or ERROR if there were any exceptions
    """
    # Set variables depedning on type of scenario
    # Disaster mode or search only
    json_name = 'scenario_rain_typhoon.json'
    special_talk = '防災（大雨・台風）'
    talk_id = 'BOSAI_RAIN_TYPHOON_TALK'

    # read file
    dir_path = os.path.dirname(os.path.realpath(__file__))
    bosai_json = os.path.join(dir_path, json_name)

    data = None

    with open(bosai_json, 'r') as bosai_file:
        data = bosai_file.read()

    # parse file
    try:
        json_obj = json.loads(data, cls=DecimalDecoder)
        bosai_messages = json_obj['scenario']
        bosai_talk = json_obj['talk']
    except json.JSONDecodeError as js_error:
        return "ERROR", str(js_error.msg)

    scenario_id = scenario + '#' + version
    if language:
        scenario_id += '#' + language

    return update_special_flow(
        scenario_id,
        bosai_messages,
        bosai_talk,
        special_talk,
        talk_id
    )

def _create_earthquake_scenario(scenario, version, language=""):
    """Creates the special Earthquake Disaster scenario and save it on the
    database.

    :param str scenario: The parent scenario name (from LBD)
    :param str version: The scenario version
    :param str language: ISO 639-1 language code

    :returns str: SUCCESS or ERROR if there were any exceptions
    """
    # Set variables depedning on type of scenario
    # Disaster mode or search only
    json_name = 'scenario_earthquake.json'
    special_talk = '防災（地震）'
    talk_id = 'BOSAI_EARTHQUAKE_TALK'

    # read file
    dir_path = os.path.dirname(os.path.realpath(__file__))
    bosai_json = os.path.join(dir_path, json_name)

    data = None

    with open(bosai_json, 'r') as bosai_file:
        data = bosai_file.read()

    # parse file
    try:
        json_obj = json.loads(data, cls=DecimalDecoder)
        bosai_messages = json_obj['scenario']
        bosai_talk = json_obj['talk']
    except json.JSONDecodeError as js_error:
        return "ERROR", str(js_error.msg)

    scenario_id = scenario + '#' + version
    if language:
        scenario_id += '#' + language

    return update_special_flow(
        scenario_id,
        bosai_messages,
        bosai_talk,
        special_talk,
        talk_id
    )


def create_search_scenario(scenario, version, language=""):
    """Creates the special Bosai scenario (search only version) and save it on the
    database.

    :param str scenario: The parent scenario name (from LBD)
    :param str version: The scenario version
    :param str language: ISO 639-1 language code

    :returns str: SUCCESS or ERROR if there were any exceptions
    """
    return create_scenario(scenario, version, language, search_only=True)

def create_individual_scenario(scenario, version, language="", name="災害"):
    """Creates an individual Disaster scenario and save it on the
    database.

    :param str scenario: The parent scenario name (from LBD)
    :param str version: The scenario version
    :param str language: ISO 639-1 language code
    :param str name: The name of the disaster to create scenario for

    :returns str: SUCCESS or ERROR if there were any exceptions
    """
    return create_named_scenario(scenario, version, name, language, search_only=False)

def create_rain_typhoon_scenario(scenario, version, language=""):
    """Creates the special Rain Typhoon Disaster scenario and save it on the database.

    :param str scenario: The parent scenario name (from LBD)
    :param str version: The scenario version
    :param str language: ISO 639-1 language code

    :returns str: SUCCESS or ERROR if there were any exceptions
    """
    return _create_rain_typhoon_scenario(scenario, version, language)

def create_earthquake_scenario(scenario, version, language=""):
    """Creates the special EarthQuake Disaster scenario and save it on the database.

    :param str scenario: The parent scenario name (from LBD)
    :param str version: The scenario version
    :param str language: ISO 639-1 language code

    :returns str: SUCCESS or ERROR if there were any exceptions
    """
    return _create_earthquake_scenario(scenario, version, language)