"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os
import io
import csv
import uuid
import time
from decimal import Decimal
import logging

import boto3
from botocore.exceptions import ClientError

from util import encode_csv_file

TRASH_SEPARATION_TALK = "TRASH_SEPARATION_TALK"

import_message_mapping = {} # Map of already created text messages to avoid duplications

class TrashScenarioException(Exception):
    """Custom exception class for trash scenario feature.
    """

def create_text_message(text):
    """Helper method to create a text message JSON.

    :param str text: The message text
    :returns dict: The text message JSON to be stored in the DB
    """
    message = {}
    message["dataId"] = str(uuid.uuid4())
    message["dataType"] = "text"
    message["params"] = {}
    message["params"]["text"] = text
    message["params"]["specialScenarioTalk"] = "ゴミ分別"

    return message

def create_composite_message():
    """Helper method to create a composite message.
    Also creates the JSON for the contained text messages.

    :param list messages: List of text messages
    :returns dict: Composite message
    """
    composite_message = {}
    composite_message["dataId"] = str(uuid.uuid4())
    composite_message["dataType"] = "compositeMessage"
    composite_message["messages"] = []
    composite_message["params"] = {}
    composite_message["params"]["specialScenarioTalk"] = "ゴミ分別"

    return composite_message

def generate_composite_message(message_list, messages):
    """Generates a composite message.
    If no reusable composite message is found, it creates a new one.

    :param message_list: List of already created messages.
    :type message_list: list[T <= dict]
    :param messages: List of CSV text messages to generate a composite message for
    """
    # Try to reuse and existing composite message
    composite_msg = None

    for msg in message_list:
        if msg.get("dataType") == "compositeMessage":
            # Check if the messages from csv record match the existing composite message contents
            comp_msg_ids = msg.get("messages")

            # If number of messages is different, we already know it can't be reused.
            if len(messages) == len(comp_msg_ids):
                can_reuse_comp_msg = True
                for idx, csv_msg in enumerate(messages):
                    # Compare the text from CSV with the text from the message
                    # mapped by the
                    existing_id = import_message_mapping.get(csv_msg)
                    if not existing_id or existing_id != comp_msg_ids[idx]:
                        # Cannot reuse
                        can_reuse_comp_msg = False

                if can_reuse_comp_msg:
                    composite_msg = msg
                    break

    # If composite_msg is still null, we didn't find an existing item to reuse
    if not composite_msg:
        composite_msg = create_composite_message()

        for msg_text in messages:
            data_id = None
            if msg_text in import_message_mapping:
                # If text message already exists, do not duplicate
                # Just get the dataId
                data_id = import_message_mapping[msg_text]
            else:
                text_message = create_text_message(msg_text)
                data_id = text_message.get("dataId")
                message_list.append(text_message)
                import_message_mapping[msg_text] = data_id

            composite_msg["messages"].append(data_id)

        message_list.append(composite_msg)

    return composite_msg

def update_mapping_message_list(message_list, messages, text_mapping, user_input):
    """Helper method to update the message list and text mapping.
    May raise an exception in case of duplicated text mapping.

    :param message_list: List of messages to update.
    :type message_list: list[T <= dict]
    :param messages: List of text messages.
    :type messages: list[T <= str]
    :param dict text_mapping: Mapping dictionary to update (user input -> messageId)
    :param str user_input: User input associated with given messages
    :param dict: Map of existing messages (message text -> unique dataId)
    """
    if user_input and len(messages) > 0:
        # If user input is already in the text mapping, raise error and abort
        if user_input in text_mapping:
            raise csv.Error(f"ユーザーテキストマッピングが重複しています：「{user_input}」")

        if len(messages) == 1:
            # Non-composite
            msg_text = messages[0]
            data_id = None
            if msg_text in import_message_mapping:
                # If text message already exists, do not duplicate
                # Just get the dataId
                data_id = import_message_mapping[msg_text]
            else:
                text_message = create_text_message(msg_text)
                data_id = text_message.get("dataId")
                message_list.append(text_message)
                import_message_mapping[msg_text] = data_id

            text_mapping[user_input] = data_id
        else:
            # Try to reuse and existing composite message
            composite_msg = generate_composite_message(message_list, messages)

            text_mapping[user_input] = composite_msg.get("dataId")

def process_trash_csv(csv_file):
    """Process the trash separation CSV file.

    :param csv_file: The csv file to be processed (can be in-memory)
    :type cvs_file: typing.TextIO
    :returns bool: If the processing was successful.
    :returns: dict[T <= dict]: Dict of messages as dict objects (DynamoDB item format)
    :returns dict: A text mapping dict
    :returns str: Error message if processing failed.
    """
    success = True
    error_message = ""
    message_list = []
    text_mapping = {}
    seen_user_inputs = set()

    import_message_mapping.clear()

    try:
        # Last saved user text mapping.
        saved_user_input = None

        # Last saved messages list.
        saved_messages = []

        trash_reader = csv.reader(csv_file)
        next(trash_reader) # Skip headers
        for row in trash_reader:
            # Check for duplicate user inputs, allow empty rows like '' to be duplicated
            user_input = row[0]
            if user_input in seen_user_inputs and user_input.strip():
                raise csv.Error(f"重複したユーザー入力が見つかりました：{user_input}")
            seen_user_inputs.add(user_input)

            # Message text
            text_message = row[1]

            # Check if user input is empty.
            # If yes, the text message is part of a composite
            # message that is triggered by the last saved user input.
            # If not, check if the last processed message is composite or not
            if user_input:
                update_mapping_message_list(message_list, saved_messages,
                                            text_mapping, saved_user_input)
                saved_user_input = user_input
                saved_messages.clear()

            saved_messages.append(text_message)

        # After reading all lines, we still need to create the last message
        update_mapping_message_list(message_list, saved_messages,
                                    text_mapping, saved_user_input)

    except csv.Error as csv_error:
        logging.exception("Error processing trash separation CSV file.")
        success = False
        error_message = str(csv_error)
    except IndexError:
        logging.exception("Error accessing CSV column. Number of fields might be incorrect.")
        success = False
        error_message = 'CSVサイズが正しくありません。 列数を確認してください。'

    csv_file.close()

    return success, message_list, text_mapping, error_message

def create_trash_talk_object(scenario):
    """Initializes the trash talk dict.

    :param str scenario: The scenario this talk pertains to.
    :returns dict: The talk object
    """
    trash_talk_obj = {}
    trash_talk_obj["dataId"] = TRASH_SEPARATION_TALK
    trash_talk_obj["scenario"] = scenario
    trash_talk_obj["dataType"] = 'talk'
    trash_talk_obj["params"] = {}
    trash_talk_obj["params"]["name"] = "ゴミ分別"
    trash_talk_obj["params"]["displayNumber"] = 1
    trash_talk_obj["params"]["selectedRichMenuId"] = None
    trash_talk_obj["params"]["webAppList"] = []
    trash_talk_obj["params"]["messages"] = []

    return trash_talk_obj

def save_trash_data(scenario, message_list, text_mapping):
    """Save trash data into the scenario data table.

    :param str scenario: The scenario identifier. scenarioId + version + language (if applicable)
    :param message_list: List of messages to save
    :type message_list: list[T <= dict]
    :param text_mapping: Dictionary containing the trash scenario text mapping
    :type text_mapping: dict
    :returns str: Result (OK or ERROR)
    :returns int: Error code
    :returns BaseException: An exception, if applicable
    :returns list[T <= str]: List of warnings
    """
    save_result = "SUCCESS"
    code_result = 200
    save_exception = None
    warnings = []

    table = boto3.resource('dynamodb').Table(os.environ['TABLE_CHATBOT_SCENARIO_DATA'])

    # Trash separation scenario talk object
    # Messages are added while they are saved in the DB
    trash_talk_obj = create_trash_talk_object(scenario)

    def batch_write_messages():
        with table.batch_writer(overwrite_by_pkeys=['scenario', 'dataId']) as batch:
            for message in message_list:
                message["scenario"] = scenario

                # Add to the talk object
                msg = {}
                msg["messageId"] = message.get("dataId")
                msg["sender"] = 'BOT'
                msg["time"] = Decimal(int(time.time()))
                trash_talk_obj["params"]["messages"].append(msg)

                batch.put_item(Item=message)

    # Save all messages in the scenarioData table
    # Use batch writer
    try:
        batch_write_messages()
    except ClientError as client_error:
        logging.exception("Error writing trash scenario messages to DB.")
        save_result = "ERROR"
        code_result = 400
        save_exception = client_error

    # Save talk object
    try:
        table.put_item(
            Item=trash_talk_obj
        )
    except ClientError as client_error:
        logging.exception("Error saving the trash separation scenario talk object.")
        save_result = "ERROR"
        code_result = 400
        save_exception = client_error

    def update_text_mapping():
        response = table.get_item(
            Key={
                'scenario': scenario,
                'dataId': 'textMapping'
            }
        )
        item = response.get('Item')
        existing_mapping = None
        if item and item.get('textMapping'):
            existing_mapping = item.get('textMapping')
        else:
            existing_mapping = {}

        existing_mapping.update(text_mapping)

        # Update mapping
        table.update_item(
            Key={
                'scenario': scenario,
                'dataId': 'textMapping'
                },
            UpdateExpression="set #map = :textMapping",
            ExpressionAttributeValues={
                ':textMapping': existing_mapping
            },
            ExpressionAttributeNames={
                '#map': 'textMapping'
            },
            ReturnValues="UPDATED_NEW"
        )

    # Update text mapping
    # Get current text mapping and merge with the trash separation dict
    try:
        update_text_mapping()
    except ClientError as client_error:
        logging.exception("Error updating text mapping with trash scenario data.")
        save_result = "ERROR"
        code_result = 400
        save_exception = client_error

    if save_exception:
        return save_result, code_result, save_exception, warnings

    return save_result, code_result, warnings

def delete_trash_text_mapping(table, talk_obj):
    """Cleans up the text mapping by removing trash scenario keys.

    :param table: The DynamodDB scenario data table reference
    :type table: Boto3 table object
    :param dict talk_obj: The trash scenario talk item
    """

    # Text mapping dict
    scenario = talk_obj.get("scenario")
    response = table.get_item(
        Key={
            'scenario': scenario,
            'dataId': 'textMapping'
        }
    )

    item = response.get('Item')
    if item and item.get('textMapping'):
        text_mapping = item.get('textMapping')

        messages = talk_obj.get("params").get("messages")

        # For each message in the talk list, find all mappings and delete them
        for msg in messages:
            for k, value in text_mapping.copy().items():
                if value == msg.get("messageId"):
                    del text_mapping[k]

        # Save updated text_mapping
        # Update mapping
        table.update_item(
            Key={
                'scenario': scenario,
                'dataId': 'textMapping'
                },
            UpdateExpression="set #map = :textMapping",
            ExpressionAttributeValues={
                ':textMapping': text_mapping
            },
            ExpressionAttributeNames={
                '#map': 'textMapping'
            },
            ReturnValues="UPDATED_NEW"
        )

def delete_trash_messages(table, talk_obj):
    """Deletes all messages cotained in the talk object from the scenario data table.

    :param table: The DynamodDB scenario data table reference
    :type table: Boto3 table object
    :param dict talk_obj: The trash scenario talk item
    """
    # Use a batch writer
    with table.batch_writer(overwrite_by_pkeys=['scenario', 'dataId']) as batch:
        scenario = talk_obj.get("scenario")

        for message in talk_obj.get("params").get("messages"):
            batch.delete_item(
                Key={
                    'scenario': scenario,
                    'dataId': message.get("messageId")
                }
            )

def delete_trash_scenario(scenario):
    """Deletes the trash scenario data stored in the database.

    :param str scenario: Scenario string identifier (name#version)
    """
    table = boto3.resource('dynamodb').Table(os.environ['TABLE_CHATBOT_SCENARIO_DATA'])

    try:
        # Retrieve the trash scenario talk object and check if it exists.
        # If not, ignore and don't do anything
        response = table.get_item(
            Key={
                'scenario': scenario,
                'dataId': TRASH_SEPARATION_TALK
            }
        )
        talk_obj = response.get('Item')
        if talk_obj:
            # First, text mapping cleanup
            delete_trash_text_mapping(table, talk_obj)

            # Second, delete all messages
            delete_trash_messages(table, talk_obj)

            # Lastly, delete the talk object itself
            table.delete_item(
                Key={
                    'scenario': scenario,
                    'dataId' : TRASH_SEPARATION_TALK
                }
            )

    except ClientError:
        logging.exception("Boto3 error deleting trash scenario data.")

def generate_csv_rows_from_scenario(scenario):
    """Generates a list of tuples containing the trash scenario mappings.

    :param str scenario: The scenario identifier to generate the CSV for.
    :returns list[T <= tuple]: The CSV rows
    """
    def get_mapped_message(table, msg_map, mapped_id):
        """Get the mapped message
        """
        mapped_message = None
        if mapped_id in msg_map:
            mapped_message = msg_map.get(mapped_id)
        else:
            # Retrieve from DB and save on mapping
            msg_get_result = table.get_item(
                Key={
                    'scenario': scenario,
                    'dataId': mapped_id
                }
            )

            msg_item = msg_get_result.get("Item")
            if msg_item:
                msg_map[mapped_id] = {}
                msg_map[mapped_id]["type"] = msg_item.get("dataType")

                if msg_map[mapped_id]["type"] == "compositeMessage":
                    msg_map[mapped_id]["messages"] = msg_item.get("messages")
                else:
                    msg_map[mapped_id]["text"] = msg_item.get("params").get("text")

                mapped_message = msg_map[mapped_id]

        return mapped_message

    def get_cvs_rows(table, text_mapping, trash_msg_id_list):
        """Process text mapping and create the CSV rows
        """
        rows = []
        message_mapping = {} # dataId -> message mapping to improve performance

        # Header
        rows.append(('ごみ品目', '回答内容'))

        # Sort text mapping by key
        txt_map_items_sorted = sorted(text_mapping.items())
        for key, value in txt_map_items_sorted:
            if not value in trash_msg_id_list:
                continue

            mapped_message = get_mapped_message(table, message_mapping, value)

            if mapped_message and mapped_message.get("type") == "compositeMessage":
                # Composite
                for idx, msg_id in enumerate(mapped_message.get("messages")):
                    msg = get_mapped_message(table, message_mapping, msg_id)
                    if msg:
                        if idx == 0: # Only first row has the key
                            rows.append((key, msg.get("text")))
                        else:
                            rows.append(('', msg.get("text")))
            else:
                # Text
                rows.append((key, mapped_message.get("text")))

        return rows

    csv_rows = None

    # First, construct a list of IDs with all the messages (composite or text)
    # that pertain to the trash talk object (scenario)
    table = boto3.resource('dynamodb').Table(os.environ['TABLE_CHATBOT_SCENARIO_DATA'])
    trash_msg_id_list = []

    talk_get_result = table.get_item(
        Key={
            'scenario': scenario,
            'dataId': TRASH_SEPARATION_TALK
        }
    )

    trash_talk = talk_get_result.get("Item")
    if trash_talk:
        trash_msg_id_list = [msg.get("messageId")\
                            for msg in trash_talk.get("params").get("messages")]

        # Get the text mapping dict and iterate through it. If mapped ID is part of the trash talk,
        # get the text message associated and create the row.
        # Before retrieving the message from the DB, check if it's already saved and reuse it.
        txt_map_get = table.get_item(
            Key={
                'scenario': scenario,
                'dataId': 'textMapping'
            }
        )

        text_mapping = txt_map_get.get("Item")
        if text_mapping:
            csv_rows = get_cvs_rows(table, text_mapping.get("textMapping"), trash_msg_id_list)
        else:
            raise TrashScenarioException("現在のシナリオとバージョンのテキストマッピングが見つかりませんでした。")

    else:
        raise TrashScenarioException("ゴミ箱シナリオトークオブジェクトが見つかりませんでした。 エクスポートするものはありません。")

    return csv_rows

def generate_csv_download_url(csv_rows, scenario):
    """Generate a S3 bucket pre-signed URL for the CSV file.

    :param list[T <= tuple]: The list of CSV rows.
    :return str
    """
    pre_signed_url = None

    # Use in memory object to write CSV
    csv_file_string = io.StringIO()
    csv.writer(csv_file_string).writerows(csv_rows)
    csv_file_string.seek(0)

    # Bytes object to be uploaded to S3 bucket
    csv_file_bytes = encode_csv_file(csv_file_string)
    csv_file_string.close()

    if csv_file_bytes:
        csv_file_bytes.seek(0)

        # S3 upload
        s3_client = boto3.client('s3')

        bucket = os.environ['BUCKET_CHATBOT_IMPORTED_RESOURCES']
        s3_file_key = "resources/trash_csv/export/csv_trash_"  + scenario + ".csv"

        s3_client.upload_fileobj(
            csv_file_bytes,
            bucket,
            s3_file_key,
            ExtraArgs={"ContentType": "text/csv"}
        )

        csv_file_bytes.close()

        # Pre-signed URL
        pre_signed_url = s3_client.generate_presigned_url(
            ClientMethod='get_object',
            Params={'Bucket' : bucket, 'Key' : s3_file_key},
            ExpiresIn=60, # 1 minute
            HttpMethod='GET')
    else:
        raise TrashScenarioException("「shift_jis」または「utf-8」コーデックを使用してCSVデータをエンコードできませんでした。")

    return pre_signed_url

def export_trash_scenario_as_csv(scenario):
    """Converts trash scenario data and upload it to the S3 bucket.

    :param str scenario: The scenario identifier to generate the CSV for.

    :returns str: Result (OK or ERROR)
    :returns int: Error code
    :returns str: A S3 bucket pre-signed URL pointing to the created CSV.
    :returns BaseException: An exception, if applicable
    :returns list[T <= str]: List of warnings
    """
    export_result = "SUCCESS"
    code_result = 200
    export_exception = None
    warnings = []
    csv_url = None

    try:
        csv_rows = generate_csv_rows_from_scenario(scenario)
        csv_url = generate_csv_download_url(csv_rows, scenario)
    except ClientError as client_error:
        export_result = "ERROR_BOTO3"
        code_result = 400
        export_exception = client_error
    except TrashScenarioException as trash_error:
        export_result = "ERROR_TRASH_SCENARIO"
        code_result = 400
        export_exception = trash_error
    except csv.Error as csv_error:
        export_result = "ERROR_CSV"
        code_result = 400
        export_exception = csv_error

    return export_result, code_result, csv_url, export_exception, warnings
