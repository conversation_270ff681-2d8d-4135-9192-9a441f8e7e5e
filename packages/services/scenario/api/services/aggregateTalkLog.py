"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
from flask import jsonify, request, abort
import boto3
import io
from datetime import datetime, timedelta, timezone
from botocore.exceptions import ClientError
from boto3.dynamodb.conditions import Key, Attr
import time
import csv
import os
import json
from loggerService import Logger
from util import DecimalEncoder, DecimalDecoder
from app import app
from util import encode_csv_file
import uuid

logger = Logger('aggregate')
DATABASE_NAME = os.environ.get('DATABASE_NAME')
TABLE_NAME = os.environ.get('TABLE_NAME')
TABLE_BI_LOGS = os.environ.get('TABLE_BI_LOGS')
LOG_BUCKET = os.environ.get('LOG_BUCKET')
dynamodb = boto3.resource('dynamodb')
output_location_base = "s3://" + LOG_BUCKET
output_location = os.path.join(output_location_base, "results")
athena_client = boto3.client('athena')
table = dynamodb.Table(TABLE_BI_LOGS)
max_file_size = 52428800

@app.route("/scenario/api/aggregate", methods=['POST'])
def startQueryExecution():
    from app import cognitoUserName, cognitoUserGroups
    
    data = request.get_json()
    fromDate = data['fromdate']
    toDate = data['todate']
    period = fromDate + " - " + toDate
    JST = timezone(timedelta(hours=+9), 'JST')
    date = datetime.now(JST).strftime("%Y-%m-%d %H:%M:%S")
    executionUser = data['user']
    logger.logApiEvent(
        apiName='aggregate',
        body=request.get_data(),
        userName=cognitoUserName)

    # athena実行クエリを作成→実行
    try:
        sql = """
SELECT
    date_format(from_iso8601_timestamp(event_time), '%Y/%m/%d/ %H:%i:%s') AS "日時",
    sender AS "送信者",
    uid AS "UID",
    action AS "アクション",
    detail AS "詳細"
FROM {}.{} 
where substr(event_time, 1, 10) 
between '{}' and '{}' 
ORDER BY event_time;""".format('"' + DATABASE_NAME + '"', TABLE_NAME, data['fromdate'], data['todate'])
        response = athena_client.start_query_execution(
            QueryString=sql,
            QueryExecutionContext={
                'Database': DATABASE_NAME
            },
            ResultConfiguration={
                'OutputLocation': output_location
            }
        )

        query_execution_id = response['QueryExecutionId']

        #ステータス管理 初回データ登録
        status = athena_client.get_query_execution(
                QueryExecutionId=query_execution_id
            )
        item_data = {
            "DataType":"Talk",
            "Date": date,
            "status": status['QueryExecution']['Status']['State'],
            "user": executionUser,
            "period": period,
            "QueryId": query_execution_id,
            "fromDate": fromDate,
            "toDate": toDate,
        }
        table.put_item(
            Item=item_data
        )
        return jsonify(result="SUCCESS",
                        status_code=200)

    except ClientError as client_error:
        return jsonify(
            result="ERROR",
            error_message=client_error.response['Error']['Message'])

@app.route('/scenario/api/talkLogLists', methods=['GET'])
# トークログ実行履歴取得
def db_talklogresults_get_item():
    from app import cognitoUserName, cognitoUserGroups

    data_type = "Talk"
    logger.logApiEvent(
        apiName='talkLogLists',
        header=request.args,
        userName=cognitoUserName)
    result = monitorStatus(data_type)

    if result == False:
        return jsonify(result="ERROR", status_code=500)
    try:
        response = table.query(
            KeyConditionExpression=Key('DataType').eq(data_type),
        )
    except ClientError as client_error:
        logger.logErrorEvent(client_error.response['Error']['Message'])
        return jsonify(
            result="ERROR",
            error_message=client_error.response['Error']['Message'])
    else:
        result = {}
        result["result"] = "OK"
        result["item"] = response.get("Items")

        return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)


def createResultCSV(resultCSVLocation, fromDate, toDate, date):
    try:
        fileName = 'scenario-log_' + fromDate + '_' + toDate + '_' + str(uuid.uuid4()) + '.csv'
        resultCSVPath = resultCSVLocation.replace(output_location_base + "/" , "")

        s3_client = boto3.client('s3')
        s3_resource = boto3.resource('s3')

        resultCSV = s3_client.get_object(
            Bucket=LOG_BUCKET,
            Key=resultCSVPath
        )

        object_summery = s3_resource.ObjectSummary(LOG_BUCKET, resultCSVPath)

        # athena集計結果50MB以上の場合、csvファイル作成せずにnullを返却する
        if max_file_size <= object_summery.size:
            return

        csv_file_bytes = io.BytesIO(resultCSV['Body'].read().decode('utf-8').encode('utf_8_sig'))

        if csv_file_bytes:
            csv_file_bytes.seek(0)
   
            s3_client.upload_fileobj(
                csv_file_bytes,
                LOG_BUCKET,
                fileName,
                ExtraArgs={"ContentType": "text/csv"}
            )
            csv_file_bytes.close()

            pre_signed_url = s3_client.generate_presigned_url(
                ClientMethod='get_object',
                Params={'Bucket' : LOG_BUCKET, 'Key' : fileName},
                ExpiresIn=900, # 15 minute
                HttpMethod='GET')

            return pre_signed_url

    except Exception as e:
        table.update_item(
            Key={
                'DataType': "Talk",
                "Date": date,
            },
            UpdateExpression="set #st = :latestStatus",
            ExpressionAttributeNames= {
                '#st' : 'status'
            },
            ExpressionAttributeValues={
                ':latestStatus': 'FAILED',
            },
        )
        logger.logErrorEvent(e)


def monitorStatus(data_type):
    # クエリ実行中のitemのみ取得
    monitorTargets = table.query(
        KeyConditionExpression=Key('DataType').eq(data_type),
        FilterExpression=Attr('status').contains('QUEUED') | Attr('status').contains('RUNNING'),
    )

    for monitorTarget in monitorTargets.get('Items'):
        executeId = monitorTarget['QueryId']
        date = monitorTarget['Date']
        fromDate = monitorTarget['fromDate']
        toDate = monitorTarget['toDate']
        try:
            # 最新のステータスを取得
            query_status = athena_client.get_query_execution(
                QueryExecutionId=executeId
            )
            query_execution_status = query_status['QueryExecution']['Status']['State']

            table.update_item(
                Key={
                    'DataType': "Talk",
                    "Date": date,
                },
                UpdateExpression="set #st = :latestStatus",
                ExpressionAttributeNames= {
                    '#st' : 'status'
                },
                ExpressionAttributeValues={
                    ':latestStatus': query_execution_status
                },
            )

            if query_execution_status == 'SUCCEEDED':

                url = createResultCSV(query_status['QueryExecution']['ResultConfiguration']['OutputLocation'], fromDate, toDate, date)
                if url:
                    #実行ステータスをTableBiLogに保存
                    table.update_item(
                        Key={
                            'DataType': "Talk",
                            "Date": date,
                        },
                        UpdateExpression="set #st = :latestStatus, #ur = :u",
                        ExpressionAttributeNames= {
                            '#st' : 'status',
                            '#ur' : 'url'
                        },
                        ExpressionAttributeValues={
                            ':latestStatus': query_execution_status,
                            ':u': url
                        },
                    )
                else:
                    table.update_item(
                        Key={
                            'DataType': "Talk",
                            "Date": date,
                        },
                        UpdateExpression="set #st = :latestStatus",
                        ExpressionAttributeNames= {
                            '#st' : 'status'
                        },
                        ExpressionAttributeValues={
                            ':latestStatus': 'CAPACITYEXCESS',
                        },
                    )
            elif query_execution_status == 'FAILED' or query_execution_status == 'CANCELLED':
                #実行ステータスをTableBiLogに保存
                table.update_item(
                    Key={
                        'DataType': "Talk",
                        "Date": date,
                    },
                    UpdateExpression="set #st = :latestStatus",
                    ExpressionAttributeNames= {
                        '#st' : 'status'
                    },
                    ExpressionAttributeValues={
                        ':latestStatus': query_execution_status,
                    },
                )
        except ClientError as client_error:
            logger.logErrorEvent(client_error.response['Error']['Message'])
            return False

    return True