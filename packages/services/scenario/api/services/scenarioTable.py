"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os
import json

import boto3
from botocore.exceptions import ClientError, ParamValidationError
from flask import jsonify, request, abort

from app import app
from util import DecimalEncoder, DecimalDecoder
from loggerService import Logger

dynamodb = boto3.resource('dynamodb') # DynamoDB reference
logger = Logger('scenarioTable')

TABLE_CHATBOT_SCENARIO = os.environ['TABLE_CHATBOT_SCENARIO']

@app.route('/scenario/api/db/scenario/scan', methods=['POST'])
def db_scenario_scan():
    """API endpoint that scans the scenario table in DynamoDB.

    :returns str: JSON with the scan results.
    """
    from app import cognitoUserName, cognitoUserGroups
    table = dynamodb.Table(TABLE_CHATBOT_SCENARIO)

    logger.logApiEvent(apiName='scan', userName=cognitoUserName)

    request_data = request.get_data()

    # Scan table
    try:
        func_kwargs = {}

        if request_data:
            request_json = json.loads(request_data, cls=DecimalDecoder)

            # Last evaluated key
            last_evaluated_key = request_json.get("lastEvaluatedKey")
            if last_evaluated_key:
                func_kwargs["ExclusiveStartKey"] = last_evaluated_key
                last_evaluated_key = None

            filter_expression = request_json.get('filterExpression')
            if filter_expression:
                func_kwargs["FilterExpression"] = filter_expression

            expression_values = request_json.get('expressionValues')
            if expression_values:
                func_kwargs["ExpressionAttributeValues"] = expression_values

            expression_names = request_json.get('expressionNames')
            if expression_names:
                func_kwargs["ExpressionAttributeNames"] = expression_names

        response = table.scan()
        last_evaluated_key = response.get('LastEvaluatedKey')

        items = []
        items.extend(response['Items'])
    except ClientError as client_error:
        logger.logErrorEvent(client_error.response['Error']['Message'])
        return jsonify(
            result="ERROR",
            error_message=client_error.response['Error']['Message'])
    except ParamValidationError as param_error:
        return jsonify(
            result="ERROR",
            error_message=str(param_error))
    else:
        result = {}
        result["result"] = "OK"
        result["items"] = items
        if last_evaluated_key:
            result["lastEvaluatedKey"] = last_evaluated_key

        return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)

# Get a single item via scenario ID
@app.route('/scenario/api/db/scenario/getItem', methods=['GET'])
def db_scenario_get_item():
    """API endpoint that gets an item from scenario table in DynamoDB.

    :returns str: JSON with the get results.
    """
    from app import cognitoUserName, cognitoUserGroups
    table = dynamodb.Table(TABLE_CHATBOT_SCENARIO)

    scenario_id = request.args.get("scenarioId")
    logger.logApiEvent(
        apiName='getItem',
        header=request.args,
        userName=cognitoUserName)

    if scenario_id:
        try:
            response = table.get_item(
                Key={
                    'scenarioId': scenario_id
                }
            )
        except ClientError as client_error:
            logger.logErrorEvent(client_error.response['Error']['Message'])
            return jsonify(
                result="ERROR",
                error_message=client_error.response['Error']['Message'])
        else:
            result = {}
            result["result"] = "OK"
            result["item"] = response.get("Item")

            return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)
    else:
        logger.logErrorEvent("Query parameter missing. Please provide a [scenarioId] to be retrieved.", False, 'db_scenario_get_item')
        return jsonify(
            result="ERROR",
            error_message="Query parameter missing. Please provide a [scenarioId] to be retrieved.")


@app.route('/scenario/api/db/scenario/putItem', methods=['POST'])
def db_scenario_put_item():
    """API endpoint that puts an item into the scenario table in DynamoDB.

    :returns str: JSON with the putItem results.
    """
    from app import cognitoUserName, cognitoUserGroups
    table = dynamodb.Table(TABLE_CHATBOT_SCENARIO)

    if "guests" in cognitoUserGroups:
        abort(403, description="ユーザーは権限がありません")

    item_data = request.get_data()
    logger.logApiEvent(
        apiName='putItem',
        body=request.get_data(),
        userName=cognitoUserName)

    if item_data:
        try:
            item_data = json.loads(item_data, cls=DecimalDecoder)
            table.put_item(
                Item=item_data
            )
        except ClientError as client_error:
            logger.logErrorEvent(client_error.response['Error']['Message'])
            return jsonify(
                result="ERROR",
                error_message=client_error.response['Error']['Message'])
        except json.JSONDecodeError as js_error:
            logger.logErrorEvent(str(js_error.msg))
            return jsonify(
                result="ERROR",
                error_message=str(js_error.msg))
        else:
            return jsonify(result="OK")
    else:
        logger.logErrorEvent("Request JSON body is empty."
                           "Provide the JSON data of the item to be put.", False, 'db_scenario_put_item')
        return jsonify(
            result="ERROR",
            error_message=("Request JSON body is empty."
                           "Provide the JSON data of the item to be put."))

@app.route('/scenario/api/db/scenario/updateItem', methods=['POST'])
def db_scenario_update_item():
    """API endpoint that updates an item from the scenario table in DynamoDB.

    :returns str: JSON with the updateItem results.
    """
    from app import cognitoUserName, cognitoUserGroups
    table = dynamodb.Table(TABLE_CHATBOT_SCENARIO)

    if "guests" in cognitoUserGroups:
        abort(403, description="ユーザーは権限がありません")

    request_data = request.get_data()
    logger.logApiEvent(
        apiName='updateItem',
        body=request.get_data(),
        userName=cognitoUserName)

    if request_data:
        try:
            request_json = json.loads(request_data, cls=DecimalDecoder)

            scenario_id = request_json.get("scenarioId")
            update_expression = request_json.get("updateExpression")

            if scenario_id and update_expression:
                func_kwargs = {}

                func_kwargs["Key"] = {
                    'scenarioId': scenario_id
                }

                func_kwargs["UpdateExpression"] = update_expression

                expression_values = request_json.get("expressionValues")
                if expression_values:
                    func_kwargs["ExpressionAttributeValues"] = expression_values

                expression_names = request_json.get("expressionNames")
                if expression_names:
                    func_kwargs["ExpressionAttributeNames"] = expression_names

                response = table.update_item(**func_kwargs)
            else:
                logger.logErrorEvent("Body JSON is incorrect."
                                   "Either 「scenarioId」or「updateExpression」is missing.", False, 'db_scenario_update_item')
                return jsonify(
                    result="ERROR",
                    error_message=("Body JSON is incorrect."
                                   "Either 「scenarioId」or「updateExpression」is missing."))

        except ClientError as client_error:
            logger.logErrorEvent(client_error.response['Error']['Message'])
            return jsonify(
                result="ERROR",
                error_message=client_error.response['Error']['Message'])
        except json.JSONDecodeError as js_error:
            logger.logErrorEvent(str(js_error.msg))
            return jsonify(
                result="ERROR",
                error_message=str(js_error.msg))
        else:
            result = {}
            result["result"] = "OK"
            result["response"] = response

            return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)
    else:
        logger.logErrorEvent("Request JSON body is empty.", False, 'db_scenario_update_item')
        return jsonify(result="ERROR", error_message="Request JSON body is empty.")


@app.route('/scenario/api/db/scenario/deleteItem', methods=['DELETE'])
def db_scenario_delete_item():
    """API endpoint that delets an item from the scenario table in DynamoDB.

    :returns str: JSON with the updateItem results.
    """
    from app import cognitoUserName, cognitoUserGroups
    table = dynamodb.Table(TABLE_CHATBOT_SCENARIO)

    if "guests" in cognitoUserGroups:
        abort(403, description="ユーザーは権限がありません")

    scenario_id = request.args.get("scenarioId")
    logger.logApiEvent(
        apiName='deleteItem',
        header=request.args,
        userName=cognitoUserName)

    if scenario_id:
        try:
            table.delete_item(
                Key={
                    'scenarioId': scenario_id
                }
            )
        except ClientError as client_error:
            logger.logErrorEvent(client_error.response['Error']['Message'])
            return jsonify(
                result="ERROR",
                error_message=client_error.response['Error']['Message'])
        else:
            return jsonify(result="OK")
    else:
        logger.logErrorEvent("Query parameter missing."
                           "Please provide a [scenarioId] to be deleted.", False, 'db_scenario_delete_item')
        return jsonify(
            result="ERROR",
            error_message=("Query parameter missing."
                           "Please provide a [scenarioId] to be deleted."))
