"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os
import json

import boto3
from boto3.dynamodb.conditions import Key
from botocore.exceptions import ClientError
from flask import jsonify, request, abort

from app import app
from util import DecimalEncoder, DecimalDecoder
from loggerService import Logger

dynamodb = boto3.resource('dynamodb') # DynamoDB reference
logger = Logger('scenarioTableData')

TABLE_CHATBOT_SCENARIO_DATA = os.environ['TABLE_CHATBOT_SCENARIO_DATA']

@app.route('/scenario/api/db/scenarioData/scan', methods=['GET'])
def db_scenariodata_scan():
    """API endpoint that scans the scenario data table in DynamoDB.

    :returns str: JSON with the scan results.
    """
    from app import cognitoUserName, cognitoUserGroups
    table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)
    logger.logApiEvent(apiName='scan', userName=cognitoUserName)

    # Scan table
    try:
        response = table.scan()

        items = []
        items.extend(response['Items'])

        while 'LastEvaluatedKey' in response:
            response = table.scan(
                ExclusiveStartKey=response['LastEvaluatedKey']
                )
            items.extend(response['Items'])

    except ClientError as client_error:
        logger.logErrorEvent(client_error.response['Error']['Message'])
        return jsonify(
            result="ERROR",
            error_message=client_error.response['Error']['Message'])
    else:
        result = {}
        result["result"] = "OK"
        result["items"] = items

        return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)

@app.route('/scenario/api/db/scenarioData/getItem', methods=['GET'])
def db_scenariodata_get_item():
    """API endpoint that gets an item from the scenario data table in DynamoDB.

    :returns str: JSON with the scan results.
    """
    from app import cognitoUserName, cognitoUserGroups
    table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)

    scenario = request.args.get("scenario")
    data_id = request.args.get("dataId")
    logger.logApiEvent(
        apiName='getItem',
        header=request.args,
        userName=cognitoUserName)

    if scenario and data_id:
        try:
            response = table.get_item(
                Key={
                    'scenario': scenario,
                    'dataId': data_id
                }
            )
        except ClientError as client_error:
            logger.logErrorEvent(client_error.response['Error']['Message'])
            return jsonify(
                result="ERROR",
                error_message=client_error.response['Error']['Message'])
        else:
            result = {}
            result["result"] = "OK"
            result["item"] = response.get("Item")

            return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)
    else:
        logger.logErrorEvent("Query parameter missing."
                           "Please provide a [scenario] and [dataId].", False, 'db_scenariodata_get_item')
        return jsonify(
            result="ERROR",
            error_message=("Query parameter missing."
                           "Please provide a [scenario] and [dataId]."))

@app.route('/scenario/api/db/scenarioData/putItem', methods=['POST'])
def db_scenariodata_put_item():
    """API endpoint that puts an item into the scenario data table in DynamoDB.

    :returns str: JSON with the scan results.
    """
    from app import cognitoUserName, cognitoUserGroups
    if "guests" in cognitoUserGroups:
        abort(403, description="ユーザーは権限がありません")
    table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)

    item_data = request.get_data()
    logger.logApiEvent(
        apiName='putItem',
        body=request.get_data(),
        userName=cognitoUserName)

    if item_data:
        try:
            item_data = json.loads(item_data, cls=DecimalDecoder)
            table.put_item(
                Item=item_data
            )
        except ClientError as client_error:
            logger.logErrorEvent(client_error.response['Error']['Message'])
            return jsonify(
                result="ERROR",
                error_message=client_error.response['Error']['Message'])
        except json.JSONDecodeError as js_error:
            logger.logErrorEvent(str(js_error.msg))
            return jsonify(
                result="ERROR",
                error_message=str(js_error.msg))
        else:
            return jsonify(result="OK")
    else:
        logger.logErrorEvent("Request JSON body is empty."
                           "Provide the JSON data of the item to be put.", False, 'db_scenariodata_put_item')
        return jsonify(
            result="ERROR",
            error_message=("Request JSON body is empty."
                           "Provide the JSON data of the item to be put."))

@app.route('/scenario/api/db/scenarioData/updateItem', methods=['POST'])
def db_scenariodata_update_item():
    """API endpoint that updates an item of the scenario data table in DynamoDB.

    :returns str: JSON with the scan results.
    """
    from app import cognitoUserName, cognitoUserGroups
    if "guests" in cognitoUserGroups:
        abort(403, description="ユーザーは権限がありません")
    table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)

    request_data = request.get_data()
    logger.logApiEvent(
        apiName='updateItem',
        body=request.get_data(),
        userName=cognitoUserName)

    if request_data:
        try:
            request_json = json.loads(request_data, cls=DecimalDecoder)

            scenario = request_json.get("scenario")
            data_id = request_json.get("dataId")
            update_expression = request_json.get("updateExpression")

            if scenario and data_id and update_expression:
                func_kwargs = {}
                func_kwargs["Key"] = {
                    'scenario': scenario,
                    'dataId' : data_id
                }

                func_kwargs["UpdateExpression"] = update_expression

                expression_values = request_json.get("expressionValues")
                if expression_values:
                    func_kwargs["ExpressionAttributeValues"] = expression_values

                expression_names = request_json.get("expressionNames")
                if expression_names:
                    func_kwargs["ExpressionAttributeNames"] = expression_names

                response = table.update_item(**func_kwargs)
            else:
                logger.logErrorEvent("Body JSON is incorrect."
                                   "Either 「scenario」, 「dataId」or「updateExpression」is missing.", False, 'db_scenariodata_update_item')
                return jsonify(
                    result="ERROR",
                    error_message=("Body JSON is incorrect."
                                   "Either 「scenario」, 「dataId」or「updateExpression」is missing."))
        except ClientError as client_error:
            logger.logErrorEvent(client_error.response['Error']['Message'])
            return jsonify(
                result="ERROR",
                error_message=client_error.response['Error']['Message'])
        except json.JSONDecodeError as js_error:
            logger.logErrorEvent(str(js_error.msg))
            return jsonify(result="ERROR", error_message=str(js_error.msg))
        else:
            result = {}
            result["result"] = "OK"
            result["response"] = response

            return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)
    else:
        logger.logErrorEvent("Request JSON body is empty.", False, 'db_scenariodata_update_item')
        return jsonify(result="ERROR", error_message="Request JSON body is empty.")

@app.route('/scenario/api/db/scenarioData/deleteItem', methods=['DELETE'])
def db_scenariodata_delete_item():
    """API endpoint that deletes an item from the scenario data table in DynamoDB.

    :returns str: JSON with the scan results.
    """
    from app import cognitoUserName, cognitoUserGroups
    if "guests" in cognitoUserGroups:
        abort(403, description="ユーザーは権限がありません")
    table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)

    scenario = request.args.get("scenario")
    data_id = request.args.get("dataId")
    logger.logApiEvent(
        apiName='deleteItem',
        header=request.args,
        userName=cognitoUserName)

    if scenario and data_id:
        try:
            table.delete_item(
                Key={
                    'scenario': scenario,
                    'dataId' : data_id
                }
            )
        except ClientError as client_error:
            logger.logErrorEvent(client_error.response['Error']['Message'])
            return jsonify(
                result="ERROR",
                error_message=client_error.response['Error']['Message'])
        else:
            return jsonify(result="OK")
    else:
        logger.logErrorEvent("Query parameter missing."
                           "Please provide a [scenario] and [dataId] to be deleted.", False, 'db_scenariodata_delete_item')
        return jsonify(
            result="ERROR",
            error_message=("Query parameter missing."
                           "Please provide a [scenario] and [dataId] to be deleted."))

@app.route('/scenario/api/db/scenarioData/batchWriteItem', methods=['POST'])
def db_scenariodata_batchwrite_item():
    """API endpoint that writes or delets multiple items
    via a batch operation.

    :returns str: JSON with the scan results.
    """
    from app import cognitoUserName, cognitoUserGroups
    if "guests" in cognitoUserGroups:
        abort(403, description="ユーザーは権限がありません")
    table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)

    request_data = request.get_data()
    logger.logApiEvent(
        apiName='batchWriteItem',
        body=request.get_data(),
        userName=cognitoUserName)
    if request_data:
        try:
            request_json = json.loads(request_data, cls=DecimalDecoder)

            put_items = request_json.get("putItems")
            delete_items = request_json.get("deleteItems")
            option = request_json.get("option")

            if put_items or delete_items:
                if option == 1:
                    # new code to process concurrency, not delete after if do that it will remove all appended
                    with table.batch_writer(overwrite_by_pkeys=['scenario', 'dataId']) as batch:
                        if delete_items:
                            for key in delete_items:
                                if 'scenario' in key and 'dataId' in key:
                                    batch.delete_item(Key={'scenario': key['scenario'], 'dataId': key['dataId']})
                                else:
                                    logger.logErrorEvent("Query parameter missing."
                                        f"Please provide a [scenario] and [dataId] to be deleted. { key }", False, 'db_scenariodata_batchwrite_item')
                        if put_items:
                            for item in put_items:
                                batch.put_item(Item=item)
                else:
                    # as default old codes
                    with table.batch_writer(overwrite_by_pkeys=['scenario', 'dataId']) as batch:
                        if put_items:
                            for item in put_items:
                                batch.put_item(Item=item)
                        if delete_items:
                            for key in delete_items:
                                if 'scenario' in key and 'dataId' in key:
                                    batch.delete_item(Key={'scenario': key['scenario'], 'dataId': key['dataId']})
                                else:
                                    logger.logErrorEvent("Query parameter missing."
                                        f"Please provide a [scenario] and [dataId] to be deleted. { key }", False, 'db_scenariodata_batchwrite_item')

            else:
                logger.logErrorEvent("Body JSON is incorrect."
                                   "「putItems」and 「deleteItems」lists are missing."
                                   "Provided at least one to execute the batch operation.", False, 'db_scenariodata_batchwrite_item')
                return jsonify(
                    result="ERROR",
                    error_message=("Body JSON is incorrect."
                                   "「putItems」and 「deleteItems」lists are missing."
                                   "Provided at least one to execute the batch operation."))

        except ClientError as client_error:
            logger.logErrorEvent(client_error.response['Error']['Message'])
            return jsonify(
                result="ERROR",
                error_message=client_error.response['Error']['Message'])
        except json.JSONDecodeError as js_error:
            logger.logErrorEvent(str(js_error.msg))
            return jsonify(result="ERROR", error_message=str(js_error.msg))
        else:
            result = {}
            result["result"] = "OK"

            return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)
    else:
        logger.logErrorEvent("Request JSON body is empty.", False, 'db_scenariodata_batchwrite_item')
        return jsonify(result="ERROR", error_message="Request JSON body is empty.")

@app.route('/scenario/api/db/scenarioData/query', methods=['POST'])
def db_scenariodata_query():
    """API endpoint that queries the scenario data table.

    :returns str: JSON with the scan results.
    """
    from app import cognitoUserName, cognitoUserGroups
    table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)

    request_data = request.get_data()
    logger.logApiEvent(
        apiName='query',
        body=request.get_data(),
        userName=cognitoUserName)

    if request_data:
        try:
            request_json = json.loads(request_data, cls=DecimalDecoder)

            key_expression = request_json.get("keyExpression")
            expression_values = request_json.get("expressionValues")

            if key_expression and expression_values:
                func_kwargs = {}
                func_kwargs["KeyConditionExpression"] = key_expression
                func_kwargs["ExpressionAttributeValues"] = expression_values

                filter_expression = request_json.get("filterExpression")
                if filter_expression:
                    func_kwargs["FilterExpression"] = filter_expression

                expression_names = request_json.get("expressionNames")
                if expression_names:
                    func_kwargs["ExpressionAttributeNames"] = expression_names

                index_name = request_json.get("indexName")
                if index_name:
                    func_kwargs["IndexName"] = index_name

                response = table.query(**func_kwargs)

                items = []
                items.extend(response['Items'])

                while 'LastEvaluatedKey' in response:
                    response = table.query(
                        ExclusiveStartKey=response['LastEvaluatedKey'],
                        **func_kwargs
                    )
                    items.extend(response['Items'])
            else:
                logger.logErrorEvent("Body JSON is incorrect."
                                   "Either 「keyExpression」or 「expressionValues」is missing.", False, 'db_scenariodata_query')
                return jsonify(
                    result="ERROR",
                    error_message=("Body JSON is incorrect."
                                   "Either 「keyExpression」or 「expressionValues」is missing."))

        except ClientError as client_error:
            logger.logErrorEvent(client_error.response['Error']['Message'])
            return jsonify(result="ERROR", error_message=client_error.response['Error']['Message'])
        except json.JSONDecodeError as js_error:
            logger.logErrorEvent(str(js_error.msg))
            return jsonify(result="ERROR", error_message=str(js_error.msg))
        else:
            result = {}
            result["result"] = "OK"
            result["items"] = items

            return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)
    else:
        logger.logErrorEvent("Request JSON body is empty.", False, 'db_scenariodata_query')
        return jsonify(result="ERROR", error_message="Request JSON body is empty.")

@app.route('/scenario/api/db/scenarioData/queryScenario', methods=['POST'])
def db_scenariodata_query_scenario():
    """API endpoint that queries the scenario data table
    by the scenario identifier only.

    :returns str: JSON with the scan results.
    """
    from app import cognitoUserName, cognitoUserGroups
    table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)

    request_data = request.get_data()
    logger.logApiEvent(
        apiName='queryScenario',
        body=request.get_data(),
        userName=cognitoUserName)

    if request_data:
        try:
            request_json = json.loads(request_data, cls=DecimalDecoder)

            key_scenario = request_json.get("scenario")

            if key_scenario:
                func_kwargs = {}
                func_kwargs["KeyConditionExpression"] = Key("scenario").eq(key_scenario)

                expression_values = request_json.get("expressionValues")
                if expression_values:
                    func_kwargs["ExpressionAttributeValues"] = expression_values

                filter_expression = request_json.get("filterExpression")
                if filter_expression:
                    func_kwargs["FilterExpression"] = filter_expression

                expression_names = request_json.get("expressionNames")
                if expression_names:
                    func_kwargs["ExpressionAttributeNames"] = expression_names

                response = table.query(**func_kwargs)

                items = []
                items.extend(response['Items'])

                while 'LastEvaluatedKey' in response:
                    response = table.query(
                        ExclusiveStartKey=response['LastEvaluatedKey'],
                        **func_kwargs
                    )
                    items.extend(response['Items'])
            else:
                logger.logErrorEvent("Body JSON is incorrect. 「scenario」is missing.", False, 'db_scenariodata_query_scenario')
                return jsonify(
                    result="ERROR",
                    error_message="Body JSON is incorrect. 「scenario」is missing.")

        except ClientError as client_error:
            logger.logErrorEvent(client_error.response['Error']['Message'])
            return jsonify(
                result="ERROR",
                error_message=client_error.response['Error']['Message'])
        except json.JSONDecodeError as js_error:
            logger.logErrorEvent(str(js_error.msg))
            return jsonify(result="ERROR", error_message=str(js_error.msg))
        else:
            result = {}
            result["result"] = "OK"
            result["items"] = items

            return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)
    else:
        logger.logErrorEvent("Request JSON body is empty.", False, 'db_scenariodata_query_scenario')
        return jsonify(result="ERROR", error_message="Request JSON body is empty.")

@app.route('/scenario/api/db/scenarioData/queryScenarioPaginated', methods=['POST'])
def db_scenariodata_query_scenario_paginated():
    """API endpoint that queries the scenario data table
    by the scenario identifier only.

    This is a paginated version of the normal query endpoint.
    It returns items in parts according to the paginated response from DynamoDB.
    The requester must pass a "lastEvaluatedKey" parameter gotten from a previous response
    to get the next batch of items.

    :returns str: JSON with the scan results.
    """
    from app import cognitoUserName, cognitoUserGroups
    table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)

    request_data = request.get_data()
    logger.logApiEvent(
        apiName='queryScenario',
        body=request.get_data(),
        userName=cognitoUserName)

    if request_data:
        try:
            request_json = json.loads(request_data, cls=DecimalDecoder)

            key_scenario = request_json.get("scenario")

            if key_scenario:
                last_evaluated_key = request_json.get("lastEvaluatedKey")

                func_kwargs = {}
                func_kwargs["KeyConditionExpression"] = Key("scenario").eq(key_scenario)

                expression_values = request_json.get("expressionValues")
                if expression_values:
                    func_kwargs["ExpressionAttributeValues"] = expression_values

                filter_expression = request_json.get("filterExpression")
                if filter_expression:
                    func_kwargs["FilterExpression"] = filter_expression

                expression_names = request_json.get("expressionNames")
                if expression_names:
                    func_kwargs["ExpressionAttributeNames"] = expression_names

                if last_evaluated_key:
                    func_kwargs["ExclusiveStartKey"] = last_evaluated_key

                response = table.query(**func_kwargs)
                last_evaluated_key = response.get('LastEvaluatedKey')

                items = []
                items.extend(response['Items'])
            else:
                logger.logErrorEvent(
                    "Body JSON is incorrect. 「scenario」is missing.",
                    False,
                    'db_scenariodata_query_scenario'
                )
                return jsonify(
                    result="ERROR",
                    error_message="Body JSON is incorrect. 「scenario」is missing.")

        except ClientError as client_error:
            logger.logErrorEvent(client_error.response['Error']['Message'])
            return jsonify(
                result="ERROR",
                error_message=client_error.response['Error']['Message'])
        except json.JSONDecodeError as js_error:
            logger.logErrorEvent(str(js_error.msg))
            return jsonify(result="ERROR", error_message=str(js_error.msg))
        else:
            result = {}
            result["result"] = "OK"
            result["items"] = items
            if last_evaluated_key:
                result["lastEvaluatedKey"] = last_evaluated_key

            return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)
    else:
        logger.logErrorEvent("Request JSON body is empty.", False, 'db_scenariodata_query_scenario')
        return jsonify(result="ERROR", error_message="Request JSON body is empty.")

@app.route('/scenario/api/db/scenarioData/queryScenarioDataType', methods=['POST'])
def db_scenariodata_query_scenario_datatype():
    """API endpoint that queries the scenario data table
    by the scenario and datatype, using a secondary index.

    :returns str: JSON with the scan results.
    """
    from app import cognitoUserName, cognitoUserGroups
    table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)
    INDEX_NAME = "scenario-dataType-index"

    request_data = request.get_data()
    logger.logApiEvent(
        apiName='queryScenarioDataType',
        body=request.get_data(), userName=cognitoUserName)

    if request_data:
        try:
            request_json = json.loads(request_data, cls=DecimalDecoder)

            key_scenario = request_json.get("scenario")
            data_type = request_json.get("dataType")

            if key_scenario and data_type:
                func_kwargs = {}
                func_kwargs["IndexName"] = INDEX_NAME
                func_kwargs["KeyConditionExpression"] = Key("scenario").eq(key_scenario)\
                                                        & Key("dataType").eq(data_type)

                expression_values = request_json.get("expressionValues")
                if expression_values:
                    func_kwargs["ExpressionAttributeValues"] = expression_values

                filter_expression = request_json.get("filterExpression")
                if filter_expression:
                    func_kwargs["FilterExpression"] = filter_expression

                expression_names = request_json.get("expressionNames")
                if expression_names:
                    func_kwargs["ExpressionAttributeNames"] = expression_names

                response = table.query(**func_kwargs)

                items = []
                items.extend(response['Items'])

                while 'LastEvaluatedKey' in response:
                    response = table.query(
                        ExclusiveStartKey=response['LastEvaluatedKey'],
                        **func_kwargs
                    )
                    items.extend(response['Items'])
            else:
                logger.logErrorEvent("Body JSON is incorrect."
                                   "「scenario」or 「dataType」is missing.", False, 'db_scenariodata_query_scenario_datatype')
                return jsonify(
                    result="ERROR",
                    error_message=("Body JSON is incorrect."
                                   "「scenario」or 「dataType」is missing."))

        except ClientError as client_error:
            logger.logErrorEvent(client_error.response['Error']['Message'])
            return jsonify(result="ERROR", error_message=client_error.response['Error']['Message'])
        except json.JSONDecodeError as js_error:
            logger.logErrorEvent(str(js_error.msg))
            return jsonify(result="ERROR", error_message=str(js_error.msg))
        else:
            result = {}
            result["result"] = "OK"
            result["items"] = items

            return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)
    else:
        logger.logErrorEvent("Request JSON body is empty.", False, 'db_scenariodata_query_scenario_datatype')
        return jsonify(result="ERROR", error_message="Request JSON body is empty.")

@app.route('/scenario/api/db/scenarioData/queryDataTypeDataId', methods=['POST'])
def db_scenariodata_query_datatype_dataid():
    """API endpoint that queries the scenario data table
    by the dataType and dataId, using a secondary index.

    :returns str: JSON with the scan results.
    """
    from app import cognitoUserName, cognitoUserGroups
    table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)
    INDEX_NAME = "gsi-dataType-dataId-index"

    request_data = request.get_data()
    logger.logApiEvent(
        apiName='queryDataTypeDataId',
        body=request.get_data(),
        userName=cognitoUserName)

    if request_data:
        try:
            request_json = json.loads(request_data, cls=DecimalDecoder)

            data_type = request_json.get("dataType")
            data_id = request_json.get("dataId")

            func_kwargs = {}
            func_kwargs["IndexName"] = INDEX_NAME

            if data_type and data_id:
                func_kwargs["KeyConditionExpression"] = Key("dataType").eq(data_type)\
                                                        & Key("dataId").eq(data_id)
            elif data_type:
                func_kwargs["KeyConditionExpression"] = Key("dataType").eq(data_type)
            else:
                logger.logErrorEvent("Body JSON is incorrect. 「dataType」is missing.", False, 'db_scenariodata_query_datatype_dataid')
                return jsonify(
                    result="ERROR",
                    error_message="Body JSON is incorrect. 「dataType」is missing.")

            expression_values = request_json.get("expressionValues")
            if expression_values:
                func_kwargs["ExpressionAttributeValues"] = expression_values

            filter_expression = request_json.get("filterExpression")
            if filter_expression:
                func_kwargs["FilterExpression"] = filter_expression

            expression_names = request_json.get("expressionNames")
            if expression_names:
                func_kwargs["ExpressionAttributeNames"] = expression_names

            response = table.query(**func_kwargs)

            items = []
            items.extend(response['Items'])

            while 'LastEvaluatedKey' in response:
                response = table.query(
                    ExclusiveStartKey=response['LastEvaluatedKey'],
                    **func_kwargs
                )
                items.extend(response['Items'])

        except ClientError as client_error:
            logger.logErrorEvent(client_error.response['Error']['Message'])
            return jsonify(result="ERROR", error_message=client_error.response['Error']['Message'])
        except json.JSONDecodeError as js_error:
            logger.logErrorEvent(str(js_error.msg))
            return jsonify(result="ERROR", error_message=str(js_error.msg))
        else:
            result = {}
            result["result"] = "OK"
            result["items"] = items

            return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)
    else:
        logger.logErrorEvent("Request JSON body is empty.", False, 'db_scenariodata_query_datatype_dataid')
        return jsonify(result="ERROR", error_message="Request JSON body is empty.")

"""
Specific Utility functions
"""
@app.route('/scenario/api/db/scenarioData/updateTalkName', methods=['POST'])
def db_scenariodata_update_talk_name():
    """API endpoint that updates the name of a talk.
    Needs to update the name in params of the talk object
    as well as the talkName on all messages that are part of the talk

    :returns str: JSON with function results.
    """
    from app import cognitoUserName, cognitoUserGroups
    table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)

    request_data = request.get_data()
    logger.logApiEvent(
        apiName='updateTalkName',
        body=request.get_data(),
        userName=cognitoUserName)

    if request_data:
        request_json = json.loads(request_data, cls=DecimalDecoder)

        scenarioId = request_json.get("scenarioId")
        versionId = request_json.get("versionId")
        talkDataId = request_json.get("talkDataId")
        newTalkName = request_json.get("newTalkName")

        if scenarioId and versionId and talkDataId and newTalkName:
            try:
                #Attempt to fetch the talk object
                talk_fetch_kwargs = {
                    "KeyConditionExpression": Key("scenario").eq(f"{scenarioId}#{versionId}")\
                                                & Key("dataId").eq(talkDataId)
                }
                response = table.query(**talk_fetch_kwargs)
                if not 'Items' in response or len(response['Items']) != 1 or not response['Items'][0].get("dataType") == 'talk':
                    return jsonify(result="ERROR", error_message="Unable to find talk using provided talkId")

                talkObject = response['Items'][0]

                #Update the name inside of the talk object
                talk_set_name_kwargs = {
                    "Key": {
                        "scenario": f"{scenarioId}#{versionId}",
                        "dataId": talkDataId
                    },
                    "UpdateExpression": "set params.#n = :n",
                    "ExpressionAttributeNames": {
                        "#n": "name"
                    },
                    "ExpressionAttributeValues": {
                        ":n": newTalkName
                    },
                    "ReturnValues": "UPDATED_NEW"
                }
                talk_update = table.update_item(**talk_set_name_kwargs)

                #Update the name inside of the individual messages
                for message in talkObject.get('params').get('messages'):
                    if message.get('sender') == 'BOT' and message.get('messageId'):
                        message_set_talk_name_kwargs = {
                            "Key": {
                                "scenario": f"{scenarioId}#{versionId}",
                                "dataId": message.get('messageId')
                            },
                            "UpdateExpression": "set #t = :t",
                            "ExpressionAttributeNames": {
                                "#t": "talk"
                            },
                            "ExpressionAttributeValues": {
                                ":t": newTalkName
                            },
                            "ReturnValues": "UPDATED_NEW"
                        }
                        message_update = table.update_item(**message_set_talk_name_kwargs)

                result = {
                    "result": "OK"
                }

                return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)
            except ClientError as client_error:
                logger.logErrorEvent(client_error.response['Error']['Message'])
                return jsonify(result="ERROR", error_message=client_error.response['Error']['Message'])
            except json.JSONDecodeError as js_error:
                logger.logErrorEvent(str(js_error.msg))
                return jsonify(result="ERROR", error_message=str(js_error.msg))
        else:
            logger.logErrorEvent("Request JSON does not contain needed params.", False, 'db_scenariodata_update_talk_name')
            return jsonify(result="ERROR", error_message="Request JSON missing required params.")
    else:
        logger.logErrorEvent("Request JSON body is empty.", False, 'db_scenariodata_update_talk_name')
        return jsonify(result="ERROR", error_message="Request JSON body is empty.")

    
    
