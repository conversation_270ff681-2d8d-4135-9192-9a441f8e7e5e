"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
from flask import jsonify, request, abort

from app import app
from lbd_export.export_manager import ExportManager
from loggerService import Logger

logger = Logger('export')

@app.route("/scenario/api/exportLBD", methods=['POST'])
def export_lbd_file():
    """Endpoint for exporting scenario data into a valid
    Line Bot Designer file.
    """
    from app import cognitoUserName, cognitoUserGroups
    data = request.get_json()
    logger.logApiEvent(
        apiName='exportLBD',
        body=request.get_data(),
        userName=cognitoUserName)

    if data and data.get("scenario") and data.get("environment"):
        export_manager = ExportManager(data['scenario'], data['environment'])
        result = export_manager.export()
        if result[0] == "SUCCESS":
            return jsonify(
                result=result[0],
                status_code=result[1],
                exception=str(result[2]),
                warning=result[3],
                url=result[4])
        logger.logErrorEvent('An exception occured while exporting scenario.', False, 'export_lbd_file')
        return jsonify(
            result=result[0],
            status_code=result[1],
            exception=str(result[2]),
            warning=result[3],
            last_item=result[4])
    logger.logErrorEvent("Missing parameters."
                              "Please provide the download path and the scenario.", False, 'export_lbd_file')
    return jsonify(result="ERROR",
                   status_code=400,
                   exception=("Missing parameters."
                              "Please provide the download path and the scenario."))
