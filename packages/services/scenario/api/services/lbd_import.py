"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os
import json

import boto3
from botocore.client import Config
from botocore.exceptions import ClientError
from flask import jsonify, request, abort

from app import app
from lbd_import.import_manager import ImportManager
from util import DecimalEncoder
from loggerService import Logger

logger = Logger('import')

@app.route("/scenario/api/importLBD", methods=['POST'])
def import_lbd_file():
    """API endpoint for import Line Bot Designer file
    and saving scenario data into AWWS DynamoDB.
    """
    from app import cognitoUserName, cognitoUserGroups
    if "guests" in cognitoUserGroups:
        abort(403, description="ユーザーは権限がありません")

    data = request.get_json()
    logger.logApiEvent(
        apiName='importLBD',
        body=request.get_data(),
        userName=cognitoUserName
    )

    if data and data.get("key") and data.get("version"):
        language = None
        if 'lang' in data:
            language = data['lang']
        active = False
        if 'active' in data:
            active = data['active']

        try:
            import_manager = ImportManager(data['key'], data['version'], active, language)
            import_manager.read_lbd()
            result = import_manager.update_db()
            if len(result) > 3:
                logger.logErrorEvent(str(result[2]), False, 'import_lbd_file')
                return jsonify(
                    result=result[0],
                    status_code=result[1],
                    exception=str(result[2]),
                    warning=result[3]
                )

            return jsonify(result=result[0], status_code=result[1], warning=result[2])

        except (OSError, json.JSONDecodeError) as exception:
            logger.logErrorEvent(str(exception))
            return jsonify(result="ERROR", status_code=400, exception=str(exception))
    else:
        logger.logErrorEvent("Missing parameters. "
                                  "Please provide the S3 Bucket key for"
                                  "the LBD file and the version name to be saved.", False, 'import_lbd_file')
        return jsonify(result="ERROR",
                       status_code=400,
                       exception=("Missing parameters. "
                                  "Please provide the S3 Bucket key for"
                                  "the LBD file and the version name to be saved."))

@app.route("/scenario/api/presignedURL", methods=['GET'])
def create_presigned_url():
    """API endpoint that creates a pre signed URL for the
    chatbot imported resources S3 bucket.
    Can be used to upload files.

    :returns str: A pre signed URL to upload a file to an S3 bucket.
    """
    from app import cognitoUserName, cognitoUserGroups
    if "guests" in cognitoUserGroups:
        abort(403, description="ユーザーは権限がありません")

    data = request.args.get("objectName")
    logger.logApiEvent(
        apiName='presignedURL',
        header=request.args,
        userName=cognitoUserName)

    if data:
        region = os.environ['AWS_REGION']

        bucket_name = os.environ['BUCKET_CHATBOT_IMPORTED_RESOURCES']

        folder_name = request.args.get("folderName")

        object_name = None

        if folder_name:
            object_name = folder_name + "/" + data
        else:
            object_name = "LBD-Files/" + data # Preset key

        expiration = 60  # 1 minute

        content_type = request.args.get("contentType")

        #Generate a presigned URL for S3 object
        s3_client = boto3.client(
            's3',
            endpoint_url='https://s3.' + region + '.amazonaws.com',
            config=Config(s3={'addressing_style': 'virtual'})
        )
        try:
            fields = None
            conditions = []

            if content_type:
                fields = {
                    "Content-Type":content_type
                }

                conditions.append({
                    "Content-Type":content_type
                })

            response = s3_client.generate_presigned_post(
                bucket_name,
                object_name,
                Fields=fields,
                Conditions=conditions,
                ExpiresIn=expiration
            )

            result = {}
            result["result"] = "OK"
            result["url"] = response["url"]
            result["fields"] = response["fields"]
            return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)
        except ClientError as client_error:
            logger.logErrorEvent(str(client_error))
            return jsonify(result="ERROR", exception=str(client_error))
    else:
        logger.logErrorEvent("Missing parameters."
                       "Please provide the S3 object name (key) as 「objectName」.", False, 'create_presigned_url')
        return jsonify(
            result="ERROR",
            status_code=400,
            exception=("Missing parameters."
                       "Please provide the S3 object name (key) as 「objectName」."))
