"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os
import io
import logging

from flask import jsonify, request, abort
import boto3
from botocore.exceptions import ClientError
from loggerService import Logger

from util import decode_csv_file
from app import app
from zipcodes.zipcode_csv import (
    process_zipcode_csv, save_zipcode_data, delete_zipcodes_from_dynamo,
    get_zipcodes_from_dynamo, ZipcodeException, export_zipcodes_as_csv
)
logger = Logger('zipcodes')

@app.route("/scenario/api/importZipcodes", methods=['POST'])
def import_zipcodes():
    """API endpoint for importing zipcodes to restrict locations from where
    the damage report feature can be used.
    """
    from app import cognitoUserName, cognitoUserGroups
    if "guests" in cognitoUserGroups:
        abort(403, description="ユーザーは権限がありません")

    data = request.get_json()
    logger.logApiEvent(apiName='importZipcodes', body=request.get_data(), userName=cognitoUserName)

    if (data and data.get("key") and data.get("scenario")):
        csv_bucket_key = data.get("key")
        scenario = data.get("scenario")

        language = None
        if 'lang' in data:
            language = data['lang']
            scenario += "#" + language

        try:
            # Retrieve CSV file from S3 bucket and store in-memory
            s3_bucket = os.environ['BUCKET_CHATBOT_IMPORTED_RESOURCES']
            csv_zipcode_file = io.BytesIO()

            boto3.resource('s3').Object(s3_bucket, csv_bucket_key).download_fileobj(csv_zipcode_file)

            # First try to decode using SHIFT-JS. If an error occurs, fallback to UTF-8
            decoded, csv_text = decode_csv_file(csv_zipcode_file)

            if not decoded:
                logger.logErrorEvent('CSVファイルのデコード中にエラーが発生しました。'
                                     '次のコーデックがサポートされています。'
                                     '「shift_jis」「shift_jis_2004」「shift_jisx0213」「utf_8」',
                                     False,
                                     'import_trash_spreadsheet'
                                    )
                return jsonify(result="ERROR", status_code=500,
                               exception=('CSVファイルのデコード中にエラーが発生しました。'
                                          '次のコーデックがサポートされています。'
                                          '「shift_jis」「shift_jis_2004」「shift_jisx0213」「utf_8」'))

            csv_zipcode_file.close()

            if csv_text:
                csv_zipcode_file = io.StringIO(csv_text)
                csv_zipcode_file.seek(0)

                # Process file and get the message and dict mapping
                success, zipcodes, error_message = process_zipcode_csv(csv_zipcode_file)

                if success:
                    # First, delete old data if it exists
                    delete_zipcodes_from_dynamo(scenario)

                    # Save it in the database
                    result = save_zipcode_data(scenario, zipcodes)

                    if len(result) > 3:
                        import_result = jsonify(result=result[0], status_code=result[1],
                                                exception=str(result[2]), warning=result[3])
                    else:
                        import_result = jsonify(result=result[0], status_code=result[1],
                                                warning=result[2])
                else:
                    logger.logErrorEvent('CSVファイルの処理中にエラーが発生しました：' + \
                                         error_message, False, 'import_zipcodes_spreadsheet')
                    import_result = jsonify(result="ERROR", status_code=400,
                                            exception=('CSVファイルの処理中にエラーが発生しました：' + \
                                                       error_message))


            else:
                logger.logErrorEvent('提供されたCSVファイルのデコード中にエラーが発生しました。'
                                     'サポートされているエンコード形式は、SHIFT-JISおよびUTF-8です。',
                                     False,
                                     'import_zipcodes_spreadsheet')
                import_result = jsonify(result="ERROR", status_code=400,
                                        exception=('提供されたCSVファイルのデコード中にエラーが発生しました。'
                                                   'サポートされているエンコード形式は、SHIFT-JISおよびUTF-8です。'))

            # Delete the imported CSV file from the bucket
            boto3.client('s3').delete_object(Bucket=s3_bucket, Key=csv_bucket_key)

            return import_result
        except ClientError as client_error:
            logger.logErrorEvent("Boto3 error during import of zipcode CSV." + str(client_error))
            logging.exception("Boto3 error during import of zipcode CSV.")
            return jsonify(result="ERROR", status_code=400, exception=str(client_error))
    else:
        logger.logErrorEvent('不足しているパラメータ。'
                             'LBDファイルのS3バケットキーと'
                             'シナリオ名とバージョンを指定してください。', False, 'import_zipcodes_spreadsheet')
        return jsonify(result="ERROR", status_code=400,
                       exception=('不足しているパラメータ。'
                                  'LBDファイルのS3バケットキーと'
                                  'シナリオ名とバージョンを指定してください。'))

@app.route("/scenario/api/deleteZipcodes", methods=['POST'])
def delete_zipcodes():
    """API endpoint for deleting damage report zip code data from the database.
    """
    from app import cognitoUserName, cognitoUserGroups
    if "guests" in cognitoUserGroups:
        abort(403, description="ユーザーは権限がありません")
    data = request.get_json()
    logger.logApiEvent(apiName='deleteZipcodes', body=request.get_data(), userName=cognitoUserName)
    response = None

    if data and data.get("scenario"):
        delete_zipcodes_from_dynamo(data.get("scenario"))
        response = jsonify(result="SUCCESS", status_code=200,
                           warning=[])
    else:
        logger.logErrorEvent('不足しているパラメータ。'
                             'シナリオ名とバージョンを指定してください。', False, 'delete_zipcodes')
        response = jsonify(result="ERROR", status_code=400,
                           exception=('不足しているパラメータ。'
                                      'シナリオ名とバージョンを指定してください。'))
    return response

@app.route("/scenario/api/getZipcodes", methods=['POST'])
def get_saved_zipcodes():
    """ API endpoint for retrieving saved damage report zipcodes in the database.
    """
    from app import cognitoUserName, cognitoUserGroups
    if "guests" in cognitoUserGroups:
        abort(403, description="ユーザーは権限がありません")

    logger.logApiEvent(apiName='getZipcodes', body=request.get_data(), userName=cognitoUserName)

    data = request.get_json()

    response = None

    if data and data.get("scenario"):
        try:
            zipcodes = get_zipcodes_from_dynamo(
                data.get("scenario")
            )

            response = jsonify(result="SUCCESS",
                               zipcodes=zipcodes)

        except ZipcodeException as zip_exception:
            response = jsonify(result="ERROR", status_code=500,
                               exception=str(zip_exception))
    else:
        logger.logErrorEvent('不足しているパラメータ。'
                             'シナリオ名とバージョンを指定してください。', False, 'get_zipcodes')
        response = jsonify(result="ERROR", status_code=400,
                           exception=('不足しているパラメータ。'
                                      '「scenario」を指定してください。'))

    return response

@app.route("/scenario/api/exportZipcodesCSV", methods=['POST'])
def export_saved_zipcodes():
    """ API endpoint from exporting saved zipcodes in the database as csv
    """
    from app import cognitoUserName, cognitoUserGroups
    if "guests" in cognitoUserGroups:
        abort(403, description="ユーザーは権限がありません")

    logger.logApiEvent(apiName='exportZipcodes', body=request.get_data(), userName=cognitoUserName)

    response = None

    data = request.get_json()

    if data and data.get("scenario"):
        result = export_zipcodes_as_csv(data.get("scenario"))
        response = jsonify(result=result[0], status_code=200, csv_url=result[2],
                           exception=str(result[3]), warning=result[4])
    else:
        logger.logErrorEvent('不足しているパラメータ。'
                             'シナリオ名とバージョンを指定してください。', False, 'export_zipcodes_spreadsheet')
        response = jsonify(result="ERROR", status_code=400,
                           exception=('不足しているパラメータ。'
                                      '「scenario」を指定してください。'))
    return response
