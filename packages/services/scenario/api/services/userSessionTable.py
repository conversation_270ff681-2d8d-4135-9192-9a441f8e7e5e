"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os
import json

import boto3
from botocore.exceptions import ClientError
from flask import jsonify, request, abort

from app import app
from util import DecimalEncoder, DecimalDecoder
from loggerService import Logger

dynamodb = boto3.resource('dynamodb') # DynamoDB reference
logger = Logger('userSession')

TABLE_CHATBOT_USER_SESSION = os.environ['TABLE_CHATBOT_USER_SESSION']

@app.route('/scenario/api/db/userSession/scan', methods=['GET'])
def db_usersession_scan():
    """API endpoint that scans the user session table in DynamoDB.

    :returns str: JSON with the scan results.
    """
    from app import cognitoUserName, cognitoUserGroups
    table = dynamodb.Table(TABLE_CHATBOT_USER_SESSION)
    logger.logApiEvent(apiName='scan', userName=cognitoUserName)

    # Scan table
    try:
        response = table.scan()

        items = []
        items.extend(response['Items'])

        while 'LastEvaluatedKey' in response:
            response = table.scan(
                ExclusiveStartKey=response['LastEvaluatedKey']
                )
            items.extend(response['Items'])

    except ClientError as client_error:
        logger.logErrorEvent(client_error.response['Error']['Message'])
        return jsonify(result="ERROR", error_message=client_error.response['Error']['Message'])
    else:
        result = {}
        result["result"] = "OK"
        result["items"] = items

        return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)

@app.route('/scenario/api/db/userSession/getItem', methods=['GET'])
def db_usersession_get_item():
    """API endpoint that gets an item from the user session table in DynamoDB.

    :returns str: JSON with the scan results.
    """
    from app import cognitoUserName, cognitoUserGroups
    table = dynamodb.Table(TABLE_CHATBOT_USER_SESSION)

    user_id = request.args.get("userId")
    env = request.args.get("environment")
    logger.logApiEvent(
        apiName='getItem',
        header=request.args,
        userName=cognitoUserName)

    if user_id and env:
        try:
            response = table.get_item(
                Key={
                    'userId': user_id,
                    'environment' : env
                }
            )
        except ClientError as client_error:
            logger.logErrorEvent(client_error.response['Error']['Message'])
            return jsonify(
                result="ERROR",
                error_message=client_error.response['Error']['Message'])
        else:
            result = {}
            result["result"] = "OK"
            result["item"] = response.get("Item")

            return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)
    else:
        logger.logErrorEvent(
            "Query parameter missing. "
            "Please provide an [userId] and [environment] to be retrieved.",
            False,
            'db_usersession_get_item'
        )
        return jsonify(
            result="ERROR",
            error_message="Query parameter missing. "
                          "Please provide an [userId] and [environment] to be retrieved."
        )

@app.route('/scenario/api/db/userSession/putItem', methods=['POST'])
def db_usersession_put_item():
    """API endpoint that puts an item into the user session table in DynamoDB.

    :returns str: JSON with the scan results.
    """
    from app import cognitoUserName, cognitoUserGroups
    if "guests" in cognitoUserGroups:
        abort(403, description="ユーザーは権限がありません")
    table = dynamodb.Table(TABLE_CHATBOT_USER_SESSION)

    item_data = request.get_data()
    logger.logApiEvent(
        apiName='putItem',
        body=request.get_data(),
        userName=cognitoUserName)

    if item_data:
        try:
            item_data = json.loads(item_data, cls=DecimalDecoder)
            table.put_item(
                Item=item_data
            )
        except ClientError as client_error:
            logger.logErrorEvent(client_error.response['Error']['Message'])
            return jsonify(
                result="ERROR",
                error_message=client_error.response['Error']['Message'])
        except json.JSONDecodeError as js_error:
            logger.logErrorEvent(str(js_error.msg))
            return jsonify(result="ERROR", error_message=str(js_error.msg))
        else:
            return jsonify(result="OK")
    else:
        logger.logErrorEvent("Request JSON body is empty."
                           "Provide the JSON data of the item to be put.", False, 'db_usersession_put_item')
        return jsonify(
            result="ERROR",
            error_message=("Request JSON body is empty."
                           "Provide the JSON data of the item to be put."))

@app.route('/scenario/api/db/userSession/updateItem', methods=['POST'])
def db_usersession_update_item():
    """API endpoint that updates an item from the user session table in DynamoDB.

    :returns str: JSON with the scan results.
    """
    from app import cognitoUserName, cognitoUserGroups
    if "guests" in cognitoUserGroups:
        abort(403, description="ユーザーは権限がありません")
    table = dynamodb.Table(TABLE_CHATBOT_USER_SESSION)

    request_data = request.get_data()
    logger.logApiEvent(
        apiName='updateItem',
        body=request.get_data(), userName=cognitoUserName)

    if request_data:
        try:
            request_json = json.loads(request_data, cls=DecimalDecoder)

            user_id = request_json.get("userId")
            env = request_json.get("environment")
            update_expression = request_json.get("updateExpression")

            if user_id and env and update_expression:
                func_kwargs = {}
                func_kwargs["Key"] = {
                    'userId': user_id,
                    'environment': env
                }
                func_kwargs["UpdateExpression"] = update_expression

                expression_values = request_json.get("expressionValues")
                if expression_values:
                    func_kwargs["ExpressionAttributeValues"] = expression_values

                expression_names = request_json.get("expressionNames")
                if expression_names:
                    func_kwargs["ExpressionAttributeNames"] = expression_names

                response = table.update_item(**func_kwargs)
            else:
                logger.logErrorEvent("Body JSON is incorrect."
                                     "Either 「userId」,「environment」 or 「updateExpression」 is missing.",
                                     False,
                                     'db_usersession_update_item'
                                     )
                return jsonify(
                    result="ERROR",
                    error_message=("Body JSON is incorrect."
                                   "Either 「userId」,「environment」 or 「updateExpression」 is missing."))
        except ClientError as client_error:
            logger.logErrorEvent(client_error.response['Error']['Message'])
            return jsonify(result="ERROR", error_message=client_error.response['Error']['Message'])
        except json.JSONDecodeError as js_error:
            logger.logErrorEvent(js_error.msg)
            return jsonify(result="ERROR", error_message=str(js_error.msg))
        else:
            result = {}
            result["result"] = "OK"
            result["response"] = response

            return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)
    else:
        logger.logErrorEvent("Request JSON body is empty.", False, 'db_usersession_update_item')
        return jsonify(result="ERROR", error_message="Request JSON body is empty.")

@app.route('/scenario/api/db/userSession/deleteItem', methods=['DELETE'])
def db_usersession_delete_item():
    """API endpoint that deletes an item from the user session table in DynamoDB.

    :returns str: JSON with the scan results.
    """
    from app import cognitoUserName, cognitoUserGroups
    if "guests" in cognitoUserGroups:
        abort(403, description="ユーザーは権限がありません")
    table = dynamodb.Table(TABLE_CHATBOT_USER_SESSION)

    user_id = request.args.get("userId")
    env = request.args.get("environment")
    logger.logApiEvent(
        apiName='deleteItem',
        header=request.args,
        userName=cognitoUserName)

    if user_id and env:
        try:
            table.delete_item(
                Key={
                    'userId': user_id,
                    'environment': env
                }
            )
        except ClientError as client_error:
            logger.logErrorEvent(client_error.response['Error']['Message'])
            return jsonify(
                result="ERROR",
                error_message=client_error.response['Error']['Message'])
        else:
            return jsonify(result="OK")
    else:
        logger.logErrorEvent("Query parameter missing."
                           "Please provide an [userId] to be deleted.", False, 'db_usersession_delete_item')
        return jsonify(
            result="ERROR",
            error_message=("Query parameter missing."
                           "Please provide an [userId] to be deleted."))
