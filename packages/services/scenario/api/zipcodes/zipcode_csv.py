"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os
import csv
import io
import logging

import boto3
from botocore.exceptions import ClientError

from util import encode_csv_file

ZIPCODE_TYPE_ID = "zipcodes"

class ZipcodeException(Exception):
    """Custom exception class for trash scenario feature.
    """

def process_zipcode_csv(csv_file):
    """Process the zipcode CSV file.

    :param csv_file: The csv file to be processed (can be in-memory)
    :type cvs_file: typing.TextIO
    :returns bool: If the processing was successful.
    :returns: list: list of all zipcodes.
    :returns str: Error message if processing failed.
    """
    success = True
    error_message = ""
    zipcodes = []

    try:
        zipcodes_reader = csv.reader(csv_file)
        for row in zipcodes_reader:
            zipcodes.append(row[0])
    except csv.Error as csv_error:
        logging.exception("Error processing zipcode CSV file.")
        success = False
        error_message = str(csv_error)
    except IndexError:
        logging.exception("Error accessing CSV column. Number of fields might be incorrect.")
        success = False
        error_message = 'CSVサイズが正しくありません。 列数を確認してください。'

    csv_file.close()

    return success, zipcodes, error_message

def save_zipcode_data(scenario, zipcodes):
    """Save zipcode data into the scenario data table.

    :param str scenario: The scenario identifier. scenarioId + version + language (if applicable)
    :param zipcodes: List of zipcodes to save
    :returns str: Result (OK or ERROR)
    :returns int: Error code
    :returns BaseException: An exception, if applicable
    :returns list[T <= str]: List of warnings
    """
    save_result = "SUCCESS"
    code_result = 200
    save_exception = None
    warnings = []

    table = boto3.resource('dynamodb').Table(os.environ['TABLE_SCENARIO_DATA'])
    # Save zipcodes in to the table
    try:
        table.put_item(
            Item={
                'id' : scenario,
                'typeId' : ZIPCODE_TYPE_ID,
                'zipcodes' : zipcodes
            }
        )
    except ClientError as client_error:
        logging.exception("Error saving zipcodes to DB.")
        save_result = "ERROR"
        code_result = 400
        save_exception = client_error

    if save_exception:
        return save_result, code_result, save_exception, warnings

    return save_result, code_result, warnings

def delete_zipcodes_from_dynamo(scenario):
    """Deletes the damage report zip data stored in the database.

    :param str scenario: Scenario string identifier (name#version)
    """
    table = boto3.resource('dynamodb').Table(os.environ['TABLE_SCENARIO_DATA'])

    try:
        # Retrieve the zipcode object and check if it exists.
        # If not, ignore and don't do anything
        response = table.get_item(
            Key={
                'id': scenario,
                'typeId': ZIPCODE_TYPE_ID
            }
        )
        zipcode_obj = response.get('Item')
        if zipcode_obj:
            # Delete zipcodes
            table.delete_item(
                Key={
                    'id': scenario,
                    'typeId': ZIPCODE_TYPE_ID
                }
            )

    except ClientError:
        logging.exception("Boto3 error deleting zipcodes.")

def get_zipcodes_from_dynamo(scenario):
    """Retrieves the damage report zip data stored in the database.

    :param str scenario: Scenario string identifier (name#version)
    """
    table = boto3.resource('dynamodb').Table(os.environ['TABLE_SCENARIO_DATA'])

    zipcodes = []

    try:
        # Retrieve the zip object and check if it exists.
        # If not, ignore and don't do anything
        response = table.get_item(
            Key={
                'id': scenario,
                'typeId': ZIPCODE_TYPE_ID
            }
        )
        zipcode_obj = response.get('Item')

        if zipcode_obj and 'zipcodes' in zipcode_obj:
            zipcodes = zipcode_obj.get('zipcodes')

    except ClientError:
        logging.exception("Boto3 error retrieving zipcodes.")
        raise ZipcodeException("郵便番号を取得する際のデータベースエラーです。")

    return zipcodes

def _generate_csv_download_url(csv_rows, scenario):
    """Generate a S3 bucket pre-signed URL for the CSV file.

    :param list[T <= tuple]: The list of CSV rows.
    :return str
    """
    pre_signed_url = None

    # Use in memory object to write CSV
    csv_file_string = io.StringIO()
    csv.writer(csv_file_string).writerows(csv_rows)
    csv_file_string.seek(0)

    # Bytes object to be uploaded to S3 bucket
    csv_file_bytes = encode_csv_file(csv_file_string)
    csv_file_string.close()

    if csv_file_bytes:
        csv_file_bytes.seek(0)

        # S3 upload
        s3_client = boto3.client('s3')

        bucket = os.environ['BUCKET_CHATBOT_IMPORTED_RESOURCES']
        s3_file_key = "resources/export/zipcodes_"  + scenario + ".csv"

        s3_client.upload_fileobj(
            csv_file_bytes,
            bucket,
            s3_file_key,
            ExtraArgs={"ContentType": "text/csv"}
        )

        csv_file_bytes.close()

        # Pre-signed URL
        pre_signed_url = s3_client.generate_presigned_url(
            ClientMethod='get_object',
            Params={'Bucket' : bucket, 'Key' : s3_file_key},
            ExpiresIn=60, # 1 minute
            HttpMethod='GET')
    else:
        raise ZipcodeException("「shift_jis」または「utf-8」コーデックを使用してCSVデータをエンコードできませんでした。")

    return pre_signed_url

def export_zipcodes_as_csv(scenario):
    """Converts damage report zipcode data into an CSV and upload it to the S3 bucket.

    :param str scenario: The scenario identifier to generate the CSV for.

    :returns str: Result (OK or ERROR)
    :returns int: Error code
    :returns str: A S3 bucket pre-signed URL pointing to the created CSV.
    :returns BaseException: An exception, if applicable
    :returns list[T <= str]: List of warnings
    """
    export_result = "SUCCESS"
    code_result = 200
    export_exception = None
    warnings = []
    csv_url = None

    try:
        # Get list of zipcodes
        zipcodes = [[zip] for zip in get_zipcodes_from_dynamo(scenario)]
        csv_url = _generate_csv_download_url(zipcodes, scenario)
    except ClientError as client_error:
        export_result = "ERROR_BOTO3"
        code_result = 500
        export_exception = client_error
    except ZipcodeException as zip_error:
        export_result = "ERROR_TRASH_SCENARIO"
        code_result = 500
        export_exception = zip_error
    except csv.Error as csv_error:
        export_result = "ERROR_CSV"
        code_result = 500
        export_exception = csv_error

    return export_result, code_result, csv_url, export_exception, warnings
