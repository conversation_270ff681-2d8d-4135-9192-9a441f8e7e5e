#!/bin/bash

# This Script allows to build the Lambda code locally and update the Lambda code
set -euo pipefail
trap "echo 'error: <PERSON><PERSON><PERSON> failed: see failed command above'" ERR

LAMBDA_NAME=${1:?"Lambda name is not specified in the first parameter"}

rm -rf dist
mkdir -p dist
rsync -av ./ dist --exclude='dist'
(cd dist && pip install -r requirements.txt --upgrade -t .)
(cd dist && rm -f lambda.zip && zip -r lambda.zip . -x lambda.zip)
aws lambda update-function-code --function-name "$LAMBDA_NAME" --zip-file fileb://dist/lambda.zip --output json

exit 0
