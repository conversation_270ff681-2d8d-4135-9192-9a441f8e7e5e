"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os
import json
import awsgi
import re
import logging as _logging
from flask import Flask, request, jsonify
from flask_cors import CORS
from linebot import LineBotApi
from linebot.exceptions import (
    InvalidSignatureError, LineBotApiError
)

from scenario.user_session import UserSession
from scenario.scenario_manager import ScenarioManager
from scenario.simulator.response_simulator import ResponseSimulator
from loggerService import Logger
from scenario.special_flows import flows as flow_manager

from webhookHandler import Handler<PERSON>rapper
import scenario.spot_scenario.constants.spot_scenario_constants as SpotScenarioConstants 
from scenario.spot_scenario.spot_scenario_manager import SpotScenarioManager

app = Flask(__name__)
app.config['JSON_AS_ASCII'] = False
logger = Logger(None)
printLog = _logging.getLogger("scenario_chatbot")
printLog.setLevel(_logging.INFO)
CORS(app)

# ensure the instance folder exists
try:
    os.makedirs(app.instance_path)
except OSError:
    pass

# Instantiate scenario manager
scenario_manager = ScenarioManager(None)

# Instantiate LINE Bot API
line_bot_api = LineBotApi(
    os.environ['LINEMESSAGING_CHANNEL_ACCESS_TOKEN'],
    timeout=10.0
)

# API path depends on environment
api_path = "/" + os.environ['API_PATH']

# フルサポ専用ーミニアプリの為
@app.route(api_path + "/getMessage", methods=['GET'])
def getmessage():
    """Endpoint to retrieve raw message data from the saved scenario.

    :returns dict: JSON containing the message data.
    """
    data = request.args.get("q")

    response_json = None

    if data:
        message = scenario_manager.getMessageByText(data)

        if message:
            response_json = jsonify(result="OK", message=str(message))
        else:
            logger.logErrorEvent("MSG_NOT_FOUND", False, 'getMessage')
            response_json = jsonify(result="ERROR", message="", ERROR="MSG_NOT_FOUND")
    else:
        logger.logErrorEvent("MISSING PARAMETER", False, 'getMessage')
        response_json = jsonify(result="ERROR", message="", ERROR="MISSING PARAMETER")

    return response_json

@app.route(api_path + "/bosaiMode", methods=['POST'])
def switch_bosai_mode():
    """Endpoint to enable or disable bosai mode for the current
    scenario set in either production or sandbox environments.

    Enabling bosai mode consists of 3 steps:
    1. Changing the active rich menu to the one set as bosai mode rich menu.
    2. Broadcast the first message of the selected talk (default to bosai flow)
    3. Set a flag in settings indicating bosai mode status
    """
    data = request.get_json(silent=True)

    if data and "activate" in data:
        result, message = scenario_manager.bosai_mode(activate=data.get('activate'), talkId=data.get('selectedTalkId'))
    else:
        result, message = scenario_manager.bosai_mode()

    return jsonify(result=result, message=message)

@app.route(api_path + "/simulateResponse", methods=['POST'])
def simulate_response():
    """Endpoint to simulate a chatbot response based on a custom made event.

    The event data can have a special flow name, the previous message ID,
    the type of event (location or image message, for example),
    and dummy data. This cannot be used to simulate text message or postback events,
    because the response can be easily infered.

    The returned data is a JSON containing a list of possible messages that can be returned
    based on the event.

    :returns str: JSON data with the response messages
    """
    data = request.get_json(silent=True)

    result = "SUCCESS"
    messages = []
    error = ""

    response_simulator = ResponseSimulator(scenario_manager)

    # Event type and message Id are required
    if data and "eventType" in data and "lastMessageId" in data and "scenario" in data:
        messages = response_simulator.simulate_response(data)
    else:
        result = "ERROR"
        error = "必要な属性が欠落しているペイロードを要求します：「scenario」,「eventType」and 「lastMessageId」。"

    return jsonify(result=result, messages=messages, error=error)

@app.route(api_path + "/flowStartEvent", methods=['POST'])
def get_flow_start_event():
    """Endpoint to retrieve the webhook event that trigger a special flow (talk).

    :returns dict: JSON containing the event type and the data.
    """
    data = request.get_json(silent=True)

    result = "SUCCESS"
    event = None
    error = ""

    if data and "name" in data and "scenario" in data:
        # Get flow object and the correspoding start event
        flow_obj = flow_manager.get_flow_object_by_name(
            data.get('name')
        )

        if flow_obj:
            event = flow_obj.get_start_event(
                data.get('scenario')
            )
        else:
            result = "ERROR"
            error = "指定された名前のフローが見つかりませんでした。"
    else:
        result = "ERROR"
        error = "必要なクエリパラメータがありません：「name」と「scenario」"

    return jsonify(result=result, event=event, error=error)

def webhook_callback(event):
    """The LINE webhook event handler.
    It receives events from the LINE messaging API, such as text messages or postback actions.
    The entry point for the chatbot system.
    """
    try:
        # get X-Line-Signature header value
        signature = event.get('signature')

        # get request body as text
        body = event.get('body')

        # handle webhook body
        handler = HandlerWrapper(scenario_manager, logger)
        handler.handle(body, signature)
    except (InvalidSignatureError, KeyError):
        logger.logErrorEvent(
            "Invalid signature. Please check your channel access token/channel secret."
        )
    except LineBotApiError as line_api_error:
        logger.logErrorEvent(str(line_api_error))

    return 'OK'

def lambda_handler(event, context):
    """Lambda handler function.

    :param event: Lambda call event
    :param context: Lambda execution context
    """
    global scenario_manager
    scenario_manager = ScenarioManager(line_bot_api)
    global logger
    logger = Logger(scenario_manager.scenario)

    return_value = None

    event_type = event.get('event_type', '')
    body=event.get('body', {})
    if isinstance(body, str):
        try:
            body_json = json.loads(body)
        except json.JSONDecodeError:
            body_json = {}
    elif isinstance(body, dict):
        body_json = body
    else:
        body_json = {}

    printLog.info(f"chatbot.app body_json: {repr(body_json)}")
    message_type = body_json.get('events', [])[0]['type'] if body_json.get('events', []) else None
    printLog.info(f"chatbot.app message_type: {repr(message_type)}")
    if message_type and message_type=='postback':
        postback_data=body_json.get('events', [])[0]['postback']['data']
        printLog.info(f"chatbot.app postback_data: {repr(postback_data)}")
        printLog.info(f'[INFO] (chatbot-lambda_hanlder) message_type: {repr(message_type)} postback_data:{repr(postback_data)}')
        #If the postback data is one of the sightseeing spot triggers, use the SpotScenarioManager
        for regex_string in SpotScenarioConstants.SPOT_SCENARIO_TRIGGER_MESSAGE_REGEXES:
            check = re.search(regex_string, postback_data)
            printLog.info(f"chatbot.app regex_string: {repr(regex_string)}")
            printLog.info(f"chatbot.app check: {repr(check)}")
            if re.search(regex_string, postback_data):
                printLog.info(f"chatbot.app spot match: {repr(regex_string)}")
                try:
                    scenario_manager = SpotScenarioManager(line_bot_api)
                except Exception as e:
                    printLog.error(f'[ERROR] (chatbot-lambda_handler) SpotScenarioManager - error: {repr(e)}')

    # 観光スポット機能専用　- location は　SpotScenarioManager で処理
    if message_type and message_type=='message': #handle location for spot
        text_message_type = body_json.get('events', [])[0]['message'].get('type')
        if text_message_type =='location':
            printLog.info(f'[INFO] (chatbot-lambda_hanlder) message_type:{repr(message_type)} eventMessage:{repr(text_message_type)}')
            user_session = UserSession()
            eventSourceUserId=body_json.get('events', [])[0]['source']['userId']
            location_data = user_session.getLocation(eventSourceUserId)
            if location_data and location_data.get('location') and re.search(SpotScenarioConstants.SPOT_LOCATION_REGEX, location_data.get('location')):
                printLog.info(f"chatbot.app spot location_data: {repr(location_data)}")
                scenario_manager = SpotScenarioManager(line_bot_api)

    printLog.info(f'event_type: {repr(event_type)}')

    if event_type == 'line_webhook':
        # LINE webhook
        return_value = webhook_callback(event)
    elif event_type == 'talk_distribution':
        talk_id = event.get('talkId')
        talk_name = event.get('talkName')
        use_disaster_richmenu = event.get('useDisasterRichmenu') or False

        if talk_id and talk_name:
            result, message = scenario_manager.initiate_talk(talk_id, talk_name, use_disaster_richmenu)
            return_value = {
                'result': result,
                'message': message
            }
        else:
            printLog.error("[ERROR] Calling scenario talk distribution event without necessary parameters. talkId and talkName")
    else:
        return_value = awsgi.response(app, event, context, base64_content_types={"image/png"})

    return return_value
