"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os
import decimal
import io
import re
import json
from urllib.parse import parse_qs

import boto3
from botocore.exceptions import ClientError
from linebot.exceptions import (
    LineBotApiError
)

from scenario.special_flows.base_flow import BaseFlow
import scenario.special_flows.damage_report.message_id as dmgReportIds
from scenario.util import send_email, get_current_date_time_report, replace_lists
from scenario.user_session import USER_SESSION_HELPER as user_session
from sendTalkLog import SendTalkLog
from scenario.util import replace_decimals
from linebot.models import (
    TextSendMessage,
    TemplateSendMessage,
    CarouselTemplate,
    CarouselColumn,
    PostbackAction,
    ButtonsTemplate,
)

dynamodb = boto3.resource('dynamodb')
sqsClient = boto3.client("sqs")

CREATE_DAMAGE_REPORT_QUEUE = os.environ['CREATE_DAMAGE_REPORT_QUEUE']

# Instantiate sender
sender = SendTalkLog()

S3_BUCKET = os.environ['BUCKET_CHATBOT_IMPORTED_RESOURCES']
SCENARIO_CLOUDFRONT_DOMAIN_NAME = os.environ['SCENARIO_CLOUDFRONT_DOMAIN_NAME']
REPORT_EMAIL_LIST = [
    os.environ['EMAIL_CHATBOT_DAMAGE_REPORT1'],
    os.environ['EMAIL_CHATBOT_DAMAGE_REPORT2'],
    os.environ['EMAIL_CHATBOT_DAMAGE_REPORT3'],
    os.environ['EMAIL_CHATBOT_DAMAGE_REPORT4']
]
TABLE_CHATBOT_SCENARIO_DATA = os.environ['TABLE_CHATBOT_SCENARIO_DATA']
TABLE_SCENARIO_DATA = os.environ['TABLE_SCENARIO_DATA']
EMAIL_DOMAIN = os.environ.get('SES_EMAIL_DOMAIN', None)
SCENARIO_ENVIRONMENT = os.environ['SCENARIO_ENVIRONMENT'] # Scenario environment
DEFAULT_DAMAGE_REPORT_TALK_DATA_ID = 'DAMAGE_REPORT_TALK'

class DamageReport(BaseFlow):
    """Implementation for the damage report special flow.
    """
    def __init__(self, scenario_manager):
        super().__init__(scenario_manager)

        self.postback_data_handler = {
            dmgReportIds.CATEGORY_NORMAL :
                self.__handle_postback_category_general__,
            dmgReportIds.CATEGORY_DETAILS_PARK_CAROUSEL :
                self.__handle_postback_category_detailed__,
            dmgReportIds.CAMERA_DETAILED_PICTURE_CONFIRM :
                self.__handle_postback_camera_detailed_pic_confirm__,
            dmgReportIds.CAMERA_ACTION :
                self.__handle_postback_camera_action__,
            dmgReportIds.STATUS_USER_COMMENT_RIVER :
                self.__handle_postback_user_comment_category__,
            dmgReportIds.STATUS_USER_COMMENT_PARK_PLAYGROUND :
                self.__handle_postback_user_comment_category__,
            dmgReportIds.STATUS_USER_COMMENT_PARK_LIGHTING :
                self.__handle_postback_user_comment_category__,
            dmgReportIds.STATUS_USER_COMMENT_PARK_BENCH :
                self.__handle_postback_user_comment_category__,
            dmgReportIds.STATUS_USER_COMMENT_PARK_WATER :
                self.__handle_postback_user_comment_category__,
            dmgReportIds.STATUS_USER_COMMENT_PARK_TREE :
                self.__handle_postback_user_comment_category__,
            dmgReportIds.STATUS_USER_COMMENT_PARK_OTHER :
                self.__handle_postback_user_comment_category__,
            dmgReportIds.STATUS_USER_COMMENT_RIVER :
                self.__handle_postback_user_comment_category__,
            dmgReportIds.REPORT_RESUME_CONFIRM :
                self.__handle_postback_report_resume_confirm__
        }

        self.postback_data_handler_simulate = {
            dmgReportIds.CATEGORY_NORMAL :
                self._simulate_postback_category_general,
            dmgReportIds.CATEGORY_DETAILS_PARK_CAROUSEL :
                self._simulate_postback_category_detailed,
            dmgReportIds.CAMERA_DETAILED_PICTURE_CONFIRM :
                self._simulate_postback_camera_detailed_pic_confirm,
            dmgReportIds.CAMERA_ACTION :
                self._simulate_postback_camera_action,
            dmgReportIds.STATUS_USER_COMMENT_RIVER :
                self._simulate_postback_user_comment_category,
            dmgReportIds.STATUS_USER_COMMENT_PARK_PLAYGROUND :
                self._simulate_postback_user_comment_category,
            dmgReportIds.STATUS_USER_COMMENT_PARK_LIGHTING :
                self._simulate_postback_user_comment_category,
            dmgReportIds.STATUS_USER_COMMENT_PARK_BENCH :
                self._simulate_postback_user_comment_category,
            dmgReportIds.STATUS_USER_COMMENT_PARK_WATER :
                self._simulate_postback_user_comment_category,
            dmgReportIds.STATUS_USER_COMMENT_PARK_TREE :
                self._simulate_postback_user_comment_category,
            dmgReportIds.STATUS_USER_COMMENT_PARK_OTHER :
                self._simulate_postback_user_comment_category,
            dmgReportIds.STATUS_USER_COMMENT_RIVER :
                self._simulate_postback_user_comment_category,
            dmgReportIds.REPORT_RESUME_CONFIRM :
                self._simulate_postback_report_resume_confirm
        }

        self.damage_report_talk_start_message_id = None
        self.damage_report_talk_start_message_text = None
        self.damage_report_talk_id = self.__get_dr_talk_id_active__()
        
    @staticmethod
    def get_identifier():
        return 'DAMAGE_REPORT'

    def is_active(self):
        return False

    def flow_start(self, event, event_type):
        # Damage report can be started either by text or postback events
        message = None
        start = False

        clear_session = False
        
        start_keyword_active = self.damage_report_talk_start_message_text
        start_keyword_event = event.postback.data if event_type == 'postback' else \
            event.message.text if event_type == 'text_message' else None
        if not start_keyword_event: return start, message
        
        if (start_keyword_event == dmgReportIds.DAMAGE_REPORT_START_KEYWORD) or\
        (start_keyword_event == start_keyword_active):
            if self._is_damage_report_enabled():
                if self.damage_report_talk_id == DEFAULT_DAMAGE_REPORT_TALK_DATA_ID:
                    message = self.scenario_manager.getMessageById(dmgReportIds.REPORT_MODE)

                if self.damage_report_talk_id != DEFAULT_DAMAGE_REPORT_TALK_DATA_ID and self.damage_report_talk_start_message_id:
                    user_session.startDamageReportVersion(event.source.user_id, self.damage_report_talk_id)
                    message = self.scenario_manager.getMessageById(self.damage_report_talk_start_message_id)
                    
                if message:
                    start = True
                    if SCENARIO_ENVIRONMENT == 'production':
                        sender.putReplyTalkLogs(
                            event,
                            message,
                            dmgReportIds.REPORT_MODE
                        )
            else:
                clear_session = True
                message = self.scenario_manager.getMessageById(
                    dmgReportIds.DISABLED_DAMAGE_REPORT_MODE
                )
        elif not self._is_damage_report_enabled() and\
        (event_type == 'postback' and event.postback.data in dmgReportIds.MESSAGE_ID_LIST):
            clear_session = True
            message = self.scenario_manager.getMessageById(
                dmgReportIds.DISABLED_DAMAGE_REPORT_MODE
            )

        if clear_session:
            user_session.setSpecialFlow(
                event.source.user_id,
                None
            )

        message = self.__embedded_value_to_messages_postback_data__(message)

        return start, message

    def handle_event_postback(self, postback):
        if self.damage_report_talk_id != DEFAULT_DAMAGE_REPORT_TALK_DATA_ID:
            return self.__handle_postback_event_versioning__(
                postback.postback.data,
                postback.source.user_id
            )
        return self._processPostbackEvent(
            postback.postback.data,
            postback.source.user_id
        )

    def handle_event_text_message(self, text_message):
        if self.damage_report_talk_id != DEFAULT_DAMAGE_REPORT_TALK_DATA_ID:
            return self.__handle_text_event_versioning__(
                text_message.message.text,
                text_message.source.user_id
            )
        return self._processTextMessage(
            text_message.message.text,
            text_message.source.user_id
        )

    def handle_event_image_message(self, image_message):
        if self.damage_report_talk_id != DEFAULT_DAMAGE_REPORT_TALK_DATA_ID:
            return self.__handle_image_message_versioning__(
                image_message.message,
                image_message.source.user_id
            )
        return self._processImageMessage(
            image_message.message,
            image_message.source.user_id
        )

    def handle_event_location_message(self, location_message):
        if self.damage_report_talk_id != DEFAULT_DAMAGE_REPORT_TALK_DATA_ID:
            return self.__handle_location_message_versioning__(
                location_message.message,
                location_message.source.user_id
            )
        return self._processLocationMessage(
            location_message.message,
            location_message.source.user_id
        )

    def save_workflow(self, user_id, event_source, event_type):
        last_message_id = user_session.\
                          last_retrieved_user_session.get("lastMessageId")
        
        event_source_data = {
            "data": "",
            "timestamp": event_source.timestamp
        }
        if event_type == "postback":
            event_source_data["data"] = event_source.postback.data

        if event_type == "text_message":
            event_source_data["data"] = event_source.message.text

        if event_type == "image_message":
            event_source_data["data"] = event_source.message.id

        if event_type == "location_message":
            location_data  = {
                "id": event_source.message.id,
                "title": event_source.message.title,
                "address": event_source.message.address,
                "latitude": event_source.message.latitude,
                "longitude": event_source.message.longitude
            }
            event_source_data["data"] = json.dumps(location_data)

        workflowItem = {
            "message_chatbot_id": last_message_id,
            "event_type": event_type,
            "event_source": event_source_data
        }
        user_session.updateDamageReportUserWorkflow(user_id, workflowItem)

    def _is_damage_report_enabled(self):
        """Checks if the damage report scenario is enabled.
        This can be changed by the user on the management screen.

        :returns bool: True if enable, False if not
        """
        active = False
        if self.scenario_manager.scenario_version_object and\
        self.scenario_manager.scenario_version_object.get("specialTalks"):
            active = self.scenario_manager.scenario_version_object.\
            get("specialTalks").get("damageReport")

        return active

    def _processPostbackEvent(self, postback_data, user_id):
        """Handles postback events when the damage report scenario is active.

        :param str postback_data: The postback string from the LINE webhook event
        :param str user_id: LINE user id associated with the event
        """
        # If postback data is in the list of damage report message IDs,
        # just return it (there are some special cases)
        next_message_id = None
        return_message = None

        if postback_data in [dmgReportIds.CATEGORY_PHONE_RIVER,
                             dmgReportIds.CATEGORY_PHONE_PARK]:
            # Deactivate flow
            user_session.setSpecialFlow(user_id, None)
            next_message_id = postback_data
        elif postback_data in dmgReportIds.MESSAGE_ID_LIST:
            next_message_id = postback_data
        else:
            # How to handle non-ID data depends on the last message
            last_message_id = user_session.\
                              last_retrieved_user_session.get("lastMessageId")

            handler = self.postback_data_handler.get(last_message_id)
            if handler:
                next_message_id = handler(postback_data, user_id)
            else:
                # Last message may have been a custom one to handle sub categories
                # or user comment
                report = user_session.last_retrieved_user_session.get("damageReport")

                if report and 'category' in report:
                    if last_message_id == report['category'].get('sub_category_msg_id', ''):
                        next_message_id = self.__handle_postback_category_detailed__(
                            postback_data,
                            user_id
                        )
                    elif last_message_id == report['category'].get('user_comment_msg_id', ''):
                        next_message_id = self.__handle_postback_user_comment_category__(
                            postback_data,
                            user_id
                        )

        if next_message_id:
            if next_message_id == dmgReportIds.REPORT_RESUME_CONFIRM:
                # Generate report resume message dynamically
                return_message = self.__generate_report_resume_message__(user_id)
            else:
                return_message = self.scenario_manager.getMessageById(
                    next_message_id
                )
        if next_message_id == dmgReportIds.END_REPORT_MODE_COMPOSITE_MESSAGE:
            if SCENARIO_ENVIRONMENT == 'production':
                sender.putReplyEndReportModeTalkLogs(user_id, next_message_id)
        else:
            if SCENARIO_ENVIRONMENT == 'production':
                sender.putReplyProcessPostbackTalkLogs(return_message, user_id, next_message_id)

        return return_message

    def _processTextMessage(self, text, user_id):
        """Handles text message events when the damage report scenario is active.

        :param str text: The text string from the LINE webhook event
        :param str user_id: LINE user id associated with the event
        """
        # Save user feedback if last message was the one requesting it
        last_message_id = user_session.\
                          last_retrieved_user_session.get("lastMessageId")

        return_message = None

        if last_message_id == dmgReportIds.STATUS_USER_COMMENT_FREEFORM_INPUT:
            user_session.setDamageReportUserFeedback(user_id, text)
            # Next message is the user comment confirm
            return_message = self.scenario_manager.getMessageById(
                dmgReportIds.STATUS_USER_COMMENT_CONFIRM
            )
        else:
            # If the inputted text maps to another message,
            # return nothing.
            # If no mapping is found, just return the previous message
            if not text in self.scenario_manager.text_mapping:
                return_message = self.scenario_manager.getMessageById(
                    last_message_id
                )

        return return_message

    def _processLocationMessage(self, message, user_id):
        """Handles location message events when the damage report scenario is active.

        :param message: The location message from the LINE webhook event
        :type message: linebot.models.LocationMessage
        :param str user_id: LINE user id associated with the event
        """
        # Save user location if last message was the one requesting it
        last_message_id = user_session.\
                          last_retrieved_user_session.get("lastMessageId")

        return_message = None

        if last_message_id == dmgReportIds.LOCATION_PICKER:

            # Create and save location JSON
            location_address = message.address
            latitude = message.latitude
            longitude = message.longitude

            location = {}
            location["address"] = location_address
            location["latitude"] = decimal.Decimal(str(latitude))
            location["longitude"] = decimal.Decimal(str(longitude))
            check_location = self.__handle_location_based_zipcode__(location_address)

            if not check_location:
                message = self.scenario_manager.getMessageById(
                    dmgReportIds.LOCATION_OUTSIDE_SEARCH_RADIUS
                )

                user_session.setSpecialFlow(
                    user_id,
                    None
                )

                return message

            user_session.setDamageReportLocation(user_id, location)

            # Next message is location confirm
            return_message = self.scenario_manager.getMessageById(
                dmgReportIds.LOCATION_CONFIRM
            )
            if SCENARIO_ENVIRONMENT == 'production':
                sender.putReplyProcessLocationTalkLogs(
                    return_message,
                    user_id,
                )

        return return_message

    def _processImageMessage(self, message, user_id):
        """Handles image message events when the damage report scenario is active.

        :param message: The image message from the LINE webhook event
        :type message: linebot.models.ImageMessage
        :param str user_id: LINE user id associated with the event
        """
        # Save user location if last message was the one requesting picture (general or detailed)
        last_message_id = user_session.\
                          last_retrieved_user_session.get("lastMessageId")

        return_message = None

        if last_message_id == dmgReportIds.CAMERA_ACTION:
            # Download image from LINE API and upload to S3
            image_url = self.__download_line_image_upload_s3__(user_id, message.id, "pic_2_遠景_")

            user_session.setDamageReportImage(user_id, "general", image_url)

            # Next message is picture confirm
            return_message = self.scenario_manager.getMessageById(
                dmgReportIds.CAMERA_PICTURE_CONFIRM
            )

        elif last_message_id == dmgReportIds.CAMERA_ACTION_DETAILED_PICTURE:
            # Download image from LINE API and upload to S3
            image_url = self.__download_line_image_upload_s3__(user_id, message.id, "pic_1_接写_")

            user_session.setDamageReportImage(user_id, "detailed", image_url)

            # Next message is picture confirm
            return_message = self.scenario_manager.getMessageById(
                dmgReportIds.CAMERA_DETAILED_PICTURE_CONFIRM
            )
        else:
            # For now just return the last message
            return_message = self.scenario_manager.getMessageById(
                last_message_id
            )
        if SCENARIO_ENVIRONMENT == 'production':
            sender.putReplyProcessPostbackTalkLogs(return_message, user_id, last_message_id)

        return return_message

    # Downloads the image file sent via LINE chat, upload to S3 bucket an return the URL.
    def __download_line_image_upload_s3__(self, user_id, image_message_id, prefix):
        url = ""
        with io.BytesIO() as image_file:
            try:
                # Download image data from LINE and save in memory
                message_content = self.scenario_manager.line_bot_api.\
                                  get_message_content(image_message_id)

                for chunk in message_content.iter_content():
                    image_file.write(chunk)

                image_file.seek(0)

                # Upload data to S3 bucket
                s3_resource = boto3.resource('s3')
                bucket = s3_resource.Bucket(S3_BUCKET)
                key = "resources/damageReport/" + str(user_id) \
                      + "/" + prefix + str(image_message_id) + ".jpeg"
                bucket.put_object(
                    Body=image_file,
                    Key=key,
                    ContentType="image/jpeg"
                )

                url = f'https://{SCENARIO_CLOUDFRONT_DOMAIN_NAME}/{key}'

            except LineBotApiError:
                pass
            except ClientError:
                pass

        return url

    # Sends the damage report e-mail and return the stamp / thanks message
    def __send_report_via_email__(self):
        # Get saved damage report data and write the e-mail
        damage_report = user_session.\
                        last_retrieved_user_session.get("damageReport")

        if damage_report:
            category_email = self.__get_report_email_for_category__(
                damage_report.get("category").get("general")
                )

            if EMAIL_DOMAIN:
                address_source = 'damage-report@' + EMAIL_DOMAIN
            else:
                address_source = category_email
            address_to = category_email
            subject = "損傷報告"

            body = self.__get_report_body__(damage_report)

            image_general_url = damage_report.get("images").get("general", "無い")
            image_detailed_url = damage_report.get("images").get("detailed", "無い")

            body = body + f'画像１URL：{image_detailed_url}\n' \
                        + f'画像２URL：{image_general_url}\n'

            attachments = {}
            attachments["type"] = "NORMAL"
            attachments["attachments"] = []

            if image_detailed_url:
                attachments["attachments"].append(image_detailed_url)
            if image_general_url and image_general_url != "無い":
                attachments["attachments"].append(image_general_url)

            send_email(address_source, address_to, subject, body, attachments)

    # Notify to other function to handle save report to db
    def __finished_report_flow__(self, line_user_id):
        try:
            damage_report = user_session.\
                            last_retrieved_user_session.get("damageReport")

            messageBody = {
                "uid": line_user_id,
                "from": "chatbot",
                "scenarioId": self.scenario_manager.scenario,
                "payload": damage_report
            }
            res = sqsClient.send_message(
                QueueUrl = CREATE_DAMAGE_REPORT_QUEUE,
                MessageBody = json.dumps(messageBody)
            )
            return res['MessageId']
        except Exception as e:
            return
    
    def __get_report_email_for_category__(self, category):
        email = ""

        # Get the message JSON data and find which action corresponds to the passed category
        category_message = self.scenario_manager.__getScenarioData__(
            self.scenario_manager.scenario,
            dmgReportIds.CATEGORY_NORMAL
            )

        mail_list_index = 0

        if category_message:
            for param_key, param_value in category_message.get("params").items():
                if param_key.startswith("actions"):
                    if category in param_value.get("data"):
                        action_number = param_key.split('.')[-1]
                        mail_list_index = int(action_number)

        email = REPORT_EMAIL_LIST[mail_list_index]

        return email

    @staticmethod
    def __get_report_body__(damage_report):
        # Body elements
        category_general = damage_report.get("category").get("general")
        category_detailed = damage_report.get("category").get("detailed", "無い")
        date_time = get_current_date_time_report()
        address = damage_report.get("location").get("address")

        other_data = ""
        if len(damage_report.get("images")) > 1:
            other_data = "画像１あり　画像２あり"
        else:
            other_data = "画像１あり"

        user_comment = damage_report.get("userFeedback")

        body = f'通報カテゴリ：{category_general}\n' \
               + f'通報カテゴリ詳細：{category_detailed}\n' \
               + f'日時：{date_time}\n' \
               + f'住所：{address}\n' \
               + f'補足情報：{other_data}\n' \
               + '---------------------\n' \
               + f'詳細：{user_comment}\n'

        return body

    def __generate_report_resume_message__(self, line_user_id):
        # Get message body
        report_text = self.__get_report_body__(
            user_session.getDamageReport(
                line_user_id
            ))

        # Get the message JSON from db
        report_msg_json = self.scenario_manager.__getScenarioData__(
            self.scenario_manager.scenario,
            dmgReportIds.REPORT_RESUME_CONFIRM
        )

        if report_msg_json:
            # Replace text with report
            # Max confirm template text size is 240
            report_msg_json["params"]["text"] = report_text[:240]

        user_session.setLastMessageId(
            line_user_id,
            dmgReportIds.REPORT_RESUME_CONFIRM
        )

        return self.scenario_manager.__generateMessage__(report_msg_json)

    ##############################
    ### POSTBACK DATA HANDLERS ###
    ##############################
    @staticmethod
    def __handle_postback_category_general__(postback_data, user_id):
        # Get category data and save it
        category = {}

        # Check if it is a custom category and extract it from the postback data string
        # Try to parse the data as a query string
        # If succesfull, treat it a custom category
        category_data = replace_lists(parse_qs(postback_data))

        if category_data:
            category["general"] = category_data.get('category', '不明なカテゴリ')
            category.update(category_data)
        else:
            category["general"] = postback_data

        user_session.setDamageReportCategory(user_id, category)

        # Depending on category, next message changes
        next_msg_id = None

        # Custom category handling
        if category_data:
            # Check for sub category message
            if 'sub_category_msg_id' in category_data:
                next_msg_id = category_data.get('sub_category_msg_id')
            else:
                next_msg_id = dmgReportIds.CAMERA_ACTION_DETAILED_PICTURE
        else:
            # Default behavior
            if postback_data == "河川":
                next_msg_id = dmgReportIds.CAMERA_ACTION_DETAILED_PICTURE # Proceed to camera action
            elif postback_data == "公園":
                # Detailed category carousel for park
                next_msg_id = dmgReportIds.CATEGORY_DETAILS_PARK_CAROUSEL
            else:
                next_msg_id = dmgReportIds.CAMERA_ACTION_DETAILED_PICTURE

        return next_msg_id

    @staticmethod
    def __handle_postback_category_detailed__(postback_data, user_id):
        # Get current general category and add detailed selection
        report = user_session.last_retrieved_user_session.get("damageReport")

        category = {}

        if report and report.get("category"):
            category = report.get("category")

        # Check if it is a custom sub category and extract it from the postback data string
        category_data = replace_lists(parse_qs(postback_data))

        if category_data:
            category["detailed"] = category_data.get('sub_category', '不明なサブカテゴリー')
            category.update(category_data)
        else:
            category["detailed"] = postback_data

        user_session.setDamageReportCategory(user_id, category)
        return dmgReportIds.CAMERA_ACTION_DETAILED_PICTURE # Proceed to camera picture

    @staticmethod
    def __handle_postback_camera_detailed_pic_confirm__(*args):
        return_value = None

        postback_data = args[0]
        if postback_data == "はい":
            # Get previously selected category (general and detailed)
            category = user_session.\
                       last_retrieved_user_session.get("damageReport").get("category")

            if category:
                general_category = category.get("general")

                if general_category == "河川":
                    return_value = dmgReportIds.STATUS_USER_COMMENT_RIVER
                elif general_category == "公園":
                    detailed_category = category.get("detailed")

                    # Depending on category, next message changes
                    if detailed_category.startswith("遊具"):
                        return_value = dmgReportIds.STATUS_USER_COMMENT_PARK_PLAYGROUND
                    elif detailed_category.startswith("照明灯"):
                        return_value = dmgReportIds.STATUS_USER_COMMENT_PARK_LIGHTING
                    elif detailed_category.startswith("ベンチなど"):
                        return_value = dmgReportIds.STATUS_USER_COMMENT_PARK_BENCH
                    elif detailed_category.startswith("水回り"):
                        return_value = dmgReportIds.STATUS_USER_COMMENT_PARK_WATER
                    elif detailed_category.startswith("樹木"):
                        return_value = dmgReportIds.STATUS_USER_COMMENT_PARK_TREE
                    elif detailed_category.startswith("その他"):
                        return_value = dmgReportIds.STATUS_USER_COMMENT_PARK_OTHER
                elif 'user_comment_msg_id' in category and category['user_comment_msg_id']:
                    return_value = category.get('user_comment_msg_id')
                else:
                    return_value = dmgReportIds.STATUS_USER_COMMENT_FREEFORM_INPUT
            else:
                return_value = dmgReportIds.STATUS_USER_COMMENT_FREEFORM_INPUT

        else:
            # Should be camera action message ID in case of negative response
            return_value = postback_data

        return return_value

    @staticmethod
    def __handle_postback_user_comment_category__(postback_data, user_id):
        # Save selected comment in database and proceed
        # to confirm message (should be dynamically generated)
        return_id = None

        if postback_data == "その他（自由記入）":
            return_id = dmgReportIds.STATUS_USER_COMMENT_FREEFORM_INPUT
        else:
            user_session.\
            setDamageReportUserFeedback(user_id, postback_data)

            return_id = dmgReportIds.STATUS_USER_COMMENT_CONFIRM

        return return_id

    def __handle_postback_report_resume_confirm__(self, postback_data, user_id):
        return_id = None

        if postback_data == "送る":
            if not self._is_damage_report_enabled():
                return_id = dmgReportIds.DISABLED_DAMAGE_REPORT_MODE
            else:
                self.__send_report_via_email__()
                self.__finished_report_flow__(user_id)
                return_id = dmgReportIds.END_REPORT_MODE_COMPOSITE_MESSAGE
            user_session.setSpecialFlow(user_id, None)
        elif postback_data == "やめる":
            # Disable this special flow
            user_session.setSpecialFlow(
                user_id,
                None
            )

        return return_id

    @staticmethod
    def __handle_postback_camera_action__(postback_data, user_id):
        # If skip option is selected, delete detailed image URL from session table
        return_id = None

        if postback_data == "スキップする":
            user_session.\
            __remove_damage_report_image__(user_id, "general")
            return_id = dmgReportIds.REPORT_RESUME_CONFIRM
        else:
            return_id = dmgReportIds.CAMERA_ACTION

        return return_id

    def __handle_location_based_zipcode__(self, address):
        report_allowed = True

        scenario = self.scenario_manager.scenario

        dynamodb = boto3.resource('dynamodb')
        table = dynamodb.Table(TABLE_SCENARIO_DATA)

        valid_zipcodes = None

        try:
            response = table.get_item(
                Key={
                    'id': scenario,
                    'typeId': 'zipcodes'
                }
            )
            item = response.get('Item')

            if item:
                valid_zipcodes = item.get('zipcodes')

        except ClientError:
            # No data found, just ignore
            pass

        if valid_zipcodes:
            regex = re.compile(r"(\b\d{3}ー\d{4}\b|\b\d{3}-\d{4}\b|\b\d{7}\b\s|\b\d{7}\b\s)")
            matches = re.findall(regex, address)

            report_allowed = False

            if matches and len(matches) > 0:
                for i in matches:
                    zipcode = i.replace('-', '')
                    zipcode = zipcode.replace('ー', '')
                    zipcode = zipcode.strip()
                    if zipcode in valid_zipcodes:
                        report_allowed = True

        return report_allowed

    ##################
    ### SIMULATION ###
    ##################
    def _simulate_text_message_event(self, scenario, last_message_id, text_data):
        messages = []

        if last_message_id == dmgReportIds.STATUS_USER_COMMENT_FREEFORM_INPUT:
            # Next message is the user comment confirm
            messages.append(
                self.scenario_manager.__getScenarioData__(
                    scenario,
                    dmgReportIds.STATUS_USER_COMMENT_CONFIRM
                )
            )

        return messages

    def _simulate_location_message_event(self, scenario, last_message_id):
        messages = []

        # Possible messages for damage report when receiving a location message
        if last_message_id == dmgReportIds.LOCATION_PICKER:
            messages.append(
                self.scenario_manager.__getScenarioData__(
                    scenario,
                    dmgReportIds.LOCATION_OUTSIDE_SEARCH_RADIUS
                )
            )

            messages.append(
                self.scenario_manager.__getScenarioData__(
                    scenario,
                    dmgReportIds.LOCATION_CONFIRM
                )
            )

        return messages

    def _simulate_image_message_event(self, scenario, last_message_id):
        messages = []

        if last_message_id == dmgReportIds.CAMERA_ACTION:
            messages.append(
                self.scenario_manager.__getScenarioData__(
                    scenario,
                    dmgReportIds.CAMERA_PICTURE_CONFIRM
                )
            )
        elif last_message_id == dmgReportIds.CAMERA_ACTION_DETAILED_PICTURE:
            messages.append(
                self.scenario_manager.__getScenarioData__(
                    scenario,
                    dmgReportIds.CAMERA_DETAILED_PICTURE_CONFIRM
                )
            )

        return messages

    def _simulate_postback_message_event(self, scenario, last_message_id, postback_data):
        params = parse_qs(postback_data)
        is_extend_payload = False
        if 'sub_category_msg_id' in params:
            is_extend_payload = True
            text_data = params['sub_category_msg_id'].pop()
        if 'user_comment_msg_id' in params:
            is_extend_payload = True
            text_data = params['user_comment_msg_id'].pop()

        messages = []
        if is_extend_payload:
            # categorized postback data
            message = self.scenario_manager.__getScenarioData__(
                scenario,
                text_data,
            )
            if message is not None:
                messages.append(message)
        elif last_message_id in self.postback_data_handler_simulate:
            next_messages = self.postback_data_handler_simulate[last_message_id](postback_data)

            for msg in next_messages:
                if isinstance(msg, str):
                    messages.append(
                        self.scenario_manager.__getScenarioData__(scenario, msg)
                    )
                else:
                    messages.append(msg)
        else:
            # normal postback data
            message = self.scenario_manager.__getScenarioData__(
                scenario,
                postback_data,
            )
            if message is not None:
                messages.append(message)

        return messages

    @staticmethod
    def _simulate_postback_category_general(postback_data):
        next_messages = []

        if postback_data == "公園":
            next_messages.append(
                dmgReportIds.CATEGORY_DETAILS_PARK_CAROUSEL
            )
        else:
            next_messages.append(
                dmgReportIds.CAMERA_ACTION_DETAILED_PICTURE
            )

        return next_messages

    @staticmethod
    def _simulate_postback_category_detailed(postback_data):
        return [dmgReportIds.CAMERA_ACTION_DETAILED_PICTURE] # Proceed to camera picture

    @staticmethod
    def _simulate_postback_camera_detailed_pic_confirm(postback_data):
        next_messages = []

        # Check if category is included in postback data
        split_postback = postback_data.split('#')

        if split_postback[0] == "はい":
            if len(split_postback) > 1:
                if split_postback[1] == "公園":
                    next_messages.append(
                        dmgReportIds.STATUS_USER_COMMENT_PARK_PLAYGROUND
                    )

                    next_messages.append(
                        dmgReportIds.STATUS_USER_COMMENT_PARK_LIGHTING
                    )

                    next_messages.append(
                        dmgReportIds.STATUS_USER_COMMENT_PARK_BENCH
                    )

                    next_messages.append(
                        dmgReportIds.STATUS_USER_COMMENT_PARK_WATER
                    )

                    next_messages.append(
                        dmgReportIds.STATUS_USER_COMMENT_PARK_TREE
                    )

                    next_messages.append(
                        dmgReportIds.STATUS_USER_COMMENT_PARK_OTHER
                    )
                elif split_postback[1] == "河川":
                    next_messages.append(
                        dmgReportIds.STATUS_USER_COMMENT_RIVER
                    )
                else:
                    next_messages.append(
                        dmgReportIds.STATUS_USER_COMMENT_FREEFORM_INPUT
                    )
            else:
                # Return all possible messages
                next_messages.append(
                    dmgReportIds.STATUS_USER_COMMENT_RIVER
                )

                next_messages.append(
                    dmgReportIds.STATUS_USER_COMMENT_PARK_PLAYGROUND
                )

                next_messages.append(
                    dmgReportIds.STATUS_USER_COMMENT_PARK_LIGHTING
                )

                next_messages.append(
                    dmgReportIds.STATUS_USER_COMMENT_PARK_BENCH
                )

                next_messages.append(
                    dmgReportIds.STATUS_USER_COMMENT_PARK_WATER
                )

                next_messages.append(
                    dmgReportIds.STATUS_USER_COMMENT_PARK_TREE
                )

                next_messages.append(
                    dmgReportIds.STATUS_USER_COMMENT_PARK_OTHER
                )
        else:
            next_messages.append(postback_data)

        return next_messages

    @staticmethod
    def _simulate_postback_camera_action(postback_data):
        next_messages = []

        if postback_data == "スキップする":
            # The report confirm message does not exist
            next_messages.append(dmgReportIds.REPORT_RESUME_CONFIRM)
        else:
            next_messages.append(dmgReportIds.CAMERA_ACTION)

        return next_messages

    @staticmethod
    def _simulate_postback_user_comment_category(postback_data):
        next_messages = []

        if postback_data == "その他（自由記入）":
            next_messages.append(
                dmgReportIds.STATUS_USER_COMMENT_FREEFORM_INPUT
            )
        else:
            next_messages.append(
                dmgReportIds.STATUS_USER_COMMENT_CONFIRM
            )

        return next_messages

    @staticmethod
    def _simulate_postback_report_resume_confirm(postback_data):
        next_messages = []

        if postback_data == "送る":
            next_messages.append(
                dmgReportIds.END_REPORT_MODE_COMPOSITE_MESSAGE
            )

        return next_messages

    def get_start_event(self, scenario):
        message = self.scenario_manager.__getScenarioData__(
            scenario,
            dmgReportIds.REPORT_MODE
        )

        return {
            "type": 'text_message',
            "data": dmgReportIds.DAMAGE_REPORT_START_KEYWORD,
            "message": message
        }

    ### additional method
    def __get_dr_talk_id_active__(self):
        result = DEFAULT_DAMAGE_REPORT_TALK_DATA_ID
        try:
            default_talk = self.__get_message_by_id(DEFAULT_DAMAGE_REPORT_TALK_DATA_ID)
            if default_talk is None:
                return result
            
            versionActive = default_talk.get('versionActive', None)
            if versionActive:
                result = versionActive
                versionActiveInfo = next((x for x in default_talk["versions"] if x["id"] == versionActive), None)
                if versionActiveInfo: 
                    self.damage_report_talk_start_message_id = versionActiveInfo["startMessageId"]
                    self.damage_report_talk_start_message_text = versionActiveInfo["textMapping"]
        except ClientError:
            # No data found
            result = None
        return result
    
    def __get_message_by_id(self, message_id):
        scenarioId = self.scenario_manager.__getActiveScenario__()
        table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)
        result = None
        try:
            response = table.get_item(
                Key={
                    'scenario': scenarioId,
                    'dataId': message_id,
                }
            )
            result = replace_decimals(response.get('Item'))
        except ClientError:
            # No data found
            result = None
        return result
    
    def __handle_webhook_event_default__(self, event, event_type):
        message = None

        if event_type == 'postback':
            message = self.scenario_manager.getMessageById(event.postback.data)
        elif event_type == 'text_message':
            message = self.scenario_manager.getMessageByText(event.message.text)
            
        return message

    def __handle_postback_event_versioning__(self, postback_data, user_id):
        last_message_id = user_session.last_retrieved_user_session.get("lastMessageId")
        lastMessage = self.__get_message_by_id(last_message_id)
        if not lastMessage: return None
        
        damageReportField = lastMessage.get('targetDataField', None)
        
        # get text of postback event & check if action is images/location skipped
        params = lastMessage.get('params')
        textField = None
        lst_special_postback_actions = ['https://line.me/R/nv/location', 'https://line.me/R/nv/camera/', 'https://line.me/R/nv/cameraRoll/single']
        isSpecialActionButReceivePostBackData = False

        # Processing embedded postback data
        if '___' in postback_data:
            postback_data, textField = postback_data.split('___')

        for key, val in params.items():
            if key.startswith('actions.') and isinstance(val, dict):
                if val.get('uri') in lst_special_postback_actions:
                    isSpecialActionButReceivePostBackData = True
                    break
                if val.get('data') == postback_data:
                    if damageReportField == 'category':
                        # save email config
                        email = val.get('categoryEmail')
                        if email:
                            user_session.setDamageReportVersion(user_id, 'categoryEmail', email, lastMessage.get('nameLBD'))
                    if textField == None:
                        textField = val.get('text', None)

        if isSpecialActionButReceivePostBackData: 
            return self.__handle_next_message_versioning(postback_data, user_id)

        # check if damageReportField is feedback => and postback_data is start with "その他"
        if damageReportField == 'feedback':
            if isinstance(textField, str):
                if textField.startswith('その他'):
                    return TextSendMessage(text="どのような事象が起きているのか、詳細をキーボードで入力してください。")
                  
        if damageReportField and textField: user_session.setDamageReportVersion(user_id, damageReportField, textField, lastMessage.get('nameLBD'))
        return self.__handle_next_message_versioning(postback_data, user_id)

    def __handle_text_event_versioning__(self, text, user_id):
        last_message_id = user_session.last_retrieved_user_session.get("lastMessageId")
        lastMessage = self.__get_message_by_id(last_message_id)
        if not lastMessage: return
        
        # map to session
        damageReportField = lastMessage.get('targetDataField')
        if damageReportField and text: user_session.setDamageReportVersion(user_id, damageReportField, text, lastMessage.get('nameLBD'))
        
        # get link to message
        linkToDataId: str = lastMessage['params'].get("linkToDataId", None)

        # Check if text is freely input of others "feedback"
        if damageReportField == 'feedback':
            lstParams = lastMessage.get('params')
            for key, value in lstParams.items():
                if lastMessage.get('dataType') == 'carousel':
                    if key.startswith('action.') and isinstance(value, dict):
                        if value.get('text').startswith('その他'):
                            linkToDataId = value.get('data')
                else:
                    if key.startswith('actions.') and isinstance(value, dict):
                        if value.get('text').startswith('その他'):
                            linkToDataId = value.get('data')

        if not linkToDataId: return
        return self.__handle_next_message_versioning(linkToDataId, user_id)

    def __handle_image_message_versioning__(self, imageMessage, user_id: str):
        last_message_id = user_session.last_retrieved_user_session.get("lastMessageId")
        lastMessage = self.__get_message_by_id(last_message_id)
        if not lastMessage: return
        
        # map to session
        damageReportField = lastMessage.get('targetDataField')

        # upload
        image_url = self.__download_line_image_upload_s3__(user_id, imageMessage.id, "pic_" + damageReportField + "_")
        
        user_session.setDamageReportVersionImage(user_id, damageReportField, image_url, lastMessage.get('nameLBD'))
        
        # get link to message
        linkToDataId: str = lastMessage['params'].get("linkToDataId", None)
        if not linkToDataId: return
        return self.__handle_next_message_versioning(linkToDataId, user_id)

    def __handle_location_message_versioning__(self, locationMessage, user_id: str):
        last_message_id = user_session.last_retrieved_user_session.get("lastMessageId")
        lastMessage = self.__get_message_by_id(last_message_id)
        if not lastMessage: return
        
        # map to session
        damageReportField = lastMessage.get('targetDataField')

        location = {}
        location["address"] = locationMessage.address
        location["latitude"] = decimal.Decimal(str(locationMessage.latitude))
        location["longitude"] = decimal.Decimal(str(locationMessage.longitude))
        check_location = self.__handle_location_based_zipcode__(locationMessage.address)

        if not check_location:
            message = self.scenario_manager.getMessageById(
                dmgReportIds.LOCATION_OUTSIDE_SEARCH_RADIUS
            )

            user_session.setSpecialFlow(
                user_id,
                None
            )

            return message
        
        user_session.setDamageReportVersionLocation(user_id, damageReportField, location, lastMessage.get('nameLBD'))
        
        # get link to message
        linkToDataId: str = lastMessage['params'].get("linkToDataId", None)
        if not linkToDataId: return
        return self.__handle_next_message_versioning(linkToDataId, user_id)
        
    def __send_report_via_email_versioning__(self):
        damage_report_version = user_session.\
                                last_retrieved_user_session.get("damageReportVersion")
        if not damage_report_version: return

        category_email = damage_report_version.get("categoryEmail", '')
        if not category_email: return

        if EMAIL_DOMAIN:
            address_source = 'damage-report@' + EMAIL_DOMAIN
        else:
            address_source = category_email
        address_to = category_email
        subject = "損傷報告"

        body = self.__get_report_body_versioning__(damage_report_version)

        images = damage_report_version.get("images", None)

        attachments = {}
        attachments["type"] = "NORMAL"
        attachments["attachments"] = []

        if images:
            image_general_url = images[0]
            image_detailed_url = images[1] if len(images) > 1 else None
            body = body + f'画像１URL：{image_detailed_url or ""}\n' \
                        + f'画像２URL：{image_general_url}\n'

            if image_detailed_url:
                attachments["attachments"].append(image_detailed_url)
            if image_general_url and image_general_url != "無い":
                attachments["attachments"].append(image_general_url)

        if isinstance(address_to, list):
            for email in address_to:
                send_email(address_source, email, subject, body, attachments)
        elif isinstance(address_to, str):
            send_email(address_source, address_to, subject, body, attachments)

    def __generate_report_resume_message_versioning__(self, next_message, user_id):
        damage_report_extra = user_session.getUserSession(user_id, True).get("damageReportVersion")
        # Get message body
        report_text = self.__get_report_body_versioning__(damage_report_extra)

        if next_message:
            # Replace text with report
            # Max confirm template text size is 240
            next_message["params"]["text"] = report_text[:240]

        return self.scenario_manager.__generateMessage__(next_message)
    
    def __handle_next_message_versioning(self, next_message_id, user_id):
        next_message_gen = self.scenario_manager.getMessageById(next_message_id)
        next_message_from_db = self.__get_message_by_id(next_message_id)
        damageReportFieldNext = next_message_from_db.get('targetDataField', None)
        
        if damageReportFieldNext == "confirm_summary_report":
            next_message_gen = self.__generate_report_resume_message_versioning__(next_message_from_db, user_id)
        elif damageReportFieldNext == "end_talk":
            self.__send_report_via_email_versioning__()
            self.__finished_report_flow_versioning__(user_id)
            user_session.resetDamageReportVersion(user_id)
        else:
            next_message_gen = self.__embedded_value_to_messages_postback_data__(next_message_gen)

        return next_message_gen
    
    # Notify to other function to handle save report to db
    def __finished_report_flow_versioning__(self, line_user_id):
        try:
            damage_report = user_session.\
                            last_retrieved_user_session.get("damageReportVersion")

            messageBody = {
                "uid": line_user_id,
                "from": "chatbot",
                "scenarioId": self.scenario_manager.scenario,
                "payload": damage_report,
                "talkId": self.damage_report_talk_id
            }
            res = sqsClient.send_message(
                QueueUrl = CREATE_DAMAGE_REPORT_QUEUE,
                MessageBody = json.dumps(messageBody)
            )
            return res['MessageId']
        except Exception as e:
            return
        
    @staticmethod
    def __get_report_body_versioning__(damage_report_extra):
        # Body elements
        category_general = damage_report_extra.get("category", "")
        category_detailed = damage_report_extra.get("subject", "")
        date_time = get_current_date_time_report()
        address = damage_report_extra.get("location", {}).get("address")

        other_data = ""
        if len(damage_report_extra.get("images", [])) > 1:
            other_data = "画像１あり　画像２あり"
        else:
            other_data = "画像１あり"

        user_comment = damage_report_extra.get("feedback", "")

        body = f'通報カテゴリ：{category_general}\n' \
               + f'通報カテゴリ詳細：{category_detailed}\n' \
               + f'日時：{date_time}\n' \
               + f'住所：{address}\n' \
               + f'補足情報：{other_data}\n' \
               + '---------------------\n' \
               + f'詳細：{user_comment}\n'

        lst_key_known = ["category", "subject", "feedback", "location", "address", "images"]
        images_keys =damage_report_extra.get("__images_key__", [])
        location_keys =damage_report_extra.get("__location_key__", [])

        otherFieldLabelMapping = damage_report_extra.get("__field_label_mappings__", [])
        for item in otherFieldLabelMapping:
            try:
                key, label = item.split("___")  # Split at the "___" delimiter
                if key not in lst_key_known and damage_report_extra.get(key):
                    if key in images_keys:
                        body = body + f'{label}: 画像１あり\n'
                    elif key in location_keys:
                        body = body + f'{label}: {damage_report_extra.get(key, {}).get("address")}\n'
                    else:
                        body = body + f'{label}: {damage_report_extra.get(key)}\n'
            except ValueError:
                print(f"Error: Invalid format for item: {item}")  # Handle cases with incorrect format

        return body

    def __embedded_value_to_messages_postback_data__(self, message):
        if isinstance(message, TemplateSendMessage):
            if isinstance(message.template, CarouselTemplate):
                for column in message.template.columns:
                    self.modify_actions(column.actions)
            elif isinstance(message.template, ButtonsTemplate):
                self.modify_actions(message.template.actions)

        return message

    @staticmethod
    def modify_actions(actions):
        if actions:
            for action in actions:
                if isinstance(action, PostbackAction):
                    action.data = f"{action.data}___{action.display_text}"