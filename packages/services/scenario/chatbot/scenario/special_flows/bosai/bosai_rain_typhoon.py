"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
from scenario.special_flows.bosai.bosai import Bosai
import scenario.special_flows.bosai.message_id as bosaiMsgIds
import os
from scenario.user_session import USER_SESSION_HELPER as user_session
from sendTalkLog import SendTalkLog

SCENARIO_ENVIRONMENT = os.environ['SCENARIO_ENVIRONMENT'] # Scenario environment

# Instantiate sender
sender = SendTalkLog()

class BosaiRainTyphoon(Bosai):
    """Implementation for the bosai special flow (search only)
    """
    def __init__(self, scenario_manager):
        super().__init__(scenario_manager)

        self.search_ids = {
            "SEARCH_RESULT": bosaiMsgIds.RAIN_TYPHOON_SHELTER_SEARCH_RESULT,
            "GUIDANCE_ENDED": bosaiMsgIds.RAIN_TYPHOON_GUIDANCE_ENDED,
            "SHELTER_NOT_FOUND": bosaiMsgIds.RAIN_TYPHOON_SHELTER_NOT_FOUND,
            "SHELTER_TEMPLATE": bosaiMsgIds.RAIN_TYPHOON_SHELTER_TEMPLATE,
        }

        self.use_bosai_shelter_setting = True

    @staticmethod
    def get_identifier():
        return 'BOSAI_RAIN_TYPHOON'

    def flow_start(self, event, event_type):
        # Damage report can be started either by text or postback events
        message = None
        start = False

        start_flow_ids = [
            bosaiMsgIds.RAIN_TYPHOON_SEARCH_START, 
            bosaiMsgIds.RAIN_TYPHOON_EXPLANATION
        ]
        if event_type == 'postback' and\
        event.postback.data in start_flow_ids:
            message = self.scenario_manager.getMessageById(
                event.postback.data
            )
            if message:
                start = True
                if SCENARIO_ENVIRONMENT == 'production':
                    sender.putReplyTalkLogs(
                        event,
                        message,
                        event.postback.data
                    )

        return start, message

    def _process_postback_event(self, postback_data, user_id):
        """Handles postback events when the bosai flow is active.

        :param str postback_data: The postback string from the LINE webhook event
        :param str user_id: LINE user id associated with the event
        """
        result = None

        if postback_data in bosaiMsgIds.BOSAI_RAIN_TYPHOON_IDS:
            result = self.scenario_manager.getMessageById(
                postback_data
            )
            if SCENARIO_ENVIRONMENT == 'production':
                sender.putReplyProcessPostbackTalkLogs(result, user_id, postback_data)

        return result

    def _process_location_message(self, message, user_id):
        """Handles location message events when the bosai flow is active.

        :param message: The location message from the LINE webhook event
        :type message: linebot.models.LocationMessage
        :param str user_id: LINE user id associated with the event
        """

        # Save user location if last message was the one requesting it
        last_message_id = user_session.\
                          last_retrieved_user_session.get("lastMessageId")

        return_messages = None

        if last_message_id in bosaiMsgIds.BOSAI_RAIN_TYPHOON_LOCATION_PICKER_IDS:
            return_messages = self.search_for_nearby_shelters(message, user_id)
            if SCENARIO_ENVIRONMENT == 'production':
                sender.putReplyProcessLocationTalkLogs(
                    return_messages[0],
                    user_id,
                )

        return return_messages

    def _simulate_location_message_event(self, scenario, last_message_id):
        messages = []

        if last_message_id in bosaiMsgIds.BOSAI_RAIN_TYPHOON_LOCATION_PICKER_IDS:
            self._simulate_shelter_search(
                scenario,
                messages
            )

        return messages

    def _simulate_postback_message_event(self, scenario, last_message_id, postback_data):
        messages = []

        if postback_data in bosaiMsgIds.BOSAI_RAIN_TYPHOON_IDS:
            messages.append(
                self.scenario_manager.__getScenarioData__(
                    scenario,
                    postback_data
                )
            )

        return messages

    def get_start_event(self, scenario):
        message = self.scenario_manager.__getScenarioData__(
            scenario,
            bosaiMsgIds.RAIN_TYPHOON_SEARCH_START
        )

        return {
            "type": 'postback',
            "data": bosaiMsgIds.RAIN_TYPHOON_SEARCH_START,
            "message": message
        }
