"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os
import json
import urllib.parse
from functools import cmp_to_key
from decimal import Decimal

import boto3
from botocore.exceptions import ClientError
from linebot.models import FlexSendMessage, TextSendMessage

from scenario.special_flows.base_flow import BaseFlow
import scenario.special_flows.bosai.message_id as bosaiMsgIds
from scenario.user_session import USER_SESSION_HELPER as user_session
from scenario.util import haversine, DecimalEncoder, get_search_area_coordinates
from sendTalkLog import SendTalkLog

# Instantiate sender
sender = SendTalkLog()

SCENARIO_ENVIRONMENT = os.environ['SCENARIO_ENVIRONMENT']

TABLE_SHELTERS = boto3.resource('dynamodb').Table(os.environ['TABLE_BOSAI_SHELTERS'])

DEFAULT_SEARCH_RADIUS = 1000

class Bosai(BaseFlow):
    """Implementation for the bosai special flow.
    """
    def __init__(self, scenario_manager):
        super().__init__(scenario_manager)

        self.postback_data_handler = {
        }

        self.shelter_status = {
            0: "未開設",
            1: "開設",
            2: "満"
        }

        self.shelter_type = {
            1: "避難所",
            2: "臨時避難所",
            3: "避難所を兼ねる避難場所",
            4: "広域避難場所:開設措置なし",
            5: "一時避難場所:開設措置なし",
            9: "福祉避難所"
        }

        self.disaster_type = {
            1: "洪水（風水害）",
            2: "崖崩れ、土石流及び地滑り",
            3: "高潮",
            4: "地震",
            5: "津波",
            6: "大規模な火事",
            7: "内水氾濫",
            8: "火山現象"
        }

        self.search_ids = {
            "SEARCH_RESULT": bosaiMsgIds.SHELTER_SEARCH_RESULT,
            "GUIDANCE_ENDED": bosaiMsgIds.GUIDANCE_ENDED,
            "SHELTER_NOT_FOUND": bosaiMsgIds.SHELTER_NOT_FOUND,
            "SHELTER_TEMPLATE": bosaiMsgIds.SHELTER_TEMPLATE,

        }

        self.use_bosai_shelter_setting = True

    @staticmethod
    def get_identifier():
        return 'BOSAI'

    def is_active(self):
        is_active = False

        bosai_settings = self.scenario_manager.settings.get('bosaiMode')

        if bosai_settings:
            bosai_active = bosai_settings.get(SCENARIO_ENVIRONMENT)
            talk_id = bosai_settings.get(SCENARIO_ENVIRONMENT + "_talkId")

            # BOSAI_FLOW_TALK, BOSAI_RAIN_TYPHOON_TALK, BOSAI_EARTHQUAKE_TALK, etc.
            is_active = bosai_active and talk_id != 'BOSAI_FLOW_SEARCH_TALK'

        return is_active

    def flow_start(self, event, event_type):
        # Damage report can be started either by text or postback events
        message = None
        start = False

        if event_type == 'postback':
            list_of_start_messages = [
                bosaiMsgIds.BOSAI_FLOW_START,
                bosaiMsgIds.BOSAI_DISASTER_EXPLANATION_1,
                bosaiMsgIds.SHELTER_SEARCH_CONFIRM_1_1
            ]

            if event.postback.data in list_of_start_messages:
                message = self.scenario_manager.getMessageById(
                    event.postback.data
                )

                if message:
                    start = True
                    if SCENARIO_ENVIRONMENT == 'production':
                        sender.putReplyTalkLogs(
                            event,
                            message,
                            event.postback.data
                        )
        return start, message

    def handle_event_postback(self, postback):
        return self._process_postback_event(
            postback.postback.data,
            postback.source.user_id
        )

    def handle_event_text_message(self, text_message):
        return self._process_text_message(
            text_message.message.text,
            text_message.source.user_id
        )

    def handle_event_image_message(self, image_message):
        return self._process_image_message(
            image_message.message,
            image_message.source.user_id
        )

    def handle_event_location_message(self, location_message):
        return self._process_location_message(
            location_message.message,
            location_message.source.user_id,
        )

    def _is_bosai_enabled(self):
        """Checks if the bosai scenario is enabled.
        This can be changed by the user on the management screen.

        :returns bool: True if enable, False if not
        """
        active = False

        if self.scenario_manager.scenario_version_object and\
        self.scenario_manager.scenario_version_object.get("specialTalks"):
            active = self.scenario_manager.scenario_version_object.\
            get("specialTalks").get("bosai")

        return active

    def _process_postback_event(self, postback_data, user_id):
        """Handles postback events when the bosai flow is active.

        :param str postback_data: The postback string from the LINE webhook event
        :param str user_id: LINE user id associated with the event
        """
        result = None

        if postback_data == 'BOSAI_GUIDANCE_ENDED':
            user_session.setSpecialFlow(user_id, None)
            result = self.scenario_manager.getMessageById(
                bosaiMsgIds.GUIDANCE_ENDED
            )
        else:
            if postback_data in bosaiMsgIds.BOSAI_FLOW_IDS:
                result = self.scenario_manager.getMessageById(
                    postback_data
                )
        if SCENARIO_ENVIRONMENT == 'production':
            sender.putReplyProcessPostbackTalkLogs(result, user_id, postback_data)

        return result

    def _process_text_message(self, text, user_id):
        """Handles text message events when the bosai flow is active.

        :param str text: The text string from the LINE webhook event
        :param str user_id: LINE user id associated with the event
        """

    def _process_location_message(self, message, user_id):
        """Handles location message events when the bosai flow is active.

        :param message: The location message from the LINE webhook event
        :type message: linebot.models.LocationMessage
        :param str user_id: LINE user id associated with the event
        """

        # Save user location if last message was the one requesting it
        last_message_id = user_session.\
                          last_retrieved_user_session.get("lastMessageId")

        return_messages = None

        if last_message_id in bosaiMsgIds.BOSAI_LOCATION_PICKER_IDS:
            return_messages = self.search_for_nearby_shelters(message, user_id)
            if SCENARIO_ENVIRONMENT == 'production':
                sender.putReplyProcessLocationTalkLogs(
                    return_messages[0],
                    user_id,
                )

        return return_messages

    def _process_image_message(self, message, user_id):
        """Handles image message events when the bosai flow is active.

        :param message: The image message from the LINE webhook event
        :type message: linebot.models.ImageMessage
        :param str user_id: LINE user id associated with the event
        """

    def search_for_nearby_shelters(self, message, user_id):
        """Helper method to search for nearby shelters
        based on received location data from LINE api.

        :param message: The location message from the LINE webhook event
        :type message: linebot.models.LocationMessage
        :param str user_id: LINE user id associated with the event
        """
        # When receiving a location message during bosai flow,
        # generate a carousel with the closest shelters in a 1km
        # radius
        messages = []

        latitude = float(message.latitude)
        longitude = float(message.longitude)

        search_radius = float(self.scenario_manager.settings.get(
            'bosaiSearchRadius',
            DEFAULT_SEARCH_RADIUS
        ))

        # Update search radius number dynamically
        # If less than 1km, write in meters
        radius_string = ''
        if search_radius < 1000:
            radius_string = str("{:.0f}m".format(search_radius))
        else:
            radius_string = str("{:.0f}km".format(search_radius / 1000))

        try:
            list_of_shelters = self._get_nearby_shelters(latitude, longitude, search_radius)

            if len(list_of_shelters) > 0:
                # Construct a carousel message with the shelter data
                carousel_msg = self._generate_shelter_carousel_message(
                    list_of_shelters,
                    latitude,
                    longitude
                )

                # Return the text message and generated carousel message at the same time
                text_message = self.scenario_manager.getMessageById(
                    self.search_ids.get("SEARCH_RESULT")
                )

                text_message.text = text_message.text.replace(
                    '${radius}',
                    radius_string
                )

                # Guidance ended message
                guidance_end_message = self.scenario_manager.getMessageById(
                    self.search_ids.get("GUIDANCE_ENDED")
                )

                messages = [text_message, carousel_msg, guidance_end_message]

                # Disable bosai flow for this user
                user_session.setSpecialFlow(user_id, None)
            else:
                # No shelters found message
                shelter_not_found_msg = self.scenario_manager.getMessageById(
                    self.search_ids.get("SHELTER_NOT_FOUND")
                )

                shelter_not_found_msg.template.text = shelter_not_found_msg.template.text.replace(
                    '${radius}',
                    radius_string
                )

                messages = [shelter_not_found_msg]

        except ClientError:
            # Error happened while fetching results
            # Return a error message to the user
            error_text = '検索結果が取得できませんでした。再検索を行ってください。\nエラー種別：データベースエラー'

            messages = [TextSendMessage(
                text=error_text
            )]

        return messages

    def _create_shelter_search_params(self, latitude, longitude, radius):
        # Consider all shelters that are possibly inside the search radius
        # Not disclosed shelters are ignored
        search_area_coordinates = get_search_area_coordinates(
            latitude,
            longitude,
            radius
        )

        filter_expression = ('#publicDisclosure = :disclosed AND '
                             '(#latitude BETWEEN :lat_min AND :lat_max) AND '
                             '(#longitude BETWEEN :lon_min AND :lon_max)')

        expression_values = {
            ':disclosed' : True,
            ':lat_max': Decimal("{:.9f}".format(search_area_coordinates.get('lat_max', 0.0))),
            ':lat_min': Decimal("{:.9f}".format(search_area_coordinates.get('lat_min', 0.0))),
            ':lon_max': Decimal("{:.9f}".format(search_area_coordinates.get('lon_max', 0.0))),
            ':lon_min': Decimal("{:.9f}".format(search_area_coordinates.get('lon_min', 0.0)))
        }

        expression_names = {
            '#publicDisclosure': 'publicDisclosure',
            '#latitude': 'latitude',
            '#longitude': 'longitude'
        }

        # Also check if only open shelters should be shown
        only_open_shelters = self.scenario_manager.settings.get(
            'bosaiOpenShelterOnly',
            False
        )

        if self.use_bosai_shelter_setting and self.is_active() and only_open_shelters:
            filter_expression += ' AND (#status = :open_status OR #status = :full_status)'
            expression_names['#status'] = 'status'
            expression_values[':open_status'] = 1
            expression_values[':full_status'] = 2

        return filter_expression, expression_values, expression_names

    def _get_nearby_shelters(self, latitude, longitude, radius):
        """Retrieves shelter inside a radius (in meters).

        Up to 10 shelters are returned and they are sorted
        in order of proximity.
        """
        nearby_shelters = []

        registered_shelters = []

        filter_expression,\
        expression_values,\
        expression_names = self._create_shelter_search_params(
            latitude,
            longitude,
            radius
        )

        response = TABLE_SHELTERS.scan(
            FilterExpression=filter_expression,
            ExpressionAttributeValues=expression_values,
            ExpressionAttributeNames=expression_names
        )

        registered_shelters.extend(response['Items'])

        while 'LastEvaluatedKey' in response:
            response = TABLE_SHELTERS.scan(
                ExclusiveStartKey=response['LastEvaluatedKey'],
                FilterExpression=filter_expression,
                ExpressionAttributeValues=expression_values,
                ExpressionAttributeNames=expression_names
            )
            registered_shelters.extend(response['Items'])

        for shelter in registered_shelters:
            # Calculate distance. If it less than passed radius,
            # add to the output list and save the distance for later sorting
            shelter_lat = float(shelter.get('latitude'))
            shelter_long = float(shelter.get('longitude'))

            distance = haversine(
                latitude,
                longitude,
                shelter_lat,
                shelter_long
            )

            if distance <= radius:
                shelter['distance'] = distance
                nearby_shelters.append(shelter)


        #Sort shelters by distance to current location
        nearby_shelters.sort(
            key=lambda item: item.get('distance')
        )

        #Group shelters by status into three lists
        #Append up to 10 to result list in order of 開設(1) > 満(2) > 不開設(0)
        open_shelters = [shelter for shelter in nearby_shelters if shelter.get('status') == 1]
        shelters_to_return = open_shelters[:10]
        if len(shelters_to_return) < 10:
            full_shelters = [shelter for shelter in nearby_shelters if shelter.get('status') == 2]
            shelters_to_return.extend(full_shelters[:(10 - len(shelters_to_return))])
        if len(shelters_to_return) < 10:
            closed_shelters = [shelter for shelter in nearby_shelters if shelter.get('status') == 0]
            shelters_to_return.extend(closed_shelters[:(10 - len(shelters_to_return))])

        return shelters_to_return

    def _generate_shelter_carousel_message(self, shelter_list, user_lat, user_long):
        carousel_msg = None

        # Get carousel bubble flex template
        shelter_template_item = self.scenario_manager.__getScenarioData__(
            self.scenario_manager.scenario,
            self.search_ids.get("SHELTER_TEMPLATE")
        )
        print("BOSAI: scenario =", self.scenario_manager.scenario, self.search_ids.get("SHELTER_TEMPLATE"))
        print("BOSAI: shelter_template_item =", shelter_template_item, self.is_active())
        shelter_flex_json = json.dumps(
            shelter_template_item.get('params'),
            ensure_ascii=False,
            cls=DecimalEncoder
        )
        shelter_flex_json = shelter_flex_json.replace(
            '#AEAEAEFF',
            '${statusColor}'
        )

        # For each shelter in the list, overwrite the values in the JSON string
        # and create a bubble flex
        flex_bubbles = []

        for shelter in shelter_list:
            # Name
            json_str = shelter_flex_json.replace(
                '${name}',
                shelter.get('shelterName')
            )

            # Status
            status = self.shelter_status.get(
                shelter.get('status')
            )

            if not status:
                # Default to open
                status = self.shelter_status.get(1)

            json_str = json_str.replace(
                '${status}',
                status
            )

            # Color
            color_mapping = {
                "未開設": "#FF0000",  # 赤
                "開設": "#AEAEAEFF",  # グレー
                "満": "#0000FF"  # 青
            }
            color_status = color_mapping.get(status)
            json_str = json_str.replace(
                '${statusColor}',
                color_status
            )

            # Address
            json_str = json_str.replace(
                '${address}',
                shelter.get('address')
            )

            # Coordinates
            shelter_coordinates = str(shelter.get('latitude')) + ',' + str(shelter.get('longitude'))
            user_coordinates = str(user_lat) + ',' + str(user_long)

            json_str = json_str.replace(
                '${shelter_coordinates}',
                shelter_coordinates
            ).replace(
                '${user_coordinates}',
                user_coordinates
            )

            # Share text is a message plus google maps URL with coordinates
            share_text = '避難を開始しました[{shelter_name}]\n'.format(
                shelter_name=shelter.get('shelterName')
            )
            share_text += f'https://www.google.com/maps/dir/?api=1&travelmode=walking&destination={shelter_coordinates}'

            json_str = json_str.replace(
                '${shelter_share_text}',
                urllib.parse.quote(share_text)
            )

            # Convert JSON back to dict
            flex_json = json.loads(json_str)
            self._update_flex_shelter_type(shelter, flex_json)
            self._update_flex_disaster_type(shelter, flex_json)
            self._update_flex_facility_ammenities(shelter, flex_json)
            flex_bubbles.append(flex_json)

        # Carousle message using LINE SDK
        carousel = {}
        carousel['type'] = "carousel"
        carousel['contents'] = flex_bubbles

        carousel_msg = FlexSendMessage(
            alt_text='避難所の検索結果',
            contents=carousel
        )

        return carousel_msg

    @staticmethod
    def _create_flex_text_element(text, bold=False):
        element = {}
        element['type'] = 'text'
        element['text'] = text

        if bold:
            element['weight'] = "bold"

        return element

    def _update_flex_shelter_type(self, shelter, flex_json):
        list_container = flex_json['body']['contents'][4]['contents']

        # For each type in the shelter item, create a text element
        type_set = shelter.get('typeShelter')

        for type_code in type_set:
            type_value = self.shelter_type.get(
                int(type_code)
            )

            if type_value:
                list_container.append(
                    self._create_flex_text_element(
                        '＊ ' + type_value
                    )
                )

    def _update_flex_disaster_type(self, shelter, flex_json):
        list_container = flex_json['body']['contents'][6]['contents']

        # For each type in the shelter item, create a text element
        type_set = shelter.get('typeDisaster')

        for type_code in type_set:
            type_value = self.disaster_type.get(
                int(type_code)
            )

            if type_value:
                list_container.append(
                    self._create_flex_text_element(
                        '＊ ' + type_value
                    )
                )

    def _update_flex_facility_ammenities(self, shelter, flex_json):
        list_container = flex_json['body']['contents'][8]['contents']

        # Check each service and create a text element if it's available at the shelter
        facility_data = shelter.get('facility')

        # Network
        if facility_data.get('network'):
            list_container.append(
                self._create_flex_text_element(
                    '＊ Wi-Fi等が存在する'
                )
            )

        # Cell Phone
        if facility_data.get('cellPhone'):
            list_container.append(
                self._create_flex_text_element(
                    '＊ 携帯の電波が入る'
                )
            )

        # Quarantine
        if facility_data.get('quarantine', 0) == 1:
            list_container.append(
                self._create_flex_text_element(
                    '＊ 隔離場所を用意可能'
                )
            )

        # Covid-19 equipment
        if facility_data.get('equipmentCovid'):
            list_container.append(
                self._create_flex_text_element(
                    '＊ コロナ関連の機材・備品等の準備'
                )
            )

    ##################
    ### SIMULATION ###
    ##################
    def _generate_sample_shelter_carousel_data(self, scenario):
        carousel = {}
        carousel['dataType'] = "carouselFlex"
        carousel['nameLBD'] = "避難所テンプレート"
        carousel['scenario'] = scenario
        carousel['dataId'] = 'DUMMY_BOSAI_SHELTER_CAROUSEL'

        carousel['params'] = {}
        carousel['params']['bubbleParam'] = [self.search_ids.get("SHELTER_TEMPLATE")]

        return carousel

    def _simulate_shelter_search(self, scenario, messages):
        # Bosai search can either return a carousel with shelter data
        # or an apology message if nothing was found
        success_msgs = []

        # Success text
        success_msgs.append(
            self.scenario_manager.__getScenarioData__(
                scenario,
                self.search_ids.get("SEARCH_RESULT")
            )
        )

        # Shelter carousel template
        success_msgs.append(
            self._generate_sample_shelter_carousel_data(
                scenario
            )
        )

        # Guidance ended message
        success_msgs.append(
            self.scenario_manager.__getScenarioData__(
                scenario,
                self.search_ids.get("GUIDANCE_ENDED")
            )
        )

        messages.append(success_msgs)

        # Not found
        messages.append(
            self.scenario_manager.__getScenarioData__(
                scenario,
                self.search_ids.get("SHELTER_NOT_FOUND")
            )
        )

    def _simulate_text_message_event(self, scenario, last_message_id, text_data):
        pass

    def _simulate_location_message_event(self, scenario, last_message_id):
        messages = []

        if last_message_id in bosaiMsgIds.BOSAI_LOCATION_PICKER_IDS:
            self._simulate_shelter_search(
                scenario,
                messages
            )

        return messages

    def _simulate_image_message_event(self, scenario, last_message_id):
        pass

    def _simulate_postback_message_event(self, scenario, last_message_id, postback_data):
        messages = []

        if postback_data == 'BOSAI_GUIDANCE_ENDED':
            messages.append(
                self.scenario_manager.__getScenarioData__(
                    scenario,
                    bosaiMsgIds.GUIDANCE_ENDED
                )
            )
        else:
            if postback_data in bosaiMsgIds.BOSAI_FLOW_IDS:
                messages.append(
                    self.scenario_manager.__getScenarioData__(
                        scenario,
                        postback_data
                    )
                )

        return messages

    def get_start_event(self, scenario):
        message = self.scenario_manager.__getScenarioData__(
            scenario,
            bosaiMsgIds.BOSAI_FLOW_START
        )

        return {
            "type": 'postback',
            "data": bosaiMsgIds.BOSAI_FLOW_START,
            "message": message
        }
