"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os

import boto3
from botocore.exceptions import ClientError

from scenario.util import replace_decimals

TABLE_USER_SESSION = os.environ['TABLE_CHATBOT_USER_SESSION']
SCENARIO_ENVIRONMENT = os.environ['SCENARIO_ENVIRONMENT']

# Class to implement api calls, such as calling external api, setting language, etc
class UserSession():
    """Helper class to handle requests to the UserSession scenario table.
    """
    def __init__(self):
        self.last_retrieved_user_session = None
        dynamodb = boto3.resource('dynamodb')
        self.table = dynamodb.Table(TABLE_USER_SESSION)

    def getUserSession(self, user_id, refresh=True):
        """Retrieves user session DynamoDB object.

        :param str user_id: Line user ID
        :param bool refresh: Optional agurment. If False, returns the last retrieved,
        cached user session data. If True, retrieves the up-to-date user session from the database
        :returns dict: User session data
        """
        if not self.last_retrieved_user_session or refresh:
            self.last_retrieved_user_session = self.__get_user_session__(user_id)

        return self.last_retrieved_user_session

    # Saves the preferred language for the user
    def setUserLanguage(self, user_id, lang_code=""):
        """Sets the language code for the user.

        :param str user_id: Line user ID
        :param str lang_code: ISO 639-1 Language Code
        :returns bool: True if successful, False if not
        """
        return self.__set_user_language__(user_id, lang_code)

    def getUserLanguage(self, user_id):
        """Gets ISO 639-1 Language Code for user.

        :param str user_id: Line user ID

        :returns The ISO 639-1 Language Code
        """
        return self.__get_user_language__(user_id)

    def setSpecialFlow(self, user_id, flow_id):
        """Sets the current active flow for the user.

        :param str user_id: Line user ID
        :param str flow_id: The special flow identifier. Can be None or an empty string.
        :returns bool: True if the DynamoDB request is successful, False if not
        """
        return self.__set_active_special_flow__(user_id, flow_id)

    def getSpecialFlow(self, user_id):
        """Get currently active special flow for the given user, if any.

        :param str user_id: Line user ID

        :returns str: The flow ID. Can be None or an empty string
        """
        return self.__get_active_special_flow__(user_id)

    def getDamageReport(self, user_id):
        """Gets damage report data for an user ID.

        :param str user_id: Line user ID
        :returns dict: Damage report data
        """
        user_session = self.__get_user_session__(user_id)
        damage_report = None

        if user_session:
            damage_report = user_session.get("damageReport")

        return damage_report

    def setLastMessageId(self, user_id, msg_id):
        """Sets the ID of the last message sent to the user by the chatbot.

        :param str user_id: Line user ID
        :param str msg_id: ID of the sent message
        :returns bool: True if successful, False if not
        """
        return self.__set_last_message_id__(user_id, msg_id)

    def getLastMessageId(self, user_id):
        """Gets the ID of the last sent message.

        :param str user_id: Line user ID
        :returns str: ID of the last sent message
        """
        user_session = self.__get_user_session__(user_id)

        last_msg_id = None
        if user_session:
            last_msg_id = user_session.get("lastMessageId")

        return last_msg_id

    def setDamageReportCategory(self, user_id, category):
        """Sets the category for the damage report special scenario.

        :param str user_id: Line user ID
        :param str category: The category to set, which depends on the scenario.
        :returns bool: True if successful, False if not
        """
        return self.__set_damage_report_category__(user_id, category)

    def startDamageReportVersion(self, user_id, versionId):
        return self.__reset_damage_report_version__(user_id, versionId)

    def setDamageReportVersionId(self, user_id, versionId):
        return self.__set_damage_report_version__(user_id, 'versionId', versionId, 'versionId')

    def setDamageReportVersion(self, user_id, key, val, label):
        return self.__set_damage_report_version__(user_id, key, val, label)
    
    def resetDamageReportVersion(self, user_id):
        return self.__reset_damage_report_version__(user_id)

    def setDamageReportVersionImage(self, user_id, key, val, label):
        return self.__set_damage_report_version_image__(user_id, key, val, label)

    def setDamageReportVersionLocation(self, user_id, key, val, label):
        return self.__set_damage_report_version_location__(user_id, key, val, label)

    def setDamageReportLocation(self, user_id, location):
        """Sets the location address for the damage report special scenario.

        :param str user_id: Line user ID
        :param str location: The location address to set
        :returns bool: True if successful, False if not
        """
        return self.__set_damage_report_location__(user_id, location)

    def setDamageReportImage(self, user_id, img_id, img_url):
        """Sets the image url for the damage report special scenario.

        :param str user_id: Line user ID
        :param str img_id: ID (dict key) of the image
        :param str img_url: HTTPS URL pointing to the image file
        :returns bool: True if successful, False if not
        """
        return self.__set_damage_report_image__(user_id, img_id, img_url)

    def setDamageReportUserFeedback(self, user_id, user_feedback):
        """Sets the user feedback for the damage report special scenario.

        :param str user_id: Line user ID
        :param str user_feedback: The feedback text inputted by the user
        :returns bool: True if successful, False if not
        """
        return self.__set_damage_report_user_feedback__(user_id, user_feedback)

    def updateDamageReportUserWorkflow(self, user_id, workflowItem, reset = False):
        """Sets the workflows for the damage report special scenario.

        :param str user_id: Line user ID
        :param str workflowItem: {scenarioId, event_source}
        :returns bool: True if successful, False if not
        """
        return self.__set_damage_report_user_workflow_item__(user_id, workflowItem, reset)

    #####################
    ### LOCATION ###
    #####################

    def getLocation(self, user_id):
        """Retrieves Location data from user session, if it exists

        :param user_id: ID of the LINE user
        :param environemt: The chatbot environemnt, production or sandbox
        """
        user_session = self.__get_user_session__(user_id)

        location = None

        if user_session and user_session.get('Location'):
            location = user_session.get('Location')

        return location

    def setStartLocation(self, userId, params=None):
        self.delLocaton(userId)
        return self.__set_start_location__(userId, params)

    def setLocation(self, userId, location):
        return self.__set_location__(userId, location)

    def delLocaton(self, userId):
        return self.__delete_location_map__(userId)

    #######################
    ### Private methods ###
    #######################

    def __get_user_session__(self, user_id):
        try:
            response = self.table.get_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                }
            )

            item = response.get('Item')
            if item:
                return replace_decimals(item)

            # If session does not exist, create an empty item
            item = {
                "userId" : user_id,
                "environment": SCENARIO_ENVIRONMENT
            }
            self.table.put_item(
                Item=item
            )

            return item
        except ClientError:
            # In case of errors return None
            return None

    def __set_user_language__(self, user_id, language):
        try:
            self.table.update_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                },
                UpdateExpression="set #lang = :langCode",
                ExpressionAttributeValues={
                    ':langCode': language
                },
                ExpressionAttributeNames={
                    '#lang': 'language'
                },
                ReturnValues="UPDATED_NEW"
            )
        except ClientError:
            return False

        return True

    def __get_user_language__(self, user_id):
        try:
            response = self.table.get_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                }
            )

            item = response.get('Item')

            if item and item.get("language"):
                return item.get("language")

            self.__set_user_language__(user_id, "")
            return ""

        except ClientError as err:
            # In case of errors fallback to Japanese
            print("[user_session] ClientError:", err.response['Error']['Message'])
            return ""

    def __set_active_special_flow__(self, user_id, flow_id):
        try:
            self.table.update_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                },
                UpdateExpression="set #flow = :flowValue",
                ExpressionAttributeValues={
                    ':flowValue': flow_id
                },
                ExpressionAttributeNames={
                    '#flow': 'specialFlow'
                },
                ReturnValues="UPDATED_NEW"
            )
        except ClientError:
            return False

        return True

    def __get_active_special_flow__(self, user_id):
        try:
            response = self.table.get_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                }
            )

            item = response.get('Item')

            flow = ""

            if item:
                flow = item.get("specialFlow")

            return flow

        except ClientError:
            # In case of errors return empty
            return ""

    def __set_last_message_id__(self, user_id, message_id):
        try:
            self.table.update_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                },
                UpdateExpression="set #lastMsgId = :msgId",
                ExpressionAttributeValues={
                    ':msgId': message_id
                },
                ExpressionAttributeNames={
                    '#lastMsgId': 'lastMessageId'
                },
                ReturnValues="UPDATED_NEW"
            )
        except ClientError:
            return False

        return True

    def __update_damage_report_map__(self, user_id, damage_report_content):
        # Try to create "damageReport" map if not exists
        try:
            self.table.update_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                },
                UpdateExpression="set #dmgRep = :dmgRepValue",
                ExpressionAttributeValues={
                    ':dmgRepValue': damage_report_content
                },
                ExpressionAttributeNames={
                    '#dmgRep': 'damageReport'
                },
                ConditionExpression="attribute_not_exists(#dmgRep)",
                ReturnValues="UPDATED_NEW"
            )
        except ClientError as client_error:
            if client_error.response['Error']['Code'] == 'ConditionalCheckFailedException':
                # The "damageReport" attribute exists,
                # so merge the current values with what was passed to the function
                damage_report = self.getDamageReport(user_id)
                damage_report.update(damage_report_content) # Merge dictionaries

                try:
                    self.table.update_item(
                        Key={
                            'userId': user_id,
                            'environment': SCENARIO_ENVIRONMENT
                        },
                        UpdateExpression="set #dmgRep = :dmgRepValue",
                        ExpressionAttributeValues={
                            ':dmgRepValue': damage_report
                        },
                        ExpressionAttributeNames={
                            '#dmgRep': 'damageReport'
                        },
                        ReturnValues="UPDATED_NEW"
                    )
                except ClientError:
                    return False
            else:
                return False

        return True

    def __set_damage_report_status__(self, user_id, status):
        # If setting status to false, delete current data (category, images, etc)
        if status:
            update_expression = "set #dmgRep.#status = :statusValue"
            expression_values = {
                ':statusValue': True
            }
            expression_names = {
                '#dmgRep': 'damageReport',
                '#status': 'status'
            }
        else:
            update_expression = "set #dmgRep.#status = :statusValue " \
                               + "remove #dmgRep.#category, #dmgRep.#images, " \
                               + "#dmgRep.#location, #dmgRep.#userFeedback"
            expression_values = {
                ':statusValue': False
            }
            expression_names = {
                '#dmgRep': 'damageReport',
                '#status': 'status',
                '#category': 'category',
                '#images': 'images',
                '#location': 'location',
                '#userFeedback': 'userFeedback'
            }

        try:
            self.table.update_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                },
                UpdateExpression=update_expression,
                ExpressionAttributeValues=expression_values,
                ExpressionAttributeNames=expression_names,
                ReturnValues="UPDATED_NEW"
            )
        except ClientError as client_error:
            if client_error.response['Error']['Code'] == 'ValidationException':
                dmg_rep = {
                    "status": status
                }
                return self.__update_damage_report_map__(user_id, dmg_rep)

            return False

        return True

    def __set_damage_report_category__(self, user_id, category):
        try:
            self.table.update_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                },
                UpdateExpression="set #dmgRep.#category = :categoryValue",
                ExpressionAttributeValues={
                    ':categoryValue': category
                },
                ExpressionAttributeNames={
                    '#dmgRep': 'damageReport',
                    '#category': 'category'
                },
                ReturnValues="UPDATED_NEW"
            )
        except ClientError as client_error:
            if client_error.response['Error']['Code'] == 'ValidationException':
                dmg_rep = {
                    "category": category
                }
                return self.__update_damage_report_map__(user_id, dmg_rep)

            return False

        return True
    
    def __reset_damage_report_version__(self, user_id, versionId = None):
        valueDRVersion = {}
        if versionId: valueDRVersion['versionId'] = versionId
        try:
            self.table.update_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                },
                UpdateExpression="set #dmgRepVersion = :val",
                ExpressionAttributeValues={
                    ':val': valueDRVersion
                },
                ExpressionAttributeNames={
                    '#dmgRepVersion': 'damageReportVersion'
                },
                ReturnValues="UPDATED_NEW"
            )
        except ClientError as client_error:
            return False

        return True
    
    def __set_damage_report_version__(self, user_id, key, value, label):
        field_label_mappings = '__field_label_mappings__'
        current_session = self.__get_user_session__(user_id)
        damageReportVersion: dict = current_session.get('damageReportVersion', {})
        current_dr_list_label_mappings: set = set(damageReportVersion.get(field_label_mappings, []))
        labelDataKey = f"{key}___{label}"
        LIST_META_FIELD = ['categoryEmail', 'category', 'subject', 'feedback', 'location', 'images']

        if value and key not in LIST_META_FIELD and labelDataKey not in current_dr_list_label_mappings:
            current_dr_list_label_mappings.add(labelDataKey)
            
        try:
            self.table.update_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                },
                UpdateExpression="set #dmgRepVersion.#key = :val, " \
                                + "#dmgRepVersion.#mappingField = :labelData",
                ExpressionAttributeValues={
                    ':val': value,
                    ':labelData': list(current_dr_list_label_mappings)
                },
                ExpressionAttributeNames={
                    '#dmgRepVersion': 'damageReportVersion',
                    '#key': key,
                    '#mappingField': field_label_mappings
                },
                ReturnValues="UPDATED_NEW"
            )
        except ClientError as client_error:
            return False

        return True
    
    def __set_damage_report_version_image__(self, user_id, key, value: str, label):
        field_label_mappings = '__field_label_mappings__'
        current_session = self.__get_user_session__(user_id)
        damageReportVersion: dict = current_session.get('damageReportVersion', {})
        current_dr_version_image: list = damageReportVersion.get(key, [])
        current_dr_list_image_key: set = set(damageReportVersion.get('__images_key__', []))
        current_dr_list_label_mappings: set = set(damageReportVersion.get(field_label_mappings, []))

        labelDataKey = f"{key}___{label}"

        if value and labelDataKey in current_dr_list_label_mappings:
            # key and field the same as previous => re capture images => replace value
            if current_dr_version_image:
                current_dr_version_image[-1] = value
        elif value:
            current_dr_version_image.append(value)
            current_dr_list_label_mappings.add(labelDataKey)
            current_dr_list_image_key.add(key)

        try:
            self.table.update_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                },
                UpdateExpression="set #dmgRepVersion.#key = :val, #dmgRepVersion.#key2 = :val2, " \
                                + "#dmgRepVersion.#mappingField = :labelData",
                ExpressionAttributeValues={
                    ':val': current_dr_version_image,
                    ':val2': list(current_dr_list_image_key),
                    ':labelData': list(current_dr_list_label_mappings)
                },
                ExpressionAttributeNames={
                    '#dmgRepVersion': 'damageReportVersion',
                    '#key': key,
                    '#key2': '__images_key__',
                    '#mappingField': field_label_mappings
                },
                ReturnValues="UPDATED_NEW"
            )
        except ClientError as client_error:
            return False

        return True

    def __set_damage_report_version_location__(self, user_id, key, value, label):
        field_label_mappings = '__field_label_mappings__'
        current_session = self.__get_user_session__(user_id)
        damageReportVersion: dict = current_session.get('damageReportVersion', {})
        current_dr_list_location_key: set = set(damageReportVersion.get('__location_key__', []))

        if value:
            current_dr_list_location_key.add(key)
        try:
            self.table.update_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                },
                UpdateExpression="set #dmgRepVersion.#key = :val, #dmgRepVersion.#key2 = :val2, " \
                                + "#dmgRepVersion.#mappingField = list_append(if_not_exists(#dmgRepVersion.#mappingField, :emptyArray), :label)",
                ExpressionAttributeValues={
                    ':val': value,
                    ':val2': list(current_dr_list_location_key),
                    ':emptyArray': [],
                    ':label': [f"{key}___{label}"]
                },
                ExpressionAttributeNames={
                    '#dmgRepVersion': 'damageReportVersion',
                    '#key': key,
                    '#key2': '__location_key__',
                    '#mappingField': field_label_mappings
                },
                ReturnValues="UPDATED_NEW"
            )
        except ClientError as client_error:
            return False

        return True

    def __set_damage_report_location__(self, user_id, location):
        try:
            self.table.update_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                },
                UpdateExpression="set #dmgRep.#loc = :locValue",
                ExpressionAttributeValues={
                    ':locValue': location
                },
                ExpressionAttributeNames={
                    '#dmgRep': 'damageReport',
                    '#loc': 'location'
                },
                ReturnValues="UPDATED_NEW"
            )
        except ClientError as client_error:
            if client_error.response['Error']['Code'] == 'ValidationException':
                dmg_rep = {
                    "location": location
                }
                return self.__update_damage_report_map__(user_id, dmg_rep)

            return False

        return True

    def __set_damage_report_image__(self, user_id, img_id, img_url):
        try:
            self.table.update_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                },
                UpdateExpression="set #dmgRep.#img.#imgId = :imgUrl",
                ExpressionAttributeValues={
                    ':imgUrl': img_url
                },
                ExpressionAttributeNames={
                    '#dmgRep': 'damageReport',
                    '#img': 'images',
                    '#imgId': img_id
                },
                ReturnValues="UPDATED_NEW"
            )
        except ClientError as client_error:
            if client_error.response['Error']['Code'] == 'ValidationException':
                dmg_rep = {
                    "images": {
                        img_id: img_url
                    }
                }
                return self.__update_damage_report_map__(user_id, dmg_rep)

            return False

        return True

    def __remove_damage_report_image__(self, user_id, img_id):
        try:
            self.table.update_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                },
                UpdateExpression="remove #dmgRep.#img.#imgId",
                ExpressionAttributeNames={
                    '#dmgRep': 'damageReport',
                    '#img': 'images',
                    '#imgId': img_id
                },
                ReturnValues="UPDATED_NEW"
            )
        except ClientError:
            return False

        return True

    def __set_damage_report_user_feedback__(self, user_id, user_feedback):
        try:
            self.table.update_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                },
                UpdateExpression="set #dmgRep.#feedback = :userFeedback",
                ExpressionAttributeValues={
                    ':userFeedback': user_feedback
                },
                ExpressionAttributeNames={
                    '#dmgRep': 'damageReport',
                    '#feedback': 'userFeedback'
                },
                ReturnValues="UPDATED_NEW"
            )
        except ClientError as client_error:
            if client_error.response['Error']['Code'] == 'ValidationException':
                dmg_rep = {
                    "userFeedback": user_feedback
                }
                return self.__update_damage_report_map__(user_id, dmg_rep)

            return False

        return True

    def __set_damage_report_user_workflow_item__(self, user_id, workflowItem, reset = False):
        workflowsData = []

        if not reset:
            damage_report = self.getDamageReport(user_id)
            workflowsData = damage_report.get("workflows", [])
            workflowsData.append(workflowItem)

        try:
            self.table.update_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                },
                UpdateExpression="set #dmgRep.#workflows = :workflows",
                ExpressionAttributeValues={
                    ':workflows': workflowsData
                },
                ExpressionAttributeNames={
                    '#dmgRep': 'damageReport',
                    '#workflows': 'workflows'
                },
                ReturnValues="UPDATED_NEW"
            )
        except ClientError as client_error:
            if client_error.response['Error']['Code'] == 'ValidationException':
                dmg_rep = {
                    "workflows": workflowsData
                }
                return self.__update_damage_report_map__(user_id, dmg_rep)

            return False

        return True

    def __set_damage_report_extra_user_workflow_item__(self, user_id, workflowItem, reset = False):
        workflowsData = []

        if not reset:
            current_session = self.__get_user_session__(user_id)
            damageReportExtra: dict = current_session.get('damageReportExtra', {})
            workflowsData = damageReportExtra.get("workflows", [])
            workflowsData.append(workflowItem)

        try:
            self.table.update_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                },
                UpdateExpression="set #dmgRep.#workflows = :workflows",
                ExpressionAttributeValues={
                    ':workflows': workflowsData
                },
                ExpressionAttributeNames={
                    '#dmgRep': 'damageReportExtra',
                    '#workflows': 'workflows'
                },
                ReturnValues="UPDATED_NEW"
            )
        except ClientError as client_error:
            return False

        return True

    def __set_start_location__(self, user_id, params=None):
        dynamodb = boto3.resource('dynamodb')
        table = dynamodb.Table(TABLE_USER_SESSION)

        try:
            if params:
                table.update_item(
                    Key={
                        'userId': user_id,
                        'environment': SCENARIO_ENVIRONMENT
                    },
                    UpdateExpression="set #loca.#location = :locvalue, "
                                     "#loca.#csv = :csvfilepath, "
                                     "#loca.#json = :jsonf, "
                                     "#loca.#fil = :filter, "
                                     "#loca.#nextact = :nextact ",
                    ExpressionAttributeValues={
                        ':locvalue': {},
                        ':csvfilepath': params['csvfilepath'],
                        ':jsonf':  params['jsonfile'],
                        ':filter':  params['filter'],
                        ':nextact':  params['nextAction'],
                    },
                    ExpressionAttributeNames={
                        '#loca': 'Location',
                        '#location': 'location',
                        '#csv': 'csvfile',
                        '#json': 'jsonfile',
                        '#fil': 'filter',
                        '#nextact': 'nextaction',
                    },
                    ReturnValues="UPDATED_NEW"
                )
            else:
                table.update_item(
                    Key={
                        'userId': user_id,
                        'environment': SCENARIO_ENVIRONMENT
                    },
                    UpdateExpression="set #loca.#location = :locValue ",
                    ExpressionAttributeValues={
                        ':locValue': ''
                    },
                    ExpressionAttributeNames={
                        '#loca': 'Location',
                        '#location': 'location'
                    },
                    ReturnValues="UPDATED_NEW"
                )

        except ClientError as client_error:
            if client_error.response['Error']['Code'] == 'ValidationException':
                loca = {
                    'location': {},
                    "csvfile": params['csvfilepath'],
                    "jsonfile": params['jsonfile'],
                    "filter": params['filter'],
                    "nextaction": params['nextAction']
                }
                return self.__update_location_map__(user_id, loca)

            return False
        return True

    def __set_location__(self, user_id, location):
        dynamodb = boto3.resource('dynamodb')
        table = dynamodb.Table(TABLE_USER_SESSION)

        try:
            table.update_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                },
                UpdateExpression="set #loca.#loc = :locValue",
                ExpressionAttributeValues={
                    ':locValue': location
                },
                ExpressionAttributeNames={
                    '#loca': 'Location',
                    '#loc': 'location'
                },
                ReturnValues="UPDATED_NEW"
            )
        except ClientError as client_error:
            if client_error.response['Error']['Code'] == 'ValidationException':
                loca = {
                    "location": location
                }
                return self.__update_location_map__(user_id, loca)

            return False

        return True

    def __update_location_map__(self, user_id, location_content):
        dynamodb = boto3.resource('dynamodb')
        table = dynamodb.Table(TABLE_USER_SESSION)

        try:
            table.update_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                },
                UpdateExpression="set #loca = :locValue",
                ExpressionAttributeValues={
                    ':locValue': location_content
                },
                ExpressionAttributeNames={
                    '#loca': 'Location'
                },
                ConditionExpression="attribute_not_exists(#loca)",
                ReturnValues="UPDATED_NEW"
            )
        except ClientError as client_error:
            if client_error.response['Error']['Code']:
                location_data = self.getLocation(user_id)
                location_data.update(location_content) # Merge dictionaries

                try:
                    table.update_item(
                        Key={
                            'userId': user_id,
                            'environment': SCENARIO_ENVIRONMENT
                        },
                        UpdateExpression="set #loca = :locValue",
                        ExpressionAttributeValues={
                            ':locaValue': location_data
                        },
                        ExpressionAttributeNames={
                            '#loca': 'Location'
                        },
                        ReturnValues="UPDATED_NEW"
                    )
                except ClientError:
                    return False
            else:
                return False

        return True

    @staticmethod
    def __delete_location_map__(user_id):
        dynamodb = boto3.resource('dynamodb')
        table = dynamodb.Table(TABLE_USER_SESSION)

        try:
            table.update_item(
                Key={
                    'userId': user_id,
                    'environment': SCENARIO_ENVIRONMENT
                },
                UpdateExpression="remove #data",
                ExpressionAttributeNames={
                    '#data': 'Location'
                },
                ReturnValues="UPDATED_NEW"
            )
        except ClientError:
            return False

        return True

USER_SESSION_HELPER = UserSession()
