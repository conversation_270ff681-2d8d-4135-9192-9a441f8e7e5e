"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os
import logging
import re
import json
import traceback

from scenario.models import (
    text_message, button_template, sticker_message, imagemap_message,
    carousel_template, flex_message, image_message, audio_message,
    video_message, confirm_template, location_message
)

from scenario.util import DecimalDecoder, replace_decimals, nested_replace
from scenario.location.location import LocationManager
from scenario.chat_bot_api import ChatBotApi
from scenario.user_session import USER_SESSION_HELPER as user_session
import scenario.spot_scenario.constants.spot_scenario_constants as SpotScenarioConstants
from spot.services.chat_spot_list_service import ChatSpotListService
from spot.services.spot_group_service import SpotGroupService
from spot.services.chat_spot_location_service import ChatSpotLocationService

# Logger setup
logger = logging.getLogger("scenario-spot_scenario_manager")
logger.setLevel(logging.INFO)
class SpotScenarioException(Exception):
    """Custom exception class for spot module.
    """
class SpotScenarioManager():
    """Helper class for managing chatbot scenario spot.
    """
    def __init__(self, line_bot_api):

        self.line_bot_api = line_bot_api

        # LINE API message generator
        self.message_generator = {
            "text": text_message.generate,
            "sticker": sticker_message.generate,
            "buttons": button_template.generate,
            "imagemap": imagemap_message.generate,
            "carousel": carousel_template.generate,
            "bubbleFlex": flex_message.generate_bubble,
            "image": image_message.generate,
            "audio": audio_message.generate,
            "video": video_message.generate,
            "confirm": confirm_template.generate,
            "location": location_message.generate
        }

        # User session helper
        self.user_session = user_session

        # Location
        self.location = LocationManager(line_bot_api, self.user_session)

        # LINE Messaging API Event source
        self.event_source = None

        # Language code for scenario (default is JP, which is empty)
        self.language_code = ""

        # Save scenario object (scenario + version)
        self.scenario_version_object = None

        # Get scenarioId and version
        self.settings = {}
        self.scenario = "default"

        # ChatBotApi
        self.chatbot_api = ChatBotApi()

        # SpotScenario Handling Functions
        self.spot_handler = {
            "spot_list": self.__handle__spot__list__event,
            "spot_tag": self.__handle__spot__tag__event,
            "spot_location": self.__handle__start__location__event,
        }

    def handle_line_webhook_event(self, event, event_type):
        """ Handler method to process linehook events for Sightseeing Spots
        Pulls out the Sightseeing Spot trigger from the postback.data of the LINE webhook event
        Matches that trigger string to a list of different Sightseeing Spot functions in spot_scenario_constants
        Calls the matched Sightseeing Spot API
        Sends the results back to the chatbot

        Args:
            event (object): The LINE webhook event object.
            event_type (event):  linebot.models.Event

        Returns:
            dict:  A list of messages to be sent by the chatbot. 
            Empty or None if there are no messages to be sent.
            
        """
        reply_messages = []

        # Store event source data
        self.event_source = event.source

        # Refresh user session
        self.user_session.getUserSession(
            self.event_source.user_id
        )

        # Get the sightseeing spot trigger and the accompanying constants
        spot_event_data = None
        if event_type == 'postback':
            spot_event_data = event.postback.data
        logger.info(f'spot_scenario_manager.handle_line_webhook_event spot_event_data: {repr(spot_event_data)}')

        spot_event = None
        if spot_event_data:
            for value in SpotScenarioConstants.SPOT_EVENT_DICTIONARY.values():
                if re.search(value.get("event_key"), spot_event_data):
                    spot_event = value
                    break
        logger.info(f'spot_scenario_manager.handle_line_webhook_event spot_event: {repr(spot_event)}')

        if spot_event and spot_event.get("event_type") in self.spot_handler:
            logger.info(f'self.spot_handler[spot_event.get("event_type")]: {repr(self.spot_handler[spot_event.get("event_type")])}')
            reply_messages = self.spot_handler[spot_event.get("event_type")](spot_event, spot_event_data)

        if event_type == 'location_message':
            reply_messages = self.__handle__location__search__event(SpotScenarioConstants.SPOT_EVENT_DICTIONARY.get('SPOT_LOCATION'), event.message)

        logger.info(f'spot_scenario_manager.handle_line_webhook_event reply_messages: {repr(reply_messages)}')
        return reply_messages

    def __handle__spot__list__event(self, event_constants, event_data):
            """
                Function to handle processing of a spotList event
                event_data should be: {spotGroupId}#spotList#{spotListId}
                @route('/spotLists/<spotGroupId>/<spotListId>', methods=['GET'])
            """
            reply_messages = []

            event_data_items = event_data.split('#')
            # spot_api_uri = ('' + str(event_constants.get('api_uri'))).replace(':spotGroupId', event_data_items[0])\
            #     .replace(':spotListId', event_data_items[2])
            spotGroupId = event_data_items[0] if len(event_data_items) > 0 else None
            spotListId = event_data_items[2] if len(event_data_items) > 2 else None

            # get spot list to send to chatbot
            try:
                # http_client_service = HttpClientService(SPOT_API_GATEWAY_URL)
                # response = http_client_service.make_get_request(spot_api_uri)
                # response = http_client_service.make_get_request(spot_api_uri)

                # if isinstance(response.get('data'), list):
                data = ChatSpotListService().get_spot_list_by_spot_list_id_for_line(spotGroupId, spotListId)
                messages = map(lambda item: {'params': item}, data)
                reply_messages = flex_message.generate_carousel(list(messages), event_constants.get('display_name'))
            except Exception as e:
                logging.exception(f"[ERROR] Error occured processing spot list event: {e}")

            return reply_messages

    # Warning: The function __handle__spot__tag__event is not in use.
    def __handle__spot__tag__event(self, event_constants, event_data):
        """
            Function to handle processing of a spotTag event
            event_data should be: {spotGroupId}#tag#{tagName}#{pageNumber}
            #{pageNumber} is optional and may not be in the event_data
        """
        reply_messages = []

        event_data_items = event_data.split('#')
        # spot_api_uri = ('' + str(event_constants.get('api_uri'))).replace(':spotGroupId', event_data_items[0])
        spotGroupId = event_data_items[0] if len(event_data_items) > 0 else None
        spotListId = event_data_items[2] if len(event_data_items) > 2 else None
        page = 1
        if len(event_data_items) >= 4 and event_data_items[3] and\
            event_data_items[3].isnumeric() and not int(event_data_items[3]) == 0:

            page = int(event_data_items[3])

        #call the Sightseeing Spot API to get the messages to send to chatbot
        try:
            # http_client_service = HttpClientService(SPOT_API_GATEWAY_URL)
            # response = http_client_service.make_get_request(spot_api_uri, {
            #     'tag': event_data_items[2],
            #     'page': page
            # })
            response = ChatSpotListService().get_spot_list_by_spot_list_id_for_line(spotGroupId, spotListId)
            logger.info(f'__handle__spot__tag__event response: {repr(response)}')

            if isinstance(response, list):
                messages = list(map(lambda item: { 'params': item }, response))
                if response.get('displayContinueText'):
                    continue_json = self.generate_continue_bubble_json(response.get('displayContinueText'),\
                        '#'.join(event_data_items[0:3]) + '#' + str(page + 1), messages[0]['params'].get('size'))
                    if continue_json:
                        messages.append({ 'params': continue_json})
                reply_messages = flex_message.generate_carousel(messages, event_constants.get('display_name'))
        except Exception as e:
            logging.error('[ERROR] Error occured processing spot list event: {}'.format(e))
            logging.info(traceback.format_exc())

        return reply_messages 

    def __handle__start__location__event(self, event_constants, event_data):
            """
                Function to handle processing start of search by location event
                event_data should be: {spotGroupId}#location
            """
            reply_messages = []

            event_data_items = event_data.split('#')
            # spot_api_uri = ('' + str(event_constants.get('api_uri'))).replace(':spotGroupId', event_data_items[0])
            spotGroupId = event_data_items[0] if len(event_data_items) > 0 else None

            #call the Sightseeing Spot API to get search by location message text
            try:
                # http_client_service = HttpClientService(SPOT_API_GATEWAY_URL)
                # response = http_client_service.make_get_request(spot_api_uri)
                response = SpotGroupService().get_spot_group_by_id_to_dto(spotGroupId)
                logger.info(f'__handle__start__location__event response: {repr(response)}')

                # if response and response.get('scenarioTriggerSettings').get('locationSearchText'):
                if response is not None:
                    # location_message = self.generate_location_message(response.get('scenarioTriggerSettings').get('locationSearchText'),\
                    #     event_constants.get('display_name'))
                    location_message = self.generate_location_message(response.get('scenarioTriggerSettings', {}).get('locationSearchText'),\
                        event_constants.get('display_name'))
                    if location_message:
                        reply_messages.append(location_message)

                        self.user_session.setLocation(self.event_source.user_id, event_data)
            except Exception as e:
                logging.error('[ERROR] Error occured while starting the spot search by location flow: {}'.format(e))
                logging.info(traceback.format_exc())

            return reply_messages
    
    def __handle__location__search__event(self, event_constants, event_data):
        """
            Function to handle processing of search by location event
            event_data should be dictionary containing latitude and longitude
        """
        reply_messages = []

        #call the Sightseeing Spot API to get list of spot flex messages or not found flex message
        try:
            user_location_info = self.user_session.getLocation(self.event_source.user_id)
            spot_event_items = user_location_info.get('location').split('#')
            # spot_api_uri = ('' + str(event_constants.get('search_api_uri'))).replace(':spotGroupId', spot_event_items[0])
            spotGroupId = spot_event_items[0] if len(spot_event_items) > 0 else None
            # location = pgh.encode(event_data.latitude, event_data.longitude)

            # http_client_service = HttpClientService(SPOT_API_GATEWAY_URL)
            # response = http_client_service.make_get_request(spot_api_uri, {
            #     'location': pgh.encode(event_data.latitude, event_data.longitude)
            # })
            # response = ChatSpotLocationService().get_spots_by_location_for_line(spotGroupId, location)
            response = ChatSpotLocationService().get_spots_by_location_for_line(spotGroupId, event_data.latitude, event_data.longitude)
            logger.info(f'__handle__location__search__event response: {repr(response)}')

            if isinstance(response, list):
                messages = map(lambda item: { 'params': item }, response)
                reply_messages = flex_message.generate_carousel(list(messages), event_constants.get('display_name'))

                self.user_session.delLocaton(self.event_source.user_id)
            elif isinstance(response, str):
                location_message = self.generate_location_message(response,\
                    event_constants.get('display_name'))
                if location_message:
                    reply_messages.append(location_message)
        except Exception as e:
            logging.error('[ERROR] Error occured while starting the spot search by location flow: {}'.format(e))
            logging.info(traceback.format_exc())

        return reply_messages

    def generate_location_message(self, text, displayName):
        dir_path = os.path.dirname(os.path.realpath(__file__))
        location_json = os.path.join(dir_path, 'constants/location_message.json')

        data = None
        with open(location_json, 'r') as location_file:
            data = location_file.read()

        try:
            json_obj = json.loads(data, cls=DecimalDecoder)
        except json.JSONDecodeError as js_error:
            logging.error('[ERROR] Unable to read location message json: ', js_error)
            return None

        json_obj = nested_replace(json_obj, '{文言}', text)
        return flex_message.generate_bubble(replace_decimals(json_obj), displayName)

    def generate_continue_bubble_json(self, text, nextData, size = None):
        dir_path = os.path.dirname(os.path.realpath(__file__))
        continue_json = os.path.join(dir_path, 'constants/continue_message.json')

        data = None
        with open(continue_json, 'r') as continue_file:
            data = continue_file.read()

        try:
            json_obj = json.loads(data, cls=DecimalDecoder)
        except json.JSONDecodeError as js_error:
            logging.error('[ERROR] Unable to read continue message json: ', js_error)
            return None
        json_obj = nested_replace(json_obj, '{文言}', text)
        json_obj = nested_replace(json_obj, '{次へ}', nextData)
        if size:
            json_obj['size'] = size

        return replace_decimals(json_obj)