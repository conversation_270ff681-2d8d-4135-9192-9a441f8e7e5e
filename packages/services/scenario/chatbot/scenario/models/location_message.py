"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
from linebot.models import LocationSendMessage

def generate(data):
    """Generate a LINE API Location message to be sent by the chatbot.

    :param dict data: Image message data
    :returns linebot.models.LocationSendMessage: The Location message to be sent
    """

    original_url = data.get("originalContentUrl")
    preview_url = data.get("previewImageUrl")

    return LocationSendMessage(
        title=data.get('title'),
        address=data.get('address'),
        latitude=data.get('latitude'),
        longitude=data.get('longitude')
    )
