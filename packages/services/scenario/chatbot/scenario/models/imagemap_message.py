"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
from linebot.models import (
    ImagemapSendMessage, BaseSize, ImagemapArea, URIImagemapAction, MessageImagemapAction
)

def __generate_message_action__(action):
    return MessageImagemapAction(
        text=action.get("text"),
        area=ImagemapArea(
            x=action.get("x"),
            y=action.get("y"),
            width=action.get("width"),
            height=action.get("height")
        )
    )

def __generate_uri_action__(action):
    return URIImagemapAction(
        link_uri=action.get("uri"),
        area=ImagemapArea(
            x=action.get("x"),
            y=action.get("y"),
            width=action.get("width"),
            height=action.get("height")
        )
    )

def __generate_web_app_action__(action):
    return URIImagemapAction(
        link_uri=action.get("webapp"),
        area=ImagemapArea(
            x=action.get("x"),
            y=action.get("y"),
            width=action.get("width"),
            height=action.get("height")
        )
    )

imagemap_action_generator = {
    "message": __generate_message_action__,
    "uri": __generate_uri_action__,
    "webapp": __generate_web_app_action__
}

def generate(data, name_lbd="イメージマップ"):
    """Generates a LINE API Imagemap message to be sent by the chatbot.

    :param dict data: Imagemap message data
    :param name_lbd: The name of the item on Line bot designer.\
    Used as the 「alt_text」property of the message.
    :returns linebot.models.ImagemapSendMessage: The Imagemap message to be sent
    """
    # Create actions list
    action_list = []

    for i in range(int(data.get("actionCount"))):
        action_data = data.get("action." + str(i))
        if action_data:
            action_list.append(imagemap_action_generator[action_data.get("type")](action_data))

    # Return imagemap message
    base_height = data.get("baseHeight")

    if not base_height:
        base_height = data.get("autoDetectedBaseHeight")

    base_width = data.get("baseWidth")

    imagemap_message = ImagemapSendMessage(
        base_url=data.get("baseUrl"),
        alt_text=name_lbd,
        base_size=BaseSize(height=base_height, width=base_width),
        actions=action_list
    )

    return imagemap_message
