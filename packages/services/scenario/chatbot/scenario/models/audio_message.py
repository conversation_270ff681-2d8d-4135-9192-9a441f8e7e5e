"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
from linebot.models import AudioSendMessage

def generate(data):
    """Generates a LINE API Audio message to be sent by the chatbot.

    :param dict data: Audio message data
    :returns linebot.models.AudioSendMessage: The audio message to be sent
    """
    original_url = data.get("originalContentUrl")

    duration = int(data.get("duration"))

    return AudioSendMessage(
        original_content_url=original_url,
        duration=duration
    )
