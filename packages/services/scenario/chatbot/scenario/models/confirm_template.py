"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
from linebot.models import ConfirmTemplate, TemplateSendMessage

from scenario.models.actions import action_generator

def generate(data, name_lbd="確認テンプレート"):
    """Generates a LINE API ConfirmTemplate message to be sent by the chatbot.

    :param dict data: ConfirmTemplate message data
    :param name_lbd: The name of the item on Line bot designer.\
    Used as the 「alt_text」property of the message.
    :returns linebot.models.TemplateSendMessage: The ConfirmTemplate message to be sent
    """
    # Create actions list
    action_list = []

    action_left = data.get("actionLeft")
    if action_left:
        action_list.append(action_generator[action_left.get("type")](action_left))

    action_right = data.get("actionRight")
    if action_right:
        action_list.append(action_generator[action_right.get("type")](action_right))

    # Create confirm template
    confirm_template = ConfirmTemplate(text=data.get("text"),
                                       actions=action_list)

    # Return template message
    return TemplateSendMessage(
        alt_text=name_lbd,
        template=confirm_template
    )
