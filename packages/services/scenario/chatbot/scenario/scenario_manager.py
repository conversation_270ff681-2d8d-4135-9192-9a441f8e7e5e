"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os
import io
import logging

import boto3
from botocore.exceptions import ClientError
from boto3.dynamodb.conditions import Key
from linebot.exceptions import LineBotApiError
from linebot.models.rich_menu import RichMenuAlias

from scenario.models import (
    text_message, button_template, sticker_message, imagemap_message,
    carousel_template, flex_message, image_message, audio_message,
    video_message, confirm_template, location_message
)

from scenario.special_flows.damage_report.damage_report import DamageReport
from scenario.util import replace_decimals, add_to_list, fuzzy_search
from scenario.special_flows.damage_report import message_id as dmgReportIds
from scenario.location.location import LocationManager
from scenario.chat_bot_api import ChatBotApi
from scenario.user_session import USER_SESSION_HELPER as user_session
from scenario.special_flows import flows as flow_manager
from scenario.special_flows.bosai.message_id import BOSAI_FLOW_START, RETURN_TO_NORMAL_MESSAGE_IDS
from scenario.special_flows.message_id import TEMPLATE_SCENARIO_TALK_IDS
from sendTalkLog import SendTalkLog
from scenario.special_flows.default_template import get_default_template_dict

TABLE_CHATBOT_SCENARIO = os.environ['TABLE_CHATBOT_SCENARIO']
TABLE_CHATBOT_SCENARIO_DATA = os.environ['TABLE_CHATBOT_SCENARIO_DATA']
SCENARIO_ENVIRONMENT = os.environ['SCENARIO_ENVIRONMENT'] # Scenario environment

no_default_rich_menu_error_text = "no default richmenu"

# Instantiate sender
sender = SendTalkLog()

class ScenarioException(Exception):
    """Custom exception class for scenario module.
    """

class ScenarioManager():
    """Helper class for managing chatbot scenario.
    Has many methods to handle webhook events sent by the LINE messaging API.
    Also handles special scenario flow such as damage report (損傷報告).
    """
    def __init__(self, line_bot_api):

        self.line_bot_api = line_bot_api

        # LINE API message generator
        self.message_generator = {
            "text": text_message.generate,
            "sticker": sticker_message.generate,
            "buttons": button_template.generate,
            "imagemap": imagemap_message.generate,
            "carousel": carousel_template.generate,
            "bubbleFlex": flex_message.generate_bubble,
            "carouselFlex": self.__generate_flex_message_helper__,
            "image": image_message.generate,
            "audio": audio_message.generate,
            "video": video_message.generate,
            "confirm": confirm_template.generate,
            "location": location_message.generate
        }

        # User session helper
        self.user_session = user_session

        # Location
        self.location = LocationManager(line_bot_api, self.user_session)

        # LINE Messaging API Event source
        self.event_source = None

        # Language code for scenario (default is JP, which is empty)
        self.language_code = ""

        # Get scenarioId and version
        self.settings = self.__getEnvironmentSettings__()

        # Save scenario object (scenario + version)
        self.scenario_version_object = None

        # Scenario is activeScenario#version
        if self.settings:
            scenario_id = self.settings.get("activeScenarioId")
            version = self.settings.get("envMapping").get(SCENARIO_ENVIRONMENT)
            self.scenario = scenario_id + "#" + version

            scenario_object = self.__getScenario__(scenario_id)
            if scenario_object:
                self.scenario_version_object = scenario_object.get("versions").get(version)
        else:
            self.scenario = "default"

        # Initialize special flows
        flow_manager.initialize_flows(self)

        # ChatBotApi
        self.chatbot_api = ChatBotApi()

        # Text - Message ID mapping (initially set to Japanese)
        self.text_mapping = None
        self.__setupTextMapping__()

        self.main_message_data_id = None

    def handle_line_webhook_event(self, event, event_type):
        """Handler method to process postback webhook events
        received from the LINE messaging API.

        :param event: The LINE webhook event object.
        :type event: linebot.models.Event
        :param str event_type: The type of event received (postack, text_message, etc)

        returns [T <= linebot.models.Message]: A list of messages to be sent by the chatbot.
        Empty or None if there are no messages to be sent.
        """
        reply_messages = []

        # Store event source data
        self.event_source = event.source

        # Refresh user session
        self.user_session.getUserSession(
            self.event_source.user_id
        )

        # Setup scenario language
        self.setupScenarioLanguage()

        # First, check if a special flow will handle the event
        messages = flow_manager.handle_line_webhook_event(event, event_type)

        if not messages:
            # Event was not handled by a special flow
            # Switch to default behaviour
            messages = self._handle_webhook_event_default(event, event_type)

        add_to_list(reply_messages, messages)

        return reply_messages

    def _handle_webhook_event_default(self, event, event_type):
        print('senario_manager _handle_webhook_event_default')
        # Default behaviour only handles postback and text message events
        # If postback, try to find a message with an ID that matches the data
        # If text message, check the user text mapping item in the DB for a match
        message = None
        message_id = None

        if event_type == 'postback':
            message = self.getMessageById(event.postback.data)
        elif event_type == 'text_message':
            message = self.getMessageByText(event.message.text)
            message_id = self.text_mapping.get(event.message.text)

            # In the case of text message, if no mapping is found,
            # do a fuzzy search for trash scenario, if available.
            if not message:
                message = self._trash_separation_fuzzy_search(event.message.text)
                message_id = self.text_mapping.get("TRASH_NOT_FOUND_DEFAULT_MESSAGE", '')

        # If the default handler returned a message, deactivate current special flow
        if message:
            self.user_session.setSpecialFlow(
                event.source.user_id,
                None
            )

        if SCENARIO_ENVIRONMENT == 'production':
            # Instantiate sender
            sender = SendTalkLog()
            # Output talk logs to firehose
            sender.putReplyTalkLogs(
                event,
                message,
                message_id
            )

        return message

    def getMessageById(self, message_id):
        """Retrieves a message from the scenario saved in the database
        to sent as a reply.

        :param str messageId: The message unique identifier
        :returns linebot.models.Message: A list of or single LINE Messaging API message object.
        """
        reply_messages = None

        if "apiCall" in message_id:
            # Convert messageId data to dictionary
            params_list = message_id.split("&")
            param_dict = {}
            for param in params_list:
                key = param.split("=")[0]
                value = param.split("=")[1]
                param_dict[key] = value

            param_dict['user_id'] = self.event_source.user_id
            param_dict['scenario'] = self.scenario

            api_call_id = param_dict.get("apiCall")

            message_data = self.__getScenarioData__(self.scenario, api_call_id)

            message_data["parameters"] = param_dict

            reply_messages = self.__generateMessage__(message_data)

        else:
            message_data = self.__getScenarioData__(self.scenario, message_id)

            if message_data:
                reply_messages = self.__generateMessage__(message_data)
            elif self.language_code:
                # Target message might be non-translatable (composite message or similar)
                base_scenario = self.__getBaseScenarioPartition__()
                message_data = self.__getScenarioData__(base_scenario, message_id)

                if message_data:
                    reply_messages = self.__generateMessage__(message_data)

        if reply_messages:
            # Save latest reply message ID in user
            # session table before sending it via LINE Messaging API
            if self.event_source:
                self.user_session.setLastMessageId(
                    self.event_source.user_id,
                    message_id)

        return reply_messages

    def __generateMessage__(self, message_data):
        """Creates the actual message object using the LINE SDK.
        :param dict message_data: JSON like dict representing a LINE API message
        :returns linebot.models.Message: A list of or single LINE Messaging API message object.
        """

        alttext_support = [
            "buttons", "imagemap", "carousel", "bubbleFlex", "carouselFlex", "confirm"
        ]

        messages = None

        # Check if composite message
        if message_data.get("dataType") == "compositeMessage":
            message_list = message_data.get("messages")
            if message_list:
                items = []
                for message_id in message_list[:5]:
                    msg_data = self.__getScenarioData__(self.scenario, message_id)
                    if msg_data:
                        items.append(msg_data)
                self.main_message_data_id = message_data.get("dataId")
                reply_msgs = []
                for item in items:
                    # Ignore types that do not have a generator
                    item_data_type = item.get("dataType")
                    if item_data_type in self.message_generator:
                        if item_data_type in alttext_support and item.get("nameLBD"):
                            reply_msgs.append(
                                self.message_generator[item_data_type](item.get("params"),
                                                                       item.get("nameLBD"))
                            )
                        else:
                            reply_msgs.append(
                                self.message_generator[item_data_type](item.get("params"))
                            )

                messages = reply_msgs
        elif message_data.get("dataType") == "apiCall":
            # Get api call data
            function_name = message_data.get("function")
            params = message_data.get("parameters")
            params['SCENARIO_ENVIRONMENT'] = SCENARIO_ENVIRONMENT

            return self.chatbot_api.call_api_method(function_name, self.event_source, params)

        elif message_data.get("dataType") in self.message_generator:
            if (message_data.get("params") and message_data.get("dataType") in alttext_support) \
            and message_data.get("nameLBD"):
                messages = self.message_generator[message_data.get("dataType")](
                    message_data.get("params"), message_data.get("nameLBD")
                )
            else:
                messages = self.message_generator[message_data.get("dataType")]\
                    (message_data.get("params"))

        return messages

    def getMessageByText(self, text):
        """Retrieves a message from the scenario by user defined text mapping.

        If no exact match is found, use fuzzy search to try and match the best possible result.

        :param str text: The text message received as an event from the webhook.
        :returns linebot.models.Message: A list of or a single LINE Messaging API message object.
        """
        message = None

        if self.text_mapping:
            message_id = self.text_mapping.get(text)
            if message_id:
                message = self.getMessageById(message_id)

        return message

    def setupScenarioLanguage(self):
        """Sets up the scenario language for the current execution of the chatbot,
        based on the user settings saved in the session table.
        """
        # Get and set user language from user session table. eventSource has LINE user ID
        lang_code = self.user_session.getUserLanguage(
            self.event_source.user_id
        )

        # If langCode is not empty, non-Japanese language
        # In case it's Japanese, it's already been set up during init
        if lang_code:
            self.language_code = lang_code
            self.scenario = self.scenario + "#" + lang_code
            self.__setupTextMapping__()

    def __generate_flex_message_helper__(self, carousel_data, name_lbd="カルーセルフレックス"):
        bubble_messages = []
        bubbles = carousel_data['bubbleParam']
        #bubble limit is 10
        limit = 10
        for index, bbl in enumerate(bubbles):
            if index == limit:
                break
            bubble_data = self.__getScenarioData__(self.scenario, bbl)
            if bubble_data:
                bubble_messages.append(bubble_data)

        return flex_message.generate_carousel(bubble_messages, name_lbd)

    # Set up text mapping based on current scenario
    def __setupTextMapping__(self):
        mapping_data_list = self.__getScenarioDataByType__(self.scenario, "textMapping")
        if len(mapping_data_list) > 0:
            self.text_mapping = mapping_data_list[0].get("textMapping")
        elif self.language_code:
            # Fallback to default
            base_scenario = self.__getBaseScenarioPartition__()
            mapping_data_list = self.__getScenarioDataByType__(base_scenario, "textMapping")

            if len(mapping_data_list) > 0:
                self.text_mapping = mapping_data_list[0].get("textMapping")

    def __getActiveScenario__(self):
        return self.scenario

    @staticmethod
    def __getEnvironmentSettings__():
        dynamodb = boto3.resource('dynamodb')
        table = dynamodb.Table(TABLE_CHATBOT_SCENARIO)
        try:
            response = table.get_item(
                Key={
                    'scenarioId': 'settings'
                }
            )
            return replace_decimals(response.get('Item'))

        except ClientError:
            # No data found
            return None

    @staticmethod
    def __getScenario__(scenario_id):
        dynamodb = boto3.resource('dynamodb')
        table = dynamodb.Table(TABLE_CHATBOT_SCENARIO)
        try:
            response = table.get_item(
                Key={
                    'scenarioId': scenario_id
                }
            )
            return replace_decimals(response.get('Item'))

        except ClientError:
            # No data found
            return None

    def __getScenarioData__(self, scenario, data_id):
        dynamodb = boto3.resource('dynamodb')
        table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)
        try:
            response = table.get_item(
                Key={
                    'scenario': scenario,
                    'dataId': data_id
                }
            )
            self.main_message_data_id = data_id
            item = replace_decimals(response.get('Item'))
            if item:
                return item
            return get_default_template_dict(data_id)

        except ClientError:
            # No data found
            return None

    @staticmethod
    def __getScenarioDataByType__(scenario, data_type):
        dynamodb = boto3.resource('dynamodb')
        table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)

        results = []
        condition_expression = Key('scenario').eq(scenario) & Key('dataType').eq(data_type)

        try:
            response = table.query(
                IndexName="scenario-dataType-index",
                KeyConditionExpression=condition_expression
            )

            results = response.get('Items')

            while 'LastEvaluatedKey' in response:
                response = table.query(
                    KeyConditionExpression=condition_expression,
                    ExclusiveStartKey=response['LastEvaluatedKey']
                )

                results.extend(response.get('Items'))
            return replace_decimals(results)

        except ClientError:
            # No data found.
            return []

    # Aux method to get the non-locale scenario partion (scenarioId + version)
    def __getBaseScenarioPartition__(self):
        scenario_split = self.scenario.split("#")
        return scenario_split[0] + "#" + scenario_split[1]

    def getUserSession(self):
        """Gets the user session data.

        :returns dict: The user session data in JSON style dict
        """
        return self.user_session.getUserSession(self.event_source.user_id)

    #####################
    ### LOCATION ###
    #####################
    def startLocationScenario(self):
        """Starts the special location scenario.

        returns linebot.models.Message: The first message of the location scenario.
        """
        self.user_session.setStartLocation(self.event_source.user_id)
        return self.getMessageById("LOCA_LOCATION_CONFIRM")

    def stopLocationScenario(self):
        """Disables location scenario by deleting data from the
        user session table.
        """
        self.user_session.delLocaton(self.event_source.user_id)
        return ""

    def handleLocationMessage(self, message):
        """Handles a LINE webhook location event (not damage report).

        :param message: Location message from LINE API webhook
        :type message: linebot.models.LocationMessage
        """
        message_id = self.location.process_location_message(
            message,
            self.event_source.user_id
        )

        return_value = None

        if message_id:
            return_value = self.getMessageById(message_id)\
                           if isinstance(message_id, str) else message_id

        return return_value

    ######################
    ## TRASH SEPARATION ##
    ######################
    def _trash_separation_fuzzy_search(self, user_input):
        """Checks if trash separation scenario exists.
        If it does, try a fuzzy search if it's enabled.
        If no message is found, then return a default message for trash separation.

        :param str user_input: The text message sent by the user for trash search
        :return linebot.models.Message: A text message or None if trash scenario does not exist.
        """
        message = None

        # Check if the trash talk object exists
        trash_talk = self.__getScenarioData__(self.scenario, "TRASH_SEPARATION_TALK")

        if trash_talk:
            fuzzy_search_enabled = 0

            if 'FUZZY_SEARCH_ENABLED' in os.environ:
                try:
                    fuzzy_search_enabled = int(os.environ['FUZZY_SEARCH_ENABLED'])
                except ValueError:
                    pass

            # Fuzzy search
            if fuzzy_search_enabled:
                best_match = fuzzy_search(
                    user_input,
                    list(self.text_mapping)
                )

                if best_match:
                    message = self.getMessageById(
                        self.text_mapping.get(
                            best_match
                        )
                    )

            if not message:
                # Return default message in case fuzzy search also failed
                default_id = self.text_mapping.get("TRASH_NOT_FOUND_DEFAULT_MESSAGE", '')
                if default_id:
                    message = self.getMessageById(default_id)

        return message

    ###########
    ## BOSAI ##
    ###########
    def _bosai_set_mode(self, activated=True, talkId=None):
        # Update bosai mode status on the scenario table
        # Check existing settings
        bosai_settings = self.settings.get('bosaiMode')
        if not bosai_settings:
            bosai_settings = {}

        bosai_settings[SCENARIO_ENVIRONMENT] = activated
        bosai_settings[SCENARIO_ENVIRONMENT + "_talkId"] = talkId

        # Update table
        dynamodb = boto3.resource('dynamodb')
        table = dynamodb.Table(TABLE_CHATBOT_SCENARIO)

        table.update_item(
            Key={
                'scenarioId': 'settings'
            },
            UpdateExpression="set #bosaiMode = :bosaiSettings",
            ExpressionAttributeValues={
                ':bosaiSettings': bosai_settings
            },
            ExpressionAttributeNames={
                '#bosaiMode': 'bosaiMode'
            },
            ReturnValues="UPDATED_NEW"
        )

    def _bosai_set_rich_menu(self, defaultProduction=None, defaultSandbox=None, bosaiProduction=None, bosaiSandbox=None):
        # Update bosai rich menu values on the scenario table
        # Check existing settings
        rich_menu_settings = self.settings.get('richMenus')
        if not rich_menu_settings:
            rich_menu_settings = {}

        rich_menu_settings["defaultProduction"] = defaultProduction
        rich_menu_settings["defaultSandbox"] = defaultSandbox
        rich_menu_settings["bosaiProduction"] = bosaiProduction
        rich_menu_settings["bosaiSandbox"] = bosaiSandbox

        # Update table
        dynamodb = boto3.resource('dynamodb')
        table = dynamodb.Table(TABLE_CHATBOT_SCENARIO)

        table.update_item(
            Key={
                'scenarioId': 'settings'
            },
            UpdateExpression="set #richMenus = :rich_menu_settings",
            ExpressionAttributeValues={
                ':rich_menu_settings': rich_menu_settings
            },
            ExpressionAttributeNames={
                '#richMenus': 'richMenus'
            },
            ReturnValues="UPDATED_NEW"
        )

    def bosai_mode(self, activate=True, talkId=None):
        """Activates or deactivates bosai mode.

        Enabling bosai mode consists of 3 steps:
        1. Changing the active rich menu to the one set as bosai mode rich menu.
        2. Broadcast the first message of the selected talk (default to bosai flow)
        3. Set a flag in settings indicating bosai mode status

        :param bool: Activation flag
        :returns str result: "SUCCESS" or "ERROR"
        :return [T <= str] warnings: List of warnings or errors, if any ocurred.
        """
        result = 'SUCCESS'
        message = ''

        # Activate or deactivate bosai mode
        # Check if message and rich menu settings exist
        try:
            msg_to_broadcast = None

            # Get the initial message from the talk object using the provided talkId
            if talkId:
                scenario_talk = self.__getScenarioData__(self.scenario, talkId)
                if scenario_talk:
                    scenario_talk_params = scenario_talk.get('params')
                    if scenario_talk_params and scenario_talk_params.get('messages'):
                        msg_to_broadcast_id = next(msg for msg in scenario_talk_params.get('messages') if msg.get('sender') == 'BOT')
                        msg_to_broadcast = self.getMessageById(msg_to_broadcast_id.get('messageId'))

            # If the message is not found, default to the Normal Bosai Flow start message
            if not msg_to_broadcast:
                msg_to_broadcast = self.getMessageById(BOSAI_FLOW_START)

            # Depending on the environment, get production or sandbox
            if not self.settings.get('richMenus'):
                # No rich menu is set, so raise an exception.
                raise ScenarioException('通常モードと災害時モードのリッチメニュー設定が見つかりませんでした。'
                                        'シナリオモジュールのリッチメニュー管理画面で設定してください。')

            normal_rich_menu_id = None
            bosai_rich_menu_id = None
            if SCENARIO_ENVIRONMENT == 'production':
                normal_rich_menu_id = self.settings.get('richMenus').get('defaultProduction')
                bosai_rich_menu_id = self.settings.get('richMenus').get('bosaiProduction')
            else:
                normal_rich_menu_id = self.settings.get('richMenus').get('defaultSandbox')
                bosai_rich_menu_id = self.settings.get('richMenus').get('bosaiSandbox')

            if activate and (not msg_to_broadcast or not bosai_rich_menu_id):
                raise ScenarioException('災害時モードは有効にできません。 リッチメニュー設定がないか、シナリオが正しく作成されていません。')

            # Rich menu switch and broadcast
            if activate:
                self.line_bot_api.broadcast(
                    msg_to_broadcast
                )
                old_rich_menu_id = None
                updated_menues = {}
                try : 
                    old_rich_menu_id = self.line_bot_api.get_default_rich_menu()
                except (LineBotApiError) as line_api_error:
                    if line_api_error.error.message != no_default_rich_menu_error_text:
                        raise line_api_error
                if old_rich_menu_id is not None :
                    ignore_menues = []
                    updated_menues = self._update_rich_menu(old_rich_menu_id, ignore_menues, updated_menues)
                    self._bosai_set_rich_menu(
                        self._get_updated_rich_menu(updated_menues, self.settings.get('richMenus').get('defaultProduction')),
                        self._get_updated_rich_menu(updated_menues, self.settings.get('richMenus').get('defaultSandbox')),
                        self._get_updated_rich_menu(updated_menues, self.settings.get('richMenus').get('bosaiProduction')),
                        self._get_updated_rich_menu(updated_menues, self.settings.get('richMenus').get('bosaiSandbox')),
                    )
                self.line_bot_api.set_default_rich_menu(self._get_updated_rich_menu(updated_menues, bosai_rich_menu_id))
            else:
                # Go back to normal mode

                # Get the return to normal mode message
                msg_normal_mode = None

                bosai_settings = self.settings.get('bosaiMode')
                bosai_initiated_by_talkId = bosai_settings.get(SCENARIO_ENVIRONMENT + "_talkId")
                # Bosai Template Scenario normal mode message
                if bosai_initiated_by_talkId and str(bosai_initiated_by_talkId) in RETURN_TO_NORMAL_MESSAGE_IDS:
                    msg_normal_mode = self.getMessageById(
                        RETURN_TO_NORMAL_MESSAGE_IDS.get(bosai_initiated_by_talkId)
                    )

                # if no normal mode message is found, send default message
                if not msg_normal_mode:
                    msg_normal_mode = text_message.generate({
                        "text": "通常モードに変更します。"
                    })

                if msg_normal_mode:
                    self.line_bot_api.broadcast(
                        msg_normal_mode
                    )

                if normal_rich_menu_id:
                    old_rich_menu_id = None
                    updated_menues = {}
                    try : 
                        old_rich_menu_id = self.line_bot_api.get_default_rich_menu()
                    except (LineBotApiError) as line_api_error:
                        if line_api_error.error.message != no_default_rich_menu_error_text:
                            raise line_api_error
                    if old_rich_menu_id is not None :
                        ignore_menues = []
                        updated_menues = self._update_rich_menu(old_rich_menu_id, ignore_menues, updated_menues)
                        self._bosai_set_rich_menu(
                            self._get_updated_rich_menu(updated_menues, self.settings.get('richMenus').get('defaultProduction')),
                            self._get_updated_rich_menu(updated_menues, self.settings.get('richMenus').get('defaultSandbox')),
                            self._get_updated_rich_menu(updated_menues, self.settings.get('richMenus').get('bosaiProduction')),
                            self._get_updated_rich_menu(updated_menues, self.settings.get('richMenus').get('bosaiSandbox')),
                        )
                    self.line_bot_api.set_default_rich_menu(self._get_updated_rich_menu(updated_menues, normal_rich_menu_id))
                else:
                    self.line_bot_api.cancel_default_rich_menu()

            self._bosai_set_mode(activate, talkId)
        except (ScenarioException, LineBotApiError) as scenario_error:
            logging.exception('災害モードのアクティブ化中にエラーが発生しました。')
            result = 'ERROR'
            message = str(scenario_error)
        except ClientError as client_error:
            result = "ERROR"
            message = client_error.response['Error']['Message']

        return result, message

    @staticmethod
    def _get_updated_rich_menu(updated_menues, value):
        return updated_menues[value] if value in updated_menues else value

    def _update_rich_menu(self, target_rich_menu_id, ignore_menues, updated_menues):
        """Recreate target_rich_menu with same richMenuAliasId

        Args:
            _line_bot_api : LINE Bot SDK object
            target_rich_menu_id : Target richmenu id to update
            ignore_menues : RichMenu ID list to ignore update
        """
        rich_menu = self.line_bot_api.get_rich_menu(target_rich_menu_id)
        new_rich_menu_id = None
    
        if not target_rich_menu_id in ignore_menues:
            # target_rich_menu を再作成する
            new_rich_menu_id = self.line_bot_api.create_rich_menu(rich_menu)
            content = self.line_bot_api.get_rich_menu_image(target_rich_menu_id)
            in_mem_file = io.BytesIO()
            for chunk in content.iter_content():
                in_mem_file.write(chunk)
            in_mem_file.seek(0)
            self.line_bot_api.set_rich_menu_image(new_rich_menu_id, content.content_type, in_mem_file)

            aliases = self.line_bot_api.get_rich_menu_alias_list()
            aliases = as_json_dict(aliases)
            existstAlias = False
            if aliases and 'aliases' in aliases:
                for alias in aliases['aliases']:
                    # リッチメニュー切り替えアクションの設定を維持するため、
                    # 再作成したtarget_rich_menuに対して、元々付与していたaliasIdを再付与する
                    if alias['richMenuId'] == target_rich_menu_id:
                        new_alias = RichMenuAlias(alias['richMenuAliasId'], new_rich_menu_id)
                        self.line_bot_api.update_rich_menu_alias(alias['richMenuAliasId'], new_alias)
                        existstAlias = True
            if not existstAlias:
                new_alias = RichMenuAlias(new_rich_menu_id[len("richmenu-"):], new_rich_menu_id)
                self.line_bot_api.create_rich_menu_alias(new_alias)

            # 既存のdefault richMenuを削除する
            self.line_bot_api.delete_rich_menu(target_rich_menu_id)

            ignore_menues.append(target_rich_menu_id)
            ignore_menues.append(new_rich_menu_id)
            updated_menues[target_rich_menu_id] = new_rich_menu_id
        rich_menu_dic = as_json_dict(rich_menu)
        if rich_menu_dic and "areas" in rich_menu_dic:
            for area in rich_menu_dic["areas"]:
                if "action" in area and area["action"]["type"] == "richmenuswitch":
                    alias = self.line_bot_api.get_rich_menu_alias(area["action"]["richMenuAliasId"])
                    alias = as_json_dict(alias)
                    if alias:
                        if not alias["richMenuId"] in ignore_menues:
                            self._update_rich_menu(alias["richMenuId"], ignore_menues, updated_menues)

        return updated_menues

    #######################
    ## TALK DISTRIBUTION ##
    #######################
    def initiate_talk(self, talkId, talkName, useDisasterRichmenu=False):
        """Sends the initial message of a specific talk in the active scenario

        Initiating a talk consists of 3 steps:
        1. Changing the active rich menu to the one set as bosai mode rich menu.
        2. Broadcast the first message of the talk
        3. If the talk is a disaster scenario, set a flag in settings indicating disaster mode status

        :param string: Talk Name
        :param bool: Change the richmenu to disaster
        :returns str result: "SUCCESS" or "ERROR"
        :return [T <= str] warnings: List of warnings or errors, if any ocurred.
        """
        result = 'SUCCESS'
        message = ''

        try:
            msg_to_broadcast_id = None

            # Get active scenario talk by talkName
            active_scenario_talks = self.__getScenarioDataByType__(self.scenario, "talk")

            matching_scenario_talks = []
            # The Id of the talk distribution is a template scenario, find talk by id. Otherwise, find my talkName
            if talkId in TEMPLATE_SCENARIO_TALK_IDS:
                matching_scenario_talks = list(filter(lambda talk: talk.get('dataId') == talkId, active_scenario_talks))
            else:
                matching_scenario_talks = list(filter(lambda talk: talk.get('params') and talk.get('params').get('name') == talkName, active_scenario_talks))
            if len(matching_scenario_talks) == 0:
                print(f"Unable to find matching talk in active scenario for given parameters. Active scenario: {self.scenario} Distribution TalkId: {talkId} Distribution TalkName: {talkName}")
                result = 'ERROR'
                message = 'トークのアイテムを見つけられませんでした。'
                return result, message

            # Get the first bot message in the talk
            matching_scenario_talk = matching_scenario_talks[0].get('params')
            if matching_scenario_talk and matching_scenario_talk.get('messages'):
                msg_to_broadcast_id = next(msg for msg in matching_scenario_talk.get('messages') if msg.get('sender') == 'BOT')
                if msg_to_broadcast_id:
                    msg_to_broadcast_id = msg_to_broadcast_id.get('messageId')

            if not msg_to_broadcast_id:
                result = 'ERROR'
                message = 'トークの最初ボットメッセージを見つけられませんでした。'
                return result, message

            msg_to_broadcast = self.getMessageById(msg_to_broadcast_id)

            # get the rich menu id to display in case disaster richmenu is needed
            rich_menu_id = None
            if useDisasterRichmenu:
                # Depending on the environment, get production or sandbox
                if not self.settings.get('richMenus'):
                    # No rich menu is set, so raise an exception.
                    raise ScenarioException('通常モードと災害時モードのリッチメニュー設定が見つかりませんでした。'
                                            'シナリオモジュールのリッチメニュー管理画面で設定してください。')

                if SCENARIO_ENVIRONMENT == 'production':
                    rich_menu_id = self.settings.get('richMenus').get('bosaiProduction')
                else:
                    rich_menu_id = self.settings.get('richMenus').get('bosaiSandbox')

            if not msg_to_broadcast or (useDisasterRichmenu and not rich_menu_id):
                raise ScenarioException('トークは起動できません。 防災リッチメニュー設定がないか、トークが正しく作成されていません。')

            self.line_bot_api.broadcast(
                    msg_to_broadcast
                )
            if useDisasterRichmenu:
                old_rich_menu_id = None
                updated_menues = {}
                try : 
                    old_rich_menu_id = self.line_bot_api.get_default_rich_menu()
                except (LineBotApiError) as line_api_error:
                    if line_api_error.error.message != no_default_rich_menu_error_text:
                        raise line_api_error
                if old_rich_menu_id is not None :
                    ignore_menues = []
                    updated_menues = self._update_rich_menu(old_rich_menu_id, ignore_menues, updated_menues)
                    self._bosai_set_rich_menu(
                        self._get_updated_rich_menu(updated_menues, self.settings.get('richMenus').get('defaultProduction')),
                        self._get_updated_rich_menu(updated_menues, self.settings.get('richMenus').get('defaultSandbox')),
                        self._get_updated_rich_menu(updated_menues, self.settings.get('richMenus').get('bosaiProduction')),
                        self._get_updated_rich_menu(updated_menues, self.settings.get('richMenus').get('bosaiSandbox')),
                    )
                self.line_bot_api.set_default_rich_menu(self._get_updated_rich_menu(updated_menues, rich_menu_id))
                self._bosai_set_mode(True, talkId)
        except (ScenarioException, LineBotApiError) as scenario_error:
            logging.exception('トーク配信を起動中にエラーが発生しました。')
            result = 'ERROR'
            message = str(scenario_error)
        except ClientError as client_error:
            result = "ERROR"
            message = client_error.response['Error']['Message']

        return result, message

def to_camel_case(text):
    """Convert to camel case.
    :param str text:
    :rtype: str
    """
    split = text.split('_')
    return split[0] + "".join(x.title() for x in split[1:])

def as_json_dict(menu):
    """Return dictionary from this object.
    :return: dict
    """
    data = {}
    for key, value in menu.__dict__.items():
        camel_key = to_camel_case(key)
        if isinstance(value, (list, tuple, set)):
            data[camel_key] = list()
            for item in value:
                if hasattr(item, 'as_json_dict'):
                    data[camel_key].append(item.as_json_dict())
                else:
                    data[camel_key].append(item)
        elif hasattr(value, 'as_json_dict'):
            data[camel_key] = value.as_json_dict()
        elif value is not None:
            data[camel_key] = value
    return data