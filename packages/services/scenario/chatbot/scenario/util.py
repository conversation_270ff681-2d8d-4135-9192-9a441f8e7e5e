"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os
import sys
import decimal
import traceback
import json
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime, timezone, timedelta
import math

import requests
import boto3
from botocore.exceptions import ClientError
import pykakasi

# Helper class to convert a DynamoDB item to JSON.
class DecimalEncoder(json.JSONEncoder):
    """Custom JSON encoder for DynamoDB compatibility.
    """
    def default(self, o):
        if isinstance(o, set):
            return list(o)
        if isinstance(o, decimal.Decimal):
            if abs(o) % 1 > 0:
                return float(o)

            return int(o)

        return super(DecimalEncoder, self).default(o)

# Helper class for JSON decoding
class DecimalDecoder(json.JSONDecoder):
    """Custom JSON decoder for DynamoDB compatibility.
    """
    def __init__(self, *args, **kwargs):
        json.JSONDecoder.__init__(
            self,
            parse_float=self.float_to_decimal,
            parse_int=self.float_to_decimal,
            *args,
            **kwargs)

    @staticmethod
    def float_to_decimal(value):
        """Converts passed value to Decimal
        """
        return decimal.Decimal(value)

# Helper method to convert Decimals in an arbitrary object
def replace_decimals(obj):
    """Converts all Decimal members of an arbitrary object to either int or float.
    Used for DynamoDB compatibility.
    """
    if isinstance(obj, list):
        for i, value in enumerate(obj):
            obj[i] = replace_decimals(value)
        return obj

    if isinstance(obj, dict):
        for key, value in obj.items():
            obj[key] = replace_decimals(value)
        return obj

    if isinstance(obj, set):
        return set(replace_decimals(i) for i in obj)

    if isinstance(obj, decimal.Decimal):
        if obj % 1 == 0:
            return int(obj)

        return float(obj)

    return obj

def replace_lists(obj):
    """Converts all list members to a single value using the first element
    """
    if isinstance(obj, dict):
        for i, value in obj.items():
            if isinstance(value, list) and len(value) > 0:
                obj[i] = value[0]

    return obj

def send_email(source, to, subject, body, attachments=None):
    """Sends an e-mail using AWS SES service.

    :param str source: E-mail sender
    :param str to: E-mail receiver
    :param str subject: E-mail subject
    :param str body: E-mail body
    :param dict attachments: Dict containing attachments
    """
    try:
        client = boto3.client('ses',
                              region_name=os.environ['SES_REGION'])

        msg = MIMEMultipart('mixed')
        msg['Subject'] = subject
        msg['From'] = source
        msg['To'] = to

        part = MIMEText(body)
        msg.attach(part)


        if attachments and len(attachments) > 0:
            #------ S3 bucket -------
            # attachments={
            #     'type':'S3',
            #     'attachments':[
            #         {
            #             'bucket':'bucket1',
            #             'key':'resources/aaa.jpg'
            #         }
            #     ]
            # }
            if attachments['type'] == 'S3':
                for attach in attachments['attachments']:
                    s3_object = boto3.client('s3', os.environ['SES_REGION'])
                    # s3_object = boto3.client('s3', 'us-west-2')
                    s3_object = s3_object.get_object(Bucket=attach['bucket'], Key=attach['key'])
                    body = s3_object['Body'].read()

                    part = MIMEApplication(body, attach['key'].split("/")[-1])
                    part.add_header(
                        "Content-Disposition",
                        'attachment',
                        filename=attach['key'].split("/")[-1]
                        )
                    msg.attach(part)


            #------ NORMAL -------
            # attachments={
            #     'type':'NORMAL',
            #     'attachments':['/tmp/bbb.jpg','/tmp/ccc.jpg']
            # }
            if attachments['type'] == 'NORMAL':
                for attach in attachments['attachments']:
                    filename = os.path.basename(attach)
                    r_attach = requests.get(attach)
                    part = MIMEApplication(r_attach.content, _subtype="octet-stream")
                    # part = MIMEApplication(open(attach, 'rb').read())
                    part.add_header('Content-Disposition', 'attachment', filename=filename)
                    msg.attach(part)

        return client.send_raw_email(
            Source=msg['From'],
            Destinations=[msg['To']],
            RawMessage={'Data' : msg.as_string()}
        )
    except ClientError as client_error:
        print("ClientError: ", client_error.response['Error']['Message'])
    except Exception as exception:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        trace_back = traceback.extract_tb(exc_tb)[-1]
        print("[ERROR]   row: {} {} {}".format(trace_back[1], trace_back[2], exception))

def get_verified_ses_domain():
    """
    Tries to retrieve a verified SES domain that can be used
    to send damage report e-mails.
    """
    valid_domain = None

    try:
        client = boto3.client(
            'ses',
            region_name=os.environ['SES_REGION']
        )

        response = client.list_identities(
            IdentityType='Domain',
            MaxItems=100,
            NextToken='',
        )

        # Find next valid domain
        response = client.get_identity_verification_attributes(
            Identities=response.get("Identities", [])
        )

        for domain in response.get("VerificationAttributes").keys():
            if response["VerificationAttributes"][domain]['VerificationStatus'] == 'Success':
                valid_domain = domain
                break

    except ClientError as ses_error:
        print("SES Error: ", ses_error.response['Error']['Message'])

    return valid_domain

def get_current_date_time_report():
    """Retrieves the current date and time in Japanese format.

    :returns str: A text representation of the current date and time.
    """
    japan_timezone = timezone(timedelta(hours=9))
    current_date = datetime.now(tz=japan_timezone)
    return current_date.strftime("%Y年%m月%d日 %H:%M")

def deg2rad(degree):
    """Converts an angle in degrees to rad format.

    :param int degree: Angle in deegres
    :returns float: Angle in rads
    """
    return float(degree) * float(math.pi / 180)

def distance(lat1, lon1, lat2, lon2):
    """Calculates the distance between two coordinates.

    :returns float: Distance between the provided coordinates
    """
    radlat1 = deg2rad(lat1)
    radlon1 = deg2rad(lon1)
    radlat2 = deg2rad(lat2)
    radlon2 = deg2rad(lon2)

    radius = 6378137.0

    average_lat = (radlat1 - radlat2) / 2
    average_lon = (radlon1 - radlon2) / 2

    _distance = radius * 2 * math.asin(
        math.sqrt(
            math.pow(math.sin(average_lat), 2)\
            + math.cos(radlat1)\
            * math.cos(radlat2)\
            * math.pow(math.sin(average_lon), 2)
        )
    )
    return _distance

def haversine(lat1, lon1, lat2, lon2):
    """ Calculates distance between two coordinates using harvesine function.

    :param float lat1, lon1, lat2, lon2: Coordinates
    :returns float: Distance in meters
    """
    # Radius of Earth in meters
    earth_radius = 6371000

    phi_1 = math.radians(lat1)
    phi_2 = math.radians(lat2)

    delta_phi = math.radians(lat2-lat1)
    delta_lambda = math.radians(lon2-lon1)

    a = math.sin(delta_phi/2.0)**2+\
        math.cos(phi_1)*math.cos(phi_2)*\
        math.sin(delta_lambda/2.0)**2

    c = 2 * math.atan2(
        math.sqrt(a),
        math.sqrt(1-a)
    )

    # Distance in meters
    return earth_radius * c

def get_search_area_coordinates(lat, lon, radius):
    """Calculate max and min coordinates for a seach area
    based on the passed radius.

    :param float lat, lon: Latitude and longitude for center
    :param float Radius in meters

    return dict: Max and min coordinates for latitude and longitude
    """
    radius_in_degrees = radius / 111000.0

    lat_max = lat + radius_in_degrees
    lat_min = lat - radius_in_degrees

    # Adjust longitude based on original latitude to account
    # for shrinking distances due to Earth curvature
    lat_rad = math.radians(lat)
    adjusted_radius = radius_in_degrees / math.cos(lat_rad)

    lon_max = lon + adjusted_radius
    lon_min = lon - adjusted_radius

    return {
        'lat_max': lat_max,
        'lat_min': lat_min,
        'lon_max': lon_max,
        'lon_min': lon_min
    }

def add_to_list(list_to_add_into, items_to_add):
    """Adds either a list of objects or a single one
    to a list.
    """
    if not items_to_add is None:
        if isinstance(items_to_add, list):
            list_to_add_into.extend(items_to_add)
        else:
            list_to_add_into.append(items_to_add)

def fuzzy_search(str_to_match, match_values):
    """ Do a fuzzy search to match a string with a list of values.
    Uses Kakasi library to convert all words to romaji.
    Romaji form is used for matching.

    :param str str_to_match: The string to search for
    :param list[T <= str] match_values: List of strings to match
    :param float min_ratio: Minimum acceptable ratio
    :returns str: The match value with highest ratio. None if found ratio is below minimum treshold
    """
    kakasi = pykakasi.kakasi()

    kakasi.setMode("H", "a") # Hiragana to ascii, default: no conversion
    kakasi.setMode("K", "a") # Katakana to ascii, default: no conversion
    kakasi.setMode("J", "a") # Japanese to ascii, default: no conversion
    kakasi.setMode("r", "Hepburn") # default: use Hepburn Roman table

    conv = kakasi.getConverter()

    # Convert all mapping values to romaji (hepburn) before doing the search
    translated_values = [conv.do(value) for value in match_values]

    # User input can be splitted in Japanase words
    # Always use the first match
    kakasi.setMode("s", True) # add space, default: no separator
    conv = kakasi.getConverter()

    user_input = conv.do(str_to_match).split(' ')[0]

    # Compare user input with existing text mappings
    matched_value = None

    for value in translated_values:
        if user_input == value:
            matched_value = value
            break

    # Return original user mapping in Japanese
    return match_values[translated_values.index(matched_value)] if matched_value else None

def nested_replace(data, original, new):
    """ Traverse a dictionary like object and replace the original string, with new string
    """
    if type(data) == list:
        return [nested_replace( item, original, new) for item in data]

    if type(data) == dict:
        return {key : nested_replace(value, original, new)
                    for key, value in data.items() }

    if data == original: # Exact match
        if new:
            return new
        else:
            return data

    elif data and data==True:
        return data

    elif data and isinstance(data,str)  and (original in data):
        newdata=data.replace(original, new)
        return newdata

    else:
        return data
