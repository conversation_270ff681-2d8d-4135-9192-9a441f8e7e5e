"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import csv
import json
import os
import traceback
import sys

import boto3
from linebot.models import FlexSendMessage
from linebot.models import CarouselColumn, CarouselContainer, BubbleContainer
from linebot.models import ButtonsTemplate, TemplateSendMessage
from linebot.exceptions import (LineBotApiError)

from botocore.exceptions import ClientError

from scenario.util import replace_decimals, distance
from scenario.models.actions import action_generator
from scenario.api_methods.base_api_method import BaseApiMethod
from scenario.user_session import UserSession

s3 = boto3.resource('s3')

# from scenario.models  import carousel_template

# Specified S3 bucket for CSV file , json template file
BUCKET_CHATBOT_IMPORTED_RESOURCES = os.environ["BUCKET_CHATBOT_IMPORTED_RESOURCES"]
TABLE_CHATBOT_SCENARIO_DATA = os.environ['TABLE_CHATBOT_SCENARIO_DATA']

class CsvToCarouselMethod(BaseApiMethod):
    """
    Example API to generate carousel from CSV Data . Will not be used in production most likely.
    """
    def __init__(self):
        super().__init__()
        self.user_session = UserSession()

        dynamodb = boto3.resource('dynamodb')
        self.table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)

    def getName(self):
        return super().getName()

    def getDescription(self):
        return "Generate carousel from CSV Data"

    def getParamsDescription(self):
        return {
            "csvfilepath" : "CSV file path in S3 bucket",
            "jsonfile" : "Json template file path in S3 bucket",
            "filter_condition" : "Condition to filter data"

        }

    @staticmethod
    def getScenarioData(scenario, data_id):
        dynamodb = boto3.resource('dynamodb')
        table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)
        try:
            response = table.get_item(
                Key={
                    'scenario': scenario,
                    'dataId': data_id
                }
            )
            return replace_decimals(response.get('Item'))

        except ClientError:
            # No data found
            return None

    @staticmethod
    def read_csv_file(bucketname, object_name, filter_express="", locationparam=None):
        """Extract csv data from pre-uploaded csv file in S3 Bucket.

        :param str bucketname: Name of S3 bucket
        :param str object_name: Key of S3 object (csv file)
        :param str filter_express: Filter expression for eading CSV file.
        :param dict locationparam: Miscenllaneous parameters

        :returns list[T <= dict]: List of CSV records mapped by header name
        """
        try:
            return_list = None

            csv_lat_name = "緯度"
            csv_lon_name = "経度"

            bucket = s3.Bucket(bucketname)
            obj = bucket.Object(key=object_name)
            response = obj.get()
            lines = response['Body'].read().decode('utf-8').splitlines()
            csvreader = list(csv.DictReader(lines))

            if locationparam: # BY LOCATION
                if locationparam['filter']:
                    radius_range = int(locationparam['filter'].split(':')[1])
                else:
                    radius_range = 1000  # Default 1 km

                data_list = []
                for item in csvreader:
                    if item[csv_lon_name] and item[csv_lon_name]:
                        dist = distance(
                            locationparam['location']['latitude'],
                            locationparam['location']['longitude'],
                            item[csv_lat_name],
                            item[csv_lon_name]
                        )

                        if dist < radius_range:
                            data_list.append(item)

                if data_list:
                    return_list = data_list
                else:
                    return_list = csvreader

            # NON LOCATION
            if filter_express:
                if "=" in filter_express:
                    key = filter_express.split("=")[0]
                    value = filter_express.split("=")[1]
                    if csvreader and key in csvreader[0]:
                        data_list = []
                        for data in csvreader:
                            if value in data[key]:
                                data_list.append(data)

                        return_list = data_list
                    else:
                        return_list = []
            else:
                return_list = list(csvreader)

        except (ClientError, KeyError) as error:
            exc_type, exc_obj, exc_tb = sys.exc_info()
            trace_back = traceback.extract_tb(exc_tb)[-1]
            print("[ERROR] read csv file: ", trace_back[1], trace_back[2], error)

    @staticmethod
    def read_json_file(bucketname, object_name):
        """Extract json template from pre-uploaded json file in S3 Bucket

        :param str bucketname: Name of the bucket containing the JSON file
        :param str object_name: S3 key of the JSON file
        """
        try:
            bucket = s3.Bucket(bucketname)

            obj = bucket.Object(key=object_name)
            file_content = obj.get()['Body'].read().decode('utf-8')

            json_content = json.loads(file_content)

            return json_content
        except (ValueError, ClientError) as error:
            exc_type, exc_obj, exc_tb = sys.exc_info()
            trace_back = traceback.extract_tb(exc_tb)[-1]
            print("[ERROR] read_json_file: ", trace_back[1], trace_back[2], error)

    @staticmethod
    def read_json_from_db(scenario, data_id):
        """Extract json template from DynamoDB.

        :param str scenario: Scenario identifier
        :param str data_id: Id of the JSON item

        :returns dict: JSON template
        """
        dynamodb = boto3.resource('dynamodb')
        table = dynamodb.Table(TABLE_CHATBOT_SCENARIO_DATA)
        try:
            response = table.get_item(
                Key={
                    'scenario': scenario,
                    'dataId': data_id
                }
            )
            params = response.get('Item').get('params')
            json_content = params
            return json_content

        except (ValueError, ClientError) as error:
            exc_type, exc_obj, exc_tb = sys.exc_info()
            trace_back = traceback.extract_tb(exc_tb)[-1]
            print("[ERROR] read_json_file: ", trace_back[1], trace_back[2], error)

    def nested_replace(self, data, original, new):
        """Matches the pre-defined key in json file with CSV data's
        column name and replace with csv data value.
        """
        return_value = None

        if isinstance(data, list):
            return_value = [self.nested_replace(item, original, new) for item in data]

        if isinstance(data, dict):
            return_value = {key : self.nested_replace(value, original, new)
                            for key, value in data.items()}

        if data == original: # Exact match
            if new:
                return_value = new.replace("https://", "").replace("http://", "")
            else:
                return_value = data

        elif isinstance(data, bool) and data:
            return_value = data

        elif isinstance(data, str) and (original in data):
            if "http" in new: # Included https
                new = new.replace("https://", "").replace("http://", "")
                newdata = data.replace(original, new)
            else:
                newdata = data.replace(original, new)
            return_value = newdata

        else:
            return_value = data

        return return_value

    def disableButton(self, obj):
        """Disables button contained in obj.

        :param dict obj: JSON data
        """
        disable_message = {
            "type": "text",
            "text": "button",
            "color": "#D8D5D2",
            "flex": 2,
            "align": "center",
            "gravity": "center",
            "contents": []
        }

        if isinstance(obj, list):
            for i in range(len(obj)):
                if "type" in obj[i] and\
                (obj[i]['type'] == "button") and\
                obj[i]['action']['type'] == "uri" and\
                ("<" in obj[i]['action']['uri']):
                    disable_message["text"] = obj[i]['action']['label']
                    obj[i] = disable_message
                elif "type" in obj[i] and  obj[i]['type'] == "box":  # Invisible empty data
                    if len(obj[i]['contents']) == 1 and\
                    'text' in obj[i]['contents'][0] and\
                    "<タグ" in obj[i]['contents'][0]['text']:
                        del obj[i]
                    else:
                        obj[i] = self.disableButton(obj[i])
                else:
                    obj[i] = self.disableButton(obj[i])

        elif isinstance(obj, dict):
            for key, value in obj.items():
                obj[key] = self.disableButton(value)

        return obj

    def execute(self, params=None):
        try:
            message_to_return = None

            if params:

                if 'user_id' in params and\
                'SCENARIO_ENVIRONMENT' in params: self.user_session.setStartLocation(
                    params['user_id'], params
                )

                # Generate Location Picker button template
                if "locationPicker" in params:

                    # Get Location picker from scenario data table
                    message_data = self.getScenarioData(
                        params['scenario'],
                        params['locationPicker']
                    )
                    data = message_data.get("params")

                    # Generate button template
                    action_list = []

                    for i in range(int(data.get("actionCount"))):
                        action_data = data.get("actions." + str(i))
                        if action_data:
                            action_list.append(action_generator[
                                action_data.get("type")
                            ](action_data))

                    thumbnail_image_url = data.get("thumbnailImageUrl")

                    if not thumbnail_image_url or thumbnail_image_url == "none":
                        thumbnail_image_url = None

                    # Create buttons template
                    button_template = ButtonsTemplate(
                        title=data.get("title"),
                        text=data.get("text"),
                        thumbnail_image_url=thumbnail_image_url,
                        actions=action_list)

                    message_to_return = TemplateSendMessage(
                        alt_text="button template",
                        template=button_template
                    )
                else: # Carousel or Bubble
                    # Retrieve from user session?
                    event_source = params.get("eventSource")
                    csvfilepath = params.get("csvfilepath")
                    jsonfile = params.get("jsonfile") # Example: S3 -> resources/stations.json
                    filter_condition = params.get("filter")
                    data_id = params.get("dataId")
                    scenario = params.get("scenario")

                    if event_source:
                        carousel = CarouselContainer()
                        json_template = {}

                        if jsonfile:
                            # get Json template from S3 Bucket
                            json_template = self.read_json_file(
                                BUCKET_CHATBOT_IMPORTED_RESOURCES,
                                jsonfile
                            )
                        else:
                            # get Json template from DynamoDB
                            json_template = self.read_json_from_db(scenario, data_id)

                        # Generate carousel by CSV Data
                        if csvfilepath:

                             # Get CSV data from S3 bucket
                            location_data = self.user_session.\
                                           getLocation(
                                               event_source.user_id
                                           )

                            # if locationData and locationData.get("Location").get("location") :
                            if location_data and location_data.get("Location"):
                                csvdatalist = self.read_csv_file(
                                    BUCKET_CHATBOT_IMPORTED_RESOURCES,
                                    location_data.get("Location").get('csvfile'),
                                    filter_condition,
                                    location_data.get("Location")
                                )
                                self.user_session.delLocaton(
                                    event_source.user_id
                                )
                            else:
                                csvdatalist = self.read_csv_file(
                                    BUCKET_CHATBOT_IMPORTED_RESOURCES,
                                    csvfilepath,
                                    filter_condition
                                )


                            # Loop csv data to replace values in Json
                            columns = []
                            bubble = []
                            for data in csvdatalist[:10]:  #Limit to 10
                                bubble_content = json_template

                                # REPLACED CSV DATA to JSON
                                for key in data:
                                    bubble_content = self.nested_replace(
                                        bubble_content,
                                        '<'+key+'>', data[key]
                                    )

                                #Carousel Template Json from S3 bucket
                                if json_template["type"] == "template":
                                    actions_list = []
                                    bubble_content = bubble_content[0]

                                    json_actions = bubble_content.get("actions")

                                    # Geneate action list
                                    for act in json_actions:
                                        # ********** TO-DO: NEED TO VALIDATE URL *********
                                        actions_list.append(action_generator[act['type']](act))

                                    try:
                                        columns.append(CarouselColumn(
                                            thumbnail_image_url=bubble_content[
                                                "thumbnailImageUrl"
                                            ],
                                            text=bubble_content.get("text"),
                                            actions=actions_list
                                        )) # THIS RESUFFLE THE STRUCTURE

                                    except LineBotApiError as line_api_error:
                                        print("lineApiError CarouselColumn ", line_api_error)

                                else:
                                    bubble = BubbleContainer.new_from_json_dict(
                                        bubble_content
                                    )
                                    carousel.contents.append(bubble)

                        # Generate carousel without csv data
                        else:
                            bubble = BubbleContainer.new_from_json_dict(
                                json_template
                            )
                            carousel.contents.append(bubble)

                        message_to_return = FlexSendMessage(
                            alt_text="Carousel Flex message",
                            contents=carousel
                        )

            return message_to_return
        except Exception as error:
            exc_type, exc_obj, exc_tb = sys.exc_info()
            trace_back = traceback.extract_tb(exc_tb)[-1]
            print(
                "\n\n[ERROR] execute FORM CAROUSEL: ",
                trace_back[1],
                trace_back[2],
                error
            )
