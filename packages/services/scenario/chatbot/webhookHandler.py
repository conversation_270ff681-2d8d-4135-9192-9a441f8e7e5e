"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os
import base64
import copy
import hashlib
import hmac
import json
import logging
from typing import List

from linebot import (
    LineBotApi, WebhookParser
)
from linebot.exceptions import LineBotApiError
from linebot.models import (
    MessageEvent, TextMessage, PostbackEvent, LocationMessage, ImageMessage
)

import boto3
from botocore.exceptions import ClientError
# AWS Lambda client
lambdaClient = boto3.client('lambda')

class HandlerWrapper:
    def __init__(self, scenario_manager, logger):
        self.__scenario_manager = scenario_manager
        self.__logger = logger
        self.__parser = WebhookParser(os.environ['LINEMESSAGING_CHANNEL_SECRET'])
        self.__line_bot_api = LineBotApi(
            os.environ['LINEMESSAGING_CHANNEL_ACCESS_TOKEN'],
            timeout=10.0
        )

    def handle(self, body, signature):
        """Handler for LINE messaging API webhook.

        :param str body: Webhook body
        :param str signature: X-Line-Signature
        """        
        parse_result = self.__parser.parse(body, signature, True)
        raw_events = json.loads(body)["events"]
        forward_events = []

        for index, event in enumerate(parse_result.events):
            func = None
            if isinstance(event, MessageEvent):
                if isinstance(event.message, TextMessage):
                    func = self.__handle_message
                if isinstance(event.message, LocationMessage):
                    func = self.__handle_location_message
                if isinstance(event.message, ImageMessage):
                    func = self.__handle_image_message

            if not func and isinstance(event, PostbackEvent):
                func = self.__handle_postback
            
            if not func:
                func = self.__handle_default

            handle_result = func(event)

            if not handle_result:
                forward_events.append(raw_events[index])
        
        if len(forward_events) > 0 and os.environ["CHATBOT_FORWARD"] == "1":
            self.__forward_external_webhook_url(parse_result.destination, forward_events)
    
    def __handle_postback(self, event):
        """Handler for LINE messaging API postback events.

        :param event: The LINE API messaging event
        :type event: linebot.models.PostbackEvent
        """
        self.__logger.logLineChannelEvent(copy.deepcopy(event))

        return self.__send_reply_messages(
            self.__scenario_manager.handle_line_webhook_event(event, "postback"),
            event
        )

    def __handle_message(self, event):
        """Handler for LINE messaging API text message events.

        :param event: The LINE API messaging event
        :type event: linebot.models.TextMessage
        """
        self.__logger.logLineChannelEvent(copy.deepcopy(event))

        return self.__send_reply_messages(
            self.__scenario_manager.handle_line_webhook_event(event, "text_message"),
            event
        )

    def __handle_location_message(self, event):
        """Handler for LINE messaging API location message events.

        :param event: The LINE API messaging event
        :type event: linebot.models.LocationMessage
        """
        self.__logger.logLineChannelEvent(copy.deepcopy(event))

        return self.__send_reply_messages(
            self.__scenario_manager.handle_line_webhook_event(event, "location_message"),
            event
        )

    def __handle_image_message(self, event):
        """Handler for LINE messaging API image message events.

        :param event: The LINE API messaging event
        :type event: linebot.models.ImageMessage
        """
        self.__logger.logLineChannelEvent(copy.deepcopy(event))

        return self.__send_reply_messages(
            self.__scenario_manager.handle_line_webhook_event(event, "image_message"),
            event
        )

    def __handle_default(self, event):
        """Handler for LINE messaging API unknown events.

        :param event: The LINE API messaging event
        :type event: linebot.models.*
        """
        self.__logger.logLineChannelEvent(copy.deepcopy(event))

        # We must return False to invoke Forward function
        return False

    def __send_reply_messages(self, reply_messages, event):
        """ Send back to the user a list of bot message using a reply token.

        :param reply_messages: List of bot messages
        :type reply_msgs: list[linebot.models.BaseMessage]

        :param event: The LINE API messaging event
        :type event: linebot.models.PostbackEvent
        """
        if reply_messages:
            try:
                self.__line_bot_api.reply_message(
                    event.reply_token,
                    reply_messages
                )
            except LineBotApiError as line_api_error:
                self.__logger.logErrorEvent(str(line_api_error))

            return True
        else:
            return False

    def __forward_external_webhook_url(self, destination: str, events: List):
        """Invoke Lambda function to forward LINE messaging API events.

        :param event: lambda raw event (not The LINE API messaging event)
        :type event: Any
        """

        # re-generate x-line-signature
        channel_secret_bytes = os.environ['LINEMESSAGING_CHANNEL_SECRET'].encode('utf-8')
        body = {
            "destination": destination,
            "events": events, 
        }
        json_body = json.dumps(body, ensure_ascii=False, indent=None)
        json_body_bytes = json_body.encode('utf-8')
        signature = hmac.new(channel_secret_bytes, json_body_bytes, hashlib.sha256).digest()
        signature_base64 = base64.b64encode(signature).decode('utf-8')
        payload = {
            "signature": signature_base64,
            "body": json_body,
        }

        try:
            # Call lambda asynchronously to forward event
            lambdaClient.invoke(
                FunctionName=os.environ['FORWARD_LAMBDA_ARN'],
                InvocationType='Event',
                Payload=json.dumps(payload).encode('utf-8')
            )
            return 'OK'
        except (ClientError, json.JSONDecodeError, TypeError) as e:
            logging.exception(
                f'Error forwarding LINE messaging API event: {e}'
            )
            return 'NG'
