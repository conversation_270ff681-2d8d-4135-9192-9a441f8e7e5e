"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import sys
import os
import json
import time
import logging
import traceback

import boto3
from botocore.exceptions import ClientError

from scenario.util import DecimalEncoder

lambdaClient = boto3.client('lambda') # Lambda reference

channelType = os.environ['SCENARIO_ENVIRONMENT']
channelId = os.environ['LINEMESSAGING_CHANNEL_ID']

class Logger():
    """Helper class to log chatbot events to DynamoDB.
    """
    def __init__(self, scenarioName):
        self.scenarioName = scenarioName

        self.eventSource = {
            'user':self.setUserSource,
            'group':self.setGroupSource,
            'room':self.setRoomSource,
        }

        #unfollow, leave have no extra params to save
        self.eventType = {
            'message':self.logMessageReceiveEvent,
            'postback':self.logPostbackReceiveEvent,
            'follow':self.logFollowReceiveEvent,
            'join':self.logJoinReceiveEvent,
            'memberJoined':self.logMemberJoinedEvent,
            'memberLeft':self.logMemberLeftEvent,
            'beacon':self.logBeaconEvent,
            'accountLink':self.logAccountLinkEvent,
            'things':self.logThingsEvent,
        }

    @staticmethod
    def setUserSource(eventData, payload):
        payload['sourceType'] = 'user'
        payload['sourceUserId'] = eventData.source.user_id

    @staticmethod
    def setGroupSource(eventData, payload):
        payload['sourceType'] = 'group'
        if eventData.type == 'message':
            payload['sourceUserId'] = eventData.source.user_id
        payload['sourceGroupId'] = eventData.source.group_id

    @staticmethod
    def setRoomSource(eventData, payload):
        payload['sourceType'] = 'user'
        if eventData.type == 'message':
            payload['sourceUserId'] = eventData.source.user_id
        payload['sourceRoomId'] = eventData.source.room_id

    def logLineChannelEvent(self, eventData):
        #setting common properties
        try:
            payload = {
                'logType': eventData.type,
                'createdAt': eventData.timestamp,
                'mode': eventData.mode,
                'channelId': channelId,
                'channelType': channelType,
                'scenarioName': self.scenarioName,
            }
            self.eventSource[eventData.source.type](eventData, payload)
            if eventData.type in self.eventType:
                self.eventType[eventData.type](eventData, payload)

            lambdaClient.invoke(
                FunctionName=os.environ['LOG_LAMBDA_ARN'],
                InvocationType='Event',
                Payload=json.dumps(payload, cls=DecimalEncoder).encode('utf-8'),
            )
        except ClientError:
            self.logErrorEvent('An error occured while saving Line Channel Event logs to DynamoDB')

    @staticmethod
    def logMessageReceiveEvent(messageData, payload):
        payload['replyToken'] = messageData.reply_token
        payload['message'] = messageData.message.as_json_dict()

    @staticmethod
    def logPostbackReceiveEvent(messageData, payload):
        payload['replyToken'] = messageData.reply_token
        payload['postback'] = messageData.postback.as_json_dict()

    @staticmethod
    def logFollowReceiveEvent(messageData, payload):
        payload['replyToken'] = messageData.reply_token

    @staticmethod
    def logJoinReceiveEvent(messageData, payload):
        payload['replyToken'] = messageData.reply_token

    @staticmethod
    def logMemberJoinedEvent(messageData, payload):
        payload['replyToken'] = messageData.reply_token
        payload['joined'] = messageData.joined.as_json_dict()

    @staticmethod
    def logMemberLeftEvent(messageData, payload):
        payload['left'] = messageData.left.as_json_dict()

    @staticmethod
    def logBeaconEvent(messageData, payload):
        payload['replyToken'] = messageData.reply_token
        payload['beacon'] = messageData.beacon.as_json_dict()

    @staticmethod
    def logAccountLinkEvent(messageData, payload):
        payload['replyToken'] = messageData.reply_token
        payload['link'] = messageData.link.as_json_dict()

    @staticmethod
    def logThingsEvent(messageData, payload):
        payload['replyToken'] = messageData.reply_token
        payload['things'] = messageData.things.as_json_dict()

    def logLineBotMessage(self, eventData, dataId):
        try:
            payload = {
                'logType': "message",
                'createdAt': int(round(time.time() * 1000)),
                'mode': eventData.mode,
                'sourceType': "bot",
                'message': dataId,
                'channelId': channelId,
                'channelType': channelType,
                'scenarioName': self.scenarioName,
            }

            if eventData.source.type == 'user' or eventData.type == 'message':
                payload['sentToUserId'] = eventData.source.user_id

            lambdaClient.invoke(
                FunctionName=os.environ['LOG_LAMBDA_ARN'],
                InvocationType='Event',
                Payload=json.dumps(payload, cls=DecimalEncoder).encode('utf-8'),
            )
        except ClientError:
            self.logErrorEvent('An error occured while saving Line Bot reply logs to DynamoDB')

    def logErrorEvent(self, errorMessage, withStackTrace=True, functionName=''):
        """Logs an error that occured while calling scenario API.

        :param str errorMessage: The error message to log
        :param boolean withStackTrace: Include the stack trace in the logs
            (Only needed for none exception logs)
        :param str functionName: The name of the function that threw the error
            (Only needed for none exception logs)
        """
        try:
            if withStackTrace:
                error_message_to_log = '"[ERROR]" ' + errorMessage + '\n'
                trace_back_text = traceback.format_exc()
                logging.error(error_message_to_log + trace_back_text)
            else:
                error_message_to_log = '"[ERROR]" ' + errorMessage + ' in function ' + functionName
                logging.error(error_message_to_log)
        except:
            exc_type, exc_value, exc_traceback = sys.exc_info()
            traceback.print_exception(exc_type, exc_value, exc_traceback, limit=2, file=sys.stdout)
