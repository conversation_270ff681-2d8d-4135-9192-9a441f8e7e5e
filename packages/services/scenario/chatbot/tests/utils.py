"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""

import hmac
import json
import base64
import hashlib


def regenerate_x_line_signature(secret, body):
    """
    re-generate x-line-signature
    """
    channel_secret_bytes = secret.encode('utf-8')
    json_body = json.dumps(body, ensure_ascii=False, indent=None)
    json_body_bytes = json_body.encode('utf-8')
    signature = hmac.new(channel_secret_bytes, json_body_bytes, hashlib.sha256).digest()
    signature_base64 = base64.b64encode(signature).decode('utf-8')
    return signature_base64


def json_as_str(data):
    return json.dumps(data, ensure_ascii=False, indent=None)


def get_textmessage_event():
    with open('./tests/sample_events/text_message.json', 'r') as f:
        return json.load(f)


def get_imagemessage_event():
    with open('./tests/sample_events/image_message.json', 'r') as f:
        return json.load(f)


def get_postback_event():
    with open('./tests/sample_events/postback.json', 'r') as f:
        return json.load(f)


def get_locationmessage_event():
    with open('./tests/sample_events/location_message.json', 'r') as f:
        return json.load(f)


def get_follow_event():
    with open('./tests/sample_events/follow_event.json', 'r') as f:
        return json.load(f)


class DummyLogger():
    """Dummy Logger
    """
    def logLineChannelEvent(self, eventData):
        print("logLineChannelEvent", eventData)

    def logLineBotMessage(self, eventData):
        print("logLineBotMessage", eventData)
    
    def logErrorEvent(self, eventData):
        print("logErrorEvent", eventData)