import pytest
from unittest.mock import patch, MagicMock
from scenario.spot_scenario.spot_scenario_manager import SpotScenarioManager
from packages.services.scenario.spot.services.chat_spot_list_service import ChatSpotListService

@pytest.fixture
def spot_scenario_manager():
    line_bot_api_mock = MagicMock()
    return SpotScenarioManager(line_bot_api_mock)

@patch.object(ChatSpotListService, 'get_spot_list_by_spot_list_id_for_line')
@patch('scenario.spot_scenario.spot_scenario_manager.flex_message.generate_carousel')
def test_handle_spot_list_event(mock_generate_carousel, mock_get_spot_list, spot_scenario_manager):
    # Mock dữ liệu trả về từ ChatSpotListService
    mock_get_spot_list.return_value = [
        {"spot_id": "123", "name": "Spot A"},
        {"spot_id": "456", "name": "Spot B"},
    ]
    
    # Mock flex_message.generate_carousel
    mock_generate_carousel.return_value = ["mocked_flex_message"]

    # D<PERSON> liệu đầu vào
    # event_constants = {"display_name": "Test Display"}
    event_constants = "spot_list"
    event_data = "spotGroup_1de1079a-95e2-47db-b84d-7bf06005a24a#spotList#spotList_bb0903e7-86bc-45ee-acc0-f6406093ec03"

    # Gọi function cần test
    response = spot_scenario_manager.__handle__spot__list__event(event_constants, event_data)

    # Kiểm tra kết quả
    assert response == ["mocked_flex_message"]

    # Đảm bảo mock được gọi đúng
    mock_get_spot_list.assert_called_once_with("group1", "list1")
    mock_generate_carousel.assert_called_once()
