# Chatbot service

```
├── api      # API server for scenario lated function in admin_web
├── chatbot  # Chatbot server
├── log      # Common logging service to store logs in DynamoDB
├── tests    # Integration test codes
├── forward  # LINE webhook forward server
└── webhook  # LINE webhook receive server
```

## For developers

First install for development.

```
$ pip install -r requirements-dev.txt
```

### Commands

To run all test, use:

```
$ tox
```

To run lint and format, use:

```
$ tox -e lint
```

# pip install all sub folders

```shell
$ which pip
# printed: /Users/<USER>/.pyenv/versions/3.11.2/bin/pip

$ cd /Users/<USER>/work/line-oss/packages/services/scenario

$ find . -name "requirements-dev.txt" -exec /Users/<USER>/.pyenv/versions/3.11.2/bin/pip install -r {} \;
$ find . -name "requirements.txt" -exec /Users/<USER>/.pyenv/versions/3.11.2/bin/pip install -r {} \;

$ cd /Users/<USER>/work/line-oss/packages/services/bosai
$ find . -name "requirements-dev.txt" -exec /Users/<USER>/.pyenv/versions/3.11.2/bin/pip install -r {} \;
$ find . -name "requirements.txt" -exec /Users/<USER>/.pyenv/versions/3.11.2/bin/pip install -r {} \;
```

# upgrade Pillow

```shell
# remove old version
$ rm -rf /Users/<USER>/work/line-oss/packages/services/scenario/api/PIL
$ rm -rf /Users/<USER>/work/line-oss/packages/services/scenario/api/Pillow.libs

# install new version
$ pip install Pillow==11.0.0 -t /Users/<USER>/work/line-oss/packages/services/scenario/api/ \
    --platform manylinux2014_x86_64 --python-version 3.11 \
    --implementation cp --only-binary=:all:

# installed folder:
# packages/services/scenario/api/PIL
# packages/services/scenario/api/pillow-11.0.0.dist-info
# packages/services/scenario/api/pillow.libs
```
