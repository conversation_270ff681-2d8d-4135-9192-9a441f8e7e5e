"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import json

from common import assert_response

from fixtures.fixture_db_table_scenario import (
    api_resource_table_scenario,
    table_scenario_insert_default_data,
    invoke_method_table_scenario
)

def test_table_scenario_scan(table_scenario_insert_default_data, invoke_method_table_scenario):
    """Scan test
    """
    response = invoke_method_table_scenario('scan', 'GET')

    body = assert_response(response)

    assert body.get("items")

    item_ids = [item.get("scenarioId") for item in body.get("items")]

    assert "test-scenario" in item_ids

def test_table_scenario_get_item(table_scenario_insert_default_data, invoke_method_table_scenario):
    """Get item test
    """
    response = invoke_method_table_scenario(
        'getItem',
        'GET',
        query='?scenarioId=test-scenario'
    )

    body = assert_response(response)
    item = body.get("item")

    assert item
    assert item.get("scenarioId") == 'test-scenario'

    versions = item.get("versions")

    assert versions
    assert len(versions.keys()) == 3

def test_table_scenario_put_item(table_scenario_insert_default_data):
    """Put item test.
    The put item endpoint is already called by the default data fixture,
    so we just check the response.
    """
    for response in table_scenario_insert_default_data:
        assert_response(response)

def test_table_scenario_update_item(table_scenario_insert_default_data, invoke_method_table_scenario):
    """Update item test.
    """
    update_item_body = {}
    update_item_body['scenarioId'] = 'test-scenario'
    update_item_body['updateExpression'] = ("SET #versions.#version_1.#lang = :lang_codes "
                                            "REMOVE #versions.#version_2")

    update_item_body['expressionNames'] = {
        '#versions' : 'versions',
        '#version_1' : 'version_1',
        '#version_2' : 'version_2',
        '#lang' : 'languages'
    }

    update_item_body['expressionValues'] = {
        ':lang_codes' : ['en', 'pt-br']
    }

    response = invoke_method_table_scenario(
        'updateItem',
        'POST',
        headers={
            'Content-Type' : 'application/json'
        },
        body=json.dumps(update_item_body)
    )

    response_body = assert_response(response)

    assert response_body.get("response")

    # Do a get operation to confirm that the items were updated
    response = invoke_method_table_scenario(
        'getItem',
        'GET',
        query='?scenarioId=test-scenario'
    )

    response_body = assert_response(response)
    item = response_body.get("item")

    assert item

    langs = item.get("versions").get("version_1").get("languages")
    assert langs == ['en', 'pt-br']
    assert not item.get("versions").get("version_2") # Verify deletion via update

def test_table_scenario_delete_item(table_scenario_insert_default_data, invoke_method_table_scenario):
    """Delete item test
    """
    response = invoke_method_table_scenario(
        'deleteItem',
        'DELETE',
        query='?scenarioId=test-scenario'
    )

    body = assert_response(response)

    # Confirm deletion by trying to retrieve the item
    response = invoke_method_table_scenario(
        'getItem',
        'GET',
        query='?scenarioId=test-scenario'
    )

    body = assert_response(response)
    assert not body.get("item")
