"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import pytest

from fixtures import api_gateway

@pytest.fixture(scope="session")
def api_resource_list():
    return api_gateway.get_api_resources()

@pytest.fixture(scope="session")
def api_resource_create_presigned_url(api_resource_list):
    """Fixture that returns the resource ID for the
    create presigned s3 bucket URL endpoint.
    """
    api_path = "/scenario/api/presignedURL"

    resource_id = api_gateway.get_resource_id_from_path(
        api_resource_list, api_path
    )

    return resource_id, api_path

@pytest.fixture(scope="session")
def api_resource_table_scenario_chatbot_data(api_resource_list):
    """Fixture that returns the resource ID for the
    scenario table DB API endpoint and the base
    URL path to call it.
    """
    api_path = "/scenario/api/db/scenarioData/{proxy+}"

    resource_id = api_gateway.get_resource_id_from_path(
        api_resource_list, api_path
    )

    return resource_id, api_path

@pytest.fixture(scope="module")
def invoke_method_table_scenario_chatbot_data(api_resource_table_scenario_chatbot_data):
    def _invoke_method(path_part, http_method, query='', headers={}, body=''):
        resource_id = api_resource_table_scenario_chatbot_data[0]
        path_query = api_resource_table_scenario_chatbot_data[1]

        path_query = path_query.replace('{proxy+}', path_part) + query

        response = api_gateway.api_gw_client.test_invoke_method(
            restApiId=api_gateway.REST_API_ID,
            resourceId=resource_id,
            httpMethod=http_method,
            pathWithQueryString=path_query,
            body=body,
            headers=headers
        )

        return response

    return _invoke_method
