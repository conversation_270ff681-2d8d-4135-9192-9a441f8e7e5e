"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import json
from os import path

from common import assert_response

from fixtures.fixture_db_table_scenario_chatbot_data import (
    table_scenario_chatbot_data_insert_default,
    table_scenario_chatbot_data_batchwrite_default
)

# def test_table_scenario_chatbot_data_scan(table_scenario_chatbot_data_insert_default, invoke_method_table_scenario_chatbot_data):
#     """Scan test
#     """
#     response = invoke_method_table_scenario_chatbot_data('scan', 'GET')
#
#     body = assert_response(response)
#
#     assert body.get("items")
#
#     item_ids = [item.get("dataId") for item in body.get("items")]
#
#     assert "test-text-message" in item_ids

def test_table_scenario_chatbot_data_get_item(table_scenario_chatbot_data_insert_default, invoke_method_table_scenario_chatbot_data):
    """Get item test
    """
    response = invoke_method_table_scenario_chatbot_data(
        'getItem',
        'GET',
        query='?scenario=test-scenario&dataId=test-text-message'
    )

    body = assert_response(response)
    item = body.get("item")

    assert item
    assert item.get("scenario") == 'test-scenario'
    assert item.get("dataId") == "test-text-message"
    assert item.get("dataType") == "text"
    assert item.get("params").get("text")

def test_table_scenario_chatbot_data_put_item(table_scenario_chatbot_data_insert_default):
    """Put item test.
    The put item endpoint is already called by the default data fixture,
    so we just check the response.
    """
    for response in table_scenario_chatbot_data_insert_default:
        assert_response(response)

def test_table_scenario_chatbot_data_update_item(table_scenario_chatbot_data_insert_default, invoke_method_table_scenario_chatbot_data):
    """Update item test.
    """
    update_item_body = {}
    update_item_body['scenario'] = 'test-scenario'
    update_item_body['dataId'] = 'test-text-message'
    update_item_body['updateExpression'] = ("SET #data_type = :new_data_type, "
                                            "#params.#text = :new_text")

    update_item_body['expressionNames'] = {
        '#data_type' : 'dataType',
        '#params' : 'params',
        '#text' : 'text'
    }

    update_item_body['expressionValues'] = {
        ':new_data_type' : 'text_2',
        ':new_text': 'Text updated!'
    }

    response = invoke_method_table_scenario_chatbot_data(
        'updateItem',
        'POST',
        headers={
            'Content-Type' : 'application/json'
        },
        body=json.dumps(update_item_body)
    )

    response_body = assert_response(response)

    assert response_body.get("response")

    # Do a get operation to confirm that the items were updated
    response = invoke_method_table_scenario_chatbot_data(
        'getItem',
        'GET',
        query='?scenario=test-scenario&dataId=test-text-message'
    )

    response_body = assert_response(response)
    item = response_body.get("item")

    assert item
    assert item.get("dataType") == 'text_2'
    assert item.get("params").get("text") == 'Text updated!'

def test_table_scenario_chatbot_data_query(table_scenario_chatbot_data_insert_default, invoke_method_table_scenario_chatbot_data):
    """Query test.
    """
    query_body = {}
    query_body["keyExpression"] = "scenario = :value_scenario"
    query_body["expressionValues"] = {
        ':value_scenario': 'test-scenario'
    }

    response = invoke_method_table_scenario_chatbot_data(
        'query',
        'POST',
        headers={
            'Content-Type' : 'application/json'
        },
        body=json.dumps(query_body)
    )

    response_body = assert_response(response)

    assert response_body.get("items")

    item_ids = [item.get("dataId") for item in response_body.get("items")]

    assert "test-text-message" in item_ids
    assert "test-text-message-2" in item_ids
    assert "test-text-message-3" in item_ids



def test_table_scenario_chatbot_data_delete_item(table_scenario_chatbot_data_insert_default, invoke_method_table_scenario_chatbot_data):
    """Delete item test
    """
    # Read item data from JSON
    basepath = path.dirname(__file__)
    filepath = path.abspath(path.join(
        basepath,
        "fixtures",
        "json_data",
        "table_scenario_chatbot_data.json"
    ))

    with open(filepath) as json_data:
        data_json = json.load(json_data)

    for item in data_json.values():
        response = invoke_method_table_scenario_chatbot_data(
            'deleteItem',
            'DELETE',
            query='?scenario=' + item.get("scenario") +\
                '&dataId=' + item.get("dataId")
        )

        body = assert_response(response)

        # Confirm deletion by trying to retrieve the item
        response = invoke_method_table_scenario_chatbot_data(
            'getItem',
            'GET',
            query='?scenario=test-scenario&dataId=test-text-message'
        )

        body = assert_response(response)
        assert not body.get("item")

def test_table_scenario_chatbot_data_batchwrite_item(table_scenario_chatbot_data_batchwrite_default):
    """Batchwrite item test
    """
    for response in table_scenario_chatbot_data_batchwrite_default:
        assert_response(response)
