"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import json

from common import assert_response

from fixtures.fixture_damage_report import (
    api_gateway,
    api_resource_create_damage_report_scenario
)

def test_add_damage_report(api_resource_create_damage_report_scenario):
    # Invoke add damage report endpoint
    resource_id = api_resource_create_damage_report_scenario[0]
    endpoint_path = api_resource_create_damage_report_scenario[1]

    body = {
        'scenario': 'test-scenario',
        'version': 'version-test'
    }

    import_response = api_gateway.api_gw_client.test_invoke_method(
        restApiId=api_gateway.REST_API_ID,
        resourceId=resource_id,
        httpMethod='POST',
        pathWithQueryString=endpoint_path,
        body=json.dumps(body),
        headers={
            'Content-Type': 'application/json'
        }
    )

    import_response_body = assert_response(import_response, success_code='SUCCESS')

    assert import_response_body.get('status_code') == 200
    assert not import_response_body.get('exception')
