"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import json

import pytest

from common import assert_response
from fixtures import api_gateway

@pytest.fixture(scope="module")
def api_resource_create_damage_report_scenario(api_resource_list, invoke_method_table_scenario_chatbot_data):
    """Fixture that returns the resource ID for
    creating the damage report scenario.
    """
    api_path = "/scenario/api/addDamageReport"

    resource_id = api_gateway.get_resource_id_from_path(
        api_resource_list, api_path
    )

    yield resource_id, api_path

    # Retrieve damage report data and delete it from the DB
    query_body = {}
    query_body["keyExpression"] = "scenario = :value_scenario"
    query_body["expressionValues"] = {
        ':value_scenario': 'test-scenario#version-test'
    }

    query_response = invoke_method_table_scenario_chatbot_data(
        'query',
        'POST',
        headers={
            'Content-Type' : 'application/json'
        },
        body=json.dumps(query_body)
    )

    response_body = assert_response(query_response)

    damage_report_items = response_body.get("items")

    assert len(damage_report_items) > 0

    # Delete inserted items
    del_request_body = {}
    del_request_body['deleteItems'] = []

    for item in damage_report_items:
        del_request_body['deleteItems'].append(
            {
                "scenario": item.get("scenario"),
                "dataId": item.get("dataId")
            }
        )

    invoke_method_table_scenario_chatbot_data(
        'batchWriteItem',
        'POST',
        headers={
            'Content-Type' : 'application/json'
        },
        body=json.dumps(del_request_body)
    )
