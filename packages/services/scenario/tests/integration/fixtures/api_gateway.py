"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import os

import boto3

api_gw_client = boto3.client("apigateway", region_name="ap-northeast-1")

REST_API_ID = os.environ['CHATBOT_TEST_REST_API_ID']

def get_api_resources():
    response = api_gw_client.get_resources(
        restApiId=REST_API_ID,
        limit=100
    )

    return response.get('items')

def get_resource_id_from_path(resource_list, path):
    api_resource = None

    for resource in resource_list:
        if resource.get("resourceMethods") and\
        resource.get("path") == path:
            api_resource = resource
            break

    resource_id = None

    if api_resource:
        resource_id = api_resource.get('id')

    return resource_id
