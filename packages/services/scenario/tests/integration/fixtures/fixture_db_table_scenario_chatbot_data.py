"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
from os import path
import json

import pytest

from fixtures import api_gateway

@pytest.fixture(scope="module")
def table_scenario_chatbot_data_insert_default(request, api_resource_table_scenario_chatbot_data):
    """Initializes the scenario table with test data.
    """
    # Read item data from JSON
    basepath = path.dirname(__file__)
    filepath = path.abspath(path.join(basepath, "json_data", "table_scenario_chatbot_data.json"))

    with open(filepath) as json_data:
        data_json = json.load(json_data)

    # Use put operation from the endpoint
    resource_id = api_resource_table_scenario_chatbot_data[0]
    endpoint_path = api_resource_table_scenario_chatbot_data[1].replace('{proxy+}', 'putItem')
    headers = {}
    headers['Content-Type'] = 'application/json'

    responses = []

    for item in data_json.values():
        responses.append(
            api_gateway.api_gw_client.test_invoke_method(
                restApiId=api_gateway.REST_API_ID,
                resourceId=resource_id,
                httpMethod='POST',
                pathWithQueryString=endpoint_path,
                body=json.dumps(item),
                headers=headers
            )
        )

    if not request.node.name.startswith("test_table_scenario_chatbot_data_delete_item"):
        yield responses

        # Teardown
        # Delete inserted items
        endpoint_path = api_resource_table_scenario_chatbot_data[1].replace(
            '{proxy+}',
            'deleteItem'
        )

        for item in data_json.values():
            # Delete operation requires a query parameter
            path_query = endpoint_path + "?scenario=" + item.get("scenario") +\
                "&dataId=" + item.get("dataId")

            api_gateway.api_gw_client.test_invoke_method(
                restApiId=api_gateway.REST_API_ID,
                resourceId=resource_id,
                httpMethod='DELETE',
                pathWithQueryString=path_query
            )
    else:
        return responses

@pytest.fixture(scope="module")
def table_scenario_chatbot_data_batchwrite_default(request, api_resource_table_scenario_chatbot_data):
    """Initializes the scenario table with test data.
    """
    # Read item data from JSON
    basepath = path.dirname(__file__)
    filepath = path.abspath(path.join(basepath, "json_data", "table_scenario_chatbot_data.json"))

    with open(filepath) as json_data:
        data_json = json.load(json_data)

    # Use put operation from the endpoint
    resource_id = api_resource_table_scenario_chatbot_data[0]
    endpoint_path = api_resource_table_scenario_chatbot_data[1].replace(
        '{proxy+}',
        'batchWriteItem'
    )
    headers = {}
    headers['Content-Type'] = 'application/json'

    responses = []

    request_body = {}
    request_body['putItems'] = list(data_json.values())

    # Use batchwrite to save multiple items.
    responses.append(
        api_gateway.api_gw_client.test_invoke_method(
            restApiId=api_gateway.REST_API_ID,
            resourceId=resource_id,
            httpMethod='POST',
            pathWithQueryString=endpoint_path,
            body=json.dumps(request_body),
            headers=headers
        )
    )

    # Delete inserted items
    del request_body['putItems']
    request_body['deleteItems'] = []

    for item in data_json.values():
        request_body['deleteItems'].append(
            {
                "scenario": item.get("scenario"),
                "dataId": item.get("dataId")
            }
        )

    responses.append(
        api_gateway.api_gw_client.test_invoke_method(
            restApiId=api_gateway.REST_API_ID,
            resourceId=resource_id,
            httpMethod='POST',
            pathWithQueryString=endpoint_path,
            body=json.dumps(request_body),
            headers=headers
        )
    )

    return responses
