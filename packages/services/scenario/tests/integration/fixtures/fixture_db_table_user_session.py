"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
from os import path
import json

import pytest

from fixtures import api_gateway

@pytest.fixture(scope="session")
def api_resource_table_user_session(api_resource_list):
    """Fixture that returns the resource ID for the
    user session table DB API endpoint and the base
    URL path to call it.
    """
    api_path = "/scenario/api/db/userSession/{proxy+}"

    resource_id = api_gateway.get_resource_id_from_path(
        api_resource_list, api_path
    )

    return resource_id, api_path

@pytest.fixture(scope="module")
def table_user_session_insert_default_data(request, api_resource_table_user_session):
    """Initializes the user session table with test data.
    """
    # Read item data from JSON
    basepath = path.dirname(__file__)
    filepath = path.abspath(path.join(basepath, "json_data", "table_user_session.json"))

    with open(filepath) as json_data:
        data_json = json.load(json_data)

    # Use put operation from the endpoint
    resource_id = api_resource_table_user_session[0]
    endpoint_path = api_resource_table_user_session[1].replace('{proxy+}', 'putItem')
    headers = {}
    headers['Content-Type'] = 'application/json'

    responses = []

    for item in data_json.values():
        responses.append(
            api_gateway.api_gw_client.test_invoke_method(
                restApiId=api_gateway.REST_API_ID,
                resourceId=resource_id,
                httpMethod='POST',
                pathWithQueryString=endpoint_path,
                body=json.dumps(item),
                headers=headers
            )
        )

    if not request.node.name.startswith("test_table_user_session_delete_item"):
        yield responses

        # Teardown
        # Delete inserted items
        endpoint_path = api_resource_table_user_session[1].replace('{proxy+}', 'deleteItem')

        for item in data_json.values():
            # Delete operation requires a query parameter
            path_query = endpoint_path + "?userId=" +\
                item.get("userId") + "&environment=" + item.get("environment")

            api_gateway.api_gw_client.test_invoke_method(
                restApiId=api_gateway.REST_API_ID,
                resourceId=resource_id,
                httpMethod='DELETE',
                pathWithQueryString=path_query
            )
    else:
        return responses


@pytest.fixture(scope="module")
def invoke_method_table_user_session(api_resource_table_user_session):
    def _invoke_method(path_part, http_method, query='', headers={}, body=''):
        resource_id = api_resource_table_user_session[0]
        path_query = api_resource_table_user_session[1]

        path_query = path_query.replace('{proxy+}', path_part) + query

        response = api_gateway.api_gw_client.test_invoke_method(
            restApiId=api_gateway.REST_API_ID,
            resourceId=resource_id,
            httpMethod=http_method,
            pathWithQueryString=path_query,
            body=body,
            headers=headers
        )

        return response

    return _invoke_method
