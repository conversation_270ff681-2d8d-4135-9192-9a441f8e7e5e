"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""

import os
import pytest
from forward.app import check_forward_params

cases = [
    (
        {},
        "",
        {
            "error": "url is empty",
            "url": None,
            "signature": None,
            "body": None,
            "resolved_ip_addr": None
        }
    ),
    (
        {
            "signature": ""
        },
        "https://example.com/messaging",
        {
            "error": "signature is empty",
            "url": "https://example.com/messaging",
            "signature": None,
            "body": None,
            "resolved_ip_addr": None
        }
    ),
    (
        {
            "signature": "dummy_signature",
            "body": "",
        },
        "https://example.com/messaging",
        {
            "error": "body is empty",
            "url": "https://example.com/messaging",
            "signature": "dummy_signature",
            "body": None,
            "resolved_ip_addr": None
        }
    ),
    (
        {
            "signature": "dummy_signature",
            "body": "",
        },
        "https://example.com/messaging",
        {
            "error": "body is empty",
            "url": "https://example.com/messaging",
            "signature": "dummy_signature",
            "body": None,
            "resolved_ip_addr": None
        }
    ),
    (
        {
            "signature": "dummy_signature",
            "body": "dummy_body",
        },
        "https://example.com/messaging",
        {
            "error": None,
            "url": "https://example.com/messaging",
            "signature": "dummy_signature",
            "body": "dummy_body",
            "resolved_ip_addr": "dummy_ip_addr"
        }
    ),
    (
        {
            "signature": "dummy_signature",
            "body": "dummy_body",
        },
        f"https://{os.environ['CLASS_A_DOMAIN']}/messaging",
        {
            "error": "invalid host ip range",
            "url": f"https://{os.environ['CLASS_A_DOMAIN']}/messaging",
            "signature": "dummy_signature",
            "body": "dummy_body",
            "resolved_ip_addr": os.environ["CLASS_A_IP_ADDR"]
        }
    ),
    (
        {
            "signature": "dummy_signature",
            "body": "dummy_body",
        },
        f"https://{os.environ['CLASS_B_DOMAIN']}/messaging",
        {
            "error": "invalid host ip range",
            "url": f"https://{os.environ['CLASS_B_DOMAIN']}/messaging",
            "signature": "dummy_signature",
            "body": "dummy_body",
            "resolved_ip_addr": os.environ["CLASS_B_IP_ADDR"]
        }
    ),
    (
        {
            "signature": "dummy_signature",
            "body": "dummy_body",
        },
        f"https://{os.environ['CLASS_C_DOMAIN']}/messaging",
        {
            "error": "invalid host ip range",
            "url": f"https://{os.environ['CLASS_C_DOMAIN']}/messaging",
            "signature": "dummy_signature",
            "body": "dummy_body",
            "resolved_ip_addr": os.environ["CLASS_C_IP_ADDR"]
        }
    ),
]

@pytest.fixture
def init_test_check_forward_params():
    assert os.environ["CLASS_A_DOMAIN"] != ""
    assert os.environ["CLASS_A_IP_ADDR"] != ""
    assert os.environ["CLASS_B_DOMAIN"] != ""
    assert os.environ["CLASS_B_IP_ADDR"] != ""
    assert os.environ["CLASS_C_DOMAIN"] != ""
    assert os.environ["CLASS_C_IP_ADDR"] != ""


@pytest.mark.parametrize('event, url, expected', cases)
def test_check_forward_params(event, url, expected):
    result = check_forward_params(event, url)
    if not result["error"] and result["resolved_ip_addr"]:
        # dns lookup success, but example.com's ip address is unknown. we must be fallback to dummy value.
        result["resolved_ip_addr"] = "dummy_ip_addr"

    assert result == expected
