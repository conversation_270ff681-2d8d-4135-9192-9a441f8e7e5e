"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""

from ipaddress import ip_address
import logging
import os
import requests
from requests.exceptions import Timeout
from socket import gethostbyname
import traceback
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

def lambda_handler(event, context):
    """Lambda handler function.

    :param event: Lambda call event
    :param context: Lambda execution context
    """

    url = os.environ.get("CHATBOT_FORWARD_URL")
    params = check_forward_params(event, url)
    if params["error"]:
        logger.error(
            "%s, url: %s, resolved_ip_addr: %s, signature: %s, body: %s",
            params["error"],
            params["url"],
            params["resolved_ip_addr"],
            params["signature"],
            params["body"]
        )
        return

    headers = {
        "Content-Type": "application/json",
        "User-Agent": "GovtechWebhook",
        "X-Line-Signature": params["signature"],
    }

    try:
        r = requests.post(url=params["url"], headers=headers, data=params["body"].encode("utf-8"), timeout=10, allow_redirects=False)
        if not r.ok:
            r.raise_for_status()
    except Timeout:
        logger.warn("forward timeout, url: %s, signature: %s, body: %s", params["url"], params["signature"], params["body"])
    except:
        logger.error(
            "forward error, url: %s, signature: %s, body: %s, error: %s",
            params["url"],
            params["signature"],
            params["body"],
            traceback.print_exc()
        )

def check_forward_params(event, url):
    result = {
        "error": None,
        "url": None,
        "signature": None,
        "body": None,
        "resolved_ip_addr": None,
    }

    if not url:
        result["error"] = "url is empty"
        return result
    result["url"] = url

    signature = event.get("signature")
    if not signature:
        result["error"] = "signature is empty"
        return result
    result["signature"] = signature

    body = event.get("body")
    if not body:
        result["error"] = "body is empty"
        return result
    result["body"] = body

    # lookup dns and check host ip range
    try:
        resolved_ip_addr = gethostbyname(urlparse(url).netloc)
        result["resolved_ip_addr"] = resolved_ip_addr
        if not ip_address(resolved_ip_addr).is_global:
            result["error"] = "invalid host ip range"
    except:
        result["error"] = "Name resolution failed"

    return result
