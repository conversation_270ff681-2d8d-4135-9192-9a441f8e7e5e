# LINE webhook forward server

## For developers

### Test

To run all test, use:

```
$ [...environment variables] tox

ex)
$ env \
  CLASS_A_DOMAIN=********.unit-test.line-smartcity.com \
  CLASS_A_IP_ADDR=******** \
  CLASS_B_DOMAIN=**********.unit-test.line-smartcity.com \
  CLASS_B_IP_ADDR=********** \
  CLASS_C_DOMAIN=***********.unit-test.line-smartcity.com \
  CLASS_C_IP_ADDR=*********** \
    tox
```

* environment variables
    * CLASS_A_DOMAIN: The domain that returns class A local network address range.
    * CLASS_A_IP_ADDR: The IP address which above domain returns.
    * CLASS_B_DOMAIN: The domain that returns class B local network address range.
    * CLASS_B_IP_ADDR: The IP address which above domain returns.
    * CLASS_C_DOMAIN: The domain that returns class C local network address range.
    * CLASS_C_IP_ADDR: The IP address which above domain returns.

