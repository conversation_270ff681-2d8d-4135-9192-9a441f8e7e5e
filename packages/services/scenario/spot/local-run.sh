#!/bin/sh

# TODO it does not work yet..

readonly DIRNAME="$( cd $(dirname $0) >/dev/null 2>&1 ; pwd -P )"
TABLE_NAME=pnl-stg-spot-scenario-static-TableScenarioSpotData-B50WVRFKRZ4A
CONTAINER_NAME=lsc-spot-dynamodb-local
NETWORK_NAME=lsc-spot-local

docker network inspect $NETWORK_NAME || docker network create $NETWORK_NAME
docker container inspect $CONTAINER_NAME || docker run -d --rm --name $CONTAINER_NAME \
  -v `pwd`/var/dynamodblocal:/home/<USER>/data \
  --network $NETWORK_NAME \
  -p 8000:8000 \
  amazon/dynamodb-local -jar DynamoDBLocal.jar -sharedDb -dbPath ./data

table_count=$(aws dynamodb list-tables --profile pnl-lsc-stg | jq -r .TableNames | grep $TABLE_NAME | wc -l | xargs)
if [[ table_count -eq 0 ]]; then
  echo "Table $TABLE_NAME not found. Creating..."
  aws dynamodb create-table \
    --billing-mode PAY_PER_REQUEST \
    --table-name $TABLE_NAME \
    --attribute-definitions AttributeName=partitionKey,AttributeType=S AttributeName=sortKey,AttributeType=S AttributeName=latLngGeohash,AttributeType=S \
    --key-schema AttributeName=partitionKey,KeyType=HASH AttributeName=sortKey,KeyType=RANGE \
    --global-secondary-indexes "IndexName=partitionKey-latLngGeohash-index,KeySchema=[{AttributeName=partitionKey,KeyType=HASH},{AttributeName=latLngGeohash,KeyType=RANGE}],Projection={ProjectionType=ALL}" \
    --endpoint-url http://localhost:8000
fi

sam local start-api --container-env-vars $DIRNAME/docs/envs.json \
   -n $DIRNAME/docs/envs.json \
   --docker-network $NETWORK_NAME \
   -p 3002
