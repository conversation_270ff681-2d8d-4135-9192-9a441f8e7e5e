"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import re
from typing import Optional


class StringUtil():
    def isNullEmptyOrBlankWithFullWidth(self, text):
        """check text is empty or blank with Full-width Space
        """
        if text is None:
            return True
        else:
            text = re.sub(r"[\u3000 \t]", "", str(text))
            return len(text) == 0

    @staticmethod
    def is_blank(text: Optional[str]) -> bool:
        return text is None or len(text.strip()) == 0
