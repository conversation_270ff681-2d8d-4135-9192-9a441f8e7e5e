"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""

import os
from pathlib import Path

from utils.s3_util import S3_RESOURCE

from constants.s3_resource import S3_BUCKET_RESOURCES


class FileUtil():

    def get_current_path_of_project(self):
        """ Get current path of project

        Returns:
            str: current path of project
        """
        dir_path = os.path.dirname(os.path.realpath(__file__))
        path = Path(dir_path)
        return path.parent

    def delete_spot_file(self, spotGroupId, file_path):
        """ Delete file from S3 bucket

        Args:
            spotGroupId (str): spotGroupId
            file_path (str): file path

        Returns:
            json:  response from S3
        """

        s3_client = S3_RESOURCE(S3_BUCKET_RESOURCES)
        file_name = os.path.basename(file_path)  # spot_image.jpeg
        s3_bucket_key = os.path.join(
            'resources/spot-images',  spotGroupId, file_name)
        # file exists
        if s3_client.is_folder_exists(s3_bucket_key):
            res = s3_client.delete_file(s3_bucket_key)
            return 'Contents' in res
        return None

    def delete_spot_default_image(self, spotGroupId):
        """ Delete default spot image file default.jpg from S3 bucket

        Args:
            spotGroupId (str): spotGroupId

        Returns:
            json:  response from S3
        """
        return self.delete_spot_file(spotGroupId, "default.jpg")
