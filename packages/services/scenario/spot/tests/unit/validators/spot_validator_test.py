import pytest

from validators.spot_validator import SpotValidator
from common.exceptions import SpotValidationError
from tests.unit.services.spot_fixtures import (
    SPOT_REQUEST, SPOT_ID, MOCK_UUID, MOCK_USER, MOCK_TIME
)

class TestSpotValidator():
    @pytest.fixture
    def validator(self):
        validator = SpotValidator()
        return validator

    @pytest.fixture(params=[
        {
            "input": {
                "partitionKey": 'spot#{}'.format(SPOT_REQUEST['spotGroupId']),
                "sortKey": SPOT_ID,
                "latitude": 30.0,
                "longitude": 130.0,
                "latLngGeohash": "wvigg1",
            },
            "result": True
        },
        {
            "input": {
                "rowIndex": 2,
                "partitionKey": 'spot#{}'.format(SPOT_REQUEST['spotGroupId']),
                "sortKey": SPOT_ID,
                "latitude": 30.0,
                "longitude": 130.0,
                "latLngGeohash": "wvigg1",
                "createdBy": MOCK_USER,
                "createdAt": MOCK_TIME,
                "image": "001_.jpg",
                "csvSource": "key/group1.csv",
                "attributes": [{"id": "attribute_00001", "value": "いけす居食家大徳利"}, {"id": "attribute_00002", "value": "平戸市木引田町429"}],
                "tags": ["食事", "お酒"],
            },
            "result": True
        },
        {
            "input": {
                "rowIndex": 3,
                "partitionKey": 'spot#{}'.format(SPOT_REQUEST['spotGroupId']),
                "sortKey": SPOT_ID,
                "latLngGeohash": "wvigg1",
            },
            "result": False
        },
        {
            "input": {
                "rowIndex": 10,
                "partitionKey": 'spot#{}'.format(SPOT_REQUEST['spotGroupId']),
                "sortKey": SPOT_ID,
                "latitude": 30.0,
                "longitude": 130.0,
                "latLngGeohash": "wvigg1",
                "tags": "invalid tag",
            },
            "result": False
        },
        {
            "input": {
                "rowIndex": 20,
                "partitionKey": 'spot#{}'.format(SPOT_REQUEST['spotGroupId']),
                "sortKey": SPOT_ID,
                "latitude": 30.0,
                "longitude": 130.0,
                "latLngGeohash": "wvigg1",
                "image": "001_.jpg",
                "csvSource": "key/group1.csv",
                # Case when there're invalid attributes
                "attributes": [{"id": 1}, {"id": "attribute_00002", "value": "平戸市木引田町429"}],
                "tags": ["食事", "お酒"],
            },
            "result": False
        },
        {
            "input": {
                "rowIndex": 20,
                "partitionKey": 'spot#{}'.format(SPOT_REQUEST['spotGroupId']),
                "sortKey": SPOT_ID,
                "latitude": 30.0,
                "longitude": 130.0,
                "latLngGeohash": "wvigg1",
                "image": "001_.jpg",
                "csvSource": "key/group1.csv",
                # Case when some attributes are missing
                "attributes": [{"id": "attribute_00001"}, {"id": "attribute_00002"}],
                "tags": ["食事", "お酒"],
            },
            "result": False
        }
    ])
    def validation_cases(self, request):
        return request.param

    def test_validate_items(self, validator: SpotValidator, validation_cases):
        input_item = validation_cases['input']
        if validation_cases['result'] is True:
            assert validator.item_validated(input_item)
        else:
            with pytest.raises(SpotValidationError):
                validator.item_validated(input_item)
