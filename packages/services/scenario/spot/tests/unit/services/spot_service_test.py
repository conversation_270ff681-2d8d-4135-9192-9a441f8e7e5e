import io
from flask import Flask
from http import HTT<PERSON>tatus
from pynamodb.exceptions import DoesNotExist, PutError
import pytest
import unittest.mock as mock
from typing import Dict

from services.spot_service import SpotService
from .spot_fixtures import (
    S3Mock,
    MOCK_TIME, MOCK_USER, MOCK_GEOHASH, MOCK_UUID, SPOT_ID, SPOT_REQUEST
)
from tests.unit.utils import MockModel, MockIterator
from common.exceptions import SpotValidationError
from common.message import Message, MessageCode

MOCK_TIME_SECOND = int(MOCK_TIME / 1000)

def does_not_exist(*args, **kwargs):
    raise DoesNotExist()

def mock_func(*args, **kwargs):
    return True

def save_error(*args, **kwargs):
    raise PutError("save error")


class TestSpotService():
    @pytest.fixture
    def req(self):
        spot_request = SPOT_REQUEST
        if 'csvSource' in spot_request:
            spot_request.pop('csvSource')
        return spot_request

    # List spot
    def test_list_spot_ok(self, mocker: mock, req: Dict, dicts_are_same):
        model_item = {
            "hash_key": 'spot#{}'.format(req.get('spotGroupId')),
            "range_key": SPOT_ID,
            "latLngGeohash": MOCK_GEOHASH,
            "latitude": req.get("latitude"),
            "longitude": req.get("longitude"),
            "attributes": req.get("attributes"),
            "image": req.get("image"),
            "tags": req.get('tags'),
            "createdAt": MOCK_TIME,
            "createdBy": MOCK_USER,
        }
        mocker.patch('time.time', return_value=MOCK_TIME_SECOND)
        mocker.patch('models.spot_model.SpotModel.get', return_value=MockModel(hash_key='spot#{}'.format(req.get('spotGroupId')), range_key=SPOT_ID))
        mocker.patch('models.spot_model.SpotModel.as_dict', side_effect=lambda x: x)
        mocker.patch('models.spot_model.SpotModel.query',
            return_value=MockIterator([MockModel(**model_item), MockModel(**model_item),])
        )

        app = Flask(__name__)
        with app.app_context():
            service = SpotService()
            res = service.list_spots(req)
            assert dicts_are_same(
                res[0].get_json(),
                {"data": {
                    "spot": [model_item, model_item]
                }}
            )
            assert res[1] == HTTPStatus.OK

    # Get spot data by ID
    def test_get_spot_ok(self, mocker: mock, req: Dict, dicts_are_same):
        model_item = {
            "hash_key": 'spot#{}'.format(req.get('spotGroupId')),
            "range_key": SPOT_ID,
            "latLngGeohash": MOCK_GEOHASH,
            "latitude": req.get("latitude"),
            "longitude": req.get("longitude"),
            "attributes": req.get("attributes"),
            "image": req.get("image"),
            "tags": req.get('tags'),
            "createdAt": MOCK_TIME,
            "createdBy": MOCK_USER,
        }
        mocker.patch('time.time', return_value=MOCK_TIME_SECOND)
        mocker.patch('models.spot_model.SpotModel.get', return_value=MockModel(**model_item))
        mocker.patch('models.spot_model.SpotModel.as_dict', side_effect=lambda x: x)

        app = Flask(__name__)
        with app.app_context():
            service = SpotService()
            res = service.get_spot_by_id(SPOT_ID, req)
            assert dicts_are_same(
                res[0].get_json(),
                {"data": {
                    "spot": model_item
                }}
            )
            assert res[1] == HTTPStatus.OK

    # Add spot
    def test_add_spot_ok(self, mocker: mock, req: Dict, dicts_are_same):
        mocker.patch('time.time', return_value=MOCK_TIME_SECOND)
        mocker.patch('models.spot_model.SpotModel.get', does_not_exist)
        mocker.patch('models.spot_model.SpotModel.save', mock_func)
        mocker.patch('models.spot_model.TagSpotModel.get', does_not_exist)
        mocker.patch('models.spot_model.TagSpotModel.save', mock_func)
        mocker.patch('pygeohash.encode', return_value=MOCK_GEOHASH)

        app = Flask(__name__)
        with app.app_context():
            service = SpotService()
            res = service.add_spot(SPOT_ID, req)
            assert res[1] == HTTPStatus.OK
            assert dicts_are_same(
                res[0].get_json(),
                {"data": {"spot": {
                    "partitionKey": 'spot#{}'.format(req.get('spotGroupId')),
                    "sortKey": SPOT_ID,
                    "latLngGeohash": MOCK_GEOHASH,
                    "latitude": float(req.get('latitude')),
                    "longitude": float(req.get('longitude')),
                    "attributes": req.get('attributes'),
                    "tags": req.get('tags'),
                    "image": req.get('image'),
                    "createdAt": MOCK_TIME,
                    "updatedAt": MOCK_TIME,
                    "createdBy": MOCK_USER,
                    "updatedBy": MOCK_USER,
                }, "tag_spot_rel": [{
                    "partitionKey": 'tagSpotRelation#{}#{}'.format(req.get("spotGroupId"), tag),
                    "sortKey": SPOT_ID,
                } for tag in req.get('tags')]
                }}
            )

    def test_add_spot_err_already_exists(self, mocker: mock, req: Dict, dicts_are_same):
        mocker.patch('time.time', return_value=MOCK_TIME_SECOND)
        mocker.patch('models.spot_model.SpotModel.get', return_value=MockModel(hash_key='spot#{}'.format(req.get('spotGroupId')), range_key=SPOT_ID))
        mocker.patch('models.spot_model.SpotModel.save', mock_func)
        mocker.patch('models.spot_model.TagSpotModel.get', does_not_exist)
        mocker.patch('models.spot_model.TagSpotModel.save', mock_func)
        mocker.patch('pygeohash.encode', return_value=MOCK_GEOHASH)

        app = Flask(__name__)
        with app.app_context():
            service = SpotService()
            res = service.add_spot(SPOT_ID, req)
            errcode = MessageCode.SE_P_0001
            assert dicts_are_same(
                res[0].get_json(),
               {
                    "errorCode": errcode,
                    "errorMessage": Message().get_message(errcode).format('spot#{}'.format(req.get('spotGroupId')), SPOT_ID),
                }
            )
            assert res[1] == HTTPStatus.BAD_REQUEST

    def test_add_spot_err_fails_to_create(self, mocker: mock, req: Dict, dicts_are_same):
        mocker.patch('time.time', return_value=MOCK_TIME_SECOND)
        mocker.patch('models.spot_model.SpotModel.get', does_not_exist)
        mocker.patch('models.spot_model.SpotModel.save', save_error)
        mocker.patch('models.spot_model.TagSpotModel.get', does_not_exist)
        mocker.patch('models.spot_model.TagSpotModel.save', mock_func)
        mocker.patch('pygeohash.encode', return_value=MOCK_GEOHASH)

        app = Flask(__name__)
        with app.app_context():
            service = SpotService()
            res = service.add_spot(SPOT_ID, req)
            errcode = MessageCode.SE_P_0040
            assert dicts_are_same(
                res[0].get_json(),
                {
                    "errorCode": errcode,
                    "errorMessage": Message().get_message(errcode).format("save error"),
                }
            )
            assert res[1] == HTTPStatus.BAD_REQUEST

    # Update spot
    def test_update_spot_ok(self, mocker: mock, req: Dict, dicts_are_same):
        mocker.patch('time.time', return_value=MOCK_TIME_SECOND)
        mocker.patch('models.spot_model.SpotModel.get', return_value=MockModel(hash_key='spot#{}'.format(req.get('spotGroupId')), range_key=SPOT_ID, tags=req.get('tags')))
        mocker.patch('models.spot_model.SpotModel.save', mock_func)
        mocker.patch('models.spot_model.TagSpotModel.get', does_not_exist)
        mocker.patch('models.spot_model.TagSpotModel.save', mock_func)
        mocker.patch('pygeohash.encode', return_value=MOCK_GEOHASH)

        app = Flask(__name__)
        with app.app_context():
            tags_updating = ["神社"]
            service = SpotService()
            req.update({"tags": tags_updating})
            res = service.update_spot(SPOT_ID, req)
            assert dicts_are_same(
                res[0].get_json(),
                {"data": {"spot": {
                    "partitionKey": 'spot#{}'.format(req.get('spotGroupId')),
                    "sortKey": SPOT_ID,
                    "latLngGeohash": MOCK_GEOHASH,
                    "latitude": float(req.get('latitude')),
                    "longitude": float(req.get('longitude')),
                    "attributes": req.get('attributes'),
                    "tags": tags_updating,
                    "image": req.get('image'),
                    "updatedAt": MOCK_TIME,
                    "updatedBy": MOCK_USER,
                }, "tag_spot_rel": [{
                    "partitionKey": 'tagSpotRelation#{}#{}'.format(req.get("spotGroupId"), tag),
                    "sortKey": SPOT_ID,
                } for tag in tags_updating]
                }}
            )
            assert res[1] == HTTPStatus.OK

    def test_update_spot_err_not_exist(self, mocker: mock, req: Dict, dicts_are_same):
        mocker.patch('time.time', return_value=MOCK_TIME_SECOND)
        mocker.patch('models.spot_model.SpotModel.get', does_not_exist)
        mocker.patch('models.spot_model.SpotModel.save', mock_func)
        mocker.patch('models.spot_model.TagSpotModel.get', does_not_exist)
        mocker.patch('models.spot_model.TagSpotModel.save', mock_func)
        mocker.patch('pygeohash.encode', return_value=MOCK_GEOHASH)

        app = Flask(__name__)
        with app.app_context():
            service = SpotService()
            res = service.update_spot(SPOT_ID, req)
            errcode = MessageCode.SE_P_0002
            assert dicts_are_same(
                res[0].get_json(),
                {
                    "errorCode": errcode,
                    "errorMessage": Message().get_message(errcode).format('spot#{}'.format(req.get('spotGroupId')), SPOT_ID),
                }
            )
            assert res[1] == HTTPStatus.BAD_REQUEST

    # Save spot
    def test_successfully_saving_spot_item(self, mocker: mock, req: Dict, dicts_are_same):
        mocker.patch('time.time', return_value=MOCK_TIME/1000)
        mocker.patch('models.spot_model.SpotModel.save', mock_func)
        mocker.patch('pygeohash.encode', return_value=MOCK_GEOHASH)

        service = SpotService()
        pk = "spot#{}".format(req.get('spotGroupId'))
        req["partitionKey"] = pk
        req["sortKey"] = SPOT_ID
        req["tags"] = req.get('tags')
        req["latLngGeohash"] = MOCK_GEOHASH
        req["updatedAt"] = MOCK_TIME
        req["updatedBy"] = MOCK_USER
        req["createdBy"] = None
        result = service._save_spot(req)
        assert dicts_are_same(result, {
            "partitionKey": pk,
            "sortKey": SPOT_ID,
            "latLngGeohash": MOCK_GEOHASH,
            "latitude": float(req.get('latitude')),
            "longitude": float(req.get('longitude')),
            "attributes": req.get('attributes'),
            "tags": req.get('tags'),
            "image": req.get('image'),
            "updatedAt": MOCK_TIME,
            "updatedBy": MOCK_USER,
        })

    def test_raise_err_saving_spot_item(self, mocker: mock, req: Dict):
        mocker.patch('time.time', return_value=MOCK_TIME_SECOND)
        mocker.patch('models.spot_model.SpotModel.save', mock_func)
        mocker.patch('pygeohash.encode', return_value=MOCK_GEOHASH)

        service = SpotService()
        pk = "spot#{}".format(req.get('spotGroupId'))
        req["partitionKey"] = pk
        req["sortKey"] = SPOT_ID
        req["tags"] = "Invalid Tag Name"
        req["latLngGeohash"] = MOCK_GEOHASH
        req["updatedAt"] = MOCK_TIME
        req["updatedBy"] = MOCK_USER
        with pytest.raises(SpotValidationError):
            service._save_spot(req)

    # Save tags and spot relationship
    def test_successfully_saving_tag_spot_relationship(self, mocker: mock, req: Dict, dicts_are_same):
        mocker.patch('time.time', return_value=MOCK_TIME_SECOND)
        mocker.patch('models.spot_model.TagSpotModel.save', mock_func)
        mocker.patch('models.spot_model.TagSpotModel.get', does_not_exist)

        service = SpotService()
        result = service._save_tag_spot_rel({
            "spotGroupId": req.get("spotGroupId"),
            "sortKey": SPOT_ID,
            "tags": req.get('tags'),
        })
        expected = []
        for tag_name in req.get('tags'):
            expected.append({
                    "partitionKey": 'tagSpotRelation#{}#{}'.format(req.get('spotGroupId'), tag_name),
                    "sortKey": SPOT_ID,
            })
        assert dicts_are_same(result, expected)

    def test_successfully_saving_tag_spot_relationship_case2(self, mocker: mock, req: Dict, dicts_are_same):
        tags_before = ["歴史", "北部"]
        tags_updating = ["歴史", "文化", "中部"]

        mocker.patch('time.time', return_value=MOCK_TIME_SECOND)
        mocker.patch('models.spot_model.TagSpotModel.save', mock_func)
        mocker.patch('models.spot_model.TagSpotModel.delete', mock_func)
        mocker.patch('models.spot_model.TagSpotModel.get', side_effect=[
            does_not_exist, does_not_exist, MockModel(hash_key='tagSpotRelation#{}#{}'.format(req.get('spotGroupId'), tags_before[1]), range_key=SPOT_ID)
        ])
        expected = []
        for tag_name in tags_updating:
            expected.append({
                    "partitionKey": 'tagSpotRelation#{}#{}'.format(req.get('spotGroupId'), tag_name),
                    "sortKey": SPOT_ID,
            })
        mock_models = [MockModel(hash_key=expitem["partitionKey"], range_key=expitem["sortKey"], attribute_values=expitem) for expitem in expected]
        # TODO mocking property is not working yet
        mocker.patch('models.spot_model.TagSpotModel', side_effect=mock_models)

        service = SpotService()
        result = service._save_tag_spot_rel({
            "spotGroupId": req.get("spotGroupId"),
            "sortKey": SPOT_ID,
            "tags": tags_updating,
            "tags_before": tags_before,
        })

    def test_raise_err_saving_tag_spot_relationship(self, mocker: mock, req: Dict):
        # TODO
        pass
