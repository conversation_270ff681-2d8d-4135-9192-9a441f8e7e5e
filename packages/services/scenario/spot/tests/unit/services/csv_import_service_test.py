import io
from pynamodb.exceptions import DoesNotExist, PutError
import pytest
import unittest.mock as mock
from typing import Dict

from services.csv_import_service import CsvImportService
from .spot_fixtures import (
    S3Mock,
    MOCK_TIME, MOCK_USER, MOCK_GEOHASH, MOCK_UUID, SPOT_ID, DEFAULT_REQUIRED_FIELDS, SPOT_REQUEST, SPOT_ATTRIBUTES_IN_GROUP, CSV_HEADER, CSV_CONTENTS_PARAMS
)
from tests.unit.utils import MockModel, MockIterator, MockBatchWrite
from common.exceptions import InvalidCsvHeader
from constants.spot_prefix import SpotPrefix
from models.spot_model import SpotModel, TagSpotModel
from models.spot_group_model import SpotGroupModel
from validators.spot_validator import error_jp_translation

MOCK_TIME_SECOND = int(MOCK_TIME / 1000)

def does_not_exist(*args, **kwargs):
    raise DoesNotExist()

def mock_func(*args, **kwargs):
    return True

def save_error(*args, **kwargs):
    raise PutError("save error")


class TestCSVImportService():
    @pytest.fixture
    def req(self):
        return SPOT_REQUEST

    def test_get_spot_attribute_map(self, mocker: mock, req: Dict, dicts_are_same):
        mocker.patch('models.spot_group_model.SpotGroupModel.get', return_value=MockModel(
            attributes=SPOT_ATTRIBUTES_IN_GROUP
        ))
        expected = {}
        for item in SPOT_ATTRIBUTES_IN_GROUP:
            expected[item.attributeName] = item
        service = CsvImportService()
        attribute_map = service._get_spot_attribute_map(req)
        assert dicts_are_same(expected, attribute_map)
        service.csv_fields.clear() # reset for next test

    def test_get_csv_header_validated_ok(self, mocker: mock, req: Dict):
        mocker.patch('models.spot_group_model.SpotGroupModel.get', return_value=MockModel(
            attributes=SPOT_ATTRIBUTES_IN_GROUP
        ))
        input_content = 'ID,タグ,名称,住所,緯度,経度,説明,連絡先電話番号,画像,URL' + "\n" + 'spot_100,"歴史,北部",35.681382,139.766084,東京都港区芝公園1丁目,03-1234-5678,https://example.com/image.jpg,'
        service = CsvImportService()
        service.csv_fields = DEFAULT_REQUIRED_FIELDS
        assert service._get_csv_header_validated(io.StringIO(input_content), req) == CSV_HEADER.split(',')
        service.csv_fields.clear() # reset for next test

    def test_get_csv_header_validated_invalid_error_case1(self, mocker: mock, req: Dict):
        mocker.patch('models.spot_group_model.SpotGroupModel.get', return_value=MockModel(
            attributes=SPOT_ATTRIBUTES_IN_GROUP
        ))
        mocker.patch('sys.exit', side_effect=mock_func)
        input_content = 'ID,タグ,名称,緯度,経度,連絡先電話番号,画像,URL' + "\n" + 'spot_100,"歴史,北部",35.681382,139.766084,東京都港区芝公園1丁目,03-1234-5678,https://example.com/image.jpg,'
        service = CsvImportService()
        service.csv_fields = DEFAULT_REQUIRED_FIELDS
        with pytest.raises(InvalidCsvHeader, match=r".*住所.*説明.*required"):
            service._get_csv_header_validated(io.StringIO(input_content), req)
        service.csv_fields.clear() # reset for next test

    def test_get_csv_header_validated_invalid_error_case2(self, mocker: mock, req: Dict):
        mocker.patch('models.spot_group_model.SpotGroupModel.get', return_value=MockModel(
            attributes=SPOT_ATTRIBUTES_IN_GROUP
        ))
        mocker.patch('sys.exit', side_effect=mock_func)
        input_content = 'ID,タグ,名称,住所,緯度,経度,説明,電話番号,画像,URL' + "\n" + 'spot_100,"歴史,北部",35.681382,139.766084,東京都港区芝公園1丁目,03-1234-5678,https://example.com/image.jpg,'
        service = CsvImportService()
        service.csv_fields = DEFAULT_REQUIRED_FIELDS
        with pytest.raises(InvalidCsvHeader, match=r".*連絡先電話番号.*required"):
            service._get_csv_header_validated(io.StringIO(input_content), req)
        service.csv_fields.clear() # reset for next test

    # Test to convert CSV to dictionary data
    @pytest.fixture(params=CSV_CONTENTS_PARAMS)
    def csv_content(self, request):
        return request.param

    def test_convert_csv_to_dict(self, mocker: mock, csv_content, req: Dict, dicts_are_same):
        mocker.patch('models.spot_group_model.SpotGroupModel.get', return_value=MockModel(
            attributes=SPOT_ATTRIBUTES_IN_GROUP
        ))
        mocker.patch('time.time', return_value=MOCK_TIME_SECOND)
        mocker.patch('uuid.uuid4', return_value=MOCK_UUID)
        mocker.patch('pygeohash.encode', return_value=MOCK_GEOHASH)
        req['createdBy'] = MOCK_USER
        service = CsvImportService()
        service.csv_fields = DEFAULT_REQUIRED_FIELDS
        data = service._convert_csv_to_dict(
            S3Mock(csv_content['input']),
            'path/to/obj.csv',
            req
        )
        assert dicts_are_same(data, csv_content['result'])
        service.csv_fields.clear() # reset for next test

    def __mocker_patch_importing_csv(self, mocker, req):
        mocker.patch('models.spot_model.SpotModel', MockModel)
        mocker.patch('models.spot_model.TagSpotModel', MockModel)
        mocker.patch('models.csv_import_model.CSVImportModel', MockModel)
        mocker.patch('time.time', return_value=MOCK_TIME_SECOND)
        mocker.patch('sys.exit', side_effect=mock_func)
        mocker.patch('uuid.uuid4', return_value=MOCK_UUID)
        mocker.patch('pygeohash.encode', return_value=MOCK_GEOHASH)

    def test_path_writing_imported_csv_to_db(self, req, mocker: mock):
        self.__mocker_patch_importing_csv(mocker, req)
        mock_converted_data = CSV_CONTENTS_PARAMS[0]['result'].copy()
        mock_converted_data.pop(3) # remove invalid item
        SpotModel.batch_write = mock.MagicMock(return_value=MockBatchWrite(MockModel()))
        TagSpotModel.batch_write = mock.MagicMock(return_value=MockBatchWrite(MockModel()))
        SpotGroupModel.save = mock.MagicMock()
        SpotGroupModel.get = mock.MagicMock(return_value=MockModel(
            partitionKey=SpotPrefix.SPOT_GROUP_PREFIX, sortKey=req.get('spotGroupId'), tags=[],
        ))

        # When
        service = CsvImportService()
        service._convert_csv_to_dict = mock.MagicMock(return_value=mock_converted_data)
        service._update_import_status = mock.MagicMock(return_value=None)
        service.csv_fields = DEFAULT_REQUIRED_FIELDS
        service._get_import_status = mock.MagicMock(return_value=None)

        service.write_imported_csv_to_dynamodb({
            'spotGroupId': req.get('spotGroupId'),
            'filePath': 'path/to/obj.csv',
            'createdBy': req.get('createdBy'),
        })
        # Then assert at basic level
        assert service.csv_source == 'path/to/obj.csv'
        service._update_import_status.assert_called_with({
            "status": 'FINISHED',
            "errors": [],
            "importFailed": 0,
            "importSuccess": mock.ANY,
        })
        # reset for next test
        service.csv_fields.clear()
        service._convert_csv_to_dict.reset_mock()
        service._update_import_status.reset_mock()

    def test_path_validation_err_writing_imported_csv_to_db(self, req, mocker: mock):
        self.__mocker_patch_importing_csv(mocker, req)
        mock_converted_data = CSV_CONTENTS_PARAMS[0]['result'].copy()
        SpotModel.batch_write = mock.MagicMock(return_value=MockBatchWrite(MockModel()))
        TagSpotModel.batch_write = mock.MagicMock(return_value=MockBatchWrite(MockModel()))
        SpotGroupModel.save = mock.MagicMock(side_effect=mock_func)
        SpotGroupModel.get = mock.MagicMock(return_value=MockModel(
            partitionKey=SpotPrefix.SPOT_GROUP_PREFIX, sortKey=req.get('spotGroupId'), tags=[],
        ))

        # When
        service = CsvImportService()
        service._convert_csv_to_dict = mock.MagicMock(return_value=mock_converted_data)
        service._update_import_status = mock.MagicMock(return_value=None)
        service.csv_fields = DEFAULT_REQUIRED_FIELDS
        service._get_import_status = mock.MagicMock(return_value=None)

        service.write_imported_csv_to_dynamodb({
            'spotGroupId': req.get('spotGroupId'),
            'filePath': 'path/to/obj.csv',
            'createdBy': req.get('createdBy'),
        })
        service._update_import_status.assert_called_with({
            "status": 'ERROR',
            "errors": [{
                "messages": [
                    "latLngGeohash: {}".format(error_jp_translation['latLngGeohash']),
                    "latitude: {}".format(error_jp_translation['latitude']),
                    "longitude: {}".format(error_jp_translation['longitude']),
                ],
                "row": mock.ANY,
            }],
            "importFailed": 1,
            "importSuccess": mock.ANY,
        })
        # reset for next test
        service.csv_fields.clear()
        service._convert_csv_to_dict.reset_mock()
        service._update_import_status.reset_mock()

    def test_retry_writing_imported_csv_to_db(self, req, mocker: mock):
        self.__mocker_patch_importing_csv(mocker, req)
        mock_converted_data = CSV_CONTENTS_PARAMS[0]['result'].copy()
        mock_converted_data.pop(3)
        SpotModel.batch_write = mock.MagicMock(return_value=MockBatchWrite(MockModel()))
        TagSpotModel.batch_write = mock.MagicMock(return_value=MockBatchWrite(MockModel()))
        SpotGroupModel.save = mock.MagicMock(side_effect=mock_func)
        SpotGroupModel.get = mock.MagicMock(return_value=MockModel(
            partitionKey=SpotPrefix.SPOT_GROUP_PREFIX, sortKey=req.get('spotGroupId'), tags=[],
        ))

        # When
        row_continuing_from = 2
        service = CsvImportService()
        service._convert_csv_to_dict = mock.MagicMock(return_value=mock_converted_data)
        service._update_import_status = mock.MagicMock(return_value=None)
        service.csv_fields = DEFAULT_REQUIRED_FIELDS
        service._get_import_status = mock.MagicMock(return_value={
            "errors": [{"messages": ["longitude: ['min value is 122']"], "row": row_continuing_from}],
        })

        service.write_imported_csv_to_dynamodb({
            'spotGroupId': req.get('spotGroupId'),
            'filePath': 'path/to/obj.csv',
            'createdBy': req.get('createdBy'),
        })
        service._update_import_status.assert_called_with({
            "status": 'FINISHED',
            "errors": [],
            "importFailed": 0,
            "importSuccess": len(mock_converted_data) - row_continuing_from + 1,
        })
        # reset for next test
        service.csv_fields.clear()
        service._convert_csv_to_dict.reset_mock()
        service._update_import_status.reset_mock()
