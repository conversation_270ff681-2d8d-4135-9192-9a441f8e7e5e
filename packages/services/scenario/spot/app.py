import os
import json
import awsgi
import uuid
import logging as _logging
from flask import Flask, request, jsonify
from flask_cors import CORS

from utils.json import (
    DecimalEncoder
)
from constants.spot_prefix import SpotPrefix
from services.spot_group_service import SpotGroupService
from services.spot_service import SpotService
from services.csv_import_service import CsvImportService
from services.spot_list_service import SpotListService
from services.chat_spot_list_service import ChatSpotListService
from services.chat_spot_location_service import ChatSpotLocationService
from services.s3_service import S3Service
from services.scenario_data_service import ScenarioDataService


# Flask app setup
app = Flask(__name__)
app.config['JSON_AS_ASCII'] = False
CORS(app)

cognitoUserName = 'anonymous'
cognitoUserGroups = 'none'

# Logger setup
logger = _logging.getLogger()

BASE_PATH = "/spot"

def route(rule, **options):
    return app.route(f"{BASE_PATH}{rule}", **options)

## Spot Groups
@route('/spotGroups', methods=['GET'])
def get_spot_groups():
    service = SpotGroupService()
    return service.get_spot_groups()

@route('/spotGroups/<spotGroupId>', methods=['GET'])
def get_spot_group_by_id(spotGroupId):
    service = SpotGroupService()
    return service.get_spot_group_by_id(spotGroupId)

@route('/spotGroups', methods=['POST'])
def add_spot_group():
    service = SpotGroupService()
    scenario_data_service = ScenarioDataService()
    check_result = scenario_data_service.check_scenario_versions()
    if check_result is not True:
        return check_result
    resp = service.add_spot_group(request.get_json())
    logger.debug('app service.add_spot_group resp: {}'.format(repr(resp[0].get_json())))
    resp_scenario_data = scenario_data_service.add_spot_location_picker_scenario(resp[0].get_json()['data']['sortKey'])
    return resp if resp_scenario_data == True else resp_scenario_data

@route('/spotGroups/<spotGroupId>', methods=['PUT'])
def put_spot_group(spotGroupId):
    service = SpotGroupService()
    return service.update_spot_group(spotGroupId, request.get_json())

@route('/spotGroups/<spotGroupId>', methods=['DELETE'])
def delete_spot_group(spotGroupId):
    service = SpotGroupService()
    return service.delete_spot_group(spotGroupId)

@route('/spotGroups/spotTemplates', methods=['GET'])
def get_spot_group_templates():
    service = SpotGroupService()
    return service.get_spot_group_templates()

@route('/spotGroups/preview', methods=['POST'])
def generate_spot_group_preview():
    service = SpotGroupService()
    return service.generate_spot_group_preview(request.get_json())

@route('/spotGroups/postbacks/<spotGroupId>', methods=['GET'])
def spot_groups_postbacks():
    # example data
    service = SpotGroupService()
    result = {}
    result["result"] = "OK"
    result["items"] = service.get_spot_groups()
    return json.dumps(result, ensure_ascii=False, cls=DecimalEncoder)

## Spots
@route('/spots', methods=['GET'])
def list_spots():
    service = SpotService()
    return service.list_spots(request.args)

@route('/spots', methods=['POST'])
def add_spot():
    service = SpotService()
    spot_id = '{}_{}'.format(SpotPrefix.SPOT_PREFIX, str(uuid.uuid4()))
    return service.add_spot(spot_id, request.get_json())

@route('/spots/delete', methods=['POST'])
def delete_spots():
    """API endpoint that query for the scenario spot by ids.
     """
    return SpotService().batch_delete_spots()

@route('/spots/<spot_id>', methods=['GET'])
def get_spot(spot_id):
    service = SpotService()
    return service.get_spot_by_id(spot_id, request.args)

@route('/spots/<spot_id>', methods=['PUT'])
def put_spot(spot_id):
    service = SpotService()
    return service.update_spot(spot_id, request.get_json())

@route('/spots/import/csv', methods=['POST'])
def spot_import_csv():
    service = SpotService()
    return service.invoke_csv_import_job(request.get_json())

@route('/spots/import-update/csv', methods=['POST'])
def spot_import_update_csv():
    service = SpotService()
    return service.invoke_csv_import_update_job(request.get_json())

@route('/spots/import/csv/status/<spotGroupId>', methods=['GET'])
def spot_import_csv_status(spotGroupId):
    service = CsvImportService()
    return service.get_csv_imported_info(spotGroupId)

@route('/spots/export/csv', methods=['GET'])
def spot_export_csv():
    service = SpotService()
    return service.export_spots_to_csv(request.args)

@route('/spots/import/images', methods=['POST'])
def spot_import_images():
    # TODO
    return json.dumps({"data": "Dummy"}, ensure_ascii=False, cls=DecimalEncoder)

## Spot Lists

@route('/spotLists/<spotGroupId>', methods=['GET', 'POST', 'PUT'])
def spot_lists(spotGroupId):
    """API endpoint that query for the scenario spot list data table in DynamoDB.
     """
    return SpotListService().crud_spot_lists(spotGroupId)

@route('/spotLists/<spotGroupId>/<spotListId>', methods=['GET'])
def spot_list_by_id(spotGroupId, spotListId):
    """API endpoint that query for the scenario spot list by id.
     """
    return SpotListService().get_spot_list_by_id(spotGroupId, spotListId)

@route('/spotLists/<spotGroupId>/delete', methods=['POST'])
def spot_list_delete_by_ids(spotGroupId):
    """API endpoint that query for the scenario spot list by id.
     """
    return SpotListService().batch_delete_spot_lists(spotGroupId)

## line display
@route('/spotLists/<spotGroupId>/<spotListId>/lineDisplay', methods=['GET'])
def spot_line_display_by_spot_list_id(spotGroupId, spotListId):
    """API endpoint that query for the scenario spot line display list by spotListId.
     """
    return ChatSpotListService().get_spot_list_by_spot_list_id_for_line_api(spotGroupId, spotListId)

@route('/spotGroup/<spotGroupId>/lineDisplay', methods=['GET'])
def spot_line_display_by_location(spotGroupId):
    """API endpoint that query for scenario spot line display info based on location.
     """
    request_args = request.args.to_dict()
    # location = request_args.get('location')
    latitude = request_args.get('latitude')
    longitude = request_args.get('longitude')
    return ChatSpotLocationService().get_spots_by_location_for_line_api(spotGroupId, latitude, longitude)

## common use
@route('/presignedUrl', methods=['GET'])
def generate_presigned_url():
    """API endpoint to generate presigned url to upload to S3. Presigned url expires after 60 seconds
     """
    return S3Service().get_presigned_url(request.args.get("objectName"), request.args.get("folderName"), request.args.get("contentType"))

def lambda_handler(event, context):
    """AWS Lambda handler method

    :param event: Lambda trigger event.
    :param context: AWS Lambda context.
    """
    global cognitoUserName
    global cognitoUserGroups

    # Supports string levels: DEBUG, INFO, WARNING
    log_level = os.environ.get('LOG_LEVEL', 'INFO')
    if (log_level == 'DEBUG'):
        logger.setLevel(_logging.DEBUG)
    if (log_level == 'INFO'):
        logger.setLevel(_logging.INFO)
    if (log_level == 'WARNING'):
        logger.setLevel(_logging.WARNING)

    if event.get('requestContext') and event.get('requestContext').get('authorizer') and  \
        event.get('requestContext').get('authorizer').get('claims'):
        cognitoInfo = event.get('requestContext').get('authorizer').get('claims')
        cognitoUserName = cognitoInfo.get('cognito:username')
        cognitoUserGroups = cognitoInfo.get('cognito:groups')

    return awsgi.response(app, event, context, base64_content_types={"image/png"})
