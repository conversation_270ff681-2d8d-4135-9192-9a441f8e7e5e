from pynamodb.attributes import (
    UnicodeAttribute,
    NumberAttribute,
    ListAttribute,
    MapAttribute
)
from typing import Dict
from models.base_model import BaseModel
from models.indexes.lat_long_geohash_index import LatLongGeohashIndex


class SpotAttribute(MapAttribute):
    """
    Custom Attribute of a sightseeing spot
    """
    id = UnicodeAttribute()
    value = UnicodeAttribute()


class SpotModel(BaseModel):
    """
    Sightseeing Spot Data
    """
    latLngGeohash = UnicodeAttribute()
    latitude = NumberAttribute()
    longitude = NumberAttribute()
    attributes = ListAttribute(of=SpotAttribute, null=False)
    urlAttribute = SpotAttribute(null=True)
    tags = ListAttribute(of=UnicodeAttribute, null=True)
    spotLists = ListAttribute(of=UnicodeAttribute, null=True)
    image = UnicodeAttribute(null=True)
    csvSource = UnicodeAttribute(null=True)
    latLng_geohash_index = LatLongGeohashIndex()

    def as_dict(self) -> Dict:
        """
        Return a dict containing the attributes of the model
        """
        result = self.attribute_values
        spot_dict_attributes = []
        for spot_attribute in self.attributes:
            if isinstance(spot_attribute, dict):
                spot_dict_attributes.append(spot_attribute)
            else:
                spot_dict_attributes.append(spot_attribute.as_dict())
        result['attributes'] = spot_dict_attributes
        if self.urlAttribute is not None:
            if isinstance(self.urlAttribute, dict):
                result['urlAttribute'] = self.urlAttribute
            else:
                result['urlAttribute'] = self.urlAttribute.as_dict()
        else:
            result['urlAttribute'] = SpotModel.default_url_attribute()
        return result

    def get_row_index(self):
        """
        Return the row index of the CSV file
        """
        return self.row_index

    def set_row_index(self, row: int):
        """
        Set the row index of the CSV file
        """
        self.row_index = row

    @staticmethod
    def default_url_attribute():
        return {
            "id": "url",
            "value": ""
        }


class TagSpotModel(BaseModel):
    """
    Sightseeing spot and tag relationship
    """
