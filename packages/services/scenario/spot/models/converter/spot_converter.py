from models.spot_group_model import SpotGroupModel


class SpotGroupDto:

    @classmethod
    def to_dto(cls, value: SpotGroupModel) -> dict:
        return {
            "partitionKey": value.partitionKey,
            "sortKey": value.sortKey,
            "createdAt": value.createdAt,
            "createdBy": value.createdBy,
            "updatedAt": value.updatedAt,
            "updatedBy": value.updatedBy,
            "groupName": value.groupName,
            "attributes": value.get_spot_group_attributes(),
            "urlAttribute": value.get_spot_url_attribute(),
            "tags": value.get_spot_tags(),
            "spotTemplate": value.get_spot_template(),
            "scenarioTriggerSettings": value.get_scenario_trigger_settings()
        }
