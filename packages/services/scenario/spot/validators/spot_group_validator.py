import logging as _logging

from pynamodb.exceptions import (
    QueryError,
    UpdateError,
    PutError,
    DeleteError,
    AttributeNullError,
    DoesNotExist,
)

from utils.string_util import StringUtil

# Logger setup
logger = _logging.getLogger()

def validate_get_spot_groups():
  return

def validate_get_spot_group_by_id():
  return

def validate_add_spot_group(spot_group):
  if StringUtil().isNullEmptyOrBlankWithFullWidth(spot_group.groupName):
    raise AttributeNullError('groupName')
  
def validate_update_spot_group(spot_group):
  if StringUtil().isNullEmptyOrBlankWithFullWidth(spot_group.groupName):
    raise AttributeNullError('groupName')

def validate_delete_spot_group_by_id():
  return

