
LOCATION_TRIGGER_DATA = {
    'name': '位置ピッカーボタン',
    'dataType': 'bubbleFlex',
    # duplicate of aws_back/scenario/chatbot_aws/chatbot/scenario/spot_scenario/constants/location_message.json
    'params': {
    "type": "bubble",
    "body": {
      "type": "box",
      "layout": "vertical",
      "contents": [
        {
          "type": "text",
          "text": "{文言}",
          "weight": "regular",
          "size": "md",
          "wrap": True,
          "align": "start"
        }
      ]
    },
    "footer": {
      "type": "box",
      "layout": "vertical",
      "spacing": "sm",
      "contents": [
        {
          "type": "button",
          "style": "link",
          "height": "sm",
          "action": {
            "type": "uri",
            "label": "位置情報を送信する",
            "uri": "https://line.me/R/nv/location"
          }
        }
      ],
      "flex": 0
    }
  },
}
