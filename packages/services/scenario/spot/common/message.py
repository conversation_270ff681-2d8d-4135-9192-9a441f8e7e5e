"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""

from common.message_code import MessageCode


class Message():

    def get_message(self, code) -> str:
        return self.error_messages.get(code)

    error_messages = {
        # common error message codes
        MessageCode.SE_C_0000: 'Misuse of API. Missing the following parameters in query: {}',
        MessageCode.SE_C_0001: 'The data has been updated by another user.',
        MessageCode.SE_C_0002: 'Cannot get template json style of spot group template spotTemplate.id: {}',
        MessageCode.SE_C_0003: 'Upload default spot template image (default.jpg) to S3 error. Exception={}',
        MessageCode.SE_C_0004: 'Upload default spot template json (spot_templates.json) to S3 error. Exception={}',

        MessageCode.SE_C_1000: 'AttributeNullError: {}',
        MessageCode.SE_C_1001: 'AttributeNullError: Attribute {} cannot be None',
        MessageCode.SE_C_1002: '{} must be at least than or equal {} characters.',
        MessageCode.SE_C_2000: 'Cannot read file on AWS S3 bucketName: {} file: {}',
        MessageCode.SE_C_2001: 'Cannot generate presigned url on AWS S3 bucketName: {}',

        # Spot group error message codes
        MessageCode.SE_G_0001: 'Spot group does not exist spotGroupId={}',
        MessageCode.SE_G_0002: 'Missing required property in Spot Group: {}',

        # Spot error message codes
        MessageCode.SE_P_0001: 'Spot already exists spotGroupId={} spotId={}',
        MessageCode.SE_P_0002: 'Spot does not exist spotGroupId={} spotId={}',
        MessageCode.SE_P_0040: 'Validation error. Exception={}',

        MessageCode.SE_SD_0040: 'シナリオトークデータが存在しません。最初にシナリオおよびトークを作成してください。',

        # Spot list error message codes
        MessageCode.SE_L_0001: 'Spot list does not exist spotGroupId={} spotListId={}',
        MessageCode.SE_L_0002: 'Spot list does not exist in spot group spotGroupId={0}',

        # Tag error message codes
        MessageCode.SE_T_0001: 'Tag does not exist spotGroupId={} tag={}',

        # Line chatbot error message codes
        MessageCode.SE_I_0001: 'Spot does not exist spotGroupId={} spotListId={}',

        # System error message codes
        MessageCode.SE_S_0001: 'System error. Exception={}',
    }
