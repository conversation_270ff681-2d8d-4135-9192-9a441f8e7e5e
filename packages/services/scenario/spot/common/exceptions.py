from typing import Dict, List


class SpotValidationError(Exception):
    """
    Exception raised when a sightseeing spot item is not valid
    """
    row_index_msg = ' at row {}'
    def __init__(self, validator_errors, row_index = None):
        self.messages = []
        self.row_index = row_index
        self.errors_by_row = {}
        
        if isinstance(row_index, list):
            # Handle multiple rows with errors
            for idx, (row_idx, errors) in enumerate(zip(row_index, validator_errors)):
                self.errors_by_row[row_idx] = errors
                # If errors is already a list of messages, extend directly
                if isinstance(errors, list):
                    self.messages.extend(errors)
                # Otherwise, process as dictionary
                else:
                    for key, value in errors.items():
                        self.messages.append(f'{key}: {value}')
        else:
            # Handle single row with errors
            self.errors_by_row[row_index] = validator_errors
            # If validator_errors is already a list of messages, extend directly
            if isinstance(validator_errors, list):
                self.messages.extend(validator_errors)
            # Otherwise, process as dictionary
            else:
                for key, value in validator_errors.items():
                    self.messages.append(f'{key}: {value}')

    def __str__(self):
        if self.row_index:
            if isinstance(self.row_index, list):
                return repr(self.messages) + f" at rows {self.row_index}"
            return repr(self.messages) + self.row_index_msg.format(self.row_index)
        else:
            return repr(self.messages)

    def get_row_index(self):
        return self.row_index
        
    def get_row_errors(self):
        return self.errors_by_row

    def get_errors(self) -> List:
        return self.messages

class InvalidCsvHeader(Exception):
    """
    Exception raised when the CSV header is not valid
    """
    def __init__(self, fields: List[str]):
        self.message = "Invalid CSV header: '%s' is required" % repr(fields)

    def __str__(self):
        return repr(self.message)
