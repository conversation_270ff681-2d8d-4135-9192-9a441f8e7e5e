"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""


class MessageCode():

    # common error codes
    SE_C_0000 = "SE_C_0000"
    SE_C_0001 = "SE_C_0001"
    SE_C_0002 = "SE_C_0002"
    SE_C_0003 = "SE_C_0003"
    SE_C_0004 = "SE_C_0004"

    SE_C_1000 = "SE_C_1000"
    SE_C_1001 = "SE_C_1001"
    SE_C_1002 = "SE_C_1002"
    SE_C_2000 = "SE_C_2000"
    SE_C_2001 = "SE_C_2001"

    # Spot group error codes
    SE_G_0001 = "SE_G_0001"
    SE_G_0002 = "SE_G_0002"
    #TODO Other spot group error codes

    # Spot error codes
    SE_P_0001 = "SE_P_0001"
    SE_P_0002 = "SE_P_0002"
    SE_P_0003 = "SE_P_0003"
    SE_P_0004 = "SE_P_0004"
    SE_P_0005 = "SE_P_0005"
    SE_P_0015 = "SE_P_0015"
    SE_P_0016 = "SE_P_0016"
    SE_P_0017 = "SE_P_0017"
    SE_P_0040 = "SE_P_0040"

    # Scenario data error codes
    SE_SD_0040 = "SE_SD_0040"

    # Spot list error codes
    SE_L_0001 = "SE_L_0001"
    SE_L_0002 = "SE_L_0002"

    # Tag error codes
    SE_T_0001 = "SE_T_0001"

    # Line chatbot error codes
    SE_I_0001 = "SE_I_0001"

    # System error codes
    SE_S_0001 = "SE_S_0001"
