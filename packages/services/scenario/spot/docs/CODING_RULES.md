# 観光機能　Python コーディングルール

## ファイル構成

```
├── spots
│   ├── common
│   ├── constants
│   ├── models
│   │    ├── spot_group_model.py
│   │    ├── spot_list_model.py
│   ├── services
│   │    ├── spot_group_service.py
│   │    ├── spot_list_service.py
│   │    ├── spot_service.py
│   ├── tests
│   │    ├── conftest.py
│   │    └── test_apis.py
│   ├── utils
│   ├── validators
│   ├── app.py
│   ├── config.py
│   └── requirements.txt
├── __init__.py
└── .gitignore
```

### インデント

1レベルインデントするごとに、スペースを4つ使いましょう。

### インポート

import 文は、以下の順で分けるべきです。

例：

```
# 最初はスタンダードライブラリ
import os
import sys

# 次はサードパーティライブラリ
import boto3

# 最後はローカルライブラリ
from util import generate_uuid

```

### 1行の長さ

すべての行の長さを、最大79文字までに制限しましょう。



## 命名規約


### クラスの名前
クラスの名前には通常 camelCase 方式を使うべきです。

例：
```
# スポットグループのクラス
class SpotGroup:

```

### 関数や変数の名前
関数名（Functions、Methods）と変数名は snakeCase 方式で、定数の場合は全て大文字にします。

例：

```

# 最大リストのサイズ
MAX_LIST_SIZE = 100

# スポットグループ一覧を取得の関数
def get_spot_group_list():

  # スポットグループリスト
  spot_group_list = []


```

もっと厳密な Python のコーディングルールは以下をご参考：

https://pep8-ja.readthedocs.io/ja/latest/
