from copy import deepcopy
import logging as _logging
import time
import os
import json
from typing import Dict
from typing import List
from flask import jsonify, request
from http import HTTPStatus
import boto3
import geohash
from pynamodb.exceptions import DoesNotExist, PutError, AttributeNullError

from models.spot_model import SpotModel, TagSpotModel
from models.spot_list_model import SpotListModel
from common.exceptions import SpotValidationError
from common.message import Message, MessageCode
from constants.spot_prefix import SpotPrefix
from constants.response_def import ResponseDef
from constants.attributes_name import AttributesName
from validators.spot_validator import SpotValidator
from utils.date_time_util import DateTimeUtil
from utils.file_util import FileUtil
from services.chat_spot_base_service import ChatSpotBaseService
from services.csv_export_service import CsvExportService

# Logger setup
logger = _logging.getLogger()


class SpotService():

    def list_spots(self, req: Dict):
        resp = {"spot": []}
        try:
            hash_key = 'spot#{}'.format(req.get('spotGroupId'))
            items = SpotModel.query(hash_key, limit=2000)
            for item in items:
                resp['spot'].append(item.as_dict())
            if items.last_evaluated_key:
                resp["last_evaluated_key"] = items.last_evaluated_key
            return jsonify({"data": resp}), HTTPStatus.OK
        except Exception as err:
            return self._handle_error(err)

    def get_spot_by_id(self, spot_id: str, req: Dict):
        try:
            hash_key = 'spot#{}'.format(req.get('spotGroupId'))
            item = SpotModel.get(hash_key, spot_id)
            resp = item.as_dict()
            resp["image"] = ChatSpotBaseService().check_and_get_spot_image_url(req.get('spotGroupId'), item)
            return jsonify({"data": {"spot": resp}}), HTTPStatus.OK
        except DoesNotExist:
            errcode = MessageCode.SE_P_0002
            return jsonify({
                "errorCode": errcode,
                "errorMessage": Message().get_message(errcode).format(hash_key, spot_id),
            }), HTTPStatus.BAD_REQUEST
        except Exception as err:
            return self._handle_error(err)

    def add_spot(self, spot_id: str, req: Dict):
        spot_hash_key = 'spot#{}'.format(req.get('spotGroupId'))
        # TODO Implement optimistic lock
        try:
            SpotModel.get(spot_hash_key, spot_id)
            errcode = MessageCode.SE_P_0001
            return jsonify({
                "errorCode": errcode,
                "errorMessage": Message().get_message(errcode).format(spot_hash_key, spot_id),
            }), HTTPStatus.BAD_REQUEST
        except DoesNotExist:
            try:
                from app import cognitoUserName
                res_spot = self._save_spot({
                    "partitionKey": spot_hash_key,
                    "sortKey": spot_id,
                    "latLngGeohash": self._generate_geohash(req.get('latitude'), req.get('longitude')),
                    "latitude": req.get("latitude"),
                    "longitude": req.get("longitude"),
                    "attributes": req.get("attributes"),
                    "urlAttribute": req.get("urlAttribute"),
                    "image": req.get("image"),
                    "tags": req.get('tags'),
                    "createdAt": int(time.time() * 1000),
                    "updatedAt": int(time.time() * 1000),
                    "createdBy": cognitoUserName,
                    "updatedBy": cognitoUserName,
                })
                res_tag_spot_rel = self._save_tag_spot_rel({
                    "spotGroupId": req.get('spotGroupId'),
                    "sortKey": spot_id,
                    "tags": req.get('tags'),
                })
                return jsonify({
                    "data": {"spot": res_spot, "tag_spot_rel": res_tag_spot_rel},
                }), HTTPStatus.OK
            except Exception as err:
                return self._handle_error(err)

    def update_spot(self, spot_id: str, req: Dict):
        oldSpot = None
        spot_item = {}
        spot_hash_key = 'spot#{}'.format(req.get('spotGroupId'))
        try:
            spot_item = SpotModel.get(spot_hash_key, spot_id)
            oldSpot = deepcopy(spot_item)
        except DoesNotExist:
            errcode = MessageCode.SE_P_0002
            return jsonify({
                "errorCode": errcode,
                "errorMessage": Message().get_message(errcode).format(spot_hash_key, spot_id),
            }), HTTPStatus.BAD_REQUEST
        try:
            from app import cognitoUserName
            resp_data = {}
            tags_before = spot_item.tags
            attributes_exist = req.get('attributes') and len(req.get('attributes')) > 0
            tags_exist = req.get('tags') and len(req.get('tags')) > 0
            latitude = req.get('latitude') if req.get('latitude') else spot_item.latitude
            longitude = req.get('longitude') if req.get('longitude') else spot_item.longitude
            resp_data["spot"] = self._save_spot({
                "partitionKey": spot_hash_key,
                "sortKey": spot_id,
                "latLngGeohash": self._generate_geohash(latitude, longitude),
                "latitude": latitude,
                "longitude": longitude,
                "attributes": req.get('attributes') if attributes_exist else list(map(
                    lambda spot_attr: spot_attr.as_dict(), spot_item.attributes
                )),
                "urlAttribute": req.get("urlAttribute", None),
                "tags": req.get('tags') if tags_exist else spot_item.tags,
                "image": req.get('image') if req.get('image') else spot_item.image,
                "updatedAt": int(time.time() * 1000),
                "updatedBy": cognitoUserName,
            })
            if tags_exist:
                resp_data["tag_spot_rel"] = self._save_tag_spot_rel({
                    "spotGroupId": req.get('spotGroupId'),
                    "partitionKey": spot_hash_key,
                    "sortKey": spot_id,
                    "tags": req.get('tags'),
                    "tags_before": tags_before
                })
            logger.debug(resp_data)

            # if spot image is changed, delete old image
            if req.get('image') and oldSpot and oldSpot.image:
                if req.get('image') != oldSpot.image:
                    FileUtil().delete_spot_file(req.get('spotGroupId'), oldSpot.image)

            return jsonify({"data": resp_data,}), HTTPStatus.OK
        except Exception as err:
            # TODO : Rollback tags of the spot item
            #if len(tags_before) > 0:
                # not work properly
            return self._handle_error(err)

    def batch_delete_spots(self):
        try:
            from app import cognitoUserName
            current_time = DateTimeUtil().getNowJstMillisecond()
            request_json = request.get_json()
            logger.info(
                f'spot_service.batch_delete_spots request_json: {request_json}')

            # validator input
            checkValidation = SpotValidator().validate_batch_delete_spots(request_json)
            if checkValidation is not None:
                return jsonify({ResponseDef.ERR_CODE: MessageCode.SE_C_1000, ResponseDef.ERROR_MSG: checkValidation}), HTTPStatus.BAD_REQUEST

            spotGroupId = request_json.get('spotGroupId')
            spotIds = request_json.get('spotIds')

            for spotId in spotIds:
                spot = self.get_spot(spotGroupId, spotId)
                if spot is None:
                    msg = Message().get_message(MessageCode.SE_P_0002).format(spotGroupId, spotId)
                    logger.info(msg)
                    continue

                with SpotModel.batch_write() as batch:
                    # delete spotTagRelation
                    if spot.tags is not None:
                        for tag in spot.tags:
                            # partitionKey
                            pk = SpotPrefix.TAG_SPOT_RELATION_PREFIX + SpotPrefix.SHARP_CHARACTER + \
                                spotGroupId + SpotPrefix.SHARP_CHARACTER + tag
                            tagSpotRelation = TagSpotModel(pk, spotId)
                            batch.delete(tagSpotRelation)
                            logger.info(
                                f'spot_service.batch_delete_spots delete spotTagRelation, spotGroupId= {spotGroupId} spotId= {spotId}')
                    # remove spotId from spotList.spots
                    if spot.spotLists is not None:
                        for spotListId in spot.spotLists:
                            # partitionKey
                            spotList = self.get_spot_list(spotGroupId, spotListId)
                            if spotList is not None:
                                if spotId in spotList.spots:
                                    # update spotList
                                    spotList.updatedAt = current_time
                                    spotList.updatedBy = cognitoUserName
                                    spotList.spots.remove(spotId)
                                    batch.save(spotList)
                                    logger.info(
                                        f'spot_service.batch_delete_spots update spotList, spotGroupId= {spotGroupId} spotListId= {spotListId}')

                # delete spot
                spot.delete()
                logger.info(
                    f'spot_service.batch_delete_spots delete spot, spotGroupId= {spotGroupId} spotId= {spotId}')
                # delete spot image
                if spot.image is not None:
                    FileUtil().delete_spot_file(spotGroupId, spot.image)
                    logger.info(
                        f'spot_service.batch_delete_spots delete spot image, image_path= {spot.image}')

        except AttributeNullError as attribute_null_error:
            errMsg = str(attribute_null_error)
            logger.error(
                f'Delete spot error, spot_service.batch_delete_spots, errMsg= {errMsg}')
            return jsonify({ResponseDef.ERR_CODE: MessageCode.SE_C_1000, ResponseDef.ERROR_MSG: Message().get_message(MessageCode.SE_C_1000).format(errMsg)}), HTTPStatus.BAD_REQUEST
        except Exception as exception:
            errMsg = "Exception: " + str(exception)
            logger.error(
                f'Delete spot error, spot_service.batch_delete_spots, errMsg= {errMsg}')
            return jsonify({ResponseDef.ERR_CODE: MessageCode.SE_S_0001, ResponseDef.ERROR_MSG: Message().get_message(MessageCode.SE_S_0001).format(errMsg)}), HTTPStatus.INTERNAL_SERVER_ERROR
        else:
            logger.info(
                f'Delete spot spotGroupId= {spotGroupId} spotIds= {spotIds}')
            return "", HTTPStatus.OK

    def get_spot(self, spotGroupId, spotId):
        """ get spot by id
        Args:
            spotGroupId (str): spotGroupId
            spotId (str): spotId
        Returns:
            None: spot does not exist 
            SpotModel: spot exists
        """
        # partitionKey
        pk = SpotPrefix.SPOT_PREFIX + SpotPrefix.SHARP_CHARACTER + spotGroupId
        try:
            spot = SpotModel.get(pk, spotId)
        except DoesNotExist:
            errMsg = Message().get_message(MessageCode.SE_P_0002).format(spotGroupId, spotId)
            logger.error(errMsg)
            return None
        else:
            return spot

    def get_spot_list(self, spotGroupId, spotListId):
        """ get spotList by id
        Args:
            spotGroupId (str): spotGroupId
            spotListId (str): spotListId
        Returns:
            None: spotList does not exist 
            SpotListModel: spotList exists
        """
        # partitionKey
        pk = SpotPrefix.SPOT_LIST_PREFIX + SpotPrefix.SHARP_CHARACTER + spotGroupId
        try:
            spotList = SpotListModel.get(pk, spotListId)
        except DoesNotExist:
            errMsg = Message().get_message(MessageCode.SE_L_0001).format(spotGroupId, spotListId)
            logger.error(errMsg)
            return None
        else:
            return spotList

    def _save_spot(self, attr: Dict):
        """
        Save sightseeing spot item and return result
        """
        # TODO check if tags exist in a spot group
        try:
            validator = SpotValidator()
            spot_id = attr.get("sortKey")
            attrs_to_save = {
                "partitionKey": attr.get("partitionKey"),
                "sortKey": spot_id,
                "latLngGeohash": attr.get("latLngGeohash"),
                "latitude": float(attr.get('latitude')),
                "longitude": float(attr.get('longitude')),
                "attributes": attr.get('attributes'),
                "urlAttribute": attr.get("urlAttribute"),
                "tags": attr.get('tags'),
                "image": attr.get('image'),
            }
            for key in ["updatedAt", "updatedBy", "createdAt", "createdBy"]:
                if attr.get(key) != None:
                    attrs_to_save.update({key: attr.get(key)})
            validator.item_validated(attrs_to_save)
            # When attributes are valid, save them to dynamodb
            logger.info('Saving spot: {}'.format(spot_id))
            model = SpotModel(**attrs_to_save)
            model.save()
        except Exception as err:
            raise err
        return model.as_dict()

    def _save_tag_spot_rel(self, attr: Dict):
        """
        Save sightseeing spot and tag relationship item and return result
        """
        results = []
        try:
            if (not attr.get("spotGroupId")):
                raise Exception("spotGroupId is required")
            tags_saving = attr.get("tags")
            tags_to_cleanup = []
            if attr.get("tags_before") and len(attr.get("tags_before")) > 0:
                tags_saving = list(set(attr.get("tags")) - set(attr.get("tags_before")))
                tags_to_cleanup = list(set(attr.get("tags_before")) - set(attr.get("tags")))
            logger.debug(tags_saving)
            logger.debug(tags_to_cleanup)
            for tag_name in tags_saving:
                item_saving = {
                    "partitionKey": 'tagSpotRelation#{}#{}'.format(attr.get('spotGroupId'), tag_name),
                    "sortKey": attr.get('sortKey'),
                }
                try:
                    item_found = TagSpotModel.get(item_saving['partitionKey'], item_saving['sortKey'])
                    if (item_found):
                        logger.debug('TagSpotRelation already exists: {}'.format(item_saving["partitionKey"]))
                        continue
                except DoesNotExist:
                    logger.info('Saving tag spot relationship: {}'.format(item_saving["partitionKey"]))
                    model = TagSpotModel(item_saving["partitionKey"], item_saving["sortKey"])
                    model.save()
                    results.append(model.attribute_values)
                # End of for loop
            for tag_name in tags_to_cleanup:
                item_to_cleanup = {
                    "partitionKey": 'tagSpotRelation#{}#{}'.format(attr.get('spotGroupId'), tag_name),
                    "sortKey": attr.get('sortKey'),
                }
                try:
                    item_found = TagSpotModel.get(item_to_cleanup["partitionKey"], item_to_cleanup["sortKey"])
                    if (item_found):
                        logger.debug('Cleaning up tag spot relationship: {}'.format(item_to_cleanup))
                        item_found.delete()
                except DoesNotExist:
                    continue
                # End of for loop
            return results
        except Exception as err:
            raise err

    def _generate_geohash(self, latitude, longitude):
        """
        Generate geohash from latitude and longitude
        """
        latitude_exists = len(str(latitude)) > 0
        longitude_exists = len(str(longitude)) > 0
        if latitude_exists and longitude_exists:
            return geohash.encode(float(latitude), float(longitude), 12)
        return None

    def _handle_error(self, err):
        """
        Handle exception and return Flask response
        """
        logger.error(err, exc_info=True)
        if (isinstance(err, AttributeNullError) or isinstance(err, PutError) or isinstance(err, SpotValidationError)):
            errcode = MessageCode.SE_P_0040
            resp = {
                "errorCode": errcode,
                "errorMessage": Message().get_message(errcode).format(str(err)),
            }
            return jsonify(resp), HTTPStatus.BAD_REQUEST
        else:
            errcode = MessageCode.SE_S_0001
            resp = {
                "errorCode": errcode,
                "errorMessage": Message().get_message(errcode).format(str(err)),
            }
            return jsonify(resp), HTTPStatus.INTERNAL_SERVER_ERROR

    def invoke_csv_import_job(self, req: Dict):
        lambda_client = boto3.client('lambda')
        try:
            function_to_invoke = os.environ.get('CSV_IMPORT_FUNCTION_NAME', '')
            resp = lambda_client.invoke(
                FunctionName=function_to_invoke,
                InvocationType='Event',
                Payload=json.dumps({
                    "filePath": req.get('filePath'),
                    "spotGroupId": req.get('spotGroupId'),
                    "createdBy": req.get('createdBy'),
                }),
            )
            logger.debug(resp)
            return jsonify({"data": ""}), HTTPStatus.OK
        except Exception as err:
            return self._handle_error(err)

    def export_spots_to_csv(self, req: Dict):
        """
        Export spots to CSV format
        """
        try:
            spot_group_id = req.get('spotGroupId')
            if not spot_group_id:
                return jsonify({
                    "errorCode": "MISSING_SPOT_GROUP_ID",
                    "errorMessage": "spotGroupId parameter is required"
                }), HTTPStatus.BAD_REQUEST

            export_service = CsvExportService()
            success, csv_url, error_message = export_service.export_spots_to_csv(spot_group_id)

            if success:
                return jsonify({
                    "data": {
                        "csvUrl": csv_url,
                        "message": "CSV export completed successfully"
                    }
                }), HTTPStatus.OK
            else:
                return jsonify({
                    "errorCode": "EXPORT_FAILED",
                    "errorMessage": error_message or "Failed to export spots to CSV"
                }), HTTPStatus.INTERNAL_SERVER_ERROR

        except Exception as err:
            return self._handle_error(err)
