import json
import io
import os
import time
import csv
import sys
import boto3
import geohash
from http import HTTPStatus
import logging as _logging
from typing import Dict, List
from pynamodb.exceptions import Does<PERSON>otExist, PutError, AttributeNullError

from models.csv_import_model import CSVImportModel
from models.spot_group_model import SpotGroupModel
from models.spot_model import SpotModel, TagSpotModel
from validators.spot_validator import SpotValidator
from constants.spot_prefix import SpotPrefix
from constants.response_def import ResponseDef
from common.exceptions import InvalidCsvHeader, SpotValidationError
from common.message import Message, MessageCode
from services.spot_group_service import SpotGroupService

# Logger setup
logger = _logging.getLogger()

CSV_DELIMITER = ','
DEFAULT_REQUIRED_CSV_FIELDS = ['ID', 'タグ', '画像', '緯度', '経度']

class CsvImportUpdateService:
    
    def __init__(self):
        self.csv_fields = DEFAULT_REQUIRED_CSV_FIELDS
        self.spot_attribute_map = {}
        self.spot_group_id = None
        self.csv_source = None
        self.url_attribute = None

    def write_imported_csv_to_dynamodb(self, data: Dict):
        """
        Update existing spots in DynamoDB based on CSV data with spot IDs
        :returns None: returns none but instead writes the result and errors (if any) to DynamoDB
        """
        logger.info('write_imported_csv_to_dynamodb() start - UPDATE MODE')
        self.spot_group_id = data.get("spotGroupId")
        self.csv_source = data.get("filePath")
        batch_items: List[Dict] = []
        updated_spots = []
        updated_tag_spots = []
        
        try:
            s3_client = boto3.client('s3')
            spot_data = self._convert_csv_to_dict(s3_client, self.csv_source, data)

            error_row_index = 0
            import_status = self._get_import_status()
            if import_status:
                if 'status' in import_status and import_status['status'] == 'FINISHED':
                    finished = {"result": "OK", "message": "Import update has already been finished"}
                    logger.info(f'finished: {finished}')
                    return finished
                if 'errors' in import_status and \
                    (len(import_status['errors']) > 0 and
                     'row' in import_status['errors'][0]):
                    error_row_index = import_status['errors'][0]['row']
                    logger.error(f'write_imported_csv_to_dynamodb() found row with errors: {error_row_index}')

            logger.debug(f'write_imported_csv_to_dynamodb() processing spot_data for update, size: {len(spot_data)}')
            
            # Prepare batch items for update
            for spot_item in spot_data:
                if spot_item['rowIndex'] < error_row_index:
                    continue
                    
                # Check if spot exists
                existing_spot = self._get_existing_spot(spot_item['sortKey'])
                if existing_spot is None:
                    logger.warning(f"Spot with ID {spot_item['sortKey']} not found, skipping update")
                    continue
                
                # Prepare update attributes, preserving creation metadata
                attrs = {
                    "partitionKey": spot_item['partitionKey'],
                    "sortKey": spot_item['sortKey'],
                    "latitude": spot_item['latitude'],
                    "longitude": spot_item['longitude'],
                    "latLngGeohash": spot_item['latLngGeohash'] if 'latLngGeohash' in spot_item else None,
                    "attributes": spot_item['attributes'],
                    "urlAttribute": spot_item['urlAttribute'],
                    "tags": spot_item['tags'],
                    "image": spot_item['image'],
                    "csvSource": spot_item['csvSource'],
                    "updatedBy": spot_item['updatedBy'],
                    "updatedAt": spot_item['updatedAt'],
                    "rowIndex": spot_item['rowIndex'],
                    "spotGroupId": spot_item['spotGroupId'],
                    # Preserve original creation metadata
                    "createdBy": existing_spot.createdBy,
                    "createdAt": existing_spot.createdAt,
                }
                batch_items.append(attrs)
                
            self._update_import_status({"status": 'IN_PROGRESS'})
            logger.debug('write_imported_csv_to_dynamodb() batch_start - UPDATE MODE')

            spot_group_model = SpotGroupModel.get(
                SpotPrefix.SPOT_GROUP_PREFIX,
                self.spot_group_id
            )
            validator = SpotValidator(url_required=spot_group_model.is_url_required())
            logger.debug(f'write_imported_csv_to_dynamodb() validator url required {spot_group_model.is_url_required()}')

            # Validate all items before updating
            for item_attrs in batch_items:
                validator.item_validated(item_attrs)
                
            spot_group_attrs = spot_group_model.attribute_values
            
            # Update spots in batch
            with SpotModel.batch_write() as batch:
                for item_attrs in batch_items:
                    logger.debug('write_imported_csv_to_dynamodb() batch_update_spot')
                    spot_group_id = item_attrs.pop('spotGroupId')
                    row = item_attrs.pop('rowIndex')
                    
                    # Get existing spot to handle tag relationships
                    existing_spot = self._get_existing_spot(item_attrs['sortKey'])
                    old_tags = existing_spot.tags if existing_spot and existing_spot.tags else []
                    new_tags = item_attrs['tags']
                    
                    # Update the spot
                    model = SpotModel(**item_attrs)
                    model.set_row_index(row)
                    batch.save(model)
                    updated_spots.append(model)
                    
                    # Handle tag relationships
                    self._update_tag_spot_relationships(
                        spot_group_id, 
                        item_attrs['sortKey'], 
                        old_tags, 
                        new_tags,
                        item_attrs['updatedBy'],
                        item_attrs['updatedAt']
                    )
                    
                    # Update spot group tags if needed
                    for tag_name in new_tags:
                        if tag_name not in spot_group_attrs['tags']:
                            spot_group_attrs['tags'].append(tag_name)
                            SpotGroupModel(**spot_group_attrs).save()
                            
            self._update_import_status({
                "status": 'FINISHED',
                "errors": [],
                "importFailed": 0,
                "importSuccess": len(batch_items),
            })
            logger.info('write_imported_csv_to_dynamodb() batch_end - UPDATE MODE')
            
        except InvalidCsvHeader as invalid_header_err:
            logger.error(str(invalid_header_err))
            self._update_import_status({
                "status": 'ERROR',
                "errors": [{
                    "messages": [str(invalid_header_err)],
                    "row": 0,
                }],
                "importFailed": len(batch_items),
            })
            sys.exit(1)
        except SpotValidationError as validation_err:
            logger.error(str(validation_err))
            self._update_import_status({
                "status": 'ERROR',
                "errors": [{
                    "messages": validation_err.get_errors(),
                    "row": validation_err.get_row_index(),
                }],
                "importFailed": len(batch_items),
                "importSuccess": 0,
            })
            sys.exit(1)
        except Exception as err:
            logger.error(str(err))
            errcode = MessageCode.SE_S_0001
            self._update_import_status({
                "status": 'ERROR',
                "errors": [{"messages": [
                    Message().get_message(errcode).format(str(err))
                ], "row": 0}],
                "importFailed": len(batch_items),
            })
            return self._handle_error(err)

    def _get_existing_spot(self, spot_id: str) -> SpotModel:
        """Get existing spot by ID"""
        try:
            partition_key = f'{SpotPrefix.SPOT_PREFIX}#{self.spot_group_id}'
            return SpotModel.get(partition_key, spot_id)
        except DoesNotExist:
            return None
        except Exception as e:
            logger.error(f"Error getting existing spot {spot_id}: {str(e)}")
            return None

    def _update_tag_spot_relationships(self, spot_group_id: str, spot_id: str, old_tags: List[str], new_tags: List[str], updated_by: str, updated_at: int):
        """Update tag-spot relationships for updated spot"""
        try:
            # Tags to add
            tags_to_add = list(set(new_tags) - set(old_tags))
            # Tags to remove  
            tags_to_remove = list(set(old_tags) - set(new_tags))
            
            # Add new tag relationships
            for tag_name in tags_to_add:
                partition_key = f'{SpotPrefix.TAG_SPOT_RELATION_PREFIX}#{spot_group_id}#{tag_name}'
                try:
                    # Check if relationship already exists
                    TagSpotModel.get(partition_key, spot_id)
                    logger.debug(f'TagSpotRelation already exists: {partition_key}')
                except DoesNotExist:
                    # Create new relationship
                    model = TagSpotModel(
                        partitionKey=partition_key,
                        sortKey=spot_id,
                        createdBy=updated_by,
                        createdAt=updated_at,
                        updatedBy=updated_by,
                        updatedAt=updated_at
                    )
                    model.save()
                    logger.info(f'Created tag spot relationship: {partition_key}')
            
            # Remove old tag relationships
            for tag_name in tags_to_remove:
                partition_key = f'{SpotPrefix.TAG_SPOT_RELATION_PREFIX}#{spot_group_id}#{tag_name}'
                try:
                    relationship = TagSpotModel.get(partition_key, spot_id)
                    relationship.delete()
                    logger.info(f'Deleted tag spot relationship: {partition_key}')
                except DoesNotExist:
                    logger.debug(f'TagSpotRelation not found for deletion: {partition_key}')
                    
        except Exception as e:
            logger.error(f"Error updating tag relationships: {str(e)}")
            raise e
