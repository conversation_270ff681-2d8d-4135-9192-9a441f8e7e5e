import json
import os
import logging as _logging
from typing import Dict
from typing import List
from flask import jsonify
from http import HTT<PERSON>tatus
from boto3.dynamodb.types import TypeSerializer
from pynamodb.exceptions import PutError, AttributeNullError
from pynamodb.connection import Connection

from constants.scenario_data import LOCATION_TRIGGER_DATA
from common.exceptions import SpotValidationError
from common.message import Message, MessageCode
from services.chat_spot_base_service import ChatSpotBaseService
from utils.json_util import JsonUtil

BATCH_SIZE = 25

# Logger setup
logger = _logging.getLogger()

# DynamoDB serializer
serializer = TypeSerializer()


class ScenarioDataNotFound(Exception):
    pass

class ScenarioDataService(ChatSpotBaseService):

    def __init__(self):
        self.region = 'ap-northeast-1'
        self.table_name = os.environ.get('TABLE_CHATBOT_SCENARIO_DATA', '')

    def add_spot_location_picker_scenario(self, spot_group_id: str):
        """
        位置情報ピッカーのシナリオを追加する
        """
        logger.debug('scenario_data_service.add_spot_location_picker_scenario starting')
        try:
            scenario_data_json = LOCATION_TRIGGER_DATA.copy()
            spot_group_item = self.get_spot_group(spot_group_id)
            JsonUtil().replace_element_deep_by_id_and_val(
                scenario_data_json,
                "text",
                "{文言}",
                spot_group_item.get_scenario_trigger_settings().get('locationSearchText')
            )
            logger.debug(f'scenario_data_service.add_spot_location_picker_scenario scenario_data_json: {repr(scenario_data_json)}')
            scenario_versions = self._list_scenario_versions()
            all_items = [
                {
                    'scenario': scenario,
                    'dataId': f'{spot_group_id}#location',
                    'dataType': scenario_data_json['dataType'],
                    'nameLBD': {'S': scenario_data_json['name']},
                    'params': serializer.serialize(scenario_data_json['params']),
                }
                for scenario in scenario_versions
            ]
            for i in range(0, len(all_items), BATCH_SIZE):
                batch = all_items[i:i + BATCH_SIZE]
                # write batch with each 25 items to DynamoDB
                self._get_db_connection().batch_write_item(
                    table_name=self.table_name,
                    put_items=batch
                )
            return True
        except Exception as err:
            return self._handle_error(err)

    def check_scenario_versions(self):
        """
        Check scenario versions, return True if any version is present
        """
        try:
            scenario_versions = self._list_scenario_versions()
            if len(scenario_versions) == 0:
                raise ScenarioDataNotFound(
                    Message().get_message(MessageCode.SE_SD_0040)
                )
            return True
        except Exception as err:
            return self._handle_error(err)

    def _list_scenario_versions(self):
        """
        List all scenario versions, querying scenario data table
        """
        response = self._get_db_connection().query(
            table_name=self.table_name,
            index_name='gsi-dataType-dataId-index',
            hash_key='talk', # Use talk dataType for the query as it has less cardinality than the others
        )
        scenario_list = []
        for item in response['Items']:
            if 'scenario' not in item:
                continue
            if item['scenario']['S'] not in scenario_list:
                scenario_list.append(item['scenario']['S'])
        logger.debug(f'scenario_data_service._list_scenario_versions scenario_list: {repr(scenario_list)}')
        return scenario_list

    def _get_db_connection(self) -> Connection:
        connection = Connection(region=self.region)
        return connection

    def _handle_error(self, err):
        """
        Handle exception and return Flask response
        """
        logger.error(err, exc_info=True)
        if (isinstance(err, AttributeNullError) or isinstance(err, PutError) or isinstance(err, SpotValidationError)):
            errcode = MessageCode.SE_P_0040
            resp = {
                "errorCode": errcode,
                "errorMessage": Message().get_message(errcode).format(str(err)),
            }
            return jsonify(resp), HTTPStatus.BAD_REQUEST
        elif isinstance(err, ScenarioDataNotFound):
            errcode = MessageCode.SE_SD_0040
            resp = {
                "errorCode": errcode,
                "errorMessage": str(err),
            }
            return jsonify(resp), HTTPStatus.BAD_REQUEST
        else:
            errcode = MessageCode.SE_S_0001
            resp = {
                "errorCode": errcode,
                "errorMessage": Message().get_message(errcode).format(str(err)),
            }
            return jsonify(resp), HTTPStatus.INTERNAL_SERVER_ERROR
