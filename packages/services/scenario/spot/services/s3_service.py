import logging as _logging

from flask import jsonify
from http import HTTPStatus

from utils.s3_util import S3_RESOURCE
from constants.s3_resource import S3_BUCKET_RESOURCES, DEFAULT_SPOT_BUCKET_FOLDER
from common.message import Message, MessageCode
from constants.response_def import ResponseDef

# Logger setup
logger = _logging.getLogger()

class S3Service():
    def __init__(self):
        pass
    
    def get_presigned_url(self, objectName, folderName = None, contentType = None):
        try:
            if not objectName:
                return jsonify({ResponseDef.ERR_CODE: MessageCode.SE_C_0000, ResponseDef.ERROR_MSG: Message().get_message(MessageCode().SE_C_0000).format('objectName')}), HTTPStatus.BAD_REQUEST
            
            object_name = None
            if folderName:
                object_name = folderName + "/" + objectName
            else:
                object_name = DEFAULT_SPOT_BUCKET_FOLDER + "/" + objectName # Preset object path

            expiration = 60  # 1 minute

            s3_client = S3_RESOURCE(S3_BUCKET_RESOURCES)

            fields = None
            conditions = []

            if contentType:
                fields = {
                    "Content-Type": contentType
                }

                conditions.append({
                    "Content-Type": contentType
                })

            response = s3_client.generate_presigned_post(object_name, fields, conditions, expiration)

            result = {}
            result["url"] = response["url"]
            result["fields"] = response["fields"]
            return jsonify(result), HTTPStatus.OK
        except Exception as err:
            errMsg = "Exception: " + str(err)
            logger.error(err)
            return jsonify({"errorCode": MessageCode.SE_S_0001, "errorMessage": Message().get_message(MessageCode().SE_S_0001).format(errMsg)}), HTTPStatus.INTERNAL_SERVER_ERROR