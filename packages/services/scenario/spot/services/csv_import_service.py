#!/usr/bin/env python3
"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import json
import io
import os
import time
import csv
import uuid
import sys
import boto3
import geohash as pygeohash
from http import HTTPStatus
import logging as _logging
from typing import Dict
from typing import List
from pynamodb.exceptions import DoesNotExist, PutError, AttributeNullError

from models.csv_import_model import CSVImportModel
from models.spot_group_model import SpotGroupModel
from models.spot_model import SpotModel, TagSpotModel
from validators.spot_validator import SpotValidator
from constants.spot_prefix import SpotPrefix
from constants.response_def import ResponseDef
from common.exceptions import InvalidCsvHeader, SpotValidationError
from common.message import Message, MessageCode
from services.spot_group_service import SpotGroupService


# Logger setup
logger = _logging.getLogger()

CSV_DELIMITER = ','
DEFAULT_REQUIRED_CSV_FIELDS = ['タグ', '画像', '緯度', '経度']
DEFAULT_REQUIRED_CSV_FIELDS_UPDATE = ['ID', 'タグ', '画像', '緯度', '経度']

class CsvImportService():

    def __init__(self):
        self.csv_fields = DEFAULT_REQUIRED_CSV_FIELDS
        self.spot_attribute_map = {}
        self.spot_group_id = None
        self.csv_source = None
        self.url_attribute = None
        self.is_update_mode = False

    def get_csv_imported_info(self, spotGroupId):
        """Function that query for the scenario csv imported information data from spot table in DynamoDB.
            :returns str: JSON with the query results.
            """

        items = []
        try:
            # partitionKey
            pk = SpotPrefix.CSV_IMPORT_PREFIX + SpotPrefix.SHARP_CHARACTER + spotGroupId

            result_iterator = CSVImportModel.query(pk)
            for item in result_iterator:
                items.append(item.as_dict())
                break
        except Exception as exception:
            errMsg = "Exception: " + str(exception)
            logger.error(
                f'csv_import_service.get_csv_imported_info, errMsg= {errMsg}')
            return json.dumps({ResponseDef.ERR_CODE: MessageCode.SE_S_0001, ResponseDef.ERROR_MSG: Message().get_message(MessageCode.SE_S_0001).format(errMsg)}), HTTPStatus.INTERNAL_SERVER_ERROR
        else:
            logger.info(
                f'Get csv imported information spotGroupId= {spotGroupId}')
            return json.dumps({ResponseDef.DATA: items}, indent=4, ensure_ascii=False), HTTPStatus.OK

    def write_imported_csv_to_dynamodb(self, data: Dict):
        """
            Write the data to DynamoDB, converting it to Dictionary CSV uploaded to S3
            Supports both import (create new) and update (modify existing) operations
            :returns None: returns none but instead writes the result and errors (if any) to DynamoDB
        """
        self.is_update_mode = data.get("isUpdate", False)
        logger.info(f'write_imported_csv_to_dynamodb() data - {data}, is_update_mode={self.is_update_mode}')

        self.spot_group_id = data.get("spotGroupId")
        self.csv_source = data.get("filePath")

        # Set appropriate CSV fields based on operation mode
        if self.is_update_mode:
            self.csv_fields = DEFAULT_REQUIRED_CSV_FIELDS_UPDATE
        else:
            self.csv_fields = DEFAULT_REQUIRED_CSV_FIELDS

        batch_items: List[Dict] = []
        added_spots = []
        added_tag_spots = []
        try:
            s3_client = boto3.client('s3')
            spot_data = self._convert_csv_to_dict(s3_client, self.csv_source, data)

            import_status = self._get_import_status()
            existing_spot_count = SpotModel.count(
                SpotPrefix.SPOT_PREFIX + SpotPrefix.SHARP_CHARACTER + self.spot_group_id
            )
            if import_status:
                if 'status' in import_status and import_status['status'] == 'FINISHED' and not self.is_update_mode and existing_spot_count > 0:
                    operation_type = "Import update" if self.is_update_mode else "Import"
                    finished = {"result": "OK", "message": f"{operation_type} has already been finished"}
                    logger.info(f'finished: {finished}')
                    return finished

            logger.debug(f'write_imported_csv_to_dynamodb() processing spot_data, size: {len(spot_data)}')

            # Process spots differently based on operation mode
            if self.is_update_mode:
                batch_items = self._prepare_update_batch_items(spot_data)
            else:
                batch_items = self._prepare_create_batch_items(spot_data)
            self._update_import_status({"status": 'IN_PROGRESS'})
            logger.debug('write_imported_csv_to_dynamodb() batch_start')

            spot_group_model = SpotGroupModel.get(
                SpotPrefix.SPOT_GROUP_PREFIX,
                self.spot_group_id
            )
            validator = SpotValidator(url_required=spot_group_model.is_url_required())
            logger.debug(f'write_imported_csv_to_dynamodb() validator url required {spot_group_model.is_url_required()}')

            # validate all items and collect errors
            all_errors = {}
            
            for item_attrs in batch_items:
                try:
                    validator.item_validated(item_attrs, True)
                except SpotValidationError as err:
                    row_index = item_attrs['rowIndex']
                    # Store errors by row index
                    all_errors[row_index] = err.get_errors()
            
            # If any validation errors were found, raise a combined error
            if all_errors:
                row_indices = list(all_errors.keys())
                error_messages = list(all_errors.values())
                raise SpotValidationError(error_messages, row_indices)

            spot_group_attrs = spot_group_model.attribute_values

            # Execute batch operations based on mode
            if self.is_update_mode:
                added_spots, added_tag_spots = self._execute_update_batch(batch_items, spot_group_attrs)
            else:
                added_spots, added_tag_spots = self._execute_create_batch(batch_items, spot_group_attrs)
            self._update_import_status({
                "status": 'FINISHED',
                "errors": [],
                "importFailed": 0,
                "importSuccess": len(batch_items),
            })
            logger.info('write_imported_csv_to_dynamodb() batch_end')
        except InvalidCsvHeader as invalid_header_err:
            logger.error(str(invalid_header_err))
            self._rollback(added_spots, added_tag_spots)
            self._update_import_status({
                "status": 'ERROR',
                "errors": [{
                    "messages": [str(invalid_header_err)],
                    "row": 0,
                }],
                "importFailed": len(batch_items),
            })
            sys.exit(1)
        except SpotValidationError as validation_err:
            logger.error(str(validation_err))
            self._rollback(added_spots, added_tag_spots)
            
            # Handle multiple rows with errors
            errors = []
            row_indices = validation_err.get_row_index()
            
            if isinstance(row_indices, list):
                # Multiple rows with errors
                row_errors = validation_err.get_row_errors()
                for row_idx in row_errors:
                    errors.append({
                        "messages": row_errors[row_idx],
                        "row": row_idx,
                    })
            else:
                # Single row with errors
                errors.append({
                    "messages": validation_err.get_errors(),
                    "row": validation_err.get_row_index(),
                })
                
            # If there is any error in the items, it stops the import process and writes the status to DB
            self._update_import_status({
                "status": 'ERROR',
                "errors": errors,
                "importFailed": len(batch_items),
                "importSuccess": 0,
            })
            sys.exit(1)
        except Exception as err:
            logger.error(str(err))
            self._rollback(added_spots, added_tag_spots)
            errcode = MessageCode.SE_S_0001
            self._update_import_status({
                "status": 'ERROR',
                "errors": [{"messages": [
                    Message().get_message(errcode).format(str(err))
                ], "row": 0}],
                "importFailed": len(batch_items),
            })
            return self._handle_error(err)

    def _rollback(self, added_spots: List[SpotModel], added_tag_spots: List[TagSpotModel]):
        service = SpotGroupService()
        service.batch_delete_by_model(TagSpotModel, added_tag_spots)
        service.batch_delete_by_model(SpotModel, added_spots)
        
    def _get_import_status(self):
        pk_prefix = SpotPrefix.CSV_IMPORT_PREFIX
        partition_key = '{}#{}'.format(pk_prefix, self.spot_group_id)
        sort_key = 'RESERVED' # TODO Fix sort key to use UUID
        try:
            item = CSVImportModel.get(partition_key, sort_key)
            return item.as_dict()
        except DoesNotExist:
            return None
        except Exception as err:
            return self._handle_error(err)

    def _update_import_status(self, attributes: Dict):
        pk_prefix = SpotPrefix.CSV_IMPORT_PREFIX
        partition_key = '{}#{}'.format(pk_prefix, self.spot_group_id)
        sort_key = 'RESERVED' # TODO Fix sort key to use UUID
        try:
            item = CSVImportModel.get(partition_key, sort_key)
            attrs_to_save = {
                "partitionKey": partition_key,
                "sortKey": sort_key,
                "csvSource": self.csv_source,
                "status": attributes.get("status") if attributes.get("status") else item.status,
                "errors": attributes.get("errors") if 'errors' in attributes else item.errors,
                "importFailed": attributes.get("importFailed") if 'importFailed' in attributes else item.importFailed,
                "importSuccess": attributes.get("importSuccess") if 'importSuccess' in attributes else item.importSuccess,
            }
            CSVImportModel(**attrs_to_save).save()
        except DoesNotExist:
            attributes['partitionKey'] = partition_key
            attributes['sortKey'] = sort_key
            attributes['csvSource'] = self.csv_source
            CSVImportModel(**attributes).save()
        except Exception as err:
            return self._handle_error(err)

    def _convert_csv_to_dict(self, s3_client, object_key: str, req: Dict):
        """
        Get CSV file from S3 bucket and convert to dictionary data
        """
        try:
            result = []
            s3_resp = s3_client.get_object(
                Bucket=os.environ.get('BUCKET_CHATBOT_IMPORTED_RESOURCES', ''),
                Key=object_key
            )
            csv_content = io.StringIO(
                s3_resp['Body'].read().decode('utf-8')
            )
            fieldnames = self._get_csv_header_validated(csv_content, req)
            spot_attrs = self._get_spot_attribute_map(req)
            csv_reader = csv.DictReader(
                csv_content,
                delimiter=CSV_DELIMITER,
                fieldnames=fieldnames,
            )
            row_index = 0
            for row in csv_reader:
                row_index += 1
                operation_mode = "UPDATE" if self.is_update_mode else "CREATE"
                logger.debug(f'convert_csv_to_dict() {operation_mode} row {row_index}: {repr(row)}')

                partition_key = '{}#{}'.format(SpotPrefix.SPOT_PREFIX, req.get('spotGroupId'))

                # Handle sort_key based on operation mode
                if self.is_update_mode:
                    # Use existing spot ID from CSV
                    sort_key = row['ID'].strip()
                    if not sort_key:
                        error_msg = f"スポットIDが空です。"
                        logger.error(error_msg)
                        # Format as dictionary for SpotValidationError
                        error_dict = {"spotId": [error_msg]}
                        raise SpotValidationError(error_dict, row_index)
                else:
                    # Generate new spot ID for create
                    sort_key = '{}_{}'.format(SpotPrefix.SPOT_PREFIX, str(uuid.uuid4()))

                latitude_exists = len(row['緯度']) > 0
                longitude_exists = len(row['経度']) > 0
                latitude_value = float(row['緯度']) if latitude_exists else ''
                longitude_value = float(row['経度']) if longitude_exists else ''
                url_attribute = SpotModel.default_url_attribute()
                if self.url_attribute['attributeName'] in row:
                    url_attribute['value'] = row[self.url_attribute['attributeName']]

                def get_attribute_value(row: Dict, attribute_name: str):
                    if attribute_name in row:
                        return row[attribute_name]
                    else:
                        raise InvalidCsvHeader([attribute_name])

                current_time = int(time.time() * 1000)
                item_to_append = {
                    "rowIndex": row_index,
                    "spotGroupId": req.get('spotGroupId'),
                    "partitionKey": partition_key,
                    "sortKey": sort_key,
                    "latitude": latitude_value,
                    "longitude": longitude_value,
                    "attributes": [{
                        "id": attr.id,
                        "value": get_attribute_value(row, attr.attributeName)
                    } for attr in spot_attrs.values()],
                    "urlAttribute": url_attribute,
                    "tags": row['タグ'].split(',') if len(row['タグ']) > 0 else [],
                    "image": row['画像'],
                    "csvSource": object_key,
                }

                # Add appropriate timestamp fields based on operation mode
                if self.is_update_mode:
                    item_to_append.update({
                        "updatedBy": req.get('createdBy'),
                        "updatedAt": current_time,
                    })
                else:
                    item_to_append.update({
                        "createdBy": req.get('createdBy'),
                        "createdAt": current_time,
                    })

                if latitude_exists and longitude_exists:
                    item_to_append['latLngGeohash'] = pygeohash.encode(latitude_value, longitude_value, 12)
                result.append(item_to_append)
            return result
        except InvalidCsvHeader as invalid_header_err:
            raise invalid_header_err
        except Exception as err:
            raise err

    def _get_csv_header_validated(self, csv_content: io.StringIO, req: Dict):
        try:
            spot_attr = self._get_spot_attribute_map(req)
            reader = csv.reader(csv_content, delimiter=CSV_DELIMITER)
            header = reader.__next__()
            error_csv_fields = []
            for field in self.csv_fields:
                if (field in spot_attr and not bool(spot_attr[field].required)): # if spot attribute is set to optional
                    continue
                if (field not in header):
                    error_csv_fields.append(field)

            url_attribute_name = self.url_attribute.get('attributeName', None)
            if url_attribute_name and self.url_attribute.get('required', False):
                if not url_attribute_name in header:
                    error_csv_fields.append(url_attribute_name)

            if (len(error_csv_fields) > 0):
                raise InvalidCsvHeader(error_csv_fields)
            return header
        except Exception as err:
            raise err

    def _get_spot_attribute_map(self, req: Dict):
        """
        Return custom spot attributes
        """
        spot_group_item = {}
        spot_group_hash = SpotPrefix.SPOT_GROUP_PREFIX
        spot_group_range = req.get("spotGroupId")
        try:
            if not bool(self.spot_attribute_map) or not self.url_attribute: # if map is empty
                spot_group_item = SpotGroupModel.get(spot_group_hash, spot_group_range)
                self.url_attribute = spot_group_item.get_spot_url_attribute()

                for spot_attribute in spot_group_item.attributes:
                    self.spot_attribute_map[spot_attribute.attributeName] = spot_attribute
                    self.csv_fields.append(spot_attribute.attributeName)
                return self.spot_attribute_map
            else:
                return self.spot_attribute_map
        except DoesNotExist:
            raise DoesNotExist
        except Exception as err:
            raise err

    def _handle_error(self, err):
        """
        Handle exception
        """
        # TODO (low priority) it is better to configure destination for async event
        logger.error(err, exc_info=True)
        sys.exit(1)

    def _prepare_create_batch_items(self, spot_data: List[Dict]) -> List[Dict]:
        """Prepare batch items for create operation"""
        batch_items = []
        for spot_item in spot_data:
            attrs = {
                "partitionKey": spot_item['partitionKey'],
                "sortKey": spot_item['sortKey'],
                "latitude": spot_item['latitude'],
                "longitude": spot_item['longitude'],
                "latLngGeohash": spot_item['latLngGeohash'] if 'latLngGeohash' in spot_item else None,
                "attributes": spot_item['attributes'],
                "urlAttribute": spot_item['urlAttribute'],
                "tags": spot_item['tags'],
                "image": spot_item['image'],
                "csvSource": spot_item['csvSource'],
                "createdBy": spot_item['createdBy'],
                "createdAt": spot_item['createdAt'],
                "rowIndex": spot_item['rowIndex'],
                "spotGroupId": spot_item['spotGroupId'],
            }
            batch_items.append(attrs)
        return batch_items

    def _prepare_update_batch_items(self, spot_data: List[Dict]) -> List[Dict]:
        """Prepare batch items for update operation"""
        batch_items = []
        missing_spots = []

        for spot_item in spot_data:
            existing_spot = self._get_existing_spot(spot_item['sortKey'])
            if existing_spot is None:
                error_msg = f"存在していません。"
                logger.error(f"Row {spot_item['rowIndex']}: {error_msg}")
                missing_spots.append({
                    "row": spot_item['rowIndex'],
                    "spotId": spot_item['sortKey'],
                    "message": error_msg
                })
                continue

            # Prepare update attributes, preserving creation metadata
            attrs = {
                "partitionKey": spot_item['partitionKey'],
                "sortKey": spot_item['sortKey'],
                "latitude": spot_item['latitude'],
                "longitude": spot_item['longitude'],
                "latLngGeohash": spot_item['latLngGeohash'] if 'latLngGeohash' in spot_item else None,
                "attributes": spot_item['attributes'],
                "urlAttribute": spot_item['urlAttribute'],
                "tags": spot_item['tags'],
                "image": spot_item['image'],
                "csvSource": spot_item['csvSource'],
                "updatedBy": spot_item['updatedBy'],
                "updatedAt": spot_item['updatedAt'],
                "rowIndex": spot_item['rowIndex'],
                "spotGroupId": spot_item['spotGroupId'],
                "createdBy": existing_spot.createdBy,
                "createdAt": existing_spot.createdAt,
                "oldTags": existing_spot.tags if existing_spot.tags else [],
            }
            batch_items.append(attrs)

        if missing_spots:
            error_dicts = []
            row_indices = []
            for item in missing_spots:
                # Create a simple message with just the ID and error
                error_dict = {"spotId": [f"{item['spotId']} が存在していません。"]}
                error_dicts.append(error_dict)
                row_indices.append(item['row'])
            raise SpotValidationError(error_dicts, row_indices)

        return batch_items

    def _get_existing_spot(self, spot_id: str) -> SpotModel:
        """Get existing spot by ID"""
        try:
            partition_key = f'{SpotPrefix.SPOT_PREFIX}#{self.spot_group_id}'
            return SpotModel.get(partition_key, spot_id)
        except DoesNotExist:
            return None
        except Exception as e:
            logger.error(f"Error getting existing spot {spot_id}: {str(e)}")
            return None

    def _execute_create_batch(self, batch_items: List[Dict], spot_group_attrs: Dict) -> tuple:
        """Execute batch create operation"""
        added_spots = []
        added_tag_spots = []

        with SpotModel.batch_write() as batch:
            for item_attrs in batch_items:
                logger.debug('write_imported_csv_to_dynamodb() batch_save_spot - CREATE')
                spot_group_id = item_attrs.pop('spotGroupId')
                row = item_attrs.pop('rowIndex')
                model = SpotModel(**item_attrs)
                model.set_row_index(row)
                batch.save(model)
                added_spots.append(model)

                # Handle tag relationships for create
                self._create_tag_spot_relationships(
                    spot_group_id,
                    item_attrs['sortKey'],
                    item_attrs['tags'],
                    item_attrs['createdBy'],
                    item_attrs['createdAt'],
                    added_tag_spots
                )

                # Update spot group tags
                for tag_name in item_attrs['tags']:
                    if tag_name not in spot_group_attrs['tags']:
                        spot_group_attrs['tags'].append(tag_name)
                        SpotGroupModel(**spot_group_attrs).save()

        return added_spots, added_tag_spots

    def _execute_update_batch(self, batch_items: List[Dict], spot_group_attrs: Dict) -> tuple:
        """Execute batch update operation"""
        updated_spots = []
        updated_tag_spots = []

        with SpotModel.batch_write() as batch:
            for item_attrs in batch_items:
                logger.debug('write_imported_csv_to_dynamodb() batch_update_spot - UPDATE')
                spot_group_id = item_attrs.pop('spotGroupId')
                row = item_attrs.pop('rowIndex')
                old_tags = item_attrs.pop('oldTags', [])

                model = SpotModel(**item_attrs)
                model.set_row_index(row)
                batch.save(model)
                updated_spots.append(model)

                # Handle tag relationships for update
                self._update_tag_spot_relationships(
                    spot_group_id,
                    item_attrs['sortKey'],
                    old_tags,
                    item_attrs['tags'],
                    item_attrs['updatedBy'],
                    item_attrs['updatedAt']
                )

                # Update spot group tags
                for tag_name in item_attrs['tags']:
                    if tag_name not in spot_group_attrs['tags']:
                        spot_group_attrs['tags'].append(tag_name)
                        SpotGroupModel(**spot_group_attrs).save()

        return updated_spots, updated_tag_spots

    def _create_tag_spot_relationships(self, spot_group_id: str, spot_id: str, tags: List[str], created_by: str, created_at: int, added_tag_spots: List):
        """Create tag-spot relationships for new spots"""
        with SpotModel.batch_write() as batch_tag_spot:
            for tag_name in tags:
                partition_key = f'{SpotPrefix.TAG_SPOT_RELATION_PREFIX}#{spot_group_id}#{tag_name}'
                attrs_to_save = {
                    "partitionKey": partition_key,
                    "sortKey": spot_id,
                    "createdBy": created_by,
                    "createdAt": created_at,
                }
                model = TagSpotModel(**attrs_to_save)
                batch_tag_spot.save(model)
                added_tag_spots.append(model)

    def _update_tag_spot_relationships(self, spot_group_id: str, spot_id: str, old_tags: List[str], new_tags: List[str], updated_by: str, updated_at: int):
        """Update tag-spot relationships for updated spots"""
        try:
            tags_to_add = list(set(new_tags) - set(old_tags))
            tags_to_remove = list(set(old_tags) - set(new_tags))

            # Add new tag relationships
            for tag_name in tags_to_add:
                partition_key = f'{SpotPrefix.TAG_SPOT_RELATION_PREFIX}#{spot_group_id}#{tag_name}'
                try:
                    # Check if relationship already exists
                    TagSpotModel.get(partition_key, spot_id)
                    logger.debug(f'TagSpotRelation already exists: {partition_key}')
                except DoesNotExist:
                    # Create new relationship
                    model = TagSpotModel(
                        partitionKey=partition_key,
                        sortKey=spot_id,
                        createdBy=updated_by,
                        createdAt=updated_at,
                        updatedBy=updated_by,
                        updatedAt=updated_at
                    )
                    model.save()
                    logger.info(f'Created tag spot relationship: {partition_key}')

            # Remove old tag relationships
            for tag_name in tags_to_remove:
                partition_key = f'{SpotPrefix.TAG_SPOT_RELATION_PREFIX}#{spot_group_id}#{tag_name}'
                try:
                    relationship = TagSpotModel.get(partition_key, spot_id)
                    relationship.delete()
                    logger.info(f'Deleted tag spot relationship: {partition_key}')
                except DoesNotExist:
                    logger.debug(f'TagSpotRelation not found for deletion: {partition_key}')

        except Exception as e:
            logger.error(f"Error updating tag relationships: {str(e)}")
            raise e
