"""
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
"""
import json
from functools import cmp_to_key
import geohash as pgh
from haversine import haversine, Unit

from services.chat_spot_base_service import ChatSpotBaseService
from common.message import Message, MessageCode
from constants.s3_resource import S3_BUCKET_RESOURCES, SPOT_TEMPLATE_FILE_PATH
from constants.response_def import ResponseDef
from constants.spot_prefix import SpotPrefix
from models.spot_model import SpotModel
import logging as _logging
from http import HTTPStatus

# Logger setup
logger = _logging.getLogger("scenario-chat_spot_location_service")
logger.setLevel(_logging.INFO)

# if not logger.handlers:
#     handler = _logging.StreamHandler()
#     formatter = _logging.Formatter('%(asctime)s %(levelname)s %(message)s')
#     handler.setFormatter(formatter)
#     logger.addHandler(handler)

# Mapping of geohash percision variables to square boundary in m
geohashPrecisionBoundaries = {
    1: 5000000,
    2: 1250000,
    3: 156000,
    4: 39100,
    5: 4890,
    6: 1220,
    7: 153,
    8: 38,
    9: 5
}


class ChatSpotLocationService(ChatSpotBaseService):
    def get_all_neighbors_pygeohash(self, geohash_code):
        """
        Get 8 Geohashes of neighboring cells

        Args:
            geohash_code (str): Mã Geohash trung tâm.

        Returns:
            dict: Dictionary containing Geohashes of 8 neighboring cells in direction
        """
        neighbors = {}
        neighbor_list = pgh.neighbors(geohash_code)
        neighbors['top'] = neighbor_list[0]  # north
        neighbors['top-right'] = neighbor_list[1]  # northeast
        neighbors['right'] = neighbor_list[2]  # east
        neighbors['bottom-right'] = neighbor_list[3]  # southeast
        neighbors['bottom'] = neighbor_list[4]  # south
        neighbors['bottom-left'] = neighbor_list[5]  # southwest
        neighbors['left'] = neighbor_list[6]  # west
        neighbors['top-left'] = neighbor_list[7]  # northwest
        
        # format all_neighbors to CSV string for logging
        neighbors_csv = "direction,neighbor\n"
        for direction, neighbor in neighbors.items():
            neighbors_csv += f"{direction},{neighbor}\n"
        logger.info(f"All Neighbors (CSV):\n{neighbors_csv.strip()}")

        return neighbors
    
    def get_spots_by_location_for_line(self, spotGroupId, latitude, longitude):
        """Function that gets up to 10 spots within search range defined in spotGroup
            If no spots are found within search range then the no spots found message is returned

            :returns str: JSON with the query results.
            """
        if latitude is None or longitude is None:
            errMsg = Message().get_message(MessageCode.SE_C_0000).format(latitude + " " + longitude)
            logger.error(errMsg)
            raise Exception(errMsg)
            # return json.dumps({ResponseDef.ERR_CODE: MessageCode.SE_C_0000, ResponseDef.ERROR_MSG: Message().get_message(MessageCode().SE_C_0000).format('location')}), HTTPStatus.BAD_REQUEST

        location = pgh.encode(latitude, longitude)
        
        # get spot group by spotGroupId
        spot_group = self.get_spot_group(spotGroupId)
        if spot_group is None:
            errMsg = Message().get_message(MessageCode.SE_G_0001).format(spotGroupId)
            logger.error(errMsg)
            raise Exception(errMsg)
            # return json.dumps({ResponseDef.ERR_CODE: MessageCode.SE_G_0001, ResponseDef.ERROR_MSG: errMsg}), HTTPStatus.INTERNAL_SERVER_ERROR

        # get the number of precision digits that will be used to search for nearby spots
        scenario_trigger_settings = spot_group.get_scenario_trigger_settings()
        logger.debug('spotGroup.get_scenario_trigger_settings {}'.format(repr(scenario_trigger_settings)))
        location_search_distance = scenario_trigger_settings.get('locationSearchDistance') or 1000
        percision_digits = 1
        for digit, search_range in geohashPrecisionBoundaries.items():
            if search_range > location_search_distance:
                percision_digits = digit
            else:
                break

        geohash_center = location[:percision_digits]
        all_neighbors = self.get_all_neighbors_pygeohash(geohash_center)
        logger.info('location_search_distance: {}'.format(repr(location_search_distance)))
        logger.info('percision_digits: {}'.format(repr(percision_digits)))
        logger.info('location: {}'.format(repr(location)))
        logger.info('location[:percision_digits]: {}'.format(repr(geohash_center)))
        # search for spots
        spots = self.query_spots(spotGroupId, geohash_center)
        for direction, neighbor in all_neighbors.items():
            if neighbor is not None:
                neighbor_spots = self.query_spots(spotGroupId, neighbor)
                spots.extend(neighbor_spots)
        logger.info('len(spots): {}'.format(repr(len(spots))))

        if len(spots) == 0:
            return []
            # return json.dumps({ResponseDef.DATA: scenario_trigger_settings.get('locationSearchNotFoundText')}, indent=4, ensure_ascii=False), HTTPStatus.OK

        csv_lines = ["spot_name,description,distance_meters"] # for logging
        # calculate distance to location, then save to list
        spot_distances = []
        for spot in spots:
            # get value of attribute_00001
            name = next((attr["value"] for attr in spot.attributes if attr["id"] == "attribute_00001"), "N/A")
            # get value of attribute_00002
            description = next((attr["value"] for attr in spot.attributes if attr["id"] == "attribute_00002"), "N/A")
            distance = haversine((latitude, longitude), (spot.latitude, spot.longitude), unit=Unit.METERS)
            csv_lines.append(f"{name},{description},{distance:.2f}")
            if distance <= location_search_distance:
                spot_distances.append((spot, distance))

        # write log
        logger.info(" | ".join(csv_lines))
        
        # Sort by distance
        spot_distances.sort(key=lambda pair: pair[1])

        # only get up to 10 spots
        spots = [spot_item for (spot_item, _) in spot_distances][:10]
        logger.info(f'only get up to 10 spots: {repr(spots)}')

        # get template from S3
        templates = self.get_template()
        if templates is None:
            errMsg = Message().get_message(MessageCode.SE_C_2000).format(
                S3_BUCKET_RESOURCES, SPOT_TEMPLATE_FILE_PATH)
            logger.error(errMsg)
            raise Exception(errMsg)
            # return json.dumps({ResponseDef.ERR_CODE: MessageCode.SE_C_2000, ResponseDef.ERROR_MSG: errMsg}), HTTPStatus.INTERNAL_SERVER_ERROR

        # get template of spotGroup
        logger.debug('chat_spot_location_service.get_template_style_of_spot_group starting')
        selected_template = templates.get(spot_group.spotTemplate.id, None)
        if selected_template is None:
            errMsg = Message().get_message(
                MessageCode.SE_C_0002).format(spot_group.spotTemplate.id)
            logger.error(errMsg)
            raise Exception(errMsg)
            # return json.dumps({ResponseDef.ERR_CODE: MessageCode.SE_C_0002, ResponseDef.ERROR_MSG: errMsg}), HTTPStatus.INTERNAL_SERVER_ERROR

        # create spot line display
        data = self.create_spot_line_display(spot_group, selected_template, spots)
        return data

        # return json.dumps({ResponseDef.DATA: data}, indent=4, ensure_ascii=False), HTTPStatus.OK
    
    def get_spots_by_location_for_line_api(self, spotGroupId, latitude, longitude):
        """Function that gets up to 10 spots within search range defined in spotGroup
            If no spots are found within search range then the no spots found message is returned

            :returns str: JSON with the query results.
            """
        try:
            # create spot line display
            data = self.get_spots_by_location_for_line(spotGroupId, latitude, longitude)

            return json.dumps({ResponseDef.DATA: data}, indent=4, ensure_ascii=False), HTTPStatus.OK

        except Exception as exception:
            errMsg = "Exception: " + str(exception)
            logger.error(
                f'Get spotLocation error, chat_spot_location_service.get_spots_by_location_for_line, errMsg= {errMsg}',
                exc_info=True
            )
            return json.dumps({ResponseDef.ERR_CODE: MessageCode.SE_S_0001, ResponseDef.ERROR_MSG: Message().get_message(MessageCode.SE_S_0001).format(errMsg)}), HTTPStatus.INTERNAL_SERVER_ERROR

    def query_spots(self, spotGroupId, location):
        logger.debug('chat_spot_location_service.query_spots')
        spots = []
        try:
            # partitionKey
            pk = SpotPrefix.SPOT_PREFIX + SpotPrefix.SHARP_CHARACTER + spotGroupId

            # query on latLngGeohash index
            for spot in SpotModel.latLng_geohash_index.query(pk, SpotModel.latLngGeohash.startswith(location)):
                spots.append(spot)

            return spots
        except Exception as exception:
            errMsg = "Exception: " + str(exception)
            logger.error(
                f'Query for spots error, chat_spot_location_service.get_spots, errMsg= {errMsg}',
                exc_info=True
            )
            return spots
