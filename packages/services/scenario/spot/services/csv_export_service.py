import csv
import io
import os
import time
import boto3
import logging as _logging
from typing import Dict, List
from botocore.exceptions import ClientError
from pynamodb.exceptions import DoesNotExist

from models.spot_model import SpotModel
from models.spot_group_model import SpotGroupModel
from constants.spot_prefix import SpotPrefix

logger = _logging.getLogger()

class CsvExportService:
    
    def __init__(self):
        self.s3_client = boto3.client('s3')
        self.bucket_name = os.environ.get('BUCKET_CHATBOT_IMPORTED_RESOURCES', '')
    
    def export_spots_to_csv(self, spot_group_id: str) -> tuple:
        """
        Export spots for a given spot group to CSV format and upload to S3
        
        :param spot_group_id: The spot group ID to export spots for
        :returns: tuple (success: bool, csv_url: str, error_message: str)
        """
        try:
            spot_group = self._get_spot_group(spot_group_id)
            if not spot_group:
                return False, None, f"Spot group {spot_group_id} not found"
            
            # Get all spots for the spot group
            spots = self._get_spots_by_group_id(spot_group_id)
            if not spots:
                return False, None, f"No spots found for spot group {spot_group_id}"
            
            csv_content = self._generate_csv_content(spots, spot_group)
            csv_url = self._upload_csv_to_s3(csv_content, spot_group_id)
            
            return True, csv_url, None
            
        except Exception as e:
            logger.error(f"Error exporting spots to CSV: {str(e)}", exc_info=True)
            return False, None, str(e)
    
    def _get_spot_group(self, spot_group_id: str) -> SpotGroupModel:
        """Get spot group by ID"""
        try:
            return SpotGroupModel.get(SpotPrefix.SPOT_GROUP_PREFIX, spot_group_id)
        except DoesNotExist:
            logger.error(f"Spot group {spot_group_id} not found")
            return None
        except Exception as e:
            logger.error(f"Error getting spot group: {str(e)}")
            return None
    
    def _get_spots_by_group_id(self, spot_group_id: str) -> List[SpotModel]:
        """Get all spots for a given spot group ID"""
        try:
            hash_key = f'spot#{spot_group_id}'
            spots = []
            
            # Query all spots for the spot group
            result_iterator = SpotModel.query(hash_key, limit=2000)
            for spot in result_iterator:
                spots.append(spot)
            
            # Handle pagination if there are more than 2000 spots
            while result_iterator.last_evaluated_key:
                result_iterator = SpotModel.query(
                    hash_key, 
                    limit=2000,
                    last_evaluated_key=result_iterator.last_evaluated_key
                )
                for spot in result_iterator:
                    spots.append(spot)
            
            return spots
            
        except Exception as e:
            logger.error(f"Error getting spots for group {spot_group_id}: {str(e)}")
            return []
    
    def _generate_csv_content(self, spots: List[SpotModel], spot_group: SpotGroupModel) -> str:
        """Generate CSV content from spots data with specific column order"""
        try:
            spot_group_attributes = spot_group.get_spot_group_attributes() or []
            url_attribute = spot_group.get_spot_url_attribute()

            headers = ['ID']

            # Add first attribute if it exists
            if len(spot_group_attributes) > 0:
                headers.append(spot_group_attributes[0]['attributeName'])

            headers.extend(['緯度', '経度', 'タグ'])

            # Add remaining attributes (attr2, attr3, attr4, etc.)
            if len(spot_group_attributes) > 1:
                for attr in spot_group_attributes[1:]:
                    headers.append(attr['attributeName'])

            headers.append('画像')

            # Add URL attribute if it exists and has a name
            if url_attribute and url_attribute.get('attributeName'):
                headers.append(url_attribute['attributeName'])

            # Create CSV content
            output = io.StringIO()
            writer = csv.writer(output, delimiter=',', quoting=csv.QUOTE_ALL)

            writer.writerow(headers)

            for spot in spots:
                row = []
                spot_dict = spot.as_dict()
                attributes_dict = {attr['id']: attr['value'] for attr in spot_dict.get('attributes', [])}

                row.append(spot.sortKey)

                # Add first attribute if it exists
                if len(spot_group_attributes) > 0:
                    attr_value = attributes_dict.get(spot_group_attributes[0]['id'], '')
                    row.append(attr_value)

                row.append(str(spot.latitude) if spot.latitude else '')
                row.append(str(spot.longitude) if spot.longitude else '')

                tags_str = ','.join(spot.tags) if spot.tags else ''
                row.append(tags_str)

                # Add remaining attributes (attr2, attr3, attr4, etc.)
                if len(spot_group_attributes) > 1:
                    for attr in spot_group_attributes[1:]:
                        attr_value = attributes_dict.get(attr['id'], '')
                        row.append(attr_value)

                row.append(spot.image if spot.image else '')

                # Add URL attribute value if it exists
                if url_attribute and url_attribute.get('attributeName'):
                    url_attr_value = ''
                    if spot_dict.get('urlAttribute') and spot_dict['urlAttribute'].get('value'):
                        url_attr_value = spot_dict['urlAttribute']['value']
                    row.append(url_attr_value)

                writer.writerow(row)

            return output.getvalue()

        except Exception as e:
            logger.error(f"Error generating CSV content: {str(e)}")
            raise e
    
    def _upload_csv_to_s3(self, csv_content: str, spot_group_id: str) -> str:
        """Upload CSV content to S3 and return presigned URL"""
        try:
            timestamp = int(time.time())
            filename = f"スポット_{spot_group_id}_{timestamp}.csv"
            s3_key = f"exports/spots/{filename}"
            
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=s3_key,
                Body=csv_content.encode('utf-8-sig'),  # Use UTF-8 with BOM for Excel compatibility
                ContentType='text/csv',
                ContentDisposition=f'attachment; filename="{filename}"'
            )
            
            # Generate presigned URL (valid for 1 hour)
            presigned_url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.bucket_name, 'Key': s3_key},
                ExpiresIn=3600
            )
            
            return presigned_url
            
        except ClientError as e:
            logger.error(f"Error uploading CSV to S3: {str(e)}")
            raise e
        except Exception as e:
            logger.error(f"Unexpected error uploading CSV: {str(e)}")
            raise e
