/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { AppConfig as Config } from './config.js';
import cs from './constants.js';

describe('config instantiation', () => {
  it('creates from object', () => {
    const object = new Config({
      key: 'value',
    });
    expect(object.get('key')).toBe('value');
  });
});
describe('confing merging', () => {
  it('updates config records', () => {
    const object = new Config({
      key: 'value',
    });
    object.merge({ key2: 'value2' });
    expect(object.get('key2')).toBe('value2');
    expect(object.get('key')).toBe('value');
  });
  it('override config values', () => {
    const object = new Config({
      key: 'value',
    });
    object.merge({ key: 'value2' });
    expect(object.get('key')).toBe('value2');
  });
});
describe('getters', () => {
  const subject = new Config({
    string: 'string',
    boolean: true,
    booleanStr: 'true',
    booleanStrFalse: '-',
  });
  it('returns boolean value', () => {
    expect(subject.getBoolean('boolean')).toBe(true);
    expect(subject.getBoolean('booleanStr')).toBe(true);
    expect(subject.getBoolean('booelanStrFalse')).toBe(false);
  });
  it('returns default value', () => {
    expect(subject.opt('unknownKey').or('default')).toBe('default');
    expect(subject.opt('string').or('default')).toBe('string');
  });
  it('return default ', () => {
    expect(subject.getOrDefaults('key', 'value2')).toBe('value2');
  });
});
describe('AppConfig', () => {
  beforeEach(() => {
    process.env.NODE_ENV = 'test';
    process.env.AWS_LAMBDA_FUNCTION_NAME = '';
  });
  it('returns environment name', () => {
    const subject = new Config({
      [cs.DEPLOY_ENV]: 'lsc-dev',
    });
    expect(subject.environmentName).toBe('lsc-dev');
  });
  it('returns development type if specified', () => {
    const subject = new Config({
      [cs.ENVIRONMENT_TYPE]: 'development',
    });
    expect(subject.environmentType).toBe('development');
  });
  it('returns development type if specified as "dev"', () => {
    const subject = new Config({
      [cs.ENVIRONMENT_TYPE]: 'dev',
    });
    expect(subject.environmentType).toBe('development');
  });
  it('returns environment type == test', () => {
    const subject = new Config({
      [cs.ENVIRONMENT_TYPE]: 'test',
    });
    expect(subject.environmentType).toBe('test');
  });
  it('returns environment type == development for lsc-dev env', () => {
    process.env.NODE_ENV = 'production';
    process.env.AWS_LAMBDA_FUNCTION_NAME = 'lambda-name';
    const subject = new Config({ DEPLOY_ENV: 'lsc-dev' });
    console.log(
      process.env.NODE_ENV,
      process.env.AWS_LAMBDA_FUNCTION_NAME,
      process.env.DEPLOY_ENV,
      subject.environmentType,
      subject.environmentName
    );
    expect(subject.environmentType).toBe('development');
  });
  it('returns environment type == production in aws env', () => {
    process.env.NODE_ENV = 'production';
    process.env.AWS_LAMBDA_FUNCTION_NAME = 'lambda-name';
    const subject = new Config();
    expect(subject.environmentType).toBe('production');
  });
  it('returns environment type == development if nothing specified', () => {
    process.env.NODE_ENV = 'production';
    const subject = new Config({});
    expect(subject.environmentType).toBe('development');
  });
});
