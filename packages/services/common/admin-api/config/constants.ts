/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

const c = {
  TABLE_CHATBOT_SCENARIO: 'TABLE_CHATBOT_USER_SESSION',
  TABLE_CHATBOT_SCENARIO_DATA: 'TABLE_CHATBOT_SCENARIO',
  TABLE_CHATBOT_USER_SESSION: 'TABLE_CHATBOT_SCENARIO_DATA',
  S3_CHATBOT_RESOURCES: 'BUCKET_CHATBOT_IMPORTED_RESOURCES',
  AWS_REGION: 'AWS_REGION',

  API_CORS_ORIGIN: 'API_CORS_ORIGIN',
  API_INTERCHANGE_LOGGING_ENABLED: 'API_INTERCHANGE_LOGGING_ENABLED',

  ADMIN_USER_POOL_ID: 'VUE_APP_AMPLIFY_AUTH_USER_POOL_ID',
  SECRET_ID: 'SECRET_ID',

  ACTION_LOGS_DELIVERY_STREAM: 'ACTION_LOGS_DELIVERY_STREAM',

  DEPLOY_ENV: 'DEPLOY_ENV',
  LOG_LEVEL: 'LOG_LEVEL',
  ENVIRONMENT_TYPE: 'ENVIRONMENT_TYPE',
  RUNTIME_ENV: 'RUNTIME_ENV',

  RESOURCES_BUCKET: 'RESOURCES_BUCKET',
  BUCKET_DISTRIBUTION_RESOURCES: 'BUCKET_DISTRIBUTION_RESOURCES',
  BUCKET_SURVEYIMG_RESOURCES: 'BUCKET_SURVEYIMG_RESOURCES',
  RESERVATION_REMINDER_BUCKET: 'RESERVATION_REMINDER_BUCKET',
  DATABASE_TABLE: 'DATABASE_TABLE',
  TABLE_SEGMENT_DELIVERY: 'TABLE_SEGMENT_DELIVERY',
  TABLE_DISTRIBUTION_RESOURCES: 'TABLE_DISTRIBUTION_RESOURCES',
  TABLE_SURVEY_CONFIGS: 'TABLE_SURVEY_CONFIGS',
  TABLE_SURVEY_RESULTS: 'TABLE_SURVEY_RESULTS',
  TABLE_SURVEY_RESULTS_HISTORY: 'TABLE_SURVEY_RESULTS_HISTORY',
  TABLE_SURVEY_CALENDARS: 'TABLE_SURVEY_CALENDARS',
  TABLE_MEMBER_CONFIGS: 'TABLE_MEMBER_CONFIGS',
  TABLE_MEMBER_RESULTS: 'TABLE_MEMBER_RESULTS',
  TABLE_REMINDERS: 'TABLE_REMINDERS',
  TABLE_PAYMENT_CONFIGS: 'TABLE_PAYMENT_CONFIGS',
  TABLE_PAYMENT_RESULTS: 'TABLE_PAYMENT_RESULTS',
  TABLE_DISTRIBUTION_HISTORY: 'TABLE_DISTRIBUTION_HISTORY',
  FUNCTION_SURVEY_SEARCH: 'FUNCTION_SURVEY_SEARCH',
  VUE_APP_LIFF_WEB_URL: 'VUE_APP_LIFF_WEB_URL',
  RESERVATION_REMINDER_CLOUDFRONT_URL: 'RESERVATION_REMINDER_CLOUDFRONT_URL',
  PAYMENT_GATEWAY_LAMBDA_NAME: 'PAYMENT_GATEWAY_LAMBDA_NAME',

  DATE_REMINDERS_TOTAL_COUNT_LIMIT: 'DATE_REMINDERS_TOTAL_COUNT_LIMIT',

  SURVEY_RESULTS_HISTORY: {
    deleted: '削除された',
    updated: '保存',
    created: '作成',
    idReset: 'ユーザーIDリセット',
    unicast: '配信',
    recentFetchCountLimit: 50,
  }
};

export default c;
