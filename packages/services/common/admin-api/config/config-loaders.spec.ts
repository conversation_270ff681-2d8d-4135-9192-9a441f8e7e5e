/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { loadFromEnvironment } from './config-loaders.js';
import cs from './constants.js';

describe('load config from environment', () => {
  process.env[cs.DEPLOY_ENV] = 'lsc-dev';
  process.env['UNKNOWN'] = 'UNKNOWN';

  const subject = loadFromEnvironment();
  it('returns test', () => {
    expect(subject[cs.DEPLOY_ENV]).toBe('lsc-dev');
  });
  it('returns nothing', () => {
    expect(subject['UNKNOWN']).toBeUndefined();
  });
});
