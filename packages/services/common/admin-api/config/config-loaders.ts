/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import envs from './env-params.js';
import { isValue } from './config.js';
import console from "console";
import * as process from "process";

const loadFromEnvironment = (environmentMap?) => {
  const result = {};
  const map = environmentMap || process.env;
  Object.keys(envs).forEach((key) => {
    const value = map[envs[key]];
    if (isValue(value)) {
      result[key] = value;
    }
  });
  return result;
};

const loadFromParameterStore = async (parameterStore) => {
  return {};
};

const loadFromSecretsManager = async (secretName) => {
  return {};
};

export {
  loadFromEnvironment,
  loadFromParameterStore,
  loadFromSecretsManager,
};
