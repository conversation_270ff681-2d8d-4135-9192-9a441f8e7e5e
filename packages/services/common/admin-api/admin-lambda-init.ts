import momentTimezone from "moment-timezone";
import {setDefaultLocaleEn, setDefaultTimezoneJapan} from "../utils/time-utils.js";
import {AppConfig as Config, compose} from "./config/config.js";
import defaultConfig from "./config/defaults.js";
import {loadFromEnvironment} from "./config/config-loaders.js";
import {staticConfig} from "./config/static.js";

momentTimezone.tz.setDefault('Asia/Tokyo')
setDefaultTimezoneJapan();
setDefaultLocaleEn();

const lazyInitialize = () => {
    const config = new Config(compose(defaultConfig, loadFromEnvironment()));
    staticConfig.merge(config);
};
lazyInitialize();
