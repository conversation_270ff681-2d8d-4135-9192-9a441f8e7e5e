/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import fs from 'fs';

let cache: Record<string, string> | null = null;

const load = (path = 'build.properties') => {
  cache = {};
  if (!fs.existsSync(path)) {
    return;
  }
  const content = fs
    .readFileSync(path, {
      encoding: 'utf8',
      flag: 'r',
    })
    .toString();
  content
    .split('\n')
    .map((s) => s.trim())
    .filter((s) => s.length > 0)
    .forEach((s) => {
      const delimiter = s.indexOf('=');
      if (delimiter > 0) {
        const key = s.substring(0, delimiter);
        const value = s.substring(delimiter + 1);
        cache![key] = value;
      }
    });
};
const get = () => {
  if (cache === null) {
    load();
  }
  return { ...cache };
};

export {
  get,
  load,
};

load('./build.properties');
console.log(get());
