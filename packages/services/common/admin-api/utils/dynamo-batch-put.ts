/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import AWS from 'aws-sdk';
import _ from 'lodash';
export type BatchPutResult<T> = {
  unprocessedItems: T[],
  consumedCapacityUnits: number;
}

export class DynamoBatchPutOperation {
  constructor(private documentClient: AWS.DynamoDB.DocumentClient) {
  }

  async batchPut<T>(table: string, items: T[]): Promise<BatchPutResult<T>> {
    const operationResult: BatchPutResult<T> = {
      consumedCapacityUnits: 0,
      unprocessedItems: [],
    }
    await makeBatchRequest(items, async (batchTask) => {
      const chunkResult = await this.documentClient.batchWrite({
        ReturnConsumedCapacity: 'TOTAL',
        RequestItems: {
          [table]: batchTask.map((t) => {
            return {
              PutRequest: {
                Item: t
              },
            }
          })
        }
      }).promise();
      if (chunkResult.UnprocessedItems && chunkResult.UnprocessedItems[table]) {
        chunkResult.UnprocessedItems[table].forEach(v => {
          if (v.PutRequest) {
            operationResult.unprocessedItems.push(v.PutRequest.Item as T);
          }
        })
      }
      operationResult.consumedCapacityUnits += extractCapacityUnits(chunkResult.ConsumedCapacity);
    });
    return operationResult;
  }


}

type BatchExecutor<Task, Result> = (t: Task[], n?: number) => Promise<Result[] | void>

const makeBatchRequest = async <Task, Result>(tasks: Task[], executor: BatchExecutor<Task, Result>, batchSize = 25): Promise<Result[]> => {
  const result: Result[] = [];
  let {batch, processed} = _nextBatch(tasks, 0, batchSize);
  let counter = 0;
  while (batch.length > 0) {
    const partialResult = await executor(batch, counter++)
    if (partialResult) {
      result.push(...partialResult);
    }
    const next = _nextBatch(tasks, processed);
    batch = next.batch;
    processed = next.processed;
  }

  return result;
}

const _nextBatch = <B>(arr: B[] = [], from = 0, size = 10) => {
  const batch = arr.slice(from, from + size);
  return {
    batch,
    processed: from + batch.length,
  };
}

function extractCapacityUnits(ConsumedCapacity: AWS.DynamoDB.DocumentClient.ConsumedCapacity | AWS.DynamoDB.DocumentClient.ConsumedCapacity[] | undefined) {
  if (!ConsumedCapacity) return 0;
  const units = _.flatten([ConsumedCapacity]);
  let sum = 0;
  units.forEach((unit) => {
    sum += (unit.CapacityUnits || 0);
  })
  return sum;
}
