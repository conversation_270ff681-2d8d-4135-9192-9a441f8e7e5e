/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import _ from 'lodash';

class ClientError extends Error {
  private type: string;
  status: number;
  protected originalStack: any;
  constructor(e) {
    super(e);
    this.type = 'client';
    this.status = 400;
    this.originalStack = e ? e.cause : undefined;
  }
}

const getOriginalException = (...args) => {
  return _.find(args, (arg) => arg && arg.constructor.name === 'Error') || undefined;
};

class BadRequest extends ClientError {
  private details: any;
  private code: string;
  constructor(
    config: {
      msg?: string,
      cause?: any,
      details?: any,
      code?: string | number,
    } = {
      msg: 'Bad Request',
      cause: undefined,
      details: {},
      code: undefined,
    }
  ) {
    super(config.cause);
    this.message = config.msg || (config.cause && config.cause.message);
    this.details = config.details || {};
    this.code = config.code || (config.cause && config.cause.code);
    this.originalStack = (config.cause && config.cause.stack) || '';
  }
}

class AccessDenied extends BadRequest {
  constructor(msg, currentUser?) {
    super({ msg: msg, details: currentUser, code: 403 });
  }
}

class NotFound extends ClientError {
  private code: string;
  constructor(message) {
    super(message);
    this.status = 404;
    this.code = 'NotFound';
  }
}

class ServerError extends Error {
  private type: string;
  private status: number;
  protected originalStack: any;
  constructor(e) {
    super(e);
    this.type = 'server';
    this.status = 500;
    this.originalStack = e.cause;
  }
}

export class GenericError extends Error {
  protected originalStack: any;
  code: string;
  __response: any;
  details: any;
  constructor(e, opts: {
    response?: any,
    details?: any,
    code?: string,
  } = {}) {
    super(e);
    this.originalStack = e.cause;
    this.code = opts.code || '';
    this.details = opts.details;
    this.__response = opts.response;
  }
}

class SystemConfigurationError extends ServerError {
  constructor(e) {
    super(e);
  }
}

class ConfigurationMiss extends SystemConfigurationError {
  private code: any;
  constructor(
    config:any = {
      msg: 'Configuration Miss',
      setting: 'not specified',
      cause: undefined,
      code: 'configuration_miss',
    }
  ) {
    super(config.cause);
    this.code = config.code || (config.cause && config.cause.code);
    this.message = config.msg || (config.cause && config.cause.message);
    this.originalStack = (config.cause && config.cause.stack) || '';
  }

  get stack() {
    return super.stack || this.originalStack;
  }
}

class MigrationError extends Error {
  code: any;
  constructor(errorCode, errorMessage) {
    super(errorMessage);
    this.code = errorCode;
  }
}

interface AppErrorType extends Error {
  code?: string | number,
  originalStack?: string,
  message: string,
  details: any
}

export {
  getOriginalException,
  ClientError,
  ServerError,
  AccessDenied,
  BadRequest,
  NotFound,
  ConfigurationMiss,
  SystemConfigurationError,
  MigrationError,
  AppErrorType,
};
