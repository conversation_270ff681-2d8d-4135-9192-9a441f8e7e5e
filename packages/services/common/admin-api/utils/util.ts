/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { v4 as uuidv4 } from 'uuid';
import _ from 'lodash';
import { DateTime } from 'luxon';
import encoding from 'encoding-japanese';
import { arrayBufferToBuffer } from 'arraybuffer-to-buffer';
import momentTimezone from 'moment-timezone';
import { ExportCsvColumnConfig } from '../../../api-admin-survey/types/survey-results.js';

const getLineMessagingCredentials = () => {
  return {
    accessToken: process.env.LINEMESSAGING_CHANNEL_ACCESS_TOKEN,
    channelSecret: process.env.LINEMESSAGING_CHANNEL_SECRET,
  };
};

const getCurrentTime = () => {
  return momentTimezone().utcOffset('+0900').format('YYYYMMDDTHHmmssZZ');
};

const getCurrentDate = () => {
  return momentTimezone().utcOffset('+0900').format('YYYYMMDD');
};

const getCurrentTimeISO = () => {
  return DateTime.now().setZone('Asia/Tokyo').toISO({includeOffset: true, suppressMilliseconds: false});
}

export function toYYYYhpMMhpDD(dateTime: DateTime = DateTime.now()) {
  return dateTime.toFormat('yyyy-MM-dd');
}

const getRegion = () => {
  return process.env.AWS_REGION || 'ap-northeast-1';
};

const isLocalRun = () => {
  return !process.env.AWS_LAMBDA_FUNCTION_NAME;
};
const isAwsEnvironment = () => {
  return !isLocalRun() && process.env.AWS_SAM_LOCAL !== 'true';
};
const isSamLocalTestEnvironment = () => {
  return process.env.AWS_SAM_LOCAL === 'true';
};
const getLogLevel = () => {
  return process.env.AWS_LAMBDA_LOG_LEVEL || 'info';
};
const hasTestRoute = () => {
  return process.env.AWS_LAMBDA_HAS_TEST_ROUTE === 'true' || false;
};
const isDebugLogging = () => {
  return getLogLevel() === 'debug';
};
const isProductionEnv = () => {
  return process.env.AWS_LAMBDA_PRODUCTION_MODE === 'true' || false;
};

const appLogDebug = (...args) => {
  if (getLogLevel() === 'debug') {
    console.log(...args);
  }
};
const appLogInfo = (...args) => {
  console.log(...args);
};

const generateUuid = () => {
  return uuidv4();
};

const decodeBase64 = (base64str) => {
  return Buffer.from(base64str, 'base64').toString('utf8');
};

const randomIntBetween = (start, end) => {
  return _.random(start, end);
};
const randomString = (len = 10) => {
  return Math.random().toString(36).substring(2, len);
};

const extractVersionPrefix = (event) => {
  let pathPattern = event.resource;
  pathPattern = pathPattern.replace(/{proxy\+}/, '___proxy___');
  pathPattern = pathPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  pathPattern = pathPattern.replace('___proxy___', '.+');
  const pathRegex = new RegExp(pathPattern);
  const prefix = event.path.replace(pathRegex, '');
  if (prefix) {
    return prefix.substring(1);
  }
  return '';
};

export const parseJsonSafe = (str: string | any, fallback: any = {}) => {
  if (_.isPlainObject(str)) {
    return str;
  }
  try {
    return JSON.parse(str);
  } catch (e: any) {
    return fallback;
  }
}

//Fortmat AnswerCode 1->000001
const getFormatedAnswerCode = (answerCode) => {
  if (Number(answerCode) > 999999) {
    return answerCode;
  }
  const str = '' + answerCode;
  const pad = '000000';
  const result = pad.substring(0, pad.length - str.length) + str;
  return result;
};

const convertSJISBufferToString = (arrayBuffer) => {
  const detectedEncoding = encoding.detect(arrayBuffer);

  const result = encoding.convert(arrayBuffer, {
    to: 'UNICODE',
    from: detectedEncoding,
    type: 'string',
  });

  return result;
};

const convertStringDataToJIS = (stringToConvert) => {
  const encoder = new TextEncoder();
  const bytesArray = encoder.encode(stringToConvert);

  const detectedEncoding = encoding.detect(bytesArray);

  const dataArrayBuffer = encoding.convert(bytesArray, {
    to: 'SJIS',
    from: detectedEncoding,
    type: 'arraybuffer',
  });

  return arrayBufferToBuffer(dataArrayBuffer);
};

const prepareExportHeaders = (configurableColumns: ExportCsvColumnConfig[]) => {
  const allColumns = configurableColumns.filter((col) => col.text !== '選択' && col.text !== '予約項目');

  //Move answerCode to the front if included in allColumns
  const answerCodeIndex = allColumns.findIndex((obj) => obj.value === 'answerCode');
  if (answerCodeIndex >= 0) {
    const answerCodeElement = allColumns.splice(answerCodeIndex, 1)[0];
    allColumns.unshift(answerCodeElement);
  }

  //Always place userId and partitionKey in the front
  allColumns.unshift({ text: 'ユーザーID', value: 'userId' }, { text: 'パーティションキー', value: 'partitionKey' });

  //Columns to move to the end in order
  const endingColumns = ['check', 'createdAt', 'updatedAt', 'note'];
  for (const columnValue of endingColumns) {
    const columnIndex = allColumns.findIndex((obj) => obj.value === columnValue);
    if (columnIndex >= 0) {
      const columnElement = allColumns.splice(columnIndex, 1)[0];
      allColumns.push(columnElement);
    }
  }

  return allColumns;
};

const removeElementFromArray = (array, element) => {
  let returnValue:any = null;

  const index = array.indexOf(element);
  if (index > -1) {
    returnValue = array.splice(index, 1);
  }

  return returnValue;
};

const sleep = (ms) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

const diffNowMs = (targetTimeIsoOrUnix) => {
  if (_.isNil(targetTimeIsoOrUnix)) {
    return -1;
  }
  let timeLuxon;
  if (typeof targetTimeIsoOrUnix === 'number') {
    timeLuxon = DateTime.fromMillis(targetTimeIsoOrUnix);
  } else if (typeof targetTimeIsoOrUnix === 'string') {
    timeLuxon = DateTime.fromISO(targetTimeIsoOrUnix);
  } else if (DateTime.isDateTime(targetTimeIsoOrUnix)) {
    timeLuxon = targetTimeIsoOrUnix;
  }
  if (!timeLuxon.isValid) {
    return -1;
  }
  return Math.abs(timeLuxon.diffNow('millisecond').milliseconds);
}

class MultiMap<V> {
  private map: Record<string, V[]>
  constructor(private uniqueValues: boolean = false) {
    this.map = {};
  }
  put(key: string, value: V) {
    let bucket = this.map[key];
    if (!bucket) {
      bucket = [];
      this.map[key] = bucket;
    }
    if (this.uniqueValues && bucket.includes(value)) {
      return;
    }
    bucket.push(value);
  }
  get(key: string) {
    return this.map[key];
  }
  keys() {
    return Object.keys(this.map);
  }
  keysWithValues() {
    return Object.entries(this.map).filter(e => e[1].length > 0).map(e => e[0]);
  }
}
/**
 * 
 * @param generator 次のIDを生成する関数
 * @param duplicateFinder 既存のアイテムを返す関数。null, undefined, false, 空文字列を返すと、IDがユニークだと判断される
 * @param maxAttempts 試行の最大回数（デフォルト１０回）
 * @returns ユニークID
 */
async function generateIdAndCheckUniq<T = string>(
  generator: (attempt: number) => T,
  duplicateFinder: (id: T) => any,
  maxAttempts = 10,
) : Promise<T> {
  const isUniqueCandidate = async (candidate: T) => {
    const result = await Promise.resolve(duplicateFinder(candidate));
    return _.isNil(result) || result === '' || result === false;
  }

  let attempt = 0;
  let candidate:T 
  let isUniq = false;
  do {
    candidate = generator(attempt++);
    isUniq = await isUniqueCandidate(candidate)
  } while(!isUniq && attempt < maxAttempts);
  if (!isUniq && attempt === maxAttempts) {
    throw new Error(`Unique ID generation: reached max attempts ${attempt}`)
  }
  return candidate;
}

function toSet<T,V>(coll: T[], extractor: (item: T)=>V) {
  const set = new Set<V>();
  (coll || []).forEach(v => {
    set.add(extractor(v));
  });
  return set;
}

export {
  getRegion,
  isAwsEnvironment,
  isSamLocalTestEnvironment,
  isLocalRun,
  getLogLevel,
  isProductionEnv,
  isDebugLogging,
  appLogInfo,
  appLogDebug,
  hasTestRoute,
  getLineMessagingCredentials,
  extractVersionPrefix,
  getCurrentTime,
  getCurrentDate,
  getCurrentTimeISO,
  generateUuid,
  decodeBase64,
  randomIntBetween,
  randomString,
  getFormatedAnswerCode,
  convertSJISBufferToString,
  convertStringDataToJIS,
  prepareExportHeaders,
  removeElementFromArray,
  sleep,
  diffNowMs,
  MultiMap,
  toSet,
  generateIdAndCheckUniq,
};
