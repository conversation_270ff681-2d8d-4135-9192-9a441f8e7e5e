/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import AWS from 'aws-sdk';
import { infoLog, debugLog } from './logger.js';
import { getEnvironmentCredentials } from './aws-helper.js';
import {HistoryRecord} from '../../../api-admin-survey/types/survey-results.js';

const getDbClient = () => {
  return new AWS.DynamoDB(getEnvironmentCredentials());
};

const queryList = async (params: AWS.DynamoDB.QueryInput) => {
  const client = getDbClient();
  const json:any[] = [];
  let request;
  do {
    const lastKey = request ? request.LastEvaluatedKey : undefined;
    request = await client
      .query({
        ...params,
        ExclusiveStartKey: lastKey,
      })
      .promise();
    request.Items.map((map) => {
      json.push(AWS.DynamoDB.Converter.unmarshall(map));
    });
  } while (request.LastEvaluatedKey !== undefined);
  return json;
};

const queryListOnce = async (params) => {
  const client = getDbClient();
  const json:any[] = [];
  const request = await client
      .query(params)
      .promise();
  request.Items.map((map) => {
    json.push(AWS.DynamoDB.Converter.unmarshall(map));
  });
  return {
    items: json,
    LastEvaluatedKey: request.LastEvaluatedKey
  };
}

const scanList = async (params) => {
  const client = getDbClient();
  const json:any[] = [];
  let request;
  do {
    const lastKey = request ? request.LastEvaluatedKey : undefined;
    request = await client
      .scan({
        ...params,
        ExclusiveStartKey: lastKey,
      })
      .promise();
    request.Items.map((map) => {
      json.push(AWS.DynamoDB.Converter.unmarshall(map));
    });
  } while (request.LastEvaluatedKey !== undefined);
  return json;
};

const queryRecent = async (params, recentCount) => {
  const client = getDbClient();
  const json: HistoryRecord[] = [];
  const request = await client
    .query({
      ...params,
      ScanIndexForward: false,
      Limit: recentCount,
    })
    .promise();
  request.Items.map((map) => {
    json.push(AWS.DynamoDB.Converter.unmarshall(map) as HistoryRecord);
  });
  return json;
}

const getMany = async (params) => {
  const clinet = getDbClient();
  const batchResult = await clinet.batchGetItem(params).promise();
  const jsonResults:any[] = [];
  Object.keys(batchResult.Responses).forEach((table) => {
    const items = batchResult.Responses[table];
    items.forEach((item) => {
      jsonResults.push(AWS.DynamoDB.Converter.unmarshall(item));
    });
  });
  return jsonResults;
};

const getOne = async (params) => {
  const client = getDbClient();
  const result = await client.getItem(params).promise();
  if (result.Item) {
    return AWS.DynamoDB.Converter.unmarshall(result.Item);
  }
  return undefined;
};

const updateOne = async (params) => {
  const client = getDbClient();
  try {
    const res = await client
      .updateItem({
        ReturnValues: 'ALL_NEW',
        ...params,
      })
      .promise();
    if (res.Attributes) {
      return AWS.DynamoDB.Converter.unmarshall(res.Attributes);
    }
    infoLog('[UPDATE_ERROR] Did not receive new attributes from DynamoDB client.' +
      'Either update was unsuccessful or nothing was changed. See response below.', res);
    return undefined;
  } catch (err: any) {
    infoLog('[UPDATE_ERROR] An error occured during Dynamodb updateItem.', err.message, err);
    throw err;
  }
};

const createOne = async (params) => {
  const client = getDbClient();
  const res = await client
    .putItem({
      ...params,
    })
    .promise();
  if (res.Attributes) {
    return AWS.DynamoDB.Converter.unmarshall(res.Attributes);
  }
  return undefined;
};

const deleteOne = async (params) => {
  const client = getDbClient();
  const response = await client
    .deleteItem({
      ...params,
      ReturnValues: 'ALL_OLD'
    })
    .promise();
  if (!(Object.keys(response).includes('Attributes'))) {
    infoLog('[DELETE_FAILURE] ', 'Expected deleted attributes to be returned but none were returned. Delete Params: ', params)
  }
};

const batchWriteItem = async (params) => {
  const client = getDbClient();
  const res = await client
    .batchWriteItem({
      ...params,
    })
    .promise();

  return res;
};

class UpdateExpressionBuilder {
  private setExpressions: any[];
  private deleteExpressions: any[];
  private addExpressions: any[];
  private expressionAttributeNames: any;
  private expressionAttributeValues: any;
  private ignoredKeys: any[];
  private addKeys: any[];
  private addValues: any[];
  private deleteValueIfUndefined: boolean;
  private source: any;
  constructor(source, opts:any = {}) {
    this.setExpressions = [];
    this.deleteExpressions = [];
    this.addExpressions = [];
    this.expressionAttributeNames = {};
    this.expressionAttributeValues = {};

    this.ignoredKeys = [];
    this.addKeys = [];
    this.addValues = [];
    this.deleteValueIfUndefined = false;

    if (typeof source === 'string') {
      this.source = JSON.parse(source);
    } else {
      this.source = source;
    }
    if (opts.ignoredKeys) {
      this.ignoredKeys.push(...opts.ignoredKeys);
    }
    if (opts.addKeys) {
      this.addKeys.push(...opts.addKeys);
    }
    if (opts.addValues) {
      this.addValues.push(...opts.addValues);
    }
    if (opts.deleteValueIfUndefined !== undefined) {
      this.deleteValueIfUndefined = opts.deleteValueIfUndefined;
    }
  }

  build() {
    Object.keys(this.source)
      .filter((key) => !this.ignoredKeys.includes(key))
      .forEach((key) => {
        const value = this.source[key];
        if (value === undefined) {
          if (this.deleteValueIfUndefined) {
            this.deleteExpressions.push(`#${key}`);
            this.expressionAttributeNames[`#${key}`] = key;
          }
          return;
        }

        this.setExpressions.push(`#${key} = :${key}`);
        this.expressionAttributeNames[`#${key}`] = key;

        let marshalled;
        if (Array.isArray(value)) {
          marshalled = AWS.DynamoDB.Converter.input(value, { convertEmptyValues: true });
        } else if (typeof value === 'number' || typeof value === 'boolean' || typeof value === 'string') {
          marshalled = AWS.DynamoDB.Converter.input(value, {
            convertEmptyValues: true,
          });
        } else if (value === null) {
          marshalled = {
            NULL: true,
          }
        } else {
          marshalled = {
            M: AWS.DynamoDB.Converter.marshall(value, {
              convertEmptyValues: true,
            }),
          };
        }
        this.expressionAttributeValues[`:${key}`] = marshalled;
      });

    if (this.addKeys.length > 0) {
      for (let i = 0; i < this.addKeys.length; i++) {
        if (this.addKeys[i].includes('.')) {
          const splitValues = this.addKeys[i].split('.');
          const addName = `#addKey${i}`;
          this.expressionAttributeNames[addName] = splitValues[1];
          const addValueKey = `:add${i}`;
          const marshalledVal = AWS.DynamoDB.Converter.input(this.addValues[i], {
            convertEmptyValues: true,
          });
          this.expressionAttributeValues[addValueKey] = marshalledVal;

          this.addExpressions.push(`${splitValues[0]}.${addName} ${addValueKey}`);
        } else {
          const addName = `#addKey${i}`;
          this.expressionAttributeNames[addName] = this.addKeys[i];
          const addValueKey = `:add${i}`;
          const marshalledVal = AWS.DynamoDB.Converter.input(this.addValues[i], {
            convertEmptyValues: true,
          });
          this.expressionAttributeValues[addValueKey] = marshalledVal;
          this.addExpressions.push(`${addName} ${addValueKey}`);
        }
      }
    }

    return this.setExpressions.length !== 0 || this.deleteExpressions.length !== 0 || this.addExpressions.length !== 0;
  }

  getSetExpression() {
    let expr = '';
    if (this.setExpressions.length > 0) {
      expr += `SET ${this.setExpressions.join(', ')}`;
    }
    if (this.deleteExpressions.length > 0) {
      expr += ` REMOVE ${this.deleteExpressions.join(', ')}`;
    }
    if (this.addExpressions.length > 0) {
      expr += ` ADD ${this.addExpressions.join(', ')}`;
    }
    return expr.trim();
  }

  getExpressionAttributeNames() {
    return this.expressionAttributeNames;
  }

  getExpressionAttributeValues() {
    return this.expressionAttributeValues;
  }

  printDebug() {
    console.log('SetExpr:', this.getSetExpression());
    console.log('ExAtNa:', this.getExpressionAttributeNames());
    console.log('ExAtVal:', JSON.stringify(this.getExpressionAttributeValues(), null, 2));
  }
}

export async function repeatQuery<T>(
  queryFuction: (lastEvalutedKey?: any) =>
      Promise<{ Items?: any[], LastEvaluatedKey?: any }>
) {
  const records: T[] = [];
  let lastEvaluatedKey = undefined;
  do {
      const result = await queryFuction(lastEvaluatedKey);
      lastEvaluatedKey = result.LastEvaluatedKey;
      result.Items.forEach(v => records.push(v));
  } while (lastEvaluatedKey !== undefined);
  return records;
}

export {
  getDbClient,
  queryList,
  queryListOnce,
  scanList,
  queryRecent,
  getMany,
  getOne,
  updateOne,
  UpdateExpressionBuilder,
  createOne,
  deleteOne,
  batchWriteItem,
};
