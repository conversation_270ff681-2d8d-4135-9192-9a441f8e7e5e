import createAPI, {API, Request, Response} from "lambda-api";
import momentTimezone from "moment-timezone";
import {setDefaultLocaleEn, setDefaultTimezoneJapan} from "../utils/time-utils.js";
import {AppConfig as Config, compose} from "./config/config.js";
import defaultConfig from "./config/defaults.js";
import {loadFromEnvironment} from "./config/config-loaders.js";
import {staticConfig} from "./config/static.js";
import {logger, onLambdaRequestStartCallback} from 'common/logs/logger.js'
import installMiddlewares from "../admin-middlewares/install.js";
import {APIGatewayProxyHandlerV2} from "aws-lambda";
import {requestContext} from "./utils/execution-context.js";
import {setHeadersOnResponseJson} from "../admin-middlewares/cors.js";
import {APIGatewayProxyStructuredResultV2} from "aws-lambda/trigger/api-gateway-proxy.js";
import console from "console";

console.log('api-lambda init')
momentTimezone.tz.setDefault('Asia/Tokyo')
setDefaultTimezoneJapan();
setDefaultLocaleEn();

const lazyInitialize = () => {
    const config = new Config(compose(defaultConfig, loadFromEnvironment()));
    staticConfig.merge(config);
};
lazyInitialize();

export const apiHandlerFactory = (base: string, routeConfiguration: (api: API) => void): APIGatewayProxyHandlerV2<void> => {
    const api = createAPI({ base } );
    installMiddlewares(api);
    routeConfiguration(api);
    return async (event, ctx) => {
        onLambdaRequestStartCallback()
        requestContext.reset();
        requestContext.setCurrentRequest(event);
        const result: APIGatewayProxyStructuredResultV2 = await api.run(event, ctx) ;
        setHeadersOnResponseJson(event, result)
        return result;
    }
}

export const nopHandler = async (req: Request, res: Response) => {
    console.log(`${req.method} ${req.path} - Not implemented`)
    res.status(200).json( { message: 'Method not implemented yet'} )
}
