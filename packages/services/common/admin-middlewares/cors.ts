/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import c from '../admin-api/config/constants.js';
import config from '../admin-api/config/static.js';
import {APIGatewayProxyEventV2, APIGatewayProxyStructuredResultV2} from "aws-lambda/trigger/api-gateway-proxy.js";
import console from "console";
import { Request, Response} from 'lambda-api'

const ALLOWED_HEADERS = 'Content-Type, Authorization, Content-Length, X-Requested-With,x-amz-security-token,x-amz-date,x-dev-username'

const setResponseCorsHeaders = (req, res) => {
    const allowedOrigin = config.opt(c.API_CORS_ORIGIN).orFirst(req.headers.origin, '*');
    res.cors({
        origin: allowedOrigin,
        methods: 'GET, PUT, POST, DELETE, OPTIONS',
        headers: ALLOWED_HEADERS,
        credentials: allowedOrigin !== '*',
        maxAge: 86400,
    });
};

const optionsCors = (req: Request, res, next) => {
    console.log("OPTIONS request", req.path)
    const allowedOrigin = config.opt(c.API_CORS_ORIGIN).orFirst(req.headers.origin, '*');
    res.cors({
        origin: allowedOrigin,
        methods: 'GET, PUT, POST, DELETE, OPTIONS',
        headers: ALLOWED_HEADERS,
        credentials: allowedOrigin !== '*',
        maxAge: 86400,
    });
    res.status(200).send({});
    next();
};

const methodsCors = (req, res, next) => {
    setResponseCorsHeaders(req, res);
    next();
};

export const setHeadersOnResponseJson = (req: APIGatewayProxyEventV2, res: APIGatewayProxyStructuredResultV2) => {
    res.headers['access-control-allow-headers'] = ALLOWED_HEADERS
    res.headers['access-control-allow-origin'] = req.headers['origin'] || '*'
    res.headers['access-control-allow-credentials'] = !!req.headers['origin'];
    res.headers['Access-Control-Max-Age'] = 86400
    res.headers['access-control-allow-methods'] = 'GET, PUT, POST, DELETE, OPTIONS'
    res.headers['x-some-header'] = 'some-value'
}

export {optionsCors, methodsCors, setResponseCorsHeaders};
