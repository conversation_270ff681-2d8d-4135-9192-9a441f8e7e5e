/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import config from '../admin-api/config/static.js';
import { getUsername } from '../admin-api/utils/auth.js';
import console from "console";
import {logger} from "../logs/logger.js";

const isEnabledRequestLogging = () => ['trace', 'debug', 'info'].includes(config.logOutputLevel);

const logRequest = (req) => {
  const username = getUsername(req) || 'anonymous';

  const reqLine = `REQ - ${req.method} ${req.path} (${username})`;
  let queryLine = '';
  if (Object.keys(req.query).length > 0) {
    queryLine = `${JSON.stringify(req.query)}`;
  }

  const { body } = req;
  let bodyLine = '';
  if (body) {
    if (typeof body === 'object') {
      bodyLine = `${JSON.stringify(body, null, 2)}`;
    } else {
      bodyLine = `${body}`;
    }
  }

  logger.info([reqLine, queryLine].filter((v) => !!v).join('\n'));
  logger.debug(bodyLine)
};

const defaultHandler = (req, res, next) => {
  if (isEnabledRequestLogging()) {
    logRequest(req);
  }
  next();
};
export { defaultHandler };
