/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { compose, member, admin, guest } from './access-guards.js';

describe('#compose', () => {
  it('calls nested functions', () => {
    let str:any = [];
    const f1 = (h) => {
      return (val) => {
        str.push('f1-' + val);
        return h(val);
      };
    };

    const f2 = (h) => {
      return (val) => {
        str.push('f2-' + val);
        return h(val);
      };
    };

    const c = compose(f1, f2);
    const composition = c((val) => {
      return -val;
    });

    const res = composition(5);

    expect(res).toEqual(-5);
    expect(str.join(',')).toEqual('f1-5,f2-5');
  });
});
