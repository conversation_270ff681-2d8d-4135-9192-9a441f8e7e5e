/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
// @ts-nocheck
import {nowLuxonDateTime} from '../datetime.js'
import { DateTime } from 'luxon';
import _ from 'lodash';
import {generateUUID} from '../uniq-id.js'

export function helpers(): GeneratorHelpers {
    const now = nowLuxonDateTime();
    return {
        nowDateTime: now,
        nowISO: now.toISO(),
        nowUnix: now.toSeconds(),
        nextGuid: generateUUID,
        clone: <T>(o: T) => _.cloneDeep(o),
    }
}

export function generate<T>(count: number) {
    return (template: T, f: GeneratorFunction<T>) => {
        const coll: T[] = [];
        for (let index = 0; index < count; index++) {
            const now = nowLuxonDateTime();
            const suffix = (val: string) => {
                return `${val}_${index}`
            }
            const o = _.cloneDeep(template)
            const toAdd = f(o, index, {
                ...helpers(),
                addSuffix: suffix,            
            })
            if (toAdd) {
                coll.push(toAdd);
            } else {
                coll.push(o);
            }
        }
    }    
}

export type GeneratorHelpers = {
    nowUnix: number,
    nowISO: string,
    nowDateTime: DateTime,
    nextGuid: () => string;
    clone: <T>(o:T) => T,
}

export type IterableGeneratorHelpers<T> = {
    addSuffix: (val: string) => string,
} & GeneratorHelpers

export type GeneratorFunction<T> = (t: T, index: number, helpers: IterableGeneratorHelpers<T>) => T | void