/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import awsSdk from 'aws-sdk';
import { execSync } from 'child_process';
import * as commandExists from 'command-exists';
import * as compose from 'docker-compose';
import * as path from 'path';
import { AWS as aws, getEnvironmentCredentials } from "../aws-helper.js";
import { TableSchema } from "./test-schema-builder.js";

const DEFAULT_DOCKER_WORKING_DIR = path.join(__dirname, '../dynamo-local')
const DEFAULT_DOCKER_COMPOSE_FILE = 'docker-compose.yaml'
const DEFAULT_SERVICE_PORT = '8003'

export function getTestEndpoint() {
    return `http://localhost:${DEFAULT_SERVICE_PORT}`;
}

export class TestDatabaseController {
    client: awsSdk.DynamoDB;
    debug = false;
    scenario: (c: TestDatabaseController) => void | Promise<void>
    constructor(private endpointUrl: string, _opts?: {
        debug?: boolean,
        scenario?: (c: TestDatabaseController) => Promise<void>
    }) {
        const opts = {
            debug: false,
            // eslint-disable-next-line
            scenario: () => { },
            ...(_opts || {})
        }
        this.debug = opts.debug
        this.scenario = opts.scenario;
        this.client = new aws.DynamoDB({
            ...getEnvironmentCredentials(),
            endpoint: this.endpointUrl,
        });
    }

    logDebug(...args: any[]) {
        if (this.debug) {
            console.log(...args);
        }
    }

    async createTable(tableSchema: TableSchema) {
        const client = new aws.DynamoDB({
            ...getEnvironmentCredentials(),
            endpoint: this.endpointUrl,
            httpOptions: {
                connectTimeout: 2000,
                timeout: 2000,
            }
        });
        await client.createTable(tableSchema).promise();
    }
    async recreateDatabase() {
        await this.cleanContainer();
        await this.scenario(this);
    }
    async cleanContainer() {
        const tables = await this.client.listTables().promise();
        this.logDebug(tables.TableNames?.length || 0, 'existed tables found, deleting...')
        for await (const t of (tables.TableNames || [])) {
            await this.client.deleteTable({ TableName: t }).promise();
        }
    }
}

class DynamoDbDatabaseContainer {
    private isRunning = false;
    constructor(
        private cmd: string,
        private dockerComposeFile: string) {            
    }

    ensureCanUse() {
        if (!commandExists.sync('docker-compose')) {
            throw new Error('docker-compose is not installed')
        }
        try {
            execSync('docker ps')
        } catch (_) {
            throw new Error('Docker is not installed or a Docker daemon is not running')
        }
    }

    async launch() {
        return compose.upAll({
            cwd: this.cmd,
            config: this.dockerComposeFile,
            log: true,
        }).then(() => this.isRunning = true)
    }
    async shutdown() {
        if (this.isRunning === false) return;
        return compose.down({
            cwd: this.cmd,
            config: this.dockerComposeFile,
        }).then(() => this.isRunning = false);
    }
    setStopOnTerm() {
        process.once('SIGINT', () => {
            this.shutdown().then(() => process.exit(1));
        })
    }
}

let containerInstance: DynamoDbDatabaseContainer | undefined = undefined;

export function getContainerInstance() {
    if (!containerInstance) {
        containerInstance = new DynamoDbDatabaseContainer(DEFAULT_DOCKER_WORKING_DIR, DEFAULT_DOCKER_COMPOSE_FILE)
    }
    return containerInstance;
}


export function dbTestSetupModule() {
    beforeAll(async () => {
        const container = getContainerInstance();
        container.ensureCanUse();
        container.setStopOnTerm();
        await container.launch();
    })
    afterAll(async () => {
        await getContainerInstance().shutdown();
    })
}

export async function suiteSetup() {
    const container = getContainerInstance();
        container.ensureCanUse();
        container.setStopOnTerm();
        await container.launch();
}
export async function suiteTeardown() {
    await getContainerInstance().shutdown();
}