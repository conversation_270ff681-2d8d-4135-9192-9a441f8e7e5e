/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { DynamoDB } from 'aws-sdk';
import { DocumentClient } from "aws-sdk/clients/dynamodb";
import _ from 'lodash';
import { AWS as SDK } from '../aws-helper.js';
import console from "console";

export type TableIndex = {
  pkField: string,
  skField?: string
}

export type TableConfiguration = {
  pkField: string,
  skField?: string,
  indexes?: { [key: string]: TableIndex },
}

export type Key = {
  pk: string,
  sk?: string,
  index?: string,
}

export type Query = {
  index?: string,
  query: string,
  mapping: { [key: string]: any },
  attributes?: string[],
  filter?: string,
  reverseSearch?: boolean,
}

type PageSettings = {
  limit?: number,
  lastEvaluatedKey?: any,
}

export type OnePageQuery = PageSettings & Query;

export type ScanFilter = {
  filter: string,
  index?: string,
  mapping: { [key: string]: any },
}

export type OnePageScan = PageSettings & ScanFilter

export type Stats = {
  totalOperationCapacity: number,
}

export type LocalAwsCredentials = {
  profile: string,
  region?: string,
}

export type ItemsRequestResultPage = {
  items: any[],
  lastEvaluatedKey: any,
}

export type AwsClientParameters =
  DocumentClient.DocumentClientOptions & DynamoDB.Types.ClientConfiguration
  | LocalAwsCredentials
  | string

export class Table {
  private readonly tableName: string;
  private config: TableConfiguration;
  private awsCfg: DocumentClient.DocumentClientOptions & DynamoDB.Types.ClientConfiguration;
  private lastOperationStats = {
    totalOperationCapacity: 0
  }

  constructor(name: string, config: TableConfiguration | null, awsConfig?: AwsClientParameters) {
    this.tableName = name;
    this.config = config || {pkField: ''};
    this.awsCfg = {
      region: 'ap-northeast-1'
    };
    if (!awsConfig) {
      this.awsCfg.credentials = new SDK.EnvironmentCredentials('AWS')
    } else if (typeof awsConfig === 'string') {
      this.awsCfg.credentials = new SDK.SharedIniFileCredentials({profile: awsConfig});
    } else if ((awsConfig as LocalAwsCredentials).profile) {
      const profile = (awsConfig as LocalAwsCredentials).profile;
      this.awsCfg.region = (awsConfig as LocalAwsCredentials).region || 'ap-northeast-1';
      this.awsCfg.credentials = new SDK.SharedIniFileCredentials({profile: profile});
    } else {
      this.awsCfg = awsConfig;
    }
  }

  get dbClient() {
    return new SDK.DynamoDB.DocumentClient(this.awsCfg);
  }

  setPrimaryKey(hashKey: string, sortKey?: string) {
    this.config.pkField = hashKey;
    this.config.skField = sortKey;
  }

  getDocumentClient() {
    return this.dbClient;
  }

  getConfiguration() {
    return this.config;
  }

  private async ensureKey() {
    if (this.config.pkField) return;
    const client = new SDK.DynamoDB(this.awsCfg);
    const table = await client.describeTable({TableName: this.tableName}).promise();
    if (table.Table && table.Table.KeySchema) {
      const ks = table.Table.KeySchema;
      this.config.pkField = ks.find((t) => t.KeyType === 'HASH')!.AttributeName;
      const skRec = ks.find((t) => t.KeyType === 'RANGE');
      if (skRec) {
        this.config.skField = skRec.AttributeName;
      }
    }
  }

  private mapKey(key?: Key) {
    const res: { [key: string]: string } = {};
    if (!key) return res;
    res[this.config.pkField] = key.pk;
    if (this.config.skField) {
      res[this.config.skField] = key.sk || "";
    }
    return res;
  }

  private makeKey(object: any): Key {
    const pk = object[this.config.pkField];
    let sk = undefined;
    if (this.config.skField) {
      sk = object[this.config.skField];
    }
    return {
      pk: pk,
      sk: sk,
    }
  }

  async getOneByPrimaryKey(key: {[key: string]: string}) {
    const result = await this.dbClient.get({
      TableName: this.tableName,
      Key: key,
      ReturnConsumedCapacity: "TOTAL",
    }).promise();
    this.saveStats(result.ConsumedCapacity);
    return result.Item;
  }

  async getItem(key: Key): Promise<any> {
    await this.ensureKey();
    const result = await this.dbClient.get({
      TableName: this.tableName,
      Key: this.mapKey(key),
      ReturnConsumedCapacity: 'TOTAL',
    }).promise();
    this.saveStats(result.ConsumedCapacity);
    return result.Item;
  }

  async putItem(item: any, key?: Key) {
    await this.ensureKey();
    const result = await this.dbClient.put({
      ReturnConsumedCapacity: 'TOTAL',
      TableName: this.tableName,
      Item: {
        ...item,
        ...this.mapKey(key),
      }
    }).promise();
    this.saveStats(result.ConsumedCapacity);
  }

  private async updateOrDeleteFieldsInternal(item: any, deleteFields = false, key?: Key): Promise<any> {
    await this.ensureKey();
    let targetKey: any;
    if (key) {
      targetKey = this.mapKey(key);
    } else {
      targetKey = {
        [this.config.pkField]: item[this.config.pkField],
      };
      if (this.config.skField) {
        targetKey[this.config.skField] = item[this.config.skField];
      }
    }
    delete item[this.config.pkField]
    if (this.config.skField) {
      delete item[this.config.skField]
    }

    const builder = new UpdateExpressionBuilder(item, {
      marshallValues: false,
      deleteKeysIfUndefinedValue: deleteFields,
      ignoreAttributes: [this.config.pkField, this.config.skField || ""]
    });
    builder.build();
    const eav = builder.getExpressionAttributeValues();
    const ean = builder.getExpressionAttributeNames();
    const expr = builder.getSetExpression();

    if (_.isEmpty(expr)) {
      const result = await this.dbClient.get({
        TableName: this.tableName,
        Key: targetKey,
        ReturnConsumedCapacity: 'TOTAL',
      }).promise();
      this.saveStats(result.ConsumedCapacity);
      return result.Item;
    }

    const result = await this.dbClient.update({
      Key: targetKey,
      TableName: this.tableName,
      UpdateExpression: expr,
      ExpressionAttributeNames: _.isEmpty(ean) ? undefined : ean,
      ExpressionAttributeValues: _.isEmpty(eav) ? undefined : eav,
      ReturnValues: "ALL_NEW",
      ReturnConsumedCapacity: 'TOTAL',
    }).promise();

    this.saveStats(result.ConsumedCapacity)
    return result.Attributes;
  }

  async update(item: any, key?: Key): Promise<any> {
    return await this.updateOrDeleteFieldsInternal(item, false, key);
  }

  async updateOrDeleteFields(fields: any, key?: Key): Promise<any> {
    return await this.updateOrDeleteFieldsInternal(fields, true, key);
  }

  resetStats() {
    this.lastOperationStats.totalOperationCapacity = 0;
  }

  getStats() {
    return this.lastOperationStats;
  }

  private saveStats(ConsumedCapacity: DocumentClient.ConsumedCapacity | DocumentClient.ConsumedCapacity[] | undefined) {
    if (!ConsumedCapacity) return;
    const units = _.flatten([ConsumedCapacity]);
    units.forEach((unit) => {
      this.lastOperationStats.totalOperationCapacity += (unit.CapacityUnits || 0);
    })
  }


  async batchPut(items: any[]) {
    const self = this;
    return await makeBatchRequest(items, async (batchTask, number) => {
      const unprocessedData: any[] = [];
      const result = await this.dbClient.batchWrite({
        ReturnConsumedCapacity: 'TOTAL',
        RequestItems: {
          [this.tableName]: batchTask.map((t) => {
            return {
              PutRequest: {
                Item: t
              },
            }
          })
        }
      }, function (err, data) {
        if (err) {
          //fail
        } else {
          if (data.UnprocessedItems && data.UnprocessedItems[self.tableName]) {
            data.UnprocessedItems[self.tableName].forEach((value, key) => {
              if (value.PutRequest) {
                unprocessedData.push(value.PutRequest.Item);
              }
            });
          }
        }
      }).promise();
      this.saveStats(result.ConsumedCapacity);
      return unprocessedData;
    });
  }

  async batchGet(keys: Key[]): Promise<any[]> {
    return await makeBatchRequest(keys, async (keysBatch: Key[]) => {
      const response = await this.dbClient.batchGet({
        ReturnConsumedCapacity: 'TOTAL',
        RequestItems: {
          [this.tableName]: {
            Keys: keysBatch.map(this.mapKey.bind(this)),
          }
        }
      }).promise();
      const responses = response.Responses;
      this.saveStats(response.ConsumedCapacity);
      return responses![this.tableName];
    }, 25);
  }

  async getAllItems(filter?: ScanFilter): Promise<any[]> {
    const req: DocumentClient.ScanInput = {
      TableName: this.tableName,
      ReturnConsumedCapacity: 'TOTAL',
    };
    if (!filter) {
      filter = {filter: "", mapping: {}};
    }
    if (filter.index) {
      req.IndexName = filter.index;
    }
    if (filter.filter) {
      req.FilterExpression = filter.filter;
    }
    if (filter.mapping) {
      const values = _.pickBy(filter.mapping, (v, k) => k.startsWith(':'));
      const keys = _.pickBy(filter.mapping, (v, k) => k.startsWith('#'));
      if (Object.keys(values).length > 0) {
        req.ExpressionAttributeValues = values;
      }
      if (Object.keys(keys).length > 0) {
        req.ExpressionAttributeNames = keys;
      }
    }
    return await this.paginateScan(req);
  }

  async scanPage(filter?: OnePageScan): Promise<ItemsRequestResultPage> {
    const req: DocumentClient.ScanInput = {
      TableName: this.tableName,
      ReturnConsumedCapacity: 'TOTAL',
    };
    if (!filter) {
      filter = {filter: "", mapping: {}};
    }
    if (filter.index) {
      req.IndexName = filter.index;
    }
    if (filter.filter) {
      req.FilterExpression = filter.filter;
    }
    if (filter.mapping) {
      const values = _.pickBy(filter.mapping, (v, k) => k.startsWith(':'));
      const keys = _.pickBy(filter.mapping, (v, k) => k.startsWith('#'));
      if (Object.keys(values).length > 0) {
        req.ExpressionAttributeValues = values;
      }
      if (Object.keys(keys).length > 0) {
        req.ExpressionAttributeNames = keys;
      }
    }
    if (!_.isEmpty(filter.lastEvaluatedKey)) {
      req.ExclusiveStartKey = filter.lastEvaluatedKey;
    }
    if (!_.isNil(filter.limit)) {
      req.Limit = filter.limit;
    }
    const result = await (this.dbClient.scan(req).promise());
    this.saveStats(result.ConsumedCapacity);
    return {
      items: result.Items || [],
      lastEvaluatedKey: result.LastEvaluatedKey,
    }
  }

  async queryItems(query: Query): Promise<any[]> {
    const req: DocumentClient.QueryInput = {
      TableName: this.tableName,
      KeyConditionExpression: query.query,
      ReturnConsumedCapacity: 'TOTAL',
      ScanIndexForward: !query.reverseSearch
    };
    if (query.mapping) {
      const values = _.pickBy(query.mapping, (v, k) => k.startsWith(':'));
      const keys = _.pickBy(query.mapping, (v, k) => k.startsWith('#'));
      if (Object.keys(values).length > 0) {
        req.ExpressionAttributeValues = values;
      }
      if (Object.keys(keys).length > 0) {
        req.ExpressionAttributeNames = keys;
      }
    }
    if (query.attributes) {
      req.ProjectionExpression = query.attributes.join(',');
    }
    if (query.filter) {
      req.FilterExpression = query.filter;
    }
    if (query.index) {
      req.IndexName = query.index;
    }
    return await this.paginateQuery(req);
  }

  async queryPage(query: OnePageQuery): Promise<ItemsRequestResultPage> {
    const req: DocumentClient.QueryInput = {
      TableName: this.tableName,
      KeyConditionExpression: query.query,
      ReturnConsumedCapacity: 'TOTAL',
      ScanIndexForward: !query.reverseSearch
    };
    if (!_.isEmpty(query.mapping)) {
      const values = _.pickBy(query.mapping, (v, k) => k.startsWith(':'));
      const keys = _.pickBy(query.mapping, (v, k) => k.startsWith('#'));
      if (Object.keys(values).length > 0) {
        req.ExpressionAttributeValues = values;
      }
      if (Object.keys(keys).length > 0) {
        req.ExpressionAttributeNames = keys;
      }
    }
    if (!_.isEmpty(query.attributes)) {
      req.ProjectionExpression = query.attributes!.join(',');
    }
    if (!_.isEmpty(query.filter)) {
      req.FilterExpression = query.filter;
    }
    if (!_.isEmpty(query.index)) {
      req.IndexName = query.index;
    }
    if (!_.isNil(query.limit)) {
      req.Limit = query.limit;
    }
    if (!_.isEmpty(query.lastEvaluatedKey)) {
      req.ExclusiveStartKey = query.lastEvaluatedKey;
    }

    const result = await (this.dbClient.query(req).promise());
    this.saveStats(result.ConsumedCapacity);
    return {
      items: result.Items || [],
      lastEvaluatedKey: result.LastEvaluatedKey,
    }
  }

  async deleteItems(keys: Key[]) {
    if (keys.length === 0) {
      return;
    }
    await this.ensureKey();
    await makeBatchRequest(keys, async (keysBatch: Key[]) => {
      const result = await this.dbClient.batchWrite({
        ReturnConsumedCapacity: 'TOTAL',
        RequestItems: {
          [this.tableName]: keysBatch.map((k) => {
            return {
              'DeleteRequest': {
                Key: this.mapKey(k),
              }
            };
          }),
        }
      }).promise();
      this.saveStats(result.ConsumedCapacity);
    }, 25);

  }

  async deleteItem(key: Key) {
    await this.ensureKey();
    const result = await this.dbClient.delete({
      TableName: this.tableName,
      Key: this.mapKey(key),
      ReturnConsumedCapacity: 'TOTAL',
    }).promise();
    this.saveStats(result.ConsumedCapacity);
  }

  async deleteAll() {
    await this.ensureKey();
    const items = await this.getAllItems();
    const keys: Key[] = items.map(this.makeKey.bind(this));
    await this.deleteItems(keys);
  }

  async testConnection() {
    const dbClient = new SDK.DynamoDB(this.awsCfg);
    try {
      await dbClient.describeTable({TableName: this.tableName}).promise();
    } catch (e: any) {
      return e.message;
    }
  }


  private async paginateQuery(request: DocumentClient.QueryInput) {
    const result: any = [];
    let lastKey: DocumentClient.Key | undefined = undefined;
    let first = true;
    while (lastKey || first) {
      first = false;
      if (lastKey) {
        request.ExclusiveStartKey = lastKey;
      }
      const response = await (this.dbClient.query(request).promise());
      result.push(...(response.Items || []));
      lastKey = response.LastEvaluatedKey;
      this.saveStats(response.ConsumedCapacity);
    }
    return result;
  }

  private async paginateScan(request: DocumentClient.ScanInput) {
    const result: any = [];
    let lastKey: DocumentClient.Key | undefined = undefined;
    let first = true;
    while (lastKey || first) {
      first = false;
      if (lastKey) {
        request.ExclusiveStartKey = lastKey;
      }
      const response = await (this.dbClient.scan(request).promise());
      result.push(...(response.Items || []));
      lastKey = response.LastEvaluatedKey;
      this.saveStats(response.ConsumedCapacity);
    }
    return result;
  }


}

type BatchExecutor<Task, Result> = (t: Task[], n?: number) => Promise<Result[] | void>

const makeBatchRequest = async <Task, Result>(tasks: Task[], executor: BatchExecutor<Task, Result>, batchSize = 25): Promise<Result[]> => {
  const result: Result[] = [];
  let {batch, processed} = _nextBatch(tasks, 0, batchSize);
  let counter = 0;
  while (batch.length > 0) {
    const partialResult = await executor(batch, counter++)
    if (partialResult) {
      result.push(...partialResult);
    }
    const next = _nextBatch(tasks, processed);
    batch = next.batch;
    processed = next.processed;
  }

  return result;
}

const _nextBatch = <B>(arr: B[] = [], from = 0, size = 10) => {
  const batch = arr.slice(from, from + size);
  return {
    batch,
    processed: from + batch.length,
  };
}

export const paginateQuery = async (client: DocumentClient, request: DocumentClient.QueryInput) => {
  const result: any = [];
  let lastKey: DocumentClient.Key | undefined = undefined;
  let first = true;
  while (lastKey || first) {
    first = false;
    if (lastKey) {
      request.ExclusiveStartKey = lastKey;
    }
    const response = await (client.query(request).promise());
    result.push(...(response.Items || []));
    lastKey = response.LastEvaluatedKey;
  }
  return result;
}

export const paginateScan = async (client: DocumentClient, request: DocumentClient.ScanInput) => {
  const result: any = [];
  let lastKey: DocumentClient.Key | undefined = undefined;
  let first = true;
  while (lastKey || first) {
    first = false;
    if (lastKey) {
      request.ExclusiveStartKey = lastKey;
    }
    const response = await (client.scan(request).promise());
    result.push(...(response.Items || []));
    lastKey = response.LastEvaluatedKey;
  }
  return result;
}

export type BuilderOptions = {
  deleteKeysIfUndefinedValue?: boolean,
  marshallValues?: boolean,
  ignoreAttributes?: string[],
}

export class UpdateExpressionBuilder {
  private setExpressions: string[] = [];
  private deleteExpressions: string[] = [];
  private expressionAttributeNames: { [key: string]: string } = {};
  private expressionAttributeValues: { [key: string]: any } = {};
  readonly opts: BuilderOptions;
  private source: { [p: string]: any };

  constructor(propertiesMap: { [key: string]: any }, opts?: BuilderOptions) {
    this.source = propertiesMap;
    this.opts = {
      ignoreAttributes: [],
      deleteKeysIfUndefinedValue: false,
      marshallValues: true,
      ...opts
    };
  }

  build() {
    const keys = Object.keys(this.source);
    keys.filter((k) => !this.opts.ignoreAttributes?.includes(k)).forEach((key) => {
      const value = this.source[key];
      if (value === undefined) {
        if (this.opts.deleteKeysIfUndefinedValue) {
          this.deleteExpressions.push(`#${key}`);
          this.expressionAttributeNames[`#${key}`] = key;
          return;
        } else {
          return;
        }
      }
      this.setExpressions.push(`#${key} = :${key}`);
      this.expressionAttributeNames[`#${key}`] = key;

      let marshalled = value;
      if (this.opts.marshallValues) {
        if (typeof value === "object" && !Array.isArray(value)) {
          marshalled = {
            M: DynamoDB.Converter.marshall(value, {convertEmptyValues: true}),
          }
        } else {
          marshalled = DynamoDB.Converter.input(value, {convertEmptyValues: true});
        }
      }
      this.expressionAttributeValues[`:${key}`] = marshalled;
    });
  };

  addSetExpression(expression: string, mapping?: { [key: string]: any }) {
    this.setExpressions.push(expression);
    if (mapping) {
      Object.keys(mapping).forEach((m) => {
        if (m.startsWith('#')) {
          this.expressionAttributeNames[m] = mapping[m];
        }
        if (m.startsWith(":")) {
          this.expressionAttributeValues[m] = mapping[m];
        }
      });
    }
  }

  getSetExpression() {
    let expr = '';
    if (this.setExpressions.length > 0) {
      expr += `SET ${this.setExpressions.join(', ')}`;
    }
    if (this.deleteExpressions.length > 0) {
      expr += ` REMOVE ${this.deleteExpressions.join(', ')}`;
    }
    return expr.trim();
  };

  getExpressionAttributeNames() {
    return this.expressionAttributeNames;
  };

  getExpressionAttributeValues() {
    return this.expressionAttributeValues;
  };

  printDebug() {
    console.log('SetExpr:', this.getSetExpression());
    console.log('ExAtNa:', this.getExpressionAttributeNames());
    console.log('ExAtVal:', JSON.stringify(this.getExpressionAttributeValues(),
      null, 2));
  }
}
