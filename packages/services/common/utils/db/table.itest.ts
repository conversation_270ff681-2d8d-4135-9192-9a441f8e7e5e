/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { useTestDatabase, useTestTable } from '../test-support/db-tests-helper.js';
import { initializeAwsTest } from '../test-support/test-config.js';
import { createTableSchema } from '../test-support/test-schema-builder.js';

const PK = 'partitionKey'
const SK = 'sortKey'

const config = initializeAwsTest();
const tableName = config.TEST_TABLE_NAME;

const database = useTestDatabase(async (c) => {
    await c.createTable(createTableSchema({
        name: tableName,
        partitionKey: { name: PK, type: 'string'},
        sortKey: { name: SK, type: 'string'},
    }));
});
const {cleanTable ,tableClient } = useTestTable(tableName);

beforeEach(cleanTable);

describe('#get', () => {

    beforeEach(async () => {
        await tableClient.putItem({
            [PK]: 'getOne',
            [SK]: '001',
            somefield: 'somevalue'
        });
    })
    it('should get the item', async () => {
        await expect(tableClient.getItem({ pk: 'getOne', sk: '001' })).resolves.toEqual(
            expect.objectContaining({
                [PK]: 'getOne',
                [SK]: '001',
                somefield: 'somevalue'
            }))
    })

})