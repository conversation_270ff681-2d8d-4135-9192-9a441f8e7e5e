/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import _ from 'lodash';
import { isEmptyStructOrString } from '../assertions.js';
import { AWS } from '../aws-helper.js';

type Patch = any;

export const updateOperation = (client: AWS.DynamoDB.DocumentClient) => {
  return new UpdateOperation(client);
}


export function buildConditions(attributeValueMap: {[key: string]: number | undefined}) {
  const res = {
    expression: undefined as any,
    eav: {},
    ean: {},
  }
  if (!attributeValueMap || _.isEmpty(attributeValueMap)) {
    return res;
  }
  const [attr, val] = Object.entries(attributeValueMap)[0];
  if (_.isNil(val)) {
    return res;
  }
  res.expression = "#__versionAttr = :__versionVal or attribute_not_exists(#__versionAttr)";
  res.eav[':__versionVal'] = val;
  res.ean['#__versionAttr'] = attr;
  return res;
}

export class UpdateOperation {
  constructor(private client: AWS.DynamoDB.DocumentClient) {
  }


  public async update({tableName, key, patch, versionCheck, conditionCheck}: {
    tableName: string,
    patch: Patch,
    key: AWS.DynamoDB.DocumentClient.Key,
    versionCheck?: {[key: string]: number | undefined},
    conditionCheck?: {[key: string]: any},
  }) {

    const sanitizedPatch = _.omitBy(patch, (value: any) => {
      return isEmptyStructOrString(value)
    })

    const builder = new UpdateExpressionBuilder(sanitizedPatch, {
      marshallValues: false,
      deleteKeysIfUndefinedValue: false,
      ignoreAttributes: Object.keys(key)
    });
    builder.build();
    const eav = builder.getExpressionAttributeValues();
    const ean = builder.getExpressionAttributeNames();
    const expr = builder.getSetExpression();

    const condition = buildConditions({
      ...(conditionCheck || {}),
      ...(versionCheck || {})
    })
    if (condition.expression) {
      Object.assign(eav, condition.eav);
      Object.assign(ean, condition.ean);
    }
    
    const updated = await this.client.update({
      Key: key,
      TableName: tableName,
      UpdateExpression: expr,
      ExpressionAttributeNames: _.isEmpty(ean) ? undefined : ean,
      ExpressionAttributeValues: _.isEmpty(eav) ? undefined : eav,
      ReturnValues: 'ALL_NEW',
      ConditionExpression: condition.expression,
    }).promise();
    return updated.Attributes;
  }
}


export type BuilderOptions = {
  deleteKeysIfUndefinedValue?: boolean,
  marshallValues?: boolean,
  ignoreAttributes?: string[],
}

export class UpdateExpressionBuilder {
  private setExpressions: string[] = [];
  private deleteExpressions: string[] = [];
  private expressionAttributeNames: { [key: string]: string } = {};
  private expressionAttributeValues: { [key: string]: any } = {};
  readonly opts: Required<BuilderOptions>;
  private source: { [p: string]: any };

  constructor(propertiesMap: { [key: string]: any }, opts?: BuilderOptions) {
    this.source = propertiesMap;
    this.opts = {
      ignoreAttributes: [],
      deleteKeysIfUndefinedValue: false,
      marshallValues: true,
      ...opts
    };
    if (!opts?.ignoreAttributes) {
      this.opts.ignoreAttributes = []
    }
  }

  build() {
    const keys = Object.keys(this.source);
    _.remove(keys, (k) => this.opts.ignoreAttributes.includes(k));
    keys.forEach((key) => {
      const value = this.source[key];
      if (value === undefined) {
        if (this.opts.deleteKeysIfUndefinedValue) {
          this.deleteExpressions.push(`#${key}`);
          this.expressionAttributeNames[`#${key}`] = key;
          return;
        } else {
          return;
        }
      }
      this.setExpressions.push(`#${key} = :${key}`);
      this.expressionAttributeNames[`#${key}`] = key;

      let marshalled = value;
      if (this.opts.marshallValues) {
        if (typeof value === 'object' && !Array.isArray(value)) {
          marshalled = {
            M: AWS.DynamoDB.Converter.marshall(value, {convertEmptyValues: true}),
          }
        } else {
          marshalled = AWS.DynamoDB.Converter.input(value, {convertEmptyValues: true});
        }
      }
      this.expressionAttributeValues[`:${key}`] = marshalled;
    });
  };

  addSetExpression(expression: string, mapping?: { [key: string]: any }) {
    this.setExpressions.push(expression);
    if (mapping) {
      Object.keys(mapping).forEach((m) => {
        if (m.startsWith('#')) {
          this.expressionAttributeNames[m] = mapping[m];
        }
        if (m.startsWith(':')) {
          this.expressionAttributeValues[m] = mapping[m];
        }
      });
    }
  }

  getSetExpression() {
    let expr = '';
    if (this.setExpressions.length > 0) {
      expr += `SET ${this.setExpressions.join(', ')}`;
    }
    if (this.deleteExpressions.length > 0) {
      expr += ` REMOVE ${this.deleteExpressions.join(', ')}`;
    }
    return expr.trim();
  };

  getExpressionAttributeNames() {
    return this.expressionAttributeNames;
  };

  getExpressionAttributeValues() {
    return this.expressionAttributeValues;
  };

  printDebug() {
    console.log('SetExpr:', this.getSetExpression());
    console.log('ExAtNa:', this.getExpressionAttributeNames());
    console.log('ExAtVal:', JSON.stringify(this.getExpressionAttributeValues(),
      null, 2));
  }
}
