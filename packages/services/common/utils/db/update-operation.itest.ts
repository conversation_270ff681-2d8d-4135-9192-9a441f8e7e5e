/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { useTestDatabase, useTestTable } from '../test-support/db-tests-helper.js';
import { createExampleSchema } from '../test-support/test-schema-builder.js';
import { updateOperation } from './update-operation.js';

const TEST_TABLE_NAME = 'test-database'
const database = useTestDatabase(async (c) => {
    await c.createTable(createExampleSchema(TEST_TABLE_NAME))
});
const {cleanTable, tableClient, documentClient} = useTestTable(TEST_TABLE_NAME);

beforeEach(cleanTable);

const key = {
    'partitionKey': 'update-operation',
    'sortKey': 'sk',
};
describe('update with optimistic lock check', () => {
    beforeEach(async () => {
        await tableClient.putItem({
            ...key,
            somefield: 'old value',
            updatedAt: 1,
        });
    });
    describe('when not updated', () => {
        it('should update', async () => {
            const operation = updateOperation(documentClient).update({
                tableName: TEST_TABLE_NAME,
                key,
                patch: {
                    somefield: 'new value',
                },
                versionCheck: {
                    updatedAt: 1,
                }
            });
            await expect(operation).resolves.toEqual(expect.objectContaining({
                somefield: 'new value',
                updatedAt: 1,
            }))
        })
    });
    describe('when already updated', () => {
        it('should faile with ConditionException', async () => {
            const operation = updateOperation(documentClient).update({
                tableName: TEST_TABLE_NAME,
                key,
                patch: {
                    somefield: 'new value',
                },
                versionCheck: {
                    updatedAt: 0,
                }
            });
            await expect(operation).rejects.toThrowError('The conditional request failed')
        })
    });
});
describe('update with optimistic lock check (when lock field not exists)', () => {
    beforeEach(async () => {
        await tableClient.putItem({
            ...key,
            somefield: 'old value',
        });
    })
    it('should update', async () => {
        const operation = updateOperation(documentClient).update({
            tableName: TEST_TABLE_NAME,
            key,
            patch: {
                somefield: 'new value',
            },
            versionCheck: {
                updatedAt: 1,
            }
        });
        await expect(operation).resolves.toEqual(expect.objectContaining({
            somefield: 'new value',
        }))
    })
})