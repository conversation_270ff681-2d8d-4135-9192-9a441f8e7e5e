/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { v4 as uuid, validate } from 'uuid';

export function generateUUID() {
  return uuid();
}

export function isUuid(str: string) {
  return validate(str);
}

export function generateRandomStringFromChars(length: number, characters = 'abcdefghijklmnoprsuqtwxyz') {
  let result = '';
  for (let i = length; i > 0; --i) result += characters[Math.floor(Math.random() * characters.length)];
  return result;
}

export function generateRandomStringOfHexSym(length = 5) {
  return generateRandomStringFromChars(length, '0123456789abcdef');
}
