/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import PromisePool from 'es6-promise-pool';

type Task<T> = { id: any, task: Promise<T> }
type TaskResult<T> = {
  result?: T,
  id: any,
  error?: any
}

export async function runParallelPool<T>(tasks: Promise<T>[], size = 100) {
  let index = 0;
  const pool = new PromisePool(() => {
    if (index < tasks.length) {
      const task = tasks[index];
      index++;
      return task;
    }
  }, size);
  await pool.start();
}

export async function runParallelSimple(tasks: Promise<unknown>[]): Promise<unknown[]> {
  return await Promise.all(tasks);
}

export async function runParallel<T>(tasks: Task<T>[]): Promise<TaskResult<T>[]> {
  const promiseResults = await Promise.allSettled(tasks.map(t => t.task));
  const results: TaskResult<T>[] = []
  for (let i = 0; i < tasks.length; i++) {
    const id = tasks[i].id;
    const result = promiseResults[i];
    if (result.status === 'fulfilled') {
      results.push({id, result: result.value as T})
    } else {
      results.push({id, error: result.reason})
    }
  }
  return results;
}
