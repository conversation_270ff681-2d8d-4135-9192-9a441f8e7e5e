/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {DateTime, Settings} from 'luxon';
import {DurationObjectUnits} from 'luxon/src/duration';
import _ from 'lodash';

const originalCurrentTimeCallback = Settings.now;

export type DateTimeLike = DateTime | string | number

export function setDefaultTimezoneJapan() {
  Settings.defaultZone = 'Asia/Tokyo';
}
export function setDefaultLocaleEn() {
  Settings.defaultLocale = 'en-US';
}

export function freezeDateTimeTo(datetime: string | DateTime) {
  const fixedUnix = DateTime.isDateTime(datetime) ? datetime.toMillis() : DateTime.fromISO(datetime).toMillis();
  Settings.now = () => {
    return fixedUnix;
  }
}

export function isToday(isoDateTime: string) {
  return DateTime.fromISO(isoDateTime).toISODate() === nowLuxonDateTime().toISODate();
}

export function unfreezeDateTime() {
  Settings.now = originalCurrentTimeCallback;
}

export function expireAfter(after: DurationObjectUnits) {
  return Math.round(DateTime.now().plus(after).toSeconds());
}

export function expireAfter3Months() {
  return expireAfter({months: 3});
}

export function nowISO() {
  return DateTime.now().toISO();
}

export function nowYYYYMM() {
  return nowLuxonDateTime().toFormat('yyyyMM')
}

export function nowUnixSec() {
  return Math.round(DateTime.now().toSeconds());
}

export function nowLuxonDateTime() {
  return DateTime.now();
}

export function fromISO(iso: string) {
  return DateTime.fromISO(iso);
}
export function fromUnixSec(seconds: number) {
  return DateTime.fromSeconds(seconds);
}
export function toYYYYMMSS(dateTime: DateTime = DateTime.now()) {
  return dateTime.toFormat('yyyyMMdd');
}
export function toYYYYMM(dateTime: DateTime = DateTime.now()) {
  return dateTime.toFormat('yyyyMM');
}
export function toYYYYhpMMhpDD(dateTime: DateTime = DateTime.now()) {
  return dateTime.toFormat('yyyy-MM-dd');
}
export function toLuxonDateTimeSafe(time: DateTimeLike) {
  if (DateTime.isDateTime(time)) {
    return time;
  }
  if (typeof time === 'number') {
    return DateTime.fromSeconds(time);
  }
  if (typeof time === 'string') {
    return DateTime.fromISO(time);
  }
  return null;
}
export function toLuxonDateTime(time: DateTimeLike) {
  const datetime = toLuxonDateTimeSafe(time);
  if (_.isNil(datetime) || !datetime.isValid) {
    throw new Error(`Invalid expression for time: [${time}]`)
  }
  return datetime;
}
export function hasPassed(time: DateTimeLike, duration: DurationObjectUnits) {
  return toLuxonDateTime(time).plus(duration).toSeconds() < nowUnixSec()
}
export function isPast(time: DateTimeLike) {
  return toLuxonDateTime(time).toSeconds() < nowUnixSec();
}
export function tomorrow() {
  return nowLuxonDateTime().plus({day: 1});
}

export class Timer {
  private started = false;
  private _finished = false;
  private timerId: any;

  constructor(private milliseconds: number) {
  }

  public start(cb?: (elapsed: number) => void) {
    this.timerId = setTimeout(
      () => {
        this._finished = true;
        if (cb) {
          cb(this.milliseconds);
        }
      }, this.milliseconds);
    this.started = true;
  }

  get isTimeout() {
    return this._finished;
  }

  public async wait() {
    this.started = true;
    return new Promise<void>((resolve) => setTimeout(() => {
      this._finished = true;
      resolve();
    }, this.milliseconds))
  }

  public cancel() {
    if (this.timerId) {
      clearTimeout(this.timerId);
    }
    this._finished = true;
  }
}

export const runTimer = (params: { seconds?: number, milliseconds?: number }, cb?: () => void) => {
  const millis = params.milliseconds ? params.milliseconds : (params.seconds ? params.seconds * 1000 : 0);
  const timer = new Timer(millis);
  timer.start(cb);
  return timer;
}

export const waitFor = async (params: { seconds?: number, milliseconds?: number }) => {
  const millis = params.milliseconds ? params.milliseconds : (params.seconds ? params.seconds * 1000 : 0);
  return new Timer(millis).wait();
}
