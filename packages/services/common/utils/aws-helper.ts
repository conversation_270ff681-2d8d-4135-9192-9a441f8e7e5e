/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import awsSdk from 'aws-sdk';

let AWS = awsSdk;

if (process.env.NODE_ENV === 'test') {
  AWS = awsSdk;
}
function setSDK(sdk: any) {
  AWS = sdk;
}

let serviceEndpoint: string | undefined = ''
function setEndpoint(endpoint: string) {
  serviceEndpoint = endpoint;
}
function restoreEndpoints() {
  serviceEndpoint = undefined;
}
function makeEndpoint() {
  return serviceEndpoint ? new awsSdk.Endpoint(serviceEndpoint) : undefined;
}

const getRegion = () => {
  return process.env.AWS_REGION, 'ap-northeast-1';
};

const initializeAwsLocal = (profileName?: string) => {
  if (!profileName) {
    profileName = process.env.AWS_PROFILE;
  }
  AWS.config.credentials = new AWS.SharedIniFileCredentials({
    profile: profileName,
  });
  AWS.config.region = getRegion();
};

export function initializeWithCredentials(credentials: {
  key: string,
  secret: string,
}) {
  AWS.config.update({
    credentials: new AWS.Credentials({
      accessKeyId: credentials.key,
      secretAccessKey: credentials.secret,
    }),
    region: getRegion()
  });
}
const getLocalCredentials = (profileName?: string) => {
  if (profileName) {
    return {
      credentials: new AWS.SharedIniFileCredentials({
        profile: profileName,
      }),
      region: getRegion(),
      endpoint: makeEndpoint(),
    };
  } else {
    return {
      credentials: AWS.config.credentials,
      region: AWS.config.region || getRegion(),
      endpoint: makeEndpoint(),
    };
  }
};

const isAwsEnvironment = () => {
  // check whether it is SAM local-api Docker env
  if (process.env.AWS_SAM_LOCAL === 'true') {
    return false;
  }
  return !!process.env.AWS_LAMBDA_LOG_STREAM_NAME;
};
const isLocalRun = () => {
  return !!process.env.IS_LOCAL;
};

const getEnvironmentCredentials = (profileName?: string) => {
  if (isAwsEnvironment()) {
    return undefined;
  }
  if (isLocalRun()) return  undefined;
  if (process.env.AWS_ACCESS_KEY_ID) {
    return {
      credentials: new AWS.Credentials({
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      }),
      region: getRegion(),
      endpoint: makeEndpoint(),
    };
  }
  return getLocalCredentials(profileName);
};

export {
  AWS,
  setEndpoint,
  restoreEndpoints,
  setSDK,
  getRegion,
  initializeAwsLocal,
  isAwsEnvironment,
  getLocalCredentials,
  getEnvironmentCredentials,
};
