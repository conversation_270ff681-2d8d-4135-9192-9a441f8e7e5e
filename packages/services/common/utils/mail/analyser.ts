/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

const parseRegex = (str:string) => {
    try {
        return new RegExp(str);
    } catch (e: any) {
        // it's OK if the regex is invalid here, because it supposed to be user's input,
        // that should be validated separately
        return null;
    }
};

export const matches = (parsedMail: {
    subject: string,
    body: string,
}, subjectRegEx: string | undefined, bodyRegEx: string | undefined) => {
    let matches = true;

    if (subjectRegEx) {
        const regex = parseRegex(subjectRegEx);
        if (regex) {
            const subject = parsedMail.subject;
            matches = regex.test(subject);
        } else {
          matches = false;
        }
    } else {
      matches = false;
    }

    if (!matches) {
        return false;
    }

    if (bodyRegEx) {
        const regex = parseRegex(bodyRegEx);
        if (regex) {
            const body = parsedMail.body;
            matches = regex.test(body);
        } else {
          matches = false;
        }
    } else {
      matches = false;
    }

    return matches;

};

export const entry = ({bodyRegex, subjectRegex}: { bodyRegex: string, subjectRegex: string}) => {
    return (mail) => {
        const body = mail.body || '';
        const subject = mail.subject || '';

        return matches({
            body, subject,
        }, subjectRegex, bodyRegex)
    }
}

export default entry;
