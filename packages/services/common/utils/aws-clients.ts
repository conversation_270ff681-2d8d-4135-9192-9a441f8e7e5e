/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { awsUtils as awsHelper  }  from './index.js';

import { DDClient } from './index.js';
import aws from 'aws-sdk';

const createIdp = () => {
  return new aws.CognitoIdentityServiceProvider(awsHelper.getEnvironmentCredentials());
};

const buildS3UploadLink = async (operation, params) => {
  return new aws.S3(awsHelper.getEnvironmentCredentials()).getSignedUrlPromise(operation, params);
};

const tableSegmentDelivery = (tableName) => {
  return new DDClient.Table(
    tableName,
    {
      pkField: 'id',
      skField: 'type',
    },
    awsHelper.getEnvironmentCredentials()
  );
};

const tableSurveyResults = (tableName) => {
  return new DDClient.Table(
    tableName,
    {
      pkField: 'partitionKey',
      skField: 'sortKey',
    },
    awsHelper.getEnvironmentCredentials()
  );
};

export const createLambdaClient = () => new aws.Lambda(awsHelper.getEnvironmentCredentials())
export const dynamoDbDocumentClient = () => new aws.DynamoDB.DocumentClient(awsHelper.getEnvironmentCredentials());
export const parameterStoreClient = () => new aws.SSM(awsHelper.getEnvironmentCredentials());
export {
  createIdp as createCognitoIdp,
  buildS3UploadLink,
  tableSegmentDelivery,
  tableSurveyResults,
};
