/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */


import _ from 'lodash';

export class AppConfig {
  private configMap: Record<string, string> = {};

  public get(key: string, defaultValue?: string) {
    const value = this.configMap[key];
    if (value === null || value === undefined) {
      return defaultValue;
    }
    return value;
  }

  public assertValuesPresence(names: string[]) {
    const missedParameters = names.filter(n => _.isEmpty(this.get(n)))
    if (missedParameters.length > 0) {
      throw new Error('Missed parameters with names: ' + missedParameters.toString());
    }
  }

  public getNumber(key: string, defaultValue?: number) {
    const value = Number.parseFloat(this.get(key) || '');
    return isNaN(value) ? defaultValue : value;
  }

  public getString(key: string, defaultValue?: string) {
    return this.get(key, defaultValue);
  }

  public merge(r: Record<string, string>) {
    Object.assign(this.configMap, r);
  }

  public mapFromProcessEnv() {
    Object.entries(process.env).forEach(([key, value]) => {
      if (_.isNil(value)) return;
      this.configMap[key] = value;
    })
  }
  public debugPrintToString() {
    let str = '----\n'
    Object.entries(this.configMap).forEach((entry) => {
      str += `${entry[0]}: ${entry[1]}\n`
    });
    str += '----'
    return str;
  }
}
