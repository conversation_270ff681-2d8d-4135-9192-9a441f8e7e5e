/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

type K = string

class SimpleBucket<V> {
    arr: V[] = [];
    
    put(v: V) {
        this.arr.push(v);
    }
    toArray() {
        return this.arr;
    }
    length() {
        return this.arr.length;
    }
}
export class MultiDict<V> {
    private map: {[key : K]: SimpleBucket<V>} = {};
    
    private createBucketAndGet(key: K) {
        let bucket = this.map[key];
        if (bucket) {
            return bucket;
        }
        bucket = new SimpleBucket<V>();
        this.map[key] = bucket;
        return bucket;
    }
    add(value: V, key: K) {
        this.createBucketAndGet(key).put(value);
    }
    getValues(key: string): V[] {
        if (!this.map[key]) return [];
        return this.createBucketAndGet(key).toArray();
    }
    getFirstValue(key: K) {
        return this.getValues(key)[0];
    }
    hasSomeValuesForKey(key: K) {
        return this.countValues(key) > 0;
    }
    countValues(key: K) {
        if (!this.map[key]) return 0;
        return this.createBucketAndGet(key).length();
    }
    keys() {
        return Object.keys(this.map);
    }
    filterKeys(predicate: (k: K) => boolean) {
        Object.keys(this.map).forEach(key => {
            if (!predicate(key)) {
                delete this.map[key];
            }
        })
    }
    prettyPrint() {
        const o = {};
        Object.keys(this.map).forEach(k => {
            o[k] = this.createBucketAndGet(k).toArray();
        })
        return JSON.stringify(o, null, 2)
    }
}
export function mdict<T = any>(): MultiDict<T> {
    return new MultiDict<T>();
}