/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { MultiDict } from "./multimap.js"

describe('multimap', () => {
    let map: MultiDict<string>
    beforeEach(() => {
        map = new MultiDict();

        map.add('v1', 'k1');
        map.add('v2', 'k1');
        map.add('v3', 'k2');
    })
    describe('stored values', () => {
        it('should have 2 vals under k1', () => {
            expect(map.getValues('k1')).toHaveLength(2);
        });
        it('should have 1 val under k2', () => {
            expect(map.getValues('k2')).toHaveLength(1);
        });
        it('should have nothing under k_null', () => {
            expect(map.getValues('k_null')).toHaveLength(0);
        })
    });
    describe('getValues', () => {
        it('should get [v1,v2] from k1', () => {
            expect(map.getValues('k1')).toEqual(['v1', 'v2'])
        });
        it('should get [] from k_null', () => {
            expect(map.getValues('k_null')).toEqual([]);
        });
    });
    
})