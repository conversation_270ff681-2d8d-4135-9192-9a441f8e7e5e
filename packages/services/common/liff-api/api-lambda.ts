import createAPI, {API, Request, Response} from "lambda-api";
import momentTimezone from "moment-timezone";
import {setDefaultLocaleEn, setDefaultTimezoneJapan} from "../utils/time-utils.js";
import {AppConfig as Config, compose} from "./config/config.js";
import defaultConfig from "./config/defaults.js";
import {logger, onLambdaRequestStartCallback} from 'common/logs/logger.js'
import installMiddlewares from "./middleware/install.js";
import {APIGatewayProxyHandlerV2} from "aws-lambda";
import {setHeadersOnResponseJson} from "./middleware/cors.js";
import {APIGatewayProxyStructuredResultV2} from "aws-lambda/trigger/api-gateway-proxy.js";
import console from "console";
import {loadFromEnvironment} from "./config/config-loaders.js";
import staticConfig from "./config/static.js";
import {requestContext} from "./execution-context.js";

console.log('api-lambda init')
momentTimezone.tz.setDefault('Asia/Tokyo')
setDefaultTimezoneJapan();
setDefaultLocaleEn();

const lazyInitialize = () => {
    const config = new Config(compose(defaultConfig, loadFromEnvironment()));
    staticConfig.merge(config);
};
lazyInitialize();

export const apiHandlerFactory = (base: string, routeConfiguration: (api: API) => void): APIGatewayProxyHandlerV2<void> => {
    const api = createAPI({ base } );
    installMiddlewares(api);
    routeConfiguration(api);
    return async (event, ctx) => {
        onLambdaRequestStartCallback()
        requestContext.reset();
        requestContext.setCurrentRequest(event);
        const result: APIGatewayProxyStructuredResultV2 = await api.run(event, ctx) ;
        setHeadersOnResponseJson(event, result)
        return result;
    }
}
