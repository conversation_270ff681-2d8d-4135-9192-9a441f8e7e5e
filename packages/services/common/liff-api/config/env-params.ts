/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import c from './constants.js';

export default {
  [c.DEVELOPMENT_MODE]: 'DEVELOPMENT_MODE',
  [c.TABLE_SURVEY_CONFIGS]: 'TABLE_SURVEY_CONFIGS',
  [c.TABLE_SURVEY_RESULTS]: 'TABLE_SURVEY_RESULTS',
  [c.TABLE_SURVEY_RESULTS_HISTORY]: 'TABLE_SURVEY_RESULTS_HISTORY',
  [c.TABLE_SURVEY_CALENDARS]: 'TABLE_SURVEY_CALENDARS',
  [c.AWS_REGION]: 'AWS_REGION',
  [c.API_CORS_ORIGIN]: 'API_CORS_ORIGIN',
  [c.API_INTERCHANGE_LOGGING_ENABLED]: 'API_INTERCHANGE_LOGGING_ENABLED',
  [c.LINEMESSAGING_CHANNEL_ACCESS_TOKEN]: 'LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
  [c.LINELOGIN_CHANNEL_ID]: 'LINELOGIN_CHANNEL_ID',
  [c.LINELOGIN_CLIENT_SECRET]: 'LINELOGIN_CLIENT_SECRET',
  [c.LINELOGIN_CLIENT_SECRET]: 'LINELOGIN_CLIENT_SECRET',
  [c.LINELOGIN_REDIRECT_URL]: 'LINELOGIN_REDIRECT_URL',
  [c.DEPLOY_ENV]: 'DEPLOY_ENV',
  [c.TABLE_LINE_USERS]: 'TABLE_LINE_USERS',
  [c.VUE_APP_LIFF_WEB_URL]: 'VUE_APP_LIFF_WEB_URL',
};
