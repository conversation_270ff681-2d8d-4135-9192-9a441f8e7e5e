/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

export default {
  TABLE_SURVEY_CONFIGS: 'TABLE_SURVEY_CONFIGS',
  TABLE_SURVEY_RESULTS: 'TABLE_SURVEY_RESULTS',
  TABLE_SURVEY_RESULTS_HISTORY: 'TABLE_SURVEY_RESULTS_HISTORY',
  TABLE_SURVEY_CALENDARS: 'TABLE_SURVEY_CALENDARS',
  TABLE_LINE_USERS: 'TABLE_LINE_USERS',

  AWS_REGION: 'AWS_REGION',

  API_CORS_ORIGIN: 'API_CORS_ORIGIN',
  API_INTERCHANGE_LOGGING_ENABLED: 'API_INTERCHANGE_LOGGING_ENABLED',

  LINEMESSAGING_CHANNEL_ACCESS_TOKEN: 'LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
  LINELOGIN_CHANNEL_ID: 'LINELOGIN_CHANNEL_ID',
  LINELOGIN_CLIENT_SECRET: 'LINELOGIN_CLIENT_SECRET',
  LINELOGIN_REDIRECT_URL: 'LINELOGIN_REDIRECT_URL',

  DEVELOPMENT_MODE: 'DEVELOPMENT_MODE',
  DEPLOY_ENV: 'DEPLOY_ENV',
  ENVIRONMENT_TYPE: 'ENVIRONMENT_TYPE',

  VUE_APP_LIFF_WEB_URL: 'VUE_APP_LIFF_WEB_URL',

  TRANSACTION_ERROR_VALUES: [
    'NoQuotas', 'DayOff',
    'CalendarOff', 'OutsideReservationControl',
    'InvalidSurvey', 'MissingBirthdayAnswer',
    'NotReservableAge',
  ],
  TRANSACTION_ERROR_MESSAGES: {
    NoQuotas: {
      code: 'no_quotas',
      message: '指定された日時は枠が埋まっています。お手数ですが、他の日付・時間を選んでください。',
    },
    DayOff: {
      code: 'day_off',
      message: '指定の日付は休日です。お手数ですが、他の日付を選んでください。',
    },
    CalendarOff: {
      code: 'calendar_off',
      message: '現在LINEからの予約を受け付けておりません。お手数ですが、画面を閉じて最初からやり直すか、管理者にお問い合わせください。',
    },
    OutsideReservationControl: {
      code: 'outside_reservation_control',
      message: '指定の日付は予約可能期間外です。お手数ですが、画面を閉じて最初からやり直すか、管理者にお問い合わせください。',
    },
    InvalidSurvey: {
      code: 'invalid_survey',
      message: '生年月日設定が存在しない、または複数設定されているため予約を完了できません。管理者にお問い合わせください。',
    },
    MissingBirthdayAnswer: {
      code: 'missing_birthday_answer',
      message: '生年月日を入力してください。',
    },
    NotReservableAge: {
      code: 'not_reservable_age',
      message: '予約可能な年齢ではありません。最初からやり直してください。'
    },
  },

  SURVEY_RESULTS_HISTORY: {
    deleted: '削除された',
    updated: '保存',
    created: '作成',
    idSet: 'ユーザーID設定',
    idSetText: 'LINEユーザーID',
  },

  CLOSE_GUIDANCE: 'お手数ですがブラウザを閉じてから、もう一度やり直してください。',
};
