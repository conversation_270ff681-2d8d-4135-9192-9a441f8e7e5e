/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import c from '../../api-reception/config/static';

export const getLogLevel = () => {
  return ['development', 'test'].includes(c.environmentType) ? 'debug' : 'info';
};

export const isDebugLogging = () => {
  return getLogLevel() === 'debug';
};

/**
 * @deprecated use #debugLog instead
 * @param args
 */
export const appLogDebug = (...args) => {
  if (getLogLevel() === 'debug') {
    console.log(...args);
  }
};
/**
 * @deprecated use #infoLog instead
 * @param args
 */
export const appLogInfo = (...args) => {
  console.log(...args);
};

const printIt = (arg) => {
  if (typeof arg === 'object') {
    if (arg.toString === Object.prototype.toString) {
      try {
        return JSON.stringify(arg, null, 2);
      } catch (e: any) {
        return arg;
      }
    } else {
      return arg;
    }
  }
  return arg;
};

export const debugLog = (...args) => {
  if (c.environmentType === 'development') {
    const args2 = args.map((a) => {
      if (typeof a === 'function') {
        return a();
      }
      return a;
    });
    console.log(...args2.map(printIt));
  }
};

export const infoLog = (...args) => {
  console.log(...args.map(printIt));
};
