/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import { infoLog } from '../logger.js';
import { setResponseCorsHeaders } from './cors.js';
import config from '../config/static.js';
import * as _ from 'lodash';

const defaultHandler = (err, req, res, next) => {
  const diagnosticInfo = {
    method: req.method,
    path: req.path,
    userId: req.userId,
    code: err.code,
  };

  const commonLog = {
    message: err.message,
    type: err.type,
    status: err.status,
    details: err.details,
    query: req.query,
    body: req.body,
    ...diagnosticInfo,
  };
  infoLog('Exception caught', commonLog);
  setResponseCorsHeaders(req, res);

  if (err) {
    if (err.type === 'client') {
      let resBody = {};
      // create response body
      if (config.isDevelopmentEnv) {
        resBody = {
          code: err.code,
          message: err.message,
          details: {
            stack: err.stack,
            originalStack: err.originalStack,
            ...(err.details || {}),
          },
        };
      } else {
        resBody = {
          code: err.code,
          message: err.message,
        };
      }

      res.status(err.status || 400).json(resBody);
    } else {
      const requestId = _.get(req,'context.awsRequestId');
      const cwLogGroup = _.get(req, 'context.logGroupName');
      const cwLogStream = _.get(req, 'context.logStreamName');

      // special message that is to be redirected to Slack
      infoLog('"[ERROR]"', req.context.userId, diagnosticInfo, err.originalStack || err.stack);

      let resBody = {};
      if (config.isDevelopmentEnv) {
        resBody = {
          code: err.code,
          message: err.message,
          details: {
            requestId,
            cwLogGroup,
            cwLogStream,
            stack: err.stack,
            originalStack: err.originalStack,
          },
        };
      } else {
        resBody = {
          message: 'Internal server error',
        };
      }

      res.status(err.status || 500).json(resBody);
    }
  }

  next();
};

export { defaultHandler };
