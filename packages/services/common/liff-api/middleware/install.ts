/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

const installMiddlewares = (app,) => {
  app.use(require('./cors.js').methodsCors);
  app.use(require('./errors.js').defaultHandler);
  app.use(async (req, res, next) => {
    if (req.query.stop) {
      res.status(401).json('Tet');
    } else {
      next();
    }
  });
  app.use(require('./authorization.js').build());
};

export default installMiddlewares;
