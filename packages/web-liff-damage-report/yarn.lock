# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8

"@ampproject/remapping@npm:^2.3.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: d3ad7b89d973df059c4e8e6d7c972cbeb1bb2f18f002a3bd04ae0707da214cb06cc06929b65aa2313b9347463df2914772298bae8b1d7973f246bb3f2ab3e8f0
  languageName: node
  linkType: hard

"@antfu/eslint-config@npm:^3.11.2":
  version: 3.11.2
  resolution: "@antfu/eslint-config@npm:3.11.2"
  dependencies:
    "@antfu/install-pkg": ^0.5.0
    "@clack/prompts": ^0.8.2
    "@eslint-community/eslint-plugin-eslint-comments": ^4.4.1
    "@eslint/markdown": ^6.2.1
    "@stylistic/eslint-plugin": ^2.11.0
    "@typescript-eslint/eslint-plugin": ^8.16.0
    "@typescript-eslint/parser": ^8.16.0
    "@vitest/eslint-plugin": ^1.1.12
    eslint-config-flat-gitignore: ^0.3.0
    eslint-flat-config-utils: ^0.4.0
    eslint-merge-processors: ^0.1.0
    eslint-plugin-antfu: ^2.7.0
    eslint-plugin-command: ^0.2.6
    eslint-plugin-import-x: ^4.4.3
    eslint-plugin-jsdoc: ^50.6.0
    eslint-plugin-jsonc: ^2.18.2
    eslint-plugin-n: ^17.14.0
    eslint-plugin-no-only-tests: ^3.3.0
    eslint-plugin-perfectionist: ^4.1.2
    eslint-plugin-regexp: ^2.7.0
    eslint-plugin-toml: ^0.11.1
    eslint-plugin-unicorn: ^56.0.1
    eslint-plugin-unused-imports: ^4.1.4
    eslint-plugin-vue: ^9.31.0
    eslint-plugin-yml: ^1.15.0
    eslint-processor-vue-blocks: ^0.1.2
    globals: ^15.12.0
    jsonc-eslint-parser: ^2.4.0
    local-pkg: ^0.5.1
    parse-gitignore: ^2.0.0
    picocolors: ^1.1.1
    toml-eslint-parser: ^0.10.0
    vue-eslint-parser: ^9.4.3
    yaml-eslint-parser: ^1.2.3
    yargs: ^17.7.2
  peerDependencies:
    "@eslint-react/eslint-plugin": ^1.5.8
    "@prettier/plugin-xml": ^3.4.1
    "@unocss/eslint-plugin": ">=0.50.0"
    astro-eslint-parser: ^1.0.2
    eslint: ^9.10.0
    eslint-plugin-astro: ^1.2.0
    eslint-plugin-format: ">=0.1.0"
    eslint-plugin-react-hooks: ^5.0.0
    eslint-plugin-react-refresh: ^0.4.4
    eslint-plugin-solid: ^0.14.3
    eslint-plugin-svelte: ">=2.35.1"
    prettier-plugin-astro: ^0.14.0
    prettier-plugin-slidev: ^1.0.5
    svelte-eslint-parser: ">=0.37.0"
  peerDependenciesMeta:
    "@eslint-react/eslint-plugin":
      optional: true
    "@prettier/plugin-xml":
      optional: true
    "@unocss/eslint-plugin":
      optional: true
    astro-eslint-parser:
      optional: true
    eslint-plugin-astro:
      optional: true
    eslint-plugin-format:
      optional: true
    eslint-plugin-react-hooks:
      optional: true
    eslint-plugin-react-refresh:
      optional: true
    eslint-plugin-solid:
      optional: true
    eslint-plugin-svelte:
      optional: true
    prettier-plugin-astro:
      optional: true
    prettier-plugin-slidev:
      optional: true
    svelte-eslint-parser:
      optional: true
  bin:
    eslint-config: bin/index.js
  checksum: 0a28952a354b1f1841932cf0aee41f1652328f78d700fc4af72ec825061d3874789dee69cd3560dcbc0ad2698da7f59e3a9fdefd46f736e3507d42fe5c8c0529
  languageName: node
  linkType: hard

"@antfu/install-pkg@npm:^0.4.1":
  version: 0.4.1
  resolution: "@antfu/install-pkg@npm:0.4.1"
  dependencies:
    package-manager-detector: ^0.2.0
    tinyexec: ^0.3.0
  checksum: 3ffd59fa5a21dcb4951a037f5c91dfbfc152adfef9e7ba231a968f108aa28c1cd22213c437a8d34f2bfea8c2b9a6df20447eb04be15b534a8e9e21a9dcf203c0
  languageName: node
  linkType: hard

"@antfu/install-pkg@npm:^0.5.0":
  version: 0.5.0
  resolution: "@antfu/install-pkg@npm:0.5.0"
  dependencies:
    package-manager-detector: ^0.2.5
    tinyexec: ^0.3.1
  checksum: 93b38f8a18f5a06d3b0ae3685cf82b53791decfb5147dfc1e9e8b357b109fcce90677cf6fadc00a36e4f0d6ea320c8ea1e52c3e7ba7da55909583ad0f827ffb5
  languageName: node
  linkType: hard

"@antfu/ni@npm:^0.23.0":
  version: 0.23.1
  resolution: "@antfu/ni@npm:0.23.1"
  bin:
    na: bin/na.mjs
    nci: bin/nci.mjs
    ni: bin/ni.mjs
    nlx: bin/nlx.mjs
    nr: bin/nr.mjs
    nu: bin/nu.mjs
    nun: bin/nun.mjs
  checksum: 63d1681b6bb774a1d3c540a0ccdef7471f8a5dc3fa4575b83282004b144571601dc08c5c0f6933f204457869a249b38f92601b62718ad0e1afed16a10eacea33
  languageName: node
  linkType: hard

"@antfu/utils@npm:^0.7.10":
  version: 0.7.10
  resolution: "@antfu/utils@npm:0.7.10"
  checksum: b93dd9e2c7e96ae6dca8a07c1fc5e7165ea9c7a89e78ecb75959bc9a8e769d3f565aea1b5c43db7374dd1f405cc277b6d14d85f884886f9d424dd6144d9203f2
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0":
  version: 7.26.2
  resolution: "@babel/code-frame@npm:7.26.2"
  dependencies:
    "@babel/helper-validator-identifier": ^7.25.9
    js-tokens: ^4.0.0
    picocolors: ^1.0.0
  checksum: db13f5c42d54b76c1480916485e6900748bbcb0014a8aca87f50a091f70ff4e0d0a6db63cade75eb41fcc3d2b6ba0a7f89e343def4f96f00269b41b8ab8dd7b8
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.26.2":
  version: 7.26.3
  resolution: "@babel/generator@npm:7.26.3"
  dependencies:
    "@babel/parser": ^7.26.3
    "@babel/types": ^7.26.3
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.25
    jsesc: ^3.0.2
  checksum: fb09fa55c66f272badf71c20a3a2cee0fa1a447fed32d1b84f16a668a42aff3e5f5ddc6ed5d832dda1e952187c002ca1a5cdd827022efe591b6ac44cada884ea
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-string-parser@npm:7.25.9"
  checksum: 6435ee0849e101681c1849868278b5aee82686ba2c1e27280e5e8aca6233af6810d39f8e4e693d2f2a44a3728a6ccfd66f72d71826a94105b86b731697cdfa99
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.24.7, @babel/helper-validator-identifier@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-validator-identifier@npm:7.25.9"
  checksum: 5b85918cb1a92a7f3f508ea02699e8d2422fe17ea8e82acd445006c0ef7520fbf48e3dbcdaf7b0a1d571fc3a2715a29719e5226636cb6042e15fe6ed2a590944
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.24.6, @babel/parser@npm:^7.25.3, @babel/parser@npm:^7.26.2, @babel/parser@npm:^7.26.3":
  version: 7.26.3
  resolution: "@babel/parser@npm:7.26.3"
  dependencies:
    "@babel/types": ^7.26.3
  bin:
    parser: ./bin/babel-parser.js
  checksum: e2bff2e9fa6540ee18fecc058bc74837eda2ddcecbe13454667314a93fc0ba26c1fb862c812d84f6d5f225c3bd8d191c3a42d4296e287a882c4e1f82ff2815ff
  languageName: node
  linkType: hard

"@babel/types@npm:^7.25.8, @babel/types@npm:^7.26.0, @babel/types@npm:^7.26.3":
  version: 7.26.3
  resolution: "@babel/types@npm:7.26.3"
  dependencies:
    "@babel/helper-string-parser": ^7.25.9
    "@babel/helper-validator-identifier": ^7.25.9
  checksum: 195f428080dcaadbcecc9445df7f91063beeaa91b49ccd78f38a5af6b75a6a58391d0c6614edb1ea322e57889a1684a0aab8e667951f820196901dd341f931e9
  languageName: node
  linkType: hard

"@bufbuild/protobuf@npm:^2.0.0":
  version: 2.2.3
  resolution: "@bufbuild/protobuf@npm:2.2.3"
  checksum: 567ca0497669a8944fe84a9fdfa236e4a91d5879190c0ec0c8727d5220cbc21a85d06a114ac1eb35387fc5cb1dcbb7adc583c4d4f6a2ecb34fbe61dcaa7e7e9b
  languageName: node
  linkType: hard

"@clack/core@npm:0.3.5":
  version: 0.3.5
  resolution: "@clack/core@npm:0.3.5"
  dependencies:
    picocolors: ^1.0.0
    sisteransi: ^1.0.5
  checksum: 50f9665bd5fdda557aed355be36498c71d3b211a9791b8ded1f5fe49532dbed0db2c9e851e3664c0430a3115177df745d719348455748f6ab582fb9c983d1465
  languageName: node
  linkType: hard

"@clack/prompts@npm:^0.8.2":
  version: 0.8.2
  resolution: "@clack/prompts@npm:0.8.2"
  dependencies:
    "@clack/core": 0.3.5
    picocolors: ^1.0.0
    sisteransi: ^1.0.5
  checksum: 1786107c8d73413206d59dfc6236406c8e8e54425cd7f1602137bff7e56f49e347e1a167c49efb69fffefa4a8ba298b09142fe47be2082f45a5a6c1d7bac59ed
  languageName: node
  linkType: hard

"@dprint/formatter@npm:^0.3.0":
  version: 0.3.0
  resolution: "@dprint/formatter@npm:0.3.0"
  checksum: 3ff486cb2395941680a84b11a5bf2c69ee10246904b1b394daa4a3ab5ced5676d0707797c3951f5d74aa0ebaf7e93219cfd2bdad448e95d9ca6d62a5a67e5c1c
  languageName: node
  linkType: hard

"@dprint/markdown@npm:^0.17.8":
  version: 0.17.8
  resolution: "@dprint/markdown@npm:0.17.8"
  checksum: f0412ab44e374b757a3fd81ec2262bd729f322f778ed245514229eaa5dfeaea0da4f35ff7e11d2994c1f12ad9e5e811e50c0c2871630b69fef6f7e0ca2d17760
  languageName: node
  linkType: hard

"@dprint/toml@npm:^0.6.3":
  version: 0.6.3
  resolution: "@dprint/toml@npm:0.6.3"
  checksum: b088d08f883772e5fbd783eea672ef4a5aaefae5707089b03514bb19f80a7414525d6c7113d17dd3d54916399dd8138a758b72d152026a1aa8b2ba79b5af5324
  languageName: node
  linkType: hard

"@es-joy/jsdoccomment@npm:^0.48.0":
  version: 0.48.0
  resolution: "@es-joy/jsdoccomment@npm:0.48.0"
  dependencies:
    comment-parser: 1.4.1
    esquery: ^1.6.0
    jsdoc-type-pratt-parser: ~4.1.0
  checksum: dc9aa1b30b43e06d94053287c2062ebff1147ed1627497c1f20f15a534744784b5adf9189e437ee62595585576112c99cff7833e84c2373614447672a4cc294f
  languageName: node
  linkType: hard

"@es-joy/jsdoccomment@npm:~0.49.0":
  version: 0.49.0
  resolution: "@es-joy/jsdoccomment@npm:0.49.0"
  dependencies:
    comment-parser: 1.4.1
    esquery: ^1.6.0
    jsdoc-type-pratt-parser: ~4.1.0
  checksum: 19f99097ceb5a3495843c3276d598cfb4e3287c5d1d809817fb28fc8352b16ef23eaa8d964fd7b0379c6466d0a591f579e51d25434ab709ff59f6650fa166dbf
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/aix-ppc64@npm:0.21.5"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/aix-ppc64@npm:0.23.1"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/android-arm64@npm:0.21.5"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/android-arm64@npm:0.23.1"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/android-arm@npm:0.21.5"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/android-arm@npm:0.23.1"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/android-x64@npm:0.21.5"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/android-x64@npm:0.23.1"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/darwin-arm64@npm:0.21.5"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/darwin-arm64@npm:0.23.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/darwin-x64@npm:0.21.5"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/darwin-x64@npm:0.23.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/freebsd-arm64@npm:0.21.5"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/freebsd-arm64@npm:0.23.1"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/freebsd-x64@npm:0.21.5"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/freebsd-x64@npm:0.23.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-arm64@npm:0.21.5"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/linux-arm64@npm:0.23.1"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-arm@npm:0.21.5"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/linux-arm@npm:0.23.1"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-ia32@npm:0.21.5"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/linux-ia32@npm:0.23.1"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-loong64@npm:0.21.5"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/linux-loong64@npm:0.23.1"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-mips64el@npm:0.21.5"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/linux-mips64el@npm:0.23.1"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-ppc64@npm:0.21.5"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/linux-ppc64@npm:0.23.1"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-riscv64@npm:0.21.5"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/linux-riscv64@npm:0.23.1"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-s390x@npm:0.21.5"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/linux-s390x@npm:0.23.1"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/linux-x64@npm:0.21.5"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/linux-x64@npm:0.23.1"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/netbsd-x64@npm:0.21.5"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/netbsd-x64@npm:0.23.1"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-arm64@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/openbsd-arm64@npm:0.23.1"
  conditions: os=openbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/openbsd-x64@npm:0.21.5"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/openbsd-x64@npm:0.23.1"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/sunos-x64@npm:0.21.5"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/sunos-x64@npm:0.23.1"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/win32-arm64@npm:0.21.5"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/win32-arm64@npm:0.23.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/win32-ia32@npm:0.21.5"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/win32-ia32@npm:0.23.1"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.21.5":
  version: 0.21.5
  resolution: "@esbuild/win32-x64@npm:0.21.5"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.23.1":
  version: 0.23.1
  resolution: "@esbuild/win32-x64@npm:0.23.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@eslint-community/eslint-plugin-eslint-comments@npm:^4.4.1":
  version: 4.4.1
  resolution: "@eslint-community/eslint-plugin-eslint-comments@npm:4.4.1"
  dependencies:
    escape-string-regexp: ^4.0.0
    ignore: ^5.2.4
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0
  checksum: 32edb04a698c1b0f2d157c57d2c931811e804f9e5df4ffdcfa52334d7f170cf22a0d5531e008efcf21e22a36474e68036882c5bc25076fedc5755e3bdfe7b8e8
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.1.2, @eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.4.0, @eslint-community/eslint-utils@npm:^4.4.1":
  version: 4.4.1
  resolution: "@eslint-community/eslint-utils@npm:4.4.1"
  dependencies:
    eslint-visitor-keys: ^3.4.3
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: a7ffc838eb6a9ef594cda348458ccf38f34439ac77dc090fa1c120024bcd4eb911dfd74d5ef44d42063e7949fa7c5123ce714a015c4abb917d4124be1bd32bfe
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.10.0, @eslint-community/regexpp@npm:^4.11.0, @eslint-community/regexpp@npm:^4.12.1, @eslint-community/regexpp@npm:^4.8.0":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 0d628680e204bc316d545b4993d3658427ca404ae646ce541fcc65306b8c712c340e5e573e30fb9f85f4855c0c5f6dca9868931f2fcced06417fbe1a0c6cd2d6
  languageName: node
  linkType: hard

"@eslint/compat@npm:^1.1.1":
  version: 1.2.4
  resolution: "@eslint/compat@npm:1.2.4"
  peerDependencies:
    eslint: ^9.10.0
  peerDependenciesMeta:
    eslint:
      optional: true
  checksum: d68b0e5d4f2890c86b439cd2e4c0f9c6e7eae09230a69cf80a0b647f7242ed5c662cc286a06d6eb06d95e3def62ed26e9e1eac494538d58b4e2cfc355d37c176
  languageName: node
  linkType: hard

"@eslint/config-array@npm:^0.19.0":
  version: 0.19.1
  resolution: "@eslint/config-array@npm:0.19.1"
  dependencies:
    "@eslint/object-schema": ^2.1.5
    debug: ^4.3.1
    minimatch: ^3.1.2
  checksum: 421aad712a5ef1a3d118b5e0857f79c080f9dd619a76ce19d20105d381521583786f7abb1195744af9e62a5124e6657066eb6780e920f4001846bd91c1a665f0
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.9.0":
  version: 0.9.1
  resolution: "@eslint/core@npm:0.9.1"
  dependencies:
    "@types/json-schema": ^7.0.15
  checksum: 33c8159842cc3a646caa267c008cb567ca60e0220bcdcf6e426128409953b8f6a9b142246db616c71d06331edf769c192d7e2792b3f19c2a6b8179e491512d89
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^3.2.0":
  version: 3.2.0
  resolution: "@eslint/eslintrc@npm:3.2.0"
  dependencies:
    ajv: ^6.12.4
    debug: ^4.3.2
    espree: ^10.0.1
    globals: ^14.0.0
    ignore: ^5.2.0
    import-fresh: ^3.2.1
    js-yaml: ^4.1.0
    minimatch: ^3.1.2
    strip-json-comments: ^3.1.1
  checksum: c898e4d12f4c9a79a61ee3c91e38eea5627a04e021cb749191e8537445858bfe32f810eca0cb2dc9902b8ad8b65ca07ef7221dc4bad52afe60cbbf50ec56c236
  languageName: node
  linkType: hard

"@eslint/js@npm:9.16.0":
  version: 9.16.0
  resolution: "@eslint/js@npm:9.16.0"
  checksum: ba2d7f7266df827df72cec069df9284ad5e7edb4894a8c58c41db0d489136b22815dc76cd34cf565284979feb4d3a8197b511e08529c03f30c80b5235d25030b
  languageName: node
  linkType: hard

"@eslint/markdown@npm:^6.2.1":
  version: 6.2.1
  resolution: "@eslint/markdown@npm:6.2.1"
  dependencies:
    "@eslint/plugin-kit": ^0.2.0
    mdast-util-from-markdown: ^2.0.1
    mdast-util-gfm: ^3.0.0
    micromark-extension-gfm: ^3.0.0
  checksum: 815ee9c812fc493dd9a6b50162699ed490041fbd825328763efa1854105b4eca3230748bf0a298cf5a91c5c756a8b4d937677587a514a386b9e0b73c81e2c923
  languageName: node
  linkType: hard

"@eslint/object-schema@npm:^2.1.5":
  version: 2.1.5
  resolution: "@eslint/object-schema@npm:2.1.5"
  checksum: 5facffc832bae93c510f4d38f0f1cbfebd3d7ec772ece6b801bd09bf2dce52e781f4dea500aa133d02257e04ed6a3958fa18cbaed1f9623974a804ee60a8ca54
  languageName: node
  linkType: hard

"@eslint/plugin-kit@npm:^0.2.0, @eslint/plugin-kit@npm:^0.2.3":
  version: 0.2.4
  resolution: "@eslint/plugin-kit@npm:0.2.4"
  dependencies:
    levn: ^0.4.1
  checksum: 5693465dca5fc6f27b090f987b51bc738f48c6a6b5678dcc1791522921834206388b462578edd362d458e8de6dcd21cce1a2e8cff47d1512411ba0389112c231
  languageName: node
  linkType: hard

"@googlemaps/js-api-loader@npm:^1.16.2":
  version: 1.16.8
  resolution: "@googlemaps/js-api-loader@npm:1.16.8"
  checksum: 2f5e2ced6b83fb72f32bcfd32c85558dba967580025a53d593e5a8bb15f63eb866e4f55b86aa7f5810e1616554c02100081a9a217d16d25766e57a128b39e825
  languageName: node
  linkType: hard

"@googlemaps/markerclusterer@npm:^2.4.0":
  version: 2.5.3
  resolution: "@googlemaps/markerclusterer@npm:2.5.3"
  dependencies:
    fast-deep-equal: ^3.1.3
    supercluster: ^8.0.1
  checksum: aa74e9b59d302a0c7444c48818f017532172973dece223c9a3f9b5cdb8aeba7ea3dd87ee785420972d24e7738937e76373e8aae8a0cf10f045bf9869d1b6b9ee
  languageName: node
  linkType: hard

"@humanfs/core@npm:^0.19.1":
  version: 0.19.1
  resolution: "@humanfs/core@npm:0.19.1"
  checksum: 611e0545146f55ddfdd5c20239cfb7911f9d0e28258787c4fc1a1f6214250830c9367aaaeace0096ed90b6739bee1e9c52ad5ba8adaf74ab8b449119303babfe
  languageName: node
  linkType: hard

"@humanfs/node@npm:^0.16.6":
  version: 0.16.6
  resolution: "@humanfs/node@npm:0.16.6"
  dependencies:
    "@humanfs/core": ^0.19.1
    "@humanwhocodes/retry": ^0.3.0
  checksum: f9cb52bb235f8b9c6fcff43a7e500669a38f8d6ce26593404a9b56365a1644e0ed60c720dc65ff6a696b1f85f3563ab055bb554ec8674f2559085ba840e47710
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 0fd22007db8034a2cdf2c764b140d37d9020bbfce8a49d3ec5c05290e77d4b0263b1b972b752df8c89e5eaa94073408f2b7d977aed131faf6cf396ebb5d7fb61
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.3.0":
  version: 0.3.1
  resolution: "@humanwhocodes/retry@npm:0.3.1"
  checksum: 7e5517bb51dbea3e02ab6cacef59a8f4b0ca023fc4b0b8cbc40de0ad29f46edd50b897c6e7fba79366a0217e3f48e2da8975056f6c35cfe19d9cc48f1d03c1dd
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.4.1":
  version: 0.4.1
  resolution: "@humanwhocodes/retry@npm:0.4.1"
  checksum: f11167c28e8266faba470fd273cbaafe2827523492bc18c5623015adb7ed66f46b2e542e3d756fed9ca614300249267814220c2f5f03a59e07fdfa64fc14ad52
  languageName: node
  linkType: hard

"@iconify/json@npm:^2.2.281":
  version: 2.2.281
  resolution: "@iconify/json@npm:2.2.281"
  dependencies:
    "@iconify/types": "*"
    pathe: ^1.1.2
  checksum: 863f65f7c6ec6b1ac99a21080205738bc701e5b3267da94b82a60e097212626eabaa383592cd205d168849e4256734fe5ef9b89eb93ff13fe85ad640af099854
  languageName: node
  linkType: hard

"@iconify/types@npm:*, @iconify/types@npm:^2.0.0":
  version: 2.0.0
  resolution: "@iconify/types@npm:2.0.0"
  checksum: 029f58542c160e9d4a746869cf2e475b603424d3adf3994c5cc8d0406c47e6e04a3b898b2707840c1c5b9bd5563a1660a34b110d89fce43923baca5222f4e597
  languageName: node
  linkType: hard

"@iconify/utils@npm:^2.1.33":
  version: 2.2.0
  resolution: "@iconify/utils@npm:2.2.0"
  dependencies:
    "@antfu/install-pkg": ^0.4.1
    "@antfu/utils": ^0.7.10
    "@iconify/types": ^2.0.0
    debug: ^4.4.0
    globals: ^15.13.0
    kolorist: ^1.8.0
    local-pkg: ^0.5.1
    mlly: ^1.7.3
  checksum: 1a11461c9c72a377eadf72a0a680665697e65ea4691a809ccd73f0447f0f88110cd8cb1ef5161942aeb2ae4554149a32b30c135c934eb84a4cd22af7143afeb0
  languageName: node
  linkType: hard

"@intlify/bundle-utils@npm:^10.0.0":
  version: 10.0.0
  resolution: "@intlify/bundle-utils@npm:10.0.0"
  dependencies:
    "@intlify/message-compiler": next
    "@intlify/shared": next
    acorn: ^8.8.2
    escodegen: ^2.1.0
    estree-walker: ^2.0.2
    jsonc-eslint-parser: ^2.3.0
    mlly: ^1.2.0
    source-map-js: ^1.0.1
    yaml-eslint-parser: ^1.2.2
  peerDependenciesMeta:
    petite-vue-i18n:
      optional: true
    vue-i18n:
      optional: true
  checksum: 502432aaded0b5a27a77ff4c9390fe77f78e2efd7502073eca77303cc55e085fa49d6f4338fc723348b889954902eaa04ffc9ebd43e50805d0b9a144a69e2b01
  languageName: node
  linkType: hard

"@intlify/core-base@npm:10.0.5":
  version: 10.0.5
  resolution: "@intlify/core-base@npm:10.0.5"
  dependencies:
    "@intlify/message-compiler": 10.0.5
    "@intlify/shared": 10.0.5
  checksum: 01c08b12554d942b3b112b7fc02c61b0c8a93a3ba24e8863dba954796827a6593ddc7d4ece9ea9bf21d349e12368473aad673a8f546758bc6ed178be659a0237
  languageName: node
  linkType: hard

"@intlify/message-compiler@npm:10.0.5":
  version: 10.0.5
  resolution: "@intlify/message-compiler@npm:10.0.5"
  dependencies:
    "@intlify/shared": 10.0.5
    source-map-js: ^1.0.2
  checksum: ce832bf9743bbeef8502e8bacd146e0085dd9add659b183a2ab5e882a74eab6d40fee15c18cebc5e18248d2f34ffde81be06937234cbfbe47d9a4b9f75068200
  languageName: node
  linkType: hard

"@intlify/message-compiler@npm:next":
  version: 11.0.0-beta.2
  resolution: "@intlify/message-compiler@npm:11.0.0-beta.2"
  dependencies:
    "@intlify/shared": 11.0.0-beta.2
    source-map-js: ^1.0.2
  checksum: 19e3d2e4c553a8660d1d6adb4b869e30d1de1ae56fda433c96cd5b89e3de29b5221ab7724ae9f9c5a63930ad37476637d2bd9a8887e408b83ebc5f5e13a1be12
  languageName: node
  linkType: hard

"@intlify/shared@npm:10.0.5, @intlify/shared@npm:^10.0.0, @intlify/shared@npm:latest":
  version: 10.0.5
  resolution: "@intlify/shared@npm:10.0.5"
  checksum: 3c007c0c43bccfd827dce57a74d8333d0003dc02d6fc5ebae79a6363f9c6bb655f8d6525166e9394b38e06016eac0676362c135a80f54d70f0b661cbec2bad0c
  languageName: node
  linkType: hard

"@intlify/shared@npm:11.0.0-beta.2, @intlify/shared@npm:next":
  version: 11.0.0-beta.2
  resolution: "@intlify/shared@npm:11.0.0-beta.2"
  checksum: 8704fee97520d1dd42721b99b09c1d6af9dae119cd477e24bc591543a6418c6efb07998af17a779506de83f972dc1308d71d876f725fbc0b72c23cffafaef97d
  languageName: node
  linkType: hard

"@intlify/unplugin-vue-i18n@npm:^6.0.1":
  version: 6.0.1
  resolution: "@intlify/unplugin-vue-i18n@npm:6.0.1"
  dependencies:
    "@eslint-community/eslint-utils": ^4.4.0
    "@intlify/bundle-utils": ^10.0.0
    "@intlify/shared": latest
    "@intlify/vue-i18n-extensions": ^7.0.0
    "@rollup/pluginutils": ^5.1.0
    "@typescript-eslint/scope-manager": ^8.13.0
    "@typescript-eslint/typescript-estree": ^8.13.0
    debug: ^4.3.3
    fast-glob: ^3.2.12
    js-yaml: ^4.1.0
    json5: ^2.2.3
    pathe: ^1.0.0
    picocolors: ^1.0.0
    source-map-js: ^1.0.2
    unplugin: ^1.1.0
    vue: ^3.4
  peerDependencies:
    petite-vue-i18n: "*"
    vue: ^3.2.25
    vue-i18n: "*"
  peerDependenciesMeta:
    petite-vue-i18n:
      optional: true
    vue-i18n:
      optional: true
  checksum: a9d25b69d877fc56092fec9ee45b7e59eae044a822d43489088ebb007f62ee1eae7bf0ac3529495243f41840f816f39e4f8091902614f38ccfdd93812d464b7f
  languageName: node
  linkType: hard

"@intlify/vue-i18n-extensions@npm:^7.0.0":
  version: 7.0.0
  resolution: "@intlify/vue-i18n-extensions@npm:7.0.0"
  dependencies:
    "@babel/parser": ^7.24.6
    "@intlify/shared": ^10.0.0
    "@vue/compiler-dom": ^3.2.45
    vue-i18n: ^10.0.0
  peerDependencies:
    "@intlify/shared": ^9.0.0 || ^10.0.0
    "@vue/compiler-dom": ^3.0.0
    vue: ^3.0.0
    vue-i18n: ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    "@intlify/shared":
      optional: true
    "@vue/compiler-dom":
      optional: true
    vue:
      optional: true
    vue-i18n:
      optional: true
  checksum: cf90f1a6325707237c0ebe0874d32dc57b17e009464c6fba190c92bd50347a56cc4133a1489bf4206b52c8de9744161e3e689062ee730180dd54732779891f67
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: ^5.1.2
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: ^7.0.1
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: ^8.1.0
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 4a473b9b32a7d4d3cfb7a614226e555091ff0c5a29a1734c28c72a182c2f6699b26fc6b5c2131dfd841e86b185aea714c72201d7c98c2fba5f17709333a67aeb
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: ^7.0.4
  checksum: 5d36d289960e886484362d9eb6a51d1ea28baed5f5d0140bbe62b99bac52eaf06cc01c2bc0d3575977962f84f6b2c4387b043ee632216643d4787b0999465bf2
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.5
  resolution: "@jridgewell/gen-mapping@npm:0.3.5"
  dependencies:
    "@jridgewell/set-array": ^1.2.1
    "@jridgewell/sourcemap-codec": ^1.4.10
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: ff7a1764ebd76a5e129c8890aa3e2f46045109dabde62b0b6c6a250152227647178ff2069ea234753a690d8f3c4ac8b5e7b267bbee272bffb7f3b0a370ab6e52
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 83b85f72c59d1c080b4cbec0fef84528963a1b5db34e4370fa4bd1e3ff64a0d80e0cee7369d11d73c704e0286fb2865b530acac7a871088fbe92b5edf1000870
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 832e513a85a588f8ed4f27d1279420d8547743cc37fcad5a5a76fc74bb895b013dfe614d0eed9cb860048e6546b798f8f2652020b4b2ba0561b05caa8c654b10
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14, @jridgewell/sourcemap-codec@npm:^1.5.0":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 05df4f2538b3b0f998ea4c1cd34574d0feba216fa5d4ccaef0187d12abf82eafe6021cec8b49f9bb4d90f2ba4582ccc581e72986a5fcf4176ae0cfeb04cf52ec
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": ^3.1.0
    "@jridgewell/sourcemap-codec": ^1.4.14
  checksum: 9d3c40d225e139987b50c48988f8717a54a8c994d8a948ee42e1412e08988761d0754d7d10b803061cc3aebf35f92a5dbbab493bd0e1a9ef9e89a2130e83ba34
  languageName: node
  linkType: hard

"@liff/add-to-home-screen@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/add-to-home-screen@npm:2.25.1"
  dependencies:
    "@liff/consts": 2.25.1
    "@liff/open-window": 2.25.1
    "@liff/types": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 6b317cd5f83c8395509317609e68a953aa45dd810becdee62dba18d246c2297eff6d7d9fcecfabe941602fec06d3d81af94cb19978a2b2c872263b0e0411396f
  languageName: node
  linkType: hard

"@liff/analytics@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/analytics@npm:2.25.1"
  dependencies:
    "@liff/consts": 2.25.1
    "@liff/core": 2.25.1
    "@liff/get-profile": 2.25.1
    "@liff/get-version": 2.25.1
    "@liff/is-logged-in": 2.25.1
    "@liff/logger": 2.25.1
    "@liff/store": 2.25.1
    "@liff/types": 2.25.1
    "@liff/use": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 5ef09592f1a873d03bcb21ad06455d77afa299016aca57ee61aa7657dd33f3bde2810791b90956a7a22fe2db8ed16b6b64ea749b83c97ef4b3b44debcdd55ecc
  languageName: node
  linkType: hard

"@liff/check-availability@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/check-availability@npm:2.25.1"
  dependencies:
    "@liff/get-version": 2.25.1
    "@liff/is-api-available": 2.25.1
    "@liff/types": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 4229b31ac331d7b2b8a4d773000425e524250e435fdb35c4a3a6dfbeb25623e4461a4dfd439af8cf4a42538c90621507e11dc4753fa3ce7dd4cdb6457374ea74
  languageName: node
  linkType: hard

"@liff/close-window@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/close-window@npm:2.25.1"
  dependencies:
    "@liff/get-line-version": 2.25.1
    "@liff/get-os": 2.25.1
    "@liff/native-bridge": 2.25.1
    "@liff/types": 2.25.1
    "@liff/use": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 3b5e998e67c1606cd55898ac835916a21dfec7dbb0e7e4ac297e896585820b8c82f160992ba503a012fdb22d43a64c217bb3f14c3e7891c0f9ba2705b22b43aa
  languageName: node
  linkType: hard

"@liff/consts@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/consts@npm:2.25.1"
  peerDependencies:
    tslib: ^2.3.0
  checksum: 6c9fd3c0e187b19a93484bae2c1c35370156b6a5f49f679a626e104efce9c22d8d23c17e7abd11fab3dcaebeac2d2aa61b1f454958ac7c08c9399314e2552d40
  languageName: node
  linkType: hard

"@liff/core@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/core@npm:2.25.1"
  dependencies:
    "@liff/get-version": 2.25.1
    "@liff/init": 2.25.1
    "@liff/native-bridge": 2.25.1
    "@liff/ready": 2.25.1
    "@liff/store": 2.25.1
    "@liff/use": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 4ee1d69a6a827f9cc247489c05e301993646aebe6accc27d018452b548e253b00e41ba4d4add4088ba0f492def3161f32059f297aa98eb1da86605a42626c0ad
  languageName: node
  linkType: hard

"@liff/create-shortcut-on-home-screen@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/create-shortcut-on-home-screen@npm:2.25.1"
  dependencies:
    "@liff/consts": 2.25.1
    "@liff/get-os": 2.25.1
    "@liff/is-api-available": 2.25.1
    "@liff/is-in-client": 2.25.1
    "@liff/open-window": 2.25.1
    "@liff/permanent-link": 2.25.1
    "@liff/server-api": 2.25.1
    "@liff/store": 2.25.1
    "@liff/use": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 8f66068c7c7f70d195bcf50e721ea638b1f032abcfd898891494650de7822c9283dd0efbcfb93a26220a7ee27d66da30e7ad4843112156a25e66fabbd3b9549d
  languageName: node
  linkType: hard

"@liff/extensions@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/extensions@npm:2.25.1"
  dependencies:
    "@liff/add-to-home-screen": 2.25.1
    "@liff/check-availability": 2.25.1
    "@liff/consts": 2.25.1
    "@liff/get-advertising-id": 2.25.1
    "@liff/get-line-version": 2.25.1
    "@liff/get-os": 2.25.1
    "@liff/logger": 2.25.1
    "@liff/scan-code": 2.25.1
    "@liff/store": 2.25.1
    "@liff/types": 2.25.1
    "@liff/util": 2.25.1
  checksum: db6ee0e83b286715bea77ab0f61932469509068d053ab93b96266e3ea0349ae6267b614be1218d934bebf8bdef7288de6e39ca3ebee9e1aaa4c7e8b50922c7e3
  languageName: node
  linkType: hard

"@liff/get-advertising-id@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/get-advertising-id@npm:2.25.1"
  dependencies:
    "@liff/types": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: e0aa6fa97291570143fc87ae19b8b62f4d1378c56ab7ec8fa2cfd1c87c32d06d56ccd259e9c81e7907a436469641ff318a6e51d579bcf7b3f189314c322e4776
  languageName: node
  linkType: hard

"@liff/get-app-language@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/get-app-language@npm:2.25.1"
  dependencies:
    "@liff/get-line-version": 2.25.1
    "@liff/get-os": 2.25.1
    "@liff/is-in-client": 2.25.1
    "@liff/use": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: df1c55d3efeb1dcde7202efd5cc0f6ffba0850e04c6dc6b79df672bd87dcef3f1c44b06adf08db3b9d6cbd7d2063f7601e99fb2c13c7db9516624ffba4034327
  languageName: node
  linkType: hard

"@liff/get-friendship@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/get-friendship@npm:2.25.1"
  dependencies:
    "@liff/permission": 2.25.1
    "@liff/server-api": 2.25.1
    "@liff/use": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: da6cedb3b1118990e14f15ce3ac7844a0d7b23d15ce984354e546bb7f4d543338382e247e305d3cc7543ee92b8f1b1c2d8d4e7988e4986c5e251aef9e28994f4
  languageName: node
  linkType: hard

"@liff/get-language@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/get-language@npm:2.25.1"
  dependencies:
    "@liff/use": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: afb2e012241c5f02646b3c53d188801e6fc402f193c66cf5b27bff58659d00aed7e44a0fc12bf39b5de387dc60dfbe2dc588890f34b2264eab49eb6ea159753f
  languageName: node
  linkType: hard

"@liff/get-line-version@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/get-line-version@npm:2.25.1"
  dependencies:
    "@liff/use": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 4217ac019d3105d2b4689978067e0b59d9431c9c2507abccbd6a7ebfc647c561043774ae04d29509fc0f9a82d32e0aca0b5a098edcb72c56a00b2611fada09b0
  languageName: node
  linkType: hard

"@liff/get-os@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/get-os@npm:2.25.1"
  dependencies:
    "@liff/use": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: b1f706de2f3ae0ca49732bb7558fe88cc10982901f6e5a251ab37af46b17d3795262a795e9ad3dc94b8ccc861b1901d4d8305e46276f82f0afcb3e74974866a6
  languageName: node
  linkType: hard

"@liff/get-profile@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/get-profile@npm:2.25.1"
  dependencies:
    "@liff/permission": 2.25.1
    "@liff/server-api": 2.25.1
    "@liff/use": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: f89e29f73e94be978410cb7eb965da6b8a56cda71dffed11c997e45cf59a06dfb3cce197c018be69fffe3bea020e9bffe7c2729a1e3e0fa979051eb0ce2eeef6
  languageName: node
  linkType: hard

"@liff/get-version@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/get-version@npm:2.25.1"
  dependencies:
    "@liff/use": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 5c625057114628d5b505110dfd81f02f5132e511cc822fa33310e719376827eaa1102605ac5be78f6d1f9711eb78748e2b182247739e749a79ef0ed0c7f85772
  languageName: node
  linkType: hard

"@liff/hooks@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/hooks@npm:2.25.1"
  peerDependencies:
    tslib: ^2.3.0
  checksum: 71d63bb39dc64be32b50afb440991faae1a36a9d8476f6f2e7a033d8a33914bfc99a51a56f7639afd698ebc657efcad5b269e4b832ade507d63f662f6c7d1009
  languageName: node
  linkType: hard

"@liff/i18n@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/i18n@npm:2.25.1"
  dependencies:
    "@liff/consts": 2.25.1
    "@liff/server-api": 2.25.1
    "@liff/use": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 59aaf6d757608849c1ca7d48fe9b9459b2dd83abe83e74212873afc95dd41bab796149f21274aecb5365439012afbd4e01fb1765055629e78092c5a958419ab3
  languageName: node
  linkType: hard

"@liff/init@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/init@npm:2.25.1"
  dependencies:
    "@liff/check-availability": 2.25.1
    "@liff/close-window": 2.25.1
    "@liff/consts": 2.25.1
    "@liff/extensions": 2.25.1
    "@liff/get-line-version": 2.25.1
    "@liff/get-os": 2.25.1
    "@liff/hooks": 2.25.1
    "@liff/i18n": 2.25.1
    "@liff/is-api-available": 2.25.1
    "@liff/is-in-client": 2.25.1
    "@liff/is-logged-in": 2.25.1
    "@liff/is-sub-window": 2.25.1
    "@liff/logger": 2.25.1
    "@liff/login": 2.25.1
    "@liff/logout": 2.25.1
    "@liff/message-bus": 2.25.1
    "@liff/native-bridge": 2.25.1
    "@liff/ready": 2.25.1
    "@liff/server-api": 2.25.1
    "@liff/store": 2.25.1
    "@liff/sub-window": 2.25.1
    "@liff/types": 2.25.1
    "@liff/use": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: f2639ea54894f147a1057e47bcbdabc17ca6d678c4dcbd9ec3ec7415c47948a69a73fd671baf387048ced5ee18dae92075b8ed55029d622959e4222d346098fa
  languageName: node
  linkType: hard

"@liff/is-api-available@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/is-api-available@npm:2.25.1"
  dependencies:
    "@liff/consts": 2.25.1
    "@liff/get-line-version": 2.25.1
    "@liff/get-os": 2.25.1
    "@liff/is-in-client": 2.25.1
    "@liff/is-logged-in": 2.25.1
    "@liff/is-sub-window": 2.25.1
    "@liff/store": 2.25.1
    "@liff/use": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 6fcd20a27acc3d51de2175057954cb5f1abc24f6aaa2a2dc442e3b4da66a21f463c1f7b4d391cf7528b74ace646025c94e875b5d0739fa5a6bd3529f0d4f6a50
  languageName: node
  linkType: hard

"@liff/is-in-client@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/is-in-client@npm:2.25.1"
  dependencies:
    "@liff/consts": 2.25.1
    "@liff/use": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: d9909d13e9fe04ca52e04e5495145216f01d023e06d7c98f06bf33007e635d617ad342ee6c5b57c147b3f3accebb7fa5670d067bef801662984508c062c5116e
  languageName: node
  linkType: hard

"@liff/is-logged-in@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/is-logged-in@npm:2.25.1"
  dependencies:
    "@liff/store": 2.25.1
    "@liff/use": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: a5b9207729901dea2f895c47458fffbdada74863594390ea6a1925be24dcd35fd4758601070065fc140e90369e525510532a0b9e8f82e51c0eee320cf3b2e50e
  languageName: node
  linkType: hard

"@liff/is-sub-window@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/is-sub-window@npm:2.25.1"
  dependencies:
    "@liff/consts": 2.25.1
    "@liff/is-in-client": 2.25.1
    "@liff/store": 2.25.1
    "@liff/use": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 9703ec7f866d8898eaa449e64eaf594d3270c34a6d794be3e306fb3de85ac426be2c4de07d855536e354e5ce0c147367d76681e92ad1b2181c05e385cc440978
  languageName: node
  linkType: hard

"@liff/liff-types@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/liff-types@npm:2.25.1"
  dependencies:
    "@liff/analytics": 2.25.1
    "@liff/close-window": 2.25.1
    "@liff/create-shortcut-on-home-screen": 2.25.1
    "@liff/get-app-language": 2.25.1
    "@liff/get-friendship": 2.25.1
    "@liff/get-language": 2.25.1
    "@liff/get-line-version": 2.25.1
    "@liff/get-os": 2.25.1
    "@liff/get-profile": 2.25.1
    "@liff/get-version": 2.25.1
    "@liff/i18n": 2.25.1
    "@liff/init": 2.25.1
    "@liff/is-api-available": 2.25.1
    "@liff/is-in-client": 2.25.1
    "@liff/is-logged-in": 2.25.1
    "@liff/is-sub-window": 2.25.1
    "@liff/login": 2.25.1
    "@liff/logout": 2.25.1
    "@liff/native-bridge": 2.25.1
    "@liff/open-window": 2.25.1
    "@liff/permanent-link": 2.25.1
    "@liff/permission": 2.25.1
    "@liff/ready": 2.25.1
    "@liff/scan-code-v2": 2.25.1
    "@liff/send-messages": 2.25.1
    "@liff/share-target-picker": 2.25.1
    "@liff/store": 2.25.1
    "@liff/sub-window": 2.25.1
    "@liff/use": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: c9491c2a6fd66ee1c5c8421b832de65d3028b1c47f28b1896eb4f522fbe51e64eb6c9e8afcac709aed1aa5198013f0e3454adda9989fd4428ac5b068802cffee
  languageName: node
  linkType: hard

"@liff/logger@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/logger@npm:2.25.1"
  peerDependencies:
    tslib: ^2.3.0
  checksum: 46d8210b99b274ec16187c7ec928353f0b36b79f5d60d70388b897f40a08edf2927a065f9ebb34b82d531780ce5529e3629ed49f331a9f5069d35d61a25f07b4
  languageName: node
  linkType: hard

"@liff/login@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/login@npm:2.25.1"
  dependencies:
    "@liff/consts": 2.25.1
    "@liff/get-version": 2.25.1
    "@liff/hooks": 2.25.1
    "@liff/is-in-client": 2.25.1
    "@liff/is-sub-window": 2.25.1
    "@liff/logger": 2.25.1
    "@liff/server-api": 2.25.1
    "@liff/store": 2.25.1
    "@liff/sub-window": 2.25.1
    "@liff/use": 2.25.1
    "@liff/util": 2.25.1
    tiny-sha256: ^1.0.2
  peerDependencies:
    tslib: ^2.3.0
  checksum: baca68062d1ca0a7aeaad4c9c96bf1190598bbcd67484363b9f5c61172a1c63f2cf32fa3f51798a80bd879f99c5cce9cc6d6bb1a88cec5f0aff6686bc1111d68
  languageName: node
  linkType: hard

"@liff/logout@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/logout@npm:2.25.1"
  dependencies:
    "@liff/store": 2.25.1
    "@liff/use": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: ecdb5e2fce7a5769ab471f7abdcd9552bfbcea41e682eeb87a09255d55e8fc49974e1059049533ccb8dad4b504714c9485885c14029c4ae2d585d943685601ea
  languageName: node
  linkType: hard

"@liff/message-bus@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/message-bus@npm:2.25.1"
  dependencies:
    "@liff/consts": 2.25.1
    "@liff/store": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: b257ec0de6bb6bfb01c01ea496953d55fe20d62a33406f270025c1240347f61fe0c3a2d6a04b0b2634ca48a2572a6377c1bca6af99e4e837aa56325da6878f76
  languageName: node
  linkType: hard

"@liff/native-bridge@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/native-bridge@npm:2.25.1"
  dependencies:
    "@liff/consts": 2.25.1
    "@liff/logger": 2.25.1
    "@liff/store": 2.25.1
    "@liff/types": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: cc7cebc79d1773a3414b3112f508a1ace77b93a45128cac89b886f89a31a78c55532273a01014bd3a67b2fa2f8b1d616bb78058f1de9184985de15784eb33ac2
  languageName: node
  linkType: hard

"@liff/open-window@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/open-window@npm:2.25.1"
  dependencies:
    "@liff/consts": 2.25.1
    "@liff/get-line-version": 2.25.1
    "@liff/get-os": 2.25.1
    "@liff/is-in-client": 2.25.1
    "@liff/native-bridge": 2.25.1
    "@liff/types": 2.25.1
    "@liff/use": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 3f0dbe2fabe8c8a881f042bf66076306d8f9501280f4d57d08fb21729120c4bac2192abfb1507f8ed5ca03bc0a769339c051a2e5db67226c5e8d956afa077d34
  languageName: node
  linkType: hard

"@liff/permanent-link@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/permanent-link@npm:2.25.1"
  dependencies:
    "@liff/consts": 2.25.1
    "@liff/server-api": 2.25.1
    "@liff/store": 2.25.1
    "@liff/use": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 6693d6d70b55bc78def48700e2a0fddceeeb9cb2ac070c08c61de0c60c3effa50b751827093d1da0df03a6feb1231ec4e594b69570324627b892d24e4c20ab10
  languageName: node
  linkType: hard

"@liff/permission@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/permission@npm:2.25.1"
  dependencies:
    "@liff/consts": 2.25.1
    "@liff/is-api-available": 2.25.1
    "@liff/is-in-client": 2.25.1
    "@liff/server-api": 2.25.1
    "@liff/store": 2.25.1
    "@liff/sub-window": 2.25.1
    "@liff/use": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 1b94804431a5325ea8f49f9a7f4576974b8c73f4aaefea8bb707da0e81c1da810c38895918ed87947e63a5e8a3e40701ad01a4b43b0ffba3ddcaed01ebc2f58f
  languageName: node
  linkType: hard

"@liff/ready@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/ready@npm:2.25.1"
  peerDependencies:
    tslib: ^2.3.0
  checksum: 1f62317d9361e3af203db161ea61d1785f761588e83b2d09bfcc53257df9564fab74f36172f9adef062775e547c5296b8bb8a4ed8fb00f5bd73fe23fe35dff7c
  languageName: node
  linkType: hard

"@liff/scan-code-v2@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/scan-code-v2@npm:2.25.1"
  dependencies:
    "@liff/consts": 2.25.1
    "@liff/is-api-available": 2.25.1
    "@liff/sub-window": 2.25.1
    "@liff/use": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 56d748625a05e86c00a1af4e9804c6a54877af8fb759bc983832a96fa7c5553a233254babb722e2c3a2b7206882b43fe7c1cd7f27eddd80ab5c2b5680fcfd223
  languageName: node
  linkType: hard

"@liff/scan-code@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/scan-code@npm:2.25.1"
  dependencies:
    "@liff/types": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 94a820b247fc6787d1d8f413d1837a59ddde998072131480b564bb32284e60af14c4e50f82d68dbae6f5caba93813762baf660deb9a0b6438feb501fdd73f211
  languageName: node
  linkType: hard

"@liff/send-messages@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/send-messages@npm:2.25.1"
  dependencies:
    "@liff/consts": 2.25.1
    "@liff/get-line-version": 2.25.1
    "@liff/get-os": 2.25.1
    "@liff/permission": 2.25.1
    "@liff/server-api": 2.25.1
    "@liff/use": 2.25.1
    "@liff/util": 2.25.1
    "@line/bot-sdk": ^7.0.0
  peerDependencies:
    tslib: ^2.3.0
  checksum: 36e7c699c6b793bbbf9715327c1086c1e0dee76dc5a744490c2b4ecfe878360279ce84ab309e7fba9581b884faf4562a9841d039b4bc5c0611c673487c1b4c6b
  languageName: node
  linkType: hard

"@liff/server-api@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/server-api@npm:2.25.1"
  dependencies:
    "@liff/consts": 2.25.1
    "@liff/store": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 146f6246c37c69b9bb179fb7b6e4f9d6b18d0911c25b9eb69f57824467f9e1b9db18a359065b3b31b25c31299a98ad70318be227e03f51beac93f138e9b85628
  languageName: node
  linkType: hard

"@liff/share-target-picker@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/share-target-picker@npm:2.25.1"
  dependencies:
    "@liff/analytics": 2.25.1
    "@liff/consts": 2.25.1
    "@liff/get-line-version": 2.25.1
    "@liff/get-os": 2.25.1
    "@liff/is-api-available": 2.25.1
    "@liff/is-in-client": 2.25.1
    "@liff/is-logged-in": 2.25.1
    "@liff/is-sub-window": 2.25.1
    "@liff/logger": 2.25.1
    "@liff/send-messages": 2.25.1
    "@liff/server-api": 2.25.1
    "@liff/store": 2.25.1
    "@liff/types": 2.25.1
    "@liff/use": 2.25.1
    "@liff/util": 2.25.1
    "@liff/window-postmessage": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: e4dce98d950119b547f42976f015d00bd212d3baf1bc142a52dd876339f7d510f031e379cf0d9ea0d3ac616d93c489cf4e874ef9c6e4dea6945e233a6c6a82f3
  languageName: node
  linkType: hard

"@liff/store@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/store@npm:2.25.1"
  dependencies:
    "@liff/consts": 2.25.1
    "@liff/is-in-client": 2.25.1
    "@liff/types": 2.25.1
    "@liff/use": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 5c36d3901b2cb7997d6237b805501a69377729b4d456b6898251cd737d63630c54b299d5d49bcc01a103f67092f82923089fee621806d113554332db52b80c83
  languageName: node
  linkType: hard

"@liff/sub-window@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/sub-window@npm:2.25.1"
  dependencies:
    "@liff/close-window": 2.25.1
    "@liff/consts": 2.25.1
    "@liff/get-os": 2.25.1
    "@liff/is-api-available": 2.25.1
    "@liff/is-in-client": 2.25.1
    "@liff/is-sub-window": 2.25.1
    "@liff/logger": 2.25.1
    "@liff/message-bus": 2.25.1
    "@liff/server-api": 2.25.1
    "@liff/store": 2.25.1
    "@liff/use": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: ab6a8ff025123843428ce59c307f18d8ad8f2c75bf608cce116812a80d6db84dbde022c1b81323577537b33687a715db39ddc6860d1978e1cd32214251e700cf
  languageName: node
  linkType: hard

"@liff/types@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/types@npm:2.25.1"
  checksum: c718fa87858623ff9f19df8900ad6d44146ee9ae317057b04998f19cb0cac6d62b0ae439214252e99fa419537c59211a4e6511fbf1a1e255261935d92761f053
  languageName: node
  linkType: hard

"@liff/use@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/use@npm:2.25.1"
  dependencies:
    "@liff/hooks": 2.25.1
    "@liff/logger": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: d95a50b8190faefbc393afaa6d2b2b9fcf4a8dfc0dfac7d17f9c66c6d3eb5616da51abb089a2b47523aeef6d324dff565f766fa414d70fb4f2995326ca95c4cb
  languageName: node
  linkType: hard

"@liff/util@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/util@npm:2.25.1"
  dependencies:
    "@liff/consts": 2.25.1
    "@liff/logger": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 617b94b73239709878b27bf03029953200a889565953a000a5ed6fba5f1d793688f735ecc87d5ea2a628a237ee0d177763e6283f9063fa9b54b02af3db0cee71
  languageName: node
  linkType: hard

"@liff/window-postmessage@npm:2.25.1":
  version: 2.25.1
  resolution: "@liff/window-postmessage@npm:2.25.1"
  dependencies:
    "@liff/consts": 2.25.1
    "@liff/logger": 2.25.1
    "@liff/util": 2.25.1
  peerDependencies:
    tslib: ^2.3.0
  checksum: 15f3260ff5e765f82c5159908f01a598011902cc184751804bfe30c3fea023a1bd900a4039df27ecd73297cc9a4b57d59675d7366f613aa7596b004f4d703ccf
  languageName: node
  linkType: hard

"@line/bot-sdk@npm:^7.0.0":
  version: 7.7.0
  resolution: "@line/bot-sdk@npm:7.7.0"
  dependencies:
    "@types/body-parser": ^1.19.2
    "@types/node": ^18.0.0
    axios: ^1.0.0
    body-parser: ^1.20.0
    file-type: ^16.5.4
    form-data: ^4.0.0
  checksum: 4ed1f22cfd26686f55440ae654c7a24c34cc6d5c0b9659bf63423731a0fe2210b3e1057db196bd28132cb5f2654f5cebe3f3f99a68dbd31f04f1849c262da638
  languageName: node
  linkType: hard

"@line/liff@npm:^2.25.1":
  version: 2.25.1
  resolution: "@line/liff@npm:2.25.1"
  dependencies:
    "@liff/analytics": 2.25.1
    "@liff/close-window": 2.25.1
    "@liff/consts": 2.25.1
    "@liff/core": 2.25.1
    "@liff/create-shortcut-on-home-screen": 2.25.1
    "@liff/extensions": 2.25.1
    "@liff/get-app-language": 2.25.1
    "@liff/get-friendship": 2.25.1
    "@liff/get-language": 2.25.1
    "@liff/get-line-version": 2.25.1
    "@liff/get-os": 2.25.1
    "@liff/get-profile": 2.25.1
    "@liff/get-version": 2.25.1
    "@liff/hooks": 2.25.1
    "@liff/i18n": 2.25.1
    "@liff/init": 2.25.1
    "@liff/is-api-available": 2.25.1
    "@liff/is-in-client": 2.25.1
    "@liff/is-logged-in": 2.25.1
    "@liff/is-sub-window": 2.25.1
    "@liff/liff-types": 2.25.1
    "@liff/login": 2.25.1
    "@liff/logout": 2.25.1
    "@liff/native-bridge": 2.25.1
    "@liff/open-window": 2.25.1
    "@liff/permanent-link": 2.25.1
    "@liff/permission": 2.25.1
    "@liff/ready": 2.25.1
    "@liff/scan-code-v2": 2.25.1
    "@liff/send-messages": 2.25.1
    "@liff/server-api": 2.25.1
    "@liff/share-target-picker": 2.25.1
    "@liff/store": 2.25.1
    "@liff/sub-window": 2.25.1
    "@liff/use": 2.25.1
    "@liff/util": 2.25.1
    tslib: ^2.3.0
    whatwg-fetch: ^3.0.0
  checksum: 75592d81fbce35357e62a777653d04fca7b97b8160b948f197f5c924164ad0e522ec2a3cbd4a9e00567656f6bd61607686dcfe1c7e178579e621614075dfa9a6
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": 2.0.5
    run-parallel: ^1.1.9
  checksum: a970d595bd23c66c880e0ef1817791432dbb7acbb8d44b7e7d0e7a22f4521260d4a83f7f9fd61d44fda4610105577f8f58a60718105fb38352baed612fd79e59
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": 2.1.5
    fastq: ^1.6.0
  checksum: 190c643f156d8f8f277bf2a6078af1ffde1fd43f498f187c2db24d35b4b4b5785c02c7dc52e356497b9a1b65b13edc996de08de0b961c32844364da02986dc53
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: ^7.1.0
    http-proxy-agent: ^7.0.0
    https-proxy-agent: ^7.0.1
    lru-cache: ^10.0.1
    socks-proxy-agent: ^8.0.3
  checksum: e8fc25d536250ed3e669813b36e8c6d805628b472353c57afd8c4fde0fcfcf3dda4ffe22f7af8c9070812ec2e7a03fb41d7151547cef3508efe661a5a3add20f
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: ^7.3.5
  checksum: 68951c589e9a4328698a35fd82fe71909a257d6f2ede0434d236fa55634f0fbcad9bb8755553ce5849bd25ee6f019f4d435921ac715c853582c4a7f5983c8d4a
  languageName: node
  linkType: hard

"@one-ini/wasm@npm:0.1.1":
  version: 0.1.1
  resolution: "@one-ini/wasm@npm:0.1.1"
  checksum: 11de17108eae57c797e552e36b259398aede999b4a689d78be6459652edc37f3428472410590a9d328011a8751b771063a5648dd5c4205631c55d1d58e313156
  languageName: node
  linkType: hard

"@oss/liff-app@workspace:.":
  version: 0.0.0-use.local
  resolution: "@oss/liff-app@workspace:."
  dependencies:
    "@antfu/eslint-config": ^3.11.2
    "@iconify/json": ^2.2.281
    "@intlify/unplugin-vue-i18n": ^6.0.1
    "@line/liff": ^2.25.1
    "@quasar/extras": ^1.16.15
    "@quasar/vite-plugin": ^1.8.1
    "@types/deep-equal": ^1.0.4
    "@types/file-saver": ^2.0.7
    "@types/google.maps": ^3.58.1
    "@types/node": ^22.10.2
    "@unhead/addons": ^1.11.14
    "@unhead/vue": ^1.11.14
    "@unocss/eslint-plugin": ^0.65.1
    "@unocss/reset": ^0.65.1
    "@vitejs/plugin-basic-ssl": ^1.2.0
    "@vitejs/plugin-vue": ^5.2.1
    "@vue/test-utils": ^2.4.6
    "@vueuse/core": ^12.0.0
    "@vueuse/router": ^12.0.0
    dayjs: ^1.11.13
    deep-equal: ^2.2.3
    destr: ^2.0.3
    eslint: ^9.16.0
    eslint-plugin-format: ^0.1.3
    file-saver: ^2.0.5
    fuse.js: ^7.0.0
    jsdom: ^25.0.1
    ky: ^1.7.3
    lint-staged: ^15.2.11
    pinia: ^2.3.0
    quasar: ^2.17.4
    reconnecting-websocket: ^4.4.0
    sass-embedded: 1.80.2
    taze: ^0.18.0
    typescript: ^5.7.2
    unocss: ^0.65.1
    unplugin-auto-import: ^0.18.6
    unplugin-icons: ^0.21.0
    unplugin-vue-components: ^0.27.5
    unplugin-vue-router: ^0.10.9
    vite: ^5.4.11
    vite-plugin-externals: ^0.6.2
    vitest: ^2.1.8
    vue: ^3.5.13
    vue-advanced-chat: ^2.1.0
    vue-router: ^4.5.0
    vue-tsc: ^2.1.10
    vue3-google-map: ^0.21.0
  languageName: unknown
  linkType: soft

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 6ad6a00fc4f2f2cfc6bff76fb1d88b8ee20bc0601e18ebb01b6d4be583733a860239a521a7fbca73b612e66705078809483549d2b18f370eb346c5155c8e4a0f
  languageName: node
  linkType: hard

"@pkgr/core@npm:^0.1.0":
  version: 0.1.1
  resolution: "@pkgr/core@npm:0.1.1"
  checksum: 6f25fd2e3008f259c77207ac9915b02f1628420403b2630c92a07ff963129238c9262afc9e84344c7a23b5cc1f3965e2cd17e3798219f5fd78a63d144d3cceba
  languageName: node
  linkType: hard

"@polka/url@npm:^1.0.0-next.24":
  version: 1.0.0-next.28
  resolution: "@polka/url@npm:1.0.0-next.28"
  checksum: 7402aaf1de781d0eb0870d50cbcd394f949aee11b38a267a5c3b4e3cfee117e920693e6e93ce24c87ae2d477a59634f39d9edde8e86471cae756839b07c79af7
  languageName: node
  linkType: hard

"@quasar/extras@npm:^1.16.15":
  version: 1.16.15
  resolution: "@quasar/extras@npm:1.16.15"
  checksum: 067324243dcf73deefff115e6017e3e46a84c490c8f9d7b565e2f6308b0452f93c44b2a525c0ddeeb4085a851e17c1b4a03e848b46df960aee10befdb391737a
  languageName: node
  linkType: hard

"@quasar/vite-plugin@npm:^1.8.1":
  version: 1.8.1
  resolution: "@quasar/vite-plugin@npm:1.8.1"
  peerDependencies:
    "@vitejs/plugin-vue": ^2.0.0 || ^3.0.0 || ^4.0.0 || ^5.0.0
    quasar: ^2.16.0
    vite: ^2.0.0 || ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0-beta.9
    vue: ^3.0.0
  checksum: e6d36fe7b0262b58dcc4340c37c15979ab6895ae91bf1e82831e3846beebe7bb00248a9b3842e5724c40738e616386dda1262d3634b1428fe30522f64c055383
  languageName: node
  linkType: hard

"@rollup/pluginutils@npm:^5.1.0, @rollup/pluginutils@npm:^5.1.2, @rollup/pluginutils@npm:^5.1.3":
  version: 5.1.3
  resolution: "@rollup/pluginutils@npm:5.1.3"
  dependencies:
    "@types/estree": ^1.0.0
    estree-walker: ^2.0.2
    picomatch: ^4.0.2
  peerDependencies:
    rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
  peerDependenciesMeta:
    rollup:
      optional: true
  checksum: a6e9bac8ae94da39679dae390b53b43fe7a218f8fa2bfecf86e59be4da4ba02ac004f166daf55f03506e49108399394f13edeb62cce090f8cfc967b29f4738bf
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm-eabi@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-android-arm-eabi@npm:4.28.1"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm64@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-android-arm64@npm:4.28.1"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-arm64@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-darwin-arm64@npm:4.28.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-x64@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-darwin-x64@npm:4.28.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-arm64@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-freebsd-arm64@npm:4.28.1"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-x64@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-freebsd-x64@npm:4.28.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-gnueabihf@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-arm-gnueabihf@npm:4.28.1"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-musleabihf@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-arm-musleabihf@npm:4.28.1"
  conditions: os=linux & cpu=arm & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-gnu@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-arm64-gnu@npm:4.28.1"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-musl@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-arm64-musl@npm:4.28.1"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-loongarch64-gnu@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-loongarch64-gnu@npm:4.28.1"
  conditions: os=linux & cpu=loong64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-powerpc64le-gnu@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-powerpc64le-gnu@npm:4.28.1"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-gnu@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-riscv64-gnu@npm:4.28.1"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-s390x-gnu@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-s390x-gnu@npm:4.28.1"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-gnu@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-x64-gnu@npm:4.28.1"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-musl@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-linux-x64-musl@npm:4.28.1"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-win32-arm64-msvc@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-win32-arm64-msvc@npm:4.28.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-win32-ia32-msvc@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-win32-ia32-msvc@npm:4.28.1"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@rollup/rollup-win32-x64-msvc@npm:4.28.1":
  version: 4.28.1
  resolution: "@rollup/rollup-win32-x64-msvc@npm:4.28.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@stylistic/eslint-plugin@npm:^2.11.0":
  version: 2.12.0
  resolution: "@stylistic/eslint-plugin@npm:2.12.0"
  dependencies:
    "@typescript-eslint/utils": ^8.13.0
    eslint-visitor-keys: ^4.2.0
    espree: ^10.3.0
    estraverse: ^5.3.0
    picomatch: ^4.0.2
  peerDependencies:
    eslint: ">=8.40.0"
  checksum: a2f2299175794d7b993070974447598831e57a3383ebb1c9713d467a2c8762c339041b17007a685302d68c05a01869f6a4546f4c0b5a6ca25dd9748228482642
  languageName: node
  linkType: hard

"@tokenizer/token@npm:^0.3.0":
  version: 0.3.0
  resolution: "@tokenizer/token@npm:0.3.0"
  checksum: 1d575d02d2a9f0c5a4ca5180635ebd2ad59e0f18b42a65f3d04844148b49b3db35cf00b6012a1af2d59c2ab3caca59451c5689f747ba8667ee586ad717ee58e1
  languageName: node
  linkType: hard

"@types/body-parser@npm:^1.19.2":
  version: 1.19.5
  resolution: "@types/body-parser@npm:1.19.5"
  dependencies:
    "@types/connect": "*"
    "@types/node": "*"
  checksum: 1e251118c4b2f61029cc43b0dc028495f2d1957fe8ee49a707fb940f86a9bd2f9754230805598278fe99958b49e9b7e66eec8ef6a50ab5c1f6b93e1ba2aaba82
  languageName: node
  linkType: hard

"@types/connect@npm:*":
  version: 3.4.38
  resolution: "@types/connect@npm:3.4.38"
  dependencies:
    "@types/node": "*"
  checksum: 7eb1bc5342a9604facd57598a6c62621e244822442976c443efb84ff745246b10d06e8b309b6e80130026a396f19bf6793b7cecd7380169f369dac3bfc46fb99
  languageName: node
  linkType: hard

"@types/debug@npm:^4.0.0":
  version: 4.1.12
  resolution: "@types/debug@npm:4.1.12"
  dependencies:
    "@types/ms": "*"
  checksum: 47876a852de8240bfdaf7481357af2b88cb660d30c72e73789abf00c499d6bc7cd5e52f41c915d1b9cd8ec9fef5b05688d7b7aef17f7f272c2d04679508d1053
  languageName: node
  linkType: hard

"@types/deep-equal@npm:^1.0.4":
  version: 1.0.4
  resolution: "@types/deep-equal@npm:1.0.4"
  checksum: db905bcb051bc9cf5dceb231a6b0b64ab2318073939a1769e043454e8c46b1eeaac1376338751d2ee7ca9294398dd62e03d4b120361b56f96f89dc87aec753e4
  languageName: node
  linkType: hard

"@types/estree@npm:1.0.6, @types/estree@npm:^1.0.0, @types/estree@npm:^1.0.6":
  version: 1.0.6
  resolution: "@types/estree@npm:1.0.6"
  checksum: 8825d6e729e16445d9a1dd2fb1db2edc5ed400799064cd4d028150701031af012ba30d6d03fe9df40f4d7a437d0de6d2b256020152b7b09bde9f2e420afdffd9
  languageName: node
  linkType: hard

"@types/file-saver@npm:^2.0.7":
  version: 2.0.7
  resolution: "@types/file-saver@npm:2.0.7"
  checksum: c3d1cd80eab1214767922cabac97681f3fb688e82b74890450d70deaca49537949bbc96d80d363d91e8f0a4752c7164909cc8902d9721c5c4809baafc42a3801
  languageName: node
  linkType: hard

"@types/google.maps@npm:^3.58.1":
  version: 3.58.1
  resolution: "@types/google.maps@npm:3.58.1"
  checksum: 7ad5bd9566ffa0396485c432368e45c43e3fe1ecc2b89324f257a49d9abbe03dfe046a771d82ae1808fa0fb6e04e6ffca870c7f2295fef73a6015a678b067364
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.15":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 97ed0cb44d4070aecea772b7b2e2ed971e10c81ec87dd4ecc160322ffa55ff330dace1793489540e3e318d90942064bb697cc0f8989391797792d919737b3b98
  languageName: node
  linkType: hard

"@types/mdast@npm:^4.0.0":
  version: 4.0.4
  resolution: "@types/mdast@npm:4.0.4"
  dependencies:
    "@types/unist": "*"
  checksum: 20c4e9574cc409db662a35cba52b068b91eb696b3049e94321219d47d34c8ccc99a142be5c76c80a538b612457b03586bc2f6b727a3e9e7530f4c8568f6282ee
  languageName: node
  linkType: hard

"@types/ms@npm:*":
  version: 0.7.34
  resolution: "@types/ms@npm:0.7.34"
  checksum: f38d36e7b6edecd9badc9cf50474159e9da5fa6965a75186cceaf883278611b9df6669dc3a3cc122b7938d317b68a9e3d573d316fcb35d1be47ec9e468c6bd8a
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 22.10.1
  resolution: "@types/node@npm:22.10.1"
  dependencies:
    undici-types: ~6.20.0
  checksum: 5a9b81500f288a8fb757b61bd939f99f72b6cb59347a5bae52dd1c2c87100ebbaa9da4256ef3cb9add2090e8704cda1d9a1ffc14ccd5db47a6466c8bae10ebcb
  languageName: node
  linkType: hard

"@types/node@npm:^18.0.0":
  version: 18.19.67
  resolution: "@types/node@npm:18.19.67"
  dependencies:
    undici-types: ~5.26.4
  checksum: 700f92c6a0b63352ce6327286392adab30bb17623c2a788811e9cf092c4dc2fb5e36ca4727247a981b3f44185fdceef20950a3b7a8ab72721e514ac037022a08
  languageName: node
  linkType: hard

"@types/node@npm:^22.10.2":
  version: 22.10.2
  resolution: "@types/node@npm:22.10.2"
  dependencies:
    undici-types: ~6.20.0
  checksum: b22401e6e7d1484e437d802c72f5560e18100b1257b9ad0574d6fe05bebe4dbcb620ea68627d1f1406775070d29ace8b6b51f57e7b1c7b8bafafe6da7f29c843
  languageName: node
  linkType: hard

"@types/normalize-package-data@npm:^2.4.0":
  version: 2.4.4
  resolution: "@types/normalize-package-data@npm:2.4.4"
  checksum: 65dff72b543997b7be8b0265eca7ace0e34b75c3e5fee31de11179d08fa7124a7a5587265d53d0409532ecb7f7fba662c2012807963e1f9b059653ec2c83ee05
  languageName: node
  linkType: hard

"@types/unist@npm:*, @types/unist@npm:^3.0.0":
  version: 3.0.3
  resolution: "@types/unist@npm:3.0.3"
  checksum: 96e6453da9e075aaef1dc22482463898198acdc1eeb99b465e65e34303e2ec1e3b1ed4469a9118275ec284dc98019f63c3f5d49422f0e4ac707e5ab90fb3b71a
  languageName: node
  linkType: hard

"@types/web-bluetooth@npm:^0.0.20":
  version: 0.0.20
  resolution: "@types/web-bluetooth@npm:0.0.20"
  checksum: d6d61da683e876e8995ac57e2e5229d829d0f536deb3568d4430898fc626ebcb7e065fe7f655ac6a5205702f7f7049e6335abe689cd5291241eef6e39e8a4371
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:^8.16.0":
  version: 8.18.0
  resolution: "@typescript-eslint/eslint-plugin@npm:8.18.0"
  dependencies:
    "@eslint-community/regexpp": ^4.10.0
    "@typescript-eslint/scope-manager": 8.18.0
    "@typescript-eslint/type-utils": 8.18.0
    "@typescript-eslint/utils": 8.18.0
    "@typescript-eslint/visitor-keys": 8.18.0
    graphemer: ^1.4.0
    ignore: ^5.3.1
    natural-compare: ^1.4.0
    ts-api-utils: ^1.3.0
  peerDependencies:
    "@typescript-eslint/parser": ^8.0.0 || ^8.0.0-alpha.0
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.8.0"
  checksum: 0d40e5426a233ddbe0cf517e1fb7a78b231882f676542ff50ae949b8301c20cffdcacd2daf05e893e119d361642625b777883ce26145ea5f3df2177569a51379
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:^8.16.0":
  version: 8.18.0
  resolution: "@typescript-eslint/parser@npm:8.18.0"
  dependencies:
    "@typescript-eslint/scope-manager": 8.18.0
    "@typescript-eslint/types": 8.18.0
    "@typescript-eslint/typescript-estree": 8.18.0
    "@typescript-eslint/visitor-keys": 8.18.0
    debug: ^4.3.4
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.8.0"
  checksum: 8d95c49440001436dfdbcd64f7fe845ff05777aa8e314c91b3fdb7d8dfb91a42b3bf62b0be16967845d1a1ef70d25aa9fc29ff79b7a0d6474ea121a9fac1f5c0
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:8.18.0, @typescript-eslint/scope-manager@npm:^8.1.0, @typescript-eslint/scope-manager@npm:^8.13.0":
  version: 8.18.0
  resolution: "@typescript-eslint/scope-manager@npm:8.18.0"
  dependencies:
    "@typescript-eslint/types": 8.18.0
    "@typescript-eslint/visitor-keys": 8.18.0
  checksum: d01f36ca17a2ffa9873851bf823942d254ab826ef3581d9104c1eee944a3e6fcebec60f521bfb65a6ee11efc11acdf2469706a4371bed9fec893009802b5cb45
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:8.18.0":
  version: 8.18.0
  resolution: "@typescript-eslint/type-utils@npm:8.18.0"
  dependencies:
    "@typescript-eslint/typescript-estree": 8.18.0
    "@typescript-eslint/utils": 8.18.0
    debug: ^4.3.4
    ts-api-utils: ^1.3.0
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.8.0"
  checksum: 60456e3cfb8cb49236bca886e0b94a3568c2ce0b1a370d71b071479f43b209489ecc959f21a7d55a0f6ec9afefdb3a7a2abdba2fd44969e1ddf28a99c88bb60a
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:8.18.0, @typescript-eslint/types@npm:^8.17.0":
  version: 8.18.0
  resolution: "@typescript-eslint/types@npm:8.18.0"
  checksum: fec2dbb356608d7538868c58b0de71851b7b2cea4ebb752cd4acdd217e0d54d19d6230344e9867559ea67dd6655fde6f2460be23f206aea487cc295c28eb6191
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:8.18.0, @typescript-eslint/typescript-estree@npm:^8.13.0":
  version: 8.18.0
  resolution: "@typescript-eslint/typescript-estree@npm:8.18.0"
  dependencies:
    "@typescript-eslint/types": 8.18.0
    "@typescript-eslint/visitor-keys": 8.18.0
    debug: ^4.3.4
    fast-glob: ^3.3.2
    is-glob: ^4.0.3
    minimatch: ^9.0.4
    semver: ^7.6.0
    ts-api-utils: ^1.3.0
  peerDependencies:
    typescript: ">=4.8.4 <5.8.0"
  checksum: 2b04a9eb1d942ee26358f411ed6df26b36366ec93d6e3d1ab94f27915c23531e01edb94456ae1d47086e7180dc94d0027035ab08d377469fe01ffa621bfaf96f
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:8.18.0, @typescript-eslint/utils@npm:^8.1.0, @typescript-eslint/utils@npm:^8.13.0, @typescript-eslint/utils@npm:^8.17.0":
  version: 8.18.0
  resolution: "@typescript-eslint/utils@npm:8.18.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.4.0
    "@typescript-eslint/scope-manager": 8.18.0
    "@typescript-eslint/types": 8.18.0
    "@typescript-eslint/typescript-estree": 8.18.0
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.8.0"
  checksum: 8da7419ae53944a3efc99e33df8fa651303ff736338ed101eae0f64fe53661ad947784ff769ca8589c9803a099dd6d43e891fbedec5212a2b2ea031f0218eb56
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:8.18.0":
  version: 8.18.0
  resolution: "@typescript-eslint/visitor-keys@npm:8.18.0"
  dependencies:
    "@typescript-eslint/types": 8.18.0
    eslint-visitor-keys: ^4.2.0
  checksum: bf4c45bb3bdfd2bc4df86bc50649e8a9734d294a80fb9a78b52cc8ed247384f9d525fb0693372fd52864175fd7036069c5f59b920c12f0ee34d52c2ab0332841
  languageName: node
  linkType: hard

"@unhead/addons@npm:^1.11.14":
  version: 1.11.14
  resolution: "@unhead/addons@npm:1.11.14"
  dependencies:
    "@rollup/pluginutils": ^5.1.3
    "@unhead/schema": 1.11.14
    "@unhead/shared": 1.11.14
    magic-string: ^0.30.14
    mlly: ^1.7.3
    ufo: ^1.5.4
    unplugin: ^1.16.0
    unplugin-ast: ^0.12.0
  checksum: 4d313036657ebe7b27f1302161d6db477d2406670053d555072a57cc242148ebc1f9e61def31fd99925325cf3bb4e9b02cf80c76aa8b92f1a53ef8bf168c2919
  languageName: node
  linkType: hard

"@unhead/dom@npm:1.11.14":
  version: 1.11.14
  resolution: "@unhead/dom@npm:1.11.14"
  dependencies:
    "@unhead/schema": 1.11.14
    "@unhead/shared": 1.11.14
  checksum: ce45bc66e52774f4d739fa5693760ad6cd7c6f708fe47a32357df593d986b251a6840d9bea77f9d880968396bb2aead5f85231ae4fa28108aee80a4cb80adc61
  languageName: node
  linkType: hard

"@unhead/schema@npm:1.11.14":
  version: 1.11.14
  resolution: "@unhead/schema@npm:1.11.14"
  dependencies:
    hookable: ^5.5.3
    zhead: ^2.2.4
  checksum: 344585e54341782ae84af7216e1cfe24159f57972db8c6e01d33f354b9e42752186e71aea87a9ba83dbb81be7443a890568e6978399a39ecb254620d4c5084ab
  languageName: node
  linkType: hard

"@unhead/shared@npm:1.11.14":
  version: 1.11.14
  resolution: "@unhead/shared@npm:1.11.14"
  dependencies:
    "@unhead/schema": 1.11.14
  checksum: c39a3b6072970d9db0dacd989f91f078ef9dc42eada11ef775e22e7086484a1254e87be43e299a4898f8d08ba0d9f6d194b51bb89e3501de0f878c986f389613
  languageName: node
  linkType: hard

"@unhead/vue@npm:^1.11.14":
  version: 1.11.14
  resolution: "@unhead/vue@npm:1.11.14"
  dependencies:
    "@unhead/schema": 1.11.14
    "@unhead/shared": 1.11.14
    defu: ^6.1.4
    hookable: ^5.5.3
    unhead: 1.11.14
  peerDependencies:
    vue: ">=2.7 || >=3"
  checksum: f2c83cb95d05b1ade31cfe998e6c7ba25151cd782ca942bbab861c495227a80715b2289153a779358bf85ad6ec80636f90e94942a216c79eab7b06c4ea164ed4
  languageName: node
  linkType: hard

"@unocss/astro@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/astro@npm:0.65.1"
  dependencies:
    "@unocss/core": 0.65.1
    "@unocss/reset": 0.65.1
    "@unocss/vite": 0.65.1
  peerDependencies:
    vite: ^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0
  peerDependenciesMeta:
    vite:
      optional: true
  checksum: bac6718b4066a519cdfe1e91b1cfca5607dece6e1af3bf1b068d9ef05f9168c2156572adeae2127445000398941ca36493a27252eeddafaed6bcae49e27c9a3d
  languageName: node
  linkType: hard

"@unocss/cli@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/cli@npm:0.65.1"
  dependencies:
    "@ampproject/remapping": ^2.3.0
    "@rollup/pluginutils": ^5.1.3
    "@unocss/config": 0.65.1
    "@unocss/core": 0.65.1
    "@unocss/preset-uno": 0.65.1
    cac: ^6.7.14
    chokidar: ^3.6.0
    colorette: ^2.0.20
    consola: ^3.2.3
    magic-string: ^0.30.14
    pathe: ^1.1.2
    perfect-debounce: ^1.0.0
    tinyglobby: ^0.2.10
  bin:
    unocss: bin/unocss.mjs
  checksum: d1b36613b1245af8a546bbb4cc981b9c8dab39fd4d4106b5ee35decc1c99f9c7e59d038e467d3817d326588c32dd1eca46181d66478b0b5309637a874cc31d67
  languageName: node
  linkType: hard

"@unocss/config@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/config@npm:0.65.1"
  dependencies:
    "@unocss/core": 0.65.1
    unconfig: ~0.5.5
  checksum: 93b38907b19805d695f0baf49388863ac953fc429fb6fa64ccac7aabbc117e70a6b0231aea361dc00ad594fc18c81835d4e1b5d69d2555c22f0e10b3dd3a9f7d
  languageName: node
  linkType: hard

"@unocss/core@npm:0.65.1, @unocss/core@npm:^0.65.1":
  version: 0.65.1
  resolution: "@unocss/core@npm:0.65.1"
  checksum: ee72322b8bd0b5108983562d7b810268662848155797430fb28c903320704ff985182926c7d89d87f7f55c9491bd205eb8303da4229db32799537ae2515a4c06
  languageName: node
  linkType: hard

"@unocss/eslint-plugin@npm:^0.65.1":
  version: 0.65.1
  resolution: "@unocss/eslint-plugin@npm:0.65.1"
  dependencies:
    "@typescript-eslint/utils": ^8.17.0
    "@unocss/config": 0.65.1
    "@unocss/core": 0.65.1
    magic-string: ^0.30.14
    synckit: ^0.9.2
  checksum: dfd9f8bb844616b5ada5bce7c6ab6356e2d2c84a7a27fef4e52d16c8e6568679fec156d2e8a0c04470a6d04b2938c82ab184e1832a48d755397b8c1375a27195
  languageName: node
  linkType: hard

"@unocss/extractor-arbitrary-variants@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/extractor-arbitrary-variants@npm:0.65.1"
  dependencies:
    "@unocss/core": 0.65.1
  checksum: 5d822d5621e594acb60462cb341f060784ee4cf63e2a1397ec9b0d186a2bde304f315ef0d6fbac5a73a04e06c5bc36686beb31bd0adf4f7eb55e9c3dd9aececd
  languageName: node
  linkType: hard

"@unocss/inspector@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/inspector@npm:0.65.1"
  dependencies:
    "@unocss/core": 0.65.1
    "@unocss/rule-utils": 0.65.1
    gzip-size: ^6.0.0
    sirv: ^2.0.4
    vue-flow-layout: ^0.1.1
  checksum: f896036e5cc0c628b06b6005b77ef7d5304ca3040ccb819e5af6727919afecf27f99d757f78bacb1ae170252efcd50f509541211ca41ae59a1b88d6d0a7baafc
  languageName: node
  linkType: hard

"@unocss/postcss@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/postcss@npm:0.65.1"
  dependencies:
    "@unocss/config": 0.65.1
    "@unocss/core": 0.65.1
    "@unocss/rule-utils": 0.65.1
    css-tree: ^3.0.1
    postcss: ^8.4.49
    tinyglobby: ^0.2.10
  peerDependencies:
    postcss: ^8.4.21
  checksum: fe11ec95c6e68f6be8e5a5f0129479315baffc9af439ea2cfb454d42a593bca5e42af6ab118f00538ea90b222eebe7996657ae1739f35f9f4ba61f76977c8d5d
  languageName: node
  linkType: hard

"@unocss/preset-attributify@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/preset-attributify@npm:0.65.1"
  dependencies:
    "@unocss/core": 0.65.1
  checksum: 60660b64113548f944d23f6756dbd39994c65bc4a076e27856b1d19a9f88b3814e7f0478eadeb0d9b041210fcb629f6b71f17a6e42c5bd39e309487213831c1c
  languageName: node
  linkType: hard

"@unocss/preset-icons@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/preset-icons@npm:0.65.1"
  dependencies:
    "@iconify/utils": ^2.1.33
    "@unocss/core": 0.65.1
    ofetch: ^1.4.1
  checksum: dbf852d396ed424f153367583223c3fb61cf45eb74297e2d249d29870dd50ea9ae08266092870969c6a598e9bfd543638775fcfb33203844d3575159b1682716
  languageName: node
  linkType: hard

"@unocss/preset-mini@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/preset-mini@npm:0.65.1"
  dependencies:
    "@unocss/core": 0.65.1
    "@unocss/extractor-arbitrary-variants": 0.65.1
    "@unocss/rule-utils": 0.65.1
  checksum: 5b43724d2ef629d6af443ee29c0589c173fb145e199eb8241da1ee8c52a72099cdf7a25706e90f8dc39642a92607c94b23eab1e28db8041408f6f081825cb13d
  languageName: node
  linkType: hard

"@unocss/preset-tagify@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/preset-tagify@npm:0.65.1"
  dependencies:
    "@unocss/core": 0.65.1
  checksum: 3933fa2c7b3ce38b7dbcfc3de4956d7fa2fcfa279054b6dad1729c870cf6ff5771d241f6b94656b34457cb64c55eb89958b46695858f0614ce4aeb8de5d01be1
  languageName: node
  linkType: hard

"@unocss/preset-typography@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/preset-typography@npm:0.65.1"
  dependencies:
    "@unocss/core": 0.65.1
    "@unocss/preset-mini": 0.65.1
  checksum: 1a714548e5cb3435bf499948ec3a4cece796eb72d1ecea7d67215a1c2cfb09fc60649544b585f5cc0b63a507ae6096979d33ef4cc1c18472850a6043aa62a6aa
  languageName: node
  linkType: hard

"@unocss/preset-uno@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/preset-uno@npm:0.65.1"
  dependencies:
    "@unocss/core": 0.65.1
    "@unocss/preset-mini": 0.65.1
    "@unocss/preset-wind": 0.65.1
    "@unocss/rule-utils": 0.65.1
  checksum: da9b6367bbdc371c3da70a9127b3b8e8e590ec3693a0665089bdb0427e79a2180ad9767dad49ef1b5a26043585ae40e4e33788aa0fbc5d7aab15dddbcb77fd46
  languageName: node
  linkType: hard

"@unocss/preset-web-fonts@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/preset-web-fonts@npm:0.65.1"
  dependencies:
    "@unocss/core": 0.65.1
    ofetch: ^1.4.1
  checksum: f737ab83aa230374e253be6c224cf56b965f3ed6db559fafc180bf1129405b09c577f844fb84ec755f94876dc2d4d19e3fc9b5d0c1b43ac3a6a6dfa4f2d0bccc
  languageName: node
  linkType: hard

"@unocss/preset-wind@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/preset-wind@npm:0.65.1"
  dependencies:
    "@unocss/core": 0.65.1
    "@unocss/preset-mini": 0.65.1
    "@unocss/rule-utils": 0.65.1
  checksum: da2535a2d01098881ce142f14dd59166e1c090cf75cea6b2ff9d0ee003a3cd5475bf810da93a2cc24adb44b1bc3c3c108624b62734b2b306844dbaa9e6ead418
  languageName: node
  linkType: hard

"@unocss/reset@npm:0.65.1, @unocss/reset@npm:^0.65.1":
  version: 0.65.1
  resolution: "@unocss/reset@npm:0.65.1"
  checksum: 8407e71f60b74be08ece72b7afb1226263e72ad49337740f531a2901cc48c2240f2d20aee6de8a43a670599499bbf83587e72c67e4a21073dc1487f7d52c095b
  languageName: node
  linkType: hard

"@unocss/rule-utils@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/rule-utils@npm:0.65.1"
  dependencies:
    "@unocss/core": ^0.65.1
    magic-string: ^0.30.14
  checksum: cce2df2ff4cd56ef97f920bd6b878fb8e41794fead6091097920c133b5df8ed952cefd20e3041d01213888badec7b50e5aba8ebb371b5ec685b840156e70d76f
  languageName: node
  linkType: hard

"@unocss/transformer-attributify-jsx@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/transformer-attributify-jsx@npm:0.65.1"
  dependencies:
    "@unocss/core": 0.65.1
  checksum: f2030ac71b29e306466a40c1387821c3a6963a4ce9b9ba572b604f91284f38a60e29cb889003f75f889d9dd5cec6ac3b5c01fef27ce9a446981f51c3a3c011d3
  languageName: node
  linkType: hard

"@unocss/transformer-compile-class@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/transformer-compile-class@npm:0.65.1"
  dependencies:
    "@unocss/core": 0.65.1
  checksum: 1d7d419f7da92f7b8f3663403ec9d3a339ad8e0eb33e71712c501d16c814b35b60619fde874db40aef28823e0c4b1797808675e1ee037fe12d4cc15a9d228f7a
  languageName: node
  linkType: hard

"@unocss/transformer-directives@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/transformer-directives@npm:0.65.1"
  dependencies:
    "@unocss/core": 0.65.1
    "@unocss/rule-utils": 0.65.1
    css-tree: ^3.0.1
  checksum: ad137a59a2ae3092a87ff3596f6aa361a0acbe9e8fa6bc4df01129e1abd556b5111806ec0576c47615bd36cc56250ffc1c9b007f463ad8dcf9dd7d53b02f44ca
  languageName: node
  linkType: hard

"@unocss/transformer-variant-group@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/transformer-variant-group@npm:0.65.1"
  dependencies:
    "@unocss/core": 0.65.1
  checksum: 806e3ef0150a07c9b2a4ad84dd3f9b926e5cf3fbae4bf1859380fb1d01fc4756437733088bf25b11a5cb3b96855b7f9d2df26fdc942b8ca821fec2dec17b2a35
  languageName: node
  linkType: hard

"@unocss/vite@npm:0.65.1":
  version: 0.65.1
  resolution: "@unocss/vite@npm:0.65.1"
  dependencies:
    "@ampproject/remapping": ^2.3.0
    "@rollup/pluginutils": ^5.1.3
    "@unocss/config": 0.65.1
    "@unocss/core": 0.65.1
    "@unocss/inspector": 0.65.1
    chokidar: ^3.6.0
    magic-string: ^0.30.14
    tinyglobby: ^0.2.10
  peerDependencies:
    vite: ^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0
  checksum: e4c222541fa0e91fd29bc9ea90ac7017e64d0940df8340080717cd71a2925d67bfd0c429ce7dcb9ce092eb3225899eeb737a43f5fc4b1dd91fde1c5edd8dffeb
  languageName: node
  linkType: hard

"@vitejs/plugin-basic-ssl@npm:^1.2.0":
  version: 1.2.0
  resolution: "@vitejs/plugin-basic-ssl@npm:1.2.0"
  peerDependencies:
    vite: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0
  checksum: 0a2d1fb8147783238a8308a3736a7d4b38026bc4223220701859bf05564ab91a35ff6cfddf75527a60611756a89ef60f3687f644e87f01eb2275cf0b887033f7
  languageName: node
  linkType: hard

"@vitejs/plugin-vue@npm:^5.2.1":
  version: 5.2.1
  resolution: "@vitejs/plugin-vue@npm:5.2.1"
  peerDependencies:
    vite: ^5.0.0 || ^6.0.0
    vue: ^3.2.25
  checksum: 4f3add595b9951d1e5b458765fa862647252a3c36a0fdc2b0704ef1d6ab4a447a63c5f646e1a9218a5b24a5d2c00d1cd00a70f2948d6cdfbf9ca6f286bd7f61b
  languageName: node
  linkType: hard

"@vitest/eslint-plugin@npm:^1.1.12":
  version: 1.1.14
  resolution: "@vitest/eslint-plugin@npm:1.1.14"
  peerDependencies:
    "@typescript-eslint/utils": ">= 8.0"
    eslint: ">= 8.57.0"
    typescript: ">= 5.0.0"
    vitest: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
    vitest:
      optional: true
  checksum: a5588075e12984efb783b6f04303e5d852c46b3ec0ac7febb1c3ded18ad5f1c8fddbd1a938966904a1e8cb4bff3c8b3a3a37ed26b8c131fe779cfa0ce21d40aa
  languageName: node
  linkType: hard

"@vitest/expect@npm:2.1.8":
  version: 2.1.8
  resolution: "@vitest/expect@npm:2.1.8"
  dependencies:
    "@vitest/spy": 2.1.8
    "@vitest/utils": 2.1.8
    chai: ^5.1.2
    tinyrainbow: ^1.2.0
  checksum: 3e81e61dfb5222797ab5e6a70bee2d032fb382c9e7ddc4abe03114f0efbc4cc3b56fc2648e89ae26f5d9261554ea7c696b3a329c8ccea2c702cb87476e4a6842
  languageName: node
  linkType: hard

"@vitest/mocker@npm:2.1.8":
  version: 2.1.8
  resolution: "@vitest/mocker@npm:2.1.8"
  dependencies:
    "@vitest/spy": 2.1.8
    estree-walker: ^3.0.3
    magic-string: ^0.30.12
  peerDependencies:
    msw: ^2.4.9
    vite: ^5.0.0
  peerDependenciesMeta:
    msw:
      optional: true
    vite:
      optional: true
  checksum: 0022d76ff78043c0788380b4e530a03690f8a89a14d1d07c4c069c8fd828a4741d2777ebc486ca0f89b2fb8cf880a7880d6891dd7cfb8cf4339f040131a8bda2
  languageName: node
  linkType: hard

"@vitest/pretty-format@npm:2.1.8, @vitest/pretty-format@npm:^2.1.8":
  version: 2.1.8
  resolution: "@vitest/pretty-format@npm:2.1.8"
  dependencies:
    tinyrainbow: ^1.2.0
  checksum: 2214ca317a19220a5f308a4e77fe403fa091c2f006d1f5b1bd91e8fad6e167db2fdc7882e564da3518d5b2cd9dedb1e97067bb666a820519c54f1c26ac9b0c5a
  languageName: node
  linkType: hard

"@vitest/runner@npm:2.1.8":
  version: 2.1.8
  resolution: "@vitest/runner@npm:2.1.8"
  dependencies:
    "@vitest/utils": 2.1.8
    pathe: ^1.1.2
  checksum: 50625597a01fbb55e7edf303b2ce6df7a46347bf05017ce7c4b4ce491ac6d85380aa5dd80127307f2621dc4b3b4081ef8ba1f3f825335f0af812a8e4e08d4aa2
  languageName: node
  linkType: hard

"@vitest/snapshot@npm:2.1.8":
  version: 2.1.8
  resolution: "@vitest/snapshot@npm:2.1.8"
  dependencies:
    "@vitest/pretty-format": 2.1.8
    magic-string: ^0.30.12
    pathe: ^1.1.2
  checksum: ff6a6033fe891c9b19fc33ef2518c29e8afca17c02c463b6b1f40e7e7d51867d5c0bb9b49f524b7c0889337a2c0cae7dcc864851a59a28d3a6e0488cecc45040
  languageName: node
  linkType: hard

"@vitest/spy@npm:2.1.8":
  version: 2.1.8
  resolution: "@vitest/spy@npm:2.1.8"
  dependencies:
    tinyspy: ^3.0.2
  checksum: 0e497e7a7f3170f761c0dbdf983f13d09616b2bae7f640c216644f8d3d1f1a6b6e59aa1e6b75ca2a773355811f0ad6b9d7b6b14596d9b99378040917cada5d49
  languageName: node
  linkType: hard

"@vitest/utils@npm:2.1.8":
  version: 2.1.8
  resolution: "@vitest/utils@npm:2.1.8"
  dependencies:
    "@vitest/pretty-format": 2.1.8
    loupe: ^3.1.2
    tinyrainbow: ^1.2.0
  checksum: 711e7998ba9785880ed416d08b478e2b881cd218d37c3d773b26477adaa6aab91758e01ac039f839175f446111118fb5aa041317b619eeeb05537e3912159eb7
  languageName: node
  linkType: hard

"@volar/language-core@npm:2.4.10, @volar/language-core@npm:~2.4.8":
  version: 2.4.10
  resolution: "@volar/language-core@npm:2.4.10"
  dependencies:
    "@volar/source-map": 2.4.10
  checksum: 463f2fb87d2cad1efbbe6c2cb06060c1285ef6fc35ee41fe1927a584d830629dd9c21d780349526350691b7230711ffcf088ad9836005f0a7e0a0ceb94dfd640
  languageName: node
  linkType: hard

"@volar/source-map@npm:2.4.10":
  version: 2.4.10
  resolution: "@volar/source-map@npm:2.4.10"
  checksum: f52cde8467a3bfd63d00b76383d39d12e6c510da28d45f561ad54f0f3ffd7cb362e0469449d26f310cffec960d99ebe91d356cd5ca72bb15e337c25f787d7da9
  languageName: node
  linkType: hard

"@volar/typescript@npm:~2.4.8":
  version: 2.4.10
  resolution: "@volar/typescript@npm:2.4.10"
  dependencies:
    "@volar/language-core": 2.4.10
    path-browserify: ^1.0.1
    vscode-uri: ^3.0.8
  checksum: d55982932c53aab4ad1f6399a5d0a80ae266ae8ad1243280ef1f21da49aabf1cea34c3390db18f789caaaa30fe2fafd4f3f3356d0e4266dd15225241aac61065
  languageName: node
  linkType: hard

"@vue-macros/common@npm:^1.15.0":
  version: 1.15.0
  resolution: "@vue-macros/common@npm:1.15.0"
  dependencies:
    "@babel/types": ^7.25.8
    "@rollup/pluginutils": ^5.1.2
    "@vue/compiler-sfc": ^3.5.12
    ast-kit: ^1.3.0
    local-pkg: ^0.5.0
    magic-string-ast: ^0.6.2
  peerDependencies:
    vue: ^2.7.0 || ^3.2.25
  peerDependenciesMeta:
    vue:
      optional: true
  checksum: 4bcfc9205b4f0f144cf557755296d5c9c2ab122bdd604b8e6bc5bc88b77c84354bdb267bb43ef6a359acd04d1f3e1a0cb5415abc1a6abfc2b5b340fe5a07beff
  languageName: node
  linkType: hard

"@vue/compiler-core@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/compiler-core@npm:3.5.13"
  dependencies:
    "@babel/parser": ^7.25.3
    "@vue/shared": 3.5.13
    entities: ^4.5.0
    estree-walker: ^2.0.2
    source-map-js: ^1.2.0
  checksum: 9c67d4bcf2bcd758e45778f1d75efcf681154be1c13c5cb1c0b78c77373277a7f6bd69a3b816c17fa157316b989421d420a8d5af4915e89049a27dc7a6d97bcb
  languageName: node
  linkType: hard

"@vue/compiler-dom@npm:3.5.13, @vue/compiler-dom@npm:^3.2.45, @vue/compiler-dom@npm:^3.5.0":
  version: 3.5.13
  resolution: "@vue/compiler-dom@npm:3.5.13"
  dependencies:
    "@vue/compiler-core": 3.5.13
    "@vue/shared": 3.5.13
  checksum: 8711fd205613829d685c5969b4ef313ff2ebba54f69b59274f0398424c0ea02ddacf51d450dd653ddbd33c9891bd42955ef8e677c58640535723673adfcf54b8
  languageName: node
  linkType: hard

"@vue/compiler-sfc@npm:3.5.13, @vue/compiler-sfc@npm:^3.5.12":
  version: 3.5.13
  resolution: "@vue/compiler-sfc@npm:3.5.13"
  dependencies:
    "@babel/parser": ^7.25.3
    "@vue/compiler-core": 3.5.13
    "@vue/compiler-dom": 3.5.13
    "@vue/compiler-ssr": 3.5.13
    "@vue/shared": 3.5.13
    estree-walker: ^2.0.2
    magic-string: ^0.30.11
    postcss: ^8.4.48
    source-map-js: ^1.2.0
  checksum: c1c03c9c19c839cf4721748dec50e2004b2f3ebe7eef2a30f3f473f4dfe386d5a04573e46d5c5c606d8411f124d28383580ae14dfc8e489e39b2a5121ce5933d
  languageName: node
  linkType: hard

"@vue/compiler-ssr@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/compiler-ssr@npm:3.5.13"
  dependencies:
    "@vue/compiler-dom": 3.5.13
    "@vue/shared": 3.5.13
  checksum: 066d6288a7ba2519ea7d9f97fc04bd140221d7a63e80e404020bfe78d502a31bb0a76381c7fb7beec841f98bd0948f4cfbea58ac53fca052965e6a4ea88af1e7
  languageName: node
  linkType: hard

"@vue/compiler-vue2@npm:^2.7.16":
  version: 2.7.16
  resolution: "@vue/compiler-vue2@npm:2.7.16"
  dependencies:
    de-indent: ^1.0.2
    he: ^1.2.0
  checksum: d6bba6c637838cf515907a7e530ae21d93d6d285c221a991a6b9afd292cc38cd822d043f333883cfe79228c6d150423fdb298df07c202a43a2b49862ecd75bde
  languageName: node
  linkType: hard

"@vue/devtools-api@npm:^6.5.0, @vue/devtools-api@npm:^6.6.3, @vue/devtools-api@npm:^6.6.4":
  version: 6.6.4
  resolution: "@vue/devtools-api@npm:6.6.4"
  checksum: beccd79c7c7794ea511e35581fbbe5411d3c5d63b2418bc3771efc66959966df4cbfc391ff5954028e2728eb0f0e37b41722e1c39fde61295f2814a143d2ef0e
  languageName: node
  linkType: hard

"@vue/language-core@npm:2.1.10":
  version: 2.1.10
  resolution: "@vue/language-core@npm:2.1.10"
  dependencies:
    "@volar/language-core": ~2.4.8
    "@vue/compiler-dom": ^3.5.0
    "@vue/compiler-vue2": ^2.7.16
    "@vue/shared": ^3.5.0
    alien-signals: ^0.2.0
    minimatch: ^9.0.3
    muggle-string: ^0.4.1
    path-browserify: ^1.0.1
  peerDependencies:
    typescript: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 9b208b54fd8409f740cefb63cbc27f6d67d281836bc1ed2a7c8c9e0f86c7ed0cf6ed27d36cb0e0503f74b3893e7e0de543a7db1de651d70cc5a6cdbb1a196ac1
  languageName: node
  linkType: hard

"@vue/reactivity@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/reactivity@npm:3.5.13"
  dependencies:
    "@vue/shared": 3.5.13
  checksum: 5c241cf7c62929dfd7a5a68ccdeca921ab245d16a7350a15928536955f8fb7edd8ca5e782b63ce9b6c0271e2aebd6d34cad1578aeb416646bb01cb63274d18e5
  languageName: node
  linkType: hard

"@vue/runtime-core@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/runtime-core@npm:3.5.13"
  dependencies:
    "@vue/reactivity": 3.5.13
    "@vue/shared": 3.5.13
  checksum: b9c732c95b83d4b8a22b3759b20f9715797926332ff74cc63d588791ef5efaaa6cdb504ab81cd65f1b1b65101800a24c92da2a7bd180a2f1c840ac62eb97fd83
  languageName: node
  linkType: hard

"@vue/runtime-dom@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/runtime-dom@npm:3.5.13"
  dependencies:
    "@vue/reactivity": 3.5.13
    "@vue/runtime-core": 3.5.13
    "@vue/shared": 3.5.13
    csstype: ^3.1.3
  checksum: c1f18baaa5e0fff2167f1835672ce2d21140ee4c2a8dc5f60686fdc35c361f482823caa90897819768fa4033d22e687b243914073a5471f2450de0adfdea50b6
  languageName: node
  linkType: hard

"@vue/server-renderer@npm:3.5.13":
  version: 3.5.13
  resolution: "@vue/server-renderer@npm:3.5.13"
  dependencies:
    "@vue/compiler-ssr": 3.5.13
    "@vue/shared": 3.5.13
  peerDependencies:
    vue: 3.5.13
  checksum: 58960d73344aeeee574977a85f5c14b28f29223f928b8eb4408bc159ac57a192e39a28f29f825ca9341486931edca7edc018cbda92feac81c8daf11c46339759
  languageName: node
  linkType: hard

"@vue/shared@npm:3.5.13, @vue/shared@npm:^3.5.0":
  version: 3.5.13
  resolution: "@vue/shared@npm:3.5.13"
  checksum: b562499b3f1506fe41d37ecb27af6a35d6585457b6ebc52bd2acae37feea30225280968b36b1121c4ae1056c34d140aa525d9020ae558a4e557445290a31c6a9
  languageName: node
  linkType: hard

"@vue/test-utils@npm:^2.4.6":
  version: 2.4.6
  resolution: "@vue/test-utils@npm:2.4.6"
  dependencies:
    js-beautify: ^1.14.9
    vue-component-type-helpers: ^2.0.0
  checksum: ae6f3c10f6ffb45b7be73e1c550c18b1d54ebec0a7d34f2600f974d1407ace8de48836600d60c872bbfb007d109a2ececb2307659a4c861e62ced913d32c7a25
  languageName: node
  linkType: hard

"@vueuse/core@npm:^12.0.0":
  version: 12.0.0
  resolution: "@vueuse/core@npm:12.0.0"
  dependencies:
    "@types/web-bluetooth": ^0.0.20
    "@vueuse/metadata": 12.0.0
    "@vueuse/shared": 12.0.0
    vue: ^3.5.13
  checksum: c0d82858f81d8cbbd221bb643d3dd9402e9a044d2fdc060974855dc0fc1df857a2214c4334c3134ee1df4784b77b5b91b3df308858d6d0848e6df7ac2ab8155a
  languageName: node
  linkType: hard

"@vueuse/metadata@npm:12.0.0":
  version: 12.0.0
  resolution: "@vueuse/metadata@npm:12.0.0"
  checksum: fd6242fce61147a595aba3622542fc18680f735bbf4b1f37a08185d95e35347203604f244099849bc26678d88b4d71ce4e99dbb6b0555613ef450b1244a4bea0
  languageName: node
  linkType: hard

"@vueuse/router@npm:^12.0.0":
  version: 12.0.0
  resolution: "@vueuse/router@npm:12.0.0"
  dependencies:
    "@vueuse/shared": 12.0.0
    vue: ^3.5.13
  peerDependencies:
    vue-router: ">=4.0.0-rc.1"
  checksum: f2e9c39449100f98ac30b3b31615bc3697d9f4ba822daef281b1e51977392a10eb66bebf5ab91bd82b15e8b46702ff9dd5ca8af545f2912ff06c31c6c40a1efe
  languageName: node
  linkType: hard

"@vueuse/shared@npm:12.0.0":
  version: 12.0.0
  resolution: "@vueuse/shared@npm:12.0.0"
  dependencies:
    vue: ^3.5.13
  checksum: 1c150c6936deaba32d87de8097bbcf4fe65b7796828e2f198ae5eeb3af4e7d6c2fe1c67757211cc1e2442c2228a9cfe6bafdfd478dd3d5202c6cff5362047b54
  languageName: node
  linkType: hard

"abbrev@npm:^2.0.0":
  version: 2.0.0
  resolution: "abbrev@npm:2.0.0"
  checksum: 0e994ad2aa6575f94670d8a2149afe94465de9cedaaaac364e7fb43a40c3691c980ff74899f682f4ca58fa96b4cbd7421a015d3a6defe43a442117d7821a2f36
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: c3d3b2a89c9a056b205b69530a37b972b404ee46ec8e5b341666f9513d3163e2a4f214a71f4dfc7370f5a9c07472d2fd1c11c91c3f03d093e37637d95da98950
  languageName: node
  linkType: hard

"acorn@npm:^8.14.0, acorn@npm:^8.4.0, acorn@npm:^8.5.0, acorn@npm:^8.8.2, acorn@npm:^8.9.0":
  version: 8.14.0
  resolution: "acorn@npm:8.14.0"
  bin:
    acorn: bin/acorn
  checksum: 8755074ba55fff94e84e81c72f1013c2d9c78e973c31231c8ae505a5f966859baf654bddd75046bffd73ce816b149298977fff5077a3033dedba0ae2aad152d4
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 87bb7ee54f5ecf0ccbfcba0b07473885c43ecd76cb29a8db17d6137a19d9f9cd443a2a7c5fd8a3f24d58ad8145f9eb49116344a66b107e1aeab82cf2383f4753
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 874972efe5c4202ab0a68379481fbd3d1b5d0a7bd6d3cc21d40d3536ebff3352a2a1fabb632d4fd2cc7fe4cbdcd5ed6782084c9bbf7f32a1536d18f9da5007d4
  languageName: node
  linkType: hard

"alien-signals@npm:^0.2.0":
  version: 0.2.2
  resolution: "alien-signals@npm:0.2.2"
  checksum: 6d7c0a315380b4de1b98f9448ae6b8071491f24f88e3bed133099b1bd68c5d8d0dd70a9a4565b466907c0feb4fdc378b7ac7265fc711a75041561ff5fbfb2586
  languageName: node
  linkType: hard

"ansi-escapes@npm:^7.0.0":
  version: 7.0.0
  resolution: "ansi-escapes@npm:7.0.0"
  dependencies:
    environment: ^1.0.0
  checksum: 19baa61e68d1998c03b3b8bd023653a6c2667f0ed6caa9a00780ffd6f0a14f4a6563c57a38b3c0aba71bd704cd49c4c8df41be60bd81c957409f91e9dd49051f
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 495834a53b0856c02acd40446f7130cb0f8284f4a39afdab20d5dc42b2e198b1196119fe887beed8f9055c4ff2055e3b2f6d4641d0be018cdfb64fedf6fc1aac
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.0.0, ansi-styles@npm:^6.1.0, ansi-styles@npm:^6.2.1":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: ef940f2f0ced1a6347398da88a91da7930c33ecac3c77b72c5905f8b8fe402c52e6fde304ff5347f616e27a742da3f1dc76de98f6866c69251ad0b07a66776d9
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: ^3.0.0
    picomatch: ^2.0.4
  checksum: 3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"are-docs-informative@npm:^0.0.2":
  version: 0.0.2
  resolution: "are-docs-informative@npm:0.0.2"
  checksum: 7a48ca90d66e29afebc4387d7029d86cfe97bad7e796c8e7de01309e02dcfc027250231c02d4ca208d2984170d09026390b946df5d3d02ac638ab35f74501c74
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 83644b56493e89a254bae05702abf3a1101b4fa4d0ca31df1c9985275a5a5bd47b3c27b7fa0b71098d41114d8ca000e6ed90cad764b306f8a503665e4d517ced
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.0":
  version: 1.0.1
  resolution: "array-buffer-byte-length@npm:1.0.1"
  dependencies:
    call-bind: ^1.0.5
    is-array-buffer: ^3.0.4
  checksum: 53524e08f40867f6a9f35318fafe467c32e45e9c682ba67b11943e167344d2febc0f6977a17e699b05699e805c3e8f073d876f8bbf1b559ed494ad2cd0fae09e
  languageName: node
  linkType: hard

"assertion-error@npm:^2.0.1":
  version: 2.0.1
  resolution: "assertion-error@npm:2.0.1"
  checksum: a0789dd882211b87116e81e2648ccb7f60340b34f19877dd020b39ebb4714e475eb943e14ba3e22201c221ef6645b7bfe10297e76b6ac95b48a9898c1211ce66
  languageName: node
  linkType: hard

"ast-kit@npm:^1.0.1, ast-kit@npm:^1.3.0, ast-kit@npm:^1.3.1":
  version: 1.3.2
  resolution: "ast-kit@npm:1.3.2"
  dependencies:
    "@babel/parser": ^7.26.2
    pathe: ^1.1.2
  checksum: ef2d35d05e9f29633c82169eac882603a10e6371b134003f0b8bb36aeb9837c54d1731343cc38b6cef8b56b0c4f6caf33e1a1ecc3f31ed5cb9a4ee243494b925
  languageName: node
  linkType: hard

"ast-walker-scope@npm:^0.6.2":
  version: 0.6.2
  resolution: "ast-walker-scope@npm:0.6.2"
  dependencies:
    "@babel/parser": ^7.25.3
    ast-kit: ^1.0.1
  checksum: e953268388b70095aeb1fe1e578ee11d280f332fe4c0bd3a834dd787ce02afcaa882631f12a69121c68dba04967a4eeb7721d338f29cab31942c1b094b6ef240
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 7b78c451df768adba04e2d02e63e2d0bf3b07adcd6e42b4cf665cb7ce899bedd344c69a1dcbce355b5f972d597b25aaa1c1742b52cffd9caccb22f348114f6be
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: ^1.0.0
  checksum: 1aa3ffbfe6578276996de660848b6e95669d9a95ad149e3dd0c0cda77db6ee1dbd9d1dd723b65b6d277b882dd0c4b91a654ae9d3cf9e1254b7e93e4908d78fd3
  languageName: node
  linkType: hard

"axios@npm:^1.0.0":
  version: 1.7.9
  resolution: "axios@npm:1.7.9"
  dependencies:
    follow-redirects: ^1.15.6
    form-data: ^4.0.0
    proxy-from-env: ^1.1.0
  checksum: cb8ce291818effda09240cb60f114d5625909b345e10f389a945320e06acf0bc949d0f8422d25720f5dd421362abee302c99f5e97edec4c156c8939814b23d19
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: bcad01494e8a9283abf18c1b967af65ee79b0c6a9e6fcfafebfe91dbe6e0fc7272bafb73389e198b310516ae04f7ad17d79aacf6cb4c0d5d5202a7e2e52c7d98
  languageName: node
  linkType: hard

"body-parser@npm:^1.20.0":
  version: 1.20.3
  resolution: "body-parser@npm:1.20.3"
  dependencies:
    bytes: 3.1.2
    content-type: ~1.0.5
    debug: 2.6.9
    depd: 2.0.0
    destroy: 1.2.0
    http-errors: 2.0.0
    iconv-lite: 0.4.24
    on-finished: 2.4.1
    qs: 6.13.0
    raw-body: 2.5.2
    type-is: ~1.6.18
    unpipe: 1.0.0
  checksum: 1a35c59a6be8d852b00946330141c4f142c6af0f970faa87f10ad74f1ee7118078056706a05ae3093c54dabca9cd3770fa62a170a85801da1a4324f04381167d
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 3e25c80ef626c3a3487c73dbfc70ac322ec830666c9ad915d11b701142fab25ec1e63eff2c450c74347acfd2de854ccde865cd79ef4db1683f7c7b046ea43bb0
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: ^1.0.0
  checksum: a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: ^7.1.1
  checksum: b95aa0b3bd909f6cd1720ffcf031aeaf46154dd88b4da01f9a1d3f7ea866a79eba76a6d01cbc3c422b2ee5cdc39a4f02491058d5df0d7bf6e6a162a832df1f69
  languageName: node
  linkType: hard

"browserslist@npm:^4.24.2":
  version: 4.24.2
  resolution: "browserslist@npm:4.24.2"
  dependencies:
    caniuse-lite: ^1.0.30001669
    electron-to-chromium: ^1.5.41
    node-releases: ^2.0.18
    update-browserslist-db: ^1.1.1
  bin:
    browserslist: cli.js
  checksum: cf64085f12132d38638f38937a255edb82c7551b164a98577b055dd79719187a816112f7b97b9739e400c4954cd66479c0d7a843cb816e346f4795dc24fd5d97
  languageName: node
  linkType: hard

"buffer-builder@npm:^0.2.0":
  version: 0.2.0
  resolution: "buffer-builder@npm:0.2.0"
  checksum: 4d8215a03dbb193a3417bcd6057e324d4b7accabb6d3db5f9b3047fd4dd314814d8e7d026a0eebac7dc3cf24420f024e545f2d3932fdf32373af27b5f03b8e5b
  languageName: node
  linkType: hard

"builtin-modules@npm:^3.3.0":
  version: 3.3.0
  resolution: "builtin-modules@npm:3.3.0"
  checksum: db021755d7ed8be048f25668fe2117620861ef6703ea2c65ed2779c9e3636d5c3b82325bd912244293959ff3ae303afa3471f6a15bf5060c103e4cc3a839749d
  languageName: node
  linkType: hard

"bundle-require@npm:^5.0.0":
  version: 5.0.0
  resolution: "bundle-require@npm:5.0.0"
  dependencies:
    load-tsconfig: ^0.2.3
  peerDependencies:
    esbuild: ">=0.18"
  checksum: c5b31e2782546c21649cfc6e4bbdbaa0a41764e0d73b31c95f0a40efe961f83bc5cdc92308ad26aee735cf82b438cf945cc96dfefa16c5d8fc61a7cca67fa3fb
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: e4bcd3948d289c5127591fbedf10c0b639ccbf00243504e4e127374a15c3bc8eed0d28d4aaab08ff6f1cf2abc0cce6ba3085ed32f4f90e82a5683ce0014e1b6e
  languageName: node
  linkType: hard

"cac@npm:^6.7.14":
  version: 6.7.14
  resolution: "cac@npm:6.7.14"
  checksum: 45a2496a9443abbe7f52a49b22fbe51b1905eff46e03fd5e6c98e3f85077be3f8949685a1849b1a9cd2bc3e5567dfebcf64f01ce01847baf918f1b37c839791a
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": ^4.0.0
    fs-minipass: ^3.0.0
    glob: ^10.2.2
    lru-cache: ^10.0.1
    minipass: ^7.0.3
    minipass-collect: ^2.0.1
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    p-map: ^7.0.2
    ssri: ^12.0.0
    tar: ^7.4.3
    unique-filename: ^4.0.0
  checksum: e95684717de6881b4cdaa949fa7574e3171946421cd8291769dd3d2417dbf7abf4aa557d1f968cca83dcbc95bed2a281072b09abfc977c942413146ef7ed4525
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0":
  version: 1.0.1
  resolution: "call-bind-apply-helpers@npm:1.0.1"
  dependencies:
    es-errors: ^1.3.0
    function-bind: ^1.1.2
  checksum: 3c55343261bb387c58a4762d15ad9d42053659a62681ec5eb50690c6b52a4a666302a01d557133ce6533e8bd04530ee3b209f23dd06c9577a1925556f8fcccdf
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.2, call-bind@npm:^1.0.5, call-bind@npm:^1.0.6, call-bind@npm:^1.0.7":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: ^1.0.0
    es-define-property: ^1.0.0
    get-intrinsic: ^1.2.4
    set-function-length: ^1.2.2
  checksum: aa2899bce917a5392fd73bd32e71799c37c0b7ab454e0ed13af7f6727549091182aade8bbb7b55f304a5bc436d543241c14090fb8a3137e9875e23f444f4f5a9
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001669":
  version: 1.0.30001687
  resolution: "caniuse-lite@npm:1.0.30001687"
  checksum: 20fea782da99d7ff964a9f0573c9eb02762eee2783522f5db5c0a20ba9d9d1cf294aae4162b5ef2f47f729916e6bd0ba457118c6d086c1132d19a46d2d1c61e7
  languageName: node
  linkType: hard

"ccount@npm:^2.0.0":
  version: 2.0.1
  resolution: "ccount@npm:2.0.1"
  checksum: 48193dada54c9e260e0acf57fc16171a225305548f9ad20d5471e0f7a8c026aedd8747091dccb0d900cde7df4e4ddbd235df0d8de4a64c71b12f0d3303eeafd4
  languageName: node
  linkType: hard

"chai@npm:^5.1.2":
  version: 5.1.2
  resolution: "chai@npm:5.1.2"
  dependencies:
    assertion-error: ^2.0.1
    check-error: ^2.1.1
    deep-eql: ^5.0.1
    loupe: ^3.1.0
    pathval: ^2.0.0
  checksum: f2341967ab5632612548d372c27b46219adad3af35021d8cba2ae3c262f588de2c60cb3f004e6ad40e363a9cad6d20d0de51f00e7e9ac31cce17fb05d4efa316
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: fe75c9d5c76a7a98d45495b91b2172fa3b7a09e0cc9370e5c8feb1c567b85c4288e2b3fded7cfdd7359ac28d6b3844feb8b82b8686842e93d23c827c417e83fc
  languageName: node
  linkType: hard

"chalk@npm:~5.3.0":
  version: 5.3.0
  resolution: "chalk@npm:5.3.0"
  checksum: 623922e077b7d1e9dedaea6f8b9e9352921f8ae3afe739132e0e00c275971bdd331268183b2628cf4ab1727c45ea1f28d7e24ac23ce1db1eb653c414ca8a5a80
  languageName: node
  linkType: hard

"character-entities@npm:^2.0.0":
  version: 2.0.2
  resolution: "character-entities@npm:2.0.2"
  checksum: cf1643814023697f725e47328fcec17923b8f1799102a8a79c1514e894815651794a2bffd84bb1b3a4b124b050154e4529ed6e81f7c8068a734aecf07a6d3def
  languageName: node
  linkType: hard

"check-error@npm:^2.1.1":
  version: 2.1.1
  resolution: "check-error@npm:2.1.1"
  checksum: d785ed17b1d4a4796b6e75c765a9a290098cf52ff9728ce0756e8ffd4293d2e419dd30c67200aee34202463b474306913f2fcfaf1890641026d9fc6966fea27a
  languageName: node
  linkType: hard

"chokidar@npm:^3.6.0":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: ~3.1.2
    braces: ~3.0.2
    fsevents: ~2.3.2
    glob-parent: ~5.1.2
    is-binary-path: ~2.1.0
    is-glob: ~4.0.1
    normalize-path: ~3.0.0
    readdirp: ~3.6.0
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: d2f29f499705dcd4f6f3bbed79a9ce2388cf530460122eed3b9c48efeab7a4e28739c6551fd15bec9245c6b9eeca7a32baa64694d64d9b6faeb74ddb8c4a413d
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: fd73a4bab48b79e66903fe1cafbdc208956f41ea4f856df883d0c7277b7ab29fd33ee65f93b2ec9192fc0169238f2f8307b7735d27c155821d886b84aa97aa8d
  languageName: node
  linkType: hard

"ci-info@npm:^4.0.0":
  version: 4.1.0
  resolution: "ci-info@npm:4.1.0"
  checksum: dcf286abdc1bb1c4218b91e4a617b49781b282282089b7188e1417397ea00c6b967848e2360fb9a6b10021bf18a627f20ef698f47c2c9c875aeffd1d2ea51d1e
  languageName: node
  linkType: hard

"clean-regexp@npm:^1.0.0":
  version: 1.0.0
  resolution: "clean-regexp@npm:1.0.0"
  dependencies:
    escape-string-regexp: ^1.0.5
  checksum: 0b1ce281b07da2463c6882ea2e8409119b6cabbd9f687cdbdcee942c45b2b9049a2084f7b5f228c63ef9f21e722963ae0bfe56a735dbdbdd92512867625a7e40
  languageName: node
  linkType: hard

"cli-cursor@npm:^5.0.0":
  version: 5.0.0
  resolution: "cli-cursor@npm:5.0.0"
  dependencies:
    restore-cursor: ^5.0.0
  checksum: 1eb9a3f878b31addfe8d82c6d915ec2330cec8447ab1f117f4aa34f0137fbb3137ec3466e1c9a65bcb7557f6e486d343f2da57f253a2f668d691372dfa15c090
  languageName: node
  linkType: hard

"cli-truncate@npm:^4.0.0":
  version: 4.0.0
  resolution: "cli-truncate@npm:4.0.0"
  dependencies:
    slice-ansi: ^5.0.0
    string-width: ^7.0.0
  checksum: d5149175fd25ca985731bdeec46a55ec237475cf74c1a5e103baea696aceb45e372ac4acbaabf1316f06bd62e348123060f8191ffadfeedebd2a70a2a7fb199d
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: ^4.2.0
    strip-ansi: ^6.0.1
    wrap-ansi: ^7.0.0
  checksum: 79648b3b0045f2e285b76fb2e24e207c6db44323581e421c3acbd0e86454cba1b37aea976ab50195a49e7384b871e6dfb2247ad7dec53c02454ac6497394cb56
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"colorette@npm:^2.0.20":
  version: 2.0.20
  resolution: "colorette@npm:2.0.20"
  checksum: 0c016fea2b91b733eb9f4bcdb580018f52c0bc0979443dad930e5037a968237ac53d9beb98e218d2e9235834f8eebce7f8e080422d6194e957454255bde71d3d
  languageName: node
  linkType: hard

"colorjs.io@npm:^0.5.0":
  version: 0.5.2
  resolution: "colorjs.io@npm:0.5.2"
  checksum: f4ad8a0ead4e7eb74612a5fed9ae999201116b215b2eac70660c1f643e21980922741641209bdbd010723923762e8e80bd499250acf7505605309f01b76f53fa
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: ~1.0.0
  checksum: 49fa4aeb4916567e33ea81d088f6584749fc90c7abec76fd516bf1c5aa5c79f3584b5ba3de6b86d26ddd64bae5329c4c7479343250cfe71c75bb366eae53bb7c
  languageName: node
  linkType: hard

"commander@npm:^10.0.0":
  version: 10.0.1
  resolution: "commander@npm:10.0.1"
  checksum: 436901d64a818295803c1996cd856621a74f30b9f9e28a588e726b2b1670665bccd7c1a77007ebf328729f0139838a88a19265858a0fa7a8728c4656796db948
  languageName: node
  linkType: hard

"commander@npm:~12.1.0":
  version: 12.1.0
  resolution: "commander@npm:12.1.0"
  checksum: 68e9818b00fc1ed9cdab9eb16905551c2b768a317ae69a5e3c43924c2b20ac9bb65b27e1cab36aeda7b6496376d4da908996ba2c0b5d79463e0fb1e77935d514
  languageName: node
  linkType: hard

"comment-parser@npm:1.4.1, comment-parser@npm:^1.4.0":
  version: 1.4.1
  resolution: "comment-parser@npm:1.4.1"
  checksum: e0f6f60c5139689c4b1b208ea63e0730d9195a778e90dd909205f74f00b39eb0ead05374701ec5e5c29d6f28eb778cd7bc41c1366ab1d271907f1def132d6bf1
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"confbox@npm:^0.1.8":
  version: 0.1.8
  resolution: "confbox@npm:0.1.8"
  checksum: 5c7718ab22cf9e35a31c21ef124156076ae8c9dc65e6463d54961caf5a1d529284485a0fdf83fd23b27329f3b75b0c8c07d2e36c699f5151a2efe903343f976a
  languageName: node
  linkType: hard

"config-chain@npm:^1.1.13":
  version: 1.1.13
  resolution: "config-chain@npm:1.1.13"
  dependencies:
    ini: ^1.3.4
    proto-list: ~1.2.1
  checksum: 828137a28e7c2fc4b7fb229bd0cd6c1397bcf83434de54347e608154008f411749041ee392cbe42fab6307e02de4c12480260bf769b7d44b778fdea3839eafab
  languageName: node
  linkType: hard

"consola@npm:^3.2.3":
  version: 3.2.3
  resolution: "consola@npm:3.2.3"
  checksum: 32ec70e177dd2385c42e38078958cc7397be91db21af90c6f9faa0b16168b49b1c61d689338604bbb2d64370b9347a35f42a9197663a913d3a405bb0ce728499
  languageName: node
  linkType: hard

"content-type@npm:~1.0.5":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: 566271e0a251642254cde0f845f9dd4f9856e52d988f4eb0d0dcffbb7a1f8ec98de7a5215fc628f3bce30fe2fb6fd2bc064b562d721658c59b544e2d34ea2766
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.38.1":
  version: 3.39.0
  resolution: "core-js-compat@npm:3.39.0"
  dependencies:
    browserslist: ^4.24.2
  checksum: 2d7d087c3271d711d03a55203d4756f6288317a1ce35cdc8bafaf1833ef21fd67a92a50cff8dcf7df1325ac63720906ab3cf514c85b238c95f65fca1040f6ad6
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.3, cross-spawn@npm:^7.0.5":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 8d306efacaf6f3f60e0224c287664093fa9185680b2d195852ba9a863f85d02dcc737094c6e512175f8ee0161f9b87c73c6826034c2422e39de7d6569cf4503b
  languageName: node
  linkType: hard

"css-tree@npm:^3.0.1":
  version: 3.1.0
  resolution: "css-tree@npm:3.1.0"
  dependencies:
    mdn-data: 2.12.2
    source-map-js: ^1.0.1
  checksum: 6b8c713c22b7223c0e71179575c3bbf421a13a61641204645d6c3b560bdc4ffed8d676220bbcb83777e07b46a934ec3b1c629aa61d57422c196c8e2e7417ee1a
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: f8c4ababffbc5e2ddf2fa9957dda1ee4af6048e22aeda1869d0d00843223c1b13ad3f5d88b51caa46c994225eacb636b764eb807a8883e2fb6f99b4f4e8c48b2
  languageName: node
  linkType: hard

"cssstyle@npm:^4.1.0":
  version: 4.1.0
  resolution: "cssstyle@npm:4.1.0"
  dependencies:
    rrweb-cssom: ^0.7.1
  checksum: a8f5746430c42347e76dc830548f3a296882e42a90af188ae44e4c1a4131aec246b0b6c8562e3e6e4fa0ff14aeee5cd14a0e2fe5a7105dcf39f98eb70d16b634
  languageName: node
  linkType: hard

"csstype@npm:^3.1.3":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 8db785cc92d259102725b3c694ec0c823f5619a84741b5c7991b8ad135dfaa66093038a1cc63e03361a6cd28d122be48f2106ae72334e067dd619a51f49eddf7
  languageName: node
  linkType: hard

"data-urls@npm:^5.0.0":
  version: 5.0.0
  resolution: "data-urls@npm:5.0.0"
  dependencies:
    whatwg-mimetype: ^4.0.0
    whatwg-url: ^14.0.0
  checksum: 5c40568c31b02641a70204ff233bc4e42d33717485d074244a98661e5f2a1e80e38fe05a5755dfaf2ee549f2ab509d6a3af2a85f4b2ad2c984e5d176695eaf46
  languageName: node
  linkType: hard

"dayjs@npm:^1.11.13":
  version: 1.11.13
  resolution: "dayjs@npm:1.11.13"
  checksum: f388db88a6aa93956c1f6121644e783391c7b738b73dbc54485578736565c8931bdfba4bb94e9b1535c6e509c97d5deb918bbe1ae6b34358d994de735055cca9
  languageName: node
  linkType: hard

"de-indent@npm:^1.0.2":
  version: 1.0.2
  resolution: "de-indent@npm:1.0.2"
  checksum: 8deacc0f4a397a4414a0fc4d0034d2b7782e7cb4eaf34943ea47754e08eccf309a0e71fa6f56cc48de429ede999a42d6b4bca761bf91683be0095422dbf24611
  languageName: node
  linkType: hard

"debug@npm:2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: 2.0.0
  checksum: d2f51589ca66df60bf36e1fa6e4386b318c3f1e06772280eea5b1ae9fd3d05e9c2b7fd8a7d862457d00853c75b00451aa2d7459b924629ee385287a650f58fe6
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.0.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.3, debug@npm:^4.3.4, debug@npm:^4.3.6, debug@npm:^4.3.7, debug@npm:^4.4.0, debug@npm:~4.4.0":
  version: 4.4.0
  resolution: "debug@npm:4.4.0"
  dependencies:
    ms: ^2.1.3
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: fb42df878dd0e22816fc56e1fdca9da73caa85212fbe40c868b1295a6878f9101ae684f4eeef516c13acfc700f5ea07f1136954f43d4cd2d477a811144136479
  languageName: node
  linkType: hard

"debug@npm:^3.2.7":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: ^2.1.1
  checksum: b3d8c5940799914d30314b7c3304a43305fd0715581a919dacb8b3176d024a782062368405b47491516d2091d6462d4d11f2f4974a405048094f8bfebfa3071c
  languageName: node
  linkType: hard

"decimal.js@npm:^10.4.3":
  version: 10.4.3
  resolution: "decimal.js@npm:10.4.3"
  checksum: 796404dcfa9d1dbfdc48870229d57f788b48c21c603c3f6554a1c17c10195fc1024de338b0cf9e1efe0c7c167eeb18f04548979bcc5fdfabebb7cc0ae3287bae
  languageName: node
  linkType: hard

"decode-named-character-reference@npm:^1.0.0":
  version: 1.0.2
  resolution: "decode-named-character-reference@npm:1.0.2"
  dependencies:
    character-entities: ^2.0.0
  checksum: f4c71d3b93105f20076052f9cb1523a22a9c796b8296cd35eef1ca54239c78d182c136a848b83ff8da2071e3ae2b1d300bf29d00650a6d6e675438cc31b11d78
  languageName: node
  linkType: hard

"deep-eql@npm:^5.0.1":
  version: 5.0.2
  resolution: "deep-eql@npm:5.0.2"
  checksum: 6aaaadb4c19cbce42e26b2bbe5bd92875f599d2602635dc97f0294bae48da79e89470aedee05f449e0ca8c65e9fd7e7872624d1933a1db02713d99c2ca8d1f24
  languageName: node
  linkType: hard

"deep-equal@npm:^2.2.3":
  version: 2.2.3
  resolution: "deep-equal@npm:2.2.3"
  dependencies:
    array-buffer-byte-length: ^1.0.0
    call-bind: ^1.0.5
    es-get-iterator: ^1.1.3
    get-intrinsic: ^1.2.2
    is-arguments: ^1.1.1
    is-array-buffer: ^3.0.2
    is-date-object: ^1.0.5
    is-regex: ^1.1.4
    is-shared-array-buffer: ^1.0.2
    isarray: ^2.0.5
    object-is: ^1.1.5
    object-keys: ^1.1.1
    object.assign: ^4.1.4
    regexp.prototype.flags: ^1.5.1
    side-channel: ^1.0.4
    which-boxed-primitive: ^1.0.2
    which-collection: ^1.0.1
    which-typed-array: ^1.1.13
  checksum: ee8852f23e4d20a5626c13b02f415ba443a1b30b4b3d39eaf366d59c4a85e6545d7ec917db44d476a85ae5a86064f7e5f7af7479f38f113995ba869f3a1ddc53
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: edb65dd0d7d1b9c40b2f50219aef30e116cedd6fc79290e740972c132c09106d2e80aa0bc8826673dd5a00222d4179c84b36a790eef63a4c4bca75a37ef90804
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: ^1.0.0
    es-errors: ^1.3.0
    gopd: ^1.0.1
  checksum: 8068ee6cab694d409ac25936eb861eea704b7763f7f342adbdfe337fc27c78d7ae0eff2364b2917b58c508d723c7a074326d068eef2e45c4edcd85cf94d0313b
  languageName: node
  linkType: hard

"define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: ^1.0.1
    has-property-descriptors: ^1.0.0
    object-keys: ^1.1.1
  checksum: b4ccd00597dd46cb2d4a379398f5b19fca84a16f3374e2249201992f36b30f6835949a9429669ee6b41b6e837205a163eadd745e472069e70dfc10f03e5fcc12
  languageName: node
  linkType: hard

"defu@npm:^6.1.4":
  version: 6.1.4
  resolution: "defu@npm:6.1.4"
  checksum: 40e3af6338f195ac1564f53d1887fa2d0429ac7e8c081204bc4d29191180059d3952b5f4e08fe5df8d59eb873aa26e9c88b56d4fac699673d4a372c93620b229
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 46fe6e83e2cb1d85ba50bd52803c68be9bd953282fa7096f51fc29edd5d67ff84ff753c51966061e5ba7cb5e47ef6d36a91924eddb7f3f3483b1c560f77a0020
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: abbe19c768c97ee2eed6282d8ce3031126662252c58d711f646921c9623f9052e3e1906443066beec1095832f534e57c523b7333f8e7e0d93051ab6baef5ab3a
  languageName: node
  linkType: hard

"dequal@npm:^2.0.0":
  version: 2.0.3
  resolution: "dequal@npm:2.0.3"
  checksum: 8679b850e1a3d0ebbc46ee780d5df7b478c23f335887464023a631d1b9af051ad4a6595a44220f9ff8ff95a8ddccf019b5ad778a976fd7bbf77383d36f412f90
  languageName: node
  linkType: hard

"destr@npm:^2.0.3":
  version: 2.0.3
  resolution: "destr@npm:2.0.3"
  checksum: 4521b145ba6118919a561f7d979d623793695a516d1b9df704de81932601bf9cf21c47278e1cb93a309c88a14f4fd1f18680bb49ebef8b2546cc7f415e7ae48e
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 0acb300b7478a08b92d810ab229d5afe0d2f4399272045ab22affa0d99dbaf12637659411530a6fcd597a9bdac718fc94373a61a95b4651bbc7b83684a565e38
  languageName: node
  linkType: hard

"devlop@npm:^1.0.0, devlop@npm:^1.1.0":
  version: 1.1.0
  resolution: "devlop@npm:1.1.0"
  dependencies:
    dequal: ^2.0.0
  checksum: d2ff650bac0bb6ef08c48f3ba98640bb5fec5cce81e9957eb620408d1bab1204d382a45b785c6b3314dc867bb0684936b84c6867820da6db97cbb5d3c15dd185
  languageName: node
  linkType: hard

"diff@npm:^5.0.0":
  version: 5.2.0
  resolution: "diff@npm:5.2.0"
  checksum: 12b63ca9c36c72bafa3effa77121f0581b4015df18bc16bac1f8e263597735649f1a173c26f7eba17fb4162b073fee61788abe49610e6c70a2641fe1895443fd
  languageName: node
  linkType: hard

"doctrine@npm:^3.0.0":
  version: 3.0.0
  resolution: "doctrine@npm:3.0.0"
  dependencies:
    esutils: ^2.0.2
  checksum: fd7673ca77fe26cd5cba38d816bc72d641f500f1f9b25b83e8ce28827fe2da7ad583a8da26ab6af85f834138cf8dae9f69b0cd6ab925f52ddab1754db44d99ce
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0":
  version: 1.0.0
  resolution: "dunder-proto@npm:1.0.0"
  dependencies:
    call-bind-apply-helpers: ^1.0.0
    es-errors: ^1.3.0
    gopd: ^1.2.0
  checksum: 6f0697b17c47377efc00651f43f34e71c09ebba85fafb4d91fe67f5810931f3fa3f45a1ef5d207debbd5682ad9abc3b71b49cb3e67222dcad71fafc92cf6199b
  languageName: node
  linkType: hard

"duplexer@npm:^0.1.2":
  version: 0.1.2
  resolution: "duplexer@npm:0.1.2"
  checksum: 62ba61a830c56801db28ff6305c7d289b6dc9f859054e8c982abd8ee0b0a14d2e9a8e7d086ffee12e868d43e2bbe8a964be55ddbd8c8957714c87373c7a4f9b0
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 7d00d7cd8e49b9afa762a813faac332dee781932d6f2c848dc348939c4253f1d4564341b7af1d041853bc3f32c2ef141b58e0a4d9862c17a7f08f68df1e0f1ed
  languageName: node
  linkType: hard

"editorconfig@npm:^1.0.4":
  version: 1.0.4
  resolution: "editorconfig@npm:1.0.4"
  dependencies:
    "@one-ini/wasm": 0.1.1
    commander: ^10.0.0
    minimatch: 9.0.1
    semver: ^7.5.3
  bin:
    editorconfig: bin/editorconfig
  checksum: 09904f19381b3ddf132cea0762971aba887236f387be3540909e96b8eb9337e1793834e10f06890cd8e8e7bb1ba80cb13e7d50a863f227806c9ca74def4165fb
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 1b4cac778d64ce3b582a7e26b218afe07e207a0f9bfe13cc7395a6d307849cfe361e65033c3251e00c27dd060cab43014c2d6b2647676135e18b77d2d05b3f4f
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.41":
  version: 1.5.72
  resolution: "electron-to-chromium@npm:1.5.72"
  checksum: 4fc182082285fc99942d4881fb99c507dd97b01c930a3fe7a60bb7d03dd981db4755deb69e7787f7e7d2420b7109ec4724625ba1ce2795460892a184acbe2571
  languageName: node
  linkType: hard

"emoji-picker-element@npm:1.12.1":
  version: 1.12.1
  resolution: "emoji-picker-element@npm:1.12.1"
  checksum: 23f7a25ee6ce8afa7f96a83eb198705a75a89fd2bd06776737ea8cb4384794ceb38e3978c63dbe95b0c7ba7c06fcbb074f436751c7be8ef12d06d27155e41cf5
  languageName: node
  linkType: hard

"emoji-regex@npm:^10.3.0":
  version: 10.4.0
  resolution: "emoji-regex@npm:10.4.0"
  checksum: a6d9a0e454829a52e664e049847776ee1fff5646617b06cd87de7c03ce1dfcce4102a3b154d5e9c8e90f8125bc120fc1fe114d523dddf60a8a161f26c72658d2
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: d4c5c39d5a9868b5fa152f00cada8a936868fd3367f33f71be515ecee4c803132d11b31a6222b2571b1e5f7e13890156a94880345594d0ce7e3c9895f560f192
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 8487182da74aabd810ac6d6f1994111dfc0e331b01271ae01ec1eb0ad7b5ecc2bbbbd2f053c05cb55a1ac30449527d819bbfbf0e3de1023db308cbcb47f86601
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.17.1":
  version: 5.17.1
  resolution: "enhanced-resolve@npm:5.17.1"
  dependencies:
    graceful-fs: ^4.2.4
    tapable: ^2.2.0
  checksum: 4bc38cf1cea96456f97503db7280394177d1bc46f8f87c267297d04f795ac5efa81e48115a2f5b6273c781027b5b6bfc5f62b54df629e4d25fa7001a86624f59
  languageName: node
  linkType: hard

"entities@npm:^4.5.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 853f8ebd5b425d350bffa97dd6958143179a5938352ccae092c62d1267c4e392a039be1bae7d51b6e4ffad25f51f9617531fedf5237f15df302ccfb452cbf2d7
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"environment@npm:^1.0.0":
  version: 1.1.0
  resolution: "environment@npm:1.1.0"
  checksum: dd3c1b9825e7f71f1e72b03c2344799ac73f2e9ef81b78ea8b373e55db021786c6b9f3858ea43a436a2c4611052670ec0afe85bc029c384cc71165feee2f4ba6
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 8b7b1be20d2de12d2255c0bc2ca638b7af5171142693299416e6a9339bd7d88fc8d7707d913d78e0993176005405a236b066b45666b27b797252c771156ace54
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: ^0.2.1
  checksum: c1c2b8b65f9c91b0f9d75f0debaa7ec5b35c266c2cac5de412c1a6de86d4cbae04ae44e510378cb14d032d0645a36925d0186f8bb7367bcc629db256b743a001
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 0512f4e5d564021c9e3a644437b0155af2679d10d80f21adaf868e64d30efdfbd321631956f20f42d655fedb2e3a027da479fad3fa6048f768eb453a80a5f80a
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: ec1414527a0ccacd7f15f4a3bc66e215f04f595ba23ca75cdae0927af099b5ec865f9f4d33e9d7e86f512f252876ac77d4281a7871531a50678132429b1271b5
  languageName: node
  linkType: hard

"es-get-iterator@npm:^1.1.3":
  version: 1.1.3
  resolution: "es-get-iterator@npm:1.1.3"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.1.3
    has-symbols: ^1.0.3
    is-arguments: ^1.1.1
    is-map: ^2.0.2
    is-set: ^2.0.2
    is-string: ^1.0.7
    isarray: ^2.0.5
    stop-iteration-iterator: ^1.0.0
  checksum: 8fa118da42667a01a7c7529f8a8cca514feeff243feec1ce0bb73baaa3514560bd09d2b3438873cf8a5aaec5d52da248131de153b28e2638a061b6e4df13267d
  languageName: node
  linkType: hard

"es-module-lexer@npm:^0.4.1":
  version: 0.4.1
  resolution: "es-module-lexer@npm:0.4.1"
  checksum: c33ee30390812df875e419783cf37f3a32b73daabbb1f8a08fe9282fe0777b0a8064a219a34a253cb954b3308bbd4227e17da9e8ec253d309a83b4c90b2e9388
  languageName: node
  linkType: hard

"es-module-lexer@npm:^1.5.3, es-module-lexer@npm:^1.5.4":
  version: 1.5.4
  resolution: "es-module-lexer@npm:1.5.4"
  checksum: a0cf04fb92d052647ac7d818d1913b98d3d3d0f5b9d88f0eafb993436e4c3e2c958599db68839d57f2dfa281fdf0f60e18d448eb78fc292c33c0f25635b6854f
  languageName: node
  linkType: hard

"esbuild@npm:^0.20.2 || ^0.21.0 || ^0.22.0 || ^0.23.0, esbuild@npm:~0.23.0":
  version: 0.23.1
  resolution: "esbuild@npm:0.23.1"
  dependencies:
    "@esbuild/aix-ppc64": 0.23.1
    "@esbuild/android-arm": 0.23.1
    "@esbuild/android-arm64": 0.23.1
    "@esbuild/android-x64": 0.23.1
    "@esbuild/darwin-arm64": 0.23.1
    "@esbuild/darwin-x64": 0.23.1
    "@esbuild/freebsd-arm64": 0.23.1
    "@esbuild/freebsd-x64": 0.23.1
    "@esbuild/linux-arm": 0.23.1
    "@esbuild/linux-arm64": 0.23.1
    "@esbuild/linux-ia32": 0.23.1
    "@esbuild/linux-loong64": 0.23.1
    "@esbuild/linux-mips64el": 0.23.1
    "@esbuild/linux-ppc64": 0.23.1
    "@esbuild/linux-riscv64": 0.23.1
    "@esbuild/linux-s390x": 0.23.1
    "@esbuild/linux-x64": 0.23.1
    "@esbuild/netbsd-x64": 0.23.1
    "@esbuild/openbsd-arm64": 0.23.1
    "@esbuild/openbsd-x64": 0.23.1
    "@esbuild/sunos-x64": 0.23.1
    "@esbuild/win32-arm64": 0.23.1
    "@esbuild/win32-ia32": 0.23.1
    "@esbuild/win32-x64": 0.23.1
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-arm64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 0413c3b9257327fb598427688b7186ea335bf1693746fe5713cc93c95854d6388b8ed4ad643fddf5b5ace093f7dcd9038dd58e087bf2da1f04dfb4c5571660af
  languageName: node
  linkType: hard

"esbuild@npm:^0.21.3":
  version: 0.21.5
  resolution: "esbuild@npm:0.21.5"
  dependencies:
    "@esbuild/aix-ppc64": 0.21.5
    "@esbuild/android-arm": 0.21.5
    "@esbuild/android-arm64": 0.21.5
    "@esbuild/android-x64": 0.21.5
    "@esbuild/darwin-arm64": 0.21.5
    "@esbuild/darwin-x64": 0.21.5
    "@esbuild/freebsd-arm64": 0.21.5
    "@esbuild/freebsd-x64": 0.21.5
    "@esbuild/linux-arm": 0.21.5
    "@esbuild/linux-arm64": 0.21.5
    "@esbuild/linux-ia32": 0.21.5
    "@esbuild/linux-loong64": 0.21.5
    "@esbuild/linux-mips64el": 0.21.5
    "@esbuild/linux-ppc64": 0.21.5
    "@esbuild/linux-riscv64": 0.21.5
    "@esbuild/linux-s390x": 0.21.5
    "@esbuild/linux-x64": 0.21.5
    "@esbuild/netbsd-x64": 0.21.5
    "@esbuild/openbsd-x64": 0.21.5
    "@esbuild/sunos-x64": 0.21.5
    "@esbuild/win32-arm64": 0.21.5
    "@esbuild/win32-ia32": 0.21.5
    "@esbuild/win32-x64": 0.21.5
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 2911c7b50b23a9df59a7d6d4cdd3a4f85855787f374dce751148dbb13305e0ce7e880dde1608c2ab7a927fc6cec3587b80995f7fc87a64b455f8b70b55fd8ec1
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1, escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 47b029c83de01b0d17ad99ed766347b974b0d628e848de404018f3abee728e987da0d2d370ad4574aa3d5b5bfc368754fd085d69a30f8e75903486ec4b5b709e
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^5.0.0":
  version: 5.0.0
  resolution: "escape-string-regexp@npm:5.0.0"
  checksum: 20daabe197f3cb198ec28546deebcf24b3dbb1a5a269184381b3116d12f0532e06007f4bc8da25669d6a7f8efb68db0758df4cd981f57bc5b57f521a3e12c59e
  languageName: node
  linkType: hard

"escodegen@npm:^2.1.0":
  version: 2.1.0
  resolution: "escodegen@npm:2.1.0"
  dependencies:
    esprima: ^4.0.1
    estraverse: ^5.2.0
    esutils: ^2.0.2
    source-map: ~0.6.1
  dependenciesMeta:
    source-map:
      optional: true
  bin:
    escodegen: bin/escodegen.js
    esgenerate: bin/esgenerate.js
  checksum: 096696407e161305cd05aebb95134ad176708bc5cb13d0dcc89a5fcbb959b8ed757e7f2591a5f8036f8f4952d4a724de0df14cd419e29212729fa6df5ce16bf6
  languageName: node
  linkType: hard

"eslint-compat-utils@npm:^0.5.0, eslint-compat-utils@npm:^0.5.1":
  version: 0.5.1
  resolution: "eslint-compat-utils@npm:0.5.1"
  dependencies:
    semver: ^7.5.4
  peerDependencies:
    eslint: ">=6.0.0"
  checksum: beccf2a5bd7c7974e3584b269f8a02667c83bca64cfd4c866f3055867f187e78b00ee826721765bdee9b13efaaa248f8068c581f7bb05803e8f47abb116e68fc
  languageName: node
  linkType: hard

"eslint-compat-utils@npm:^0.6.0":
  version: 0.6.4
  resolution: "eslint-compat-utils@npm:0.6.4"
  dependencies:
    semver: ^7.5.4
  peerDependencies:
    eslint: ">=6.0.0"
  checksum: f011a5267e75bef5610afd6ca032b699775efe78f3b15ead24231ee65a380aee87103526cb051498e6d3f3d10dee260e70dac61df66c0149569f95f11db72301
  languageName: node
  linkType: hard

"eslint-config-flat-gitignore@npm:^0.3.0":
  version: 0.3.0
  resolution: "eslint-config-flat-gitignore@npm:0.3.0"
  dependencies:
    "@eslint/compat": ^1.1.1
    find-up-simple: ^1.0.0
  peerDependencies:
    eslint: ^9.5.0
  checksum: 80d1c843ba3c7a1284035c7baeaed595146d051acf29d872eb4f318d0d2835a8aa767098bf1ad6622200f6eb1904e4fd00b6c87aed59c43856272a1ae76d331f
  languageName: node
  linkType: hard

"eslint-flat-config-utils@npm:^0.4.0":
  version: 0.4.0
  resolution: "eslint-flat-config-utils@npm:0.4.0"
  dependencies:
    pathe: ^1.1.2
  checksum: f0ac432d157d5a03bf52e7f4d314b6851733ceb2dd0bcb39820f239d98060204f6751693d7e7fa7227e2302521c9a29f72701949b7df156d823bf492fce5d3c6
  languageName: node
  linkType: hard

"eslint-formatting-reporter@npm:^0.0.0":
  version: 0.0.0
  resolution: "eslint-formatting-reporter@npm:0.0.0"
  dependencies:
    prettier-linter-helpers: ^1.0.0
  peerDependencies:
    eslint: ">=8.40.0"
  checksum: be163c22982dbe267900d32f4a802dab8b49a9c731a7b825db33ea80a03e1b0478bc871d1c18637ce580f88da41d09ac7377e2b80d84dc3aad9b4d1db28b10d7
  languageName: node
  linkType: hard

"eslint-import-resolver-node@npm:^0.3.9":
  version: 0.3.9
  resolution: "eslint-import-resolver-node@npm:0.3.9"
  dependencies:
    debug: ^3.2.7
    is-core-module: ^2.13.0
    resolve: ^1.22.4
  checksum: 439b91271236b452d478d0522a44482e8c8540bf9df9bd744062ebb89ab45727a3acd03366a6ba2bdbcde8f9f718bab7fe8db64688aca75acf37e04eafd25e22
  languageName: node
  linkType: hard

"eslint-json-compat-utils@npm:^0.2.1":
  version: 0.2.1
  resolution: "eslint-json-compat-utils@npm:0.2.1"
  dependencies:
    esquery: ^1.6.0
  peerDependencies:
    eslint: "*"
    jsonc-eslint-parser: ^2.4.0
  peerDependenciesMeta:
    "@eslint/json":
      optional: true
  checksum: 79ac7edba0bbc3501b919f555407be9d7cb2cb74a6444f61aa4ec36be997ddade046163d0f29f02ed00960d8c384b268c116bb2380506914b38da70040b1b54f
  languageName: node
  linkType: hard

"eslint-merge-processors@npm:^0.1.0":
  version: 0.1.0
  resolution: "eslint-merge-processors@npm:0.1.0"
  peerDependencies:
    eslint: "*"
  checksum: ace4d897d9e0d9a7d4eafbf956bd399ab55245d67a4b6ed59c13d52a3c42c3b326d0493240e41da37b4bf30e005a9fe3346453bb84b6f3fe2bea04862edfa119
  languageName: node
  linkType: hard

"eslint-parser-plain@npm:^0.1.0":
  version: 0.1.0
  resolution: "eslint-parser-plain@npm:0.1.0"
  checksum: 8a6b61dd8335f2b0aac609256d4f03faf021aa5b96a64478da1f9304ece2935b2ed9ce0f529a082b14281061ec40f642b2ae01b5b4c4dd32d744085abb5cc030
  languageName: node
  linkType: hard

"eslint-plugin-antfu@npm:^2.7.0":
  version: 2.7.0
  resolution: "eslint-plugin-antfu@npm:2.7.0"
  dependencies:
    "@antfu/utils": ^0.7.10
  peerDependencies:
    eslint: "*"
  checksum: 3fd7f95b12d74efb05e137dd264b68c0028959561cfeda3f00c1e0eff74bac0ba1a8915f7d376480831afa93c8410dba2160718caf5c7b37005b0be2a799d636
  languageName: node
  linkType: hard

"eslint-plugin-command@npm:^0.2.6":
  version: 0.2.6
  resolution: "eslint-plugin-command@npm:0.2.6"
  dependencies:
    "@es-joy/jsdoccomment": ^0.48.0
  peerDependencies:
    eslint: "*"
  checksum: 86122cd3d8432639be8431e64c7a33ad8e77fc6723f2951d1eea1f1f1058613e7b7bf775cd521eb6fcb27afe1c066b5f8dc207c087114bce4a8ba7cf3b497279
  languageName: node
  linkType: hard

"eslint-plugin-es-x@npm:^7.8.0":
  version: 7.8.0
  resolution: "eslint-plugin-es-x@npm:7.8.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.1.2
    "@eslint-community/regexpp": ^4.11.0
    eslint-compat-utils: ^0.5.1
  peerDependencies:
    eslint: ">=8"
  checksum: c30fc6bd94f86781eaf34dec59e7d52ee68b8a12305ae76222d8d0ff6cc0a5c94e8306ed079b4234d64f7464bcd162a5fef59e7cc69a978ba77950e0395c79f8
  languageName: node
  linkType: hard

"eslint-plugin-format@npm:^0.1.3":
  version: 0.1.3
  resolution: "eslint-plugin-format@npm:0.1.3"
  dependencies:
    "@dprint/formatter": ^0.3.0
    "@dprint/markdown": ^0.17.8
    "@dprint/toml": ^0.6.3
    eslint-formatting-reporter: ^0.0.0
    eslint-parser-plain: ^0.1.0
    prettier: ^3.4.1
    synckit: ^0.9.2
  peerDependencies:
    eslint: ^8.40.0 || ^9.0.0
  checksum: 5ffda3506093a9f3837146dcb04161969ba3cf4e7ad8738b15bfaf8cdd1695f9c3d95e2c1dfdd2f321b6fe0eb703a000cb38ee322218dc3290af963bb50927dd
  languageName: node
  linkType: hard

"eslint-plugin-import-x@npm:^4.4.3":
  version: 4.5.0
  resolution: "eslint-plugin-import-x@npm:4.5.0"
  dependencies:
    "@typescript-eslint/scope-manager": ^8.1.0
    "@typescript-eslint/utils": ^8.1.0
    debug: ^4.3.4
    doctrine: ^3.0.0
    eslint-import-resolver-node: ^0.3.9
    get-tsconfig: ^4.7.3
    is-glob: ^4.0.3
    minimatch: ^9.0.3
    semver: ^7.6.3
    stable-hash: ^0.0.4
    tslib: ^2.6.3
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
  checksum: 16d5d86c717d4d34f220fd980f22befb01f635691094f3cf043c7a028dab1e967649e47dd5e50aae0ea9f352ca4fb470f12493b0c0fca8cdb6d0d79133a11e15
  languageName: node
  linkType: hard

"eslint-plugin-jsdoc@npm:^50.6.0":
  version: 50.6.0
  resolution: "eslint-plugin-jsdoc@npm:50.6.0"
  dependencies:
    "@es-joy/jsdoccomment": ~0.49.0
    are-docs-informative: ^0.0.2
    comment-parser: 1.4.1
    debug: ^4.3.6
    escape-string-regexp: ^4.0.0
    espree: ^10.1.0
    esquery: ^1.6.0
    parse-imports: ^2.1.1
    semver: ^7.6.3
    spdx-expression-parse: ^4.0.0
    synckit: ^0.9.1
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0 || ^9.0.0
  checksum: 012528306fa67cf76ef6d202d59d1c1834aa33f450950a30eae50805d244d90c4d8a3a2887a508be32f07992239d96fab880b7f5123e6dd1a05239759b5af00e
  languageName: node
  linkType: hard

"eslint-plugin-jsonc@npm:^2.18.2":
  version: 2.18.2
  resolution: "eslint-plugin-jsonc@npm:2.18.2"
  dependencies:
    "@eslint-community/eslint-utils": ^4.2.0
    eslint-compat-utils: ^0.6.0
    eslint-json-compat-utils: ^0.2.1
    espree: ^9.6.1
    graphemer: ^1.4.0
    jsonc-eslint-parser: ^2.0.4
    natural-compare: ^1.4.0
    synckit: ^0.6.0
  peerDependencies:
    eslint: ">=6.0.0"
  checksum: 713754181222f2b6d867a45ec75f235eca9acf24367707c7fc0fa667722478142c80a7b0eae5a928cd0bb81ef0078fc6654f1008779ed91a89daeed50f002b93
  languageName: node
  linkType: hard

"eslint-plugin-n@npm:^17.14.0":
  version: 17.15.0
  resolution: "eslint-plugin-n@npm:17.15.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.4.1
    enhanced-resolve: ^5.17.1
    eslint-plugin-es-x: ^7.8.0
    get-tsconfig: ^4.8.1
    globals: ^15.11.0
    ignore: ^5.3.2
    minimatch: ^9.0.5
    semver: ^7.6.3
  peerDependencies:
    eslint: ">=8.23.0"
  checksum: 08cd1a8047393d9f75f149520354932a2b543d250ad6f806971c3a2b3e7603854be79ea1e8cf5db2bb2e66fac909972281e806c05294be6aba0990158c2e419a
  languageName: node
  linkType: hard

"eslint-plugin-no-only-tests@npm:^3.3.0":
  version: 3.3.0
  resolution: "eslint-plugin-no-only-tests@npm:3.3.0"
  checksum: 1b3a88e392113240758405966047ef40dd742fbd828f3c8d02a207125edaa5303ef9a0319a778551bd88789110423221fff4e9db02896c20836389b13c27b32e
  languageName: node
  linkType: hard

"eslint-plugin-perfectionist@npm:^4.1.2":
  version: 4.2.0
  resolution: "eslint-plugin-perfectionist@npm:4.2.0"
  dependencies:
    "@typescript-eslint/types": ^8.17.0
    "@typescript-eslint/utils": ^8.17.0
    natural-orderby: ^5.0.0
  peerDependencies:
    eslint: ">=8.0.0"
  checksum: 7b602804f7d0b08189645bfcd16942c0a76a00f229d5f386bfc8fca2f4f04682bd35d42de95a0ef0aec0b31438dc61be75e2de8093977c50024337779c99f20b
  languageName: node
  linkType: hard

"eslint-plugin-regexp@npm:^2.7.0":
  version: 2.7.0
  resolution: "eslint-plugin-regexp@npm:2.7.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.2.0
    "@eslint-community/regexpp": ^4.11.0
    comment-parser: ^1.4.0
    jsdoc-type-pratt-parser: ^4.0.0
    refa: ^0.12.1
    regexp-ast-analysis: ^0.7.1
    scslre: ^0.3.0
  peerDependencies:
    eslint: ">=8.44.0"
  checksum: 8df259c085288e887d5b832cb4c3295cc863e7a917029069930c6ff9228fdc2c4d5252d7bc9bcfda8a87501ce9ccfb08b211547397c5888a32152afbe80718b5
  languageName: node
  linkType: hard

"eslint-plugin-toml@npm:^0.11.1":
  version: 0.11.1
  resolution: "eslint-plugin-toml@npm:0.11.1"
  dependencies:
    debug: ^4.1.1
    eslint-compat-utils: ^0.5.0
    lodash: ^4.17.19
    toml-eslint-parser: ^0.10.0
  peerDependencies:
    eslint: ">=6.0.0"
  checksum: b6c7f2435045ee396ac14af76d61f4ccc66269e42e61a3d53e51544f745fd974deba37917fc475ed6abe7cc6002d070898c1f2d81eb4a3d17982b69d03e4a8be
  languageName: node
  linkType: hard

"eslint-plugin-unicorn@npm:^56.0.1":
  version: 56.0.1
  resolution: "eslint-plugin-unicorn@npm:56.0.1"
  dependencies:
    "@babel/helper-validator-identifier": ^7.24.7
    "@eslint-community/eslint-utils": ^4.4.0
    ci-info: ^4.0.0
    clean-regexp: ^1.0.0
    core-js-compat: ^3.38.1
    esquery: ^1.6.0
    globals: ^15.9.0
    indent-string: ^4.0.0
    is-builtin-module: ^3.2.1
    jsesc: ^3.0.2
    pluralize: ^8.0.0
    read-pkg-up: ^7.0.1
    regexp-tree: ^0.1.27
    regjsparser: ^0.10.0
    semver: ^7.6.3
    strip-indent: ^3.0.0
  peerDependencies:
    eslint: ">=8.56.0"
  checksum: 2c27f46beb1c2681749cc2591508c9904aa02de6147cc41fa06e602763d2dbe4a9587f13a8c1dcd870a85c6ddcdaa803d3fcce0e3551435afa28fd266365ada7
  languageName: node
  linkType: hard

"eslint-plugin-unused-imports@npm:^4.1.4":
  version: 4.1.4
  resolution: "eslint-plugin-unused-imports@npm:4.1.4"
  peerDependencies:
    "@typescript-eslint/eslint-plugin": ^8.0.0-0 || ^7.0.0 || ^6.0.0 || ^5.0.0
    eslint: ^9.0.0 || ^8.0.0
  peerDependenciesMeta:
    "@typescript-eslint/eslint-plugin":
      optional: true
  checksum: 1f4ce3e3972699345513840f3af1b783033dbc3a3e85b62ce12b3f6a89fd8c92afe46d0c00af40bacb14465445983ba0ccc326a6fd5132553061fb0e47bcba19
  languageName: node
  linkType: hard

"eslint-plugin-vue@npm:^9.31.0":
  version: 9.32.0
  resolution: "eslint-plugin-vue@npm:9.32.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.4.0
    globals: ^13.24.0
    natural-compare: ^1.4.0
    nth-check: ^2.1.1
    postcss-selector-parser: ^6.0.15
    semver: ^7.6.3
    vue-eslint-parser: ^9.4.3
    xml-name-validator: ^4.0.0
  peerDependencies:
    eslint: ^6.2.0 || ^7.0.0 || ^8.0.0 || ^9.0.0
  checksum: 14320cdb5f8c9f7cb23cad4595c1fd22f61c0b68ef1b7bd9071060d9f5bd32277ad7d022a7bf49fcb4c302d32a60045dbe1e734461edde6af160e0ade2a06410
  languageName: node
  linkType: hard

"eslint-plugin-yml@npm:^1.15.0":
  version: 1.16.0
  resolution: "eslint-plugin-yml@npm:1.16.0"
  dependencies:
    debug: ^4.3.2
    eslint-compat-utils: ^0.6.0
    lodash: ^4.17.21
    natural-compare: ^1.4.0
    yaml-eslint-parser: ^1.2.1
  peerDependencies:
    eslint: ">=6.0.0"
  checksum: ef128ab445a476127fd16157ce2eb80f2d673a83bb10515b6299d61b1305b6e5715c448e7b6319c8906a6ffc6de78edfb109039f97913ab8a3d791bad6f0861d
  languageName: node
  linkType: hard

"eslint-processor-vue-blocks@npm:^0.1.2":
  version: 0.1.2
  resolution: "eslint-processor-vue-blocks@npm:0.1.2"
  peerDependencies:
    "@vue/compiler-sfc": ^3.3.0
    eslint: ^8.50.0 || ^9.0.0
  checksum: ae56d2a62e64e073ea062b691b87a176ee4ff1a8c3d9c9ebf248a103a9d01dad9910a9d897b9be49d8717a2171923d28aaff48be9fd3aec7dbde9f15b46a59b2
  languageName: node
  linkType: hard

"eslint-scope@npm:^7.1.1":
  version: 7.2.2
  resolution: "eslint-scope@npm:7.2.2"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^5.2.0
  checksum: ec97dbf5fb04b94e8f4c5a91a7f0a6dd3c55e46bfc7bbcd0e3138c3a76977570e02ed89a1810c778dcd72072ff0e9621ba1379b4babe53921d71e2e4486fda3e
  languageName: node
  linkType: hard

"eslint-scope@npm:^8.2.0":
  version: 8.2.0
  resolution: "eslint-scope@npm:8.2.0"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^5.2.0
  checksum: 750eff4672ca2bf274ec0d1bbeae08aadd53c1907d5c6aff5564d8e047a5f49afa8ae6eee333cab637fd3ebcab2141659d8f2f040f6fdc982b0f61f8bf03136f
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.0.0, eslint-visitor-keys@npm:^3.3.0, eslint-visitor-keys@npm:^3.4.1, eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 36e9ef87fca698b6fd7ca5ca35d7b2b6eeaaf106572e2f7fd31c12d3bfdaccdb587bba6d3621067e5aece31c8c3a348b93922ab8f7b2cbc6aaab5e1d89040c60
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.2.0":
  version: 4.2.0
  resolution: "eslint-visitor-keys@npm:4.2.0"
  checksum: 779c604672b570bb4da84cef32f6abb085ac78379779c1122d7879eade8bb38ae715645324597cf23232d03cef06032c9844d25c73625bc282a5bfd30247e5b5
  languageName: node
  linkType: hard

"eslint@npm:^9.16.0":
  version: 9.16.0
  resolution: "eslint@npm:9.16.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.2.0
    "@eslint-community/regexpp": ^4.12.1
    "@eslint/config-array": ^0.19.0
    "@eslint/core": ^0.9.0
    "@eslint/eslintrc": ^3.2.0
    "@eslint/js": 9.16.0
    "@eslint/plugin-kit": ^0.2.3
    "@humanfs/node": ^0.16.6
    "@humanwhocodes/module-importer": ^1.0.1
    "@humanwhocodes/retry": ^0.4.1
    "@types/estree": ^1.0.6
    "@types/json-schema": ^7.0.15
    ajv: ^6.12.4
    chalk: ^4.0.0
    cross-spawn: ^7.0.5
    debug: ^4.3.2
    escape-string-regexp: ^4.0.0
    eslint-scope: ^8.2.0
    eslint-visitor-keys: ^4.2.0
    espree: ^10.3.0
    esquery: ^1.5.0
    esutils: ^2.0.2
    fast-deep-equal: ^3.1.3
    file-entry-cache: ^8.0.0
    find-up: ^5.0.0
    glob-parent: ^6.0.2
    ignore: ^5.2.0
    imurmurhash: ^0.1.4
    is-glob: ^4.0.0
    json-stable-stringify-without-jsonify: ^1.0.1
    lodash.merge: ^4.6.2
    minimatch: ^3.1.2
    natural-compare: ^1.4.0
    optionator: ^0.9.3
  peerDependencies:
    jiti: "*"
  peerDependenciesMeta:
    jiti:
      optional: true
  bin:
    eslint: bin/eslint.js
  checksum: d7b77caed2e319dba9bdf5fd3275c643332e4c79fcfe62cf19031fc430c27fe691daa718474d29a1050b83348085f8df50e04f260e081e5b1fbee1d2ca9c5c74
  languageName: node
  linkType: hard

"espree@npm:^10.0.1, espree@npm:^10.1.0, espree@npm:^10.3.0":
  version: 10.3.0
  resolution: "espree@npm:10.3.0"
  dependencies:
    acorn: ^8.14.0
    acorn-jsx: ^5.3.2
    eslint-visitor-keys: ^4.2.0
  checksum: 63e8030ff5a98cea7f8b3e3a1487c998665e28d674af08b9b3100ed991670eb3cbb0e308c4548c79e03762753838fbe530c783f17309450d6b47a889fee72bef
  languageName: node
  linkType: hard

"espree@npm:^9.0.0, espree@npm:^9.3.1, espree@npm:^9.6.1":
  version: 9.6.1
  resolution: "espree@npm:9.6.1"
  dependencies:
    acorn: ^8.9.0
    acorn-jsx: ^5.3.2
    eslint-visitor-keys: ^3.4.1
  checksum: eb8c149c7a2a77b3f33a5af80c10875c3abd65450f60b8af6db1bfcfa8f101e21c1e56a561c6dc13b848e18148d43469e7cd208506238554fb5395a9ea5a1ab9
  languageName: node
  linkType: hard

"esprima@npm:^4.0.1":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: b45bc805a613dbea2835278c306b91aff6173c8d034223fa81498c77dcbce3b2931bf6006db816f62eacd9fd4ea975dfd85a5b7f3c6402cfd050d4ca3c13a628
  languageName: node
  linkType: hard

"esquery@npm:^1.4.0, esquery@npm:^1.5.0, esquery@npm:^1.6.0":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: ^5.1.0
  checksum: 08ec4fe446d9ab27186da274d979558557fbdbbd10968fa9758552482720c54152a5640e08b9009e5a30706b66aba510692054d4129d32d0e12e05bbc0b96fb2
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: ebc17b1a33c51cef46fdc28b958994b1dc43cd2e86237515cbc3b4e5d2be6a811b2315d0a1a4d9d340b6d2308b15322f5c8291059521cc5f4802f65e7ec32837
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0, estraverse@npm:^5.3.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 072780882dc8416ad144f8fe199628d2b3e7bbc9989d9ed43795d2c90309a2047e6bc5979d7e2322a341163d22cfad9e21f4110597fe487519697389497e4e2b
  languageName: node
  linkType: hard

"estree-walker@npm:^2.0.2":
  version: 2.0.2
  resolution: "estree-walker@npm:2.0.2"
  checksum: 6151e6f9828abe2259e57f5fd3761335bb0d2ebd76dc1a01048ccee22fabcfef3c0859300f6d83ff0d1927849368775ec5a6d265dde2f6de5a1be1721cd94efc
  languageName: node
  linkType: hard

"estree-walker@npm:^3.0.3":
  version: 3.0.3
  resolution: "estree-walker@npm:3.0.3"
  dependencies:
    "@types/estree": ^1.0.0
  checksum: a65728d5727b71de172c5df323385755a16c0fdab8234dc756c3854cfee343261ddfbb72a809a5660fac8c75d960bb3e21aa898c2d7e9b19bb298482ca58a3af
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 22b5b08f74737379a840b8ed2036a5fb35826c709ab000683b092d9054e5c2a82c27818f12604bfc2a9a76b90b6834ef081edbc1c7ae30d1627012e067c6ec87
  languageName: node
  linkType: hard

"eventemitter3@npm:^5.0.1":
  version: 5.0.1
  resolution: "eventemitter3@npm:5.0.1"
  checksum: 543d6c858ab699303c3c32e0f0f47fc64d360bf73c3daf0ac0b5079710e340d6fe9f15487f94e66c629f5f82cd1a8678d692f3dbb6f6fcd1190e1b97fcad36f8
  languageName: node
  linkType: hard

"execa@npm:~8.0.1":
  version: 8.0.1
  resolution: "execa@npm:8.0.1"
  dependencies:
    cross-spawn: ^7.0.3
    get-stream: ^8.0.1
    human-signals: ^5.0.0
    is-stream: ^3.0.0
    merge-stream: ^2.0.0
    npm-run-path: ^5.1.0
    onetime: ^6.0.0
    signal-exit: ^4.1.0
    strip-final-newline: ^3.0.0
  checksum: cac1bf86589d1d9b73bdc5dda65c52012d1a9619c44c526891956745f7b366ca2603d29fe3f7460bacc2b48c6eab5d6a4f7afe0534b31473d3708d1265545e1f
  languageName: node
  linkType: hard

"expect-type@npm:^1.1.0":
  version: 1.1.0
  resolution: "expect-type@npm:1.1.0"
  checksum: 65d25ec10bca32bcf650dcfe734532acc4b7a73677c656f299a7cbed273b5c4d6a3dab11af76f452645d54a95c4ef39fc73772f2c8eb6684ba35672958d6f3b3
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.1
  resolution: "exponential-backoff@npm:3.1.1"
  checksum: 3d21519a4f8207c99f7457287291316306255a328770d320b401114ec8481986e4e467e854cb9914dd965e0a1ca810a23ccb559c642c88f4c7f55c55778a9b48
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-diff@npm:^1.1.2":
  version: 1.3.0
  resolution: "fast-diff@npm:1.3.0"
  checksum: d22d371b994fdc8cce9ff510d7b8dc4da70ac327bcba20df607dd5b9cae9f908f4d1028f5fe467650f058d1e7270235ae0b8230809a262b4df587a3b3aa216c3
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.12, fast-glob@npm:^3.3.2":
  version: 3.3.2
  resolution: "fast-glob@npm:3.3.2"
  dependencies:
    "@nodelib/fs.stat": ^2.0.2
    "@nodelib/fs.walk": ^1.2.3
    glob-parent: ^5.1.2
    merge2: ^1.3.0
    micromatch: ^4.0.4
  checksum: 900e4979f4dbc3313840078419245621259f349950411ca2fa445a2f9a1a6d98c3b5e7e0660c5ccd563aa61abe133a21765c6c0dec8e57da1ba71d8000b05ec1
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 92cfec0a8dfafd9c7a15fba8f2cc29cd0b62b85f056d99ce448bbcd9f708e18ab2764bda4dd5158364f4145a7c72788538994f0d1787b956ef0d1062b0f7c24c
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.17.1
  resolution: "fastq@npm:1.17.1"
  dependencies:
    reusify: ^1.0.4
  checksum: a8c5b26788d5a1763f88bae56a8ddeee579f935a831c5fe7a8268cea5b0a91fbfe705f612209e02d639b881d7b48e461a50da4a10cfaa40da5ca7cc9da098d88
  languageName: node
  linkType: hard

"fdir@npm:^6.4.2":
  version: 6.4.2
  resolution: "fdir@npm:6.4.2"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 517ad31c495f1c0778238eef574a7818788efaaf2ce1969ffa18c70793e2951a9763dfa2e6720b8fcef615e602a3cbb47f9b8aea9da0b02147579ab36043f22f
  languageName: node
  linkType: hard

"file-entry-cache@npm:^8.0.0":
  version: 8.0.0
  resolution: "file-entry-cache@npm:8.0.0"
  dependencies:
    flat-cache: ^4.0.0
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"file-saver@npm:^2.0.5":
  version: 2.0.5
  resolution: "file-saver@npm:2.0.5"
  checksum: c62d96e5cebc58b4bdf3ae8a60d5cf9607ad82f75f798c33a4ee63435ac2203002584d5256a2a780eda7feb5e19dc3b6351c2212e58b3f529e63d265a7cc79f7
  languageName: node
  linkType: hard

"file-type@npm:^16.5.4":
  version: 16.5.4
  resolution: "file-type@npm:16.5.4"
  dependencies:
    readable-web-to-node-stream: ^3.0.0
    strtok3: ^6.2.4
    token-types: ^4.1.1
  checksum: d983c0f36491c57fcb6cc70fcb02c36d6b53f312a15053263e1924e28ca8314adf0db32170801ad777f09432c32155f31715ceaee66310947731588120d7ec27
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: b4abfbca3839a3d55e4ae5ec62e131e2e356bf4859ce8480c64c4876100f4df292a63e5bb1618e1d7460282ca2b305653064f01654474aa35c68000980f17798
  languageName: node
  linkType: hard

"find-up-simple@npm:^1.0.0":
  version: 1.0.0
  resolution: "find-up-simple@npm:1.0.0"
  checksum: 91c3d51c1111b5eb4e6e6d71d21438f6571a37a69dc288d4222b98996756e2f472fa5393a4dddb5e1a84929405d87e86f4bdce798ba84ee513b79854960ec140
  languageName: node
  linkType: hard

"find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: ^5.0.0
    path-exists: ^4.0.0
  checksum: 4c172680e8f8c1f78839486e14a43ef82e9decd0e74145f40707cc42e7420506d5ec92d9a11c22bd2c48fb0c384ea05dd30e10dd152fefeec6f2f75282a8b844
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: ^6.0.0
    path-exists: ^4.0.0
  checksum: 07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"flat-cache@npm:^4.0.0":
  version: 4.0.1
  resolution: "flat-cache@npm:4.0.1"
  dependencies:
    flatted: ^3.2.9
    keyv: ^4.5.4
  checksum: 899fc86bf6df093547d76e7bfaeb900824b869d7d457d02e9b8aae24836f0a99fbad79328cfd6415ee8908f180699bf259dc7614f793447cb14f707caf5996f6
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.2
  resolution: "flatted@npm:3.3.2"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.15.6":
  version: 1.15.9
  resolution: "follow-redirects@npm:1.15.9"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 859e2bacc7a54506f2bf9aacb10d165df78c8c1b0ceb8023f966621b233717dab56e8d08baadc3ad3b9db58af290413d585c999694b7c146aaf2616340c3d2a6
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3":
  version: 0.3.3
  resolution: "for-each@npm:0.3.3"
  dependencies:
    is-callable: ^1.1.3
  checksum: 6c48ff2bc63362319c65e2edca4a8e1e3483a2fabc72fbe7feaf8c73db94fc7861bd53bc02c8a66a0c1dd709da6b04eec42e0abdd6b40ce47305ae92a25e5d28
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.0
  resolution: "foreground-child@npm:3.3.0"
  dependencies:
    cross-spawn: ^7.0.0
    signal-exit: ^4.0.1
  checksum: 1989698488f725b05b26bc9afc8a08f08ec41807cd7b92ad85d96004ddf8243fd3e79486b8348c64a3011ae5cc2c9f0936af989e1f28339805d8bc178a75b451
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.1
  resolution: "form-data@npm:4.0.1"
  dependencies:
    asynckit: ^0.4.0
    combined-stream: ^1.0.8
    mime-types: ^2.1.12
  checksum: ccee458cd5baf234d6b57f349fe9cc5f9a2ea8fd1af5ecda501a18fd1572a6dd3bf08a49f00568afd995b6a65af34cb8dec083cf9d582c4e621836499498dd84
  languageName: node
  linkType: hard

"fs-extra@npm:^10.0.0":
  version: 10.1.0
  resolution: "fs-extra@npm:10.1.0"
  dependencies:
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: dc94ab37096f813cc3ca12f0f1b5ad6744dfed9ed21e953d72530d103cea193c2f81584a39e9dee1bea36de5ee66805678c0dddc048e8af1427ac19c00fffc50
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: ^7.0.3
  checksum: 8722a41109130851d979222d3ec88aabaceeaaf8f57b2a8f744ef8bd2d1ce95453b04a61daa0078822bc5cd21e008814f06fe6586f56fef511e71b8d2394d802
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2, fsevents@npm:~2.3.3":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: latest
  checksum: 11e6ea6fea15e42461fc55b4b0e4a0a3c654faa567f1877dbd353f39156f69def97a69936d1746619d656c4b93de2238bf731f6085a03a50cabf287c9d024317
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@~2.3.2#~builtin<compat/fsevents>, fsevents@patch:fsevents@~2.3.3#~builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#~builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: latest
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 2b0ff4ce708d99715ad14a6d1f894e2a83242e4a52ccfcefaee5e40050562e5f6dafc1adbb4ce2d4ab47279a45dc736ab91ea5042d843c3c092820dfe032efb1
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: c3f1f5ba20f4e962efb71344ce0a40722163e85bee2101ce25f88214e78182d2d2476aa85ef37950c579eb6cf6ee811c17b3101bb84004bb75655f3e33f3fdb5
  languageName: node
  linkType: hard

"fuse.js@npm:^7.0.0":
  version: 7.0.0
  resolution: "fuse.js@npm:7.0.0"
  checksum: d15750efec1808370c0cae92ec9473aa7261c59bca1f15f1cf60039ba6f804b8f95340b5cabd83a4ef55839c1034764856e0128e443921f072aa0d8a20e4cacf
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-east-asian-width@npm:^1.0.0":
  version: 1.3.0
  resolution: "get-east-asian-width@npm:1.3.0"
  checksum: 757a34c7a46ff385e2775f96f9d3e553f6b6666a8898fb89040d36a1010fba692332772945606a7d4b0f0c6afb84cd394e75d5477c56e1f00f1eb79603b0aecc
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.1.3, get-intrinsic@npm:^1.2.1, get-intrinsic@npm:^1.2.2, get-intrinsic@npm:^1.2.4":
  version: 1.2.5
  resolution: "get-intrinsic@npm:1.2.5"
  dependencies:
    call-bind-apply-helpers: ^1.0.0
    dunder-proto: ^1.0.0
    es-define-property: ^1.0.1
    es-errors: ^1.3.0
    function-bind: ^1.1.2
    gopd: ^1.2.0
    has-symbols: ^1.1.0
    hasown: ^2.0.2
  checksum: 4578a7ca15d9e1fc6706f32597c4c75eaeb8bb92b251253ebf42c70acc95be03d5ab5d680e28a9986c71207713670da4ac5096103f351cc77cb8413d9f847ae2
  languageName: node
  linkType: hard

"get-stream@npm:^8.0.1":
  version: 8.0.1
  resolution: "get-stream@npm:8.0.1"
  checksum: 01e3d3cf29e1393f05f44d2f00445c5f9ec3d1c49e8179b31795484b9c117f4c695e5e07b88b50785d5c8248a788c85d9913a79266fc77e3ef11f78f10f1b974
  languageName: node
  linkType: hard

"get-tsconfig@npm:^4.7.3, get-tsconfig@npm:^4.7.5, get-tsconfig@npm:^4.8.1":
  version: 4.8.1
  resolution: "get-tsconfig@npm:4.8.1"
  dependencies:
    resolve-pkg-maps: ^1.0.0
  checksum: 12df01672e691d2ff6db8cf7fed1ddfef90ed94a5f3d822c63c147a26742026d582acd86afcd6f65db67d809625d17dd7f9d34f4d3f38f69bc2f48e19b2bdd5b
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: ^4.0.1
  checksum: f4f2bfe2425296e8a47e36864e4f42be38a996db40420fe434565e4480e3322f18eb37589617a98640c5dc8fdec1a387007ee18dbb1f3f5553409c34d17f425e
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: ^4.0.3
  checksum: c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10, glob@npm:^10.3.3, glob@npm:^10.3.7":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: ^3.1.0
    jackspeak: ^3.1.2
    minimatch: ^9.0.4
    minipass: ^7.1.2
    package-json-from-dist: ^1.0.0
    path-scurry: ^1.11.1
  bin:
    glob: dist/esm/bin.mjs
  checksum: 0bc725de5e4862f9f387fd0f2b274baf16850dcd2714502ccf471ee401803997983e2c05590cb65f9675a3c6f2a58e7a53f9e365704108c6ad3cbf1d60934c4a
  languageName: node
  linkType: hard

"globals@npm:^13.24.0":
  version: 13.24.0
  resolution: "globals@npm:13.24.0"
  dependencies:
    type-fest: ^0.20.2
  checksum: 56066ef058f6867c04ff203b8a44c15b038346a62efbc3060052a1016be9f56f4cf0b2cd45b74b22b81e521a889fc7786c73691b0549c2f3a6e825b3d394f43c
  languageName: node
  linkType: hard

"globals@npm:^14.0.0":
  version: 14.0.0
  resolution: "globals@npm:14.0.0"
  checksum: 534b8216736a5425737f59f6e6a5c7f386254560c9f41d24a9227d60ee3ad4a9e82c5b85def0e212e9d92162f83a92544be4c7fd4c902cb913736c10e08237ac
  languageName: node
  linkType: hard

"globals@npm:^15.11.0, globals@npm:^15.12.0, globals@npm:^15.13.0, globals@npm:^15.9.0":
  version: 15.13.0
  resolution: "globals@npm:15.13.0"
  checksum: 3f98514ce25a21150b246fbd63aeaeb271a93b3340cf7f4f6e9934d3b37dbb4b0fddef9c470183097dcfd2e8757bb86bbae701588f0e376667d8d9d6f665db3b
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.1.0, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: cc6d8e655e360955bdccaca51a12a474268f95bb793fc3e1f2bdadb075f28bfd1fd988dab872daf77a61d78cbaf13744bc8727a17cfb1d150d76047d805375f3
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: ac85f94da92d8eb6b7f5a8b20ce65e43d66761c55ce85ac96df6865308390da45a8d3f0296dd3a663de65d30ba497bd46c696cc1e248c72b13d6d567138a4fc7
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: bab8f0be9b568857c7bec9fda95a89f87b783546d02951c40c33f84d05bb7da3fd10f863a9beb901463669b6583173a8c8cc6d6b306ea2b9b9d5d3d943c3a673
  languageName: node
  linkType: hard

"gzip-size@npm:^6.0.0":
  version: 6.0.0
  resolution: "gzip-size@npm:6.0.0"
  dependencies:
    duplexer: ^0.1.2
  checksum: 2df97f359696ad154fc171dcb55bc883fe6e833bca7a65e457b9358f3cb6312405ed70a8da24a77c1baac0639906cd52358dc0ce2ec1a937eaa631b934c94194
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-bigints@npm:1.0.2"
  checksum: 390e31e7be7e5c6fe68b81babb73dfc35d413604d7ee5f56da101417027a4b4ce6a27e46eff97ad040c835b5d228676eae99a9b5c3bc0e23c8e81a49241ff45b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: ^1.0.0
  checksum: fcbb246ea2838058be39887935231c6d5788babed499d0e9d0cc5737494c48aba4fe17ba1449e0d0fbbb1e36175442faa37f9c427ae357d6ccb1d895fbcd3de3
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: b2316c7302a0e8ba3aaba215f834e96c22c86f192e7310bdf689dd0e6999510c89b00fbc5742571507cebf25764d68c988b3a0da217369a73596191ac0ce694b
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.0, has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: ^1.0.3
  checksum: 999d60bb753ad714356b2c6c87b7fb74f32463b8426e159397da4bde5bca7e598ab1073f4d8d4deafac297f2eb311484cd177af242776bf05f0d11565680468d
  languageName: node
  linkType: hard

"hasown@npm:^2.0.0, hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: ^1.1.2
  checksum: e8516f776a15149ca6c6ed2ae3110c417a00b62260e222590e54aa367cbcd6ed99122020b37b7fbdf05748df57b265e70095d7bf35a47660587619b15ffb93db
  languageName: node
  linkType: hard

"he@npm:^1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 3d4d6babccccd79c5c5a3f929a68af33360d6445587d628087f39a965079d84f18ce9c3d3f917ee1e3978916fc833bb8b29377c3b403f919426f91bc6965e7a7
  languageName: node
  linkType: hard

"hookable@npm:^5.5.3":
  version: 5.5.3
  resolution: "hookable@npm:5.5.3"
  checksum: df659977888398649b6ef8c4470719e7e8384a1d939a6587e332e86fd55b3881806e2f8aaebaabdb4f218f74b83b98f2110e143df225e16d62a39dc271e7e288
  languageName: node
  linkType: hard

"hosted-git-info@npm:^2.1.4":
  version: 2.8.9
  resolution: "hosted-git-info@npm:2.8.9"
  checksum: c955394bdab888a1e9bb10eb33029e0f7ce5a2ac7b3f158099dc8c486c99e73809dca609f5694b223920ca2174db33d32b12f9a2a47141dc59607c29da5a62dd
  languageName: node
  linkType: hard

"html-encoding-sniffer@npm:^4.0.0":
  version: 4.0.0
  resolution: "html-encoding-sniffer@npm:4.0.0"
  dependencies:
    whatwg-encoding: ^3.1.1
  checksum: 3339b71dab2723f3159a56acf541ae90a408ce2d11169f00fe7e0c4663d31d6398c8a4408b504b4eec157444e47b084df09b3cb039c816660f0dd04846b8957d
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 83ac0bc60b17a3a36f9953e7be55e5c8f41acc61b22583060e8dedc9dd5e3607c823a88d0926f9150e571f90946835c7fe150732801010845c72cd8bbff1a236
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: 2.0.0
    inherits: 2.0.4
    setprototypeof: 1.2.0
    statuses: 2.0.1
    toidentifier: 1.0.1
  checksum: 9b0a3782665c52ce9dc658a0d1560bcb0214ba5699e4ea15aefb2a496e2ca83db03ebc42e1cce4ac1f413e4e0d2d736a3fd755772c556a9a06853ba2a0b7d920
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0, http-proxy-agent@npm:^7.0.2":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: ^7.1.0
    debug: ^4.3.4
  checksum: 670858c8f8f3146db5889e1fa117630910101db601fff7d5a8aa637da0abedf68c899f03d3451cac2f83bcc4c3d2dabf339b3aa00ff8080571cceb02c3ce02f3
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1, https-proxy-agent@npm:^7.0.5":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: ^7.1.2
    debug: 4
  checksum: b882377a120aa0544846172e5db021fa8afbf83fea2a897d397bd2ddd8095ab268c24bc462f40a15f2a8c600bf4aa05ce52927f70038d4014e68aefecfa94e8d
  languageName: node
  linkType: hard

"human-signals@npm:^5.0.0":
  version: 5.0.0
  resolution: "human-signals@npm:5.0.0"
  checksum: 6504560d5ed91444f16bea3bd9dfc66110a339442084e56c3e7fa7bbdf3f406426d6563d662bdce67064b165eac31eeabfc0857ed170aaa612cf14ec9f9a464c
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3"
  checksum: bd9f120f5a5b306f0bc0b9ae1edeb1577161503f5f8252a20f1a9e56ef8775c9959fd01c55f2d3a39d9a8abaf3e30c1abeb1895f367dcbbe0a8fd1c9ca01c4f6
  languageName: node
  linkType: hard

"iconv-lite@npm:0.6.3, iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"ieee754@npm:^1.2.1":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 5144c0c9815e54ada181d80a0b810221a253562422e7c6c3a60b1901154184f49326ec239d618c416c1c5945a2e197107aee8d986a3dd836b53dffefd99b5e7e
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0, ignore@npm:^5.2.4, ignore@npm:^5.3.1, ignore@npm:^5.3.2":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 2acfd32a573260ea522ea0bfeff880af426d68f6831f973129e2ba7363f422923cf53aab62f8369cbf4667c7b25b6f8a3761b34ecdb284ea18e87a5262a865be
  languageName: node
  linkType: hard

"immutable@npm:^4.0.0":
  version: 4.3.7
  resolution: "immutable@npm:4.3.7"
  checksum: 1c50eb053bb300796551604afff554066f041aa8e15926cf98f6d11d9736b62ad12531c06515dd96375258653878b4736f8051cd20b640f5f976d09fa640e3ec
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1":
  version: 3.3.0
  resolution: "import-fresh@npm:3.3.0"
  dependencies:
    parent-module: ^1.0.0
    resolve-from: ^4.0.0
  checksum: 2cacfad06e652b1edc50be650f7ec3be08c5e5a6f6d12d035c440a42a8cc028e60a5b99ca08a77ab4d6b1346da7d971915828f33cdab730d3d42f08242d09baa
  languageName: node
  linkType: hard

"importx@npm:^0.4.3":
  version: 0.4.4
  resolution: "importx@npm:0.4.4"
  dependencies:
    bundle-require: ^5.0.0
    debug: ^4.3.6
    esbuild: ^0.20.2 || ^0.21.0 || ^0.22.0 || ^0.23.0
    jiti: 2.0.0-beta.3
    jiti-v1: "npm:jiti@^1.21.6"
    pathe: ^1.1.2
    tsx: ^4.19.0
  checksum: 8aa0ce1226005c0eae1ab6b2b045ed9b029158de4223fe2fa4da8971ba774997929f7db1961d6a15b93084716a0f0e0f0871f6652431701f8d2813c10202478e
  languageName: node
  linkType: hard

"importx@npm:^0.5.0":
  version: 0.5.0
  resolution: "importx@npm:0.5.0"
  dependencies:
    bundle-require: ^5.0.0
    debug: ^4.3.7
    esbuild: ^0.20.2 || ^0.21.0 || ^0.22.0 || ^0.23.0
    jiti: ^2.0.0
    pathe: ^1.1.2
    tsx: ^4.19.1
  checksum: 2d31655c4b6dcd296ed343bf7df5068757129d34892977286c3c9dd55d9c79ca5f06131dbd5797475e1ba33644b477a55e0c60e545e0047262a5c16da8cbec8b
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 7cae75c8cd9a50f57dadd77482359f659eaebac0319dd9368bcd1714f55e65badd6929ca58569da2b6494ef13fdd5598cd700b1eba23f8b79c5f19d195a3ecf7
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 824cfb9929d031dabf059bebfe08cf3137365e112019086ed3dcff6a0a7b698cb80cf67ccccde0e25b9e2d7527aa6cc1fed1ac490c752162496caba3e6699612
  languageName: node
  linkType: hard

"inherits@npm:2.0.4, inherits@npm:^2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4a48a733847879d6cf6691860a6b1e3f0f4754176e4d71494c41f3475553768b10f84b5ce1d40fbd0e34e6bfbb864ee35858ad4dd2cf31e02fc4a154b724d7f1
  languageName: node
  linkType: hard

"ini@npm:^1.3.4":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: dfd98b0ca3a4fc1e323e38a6c8eb8936e31a97a918d3b377649ea15bdb15d481207a0dda1021efbd86b464cae29a0d33c1d7dcaf6c5672bee17fa849bc50a1b3
  languageName: node
  linkType: hard

"internal-slot@npm:^1.0.4":
  version: 1.0.7
  resolution: "internal-slot@npm:1.0.7"
  dependencies:
    es-errors: ^1.3.0
    hasown: ^2.0.0
    side-channel: ^1.0.4
  checksum: cadc5eea5d7d9bc2342e93aae9f31f04c196afebb11bde97448327049f492cd7081e18623ae71388aac9cd237b692ca3a105be9c68ac39c1dec679d7409e33eb
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: 1.1.0
    sprintf-js: ^1.1.3
  checksum: aa15f12cfd0ef5e38349744e3654bae649a34c3b10c77a674a167e99925d1549486c5b14730eebce9fea26f6db9d5e42097b00aa4f9f612e68c79121c71652dc
  languageName: node
  linkType: hard

"is-arguments@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-arguments@npm:1.1.1"
  dependencies:
    call-bind: ^1.0.2
    has-tostringtag: ^1.0.0
  checksum: 7f02700ec2171b691ef3e4d0e3e6c0ba408e8434368504bb593d0d7c891c0dbfda6d19d30808b904a6cb1929bca648c061ba438c39f296c2a8ca083229c49f27
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.2, is-array-buffer@npm:^3.0.4":
  version: 3.0.4
  resolution: "is-array-buffer@npm:3.0.4"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.2.1
  checksum: e4e3e6ef0ff2239e75371d221f74bc3c26a03564a22efb39f6bb02609b598917ddeecef4e8c877df2a25888f247a98198959842a5e73236bc7f22cabdf6351a7
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: eef4417e3c10e60e2c810b6084942b3ead455af16c4509959a27e490e7aee87cfb3f38e01bbde92220b528a0ee1a18d52b787e1458ee86174d8c7f0e58cd488f
  languageName: node
  linkType: hard

"is-bigint@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-bigint@npm:1.1.0"
  dependencies:
    has-bigints: ^1.0.2
  checksum: ee1544f0e664f253306786ed1dce494b8cf242ef415d6375d8545b4d8816b0f054bd9f948a8988ae2c6325d1c28260dd02978236b2f7b8fb70dfc4838a6c9fa7
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: ^2.0.0
  checksum: 84192eb88cff70d320426f35ecd63c3d6d495da9d805b19bc65b518984b7c0760280e57dbf119b7e9be6b161784a5a673ab2c6abe83abb5198a432232ad5b35c
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.2.0":
  version: 1.2.0
  resolution: "is-boolean-object@npm:1.2.0"
  dependencies:
    call-bind: ^1.0.7
    has-tostringtag: ^1.0.2
  checksum: cebc780cc3881dfb0c6c933e308f6a8eccf07ef92a7ea533fb2ee4fb7d704473b476f0b345fea4f2f45fe70937ef568a2f450eb6000d08b99350d87280927ff8
  languageName: node
  linkType: hard

"is-builtin-module@npm:^3.2.1":
  version: 3.2.1
  resolution: "is-builtin-module@npm:3.2.1"
  dependencies:
    builtin-modules: ^3.3.0
  checksum: e8f0ffc19a98240bda9c7ada84d846486365af88d14616e737d280d378695c8c448a621dcafc8332dbf0fcd0a17b0763b845400709963fa9151ddffece90ae88
  languageName: node
  linkType: hard

"is-callable@npm:^1.1.3":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 61fd57d03b0d984e2ed3720fb1c7a897827ea174bd44402878e059542ea8c4aeedee0ea0985998aa5cc2736b2fa6e271c08587addb5b3959ac52cf665173d1ac
  languageName: node
  linkType: hard

"is-core-module@npm:^2.13.0":
  version: 2.15.1
  resolution: "is-core-module@npm:2.15.1"
  dependencies:
    hasown: ^2.0.2
  checksum: df134c168115690724b62018c37b2f5bba0d5745fa16960b329c5a00883a8bea6a5632fdb1e3efcce237c201826ba09f93197b7cd95577ea56b0df335be23633
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5":
  version: 1.0.5
  resolution: "is-date-object@npm:1.0.5"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: baa9077cdf15eb7b58c79398604ca57379b2fc4cf9aa7a9b9e295278648f628c9b201400c01c5e0f7afae56507d741185730307cbe7cad3b9f90a77e5ee342fc
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^4.0.0":
  version: 4.0.0
  resolution: "is-fullwidth-code-point@npm:4.0.0"
  checksum: 8ae89bf5057bdf4f57b346fb6c55e9c3dd2549983d54191d722d5c739397a903012cc41a04ee3403fd872e811243ef91a7c5196da7b5841dc6b6aae31a264a8d
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^5.0.0":
  version: 5.0.0
  resolution: "is-fullwidth-code-point@npm:5.0.0"
  dependencies:
    get-east-asian-width: ^1.0.0
  checksum: 8dfb2d2831b9e87983c136f5c335cd9d14c1402973e357a8ff057904612ed84b8cba196319fabedf9aefe4639e14fe3afe9d9966d1d006ebeb40fe1fed4babe5
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: ^2.1.1
  checksum: d381c1319fcb69d341cc6e6c7cd588e17cd94722d9a32dbd60660b993c4fb7d0f19438674e68dfec686d09b7c73139c9166b47597f846af387450224a8101ab4
  languageName: node
  linkType: hard

"is-map@npm:^2.0.2, is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: e6ce5f6380f32b141b3153e6ba9074892bbbbd655e92e7ba5ff195239777e767a976dcd4e22f864accaf30e53ebf961ab1995424aef91af68788f0591b7396cc
  languageName: node
  linkType: hard

"is-number-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-number-object@npm:1.1.0"
  dependencies:
    call-bind: ^1.0.7
    has-tostringtag: ^1.0.2
  checksum: 965f91493e5c02a44bb9c5d8dd4ae40da20bd9bd1cff9cd92e2f2e66a486935a0a01f8a4744eab033c450888f01a4ec3226e1c75bbcff973ce12d06ed79eb17b
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-potential-custom-element-name@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-potential-custom-element-name@npm:1.0.1"
  checksum: ced7bbbb6433a5b684af581872afe0e1767e2d1146b2207ca0068a648fb5cab9d898495d1ac0583524faaf24ca98176a7d9876363097c2d14fee6dd324f3a1ab
  languageName: node
  linkType: hard

"is-regex@npm:^1.1.4":
  version: 1.2.0
  resolution: "is-regex@npm:1.2.0"
  dependencies:
    call-bind: ^1.0.7
    gopd: ^1.1.0
    has-tostringtag: ^1.0.2
    hasown: ^2.0.2
  checksum: dd2693d71866850d1276815204a2629d28dc1d24bd56b734e57a39f56b777cd87030d57552e7093d91a2ac331d99af9dba49a0a641fa4e4435d40e944d4dde12
  languageName: node
  linkType: hard

"is-set@npm:^2.0.2, is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: 36e3f8c44bdbe9496c9689762cc4110f6a6a12b767c5d74c0398176aa2678d4467e3bf07595556f2dba897751bde1422480212b97d973c7b08a343100b0c0dfe
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.2":
  version: 1.0.3
  resolution: "is-shared-array-buffer@npm:1.0.3"
  dependencies:
    call-bind: ^1.0.7
  checksum: a4fff602c309e64ccaa83b859255a43bb011145a42d3f56f67d9268b55bc7e6d98a5981a1d834186ad3105d6739d21547083fe7259c76c0468483fc538e716d8
  languageName: node
  linkType: hard

"is-stream@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-stream@npm:3.0.0"
  checksum: 172093fe99119ffd07611ab6d1bcccfe8bc4aa80d864b15f43e63e54b7abc71e779acd69afdb854c4e2a67fdc16ae710e370eda40088d1cfc956a50ed82d8f16
  languageName: node
  linkType: hard

"is-string@npm:^1.0.7, is-string@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-string@npm:1.1.0"
  dependencies:
    call-bind: ^1.0.7
    has-tostringtag: ^1.0.2
  checksum: 1e330e9fe0984cdf37371f704f9babf9b56d50b1e9d2e6c19b8b78443be3e9771c33309b4aadde9ba2a8870769374538681e01f54113a335dd393c80a72e7d11
  languageName: node
  linkType: hard

"is-symbol@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-symbol@npm:1.1.0"
  dependencies:
    call-bind: ^1.0.7
    has-symbols: ^1.0.3
    safe-regex-test: ^1.0.3
  checksum: 3623c934c8e61ddd6ef0927a17eb3da3cb9a9894f2fb8a96d447887d085d43e5d8bb59a8f97e46b54a919fc3f8845df29686672ad693d028570627bc661bcb6c
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: f36aef758b46990e0d3c37269619c0a08c5b29428c0bb11ecba7f75203442d6c7801239c2f31314bc79199217ef08263787f3837d9e22610ad1da62970d6616d
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-weakset@npm:2.0.3"
  dependencies:
    call-bind: ^1.0.7
    get-intrinsic: ^1.2.4
  checksum: 8b6a20ee9f844613ff8f10962cfee49d981d584525f2357fee0a04dfbcde9fd607ed60cb6dab626dbcc470018ae6392e1ff74c0c1aced2d487271411ad9d85ae
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: bd5bbe4104438c4196ba58a54650116007fa0262eccef13a4c55b2e09a5b36b59f1e75b9fcc49883dd9d4953892e6fc007eef9e9155648ceea036e184b0f930a
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": ^8.0.2
    "@pkgjs/parseargs": ^0.11.0
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: be31027fc72e7cc726206b9f560395604b82e0fddb46c4cbf9f97d049bcef607491a5afc0699612eaa4213ca5be8fd3e1e7cd187b3040988b65c9489838a7c00
  languageName: node
  linkType: hard

"jiti-v1@npm:jiti@^1.21.6":
  version: 1.21.6
  resolution: "jiti@npm:1.21.6"
  bin:
    jiti: bin/jiti.js
  checksum: 9ea4a70a7bb950794824683ed1c632e2ede26949fbd348e2ba5ec8dc5efa54dc42022d85ae229cadaa60d4b95012e80ea07d625797199b688cc22ab0e8891d32
  languageName: node
  linkType: hard

"jiti@npm:2.0.0-beta.3":
  version: 2.0.0-beta.3
  resolution: "jiti@npm:2.0.0-beta.3"
  bin:
    jiti: lib/jiti-cli.mjs
  checksum: 29dcbdb2e7837083c994d52387226625a05eecccb6d2073e91683ee33b79dd950622beb8113941db8f06b4aeaf7c431b6403194910e94684cfc5ea1739e71f90
  languageName: node
  linkType: hard

"jiti@npm:^2.0.0":
  version: 2.4.1
  resolution: "jiti@npm:2.4.1"
  bin:
    jiti: lib/jiti-cli.mjs
  checksum: a716022218ead97e8b1288523482188b7ec428af5f7e805036d27d7ebeead1c7ad6541312d6db4fa4d0252cbbef7d32080bf0ea837f7dd8fcfdad579dd720f82
  languageName: node
  linkType: hard

"js-beautify@npm:^1.14.9":
  version: 1.15.1
  resolution: "js-beautify@npm:1.15.1"
  dependencies:
    config-chain: ^1.1.13
    editorconfig: ^1.0.4
    glob: ^10.3.3
    js-cookie: ^3.0.5
    nopt: ^7.2.0
  bin:
    css-beautify: js/bin/css-beautify.js
    html-beautify: js/bin/html-beautify.js
    js-beautify: js/bin/js-beautify.js
  checksum: 0428ea358cdf169da15e11a8b63f13845ee39c707f3718a3ec515eb89d585544525aa8ba5306503431c61e33e1fbfebdf2af41c461e512619d8a2f8664d6c0c4
  languageName: node
  linkType: hard

"js-cookie@npm:^3.0.5":
  version: 3.0.5
  resolution: "js-cookie@npm:3.0.5"
  checksum: 2dbd2809c6180fbcf060c6957cb82dbb47edae0ead6bd71cbeedf448aa6b6923115003b995f7d3e3077bfe2cb76295ea6b584eb7196cca8ba0a09f389f64967a
  languageName: node
  linkType: hard

"js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 8a95213a5a77deb6cbe94d86340e8d9ace2b93bc367790b260101d2f36a2eaf4e4e22d9fa9cf459b38af3a32fb4190e638024cf82ec95ef708680e405ea7cc78
  languageName: node
  linkType: hard

"js-tokens@npm:^9.0.1":
  version: 9.0.1
  resolution: "js-tokens@npm:9.0.1"
  checksum: 8b604020b1a550e575404bfdde4d12c11a7991ffe0c58a2cf3515b9a512992dc7010af788f0d8b7485e403d462d9e3d3b96c4ff03201550fdbb09e17c811e054
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: ^2.0.1
  bin:
    js-yaml: bin/js-yaml.js
  checksum: c7830dfd456c3ef2c6e355cc5a92e6700ceafa1d14bba54497b34a99f0376cecbb3e9ac14d3e5849b426d5a5140709a66237a8c991c675431271c4ce5504151a
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 944f924f2bd67ad533b3850eee47603eed0f6ae425fd1ee8c760f477e8c34a05f144c1bd4f5a5dd1963141dc79a2c55f89ccc5ab77d039e7077f3ad196b64965
  languageName: node
  linkType: hard

"jsdoc-type-pratt-parser@npm:^4.0.0, jsdoc-type-pratt-parser@npm:~4.1.0":
  version: 4.1.0
  resolution: "jsdoc-type-pratt-parser@npm:4.1.0"
  checksum: e7642a508b090b1bdf17775383000ed71013c38e1231c1e576e5374636e8baf7c3fae8bf0252f5e1d3397d95efd56e8c8a5dd1a0de76d05d1499cbcb3c325bc3
  languageName: node
  linkType: hard

"jsdom@npm:^25.0.1":
  version: 25.0.1
  resolution: "jsdom@npm:25.0.1"
  dependencies:
    cssstyle: ^4.1.0
    data-urls: ^5.0.0
    decimal.js: ^10.4.3
    form-data: ^4.0.0
    html-encoding-sniffer: ^4.0.0
    http-proxy-agent: ^7.0.2
    https-proxy-agent: ^7.0.5
    is-potential-custom-element-name: ^1.0.1
    nwsapi: ^2.2.12
    parse5: ^7.1.2
    rrweb-cssom: ^0.7.1
    saxes: ^6.0.0
    symbol-tree: ^3.2.4
    tough-cookie: ^5.0.0
    w3c-xmlserializer: ^5.0.0
    webidl-conversions: ^7.0.0
    whatwg-encoding: ^3.1.1
    whatwg-mimetype: ^4.0.0
    whatwg-url: ^14.0.0
    ws: ^8.18.0
    xml-name-validator: ^5.0.0
  peerDependencies:
    canvas: ^2.11.2
  peerDependenciesMeta:
    canvas:
      optional: true
  checksum: b637d28445d570014195b3c77d06e54ef69d1f807eaf61388cb470e4d9227244e7fe2e0c32b6df03ac4fe35f746d7c721672d9136ecebb49d2e61a04ab9628e0
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.0.2
  resolution: "jsesc@npm:3.0.2"
  bin:
    jsesc: bin/jsesc
  checksum: a36d3ca40574a974d9c2063bf68c2b6141c20da8f2a36bd3279fc802563f35f0527a6c828801295bdfb2803952cf2cf387786c2c90ed564f88d5782475abfe3c
  languageName: node
  linkType: hard

"jsesc@npm:~0.5.0":
  version: 0.5.0
  resolution: "jsesc@npm:0.5.0"
  bin:
    jsesc: bin/jsesc
  checksum: b8b44cbfc92f198ad972fba706ee6a1dfa7485321ee8c0b25f5cedd538dcb20cde3197de16a7265430fce8277a12db066219369e3d51055038946039f6e20e17
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 9026b03edc2847eefa2e37646c579300a1f3a4586cfb62bf857832b60c852042d0d6ae55d1afb8926163fa54c2b01d83ae24705f34990348bdac6273a29d4581
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 798ed4cf3354a2d9ccd78e86d2169515a0097a5c133337807cdf7f1fc32e1391d207ccfc276518cc1d7d8d4db93288b8a50ba4293d212ad1336e52a8ec0a941f
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: cff44156ddce9c67c44386ad5cddf91925fe06b1d217f2da9c4910d01f358c6e3989c4d5a02683c7a5667f9727ff05831f7aa8ae66c8ff691c556f0884d49215
  languageName: node
  linkType: hard

"json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 2a7436a93393830bce797d4626275152e37e877b265e94ca69c99e3d20c2b9dab021279146a39cdb700e71b2dd32a4cebd1514cd57cee102b1af906ce5040349
  languageName: node
  linkType: hard

"jsonc-eslint-parser@npm:^2.0.4, jsonc-eslint-parser@npm:^2.3.0, jsonc-eslint-parser@npm:^2.4.0":
  version: 2.4.0
  resolution: "jsonc-eslint-parser@npm:2.4.0"
  dependencies:
    acorn: ^8.5.0
    eslint-visitor-keys: ^3.0.0
    espree: ^9.0.0
    semver: ^7.3.5
  checksum: 495d8be340b464137db0bb25f8280deda2ad773cca6b8b5605325fddb50e8e317842a07ffdfa692b0adcba2d06e25d127087f2d703a63032923d1e67ee5a9efe
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: ^4.1.6
    universalify: ^2.0.0
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 7af3b8e1ac8fe7f1eccc6263c6ca14e1966fcbc74b618d3c78a0a2075579487547b94f72b7a1114e844a1e15bb00d440e5d1720bfc4612d790a6f285d5ea8354
  languageName: node
  linkType: hard

"kdbush@npm:^4.0.2":
  version: 4.0.2
  resolution: "kdbush@npm:4.0.2"
  checksum: 6782ef2cdaec9322376b9955a16b0163beda0cefa2f87da76e8970ade2572d8b63bec915347aaeac609484b0c6e84d7b591f229ef353b68b460238095bacde2d
  languageName: node
  linkType: hard

"keyv@npm:^4.5.4":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: 3.0.1
  checksum: 74a24395b1c34bd44ad5cb2b49140d087553e170625240b86755a6604cd65aa16efdbdeae5cdb17ba1284a0fbb25ad06263755dbc71b8d8b06f74232ce3cdd72
  languageName: node
  linkType: hard

"kleur@npm:^4.0.3":
  version: 4.1.5
  resolution: "kleur@npm:4.1.5"
  checksum: 1dc476e32741acf0b1b5b0627ffd0d722e342c1b0da14de3e8ae97821327ca08f9fb944542fb3c126d90ac5f27f9d804edbe7c585bf7d12ef495d115e0f22c12
  languageName: node
  linkType: hard

"kolorist@npm:^1.8.0":
  version: 1.8.0
  resolution: "kolorist@npm:1.8.0"
  checksum: b056de671acc8a17f1e78d6d46c47dae3e06481eabc9fed213dd9079a7454fd3a7ea1226ec718df81c9208877f7475d038ac27a400958fec278d975839e33643
  languageName: node
  linkType: hard

"ky@npm:^1.7.3":
  version: 1.7.3
  resolution: "ky@npm:1.7.3"
  checksum: 9088e809b2e9443be92a2db609020a821e119510ff3a299294cad6e60d79b345f2a24c3112c2816406287b7f1ccd08820cba9bf68259fc058c7c0d7e6e2381b5
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: ^1.2.1
    type-check: ~0.4.0
  checksum: 12c5021c859bd0f5248561bf139121f0358285ec545ebf48bb3d346820d5c61a4309535c7f387ed7d84361cf821e124ce346c6b7cef8ee09a67c1473b46d0fc4
  languageName: node
  linkType: hard

"lilconfig@npm:~3.1.3":
  version: 3.1.3
  resolution: "lilconfig@npm:3.1.3"
  checksum: 644eb10830350f9cdc88610f71a921f510574ed02424b57b0b3abb66ea725d7a082559552524a842f4e0272c196b88dfe1ff7d35ffcc6f45736777185cd67c9a
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"lint-staged@npm:^15.2.11":
  version: 15.2.11
  resolution: "lint-staged@npm:15.2.11"
  dependencies:
    chalk: ~5.3.0
    commander: ~12.1.0
    debug: ~4.4.0
    execa: ~8.0.1
    lilconfig: ~3.1.3
    listr2: ~8.2.5
    micromatch: ~4.0.8
    pidtree: ~0.6.0
    string-argv: ~0.3.2
    yaml: ~2.6.1
  bin:
    lint-staged: bin/lint-staged.js
  checksum: 9668e67791fd73aa91cf9268641a5b41191b4221ac9e4d34b51bb10088a082548aa5984d9b38066c3832e41f5a01bbb36aa41d9a8c96bbc775301cb5c9071e13
  languageName: node
  linkType: hard

"listr2@npm:~8.2.5":
  version: 8.2.5
  resolution: "listr2@npm:8.2.5"
  dependencies:
    cli-truncate: ^4.0.0
    colorette: ^2.0.20
    eventemitter3: ^5.0.1
    log-update: ^6.1.0
    rfdc: ^1.4.1
    wrap-ansi: ^9.0.0
  checksum: 0ca2387b067eb11bbe91863f36903f3a5a040790422a499cc1a15806d8497979e7d1990bd129061c0510906b2971eaa97a74a9635e3ec5abd5830c9749b655b9
  languageName: node
  linkType: hard

"load-tsconfig@npm:^0.2.3":
  version: 0.2.5
  resolution: "load-tsconfig@npm:0.2.5"
  checksum: 631740833c4a7157bb7b6eeae6e1afb6a6fac7416b7ba91bd0944d5c5198270af2d68bf8347af3cc2ba821adc4d83ef98f66278bd263bc284c863a09ec441503
  languageName: node
  linkType: hard

"local-pkg@npm:^0.5.0, local-pkg@npm:^0.5.1":
  version: 0.5.1
  resolution: "local-pkg@npm:0.5.1"
  dependencies:
    mlly: ^1.7.3
    pkg-types: ^1.2.1
  checksum: 478effb440780d412bff78ed80d1593d707a504931a7e5899d6570d207da1e661a6128c3087286ff964696a55c607c2bbd2bbe98377401c7d395891c160fa6e1
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: ^4.1.0
  checksum: 83e51725e67517287d73e1ded92b28602e3ae5580b301fe54bfb76c0c723e3f285b19252e375712316774cf52006cb236aed5704692c32db0d5d089b69696e30
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: ^5.0.0
  checksum: 72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: ad580b4bdbb7ca1f7abf7e1bce63a9a0b98e370cf40194b03380a46b4ed799c9573029599caebc1b14e3f24b111aef72b96674a56cfa105e0f5ac70546cdc005
  languageName: node
  linkType: hard

"lodash@npm:^4.17.19, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: eb835a2e51d381e561e508ce932ea50a8e5a68f4ebdd771ea240d3048244a8d13658acbd502cd4829768c56f2e16bdd4340b9ea141297d472517b83868e677f7
  languageName: node
  linkType: hard

"log-update@npm:^6.1.0":
  version: 6.1.0
  resolution: "log-update@npm:6.1.0"
  dependencies:
    ansi-escapes: ^7.0.0
    cli-cursor: ^5.0.0
    slice-ansi: ^7.1.0
    strip-ansi: ^7.1.0
    wrap-ansi: ^9.0.0
  checksum: 817a9ba6c5cbc19e94d6359418df8cfe8b3244a2903f6d53354e175e243a85b782dc6a98db8b5e457ee2f09542ca8916c39641b9cd3b0e6ef45e9481d50c918a
  languageName: node
  linkType: hard

"longest-streak@npm:^3.0.0":
  version: 3.1.0
  resolution: "longest-streak@npm:3.1.0"
  checksum: d7f952ed004cbdb5c8bcfc4f7f5c3d65449e6c5a9e9be4505a656e3df5a57ee125f284286b4bf8ecea0c21a7b3bf2b8f9001ad506c319b9815ad6a63a47d0fd0
  languageName: node
  linkType: hard

"loupe@npm:^3.1.0, loupe@npm:^3.1.2":
  version: 3.1.2
  resolution: "loupe@npm:3.1.2"
  checksum: 4a75bbe8877a1ced3603e08b1095cd6f4c987c50fe63719fdc3009029560f91e07a915e7f6eff1322bb62bfb2a2beeef06b13ccb3c12f81bda9f3674434dcab9
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 6476138d2125387a6d20f100608c2583d415a4f64a0fecf30c9e2dda976614f09cad4baa0842447bd37dd459a7bd27f57d9d8f8ce558805abd487c583f3d774a
  languageName: node
  linkType: hard

"magic-string-ast@npm:^0.6.2, magic-string-ast@npm:^0.6.3":
  version: 0.6.3
  resolution: "magic-string-ast@npm:0.6.3"
  dependencies:
    magic-string: ^0.30.13
  checksum: 6d9c447b417267c80a67c94d4a31c938f4c824ac29f0bca05618e2191dc579e70c617e40d37952b2df1b7515935ad434a6848d9e57b1424c6402a132aacd028e
  languageName: node
  linkType: hard

"magic-string@npm:^0.25.7":
  version: 0.25.9
  resolution: "magic-string@npm:0.25.9"
  dependencies:
    sourcemap-codec: ^1.4.8
  checksum: 9a0e55a15c7303fc360f9572a71cffba1f61451bc92c5602b1206c9d17f492403bf96f946dfce7483e66822d6b74607262e24392e87b0ac27b786e69a40e9b1a
  languageName: node
  linkType: hard

"magic-string@npm:^0.30.11, magic-string@npm:^0.30.12, magic-string@npm:^0.30.13, magic-string@npm:^0.30.14":
  version: 0.30.14
  resolution: "magic-string@npm:0.30.14"
  dependencies:
    "@jridgewell/sourcemap-codec": ^1.5.0
  checksum: 67b3b2d817a7c4e94cb63e2dcaffbeee3b76ff0798eeaee8159a6ff4faee30db824375b2cadbf43807b56c6802fe6373b40d02567d489593017012d74ec0b719
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": ^3.0.0
    cacache: ^19.0.1
    http-cache-semantics: ^4.1.1
    minipass: ^7.0.2
    minipass-fetch: ^4.0.0
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^1.0.0
    proc-log: ^5.0.0
    promise-retry: ^2.0.1
    ssri: ^12.0.0
  checksum: 6fb2fee6da3d98f1953b03d315826b5c5a4ea1f908481afc113782d8027e19f080c85ae998454de4e5f27a681d3ec58d57278f0868d4e0b736f51d396b661691
  languageName: node
  linkType: hard

"markdown-table@npm:^3.0.0":
  version: 3.0.4
  resolution: "markdown-table@npm:3.0.4"
  checksum: bc24b177cbb3ef170cb38c9f191476aa63f7236ebc8980317c5e91b5bf98c8fb471cf46d8920478c5e770d7f4337326f6b5b3efbf0687c2044fd332d7a64dfcb
  languageName: node
  linkType: hard

"mdast-util-find-and-replace@npm:^3.0.0":
  version: 3.0.1
  resolution: "mdast-util-find-and-replace@npm:3.0.1"
  dependencies:
    "@types/mdast": ^4.0.0
    escape-string-regexp: ^5.0.0
    unist-util-is: ^6.0.0
    unist-util-visit-parents: ^6.0.0
  checksum: 05d5c4ff02e31db2f8a685a13bcb6c3f44e040bd9dfa54c19a232af8de5268334c8755d79cb456ed4cced1300c4fb83e88444c7ae8ee9ff16869a580f29d08cd
  languageName: node
  linkType: hard

"mdast-util-from-markdown@npm:^2.0.0, mdast-util-from-markdown@npm:^2.0.1":
  version: 2.0.2
  resolution: "mdast-util-from-markdown@npm:2.0.2"
  dependencies:
    "@types/mdast": ^4.0.0
    "@types/unist": ^3.0.0
    decode-named-character-reference: ^1.0.0
    devlop: ^1.0.0
    mdast-util-to-string: ^4.0.0
    micromark: ^4.0.0
    micromark-util-decode-numeric-character-reference: ^2.0.0
    micromark-util-decode-string: ^2.0.0
    micromark-util-normalize-identifier: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
    unist-util-stringify-position: ^4.0.0
  checksum: 1ad19f48b30ac6e0cb756070c210c78ad93c26876edfb3f75127783bc6df8b9402016d8f3e9964f3d1d5430503138ec65c145e869438727e1aa7f3cebf228fba
  languageName: node
  linkType: hard

"mdast-util-gfm-autolink-literal@npm:^2.0.0":
  version: 2.0.1
  resolution: "mdast-util-gfm-autolink-literal@npm:2.0.1"
  dependencies:
    "@types/mdast": ^4.0.0
    ccount: ^2.0.0
    devlop: ^1.0.0
    mdast-util-find-and-replace: ^3.0.0
    micromark-util-character: ^2.0.0
  checksum: 5630b12e072d7004cb132231c94f667fb5813486779cb0dfb0a196d7ae0e048897a43b0b37e080017adda618ddfcbea1d7bf23c0fa31c87bfc683e0898ea1cfe
  languageName: node
  linkType: hard

"mdast-util-gfm-footnote@npm:^2.0.0":
  version: 2.0.0
  resolution: "mdast-util-gfm-footnote@npm:2.0.0"
  dependencies:
    "@types/mdast": ^4.0.0
    devlop: ^1.1.0
    mdast-util-from-markdown: ^2.0.0
    mdast-util-to-markdown: ^2.0.0
    micromark-util-normalize-identifier: ^2.0.0
  checksum: 45d26b40e7a093712e023105791129d76e164e2168d5268e113298a22de30c018162683fb7893cdc04ab246dac0087eed708b2a136d1d18ed2b32b3e0cae4a79
  languageName: node
  linkType: hard

"mdast-util-gfm-strikethrough@npm:^2.0.0":
  version: 2.0.0
  resolution: "mdast-util-gfm-strikethrough@npm:2.0.0"
  dependencies:
    "@types/mdast": ^4.0.0
    mdast-util-from-markdown: ^2.0.0
    mdast-util-to-markdown: ^2.0.0
  checksum: fe9b1d0eba9b791ff9001c008744eafe3dd7a81b085f2bf521595ce4a8e8b1b44764ad9361761ad4533af3e5d913d8ad053abec38172031d9ee32a8ebd1c7dbd
  languageName: node
  linkType: hard

"mdast-util-gfm-table@npm:^2.0.0":
  version: 2.0.0
  resolution: "mdast-util-gfm-table@npm:2.0.0"
  dependencies:
    "@types/mdast": ^4.0.0
    devlop: ^1.0.0
    markdown-table: ^3.0.0
    mdast-util-from-markdown: ^2.0.0
    mdast-util-to-markdown: ^2.0.0
  checksum: 063a627fd0993548fd63ca0c24c437baf91ba7d51d0a38820bd459bc20bf3d13d7365ef8d28dca99176dd5eb26058f7dde51190479c186dfe6af2e11202957c9
  languageName: node
  linkType: hard

"mdast-util-gfm-task-list-item@npm:^2.0.0":
  version: 2.0.0
  resolution: "mdast-util-gfm-task-list-item@npm:2.0.0"
  dependencies:
    "@types/mdast": ^4.0.0
    devlop: ^1.0.0
    mdast-util-from-markdown: ^2.0.0
    mdast-util-to-markdown: ^2.0.0
  checksum: 37db90c59b15330fc54d790404abf5ef9f2f83e8961c53666fe7de4aab8dd5e6b3c296b6be19797456711a89a27840291d8871ff0438e9b4e15c89d170efe072
  languageName: node
  linkType: hard

"mdast-util-gfm@npm:^3.0.0":
  version: 3.0.0
  resolution: "mdast-util-gfm@npm:3.0.0"
  dependencies:
    mdast-util-from-markdown: ^2.0.0
    mdast-util-gfm-autolink-literal: ^2.0.0
    mdast-util-gfm-footnote: ^2.0.0
    mdast-util-gfm-strikethrough: ^2.0.0
    mdast-util-gfm-table: ^2.0.0
    mdast-util-gfm-task-list-item: ^2.0.0
    mdast-util-to-markdown: ^2.0.0
  checksum: 62039d2f682ae3821ea1c999454863d31faf94d67eb9b746589c7e136076d7fb35fabc67e02f025c7c26fd7919331a0ee1aabfae24f565d9a6a9ebab3371c626
  languageName: node
  linkType: hard

"mdast-util-phrasing@npm:^4.0.0":
  version: 4.1.0
  resolution: "mdast-util-phrasing@npm:4.1.0"
  dependencies:
    "@types/mdast": ^4.0.0
    unist-util-is: ^6.0.0
  checksum: 3a97533e8ad104a422f8bebb34b3dde4f17167b8ed3a721cf9263c7416bd3447d2364e6d012a594aada40cac9e949db28a060bb71a982231693609034ed5324e
  languageName: node
  linkType: hard

"mdast-util-to-markdown@npm:^2.0.0":
  version: 2.1.2
  resolution: "mdast-util-to-markdown@npm:2.1.2"
  dependencies:
    "@types/mdast": ^4.0.0
    "@types/unist": ^3.0.0
    longest-streak: ^3.0.0
    mdast-util-phrasing: ^4.0.0
    mdast-util-to-string: ^4.0.0
    micromark-util-classify-character: ^2.0.0
    micromark-util-decode-string: ^2.0.0
    unist-util-visit: ^5.0.0
    zwitch: ^2.0.0
  checksum: 288d152bd50c00632e6e01c610bb904a220d1e226c8086c40627877959746f83ab0b872f4150cb7d910198953b1bf756e384ac3fee3e7b0ddb4517f9084c5803
  languageName: node
  linkType: hard

"mdast-util-to-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "mdast-util-to-string@npm:4.0.0"
  dependencies:
    "@types/mdast": ^4.0.0
  checksum: 35489fb5710d58cbc2d6c8b6547df161a3f81e0f28f320dfb3548a9393555daf07c310c0c497708e67ed4dfea4a06e5655799e7d631ca91420c288b4525d6c29
  languageName: node
  linkType: hard

"mdn-data@npm:2.12.2":
  version: 2.12.2
  resolution: "mdn-data@npm:2.12.2"
  checksum: 77f38c180292cfbbd41c06641a27940cc293c08f47faa98f80bf64f98bb1b2a804df371e864e31a1ea97bdf181c0b0f85a2d96d1a6261f43c427b32222f33f1f
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: af1b38516c28ec95d6b0826f6c8f276c58aec391f76be42aa07646b4e39d317723e869700933ca6995b056db4b09a78c92d5440dc23657e6764be5d28874bba1
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 6fa4dcc8d86629705cea944a4b88ef4cb0e07656ebf223fa287443256414283dd25d91c1cd84c77987f2aec5927af1a9db6085757cb43d90eb170ebf4b47f4f4
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"micromark-core-commonmark@npm:^1.0.0, micromark-core-commonmark@npm:^1.0.1":
  version: 1.1.0
  resolution: "micromark-core-commonmark@npm:1.1.0"
  dependencies:
    decode-named-character-reference: ^1.0.0
    micromark-factory-destination: ^1.0.0
    micromark-factory-label: ^1.0.0
    micromark-factory-space: ^1.0.0
    micromark-factory-title: ^1.0.0
    micromark-factory-whitespace: ^1.0.0
    micromark-util-character: ^1.0.0
    micromark-util-chunked: ^1.0.0
    micromark-util-classify-character: ^1.0.0
    micromark-util-html-tag-name: ^1.0.0
    micromark-util-normalize-identifier: ^1.0.0
    micromark-util-resolve-all: ^1.0.0
    micromark-util-subtokenize: ^1.0.0
    micromark-util-symbol: ^1.0.0
    micromark-util-types: ^1.0.1
    uvu: ^0.5.0
  checksum: c6dfedc95889cc73411cb222fc2330b9eda6d849c09c9fd9eb3cd3398af246167e9d3cdb0ae3ce9ae59dd34a14624c8330e380255d41279ad7350cf6c6be6c5b
  languageName: node
  linkType: hard

"micromark-core-commonmark@npm:^2.0.0":
  version: 2.0.2
  resolution: "micromark-core-commonmark@npm:2.0.2"
  dependencies:
    decode-named-character-reference: ^1.0.0
    devlop: ^1.0.0
    micromark-factory-destination: ^2.0.0
    micromark-factory-label: ^2.0.0
    micromark-factory-space: ^2.0.0
    micromark-factory-title: ^2.0.0
    micromark-factory-whitespace: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-chunked: ^2.0.0
    micromark-util-classify-character: ^2.0.0
    micromark-util-html-tag-name: ^2.0.0
    micromark-util-normalize-identifier: ^2.0.0
    micromark-util-resolve-all: ^2.0.0
    micromark-util-subtokenize: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: e49d78429baf72533a02d06ae83e5a24d4d547bc832173547ffbae93c0960a7dbf0d8896058301498fa4297f280070a5a66891e0e6160040d6c5ef9bc5d9cd51
  languageName: node
  linkType: hard

"micromark-extension-gfm-autolink-literal@npm:^1.0.0":
  version: 1.0.5
  resolution: "micromark-extension-gfm-autolink-literal@npm:1.0.5"
  dependencies:
    micromark-util-character: ^1.0.0
    micromark-util-sanitize-uri: ^1.0.0
    micromark-util-symbol: ^1.0.0
    micromark-util-types: ^1.0.0
  checksum: ec2f6bc4a3eb238c1b8be9744454ffbc2957e3d8a248697af5a26bb21479862300c0e40e0a92baf17c299ddf70d4bc4470d4eee112cd92322f87d81e45c2e83d
  languageName: node
  linkType: hard

"micromark-extension-gfm-autolink-literal@npm:^2.0.0":
  version: 2.1.0
  resolution: "micromark-extension-gfm-autolink-literal@npm:2.1.0"
  dependencies:
    micromark-util-character: ^2.0.0
    micromark-util-sanitize-uri: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: e00a570c70c837b9cbbe94b2c23b787f44e781cd19b72f1828e3453abca2a9fb600fa539cdc75229fa3919db384491063645086e02249481e6ff3ec2c18f767c
  languageName: node
  linkType: hard

"micromark-extension-gfm-footnote@npm:^1.0.0":
  version: 1.1.2
  resolution: "micromark-extension-gfm-footnote@npm:1.1.2"
  dependencies:
    micromark-core-commonmark: ^1.0.0
    micromark-factory-space: ^1.0.0
    micromark-util-character: ^1.0.0
    micromark-util-normalize-identifier: ^1.0.0
    micromark-util-sanitize-uri: ^1.0.0
    micromark-util-symbol: ^1.0.0
    micromark-util-types: ^1.0.0
    uvu: ^0.5.0
  checksum: c151a629ee1cd92363c018a50f926a002c944ac481ca72b3720b9529e9c20f1cbef98b0fefdcd2d594af37d0d9743673409cac488af0d2b194210fd16375dcb7
  languageName: node
  linkType: hard

"micromark-extension-gfm-footnote@npm:^2.0.0":
  version: 2.1.0
  resolution: "micromark-extension-gfm-footnote@npm:2.1.0"
  dependencies:
    devlop: ^1.0.0
    micromark-core-commonmark: ^2.0.0
    micromark-factory-space: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-normalize-identifier: ^2.0.0
    micromark-util-sanitize-uri: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: ac6fb039e98395d37b71ebff7c7a249aef52678b5cf554c89c4f716111d4be62ef99a5d715a5bd5d68fa549778c977d85cb671d1d8506dc8a3a1b46e867ae52f
  languageName: node
  linkType: hard

"micromark-extension-gfm-strikethrough@npm:^1.0.0":
  version: 1.0.7
  resolution: "micromark-extension-gfm-strikethrough@npm:1.0.7"
  dependencies:
    micromark-util-chunked: ^1.0.0
    micromark-util-classify-character: ^1.0.0
    micromark-util-resolve-all: ^1.0.0
    micromark-util-symbol: ^1.0.0
    micromark-util-types: ^1.0.0
    uvu: ^0.5.0
  checksum: 169e310a4408feade0df80180f60d48c5cc5b7070e5e75e0bbd914e9100273508162c4bb20b72d53081dc37f1ff5834b3afa137862576f763878552c03389811
  languageName: node
  linkType: hard

"micromark-extension-gfm-strikethrough@npm:^2.0.0":
  version: 2.1.0
  resolution: "micromark-extension-gfm-strikethrough@npm:2.1.0"
  dependencies:
    devlop: ^1.0.0
    micromark-util-chunked: ^2.0.0
    micromark-util-classify-character: ^2.0.0
    micromark-util-resolve-all: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: cdb7a38dd6eefb6ceb6792a44a6796b10f951e8e3e45b8579f599f43e7ae26ccd048c0aa7e441b3c29dd0c54656944fe6eb0098de2bc4b5106fbc0a42e9e016c
  languageName: node
  linkType: hard

"micromark-extension-gfm-table@npm:^1.0.0":
  version: 1.0.7
  resolution: "micromark-extension-gfm-table@npm:1.0.7"
  dependencies:
    micromark-factory-space: ^1.0.0
    micromark-util-character: ^1.0.0
    micromark-util-symbol: ^1.0.0
    micromark-util-types: ^1.0.0
    uvu: ^0.5.0
  checksum: 4853731285224e409d7e2c94c6ec849165093bff819e701221701aa7b7b34c17702c44f2f831e96b49dc27bb07e445b02b025561b68e62f5c3254415197e7af6
  languageName: node
  linkType: hard

"micromark-extension-gfm-table@npm:^2.0.0":
  version: 2.1.0
  resolution: "micromark-extension-gfm-table@npm:2.1.0"
  dependencies:
    devlop: ^1.0.0
    micromark-factory-space: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: 249d695f5f8bd222a0d8a774ec78ea2a2d624cb50a4d008092a54aa87dad1f9d540e151d29696cf849eb1cee380113c4df722aebb3b425a214832a2de5dea1d7
  languageName: node
  linkType: hard

"micromark-extension-gfm-tagfilter@npm:^1.0.0":
  version: 1.0.2
  resolution: "micromark-extension-gfm-tagfilter@npm:1.0.2"
  dependencies:
    micromark-util-types: ^1.0.0
  checksum: 7d2441df51f890c86f8e7cf7d331a570b69c8105fa1c2fc5b737cb739502c16c8ee01cf35550a8a78f89497c5dfacc97cf82d55de6274e8320f3aec25e2b0dd2
  languageName: node
  linkType: hard

"micromark-extension-gfm-tagfilter@npm:^2.0.0":
  version: 2.0.0
  resolution: "micromark-extension-gfm-tagfilter@npm:2.0.0"
  dependencies:
    micromark-util-types: ^2.0.0
  checksum: cf21552f4a63592bfd6c96ae5d64a5f22bda4e77814e3f0501bfe80e7a49378ad140f827007f36044666f176b3a0d5fea7c2e8e7973ce4b4579b77789f01ae95
  languageName: node
  linkType: hard

"micromark-extension-gfm-task-list-item@npm:^1.0.0":
  version: 1.0.5
  resolution: "micromark-extension-gfm-task-list-item@npm:1.0.5"
  dependencies:
    micromark-factory-space: ^1.0.0
    micromark-util-character: ^1.0.0
    micromark-util-symbol: ^1.0.0
    micromark-util-types: ^1.0.0
    uvu: ^0.5.0
  checksum: 929f05343d272cffb8008899289f4cffe986ef98fc622ebbd1aa4ff11470e6b32ed3e1f18cd294adb69cabb961a400650078f6c12b322cc515b82b5068b31960
  languageName: node
  linkType: hard

"micromark-extension-gfm-task-list-item@npm:^2.0.0":
  version: 2.1.0
  resolution: "micromark-extension-gfm-task-list-item@npm:2.1.0"
  dependencies:
    devlop: ^1.0.0
    micromark-factory-space: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: b1ad86a4e9d68d9ad536d94fb25a5182acbc85cc79318f4a6316034342f6a71d67983cc13f12911d0290fd09b2bda43cdabe8781a2d9cca2ebe0d421e8b2b8a4
  languageName: node
  linkType: hard

"micromark-extension-gfm@npm:^2.0.1":
  version: 2.0.3
  resolution: "micromark-extension-gfm@npm:2.0.3"
  dependencies:
    micromark-extension-gfm-autolink-literal: ^1.0.0
    micromark-extension-gfm-footnote: ^1.0.0
    micromark-extension-gfm-strikethrough: ^1.0.0
    micromark-extension-gfm-table: ^1.0.0
    micromark-extension-gfm-tagfilter: ^1.0.0
    micromark-extension-gfm-task-list-item: ^1.0.0
    micromark-util-combine-extensions: ^1.0.0
    micromark-util-types: ^1.0.0
  checksum: c4a917c16d7aa5d00d1767b5ce5f3b1a78c0de11dbd5c8f69d2545083568aa6bb13bd9d8e4c7fec5f4da10e7ed8344b15acffc843b33a615c17396a118bc2bc1
  languageName: node
  linkType: hard

"micromark-extension-gfm@npm:^3.0.0":
  version: 3.0.0
  resolution: "micromark-extension-gfm@npm:3.0.0"
  dependencies:
    micromark-extension-gfm-autolink-literal: ^2.0.0
    micromark-extension-gfm-footnote: ^2.0.0
    micromark-extension-gfm-strikethrough: ^2.0.0
    micromark-extension-gfm-table: ^2.0.0
    micromark-extension-gfm-tagfilter: ^2.0.0
    micromark-extension-gfm-task-list-item: ^2.0.0
    micromark-util-combine-extensions: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: 2060fa62666a09532d6b3a272d413bc1b25bbb262f921d7402795ac021e1362c8913727e33d7528d5b4ccaf26922ec51208c43f795a702964817bc986de886c9
  languageName: node
  linkType: hard

"micromark-factory-destination@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-destination@npm:1.1.0"
  dependencies:
    micromark-util-character: ^1.0.0
    micromark-util-symbol: ^1.0.0
    micromark-util-types: ^1.0.0
  checksum: 9e2b5fb5fedbf622b687e20d51eb3d56ae90c0e7ecc19b37bd5285ec392c1e56f6e21aa7cfcb3c01eda88df88fe528f3acb91a5f57d7f4cba310bc3cd7f824fa
  languageName: node
  linkType: hard

"micromark-factory-destination@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-destination@npm:2.0.1"
  dependencies:
    micromark-util-character: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: 9c4baa9ca2ed43c061bbf40ddd3d85154c2a0f1f485de9dea41d7dd2ad994ebb02034a003b2c1dbe228ba83a0576d591f0e90e0bf978713f84ee7d7f3aa98320
  languageName: node
  linkType: hard

"micromark-factory-label@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-label@npm:1.1.0"
  dependencies:
    micromark-util-character: ^1.0.0
    micromark-util-symbol: ^1.0.0
    micromark-util-types: ^1.0.0
    uvu: ^0.5.0
  checksum: fcda48f1287d9b148c562c627418a2ab759cdeae9c8e017910a0cba94bb759a96611e1fc6df33182e97d28fbf191475237298983bb89ef07d5b02464b1ad28d5
  languageName: node
  linkType: hard

"micromark-factory-label@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-label@npm:2.0.1"
  dependencies:
    devlop: ^1.0.0
    micromark-util-character: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: bd03f5a75f27cdbf03b894ddc5c4480fc0763061fecf9eb927d6429233c930394f223969a99472df142d570c831236134de3dc23245d23d9f046f9d0b623b5c2
  languageName: node
  linkType: hard

"micromark-factory-space@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-space@npm:1.1.0"
  dependencies:
    micromark-util-character: ^1.0.0
    micromark-util-types: ^1.0.0
  checksum: b58435076b998a7e244259a4694eb83c78915581206b6e7fc07b34c6abd36a1726ade63df8972fbf6c8fa38eecb9074f4e17be8d53f942e3b3d23d1a0ecaa941
  languageName: node
  linkType: hard

"micromark-factory-space@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-space@npm:2.0.1"
  dependencies:
    micromark-util-character: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: 1bd68a017c1a66f4787506660c1e1c5019169aac3b1cb075d49ac5e360e0b2065e984d4e1d6e9e52a9d44000f2fa1c98e66a743d7aae78b4b05616bf3242ed71
  languageName: node
  linkType: hard

"micromark-factory-title@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-title@npm:1.1.0"
  dependencies:
    micromark-factory-space: ^1.0.0
    micromark-util-character: ^1.0.0
    micromark-util-symbol: ^1.0.0
    micromark-util-types: ^1.0.0
  checksum: 4432d3dbc828c81f483c5901b0c6591a85d65a9e33f7d96ba7c3ae821617a0b3237ff5faf53a9152d00aaf9afb3a9f185b205590f40ed754f1d9232e0e9157b1
  languageName: node
  linkType: hard

"micromark-factory-title@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-title@npm:2.0.1"
  dependencies:
    micromark-factory-space: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: b4d2e4850a8ba0dff25ce54e55a3eb0d43dda88a16293f53953153288f9d84bcdfa8ca4606b2cfbb4f132ea79587bbb478a73092a349f893f5264fbcdbce2ee1
  languageName: node
  linkType: hard

"micromark-factory-whitespace@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-factory-whitespace@npm:1.1.0"
  dependencies:
    micromark-factory-space: ^1.0.0
    micromark-util-character: ^1.0.0
    micromark-util-symbol: ^1.0.0
    micromark-util-types: ^1.0.0
  checksum: ef0fa682c7d593d85a514ee329809dee27d10bc2a2b65217d8ef81173e33b8e83c549049764b1ad851adfe0a204dec5450d9d20a4ca8598f6c94533a73f73fcd
  languageName: node
  linkType: hard

"micromark-factory-whitespace@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-whitespace@npm:2.0.1"
  dependencies:
    micromark-factory-space: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: 67b3944d012a42fee9e10e99178254a04d48af762b54c10a50fcab988688799993efb038daf9f5dbc04001a97b9c1b673fc6f00e6a56997877ab25449f0c8650
  languageName: node
  linkType: hard

"micromark-util-character@npm:^1.0.0":
  version: 1.2.0
  resolution: "micromark-util-character@npm:1.2.0"
  dependencies:
    micromark-util-symbol: ^1.0.0
    micromark-util-types: ^1.0.0
  checksum: 089e79162a19b4a28731736246579ab7e9482ac93cd681c2bfca9983dcff659212ef158a66a5957e9d4b1dba957d1b87b565d85418a5b009f0294f1f07f2aaac
  languageName: node
  linkType: hard

"micromark-util-character@npm:^2.0.0":
  version: 2.1.1
  resolution: "micromark-util-character@npm:2.1.1"
  dependencies:
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: e9e409efe4f2596acd44587e8591b722bfc041c1577e8fe0d9c007a4776fb800f9b3637a22862ad2ba9489f4bdf72bb547fce5767dbbfe0a5e6760e2a21c6495
  languageName: node
  linkType: hard

"micromark-util-chunked@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-chunked@npm:1.1.0"
  dependencies:
    micromark-util-symbol: ^1.0.0
  checksum: c435bde9110cb595e3c61b7f54c2dc28ee03e6a57fa0fc1e67e498ad8bac61ee5a7457a2b6a73022ddc585676ede4b912d28dcf57eb3bd6951e54015e14dc20b
  languageName: node
  linkType: hard

"micromark-util-chunked@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-chunked@npm:2.0.1"
  dependencies:
    micromark-util-symbol: ^2.0.0
  checksum: f8cb2a67bcefe4bd2846d838c97b777101f0043b9f1de4f69baf3e26bb1f9885948444e3c3aec66db7595cad8173bd4567a000eb933576c233d54631f6323fe4
  languageName: node
  linkType: hard

"micromark-util-classify-character@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-classify-character@npm:1.1.0"
  dependencies:
    micromark-util-character: ^1.0.0
    micromark-util-symbol: ^1.0.0
    micromark-util-types: ^1.0.0
  checksum: 8499cb0bb1f7fb946f5896285fcca65cd742f66cd3e79ba7744792bd413ec46834f932a286de650349914d02e822946df3b55d03e6a8e1d245d1ddbd5102e5b0
  languageName: node
  linkType: hard

"micromark-util-classify-character@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-classify-character@npm:2.0.1"
  dependencies:
    micromark-util-character: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: 4d8bbe3a6dbf69ac0fc43516866b5bab019fe3f4568edc525d4feaaaf78423fa54e6b6732b5bccbeed924455279a3758ffc9556954aafb903982598a95a02704
  languageName: node
  linkType: hard

"micromark-util-combine-extensions@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-combine-extensions@npm:1.1.0"
  dependencies:
    micromark-util-chunked: ^1.0.0
    micromark-util-types: ^1.0.0
  checksum: ee78464f5d4b61ccb437850cd2d7da4d690b260bca4ca7a79c4bb70291b84f83988159e373b167181b6716cb197e309bc6e6c96a68cc3ba9d50c13652774aba9
  languageName: node
  linkType: hard

"micromark-util-combine-extensions@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-combine-extensions@npm:2.0.1"
  dependencies:
    micromark-util-chunked: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: 5d22fb9ee37e8143adfe128a72b50fa09568c2cc553b3c76160486c96dbbb298c5802a177a10a215144a604b381796071b5d35be1f2c2b2ee17995eda92f0c8e
  languageName: node
  linkType: hard

"micromark-util-decode-numeric-character-reference@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-decode-numeric-character-reference@npm:1.1.0"
  dependencies:
    micromark-util-symbol: ^1.0.0
  checksum: 4733fe75146e37611243f055fc6847137b66f0cde74d080e33bd26d0408c1d6f44cabc984063eee5968b133cb46855e729d555b9ff8d744652262b7b51feec73
  languageName: node
  linkType: hard

"micromark-util-decode-numeric-character-reference@npm:^2.0.0":
  version: 2.0.2
  resolution: "micromark-util-decode-numeric-character-reference@npm:2.0.2"
  dependencies:
    micromark-util-symbol: ^2.0.0
  checksum: ee11c8bde51e250e302050474c4a2adca094bca05c69f6cdd241af12df285c48c88d19ee6e022b9728281c280be16328904adca994605680c43af56019f4b0b6
  languageName: node
  linkType: hard

"micromark-util-decode-string@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-decode-string@npm:2.0.1"
  dependencies:
    decode-named-character-reference: ^1.0.0
    micromark-util-character: ^2.0.0
    micromark-util-decode-numeric-character-reference: ^2.0.0
    micromark-util-symbol: ^2.0.0
  checksum: e9546ae53f9b5a4f9aa6aaf3e750087100d3429485ca80dbacec99ff2bb15a406fa7d93784a0fc2fe05ad7296b9295e75160ef71faec9e90110b7be2ae66241a
  languageName: node
  linkType: hard

"micromark-util-encode@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-encode@npm:1.1.0"
  checksum: 4ef29d02b12336918cea6782fa87c8c578c67463925221d4e42183a706bde07f4b8b5f9a5e1c7ce8c73bb5a98b261acd3238fecd152e6dd1cdfa2d1ae11b60a0
  languageName: node
  linkType: hard

"micromark-util-encode@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-encode@npm:2.0.1"
  checksum: be890b98e78dd0cdd953a313f4148c4692cc2fb05533e56fef5f421287d3c08feee38ca679f318e740530791fc251bfe8c80efa926fcceb4419b269c9343d226
  languageName: node
  linkType: hard

"micromark-util-html-tag-name@npm:^1.0.0":
  version: 1.2.0
  resolution: "micromark-util-html-tag-name@npm:1.2.0"
  checksum: ccf0fa99b5c58676dc5192c74665a3bfd1b536fafaf94723bd7f31f96979d589992df6fcf2862eba290ef18e6a8efb30ec8e1e910d9f3fc74f208871e9f84750
  languageName: node
  linkType: hard

"micromark-util-html-tag-name@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-html-tag-name@npm:2.0.1"
  checksum: dea365f5ad28ad74ff29fcb581f7b74fc1f80271c5141b3b2bc91c454cbb6dfca753f28ae03730d657874fcbd89d0494d0e3965dfdca06d9855f467c576afa9d
  languageName: node
  linkType: hard

"micromark-util-normalize-identifier@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-normalize-identifier@npm:1.1.0"
  dependencies:
    micromark-util-symbol: ^1.0.0
  checksum: 8655bea41ffa4333e03fc22462cb42d631bbef9c3c07b625fd852b7eb442a110f9d2e5902a42e65188d85498279569502bf92f3434a1180fc06f7c37edfbaee2
  languageName: node
  linkType: hard

"micromark-util-normalize-identifier@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-normalize-identifier@npm:2.0.1"
  dependencies:
    micromark-util-symbol: ^2.0.0
  checksum: 1eb9a289d7da067323df9fdc78bfa90ca3207ad8fd893ca02f3133e973adcb3743b233393d23d95c84ccaf5d220ae7f5a28402a644f135dcd4b8cfa60a7b5f84
  languageName: node
  linkType: hard

"micromark-util-resolve-all@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-resolve-all@npm:1.1.0"
  dependencies:
    micromark-util-types: ^1.0.0
  checksum: 1ce6c0237cd3ca061e76fae6602cf95014e764a91be1b9f10d36cb0f21ca88f9a07de8d49ab8101efd0b140a4fbfda6a1efb72027ab3f4d5b54c9543271dc52c
  languageName: node
  linkType: hard

"micromark-util-resolve-all@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-resolve-all@npm:2.0.1"
  dependencies:
    micromark-util-types: ^2.0.0
  checksum: 9275f3ddb6c26f254dd2158e66215d050454b279707a7d9ce5a3cd0eba23201021cedcb78ae1a746c1b23227dcc418ee40dd074ade195359506797a5493550cc
  languageName: node
  linkType: hard

"micromark-util-sanitize-uri@npm:^1.0.0":
  version: 1.2.0
  resolution: "micromark-util-sanitize-uri@npm:1.2.0"
  dependencies:
    micromark-util-character: ^1.0.0
    micromark-util-encode: ^1.0.0
    micromark-util-symbol: ^1.0.0
  checksum: 6663f365c4fe3961d622a580f4a61e34867450697f6806f027f21cf63c92989494895fcebe2345d52e249fe58a35be56e223a9776d084c9287818b40c779acc1
  languageName: node
  linkType: hard

"micromark-util-sanitize-uri@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-sanitize-uri@npm:2.0.1"
  dependencies:
    micromark-util-character: ^2.0.0
    micromark-util-encode: ^2.0.0
    micromark-util-symbol: ^2.0.0
  checksum: d01517840c17de67aaa0b0f03bfe05fac8a41d99723cd8ce16c62f6810e99cd3695364a34c335485018e5e2c00e69031744630a1b85c6868aa2f2ca1b36daa2f
  languageName: node
  linkType: hard

"micromark-util-subtokenize@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-subtokenize@npm:1.1.0"
  dependencies:
    micromark-util-chunked: ^1.0.0
    micromark-util-symbol: ^1.0.0
    micromark-util-types: ^1.0.0
    uvu: ^0.5.0
  checksum: 4a9d780c4d62910e196ea4fd886dc4079d8e424e5d625c0820016da0ed399a281daff39c50f9288045cc4bcd90ab47647e5396aba500f0853105d70dc8b1fc45
  languageName: node
  linkType: hard

"micromark-util-subtokenize@npm:^2.0.0":
  version: 2.0.3
  resolution: "micromark-util-subtokenize@npm:2.0.3"
  dependencies:
    devlop: ^1.0.0
    micromark-util-chunked: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: 3e95112b3ae640348e611dd69dc73e03f96a62e6d510d7c801685bd701041a61b5835e119ec84044972f2873b60ba21dbc58082a345d7745f4c19465b6d1b644
  languageName: node
  linkType: hard

"micromark-util-symbol@npm:^1.0.0":
  version: 1.1.0
  resolution: "micromark-util-symbol@npm:1.1.0"
  checksum: 02414a753b79f67ff3276b517eeac87913aea6c028f3e668a19ea0fc09d98aea9f93d6222a76ca783d20299af9e4b8e7c797fe516b766185dcc6e93290f11f88
  languageName: node
  linkType: hard

"micromark-util-symbol@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-symbol@npm:2.0.1"
  checksum: fb7346950550bc85a55793dda94a8b3cb3abc068dbd7570d1162db7aee803411d06c0a5de4ae59cd945f46143bdeadd4bba02a02248fa0d18cc577babaa00044
  languageName: node
  linkType: hard

"micromark-util-types@npm:^1.0.0, micromark-util-types@npm:^1.0.1":
  version: 1.1.0
  resolution: "micromark-util-types@npm:1.1.0"
  checksum: b0ef2b4b9589f15aec2666690477a6a185536927ceb7aa55a0f46475852e012d75a1ab945187e5c7841969a842892164b15d58ff8316b8e0d6cc920cabd5ede7
  languageName: node
  linkType: hard

"micromark-util-types@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-types@npm:2.0.1"
  checksum: 630aac466628a360962f478f69421599c53ff8b3080765201b7be3b3a4be7f4c5b73632b9a6dd426b9e06035353c18acccee637d6c43d9b0bf1c31111bbb88a7
  languageName: node
  linkType: hard

"micromark@npm:^3.1.0":
  version: 3.2.0
  resolution: "micromark@npm:3.2.0"
  dependencies:
    "@types/debug": ^4.0.0
    debug: ^4.0.0
    decode-named-character-reference: ^1.0.0
    micromark-core-commonmark: ^1.0.1
    micromark-factory-space: ^1.0.0
    micromark-util-character: ^1.0.0
    micromark-util-chunked: ^1.0.0
    micromark-util-combine-extensions: ^1.0.0
    micromark-util-decode-numeric-character-reference: ^1.0.0
    micromark-util-encode: ^1.0.0
    micromark-util-normalize-identifier: ^1.0.0
    micromark-util-resolve-all: ^1.0.0
    micromark-util-sanitize-uri: ^1.0.0
    micromark-util-subtokenize: ^1.0.0
    micromark-util-symbol: ^1.0.0
    micromark-util-types: ^1.0.1
    uvu: ^0.5.0
  checksum: 56c15851ad3eb8301aede65603473443e50c92a54849cac1dadd57e4ec33ab03a0a77f3df03de47133e6e8f695dae83b759b514586193269e98c0bf319ecd5e4
  languageName: node
  linkType: hard

"micromark@npm:^4.0.0":
  version: 4.0.1
  resolution: "micromark@npm:4.0.1"
  dependencies:
    "@types/debug": ^4.0.0
    debug: ^4.0.0
    decode-named-character-reference: ^1.0.0
    devlop: ^1.0.0
    micromark-core-commonmark: ^2.0.0
    micromark-factory-space: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-chunked: ^2.0.0
    micromark-util-combine-extensions: ^2.0.0
    micromark-util-decode-numeric-character-reference: ^2.0.0
    micromark-util-encode: ^2.0.0
    micromark-util-normalize-identifier: ^2.0.0
    micromark-util-resolve-all: ^2.0.0
    micromark-util-sanitize-uri: ^2.0.0
    micromark-util-subtokenize: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: 83ea084e8bf84442cc70c1207e916df11f0fde0ebd9daf978c895a1466c47a1dd4ed42b21b6e65bcc0d268fcbec24b4b1b28bc59c548940fe690929b8e0e7732
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4, micromatch@npm:~4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: ^3.0.3
    picomatch: ^2.3.1
  checksum: 79920eb634e6f400b464a954fcfa589c4e7c7143209488e44baf627f9affc8b1e306f41f4f0deedde97e69cb725920879462d3e750ab3bd3c1aed675bb3a8966
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 0d99a03585f8b39d68182803b12ac601d9c01abfa28ec56204fa330bc9f3d1c5e14beb049bafadb3dbdf646dfb94b87e24d4ec7b31b7279ef906a8ea9b6a513f
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:~2.1.24":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: 1.52.0
  checksum: 89a5b7f1def9f3af5dad6496c5ed50191ae4331cc5389d7c521c8ad28d5fdad2d06fd81baf38fed813dc4e46bb55c8145bb0ff406330818c9cf712fb2e9b3836
  languageName: node
  linkType: hard

"mimic-fn@npm:^4.0.0":
  version: 4.0.0
  resolution: "mimic-fn@npm:4.0.0"
  checksum: 995dcece15ee29aa16e188de6633d43a3db4611bcf93620e7e62109ec41c79c0f34277165b8ce5e361205049766e371851264c21ac64ca35499acb5421c2ba56
  languageName: node
  linkType: hard

"mimic-function@npm:^5.0.0":
  version: 5.0.1
  resolution: "mimic-function@npm:5.0.1"
  checksum: eb5893c99e902ccebbc267c6c6b83092966af84682957f79313311edb95e8bb5f39fb048d77132b700474d1c86d90ccc211e99bae0935447a4834eb4c882982c
  languageName: node
  linkType: hard

"min-indent@npm:^1.0.0":
  version: 1.0.1
  resolution: "min-indent@npm:1.0.1"
  checksum: bfc6dd03c5eaf623a4963ebd94d087f6f4bbbfd8c41329a7f09706b0cb66969c4ddd336abeb587bc44bc6f08e13bf90f0b374f9d71f9f01e04adc2cd6f083ef1
  languageName: node
  linkType: hard

"minimatch@npm:9.0.1":
  version: 9.0.1
  resolution: "minimatch@npm:9.0.1"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 97f5f5284bb57dc65b9415dec7f17a0f6531a33572193991c60ff18450dcfad5c2dad24ffeaf60b5261dccd63aae58cc3306e2209d57e7f88c51295a532d8ec3
  languageName: node
  linkType: hard

"minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: c154e566406683e7bcb746e000b84d74465b3a832c45d59912b9b55cd50dee66e5c4b1e5566dba26154040e51672f9aa450a9aef0c97cfc7336b78b7afb9540a
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.3, minimatch@npm:^9.0.4, minimatch@npm:^9.0.5":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 2c035575eda1e50623c731ec6c14f65a85296268f749b9337005210bb2b34e2705f8ef1a358b188f69892286ab99dc42c8fb98a57bde55c8d81b3023c19cea28
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: ^7.0.3
  checksum: b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.0
  resolution: "minipass-fetch@npm:4.0.0"
  dependencies:
    encoding: ^0.1.13
    minipass: ^7.0.3
    minipass-sized: ^1.0.3
    minizlib: ^3.0.1
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 7d59a31011ab9e4d1af6562dd4c4440e425b2baf4c5edbdd2e22fb25a88629e1cdceca39953ff209da504a46021df520f18fd9a519f36efae4750ff724ddadea
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 79076749fcacf21b5d16dd596d32c3b6bf4d6e62abb43868fac21674078505c8b15eaca4e47ed844985a4514854f917d78f588fcd029693709417d8f98b2bd60
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: ^4.0.0
  checksum: a30d083c8054cee83cdcdc97f97e4641a3f58ae743970457b1489ce38ee1167b3aaf7d815cd39ec7a99b9c40397fd4f686e83750e73e652b21cb516f6d845e48
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 2bfd325b95c555f2b4d2814d49325691c7bee937d753814861b0b49d5edcda55cbbf22b6b6a60bb91eddac8668771f03c5ff647dcd9d0f798e9548b9cdc46ee3
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.1
  resolution: "minizlib@npm:3.0.1"
  dependencies:
    minipass: ^7.0.4
    rimraf: ^5.0.5
  checksum: da0a53899252380475240c587e52c824f8998d9720982ba5c4693c68e89230718884a209858c156c6e08d51aad35700a3589987e540593c36f6713fe30cd7338
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 972deb188e8fb55547f1e58d66bd6b4a3623bf0c7137802582602d73e6480c1c2268dcbafbfb1be466e00cc7e56ac514d7fd9334b7cf33e3e2ab547c16f83a8d
  languageName: node
  linkType: hard

"mlly@npm:^1.2.0, mlly@npm:^1.7.2, mlly@npm:^1.7.3":
  version: 1.7.3
  resolution: "mlly@npm:1.7.3"
  dependencies:
    acorn: ^8.14.0
    pathe: ^1.1.2
    pkg-types: ^1.2.1
    ufo: ^1.5.4
  checksum: 60d309c7ce2ac162224a087fcd683a891260511f57011b2f436b54dfef146b8aae7473013958a58d5b6039f2a8692c32a2599c8390c5b307d1119ad0d917b414
  languageName: node
  linkType: hard

"mri@npm:^1.1.0":
  version: 1.2.0
  resolution: "mri@npm:1.2.0"
  checksum: 83f515abbcff60150873e424894a2f65d68037e5a7fcde8a9e2b285ee9c13ac581b63cfc1e6826c4732de3aeb84902f7c1e16b7aff46cd3f897a0f757a894e85
  languageName: node
  linkType: hard

"mrmime@npm:^2.0.0":
  version: 2.0.0
  resolution: "mrmime@npm:2.0.0"
  checksum: f6fe11ec667c3d96f1ce5fd41184ed491d5f0a5f4045e82446a471ccda5f84c7f7610dff61d378b73d964f73a320bd7f89788f9e6b9403e32cc4be28ba99f569
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 0e6a22b8b746d2e0b65a430519934fefd41b6db0682e3477c10f60c76e947c4c0ad06f63ffdf1d78d335f83edee8c0aa928aa66a36c7cd95b69b26f468d527f4
  languageName: node
  linkType: hard

"ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"muggle-string@npm:^0.4.1":
  version: 0.4.1
  resolution: "muggle-string@npm:0.4.1"
  checksum: 85fe1766d18d43cf22b6da7d047203a65b2e2b1ccfac505b699c2a459644f95ebb3c854a96db5be559eea0e213f6ee32b986b8c2f73c48e6c89e1fd829616532
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.7":
  version: 3.3.8
  resolution: "nanoid@npm:3.3.8"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: dfe0adbc0c77e9655b550c333075f51bb28cfc7568afbf3237249904f9c86c9aaaed1f113f0fddddba75673ee31c758c30c43d4414f014a52a7a626efc5958c9
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"natural-orderby@npm:^5.0.0":
  version: 5.0.0
  resolution: "natural-orderby@npm:5.0.0"
  checksum: bfdc62b91fb1b08bd3a8a9b66ec3476c17f867544f34778ce85344ffcff257342d632e94bc125c97f02b89c746479edd62c8727a310c94cab1a1d4a016eeeb46
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 20ebfe79b2d2e7cf9cbc8239a72662b584f71164096e6e8896c8325055497c96f6b80cd22c258e8a2f2aa382a787795ec3ee8b37b422a302c7d4381b0d5ecfbb
  languageName: node
  linkType: hard

"node-fetch-native@npm:^1.6.4":
  version: 1.6.4
  resolution: "node-fetch-native@npm:1.6.4"
  checksum: 7b159f610e037e8813750096a6616ec6771e9abf868aa6e75e5b790bfc2ba2d92cf2abcce33c18fd01f2e5e5cc72de09c78bd4381e7f8c0887f7de21bd96f045
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.0.0
  resolution: "node-gyp@npm:11.0.0"
  dependencies:
    env-paths: ^2.2.0
    exponential-backoff: ^3.1.1
    glob: ^10.3.10
    graceful-fs: ^4.2.6
    make-fetch-happen: ^14.0.3
    nopt: ^8.0.0
    proc-log: ^5.0.0
    semver: ^7.3.5
    tar: ^7.4.3
    which: ^5.0.0
  bin:
    node-gyp: bin/node-gyp.js
  checksum: d7d5055ccc88177f721c7cd4f8f9440c29a0eb40e7b79dba89ef882ec957975dfc1dcb8225e79ab32481a02016eb13bbc051a913ea88d482d3cbdf2131156af4
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.18":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 917dbced519f48c6289a44830a0ca6dc944c3ee9243c468ebd8515a41c97c8b2c256edb7f3f750416bc37952cc9608684e6483c7b6c6f39f6bd8d86c52cfe658
  languageName: node
  linkType: hard

"nopt@npm:^7.2.0":
  version: 7.2.1
  resolution: "nopt@npm:7.2.1"
  dependencies:
    abbrev: ^2.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 6fa729cc77ce4162cfad8abbc9ba31d4a0ff6850c3af61d59b505653bef4781ec059f8890ecfe93ee8aa0c511093369cca88bfc998101616a2904e715bbbb7c9
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.0.0
  resolution: "nopt@npm:8.0.0"
  dependencies:
    abbrev: ^2.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 2cfc65e7ee38af2e04aea98f054753b0230011c0eeca4ecf131bd7d25984cbbf6f214586e0ae5dfcc2e830bc0bffa5a7fb28ea8d0b306ffd4ae8ea2d814c1ab3
  languageName: node
  linkType: hard

"normalize-package-data@npm:^2.5.0":
  version: 2.5.0
  resolution: "normalize-package-data@npm:2.5.0"
  dependencies:
    hosted-git-info: ^2.1.4
    resolve: ^1.10.0
    semver: 2 || 3 || 4 || 5
    validate-npm-package-license: ^3.0.1
  checksum: 7999112efc35a6259bc22db460540cae06564aa65d0271e3bdfa86876d08b0e578b7b5b0028ee61b23f1cae9fc0e7847e4edc0948d3068a39a2a82853efc8499
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"npm-run-path@npm:^5.1.0":
  version: 5.3.0
  resolution: "npm-run-path@npm:5.3.0"
  dependencies:
    path-key: ^4.0.0
  checksum: ae8e7a89da9594fb9c308f6555c73f618152340dcaae423e5fb3620026fefbec463618a8b761920382d666fa7a2d8d240b6fe320e8a6cdd54dc3687e2b659d25
  languageName: node
  linkType: hard

"nth-check@npm:^2.1.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: ^1.0.0
  checksum: 5afc3dafcd1573b08877ca8e6148c52abd565f1d06b1eb08caf982e3fa289a82f2cae697ffb55b5021e146d60443f1590a5d6b944844e944714a5b549675bcd3
  languageName: node
  linkType: hard

"nwsapi@npm:^2.2.12":
  version: 2.2.16
  resolution: "nwsapi@npm:2.2.16"
  checksum: 467b36a74b7b8647d53fd61d05ca7d6c73a4a5d1b94ea84f770c03150b00ef46d38076cf8e708936246ae450c42a1f21e28e153023719784dc4d1a19b1737d47
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.1":
  version: 1.13.3
  resolution: "object-inspect@npm:1.13.3"
  checksum: 8c962102117241e18ea403b84d2521f78291b774b03a29ee80a9863621d88265ffd11d0d7e435c4c2cea0dc2a2fbf8bbc92255737a05536590f2df2e8756f297
  languageName: node
  linkType: hard

"object-is@npm:^1.1.5":
  version: 1.1.6
  resolution: "object-is@npm:1.1.6"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
  checksum: 3ea22759967e6f2380a2cbbd0f737b42dc9ddb2dfefdb159a1b927fea57335e1b058b564bfa94417db8ad58cddab33621a035de6f5e5ad56d89f2dd03e66c6a1
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: b363c5e7644b1e1b04aa507e88dcb8e3a2f52b6ffd0ea801e4c7a62d5aa559affe21c55a07fd4b1fd55fc03a33c610d73426664b20032405d7b92a1414c34d6a
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.4":
  version: 4.1.5
  resolution: "object.assign@npm:4.1.5"
  dependencies:
    call-bind: ^1.0.5
    define-properties: ^1.2.1
    has-symbols: ^1.0.3
    object-keys: ^1.1.1
  checksum: f9aeac0541661370a1fc86e6a8065eb1668d3e771f7dbb33ee54578201336c057b21ee61207a186dd42db0c62201d91aac703d20d12a79fc79c353eed44d4e25
  languageName: node
  linkType: hard

"ofetch@npm:^1.4.1":
  version: 1.4.1
  resolution: "ofetch@npm:1.4.1"
  dependencies:
    destr: ^2.0.3
    node-fetch-native: ^1.6.4
    ufo: ^1.5.4
  checksum: 005974d238b7212dc10b67ddb019eda9cf89ba781dfa8c2f31d8eea0782261d626ce7a36ac377deb71ec0f72f05a023e6d3cc31b7384fbbabdb328afbf1bf929
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: 1.1.1
  checksum: d20929a25e7f0bb62f937a425b5edeb4e4cde0540d77ba146ec9357f00b0d497cdb3b9b05b9c8e46222407d1548d08166bff69cc56dfa55ba0e4469228920ff0
  languageName: node
  linkType: hard

"onetime@npm:^6.0.0":
  version: 6.0.0
  resolution: "onetime@npm:6.0.0"
  dependencies:
    mimic-fn: ^4.0.0
  checksum: 0846ce78e440841335d4e9182ef69d5762e9f38aa7499b19f42ea1c4cd40f0b4446094c455c713f9adac3f4ae86f613bb5e30c99e52652764d06a89f709b3788
  languageName: node
  linkType: hard

"onetime@npm:^7.0.0":
  version: 7.0.0
  resolution: "onetime@npm:7.0.0"
  dependencies:
    mimic-function: ^5.0.0
  checksum: eb08d2da9339819e2f9d52cab9caf2557d80e9af8c7d1ae86e1a0fef027d00a88e9f5bd67494d350df360f7c559fbb44e800b32f310fb989c860214eacbb561c
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: ^0.1.3
    fast-levenshtein: ^2.0.6
    levn: ^0.4.1
    prelude-ls: ^1.2.1
    type-check: ^0.4.0
    word-wrap: ^1.2.5
  checksum: ecbd010e3dc73e05d239976422d9ef54a82a13f37c11ca5911dff41c98a6c7f0f163b27f922c37e7f8340af9d36febd3b6e9cef508f3339d4c393d7276d716bb
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: ^2.0.0
  checksum: 84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: ^0.1.0
  checksum: 7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: ^2.2.0
  checksum: 513bd14a455f5da4ebfcb819ef706c54adb09097703de6aeaa5d26fe5ea16df92b48d1ac45e01e3944ce1e6aa2a66f7f8894742b8c9d6e276e16cd2049a2b870
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: ^3.0.2
  checksum: 1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 8c92d533acf82f0d12f7e196edccff773f384098bbb048acdd55a08778ce4fc8889d8f1bde72969487bd96f9c63212698d79744c20bedfce36c5b00b46d369f8
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 58ee9538f2f762988433da00e26acc788036914d57c71c246bf0be1b60cdbd77dd60b6a3e1a30465f0b248aeb80079e0b34cb6050b1dfa18c06953bb1cbc7602
  languageName: node
  linkType: hard

"package-manager-detector@npm:^0.2.0, package-manager-detector@npm:^0.2.2, package-manager-detector@npm:^0.2.5":
  version: 0.2.7
  resolution: "package-manager-detector@npm:0.2.7"
  checksum: 8dc58fb1fe9ba29a2750ecccde41c02357df8cefe5dcacf4a61bedcfab746d3589545a1703e7f1d1ade2611bd646f47f1c4cbc002d7507fc6f7d34b015fcecfc
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: ^3.0.0
  checksum: 6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse-gitignore@npm:^2.0.0":
  version: 2.0.0
  resolution: "parse-gitignore@npm:2.0.0"
  checksum: 81e2fc56aefe004bb1c2b70f69a4a599969c21d2e4ee66e1e9c7eb84ae30286a25cf84d3a3b09e8b1772fd0e2b028560d97998f2e2ad1c3eebb89f84cedf36ea
  languageName: node
  linkType: hard

"parse-imports@npm:^2.1.1":
  version: 2.2.1
  resolution: "parse-imports@npm:2.2.1"
  dependencies:
    es-module-lexer: ^1.5.3
    slashes: ^3.0.12
  checksum: 0b5cedd10b6b45eea4f365bf047074a874d90e952597f83d4a8a00f1edece180b5870e42401b5531088916836f98c20eecbddc608d8717eb4a6be99a41f2b6fd
  languageName: node
  linkType: hard

"parse-json@npm:^5.0.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": ^7.0.0
    error-ex: ^1.3.1
    json-parse-even-better-errors: ^2.3.0
    lines-and-columns: ^1.1.6
  checksum: 62085b17d64da57f40f6afc2ac1f4d95def18c4323577e1eced571db75d9ab59b297d1d10582920f84b15985cbfc6b6d450ccbf317644cfa176f3ed982ad87e2
  languageName: node
  linkType: hard

"parse5@npm:^7.1.2":
  version: 7.2.1
  resolution: "parse5@npm:7.2.1"
  dependencies:
    entities: ^4.5.0
  checksum: 11253cf8aa2e7fc41c004c64cba6f2c255f809663365db65bd7ad0e8cf7b89e436a563c20059346371cc543a6c1b567032088883ca6a2cbc88276c666b68236d
  languageName: node
  linkType: hard

"path-browserify@npm:^1.0.1":
  version: 1.0.1
  resolution: "path-browserify@npm:1.0.1"
  checksum: c6d7fa376423fe35b95b2d67990060c3ee304fc815ff0a2dc1c6c3cfaff2bd0d572ee67e18f19d0ea3bbe32e8add2a05021132ac40509416459fffee35200699
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-key@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-key@npm:4.0.0"
  checksum: 8e6c314ae6d16b83e93032c61020129f6f4484590a777eed709c4a01b50e498822b00f76ceaf94bc64dbd90b327df56ceadce27da3d83393790f1219e07721d7
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: ^10.2.0
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
  checksum: 890d5abcd593a7912dcce7cf7c6bf7a0b5648e3dee6caf0712c126ca0a65c7f3d7b9d769072a4d1baf370f61ce493ab5b038d59988688e0c5f3f646ee3c69023
  languageName: node
  linkType: hard

"pathe@npm:^1.0.0, pathe@npm:^1.1.2":
  version: 1.1.2
  resolution: "pathe@npm:1.1.2"
  checksum: ec5f778d9790e7b9ffc3e4c1df39a5bb1ce94657a4e3ad830c1276491ca9d79f189f47609884671db173400256b005f4955f7952f52a2aeb5834ad5fb4faf134
  languageName: node
  linkType: hard

"pathval@npm:^2.0.0":
  version: 2.0.0
  resolution: "pathval@npm:2.0.0"
  checksum: 682b6a6289de7990909effef7dae9aa7bb6218c0426727bccf66a35b34e7bfbc65615270c5e44e3c9557a5cb44b1b9ef47fc3cb18bce6ad3ba92bcd28467ed7d
  languageName: node
  linkType: hard

"peek-readable@npm:^4.1.0":
  version: 4.1.0
  resolution: "peek-readable@npm:4.1.0"
  checksum: 02c673f9bc816f8e4e74a054c097225ad38d457d745b775e2b96faf404a54473b2f62f5bcd496f5ebc28696708bcc5e95bed409856f4bef5ed62eae9b4ac0dab
  languageName: node
  linkType: hard

"perfect-debounce@npm:^1.0.0":
  version: 1.0.0
  resolution: "perfect-debounce@npm:1.0.0"
  checksum: 220343acf52976947958fef3599849471605316e924fe19c633ae2772576298e9d38f02cefa8db46f06607505ce7b232cbb35c9bfd477bd0329bd0a2ce37c594
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.1.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: e1cf46bf84886c79055fdfa9dcb3e4711ad259949e3565154b004b260cd356c5d54b31a1437ce9782624bf766272fe6b0154f5f0c744fb7af5d454d2b60db045
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: a7a5188c954f82c6585720e9143297ccd0e35ad8072231608086ca950bee672d51b0ef676254af0788205e59bd4e4deb4e7708769226bed725bf13370a7d1464
  languageName: node
  linkType: hard

"pidtree@npm:~0.6.0":
  version: 0.6.0
  resolution: "pidtree@npm:0.6.0"
  bin:
    pidtree: bin/pidtree.js
  checksum: 8fbc073ede9209dd15e80d616e65eb674986c93be49f42d9ddde8dbbd141bb53d628a7ca4e58ab5c370bb00383f67d75df59a9a226dede8fa801267a7030c27a
  languageName: node
  linkType: hard

"pinia@npm:^2.3.0":
  version: 2.3.0
  resolution: "pinia@npm:2.3.0"
  dependencies:
    "@vue/devtools-api": ^6.6.3
    vue-demi: ^0.14.10
  peerDependencies:
    typescript: ">=4.4.4"
    vue: ^2.7.0 || ^3.5.11
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 556dd718563c535e21f35576750afc2abfde0ab60f40b1e31e6cf2ecceee17489549d0c6ffc445a168a84505e9117cfa8d8ae859fc47332db00f88d1db480f56
  languageName: node
  linkType: hard

"pkg-types@npm:^1.2.1":
  version: 1.2.1
  resolution: "pkg-types@npm:1.2.1"
  dependencies:
    confbox: ^0.1.8
    mlly: ^1.7.2
    pathe: ^1.1.2
  checksum: d2e3ad7aef36cc92b17403e61c04db521bf0beb175ccb4d432c284239f00ec32ff37feb072a260613e9ff727911cff1127a083fd52f91b9bec6b62970f385702
  languageName: node
  linkType: hard

"pluralize@npm:^8.0.0":
  version: 8.0.0
  resolution: "pluralize@npm:8.0.0"
  checksum: 08931d4a6a4a5561a7f94f67a31c17e6632cb21e459ab3ff4f6f629d9a822984cf8afef2311d2005fbea5d7ef26016ebb090db008e2d8bce39d0a9a9d218736e
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.0.0
  resolution: "possible-typed-array-names@npm:1.0.0"
  checksum: b32d403ece71e042385cc7856385cecf1cd8e144fa74d2f1de40d1e16035dba097bc189715925e79b67bdd1472796ff168d3a90d296356c9c94d272d5b95f3ae
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.0.15":
  version: 6.1.2
  resolution: "postcss-selector-parser@npm:6.1.2"
  dependencies:
    cssesc: ^3.0.0
    util-deprecate: ^1.0.2
  checksum: ce9440fc42a5419d103f4c7c1847cb75488f3ac9cbe81093b408ee9701193a509f664b4d10a2b4d82c694ee7495e022f8f482d254f92b7ffd9ed9dea696c6f84
  languageName: node
  linkType: hard

"postcss@npm:^8.4.43, postcss@npm:^8.4.48, postcss@npm:^8.4.49":
  version: 8.4.49
  resolution: "postcss@npm:8.4.49"
  dependencies:
    nanoid: ^3.3.7
    picocolors: ^1.1.1
    source-map-js: ^1.2.1
  checksum: eb5d6cbdca24f50399aafa5d2bea489e4caee4c563ea1edd5a2485bc5f84e9ceef3febf170272bc83a99c31d23a316ad179213e853f34c2a7a8ffa534559d63a
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: cd192ec0d0a8e4c6da3bb80e4f62afe336df3f76271ac6deb0e6a36187133b6073a19e9727a1ff108cd8b9982e4768850d413baa71214dd80c7979617dca827a
  languageName: node
  linkType: hard

"prettier-linter-helpers@npm:^1.0.0":
  version: 1.0.0
  resolution: "prettier-linter-helpers@npm:1.0.0"
  dependencies:
    fast-diff: ^1.1.2
  checksum: 00ce8011cf6430158d27f9c92cfea0a7699405633f7f1d4a45f07e21bf78e99895911cbcdc3853db3a824201a7c745bd49bfea8abd5fb9883e765a90f74f8392
  languageName: node
  linkType: hard

"prettier@npm:^3.4.1":
  version: 3.4.2
  resolution: "prettier@npm:3.4.2"
  bin:
    prettier: bin/prettier.cjs
  checksum: 061c84513db62d3944c8dc8df36584dad82883ce4e49efcdbedd8703dce5b173c33fd9d2a4e1725d642a3b713c932b55418342eaa347479bc4a9cca114a04cd0
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: c78b26ecef6d5cce4a7489a1e9923d7b4b1679028c8654aef0463b27f4a90b0946cd598f55799da602895c52feb085ec76381d007ab8dcceebd40b89c2f9dfe0
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: f96a3f6d90b92b568a26f71e966cbbc0f63ab85ea6ff6c81284dc869b41510e6cdef99b6b65f9030f0db422bf7c96652a3fff9f2e8fb4a0f069d8f4430359429
  languageName: node
  linkType: hard

"proto-list@npm:~1.2.1":
  version: 1.2.4
  resolution: "proto-list@npm:1.2.4"
  checksum: 4d4826e1713cbfa0f15124ab0ae494c91b597a3c458670c9714c36e8baddf5a6aad22842776f2f5b137f259c8533e741771445eb8df82e861eea37a6eaba03f7
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: ed7fcc2ba0a33404958e34d95d18638249a68c430e30fcb6c478497d72739ba64ce9810a24f53a7d921d0c065e5b78e3822759800698167256b04659366ca4d4
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0, punycode@npm:^2.3.1":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: bb0a0ceedca4c3c57a9b981b90601579058903c62be23c5e8e843d2c2d4148a3ecf029d5133486fb0e1822b098ba8bba09e89d6b21742d02fa26bda6441a6fb2
  languageName: node
  linkType: hard

"qs@npm:6.13.0":
  version: 6.13.0
  resolution: "qs@npm:6.13.0"
  dependencies:
    side-channel: ^1.0.6
  checksum: e9404dc0fc2849245107108ce9ec2766cde3be1b271de0bf1021d049dc5b98d1a2901e67b431ac5509f865420a7ed80b7acb3980099fe1c118a1c5d2e1432ad8
  languageName: node
  linkType: hard

"quasar@npm:^2.17.4":
  version: 2.17.4
  resolution: "quasar@npm:2.17.4"
  checksum: ea1ba0edfb7c4559f6972e68abfefce38c90299ed96826c5ef95cf7f07b1ba62a787ce92eb1cae03b804588db4a8cbbed5914f3eb7189cc90952e64f3568ec4e
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: b676f8c040cdc5b12723ad2f91414d267605b26419d5c821ff03befa817ddd10e238d22b25d604920340fd73efd8ba795465a0377c4adf45a4a41e4234e42dc4
  languageName: node
  linkType: hard

"raw-body@npm:2.5.2":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: 3.1.2
    http-errors: 2.0.0
    iconv-lite: 0.4.24
    unpipe: 1.0.0
  checksum: ba1583c8d8a48e8fbb7a873fdbb2df66ea4ff83775421bfe21ee120140949ab048200668c47d9ae3880012f6e217052690628cf679ddfbd82c9fc9358d574676
  languageName: node
  linkType: hard

"read-pkg-up@npm:^7.0.1":
  version: 7.0.1
  resolution: "read-pkg-up@npm:7.0.1"
  dependencies:
    find-up: ^4.1.0
    read-pkg: ^5.2.0
    type-fest: ^0.8.1
  checksum: e4e93ce70e5905b490ca8f883eb9e48b5d3cebc6cd4527c25a0d8f3ae2903bd4121c5ab9c5a3e217ada0141098eeb661313c86fa008524b089b8ed0b7f165e44
  languageName: node
  linkType: hard

"read-pkg@npm:^5.2.0":
  version: 5.2.0
  resolution: "read-pkg@npm:5.2.0"
  dependencies:
    "@types/normalize-package-data": ^2.4.0
    normalize-package-data: ^2.5.0
    parse-json: ^5.0.0
    type-fest: ^0.6.0
  checksum: eb696e60528b29aebe10e499ba93f44991908c57d70f2d26f369e46b8b9afc208ef11b4ba64f67630f31df8b6872129e0a8933c8c53b7b4daf0eace536901222
  languageName: node
  linkType: hard

"readable-stream@npm:^3.6.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: ^2.0.3
    string_decoder: ^1.1.1
    util-deprecate: ^1.0.1
  checksum: bdcbe6c22e846b6af075e32cf8f4751c2576238c5043169a1c221c92ee2878458a816a4ea33f4c67623c0b6827c8a400409bfb3cf0bf3381392d0b1dfb52ac8d
  languageName: node
  linkType: hard

"readable-web-to-node-stream@npm:^3.0.0":
  version: 3.0.2
  resolution: "readable-web-to-node-stream@npm:3.0.2"
  dependencies:
    readable-stream: ^3.6.0
  checksum: 8c56cc62c68513425ddfa721954875b382768f83fa20e6b31e365ee00cbe7a3d6296f66f7f1107b16cd3416d33aa9f1680475376400d62a081a88f81f0ea7f9c
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: ^2.2.1
  checksum: 1ced032e6e45670b6d7352d71d21ce7edf7b9b928494dcaba6f11fba63180d9da6cd7061ebc34175ffda6ff529f481818c962952004d273178acd70f7059b320
  languageName: node
  linkType: hard

"reconnecting-websocket@npm:^4.4.0":
  version: 4.4.0
  resolution: "reconnecting-websocket@npm:4.4.0"
  checksum: 7ee379ff3a4bddf9d47fb6f3fb2d9450865f0ed2d91f003e6d0c12c901e8c135379a143a933e7bed7bc33133f04bdaed534bd68591c95730044dc69d32da4866
  languageName: node
  linkType: hard

"refa@npm:^0.12.0, refa@npm:^0.12.1":
  version: 0.12.1
  resolution: "refa@npm:0.12.1"
  dependencies:
    "@eslint-community/regexpp": ^4.8.0
  checksum: 845cef54786884d5f09558dd600cec20ee354ebbcabd206503d5cc5d63d22bb69dee8b66bf8411de622693fc5c2b9d42b9cf1e5e6900c538ee333eefb5cc1b41
  languageName: node
  linkType: hard

"regexp-ast-analysis@npm:^0.7.0, regexp-ast-analysis@npm:^0.7.1":
  version: 0.7.1
  resolution: "regexp-ast-analysis@npm:0.7.1"
  dependencies:
    "@eslint-community/regexpp": ^4.8.0
    refa: ^0.12.1
  checksum: c1c47fea637412d8362a9358b1b2952a6ab159daaede2244c05e79c175844960259c88e30072e4f81a06e5ac1c80f590b14038b17bc8cff09293f752cf843b1c
  languageName: node
  linkType: hard

"regexp-tree@npm:^0.1.27":
  version: 0.1.27
  resolution: "regexp-tree@npm:0.1.27"
  bin:
    regexp-tree: bin/regexp-tree
  checksum: 129aebb34dae22d6694ab2ac328be3f99105143737528ab072ef624d599afecbcfae1f5c96a166fa9e5f64fa1ecf30b411c4691e7924c3e11bbaf1712c260c54
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.1":
  version: 1.5.3
  resolution: "regexp.prototype.flags@npm:1.5.3"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-errors: ^1.3.0
    set-function-name: ^2.0.2
  checksum: 83ff0705b837f7cb6d664010a11642250f36d3f642263dd0f3bdfe8f150261aa7b26b50ee97f21c1da30ef82a580bb5afedbef5f45639d69edaafbeac9bbb0ed
  languageName: node
  linkType: hard

"regjsparser@npm:^0.10.0":
  version: 0.10.0
  resolution: "regjsparser@npm:0.10.0"
  dependencies:
    jsesc: ~0.5.0
  bin:
    regjsparser: bin/parser
  checksum: 17550661f43ba792f8365fb95b3dbdb64e25f14e31ef7c2c11876c240a60e87b7bfc28c98589f4e76b7cf49307e45fb24d030f57d68dd0cc41c56b4d378e9254
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: fb47e70bf0001fdeabdc0429d431863e9475e7e43ea5f94ad86503d918423c1543361cc5166d713eaa7029dd7a3d34775af04764bebff99ef413111a5af18c80
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: f4ba0b8494846a5066328ad33ef8ac173801a51739eb4d63408c847da9a2e1c1de1e6cbbf72699211f3d13f8fc1325648b169bd15eb7da35688e30a5fb0e4a7f
  languageName: node
  linkType: hard

"resolve-pkg-maps@npm:^1.0.0":
  version: 1.0.0
  resolution: "resolve-pkg-maps@npm:1.0.0"
  checksum: 1012afc566b3fdb190a6309cc37ef3b2dcc35dff5fa6683a9d00cd25c3247edfbc4691b91078c97adc82a29b77a2660c30d791d65dab4fc78bfc473f60289977
  languageName: node
  linkType: hard

"resolve@npm:^1.10.0, resolve@npm:^1.22.4":
  version: 1.22.8
  resolution: "resolve@npm:1.22.8"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: f8a26958aa572c9b064562750b52131a37c29d072478ea32e129063e2da7f83e31f7f11e7087a18225a8561cfe8d2f0df9dbea7c9d331a897571c0a2527dbb4c
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.10.0#~builtin<compat/resolve>, resolve@patch:resolve@^1.22.4#~builtin<compat/resolve>":
  version: 1.22.8
  resolution: "resolve@patch:resolve@npm%3A1.22.8#~builtin<compat/resolve>::version=1.22.8&hash=c3c19d"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 5479b7d431cacd5185f8db64bfcb7286ae5e31eb299f4c4f404ad8aa6098b77599563ac4257cb2c37a42f59dfc06a1bec2bcf283bb448f319e37f0feb9a09847
  languageName: node
  linkType: hard

"restore-cursor@npm:^5.0.0":
  version: 5.1.0
  resolution: "restore-cursor@npm:5.1.0"
  dependencies:
    onetime: ^7.0.0
    signal-exit: ^4.1.0
  checksum: 838dd54e458d89cfbc1a923b343c1b0f170a04100b4ce1733e97531842d7b440463967e521216e8ab6c6f8e89df877acc7b7f4c18ec76e99fb9bf5a60d358d2c
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 623bd7d2e5119467ba66202d733ec3c2e2e26568074923bc0585b6b99db14f357e79bdedb63cab56cec47491c4a0da7e6021a7465ca6dc4f481d3898fdd3158c
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: c3076ebcc22a6bc252cb0b9c77561795256c22b757f40c0d8110b1300723f15ec0fc8685e8d4ea6d7666f36c79ccc793b1939c748bf36f18f542744a4e379fcc
  languageName: node
  linkType: hard

"rfdc@npm:^1.4.1":
  version: 1.4.1
  resolution: "rfdc@npm:1.4.1"
  checksum: 3b05bd55062c1d78aaabfcea43840cdf7e12099968f368e9a4c3936beb744adb41cbdb315eac6d4d8c6623005d6f87fdf16d8a10e1ff3722e84afea7281c8d13
  languageName: node
  linkType: hard

"rimraf@npm:^5.0.5":
  version: 5.0.10
  resolution: "rimraf@npm:5.0.10"
  dependencies:
    glob: ^10.3.7
  bin:
    rimraf: dist/esm/bin.mjs
  checksum: 50e27388dd2b3fa6677385fc1e2966e9157c89c86853b96d02e6915663a96b7ff4d590e14f6f70e90f9b554093aa5dbc05ac3012876be558c06a65437337bc05
  languageName: node
  linkType: hard

"rollup@npm:^4.20.0":
  version: 4.28.1
  resolution: "rollup@npm:4.28.1"
  dependencies:
    "@rollup/rollup-android-arm-eabi": 4.28.1
    "@rollup/rollup-android-arm64": 4.28.1
    "@rollup/rollup-darwin-arm64": 4.28.1
    "@rollup/rollup-darwin-x64": 4.28.1
    "@rollup/rollup-freebsd-arm64": 4.28.1
    "@rollup/rollup-freebsd-x64": 4.28.1
    "@rollup/rollup-linux-arm-gnueabihf": 4.28.1
    "@rollup/rollup-linux-arm-musleabihf": 4.28.1
    "@rollup/rollup-linux-arm64-gnu": 4.28.1
    "@rollup/rollup-linux-arm64-musl": 4.28.1
    "@rollup/rollup-linux-loongarch64-gnu": 4.28.1
    "@rollup/rollup-linux-powerpc64le-gnu": 4.28.1
    "@rollup/rollup-linux-riscv64-gnu": 4.28.1
    "@rollup/rollup-linux-s390x-gnu": 4.28.1
    "@rollup/rollup-linux-x64-gnu": 4.28.1
    "@rollup/rollup-linux-x64-musl": 4.28.1
    "@rollup/rollup-win32-arm64-msvc": 4.28.1
    "@rollup/rollup-win32-ia32-msvc": 4.28.1
    "@rollup/rollup-win32-x64-msvc": 4.28.1
    "@types/estree": 1.0.6
    fsevents: ~2.3.2
  dependenciesMeta:
    "@rollup/rollup-android-arm-eabi":
      optional: true
    "@rollup/rollup-android-arm64":
      optional: true
    "@rollup/rollup-darwin-arm64":
      optional: true
    "@rollup/rollup-darwin-x64":
      optional: true
    "@rollup/rollup-freebsd-arm64":
      optional: true
    "@rollup/rollup-freebsd-x64":
      optional: true
    "@rollup/rollup-linux-arm-gnueabihf":
      optional: true
    "@rollup/rollup-linux-arm-musleabihf":
      optional: true
    "@rollup/rollup-linux-arm64-gnu":
      optional: true
    "@rollup/rollup-linux-arm64-musl":
      optional: true
    "@rollup/rollup-linux-loongarch64-gnu":
      optional: true
    "@rollup/rollup-linux-powerpc64le-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-gnu":
      optional: true
    "@rollup/rollup-linux-s390x-gnu":
      optional: true
    "@rollup/rollup-linux-x64-gnu":
      optional: true
    "@rollup/rollup-linux-x64-musl":
      optional: true
    "@rollup/rollup-win32-arm64-msvc":
      optional: true
    "@rollup/rollup-win32-ia32-msvc":
      optional: true
    "@rollup/rollup-win32-x64-msvc":
      optional: true
    fsevents:
      optional: true
  bin:
    rollup: dist/bin/rollup
  checksum: 092b87526d32e6f97aa4912184f7b29b7e3b28009b2b8c6cac841c311c07e7636f6108c4338f1f66d8ed699ddd9100db4218faf50d6cfd358b2a85749aeb8935
  languageName: node
  linkType: hard

"rrweb-cssom@npm:^0.7.1":
  version: 0.7.1
  resolution: "rrweb-cssom@npm:0.7.1"
  checksum: 62e410ddbaaba6abc196c3bbfa8de4952e0a134d9f2b454ee293039bf9931322d806e14d52ed122a5c2bd332a868b9da2e99358fb6232c33758b5ede86d992c8
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: ^1.2.2
  checksum: cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"rxjs@npm:^7.4.0":
  version: 7.8.1
  resolution: "rxjs@npm:7.8.1"
  dependencies:
    tslib: ^2.1.0
  checksum: de4b53db1063e618ec2eca0f7965d9137cabe98cf6be9272efe6c86b47c17b987383df8574861bcced18ebd590764125a901d5506082be84a8b8e364bf05f119
  languageName: node
  linkType: hard

"sade@npm:^1.7.3":
  version: 1.8.1
  resolution: "sade@npm:1.8.1"
  dependencies:
    mri: ^1.1.0
  checksum: 0756e5b04c51ccdc8221ebffd1548d0ce5a783a44a0fa9017a026659b97d632913e78f7dca59f2496aa996a0be0b0c322afd87ca72ccd909406f49dbffa0f45d
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.0.3":
  version: 1.0.3
  resolution: "safe-regex-test@npm:1.0.3"
  dependencies:
    call-bind: ^1.0.6
    es-errors: ^1.3.0
    is-regex: ^1.1.4
  checksum: 6c7d392ff1ae7a3ae85273450ed02d1d131f1d2c76e177d6b03eb88e6df8fa062639070e7d311802c1615f351f18dc58f9454501c58e28d5ffd9b8f502ba6489
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"sass-embedded-android-arm64@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-android-arm64@npm:1.80.2"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"sass-embedded-android-arm@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-android-arm@npm:1.80.2"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"sass-embedded-android-ia32@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-android-ia32@npm:1.80.2"
  conditions: os=android & cpu=ia32
  languageName: node
  linkType: hard

"sass-embedded-android-riscv64@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-android-riscv64@npm:1.80.2"
  conditions: os=android & cpu=riscv64
  languageName: node
  linkType: hard

"sass-embedded-android-x64@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-android-x64@npm:1.80.2"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"sass-embedded-darwin-arm64@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-darwin-arm64@npm:1.80.2"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"sass-embedded-darwin-x64@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-darwin-x64@npm:1.80.2"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"sass-embedded-linux-arm64@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-linux-arm64@npm:1.80.2"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"sass-embedded-linux-arm@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-linux-arm@npm:1.80.2"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"sass-embedded-linux-ia32@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-linux-ia32@npm:1.80.2"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"sass-embedded-linux-musl-arm64@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-linux-musl-arm64@npm:1.80.2"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"sass-embedded-linux-musl-arm@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-linux-musl-arm@npm:1.80.2"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"sass-embedded-linux-musl-ia32@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-linux-musl-ia32@npm:1.80.2"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"sass-embedded-linux-musl-riscv64@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-linux-musl-riscv64@npm:1.80.2"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"sass-embedded-linux-musl-x64@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-linux-musl-x64@npm:1.80.2"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"sass-embedded-linux-riscv64@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-linux-riscv64@npm:1.80.2"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"sass-embedded-linux-x64@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-linux-x64@npm:1.80.2"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"sass-embedded-win32-arm64@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-win32-arm64@npm:1.80.2"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"sass-embedded-win32-ia32@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-win32-ia32@npm:1.80.2"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"sass-embedded-win32-x64@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded-win32-x64@npm:1.80.2"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"sass-embedded@npm:1.80.2":
  version: 1.80.2
  resolution: "sass-embedded@npm:1.80.2"
  dependencies:
    "@bufbuild/protobuf": ^2.0.0
    buffer-builder: ^0.2.0
    colorjs.io: ^0.5.0
    immutable: ^4.0.0
    rxjs: ^7.4.0
    sass-embedded-android-arm: 1.80.2
    sass-embedded-android-arm64: 1.80.2
    sass-embedded-android-ia32: 1.80.2
    sass-embedded-android-riscv64: 1.80.2
    sass-embedded-android-x64: 1.80.2
    sass-embedded-darwin-arm64: 1.80.2
    sass-embedded-darwin-x64: 1.80.2
    sass-embedded-linux-arm: 1.80.2
    sass-embedded-linux-arm64: 1.80.2
    sass-embedded-linux-ia32: 1.80.2
    sass-embedded-linux-musl-arm: 1.80.2
    sass-embedded-linux-musl-arm64: 1.80.2
    sass-embedded-linux-musl-ia32: 1.80.2
    sass-embedded-linux-musl-riscv64: 1.80.2
    sass-embedded-linux-musl-x64: 1.80.2
    sass-embedded-linux-riscv64: 1.80.2
    sass-embedded-linux-x64: 1.80.2
    sass-embedded-win32-arm64: 1.80.2
    sass-embedded-win32-ia32: 1.80.2
    sass-embedded-win32-x64: 1.80.2
    supports-color: ^8.1.1
    varint: ^6.0.0
  dependenciesMeta:
    sass-embedded-android-arm:
      optional: true
    sass-embedded-android-arm64:
      optional: true
    sass-embedded-android-ia32:
      optional: true
    sass-embedded-android-riscv64:
      optional: true
    sass-embedded-android-x64:
      optional: true
    sass-embedded-darwin-arm64:
      optional: true
    sass-embedded-darwin-x64:
      optional: true
    sass-embedded-linux-arm:
      optional: true
    sass-embedded-linux-arm64:
      optional: true
    sass-embedded-linux-ia32:
      optional: true
    sass-embedded-linux-musl-arm:
      optional: true
    sass-embedded-linux-musl-arm64:
      optional: true
    sass-embedded-linux-musl-ia32:
      optional: true
    sass-embedded-linux-musl-riscv64:
      optional: true
    sass-embedded-linux-musl-x64:
      optional: true
    sass-embedded-linux-riscv64:
      optional: true
    sass-embedded-linux-x64:
      optional: true
    sass-embedded-win32-arm64:
      optional: true
    sass-embedded-win32-ia32:
      optional: true
    sass-embedded-win32-x64:
      optional: true
  bin:
    sass: dist/bin/sass.js
  checksum: 095525edde367edca82e2dee3b9b2e8106884b92662b6e1d05e65fd971cc1434156755770b79e1acba443c9333546b84b7a52ab40f34c041fe2a2e32b18a7d1b
  languageName: node
  linkType: hard

"saxes@npm:^6.0.0":
  version: 6.0.0
  resolution: "saxes@npm:6.0.0"
  dependencies:
    xmlchars: ^2.2.0
  checksum: d3fa3e2aaf6c65ed52ee993aff1891fc47d5e47d515164b5449cbf5da2cbdc396137e55590472e64c5c436c14ae64a8a03c29b9e7389fc6f14035cf4e982ef3b
  languageName: node
  linkType: hard

"scslre@npm:^0.3.0":
  version: 0.3.0
  resolution: "scslre@npm:0.3.0"
  dependencies:
    "@eslint-community/regexpp": ^4.8.0
    refa: ^0.12.0
    regexp-ast-analysis: ^0.7.0
  checksum: a89d4fe5dbf632cae14cc1e53c9d18012924cc88d6615406ad90190d2b9957fc8db16994c2023235af1b6a6c25290b089eb4c26e47d21b05073b933be5ca9d33
  languageName: node
  linkType: hard

"scule@npm:^1.3.0":
  version: 1.3.0
  resolution: "scule@npm:1.3.0"
  checksum: f2968b292e33c0eddca4a68b5c70f08dfc8479e492461c248f72873deaf77ae87c9f27dde7a342b3cb6394d2fae9665890b07a2243f79cff5cba65c9525ccf7e
  languageName: node
  linkType: hard

"semver@npm:2 || 3 || 4 || 5":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: fb4ab5e0dd1c22ce0c937ea390b4a822147a9c53dbd2a9a0132f12fe382902beef4fbf12cf51bb955248d8d15874ce8cd89532569756384f994309825f10b686
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.3.6, semver@npm:^7.5.3, semver@npm:^7.5.4, semver@npm:^7.6.0, semver@npm:^7.6.3":
  version: 7.6.3
  resolution: "semver@npm:7.6.3"
  bin:
    semver: bin/semver.js
  checksum: 4110ec5d015c9438f322257b1c51fe30276e5f766a3f64c09edd1d7ea7118ecbc3f379f3b69032bacf13116dc7abc4ad8ce0d7e2bd642e26b0d271b56b61a7d8
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: ^1.1.4
    es-errors: ^1.3.0
    function-bind: ^1.1.2
    get-intrinsic: ^1.2.4
    gopd: ^1.0.1
    has-property-descriptors: ^1.0.2
  checksum: a8248bdacdf84cb0fab4637774d9fb3c7a8e6089866d04c817583ff48e14149c87044ce683d7f50759a8c50fb87c7a7e173535b06169c87ef76f5fb276dfff72
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: ^1.1.4
    es-errors: ^1.3.0
    functions-have-names: ^1.2.3
    has-property-descriptors: ^1.0.2
  checksum: d6229a71527fd0404399fc6227e0ff0652800362510822a291925c9d7b48a1ca1a468b11b281471c34cd5a2da0db4f5d7ff315a61d26655e77f6e971e6d0c80f
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: be18cbbf70e7d8097c97f713a2e76edf84e87299b40d085c6bf8b65314e994cc15e2e317727342fa6996e38e1f52c59720b53fe621e2eb593a6847bf0356db89
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.4, side-channel@npm:^1.0.6":
  version: 1.0.6
  resolution: "side-channel@npm:1.0.6"
  dependencies:
    call-bind: ^1.0.7
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.4
    object-inspect: ^1.13.1
  checksum: bfc1afc1827d712271453e91b7cd3878ac0efd767495fd4e594c4c2afaa7963b7b510e249572bfd54b0527e66e4a12b61b80c061389e129755f34c493aad9b97
  languageName: node
  linkType: hard

"siginfo@npm:^2.0.0":
  version: 2.0.0
  resolution: "siginfo@npm:2.0.0"
  checksum: 8aa5a98640ca09fe00d74416eca97551b3e42991614a3d1b824b115fc1401543650914f651ab1311518177e4d297e80b953f4cd4cd7ea1eabe824e8f2091de01
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1, signal-exit@npm:^4.1.0":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 64c757b498cb8629ffa5f75485340594d2f8189e9b08700e69199069c8e3070fb3e255f7ab873c05dc0b3cec412aea7402e10a5990cb6a050bd33ba062a6c549
  languageName: node
  linkType: hard

"sirv@npm:^2.0.4":
  version: 2.0.4
  resolution: "sirv@npm:2.0.4"
  dependencies:
    "@polka/url": ^1.0.0-next.24
    mrmime: ^2.0.0
    totalist: ^3.0.0
  checksum: 6853384a51d6ee9377dd657e2b257e0e98b29abbfbfa6333e105197f0f100c8c56a4520b47028b04ab1833cf2312526206f38fcd4f891c6df453f40da1a15a57
  languageName: node
  linkType: hard

"sisteransi@npm:^1.0.5":
  version: 1.0.5
  resolution: "sisteransi@npm:1.0.5"
  checksum: aba6438f46d2bfcef94cf112c835ab395172c75f67453fe05c340c770d3c402363018ae1ab4172a1026a90c47eaccf3af7b6ff6fa749a680c2929bd7fa2b37a4
  languageName: node
  linkType: hard

"slashes@npm:^3.0.12":
  version: 3.0.12
  resolution: "slashes@npm:3.0.12"
  checksum: 6b68feb5a56d53d76acd4729b0e457f47a0b687877161ca2c05486ec0bc750e0694b37094b2f5f00a339dfe490269292c4197a70da7eba2be47bc56e35f10a60
  languageName: node
  linkType: hard

"slice-ansi@npm:^5.0.0":
  version: 5.0.0
  resolution: "slice-ansi@npm:5.0.0"
  dependencies:
    ansi-styles: ^6.0.0
    is-fullwidth-code-point: ^4.0.0
  checksum: 7e600a2a55e333a21ef5214b987c8358fe28bfb03c2867ff2cbf919d62143d1812ac27b4297a077fdaf27a03da3678e49551c93e35f9498a3d90221908a1180e
  languageName: node
  linkType: hard

"slice-ansi@npm:^7.1.0":
  version: 7.1.0
  resolution: "slice-ansi@npm:7.1.0"
  dependencies:
    ansi-styles: ^6.2.1
    is-fullwidth-code-point: ^5.0.0
  checksum: 10313dd3cf7a2e4b265f527b1684c7c568210b09743fd1bd74f2194715ed13ffba653dc93a5fa79e3b1711518b8990a732cb7143aa01ddafe626e99dfa6474b2
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: b5167a7142c1da704c0e3af85c402002b597081dd9575031a90b4f229ca5678e9a36e8a374f1814c8156a725d17008ae3bde63b92f9cfd132526379e580bec8b
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: ^7.1.2
    debug: ^4.3.4
    socks: ^2.8.3
  checksum: b4fbcdb7ad2d6eec445926e255a1fb95c975db0020543fbac8dfa6c47aecc6b3b619b7fb9c60a3f82c9b2969912a5e7e174a056ae4d98cb5322f3524d6036e1d
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.3
  resolution: "socks@npm:2.8.3"
  dependencies:
    ip-address: ^9.0.5
    smart-buffer: ^4.2.0
  checksum: 7a6b7f6eedf7482b9e4597d9a20e09505824208006ea8f2c49b71657427f3c137ca2ae662089baa73e1971c62322d535d9d0cf1c9235cf6f55e315c18203eadd
  languageName: node
  linkType: hard

"source-map-js@npm:^1.0.1, source-map-js@npm:^1.0.2, source-map-js@npm:^1.2.0, source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 4eb0cd997cdf228bc253bcaff9340afeb706176e64868ecd20efbe6efea931465f43955612346d6b7318789e5265bdc419bc7669c1cebe3db0eb255f57efa76b
  languageName: node
  linkType: hard

"source-map@npm:~0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 59ce8640cf3f3124f64ac289012c2b8bd377c238e316fb323ea22fbfe83da07d81e000071d7242cad7a23cd91c7de98e4df8830ec3f133cb6133a5f6e9f67bc2
  languageName: node
  linkType: hard

"sourcemap-codec@npm:^1.4.8":
  version: 1.4.8
  resolution: "sourcemap-codec@npm:1.4.8"
  checksum: b57981c05611afef31605732b598ccf65124a9fcb03b833532659ac4d29ac0f7bfacbc0d6c5a28a03e84c7510e7e556d758d0bb57786e214660016fb94279316
  languageName: node
  linkType: hard

"spdx-correct@npm:^3.0.0":
  version: 3.2.0
  resolution: "spdx-correct@npm:3.2.0"
  dependencies:
    spdx-expression-parse: ^3.0.0
    spdx-license-ids: ^3.0.0
  checksum: e9ae98d22f69c88e7aff5b8778dc01c361ef635580e82d29e5c60a6533cc8f4d820803e67d7432581af0cc4fb49973125076ee3b90df191d153e223c004193b2
  languageName: node
  linkType: hard

"spdx-exceptions@npm:^2.1.0":
  version: 2.5.0
  resolution: "spdx-exceptions@npm:2.5.0"
  checksum: bb127d6e2532de65b912f7c99fc66097cdea7d64c10d3ec9b5e96524dbbd7d20e01cba818a6ddb2ae75e62bb0c63d5e277a7e555a85cbc8ab40044984fa4ae15
  languageName: node
  linkType: hard

"spdx-expression-parse@npm:^3.0.0":
  version: 3.0.1
  resolution: "spdx-expression-parse@npm:3.0.1"
  dependencies:
    spdx-exceptions: ^2.1.0
    spdx-license-ids: ^3.0.0
  checksum: a1c6e104a2cbada7a593eaa9f430bd5e148ef5290d4c0409899855ce8b1c39652bcc88a725259491a82601159d6dc790bedefc9016c7472f7de8de7361f8ccde
  languageName: node
  linkType: hard

"spdx-expression-parse@npm:^4.0.0":
  version: 4.0.0
  resolution: "spdx-expression-parse@npm:4.0.0"
  dependencies:
    spdx-exceptions: ^2.1.0
    spdx-license-ids: ^3.0.0
  checksum: 936be681fbf5edeec3a79c023136479f70d6edb3fd3875089ac86cd324c6c8c81add47399edead296d1d0af17ae5ce88c7f88885eb150b62c2ff6e535841ca6a
  languageName: node
  linkType: hard

"spdx-license-ids@npm:^3.0.0":
  version: 3.0.20
  resolution: "spdx-license-ids@npm:3.0.20"
  checksum: 0c57750bedbcff48f3d0e266fbbdaf0aab54217e182f669542ffe0b5a902dce69e8cdfa126a131e1ddd39a9bef4662e357b2b41315d7240b4a28c0a7e782bb40
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: a3fdac7b49643875b70864a9d9b469d87a40dfeaf5d34d9d0c5b1cda5fd7d065531fcb43c76357d62254c57184a7b151954156563a4d6a747015cfb41021cad0
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: ^7.0.3
  checksum: ef4b6b0ae47b4a69896f5f1c4375f953b9435388c053c36d27998bc3d73e046969ccde61ab659e679142971a0b08e50478a1228f62edb994105b280f17900c98
  languageName: node
  linkType: hard

"stable-hash@npm:^0.0.4":
  version: 0.0.4
  resolution: "stable-hash@npm:0.0.4"
  checksum: 21c039d21c1cb739cf8342561753a5e007cb95ea682ccd452e76310bbb9c6987a89de8eda023e320b019f3e4691aabda75079cdbb7dadf7ab9013e931f2f23cd
  languageName: node
  linkType: hard

"stackback@npm:0.0.2":
  version: 0.0.2
  resolution: "stackback@npm:0.0.2"
  checksum: 2d4dc4e64e2db796de4a3c856d5943daccdfa3dd092e452a1ce059c81e9a9c29e0b9badba91b43ef0d5ff5c04ee62feb3bcc559a804e16faf447bac2d883aa99
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 18c7623fdb8f646fb213ca4051be4df7efb3484d4ab662937ca6fbef7ced9b9e12842709872eb3020cc3504b93bde88935c9f6417489627a7786f24f8031cbcb
  languageName: node
  linkType: hard

"std-env@npm:^3.8.0":
  version: 3.8.0
  resolution: "std-env@npm:3.8.0"
  checksum: ad4554485c2d09138a1d0f03944245e169510e6f5200b7d30fcdd4536e27a2a9a2fd934caff7ef58ebbe21993fa0e2b9e5b1979f431743c925305863b7ff36d5
  languageName: node
  linkType: hard

"stop-iteration-iterator@npm:^1.0.0":
  version: 1.0.0
  resolution: "stop-iteration-iterator@npm:1.0.0"
  dependencies:
    internal-slot: ^1.0.4
  checksum: d04173690b2efa40e24ab70e5e51a3ff31d56d699550cfad084104ab3381390daccb36652b25755e420245f3b0737de66c1879eaa2a8d4fc0a78f9bf892fcb42
  languageName: node
  linkType: hard

"string-argv@npm:~0.3.2":
  version: 0.3.2
  resolution: "string-argv@npm:0.3.2"
  checksum: 8703ad3f3db0b2641ed2adbb15cf24d3945070d9a751f9e74a924966db9f325ac755169007233e8985a39a6a292f14d4fee20482989b89b96e473c4221508a0f
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: ^0.2.0
    emoji-regex: ^9.2.2
    strip-ansi: ^7.0.1
  checksum: 7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string-width@npm:^7.0.0":
  version: 7.2.0
  resolution: "string-width@npm:7.2.0"
  dependencies:
    emoji-regex: ^10.3.0
    get-east-asian-width: ^1.0.0
    strip-ansi: ^7.1.0
  checksum: 42f9e82f61314904a81393f6ef75b832c39f39761797250de68c041d8ba4df2ef80db49ab6cd3a292923a6f0f409b8c9980d120f7d32c820b4a8a84a2598a295
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: ~5.2.0
  checksum: 8417646695a66e73aefc4420eb3b84cc9ffd89572861fe004e6aeb13c7bc00e2f616247505d2dbbef24247c372f70268f594af7126f43548565c68c117bdeb56
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: f3cd25890aef3ba6e1a74e20896c21a46f482e93df4a06567cebf2b57edabb15133f1f94e57434e0a958d61186087b1008e89c94875d019910a213181a14fc8c
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1, strip-ansi@npm:^7.1.0":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: ^6.0.1
  checksum: 859c73fcf27869c22a4e4d8c6acfe690064659e84bef9458aa6d13719d09ca88dcfd40cbf31fd0be63518ea1a643fe070b4827d353e09533a5b0b9fd4553d64d
  languageName: node
  linkType: hard

"strip-final-newline@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-final-newline@npm:3.0.0"
  checksum: 23ee263adfa2070cd0f23d1ac14e2ed2f000c9b44229aec9c799f1367ec001478469560abefd00c5c99ee6f0b31c137d53ec6029c53e9f32a93804e18c201050
  languageName: node
  linkType: hard

"strip-indent@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-indent@npm:3.0.0"
  dependencies:
    min-indent: ^1.0.0
  checksum: 18f045d57d9d0d90cd16f72b2313d6364fd2cb4bf85b9f593523ad431c8720011a4d5f08b6591c9d580f446e78855c5334a30fb91aa1560f5d9f95ed1b4a0530
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"strip-literal@npm:^2.1.1":
  version: 2.1.1
  resolution: "strip-literal@npm:2.1.1"
  dependencies:
    js-tokens: ^9.0.1
  checksum: 781f2018b2aa9e8e149882dfa35f4d284c244424e7b66cc62259796dbc4bc6da9d40f9206949ba12fa839f5f643d6c62a309f7eec4ff6e76ced15f0730f04831
  languageName: node
  linkType: hard

"strtok3@npm:^6.2.4":
  version: 6.3.0
  resolution: "strtok3@npm:6.3.0"
  dependencies:
    "@tokenizer/token": ^0.3.0
    peek-readable: ^4.1.0
  checksum: 90732cff3f325aef7c47c511f609b593e0873ec77b5081810071cde941344e6a0ee3ccb0cae1a9f5b4e12c81a2546fd6b322fabcdfbd1dd08362c2ce5291334a
  languageName: node
  linkType: hard

"supercluster@npm:^8.0.1":
  version: 8.0.1
  resolution: "supercluster@npm:8.0.1"
  dependencies:
    kdbush: ^4.0.2
  checksum: 39d141f768a511efa53260252f9dab9a2ce0228b334e55482c8d3019e151932f05e1a9a0252d681737651b13c741c665542a6ddb40ec27de96159ea7ad41f7f4
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-color@npm:^8.1.1":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: ^4.0.0
  checksum: c052193a7e43c6cdc741eb7f378df605636e01ad434badf7324f17fb60c69a880d8d8fcdcb562cf94c2350e57b937d7425ab5b8326c67c2adc48f7c87c1db406
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"symbol-tree@npm:^3.2.4":
  version: 3.2.4
  resolution: "symbol-tree@npm:3.2.4"
  checksum: 6e8fc7e1486b8b54bea91199d9535bb72f10842e40c79e882fc94fb7b14b89866adf2fd79efa5ebb5b658bc07fb459ccce5ac0e99ef3d72f474e74aaf284029d
  languageName: node
  linkType: hard

"synckit@npm:^0.6.0":
  version: 0.6.2
  resolution: "synckit@npm:0.6.2"
  dependencies:
    tslib: ^2.3.1
  checksum: 267965f9bc2df7e945630ac1e5c3bb28d0d9a639fa6ed0f68f7ed768ee95c8a96172553f52edcebfc8602ef1a8256a4299abc0707d7ac6af133aad4e9a1dd148
  languageName: node
  linkType: hard

"synckit@npm:^0.9.1, synckit@npm:^0.9.2":
  version: 0.9.2
  resolution: "synckit@npm:0.9.2"
  dependencies:
    "@pkgr/core": ^0.1.0
    tslib: ^2.6.2
  checksum: 3a30e828efbdcf3b50fccab4da6e90ea7ca24d8c5c2ad3ffe98e07d7c492df121e0f75227c6e510f96f976aae76f1fa4710cb7b1d69db881caf66ef9de89360e
  languageName: node
  linkType: hard

"tapable@npm:^2.2.0":
  version: 2.2.1
  resolution: "tapable@npm:2.2.1"
  checksum: 3b7a1b4d86fa940aad46d9e73d1e8739335efd4c48322cb37d073eb6f80f5281889bf0320c6d8ffcfa1a0dd5bfdbd0f9d037e252ef972aca595330538aac4d51
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": ^4.0.0
    chownr: ^3.0.0
    minipass: ^7.1.2
    minizlib: ^3.0.1
    mkdirp: ^3.0.1
    yallist: ^5.0.0
  checksum: 8485350c0688331c94493031f417df069b778aadb25598abdad51862e007c39d1dd5310702c7be4a6784731a174799d8885d2fde0484269aea205b724d7b2ffa
  languageName: node
  linkType: hard

"taze@npm:^0.18.0":
  version: 0.18.0
  resolution: "taze@npm:0.18.0"
  dependencies:
    "@antfu/ni": ^0.23.0
    js-yaml: ^4.1.0
    ofetch: ^1.4.1
    package-manager-detector: ^0.2.2
    tinyexec: ^0.3.1
    unconfig: ^0.6.0
    yargs: ^17.7.2
  bin:
    taze: bin/taze.mjs
  checksum: b8f468ed9038e7d7f582e6e01fca4a1dd3a2a9d81d4ecdaa00dd94011131e9d8bc03ec95d9a2cc8358ea66d920619fc37f66efecefffc053cadd3e38c0a5665c
  languageName: node
  linkType: hard

"tiny-sha256@npm:^1.0.2":
  version: 1.0.2
  resolution: "tiny-sha256@npm:1.0.2"
  checksum: 3b27cd48918a43fca1f8b2b40998a9754935842eb2cd992907fd68f869d22e862923b85c1cdb9088a99474893fd754efe92d9ccf77f7b5e6e7cbc9b6d0e44f88
  languageName: node
  linkType: hard

"tinybench@npm:^2.9.0":
  version: 2.9.0
  resolution: "tinybench@npm:2.9.0"
  checksum: 1ab00d7dfe0d1f127cbf00822bacd9024f7a50a3ecd1f354a8168e0b7d2b53a639a24414e707c27879d1adc0f5153141d51d76ebd7b4d37fe245e742e5d91fe8
  languageName: node
  linkType: hard

"tinyexec@npm:^0.3.0, tinyexec@npm:^0.3.1":
  version: 0.3.1
  resolution: "tinyexec@npm:0.3.1"
  checksum: 691b531d464bdc09eeba934e43d8ac2a74c9d22a4bec9cd7f4991375c64e22712f7e5a95ba243a9369a478afd34d41171359012a2248ea49615cd2816ab12959
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.10":
  version: 0.2.10
  resolution: "tinyglobby@npm:0.2.10"
  dependencies:
    fdir: ^6.4.2
    picomatch: ^4.0.2
  checksum: 7e2ffe262ebc149036bdef37c56b32d02d52cf09efa7d43dbdab2ea3c12844a4da881058835ce4c74d1891190e5ad5ec5133560a11ec8314849b68ad0d99d3f4
  languageName: node
  linkType: hard

"tinypool@npm:^1.0.1":
  version: 1.0.2
  resolution: "tinypool@npm:1.0.2"
  checksum: 752f23114d8fc95a9497fc812231d6d0a63728376aa11e6e8499c10423a91112e760e388887ea7854f1b16977c321f07c0eab061ec2f60f6761e58b184aac880
  languageName: node
  linkType: hard

"tinyrainbow@npm:^1.2.0":
  version: 1.2.0
  resolution: "tinyrainbow@npm:1.2.0"
  checksum: d1e2cb5400032c0092be00e4a3da5450bea8b4fad58bfb5d3c58ca37ff5c5e252f7fcfb9af247914854af79c46014add9d1042fe044358c305a129ed55c8be35
  languageName: node
  linkType: hard

"tinyspy@npm:^3.0.2":
  version: 3.0.2
  resolution: "tinyspy@npm:3.0.2"
  checksum: 5db671b2ff5cd309de650c8c4761ca945459d7204afb1776db9a04fb4efa28a75f08517a8620c01ee32a577748802231ad92f7d5b194dc003ee7f987a2a06337
  languageName: node
  linkType: hard

"tldts-core@npm:^6.1.66":
  version: 6.1.66
  resolution: "tldts-core@npm:6.1.66"
  checksum: a915d868b4cee4ce8054d957690d2deee8e75afb460b06dff423775360fdf1c3a642e5ad2b3215013cc6fb2249b1cf149de4de2cc5fcb5ba7ae530b9f2a8d318
  languageName: node
  linkType: hard

"tldts@npm:^6.1.32":
  version: 6.1.66
  resolution: "tldts@npm:6.1.66"
  dependencies:
    tldts-core: ^6.1.66
  bin:
    tldts: bin/cli.js
  checksum: 26d2cfe00f4275d98a4b53c43a116de69ecbdb7357336131c1358dcbc856b32f04d0dff7cb5482dc07f22a430c75a390d6b9df47b6d35f0d38017ece2fcd9128
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 952c29e2a85d7123239b5cfdd889a0dde47ab0497f0913d70588f19c53f7e0b5327c95f4651e413c74b785147f9637b17410ac8c846d5d4a20a5a33eb6dc3a45
  languageName: node
  linkType: hard

"token-types@npm:^4.1.1":
  version: 4.2.1
  resolution: "token-types@npm:4.2.1"
  dependencies:
    "@tokenizer/token": ^0.3.0
    ieee754: ^1.2.1
  checksum: cce256766b33e0f08ceffefa2198fb4961a417866d00780e58625999ab5c0699821407053e64eadc41b00bbb6c0d0c4d02fbd2199940d8a3ccb71e1b148ab9a2
  languageName: node
  linkType: hard

"toml-eslint-parser@npm:^0.10.0":
  version: 0.10.0
  resolution: "toml-eslint-parser@npm:0.10.0"
  dependencies:
    eslint-visitor-keys: ^3.0.0
  checksum: ddf934db9d9edd8e388bfa85564510f04be422669932c2e2f690ac1278aa523273e541d338109a87542b5c60401f83b4ded0451e5366af22419629534310f624
  languageName: node
  linkType: hard

"totalist@npm:^3.0.0":
  version: 3.0.1
  resolution: "totalist@npm:3.0.1"
  checksum: 5132d562cf88ff93fd710770a92f31dbe67cc19b5c6ccae2efc0da327f0954d211bbfd9456389655d726c624f284b4a23112f56d1da931ca7cfabbe1f45e778a
  languageName: node
  linkType: hard

"tough-cookie@npm:^5.0.0":
  version: 5.0.0
  resolution: "tough-cookie@npm:5.0.0"
  dependencies:
    tldts: ^6.1.32
  checksum: 774f6c939c96f74b5847361f7e11e0d69383681d21a35a2d37a20956638e614ec521782d2d20bcb32b58638ff337bba87cc72fb72c987bd02ea0fdfc93994cdb
  languageName: node
  linkType: hard

"tr46@npm:^5.0.0":
  version: 5.0.0
  resolution: "tr46@npm:5.0.0"
  dependencies:
    punycode: ^2.3.1
  checksum: 8d8b021f8e17675ebf9e672c224b6b6cfdb0d5b92141349e9665c14a2501c54a298d11264bbb0b17b447581e1e83d4fc3c038c929f3d210e3964d4be47460288
  languageName: node
  linkType: hard

"ts-api-utils@npm:^1.3.0":
  version: 1.4.3
  resolution: "ts-api-utils@npm:1.4.3"
  peerDependencies:
    typescript: ">=4.2.0"
  checksum: ea00dee382d19066b2a3d8929f1089888b05fec797e32e7a7004938eda1dccf2e77274ee2afcd4166f53fab9b8d7ee90ebb225a3183f9ba8817d636f688a148d
  languageName: node
  linkType: hard

"tslib@npm:^2.1.0, tslib@npm:^2.3.0, tslib@npm:^2.3.1, tslib@npm:^2.6.2, tslib@npm:^2.6.3":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: e4aba30e632b8c8902b47587fd13345e2827fa639e7c3121074d5ee0880723282411a8838f830b55100cbe4517672f84a2472667d355b81e8af165a55dc6203a
  languageName: node
  linkType: hard

"tsx@npm:^4.19.0, tsx@npm:^4.19.1":
  version: 4.19.2
  resolution: "tsx@npm:4.19.2"
  dependencies:
    esbuild: ~0.23.0
    fsevents: ~2.3.3
    get-tsconfig: ^4.7.5
  dependenciesMeta:
    fsevents:
      optional: true
  bin:
    tsx: dist/cli.mjs
  checksum: 7f9f1b338a73297725a9217cedaaad862f7c81d5264093c74b98a71491ad5413b11248d604c0e650f4f7da6f365249f1426fdb58a1325ab9e15448156b1edff6
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: ^1.2.1
  checksum: ec688ebfc9c45d0c30412e41ca9c0cdbd704580eb3a9ccf07b9b576094d7b86a012baebc95681999dd38f4f444afd28504cb3a89f2ef16b31d4ab61a0739025a
  languageName: node
  linkType: hard

"type-fest@npm:^0.20.2":
  version: 0.20.2
  resolution: "type-fest@npm:0.20.2"
  checksum: 4fb3272df21ad1c552486f8a2f8e115c09a521ad7a8db3d56d53718d0c907b62c6e9141ba5f584af3f6830d0872c521357e512381f24f7c44acae583ad517d73
  languageName: node
  linkType: hard

"type-fest@npm:^0.6.0":
  version: 0.6.0
  resolution: "type-fest@npm:0.6.0"
  checksum: b2188e6e4b21557f6e92960ec496d28a51d68658018cba8b597bd3ef757721d1db309f120ae987abeeda874511d14b776157ff809f23c6d1ce8f83b9b2b7d60f
  languageName: node
  linkType: hard

"type-fest@npm:^0.8.1":
  version: 0.8.1
  resolution: "type-fest@npm:0.8.1"
  checksum: d61c4b2eba24009033ae4500d7d818a94fd6d1b481a8111612ee141400d5f1db46f199c014766b9fa9b31a6a7374d96fc748c6d688a78a3ce5a33123839becb7
  languageName: node
  linkType: hard

"type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: 0.3.0
    mime-types: ~2.1.24
  checksum: 2c8e47675d55f8b4e404bcf529abdf5036c537a04c2b20177bcf78c9e3c1da69da3942b1346e6edb09e823228c0ee656ef0e033765ec39a70d496ef601a0c657
  languageName: node
  linkType: hard

"typescript@npm:^5.7.2":
  version: 5.7.2
  resolution: "typescript@npm:5.7.2"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: b55300c4cefee8ee380d14fa9359ccb41ff8b54c719f6bc49b424899d662a5ce62ece390ce769568c7f4d14af844085255e63788740084444eb12ef423b13433
  languageName: node
  linkType: hard

"typescript@patch:typescript@^5.7.2#~builtin<compat/typescript>":
  version: 5.7.2
  resolution: "typescript@patch:typescript@npm%3A5.7.2#~builtin<compat/typescript>::version=5.7.2&hash=5da071"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 803430c6da2ba73c25a21880d8d4f08a56d9d2444e6db2ea949ac4abceeece8e4a442b7b9b585db7d8a0b47ebda2060e45fe8ee8b8aca23e27ec1d4844987ee6
  languageName: node
  linkType: hard

"ufo@npm:^1.5.4":
  version: 1.5.4
  resolution: "ufo@npm:1.5.4"
  checksum: f244703b7d4f9f0df4f9af23921241ab73410b591f4e5b39c23e3147f3159b139a4b1fb5903189c306129f7a16b55995dac0008e0fbae88a37c3e58cbc34d833
  languageName: node
  linkType: hard

"unconfig@npm:^0.6.0":
  version: 0.6.0
  resolution: "unconfig@npm:0.6.0"
  dependencies:
    "@antfu/utils": ^0.7.10
    defu: ^6.1.4
    importx: ^0.5.0
  checksum: e1bdf957713bac3d5431717f29b2cea34d2b1c944f54ce5426dabcc0cd4b42678ef4d0c4be3ed0ef61f8d1fc666f2c36c0823317bebfc02a333b892b31cc07b0
  languageName: node
  linkType: hard

"unconfig@npm:~0.5.5":
  version: 0.5.5
  resolution: "unconfig@npm:0.5.5"
  dependencies:
    "@antfu/utils": ^0.7.10
    defu: ^6.1.4
    importx: ^0.4.3
  checksum: 0af3a91091217fef15507680a2cc5594de10a6dcf5989356204ab31102860294329873b6c3b369e64b223df2491aa68139bb5fe400fbe64454a0449b786738d8
  languageName: node
  linkType: hard

"undici-types@npm:~5.26.4":
  version: 5.26.5
  resolution: "undici-types@npm:5.26.5"
  checksum: 3192ef6f3fd5df652f2dc1cd782b49d6ff14dc98e5dced492aa8a8c65425227da5da6aafe22523c67f035a272c599bb89cfe803c1db6311e44bed3042fc25487
  languageName: node
  linkType: hard

"undici-types@npm:~6.20.0":
  version: 6.20.0
  resolution: "undici-types@npm:6.20.0"
  checksum: b7bc50f012dc6afbcce56c9fd62d7e86b20a62ff21f12b7b5cbf1973b9578d90f22a9c7fe50e638e96905d33893bf2f9f16d98929c4673c2480de05c6c96ea8b
  languageName: node
  linkType: hard

"unhead@npm:1.11.14":
  version: 1.11.14
  resolution: "unhead@npm:1.11.14"
  dependencies:
    "@unhead/dom": 1.11.14
    "@unhead/schema": 1.11.14
    "@unhead/shared": 1.11.14
    hookable: ^5.5.3
  checksum: e81e66d5e9ed0f95324a8f8bb4f71af5c5d63e274f92714b529ae6d07e4f288307bed7adaec5ea177c9c7e73d599196f9f6f6a994bde6f5eab71d5cf26020ef6
  languageName: node
  linkType: hard

"unimport@npm:^3.13.4":
  version: 3.14.4
  resolution: "unimport@npm:3.14.4"
  dependencies:
    "@rollup/pluginutils": ^5.1.3
    acorn: ^8.14.0
    escape-string-regexp: ^5.0.0
    estree-walker: ^3.0.3
    local-pkg: ^0.5.1
    magic-string: ^0.30.14
    mlly: ^1.7.3
    pathe: ^1.1.2
    picomatch: ^4.0.2
    pkg-types: ^1.2.1
    scule: ^1.3.0
    strip-literal: ^2.1.1
    tinyglobby: ^0.2.10
    unplugin: ^1.16.0
  checksum: ff556997c4ed128abb5ec6a05260fe5ced7962ec1bf0abc86f2ce9c1b8a0f6e77b6b6cbbb7f5ec9c781cd17ad6689dca1fed660d67da9046fa5a459cce6282cb
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: ^5.0.0
  checksum: 6a62094fcac286b9ec39edbd1f8f64ff92383baa430af303dfed1ffda5e47a08a6b316408554abfddd9730c78b6106bef4ca4d02c1231a735ddd56ced77573df
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 222d0322bc7bbf6e45c08967863212398313ef73423f4125e075f893a02405a5ffdbaaf150f7dd1e99f8861348a486dd079186d27c5f2c60e465b7dcbb1d3e5b
  languageName: node
  linkType: hard

"unist-util-is@npm:^6.0.0":
  version: 6.0.0
  resolution: "unist-util-is@npm:6.0.0"
  dependencies:
    "@types/unist": ^3.0.0
  checksum: f630a925126594af9993b091cf807b86811371e465b5049a6283e08537d3e6ba0f7e248e1e7dab52cfe33f9002606acef093441137181b327f6fe504884b20e2
  languageName: node
  linkType: hard

"unist-util-stringify-position@npm:^4.0.0":
  version: 4.0.0
  resolution: "unist-util-stringify-position@npm:4.0.0"
  dependencies:
    "@types/unist": ^3.0.0
  checksum: e2e7aee4b92ddb64d314b4ac89eef7a46e4c829cbd3ee4aee516d100772b490eb6b4974f653ba0717a0071ca6ea0770bf22b0a2ea62c65fcba1d071285e96324
  languageName: node
  linkType: hard

"unist-util-visit-parents@npm:^6.0.0":
  version: 6.0.1
  resolution: "unist-util-visit-parents@npm:6.0.1"
  dependencies:
    "@types/unist": ^3.0.0
    unist-util-is: ^6.0.0
  checksum: 08927647c579f63b91aafcbec9966dc4a7d0af1e5e26fc69f4e3e6a01215084835a2321b06f3cbe7bf7914a852830fc1439f0fc3d7153d8804ac3ef851ddfa20
  languageName: node
  linkType: hard

"unist-util-visit@npm:^5.0.0":
  version: 5.0.0
  resolution: "unist-util-visit@npm:5.0.0"
  dependencies:
    "@types/unist": ^3.0.0
    unist-util-is: ^6.0.0
    unist-util-visit-parents: ^6.0.0
  checksum: 9ec42e618e7e5d0202f3c191cd30791b51641285732767ee2e6bcd035931032e3c1b29093f4d7fd0c79175bbc1f26f24f26ee49770d32be76f8730a652a857e6
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: ecd8469fe0db28e7de9e5289d32bd1b6ba8f7183db34f3bfc4ca53c49891c2d6aa05f3fb3936a81285a905cc509fb641a0c3fc131ec786167eff41236ae32e60
  languageName: node
  linkType: hard

"unocss@npm:^0.65.1":
  version: 0.65.1
  resolution: "unocss@npm:0.65.1"
  dependencies:
    "@unocss/astro": 0.65.1
    "@unocss/cli": 0.65.1
    "@unocss/core": 0.65.1
    "@unocss/postcss": 0.65.1
    "@unocss/preset-attributify": 0.65.1
    "@unocss/preset-icons": 0.65.1
    "@unocss/preset-mini": 0.65.1
    "@unocss/preset-tagify": 0.65.1
    "@unocss/preset-typography": 0.65.1
    "@unocss/preset-uno": 0.65.1
    "@unocss/preset-web-fonts": 0.65.1
    "@unocss/preset-wind": 0.65.1
    "@unocss/transformer-attributify-jsx": 0.65.1
    "@unocss/transformer-compile-class": 0.65.1
    "@unocss/transformer-directives": 0.65.1
    "@unocss/transformer-variant-group": 0.65.1
    "@unocss/vite": 0.65.1
  peerDependencies:
    "@unocss/webpack": 0.65.1
    vite: ^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0
  peerDependenciesMeta:
    "@unocss/webpack":
      optional: true
    vite:
      optional: true
  checksum: 7e8ffcefb15cd26ef6f99adcc6c04a1db6f03f7ec92eceb02e53d1ff0a55ce63c50f17eeab1b245662a9a02ac0b4a11417f0d8f208aca9470cb9c6041f27dbed
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 4fa18d8d8d977c55cb09715385c203197105e10a6d220087ec819f50cb68870f02942244f1017565484237f1f8c5d3cd413631b1ae104d3096f24fdfde1b4aa2
  languageName: node
  linkType: hard

"unplugin-ast@npm:^0.12.0":
  version: 0.12.0
  resolution: "unplugin-ast@npm:0.12.0"
  dependencies:
    "@antfu/utils": ^0.7.10
    "@babel/generator": ^7.26.2
    "@rollup/pluginutils": ^5.1.3
    ast-kit: ^1.3.1
    magic-string-ast: ^0.6.3
    unplugin: ^2.0.0-beta.1
  checksum: 4c350b8e6b3dd392b31892b376fcc783d45742629384c2aa04aa31d9b5278da5a462f0f49c6edcea0d443aa2925122e383c67596287c0bbf4de224b8b2f89d43
  languageName: node
  linkType: hard

"unplugin-auto-import@npm:^0.18.6":
  version: 0.18.6
  resolution: "unplugin-auto-import@npm:0.18.6"
  dependencies:
    "@antfu/utils": ^0.7.10
    "@rollup/pluginutils": ^5.1.3
    fast-glob: ^3.3.2
    local-pkg: ^0.5.1
    magic-string: ^0.30.14
    minimatch: ^9.0.5
    unimport: ^3.13.4
    unplugin: ^1.16.0
  peerDependencies:
    "@nuxt/kit": ^3.2.2
    "@vueuse/core": "*"
  peerDependenciesMeta:
    "@nuxt/kit":
      optional: true
    "@vueuse/core":
      optional: true
  checksum: 0c60e7045db06815ceb51891a60525c8740b0b943dc2c7df67f1d8267ed45e06e85f7a50cd158c799120f010a27649c51802446465940a6dfe6a13bd2d5515cd
  languageName: node
  linkType: hard

"unplugin-icons@npm:^0.21.0":
  version: 0.21.0
  resolution: "unplugin-icons@npm:0.21.0"
  dependencies:
    "@antfu/install-pkg": ^0.5.0
    "@antfu/utils": ^0.7.10
    "@iconify/utils": ^2.1.33
    debug: ^4.3.7
    kolorist: ^1.8.0
    local-pkg: ^0.5.1
    unplugin: ^1.16.0
  peerDependencies:
    "@svgr/core": ">=7.0.0"
    "@svgx/core": ^1.0.1
    "@vue/compiler-sfc": ^3.0.2 || ^2.7.0
    svelte: ^3.0.0 || ^4.0.0 || ^5.0.0
    vue-template-compiler: ^2.6.12
    vue-template-es2015-compiler: ^1.9.0
  peerDependenciesMeta:
    "@svgr/core":
      optional: true
    "@svgx/core":
      optional: true
    "@vue/compiler-sfc":
      optional: true
    svelte:
      optional: true
    vue-template-compiler:
      optional: true
    vue-template-es2015-compiler:
      optional: true
  checksum: 13bb84d2bfdb6e40244ba65e8b2c8acfda18241efc248aa49619bf3d6d71f7f2d35819301981322398215175a14db99fd941a371a3bcf1e8c42ca06cdeb84b19
  languageName: node
  linkType: hard

"unplugin-vue-components@npm:^0.27.5":
  version: 0.27.5
  resolution: "unplugin-vue-components@npm:0.27.5"
  dependencies:
    "@antfu/utils": ^0.7.10
    "@rollup/pluginutils": ^5.1.3
    chokidar: ^3.6.0
    debug: ^4.3.7
    fast-glob: ^3.3.2
    local-pkg: ^0.5.1
    magic-string: ^0.30.14
    minimatch: ^9.0.5
    mlly: ^1.7.3
    unplugin: ^1.16.0
  peerDependencies:
    "@babel/parser": ^7.15.8
    "@nuxt/kit": ^3.2.2
    vue: 2 || 3
  peerDependenciesMeta:
    "@babel/parser":
      optional: true
    "@nuxt/kit":
      optional: true
  checksum: 597074b63bcaa6d3fa27e7d13287bfc56de9958ac9f29f01863f7f4ee28dcad7fed0634b02d5d53c66277fbe68ca7b2e11edfe17ea0495142251490d1d155ff7
  languageName: node
  linkType: hard

"unplugin-vue-router@npm:^0.10.9":
  version: 0.10.9
  resolution: "unplugin-vue-router@npm:0.10.9"
  dependencies:
    "@babel/types": ^7.26.0
    "@rollup/pluginutils": ^5.1.3
    "@vue-macros/common": ^1.15.0
    ast-walker-scope: ^0.6.2
    chokidar: ^3.6.0
    fast-glob: ^3.3.2
    json5: ^2.2.3
    local-pkg: ^0.5.1
    magic-string: ^0.30.14
    mlly: ^1.7.3
    pathe: ^1.1.2
    scule: ^1.3.0
    unplugin: 2.0.0-beta.1
    yaml: ^2.6.1
  peerDependencies:
    vue-router: ^4.4.0
  peerDependenciesMeta:
    vue-router:
      optional: true
  checksum: 359cbc8493362619830cc72ab8f378f59e02e8c6b3f54ac196d283f81a29af8b7a2c413542bc695fd87a77306fdea96092cf7d380def2d003767bf7a4412a19b
  languageName: node
  linkType: hard

"unplugin@npm:2.0.0-beta.1":
  version: 2.0.0-beta.1
  resolution: "unplugin@npm:2.0.0-beta.1"
  dependencies:
    acorn: ^8.14.0
    webpack-virtual-modules: ^0.6.2
  checksum: 62baae6ac7721844d5da3dac22f67cd66bea492ae6e7adaf5fcdd0bf390589473748a79db34ea028380583f17cc68829edfea081d9fb5b6ddcfb9a96eecb3d77
  languageName: node
  linkType: hard

"unplugin@npm:^1.1.0, unplugin@npm:^1.16.0":
  version: 1.16.0
  resolution: "unplugin@npm:1.16.0"
  dependencies:
    acorn: ^8.14.0
    webpack-virtual-modules: ^0.6.2
  checksum: 84bff88dd8fd6ba88bd21dad1b170fea2a91f7ff8ddcfadf826297cf77dfe305f3428f1612c0637f30d7ac10d668491f15fdf8f378dd56def370fdbc16edd85e
  languageName: node
  linkType: hard

"unplugin@npm:^2.0.0-beta.1":
  version: 2.0.0
  resolution: "unplugin@npm:2.0.0"
  dependencies:
    acorn: ^8.14.0
    webpack-virtual-modules: ^0.6.2
  checksum: 9004bc85c4d8ea37266170e6b976d293f9deb2dae04e69fc84fe40c6efd7353b3d815c079edec7fbe6ab6816c04aa2efe66cef6d088dedca551f3d043c8b1c3a
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.1":
  version: 1.1.1
  resolution: "update-browserslist-db@npm:1.1.1"
  dependencies:
    escalade: ^3.2.0
    picocolors: ^1.1.0
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 2ea11bd2562122162c3e438d83a1f9125238c0844b6d16d366e3276d0c0acac6036822dc7df65fc5a89c699cdf9f174acf439c39bedf3f9a2f3983976e4b4c3e
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 7167432de6817fe8e9e0c9684f1d2de2bb688c94388f7569f7dbdb1587c9f4ca2a77962f134ec90be0cc4d004c939ff0d05acc9f34a0db39a3c797dada262633
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:^1.0.2":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"uvu@npm:^0.5.0":
  version: 0.5.6
  resolution: "uvu@npm:0.5.6"
  dependencies:
    dequal: ^2.0.0
    diff: ^5.0.0
    kleur: ^4.0.3
    sade: ^1.7.3
  bin:
    uvu: bin.js
  checksum: 09460a37975627de9fcad396e5078fb844d01aaf64a6399ebfcfd9e55f1c2037539b47611e8631f89be07656962af0cf48c334993db82b9ae9c3d25ce3862168
  languageName: node
  linkType: hard

"validate-npm-package-license@npm:^3.0.1":
  version: 3.0.4
  resolution: "validate-npm-package-license@npm:3.0.4"
  dependencies:
    spdx-correct: ^3.0.0
    spdx-expression-parse: ^3.0.0
  checksum: 35703ac889d419cf2aceef63daeadbe4e77227c39ab6287eeb6c1b36a746b364f50ba22e88591f5d017bc54685d8137bc2d328d0a896e4d3fd22093c0f32a9ad
  languageName: node
  linkType: hard

"varint@npm:^6.0.0":
  version: 6.0.0
  resolution: "varint@npm:6.0.0"
  checksum: 7684113c9d497c01e40396e50169c502eb2176203219b96e1c5ac965a3e15b4892bd22b7e48d87148e10fffe638130516b6dbeedd0efde2b2d0395aa1772eea7
  languageName: node
  linkType: hard

"vite-node@npm:2.1.8":
  version: 2.1.8
  resolution: "vite-node@npm:2.1.8"
  dependencies:
    cac: ^6.7.14
    debug: ^4.3.7
    es-module-lexer: ^1.5.4
    pathe: ^1.1.2
    vite: ^5.0.0
  bin:
    vite-node: vite-node.mjs
  checksum: 17914342d05f9ace35c1574555c59dd4116148b71a22bf330f019681d7238a2244b6c2b4a8930d03d5f78e24666d81806c68b84a8db42d7e84165cb10d1c756a
  languageName: node
  linkType: hard

"vite-plugin-externals@npm:^0.6.2":
  version: 0.6.2
  resolution: "vite-plugin-externals@npm:0.6.2"
  dependencies:
    acorn: ^8.4.0
    es-module-lexer: ^0.4.1
    fs-extra: ^10.0.0
    magic-string: ^0.25.7
  peerDependencies:
    vite: ">=2.0.0"
  checksum: 585dcc432a7c3032b32516d8c30cd58e79578925a535eef0e9fe5a0d06c0efdd47bfe700129e89a2c6008380546f68427a3f122e48d0e602c775dec84737df96
  languageName: node
  linkType: hard

"vite@npm:^5.0.0, vite@npm:^5.4.11":
  version: 5.4.11
  resolution: "vite@npm:5.4.11"
  dependencies:
    esbuild: ^0.21.3
    fsevents: ~2.3.3
    postcss: ^8.4.43
    rollup: ^4.20.0
  peerDependencies:
    "@types/node": ^18.0.0 || >=20.0.0
    less: "*"
    lightningcss: ^1.21.0
    sass: "*"
    sass-embedded: "*"
    stylus: "*"
    sugarss: "*"
    terser: ^5.4.0
  dependenciesMeta:
    fsevents:
      optional: true
  peerDependenciesMeta:
    "@types/node":
      optional: true
    less:
      optional: true
    lightningcss:
      optional: true
    sass:
      optional: true
    sass-embedded:
      optional: true
    stylus:
      optional: true
    sugarss:
      optional: true
    terser:
      optional: true
  bin:
    vite: bin/vite.js
  checksum: 8c5b31d17487b69c40a30419dc0ade9f33360eb6893dbfa33a90980271bd74d35ae550b5cbb2a9e640f0df41ea36fd1bb4f222c98f6d02e607080f20832e69e8
  languageName: node
  linkType: hard

"vitest@npm:^2.1.8":
  version: 2.1.8
  resolution: "vitest@npm:2.1.8"
  dependencies:
    "@vitest/expect": 2.1.8
    "@vitest/mocker": 2.1.8
    "@vitest/pretty-format": ^2.1.8
    "@vitest/runner": 2.1.8
    "@vitest/snapshot": 2.1.8
    "@vitest/spy": 2.1.8
    "@vitest/utils": 2.1.8
    chai: ^5.1.2
    debug: ^4.3.7
    expect-type: ^1.1.0
    magic-string: ^0.30.12
    pathe: ^1.1.2
    std-env: ^3.8.0
    tinybench: ^2.9.0
    tinyexec: ^0.3.1
    tinypool: ^1.0.1
    tinyrainbow: ^1.2.0
    vite: ^5.0.0
    vite-node: 2.1.8
    why-is-node-running: ^2.3.0
  peerDependencies:
    "@edge-runtime/vm": "*"
    "@types/node": ^18.0.0 || >=20.0.0
    "@vitest/browser": 2.1.8
    "@vitest/ui": 2.1.8
    happy-dom: "*"
    jsdom: "*"
  peerDependenciesMeta:
    "@edge-runtime/vm":
      optional: true
    "@types/node":
      optional: true
    "@vitest/browser":
      optional: true
    "@vitest/ui":
      optional: true
    happy-dom:
      optional: true
    jsdom:
      optional: true
  bin:
    vitest: vitest.mjs
  checksum: 2d2f69364556829123c3ff704b36dfd7a2f11cc05fad8a7caf9f0b8c74205caee92f892d4bd5b92a9c2a48267e9b0865a171b2f40fcd593d681f980c3486b299
  languageName: node
  linkType: hard

"vscode-uri@npm:^3.0.8":
  version: 3.0.8
  resolution: "vscode-uri@npm:3.0.8"
  checksum: 514249126850c0a41a7d8c3c2836cab35983b9dc1938b903cfa253b9e33974c1416d62a00111385adcfa2b98df456437ab704f709a2ecca76a90134ef5eb4832
  languageName: node
  linkType: hard

"vue-advanced-chat@npm:^2.1.0":
  version: 2.1.0
  resolution: "vue-advanced-chat@npm:2.1.0"
  dependencies:
    emoji-picker-element: 1.12.1
    micromark: ^3.1.0
    micromark-extension-gfm: ^2.0.1
  checksum: 79d629ed7240ca345a7361f3ca83acd36d4db10fa5d85699a312f570bb3623db80f65f5e97d2dd52a6235bf8933dc55982489c8d63044161c36d05cb8a7ba954
  languageName: node
  linkType: hard

"vue-component-type-helpers@npm:^2.0.0":
  version: 2.1.10
  resolution: "vue-component-type-helpers@npm:2.1.10"
  checksum: 5261082e5c1077273fdafa1dc852b7dd26fcfa0ee2fa550116a3ff5126611e3de7894990d5f737bea22afbbe0cfbd3a9f3cfbe0f06689279c1f73a5d7dc3d733
  languageName: node
  linkType: hard

"vue-demi@npm:^0.14.10":
  version: 0.14.10
  resolution: "vue-demi@npm:0.14.10"
  peerDependencies:
    "@vue/composition-api": ^1.0.0-rc.1
    vue: ^3.0.0-0 || ^2.6.0
  peerDependenciesMeta:
    "@vue/composition-api":
      optional: true
  bin:
    vue-demi-fix: bin/vue-demi-fix.js
    vue-demi-switch: bin/vue-demi-switch.js
  checksum: 9b4106f99be3b0c1dd4a6dc5725f5e8f79c6b98d1eeb849bf2c54416cd77f4aa344960b202768865245cfa82d57f49a9d96f67f5d8e256604b9dac1c5df9a8d6
  languageName: node
  linkType: hard

"vue-eslint-parser@npm:^9.4.3":
  version: 9.4.3
  resolution: "vue-eslint-parser@npm:9.4.3"
  dependencies:
    debug: ^4.3.4
    eslint-scope: ^7.1.1
    eslint-visitor-keys: ^3.3.0
    espree: ^9.3.1
    esquery: ^1.4.0
    lodash: ^4.17.21
    semver: ^7.3.6
  peerDependencies:
    eslint: ">=6.0.0"
  checksum: 8d5b7ef7c5ee264ca2ba78da4b95ac7a66175a458d153a35e92cd7c55b794db0f2c31a8fdd40021bab4496f2f64ab80d7dbb6dccff4103beb4564c439a88fa42
  languageName: node
  linkType: hard

"vue-flow-layout@npm:^0.1.1":
  version: 0.1.1
  resolution: "vue-flow-layout@npm:0.1.1"
  peerDependencies:
    vue: ^3.4.37
  checksum: 854345f79f0e00fcd5162bb8d1aa7de8eb51f3d83822050b35b33ec6a65dd01122d6c60f6a6f3447796fc64e68a7a1d9e92892078d1e3b5fcae804c35d84c181
  languageName: node
  linkType: hard

"vue-i18n@npm:^10.0.0":
  version: 10.0.5
  resolution: "vue-i18n@npm:10.0.5"
  dependencies:
    "@intlify/core-base": 10.0.5
    "@intlify/shared": 10.0.5
    "@vue/devtools-api": ^6.5.0
  peerDependencies:
    vue: ^3.0.0
  checksum: 23a23a623e7b56c0a108bbb36c4ea1dd1ff506f0665a10178847d3a8b7696b8c26f9cb3f356a8926dfd8cbb9e116ff6853ac32f2751e8abea8604bd8121757c6
  languageName: node
  linkType: hard

"vue-router@npm:^4.5.0":
  version: 4.5.0
  resolution: "vue-router@npm:4.5.0"
  dependencies:
    "@vue/devtools-api": ^6.6.4
  peerDependencies:
    vue: ^3.2.0
  checksum: 2604db5a6b33c5079e3ba1a3aabf5693b4a112899b7cb2c44521961cf60677b48efdf87bb76a284ce938d70ca39e5e46e2b5dbe59f684ca519526bc6cd91efaa
  languageName: node
  linkType: hard

"vue-tsc@npm:^2.1.10":
  version: 2.1.10
  resolution: "vue-tsc@npm:2.1.10"
  dependencies:
    "@volar/typescript": ~2.4.8
    "@vue/language-core": 2.1.10
    semver: ^7.5.4
  peerDependencies:
    typescript: ">=5.0.0"
  bin:
    vue-tsc: ./bin/vue-tsc.js
  checksum: a64ad7b4388a1e2679cc0a53b6ecd7e26e5fda131ada06d7f02040c650616bdf93532482224f0e719a912b4d3222ec64997171f4721a29cf734b964141629691
  languageName: node
  linkType: hard

"vue3-google-map@npm:^0.21.0":
  version: 0.21.0
  resolution: "vue3-google-map@npm:0.21.0"
  dependencies:
    "@googlemaps/js-api-loader": ^1.16.2
    "@googlemaps/markerclusterer": ^2.4.0
    fast-deep-equal: ^3.1.3
  peerDependencies:
    vue: ^3
  checksum: a8ae8b9b8897909da2078b16d71127dcd3ddea5e94448841ca1fe48d9786877b17edc1610c89a6f457fd2b2f3f2f41167f27ad7c72a93451ae312e438bbb4bdd
  languageName: node
  linkType: hard

"vue@npm:^3.4, vue@npm:^3.5.13":
  version: 3.5.13
  resolution: "vue@npm:3.5.13"
  dependencies:
    "@vue/compiler-dom": 3.5.13
    "@vue/compiler-sfc": 3.5.13
    "@vue/runtime-dom": 3.5.13
    "@vue/server-renderer": 3.5.13
    "@vue/shared": 3.5.13
  peerDependencies:
    typescript: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 3d435ffaae2736234bbcf30cd14e88631c07ebb9447d2b4815d9dc9a1254b8d9368048a453c7fe29d2d87194052e1fef85b12c1e72ea64bda3ec816a19c22560
  languageName: node
  linkType: hard

"w3c-xmlserializer@npm:^5.0.0":
  version: 5.0.0
  resolution: "w3c-xmlserializer@npm:5.0.0"
  dependencies:
    xml-name-validator: ^5.0.0
  checksum: 593acc1fdab3f3207ec39d851e6df0f3fa41a36b5809b0ace364c7a6d92e351938c53424a7618ce8e0fbaffee8be2e8e070a5734d05ee54666a8bdf1a376cc40
  languageName: node
  linkType: hard

"webidl-conversions@npm:^7.0.0":
  version: 7.0.0
  resolution: "webidl-conversions@npm:7.0.0"
  checksum: f05588567a2a76428515333eff87200fae6c83c3948a7482ebb109562971e77ef6dc49749afa58abb993391227c5697b3ecca52018793e0cb4620a48f10bd21b
  languageName: node
  linkType: hard

"webpack-virtual-modules@npm:^0.6.2":
  version: 0.6.2
  resolution: "webpack-virtual-modules@npm:0.6.2"
  checksum: 7e8e1d63f35864c815420cc2f27da8561a1e028255040698a352717de0ba46d3b3faf16f06c1a1965217054c4c2894eb9af53a85451870e919b5707ce9c5822d
  languageName: node
  linkType: hard

"whatwg-encoding@npm:^3.1.1":
  version: 3.1.1
  resolution: "whatwg-encoding@npm:3.1.1"
  dependencies:
    iconv-lite: 0.6.3
  checksum: f75a61422421d991e4aec775645705beaf99a16a88294d68404866f65e92441698a4f5b9fa11dd609017b132d7b286c3c1534e2de5b3e800333856325b549e3c
  languageName: node
  linkType: hard

"whatwg-fetch@npm:^3.0.0":
  version: 3.6.20
  resolution: "whatwg-fetch@npm:3.6.20"
  checksum: c58851ea2c4efe5c2235f13450f426824cf0253c1d45da28f45900290ae602a20aff2ab43346f16ec58917d5562e159cd691efa368354b2e82918c2146a519c5
  languageName: node
  linkType: hard

"whatwg-mimetype@npm:^4.0.0":
  version: 4.0.0
  resolution: "whatwg-mimetype@npm:4.0.0"
  checksum: f97edd4b4ee7e46a379f3fb0e745de29fe8b839307cc774300fd49059fcdd560d38cb8fe21eae5575b8f39b022f23477cc66e40b0355c2851ce84760339cef30
  languageName: node
  linkType: hard

"whatwg-url@npm:^14.0.0":
  version: 14.1.0
  resolution: "whatwg-url@npm:14.1.0"
  dependencies:
    tr46: ^5.0.0
    webidl-conversions: ^7.0.0
  checksum: e429d1d2a5fc1b7886d9343f5b03d91201a9a32726b13e48a7fb943cf94c276771f6aa648337ae520484deb25b657ce6ad19a90dfca0d2d1c9596e21b438e3a0
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.0.2":
  version: 1.1.0
  resolution: "which-boxed-primitive@npm:1.1.0"
  dependencies:
    is-bigint: ^1.1.0
    is-boolean-object: ^1.2.0
    is-number-object: ^1.1.0
    is-string: ^1.1.0
    is-symbol: ^1.1.0
  checksum: 49ebec9693ed21ee8183b9e353ee7134a03722776c84624019964124885a4a940f469af3d1508ad83022a68cc515aecbef70fb1256ace57a871c43d24d050304
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.1":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: ^2.0.3
    is-set: ^2.0.3
    is-weakmap: ^2.0.2
    is-weakset: ^2.0.3
  checksum: c51821a331624c8197916598a738fc5aeb9a857f1e00d89f5e4c03dc7c60b4032822b8ec5696d28268bb83326456a8b8216344fb84270d18ff1d7628051879d9
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.13":
  version: 1.1.16
  resolution: "which-typed-array@npm:1.1.16"
  dependencies:
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.7
    for-each: ^0.3.3
    gopd: ^1.0.1
    has-tostringtag: ^1.0.2
  checksum: 903d398ec234d608011e1df09af6c004e66965bb24d5e1a82856cba0495fa6389ae393d1c9d5411498a9cba8e61b2e39a8e8be7b3005cbeadd317f772b1bdaef
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: ^3.1.1
  bin:
    node-which: bin/which.js
  checksum: 6ec99e89ba32c7e748b8a3144e64bfc74aa63e2b2eacbb61a0060ad0b961eb1a632b08fb1de067ed59b002cec3e21de18299216ebf2325ef0f78e0f121e14e90
  languageName: node
  linkType: hard

"why-is-node-running@npm:^2.3.0":
  version: 2.3.0
  resolution: "why-is-node-running@npm:2.3.0"
  dependencies:
    siginfo: ^2.0.0
    stackback: 0.0.2
  bin:
    why-is-node-running: cli.js
  checksum: 58ebbf406e243ace97083027f0df7ff4c2108baf2595bb29317718ef207cc7a8104e41b711ff65d6fa354f25daa8756b67f2f04931a4fd6ba9d13ae8197496fb
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: f93ba3586fc181f94afdaff3a6fef27920b4b6d9eaefed0f428f8e07adea2a7f54a5f2830ce59406c8416f033f86902b91eb824072354645eea687dff3691ccb
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: a790b846fd4505de962ba728a21aaeda189b8ee1c7568ca5e817d85930e06ef8d1689d49dbf0e881e8ef84436af3a88bc49115c2e2788d841ff1b8b5b51a608b
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: ^6.1.0
    string-width: ^5.0.1
    strip-ansi: ^7.0.1
  checksum: 371733296dc2d616900ce15a0049dca0ef67597d6394c57347ba334393599e800bab03c41d4d45221b6bc967b8c453ec3ae4749eff3894202d16800fdfe0e238
  languageName: node
  linkType: hard

"wrap-ansi@npm:^9.0.0":
  version: 9.0.0
  resolution: "wrap-ansi@npm:9.0.0"
  dependencies:
    ansi-styles: ^6.2.1
    string-width: ^7.0.0
    strip-ansi: ^7.1.0
  checksum: b2d43b76b3d8dcbdd64768165e548aad3e54e1cae4ecd31bac9966faaa7cf0b0345677ad6879db10ba58eb446ba8fa44fb82b4951872fd397f096712467a809f
  languageName: node
  linkType: hard

"ws@npm:^8.18.0":
  version: 8.18.0
  resolution: "ws@npm:8.18.0"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 91d4d35bc99ff6df483bdf029b9ea4bfd7af1f16fc91231a96777a63d263e1eabf486e13a2353970efc534f9faa43bdbf9ee76525af22f4752cbc5ebda333975
  languageName: node
  linkType: hard

"xml-name-validator@npm:^4.0.0":
  version: 4.0.0
  resolution: "xml-name-validator@npm:4.0.0"
  checksum: af100b79c29804f05fa35aa3683e29a321db9b9685d5e5febda3fa1e40f13f85abc40f45a6b2bf7bee33f68a1dc5e8eaef4cec100a304a9db565e6061d4cb5ad
  languageName: node
  linkType: hard

"xml-name-validator@npm:^5.0.0":
  version: 5.0.0
  resolution: "xml-name-validator@npm:5.0.0"
  checksum: 86effcc7026f437701252fcc308b877b4bc045989049cfc79b0cc112cb365cf7b009f4041fab9fb7cd1795498722c3e9fe9651afc66dfa794c16628a639a4c45
  languageName: node
  linkType: hard

"xmlchars@npm:^2.2.0":
  version: 2.2.0
  resolution: "xmlchars@npm:2.2.0"
  checksum: 8c70ac94070ccca03f47a81fcce3b271bd1f37a591bf5424e787ae313fcb9c212f5f6786e1fa82076a2c632c0141552babcd85698c437506dfa6ae2d58723062
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 54f0fb95621ee60898a38c572c515659e51cc9d9f787fb109cef6fde4befbe1c4602dc999d30110feee37456ad0f1660fa2edcfde6a9a740f86a290999550d30
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: eba51182400b9f35b017daa7f419f434424410691bbc5de4f4240cc830fdef906b504424992700dc047f16b4d99100a6f8b8b11175c193f38008e9c96322b6a5
  languageName: node
  linkType: hard

"yaml-eslint-parser@npm:^1.2.1, yaml-eslint-parser@npm:^1.2.2, yaml-eslint-parser@npm:^1.2.3":
  version: 1.2.3
  resolution: "yaml-eslint-parser@npm:1.2.3"
  dependencies:
    eslint-visitor-keys: ^3.0.0
    lodash: ^4.17.21
    yaml: ^2.0.0
  checksum: e79910df831862feecef1f3ca96ef5d8b3f344be08af116387155aa987843689ea63f2cf611a092ac21368211a67441f8fc2643a098dff4cc4cdf7d4f0d48687
  languageName: node
  linkType: hard

"yaml@npm:^2.0.0, yaml@npm:^2.6.1, yaml@npm:~2.6.1":
  version: 2.6.1
  resolution: "yaml@npm:2.6.1"
  bin:
    yaml: bin.mjs
  checksum: 5cf2627f121dcf04ccdebce8e6cbac7c9983d465c4eab314f6fbdc13cda8a07f4e8f9c2252a382b30bcabe05ee3c683647293afd52eb37cbcefbdc7b6ebde9ee
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: ed2d96a616a9e3e1cc7d204c62ecc61f7aaab633dcbfab2c6df50f7f87b393993fe6640d017759fe112d0cb1e0119f2b4150a87305cc873fd90831c6a58ccf1c
  languageName: node
  linkType: hard

"yargs@npm:^17.7.2":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: ^8.0.1
    escalade: ^3.1.1
    get-caller-file: ^2.0.5
    require-directory: ^2.1.1
    string-width: ^4.2.3
    y18n: ^5.0.5
    yargs-parser: ^21.1.1
  checksum: 73b572e863aa4a8cbef323dd911d79d193b772defd5a51aab0aca2d446655216f5002c42c5306033968193bdbf892a7a4c110b0d77954a7fdf563e653967b56a
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard

"zhead@npm:^2.2.4":
  version: 2.2.4
  resolution: "zhead@npm:2.2.4"
  checksum: 22836aa6947b27d35176cde40e3f5b19c24e0f46ee52d860e8df674a35aec07b34f6c78437323c797d1dc37ec0847e61e4f07ab605d519cc742245b5fa25b889
  languageName: node
  linkType: hard

"zwitch@npm:^2.0.0":
  version: 2.0.4
  resolution: "zwitch@npm:2.0.4"
  checksum: f22ec5fc2d5f02c423c93d35cdfa83573a3a3bd98c66b927c368ea4d0e7252a500df2a90a6b45522be536a96a73404393c958e945fdba95e6832c200791702b6
  languageName: node
  linkType: hard
