{
    // Place your snippets for vue here. Each snippet is defined under a snippet name and has a prefix, body and 
    // description. The prefix is what is used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
    // $1, $2 for tab stops, $0 for the final cursor position, and \${1:label}, \${2:another} for placeholders. Placeholders with the 
    // same ids are connected.
    // Example:
    "onBeforeMount": {
        "prefix": "onBeforeMount",
        "body": [
            "onBeforeMount(() => {$1})"
        ],
        "description": "onBeforeMount"
    },
    "onMounted": {
        "prefix": "onMounted",
        "body": [
            "onMounted(() => {$1})"
        ],
        "description": "onMounted"
    },
    "onBeforeUpdate": {
        "prefix": "onBeforeUpdate",
        "body": [
            "onBeforeUpdate(() => {$1})"
        ],
        "description": "onBeforeUpdate"
    },
    "onUpdated": {
        "prefix": "onUpdated",
        "body": [
            "onUpdated(() => {$1})"
        ],
        "description": "onUpdated"
    },
    "onBeforeUnmount": {
        "prefix": "onBeforeUnmount",
        "body": [
            "onBeforeUnmount(() => {$1})"
        ],
        "description": "onBeforeUnmount"
    },
    "onUnmounted": {
        "prefix": "onUnmounted",
        "body": [
            "onUnmounted(() => {$1})"
        ],
        "description": "onUnmounted"
    },
    "onErrorCaptured": {
        "prefix": "onErrorCaptured",
        "body": [
            "onErrorCaptured(() => {$1})"
        ],
        "description": "onErrorCaptured"
    },
    "import": {
        "prefix": "import",
        "body": [
            "import $1 from '$2'"
        ],
        "description": "import ... from ..."
    },
    "sfc": {
        "scope": "vue",
        "prefix": "sfc",
        "body": [
            "<script setup lang=\"ts\">",
            "  $2",
            "</script>",
            "",
            "<template>",
            "  $1",
            "</template>",
            "",
        ],
        "description": "Create <template> block"
    },
    "template": {
        "scope": "vue",
        "prefix": "template",
        "body": [
            "<template>",
            "  $1",
            "</template>"
        ],
        "description": "Create <template> block"
    },
    "dfprop": {
        "prefix": "dfprop",
        "body": [
            "const props = withDefaults(defineProps<{",
            "  $1?: $2",
            "}>(), {",
            "  $1: $3,",
            "})"
        ],
        "description": "dfprop"
    },
    "script": {
        "scope": "vue",
        "prefix": "script",
        "body": [
            "<script lang=\"ts\">",
            "import { defineComponent } from 'vue';",
            "",
            "export default defineComponent({",
            "  $2",
            "});",
            "</script>"
        ],
        "description": "Create <script> block"
    },
    "script setup": {
        "scope": "vue",
        "prefix": "script setup",
        "body": [
            "<script lang=\"ts\" setup>",
            "defineProps<{ $1 }>();",
            "</script>"
        ],
        "description": "Create <script setup> + <script> blocks"
    },
    "style": {
        "scope": "vue",
        "prefix": "style",
        "body": [
            "<style lang=\"scss\" scoped>",
            "$1",
            "</style>"
        ],
        "description": "Create <style> block"
    },
    "computed": {
        "prefix": "computed",
        "body": [
            "computed(() => {",
            "  $1",
            "})"
        ],
        "description": "Create computed block"
    },
}