<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <link rel="icon" href="https://www.playnext-lab.co.jp/wp-content/uploads/2020/06/pnl-shortcut-icon.png" type="image/svg+xml" />
    <title>Line OSS</title>
    <meta name="description" content="Line OSS" />
    <script src="https://unpkg.com/fuse.js@7.0.0/dist/fuse.min.js" defer></script>
  </head>
  <body class="font-sans dark:text-white dark:bg-hex-121212">
    <div id="app"></div>
    <noscript>
      <div>Please enable JavaScript to use this application.</div>
    </noscript>
    <script>
      ;(function () {
        const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        const setting = localStorage.getItem('color-schema') || 'auto'
        if (setting === 'dark' || (prefersDark && setting !== 'light'))
          document.documentElement.classList.toggle('dark', true)
      })()
    </script>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
