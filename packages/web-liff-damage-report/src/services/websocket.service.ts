import ReconnectingWebSocket from 'reconnecting-websocket'
import { CHAT_EVENTS } from '~/constants/chat.constant'

class WebSocketService {
  endpoint_ws_chat: string | undefined
  listeners: object
  token: any
  send: string
  websocketUrl: string
  socket: any

  constructor() {
    this.endpoint_ws_chat = import.meta.env.VITE_DAMAGE_REPORT_CHAT_WS_ENDPOINT
    this.listeners = {}
    this.initWebSocket()
  }

  initWebSocket() {
    const { liff } = useLiffStore()
    try {
      this.token = liff?.getAccessToken()
      this.send = 'liff'
      this.websocketUrl = `${this.endpoint_ws_chat}?token=${this.token}&from=${this.send}`
      this.socket = new ReconnectingWebSocket(this.websocketUrl, [], {
        maxRetries: 3,
      })

      this.socket.addEventListener('open', this.handleOpen.bind(this))
      this.socket.addEventListener('message', this.handleMessage.bind(this))
      this.socket.addEventListener('close', this.handleClose.bind(this))
      this.socket.addEventListener('error', this.handleError.bind(this))
    }
    catch (error) {
      console.error('Error initializing WebSocket:', error)
    }
  }

  on(event, callback) {
    if (!this.listeners[event]) {
      this.listeners[event] = []
    }
    this.listeners[event].push(callback)
  }

  off(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(
        listener => listener !== callback,
      )
    }
  }

  emit(event, payload) {
    if (this.listeners[event]) {
      this.listeners[event].forEach((listener) => {
        listener(payload)
      })
    }
  }

  handleOpen(event) {
    this.emit('open', 'success')
  }

  handleMessage(event) {
    try {
      const message = JSON.parse(event.data)
      if (
        message?.event_name === CHAT_EVENTS.SEND_MESSAGE
      ) {
        this.emit(CHAT_EVENTS.GET_MESSAGE, message?.payload)
      }
      if (message?.event_name === CHAT_EVENTS.READ) {
        this.emit(CHAT_EVENTS.READ, message?.payload)
      }
    }
    catch (error) {
      console.error('Error parsing WebSocket message:', error)
    }
  }

  handleClose(event) {
    // eslint-disable-next-line no-console
    console.log('WebSocket connection closed:', event)
  }

  handleError(event) {
    console.error('WebSocket error:', event)
  }

  sendMessage(message) {
    try {
      if (this.socket) {
        this.socket.send(JSON.stringify(message))
      }
      else {
        console.error('WebSocket connection is not open.')
      }
    }
    catch (error) {
      console.error('Error sending message via WebSocket:', error)
    }
  }

  close() {
    this.socket.close()
  }
}

export default WebSocketService
