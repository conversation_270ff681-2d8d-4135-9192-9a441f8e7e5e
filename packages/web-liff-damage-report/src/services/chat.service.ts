import type { Options } from 'ky'
import { createClient } from './helper/api-client'

const HOST = import.meta.env.VITE_DAMAGE_REPORT_API_ENDPOINT_URL
const BASE_PATH = '/liff'

export interface ChatServices {
  getMessages: (data: any) => Promise<unknown>
  getGroup: (data: { gid: string, uid: string }) => Promise<unknown>
  createGroup: (data: {
    // id: string
    gid: string
  }) => Promise<unknown>
  getFilePresignedUrl: (data: any) => Promise<unknown>
  updateUnread: (data: any) => Promise<unknown>
}

export function initChatService(clientOptions: Options): ChatServices {
  const apiClient = createClient({
    prefixUrl: HOST + BASE_PATH,
    ...clientOptions,
  })

  async function getMessages(data) {
    return await apiClient.post('chat/list-message', { json: data }).json()
    // return { data: DRAFT_DATA };
  }

  async function getGroup(data: { gid: string, uid: string }) {
    return await apiClient.get('chat/group', { searchParams: data }).json()
  }

  async function createGroup(data: {
    // id: string
    gid: string
    // role: string
    // name: string
  }) {
    return await apiClient.post('chat/group', { searchParams: data }).json()
  }

  async function getFilePresignedUrl(data = {}) {
    return await apiClient.post('utils/presigned-chat', { json: data }).json()
  }

  async function updateUnread(data = {}) {
    return await apiClient.put('chat/unread', { json: data }).json()
  }

  return { getMessages, getGroup, createGroup, getFilePresignedUrl, updateUnread }
}
