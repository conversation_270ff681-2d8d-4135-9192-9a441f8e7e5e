import type { Options } from 'ky'
import type { DamageReportItem, QueryDamageReportDto } from './types/damage-report'
import { createClient } from './helper/api-client'

const HOST = import.meta.env.VITE_DAMAGE_REPORT_API_ENDPOINT_URL
const BASE_PATH = '/liff'

export interface RectangleQuery {
  minPoint: {
    latitude: number
    longitude: number
  }
  maxPoint: {
    latitude: number
    longitude: number
  }
}
export function initDamageReportService(clientOptions: Options) {
  const apiClient = createClient({
    prefixUrl: HOST + BASE_PATH,
    ...clientOptions,
  })

  // #region crud
  async function QueryUserDamageReports(options?: QueryDamageReportDto): Promise<DamageReportItem[]> {
    return await apiClient.post('damage-report/query', {
      json: options,
    }).json()
  }

  async function QueryDamageReportsRectangle(options?: RectangleQuery): Promise<DamageReportItem[]> {
    return await apiClient.post('map/query-rectangle', {
      json: options,
    }).json()
  }
  async function QueryDamageReportsPublished(): Promise<DamageReportItem[]> {
    return await apiClient.get('damage-report/query-published', {
    }).json()
  }

  async function CreateDamageReports(options?) {
    return await apiClient.post('damage-report', {
      json: options,
    }).json()
  }
  async function GetDamageReportsByID(id: string, options?) {
    return await apiClient.get(`damage-report/${id}`, {
      json: options,
    }).json()
  }
  async function UpdateDamageReportsByID(id: string, reportData) {
    return await apiClient.put(`damage-report/${id}`, {
      json: reportData,
    }).json()
  }
  async function DeleteDamageReportsByID(id: string) {
    return await apiClient.delete(`damage-report/${id}`).json()
  }
  async function UserCancelDamageReportsByID(id: string) {
    return await apiClient.post(`damage-report/${id}/cancel`).json()
  }
  // #region S3
  interface PreSignedUploadPayload {
    type: 'image' | 'file'
    fileName: string
  }
  interface PreSignedDownloadPayload {
    fullKey: string
  }

  async function getPresignedUploadUrl(payload: PreSignedUploadPayload[]) {
    return await apiClient.post(`utils/upload-files`, {
      json: payload,
    }).json()
  }
  async function getTalkRelationship(payload: {
    scenario: string
    talkDrVersionId: string
  }) {
    return await apiClient.post(`utils/get-talk-relationship`, {
      json: payload,
    }).json()
  }
  async function getScenarioFormConfig(payload: {
    scenario: string
    talkDrVersionId: string
  }) {
    return await apiClient.post(`utils/get-scenario-config-form`, {
      json: payload,
    }).json()
  }
  async function UploadImagesS3(files: HTMLInputElement['files']) {
    if (!files)
      return

    const payload: PreSignedUploadPayload[] = []
    for (let i = 0; i < files.length; i++) {
      payload.push({
        type: 'image',
        fileName: files[i].name,
      })
    }

    const signedURL = await getPresignedUploadUrl(payload)

    if (!Array.isArray(signedURL)) {
      return 'Error getting presigned url'
    }

    const promises: Promise<any>[] = []
    const uploadResult: unknown[] = []
    for (let i = 0; i < files.length; i++) {
      const formData = new FormData()
      for (const [key, val] of Object.entries(signedURL[i].fields)) {
        formData.append(key, val as any)
      }
      formData.append('Content-Type', files[i].type)
      formData.append('file', files[i])

      promises.push(fetch(signedURL[i].url, {
        method: 'POST',
        body: formData,
      })
        .then((response) => {
          if (response.ok) {
            uploadResult[i] = {
              key: signedURL[i].fullKey,
            }
          }
        })
        .catch((error) => {
          return error
        }))
    }
    await Promise.allSettled(promises)
    return uploadResult
  }
  async function GetSignedImagesInfo(payload: PreSignedDownloadPayload[]) {
    if (Array.isArray(payload) && payload.length > 0) {
      return await apiClient.post(`utils/signed-files`, {
        json: payload,
      }).json()
    }
    return []
  }
  // #endregion
  return {
    QueryUserDamageReports,
    QueryDamageReportsRectangle,
    QueryDamageReportsPublished,
    CreateDamageReports,
    GetDamageReportsByID,
    UpdateDamageReportsByID,
    UserCancelDamageReportsByID,
    DeleteDamageReportsByID,
    UploadImagesS3,
    GetSignedImagesInfo,
    getTalkRelationship,
    getScenarioFormConfig,
  }
}
