import { useQuasar } from 'quasar'

export function useNotify() {
  const $q = useQuasar()
  const { t } = useI18n()

  function alertInfo(message: string) {
    $q.notify({
      message: t(message),
    })
  }
  function alertSuccess(message: string) {
    $q.notify({
      message: t(message),
      type: 'success',
    })
  }
  function alertAddSuccess(message: null | string = null) {
    $q.notify({
      message: t(message || 'INF_REGISTER_SUCCESS'),
      type: 'success',
    })
  }
  function alertUpdateSuccess(message: null | string = null) {
    $q.notify({
      message: t(message || 'INF_COM_0008'),
      type: 'success',
    })
  }
  function alertDeleteSuccess(message: null | string = null) {
    $q.notify({
      message: t(message || 'INF_COM_0018'),
      type: 'success',
    })
  }
  function alertUnknownError() {
    $q.notify({
      message: t('error.unknown'),
      type: 'negative',
    })
  }
  function alertError(message: null | string = null) {
    $q.notify({
      message: t(message || 'INF_COM_0004'),
      type: 'negative',
    })
  }

  return {
    alertInfo,
    alertSuccess,
    alertAddSuccess,
    alertUpdateSuccess,
    alertDeleteSuccess,
    alertUnknownError,
    alertError,
  }
}
