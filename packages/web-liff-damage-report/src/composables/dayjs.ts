import d from 'dayjs'
import locale from 'dayjs/locale/ja'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import localizedFormat from 'dayjs/plugin/localizedFormat'

// import plugin
import relativeTime from 'dayjs/plugin/relativeTime'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

d.locale(locale)
d.extend(relativeTime)
d.extend(customParseFormat)
d.extend(localizedFormat)
d.extend(utc)
d.extend(timezone)

export const dayjs = d

export function formatDateJP(date?: number, format: string = 'YYYY年MM月DD日(ddd) HH:mm', timezoneStr: string = 'Asia/Tokyo'): string {
  if (date == null) {
    return ''
  }

  const day = d.unix(date).tz(timezoneStr)
  return day.isValid() ? day.format(format) : ''
}
