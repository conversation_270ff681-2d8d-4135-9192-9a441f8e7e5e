<script setup lang="ts">
const { t } = useI18n()
const router = useRouter()
useLocaleTitle('home')
</script>

<template>
  <div class="grid grid-rows-[max-content_1fr] h-full gap-4 overflow-auto">
    <Header button-link="/" />
    <div p="4" text="xl" font="600" class="flex flex-col gap-4">
      <div border="b-2" p="1" class="flex cursor-pointer items-center justify-between" @click="router.push('report-history')">
        {{ t('page.report-history') }}
        <div class="i-mdi:chevron-right text-2xl" />
      </div>
      <div border="b-2" p="1" class="flex cursor-pointer items-center justify-between" @click="router.push('report-map')">
        {{ t('page.report-map') }}
        <div class="i-mdi:chevron-right text-2xl" />
      </div>
    </div>
  </div>
</template>
