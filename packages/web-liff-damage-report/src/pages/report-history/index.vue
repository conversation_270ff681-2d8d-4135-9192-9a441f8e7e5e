<script setup lang="ts">
const router = useRouter()
const { t } = useI18n()

const reportStore = useDamageReportStore()
const { listDamageReport, loadings } = storeToRefs(reportStore)
reportStore.queryUserDamageReports().then((res) => {
  listDamageReport.value = res
})
// const { liff } = storeToRefs(useLiffStore())

useLocaleTitle('report-history')
</script>

<template>
  <div class="grid grid-rows-[max-content_1fr] h-full overflow-auto">
    <Header button-link="/" :name="t('page.report-history')" :loading="loadings?.queryDamageReport" />
    <div class="overflow-y-scroll">
      <div v-if="listDamageReport?.length > 0" p="4" class="flex flex-col gap-4">
        <q-card v-for="(report) of listDamageReport" :key="report?.id">
          <div text="white xl" p="4" font="600" class="bg-primary flex items-center">
            {{ report.category }}
          </div>
          <div p="4" class="grid grid-cols-[max-content_1fr] gap-x-4 gap-y-2 text-lg">
            <div>
              {{ t('form.field.ID') }}
            </div>
            <div class="text-right">
              {{ report.id }}
            </div>

            <div>
              {{ t('form.field.CreatedAt') }}
            </div>
            <div class="text-right">
              {{ formatDateJP(report.createdAt) }}
            </div>
          </div>
          <div m="x-4" p="y-4" class="flex justify-evenly" border="t-2">
            <q-btn
              size="lg" rounded class="w-7em font-semibold" @click="router.push({
                name: '/report-history/detail/[_id]',
                params: {
                  _id: report.id,
                } })"
            >
              {{ t('btn.detail') }}
            </q-btn>
            <q-btn
              size="lg" rounded class="w-7em font-semibold" @click="router.push({
                name: '/report-history/chat/[_id]',
                params: {
                  _id: report.id,
                },
              })"
            >
              {{ t('btn.talk') }}
            </q-btn>
          </div>
        </q-card>
      </div>
      <div v-else-if="!loadings.queryDamageReport && listDamageReport?.length === 0" p="4" class="flex flex-col gap-4">
        <div class="text-primary text-center text-2xl" v-html="t('data.list.empty', { 0: '<br/>' })" />
      </div>
      <div v-else-if="loadings.queryDamageReport" class="flex items-center justify-center">
        <div class="i-line-md:loading-twotone-loop text-4xl" />
      </div>
    </div>
  </div>
</template>
