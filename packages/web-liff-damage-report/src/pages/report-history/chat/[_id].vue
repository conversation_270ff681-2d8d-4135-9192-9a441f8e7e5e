<script setup lang="ts">
useTitle('Report chat')
const router = useRouter()
const _id = useRouteParams('_id', '')

const { t } = useI18n()
const { userProfile } = useLiffStore()
const user = ref<ChatUser>()
const rooms = ref<ChatRoom[]>([])
if (userProfile) {
  // debug
  user.value = {
    _id: userProfile.userId,
    username: userProfile.displayName,
  }
  if (user.value) {
    rooms.value = [{
      roomId: _id.value,
      users: [
        {
          _id: user.value._id,
          username: user.value.username,
        },
        { _id: 'uid-manager-common', username: '管理者' },
      ],
    },
    ]
  }
}
useLocaleTitle('report-chat')
</script>

<template>
  <div class="grid grid-rows-[max-content_max-content_1fr] h-full overflow-auto">
    <Header button-link="/" :name="t('page.report-chat')" class="sticky top-0 z-100 backdrop-blur-md" btn-back="/report-history" />
    <div p="x-4 y-2" text="2xl" border="b-2" class="flex items-center justify-between">
      <div>
        <q-btn
          rounded class="text-primary bg-gray-100 font-semibold" @click="router.push({
            name: '/report-history/detail/[_id]',
            params: {
              _id: _id.toString(),
            },
          })"
        >
          {{ t('btn.detail') }}
        </q-btn>
      </div>
    </div>
    <ChatRoom v-if="user" :user="user" :rooms="rooms" />
  </div>
</template>
