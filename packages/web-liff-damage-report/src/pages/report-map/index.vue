<script setup lang="ts">
import type { Ref } from 'vue'
import Fuse from 'fuse.js'
import { ref, watch } from 'vue'
import {
  AdvancedMarker,
  CustomControl,
  GoogleMap,
  MarkerCluster,
} from 'vue3-google-map'

const apiKey = import.meta.env.VITE_GOOGLE_MAP_API_KEY

useLocaleTitle('report-map')
const { t } = useI18n()
const { userProfile } = useLiffStore()

const mapRef: Ref<any> = ref(null)
const mapLoaded = ref(false)

// get current location as center
// default tokyo
const TOKYO_COORDINATES = {
  lat: 35.6684103,
  lng: 139.5760601,
}
const reportStore = useDamageReportStore()

const { coords, locatedAt, pause } = useGeolocation()
const center = computed(() => {
  if (!locatedAt.value) {
    return TOKYO_COORDINATES
  }
  pause()
  return { lat: coords.value.latitude, lng: coords.value.longitude }
})

const selectOptionsReport = ref<any[]>([])
const markerList = ref<any[]>([])

// autocomplete search Report
const optionsFuseJS = {
  includeScore: true,
  isCaseSensitive: false,
  threshold: 0.4,
  shouldSort: true,
  minMatchCharLength: 0,
  keys: ['category'],
}
const fuse = new Fuse(markerList.value, optionsFuseJS)
function filterOptionsReport(val, update, abort) {
  update(() => {
    if (!val) {
      selectOptionsReport.value = markerList.value?.map((x: any) => {
        return {
          label: `${x.subject || x.category} (${x?.location?.lat}, ${x?.location?.long})`,
          value: x,
        }
      })
    }
    else {
      selectOptionsReport.value = fuse.search(val).map((x: any) => {
        return {
          label: `${x.subject || x.item.category} - ${x.item?.location?.lat} - ${x.item?.location?.long} - ${x.score}`,
          value: x.item,
        }
      })
    }
  })
}

// Pan to selected
const searchMapSelect = ref<any>()
watch(searchMapSelect, () => {
  if (searchMapSelect.value?.value?.location) {
    mapRef.value.map.panTo({ lat: searchMapSelect.value?.value?.location?.lat, lng: searchMapSelect.value?.value?.location?.long })
  }
})

// Debounce query Reqport when bounds change
// const debouncedonMapBoundsChange = useDebounceFn(() => {
//   const bounds = (mapRef.value.map as google.maps.Map).getBounds()
//   if (!bounds)
//     return
//   const maxPoint = bounds.getNorthEast()
//   const minPoint = bounds.getSouthWest()
//   reportStore.queryDamageReportsRectangle({
//     maxPoint: {
//       latitude: maxPoint.lat(),
//       longitude: maxPoint.lng(),
//     },
//     minPoint: {
//       latitude: minPoint.lat(),
//       longitude: minPoint.lng(),
//     },
//   })
// }, 1500)

watch(
  () => mapRef.value?.ready,
  async (ready) => {
    if (!ready)
      return

    // Map init ready => add listener
    // mapRef.value.map?.addListener('bounds_changed', debouncedonMapBoundsChange)
    mapRef.value.map?.addListener('click', onMapClick)

    const publishedReports = await reportStore.queryDamageReportsPublished()
    // const userReports = await reportStore.queryUserDamageReports()
    markerList.value = [
      // ...Array.isArray(userReports)
      //   ? userReports?.map((x) => {
      //     return {
      //       ...x,
      //       markerOptions: {
      //         position: { lat: x.location?.lat || center.value.lat, lng: x.location?.long || center.value.lng },
      //       },
      //       infoWindowOptions: {
      //         headerContent: x?.location?.address
      //           || `${x?.location?.lat?.toFixed(5)}, ${x?.location?.long?.toFixed(5)}`,
      //       },
      //       pinOptions: { },
      //     }
      //   })
      //   : [],

      ...Array.isArray(publishedReports)
        ? publishedReports.map((x) => {
            return {
              ...x,
              markerOptions: {
                position: { lat: x.location?.lat || center.value.lat, lng: x.location?.long || center.value.lng },
              },
              infoWindowOptions: {
                headerContent: x?.location?.address
                  || `${x?.location?.lat?.toFixed(5)}, ${x?.location?.long?.toFixed(5)}`,
              },
              pinOptions: x.uid !== userProfile?.userId ? { background: '#FBBC04' } : {},
            }
          })
        : [],
    ]

    if (markerList.value[0] && !locatedAt.value) {
      searchMapSelect.value = {
        label: `${markerList.value[0].subject || markerList.value[0].category} (${markerList.value[0]?.location?.lat}, ${markerList.value[0]?.location?.long})`,
        value: markerList.value[0],
      }
    }
    setTimeout(() => {
      mapLoaded.value = true
    }, 800)
  },
)

const isShowMarkerInfo = ref(false)
const curReport = ref<any>()
function markerClick(report, event) {
  // console.log(report, event)
  curReport.value = report
  isShowMarkerInfo.value = true
}
function onMapClick() {
  isShowMarkerInfo.value = false
}
</script>

<template>
  <div class="grid grid-rows-[max-content_1fr] h-full overflow-auto">
    <Header button-link="/" :name="t('page.report-map')" class="sticky top-0 z-100 backdrop-blur-md" btn-back :loading="!mapLoaded" />
    <GoogleMap
      ref="mapRef"
      :keyboard-shortcuts="false"
      :fullscreen-control="false"
      region="ja-Jpan"
      language="ja-Jpan"
      :disable-default-ui="true"
      :api-key="apiKey"
      map-id="DEMO_MAP_ID"
      style="width: 100%; height: 100%"
      :center="center"
      :zoom="14"
    >
      <CustomControl v-if="markerList.length > 0" position="TOP_LEFT" class="w-full">
        <div
          class="mx-6 mt-2 w-[calc(100%-4em)] flex items-center gap-4 rounded-full bg-white"
        >
          <q-select
            v-model="searchMapSelect"
            input-debounce="0"
            class="w-full" placeholder="Search"
            :options="selectOptionsReport"
            outlined dense clearable use-input hide-selected fill-input options-dense rounded
            @filter="filterOptionsReport"
          />
        </div>
      </CustomControl>

      <CustomControl v-if="isShowMarkerInfo" position="BOTTOM_CENTER" class="w-full">
        <div
          class="flex flex-col items-center bg-white"
        >
          <div class="relative w-full">
            <MapInfoWindow
              v-model="isShowMarkerInfo"
              :data="curReport"
            />
          </div>
          <div class="sticky bottom-0 h-8 w-full" />
        </div>
      </CustomControl>
      <MarkerCluster>
        <AdvancedMarker
          v-for="(report) of markerList"
          :key="report.id" :options="report.markerOptions" :pin-options="report.pinOptions" @click="markerClick(report, $event)"
        />
      </MarkerCluster>
    </GoogleMap>
  </div>
</template>
