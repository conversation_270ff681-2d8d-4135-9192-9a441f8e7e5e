import messages from '@intlify/unplugin-vue-i18n/messages'
import { createHead } from '@unhead/vue'
import { createPinia } from 'pinia'
// Quasar
import { Dialog, Notify, Quasar } from 'quasar'
import { createApp } from 'vue'
import { createI18n } from 'vue-i18n'
import { createRouter, createWebHistory } from 'vue-router'
import { routes } from 'vue-router/auto-routes'
import App from './App.vue'

import '@quasar/extras/material-icons/material-icons.css'
import 'quasar/src/css/index.sass'
import '@unocss/reset/tailwind.css'
import './styles/main.css'

import 'uno.css'

const app = createApp(App)
const router = createRouter({
  routes,
  history: createWebHistory(import.meta.env.BASE_URL),
})
app.use(router)

const head = createHead()
app.use(head)

app.use(Quasar, {
  plugins: {
    Notify,
    Dialog,
  }, // import Quasar plugins and add here
})

const pinia = createPinia()
app.use(pinia)

const localeSaved = useLocalStorage('locale', 'ja')
const i18n = createI18n({
  locale: localeSaved.value,
  messages,
  fallbackWarn: false,
  missingWarn: false,
  fallbackLocale: 'ja',
  missing: (lang, key) => `$${key}$`,
})
app.use(i18n)

app.mount('#app')
