/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ChatRoom: typeof import('./../components/ui/ChatRoom.vue')['default']
    DamageReportDynamicSelect: typeof import('./../components/ui/DamageReportDynamicSelect.vue')['default']
    Header: typeof import('./../components/layout/Header.vue')['default']
    ImageViewerDialog: typeof import('./../components/ui/ImageViewerDialog.vue')['default']
    InputImages: typeof import('./../components/ui/InputImages.vue')['default']
    LocaleChange: typeof import('./../components/ui/LocaleChange.vue')['default']
    MapInfoWindow: typeof import('./../components/ui/MapInfoWindow.vue')['default']
    ReportForm: typeof import('./../components/ui/ReportForm.vue')['default']
    ReportLocation: typeof import('./../components/ui/ReportLocation.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
