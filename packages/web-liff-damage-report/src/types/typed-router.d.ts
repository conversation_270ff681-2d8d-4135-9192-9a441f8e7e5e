/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/': RouteRecordInfo<'/', '/', Record<never, never>, Record<never, never>>,
    '/[...all]': RouteRecordInfo<'/[...all]', '/:all(.*)', { all: ParamValue<true> }, { all: ParamValue<false> }>,
    '/auth': RouteRecordInfo<'/auth', '/auth', Record<never, never>, Record<never, never>>,
    '/report-history/': RouteRecordInfo<'/report-history/', '/report-history', Record<never, never>, Record<never, never>>,
    '/report-history/chat/[_id]': RouteRecordInfo<'/report-history/chat/[_id]', '/report-history/chat/:_id', { _id: ParamValue<true> }, { _id: ParamValue<false> }>,
    '/report-history/detail/[_id]': RouteRecordInfo<'/report-history/detail/[_id]', '/report-history/detail/:_id', { _id: ParamValue<true> }, { _id: ParamValue<false> }>,
    '/report-map/': RouteRecordInfo<'/report-map/', '/report-map', Record<never, never>, Record<never, never>>,
  }
}
