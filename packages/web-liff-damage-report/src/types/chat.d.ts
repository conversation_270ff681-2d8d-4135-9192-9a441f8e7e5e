// https://github.com/advanced-chat/vue-advanced-chat?tab=readme-ov-file#messages-prop
interface ChatMessage {
  _id: string
  date: string
  content: string
  indexId?: number
  senderId: string
  username?: string
  avatar?: string
  timestamp: string
  system?: boolean
  saved?: boolean
  distributed?: boolean
  seen?: boolean
  deleted?: boolean
  failure?: boolean
  disableActions?: boolean
  disableReactions?: boolean
  files?: ChatMessageFile[]
  reactions?: ChatReactions
  replyMessage?: ChatReplyMessage
}
interface ChatReplyMessage {
  content: string
  senderId: string
  files?: ChatMessageFile[]
}
interface ChatReactions {
  [key: string]: string[]
}
interface ChatMessageFile {
  name: string
  size: number
  type: string
  audio?: boolean
  duration?: number
  url?: string
  preview?: string
  progress?: number
}
interface ChatSendFile {
  blob: any
  name: string
  size: number
  type: string
  extension: string
  localUrl?: string
  url?: string
}

interface ChatRoom {
  roomId: string
  roomName?: string
  users?: User[]
  unreadCount?: number
  avatar?: string
  index?: number
  lastMessage?: ChatMessage
  typingUsers?: number[]
}
interface ChatUser {
  _id: string
  username: string
  avatar?: string
  status?: Status
}
interface Status {
  state: 'online' | 'offline'
  lastChanged: string
}
