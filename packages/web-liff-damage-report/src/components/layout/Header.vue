<script setup lang="ts">
import type { RouteLocationRaw } from 'vue-router'

const props = withDefaults(defineProps<{
  name?: string
  buttonLink?: string | RouteLocationRaw
  buttonlabel?: string
  btnBack?: boolean | string
  loading?: boolean
  size?: string
}>(), {
  name: '',
  buttonLink: '',
  buttonlabel: '',
  btnBack: false,
  loading: false,
  size: 'md',
})
const { t } = useI18n()
const router = useRouter()
const enableI18N = import.meta.env.VITE_I18N_ENABLE === 'true'
</script>

<template>
  <div border="b-2" :p="size === 'sm' ? 'x-4 y-2' : 4" class="flex items-center gap-2">
    <div class="text-primary flex items-center gap-2 text-2xl font-semibold">
      {{ props.name || t('page.home') }}
      <div v-if="loading">
        <div class="i-line-md:loading-twotone-loop text-2xl" />
      </div>
    </div>
    <div class="ml-auto">
      <LocaleChange v-if="enableI18N" />
    </div>
    <div v-if="btnBack">
      <q-btn rounded :size="size" class="text-primary bg-gray-100 font-semibold" @click="typeof (btnBack) === 'string' ? router.push(btnBack) : router.go(-1)">
        <div class="i-mdi:chevron-left" :class="size === 'sm' ? 'text-lg' : 'text-xl'" />
      </q-btn>
    </div>
    <div v-if="buttonLink">
      <q-btn rounded :size="size" class="text-primary bg-gray-100 font-semibold" @click="router.push(props.buttonLink)">
        {{ props.buttonlabel || t('page.home') }}
      </q-btn>
    </div>
  </div>
</template>
