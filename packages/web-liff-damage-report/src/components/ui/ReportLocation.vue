<script setup lang="ts">
import type { DamageReportItem } from '~/services/types/damage-report'
import {
  AdvancedMarker,
  CustomControl,
  GoogleMap,
} from 'vue3-google-map'

const props = withDefaults(defineProps<{
  data: DamageReportItem
  mode: 'view' | 'edit'
}>(), {
  data: undefined,
  mode: 'view',
})

const emit = defineEmits(['changeLocation'])

const apiKey = import.meta.env.VITE_GOOGLE_MAP_API_KEY

const { t } = useI18n()
const dialog = defineModel<boolean>()

// default tokyo
const TOKYO_COORDINATES = {
  lat: 35.6684103,
  lng: 139.5760601,
}
const center = ref(props.data?.location?.lat && props.data?.location?.long ? { lat: props.data?.location?.lat, lng: props.data?.location?.long } : TOKYO_COORDINATES)
const mapRef = ref()
const markerOptions = ref({
  position: props.data?.location?.lat && props.data?.location?.long ? { lat: props.data?.location?.lat, lng: props.data?.location?.long } : TOKYO_COORDINATES,
})
const infoWindowOptions = {
  headerContent: `${props.data?.location?.lat?.toFixed(5)}, ${props.data?.location?.long?.toFixed(5)}`,
}
function buildGGMapLink(lat, long) {
  return `https://www.google.com/maps/search/?api=1&query=${lat},${long}`
}

const renderCurMarker = ref(0)

// report map info windows
const geocoder = ref()
const currentLocation = ref()
const isShowMarkerInfo = ref(false)
const curReport = ref<any>()
function markerClick(report, event) {
  // console.log(report, event)
  curReport.value = report
  isShowMarkerInfo.value = true
}
watch(
  () => mapRef.value?.ready,
  async (ready) => {
    if (!ready)
      return
    geocoder.value = new mapRef.value.api.Geocoder()
    mapRef.value.map?.addListener('click', onMapClick)
  },
)
// catch click event to set marker location
function onMapClick(mapsMouseEvent) {
  if (props.mode === 'edit') {
    markerOptions.value.position = mapsMouseEvent.latLng.toJSON()
  }
  else {
    isShowMarkerInfo.value = false
  }
  geocoder.value?.geocode({ location: mapsMouseEvent.latLng })
    .then((response) => {
      if (response.results[0]) {
        currentLocation.value = {
          lat: markerOptions.value.position.lat,
          long: markerOptions.value.position.lng,
          address: response.results[0].formatted_address,
        }
      }
    })
    .catch(e => console.error(`Geocoder failed due to: ${e}`))

  renderCurMarker.value++
}
function confirmPickLocation() {
  emit('changeLocation', currentLocation.value)
}
</script>

<template>
  <div class="q-pa-md q-gutter-sm">
    <q-dialog
      v-model="dialog"
      :maximized="true"
      transition-show="slide-up"
      transition-hide="slide-down"
    >
      <q-card class="grid grid-rows-[max-content_max-content_1fr]">
        <Header />
        <div class="flex items-center justify-end gap-2 bg-gray-100 p-1" border="b">
          <div class="ml-2 mr-auto">
            {{ markerOptions.position.lat?.toFixed(5) }}, {{ markerOptions.position.lng?.toFixed(5) }}
          </div>
          <q-btn
            v-if="mode === 'edit'"
            color="primary"
            rounded class="font-semibold" @click="confirmPickLocation"
          >
            {{ t('btn.confirm') }}
          </q-btn>
          <q-btn
            dense
            class="bg-gray-100 font-semibold" round @click="dialog = !dialog"
          >
            <div class="i-mdi-close p-2 text-xl" />
          </q-btn>
        </div>
        <div>
          <GoogleMap
            ref="mapRef"
            :keyboard-shortcuts="false"
            :fullscreen-control="false"
            region="ja-Jpan"
            language="ja-Jpan"
            :disable-default-ui="true"
            :api-key="apiKey"
            map-id="DEMO_MAP_ID"
            style="width: 100%; height: 100%"
            :center="center"
            :zoom="14"
          >
            <CustomControl v-if="mode === 'view' && isShowMarkerInfo" position="BOTTOM_CENTER" class="w-full">
              <div
                class="flex flex-col items-center bg-white"
              >
                <div class="relative w-full">
                  <MapInfoWindow
                    v-model="isShowMarkerInfo"
                    :data="curReport"
                  />
                </div>
                <div class="sticky bottom-0 h-8 w-full" />
              </div>
            </CustomControl>
            <AdvancedMarker :key="renderCurMarker" :options="markerOptions" @click="markerClick(data, $event)" />
          </GoogleMap>
        </div>
      </q-card>
    </q-dialog>
  </div>
</template>
