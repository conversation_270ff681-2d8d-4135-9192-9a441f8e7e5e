<script setup lang="ts">
import { ref } from 'vue'

const props = withDefaults(
  defineProps<{
    images: any[]
  }>(),
  {
    images: () => [],
  },
)
const dialog = defineModel<boolean>()
const imagesSlide = ref(0)
const imagesSlideFullscreen = ref(false)
</script>

<template>
  <div class="q-pa-md q-gutter-sm">
    <q-dialog
      v-model="dialog"
      :maximized="false"
      transition-show="slide-up"
      transition-hide="slide-down"
    >
      <q-card
        class="grid h-full w-full"
        style="grid-template-rows: max-content 1fr; max-width: 1280px"
      >
        <div
          class="flex items-center justify-end gap-2 border-b bg-gray-100 p-1"
        >
          <q-btn
            dense
            class="bg-gray-100 font-semibold"
            round
            icon="close"
            @click="dialog = !dialog"
          />
        </div>
        <div>
          <q-carousel
            v-model="imagesSlide"
            v-model:fullscreen="imagesSlideFullscreen"
            class="h-full w-full"
            control-color="primary"
            control-type="flat"
            dense animated navigation infinite swipeable arrows
          >
            <q-carousel-slide
              v-for="(img, index) of props.images"
              :key="img.url"
              class="bg-contain bg-no-repeat"
              :name="index"
              :img-src="img.url"
              @click="imagesSlideFullscreen = !imagesSlideFullscreen"
            />
          </q-carousel>
        </div>
      </q-card>
    </q-dialog>
  </div>
</template>
