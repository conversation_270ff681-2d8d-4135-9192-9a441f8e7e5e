<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    data: any
  }>(),
  {
  },
)
const isShow = defineModel()
const pinColorClass = computed(() => {
  return props.data.pinOptions?.background || '#ea4335'
})
</script>

<template>
  <div class="grid grid-rows-[max-content_1fr] max-h-40svh">
    <div class="flex items-center px-3 py-1 text-#ea4335" :style="`color:${pinColorClass}`">
      <div class="i-mdi:map-marker-star text-3xl" />
      <div class="text-xl">
        {{ data?.category }}
      </div>
      <q-btn
        dense
        class="ml-auto bg-gray-100 font-semibold"
        round
        icon="close"
        @click="isShow = !isShow"
      />
    </div>
    <ReportForm v-if="props.data" :model-value="props.data" mode="view" is-view-map class="overflow-y-scroll px-3" />
  </div>
</template>

<style scoped>
.parent {
  display: grid;
  gap: 0.5rem;
  margin: 0.5rem 0;
  grid-template-columns: auto 1fr;
}
.input-group {
  display: grid;
  min-height: 20px;
  grid-column: 1/-1;
  grid-template-columns: subgrid;
  justify-items: start;
  align-items: baseline;
}
</style>

<style>
.gm-style-iw-ch {
  font-weight: 600 !important;
}
</style>
