<script setup lang="ts">
const { locale } = useI18n()
const localeSaved = useLocalStorage('locale', 'ja')
const languageIconMapper = {
  ja: 'i-flag:jp-4x3',
  en: 'i-flag:us-4x3',
}
function toggleLocales() {
  locale.value = locale.value === 'ja' ? 'en' : 'ja'
  localeSaved.value = locale.value
  dayjs.locale(locale.value)
}
</script>

<template>
  <div class="h-8 w-8 flex cursor-pointer items-center justify-center">
    <q-btn round size="sm" @click="toggleLocales">
      <div class="h-5 w-5" :class="languageIconMapper[locale || 'ja']" />
    </q-btn>
  </div>
</template>

<style>
.gm-style-iw-ch {
  font-weight: 600 !important;
}
</style>
