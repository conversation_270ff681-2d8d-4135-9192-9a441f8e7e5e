import type { Profile } from '@liff/get-profile'
import type { Liff } from '@line/liff'
import { liff } from '@line/liff'
import { ref } from 'vue'

export const useLiffStore = defineStore('liff', () => {
  const liffInstance = ref<Liff | null>(null)
  const isLogged = ref<boolean>(false)
  const errMsg = ref<string>('')
  const loading = ref<boolean>(false)
  const userProfile = ref<Profile>()

  const init = () => {
    if (isLogged.value === true) {
      return
    }
    loading.value = true
    liff?.init({
      liffId: import.meta.env.VITE_LIFF_ID,
      withLoginOnExternalBrowser: true,
    }, async () => {
      // login success
      liffInstance.value = liff
      if (liffInstance.value?.isLoggedIn()) {
        if (liffInstance.value.getContext()?.scope.includes('profile')) {
          userProfile.value = await liffInstance.value.getProfile()
        }
        else if (liffInstance.value.getContext()?.scope.includes('openid')) {
          const decodedTokenData = liffInstance.value.getDecodedIDToken()
          userProfile.value = {
            userId: decodedTokenData?.sub as string,
            displayName: decodedTokenData?.name || '',
          }
        }
        isLogged.value = true
      }
      loading.value = false
    }, (err: any) => {
      // login error
      console.error(err)
      errMsg.value = err?.message
      loading.value = false
    })
  }
  init()

  return {
    liff: liffInstance,
    userProfile,
    isLogged,
    errMsg,
    loading,
  }
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useLiffStore, import.meta.hot))
}
