/// <reference types="vitest" />

import path from 'node:path'
import { fileURLToPath } from 'node:url'
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite'
import { quasar, transformAssetUrls } from '@quasar/vite-plugin'
import UnheadVite from '@unhead/addons/vite'
import { unheadVueComposablesImports } from '@unhead/vue'
import basicSsl from '@vitejs/plugin-basic-ssl'
import Vue from '@vitejs/plugin-vue'
import UnoCSS from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import IconsResolver from 'unplugin-icons/resolver'
import Icons from 'unplugin-icons/vite'
import Components from 'unplugin-vue-components/vite'
import { VueRouterAutoImports } from 'unplugin-vue-router'
import VueRouter from 'unplugin-vue-router/vite'
import { defineConfig } from 'vite'
import { viteExternalsPlugin } from 'vite-plugin-externals'

export default defineConfig({
  resolve: {
    alias: {
      '~/': `${path.resolve(__dirname, 'src')}/`,
    },
  },
  plugins: [
    Vue({
      template: {
        transformAssetUrls,
        compilerOptions: {
          isCustomElement: tag => ['vue-advanced-chat'].includes(tag),
        },
      },
    }),
    // https://github.com/posva/unplugin-vue-router
    VueRouter({
      extensions: ['.vue'],
      routesFolder: [
        'src/pages',
      ],
      dts: 'src/types/typed-router.d.ts',
    }),

    Icons({
      scale: 1.2,
    }),

    // https://github.com/antfu/unplugin-auto-import
    AutoImport({
      imports: [
        'vue',
        'vue-i18n',
        '@vueuse/core',
        unheadVueComposablesImports,
        VueRouterAutoImports,
        {
          '@vueuse/router': [
            'useRouteParams', // import { useRouteParams } from '@vueuse/router',
            'useRouteQuery',
          ],
          // add any other imports you were relying on
          'vue-router/auto': ['useLink'],
          'pinia': ['acceptHMRUpdate', 'defineStore', 'storeToRefs'],
        },
      ],
      dts: 'src/types/auto-imports.d.ts',
      dirs: [
        './src/composables',
        './src/stores',
      ],
      vueTemplate: true,
    }),

    // https://github.com/antfu/vite-plugin-components
    Components({
      dts: 'src/types/components.d.ts',
      resolvers: [
        IconsResolver(),
      ],
    }),

    // https://github.com/antfu/unocss
    // see uno.config.ts for config
    UnoCSS(),
    UnheadVite(),

    // @quasar/plugin-vite options list:
    // https://github.com/quasarframework/quasar/blob/dev/vite-plugin/index.d.ts
    quasar({
      sassVariables: fileURLToPath(
        new URL('./src/styles/quasar.variables.scss', import.meta.url),
      ),
    }),

    viteExternalsPlugin({
      'fuse.js': 'Fuse',
    }),

    basicSsl({
      /** name of certification */
      name: 'test',
    }),

    VueI18nPlugin({
      runtimeOnly: false,
      compositionOnly: true,
      fullInstall: false,
      include: [path.resolve(__dirname, 'src/locales/**')],
    }),
  ],
  optimizeDeps: {
    include: [
      '@line/liff',
      'dayjs',
      'dayjs/locale/ja',
      'dayjs/plugin/customParseFormat',
      'dayjs/plugin/localizedFormat',
      'dayjs/plugin/relativeTime',
      'deep-equal',
      'destr',
      'file-saver',
      'ky',
      'reconnecting-websocket',
      'vue-advanced-chat',
      'vue3-google-map',
    ],
  },
  build: {
    chunkSizeWarningLimit: 1500,
  },
  // https://github.com/vitest-dev/vitest
  test: {
    environment: 'jsdom',
  },
})
