import {EventBridge} from "@aws-sdk/client-eventbridge";

export const LOG_SOURCE = 'logging';
export enum LogDetail {
    SURVEY = 'SURVEY',
    DISTRIBUTION = 'DISTRIBUTION',
    SEGMENT_DELIVERY = 'SEGMENT_DELIVERY',
    CHATBOT_SCENARIO = 'CHATBOT_SCENARIO'
}

export async function log<T extends {}>(busName: string, source: LogDetail, body: T) {
    const events = [
        {
            EventBusName: busName,
            Source: LOG_SOURCE,
            DetailType: source,
            Detail: JSON.stringify(body),
        }
    ]

    await new EventBridge().putEvents({ Entries: events });
}