export enum DistributionType {
    EXTERNAL ='external',
    INSTANT = 'instant',
    REPEATING = 'repeating',
    POSTPONED = 'postponed',
    REMINDER = 'reminder',
    TALK = 'talk'
}

export enum TargetsSelectionType {
    SELECTED = 'selected',
    BROADCAST = 'broadcast',
    SURVEY_CONDITIONS = 'surveyConditions',
    SCENARIO = 'scenario'
}

export enum DeliveryState {
    NOT_STARTED = 'NOT_STARTED',
    IN_PROGRESS = 'IN_PROGRESS',
    FINISHED = 'FINISHED',
    ERROR = 'ERROR'
}

export enum DeliveryStartedAs {
    MANUAL_HOME_SELECTION = 'manual-home-selection',
    MANUAL_HOME_CONDITIONS = 'manual-home-conditions',
    MANUAL_INSTANT = 'manual-instant',
    MANUAL_DELAYED = 'manual-delayed',
    AUTO_REPEATING = 'auto-repeating',
    AUTO_MAIL = 'auto-mail',
    UNSPECIFIED = 'unspecified'
}

