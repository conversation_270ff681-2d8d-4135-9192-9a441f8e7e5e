{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "include": ["*.d.ts", "src/**/*", "src/**/*.vue"],
  "exclude": ["src/**/__tests__/*"],
  "compilerOptions": {
    "composite": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",

    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@utils/*": ["./src/utils/*"],
      "@components/*": ["./src/components/*"],
      "@pages/*": ["./src/pages/*"],
      "@stores/*": ["./src/stores/*"],
      "@assets/*": ["./src/assets/*"],
      "@services/*": ["./src/services/*"],
      "@constants/*": ["./src/constants/*"],
    }
  },

}
