{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": false, "jsx": "preserve", "importHelpers": true, "composite": true, "noEmit": true, "moduleResolution": "<PERSON><PERSON><PERSON>", "outDir": "./dist", "allowJs": true, "checkJs": false, "noImplicitAny": false, "strictNullChecks": false, "resolveJsonModule": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": false, "baseUrl": ".", "types": ["node", "vite/client"], "paths": {"@/*": ["./src/*"], "@utils/*": ["./src/utils/*"], "@components/*": ["./src/components/*"], "@pages/*": ["./src/pages/*"], "@stores/*": ["./src/stores/*"], "@assets/*": ["./src/assets/*"], "@services/*": ["./src/services/*"], "@constants/*": ["./src/constants/*"], "lsc-form-rendering": ["./src/typings/lsc-form-rendering.d.ts"], "@shared/*": ["../web-shared/src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx"], "exclude": ["node_modules", "**/__tests__/*", "dist"]}