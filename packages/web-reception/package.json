{"name": "lsc-web-liff", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --port 8888", "serves": "vue-cli-service serve --port 8888 --https", "dev": "quasar dev", "build": "quasar build", "test:unit": "vue-cli-service test:unit", "test": "yarn test:unit", "lint": "vue-cli-service lint", "lint-check": "eslint --ext .js,.ts,.vue --quiet", "format": "prettier --write ./src", "fix-format-lint": "yarn format && eslint --fix .", "precommit": "lint-staged", "build:analyze": "cross-env BUNDLE_ANALYZE=true yarn run build", "typecheck": "tsc --noEmit", "storeConv": "npx tsx migration/vuex_2_pinia_conv.ts", "migrate": "npx tsx ../web-admin-v2-2/migration/vue_script_upg.ts"}, "dependencies": {"@line/liff": "^2.13.0", "@quasar/extras": "^1.16.13", "amazon-cognito-identity-js": "^6.3.3", "aws-amplify": "^5.3.8", "axios": "^1.2.1", "core-js": "^3.12.1", "cross-env": "^7.0.3", "crypto-js": "^4.1.1", "dayjs": "^1.11.13", "epic-spinners": "^2.0.0", "is-fullwidth-code-point": "^3.0.0", "japan-post-code-custom-reup": "^1.0.1", "less": "^4.2.1", "less-loader": "^6.1.2", "lodash": "^4.17.21", "luxon": "^2.4.0", "markdown-it": "^13.0.1", "markdown-it-link-attributes": "^4.0.0", "moment": "^2.29.4", "pinia": "^2.2.8", "quasar": "^2.17.4", "vue": "^3.4.5", "vue-chartjs": "^5.3.1", "vue-cli-plugin-s3-deploy": "^4.0.0-rc3", "vue-codemirror": "^4.0.6", "vue-i18n": "^10.0.1", "vue-pdf-embed": "^1.1.6", "vue-router": "^4.0.0", "vue3-toastify": "^0.2.1"}, "devDependencies": {"@oss/web-shared": "workspace:packages/web-shared", "@quasar/app-vite": "^1.9.5", "@quasar/vite-plugin": "^1.7.0", "@types/crypto-js": "^4.1.1", "@types/jest": "^24.0.19", "@types/luxon": "^2.3.2", "@typescript-eslint/eslint-plugin": "^8.16.0", "@typescript-eslint/parser": "^8.17.0", "@vue/eslint-config-typescript": "^11.0.0", "babel-eslint": "^10.1.0", "eslint": "^8.57.1", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.19.1", "eslint-plugin-n": "^15.0.0", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-quasar": "^1.1.0", "eslint-plugin-storybook": "^0.6.13", "eslint-plugin-vue": "^9.32.0", "lint-staged": "^11.0.0", "sass": "~1.32.13", "sass-embedded": "^1.80.2", "sass-loader": "^8.0.0", "typescript": "^4.4.3", "vite": "^2.0.0", "vue-cli-plugin-vuetify": "^2.4.0", "vue-eslint-parser": "^9.4.3", "vue-template-compiler": "^2.6.11", "webpack": "^4.46.0", "webpack-bundle-analyzer": "^4.4.2"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "bit": {"env": {}, "componentsDefaultDirectory": "components/{name}", "packageManager": "npm"}}