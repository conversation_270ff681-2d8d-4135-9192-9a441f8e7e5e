import {
    GetSchedule,
} from "@/services/calendars.service";


import { defineStore } from 'pinia';

export const useCalendarsStore = defineStore("calendars", {
    actions: {
        // actions
        async getDayOffCount(payload: any) {
            let dayOffCount = 0;
            try {
                const _scheduleData = await GetSchedule(payload.categoryId, payload.fromDate, payload.toDate);
                const dayOffInfo = _scheduleData["dayOff"];
                if (dayOffInfo) {
                    for (const k in dayOffInfo) {
                        if (dayOffInfo[k] === 1) {
                            dayOffCount++;
                        }
                    }
                }
            } catch (err) {
                console.error(err);
            }

            return dayOffCount;
        },
    }
});
