import {
    CheckSaibanExist,
    CheckSaibanExistForUpdate,
    fetchMemberByUserId,
    GetFilteredMemberResultByFormData,
} from "@/services/member.service"
import {
    GetSurveyConfigList,
} from "@/services/surveys.service"


import { defineStore } from 'pinia';

interface MemberStore {
    selectedMemberConfig: any;
    memberData: any;
    searchMemberDataError: any;

    linkedSurveyConfigs: any[];
    fetchingLinkedSurveyConfigs: boolean;
    fetchingLinkedSurveyConfigsError: any;
}

export const useMemberStore = defineStore("member", {
    state: (): MemberStore => ({
        selectedMemberConfig: {},
        memberData: {},
        searchMemberDataError: null,

        linkedSurveyConfigs: [],
        fetchingLinkedSurveyConfigs: false,
        fetchingLinkedSurveyConfigsError: null,
    }),
    actions: {
        // actions

        async findMemberResultsByUserId(payload) {
            let foundMember = {};
            try {
                const response = await fetchMemberByUserId(payload.surveyId);
                if (response.result == 'OK') {
                    foundMember = response.userData;
                }
            } catch (err) {
                console.error("Error occured trying to fetch member");
                console.error(err);
            } finally {
                this.setFoundMemberData(foundMember);
            }
        },
        async getFilteredMemberResultsByFormData(payload) {
            try {
                this.setFindingMemberDataError(null);
                const response = await GetFilteredMemberResultByFormData(payload);
                if (Array.isArray(response.data)) {
                    this.setFoundMemberData(response.data[0]);
                } else if (response.result == 'ERROR') {
                    this.setFindingMemberDataError(response.errorMessage);
                }
            } catch (err) {
                this.setFindingMemberDataError(err.message);
            }
        },
        //payload is array of survey configs array
        async fetchSurveyConfigList(payload) {
            try {
                this.setIsFetchingLinkedSurveyConfigs(true);
                const response = await GetSurveyConfigList(payload);
                if (response.result === 'ERROR') {
                    this.setFetchingLinkedSurveyConfigsError(response.errorMessage ? response.errorMessage : '不明なエラー発生しました。管理者に問い合わせください。');
                } else {
                    this.setLinkedSurveyConfigs(response.items);
                }
            } catch (err) {
                this.setFetchingLinkedSurveyConfigsError('「' + err.message + '」エラー発生しました。管理者に問い合わせください。');
            } finally {
                this.setIsFetchingLinkedSurveyConfigs(false);
            }
        },

        async checkSaibanExist(payload: any): Promise<any> {
            try {
              this.setCheckSaibanExistError(null);
              let result = await CheckSaibanExist(payload);
              return result;
            } catch (error: any) {
              this.setCheckSaibanExistError(error);
              return false;
            }
          },
      
          async checkSaibanExistForUpdate(payload: any): Promise<any> {
            try {
              this.setCheckSaibanExistForUpdateError(null);
              let result = await CheckSaibanExistForUpdate(payload);
              return result;
            } catch (error: any) {
              this.setCheckSaibanExistForUpdateError(error);
              return false;
            }
          },


        // mutations


        setSelectedMemberConfig(value: any) {
            this.selectedMemberConfig = value
        },
        setFoundMemberData(value: any) {
            this.memberData = value
        },
        setFindingMemberDataError(value: any) {
            this.searchMemberDataError = value
        },
        setLinkedSurveyConfigs(value: any) {
            this.linkedSurveyConfigs = value
        },
        setIsFetchingLinkedSurveyConfigs(value: any) {
            this.fetchingLinkedSurveyConfigs = value
        },
        setFetchingLinkedSurveyConfigsError(value: any) {
            this.fetchingLinkedSurveyConfigsError = value
        },
    },
    getters: {

    }
});
