import {
    cancelSurveyResults,
    getBySurveyIdUserIdIndex,
} from "@/services/surveys.service"
import {
    GetTagNames,
} from "@/services/calendars.service"
import { transformCompiledDataToArray } from "@/utils/surveyResultsUtils"

import { defineStore } from 'pinia';
import { Auth } from "@/services/auth.service";

export const useSurveyResultsStore = defineStore("surveyResults", {
    state: () => ({
        calendarDisplaySettings: {},
        calendarUseParamsJSON: {},

        reservationItemCost: 1,
        reservationItemName: null,

        coronaVaccinationData: null,
        vaccinationAllowedStartDate: null,
        surveyResults: [],

    }),
    actions: {
        // actions

        setCheckOfSurveyResult(payload: { partitionKey: any; check: string }) {
            const surveyResult = this.surveyResults.find((elem: { partitionKey: any; }) => elem.partitionKey === payload.partitionKey);
            if (surveyResult && surveyResult.data) {
                for (const result of surveyResult.data) {
                    result.check = payload.check;
                }
            }
        },
        setSurveyResults(value: any) {
            if (Array.isArray(value)) {
                this.surveyResults = value;
            } else {
                if (value.data && Array.isArray(value.data) && value.survey && value.survey.surveySchema) {
                    const tempData = [];
                    value.data.forEach((elem: { partitionKey: any; }) => {
                        tempData.push({
                            partitionKey: elem.partitionKey,
                            data: transformCompiledDataToArray(elem, value.survey),
                        });
                    });
                    this.surveyResults = tempData;
                } else {
                    this.surveyResults = [];
                }
            }
        },

        async fetchSurveyResultsBySurveyIdAndUserId(payload: any) {
            try {
                const response = await getBySurveyIdUserIdIndex(payload.survey.surveyId);
                if (response.result === 'OK') {
                    this.setSurveyResults({
                        data: response.items,
                        survey: payload.survey,
                    });
                } else {
                    this.setSurveyResults([]);
                }
                return response;
            } catch (err) {
                console.error("Error occured trying to fetch survey results");
                console.error(err);
                return { result: 'ERROR', errorMessage: err.message };
            }
        },
        async fetchCalendarDisplaySettings() {
            try {
                const response = await GetTagNames();
                this.setCalendarDisplaySettings(response);
            } catch (err) {
                console.error("error occured fetching calendar tags");
            }
        },
        async cancelSurveyResults(payload: any) {
            try {
                const response = await cancelSurveyResults(payload);
                if (response.result === 'OK') {
                    this.setCheckOfSurveyResult({
                        partitionKey: payload.partitionKey,
                        check: 'キャンセル'
                    });
                }

                return response;
            } catch (err) {
                console.error("Error cancelling results", err);
                return { result: 'ERROR', errorMessage: err.message + '。エラー発生しました。管理者にお問い合わせください。' }
            }
        },
        resetCalendarUseParamsJson() {
            this.setCalendarUseParamsJson({});
        },

        setReservationItemCost(value: any) {
            this.reservationItemCost = value;
        },

        setReservationItemName(value: any) {
            this.reservationItemName = value;
        },

        // mutations

        setCalendarDisplaySettings(value: any) {
            this.calendarDisplaySettings = value
        },
        setCalendarUseParamsJson(value) {
            this.calendarUseParamsJSON = value;
        },

    },
    getters: {

    }
});
