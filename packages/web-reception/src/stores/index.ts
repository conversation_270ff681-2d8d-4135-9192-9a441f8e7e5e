import { Auth } from '@/services/auth.service';
import { transformCompiledDataToArray } from '@/utils/surveyResultsUtils';
import { createPinia, defineStore } from 'pinia';

// Create the Pinia instance
const pinia = createPinia();

// Define a main store
export const useMainStore = defineStore('main', {
  state: () => ({
    authSession: null,
    surveyConfig: null,
    surveyConfigAllItem: null,
    surveyResults: null,
    surveyInput: null,
    surveyInputRegisterPayment: null,
    selectedSurveyResults: null,
    surveyFamilyName: null,
    surveyFirstName: null,
    lineUser: null,
    inEditingAppendingMode: false,
    calendarSettings: {},
    categoryValues: {},
    categoryLabels: [],
    reservationItemCost: 1,
    reservationItemName: null,
    surveyResultsSavedWhenMovingToCalendar: [],
    surveyResultsDisplayValues: {},
    selectedValuesForCancel: [],
    reservationComplite: false,
    showErrorComponent: false,
    realSurveyConfig: null,

    patientFamilyName: null,
    patientFirstName: null,

    surveySchema1:null,
  }),
  actions: {
    setLineUserInfo(value) {
      this.lineUser = value;
    },
    setAuthSession(value) {
      this.authSession = value;
    },
    setShowErrorComponent(value) {
      this.showErrorComponent = value;
    },
    setEditingAppendingMode(value) {
      this.inEditingAppendingMode = value;
    },
    setCheckOfSurveyResult(payload) {
      const surveyResult = this.surveyResults.find((elem) => elem.partitionKey === payload.partitionKey);
      if (surveyResult && surveyResult.data) {
        for (const result of surveyResult.data) {
          result.check = payload.check;
        }
      }
    },
    setUnalteredSurveyConfig(value) {
      this.realSurveyConfig = value;
    },
    setSurveyResults(value) {
      if (Array.isArray(value)) {
        this.surveyResults = value;
      } else {
        if (value.data && Array.isArray(value.data) && value.survey && value.survey.surveySchema) {
          const tempData = [];
          value.data.forEach((elem) => {
            tempData.push({
              partitionKey: elem.partitionKey,
              data: transformCompiledDataToArray(elem, value.survey),
            });
          });
          this.surveyResults = tempData;
        } else {
          this.surveyResults = [];
        }
      }
    },
    setReservationItemCost(value) {
      this.reservationItemCost = value;
    },
    setSurveyInputRegisterPayment(value) {
      this.surveyInputRegisterPayment = value;
    },
    setSurveySchema1(value) {
      this.surveySchema1 = value;
    },
    async fetchSession() {
      return await Auth.currentSession();
    },
  },
  getters: {},
});

export default pinia;