/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

//会員
export const FIND_MEMBER_RESULTS_BY_USER_ID = "FIND_MEMBER_RESULTS_BY_USER_ID";
export const GET_FILTERED_MEMBER_RESULTS_BY_FORM_DATA = "GET_FILTERED_MEMBER_RESULTS_BY_FORM_DATA";
export const FETCH_SURVEY_CONFIG_LIST = "FETCH_SURVEY_CONFIG_LIST";

//帳票
export const FETCH_SURVEY_RESULTS_BY_SURVEY_ID_AND_USER_ID = "FETCH_SURVEY_RESULTS_BY_SURVEY_ID_AND_USER_ID";
export const FETCH_CALENDAR_DISPLAY_SETTINGS = "FETCH_CALENDAR_DISPLAY_SETTINGS";
export const CANCEL_SURVEY_RESULTS = "CANCEL_SURVEY_RESULTS";
export const RESET_CALENDAR_USE_PARAMS_JSON = "RESET_CALENDAR_USE_PARAMS_JSON";

//カレンダー
export const GET_DAY_OFF_COUNT = "GET_DAY_OFF_COUNT";

// 決済
export const GET_TAX_RATE_SETTINGS = "GET_TAX_RATE_SETTINGS";
export const GET_PAYMENT_SERVICE = "GET_PAYMENT_SERVICE";
export const RESET_PAYMENT_SERVICE = "RESET_PAYMENT_SERVICE";
export const GET_RESERVATION_PAYMENT_SERVICE = "GET_RESERVATION_PAYMENT_SERVICE";
export const GET_PAYMENT_RESULT = "GET_PAYMENT_RESULT";
