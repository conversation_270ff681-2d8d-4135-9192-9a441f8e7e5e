/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {
    GET_DAY_OFF_COUNT,
} from "@/store/action-types"

import {
    GetSchedule,
} from "@/services/calendars.service";

  export default {
    [GET_DAY_OFF_COUNT]: async ({ commit, state, dispatch }, payload: any) => {
        let dayOffCount = 0;
        try {
            const _scheduleData = await GetSchedule(payload.categoryId, payload.fromDate, payload.toDate);
            const dayOffInfo = _scheduleData["dayOff"];
            if (dayOffInfo) {
                for (const k in dayOffInfo) {
                    if (dayOffInfo[k] === 1) {
                        dayOffCount++;
                    }
                }
            }
        } catch(err) {
            console.error(err);
        }

        return dayOffCount;
    },
  };
