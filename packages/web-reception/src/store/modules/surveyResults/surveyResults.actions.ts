/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {
    FETCH_SURVEY_RESULTS_BY_SURVEY_ID_AND_USER_ID,
    FETCH_CALENDAR_DISPLAY_SETTINGS,
    CANCEL_SURVEY_RESULTS,
    RESET_CALENDAR_USE_PARAMS_JSON,
} from "@/store/action-types"

import {
    SET_CALENDAR_DISPLAY_SETTINGS,
    SET_CHECK_OF_SURVEY_RESULT,
    SET_SURVEY_RESULTS,
    SET_CALENDAR_USE_PARAMS_JSON,
} from "@/store/mutation-types"

import {
    cancelSurveyResults,
    getBySurveyIdUserIdIndex,
} from "@/services/surveys.service"

import {
    GetTagNames,
} from "@/services/calendars.service"

export default {
    [FETCH_SURVEY_RESULTS_BY_SURVEY_ID_AND_USER_ID]: async ({ commit, state, dispatch }, payload: any) => {
        try {
            const response = await getBySurveyIdUserIdIndex(payload.survey.surveyId);
            if (response.result === 'OK') {
                commit(SET_SURVEY_RESULTS, {
                    data: response.items,
                    survey: payload.survey,
                });
            } else {
                commit(SET_SURVEY_RESULTS, []);
            }
            return response;
        } catch (err) {
            console.error("Error occured trying to fetch survey results");
            console.error(err);
            return { result: 'ERROR', errorMessage: err.message };
        }
    },
    [FETCH_CALENDAR_DISPLAY_SETTINGS]: async ({ commit, state, dispatch }) => {
        try {
            const response = await GetTagNames();
            commit(SET_CALENDAR_DISPLAY_SETTINGS, response)
        } catch (err) {
            console.error("error occured fetching calendar tags");
        }
    },
    [CANCEL_SURVEY_RESULTS]: async ({ commit, state, dispatch }, payload: any) => {
        try {
            const response = await cancelSurveyResults(payload);
            if (response.result === 'OK') {
                commit(SET_CHECK_OF_SURVEY_RESULT, {
                    partitionKey: payload.partitionKey,
                    check: 'キャンセル'
                })
            }

            return response;
        } catch (err) {
            console.error("Error cancelling results");
            return { result: 'ERROR', errorMessage: err.message + '。エラー発生しました。管理者にお問い合わせください。'}
        }
    },
    [RESET_CALENDAR_USE_PARAMS_JSON]: ({ commit }) => {
        commit(SET_CALENDAR_USE_PARAMS_JSON, {});
    },
};
