/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import {
  SET_CALENDAR_DISPLAY_SETTINGS,
  SET_CALENDAR_USE_PARAMS_JSON,
  SET_RESERVATION_ITEM_COST,
} from "@/store/mutation-types";

export default {
  [SET_CALENDAR_DISPLAY_SETTINGS]: (state: { calendarDisplaySettings: any; }, value: any) =>
    (state.calendarDisplaySettings = value),
  [SET_CALENDAR_USE_PARAMS_JSON]: (state, value) => {
    state.calendarUseParamsJSON = value;
  },
  [SET_RESERVATION_ITEM_COST]: (state, value: number) => {
    state.reservationItemCost = value;
  },
};
