/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import {
    SET_SELECTED_MEMBER_CONFIG,
    SET_FOUND_MEMBER_DATA,
    SET_FINDING_MEMBER_DATA_ERROR,
    SET_LINKED_SURVEY_CONFIGS,
    SET_IS_FETCHING_LINKED_SURVEY_CONFIGS,
    SET_FETCHING_LINKED_SURVEY_CONFIGS_ERROR,
} from "@/store/mutation-types"

export default {
    [SET_SELECTED_MEMBER_CONFIG]: (state: { selectedMemberConfig: any; }, value: any) =>
        (state.selectedMemberConfig = value),
    [SET_FOUND_MEMBER_DATA]: (state: { memberData: any; }, value: any) =>
        (state.memberData = value),
    [SET_FINDING_MEMBER_DATA_ERROR]: (state: { searchMemberDataError: any; }, value: any) =>
        (state.searchMemberDataError = value),
    [SET_LINKED_SURVEY_CONFIGS]: (state: { linkedSurveyConfigs: any; }, value: any) =>
        (state.linkedSurveyConfigs = value),
    [SET_IS_FETCHING_LINKED_SURVEY_CONFIGS]: (state: { fetchingLinkedSurveyConfigs: any; }, value: any) =>
        (state.fetchingLinkedSurveyConfigs = value),
    [SET_FETCHING_LINKED_SURVEY_CONFIGS_ERROR]: (state: { fetchingLinkedSurveyConfigsError: any; }, value: any) =>
        (state.fetchingLinkedSurveyConfigsError = value),
};
