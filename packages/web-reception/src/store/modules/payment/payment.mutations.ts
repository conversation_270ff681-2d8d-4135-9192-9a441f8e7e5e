/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import {
  SET_APPLYING_TAX_RATE,
  SET_IS_FETHING_TAX_RATE_SETTINGS,
  SET_PAYMET_SERVICE,
  SET_IS_FETHING_PAYMENT_SERVICE,
  SET_PRODUCT_LIST,
  ADD_RESERVATION_PAYMENT_SERVICE_TO_MAP,
  SET_PAYMENT_RESULT,
  SET_IS_FETHING_PAYMENT_RESULT,
  SET_PAYMENT_ORDER_INPUT_FROM_SURVEY,
} from '@/store/mutation-types';
import PaymentState from './payment.state';

type State = typeof PaymentState;

export default {
  [SET_APPLYING_TAX_RATE]: (state: State, value: number) => {
    state.applyingTaxRate = value;
  },
  [SET_IS_FETHING_TAX_RATE_SETTINGS]: (state: State, value: boolean) => {
    state.isFethingTaxRateSettings = value;
  },
  [SET_PAYMET_SERVICE]: (state: State, value) => {
    state.paymentService = value;
  },
  [SET_IS_FETHING_PAYMENT_SERVICE]: (state: State, value: boolean) => {
    state.isFetchingPaymentService = value;
  },
  [SET_PRODUCT_LIST]: (state: State, value) => {
    state.productList = value;
  },
  [ADD_RESERVATION_PAYMENT_SERVICE_TO_MAP]: (state: State, payload) => {
    state.reservationPaymentServiceMap[payload.categoryId] = payload.paymentService;
  },
  [SET_PAYMENT_RESULT]: (state: State, value) => {
    state.paymentResult = value;
  },
  [SET_IS_FETHING_PAYMENT_RESULT]: (state: State, value: boolean) => {
    state.isFethingPaymentResult = value;
  },
  [SET_PAYMENT_ORDER_INPUT_FROM_SURVEY]: (state: State, value) => {
    state.paymentOrderInputFromSurvey = value;
  },
};
