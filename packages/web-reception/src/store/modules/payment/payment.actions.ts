/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

import {
  GET_TAX_RATE_SETTINGS,
  GET_PAYMENT_SERVICE,
  RESET_PAYMENT_SERVICE,
  GET_RESERVATION_PAYMENT_SERVICE,
  GET_PAYMENT_RESULT,
} from '@/store/action-types';

import {
  SET_APPLYING_TAX_RATE,
  SET_IS_FETHING_TAX_RATE_SETTINGS,
  SET_PAYMET_SERVICE,
  SET_IS_FETHING_PAYMENT_SERVICE,
  SET_PRODUCT_LIST,
  ADD_RESERVATION_PAYMENT_SERVICE_TO_MAP,
  SET_PAYMENT_RESULT,
  SET_IS_FETHING_PAYMENT_RESULT,
} from '@/store/mutation-types';

import {
  GetTaxRateSettings,
  GetPaymentService,
} from '@/services/payment.service';

import { GetReservationPaymentService } from '@/services/calendars.service';

import { GetPaymentResult } from '@/services/paymentResults.service';

import { getApplyingRate } from '@/utils/paymentResultUtils';

export default {
  [GET_TAX_RATE_SETTINGS]: async ({ commit }) => {
    try {
      commit(SET_IS_FETHING_TAX_RATE_SETTINGS, true);
      const response = await GetTaxRateSettings();
      switch (response.code) {
        case 'success': {
          const rateSettings = response.data.rateSettings;
          const rate = getApplyingRate(rateSettings);
          if (rate === null) {
            throw '適用中の消費税設定が見つかりませんでした。';
          }
          commit(SET_APPLYING_TAX_RATE, rate);
          break;
        }
        case 'not_found': {
          throw '消費税設定が未設定です。';
        }
        default: {
          throw response.errorMessage || '消費税設定の取得中にエラーが発生しました。';
        }
      }
    } catch (error) {
      console.error(new Error(error));
    } finally {
      commit(SET_IS_FETHING_TAX_RATE_SETTINGS, false);
    }
  },
  [GET_PAYMENT_SERVICE]: async ({ commit }, serviceKey: string) => {
    try {
      commit(SET_IS_FETHING_PAYMENT_SERVICE, true);
      const response = await GetPaymentService(serviceKey);
      if (response.code !== 'success') {
        throw response.errorMessage;
      }
      commit(SET_PAYMET_SERVICE, {
        serviceSortKey: response.data.sortKey,
        serviceId: response.data.serviceId,
        serviceName: response.data.serviceName,
        taxType: response.data.taxType,
        calculationType: response.data.calculationType,
        roundingType: response.data.roundingType,
        purchaseLimit: response.data.purchaseLimit,
        reservationCostType: response.data.reservationCostType,
      });
      commit(SET_PRODUCT_LIST, response.data.products);
    } catch (error) {
      console.error(new Error(error));
    } finally {
      commit(SET_IS_FETHING_PAYMENT_SERVICE, false);
    }
  },
  [RESET_PAYMENT_SERVICE]: ({ commit }) => {
    commit(SET_PAYMET_SERVICE, null);
    commit(SET_PRODUCT_LIST, []);
  },
  [GET_RESERVATION_PAYMENT_SERVICE]: async ({ commit }, categoryId: string) => {
    try {
      commit(SET_IS_FETHING_PAYMENT_SERVICE, true);
      const response = await GetReservationPaymentService(categoryId);
      if (response.code !== 'success') {
        throw response.errorMessage || '決済予約項目の取得に失敗しました。';
      }
      commit(ADD_RESERVATION_PAYMENT_SERVICE_TO_MAP, {
        categoryId,
        paymentService: response.data
      });
    } catch (error) {
      console.error(new Error(error));
    } finally {
      commit(SET_IS_FETHING_PAYMENT_SERVICE, false);
    }
  },
  [GET_PAYMENT_RESULT]: async ({ commit }, surveyResultPartitionKey: string) => {
    try {
      commit(SET_IS_FETHING_PAYMENT_RESULT, true);
      const response = await GetPaymentResult(surveyResultPartitionKey);
      if (response.code !== 'success') {
        throw response.errorMessage || '決済予約項目の取得に失敗しました。';
      }
      commit(SET_PAYMENT_RESULT, response.data);
    } catch (error) {
      console.error(new Error(error));
    } finally {
      commit(SET_IS_FETHING_PAYMENT_RESULT, false);
    }
  },
};
