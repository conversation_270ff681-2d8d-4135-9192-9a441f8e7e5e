/*
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
 */
import { API } from "aws-amplify";
import { ApiName } from "./amplify.service";
import { axiosRetry, isTimeout } from "./helpers/axios-retry";
import { createAppendingPayload } from '@/utils/surveyResultsUtils';
import { PaymentData } from '@/utils/paymentResultUtils';

const SURVEY_API_PATH: any = {
  GET_SCHEDULE: "/survey/api/v1/db/calendars/schedule/",
  GET_SINGLE_CATEGORY: "/survey/api/v1/db/calendars/categories/",
  GET_ALL_CATEGORIES: "/survey/api/v1/db/calendars/categories",
  PUT_SURVEY: "/survey/api/v1/db/reservation/putItem",
  GET_CATEGORY_TREE: "/survey/api/v1/db/calendars/categories_tree",
  GET_TAG_NAMES: "/survey/api/v1/db/calendars/tag_labels",
  GET_CALENDARS: "/survey/api/v1/db/calendars/getCalendars",
  GET_CALENDAR_INFO: "/survey/api/v1/db/calendars/info/",
  CHECK_CAN_BE_RESERVE: "/survey/api/v1/db/calendars/canBeReserve/",
  CHECK_RESERVATION: "/survey/api/v1/db/survey-results/checkReservation",
  GET_RESERVATION_ITEM_INFO: "/survey/api/v1/db/calendars/item_info/",
  GET_RESERVATION_PAYMENT_SERVICE: "/survey/api/v1/payment/configs/reservation-payment-products",
};

const addAuthorization = (params: any, authSession: any): void => {
  if (authSession) {
    if (!params.headers) {
      params.headers = {};
    }
    params.headers.Authorization = `Bearer ${authSession.idToken.jwtToken}`;
  }
};

export const GetAllCalendars = async (authSession = null): Promise<any> => {
  const params = {};
  addAuthorization(params, authSession);
  return await API.get(ApiName, SURVEY_API_PATH.GET_CALENDARS, params);
};

/**
 * スケジュールを取得します
 * @param {string} categoryId カテゴリID
 * @param {string} startDay   対象開始日
 * @param {string} endDay     対象終了日
 */
export const GetSchedule = async (category_id: string | number | boolean, start_day: any, end_day: any): Promise<any> => {
  try {
    const params = {
      queryStringParameters: {
        start_day,
        end_day,
      },
    };

    const result = await axiosRetry(
      () => API.get(ApiName, SURVEY_API_PATH.GET_SCHEDULE + encodeURIComponent(category_id), params),
      { retryOnNetwork: true }
    );
    return result;
  } catch (err) {
    // alert(err);
    return err;
  }
};

/**
 * カテゴリーの情報を取得します
 * @param {string} categoryId カテゴリID
 */
export const GetSingleCategory = async (category_id: string | number | boolean): Promise<any> => {
  try {
    const params = {
      queryStringParameters: {},
    };

    const result = await API.get(ApiName, SURVEY_API_PATH.GET_SINGLE_CATEGORY + encodeURIComponent(category_id), params);
    return result;
  } catch (err) {
    // alert(err);
    return err;
  }
};

/**
 * カテゴリーの情報を取得します
 */
export const GetAllCategories = async (authSession = null): Promise<any> => {
  try {
    const params = {
      queryStringParameters: {},
    };
    addAuthorization(params, authSession);
    const result = await API.get(ApiName, SURVEY_API_PATH.GET_ALL_CATEGORIES, params);
    return result;
  } catch (err) {
    // alert(err);
    return err;
  }
};

/**
 * カテゴリーツリーを取得します
 */
export const GetCategoriesTree = async (): Promise<any> => {
  try {
    const params = {};

    const result = await axiosRetry(() => API.get(ApiName, SURVEY_API_PATH.GET_CATEGORY_TREE, params), {
      retryOnNetwork: true,
    });
    return result;
  } catch (err) {
    if (isTimeout(err)) {
      return "Request timeout";
    }
    return err;
  }
};

/**
 * カレンダー情報を取得します
 */
export const GetCalendarInfo = async (category_id: string | number | boolean, authSession = null): Promise<any> => {
  try {
    const params = {
      queryStringParameters: {},
    };
    addAuthorization(params, authSession);
    const result = await API.get(ApiName, SURVEY_API_PATH.GET_CALENDAR_INFO + encodeURIComponent(category_id), params);
    return result;
  } catch (err) {
    return err;
  }
};

/**
 * 指定されたカレンダーに予約可能枠があるかどうか確認します。
 */
export const calendarCanBeReserve = async (calendar_id: string | number | boolean, authSession = null): Promise<any> => {
  try {
    const params = {
      queryStringParameters: {},
    };
    addAuthorization(params, authSession);
    const result = await API.get(ApiName, SURVEY_API_PATH.CHECK_CAN_BE_RESERVE + encodeURIComponent(calendar_id), params);
    return result;
  } catch (err) {
    console.error(err);
    // 確認できない場合、カレンダーを表示します。
    return true;
  }
};

/**
 * タグ名称を取得します
 */
export const GetTagNames = async (): Promise<any> => {
  try {
    const params = {};

    const result = await API.get(ApiName, SURVEY_API_PATH.GET_TAG_NAMES, params);
    return result;
  } catch (err) {
    return err;
  }
};

/**
 * ユーザーの予約状況をチェック
 */
export const checkReservation = async (payload: any): Promise<any> => {
  try {
    return await axiosRetry(() => API.post(ApiName, SURVEY_API_PATH.CHECK_RESERVATION, { body: payload }));
  } catch (err) {
    if (isTimeout(err)) {
      return {
        result: "ERROR",
        errorMessage: "Request timeout",
        checkDetail: "",
      };
    }
    return err;
  }
};

/**
 * 帳票結果を保存します
 * @param {object} results 入力データ
 *
 */
export const PutSurveyResult = async (
  results: { surveyId: any; userId: any; items: any[]; partitionKey: any; },
  subResults: any,
  paymentData?: PaymentData,
): Promise<any> => {
  const data = createAppendingPayload(results);
  const postBody = {};
  postBody["data"] = data;
  if (subResults) {
    postBody["subData"] = subResults;
  }
  if (paymentData) {
    postBody['order'] = paymentData;
  }
  try {
    const params = {
      body: postBody,
    };
    return await API.post(ApiName, SURVEY_API_PATH.PUT_SURVEY, params);
  } catch (err) {
    if (err.toString() === "Error: Network Error") {
      return {
        result: "ERROR",
        errorMessage: err.toString(),
      }
    }
    return err;
  }
};

/**
 * 予約項目を取得します
 * @param {string} category_id カテゴリID
 */
export const getReservationItemInfo = async (category_id: string | number | boolean): Promise<any> => {
  try {
    const result = await Promise.race([ 
      API.get(ApiName, SURVEY_API_PATH.GET_RESERVATION_ITEM_INFO + encodeURIComponent(category_id), null),
      new Promise((resolve, reject) => setTimeout(() => {
        console.warn('Request timeout');
        resolve([]);
      }, 8000))
    ]);
    return result;
  } catch (err) {
    console.error(err);
    return err;
  }
};


/**
 * 予約項目(商品)を取得します
 * @param {string} categoryId カテゴリID
 */
export const GetReservationPaymentService = async (categoryId: string): Promise<any> => {
  return await API.get(ApiName, `${SURVEY_API_PATH.GET_RESERVATION_PAYMENT_SERVICE}/${encodeURIComponent(categoryId)}`, {});
}
