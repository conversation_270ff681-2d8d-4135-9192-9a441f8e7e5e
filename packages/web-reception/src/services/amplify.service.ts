/*
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
 */
import { Auth, Amplify } from 'aws-amplify';
import { Auth as AuthService } from "./auth.service";
import { getApiUrl } from "../utils/configuration";
export const Prefix = "v1";
export const ApiName = "SurveyApi";
export const ServerlessMode = true;

const AmplifyService = {
  configure() {
    const endpointSurveyApiUrl = getApiUrl();
    Amplify.configure({
      Auth: {
        identityPoolId: import.meta.env.VITE_AMPLIFY_AUTH_IDENTITY_POOL_ID,
        userPoolId: import.meta.env.VITE_AMPLIFY_AUTH_USER_POOL_ID,
        userPoolWebClientId: import.meta.env
          .VITE_AMPLIFY_AUTH_USER_POOL_WEB_CLIENT_ID,
        identityPoolRegion: import.meta.env.VITE_DEPLOY_AWS_REGION,
        region: import.meta.env.VITE_AMPLIFY_AUTH_REGION,
      },
      API: {
        endpoints: [
          {
            name: ApiName,
            endpoint: import.meta.env.VITE_LIFF_API_URL_BASE,
            region: import.meta.env.VITE_AMPLIFY_AUTH_REGION,
            custom_header: async () => {
              try {
                return {
                  Authorization: `Bearer ${(await AuthService.currentSession()).getIdToken().getJwtToken()}`,
                };
              } catch (e) {
                return {};
              }
            },
          },
        ],
      },
    });
  },
};

export { AmplifyService, Prefix as PrefixApi };
