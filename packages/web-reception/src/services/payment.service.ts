/*
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
 */

import { API } from "aws-amplify";
import { ApiName } from "./amplify.service";

const BASE_PATH = '/survey/api/v1/payment/configs';
const API_PATH_MAP = {
  SERVICES: BASE_PATH + '/services',
  TAX_SETTING: BASE_PATH + '/tax-setting',
};

export const GetTaxRateSettings = async (): Promise<any> => {
  return await API.get(ApiName, API_PATH_MAP.TAX_SETTING, {});
};

export const GetPaymentService = async (serviceId: string): Promise<any> => {
  return await API.get(ApiName, `${API_PATH_MAP.SERVICES}/${encodeURIComponent(serviceId)}`, {
    queryStringParameters: {
      // NOTE: LIFFでは商品も一緒に取得するパターンのみなのでtrueで固定
      include_products: true,
    }
  });
};