/*
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
 */
import { API } from "aws-amplify";
import { ApiName } from "./amplify.service";
import { axiosRetry } from "./helpers/axios-retry";
import { PaymentData } from '@/utils/paymentResultUtils';

const SURVEY_API_PATH = {
  GET_SURVEY_CONFIG: "/survey/api/v1/db/survey-configs/getItem",
  GET_ALL_SURVEYS: "/survey/api/v1/db/survey-configs/getAllSearchable",
  GET_SURVEY_CONFIGS_LIST: "/survey/api/v1/db/survey-configs/getSurveyConfigsList",
  GET_SURVEY_RESULTS: "/survey/api/v1/db/survey-results/query",
  PUT_SURVEY: "/survey/api/v1/db/reservation/putItem",
  CANCEL_SURVEY: "/survey/api/v1/db/reservation/cancelItem",
  GET_CATEGORIES: "/survey/api/v1/db/calendars/categories",
  GET_CATEGORIES_TREE: "/survey/api/v1/db/calendars/categories_tree",
  GET_DISPLAY_SETTINGS: "/survey/api/v1/db/calendars/display_settings",
  GET_REQUEST_TOKEN: "/survey/api/v1/db/login/token",
  PUT_USER_ID_TO_SURVEY_RESULTS: "/survey/api/v1/db/reservation/putUserId",
  GET_FILTERED_SURVEY_RESULTS: "/survey/api/v1/db/survey-results/filterQuery",
  GET_RESULTS_BY_SURVEY_ID_AND_USER_ID: "/survey/api/v1/db/survey-results/getBySurveyIdUserIdIndex",
  GET_RESULTS_BY_SURVEY_ID_AND_PARTITION_KEY: "/survey/api/v1/db/survey-results/getBySurveyIdPartitionKeyIndex",
  DELETE_SURVEY_RESULT: "/survey/api/v1/db/reservation/deleteItem",
  SURVEY_RESULTS_TEMPORARY: "/survey/api/v1/db/reservation/temprary",
};

const addAuthorization = (params: any, authSession: any): void => {
  if (authSession) {
    if (!params.headers) {
      params.headers = {};
    }
    params.headers.Authorization = `Bearer ${authSession.idToken.jwtToken}`;
  }
};

/**
 * 帳票定義を取得します
 * @param {string} surveyId 帳票ID
 * @param {object} authSession 認証データ
 */
export const GetSurveyById = async (surveyId: any): Promise<any> => {
  try {
    const params = {
      queryStringParameters: {
        surveyId,
      },
    };

    const result = await API.get(ApiName, SURVEY_API_PATH.GET_SURVEY_CONFIG, params);
    return result;
  } catch (err) {
    return err;
  }
};

export const GetAllSearchableSurveys = async (): Promise<any> => {
  try {
    const params = {};

    const result = await API.get(ApiName, SURVEY_API_PATH.GET_ALL_SURVEYS, params);
    return result;
  } catch (err) {
    return err;
  }
};

/**
 * ユーザーの帳票結果を取得します
 * @param {string} surveyId 帳票ID
 * @param {object} authSession 認証データ
 */
export const GetSurveyResult = async (
  surveyId: any,
  authSession: { getIdToken: () => { (): any; new (): any; payload: any } },
  partitionKey = ""
): Promise<any> => {
  // dynamoDB の query operation に使うパラメータ
  // パーティションキーは優先で
  // isScan: 前方一致（追加型用）
  let data = {};
  if (partitionKey !== "") {
    data = {
      queryStringParameters: {
        surveyId: surveyId,
        partitionKey: partitionKey,
      },
    };
  } else {
    data = {
      body: {
        surveyId: surveyId,
        userId: authSession.getIdToken().payload.identities[0].userId,
      },
    };
  }
  try {
    let result = null;
    if (partitionKey !== "") {
      result = await API.get(ApiName, SURVEY_API_PATH.GET_RESULTS_BY_SURVEY_ID_AND_PARTITION_KEY, data);
    } else {
      result = await API.post(ApiName, SURVEY_API_PATH.GET_SURVEY_RESULTS, data);
    }
    return result;
  } catch (err) {
    return err;
  }
};

export const UpdateUserIdToSurveyResultByPks = async (formData: any): Promise<any> => {
  const data = Object.assign(formData);
  try {
    const params = { body: data };
    const result = await API.post(ApiName, SURVEY_API_PATH.PUT_USER_ID_TO_SURVEY_RESULTS, params);
    return result;
  } catch (err) {
    return err;
  }
};

export const GetFilteredSurveyResultByFormData = async (requestingUserId: any, formData: any): Promise<any> => {
  const data = Object.assign(formData);
  try {
    const params = {
      body: {
        queryParams: data,
        requestingUserId: requestingUserId,
      },
    };
    const result = await API.post(ApiName, SURVEY_API_PATH.GET_FILTERED_SURVEY_RESULTS, params);

    return result;
  } catch (err) {
    console.error(err);
    return err;
  }
};

export const getBySurveyIdUserIdIndex = async (surveyId: any): Promise<any> => {
  try {
    const params = {
      queryStringParameters: {
        surveyId: surveyId,
      },
    };
    return await API.get(ApiName, SURVEY_API_PATH.GET_RESULTS_BY_SURVEY_ID_AND_USER_ID, params);
  } catch (error) {
    return { result: "ERROR", errorMessage: error.message };
  }
};

/**
 * 帳票結果を保存します
 * @param {object} results 入力データ
 * @param {object} config survey-config
 * @param {object} authSession 認証データ
 *
 * results の例
 * results = {
          surveyId: "<some surveyI>",
          userId: "<some Id>",
          surveyResults: [
            {
              itemKey: "28dcd81d",
              title: "情報区分",
              value: "イベント"
            },
            {
              itemKey: "28dcd81d",
              title: "情報区分",
              value: "子育て"
            },
            {
              itemKey: "eb2fb1a3",
              title: "年代",
              value: "20〜29歳"
            },
            {
              itemKey: "f702ad32",
              title: "ご要望",
              value: "ああああああああ"
            }
          ]
        }
 */
export const PutSurveyResult = async (
  results: any,
  config: any,
  authSession: any,
  additionalData: any = {},
  paymentData?: PaymentData,
  surveySchema1?: any,
): Promise<any> => {
  const data: any = {};
  // itemKey で schema を取得するための object
  const schemas = {};

  let partitionKey =
    "updateExist" in results && results.updateExist === true
      ? results.surveyResults[0].partitionKey
      : results.partitionKey;

  if (!partitionKey) {
    partitionKey =
      config.isAppending && config.isAppending.value
        ? `${results.surveyId}#${results.userId}#${Date.now()}`
        : `${results.surveyId}#${results.userId}`;
  }

  // 複数選択可能な項目で選択されていない項目は value = '' でPOST する
  // その準備としてデフォルトデータを作成しておく
  for (let i = 0; i < config.surveySchema.length; i++) {
    const schema = config.surveySchema[i];

    if (schema.type === "selectProducts") {
      continue;
    }

    schemas[schema.itemKey] = schema;

    // 現状、複数選択肢は checkboxes のみ
    if (schema.type === "checkboxes" || schema.type === "radio") {
      // 各 options のデフォルトデータを作成
      for (let j = 0; j < schema.options.length; j++) {
        const option = schema.options[j];

        const tmp = {
          userId: results.userId,
          surveyId: results.surveyId,
          value: "",
          partitionKey: partitionKey,
          sortKey: `${schema.itemKey}#${option}`,
          itemKey: schema.itemKey,
          userSearchKey: `${schema.itemKey}#${option}#`,
          isLiffMode: results.isLiffMode !== undefined ? results.isLiffMode : false,
          check: "未対応",
        };
        data[tmp.sortKey] = tmp;
      }
    }
  }

  //handle for special case: only radio or dropdown but user do not select any thing
  if (results.surveyResults.length === 0) {
    const _firstItem = config.surveySchema[0];
    const tmp = {
      userId: results.userId,
      surveyId: results.surveyId,
      value: "",
      partitionKey: partitionKey,
      sortKey: _firstItem.itemKey,
      itemKey: _firstItem.itemKey,
      userSearchKey: _firstItem.itemKey,
      check: "未対応",
      isLiffMode: results.isLiffMode !== undefined ? results.isLiffMode : false,
    };
    data[tmp.sortKey] = tmp;
  } else {
    // 入力データをもとに POST データ追加 (複数選択項目は上書き)
    for (let i = 0; i < results.surveyResults.length; i++) {
      const surveyResult = results.surveyResults[i];
      const schema = schemas[surveyResult.itemKey];
      const sortKey = `${surveyResult.itemKey}#${surveyResult.value}`;
      if (!schema) continue;
      let userSearchKey = "";
      if (schema.type === "checkboxes") {
        // console.log("surveyResult", surveyResult);
        // console.log("data", data);
        // console.log("sortKey", sortKey);
        // console.log("data[sortKey]", data[sortKey]);
        userSearchKey = data[sortKey].userSearchKey + surveyResult.value;
      } else {
        userSearchKey = `${surveyResult.itemKey}#${surveyResult.value}`;
      }

      const tmp = {
        userId: results.userId,
        surveyId: results.surveyId,
        value: surveyResult.value,
        partitionKey: partitionKey,
        sortKey: sortKey,
        itemKey: surveyResult.itemKey,
        userSearchKey: userSearchKey,
        check: surveyResult.check ? surveyResult.check : "未対応",
        isLiffMode: results.isLiffMode !== undefined ? results.isLiffMode : false,
      } as any;

      if (schema.type === "files") {
        tmp.files = surveyResult.files;
      }

      data[tmp.sortKey] = tmp;
    }
  }
  // TODO use IF needed
  // const surveyReqCheck = surveySchema1 || config.surveySchema
  // const requiredCheck = checkRequiredFields(results, surveyReqCheck);
  // if(Object.keys(requiredCheck).length > 0) {
  //   return {
  //     result: "ERROR",
  //     errorMessage: "必須項目が入力されていません。",
  //     requiredFields: requiredCheck,
  //   };
  // }

  const postBody = {};

  if (additionalData.oldPartitionKey) {
    data.oldPartitionKey = {
      oldPartitionKey: additionalData.oldPartitionKey,
    };
  }
  if (additionalData.subData) {
    postBody["subData"] = additionalData.subData;
  }
  postBody["data"] = Object.values(data);

  if (config?.cancelPost) {
    return postBody;
  }
  if (paymentData) {
    postBody['order'] = paymentData;
  }

  try {
    const params = { body: postBody };
    return await axiosRetry(() => API.post(ApiName, SURVEY_API_PATH.PUT_SURVEY, params));
  } catch (err) {
    return err;
  }
};

export const cancelSurveyResults = async (payload: { surveyId: any; partitionKey: any; userId: any }): Promise<any> => {
  const params = {
    body: {
      surveyId: payload.surveyId,
      partitionKey: payload.partitionKey,
      userId: payload.userId,
    },
  };
  return await API.post(ApiName, SURVEY_API_PATH.CANCEL_SURVEY, params);
};

/**
 * Delete a single survey Item
 * @param {*} surveyItem
 * @param {*} authSession
 */
export const DeleteSingleSurveyItem = async (surveyItem: any, authSession = null): Promise<any> => {
  try {
    const params = { body: surveyItem };
    return await API.del(ApiName, SURVEY_API_PATH.DELETE_SURVEY_RESULT, params);
  } catch (err) {
    return err;
  }
};

/**
 * ユーザーの帳票結果を取得します
 * @param {string} surveyId 帳票ID
 * @param {object} authSession 認証データ
 */
export const GetCategories = async (tag = "tag1", authSession: any = null): Promise<any> => {
  // userId を取得する
  if (authSession) {
    const userInfo = authSession.getIdToken().payload;
    const userId = userInfo.identities[0].userId;
  }

  // dynamoDB の query operation に使うパラメータ
  const data = { query: { tag1: 1, tag2: 1, tag3: 1 } };
  data["query"][tag] = 1;

  try {
    const params = { body: data };
    const result = await API.get(ApiName, SURVEY_API_PATH.GET_CATEGORIES, {
      body: data,
    });

    // details: {,…}
    // stack: "RouteError: Route not found↵    at REQUEST.parseRequest (/var/task/node_modules/lambda-api/lib/request.js:212:15)↵    at API.run (/var/task/node_modules/lambda-api/index.js:220:21)↵    at Runtime.handler (/var/task/handler.js:51:30)"
    // message: "Route not found"

    // 選択していない checkboxes の value などは 不要なので取り除いておく
    // result.data = result.data.filter((item) => item.value);

    return result;
  } catch (err) {
    return err;
  }
};

/**
 * ユーザーの帳票結果を取得します
 * @param {string} surveyId 帳票ID
 * @param {object} authSession 認証データ
 */
export const GetCategoriesTree = async (authSession: any = null): Promise<any> => {
  // userId を取得する
  if (authSession) {
    const userInfo = authSession.getIdToken().payload;
    const userId = userInfo.identities[0].userId;
  }

  // dynamoDB の query operation に使うパラメータ
  const request_data = {};

  const result = { data: { tree: [], display: {} }, status: { value: 0, message: "" }, error: null };
  try {
    const params = { body: request_data };
    result.data.tree = await API.get(ApiName, SURVEY_API_PATH.GET_CATEGORIES_TREE, params);
    result.data.display = await API.get(ApiName, SURVEY_API_PATH.GET_DISPLAY_SETTINGS, params);

    return result;
  } catch (err) {
    result.error = err;
    result.status.value = 1;
    result.status.message = err.message || "Unknown error";
    return result;
  }
};

export const GetRequestToken = async (code: any): Promise<any> => {
  try {
    const result = await API.get(ApiName, SURVEY_API_PATH.GET_REQUEST_TOKEN, {
      queryStringParameters: {
        code: code,
        redirectTo: `https://${window.location.host}/login`
      },
    });
    return result;
  } catch (err) {
    return { error: err };
  }
};

export const GetSurveyConfigList = async (payload: any): Promise<any> => {
  const params = {
    body: Object.assign({
      surveyIdList: payload,
    }),
  };
  const result = await API.post(ApiName, SURVEY_API_PATH.GET_SURVEY_CONFIGS_LIST, params);

  return result;
};

type TempSaveResult = {
  code: 'success',
  orderId: string,
  form: Record<string, any>,
  message: string,
  errorMessage: string,
  payload: any,
}

export const SaveTemporarySurveyResults = async (payload: any): Promise<TempSaveResult> => {
  return await API.post(ApiName, SURVEY_API_PATH.SURVEY_RESULTS_TEMPORARY, {
    body: payload
  });
};


// Item Validation (FE) (Only Required)
// 項目バリデーション (FE) (必須項目のみ)

const checkRequiredFields = (data: any, surveySchema: any): any => {
  const requiredFields: Record<string, string[]> = {};

  const checkItem = (schemaItem: any, parentDisabled: boolean) => {
    const isRequired = schemaItem.isRequired?.value === true;
    if (isRequired && !parentDisabled) {
      const entry = data.surveyResults.find((item: any) => item.itemKey === schemaItem.itemKey);
      if (!entry || entry.value === undefined || entry.value === null || entry.value === '') {
        requiredFields[schemaItem.itemKey] = ["必須項目"];
      }
    }
  };

  for (const schema of surveySchema) {
    const parentDisabled = schema.disabled ?? false;

    if (Array.isArray(schema.items)) {
      for (const nestedItem of schema.items) {
        checkItem(nestedItem, parentDisabled);
      }
    } else {
      checkItem(schema, parentDisabled);
    }
  }

  return requiredFields;
};

