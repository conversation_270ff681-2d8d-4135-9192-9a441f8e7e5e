/*
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
 */
import { createApp } from 'vue'
import router from './router/index';
import store from './store';
import { AmplifyService } from './services/amplify.service';
import { LiffService } from './services/liff.service';

import { Quasar } from 'quasar';  // Quasarプラグイン
import quasarLang from 'quasar/lang/ja';  // Quasarの言語設定（日本語）
//import quasarUserOptions from '.quasar/quasar-user-options';

import App from './App.vue';
import ContentLoading from './components/common/ContentLoading.vue';

import { createPinia } from 'pinia';

import dotenv from 'dotenv';

dotenv.config();

const app = createApp(App);

// Setup service 
AmplifyService.configure();
LiffService.setup();




app.use(store);

app.use(router);

// Quasarをアプリに登録し、日本語設定を適用
app.use(Quasar, {
  lang: quasarLang,
});
//app.use(Quasar,quasarUserOptions);


const pinia = createPinia();
app.use(pinia);

//app.config.productionTip = false;
app.component("content-loading", ContentLoading);
app.mount("#app");
