<style scoped>
.results-display-card {
  padding: 0.5em;
}

.confirm-details-title {
  font-weight: bold;
}
</style>
<template>
  <div class="q-pa-md">
    <div class="row flex justify-center">
      <div class="col-mx-auto col-12 col-sm-10 col-md-6 col-xl-4">
        <q-card shaped class="mx-auto">
          <q-list>
            <q-item>
              <q-item-section>
                <q-avatar v-if="lineUser && lineUser.picture">
                  <q-img :src="lineUser.picture"></q-img>
                </q-avatar>
                <q-avatar v-else color="primary">
                  <q-icon dark name="mdi-account-circle"></q-icon>
                </q-avatar>
              </q-item-section>

              <q-item-section>
                <q-item-label>
                  <span v-if="lineUser">{{ lineUser.name }}</span> 様
                </q-item-label>
              </q-item-section>

              <q-item-section></q-item-section>
            </q-item>
          </q-list>
        </q-card>
        <div class="row">
          <div align="center" class="col-12 q-py-md">
            <span class="confirm-details-title">現在の登録情報</span>
          </div>
        </div>
        <div v-if="doesUsePayment && !isPaymentAvailable">
          <Alert dense outlined type="error" class="my-4" elevation="4">
            現在キャンセルを受け付けておりません。
          </Alert>
        </div>
        <div v-if="isPushing" class="pa-4 text-center">
          <q-circular-progress indeterminate color="primary"></q-circular-progress>
          しばらくお待ちください。
        </div>
        <div v-else-if="cancelFinish">
          <Alert dismissible dense outlined type="info" class="my-4" elevation="4">
            キャンセルが完了しました。<br />
          </Alert>
        </div>
        <div v-else-if="hasError">
          <Alert v-model="hasError" dismissible dense outlined type="error" class="my-4" elevation="4">
            {{ errorMessage }}
            <br />
            画面を閉じてやり直してください。
          </Alert>
        </div>
        <q-card class="q-mb-md results-display-card">
          <div v-for="(item, index) in resultsForDisplay" :key="index">
            <span v-if="item.name !== '購入商品'">{{ item.name }} : {{ item.value }}</span>
            <div v-else>
              購入商品:
              <SelectedProduct :order="item.value" />
            </div>
          </div>
          <p></p>
        </q-card>

        <BackButtonMessage />

        <q-card-actions>
          <q-space></q-space>
          <q-btn v-if="isShowCancelButton" color="negative" @click="showCancelConfirmPopup">キャンセル</q-btn>
          <q-btn color="primary" @click="returnToConfirmList">戻る</q-btn>
          <q-space></q-space>
        </q-card-actions>
      </div>
      <CancelConfirmPopUp :visible="cancelConfirmPopup" @close="cancelConfirm" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Auth } from '@/services/auth.service';
import { useSurveyResultsStore } from '@/stores/modules/surveyResults';
import moment from 'moment';
import { ref, computed, onBeforeMount, ComputedRef } from 'vue';

import { useRoute, useRouter } from 'vue-router';
import { useMainStore } from '@/stores';


// old imports
// 旧インポート
import liff from "@line/liff";
import CancelConfirmPopUp from "@/components/CancelConfirmPopUp.vue";
import BackButtonMessage from "@/components/BackButtonMessage.vue";
import SelectedProduct from "@/components/SelectedProduct.vue";
/* import {
  CANCEL_SURVEY_RESULTS,
} from "@/surveyResultsStore/action-types";
 */

const mainStore = useMainStore();
const surveyResultsStore = useSurveyResultsStore();

// emits 
const emits = defineEmits<{
  (event: 'close'): void;
}>();
const router = useRouter();
const route = useRoute();

// props
const props = defineProps({
  close: Function
});

// data
const surveyConfig = ref<any>(null);
const specialDisplayValues = ref<any>(["予約日", "予約時間"]);
const reservationTime = ref<string>("予約時間");
const check = ref<string>("check.value");
const reservationDate = ref<string>("予約日");
const hasError = ref<boolean>(false);
const isPushing = ref<boolean>(false);
const cancelFinish = ref<boolean>(false);
const japaneseDayMapping = ref<any>({
  1: "月",
  2: "火",
  3: "水",
  4: "木",
  5: "金",
  6: "土",
  0: "日",
});
const cancelConfirmPopup = ref<boolean>(false);
const additionalErrorMessage = ref<string>('');
const defaultErrorMessage = ref<string>('エラ－が発生しました。');

// methods
const cancelSurveyResult = surveyResultsStore.cancelSurveyResults;
const returnToConfirmList = (): void => {
  router.push({ name: "SurveyConfirmList" });
};
const cancelConfirm = async (value: any): Promise<void> => {
  cancelConfirmPopup.value = false;
  if (value) {
    //Update Status to キャンセル
    await updateStatus();
    emits("close");
  }
};
const showCancelConfirmPopup = (): void => {
  cancelConfirmPopup.value = true;
};
const updateStatus = async (): Promise<void> => {
  try {
    additionalErrorMessage.value = '';
    isPushing.value = true;
    const _pushRes = await cancelSurveyResult({
      surveyId: surveyConfigAllItem.value.surveyId,
      partitionKey: mainStore.selectedSurveyResults.data[0].partitionKey,
      userId: getUserId.value,
    });

    if (_pushRes && _pushRes.response && _pushRes.response.status !== 200) {
      hasError.value = true;
      isPushing.value = false;
      return;
    }

    if (_pushRes && _pushRes.result !== "ERROR") {
      cancelFinish.value = true;
      hasError.value = false;
    } else {
      additionalErrorMessage.value = _pushRes.errorMessage.value;
      hasError.value = true;
    }
  } catch (error) {
    hasError.value = true;
  } finally {
    isPushing.value = false;
  }
};
const getFormattedDate = (date: moment.MomentInput): string => {
  try {
    const momentDate = moment(date);
    return (
      momentDate.month() +
      1 +
      "月" +
      momentDate.date() +
      "日" +
      "（" +
      japaneseDayMapping.value[momentDate.day()] +
      "）"
    );
  } catch (error) {
    console.error(error);
    return "不明日付形";
  }
};
const getSurveyValueByTitle = (titleToSearch: any): any => {
  let itemKey = null;
  for (let i = 0; i < mainStore.surveyConfig.value.surveySchema.length; i++) {
    const item = mainStore.surveyConfig.value.surveySchema[i];
    if (titleToSearch === item.title) {
      itemKey = item.itemKey;
    }
  }
  if (itemKey == null) {
    return "不明";
  }
  for (let i = 0; i < mainStore.selectedSurveyResults.data.length; i++) {
    const item = mainStore.selectedSurveyResults.data[i];
    if (itemKey === item.itemKey) {
      return item.value;
    }
  }
  return "不明";
};
const getSurveyConfigTitle = (itemKey: any): any => {
  for (let i = 0; i < mainStore.surveyConfig.surveySchema.length; i++) {
    const item = mainStore.surveyConfig.surveySchema[i];
    if (itemKey === item.itemKey) {
      return item.title;
    }
  }
  return "";
};

// computed
const authSession = computed(() => mainStore.authSession);
const selectedSurveyResults = computed(() => mainStore.selectedSurveyResults);
const surveyConfigAllItem: ComputedRef<any> = computed(() => mainStore.surveyConfig);
const lineUser = computed((): any => {
  return mainStore.lineUser;
});
const getUserId = computed((): any => {
  // userId を取得する
  const userInfo = authSession.value.getIdToken().payload;
  return userInfo.identities[0].userId;
});
const resultsForDisplay = computed((): any => {
  return mainStore.selectedValuesForCancel;
});
const doesUsePayment = computed((): boolean => {
  return surveyConfigAllItem.value.usePayment === 1;
});
const errorMessage = computed((): string => {
  return `${additionalErrorMessage.value || defaultErrorMessage.value}`;
});
const isPaymentAvailable = computed((): boolean => {
  return import.meta.env.VITE_USE_PAYMENT === '1';
});
const isShowCancelButton = computed((): boolean => {
  if (doesUsePayment.value) {
    return isPaymentAvailable.value && !cancelFinish.value;
  } else {
    return !cancelFinish.value;
  }
});

// hooks

onBeforeMount(async () => {
  const session = await Auth.currentSession().catch(e => {
    // 未ログイン
    return null;
  });
  if (!session) {
    // alert("auth error"); // /loginを通ってきてればあるはず
    window.sessionStorage.setItem("lif.", route.path);
    await router.push({
      name: "Login"
    });
    return;
  }
  if (!mainStore.authSession || !mainStore.selectedValuesForCancel) {
    await router.replace({
      name: "SurveyConfirm"
    });
  }
});

</script>

<style lang="less">
.form-design-code .CodeMirror {
  height: 80vh !important;
}

.confirmation-table {
  border: 1px solid rgba(0, 0, 0, 0.12);

  .confirm-title {
    background: rgba(0, 0, 0, 0.04);
  }
}
</style>
