<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-layout view="hHh Lpr lFf">
    <q-page-container>
      <div class="q-ma-sm q-mx-md">
        <div cols="12" sm="10" md="6" xl="4" class="q-mx-auto">
          <div v-if="isLoading" class="full-height">
            <q-page-container style="height: 90vh">
              <div class="full-height flex flex-center" align-content="center" justify="center">
                <div class="subtitle-1 text-center" cols="12">
                  情報を取得中です。
                  <br />しばらくおまちください。
                  <div class="q-mt-md">
                    <q-linear-progress color="green-14" indeterminate rounded height="6"></q-linear-progress>
                  </div>
                </div>
              </div>
            </q-page-container>
          </div>
          <div v-else>
            <div v-if="surveyConfig != null" class="window-height">
              <ErrorComponent :errorMessage="error" :errorDetails="errorDetails" :isDismissible="true" />
              <FormRendering :configJson="surveyConfig" :isLiffMode="true" :userInfo="lineUser"
                :isSurveyRegistering="false"
                :pinia="getActivePinia()"
                :isConfirmSurveyMode="true" :userId="getUserId" ref="memberInputForm" />
              <q-btn color="primary" dark @click="onClickSearch" block class="q-my-md full-width">
                検索
              </q-btn>
            </div>
            <div v-else>
              <LineUserCard />
              <ErrorComponent :errorMessage="error" :errorDetails="errorDetails" />
            </div>
          </div>
        </div>
      </div>
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeMount } from 'vue';

import { useMemberStore } from '@stores/modules/member';
import { useMainStore } from '@stores/index';
import { useRoute, useRouter } from 'vue-router';

// old imports
// 旧インポート
import { Auth } from "@/services/auth.service";
import FormRendering from "lsc-form-rendering";
import ErrorComponent from "@/components/common/ErrorComponent.vue";
import LineUserCard from "@/components/common/LineUserCard.vue";
import {
  GetCategoriesTree,
  getReservationItemInfo,
} from "@/services/calendars.service";
import {
  GetSurveyById,
} from "@/services/member.service";
/*import {
  FIND_MEMBER_RESULTS_BY_USER_ID,
  GET_FILTERED_MEMBER_RESULTS_BY_FORM_DATA,
} from "@/store/action-types";
import {
  SET_LINE_USER_INFO,
  SET_AUTH_SESSION,
  SET_SELECTED_MEMBER_CONFIG,
  SET_SHOW_ERROR_COMPONENT,
} from "@/store/mutation-types";*/
import { cloneDeep } from 'lodash';
import { getActivePinia } from 'pinia';
import { triggerLogin } from '@/utils/surveyResultsUtils';
import { useQuasar } from 'quasar';

const mainStore = useMainStore();
const memberStore = useMemberStore();

const router = useRouter();
const route = useRoute();
const $q = useQuasar();

const memberInputForm = ref<any>(null);

// data
const formData = ref<FormData>(new FormData());
const surveyConfig = ref<any>();
const isLoading = ref<boolean>(false);
const error = ref<any>(null);
const errorDetails = ref<any>(null);
const reservationItemData = ref<any>(null);

const authSession = ref<any>(null);

// methods
const findMemberResultsByUserId = memberStore.findMemberResultsByUserId;
const GetFilteredMemberResultByFormData = memberStore.getFilteredMemberResultsByFormData;
const setSelectedMemberConfig = memberStore.setSelectedMemberConfig;
const setLineUser = mainStore.setLineUserInfo;
const setAuthSession = mainStore.setAuthSession;
const setShowErrorComponent = mainStore.setShowErrorComponent;
const attemptToFindMemberResultsByUserId = async (): Promise<void> => {
  await findMemberResultsByUserId({
    surveyId: selectedMemberConfig.value.memberSurveyId,
    userId: getUserId.value
  });
};
const setUpMemberConfirmPage = async (): Promise<void> => {
  isLoading.value = true;
  let result = await GetSurveyById(route.params.id);

  let surveyConfigCopy = null;
  if (result && result.data) {
    surveyConfigCopy = cloneDeep(result.data);
    setSelectedMemberConfig(result.data)
  } else {
    displayError("帳票スキーマ見つけられません。管理者にお問い合わせください。")
    isLoading.value = false;
    return;
  }

  try {
    await attemptToFindMemberResultsByUserId();

    //LINEユーザーIDで会員情報見つけました。そのまま一覧画面遷移します。
    if (memberData.value && memberData.value.partitionKey) {
      await router.push({ name: "MemberConfirmList" });
      return;
    }

    let tempSurveySchema = [];
    let categories_tree = null;
    //照会アイテムを帳票スキーマのみ表示したい
    for (const item in surveyConfigCopy.surveySchema) {
      let tempItem = surveyConfigCopy.surveySchema[item];
      //固定でプルダウンの検索入力に大分類の値を入れる
      if (tempItem.type == 'reservation' && categories_tree == null) {
        categories_tree = await GetCategoriesTree();
        if (Array.isArray(categories_tree)) {
          tempItem.options = categories_tree.map(x => x.name);
        }
      }
      if ('isSearchable' in tempItem && tempItem.isSearchable.value) {
        tempSurveySchema.push(tempItem);
      }
    }
    // 予約項目をセット
    let itemInfo = await getReservationItemInfo('all');
    reservationItemData.value = itemInfo;
    tempSurveySchema.forEach(function (ss) {
      if (ss.type === 'reservation') {
        ss['reservationItemData.value'] = itemInfo;
      }
    });

    surveyConfigCopy.surveySchema = tempSurveySchema;

    surveyConfig.value = surveyConfigCopy;
  } catch (error) {
    console.error(error);
    displayError("画面表示中にエラーが発生しました。管理者にお問い合わせください。", error.message);
  } finally {
    isLoading.value = false;
  }
};

const onClickSearch = async (): Promise<void> => {
  try {
    isLoading.value = true;
    error.value = null;
    errorDetails.value = null;

    const formData = memberInputForm.value.getFormDataJson(getUserId.value); //Input form data in dict

    // 暫定処置として必須項目だけ判定する
    const searchData = [];
    if (formData.value) {
      //loop input form data in dict to map the itemKey with extracted required itemKey ↑
      for (let i = 0; i < formData.value.surveyResults.length; i++) {
        const data = formData.value.surveyResults[i];
        if (data.value) {
          data.value = toASCII(data.value)
        }
        searchData.push(data);
      }
    }
    formData.value.surveyResults = searchData //required input form data

    // get data by parametering the required input form data from liff/src/services/surveys.service.js
    // post /survey/api/v1/db/survey-results/filterQuery -> aws_back/survey_api/accessor/lambda/src/controllers/survey-results.js / getSurveyResultFilter
    await GetFilteredMemberResultByFormData(formData.value);

    if (searchMemberDataError.value) {
      displayError("検索実行中にエラー発生しました。管理者にお問い合わせください。", searchMemberDataError.value);
      return;
    } else if (memberData.value && memberData.value.partitionKey) {
      await router.push({ name: "MemberConfirmList" });
      return;
    } else {
      alert('検索対象のデータがありません')
    }
  } catch (err) {
    displayError("検索実行中にエラー発生しました。管理者にお問い合わせください。", err.message);
  } finally {
    isLoading.value = false;
  }
};
const toASCII = (chars: string | any[]): any => {
  var ascii = '';
  for (var i = 0, l = chars.length; i < l; i++) {
    var c = chars[i].charCodeAt(0);

    // make sure we only convert half-full width char
    if (c >= 0xFF00 && c <= 0xFFEF) {
      c = 0xFF & (c + 0x20);
    }

    ascii += String.fromCharCode(c);
  }

  return ascii ? ascii : chars;
};
const displayError = (mainError: any, errorDetails?: any): void => {
  error.value = mainError;
  errorDetails.value = errorDetails.value;
  setShowErrorComponent(true);
};

// computed
const selectedMemberConfig = computed(() => memberStore.selectedMemberConfig);
const memberData = computed(() => memberStore.memberData);
const searchMemberDataError = computed(() => memberStore.searchMemberDataError);
const lineUser = computed(() => mainStore.lineUser);
const getUserId = computed(() => {
  const userInfo = authSession.value.getIdToken().payload;
  return userInfo.identities[0].userId;
});

// watch
  // userId を取得する


// hooks

onBeforeMount(async () => {
  authSession.value = await Auth.currentSession().catch((e: any) => {
    // 未ログイン
    return null;
  });
  if (!window.sessionStorage.getItem('liffAccessToken')) {
    triggerLogin($q, router, route);
    return;
  }
  setAuthSession(authSession.value);
  setLineUser(authSession.value.getIdToken().payload);
  await setUpMemberConfirmPage();
});

</script>

<style lang="less">
.form-design-code .CodeMirror {
  height: 80vh !important;
}

.confirmation-table {
  border: 1px solid rgba(0, 0, 0, 0.12);

  .confirm-title {
    background: rgba(0, 0, 0, 0.04);
  }
}
</style>
