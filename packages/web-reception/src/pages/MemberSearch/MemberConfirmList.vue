<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
  .full-width {
    width: 100%;
  }
</style>

<template>
  <div>
    <q-page-container>
      <q-row>
        <q-col cols="12" sm="10" md="6" xl="4" class="mx-auto">
          <div v-if="isLoading">
            <q-page-container style="height: 90vh;">
              <q-row
                  class="fill-height"
                  align-content="center"
                  justify="center"
              >
                <q-col class="subtitle-1 text-center" cols="12">
                  情報を取得中です。
                  <br />しばらくおまちください。
                </q-col>
                <q-col cols="6">
                  <q-linear-progress
                      color="green accent-4"
                      indeterminate
                      rounded
                      height="6"
                  ></q-linear-progress>
                </q-col>
              </q-row>
            </q-page-container>
          </div>
          <div v-else>
            <!--LINE User Card-->
            <LineUserCard/>

            <!--会員情報-->
            <q-row>
                <q-col align="center" class="confirm-list-title-row" cols="12">
                    <span class="bold-text">{{selectedMemberConfig.surveyTitle}}</span><br>
                    <SurveyResultsCard
                        :surveyConfig="selectedMemberConfig"
                        :surveyResults="memberData"
                        cardTitle="会員情報"
                        :isMemberResults="true"
                    />
                </q-col>
            </q-row>
            <!--連携帳票ピッカー-->
            <q-row>
              <q-col align="center">
                <div v-if="fetchingLinkedSurveyConfigs">
                  <ContentLoading />
                </div>
                <div v-else class="full-width">
                  <q-select
                    v-model="selectedSurvey"
                    :items="linkedSurveyConfigs"
                    item-text="surveyTitle"
                    label="連携帳票"
                    :disabled="displayResultsLoading"
                    return-object
                    solo>
                  </q-select>
                  <br/>
                  <SurveyResultsDisplay v-if="selectedSurvey"
                    :surveyConfig="selectedSurvey"
                    @loadingResults="onLoadingResults"
                  />
                </div>
              </q-col>
            </q-row>
          </div>
        </q-col>
      </q-row>
    </q-page-container>
  </div>
</template>

<script setup lang="ts">
import {ref,  computed, onBeforeMount } from 'vue';

import { useMemberStore } from '@stores/modules/member';
import { useRoute, useRouter } from 'vue-router';

const memberStore = useMemberStore();
const surveyResultsStore = useSurveyResultsStore();

// old imports
// 旧インポート
import { Auth } from "@/services/auth.service";
import SurveyResultsCard from "@/components/common/SurveyResults/SurveyResultsCard.vue";
import SurveyResultsDisplay from "@/components/common/SurveyResults/SurveyResultsDisplay.vue";
import LineUserCard from "@/components/common/LineUserCard.vue";
import ContentLoading from "@/components/common/ContentLoading.vue";
import { useSurveyResultsStore } from '@/stores/modules/surveyResults';
/* import {
  FETCH_SURVEY_CONFIG_LIST,
  FETCH_CALENDAR_DISPLAY_SETTINGS,
} from "@/store/action-types"; */

const router = useRouter();
const route = useRoute();

// data
const isLoading = ref<boolean>(false);
const error = ref<any>(null);
const selectedSurvey = ref<any>(null);
const displayResultsLoading = ref<boolean>(false);

const authSession = ref<any>(null);

// methods
const fetchLinkedSurveyConfigs = memberStore.fetchSurveyConfigList;
const getCalendarDisplaySettings = surveyResultsStore.fetchCalendarDisplaySettings;
const onLoadingResults = (value: any): void => {
      displayResultsLoading.value = value;
    };

// computed
const selectedMemberConfig = computed(() => memberStore.selectedMemberConfig);
const linkedSurveyConfigs = computed(() => memberStore.linkedSurveyConfigs);
const fetchingLinkedSurveyConfigs = computed(() => memberStore.fetchingLinkedSurveyConfigs);
const memberData = computed(() => memberStore.memberData.value);
const getUserId = computed((): Promise<any> => {
      const userInfo = authSession.value.getIdToken().payload;
      return userInfo.identities[0].userId;
    });

// hooks

onBeforeMount(async () => {
  isLoading.value = true;
  authSession.value = await Auth.currentSession().catch((e: any) => {
    // 未ログイン
    return null;
  });
  if (!authSession.value) {
    window.sessionStorage.setItem("liffState", route.path.replace("member-confirmList", "member-confirm"));
    await router.push({
      name: "Login"
    });
    return;
  }
  if (!memberData.value || !memberData.value.partitionKey) {
    await router.push({
      name: "MemberConfirm"
    });
    return;
  }
  fetchLinkedSurveyConfigs(selectedMemberConfig.value.linkedForms);
  getCalendarDisplaySettings();
  isLoading.value = false;
});

</script>
