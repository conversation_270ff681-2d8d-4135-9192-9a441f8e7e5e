<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.bold-text {
  font-weight: bold;
}

.appointment-card {
  padding: 0.5em;
}

.confirm-list-title-row {
  padding-bottom: 0;
}

.confirm-list-patient-row {
  padding-top: 0;
}

.confirm-list-button {
  width: inherit;
}
</style>
<template>
  <div>
    <q-overlay :opacity="0.2" v-if="loadingResults">
      <content-loading :size="50" text="" />
    </q-overlay>
    <q-page-container>
      <q-row>
        <q-col cols="12" sm="10" md="6" xl="4" class="mx-auto">
          <q-card shaped elevation="1" class="mx-auto">
            <q-list>
              <q-item>
                <q-item-section avatar>
                  <q-img v-if="lineUser && lineUser.picture" :src="lineUser.picture"></q-img>
                  <q-avatar v-else color="primary">
                    <q-icon dark>mdi-account-circle</q-icon>
                  </q-avatar>
                </q-item-section>

                <q-item-section>
                  <q-item-label>
                    <span v-if="lineUser">{{ lineUser.name }}</span> 様
                  </q-item-label>
                </q-item-section>

                <q-item-section> </q-item-section>
              </q-item>
            </q-list>
          </q-card>
          <q-row>
            <q-col align="center" class="confirm-list-title-row" cols="12">
              <span class="bold-text">現在の予約</span>
            </q-col>
          </q-row>
          <q-row v-if="shouldShowName">
            <q-col align="center" class="confirm-list-patient-row" cols="12">
              <span>患者氏名:</span>
              <span v-if="mainStore.surveyFamilyName"> {{ mainStore.surveyFamilyName }} </span>
              <span v-if="mainStore.surveyFirstName">{{ mainStore.surveyFirstName }}</span>
            </q-col>
          </q-row>

          <div v-for="(result, index) in displayDates" :key="result">
            <span class="bold-text">{{ getFrontEndDisplay(result) }}</span>
            <q-card class="mb-8 appointment-card" v-for="surveyResult in getValuesFromDisplayResult(result)"
              :key="surveyResult.partitionKey">
              <div>診察予約：{{ mainStore.surveyConfig.surveyTitle }}</div>
              <div>
                予約時間：<span :class="{ 'cancelled-reservation': cancelledReservationCheck(index) }">{{
                  getTimeDisplay(index)
                }}</span>
              </div>
              <div v-for="(category, categoryIndex) in categoryDisplayLables" :key="categoryIndex">
                {{ category }}：{{ getTagValueFromIndex(index, String(categoryIndex + 1)) }}
              </div>
              <!--<div>
                  {{ surveyResult.partitionKey }}
                </div>
                <div>
                  answer code : {{ surveyResult.data[0].answerCode }}
                </div>
                <div>
                  Status : {{ surveyResult.data[0].check }}
                </div>-->
              <q-card-actions>
                <q-space></q-space>
                <q-btn color="primary" @click="changeConfirm(surveyResult)"> 編集 </q-btn>
                <q-btn color="primary" @click="viewSurveyDetails(surveyResult)"> 詳細確認 </q-btn>
                <q-btn color="primary" @click="cancelConfirm(surveyResult)"> キャンセル </q-btn>
                <q-space></q-space>
              </q-card-actions>
            </q-card>
          </div>

          <BackButtonMessage />

          <q-row>
            <q-col align="center" cols="4" offset="4">
              <q-btn @click="returnToSearchForm" dark block class="primary confirm-list-button"> 戻る </q-btn>
            </q-col>
          </q-row>
        </q-col>
      </q-row>
    </q-page-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeMount } from 'vue';

import { useRoute, useRouter } from 'vue-router';

import { useMainStore } from '@/stores';

const mainStore = useMainStore();

// old imports
// 旧インポート
import moment from "moment";
import {
  GetSurveyResult,
  PutSurveyResult,
  // GetSurveyResultByFormData,
  UpdateUserIdToSurveyResultByPks,
  DeleteSingleSurveyItem,
} from "@/services/surveys.service";
import { GetCalendarInfo, GetAllCategories, GetTagNames } from "@/services/calendars.service";
import { cloneDeep } from "lodash";
import liff from "@line/liff";
import BackButtonMessage from "@/components/BackButtonMessage.vue";

const router = useRouter();
const route = useRoute();

// data
const displayResults = ref<any>(null);
const displayDates = ref<any>(null);
const surveyResults = ref<any>(null);
const patientFamilyName = ref<any>(null);
const patientFirstName = ref<any>(null);
const loadingResults = ref<boolean>(false);
const surveyIsCorona = ref<boolean>(false);
const categoryValuesForResults = ref<any>([]);
const displayTimesForResults = ref<any>({});
const displayCategoriesForResults = ref<any>({});
const cancelledText = ref<string>("キャンセル済");
const coronaManagementDate = ref<string>("接種実施日");
const reservationTime = ref<string>("予約時間");
const reservationDate = ref<string>("予約日");
const reservationDateNotFound = ref<string>("予約日付不明");
const partitionKey = ref<string>("予約日付不明");
const japaneseDayMapping = ref<any>({
  1: "月",
  2: "火",
  3: "水",
  4: "木",
  5: "金",
  6: "土",
  0: "日",
});

// methods
const returnToSearchForm = (): void => {
  router.push({ name: "ConfirmSearchForm" });
};
const closeLiffApp = (): void => {
  liff.closeWindow();
};
const getTagValueFromIndex = (surveyIndex: number, tagIndex: string): any => {
  return surveyIndex in displayCategoriesForResults.value
    ? displayCategoriesForResults.value[surveyIndex]["tag" + tagIndex]
    : "不明";
};
const getDisplayString = (surveyResult: any, titleToSearch: string): string => {
  let itemKeyToSearch = null;
  for (let i = 0; i < mainStore.surveyConfig.surveySchema.length; i++) {
    const item = mainStore.surveyConfig.surveySchema[i];
    if (titleToSearch == item.title) {
      itemKeyToSearch = item.itemKey;
      break;
    }
  }

  return titleToSearch + "：" + getSurveyValue(surveyResult, itemKeyToSearch);
};
const cancelledReservationCheck = (index: number): boolean => {
  return index in Object.keys(displayTimesForResults.value)
    ? displayTimesForResults.value[index] == cancelledText.value
    : false;
};
const getTimeDisplay = (index: number): any => {
  return index in Object.keys(displayTimesForResults.value) ? displayTimesForResults.value[index] : "不明";
};
const getReservationTimeDisplay = async (categoryData: string, surveyResultIndex: string | number): Promise<void> => {
  //get the coma from categories,
  //fetch the value of coma for that category
  var displayResult = "";
  try {
    var parsedCategoryData = categoryData.split("|");
    var categoryId = parsedCategoryData[0].split("_")[0];
    var comaId = parsedCategoryData[2];
    var momentDate = null;
    if (Object.keys(mainStore.calendarSettings).includes(categoryId)) {
      momentDate = moment(mainStore.calendarSettings[categoryId].comaList[comaId].start, "hmm");
      if (momentDate._isValid) {
        displayResult = momentDate.format("HH:mm");
      } else {
        displayResult = "不明";
      }
      //can get from state
    } else {
      //fetch from backend
      var calendarInfo = await GetCalendarInfo(categoryId);
      mainStore.calendarSettings[categoryId] = calendarInfo;
      momentDate = moment(calendarInfo.comaList[comaId].start, "hmm");
      if (momentDate._isValid) {
        displayResult = momentDate.format("HH:mm");
      } else {
        displayResult = "不明";
      }
    }
  } catch (error) {
    //invalid category format or coma value
    displayResult = "不明";
  }

  displayTimesForResults.value[surveyResultIndex] = displayResult;
};
const getCategoryValuesDisplay = async (categoryData: string, surveyResultIndex: string | number): Promise<void> => {
  //categoryValues
  var displayResult = [];
  try {
    var parsedCategoryData = categoryData.split("|");
    var categoryId = parsedCategoryData[0].split("_")[0];

    if (Object.keys(mainStore.categoryValues).includes(categoryId)) {
      displayResult = mainStore.categoryValues[categoryId];
    }
  } catch (err) {
    displayResult = [];
  }

  displayCategoriesForResults.value[surveyResultIndex] = displayResult;
};
const getItemKeyByTitle = (titleToSearch: any): any => {
  for (let i = 0; i < mainStore.surveyConfig.surveySchema.length; i++) {
    const item = mainStore.surveyConfig.surveySchema[i];
    if (titleToSearch == item.title) {
      return item.itemKey;
    }
  }
  return "";
};
const getSurveyType = (itemKey: any): any => {
  for (let i = 0; i < mainStore.surveyConfig.surveySchema.length; i++) {
    const item = mainStore.surveyConfig.surveySchema[i];
    if (itemKey == item.itemKey) {
      return item.type;
    }
  }
  return "";
};
const getSurveyValue = (surveyResult: { data: string | any[]; }, itemKey: any): any => {
  for (let i = 0; i < surveyResult.data.length; i++) {
    const item = surveyResult.data[i];
    if (itemKey == item.itemKey) {
      return item.value;
    }
  }
  return "";
};
const getFrontEndDisplay = (date: moment.MomentInput): string => {
  var momentDate = moment(date);
  if ((momentDate as any)._isValid) {
    return (
      momentDate.month() +
      1 +
      "月" +
      momentDate.date() +
      "日" +
      "（" +
      japaneseDayMapping.value[momentDate.day()] +
      "）"
    );
  } else {
    return "不明日付形";
  }
};
const viewSurveyDetails = (surveyResult: { partitionKey: any; }): void => {
  var resultsToDisplay = mainStore.surveyResults.value.find((survey: { partitionKey: any }) =>
    survey.partitionKey.value == surveyResult.partitionKey.value);

  mainStore.selectedSurveyResults = resultsToDisplay;

  router.push({ name: "ConfirmDetails" });
};
const cancelConfirm = (surveyResult: any): void => {
  mainStore.selectedSurveyResults = surveyResult;
  router.push({ name: "CancelConfirm" });
};
const changeConfirm = (surveyResult: any): void => {
  // mainStore.selectedSurveyResults = surveyResult;
  // router.push({ name: "CancelConfirm" });
  alert("開発中。。。。");
};
const getValuesFromDisplayResult = (dateValue: string): any => {
  return dateValue in displayResults.value
    ? displayResults.value[dateValue]
    : displayResults.value[reservationDateNotFound.value];
};
const checkUserId = (): boolean => {
  if (!surveyResults.value) {
    return true;
  }
  const userId = getUserId.value;
  for (let result of surveyResults.value) {
    for (let item of result.data) {
      if (item.userId && item.userId !== "" && item.userId.startsWith("U") && item.userId !== userId) {
        alert(
          "ご利用のLINE アカウントでは、対象データを照会することは出来ません。\n以前のアカウントで再度照会いただくか、お電話にてお問い合わせください。"
        );
        router.push({ name: "ConfirmSearchForm" });
        return false;
      }
    }
  }
  return true;
};
const updateUserIdToSurveyResult = async (): Promise<void> => {
  if (!surveyResults.value) {
    return;
  }
  const userId = getUserId.value;
  let keys = [];
  for (let result of surveyResults.value) {
    for (let item of result.data) {
      keys.push({ partitionKey: item.partitionKey, sortKey: item.sortKey });
    }
  }

  let params = { userId: userId, keys: keys };
  UpdateUserIdToSurveyResultByPks(params);
};
const setupResultsForDisplay = async (): Promise<void> => {
  surveyResults.value = mainStore.surveyResults;
  let searchResultsByDay = [];
  let tempDisplayDates = [];

  if (surveyResults.value) {
    var resultCounter = 0;
    for (var result of surveyResults.value) {
      let tempList = result.data;
      let tempDate = null;
      for (var item of tempList) {
        if (getSurveyType(item.itemKey) === "reservation") {
          try {
            tempDate = item.value.split("|")[1];
          } catch (error) {
            //invalid category item
          }
          //Try to search survey config for a reservation/分類 type
          //item.value should look like category#068536|20210220|2
          categoryValuesForResults.value.push(item.value);
          await getReservationTimeDisplay(item.value, resultCounter);
          await getCategoryValuesDisplay(item.value, resultCounter);

          if (surveyIsCorona.value) {
            //check that the 接種実施日 value is empty and that
            //the time on the clock is tempDate and getReservationTimeDisplay
            //if so, clear the reservation value from the db and display
            var managementInputDate = getSurveyValue(
              result,
              getItemKeyByTitle(coronaManagementDate.value)
            );
            if (!managementInputDate) {
              var reservationTime = moment(
                tempDate + " " + displayTimesForResults.value[resultCounter],
                "YYYYMMDD HH:mm"
              );

              var currentTime = moment();
              if (currentTime.isAfter(reservationTime)) {
                var deleteResult = await DeleteSingleSurveyItem(item);
                if (deleteResult.result == "OK") {
                  var updateResult = cloneDeep(result);

                  updateResult.data = updateResult.data.filter((i: { itemKey: any; }) => i.itemKey !== item.itemKey);

                  mainStore.surveyResults.value[resultCounter] = updateResult;

                  displayTimesForResults.value[resultCounter] = cancelledText.value;
                }
              }
            }
          }
        }
      }
      if (tempDate != null && !(tempDate in searchResultsByDay)) {
        searchResultsByDay[tempDate] = [result];
        tempDisplayDates.push(tempDate);
      } else if (tempDate != null && tempDate in searchResultsByDay) {
        searchResultsByDay[tempDate].push(result);
      } else {
        if (reservationDateNotFound.value in searchResultsByDay) {
          searchResultsByDay[reservationDateNotFound.value].push(result);
        } else {
          searchResultsByDay[reservationDateNotFound.value] = [result];
          tempDisplayDates.push(reservationDateNotFound.value);
        }
      }
      resultCounter++;
    }
    displayDates.value = tempDisplayDates.sort();
    for (const dayValue in searchResultsByDay) {
      searchResultsByDay[dayValue].sort((a: any, b: any) => {
        try {
          let tempKey = getItemKeyByTitle(reservationTime);
          let firstPartA = getSurveyValue(a, tempKey).split(":")[0];
          let firstPartB = getSurveyValue(b, tempKey).split(":")[0];
          return parseInt(firstPartA) - parseInt(firstPartB);
        } catch (error) {
          return 0;
        }
      });
    }
    displayResults.value = searchResultsByDay;
  }
};

// computed
const lineUser = computed((): any => {
  return mainStore.lineUser.value;
});
const isShowCloseButton = computed((): boolean => {
  return liff.isInClient();
});
const categoryDisplayLables = computed((): any => {
  return mainStore.categoryLabels;
});
const shouldShowName = computed((): any => {
  return mainStore.patientFamilyName || mainStore.patientFirstName;
});
const getUserId = computed((): any => {
  // userId を取得する
  let authSession = mainStore.authSession;
  const userInfo = authSession.getIdToken().payload;
  const userId = userInfo.identities[0].userId;
  return userId;
});

// hooks

onMounted(() => {
  // スクロール位置が下にある状態で描画される可能性があるため、Scroll位置を0に戻す
  window.scrollTo(0, 0);
});


onBeforeMount(async () => {
  if (!mainStore.authSession || !mainStore.surveyResults.value) {
    router.replace("Confirm");
  }
  loadingResults.value = true;
  //reset some display stuff
  categoryValuesForResults.value = [];
  displayTimesForResults.value = {};
  mainStore.categoryLabels = [];
  try {
    if (Object.keys(mainStore.categoryValues).length == 0) {
      var allCategories = await GetAllCategories();
      for (var calendar of allCategories) {
        mainStore.categoryValues[calendar.id] = {
          tag1: calendar.tag1,
          tag2: calendar.tag2,
          tag3: calendar.tag3
        };
      }
    }
    if (mainStore.categoryLabels.length == 0) {
      var calendarSettings = await GetTagNames();
      if (calendarSettings.tag1) {
        mainStore.categoryLabels.push(calendarSettings.tag1);
      }
      if (calendarSettings.tag2) {
        mainStore.categoryLabels.push(calendarSettings.tag2);
      }
      if (calendarSettings.tag3) {
        mainStore.categoryLabels.push(calendarSettings.tag3);
      }
    }

    //if surveySchema type is corona
    //do special processing here
    if ("surveyType" in mainStore.surveyConfig && mainStore.surveyConfig.surveyType == "corona") {
      surveyIsCorona.value = true;
    }
    await setupResultsForDisplay();
    let chkUid = await checkUserId();
    if (chkUid) {
      await updateUserIdToSurveyResult();
    }
  } catch (err) {
    console.error(err);
    loadingResults.value = false;
  }
  loadingResults.value = false;
});

</script>

<style lang="less">
.form-design-code .CodeMirror {
  height: 80vh !important;
}

.confirmation-table {
  border: 1px solid rgba(0, 0, 0, 0.12);

  .confirm-title {
    background: rgba(0, 0, 0, 0.04);
  }
}

.cancelled-reservation {
  color: darkred;
}
</style>
