<template>
  <q-layout :style="showMonthlySchedule ? 'min-height:100vh;' : ''">
    <div class="q-pa-sm">
      <div dense v-show="loaded" class="full-width row flex-center">
        <div dense style="width: 90%">
          <!-- カレンダー -->
          <div fluid v-if="!LastConfirm && !reservationComplite && showWeeklySchedule">
            <div class="row full-width items-center justify-between q-my-sm">
              <div class="flex items-center col">
                <span class="text-bold">{{ getSurveyTitle }}</span>
              </div>
              <div class="q-mx-sm text-right col-3 col-sm-3">
                <q-btn v-if="showWeeklySchedule" color="primary" outline small
                       @click="onShowMonthlySchedule">
                  <q-icon x-small color="primary" name="mdi-calendar-month-outline"></q-icon>
                  月表示
                </q-btn>
              </div>
            </div>
            <div dense v-show="calendarId" class="row justify-between flex-center" style="background-color:#266BB9;">
              <div class="col" cols="3" sm="3">
                <q-btn elevation="0" flat class="float-left font-weight-bold white--text" color="white"
                       @click="prevWeek">
                  <q-icon small class="mr-1" name="mdi-chevron-left"></q-icon>
                  前へ
                </q-btn>
              </div>
              <div class="col" cols="6" sm="6" align-self="center">
                <div class="text-center">
                  <span v-if="loaded" class="text-white text-weight-bold" style="font-size: 16px;">{{
                      weeklyViewLabel
                    }}</span>
                </div>
              </div>
              <div class="col" cols="3" sm="3">
                <q-btn elevation="0" flat class="float-right font-weight-bold white--text" color="white"
                       @click="nextWeek">
                  次へ
                  <q-icon small class="ml-1" name="mdi-chevron-right"></q-icon>
                </q-btn>
              </div>
            </div>
            <div class="q-pb-md q-mt-none" no-gutter>
              <div class="col-12 col-sm-12">
                <q-card v-show="calendarId">
                  <div class="sticky-table-wrapper">
                    <table>
                      <thead>
                      <tr>
                        <th></th>
                        <th v-for="targetDate in targetDates" :key="targetDate.day"
                            class="text-center data-cell q-pt-sm q-pb-sm">
                          <span class="blue-grey--text">{{ targetDate.youbi }}</span><br/>
                          {{ targetDate.day }}
                        </th>
                      </tr>
                      </thead>
                      <tbody v-if="activeComaCount > 0">
                      <tr v-for="(item, i) in datas" :key="item.timezone.key"
                          v-show="checkComaDisplay(item, activeComaIndex)">
                        <td class="text-center">{{ item.timezone.label }}</td>
                        <td v-show="item.coma[activeComaIndex].front_colspan > 0 && i == item.coma[activeComaIndex].comaStart
                            " :colspan="item.coma[activeComaIndex].front_colspan"
                            :rowspan="item.coma[activeComaIndex].rowspan" class="disableComa data-cell"
                            style="position: relative">
                          <q-btn elevation="2" color="primary" class="comaChangeBtn" x-small height="36" dark
                                 @click="comaChange(-1)">
                            <q-icon small name="mdi-refresh"></q-icon>
                          </q-btn>
                        </td>
                        <td v-for="dayitem in item.data" :key="dayitem.id" class="text-center data-cell q-pt-md q-pb-md"
                            :class="[
                              {
                                active: dayitem.active,
                                inactive: dayitem.value < dayitem.itemCost,
                                selected: selectId == dayitem.id,
                                disableComa: dayitem.comaIndex != activeComaIndex,
                                fullbooked: dayitem.isFullBooked,
                              },
                            ]" v-show="dayitem.comaIndex == activeComaIndex" :keyid="dayitem.id" :quota="dayitem.quota"
                            :reservations="dayitem.reservations" :coma="dayitem.comaIndex" @click="
                              selectData(
                                dayitem.date,
                                item.timezone,
                                dayitem.value,
                                dayitem.id,
                                dayitem.key,
                                dayitem.youbi,
                                dayitem.active
                              )
                              ">
                          <!-- 選択状態 -->
                          <q-icon v-if="selectId == dayitem.id" small color="#06C755" name="mdi-check"></q-icon>
                          <!-- 予約可能 -->
                          <q-icon v-else-if="dayitem.value >= dayitem.itemCost" small
                                  color="#607D8B" name="mdi-circle-outline"></q-icon>
                          <!-- 満席 -->
                          <q-icon v-else-if="dayitem.isFullBooked" small color="#B71C1C" name="mdi-close"></q-icon>
                          <!-- 予約範囲外(公開期間、ワクチン間隔制限) -->
                          <q-icon v-else-if="dayitem.isResrevationImpossible" small
                                  color="#607D8B"><!-- 何も表示しない --></q-icon>
                          <!-- 休日 or 枠なし "dayitem.isDayOff || dayitem.quota == 0" -->
                          <q-icon v-else small color="#607D8B" name="mdi-close"></q-icon>
                        </td>
                        <td v-show="item.coma[activeComaIndex].back_colspan > 0 && i == item.coma[activeComaIndex].comaStart
                            " :colspan="item.coma[activeComaIndex].back_colspan"
                            :rowspan="item.coma[activeComaIndex].rowspan" class="disableComa data-cell"
                            style="position: relative">
                          <q-btn elevation="2" color="primary" class="comaChangeBtn" x-small height="36" dark
                                 @click="comaChange(1)">
                            <q-icon small name="mdi-refresh"></q-icon>
                          </q-btn>
                        </td>
                      </tr>
                      </tbody>
                      <tbody v-if="activeComaCount == 0">
                      <tr>
                        <td colspan="8" class="text-center data-cell noData pt-3 pb-3">
                          <q-icon color="#0453c2" class="mr-1 mb-1" name="mdi-alert"></q-icon>
                          この週は現在予約を受け付けておりません
                        </td>
                      </tr>
                      </tbody>
                    </table>
                  </div>
                </q-card>
                <div v-show="!calendarId" class="disableMessage">
                  <q-icon color="#555" class="mr-1 mb-1" name="mdi-alert"></q-icon>
                  現在予約を受け付けておりません
                </div>
              </div>
            </div>
          </div>
          <!-- 月表示 -->
          <div v-if="showMonthlySchedule && !LastConfirm && !reservationComplite">
            <div class="row justify-between items-center q-my-sm">
              <div class="col">
                <span class="text-bold font-size-14">{{ getSurveyTitle }}</span>
              </div>
              <div class="q-mx-sm text-right col-3 col-sm-3">
                <q-btn elevation="0" color="primary" outline small @click="onShowWeeklySchedule()">
                  <q-icon x-small color="primary" name="mdi-calendar-month-outline"></q-icon>
                  週表示
                </q-btn>
              </div>
            </div>

            <div v-show="calendarId" class="row justify-between items-center" style="background-color:#266BB9;">
              <div class="col-auto">
                <q-btn elevation="0" flat class="font-weight-bold white--text" color="white"
                       style="background-color: #266BB9"
                       @click="prevMonth">
                  <q-icon small class="q-mr-sm" name="mdi-chevron-left"></q-icon>
                  前へ
                </q-btn>
              </div>
              <div class="col-auto">
                <div class="text-center">
                  <span v-if="loaded" class="text-white text-weight-bold" style="font-size: 16px;">{{
                      monthlyViewLabel
                    }}</span>
                </div>
              </div>
              <div class="col-auto">
                <q-btn elevation="0" flat class="font-weight-bold white--text" color="white"
                       style="background-color: #266BB9"
                       @click="nextMonth">
                  次へ
                  <q-icon small class="q-ml-sm" name="mdi-chevron-right"></q-icon>
                </q-btn>
              </div>
            </div>
            <div class="row q-my-xs flex-center">
              <div align-self="center" class="q-py-sm col">
                <div class="text-center">
                  <span class="q-px-sm q-mx-sm q-py-xs day-example"
                        style="border-color:#263238;color:#263238">空きあり</span>
                  <span class="q-px-sm q-py-xs day-example">空きなし</span>
                </div>
              </div>
            </div>
            <div class="q-mb-md q-mt-none" no-gutter>
              <div class="col-12 col-sm-12">
                <q-card elevation="0" outlined v-show="calendarId" class="mx-5">
                  <div class="monthly-view-wrapper q-py-md q-px-sm">
                    <table class="q-mx-auto" style="min-width:100%;">
                      <thead>
                      <tr>
                        <th v-for="targetDate in targetDates" :key="targetDate.day"
                            class="text-center data-cell pt-2 pb-2">
                          <span class="text-blue-grey-5">{{ targetDate.youbi }}</span>
                        </th>
                      </tr>
                      </thead>
                      <tbody>
                      <tr v-for="targetWeek in monthlySchedule" :key="targetWeek.key">
                        <td v-for="targetDate in targetWeek.attr" :key="targetDate.key" class="text-center" :class="[
                            {
                              activeDay: targetDate.active,
                              inactiveDay: !targetDate.active,
                              fullbookedDay: targetDate.isFullBooked,
                              hide: targetDate.hide,
                              onTheDay: onTheDayKey == targetDate.key && !targetDate.hide,
                            },
                          ]" @click="targetDate.hide || !targetDate.active ? '' :
                              onShowWeeklySchedule(
                                targetDate.key
                              )
                              ">
                            <span v-show="!targetDate.hide" class="pa-2">
                              {{ targetDate.day }}
                            </span>
                        </td>
                      </tr>
                      </tbody>
                    </table>
                  </div>
                </q-card>
              </div>
            </div>
          </div>

          <!-- 予約内容確認 -->
          <div fluid v-if="LastConfirm && !reservationComplite" class="full-width flex flex-center column">
            <div class="row q-mb-md full-width bg-white flex-center">
              <div class="text-center q-pa-md col">
                <span class="text-weight-bold font-size-20">予約内容を確認</span>
              </div>
            </div>
            <div class="row q-mb-md full-width q-pb-xl">
              <div class="bg-white full-width col">
                <q-card flat class="q-pa-md">
                  <div fluid>
                    <div class="row">
                      <div class="confirm_card col">
                        <div class="row confirm_setsumei">
                          <div class="col">
                            <p class="text-weight-bold font-size-20">以下の内容で予約を確定します。</p>
                          </div>
                        </div>
                        <div class="row" v-for="reservationData in reservationDatas" :key="reservationData.label">
                          <div class="col" v-if="reservationData.display">
                            <span class="confirm-items">{{ reservationData.label }}</span><br/>
                            <span v-html="reservationData.value"></span>
                          </div>
                        </div>
                        <div class="row" v-if="reservationSettings.itemName">
                          <div class="col">
                            <span class="confirm-items">予約項目</span><br/>
                            <span>{{ reservationSettings.itemName }}</span>
                          </div>
                        </div>
                        <div class="row">
                          <div class="col">
                            <span class="confirm-items">予約日</span><br/>
                            <span>{{ selectDate }}({{ selectYoubi }})</span>
                          </div>
                        </div>
                        <div class="row">
                          <div class="col">
                            <span class="confirm-items">予約時間</span><br/>
                            <span>{{ selectTime }}</span>
                          </div>
                        </div>
                        <div class="row" v-if="paymentOrderInputFromSurvey">
                          <div class="col">
                            <span class="confirm-items">購入商品</span>
                            <div>
                              <SelectedProduct :order="paymentOrderInputFromSurvey"/>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </q-card>
              </div>
            </div>
          </div>

          <!-- 完了メッセージ -->
          <div fluid v-if="reservationComplite" class="flex flex-center full-width column">
            <div class="row flex-center">
              <div class="flex-center q-mb-sm col">
                <span class="text-weight-bold font-size-20">完了</span>
              </div>
            </div>

            <div class="q-my-sm flex flex-center">
              <div class="flex-center col">
                <div v-if="
                  realSurveyConfig &&
                  realSurveyConfig.endOfSurveyMessage &&
                  realSurveyConfig.endOfSurveyMessage.length > 0 &&
                  !(realSurveyConfig.endOfSurveyMessage.length === 1 && !realSurveyConfig.endOfSurveyMessage[0].text) &&
                  showEndOfSurveyMessage
                " style="white-space: pre-line">
                  <div v-if="Array.isArray(realSurveyConfig.endOfSurveyMessage)">
                    <div v-for="(message, index) in realSurveyConfig.endOfSurveyMessage" :key="index">
                      <div v-if="message.type === 'text'">
                        {{ message.text }}
                      </div>
                      <div v-if="message.type === 'image'">
                        <img style="max-width: 100%; max-height: 66vh" :src="message.originalContentUrl" alt="image"/>
                      </div>
                    </div>
                  </div>
                  <div v-else>
                    {{ realSurveyConfig.endOfSurveyMessage }}
                  </div>
                </div>
                <div v-else>
                  <span v-html="compliteMsg"></span>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>

    <!-- フッター -->
    <q-footer app padless class="text-center bg-white" v-show="loaded && !reservationComplite">
      <div fluid>
        <div dense v-if="!LastConfirm" class="row flex-center q-my-sm">
          <div class="col">
            <q-btn color="primary" outline class="font-weight-bold"
                   @click="historyBack">
              戻る
            </q-btn>
            <q-btn color="primary" class="white--text q-ml-md" v-show="calendarId"
                   @click="showLastConfirm" :disabled="!selectId">
              予約内容を確認
            </q-btn>
          </div>
        </div>

        <div class="row flex-center" v-if="LastConfirm">
          <div class="col q-my-sm">
            <q-btn color="primary" height="43" width="140" outlined
                   @click="dataReacquisitionWhenReturningFromLastConfirm">
              戻る
            </q-btn>
            <q-btn color="primary" height="43" width="140" class="q-ml-md white--text"
                   @click="pushSurveyAnswer" :loading="isSendingAnswer">
              {{ pushButtonText }}
            </q-btn>
          </div>
        </div>
      </div>
    </q-footer>

    <!-- 確認ダイアログ -->
    <q-dialog v-model="confirm.show" width="380">
      <q-card>
        <div fluid>
          <div>
            <div class="col-12 col-sm-12" align="center"> 以下の日時でよろしいですか？</div>
          </div>
          <div>
            <div align="center" class="text-weight-bold headline col-12 col-sm-12">
              {{ confirm.time }}
            </div>
          </div>
          <div>
            <div class="col-12 col-sm-12" align="center">
              <q-btn elevation="2" color="#FFC000" height="60" width="160" text-center class="text-weight-bold title"
                     @click="showLastConfirm">
                はい
              </q-btn>
            </div>
          </div>
          <div dense>
            <div align="center" class="mb-3 col-12 col-sm-12">
              <q-btn elevation="2" color="#4472C4" height="60" width="160" class="text-weight-bold title" dark
                     @click="confirm.show = false">
                いいえ
              </q-btn>
            </div>
          </div>
        </div>
      </q-card>
    </q-dialog>

    <!-- アラートメッセージ -->
    <q-dialog v-model="alert.show" width="400">
      <q-card dark class="q-pa-md">
        <div fluid>
          <div class="row flex-center">
            <div class="col-8" align="center">
              <q-icon color="warning" class="mr-1 mb-1" name="mdi-alert"></q-icon>
              <span v-html="alert.message"></span>
            </div>
          </div>
          <div class="row flex-center">
            <div class="col-8 q-mb-md" align="center">
              <q-btn elevation="2" small dark @click="alert.show = false" class="warning">
                <q-icon dark color="white" class="q-mx-sm" name="mdi-close"></q-icon>
                閉じる
              </q-btn>
            </div>
          </div>
        </div>
      </q-card>
    </q-dialog>

    <!-- 処理中表示 -->
    <q-dialog v-model="loading">
      <q-spinner
          color="primary"
          size="50"
          :thickness="5"
          speed="0.6"
      />
    </q-dialog>
    <input type="hidden" id="setting" :reservationPossibleStartDate="reservationSettings.reservationPossibleStartDate"
           :reservationPossibleEndDate="reservationSettings.reservationPossibleEndDate"
           :reservationStart="reservationSettings.reservationStart"
           :reservationPossibleStart="reservationSettings.reservationPossibleStart"
           :reservationPossibleMonths="reservationSettings.reservationPossibleMonths"
           :reservationMaxCount="reservationSettings.reservationMaxCount"
           :reservationMaxCountOfDay="reservationSettings.reservationMaxCountOfDay"
           :controlType="reservationSettings.controlType" :scheduleStartDate="reservationSettings.scheduleStartDate"
           :scheduleEndDate="reservationSettings.scheduleEndDate"/>
  </q-layout>
</template>

<script setup lang="ts">
import {computed, watch, onMounted, onBeforeMount, ref, Ref, ComputedRef} from 'vue';

import {useMainStore} from '@/stores';
import {useSurveyResultsStore} from '@stores/modules/surveyResults';
import {useCalendarsStore} from '@/stores/modules/calendar';
import {usePaymentStore} from '@stores/modules/payment';

const surveyResultsStore = useSurveyResultsStore();
const calendarStore = useCalendarsStore();
const paymentStore = usePaymentStore();

const $q = useQuasar();

// old imports
// 旧インポート
import moment from "moment";
import {cloneDeep, isEmpty} from "lodash";
import {
  GetSchedule,
  PutSurveyResult,
  GetCategoriesTree,
  GetTagNames,
  GetAllCalendars,
  checkReservation,
} from "@/services/calendars.service";
import {SaveTemporarySurveyResults} from '@/services/surveys.service';
import {PostPaymentOrder, PostPaymentOrderNew} from '@/services/paymentResults.service';
import {MULTIPLE_REQUEST_MESSAGE} from "@/utils/errorMessageUtils";
import {getBasedateOfMonthViewFromWeekView} from "@/utils/calendarUtils";
import {createAppendingPayload} from '@/utils/surveyResultsUtils';
import {PAY_TYPES} from '@/utils/paymentResultUtils';
import SelectedProduct from "@/components/SelectedProduct.vue";
import {Auth} from '@/services/auth.service';
import {getApiUrl, getCurrentAppUrl} from "@utils/configuration";
import { useQuasar } from 'quasar';

const TEMPORARY_LOCKED = "temporary_locked";

const mainStore = useMainStore();

// data

const reservationDatas = ref([]); // 予約情報
const surveyId = ref("");
const userId = ref("");
const categoryId = ref("");
const itemId = ref("0");
const startDate: Ref<Date> = ref(null);
const startMonth: Ref<Date> = ref(null);
const weeklyViewLabel = ref('');
const targetDates = ref([]);
const targetMonthForMonthView = ref([]);
const monthlySchedule = ref([]);
const timezones = ref([]);
const datas = ref([]);
const selectId = ref(null);
const selectDate = ref("");
const selectTime = ref("");
const selectYoubi = ref("");
const selectKey = ref("");
const reservationSettings = ref({
  reservationStart: 2, // 当日の何日目以降から予約可能か
  reservationPossibleStart: null,
  reservationPossibleMonths: null,
  reservationMaxCount: null,
  reservationMaxCountOfDay: null,
  reservationPossibleStartDate: null,
  reservationPossibleEndDate: null,
  controlType: 0,
  scheduleStartDate: null,
  scheduleEndDate: null,
  itemName: null,
});
const disabledCheck = ref(true); // 過去日は選択不可
const loading = ref(false);
const confirm = ref({
  show: false,
  time: "",
});
const alert = ref({
  show: false,
  message: "",
});
const LastConfirm = ref(false);
const reservationComplite = ref(false);
const lineUser = ref(null);
const activeComaIndex = ref(0);
const comaCount = ref(0);
const fixedLargeCategoryTitle = ref("");
const fixedMediumCategoryTitle = ref("");
const fixedSmallCategoryTitle = ref("");
const calendarId = ref(null);
const loaded = ref(false);
const compliteMsg = ref("");
const activeComaCount = ref(0);
const hasCountVaccinesItem = ref(false);
const surveyTitle = ref("");
const showWeeklySchedule = ref(false);
const showMonthlySchedule = ref(true);
const isSendingAnswer = ref(false);
const showEndOfSurveyMessage = ref(false);

// methods
const init = async (): Promise<void> => {
  try {
    // console.log("init");
    // 遷移元画面から渡されるデータをセット
    let params = typeof calendarUseParamsJSON.value === 'string' ? JSON.parse(calendarUseParamsJSON.value) : calendarUseParamsJSON.value;
    //console.log("params", params);
    for (let key in params) {
      //console.log("key", key);
      if (key === "surveyId") {
        surveyId.value = params[key];
        //console.log("surveyId", surveyId.value);
      } else if (key === "userId") {
        userId.value = params[key];
        //console.log("userId", userId.value);
      } else {
        if (Array.isArray(params[key].value)) {
          let setValue = "<ul>";
          let multipleItems = [];
          for (let i = 0; i < params[key].value.length; i++) {
            setValue += "<li>" + params[key].value[i] + "</li>";
            multipleItems.push(params[key].value[i]);
          }
          setValue += "</ul>";
          setReservateionData(key, params[key].title, setValue, true, params[key].type, multipleItems);
        } else {
          //console.log("params[key] (NON-ARR)", params[key]);
          let isDisplay = true;
          let setValue = params[key].value;
          if (params[key].type == "reservation") {
            let categoryData = setValue.split("_");
            categoryId.value = categoryData[0];
            if (categoryData.length > 1) {
              itemId.value = categoryData[1];
            } else {
              itemId.value = "0";
            }
            isDisplay = false;
            //console.log("params[key]", params[key]);
            fixedLargeCategoryTitle.value = params[key].fixedLargeCategoryTitle
              ? params[key].fixedLargeCategoryTitle
              : "";
            fixedMediumCategoryTitle.value = params[key].fixedMediumCategoryTitle
              ? params[key].fixedMediumCategoryTitle
              : "";
            fixedSmallCategoryTitle.value = params[key].fixedSmallCategoryTitle
              ? params[key].fixedSmallCategoryTitle
              : "";
          }
          setReservateionData(key, params[key].title, setValue, isDisplay, params[key].type);
        }
      }
    }

    reservationSettings.value.itemName = surveyResultsStore.reservationItemName;
    surveyTitle.value = mainStore.realSurveyConfig ? mainStore.realSurveyConfig.surveyTitle : "";

    await GetCategoryDetail();
    setHasCountVaccinesItem();
  } catch (err) {
    console.error(err);
    historyBack();
  }
};
const GetCategoryDetail = async (): Promise<void> => {
  try {
    const tagNames = await GetTagNames();
    const categoryTree = await GetCategoriesTree();
    let calendarIdLocal: any = null;

    //console.log("categoryTree", categoryTree);
    //console.log("tagNames", tagNames);


    categoryTree.forEach((tag1_data) => {
      const tag1_value = tag1_data.name;
      let tag2_value = "";
      let tag3_value = "";

      if (categoryId.value === tag1_data.id) {
        const largeCategoryTitle = fixedLargeCategoryTitle.value || tagNames.tag1;
        setReservateionData("", largeCategoryTitle, tag1_value, true, "category");
        return true;
      }


      if (tag1_data.children.length > 0) {
        tag1_data.children.forEach((tag2_data) => {
          //console.log("tag2_data", tag2_data);
          tag2_value = tag2_data.name;

          if (categoryId.value === tag2_data.id) {
            const largeCategoryTitle = fixedLargeCategoryTitle.value || tagNames.tag1;
            const mediumCategoryTitle = fixedMediumCategoryTitle.value || tagNames.tag2;

            setReservateionData("", largeCategoryTitle, tag1_value, true, "category");
            setReservateionData("", mediumCategoryTitle, tag2_value, true, "category");
            calendarIdLocal = tag2_data.calendarId;
            return true;
          }

          //console.log("categoryId", calendarIdLocal);

          if (tag2_data.children.length > 0) {
            tag2_data.children.forEach((tag3_data) => {
              tag3_value = tag3_data.name;

              if (categoryId.value === tag3_data.id) {
                const largeCategoryTitle = fixedLargeCategoryTitle.value || tagNames.tag1 || '大項目';
                const mediumCategoryTitle = fixedMediumCategoryTitle.value || tagNames.tag2 || '中項目';
                const smallCategoryTitle = fixedSmallCategoryTitle.value || tagNames.tag3 || '小項目';

                setReservateionData("", largeCategoryTitle, tag1_value, true, "category");
                setReservateionData("", mediumCategoryTitle, tag2_value, true, "category");
                setReservateionData("", smallCategoryTitle, tag3_value, true, "category");
                calendarIdLocal = tag3_data.calendarId;
                return true;
              }
            });
          }
        });
      }
    });

    calendarId.value = calendarIdLocal;

    // カレンダー情報取得（予約可能範囲など）
    const result = await getCalendarInfo(null);

    if (result) {
      await GetScheduleData();
      await setMonthlySchedule();
      await getUserInfo();
    }
  } catch (err) {
    console.error(err);
    alert.value.message = err.message;
    alert.value.show = true;
  }
};

const getCalendarInfo = async (startDate: string | number | Date): Promise<boolean> => {
  const session = await Auth.currentSession().catch((e) => {
    return false;
  });
  let calendars = await GetAllCalendars(session);
  let selectedCalendar = calendars.find((obj: { sortKey: any; }) => obj.sortKey == calendarId.value);

  if (selectedCalendar && selectedCalendar !== undefined) {
    reservationSettings.value.reservationStart =
        "reservationStart" in selectedCalendar ? selectedCalendar.reservationStart : 0;
    reservationSettings.value.reservationPossibleStart =
        "reservationPossibleStart" in selectedCalendar ? selectedCalendar.reservationPossibleStart : null;
    reservationSettings.value.reservationPossibleMonths =
        "reservationPossibleMonths" in selectedCalendar ? selectedCalendar.reservationPossibleMonths : null;
    reservationSettings.value.reservationMaxCount =
        "reservationMaxCount" in selectedCalendar ? selectedCalendar.reservationMaxCount : null;
    reservationSettings.value.reservationMaxCountOfDay =
        "reservationMaxCountOfDay" in selectedCalendar ? selectedCalendar.reservationMaxCountOfDay : null;
    reservationSettings.value.controlType =
        "reservationControlType" in selectedCalendar ? selectedCalendar.reservationControlType : null;
    reservationSettings.value.scheduleStartDate =
        "startDate" in selectedCalendar ? selectedCalendar.startDate : null;
    reservationSettings.value.scheduleEndDate = "endDate" in selectedCalendar ? selectedCalendar.endDate : null;

    if (reservationSettings.value.controlType === 0) {
      // 予約可能日設定：全期間
      reservationSettings.value.reservationPossibleStartDate = null;
      reservationSettings.value.reservationPossibleEndDate = null;
    } else if (reservationSettings.value.controlType === 1 || reservationSettings.value.controlType === 2) {
      // 予約可能日設定：詳細設定
      // 予約可能開始日(reservationPossibleStart)が設定されている場合、営業日を絡めて開始日を算出
      if (
          reservationSettings.value.reservationPossibleStart ||
          reservationSettings.value.reservationPossibleStart === 0
      ) {
        const from = moment().add(1, "days").format("YYYYMMDD");
        const addDays = 14 + reservationSettings.value.reservationPossibleStart;
        const to = moment().add(addDays, "days").format("YYYYMMDD");
        let _scheduleData = await GetSchedule(categoryId.value, from, to);
        let dayOffInfo = _scheduleData["dayOff"];
        if (dayOffInfo) {
          if (Object.keys(dayOffInfo).length === 0) {
            dayOffInfo = {};
            for (let key in _scheduleData["schedule"]) {
              dayOffInfo[key] = 0;
            }
          } else {
            for (let key in _scheduleData["schedule"]) {
              if (!(key in dayOffInfo)) {
                dayOffInfo[key] = 0;
              }
            }
          }
        }

        let setDate = null;
        const calcDays = reservationSettings.value.reservationPossibleStart;
        const dayOffCount = getDayOffCount(dayOffInfo, calcDays);
        const addedCalcDate = moment().add(calcDays, "days");
        for (let k in dayOffInfo) {
          // dayOffInfo[今日の日付 + calcDays] === 0(営業日の場合)：setDate = 今日の日付 + caldDays
          // dayOffInfo[今日の日付 + calcDays] === 1(休日の場合)：addedCalcDateの翌日で再度算出
          const copyiedDate = addedCalcDate.clone().format("YYYYMMDD");
          const isDayOff = dayOffInfo[copyiedDate] === 1;

          if (!isDayOff) {
            // 営業日の場合
            setDate = addedCalcDate.add(dayOffCount, "days").format("YYYY/MM/DD 00:00:00");
            break;
          } else {
            // 休日の場合
            addedCalcDate.add(1, "days");
          }
        }

        if (setDate) {
          // 日付の算出ができている場合
          if (reservationSettings.value.scheduleStartDate) {
            // 予約制御が設定されている場合は日付を比較する
            reservationSettings.value.reservationPossibleStartDate = compartionPossibleStartDate(setDate);
          } else {
            // 予約制御が設定されていない場合は比較せずそのままセット
            reservationSettings.value.reservationPossibleStartDate = new Date(setDate);
          }
        } else {
          // 日付の算出ができていない場合
          let wkDate = new Date();
          wkDate.setDate(wkDate.getDate() + reservationSettings.value.reservationPossibleStart);
          if (reservationSettings.value.scheduleStartDate) {
            // 予約制御が設定されている場合は日付を比較する
            wkDate = compartionPossibleStartDate(wkDate);
          }
          reservationSettings.value.reservationPossibleStartDate = wkDate;
        }
      } else if (reservationSettings.value.scheduleStartDate) {
        // reservationPossibleStartが未設定・カスタム設定のみの場合
        // LINEのブラウザだと「yyyy-mm-dd」を日付に変換できない模様、yyyy/mm/ddに変換
        const scheduleStartDate = new Date(
            `${reservationSettings.value.scheduleStartDate.replace(/-/g, "/")} 00:00:00`
        );
        const scheduleStartDateIsPast = checkIsPastDate(scheduleStartDate);
        // scheduleStartDate.valueが過去日の場合は今日の日付から予約可能とする
        reservationSettings.value.reservationPossibleStartDate = scheduleStartDateIsPast
            ? new Date()
            : scheduleStartDate;
      }

      // scheduleEndDateが設定されている場合はscheduleEndDateを参照
      if (reservationSettings.value.scheduleEndDate) {
        // LINEのブラウザだと「yyyy-mm-dd」を日付に変換できない模様、yyyy/mm/ddに変換
        reservationSettings.value.reservationPossibleEndDate = new Date(
            `${reservationSettings.value.scheduleEndDate.replace(/-/g, "/")} 23:59:59`
        );
      } else if (
          reservationSettings.value.reservationPossibleMonths ||
          reservationSettings.value.reservationPossibleMonths === 0
      ) {
        // 予約可能制御日が未設定・かつ予約可能終了日(reservationPossibleMonths)が設定されている場合の終了日を算出
        // 設定値が0の場合当月末まで、2の場合2か月後の月末まで
        let nowDate = new Date();
        nowDate.setMonth(nowDate.getMonth() + (reservationSettings.value.reservationPossibleMonths + 1));
        let wkDate = new Date(`${nowDate.getFullYear()}/${("00" + (nowDate.getMonth() + 1)).slice(-2)}/01`);
        wkDate.setDate(wkDate.getDate() - 1);
        reservationSettings.value.reservationPossibleEndDate = wkDate;
      }
    }

    let setStartDate = startDate ? new Date(startDate) : null;
    setTargetDates(setStartDate);
    setTargetMonthForMonthView(setStartDate);

    return true;
  } else {
    loaded.value = true;
    return false;
  }
};

const compartionPossibleStartDate = (date: string | number | Date): Date => {
  const setDate = new Date(date);
  const scheduleStartDate = new Date(reservationSettings.value.scheduleStartDate);

  if (setDate > scheduleStartDate) {
    return setDate;
  } else {
    return scheduleStartDate;
  }
};
const checkIsPastDate = (targetDate: any): boolean => {
  const now = new Date();
  const isPast = targetDate < now;
  return isPast;
};
const getDayOffCount = (dayOffInfo: { [x: string]: number; }, addDate: moment.DurationInputArg1): any => {
  let dayOffCount = 0;
  const to = moment().add(addDate, "days").format("YYYYMMDD");
  for (let k in dayOffInfo) {
    const isDayOff = dayOffInfo[k] === 1;
    if (k < to && isDayOff) {
      dayOffCount++;
    }
  }
  return dayOffCount;
};
const setReservateionData = (p_itemKey: any, p_label: any, p_value: any, p_isDisplay: any, p_type?: any, p_multiple_items?: any): void => {
  //console.log("label", p_label);
  reservationDatas.value.push({
    itemKey: p_itemKey,
    label: p_label,
    value: p_value,
    display: p_isDisplay,
    type: p_type,
    multipleValue: p_multiple_items,
  });
};
const setMonthlySchedule = async (): Promise<void> => {
  try {
    const targetMonthDates = targetMonthForMonthView.value;
    let datas = [];
    let dateId = 0;
    loading.value = true;

    for await (let week of targetMonthDates) {
      const startDateKeyOfWeek = week.weekDates[0].key;
      const startDateOfWeekStr = moment(startDateKeyOfWeek).format('YYYYMMDD');
      const endDateOfWeekStr = moment(startDateKeyOfWeek).add(6, 'd').format('YYYYMMDD');

      // ■ スケジュールを取得（GetSchedule）
      let _scheduleData = await GetSchedule(categoryId.value, startDateOfWeekStr, endDateOfWeekStr);
      if (!_scheduleData) {
        _scheduleData = {};
      }
      if (!_scheduleData["schedule"]) {
        _scheduleData["schedule"] = {};
      }
      if (!_scheduleData["comaList"]) {
        _scheduleData["comaList"] = {};
      }

      let slotDataInWeek = [];
      for await (let targetDate of week.weekDates) {
        let emptySlot = 0;
        let totalQuota = 0;
        let totalReservations = 0;
        let isDayOff = 0;
        let comaCnt = 0;

        if (targetDate.disabled && disabledCheck.value) {
          emptySlot = -1;
        } else {
          if (_scheduleData["schedule"][targetDate.key]) {
            totalQuota = Object.keys(_scheduleData["schedule"][targetDate.key])
                .reduce((sum, key) =>
                    Number(sum) + Number(_scheduleData["schedule"][targetDate.key][key][0]), 0);
            totalReservations = Object.keys(_scheduleData["schedule"][targetDate.key])
                .reduce((sum, key) =>
                    Number(sum) + Number(_scheduleData["schedule"][targetDate.key][key][1]), 0);
            emptySlot = totalQuota - totalReservations;
          }
          if (_scheduleData["dayOff"]) {
            if (_scheduleData["dayOff"][targetDate.key]) {
              isDayOff = _scheduleData["dayOff"][targetDate.key];
            }
          }
          if (_scheduleData["comaList"]) {
            if (_scheduleData["comaList"][targetDate.key]) {
              comaCnt = Object.keys(_scheduleData["comaList"][targetDate.key]).length;
            }
          }
        }
        dateId += 1;
        slotDataInWeek.push({
          id: dateId,
          key: targetDate.key,
          date: targetDate.date,
          year: targetDate.year,
          youbi: targetDate.youbi,
          day: targetDate.day,
          hide: targetDate.hide,
          value: isDayOff == 0 ? emptySlot : 0,
          comaCnt: comaCnt,
          active: emptySlot >= reservationItemCost.value && isDayOff == 0,
          isFullBooked: emptySlot < reservationItemCost.value
              && isDayOff == 0
              && totalReservations > 0,
          existsInCalendar: _scheduleData["schedule"][targetDate.key] !== undefined
        });
      }
      datas.push({
        attr: slotDataInWeek,
        key: `${slotDataInWeek[0].key}-${slotDataInWeek[5].key}`,
      });
    }

    monthlySchedule.value = datas;
    loaded.value = true;
    loading.value = false;
  } catch (err) {
    loading.value = false;
    loaded.value = true;
    alert.value.message = err;
    alert.value.show = true;
  }
};
const GetScheduleData = async (forceStoreSelectedId = false): Promise<void> => {
  try {
    let datasLocal = [];
    let wkId = 1;
    loading.value = true;
    let from = `${startDate.value.getFullYear()}${("00" + (startDate.value.getMonth() + 1)).slice(-2)}${(
        "00" + startDate.value.getDate()
    ).slice(-2)}`;
    let wkDate = new Date(
        `${startDate.value.getFullYear()}/${("00" + (startDate.value.getMonth() + 1)).slice(-2)}/${(
            "00" + startDate.value.getDate()
        ).slice(-2)}`
    );
    let wkTo = new Date(wkDate.setDate(wkDate.getDate() + 6));
    let to = `${wkTo.getFullYear()}${("00" + (wkTo.getMonth() + 1)).slice(-2)}${("00" + wkTo.getDate()).slice(-2)}`;
    const authSession = await Auth.currentSession().catch((e) => {
      return null;
    });
    // ■ スケジュールを取得（GetSchedule）
    let _scheduleData = await GetSchedule(categoryId.value, from, to);

    activeComaIndex.value = 0;

    if (_scheduleData) {
      if (!_scheduleData["schedule"]) {
        _scheduleData["schedule"] = {};
      }
      if (!_scheduleData["comaList"]) {
        _scheduleData["comaList"] = {};
      }
    } else {
      _scheduleData = {};
      _scheduleData["schedule"] = {};
      _scheduleData["comaList"] = {};
    }

    let scheduleComaType = [];
    let comaCheck = "";
    let comaIndex = 0;
    for (let key in _scheduleData["schedule"]) {
      let comaDetail = "";
      let firstComaId = null;
      for (let comaID in _scheduleData["schedule"][key]) {
        comaDetail += (comaDetail == "" ? "" : ",") + comaID;
        if (!firstComaId) {
          firstComaId = comaID;
        }
      }
      if (comaCheck !== comaDetail) {
        if (comaCheck !== "" && comaDetail != "") {
          comaIndex += 1;
        }
        comaCheck = comaDetail;
      }
      let firstComaIndex = 0;
      for (let key2 in _scheduleData["comaList"]) {
        if (firstComaId == key2) {
          break;
        }
        firstComaIndex += 1;
      }
      scheduleComaType["dt_" + key] = {
        ids: comaDetail.split(","),
        index: comaIndex,
        firstComaIndex: firstComaIndex,
      };
    }
    comaCount.value = comaIndex;

    let wkComaData = [];
    for (let coma in scheduleComaType) {
      if (wkComaData[scheduleComaType[coma]["index"]]) {
        wkComaData[scheduleComaType[coma]["index"]]["colspan"] += 1;
      } else {
        wkComaData[scheduleComaType[coma]["index"]] = {
          colspan: 1,
          rowspan: scheduleComaType[coma]["ids"].length,
          firstComaIndex: scheduleComaType[coma]["firstComaIndex"],
        };
      }
    }

    let comaData = [];
    for (let coma in wkComaData) {
      comaData[coma] = {
        front_colspan: 0,
        back_colspan: 0,
        rowspan: wkComaData[coma]["rowspan"],
        comaStart: 0,
      };
      for (let coma2 in wkComaData) {
        if (coma < coma2) {
          comaData[coma]["back_colspan"] += wkComaData[coma2]["colspan"];
        }
        if (coma > coma2) {
          comaData[coma]["front_colspan"] += wkComaData[coma2]["colspan"];
        }
      }
      comaData[coma]["comaStart"] = wkComaData[coma]["firstComaIndex"];
    }

    if (comaData.length === 0) {
      comaData[0] = {
        front_colspan: 0,
        back_colspan: 0,
        rowspan: 0,
        comaStart: 0,
      };
    }

    let comaCountLocal = 0;
    let timezone = [];
    let comaItemIndex = [];
    comaItemIndex[0] = 0;
    for (let key in _scheduleData["comaList"]) {
      let targetComa = [];
      for (let key2 in scheduleComaType) {
        if (scheduleComaType[key2]["ids"].includes(key)) {
          targetComa.push(scheduleComaType[key2]["index"]);
          comaItemIndex[scheduleComaType[key2]["index"]] = 0;
        }
      }
      let setComaIndex = targetComa.filter(function (x, i, self) {
        return self.indexOf(x) === i;
      });
      comaCountLocal += setComaIndex.length;
      timezone.push({
        key: key,
        label: _scheduleData["comaList"][key]["start"] +
            (_scheduleData["comaList"][key]["end"].length > 0 ? " ～ " + _scheduleData["comaList"][key]["end"] : ""),
        comaIndex: setComaIndex,
      });
    }
    activeComaCount.value = comaCountLocal;
    timezones.value = timezone;
    if (!forceStoreSelectedId) {
      selectId.value = null;
    }
    let dataCnt = 0;
    timezones.value.forEach(function (timezone: { [x: string]: string | number; }, index: any) {
      let wkData = [];
      let wkComaIndex = 0;
      targetDates.value.forEach(function (targetDate: {
        disabled: any;
        key: string;
        date: any;
        youbi: any;
      }, index2: any) {
        let availableSlotCount = 0;
        let quota = 0;
        let reservations = 0;
        let isDayOff = 0;
        if (targetDate.disabled && disabledCheck.value) {
          availableSlotCount = -1;
          wkComaIndex = scheduleComaType["dt_" + targetDate.key]["index"];
          comaItemIndex[wkComaIndex] += 1;
        } else {
          if (_scheduleData["schedule"][targetDate.key]) {
            wkComaIndex = scheduleComaType["dt_" + targetDate.key]["index"];
            comaItemIndex[wkComaIndex] += 1;
            if (_scheduleData["schedule"][targetDate.key][timezone["key"]]) {
              quota = Number(_scheduleData["schedule"][targetDate.key][timezone["key"]][0]);
              reservations = Number(_scheduleData["schedule"][targetDate.key][timezone["key"]][1]);
              availableSlotCount = quota - reservations;
              dataCnt += 1;
            }
          }
          if (_scheduleData["dayOff"]) {
            if (_scheduleData["dayOff"][targetDate.key]) {
              isDayOff = _scheduleData["dayOff"][targetDate.key];
            }
          }
        }
        wkId += 1;
        wkData.push({
          id: wkId,
          date: targetDate.date,
          value: isDayOff == 0 ? availableSlotCount : 0,
          active: availableSlotCount >= reservationItemCost.value && isDayOff == 0,
          quota: quota,
          reservations: reservations,
          key: targetDate.key,
          youbi: targetDate.youbi,
          comaIndex: wkComaIndex,
          isDayOff: isDayOff,
          isFullBooked: availableSlotCount < reservationItemCost.value && isDayOff == 0 && reservations > 0,
          itemCost: reservationItemCost.value,
          existsInCalendar: _scheduleData["schedule"][targetDate.key] !== undefined,
          isResrevationImpossible: targetDate.disabled
        });
      });

      datasLocal.push({
        timezone: timezone,
        data: wkData,
        coma: comaData,
      });
    });
    datas.value = datasLocal;
    loaded.value = true;
    loading.value = false;
  } catch (err) {
    loading.value = false;
    loaded.value = true;
    alert.value.message = err;
    alert.value.show = true;
  }
};
const getWeeklyViewLabelFromShownDate = (mondayOfWeek: Date): string => {
  const monday = moment(mondayOfWeek);
  const sunday = moment(mondayOfWeek).add({days: 6});
  if (monday.month() == sunday.month()) {
    return monday.format('YYYY年M月');
  } else {
    return `${monday.format('YYYY年M月')}-${sunday.format('M月')}`;
  }
};
const setTargetDates = (p_date: any): void => {
  let nowDate = new Date();
  if (reservationSettings.value.reservationPossibleStartDate) {
    nowDate = reservationSettings.value.reservationPossibleStartDate;
  }
  let wkDate = p_date ? p_date : nowDate;
  let this_year = wkDate.getFullYear();
  let this_month = wkDate.getMonth();
  let date = wkDate.getDate();
  let day_num = wkDate.getDay();
  let this_monday = date - (day_num > 0 ? day_num : 7) + 1;
  let targetDate = new Date(this_year, this_month, this_monday);
  startDate.value = targetDate;

  let dates = [];

  let WeekChars = ["日", "月", "火", "水", "木", "金", "土"];
  let endDate = reservationSettings.value.reservationPossibleEndDate
      ? reservationSettings.value.reservationPossibleEndDate
      : new Date("2099/12/31");
  nowDate = new Date(`${nowDate.getFullYear()}/${nowDate.getMonth() + 1}/${nowDate.getDate()} 00:00:00`);

  weeklyViewLabel.value = getWeeklyViewLabelFromShownDate(targetDate);

  for (let i = 0; i < 7; i++) {
    targetDate = new Date(targetDate.setDate(targetDate.getDate() + (i === 0 ? 0 : 1)));

    let isResrevationImpossible = targetDate < nowDate || targetDate > endDate;
    // If you can reservation in "予約可能期間設定",
    // Check if reservations are possible by referring to the vaccination interval
    if (!isResrevationImpossible && !hasCountVaccinesItem.value) {
      isResrevationImpossible = checkIsReservationImpossibleByFirstVaccination(targetDate);
    }
    if (!isResrevationImpossible && hasCountVaccinesItem.value) {
      isResrevationImpossible = checkIsReservationImpossibleByPreviousVaccination(targetDate)
    }

    dates.push({
      day: targetDate.getDate(),
      date: `${targetDate.getMonth() + 1}月${targetDate.getDate()}日`,
      key: `${targetDate.getFullYear()}${("00" + (targetDate.getMonth() + 1)).slice(-2)}${(
          "00" + targetDate.getDate()
      ).slice(-2)}`,
      youbi: WeekChars[targetDate.getDay()],
      saturday: targetDate.getDay() === 6,
      sunday: targetDate.getDay() === 0,
      disabled: isResrevationImpossible,
    });
  }

  targetDates.value = dates;
};
const setTargetMonthForMonthView = (selectedDate: Date): void => {
  let nowDate = moment().startOf('day');
  if (reservationSettings.value.reservationPossibleStartDate) {
    nowDate = moment(reservationSettings.value.reservationPossibleStartDate).startOf('day');
  }
  const basisDate = selectedDate ? moment(selectedDate) : nowDate;
  const startDateOfMonth = moment(basisDate).startOf('month');
  const endDateOfMonth = moment(basisDate).endOf('month');
  const thisMonth = startDateOfMonth.month();
  let targetDate = moment(startDateOfMonth).startOf('isoWeek');
  startMonth.value = startDateOfMonth.toDate();

  let monthDates = [];
  let dates = [];
  const WeekChars = ["日", "月", "火", "水", "木", "金", "土"];
  const endDate = reservationSettings.value.reservationPossibleEndDate
      ? moment(reservationSettings.value.reservationPossibleEndDate)
      : moment("2099/12/31");

  let breakFlg = false;
  for (let j = 0; j < 6; j++) {
    dates = [];
    for (let i = 0; i < 7; i++) {
      let isResrevationImpossible = targetDate.isBefore(nowDate.toDate()) || targetDate.isAfter(endDate.toDate());
      const shouldHide = targetDate.month() !== thisMonth;
      // If you can reservation in "予約可能期間設定",
      // Check if reservations are possible by referring to the vaccination interval
      if (!isResrevationImpossible && !hasCountVaccinesItem.value) {
        isResrevationImpossible = checkIsReservationImpossibleByFirstVaccination(targetDate.toDate());
      }
      if (!isResrevationImpossible && hasCountVaccinesItem.value) {
        isResrevationImpossible = checkIsReservationImpossibleByPreviousVaccination(targetDate.toDate());
      }

      breakFlg = targetDate.isAfter(endDateOfMonth) || targetDate.isSame(endDateOfMonth, 'day');
      dates.push({
        day: targetDate.date(),
        date: targetDate.format('M月D日'),
        key: targetDate.format('YYYYMMDD'),
        youbi: WeekChars[targetDate.day()],
        year: targetDate.year(),
        saturday: targetDate.day() === 6,
        sunday: targetDate.day() === 0,
        disabled: isResrevationImpossible,
        hide: shouldHide,
      });
      targetDate = targetDate.add(1, 'd');
    }

    monthDates.push({
      weekDates: dates,
      key: `${dates[0].key}-${dates[6].key}`,
      startDateOfMonth,
    });
    // 最後の日が当月末日より小さかったらもう一回繰り返す
    if (breakFlg) {
      break;
    }
  }
  targetMonthForMonthView.value = monthDates;
};
const checkIsReservationImpossibleByFirstVaccination = (targetDate: Date): boolean => {
  if (surveyResultsStore.coronaVaccinationData?.current_time === 'second_time') {
    const {
      allowed_started_date: allowedStartedDate,
      allowed_after_days: allowedAfterDays,
    } = surveyResultsStore.coronaVaccinationData.second_time;

    if (!allowedStartedDate.date) {
      const allowedStartedDateString = moment().add(allowedAfterDays, 'days').format('YYYY-MM-DD');
      allowedStartedDate.string = allowedStartedDateString;
      allowedStartedDate.moment = moment(allowedStartedDateString);
      allowedStartedDate.date = new Date(allowedStartedDateString);
    }

    // Reservation is not possible if it is before the required interval date from the first vaccination date
    return moment(targetDate).isBefore(allowedStartedDate.string);
  }
  return false;
};
const checkIsReservationImpossibleByPreviousVaccination = (targetDate: Date): boolean => {
  if (surveyResultsStore.vaccinationAllowedStartDate) {
    const _targetDate = moment(targetDate);
    return _targetDate.isBefore(surveyResultsStore.vaccinationAllowedStartDate);
  }
  return false;
};
const selectData = (p_date: any, p_time: {
  label: any;
  key: any;
}, p_value: number, p_id: any, p_key: any, p_youbi: any, p_active: any): void => {
  if (p_value > 0 && p_active) {
    if (selectId.value !== p_id) {
      selectDate.value = p_date;
      selectTime.value = p_time.label;
      selectYoubi.value = p_youbi;
      confirm.value.time = `${p_date}(${p_youbi}) ${p_time.label}`;
      selectId.value = p_id;
      selectKey.value = `${p_key}|${p_time.key}`;
    }
  }
};
const prevWeek = async (): Promise<void> => {
  let wkDate = startDate.value;
  setTargetDates(new Date(wkDate.setDate(wkDate.getDate() - 7)));
  await GetScheduleData();
};
const nextWeek = async (): Promise<void> => {
  let wkDate = startDate.value;
  setTargetDates(new Date(wkDate.setDate(wkDate.getDate() + 7)));
  await GetScheduleData();
};
const prevMonth = async (): Promise<void> => {
  let wkDate = startMonth.value;
  setTargetMonthForMonthView(new Date(wkDate.setDate(wkDate.getDate() - 1)));
  await setMonthlySchedule();
};
const nextMonth = async (): Promise<void> => {
  let wkDate = startMonth.value;
  setTargetMonthForMonthView(new Date(wkDate.setDate(wkDate.getDate() + 31)));
  await setMonthlySchedule();
};
const historyBack = (): void => {
  history.back();
};
const getUserInfo = async (): Promise<void> => {
  const session = await Auth.currentSession().catch((e) => {
    return null;
  });
  const userInfo = session.getIdToken().payload;
  lineUser.value = userInfo;
};
const showLastConfirm = (): void => {
  confirm.value.show = false;
  LastConfirm.value = true;
  (window as any).scrollY = 0;
};
const onShowWeeklySchedule = (selectedKey?: string, forceStoreSelectedId = false): void => {
  // selectedKeyがないとき（週表示ボタンで切替時）は、月表示Viewされている月の初日をstartDate.valueにする

  let startDateKey = selectedKey || startDate.value;
  if (!startDateKey && targetMonthForMonthView.value[0]) {
    targetMonthForMonthView.value[0].startDateOfMonth.format('YYYYMMDD');
  }

  // startDateKeyがないときは、日付選択がないため何もしない。（ただ、通知は出す）
  if (!startDateKey) {
    console.error('Nor selectedKey, nor targetMonthForMonthView.value[0] is existing, do nothing')
    $q.notify({message: '週表示をするには日付選択が必要です。', color: 'info'});
    return;
  }

  startDate.value = moment(startDateKey).toDate();
  setTargetDates(startDate.value);
  GetScheduleData(forceStoreSelectedId);
  showMonthlySchedule.value = false;
  showWeeklySchedule.value = true;
};
const onShowMonthlySchedule = (): void => {
  const startDateOfMonthView = startMonth.value.getMonth() !== startDate.value.getMonth()
      ? getBasedateOfMonthViewFromWeekView(startDate.value)
      : startMonth.value;
  setTargetMonthForMonthView(startDateOfMonthView);
  setMonthlySchedule();
  showWeeklySchedule.value = false;
  showMonthlySchedule.value = true;
};
const pushSurveyAnswer = async (): Promise<boolean> => {
  isSendingAnswer.value = true;
  try {
    if (mainStore.reservationComplite || sessionStorage.getItem("reservationComplite.value") === "complite") {
      compliteMsg.value =
          "この予約はすでに送信されています。<br>変更されたい場合は、<br>ブラウザを閉じてから<br>もう一度やり直してください<br>";
      LastConfirm.value = false;
      reservationComplite.value = true;
      return false;
    }

    let items = [];
    let reservationItemKey = null;
    reservationDatas.value.forEach(function (r_data: {
      value: any;
      itemKey: any;
      type: string;
      multipleValue: string | any[];
    }, index: any) {
      let setValue = r_data.value;
      if (setValue && r_data.itemKey) {
        if (r_data.type == "reservation") {
          setValue += `|${selectKey.value}`;
          reservationItemKey = r_data.itemKey;
        }
        if (!r_data.multipleValue) {
          items.push({
            itemKey: r_data.itemKey,
            value: setValue,
          });
        } else {
          for (let i = 0; i < r_data.multipleValue.length; i++) {
            items.push({
              itemKey: r_data.itemKey,
              value: r_data.multipleValue[i],
            });
          }
        }
      }
    });
    let PutSurveyResultParams: any = {
      surveyId: surveyId.value,
      userId: userId.value,
      items: items,
    };

    // ユーザーの予約状況チェック
    const from = reservationSettings.value.reservationPossibleStartDate;
    let to = reservationSettings.value.reservationPossibleEndDate;
    const maxCount = reservationSettings.value.reservationMaxCount
        ? reservationSettings.value.reservationMaxCount
        : 0;
    const maxCountOfDay = reservationSettings.value.reservationMaxCountOfDay
        ? reservationSettings.value.reservationMaxCountOfDay
        : 0;
    const controlType = reservationSettings.value.controlType ? reservationSettings.value.controlType : 0;
    const targetDay = selectKey.value.split("|");
    let chkFrom = null;
    let chkTo = null;
    if (controlType !== 0 && (from || from === 0)) {
      chkFrom = moment(from).format('YYYYMMDD');
      if (!to) {
        to = new Date("2099/12/31");
      }
      chkTo = moment(to).format('YYYYMMDD');
    }
    let checkParams = {
      surveyId: surveyId.value,
      itemKey: reservationItemKey,
      userId: userId.value,
      from: chkFrom,
      to: chkTo,
      targetDay: targetDay[0],
      maxCount: maxCount,
      maxCountOfDay: maxCountOfDay,
      dayCheckOnly: controlType === 0,
      partitionKey: (mainStore.selectedSurveyResults
          && mainStore.selectedSurveyResults.partitionKey)
          ? mainStore.selectedSurveyResults.partitionKey
          : null,
    };
    let _reservationCheck = await checkReservation(checkParams);
    if (_reservationCheck.result != "OK") {
      alert.value.message =
          `${_reservationCheck.errorMessage}<br />（${_reservationCheck.checkDetail}）`
      alert.value.show = true;
      return false;
    }

    const session = await Auth.currentSession().catch((e) => {
      return null;
    });
    if (mainStore.selectedSurveyResults && mainStore.selectedSurveyResults.partitionKey) {
      PutSurveyResultParams.partitionKey = cloneDeep(mainStore.selectedSurveyResults.partitionKey);
    }
    let updateSurveyResultsCheckParams = null;
    if (mainStore.inEditingAppendingMode) {
      //Editing an appending mode survey's results
      //Set the check value to 取り消し for the old results
      updateSurveyResultsCheckParams = {
        surveyId: surveyId.value,
        items: mainStore.selectedSurveyResults.data,
        checkStatus: "取り消し",
      };
    }

    let _pushRes: any = {};
    // NOTE: 決済帳票で現金支払もしくは購入金額が0円の場合は決済をせずに帳票結果・決済結果を作成
    if (
        !doesUsePayment.value ||
        (
            doesUsePayment.value &&
            (paymentOrderInputFromSurvey.value?.selectedPayType === PAY_TYPES.CASH || paymentOrderInputFromSurvey.value?.amount === 0)
        )
    ) {
      // NOTE: 一度決済結果が作成されている場合は決済結果データをリクエストに含めない
      const paymentData = !isEmpty(paymentResult.value) ? null : paymentOrderInputFromSurvey.value;
      _pushRes = await PutSurveyResult(PutSurveyResultParams, updateSurveyResultsCheckParams, paymentData);
    } else {
      const result = createAppendingPayload(PutSurveyResultParams);
      const response = await SaveTemporarySurveyResults({
        data: result,
        order: paymentOrderInputFromSurvey.value,
        callbackApiBaseUrl: getApiUrl(),
        liffBaseUrl: getCurrentAppUrl(),
      });
      if (response.code !== 'success') {
        throw response.errorMessage;
      }
      await PostPaymentOrderNew(response.form);
      return;
    }

    if (_pushRes.result === "OK") {
      // 予約完了、以降登録処理は行わせない
      mainStore.reservationComplite = true;
      sessionStorage.setItem("reservationComplite.value", "complite");
      compliteMsg.value =
          "ブラウザを閉じてください。<br><br>変更処理には時間がかかることがあります。ブラウザバックを行うと処理が正常に終了しない可能性がありますので、必ず一度ブラウザを閉じてください。<br>";

      LastConfirm.value = false;
      reservationComplite.value = true;
      showEndOfSurveyMessage.value = true;
    } else {
      let msg =
          japaneseMessageConvert(_pushRes.errorMessage) || "不明なエラーです。管理者にお問い合わせください。";
      if (_pushRes.type === "quota_exceeded") {
        msg = japaneseMessageConvert(_pushRes.type);
      } else if (_pushRes.response) {
        if (
            _pushRes.response.status == 409 &&
            _pushRes.response.data &&
            _pushRes.response.data.code &&
            _pushRes.response.data.code === TEMPORARY_LOCKED
        ) {
          msg = MULTIPLE_REQUEST_MESSAGE;
        }
      }

      alert.value.message = msg;
      alert.value.show = true;
    }
  } catch (error) {
    alert.value.message = error;
    alert.value.show = true;
  } finally {
    isSendingAnswer.value = false;
  }
};
const japaneseMessageConvert = (msg: string | number): any => {
  let japaneseMessagesList = {
    "no survey config.":
        "●タイムアウトしました。\n●何度もこの表示が出る場合は、本帳票が無効になっている可能性があります。お手数ですが管理者にお問い合わせください。",
    "Error: mainStore.request failed with status code 404":
        "●タイムアウトしました。\n●何度もこの表示が出る場合は、本帳票が無効になっている可能性があります。お手数ですが管理者にお問い合わせください。",
    quota_exceeded: "指定された日付・時間の枠は埋まっています。お手数ですが、他の日付・時間を選んでください。",
    "Error: mainStore.network Error": "ネットワークエラーが発生しました。お手数ですが、もう一度再度やり直してください。",
  };
  let result = japaneseMessagesList[msg] || msg;
  return result;
};
const comaChange = (p_val: any): void => {
  let setIndex = (activeComaIndex.value += p_val);
  if (setIndex > comaCount.value) {
    setIndex = 0;
  }
  if (setIndex < 0) {
    setIndex = comaCount.value - 1;
  }
  activeComaIndex.value = setIndex;
};
const checkComaDisplay = (item: {
  timezone: { comaIndex: string | number | any[]; };
}, activeComaIndex: any): boolean => {
  let ret = false;
  if (item.timezone) {
    if (item.timezone.comaIndex || item.timezone.comaIndex === 0) {
      ret = (item as any).timezone.comaIndex.includes(activeComaIndex);
    }
  }
  return ret;
};
const reloadCalendar = async (): Promise<void> => {
  // カレンダー情報取得（予約可能範囲など）
  await getCalendarInfo(startDate.value);
  GetScheduleData();
  setMonthlySchedule();
};
const setHasCountVaccinesItem = (): void => {
  // コロナワクチン接種（N回目接種）の固有アイテムを持っているかチェック
  const surveyConfig = mainStore.realSurveyConfig;
  if (!surveyConfig?.surveySchema) {
    return;
  }
  const countVaccinesItem = surveyConfig.surveySchema.find((item) => item.type === 'countVaccines');
  hasCountVaccinesItem.value = !!countVaccinesItem;
};
const dataReacquisitionWhenReturningFromLastConfirm = (): void => {
  // 「予約内容を確認」画面から戻ったときは、選択中のアイテム表示を維持する
  const forceStoreSelectedId = true;
  onShowWeeklySchedule(selectKey.value.substring(0, 8), forceStoreSelectedId);
  LastConfirm.value = false;
};

// computed
const calendarUseParamsJSON: ComputedRef<object | string> = computed(() => surveyResultsStore.calendarUseParamsJSON);
const realSurveyConfig = computed(() => mainStore.realSurveyConfig);
const paymentOrderInputFromSurvey = computed(() => paymentStore.paymentOrderInputFromSurvey);
const paymentResult = computed(() => paymentStore.paymentResult);
const reservationItemCost = computed(() => surveyResultsStore.reservationItemCost);
const getSurveyTitle = computed((): string => {
  return surveyTitle.value ? surveyTitle.value : "";
});
const monthlyViewLabel = computed((): string => {
  return (targetMonthForMonthView.value && targetMonthForMonthView.value[0]) ? targetMonthForMonthView.value[0].startDateOfMonth.format('YYYY年M月') : "";
});
const onTheDayKey = computed((): string => {
  return moment().format('YYYYMMDD');
});
const doesUsePayment = computed((): boolean => {
  return realSurveyConfig?.value.usePayment === 1
      && paymentOrderInputFromSurvey.value
      && !('status' in paymentOrderInputFromSurvey.value);
});
const pushButtonText = computed((): string => {
  return doesUsePayment.value ? '決済画面へ' : '予約する';
});

// watch
watch(() => reservationComplite.value, (state) => {
  // スクロール位置が下にある状態で完了メッセージ画面が表示される可能性があるため、送信処理が実行されたらScroll位置を0に戻す
  if (state) {
    window.scrollTo(0, 0);
  }
});

// hooks

onMounted(() => {
  // スクロール位置が下にある状態で描画される可能性があるため、Scroll位置を0に戻す
  window.scrollTo(0, 0);
  // NOTE: 念の為、paymentResultとpaymentOrderInputFromSurveyが両方も設定されないようにリセット
  paymentStore.setPaymentResult(undefined);
});


onBeforeMount(async () => {
  loading.value = true;
  await init();
  loading.value = false;
});

</script>

<style lang="less" scoped>
.sticky-table-wrapper {
  max-height: 70vh;
  overflow-y: scroll;
}

.sticky-table-wrapper table {
  width: 100%;
  border-spacing: 0;
}

.sticky-table-wrapper table > thead > tr > th {
  position: sticky;
  top: 0;
  z-index: 2;
  user-select: none;
  font-size: .75rem;
  height: 48px;
}

.sticky-table-wrapper table > thead > tr:nth-child(2) > th {
  top: 48px;
}

td,
th {
  background-color: #fff;
  white-space: nowrap;
}

.sticky-table-wrapper th:first-child {
  border-right: solid 2px #CFD8DC;
}

.sticky-table-wrapper td {
  border-bottom: thin solid #CFD8DC !important;
  font-size: .875rem;
  height: 48px;
}

.sticky-table-wrapper th {
  border-bottom: solid 2px #CFD8DC !important;
  font-size: .875rem;
  height: 48px;
}

th.data-cell {
  border-left: solid 1px #CFD8DC !important;
  border-color: #CFD8DC !important;
  padding: 0px !important;
}

th.saturday {
  background-color: #99f3ff !important;
  color: #1c64ff !important;
}

th.sunday {
  background-color: #ffb199 !important;
  color: #ce1c1d !important;
}

td.data-cell {
  border-left: solid 1px #CFD8DC !important;
  background-color: #fff;
  border-color: #CFD8DC !important;
  padding: 0px !important;
  min-width: 22px !important;
  max-width: 60px !important;
}

.sticky-table-wrapper > td:first-child {
  border-left: solid 2px #CFD8DC !important;
}

.sticky-table-wrapper tr td:first-child {
  border-color: #CFD8DC !important;
  border-right: solid 2px #CFD8DC !important;
  text-align: center;
  padding: 4px !important;
  max-width: 135px !important;
  position: sticky !important;
  position: -webkit-sticky !important;
  left: 0;
  z-index: 1;
}

tr th:first-child {
  max-width: 135px !important;
  position: sticky !important;
  position: -webkit-sticky !important;
  left: 0 !important;
  z-index: 5 !important;
}

.month-cell {
  border-bottom: solid 1px #e5e5e5;
}

.active {
  font-size: 15px !important;
  cursor: pointer;
}

.inactive {
  background-color: #F5F7F8 !important;
  cursor: default;
}

.selected {
  background-color: #E9FAED !important;
  font-size: 15px !important;
}

.fullbooked {
  background-color: #FFEBEE !important;
}

.disableComa {
  background-color: lightgray !important;
}

.hiddenComaRow {
  display: none;
}

.comaChangeBtn {
  position: absolute;
  top: calc(50% - 18px);
  left: calc(50% - 18px);
}

.confirm_table td:first-child {
  text-align: right !important;
  vertical-align: middle !important;
  background-color: #fff !important;
  color: #000 !important;
  font-weight: normal !important;
  padding-right: 5px;
  max-width: 140px;
  white-space: normal !important;
}

.confirm_table td {
  text-align: left !important;
  vertical-align: middle !important;
  background-color: #fff !important;
  color: #000 !important;
  font-weight: normal !important;
  white-space: normal !important;
}

.confirm_setsumei {
  text-align: left;
}

.disableMessage {
  width: 100%;
  text-align: center;
  padding-top: 68%;
  color: #555;
}

.confirm-items {
  color: #607D8B !important;
}

.noData {
  font-weight: normal !important;
  height: 120px !important;
}

.font-size-20 {
  font-size: 20px;
}

.font-size-10 {
  font-size: 10px;
}

.font-size-14 {
  font-size: 14px;
}

.monthly-view-wrapper {
  width: 100%;
  border-spacing: 0;
}

.monthly-view-wrapper td {
  border: none !important;
  font-size: 1rem;
  height: 48px;
}

.monthly-view-wrapper th {
  border: none !important;
  font-size: 1rem;
  height: 36px;
  width: 40px;
}

.monthly-wrapper table {
  border-spacing: 0;
}

.activeDay {
  cursor: pointer;
}

.inactiveDay {
  color: #B0BEC5 !important;
  cursor: default;
}

.hide {
  cursor: default !important;
}

.day-example {
  font-size: 12px;
  color: #607D8B;
  background-color: white;
  border: solid thin #607D8B;
  border-radius: 16px;
}

.onTheDay {
  position: relative;
}

.onTheDay:before {
  content: '';
  border: thin solid #06C755;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
}

.v-footer {
  background-color: white !important;
}
</style>
