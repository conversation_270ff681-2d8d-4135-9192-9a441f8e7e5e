<template>
  <div class="q-pa-md">
    <q-dialog 
      v-if="loadingResults" 
      :opacity="0.2"
    >
      <content-loading 
        :size="50" 
        text="" 
      />
    </q-dialog>
    <div>
      <div class="row flex justify-center">
        <div class="col-12 col-sm-10 col-md-6 col-xl-4 col-mx-auto">
          <q-card 
            shaped 
            class="mx-auto"
          >
            <q-list>
              <q-item>
                <q-item-section avatar>
                  <q-avatar v-if="lineUser && lineUser.picture" >
                    <q-img :src="lineUser.picture" />
                  </q-avatar>
                  <q-avatar 
                    v-else 
                    color="primary"
                  >
                    <q-icon 
                      dark 
                      name="mdi-account-circle" 
                    />
                  </q-avatar>
                </q-item-section>

                <q-item-section>
                  <q-item-label>
                    <span v-if="lineUser">{{ lineUser.name }}</span> 様
                  </q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-card>
          <div class="row q-pt-md">
            <div 
              align="center" 
              class="confirm-list-title-row col-12" 
            >
              <span class="bold-text">{{ listTitle }}</span><br>
              <span class="bold-text">{{ surveyConfig.surveyTitle }}</span>
            </div>
          </div>
          <br>
          <div v-for="result in displayDates" :key="result">
            <span v-if="hasCategory()" class="bold-text">{{ getFrontEndDisplay(result) }}</span>
            <q-card 
              v-for="surveyResult in getValuesFromDisplayResult(result)"
              :key="surveyResult.partitionKey"
              class="mb-8 appointment-card" 
            >
              <div v-for="item in surveyResult.data" :key="item.sortKey">
                <div v-if="
                  getItemType(item.itemKey) !== 'reservation' &&
                  getItemType(item.itemKey) !== 'linkbutton' &&
                  getItemType(item.itemKey) !== 'files' &&
                  !isAdminItem(item.itemKey) && item.type !== 'none'
                ">
                  {{ getItemTitle(item.itemKey) }}：{{ item.value }}
                </div>
                <div v-else-if="getItemType(item.itemKey) === 'files'">
                  {{ getItemTitle(item.itemKey) }}：
                  <q-list v-if="fileData?.length > 0" bordered dense>
                    <q-item v-for="file in fileData" :key="file.name" class="q-py-none">
                      <q-item-section class="q-pt-none">
                        <a class="text-blue" style="text-decoration:underline;" :href="file.url" download target="_blank">{{ file.name }}</a>
                      </q-item-section>
                    </q-item>
                  </q-list>
                  <span v-else>ーー</span>
                </div>
              </div>
              <div v-if="hasCategory()">
                予約時間：
                <span :class="{ 'cancelled-reservation': cancelledReservationCheck(surveyResult.partitionKey) }">
                  {{ getTimeDisplay(surveyResult.partitionKey) }}
                </span>
                <div 
                  v-for="(category, categoryIndex) in categoryDisplayLabels" 
                  :key="categoryIndex"
                >
                  {{ category }}：{{ getTagValueFromIndex(surveyResult.partitionKey, String(categoryIndex + 1)) }}
                </div>
                <div v-if="reservationItemName[surveyResult.partitionKey]">
                  予約項目：{{ reservationItemName[surveyResult.partitionKey] }}
                </div>
              </div>
              <div v-if="doesUsePayment">
                購入商品:
                <SelectedProduct :order="getPaymentResultByKey(surveyResult.partitionKey)" />
              </div>
              <div v-if="cancelLimitList[surveyResult.partitionKey]">
                <br>
                <p>予約変更・キャンセルの期限を過ぎています。<br>予約窓口へ直接ご連絡をお願い致します。</p>
              </div>
              <div v-else-if="isCanceledPaymentStatus(surveyResult.partitionKey)">
                <br>
                <p>キャンセル済みの為変更はできません。<br>管理者へ直接ご連絡をお願い致します。</p>
              </div>
              <div v-else-if="isOutOfCancelablePeriod(surveyResult.partitionKey)">
                <br>
                <p>キャンセルの期限を過ぎています。<br>管理者へ直接ご連絡をお願い致します。</p>
              </div>
              <div v-else-if="doesUsePayment && isEditableResults(surveyResult.partitionKey)">
                <br>
                <p>商品明細以外の情報は変更ができます。<br>商品の変更や購入数の変更は、キャンセル後に再度購入手続きをしてください。</p>
              </div>
              <q-card-actions 
                v-if="!isEditDisabled() && !cancelLimitList[surveyResult.partitionKey]" 
                class="mt-5"
              >
                <q-space />
                <q-btn
                  v-if="isEditableResults(surveyResult.partitionKey)" 
                  color="primary"
                  @click="changeConfirm(surveyResult)"
                >
                  {{ textEditButton }}
                </q-btn>
                <q-btn 
                  v-if="isShowCancelButton(surveyResult.partitionKey)" 
                  color="negative"
                  @click="cancelConfirm(surveyResult)"
                >
                  キャンセル
                </q-btn>
                <q-space />
              </q-card-actions>
            </q-card>
          </div>

          <BackButtonMessage />

          <div class="row">
            <div 
              align="center" 
              class="col" 
            >
              <q-btn 
                dark 
                block 
                color="primary"
                class="confirm-list-button" 
                @click="returnToSearchForm"
              > 
                戻る
              </q-btn>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, onBeforeMount } from 'vue';

import { useRoute, useRouter } from 'vue-router';

import { useMainStore } from '@/stores';
import { useSurveyResultsStore } from '@/stores/modules/surveyResults';
import { usePaymentStore } from '@/stores/modules/payment';

const mainStore = useMainStore();
const surveyResultsStore = useSurveyResultsStore();
const paymentStore = usePaymentStore();

// old imports
// 旧インポート
import { cloneDeep } from "lodash";
import moment from "moment";
import { UpdateUserIdToSurveyResultByPks } from "@/services/surveys.service";
import {
  GetCalendarInfo,
  GetAllCategories,
  GetTagNames,
  getReservationItemInfo,
  GetAllCalendars,
  GetSchedule,
} from "@/services/calendars.service";
import { GetPaymentResults } from '@/services/paymentResults.service';
import {
  canEditPaymentStatus,
  getCancelablePaymentResults,
  isRegistedFromAdmin,
  isCanceledStatus,
  isCancelablePeriod,
  isFinishedPaymentStatus,
} from '@/utils/paymentResultUtils';
import BackButtonMessage from "@/components/BackButtonMessage.vue";
import SelectedProduct from "@/components/SelectedProduct.vue";
import { GetUploadFile } from '@/services/member.service';
/* import {
  CANCEL_SURVEY_RESULTS,
  RESET_CALENDAR_USE_PARAMS_JSON,
} from "@/store/action-types";
import {
  SET_PAYMENT_ORDER_INPUT_FROM_SURVEY,
} from "@/store/mutation-types"; */

const router = useRouter();
const route = useRoute();

// data
const displayResults = ref<any>(null);
const displayDates = ref<any>(null);
const surveyResults = ref<any>(null);
const loadingResults = ref<boolean>(false);
const surveyIsCorona = ref<boolean>(false);
const categoryValuesForResults = ref<any>([]);
const displayTimesForResults = ref<any>({});
const displayCategoriesForResults = ref<any>({});
const cancelledText = ref<string>("キャンセル済");
const coronaManagementDate = ref<string>("接種実施日");
const reservationTime = ref<string>("予約時間");
const reservationDate = ref<string>("予約日");
const reservationDateNotFound = ref<string>("予約日付不明");
const partitionKey = ref<string>("予約日付不明");
const completedSurveyList = ref<any>({});
const notCancelSurveyList = ref<any>({});
const calendars = ref<any>([]);
const cancelLimitList = ref<any>({});
const reservationItemName = ref<any>({});
const thisTimeVaccineDateTitle = ref<string>("今回の接種実施日");
const searchedPaymentResults = ref<any>([]);
const listPartitionKey = ref<any>([]);
const cancelablePaymentResultsKeys = ref<any>([]);
const fileData = ref();

// methods
const cancelSurveyResult = surveyResultsStore.cancelSurveyResults;
const resetCalendarUseParams = surveyResultsStore.resetCalendarUseParamsJson;
const setPaymentOrderInput = paymentStore.setPaymentOrderInputFromSurvey;
const hasCategory = (): boolean => {
  return mainStore.surveyConfig.surveySchema.find((x: { type: string; }) => x.type === "reservation") !== undefined;
};
const isEditDisabled = (): boolean => {
  return (
    mainStore.surveyConfig.isEditDisabled && mainStore.surveyConfig.isEditDisabled.value === true
  );
};
const returnToSearchForm = (): void => {
  router.replace({ name: "SurveyConfirm" });
};
const getTagValueFromIndex = (surveyIndex: string, tagIndex: string): any => {
  return surveyIndex in displayCategoriesForResults.value
    ? displayCategoriesForResults.value[surveyIndex]["tag" + tagIndex]
    : "不明";
};
const getDisplayString = (surveyResult: any, titleToSearch: string): string => {
  let itemKeyToSearch = null;
  for (let i = 0; i < mainStore.surveyConfig.surveySchema.length; i++) {
    const item = mainStore.surveyConfig.surveySchema[i];
    if (titleToSearch == item.title) {
      itemKeyToSearch = item.itemKey;
      break;
    }
  }

  return titleToSearch + "：" + getSurveyValue(surveyResult, itemKeyToSearch);
};
const cancelledReservationCheck = (index: string): boolean => {
  return index in displayTimesForResults.value ? displayTimesForResults.value[index] == cancelledText.value : false;
};
const getTimeDisplay = (index: string): any => {
  return index in displayTimesForResults.value ? displayTimesForResults.value[index] : "";
};
const getReservationTimeDisplay = async (categoryData: string, surveyResultIndex: string | number, surveyResultItem: { check: string; }): Promise<void> => {
  //get the coma from categories,
  //fetch the value of coma for that category
  let displayResult = "";
  try {
    if ("check" in surveyResultItem && surveyResultItem.check == "キャンセル") {
      displayResult = cancelledText.value;
    } else {
      const parsedCategoryData = categoryData.split("|");
      const categoryId = parsedCategoryData[0].split("_")[0];
      const comaId = parsedCategoryData[2];
      let reservationTime = null;
      if (Object.keys(mainStore.calendarSettings).includes(categoryId)) {
        reservationTime.value = mainStore.calendarSettings[categoryId].comaList[comaId].start;
        if (reservationTime.value) {
          displayResult = reservationTime.value;
        }
        //can get from state
      } else {
        //fetch from backend
        const calendarInfo = await GetCalendarInfo(categoryId);
        mainStore.calendarSettings[categoryId] = calendarInfo;
        reservationTime.value = calendarInfo.comaList[comaId].start;
        if (reservationTime.value) {
          displayResult = reservationTime.value;
        }
      }
    }
  } catch (error) {
    //invalid category format or coma value
    console.error("Error occured trying to find coma");
  }

  displayTimesForResults.value[surveyResultIndex] = displayResult;
};
const getCategoryValuesDisplay = async (categoryData: string, surveyResultIndex: string | number): Promise<void> => {
  //categoryValues
  let displayResult = [];
  try {
    const parsedCategoryData = categoryData.split("|");
    const categoryId = parsedCategoryData[0].split("_")[0];

    if (Object.keys(mainStore.categoryValues).includes(categoryId)) {
      displayResult = mainStore.categoryValues[categoryId];
    }
  } catch (err) {
    displayResult = [];
  }

  displayCategoriesForResults.value[surveyResultIndex] = displayResult;
};
const getItemKeyByTitle = (titleToSearch: any): any => {
  const sc = mainStore.surveyConfig;
  if (!sc) return "";

  for (let i = 0; i < sc.surveySchema.length; i++) {
    const item = sc.surveySchema[i];
    if (titleToSearch === item.title) {
      return item.itemKey;
    }
  }
  return "";
};
const getItemTitle = (itemKey: any): any => {
  const sc = mainStore.surveyConfigAllItem;
  if (!sc) return "";

  for (let i = 0; i < sc.surveySchema.length; i++) {
    const item = sc.surveySchema[i];
    if (itemKey === item.itemKey) {
      return item.title;
    }
  }
  return "";
};
const getFixedCategoryTitle = (itemKey: any): any => {
  let result = {
    fixedLargeCategoryTitle: "",
    fixedMediumCategoryTitle: "",
    fixedSmallCategoryTitle: "",
  };
  let _item: { setLargeCategoryTitle: string; setMediumCategoryTitle: string; setSmallCategoryTitle: string; };

  const sc = mainStore.surveyConfig;
  if (!sc) return result;

  for (let i = 0; i < sc.surveySchema.length; i++) {
    const item = sc.surveySchema[i];
    if (itemKey === item.itemKey) {
      _item = item;
      break;
    }
  }
  if (!_item) return result;

  if (_item.setLargeCategoryTitle) {
    result.fixedLargeCategoryTitle = _item.setLargeCategoryTitle;
  }
  if (_item.setMediumCategoryTitle) {
    result.fixedMediumCategoryTitle = _item.setMediumCategoryTitle;
  }
  if (_item.setLargeCategoryTitle) {
    result.fixedSmallCategoryTitle = _item.setSmallCategoryTitle;
  }

  return result;
};
const getItemType = (itemKey: any): any => {
  const sc = mainStore.surveyConfigAllItem;
  if (!sc) return "";

  for (let i = 0; i < sc.surveySchema.length; i++) {
    const item = sc.surveySchema[i];
    if (itemKey === item.itemKey) {
      return item.type;
    }
  }
  return "";
};
const isAdminItem = (itemKey: any): boolean => {
  const sc = mainStore.surveyConfig;
  if (!sc) return false;

  for (let i = 0; i < sc.surveySchema.length; i++) {
    const item = sc.surveySchema[i];
    if (itemKey === item.itemKey && item.isAdminItem) {
      return true;
    }
  }
  return false;
};
const getSurveyValue = (surveyResult: { data: string | any[]; }, itemKey: any): any => {
  for (let i = 0; i < surveyResult.data.length; i++) {
    const item = surveyResult.data[i];
    if (itemKey === item.itemKey) {
      return item.value;
    }
  }
  return "";
};
const getFrontEndDisplay = (date: moment.MomentInput): string => {
  if (date === null || date === undefined) {
    return "予約なし";
  } else {
    moment.locale("ja");
    const momentDate = moment(date);
    if ((momentDate as any)._isValid) {
      return momentDate.format("M月D日 (ddd)");
    } else {
      return "予約なし";
    }
  }
};
const viewSurveyDetails = (surveyResult: any): void => {
  mainStore.selectedSurveyResults = surveyResult;
  router.push({ name: "SurveyConfirmDetails" });
};
const cancelConfirm = (surveyResult: { partitionKey: string | number; }): void => {
  const partitionKey = surveyResult.partitionKey;
  mainStore.selectedSurveyResults = surveyResult;
  mainStore.selectedValuesForCancel = mainStore.surveyResultsDisplayValues[partitionKey];
  if (reservationItemName.value[partitionKey]) {
    mainStore.selectedValuesForCancel.push({
      name: "予約項目",
      value: reservationItemName.value[partitionKey],
    });
  }
  const paymentResult = getPaymentResultByKey(partitionKey as string);
  if (paymentResult) {
    mainStore.selectedValuesForCancel.push({ name: '購入商品', value: paymentResult });
  }
  router.push({ name: "CancelSurveyConfirm" });
};
const changeConfirm = (surveyResult: { partitionKey: any; data: string | any[]; }): void => {
  // re-convert checkboxes items
  for (let j = 0; j < surveyResult.data.length; j++) {
    const _item = surveyResult.data[j];
    if (
      _item.type === 'checkboxes' && _item.value !== _item.sortKey.replace(`{_item.itemKey}#`, '')
    ) {
      surveyResult.data[j].value = surveyResult.data[j].sortKey.replace(`{_item.itemKey}#`, '');
    }
  }
  mainStore.selectedSurveyResults = surveyResult;
  mainStore.inEditingAppendingMode = true;
  router.push({
    name: "LiffSurvey",
    params: { id: mainStore.surveyConfig.surveyId },
    query: { partitionKey: surveyResult.partitionKey },
  });
};
const convertCheckboxes = (result: any): void => {
  // merge checkboxes items for display
  if (result.data && result.data.length > 0) {
    let firstItemCheckbox = [];
    for (let j = 0; j < result.data.length; j++) {
      const _item = result.data[j];
      result.data[j].type = getItemType(_item.itemKey);
      if (
        _item.type === 'checkboxes' &&
        !firstItemCheckbox.some((item) => item.itemKey === _item.itemKey)
      ) {
        firstItemCheckbox.push({
          itemKey: _item.itemKey,
          index: j,
        });
      }
      const findItemCheckbox = firstItemCheckbox.find(
        (item) => item.itemKey === _item.itemKey
      );
      if (
        firstItemCheckbox.some((item) => item.itemKey === _item.itemKey) &&
        findItemCheckbox.index !== j
      ) {
        result.data[findItemCheckbox.index].value = result.data[
          findItemCheckbox.index
        ].value.concat(',', result.data[j].value);
        result.data[j].type = 'none';
        continue;
      }
    }
  }
  return result;
};
const getValuesFromDisplayResult = (dateValue: string): any => {
  if (displayResults.value) {
    return dateValue in displayResults.value
      ? displayResults.value[dateValue]
      : displayResults.value[reservationDateNotFound.value];
  } else return "";
};
const cancelReservationAutomatically = async (surveyResults: { partitionKey: string | number; data: { partitionKey: any; }[]; }): Promise<any> => {
  notCancelSurveyList.value[surveyResults.partitionKey] = true;

  let config_data = mainStore.surveyConfig;

  let _pushRes = await cancelSurveyResult({
    surveyId: config_data.surveyId,
    partitionKey: surveyResults.data[0].partitionKey.value,
    userId: getUserId.value,
  });

  return _pushRes;
};
const hasBunruiConfig = (schema: any): any => {
  let _hasBunruiConfig = null;
  for (var item of schema) {
    if (item.type === "reservation") {
      _hasBunruiConfig = item.itemKey;
    }
  }
  return _hasBunruiConfig;
};
const hasBunruiResults = (results: any, key: any): boolean => {
  let _hasBunruiResults = false;
  for (var item of results) {
    if (item.itemKey === key) {
      _hasBunruiResults = true;
    }
  }
  return _hasBunruiResults;
};
// eslint-disable-next-line max-statements
const setupResultsForDisplay = async (): Promise<void> => {
  // line-smart-cityではsurveyConfigAllItemから取得してるので、修正した
  // surveyConfigAllItemではないとpartitionKeyが遷移されない
  // let hasBunruiConfigVal = await hasBunruiConfig(mainStore.surveyConfig.surveySchema);
  let hasBunruiConfigVal = await hasBunruiConfig(mainStore.surveyConfigAllItem.surveySchema);
  surveyResults.value = mainStore.surveyResults;
  let searchResultsByDay = [];
  let tempDisplayDates = [];
  let cancelLimit = 1;

  if (surveyResults.value) {
    let resultCounter = 0;
    for (const result of surveyResults.value) {
      convertCheckboxes(result);
      let tempList = removePreviousVaccineAnswer(result.data);
      let tempDate = null;
      let categoryId = null;
      completedSurveyList.value[result.partitionKey.value] = true;
      notCancelSurveyList.value[result.partitionKey.value] = true;
      cancelLimitList.value[result.partitionKey.value] = false;

      if (!hasBunruiConfigVal) {
        if (tempList[0].check !== "完了") {
          completedSurveyList.value[result.partitionKey.value] = false;
          if (tempList[0].check !== "キャンセル") {
            notCancelSurveyList.value[result.partitionKey.value] = false;
          }
        }
      } else {
        let hasBunruiResultsVal = await hasBunruiResults(tempList, hasBunruiConfig);
        if (!hasBunruiResultsVal) {
          if (tempList[0].check !== "完了") {
            completedSurveyList.value[result.partitionKey.value] = false;
            if (tempList[0].check !== "キャンセル") {
              notCancelSurveyList.value[result.partitionKey.value] = false;
            }
          }
        } else {
          for (const item of tempList) {
            if (getItemType(item.itemKey) === "reservation") {
              let fixedCategoryTitle = getFixedCategoryTitle(item.itemKey);

              if (fixedCategoryTitle.fixedLargeCategoryTitle) {
                mainStore.categoryLabels[0] = fixedCategoryTitle.fixedLargeCategoryTitle;
              }
              if (fixedCategoryTitle.fixedMediumCategoryTitle) {
                mainStore.categoryLabels[1] = fixedCategoryTitle.fixedMediumCategoryTitle;
              }
              if (fixedCategoryTitle.fixedSmallCategoryTitle) {
                mainStore.categoryLabels[2] = fixedCategoryTitle.fixedSmallCategoryTitle;
              }

              try {
                tempDate = item.value.split("|")[1];
                categoryId = item.value.split("|")[0].split("_")[0];
              } catch (error) {
                //invalid category item
                console.error("Invalid category item");
              }

              //Try to search survey config for a reservation/分類 type
              //item.value should look like category#068536|20210220|2
              categoryValuesForResults.value.push(item.value);
              await getReservationTimeDisplay(item.value, result.partitionKey, item);
              await getCategoryValuesDisplay(item.value, result.partitionKey);

              const _categoryId = item.value.split("|")[0].split("_")[0];
              if (Object.keys(mainStore.calendarSettings).includes(_categoryId)) {
                let selectedCalendar = calendars.value.find(
                  (obj: { sortKey: any; }) => obj.sortKey == mainStore.calendarSettings[_categoryId].sortKey
                );
                if (selectedCalendar && selectedCalendar !== undefined) {
                  cancelLimit =
                    "reservationCancelLimit" in selectedCalendar ? selectedCalendar.reservationCancelLimit : 1;
                }
              }

              // 予約項目を取得
              let itemInfo = await getReservationItemInfo(item.value.split("|")[0]);
              if (itemInfo.length > 0) {
                reservationItemName.value[result.partitionKey] = itemInfo[0].itemName;
              }

              if (surveyIsCorona.value) {
                //check that the 接種実施日 value is empty and that
                //the time on the clock is tempDate and getReservationTimeDisplay
                //if so, clear the reservation value from the db and display
                const hasCountVaccinesItemVal = hasCountVaccinesItem();
                const titleToSearch = hasCountVaccinesItemVal
                  ? thisTimeVaccineDateTitle.value
                  : coronaManagementDate.value;
                const vaccinationDate = getSurveyValue(
                  result,
                  getItemKeyByTitle(titleToSearch)
                );
                if (!vaccinationDate) {
                  // 一回目の接種完了してない
                  completedSurveyList.value[result.partitionKey.value] = false;
                  if (item.check !== "キャンセル") {
                    notCancelSurveyList.value[result.partitionKey.value] = false;
                  }
                  // コロナワクチン接種（N回目接種）の固有アイテムを持っていない場合は自動キャンセルする
                  if (!hasCountVaccinesItem) {
                    //If reservation date is today, it's still available (not canceled until tomorrow)
                    const tempDateClone = cloneDeep(tempDate);
                    const reservationExpireDate = moment(tempDateClone, "YYYYMMDD").add(1, "day");

                    const currentTime = moment();
                    if (currentTime.isAfter(reservationExpireDate)) {
                      if (item.check !== "キャンセル") {
                        await cancelReservationAutomatically(result);
                        await getReservationTimeDisplay(item.value, result.partitionKey.value, item);
                      }
                    }
                  }
                }
              } else {
                if (item.check !== "完了") {
                  completedSurveyList.value[result.partitionKey.value] = false;
                  if (item.check !== "キャンセル") {
                    notCancelSurveyList.value[result.partitionKey.value] = false;
                  }
                }
              }
            }
          }
        }
      }
      let displayValues = [];
      try {
        // キャンセル変更期限設定が0の場合はチェック無し
        // 設定値の-1日の日から変更不可（1⇒当日、2⇒前日、3⇒2日前・・・）
        // 休日は営業日としてカウントしない
        if (cancelLimit !== 0 && tempDate != null && categoryId) {
          // 休日数を取得
          const fromDate = moment().format('YYYYMMDD');
          const dayOffCount = await getDayOffCount(categoryId, fromDate, tempDate)
            .then((res: any) => { return res })
            .catch((e: any) => {
              console.error(e);
              return 0;
            });
          // キャンセル・変更可能日の算出
          const calcDays = cancelLimit + dayOffCount - 1;
          let reservationDate: any = moment(tempDate).subtract(calcDays, 'days').format('YYYY/MM/DD 00:00:00');
          reservationDate.value = new Date(reservationDate.value);
          let nowDate: any = moment().format('YYYY/MM/DD 00:00:00');
          nowDate = new Date(nowDate);
          if (reservationDate.value <= nowDate && tempList[0].check !== "キャンセル") {
            // 予約変更、キャンセル不可
            cancelLimitList.value[result.partitionKey.value] = true;
          }
        }

        if (hasCategory()) {
          displayValues.push({ name: "予約日付", value: getFrontEndDisplay(tempDate) });
          displayValues.push({ name: "予約時間", value: displayTimesForResults.value[result.partitionKey.value] });
        }
        for (let displayItem of tempList) {
          if (getItemType(displayItem.itemKey) != "reservation") {
            displayValues.push({ name: getItemTitle(displayItem.itemKey), value: displayItem.value });
          }
        }
        if (hasCategory()) {
          var tempCounter = 1;
          for (let category of categoryDisplayLabels.value) {
            displayValues.push({
              name: category,
              value: getTagValueFromIndex(result.partitionKey.value, String(tempCounter)),
            });
            tempCounter++;
          }
        }
      } catch (err) {
        console.error("error occured getting values for display");
      }

      mainStore.surveyResultsDisplayValues[result.partitionKey] = displayValues;

      if (tempDate != null && !(tempDate in searchResultsByDay)) {
        searchResultsByDay[tempDate] = [result];
        tempDisplayDates.push(tempDate);
      } else if (tempDate != null && tempDate in searchResultsByDay) {
        searchResultsByDay[tempDate].push(result);
      } else {
        if (reservationDateNotFound.value in searchResultsByDay) {
          searchResultsByDay[reservationDateNotFound.value].push(result);
        } else {
          searchResultsByDay[reservationDateNotFound.value] = [result];
          tempDisplayDates.push(reservationDateNotFound.value);
        }
      }
      resultCounter++;
    }
    if (doesUsePayment.value) {
      await getPaymentResults();
      setCancelablePaymentResultsKeys();
    }
    displayDates.value = tempDisplayDates.sort();
    for (const dayValue in searchResultsByDay) {
      searchResultsByDay[dayValue].sort((a: any, b: any) => {
        try {
          let tempKey = getItemKeyByTitle(reservationTime.value);
          let firstPartA = getSurveyValue(a, tempKey).split(":")[0];
          let firstPartB = getSurveyValue(b, tempKey).split(":")[0];
          return parseInt(firstPartA) - parseInt(firstPartB);
        } catch (error) {
          return 0;
        }
      });
    }

    //Sort reservationDateNotFound.value
    if (
      searchResultsByDay[reservationDateNotFound.value] &&
      searchResultsByDay[reservationDateNotFound.value].length > 1
    ) {
      searchResultsByDay[reservationDateNotFound.value].sort(function (a: { data: any[]; }, b: { data: any[]; }) {
        let aData = a.data.filter((e: { title: string; sortKey: string | string[]; }) => e.title === "接種回数" || e.sortKey.includes("回目"));
        let aPosition = aData[0] ? parseInt(aData[0].value.substring(0, 1)) : 0;
        let bData = b.data.filter((e: { title: string; sortKey: string | string[]; }) => e.title === "接種回数" || e.sortKey.includes("回目"));
        let bPosition = bData[0] ? parseInt(bData[0].value.substring(0, 1)) : 0;
        return aPosition > bPosition ? 1 : bPosition > aPosition ? -1 : 0;
      });
    }
    displayResults.value = removePreviousVaccineAnswerByDate(searchResultsByDay);
  }
};
const getDayOffCount = async (categoryId: any, fromDate: any, toDate: any): Promise<number> => {
  let dayOffCount = 0;
  const _scheduleData = await GetSchedule(categoryId, fromDate, toDate);
  const dayOffInfo = _scheduleData["dayOff"];
  if (dayOffInfo) {
    for (let k in dayOffInfo) {
      if (dayOffInfo[k] === 1) {
        dayOffCount++;
      }
    }
  }
  return dayOffCount;
};
const removePreviousVaccineAnswerByDate = (searchResultsByDate: any): any => {
  if (!isFirstVaccination.value) {
    return searchResultsByDate;
  }

  // Get itemKeys of previoueVaccineItem
  const previousVaccineItemKeys = getPreviousVaccineItemKeys();
  // Remove previousVaccineAnswer
  for (const date in searchResultsByDate) {
    const results = searchResultsByDate[date];
    results.forEach((_, i) => {
      results[i].data = results[i].data.filter(answer => {
        return !previousVaccineItemKeys.includes(answer.itemKey);
      });
    });
  }

  return searchResultsByDate;
};
const removePreviousVaccineAnswer = (surveyResults: any): any => {
  if (!isFirstVaccination.value) {
    return surveyResults;
  }

  // Get itemKeys of previoueVaccineItem
  const previousVaccineItemKeys = getPreviousVaccineItemKeys();
  // Remove previousVaccineAnswer
  return surveyResults.filter(result => !previousVaccineItemKeys.includes(result.itemKey));
};
const getPreviousVaccineItemKeys = (): string[] => {
  const { surveySchema } = mainStore.surveyConfig;
  const previousVaccineItemKeys = [];
  const previousVaccineItemTypes = ['previousVaccineMaker', 'previousVaccineDate'];
  surveySchema.forEach(({ itemKey, type }) => {
    if (previousVaccineItemTypes.includes(type)) {
      previousVaccineItemKeys.push(itemKey);
    }
  });
  return previousVaccineItemKeys;
};
const updateUserIdToSurveyResult = async (): Promise<void> => {
  if (!surveyResults.value) {
    return;
  }
  const userId = getUserId.value;
  let keys = [];
  for (let result of surveyResults.value) {
    for (let item of result.data) {
      keys.push({ partitionKey: item.partitionKey, sortKey: item.sortKey });
    }
  }

  let params = { userId: userId, keys: keys };
  await UpdateUserIdToSurveyResultByPks(params);
};
const hasCountVaccinesItem = (): boolean => {
  //コロナワクチン接種（N回目接種）の固有のアイテムを持っているかチェック
  const surveyConfig = mainStore.surveyConfig;
  if (!surveyConfig?.surveySchema) {
    return;
  }
  const countVaccinesItem = surveyConfig.surveySchema.find((item) => item.type === 'countVaccines');
  return !!countVaccinesItem;
};
const getSurveyResultPartitionKeys = (): string[] => {
  return surveyResults.value ? surveyResults.value.map(({ partitionKey }) => partitionKey) : [];
};
const getPaymentResults = async (): Promise<void> => {
  const partitionKeys = getSurveyResultPartitionKeys();
  if (partitionKeys.length > 0) {
    try {
      const res = await GetPaymentResults({ keys: partitionKeys });
      if (res.code !== 'success') {
        throw res.errorMessage || res;
      }
      searchedPaymentResults.value = res.data;
    } catch (error) {
      console.error(error);
    }
  }
};
const setCancelablePaymentResultsKeys = (): void => {
  cancelablePaymentResultsKeys.value = getCancelablePaymentResults(searchedPaymentResults.value).map(result => result.sortKey);
};
const getPaymentResultByKey = (surveyResultPartitionKey: string) => {
  return searchedPaymentResults.value.find(paymentResult => paymentResult.sortKey === surveyResultPartitionKey);
};
const isShowCancelButton = (partitionKey: string): boolean => {
  const isCancelableReservation = getTimeDisplay(partitionKey) !== '' && !notCancelSurveyList.value[partitionKey];
  if (doesUsePayment.value) {
    const isCancelableSurveyResults = hasCategory() ? isCancelableReservation : !notCancelSurveyList.value[partitionKey];
    const paymentResult = getPaymentResultByKey(partitionKey);
    const isCancelablePaymentResults = paymentResult && cancelablePaymentResultsKeys.value.includes(partitionKey);
    return isCancelableSurveyResults && isCancelablePaymentResults && isPaymentAvailable.value;
  } else {
    return isCancelableReservation;
  }
};
const isEditableResults = (partitionKey: string): boolean => {
  const isEditableSurveyResults = !completedSurveyList.value[partitionKey] && !cancelledReservationCheck(partitionKey);
  if (doesUsePayment.value) {
    const paymentResult = getPaymentResultByKey(partitionKey);
    const paymentStatus = paymentResult.status;
    const surveyResultsLocal = surveyResults.value.find(r => r.partitionKey === partitionKey);
    if (!surveyResultsLocal) return false;
    const isCanceledSurvey = surveyResultsLocal.data[0].check === 'キャンセル';
    return paymentResult
      && !isRegistedFromAdmin(paymentResult.userId)
      && canEditPaymentStatus(paymentStatus)
      && isEditableSurveyResults
      && !isCanceledSurvey
      && isPaymentAvailable.value;
  } else {
    return !completedSurveyList.value[partitionKey];
  }
};
const isCanceledPaymentStatus = (partitionKey: string): boolean => {
  const paymentResult = getPaymentResultByKey(partitionKey);
  return isCanceledStatus(paymentResult?.status) ||
    (notCancelSurveyList.value[partitionKey] && doesUsePayment.value);
};
const isOutOfCancelablePeriod = (partitionKey: string): boolean => {
  const paymentResult = getPaymentResultByKey(partitionKey);
  if (!isFinishedPaymentStatus(paymentResult?.status)) {
    return false;
  }
  return paymentResult ? !isCancelablePeriod(paymentResult.completedDate || paymentResult.createdAt) : false;
};

// computed
const lineUser = computed((): any => {
  return mainStore.lineUser;
});
const surveyConfig = computed((): any => {
  return mainStore.surveyConfig;
});
const categoryDisplayLabels = computed((): any => {
  return mainStore.categoryLabels;
});
const getUserId = computed((): any => {
  // userId を取得する
  let authSession = mainStore.authSession;
  const userInfo = authSession.getIdToken().payload;
  return userInfo.identities[0].userId;
});
const isFirstVaccination = computed((): boolean => {
  const { surveySchema } = surveyConfig.value;
  const countVaccinesInput = surveySchema.find(item => item.type === 'countVaccines')?.default;
  const isFirstVaccination = Number(countVaccinesInput) === 1;
  return isFirstVaccination;
});
const doesUsePayment = computed((): boolean => {
  return surveyConfig.value.usePayment === 1;
});
const listTitle = computed((): string => {
  return doesUsePayment.value ? '検索結果一覧' : '予約一覧';
});
const textEditButton = computed((): string => {
  return doesUsePayment.value ? '変更' : '予約・変更';
});
const isPaymentAvailable = computed((): boolean => {
  return import.meta.env.VITE_USE_PAYMENT === '1';
});

// hooks

onMounted(() => {
  // スクロール位置が下にある状態で描画される可能性があるため、Scroll位置を0に戻す
  window.scrollTo(0, 0);
});


onBeforeMount(async () => {
  if (!mainStore.authSession || !mainStore.surveyResults) {
    await router.replace({
      name: "SurveyConfirm"
    });
    return;
  }
  surveyResults.value = mainStore.surveyResults;
  loadingResults.value = true;
  await updateUserIdToSurveyResult();
  resetCalendarUseParams();
  setPaymentOrderInput(null);

  //reset some display stuff
  categoryValuesForResults.value = [];
  displayTimesForResults.value = {};
  mainStore.categoryLabels = [];
  mainStore.surveyResultsDisplayValues = {};
  try {
    if (Object.keys(mainStore.categoryValues).length === 0) {
      var allCategories = await GetAllCategories();
      for (var calendar of allCategories) {
        mainStore.categoryValues[calendar.id] = {
          tag1: calendar.tag1,
          tag2: calendar.tag2,
          tag3: calendar.tag3
        };
      }
    }
    if (mainStore.categoryLabels.length === 0) {
      var calendarSettings = await GetTagNames();
      if (calendarSettings.tag1) {
        mainStore.categoryLabels.push(calendarSettings.tag1);
      }
      if (calendarSettings.tag2) {
        mainStore.categoryLabels.push(calendarSettings.tag2);
      }
      if (calendarSettings.tag3) {
        mainStore.categoryLabels.push(calendarSettings.tag3);
      }
    }

    //if surveySchema type is corona
    //do special processing here
    if ("surveyType" in mainStore.surveyConfig && mainStore.surveyConfig.surveyType == "corona") {
      surveyIsCorona.value = true;
    }
    calendars.value = await GetAllCalendars();
    await setupResultsForDisplay();
  } catch (err) {
    console.error(err);
  } finally {
    loadingResults.value = false;
  }

  const findData = mainStore.surveyResults[0].data.find((elm) => elm.type === 'files');
  fileData.value = [];
  await findData?.files?.forEach(async (elm) => {
    await GetUploadFile(elm).then((getData) => {
      let str = elm.split('/');
      if (getData.result === 'OK') {
        fileData.value.push({ url: getData.data, name: str[str.length -1]});
      }
    });
  });
});

</script>

<style scoped>
.bold-text {
  font-weight: bold;
}

.appointment-card {
  padding: 0.5em;
}

.confirm-list-title-row {
  padding-bottom: 0;
}

.confirm-list-button {
  width: inherit;
}
</style>

<style lang="less">
.form-design-code .CodeMirror {
  height: 80vh !important;
}

.confirmation-table {
  border: 1px solid rgba(0, 0, 0, 0.12);

  .confirm-title {
    background: rgba(0, 0, 0, 0.04);
  }
}

.cancelled-reservation {
  color: darkred;
}
</style>
