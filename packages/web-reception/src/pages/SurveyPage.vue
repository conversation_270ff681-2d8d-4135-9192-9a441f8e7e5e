<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div class="q-mx-auto q-py-md" style="width: 90%;">
    <div v-if="!pushDone">
      <q-row>
        <q-col>
          <template v-if="!isEmpty(surveyConfig) && !isEmpty(lineUser) && !error">
            <FormRendering 
              :configJson="configJsonWithoutAdminItem" 
              :isLiffMode="true" 
              :userInfo="lineUser"
              :accent="primaryColor" 
              ref="inputForm" 
              @redirect="handleRedirect" 
              :categoryRequiredFlag="true"
              :calendarLabelDisplay="false" 
              :calendarButtonDisplay="true" 
              :taxRate="applyingTaxRate"
              :paymentResult="paymentResult" 
              :paymentService="paymentService" 
              :products="productList"
              :isFetchingPaymentInfo="isFetchingPaymentInfo" 
              @handleCheckSaibanExisting="handleCheckSaibanExisting"
              @setPaymentInfo="hendleSetPaymentInfo"
              :isNewAnswer="!route.query.partitionKey" 
              :pinia="getActivePinia()" 
              @updateIsFormValid="updateIsFormValid"
            />
            <q-btn v-if="!hasLinkButton" color="primary" dark @click="openDialog" class="q-my-md full-width">
              {{ textBtnSubmit }}
            </q-btn>
            <q-btn color="primary" dark @click="reloadPage" block class="q-my-md" v-if="isLatest === false">
              再読み込み
            </q-btn>
            <q-btn v-if="isShowCloseButton" color="grey" dark block class="q-my-md" @click="closeWindow"> 閉じる </q-btn>
          </template>
          <div v-if="isLoading">
            <div style="height: 90vh">
              <SurveyLoading />
            </div>
          </div>
          <div v-if="error">
            <div class="row">
              <div class="q-mx-auto col-12">
                <q-card shaped class="q-mx-auto userInfoTitle">
                  <q-list>
                    <q-item>
                      <q-item-section avatar>
                        <q-avatar v-if="lineUser && lineUser.picture">
                          <q-img :src="lineUser.picture"></q-img>
                        </q-avatar>
                        <q-avatar v-else color="primary">
                          <q-icon dark name="mdi-account-circle"></q-icon>
                        </q-avatar>
                      </q-item-section>

                      <q-item-section>
                        <q-item-label>
                          <span v-if="lineUser">{{ lineUser.name }}</span> 様
                        </q-item-label>
                      </q-item-section>

                      <q-item-section> </q-item-section>
                    </q-item>
                  </q-list>
                </q-card>
              </div>
            </div>

            <q-card class="q-my-md">
              <q-bar class="bg-primary" style="height: 7px;"></q-bar>
              <div class="col-12">
                <Alert class="q-my-none" colorBg="white" colorText="black" title="エラー">
                    <div>画面表示に問題が発生しました。</div>
                    <div>エラー:</div>
                    <div style="white-space: pre-wrap">{{ error }}</div>
                </Alert>
              </div>
            </q-card>
          </div>
        </q-col>
      </q-row>

      <q-row justify="center">
        <q-dialog v-model="dialog" scrollable>
          <q-card style="max-width: 800px; width: 800px;">
            <div class="q-px-xs q-py-md text-center">
              {{ isPushing ? "送信中..." : textConfirm }}
            </div>
            <q-separator></q-separator>
            <q-card-section v-if="formData !== undefined" class="q-pa-sm" style="max-height: 300px; overflow-y: auto;">
              <div v-if="isPushing" class="q-pa-md text-center">
                <q-circular-progress indeterminate color="primary"></q-circular-progress>
                しばらくお待ちください。
              </div>
              <div v-else>
                <Alert v-if="hasError" dismissible dense outlined type="error" class="q-my-md" elevation="4">
                  エラーが発生しました。<br />
                  
                  <div v-html="pushError"></div>
                  <ul v-if="validErrors && Object.keys(validErrors).length > 0">
                    <li v-for="(error, key) in validErrors" :key="key">
                        <span>{{ getTitle(key) }}: {{ error[0] }}</span>
                    </li>
                  </ul>
                </Alert>
                <SurveyConfirm :dataProp="surveyResultNotAdminItem" :surveyConfig="surveyConfig"
                  :paymentData="paymentOrderData" :fileData="fileData" />
              </div>
            </q-card-section>
            <q-separator></q-separator>
            <q-card-section v-if="!isFormValid">
              <Alert type="error" colorText="negative" colorBg="red-1">入力エラーがあります。</Alert>
            </q-card-section>
            <q-card-actions class="q-mx-sm q-mb-xs bg" v-if="!isPushing">
              <q-space></q-space>
              <q-btn class="q-px-lg" color="grey-6" dark @click="dialog = false"> 閉じる </q-btn>
              <q-btn class="q-px-xl" color="primary" dark @click="pushSurveyAnswer" v-if="isLatest === true" :disable="!isFormValid">
                {{ textBtnConfirm }}
              </q-btn>
            </q-card-actions>
          </q-card>
        </q-dialog>
      </q-row>
    </div>

    <div v-else>
      <q-row>
        <q-col class="q-mx-auto">
          <q-card shaped class="userInfoTitle">
            <q-list>
              <q-item class="row">
                <q-item-section class="col-auto">
                  <q-avatar v-if="lineUser && lineUser.picture">
                    <q-img :src="lineUser.picture"></q-img>
                  </q-avatar>
                  <q-avatar v-else color="primary">
                    <q-icon dark name="mdi-account-circle"></q-icon>
                  </q-avatar>
                </q-item-section>

                <q-item-section class="col-11">
                  <q-item-label style="font-size: 21px;">
                    <span v-if="lineUser">{{ lineUser.name }}</span> 様
                  </q-item-label>
                </q-item-section>

                <q-item-section> </q-item-section>
              </q-item>
            </q-list>
          </q-card>
        </q-col>
      </q-row>

      <q-row align="center" justify="center">
        <q-col cols="12" sm="10" md="6" xl="4" class="flex flex-center ">
          <Alert class="q-my-md" :bar="{ place: 'top', color: 'primary' }" colorBg="blue-1" colorText="primary" colored-border elevation="2">
            <div>
              <div v-if="surveyConfig && surveyConfig.endOfSurveyMessage && surveyConfig.endOfSurveyMessage.length > 0"
                style="white-space: pre-line">
                <div v-if="Array.isArray(surveyConfig.endOfSurveyMessage)">
                  <div v-for="(message, index) in surveyConfig.endOfSurveyMessage" :key="index">
                    <div v-if="message.type === 'text'">
                      {{ message.text }}
                    </div>
                    <div v-if="message.type === 'image'">
                      <img style="max-width: 100%; max-height: 66vh" :src="message.originalContentUrl" alt="image" />
                    </div>
                  </div>
                </div>
                <div v-else>
                  {{ surveyConfig.endOfSurveyMessage }}
                </div>
              </div>
              <!-- keep default message -->
              <div v-else>
                ありがとうございます。<br>
                情報を更新しました。<br>
                ブラウザを閉じてください。<br>
              </div>
              <div 
                v-if="
                  surveyConfig &&
                    surveyConfig.isAppending &&
                    surveyConfig.isAppending.value &&
                    isShowAnswerCode &&
                    answerCode
                "
              >
                受付番号：{{ answerCode }}
              </div>
            </div>
          </Alert>
        </q-col>
      </q-row>
    </div>

    <!-- アラートメッセージ -->
    <q-dialog v-model="alert.show" width="320">
      <q-card dark>
        <div fluid class="q-pa-md">
          <q-row class="row flex-center">
            <q-col :align="alert.multipleLine ? 'left' : 'center'" class="line-break">
              <q-icon color="warning" class="q-mr-xs q-mb-xs" size="sm" name="mdi-alert"></q-icon>{{ alert.message }}
            </q-col>
          </q-row>
          <q-row class="row flex-center">
            <q-col align="center">
              <q-btn class="q-mx-sm warning" dark @click="alert.show = false"><q-icon dark color="white"
                  name="mdi-close"></q-icon>閉じる</q-btn>
            </q-col>
          </q-row>
        </div>
      </q-card>
    </q-dialog>

    <!-- 処理中表示 -->
    <q-dialog v-model="loading">
      <q-spinner color="primary" :size="50" :thickness="5" :speed="0.6" />
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, onMounted, onBeforeMount, ref, Ref, toRaw } from 'vue';

import { useMainStore } from '@/stores';
import { useSurveyResultsStore } from '@stores/modules/surveyResults';
import { usePaymentStore } from '@stores/modules/payment';
import { useRoute, useRouter } from 'vue-router';
import moment from 'moment';
import { cloneDeep, isEmpty } from 'lodash';

import SurveyLoading from '@/components/common/SurveyLoading.vue';

// old imports
// 旧インポート
import FormRendering from "lsc-form-rendering";
import SurveyConfirm from "@/components/SurveyConfirm.vue";
import {
  GetSurveyById,
  GetSurveyResult,
  PutSurveyResult,
  SaveTemporarySurveyResults,
} from "@/services/surveys.service";
import { GetCalendarInfo, getReservationItemInfo, calendarCanBeReserve } from "@/services/calendars.service";
import {PostPaymentOrder, PostPaymentOrderNew} from '@/services/paymentResults.service';
import { Auth } from "../services/auth.service";
import liff from "@line/liff";
import { NOT_FOUND_MESSAGE, TOKEN_EXPIRED_MESSAGE } from "@/constants/errorMessages";
import { createAppendingPayload, triggerLogin } from '@/utils/surveyResultsUtils';
import { MULTIPLE_REQUEST_MESSAGE } from "@/utils/errorMessageUtils";
import { isFinishedPaymentStatus, PAY_TYPES } from '@/utils/paymentResultUtils';
import { useQuasar } from 'quasar';
import { getActivePinia } from 'pinia';
import {getApiUrl, getCurrentAppUrl} from "@utils/configuration";
import { GetUploadFile, UploadFile } from '@/services/member.service';
import Alert from '@/components/common/Alert.vue';
import {PaymentData} from '@/utils/paymentResultUtils';

defineOptions({
  inheritAttrs: false
})

const TEMPORARY_LOCKED = "temporary_locked";

const mainStore = useMainStore();
const paymentStore = usePaymentStore();
const surveyResultsStore = useSurveyResultsStore();

const router = useRouter();
const route = useRoute();

const $q = useQuasar();

interface LocalState {
  dialog: boolean;
  formData: FormData;
  error: any;
  isLoading: boolean;
  lineUser: any;
  surveyConfig: any;
  surveyInput: any;
  surveyResult: any;
  isPushing: boolean;
  pushError: any;
  hasError: boolean;
  pushDone: boolean;
  isLatest: boolean;
  partitionKey: any;
  hasLinkButton: boolean;
  answerCode: any;
  isShowAnswerCode: boolean;
  alert: {
    show: boolean;
    multipleLine: boolean;
    message: string;
  };
  loading: boolean;
  surveyResultNotAdminItem: any;
  reservationItemData: any;
  paymentOrderData: Record<any, any>;
}

const inputForm = ref(null);

const dialog = ref(false);
const formData = ref({});
const error = ref(null);
const isLoading = ref(false);
const lineUser: Ref<any> = ref({});
const surveyConfig: Ref<any> = ref({});
const surveyInput = ref();
const surveyResult = ref();
const isPushing = ref(false);
const pushError = ref({});
const hasError = ref(false);
const pushDone = ref(false);
const isLatest = ref(true);
const partitionKey = ref(null);
const hasLinkButton = ref(false);
const answerCode = ref(null);
const isShowAnswerCode = ref(false);
const alert = ref({
  show: false,
  multipleLine: false,
  message: '',
});
const loading = ref(false);
const surveyResultNotAdminItem = ref(null);
const reservationItemData = ref(null);
const paymentOrderData: Ref<PaymentData | undefined> = ref(undefined);
const fileData = ref<File[]>();
const isFormValid = ref<boolean>(false);

const validErrors = ref<any>({});

// Since we cannot connect to the admin api, default accent color is set here
// 管理画面APIに接続できないため、デフォルトのアクセントカラーをここで設定します
const primaryColor = "#1976D2";
//console.log('primaryColor', primaryColor);

// methods
const setCalendarUseParams = surveyResultsStore.setCalendarUseParamsJson;
const setEditingAppendingMode = mainStore.setEditingAppendingMode;
const setPaymentService = paymentStore.setPaymetService;
const setProductList = paymentStore.setProductList;
const setPaymentResult = paymentStore.setPaymentResult;
const setPaymentOrderInput = paymentStore.setPaymentOrderInputFromSurvey;
const setReservationItemCost = surveyResultsStore.setReservationItemCost;
const getTaxRateSettings = paymentStore.getTaxRateSettings;
const getPaymentServcie = paymentStore.getPaymentService;
const resetPaymentInfo = paymentStore.resetPaymentService;
const getReservationPaymentService = paymentStore.getReservationPaymentService;
const getPaymentResult = paymentStore.getPaymentResult;
const updateIsFormValid = (valid) => {
  isFormValid.value = valid;
}
const onSubmitSelectProductForm = async () => {
  try {
    loading.value = true;
    if (!realSurveyConfig.value?.isAppending?.value) {
      throw '決済機能は追加型帳票でのみ利用可能です。';
    }
    const results = createAppendingPayload({
      surveyId: realSurveyConfig.value?.surveyId,
      userId: lineUserId.value,
      items: surveyResultNotAdminItem.value.surveyResults,
    });
    const payload = {
      data: results,
      order: toRaw(paymentOrderData.value),
      callbackApiBaseUrl: getApiUrl(),
      liffBaseUrl: getCurrentAppUrl(),
    };

    const res = await SaveTemporarySurveyResults(payload);
    if (res.code !== "success") {
      throw res.errorMessage || res.message;
    }
    await PostPaymentOrderNew(res.form);
  } catch (error) {
    alert.value.message = error;
    alert.value.multipleLine = false;
    alert.value.show = true;
  } finally {
    loading.value = false;
  }
};
const getCoronaVaccinationData = async (): Promise<void> => {
  let coronaVaccinationData = null;
  if (mainStore.realSurveyConfig.value && mainStore.realSurveyConfig.value.surveyType === "corona") {
    coronaVaccinationData = {
      allowed_after_days: 21,
      current_time: null, // "first_time", "second_time"
      first_time: {
        itemKey: null,
        sesshu_jisshi_bi: {
          // 接種実施日
          value: null,
          itemKey: null,
        },
        sesshuVaccineMaker: {
          // 接種ワクチンのメーカー
          value: null,
          itemKey: null,
          sectionOptions: [],
        },
        survey_result: null, // important! using!
      },
      second_time: {
        itemKey: null,
        allowed_started_date: {
          // >= first_time.sesshu_jisshi_bi + allowed_after_days
          string: null,
          date: null,
        },
        survey_result: null, // not important, do not use
      },
    };

    if (
      mainStore.realSurveyConfig.value.surveySchema &&
      Array.isArray(mainStore.realSurveyConfig.value.surveySchema)
    ) {
      mainStore.realSurveyConfig.value.surveySchema.forEach(
        (schema: {
          type: string;
          itemKey: any;
          attributes: { isSesshuKaisuu: boolean; hasAllowedDays: boolean };
        }) => {
          if (schema.type === "sesshuJisshiDate") {
            coronaVaccinationData.first_time.sesshu_jisshi_bi.itemKey = schema.itemKey;
          } else if (schema.type === "choicegroupheader" && schema.attributes.isSesshuKaisuu === true) {
            coronaVaccinationData.first_time.itemKey = schema.itemKey;
            coronaVaccinationData.second_time.itemKey = schema.itemKey;
          } else if (schema.type === "sesshuVaccineMaker" && schema.attributes.hasAllowedDays === true) {
            coronaVaccinationData.first_time.sesshuVaccineMaker = schema;
          }
        }
      );
    }

    if (coronaVaccinationData.first_time.sesshu_jisshi_bi.itemKey) {
      mainStore.surveyInput.forEach((input: { itemKey: any; value: any }) => {
        if (input.itemKey === coronaVaccinationData.second_time.itemKey) {
          switch (input.value) {
            case "1回目":
              coronaVaccinationData.current_time = "first_time";
              break;
            case "2回目":
              coronaVaccinationData.current_time = "second_time";
              break;
          }
          return;
        }
      });
    }

    if (coronaVaccinationData.current_time !== "second_time") {
      return null;
    }

    if (mainStore.surveyResults && Array.isArray(mainStore.surveyResults)) {
      let first_time = {
        list: [],
        value: null,
        previous: {
          surveyResult: null,
          updatedAt: 0,
        },
        current: {
          surveyResult: null,
          updatedAt: 0,
        },
        latest: {
          surveyResult: null,
          updatedAt: 0,
        },
      };

      // collect record that value is "1回目" and type is "sesshuJisshiDate"
      {
        mainStore.surveyResults.forEach((surveyResult: { data: any[] }) => {
          if (surveyResult.data && Array.isArray(surveyResult.data)) {
            surveyResult.data.forEach((item: { value: string; itemKey: any }) => {
              if (item.value === "1回目" && item.itemKey === coronaVaccinationData.first_time.itemKey) {
                first_time.list.push(surveyResult);
                return;
              }
            });
          }
        });

        if (first_time.list && Array.isArray(first_time.list) && first_time.list.length > 0) {
          let list_1kaime = first_time.list;
          first_time.list = [];
          list_1kaime.forEach((surveyResult) => {
            surveyResult.data.forEach((item: { itemKey: any }) => {
              if (item.itemKey === coronaVaccinationData.first_time.sesshu_jisshi_bi.itemKey) {
                first_time.list.push(surveyResult);
                return;
              }
            });
          });
        }

        // get the latest 1回目 record
        if (first_time.list && Array.isArray(first_time.list) && first_time.list.length > 0) {
          let sesshuJisshiDate_list = first_time.list;
          first_time.list = [];
          sesshuJisshiDate_list.forEach((surveyResult) => {
            first_time.current.surveyResult = surveyResult;

            if (surveyResult.data && Array.isArray(surveyResult.data) && surveyResult.data.length > 0) {
              first_time.current.updatedAt = surveyResult.data[0].updatedAt ?? 0;
            }

            if (first_time.previous.surveyResult === null) {
              first_time.latest.surveyResult = surveyResult;
              if (surveyResult.data && Array.isArray(surveyResult.data) && surveyResult.data.length > 0) {
                first_time.latest.updatedAt = surveyResult.data[0].updatedAt ?? 0;
              }
            } else {
              if (first_time.previous.updatedAt < first_time.current.updatedAt) {
                first_time.latest = first_time.current;
              } else {
                first_time.latest = first_time.previous;
              }
            }

            first_time.previous = first_time.current;
          });
        }
      }

      coronaVaccinationData.first_time.survey_result = first_time.latest.surveyResult;
    }

    if (
      coronaVaccinationData &&
      coronaVaccinationData.first_time &&
      coronaVaccinationData.first_time.survey_result &&
      coronaVaccinationData.first_time.survey_result.data &&
      Array.isArray(coronaVaccinationData.first_time.survey_result.data)
    ) {
      // process to get allowed_after_days
      if (coronaVaccinationData.first_time.sesshuVaccineMaker.itemKey) {
        coronaVaccinationData.first_time.survey_result.data.forEach((item: { itemKey: any; value: any }) => {
          if (coronaVaccinationData.first_time.sesshuVaccineMaker.itemKey == item.itemKey) {
            {
              coronaVaccinationData.first_time.sesshuVaccineMaker.value = item.value;
              let sesshuVaccineMakerValue = item.value;

              if (
                coronaVaccinationData.first_time.sesshuVaccineMaker.sectionOptions &&
                Array.isArray(coronaVaccinationData.first_time.sesshuVaccineMaker.sectionOptions)
              ) {
                coronaVaccinationData.first_time.sesshuVaccineMaker.sectionOptions.forEach(
                  (sectionOption: { option: { value: any }; groupheader: { value: any } }) => {
                    if (sesshuVaccineMakerValue === sectionOption.option.value) {
                      let days_value = sectionOption.groupheader.value;

                      if (isNaN(days_value)) {
                        days_value = 0;
                      } else {
                        days_value = parseInt(days_value);
                      }
                      days_value++;
                      coronaVaccinationData.allowed_after_days = days_value;

                      return; // forEach
                    }
                  }
                );
              }
            }

            return; // forEach
          }
        });
      }

      if (coronaVaccinationData.first_time.sesshu_jisshi_bi.itemKey) {
        coronaVaccinationData.first_time.survey_result.data.forEach(
          (item: { itemKey: any; value: string | number | Date }) => {
            if (coronaVaccinationData.first_time.sesshu_jisshi_bi.itemKey == item.itemKey) {
              let d = null;
              if (item.value && item.value != null) {
                d = new Date(item.value);
              } else {
                d = new Date();
              }
              // d.setHours(0,0,0,0) // <= do NOT add this code in here. It will reduce 1 day of date

              {
                let dString = d.toISOString().slice(0, 10);
                coronaVaccinationData.first_time.sesshu_jisshi_bi.value = dString;
                {
                  d.setDate(d.getDate() + coronaVaccinationData.allowed_after_days);
                  coronaVaccinationData.second_time.allowed_started_date.string = d.toISOString().slice(0, 10);
                  coronaVaccinationData.second_time.allowed_started_date.date = new Date(
                    coronaVaccinationData.second_time.allowed_started_date.string
                  );
                }
              }

              return;
            }
          }
        );
      }
    }
  }

  surveyResultsStore.coronaVaccinationData = coronaVaccinationData;
  return coronaVaccinationData;
};
const checkHasCountVaccinesItem = (): boolean => {
  const surveyConfig = mainStore.realSurveyConfig.value;
  if (!surveyConfig?.surveySchema) {
    return;
  }
  const countVaccinesItem = surveyConfig.surveySchema.find((item) => item.type === "countVaccines");
  return !!countVaccinesItem;
};
const setPreviousVaccinationDataToStore = (surveyResults): void => {
  const surveyConfig = mainStore.realSurveyConfig.value;
  if (!surveyConfig?.surveySchema) {
    return;
  }
  // Assuming that vaccination interval values, "previousVaccineDate" are always present in "新型コロナワクチン接種(N回目)"

  // Get the value of the vaccination interval
  const countVaccinesItem = surveyConfig.surveySchema.find((item) => item.type === "countVaccines");
  const countVaccinesInput = countVaccinesItem.default != null ? Number(countVaccinesItem.default) : 0;
  if (countVaccinesInput > 1) {
    const previousVaccineMakerItem = surveyConfig.surveySchema.find((item) => item.type === "previousVaccineMaker");
    const previousVaccineMakerValue = surveyResults.find(
      (result) => result.itemKey === previousVaccineMakerItem.itemKey
    ).value;
    const intervalOption = previousVaccineMakerItem.sectionOptions.find(
      (obj) => obj.option.value === previousVaccineMakerValue
    );
    const intervalTimes = intervalOption.groupheader.value != null ? Number(intervalOption.groupheader.value) : undefined;

    // Get the value of "previousVaccineDate"
    const previousVaccineDateItem = surveyConfig.surveySchema.find((item) => item.type === "previousVaccineDate");
    const previousVaccineDateValue = surveyResults.find(
      (result) => result.itemKey === previousVaccineDateItem?.itemKey
    ).value;

    // Check interval and Set vaccination allowed start date
    const vaccinationIntervalType = surveyConfig.vaccinationIntervalType.input;
    if (vaccinationIntervalType === "日数") {
      // 接種日の翌日から間隔を計算するのでintervalTimes + 1を加算
      surveyResultsStore.vaccinationAllowedStartDate = moment(previousVaccineDateValue)
        .add(intervalTimes + 1, "days")
        .format("YYYYMMDD");
      return;
    }
    if (vaccinationIntervalType === "月数") {
      const previousVaccineDay = moment(previousVaccineDateValue).format("DD");
      const dateOfIntervalTimesLater = moment(previousVaccineDateValue).add(intervalTimes, "months");
      const intervalTimesLaterDay = moment(dateOfIntervalTimesLater).format("DD");

      if (intervalTimesLaterDay === previousVaccineDay) {
        surveyResultsStore.vaccinationAllowedStartDate = dateOfIntervalTimesLater.format("YYYYMMDD");
        return;
      } else {
        // Nか月後の日数が同日ではない場合は、Nか月後の翌日から予約可能開始日とする
        // 例：2021/6/30の8か月後は2022/2/29なので、2022/3/1から予約可能
        surveyResultsStore.vaccinationAllowedStartDate = dateOfIntervalTimesLater.add(1, "days").format("YYYYMMDD");
      }
    }
  }
};
const handleRedirect = async (): Promise<void> => {
  //console.log("handleRedirect");
  const _surveyResult = await handleSurveyResults();
  //console.log("handleRedirect", _surveyResult);
  if (_surveyResult) {
    let hasError = false;
    const hasCountVaccinesItem = checkHasCountVaccinesItem();

    if (hasCountVaccinesItem) {
      // For the "新型コロナワクチン接種(N回目)" template
      setPreviousVaccinationDataToStore(_surveyResult.surveyResults);
    } else {
      await getCoronaVaccinationData();

      if (surveyResultsStore.coronaVaccinationData) {
        // コロナワクチン
        if (surveyResultsStore.coronaVaccinationData.current_time === "second_time") {
          if (
            surveyResultsStore.coronaVaccinationData.first_time.sesshu_jisshi_bi.value == null ||
            surveyResultsStore.coronaVaccinationData.first_time.sesshu_jisshi_bi.value == "" ||
            surveyResultsStore.coronaVaccinationData.first_time.sesshu_jisshi_bi.value == "null"
          ) {
            // console.log("1回目の接種日が未入力です");
            hasError = true;
            alert.value.message =
              "1回目の接種が完了していない場合、2回目の接種の予約を実施できません。\n\n1回目の接種が完了した後、再度ご予約ください。\n\n※接種完了直後は、システムへの反映が終わっていない場合も御座います。\n\nその場合は、暫くたってから再度ご予約いただきますようお願いします。";
            alert.value.multipleLine = true;
            alert.value.show = true;
          }
        }
      }
    }

    if (!hasError) {
      let _url = "";
      let categoryId = null;
      loading.value = true;
      let _params = {};
      _params["surveyId"] = _surveyResult.surveyId;
      _params["userId"] = _surveyResult.userId;
      if (_surveyResult.surveyResults.length > 0) {
        for (var i = 0; i < _surveyResult.surveyResults.length; i++) {
          const _result = _surveyResult.surveyResults[i];

          const type = getType(_result.itemKey);
          if (type !== "linkbutton") {
            if (type !== "checkboxes") {
              _params[_result.itemKey] = { title: _result.title, value: _result.value, type: type };

              if (type === "reservation") {
                let fixCategoryTitle = getFixedCategoryTitle(_result.itemKey);
                _params[_result.itemKey] = Object.assign(_params[_result.itemKey], fixCategoryTitle);

                if (!doesUsePayment.value) {
                  let itemCost = 1;
                  let itemName = null;
                  for (const item of reservationItemData.value) {
                    if (item.categoryId + "_" + item.itemId === _params[_result.itemKey].value) {
                      itemCost = item.cost;
                      itemName = item.itemName;
                      break;
                    }
                  }
                  surveyResultsStore.reservationItemCost = itemCost != null ? Number(itemCost) : undefined;
                  surveyResultsStore.reservationItemName = itemName;
                } else {
                  surveyResultsStore.reservationItemCost = paymentOrderData.value.reservationCost;
                }
              }
            } else {
              if (!_params[_result.itemKey]) {
                _params[_result.itemKey] = { title: _result.title, value: [], type: type };
              }
              _params[_result.itemKey]["value"].push(_result.value);
            }
          } else {
            _url = _result.value;
          }
          if (type === "reservation") {
            categoryId = _result.value.split("_")[0];
          }
        }
      }
      let chkCalendarOff = false;
      let chkCalendarCanBeReserve = true;
      if (categoryId) {
        chkCalendarOff = await checkCalendarIsOff(categoryId);
        chkCalendarCanBeReserve = await checkCalendarCanBeReserve(categoryId);
      }
      if (chkCalendarOff) {
        alert.value.message = "現在予約を受け付けておりません";
        alert.value.multipleLine = false;
        alert.value.show = true;
      } else if (!chkCalendarCanBeReserve) {
        alert.value.message = "現在予約可能な枠がありません";
        alert.value.multipleLine = false;
        alert.value.show = true;
      } else {
        if (_url.length == 0) {
          _url = "/calendar";
        }

        loading.value = false;

        //console.log("setCalendarUseParams", _params);

        setCalendarUseParams(_params);
        mainStore.surveyResultsSavedWhenMovingToCalendar = cloneDeep(_surveyResult.surveyResults);
        if (doesUsePayment.value) {
          setPaymentOrderInput(paymentOrderData.value);
          if (hasCategory.value) {
            if (paymentOrderInputFromSurvey.value?.cost ?? false) {
              setReservationItemCost(paymentOrderInputFromSurvey.value.cost);
            } else {
              setReservationItemCost(paymentOrderData.value.reservationCost);
            }
          }
        }

        var removedFirstSlashUrl = _url.replace("/", "");
        await router.push({ name: removedFirstSlashUrl }).catch((error) => {
          console.error("error", error);
        });
      }
    }
  } else {
    alert.value.message = "入力内容を確認してください";
    alert.value.multipleLine = false;
    alert.value.show = true;

    $q.notify({
      message: "入力内容を確認してください",
      color: "warning",
      position: "top",
      timeout: 2000,
    });
  }
  loading.value = false;
};
const setSurveyResultNotAdminItem = (surveyResult: { surveyResults: any }): any => {
  if (surveyResult && surveyResult.surveyResults) {
    // Survey Schema Result
    const surveyResults = [];
    for (const item of surveyResult.surveyResults) {
      if (item.itemKey === 'paymentData') {
        continue;
      }
      // Check admin item or not
      const _item = configJson.value.surveySchema.find((obj: { itemKey: any }) => obj.itemKey === item.itemKey);
      if (_item && _item.isAdminItem) {
        continue;
      }
      surveyResults.push(item);
    }
    return {
      surveyResults,
    };
  }
  return null;
};
const openDialog = async (): Promise<void> => {
  const _surveyResult = await handleSurveyResults();
  //console.log("openDialog", _surveyResult);

  //console.log("openDialog", _surveyResult);

  surveyResultNotAdminItem.value = setSurveyResultNotAdminItem(_surveyResult);

  if (surveyResultNotAdminItem.value) {
    dialog.value = true;
  } else {
    alert.value.message = "入力内容を確認してください";
    alert.value.multipleLine = false;
    alert.value.show = true;
  }

  surveyResult.value = _surveyResult;
  if (partitionKey.value) {
    surveyResult.value.partitionKey = partitionKey;
  }
};
const reloadPage = (): void => {
  window.location.reload();
};
const getTitle = (key: any): any => {
  let _item = configJson.value.surveySchema.find((obj: { itemKey: any }) => obj.itemKey === key);
  return _item ? _item.title : "";
};
const getFixedCategoryTitle = (key: any): any => {
  let result = {
    fixedLargeCategoryTitle: "",
    fixedMediumCategoryTitle: "",
    fixedSmallCategoryTitle: "",
  };
  let _item = configJson.value.surveySchema.find((obj: { itemKey: any }) => obj.itemKey === key);
  if (!_item) return result;

  //console.log("getFixedCategoryTitle", _item);  

  // Upd: Added fallback to usual titles.
  if (_item.setLargeCategoryTitle) {
    result.fixedLargeCategoryTitle = _item.setLargeCategoryTitle;
  }
  if (_item.setMediumCategoryTitle) {
    result.fixedMediumCategoryTitle = _item.setMediumCategoryTitle;
  }
  if (_item.setLargeCategoryTitle) {
    result.fixedSmallCategoryTitle = _item.setSmallCategoryTitle;
  }

  return result;
};
const getType = (key: any): any => {
  let _item = configJson.value.surveySchema.find((obj: { itemKey: any }) => obj.itemKey === key);
  return _item ? _item.type : "";
};
const hasType = (findType: any): boolean => {
  let _item = configJson.value.surveySchema.find((obj: { type: any }) => obj.type === findType);
  return _item ? true : false;
};
const isAppendingTypeConfig = (surveyConfig): boolean => {
  return !!surveyConfig?.isAppending?.value;
};
const initSurvey = async (authSession: any): Promise<void> => {
  try {
    isLoading.value = true;

    //console.log("initSurvey");

    let _surveyConfig = await GetSurveyById(route.params.id);
    const originalSurveyConfigResponse = cloneDeep(_surveyConfig);

    //console.log("surveyConfig", _surveyConfig);

    // Redirect in case there is a member form
    if (originalSurveyConfigResponse.data) {
      if ("hasMember" in _surveyConfig && (_surveyConfig.hasMember === false)) {
        if (originalSurveyConfigResponse.data.memberFormId) {
          router.push({ name: "LiffMember", params: { id: originalSurveyConfigResponse.data.memberFormId } });
        }
      }
    }

    //console.log("HAS MEMBER", _surveyConfig.hasMember);

    if (_surveyConfig && _surveyConfig.response) {
      //console.log("RESPONSE", _surveyConfig.response);
      if (_surveyConfig.response.status === 403) {
        triggerLogin($q, router, route, false);
        if (
          _surveyConfig.response.data.error === "invalid_request" &&
          _surveyConfig.response.data.error_description === "IdToken expired."
        ) {
          error.value = japaneseMessageConvert(_surveyConfig.response.data.error_description);
        }
        throw _surveyConfig;
      } else if (_surveyConfig.response.status === 404) {
        error.value = japaneseMessageConvert(_surveyConfig.response.status);
      } else if (_surveyConfig.response.status === 400) {
        //triggerLogin($q, router, route, false);
      }
      console.error("Error in SurveyPage.initSurvey", _surveyConfig);
      return;
    }

    let _surveyInput: { result: string; data: any };

    //console.log("BEFORE GETTING SURVEY RESULTS");
    //Handle getting survey results
    if (Object.keys(calendarUseParamsJSON.value).length != 0) {
      //reroute from calendar back
      surveyInput.value = mainStore.surveyResultsSavedWhenMovingToCalendar;
    } else if (_surveyConfig && _surveyConfig.data && mainStore.selectedSurveyResults) {
      //reroute from survey confirm
      surveyInput.value = cloneDeep(mainStore.selectedSurveyResults.data);
    } else if (route.query.partitionKey) {
      _surveyInput = await GetSurveyResult(route.params.id, authSession, route.query.partitionKey as string);
      if (isAppendingTypeConfig(_surveyConfig?.data)) {
        setEditingAppendingMode(true);
      }
    } else if (
      _surveyConfig &&
      _surveyConfig.data &&
      (!_surveyConfig.data.isAppending || // 古いバージョン
        !_surveyConfig.data.isAppending.value) // 更新型
    ) {
      _surveyInput = await GetSurveyResult(route.params.id, authSession, "");
    } else {
      surveyInput.value = [];
    }

    //console.log(`_surveyInput`, _surveyInput);
    
    //console.log("BEFORE GETTING CALENDAR INFO");

    // 予約項目をセット
    let itemInfo = await getReservationItemInfo("all");
    //console.log("CALENDAR INFO", itemInfo);
    let setItemInfo = itemInfo.filter(function (res: { deletedAt: any }) {
      return res.deletedAt === null;
    });
    reservationItemData.value = setItemInfo;
    //console.log("PARSE FILES");
    _surveyConfig.data?.surveySchema.forEach(async function (ss: { [x: string]: any; type: string }) {
      if (ss.type === "reservation") {
        ss["reservationItemData"] = setItemInfo;
      }

    });

    //console.log("BEFORE RECEIVE DATA");

    if (_surveyConfig && _surveyConfig.result === "OK") {
      //console.log("Recursive Call", _surveyConfig.data);
      surveyConfig.value = removePreviousVaccineItemIfFirstVaccination(_surveyConfig.data);
      //surveyConfig.value = cloneDeep(_surveyConfig.data);
      if (_surveyInput && _surveyInput.result === "OK") {
        surveyInput.value = _surveyInput.data;
        //console.log("surveyInput.value FINAL", surveyInput.value);
      }
      //console.log("surveyConfig_FINAL", surveyConfig.value);
    } else {
      // 403エラーの場合はログイン画面に遷移
      if(_surveyConfig.status === 403) {
        triggerLogin($q, router, route);
      }
      error.value = japaneseMessageConvert(_surveyConfig);
    }
    // linkbuttonがあるか確認
    //console.log("surveyConfig.value", surveyConfig.value);
    if (surveyConfig.value && surveyConfig.value.surveySchema) {
      //console.log("surveyConfig.value.surveySchema", surveyConfig.value.surveySchema);
      for (let item of surveyConfig.value.surveySchema) {
        if (item.type === "linkbutton") {
          hasLinkButton.value = true;
        }
      }
    }

    //console.log("linkbutton", hasLinkButton.value); 

    //Set the selectedSurveyResults
    if (!mainStore.selectedSurveyResults && surveyInput.value.length > 0) {
      mainStore.selectedSurveyResults = {
        partitionKey: cloneDeep(surveyInput.value[0].partitionKey),
        data: cloneDeep(surveyInput.value),
      };
    }

    //console.log("selectedSurveyResults", mainStore.selectedSurveyResults);

    mainStore.realSurveyConfig = cloneDeep(surveyConfig.value);
    mainStore.surveyInput = surveyInput.value;



    // 決済設定情報を取得
    const doesUsePayment = _surveyConfig.data?.usePayment === 1;
    if (doesUsePayment) {
      //console.log("surveyUsePayment", doesUsePayment);
      if (import.meta.env.VITE_USE_PAYMENT !== '1') {
        throw '現在決済を受け付けておりません。'
      }
      //console.log("systemUsePayment", doesUsePayment);
      await handleGetPaymentSettings(originalSurveyConfigResponse.data);
    }
  } catch (err) {
    console.error("Error in SurveyPage.initSurvey", err);
    error.value = err;
    if (err.response && err.response.status === 403) {
      if (err.response.data.error === "invalid_request" && err.response.data.error_description === "IdToken expired.") {
        // debug use only
        // デバッグ用
        localStorage.clear();
        sessionStorage.clear();
        router.push({ name: "Login" });
      }
    }
  } finally {
    //console.log("initSurvey done");
    isLoading.value = false;
  }
  isLoading.value = false;
  //console.log("initSurvey done (out)");
};
const handleGetPaymentSettings = async (surveyConfig) => {
  await getTaxRateSettings();
  if (!hasCategory.value) {
    const selectProductsItem = surveyConfig.surveySchema.find(item => item.type === 'selectProducts');
    const serviceKey = selectProductsItem.input;
    await getPaymentServcie(serviceKey);
  }
  // Set paymentResult.value
  if (paymentOrderInputFromSurvey.value) {
    // reroute from calendar back
    const paymentResultCopy = paymentResult.value || {};
    const payload = { ...paymentResultCopy, ...paymentOrderInputFromSurvey.value };
    setPaymentResult(payload);
  } else if (route.query.partitionKey) {
    // reroute from survey confirm
    await getPaymentResult(route.query.partitionKey);
  }
};
const hendleSetPaymentInfo = async (categoryId: string | null, itemKey: string) => {
  if (!categoryId || categoryId === '') {
    // console.log('IDの入力値がないため却下')
    return;
  };
  
  if (mainStore.surveyInputRegisterPayment && mainStore.surveyInputRegisterPayment[itemKey] === categoryId) {
    // console.log('現在の入力値と同じため却下');
    return;
  }

  await inputForm.value.getFormDataJson(undefined, true).then((formData) => {
    let inputs = {};
    formData?.surveyResults?.forEach((elem) => {
      inputs[elem.itemKey] = elem.value;
    });
    
    if (!inputs[itemKey]) inputs[itemKey] = categoryId;
    // console.log(inputs)

    mainStore.surveyInputRegisterPayment = inputs;
    configJson.value.inputs = inputs;
  });

  if (!categoryId) {
    resetPaymentInfo();
    return;
  }

  categoryId = categoryId.split('_')[0];

  if (!reservationPaymentServiceMap.value[categoryId]) {
    await getReservationPaymentService(categoryId);
  }
  const service = reservationPaymentServiceMap.value[categoryId];
  setPaymentService(service || null);
  setProductList(service ? service.reservationPaymentItems : []);
};
const removePreviousVaccineItemIfFirstVaccination = (surveyConfig: any): any => {
  const surveyConfigCopy = cloneDeep(surveyConfig);
  const { surveySchema } = surveyConfigCopy;
  const countVaccinesInput = surveySchema.find((item) => item.type === "countVaccines")?.default;
  const isFirstVaccination = countVaccinesInput != null ? countVaccinesInput === 1 : false;
  if (!isFirstVaccination) {
    return surveyConfigCopy;
  }

  surveyConfigCopy.surveySchema = surveySchema.filter((item) => {
    const previousVaccineItemTypes = ["previousVaccineMaker", "previousVaccineDate"];
    return !previousVaccineItemTypes.includes(item.type);
  });
  return surveyConfigCopy;
};
const pushSurveyAnswer = async (): Promise<void> => {
  try {
    isPushing.value = true;
    pushError.value = null;
    const session = await Auth.currentSession().catch((e) => {
      // 未ログイン
      return null;
    });

    // NOTE: 決済帳票かつ決済がされていないかつ現金支払いではない場合で購入金額が1円以上の場合
    if (
      doesUsePayment.value &&
      !isFinishedPayment.value &&
      paymentOrderData.value?.selectedPayType !== PAY_TYPES.CASH &&
      paymentOrderData.value?.amount > 0
    ) {
      await onSubmitSelectProductForm(); 
      return;
    }

    surveyResult.value.isLiffMode = true;

    const subData = {};
    if (inEditingAppendingMode.value && route.query.partitionKey) {
      //追加型なら、古いパーティションキーの物「取り消し」に設定して
      Object.assign(subData, {
        oldPartitionKey: route.query.partitionKey,
        subData: {
          checkStatus: "取り消し",
        },
      });
    }
    // NOTE: 現金決済もしくは購入金額が0円の場合かつ前回の決済結果が存在しない場合は決済結果も一緒に作成する(決済はしない)
    const paymentData = (paymentOrderData.value?.selectedPayType === PAY_TYPES.CASH || paymentOrderData.value?.amount === 0) && isEmpty(paymentResult.value)
      ? paymentOrderData.value
      : null;

    // 添付ファイルアップロード
    let fileData = await getFileData();
    if (fileData) {
      await uploadFile(fileData, surveyResult.value.userId, surveyResult.value.surveyId);
    }

    //console.log(`surveyResult.value`, surveyResult.value);
    

    const _pushRes = await PutSurveyResult(surveyResult.value, surveyConfig.value, session, subData, paymentData);

    if (_pushRes.result === "OK") {
      pushDone.value = true;
      $q.notify({
        message: "ブラウザを閉じてください。",
        color: "positive",
        position: "bottom",
        timeout: 2000,
        actions: [{ label: "閉じる", color: "white", handler: () => { } }],

      });
      dialog.value = false;
      isLatest.value = true;
      if (_pushRes.answercode) {
        answerCode.value = _pushRes.answercode;
        isShowAnswerCode.value = true;
      } else {
        isShowAnswerCode.value = false;
      }
    } else {

      // 403エラーの場合はログイン画面に遷移
      if(_pushRes.status === 403) {
        triggerLogin($q, router, route);
      }

      const sysErr = "不明なエラーです。";
      const devErrMsg =
        "登録申請された帳票の処理ができません。<br />\
            内容が現在のバージョンではご利用になれないか開発中の可能性があります。<br />\
            一定期間経過後も改善されないようであれば管理者へお問い合わせください。";
      let hasGrpHeadOrDt = hasType("groupheader") || hasType("date");
      let jpMsg =
        japaneseMessageConvert(_pushRes.errorMessage) || "不明なエラーです。管理者にお問い合わせください。";
      let msg = jpMsg ? jpMsg : hasGrpHeadOrDt ? devErrMsg : sysErr;

      if (_pushRes.response) {
        if (
          _pushRes.response.status == 409 &&
          _pushRes.response.data &&
          _pushRes.response.data.code &&
          _pushRes.response.data.code === TEMPORARY_LOCKED
        ) {
          msg = MULTIPLE_REQUEST_MESSAGE;
        }
      }

      // バリデーションエラーがある場合は
      validErrors.value = _pushRes.validatorErrorMessage;

      // FE バリデーションエラーがある場合は
      if(_pushRes.requiredFields) {
        validErrors.value = _pushRes.requiredFields;  
      }

      pushError.value = msg;
      if (_pushRes.needRefresh === true) {
        isLatest.value = false;
      }
    }
  } catch (error) {
    pushError.value = error;
    console.error("error", error);
  } finally {
    isPushing.value = false;
  }
  hasError.value = pushError.value !== null;
  //console.log("pushError.value", pushError.value);
};

const uploadFile = async (files: any, userId: string, surveyId: string) => {
  await files.forEach(async (file) => {
  if(file.alreadyUploaded) return;
  const newFile = file;
  //console.log("newFile", newFile);
    let params = {
      contentType: newFile.file.type,
      key: `public/resources/survey_results/${userId}/${surveyId}/${newFile.file.name}`,
      file: newFile.file,
    }
    //console.log(`params`, params);
    
    let result = await UploadFile(params);
    if (result?.error) {
      console.error(result);
    }
  });
};

const japaneseMessageConvert = (msg: string): any => {
  let japaneseMessagesList = {
    "no survey config.":
      "●タイムアウトしました。再度やり直してください。\n●何度もこの表示が出る場合は、本帳票が無効になっている可能性があります。管理者にお問い合わせください。",
    "Error: store.request failed with status code 404":
      "●タイムアウトしました。再度やり直してください。\n●何度もこの表示が出る場合は、本帳票が無効になっている可能性があります。管理者にお問い合わせください。",
    "Error: store.request failed with status code 403": TOKEN_EXPIRED_MESSAGE,
    '404': NOT_FOUND_MESSAGE,
    NotFound: NOT_FOUND_MESSAGE,
    "IdToken expired.": TOKEN_EXPIRED_MESSAGE,
  };
  if (!msg) {
    msg = "Error: store.request failed with status code 404";
  }

  let result = japaneseMessagesList[msg] || msg;
  return result;
};
const handleSurveyResults = async (): Promise<any> => {
  const formData = await inputForm.value.getFormData();
  fileData.value = await getFileData();

  //console.log("formData", formData);
  if (formData) {
    const surveyResults = [];
    const previousCheck = surveyInput.value && surveyInput.value.length > 0 ? surveyInput.value[0].check : "未対応";
    for (const [itemKey, input] of formData.entries()) {
      const type = getType(itemKey);
      //console.log('itemKey',itemKey,'input',input);
      let value = type === "date" && input === "null" ? null : input;
      value = value === "null" ? null : value;

      //console.log(`itemKey: ${itemKey}, type: ${type}, value: ${typeof value}`);
      
      if(isEmpty(value) || value === "undefined") {
        continue;
      }

      if (value) {
        if (itemKey === 'paymentData') {
          paymentOrderData.value = JSON.parse(value);
          if (paymentOrderData.value.details?.length === 0) {
            return;
          }
          continue;
        }
        if (value === 'attachedFiles') {
          // 添付ファイルがある場合　ファイルの数分surveyResultを作成
          let files = [] as any;
          //console.log(`fileData.value`, fileData.value);
          
          fileData.value?.forEach((file: any) => {
            files.push(`public/resources/survey_results/${lineUser.value.identities[0].userId}/${route.params.id}/${file.file.name}`);
          });

          //console.log(`files`, files);
          

          surveyResults.push({
            itemKey: itemKey,
            check: previousCheck,
            title: getTitle(itemKey),
            value,
            files,
          });
        }
        else {
          surveyResults.push({
            itemKey: itemKey,
            check: previousCheck,
            title: getTitle(itemKey),
            value,
          });
        }
      }
    }

    return {
      surveyId: route.params.id,
      userId: lineUser.value.identities[0].userId,
      surveyResults,
    };
  }
  return null;
};
const getFileData = async () : Promise<File[] | undefined> => {
  const jsonData = await inputForm.value.getFormDataJson();
  return jsonData?.fileData ?? undefined;
};

const getCalendarInfo = async (categoryId: any): Promise<any> => {
  return await GetCalendarInfo(categoryId);
};
const checkCalendarIsOff = async (categoryId: any): Promise<boolean> => {
  let calendarInfo = await getCalendarInfo(categoryId);
  return calendarInfo && calendarInfo.calendarOff && calendarInfo.calendarOff === 1;
};
const checkCalendarCanBeReserve = async (categoryId: any): Promise<boolean> => {
  let calendarInfo = await getCalendarInfo(categoryId);
  let _canBeReserve = true;
  if (calendarInfo && (!calendarInfo.calendarOff || (calendarInfo.calendarOff && calendarInfo.calendarOff !== 1))) {
    _canBeReserve = await calendarCanBeReserve(calendarInfo.sortKey);
  }
  return _canBeReserve;
};
const closeWindow = (): void => {
  liff.closeWindow();
};

const productList = ref(paymentStore.productList);

watch(
  () => paymentStore.productList,
  (newValue) => {
    productList.value = newValue;
  }
);

// computed
const inEditingAppendingMode = computed(() => mainStore.inEditingAppendingMode);
const calendarUseParamsJSON = computed(() => surveyResultsStore.calendarUseParamsJSON);
const realSurveyConfig = computed(() => mainStore.realSurveyConfig);
const applyingTaxRate = computed(() => paymentStore.applyingTaxRate);
const isFethingTaxRateSettings = computed(() => paymentStore.isFethingTaxRateSettings);
const paymentService = computed(() => paymentStore.paymentService);
const isFetchingPaymentService = computed(() => paymentStore.isFetchingPaymentService);
const reservationPaymentServiceMap = computed(() => paymentStore.reservationPaymentServiceMap);
const paymentResult = computed(() => paymentStore.paymentResult);
const isFethingPaymentResult = computed(() => paymentStore.isFethingPaymentResult);
const paymentOrderInputFromSurvey = computed(() => paymentStore.paymentOrderInputFromSurvey);

const configJson = ref();
watch(
  () => surveyConfig.value,
  (newVal) => {
    if (Object.keys(newVal).length > 0) {
      let isSubmitted = surveyInput.value ? surveyInput.value.length > 0 : false;
      let inputs = []
      if (surveyInput.value.length > 0) {
        inputs = newVal.surveySchema.reduce(
        (map: { [x: string]: any }, obj: { itemKey: string | number; type: string }) => {
          let _values = surveyInput.value
            .filter((item: { itemKey: any; value: any }) => item.itemKey === obj.itemKey && item.value != null)
            .map((item: any) => item.value);
          
          if (obj.type === "files") {
            _values = surveyInput.value
              .filter((item: any) => item.itemKey === obj.itemKey && item.files != null)
              .map((item: any) => item.files);
            map[obj.itemKey] = _values;
          } else if (obj.type !== "checkboxes") {
            _values = _values.join("");
            if (_values) {
              map[obj.itemKey] = _values;
            }
          } else {
            //  if (_values.length > 0) {
              map[obj.itemKey] = _values;
            //  }
          }
          return map;
        },
        {}
      );
      }
       
      if (Object.keys(inputs).length > 0) {
        configJson.value = {
          ...newVal,
          inputs: inputs,
        };
      } else {
        if (isSubmitted) {
          configJson.value = { ...newVal, inputs: [] };
        } else {
          configJson.value = { ...newVal };
        }
      }
    } else {
      configJson.value = {};
    }
  },
  { immediate: true }
);

watch(
  () => surveyInput.value,
  async (newVal) => {
    if (newVal) {
      await updateFiles(newVal);
    }
  },
  { deep: true }
);

const updateFiles = async (inputs: any): Promise<any> => {
  let filesItem = inputs.find((item: { value: any }) => item.value === "attachedFiles");
  // update the files item
  
  if (filesItem) {
    let files = filesItem.files;
    // GetFileUrl from each file
    const mappedFiles = await Promise.all(
      files.map(async (file: any) => ({
        name: file?.substring(file.lastIndexOf("/") + 1),
        url: (await GetUploadFile(file)).data,
      }))
    );
    
    // update configJson.value.input
    Object.keys(configJson.value.inputs).forEach((key) => {
      if (key === filesItem.itemKey) {
        configJson.value.inputs[key] = mappedFiles;
      }
    });
    
  }
};

const configJsonWithoutAdminItem = computed(() => {
  if (Object.keys(configJson.value).length === 0) return {};
  const cloneConfigJson = cloneDeep(configJson.value);
  const surveySchema = cloneConfigJson.surveySchema.filter((item) => !item.isAdminItem);
  return {
    ...cloneConfigJson,
    surveySchema,
  };
});
const isShowCloseButton = computed(() => {
  return liff.isInClient();
});
const lineUserId = computed(() => {
  return lineUser.value.identities[0].userId;
});
const doesUsePayment = computed((): boolean => {
  return realSurveyConfig.value?.usePayment === 1;
});
const isFinishedPayment = computed((): boolean => {
  return isFinishedPaymentStatus(paymentResult.value?.status);
});
const hasCategory = computed((): boolean => {
  return realSurveyConfig.value
    ? realSurveyConfig.value.surveySchema.some(item => item.type === 'reservation')
    : false;
});
const isFetchingPaymentInfo = computed((): boolean => {
  return isFethingTaxRateSettings.value || isFetchingPaymentService.value || isFethingPaymentResult.value;
});
const textBtnSubmit = computed((): string => {
  return doesUsePayment.value && !isFinishedPayment.value ? '決済画面へ' : '送信';
});
const textBtnConfirm = computed((): string => {
  return doesUsePayment.value && !isFinishedPayment.value ? '進む' : '送信';
});
const textConfirm = computed((): string => {
  return doesUsePayment.value && !isFinishedPayment.value ? '以下の内容で購入手続きを進めてよろしいでしょうか' : '以下の内容を送信してよろしいでしょうか';
});

// watch
watch(() => dialog.value, (newValue) => {
  // If dialog is closed, hide error.value
  if (!newValue) {
    hasError.value = false;
  }
});
watch(() => pushDone.value, (state) => {
  // スクロール位置が下にある状態で完了メッセージ画面が表示される可能性があるため、送信処理が実行されたらScroll位置を0に戻す
  if (state) {
    window.scrollTo(0, 0);
  }
});

// hooks
onMounted(() => {
  // スクロール位置が下にある状態で描画される可能性があるため、Scroll位置を0に戻す
  window.scrollTo(0, 0);
});

watch(
  () => inputForm.value,
  (newVal) => {
    newVal?.getFormData();
  }
);

watch(
  () => route.query.error,
  (newError) => {
    if (newError?.toUpperCase() === 'ACCESS_DENIED') {

      if (liff.isInClient()) {
        liff.closeWindow();
      }
    }
  },
  { immediate: true }
);

onBeforeMount(async () => {
  //console.log("TRIGGERED onBeforeMount");
  if (route.query.error?.toUpperCase() === 'ACCESS_DENIED') {

    // Close the LIFF window
    if (liff.isInClient()) {
      liff.closeWindow();
    }
    return;
  }
  const session = await Auth.currentSession().catch(e => {
    // 未ログイン
    return null;
  });
  if (!window.sessionStorage.getItem('liffAccessToken')) {
    // alert("auth error.value"); // /loginを通ってきてればあるはず
    // 5秒でログインページにリダイレクト
    //triggerLogin($q, router, route);
  }
  const userInfo = session?.getIdToken().payload;

  if(!userInfo) {
    triggerLogin($q, router, route, true);
    return;
  }

  lineUser.value = !isEmpty(userInfo) ? userInfo : lineUser.value;
  //console.log("lineUser", lineUser.value);
  await initSurvey(session);
  isLoading.value = false;
});

</script>

<style scoped>
.line-break {
  white-space: pre-line;
}

.userInfoTitle {
  border-radius: 0px;
  border-top-left-radius: 0.8rem;
  border-bottom-right-radius: 0.8rem;
}
</style>

<style lang="less" scoped>
.bg {
  background-color: white;
}

.form-design-code .CodeMirror {
  height: 80vh !important;
}

.confirmation-table {
  border: 1px solid rgba(0, 0, 0, 0.12);

  .confirm-title {
    background: rgba(0, 0, 0, 0.04);
  }
}
</style>
