<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.results-display-card {
  padding: 0.5em;
}
.confirm-details-title {
  font-weight: bold;
}
</style>
<template>
  <div>
    <q-page-container>
      <q-row>
        <q-col cols="12" sm="10" md="6" xl="4" class="mx-auto">
          <q-card shaped elevation="1" class="mx-auto">
            <q-list>
              <q-item>
                <q-item-section avatar>
                  <q-img v-if="lineUser && lineUser.picture" :src="lineUser.picture"></q-img>
                  <q-avatar v-else color="primary">
                    <q-icon dark name="mdi-account-circle"></q-icon>
                  </q-avatar>
                </q-item-section>

                <q-item-section>
                  <q-item-label>
                    <span v-if="lineUser">{{ lineUser.name }}</span> 様
                  </q-item-label>
                </q-item-section>

                <q-item-section> </q-item-section>
              </q-item>
            </q-list>
          </q-card>
          <q-row>
            <q-col align="center" cols="12">
              <span class="confirm-details-title">現在の登録情報</span>
            </q-col>
          </q-row>
          <q-card v-if="sortedSurveyResults" class="mb-8 results-display-card">
            <div v-for="surveyResult in sortedSurveyResults" :key="surveyResult.itemKey">
              <div v-if="itemIsNotAdmin(surveyResult)">
                <div
                  v-if="
                    !specialDisplayValues.includes(getSurveyConfigTitle(surveyResult.itemKey)) &&
                    getSurveyTypeByKey(surveyResult.itemKey) != 'reservation'
                  "
                >
                  {{ getSurveyConfigTitle(surveyResult.itemKey) }} : {{ surveyResult.value }}
                </div>
                <div v-if="getSurveyTypeByKey(surveyResult.itemKey) === 'reservation'">
                  <div v-for="(category, categoryIndex) in categoryDisplayLables" :key="categoryIndex">
                    {{ category }}：{{ getTagDisplayFromCategoryId(surveyResult.value, String(categoryIndex + 1)) }}
                  </div>
                </div>
              </div>
            </div>
            <p></p>
            <div>
              <span>{{ reservationDate }} : {{ attemptToGetReservationDate() }}</span>
            </div>
            <div>
              <span>{{ reservationTime }} : {{ attemptToGetReservationTime() }}</span>
            </div>
          </q-card>

          <BackButtonMessage />

          <q-row>
            <q-col align="center" cols="12">
              <q-btn class="primary" @click="returnToConfirmList">予約一覧へ戻る</q-btn>
            </q-col>
          </q-row>
        </q-col>
      </q-row>
    </q-page-container>
  </div>
</template>

<script setup lang="ts">
import {ref,  computed, onBeforeMount } from 'vue';

import { useRoute, useRouter } from 'vue-router';

import { useMainStore } from '@/stores';

const mainStore = useMainStore();

// old imports
// 旧インポート
import { cloneDeep } from "lodash";
import moment from "moment";
import BackButtonMessage from "@/components/BackButtonMessage.vue";

const router = useRouter();
const route = useRoute();

// data
const sortedSurveyResults = ref<any>(null);
const specialDisplayValues = ref<any>(["予約日", "予約時間"]);
const reservationTime = ref<string>("予約時間");
const reservationDate = ref<string>("予約日");
const japaneseDayMapping = ref<any>({
        1: "月",
        2: "火",
        3: "水",
        4: "木",
        5: "金",
        6: "土",
        0: "日",
      });

// methods
const returnToConfirmList = (): void => {
      router.push({ name: "ConfirmList" });
    };
const itemIsNotAdmin = (surveyResult: { itemKey: any; }): boolean => {
      var schemaItem = mainStore.surveyConfig.surveySchema.find((obj: { itemKey: any; }) => obj.itemKey == surveyResult.itemKey);
      return schemaItem ? !schemaItem.isAdminItem : false;
    };
const getTagDisplayFromCategoryId = (categoryId: string, tagNumber: string): string => {
      var result = "不明";
      try {
        var actualCategoryId = categoryId.split("|")[0];
        if (actualCategoryId in mainStore.categoryValues) {
          result = mainStore.categoryValues[actualCategoryId]["tag" + tagNumber];
        }
      } catch (err) {
        console.error(err);
      }

      return result;
    };
const attemptToGetReservationTime = (): string => {
      var result = "不明";
      try {
        var foundReservation = null;
        for (var question of sortedSurveyResults.value) {
          if (getSurveyTypeByKey(question.itemKey) == "reservation") {
            foundReservation = question;
          }
        }
        if (foundReservation) {
          var parsedCategory = foundReservation.value.split("|");
          var categoryId = parsedCategory[0];
          var comaId = parsedCategory[2];
          if (
            categoryId in mainStore.calendarSettings &&
            comaId in mainStore.calendarSettings[categoryId].comaList
          )
            var momentDate = moment(mainStore.calendarSettings[categoryId].comaList[comaId].start, "hmm");
          if ((momentDate as any)._isValid) {
            result = momentDate.format("HH:mm");
          }
        }
      } catch (err) {
        console.error(err);
      }
      return result;
    };
const attemptToGetReservationDate = (): string => {
      var result = "不明";
      try {
        var foundReservation = null;
        for (var question of sortedSurveyResults.value) {
          if (getSurveyTypeByKey(question.itemKey) == "reservation") {
            foundReservation = question;
          }
        }
        if (foundReservation) {
          var parsedCategory = foundReservation.value.split("|");
          var date = parsedCategory[1];

          result = getFormattedDate(date);
        }
      } catch (err) {
        console.error(err);
      }
      return result;
    };
const getFormattedDate = (date: moment.MomentInput): string => {
      try {
        var momentDate = moment(date);
        return (
          momentDate.month() +
          1 +
          "月" +
          momentDate.date() +
          "日" +
          "（" +
          japaneseDayMapping.value[momentDate.day()] +
          "）"
        );
      } catch (error) {
        console.error(error);
        return "不明日付形";
      }
    };
const getSurveyTypeByKey = (itemKeyToSearch: any): any => {
      for (let i = 0; i < mainStore.surveyConfig.surveySchema.length; i++) {
        const item = mainStore.surveyConfig.surveySchema[i];
        if (itemKeyToSearch == item.itemKey) {
          return item.type;
        }
      }
      return "不明";
    };
const getSurveyValueByTitle = (titleToSearch: any): any => {
      let itemKey = null;
      for (let i = 0; i < mainStore.surveyConfig.surveySchema.length; i++) {
        const item = mainStore.surveyConfig.surveySchema[i];
        if (titleToSearch == item.title) {
          itemKey = item.itemKey;
        }
      }
      if (itemKey == null) {
        return "不明";
      }
      for (let i = 0; i < mainStore.selectedSurveyResults.data.length; i++) {
        const item = mainStore.selectedSurveyResults.data[i];
        if (itemKey == item.itemKey) {
          return item.value;
        }
      }
      return "不明";
    };
const sortSurveyResults = (): void => {
      let tempResult = [];
      for (let i = 0; i < mainStore.surveyConfig.surveySchema.length; i++) {
        const keyToSearch = mainStore.surveyConfig.surveySchema[i].itemKey;
        var result = mainStore.selectedSurveyResults.data.filter((item: { itemKey: any; }) => {
          return item.itemKey == keyToSearch;
        });
        if (result.length > 0) {
          tempResult.push(result[0]);
        }
      }
      sortedSurveyResults.value = cloneDeep(tempResult);
    };
const getSurveyConfigTitle = (itemKey: any): any => {
      for (let i = 0; i < mainStore.surveyConfig.surveySchema.length; i++) {
        const item = mainStore.surveyConfig.surveySchema[i];
        if (itemKey == item.itemKey) {
          return item.title;
        }
      }
      return "";
    };

// computed
const lineUser = computed((): any => {
      return mainStore.lineUser.value;
    });
const categoryDisplayLables: any = computed((): any => {
      return mainStore.categoryLabels;
    });

// hooks

onBeforeMount(async () => {
  if (!mainStore.authSession || !mainStore.selectedSurveyResults) {
    router.replace("Confirm");
  }
  if (mainStore.surveyConfig && mainStore.selectedSurveyResults) {
    sortSurveyResults();
  }
});

</script>

<style lang="less">
.form-design-code .CodeMirror {
  height: 80vh !important;
}
.confirmation-table {
  border: 1px solid rgba(0, 0, 0, 0.12);
  .confirm-title {
    background: rgba(0, 0, 0, 0.04);
  }
}
</style>
