<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.results-display-card {
  padding: 0.5em;
}
.confirm-details-title {
  font-weight: bold;
}
</style>
<template>
  <div>
    <q-page-container>
      <q-row>
        <q-col cols="12" sm="10" md="6" xl="4" class="mx-auto">
          <q-card shaped elevation="1" class="mx-auto">
            <q-list>
              <q-item>
                <q-item-section avatar>
                  <q-img v-if="lineUser && lineUser.picture" :src="lineUser.picture"></q-img>
                  <q-avatar v-else color="primary">
                    <q-icon dark>mdi-account-circle</q-icon>
                  </q-avatar>
                </q-item-section>

                <q-item-section>
                  <q-item-label>
                    <span v-if="lineUser">{{ lineUser.name }}</span> 様
                  </q-item-label>
                </q-item-section>

                <q-item-section> </q-item-section>
              </q-item>
            </q-list>
          </q-card>
          <q-row>
            <q-col align="center" cols="12">
              <span class="confirm-details-title">現在の登録情報</span>
            </q-col>
          </q-row>
          <q-card v-if="sortedSurveyResults" class="mb-8 results-display-card">
            <div>
              <span>{{ sortedSurveyResults[0].partitionKey }}</span>
            </div>
            <div>
              <span>{{ sortedSurveyResults[0].answerCode }}</span>
            </div>
            <div>
              <span>{{ sortedSurveyResults[0].check }}</span>
            </div>
            <div v-for="surveyResult in sortedSurveyResults" :key="surveyResult.itemKey">
              <div v-if="!specialDisplayValues.includes(getSurveyConfigTitle(surveyResult.itemKey))">
                {{ getSurveyConfigTitle(surveyResult.itemKey) }} : {{ surveyResult.value }}
              </div>
            </div>
            <p></p>
            <div>
              <span>{{ reservationDate }} : {{ getFormattedDate(getSurveyValueByTitle(reservationDate)) }}</span>
            </div>
            <div>
              <span>{{ reservationTime }} : {{ getSurveyValueByTitle(reservationTime) }}</span>
            </div>
          </q-card>
          <q-row>
            <q-col align="center" cols="12">
              <q-btn class="primary" @click="cancelConfirm">キャンセルする</q-btn>
            </q-col>
          </q-row>
          <q-row>
            <q-col align="center" cols="12">
              <q-btn class="primary" @click="returnToConfirmList">戻る</q-btn>
            </q-col>
          </q-row>
        </q-col>
      </q-row>
    </q-page-container>
  </div>
</template>

<script setup lang="ts">
import { PropType, ref,  computed, onBeforeMount } from 'vue';

import { useRoute, useRouter } from 'vue-router';

import { useMainStore } from '@/stores';

// old imports
// 旧インポート
import { cloneDeep } from "lodash";
import moment from "moment";
import liff from "@line/liff";
import {
  // GetSurveyResultByFormData,
  PutSurveyResult,
} from "@/services/surveys.service";
import { Auth } from "../services/auth.service";

const mainStore = useMainStore();

// emits 
const emits = defineEmits<{
  (event: 'close'): void;
}>();
const router = useRouter();
const route = useRoute();

// props
const props = defineProps({
  close: Function
});

// data
const sortedSurveyResults = ref(null);
const surveyConfig = ref<string>(null);
const specialDisplayValues = ref<any>(["予約日", "予約時間"]);
const reservationTime = ref<string>("予約時間");
const check = ref<string>("check");
const reservationDate = ref<string>("予約日");
const japaneseDayMapping = ref<any>({
        1: "月",
        2: "火",
        3: "水",
        4: "木",
        5: "金",
        6: "土",
        0: "日",
      });

// methods
const returnToConfirmList = (): void => {
      router.push({ name: "ConfirmList" });
    };
const cancelConfirm = (): void => {
      //Update Status to NG
      updateStatus();

      liff.closeWindow();
      emits("close");
    };
const updateStatus = async (): Promise<void> => {
      const session = await Auth.currentSession().catch((e) => {
        return null;
      });

      let config_data = mainStore.surveyConfig.value;
      config_data.isAppending.value = false;

      let survey_result = mainStore.selectedSurveyResults;
      for (let i = 0; i < survey_result.data.length; i++) {
        survey_result.data[i]["check.value"] = "NG";
      }

      var selected_survey = {
        surveyId: config_data.surveyId,
        userId: mainStore.lineUser.value.identities[0].userId,
        surveyResults: survey_result.data,
        updateExist: true,
      };

      let _pushRes = await PutSurveyResult(selected_survey, config_data, session);

      if (_pushRes.result === "OK") {
        return;
      } else {
        const sysErr = "不明なエラーです。";
        const devErrMsg =
          "statusの処理ができません。<br />\
            内容が現在のバージョンではご利用になれないか開発中の可能性があります。<br />\
            一定期間経過後も改善されないようであれば管理者へお問い合わせください。";

        // None of this is used anywhere else?
        // これは他の場所で使用されていませんか？
/*         let hasGrpHeadOrDt = hasType("groupheader") || hasType("date");
        let jpMsg = japaneseMessageConvert(_pushRes.errorMessage);
        let msg = jpMsg ? jpMsg : hasGrpHeadOrDt ? devErrMsg : sysErr;
        pushError = msg;
        if (_pushRes.needRefresh === true) {
          isLastest = false;
        } */
      }
    };
const getFormattedDate = (date: moment.MomentInput): string => {
      try {
        var momentDate = moment(date);
        return (
          momentDate.month() +
          1 +
          "月" +
          momentDate.date() +
          "日" +
          "（" +
          japaneseDayMapping.value[momentDate.day()] +
          "）"
        );
      } catch (error) {
        console.error(error);
        return "不明日付形";
      }
    };
const getSurveyValueByTitle = (titleToSearch: any): any => {
      let itemKey = null;
      for (let i = 0; i < mainStore.surveyConfig.value.surveySchema.length; i++) {
        const item = mainStore.surveyConfig.value.surveySchema[i];
        if (titleToSearch == item.title) {
          itemKey = item.itemKey;
        }
      }
      if (itemKey == null) {
        return "不明";
      }
      for (let i = 0; i < mainStore.selectedSurveyResults.data.length; i++) {
        const item = mainStore.selectedSurveyResults.data[i];
        if (itemKey == item.itemKey) {
          return item.value;
        }
      }
      return "不明";
    };
const sortSurveyResults = (): void => {
      let tempResult = [];
      for (let i = 0; i < mainStore.surveyConfig.value.surveySchema.length; i++) {
        const keyToSearch = mainStore.surveyConfig.value.surveySchema[i].itemKey;
        var result = mainStore.selectedSurveyResults.data.filter((item: { itemKey: any; }) => {
          return item.itemKey == keyToSearch;
        });
        if (result.length > 0) {
          tempResult.push(result[0]);
        }
      }
      sortedSurveyResults.value = cloneDeep(tempResult);
    };
const getSurveyConfigTitle = (itemKey: any): any => {
      for (let i = 0; i < mainStore.surveyConfig.value.surveySchema.length; i++) {
        const item = mainStore.surveyConfig.value.surveySchema[i];
        if (itemKey == item.itemKey) {
          return item.title;
        }
      }
      return "";
    };

// computed
const lineUser = computed((): any => {
      return mainStore.lineUser.value;
    });

// hooks

onBeforeMount(async () => {
  const session = await Auth.currentSession().catch(e => {
    // 未ログイン
    return null;
  });
  if (!session) {
    // alert("auth error"); // /loginを通ってきてればあるはず
    window.sessionStorage.setItem("liffState", route.path);
    router.push({
      name: "Login"
    });
    return;
  }
  if (!mainStore.authSession || !mainStore.selectedSurveyResults) {
    router.replace("Confirm");
  }
  if (mainStore.surveyConfig.value && mainStore.selectedSurveyResults) {
    sortSurveyResults();
  }
});

</script>

<style lang="less">
.form-design-code .CodeMirror {
  height: 80vh !important;
}
.confirmation-table {
  border: 1px solid rgba(0, 0, 0, 0.12);
  .confirm-title {
    background: rgba(0, 0, 0, 0.04);
  }
}
</style>
