<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div>
    <!-- loading -->
    <div v-if="isLoading" class="fill-height" align-content="center" justify="center" style="height: 90vh">
      <SurveyLoading />
    </div>
    <!-- contents -->
    <q-row v-else>
      <q-col cols="12" sm="10" md="6" xl="4" class="mx-auto">
        <!-- login user info -->
        <q-card shaped class="q-mx-auto userInfoTitle">
          <q-list>
            <q-item>
              <q-item-section avatar>
                <q-avatar v-if="lineUser && lineUser.picture">
                  <q-img :src="lineUser.picture"></q-img>
                </q-avatar>
                <q-avatar v-else color="primary">
                  <q-icon dark name="mdi-account-circle"></q-icon>
                </q-avatar>
              </q-item-section>

              <q-item-section>
                <q-item-label>
                  <span v-if="lineUser">{{ lineUser.name }}</span> 様
                </q-item-label>
              </q-item-section>

              <q-item-section> </q-item-section>
            </q-item>
          </q-list>
        </q-card>
        <!-- error -->
        <q-card class="text-center q-pa-md" flat v-if="isError">
          <div class="text-negative text-weight-bold">エラー</div>
          <div v-for="(item, index) in errors" :key="index">
            {{ item }}
          </div>
        </q-card>
        <!-- receipt -->
        <template v-else>
            <div class="q-pb-sm q-pt-lg text-center text-weight-bold text-h4">領収書</div>
          <q-card class="self-center q-ma-md">
            <div class="q-pa-md">
              <q-row>
                <q-col class="label q-pa-sm">宛名</q-col>
              </q-row>

              <q-row><q-separator /></q-row>

              <q-row>
                <q-col>
                  <div class="label q-pt-sm">取引年月日</div>
                  <div >{{ formatDate(receiptData.completedDate || receiptData.createdAt) }}</div>
                </q-col>
              </q-row>
              <q-row>
                <q-col>
                  <div class="label column q-pt-sm">
                    <span>受付番号</span>
                    <q-btn color="secondary" outlined elevation="0" class="q-pa-xs" height="24" style="font-size:12px; width: 120px"
                      @click="onCopy(receiptData.orderId)">受付番号をコピー</q-btn>
                  </div>
                  <div>{{ receiptData.orderId }}</div>
                </q-col>
              </q-row>
              <q-row>
                <q-col>
                  <div class="label q-pt-sm">取引内容</div>
                  <div>{{ receiptData.serviceName }}</div>
                </q-col>
              </q-row>
              <q-row>
                <q-col>
                  <div class="label q-pt-sm">支払方法</div>
                  <div>{{ payMethodText }}</div>
                </q-col>
              </q-row>

              <q-separator class="q-my-md" />

              <q-row>
                <q-col class="label">商品明細</q-col>
              </q-row>
                <q-row>
                <q-item v-for="(detail, index) in receiptData.details" :key="index" class="q-px-none full-width">
                  <q-item-label style="width: 120px">{{ detail.productName }}</q-item-label>
                  <div class="text-right full-width">
                  <span>{{ numberToLocaleString(detail.amount) }}</span>
                  <span class="q-pl-sm">円</span>
                  </div>
                </q-item>

                <q-item class="q-px-none full-width">
                  <q-item-label style="width: 120px">小計</q-item-label>
                  <div class="text-right full-width">
                  <span>{{ subTotal }}</span>
                  <span class="q-pl-sm">円</span>
                  </div>
                </q-item>

                <q-item class="q-px-none full-width">
                  <q-item-label style="width: 120px">{{ taxText }}</q-item-label>
                  <div class="text-right full-width">
                  <span>{{ numberToLocaleString(receiptData.tax) }}</span>
                  <span class="q-pl-sm">円</span>
                  </div>
                </q-item>

                <q-item class="q-px-none full-width">
                  <q-item-label style="width: 120px">合計金額</q-item-label>
                  <div class="text-right full-width">
                  <span>{{ numberToLocaleString(receiptData.amount) }}</span>
                  <span class="q-pl-sm">円</span>
                  </div>
                </q-item>
                </q-row>

              <q-separator class="q-my-md" />

              <q-row class="q-mt-sm">
                <q-col>{{ receiptData.receiptCreatorName }}</q-col>
              </q-row>
              <q-row class="q-mt-sm q-pl-md">
                <q-col>{{ receiptData.receiptDisplayAddress }}</q-col>
              </q-row>
            </div>
          </q-card>
        </template>
      </q-col>
    </q-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeMount } from 'vue';

import { useRoute, useRouter } from 'vue-router';

// old imports
// 旧インポート
import { Auth } from "@/services/auth.service";
import { GetReceipt } from "@/services/paymentResults.service";
import { getPayMethodText, numberToLocaleString, getSubTotal, getTaxTypeText } from '@/utils/paymentResultUtils';
import { DateTime } from 'luxon';
import SurveyLoading from '@/components/common/SurveyLoading.vue';

import { useQuasar } from 'quasar';

const $q = useQuasar();

const router = useRouter();
const route = useRoute();

// data
const lineUser = ref<any>({});
const receiptData = ref<any>({});
const errors = ref<any>([]);
const isLoading = ref<boolean>(false);
const isShowSnackbar = ref<boolean>(false);

// methods
const resetErrors = (): void => {
  errors.value = [];
};
const getReceiptData = async (): Promise<void> => {
  resetErrors();
  const { key } = route.query;
  if (!key) {
    errors.value.push('不正なURLです。');
    return;
  }

  const res = await GetReceipt(key as string);
  if (res.code !== 'success') {
    throw res.errorMessage;
  }
  receiptData.value = res.data;
};
const formatDate = (val: number): string => {
  return DateTime.fromSeconds(val).toFormat("yyyy年M月d日");
};

const onCopy = async (orderId: string): Promise<void> => {
  try {
    await navigator.clipboard.writeText(orderId);
    isShowSnackbar.value = true;
  } catch (e) {
    onCopyByExecCommand(orderId);
  }
};

const onCopyByExecCommand = (orderId: string): void => {
  navigator.clipboard.writeText(orderId).then(() => {
    $q.notify({
      message: "クリップボードにコピーしました。",
      color: "green-6",
      icon: "mdi-check",
    });
  }).catch(() => {
    console.error("クリップボードにコピーできませんでした。");
  });
};

// computed
const lineUserId = computed((): string => {
  return lineUser.value.identities[0].userId;
});
const isError = computed((): boolean => {
  return errors.value.length > 0;
});
const payMethodText = computed((): string => {
  return getPayMethodText(receiptData.value.payMethod);
});
const subTotal = computed((): string => {
  return receiptData.value.details ? getSubTotal(receiptData.value.details) : '';
});
const taxText = computed((): string => {
  const taxTypeText = getTaxTypeText(receiptData.value.taxType);
  return `消費税(${taxTypeText}) ${receiptData.value.taxRate}%`;
});

// hooks

onBeforeMount(async () => {
  isLoading.value = true;
  const session = await Auth.currentSession().catch(e => {
    // 未ログイン
    return null;
  });

  if (!session) {
    // /loginを通ってきてればあるはず
    const {
      key
    } = route.query;
    const path = key ? `${route.path}?key=${encodeURIComponent(key as string)}` : route.path;
    window.sessionStorage.setItem("liffState", path);
    router.push({
      name: "Login"
    });
    return;
  }
  const userInfo = session.getIdToken().payload;
  lineUser.value = userInfo;
  try {
    await getReceiptData();
  } catch (error) {
    errors.value.push(...['画面表示中にエラーが発生しました 。', error]);
  } finally {
    isLoading.value = false;
  }
});

</script>

<style scoped>
.label {
  color: #607D8B !important;
}

.receipt-info {
  letter-spacing: normal;
  min-height: 48px;
  outline: none;
  padding: 12px 16px;
  text-decoration: none;
}
</style>
