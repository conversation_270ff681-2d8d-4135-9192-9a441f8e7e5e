<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.survey-confirm-selector {
  padding-top: 0.5em;
  padding-left: 0.5em;
  padding-right: 0.5em;
}

.survey-selector-card {
  margin-top: 1em;
}
</style>
<template>
  <q-layout>
    <q-page-container v-if="!pushDone">
      <q-row>
        <q-col cols="12" sm="10" md="6" xl="4" class="mx-auto">
          <template v-if="!isLoading && !error">
            <q-card shaped elevation="1" class="mx-auto">
              <q-list>
                <q-item>
                  <q-item-section avatar>
                    <q-img v-if="lineUser && lineUser.picture" :src="lineUser.picture"></q-img>
                    <q-avatar v-else color="primary">
                      <q-icon dark>mdi-account-circle</q-icon>
                    </q-avatar>
                  </q-item-section>

                  <q-item-section>
                    <q-item-label>
                      <span v-if="lineUser">{{ lineUser.name }}</span> 様
                    </q-item-label>
                  </q-item-section>

                  <q-item-section> </q-item-section>
                </q-item>
              </q-list>
            </q-card>

            <q-card elevation="1" class="mx-auto survey-selector-card">
              <q-select class="survey-confirm-selector" v-model="selectedSurvey" :items="surveysToSelect"
                item-text="surveyTitle" item-value="surveyId" @change="setSelectedSurvey" single-line label="照会種別">
              </q-select>
            </q-card>

            <q-btn color="primary" dark :disabled="selectedSurvey === null" @click="onClickNext" block class="my-4">
              次へ
            </q-btn>
          </template>
          <div v-if="isLoading">
            <q-page-container style="height: 90vh">
              <q-row class="fill-height" align-content="center" justify="center">
                <q-col class="subtitle-1 text-center" cols="12">
                  情報を取得中です。
                  <br />しばらくおまちください。
                </q-col>
                <q-col cols="6">
                  <q-linear-progress color="green accent-4" indeterminate rounded height="6"></q-linear-progress>
                </q-col>
              </q-row>
            </q-page-container>
          </div>
          <div v-if="error">
            <q-row>
              <q-col cols="12" sm="12" md="12" xl="12" class="mx-auto">
                <q-card shaped elevation="1" class="mx-auto">
                  <q-list>
                    <q-item>
                      <q-item-section avatar>
                        <q-img v-if="lineUser && lineUser.picture" :src="lineUser.picture"></q-img>
                        <q-avatar v-else color="primary">
                          <q-icon dark>mdi-account-circle</q-icon>
                        </q-avatar>
                      </q-item-section>

                      <q-item-section>
                        <q-item-label>
                          <span v-if="lineUser">{{ lineUser.name }}</span> 様
                        </q-item-label>
                      </q-item-section>

                      <q-item-section> </q-item-section>
                    </q-item>
                  </q-list>
                </q-card>
              </q-col>
            </q-row>

            <q-row>
              <q-col cols="12" sm="12" md="12" xl="12" class="mx-auto">
                <Alert class="my-0" border="top" color="primary" colored-border elevation="2">
                  <div>
                    <div>
                      <h1>エラー</h1>
                      <div>画面表示に問題が発生しました。</div>
                      <div>エラー: {{ error }}</div>
                    </div>
                  </div>
                </Alert>
              </q-col>
            </q-row>
          </div>
        </q-col>
      </q-row>
    </q-page-container>

    <q-page-container v-else>
      <q-row align="center" justify="center">
        <q-col cols="12" sm="10" md="6" xl="4" class="mx-auto">
          <q-card shaped elevation="1">
            <q-list>
              <q-item>
                <q-item-section avatar>
                  <q-img v-if="lineUser && lineUser.picture" :src="lineUser.picture"></q-img>
                  <q-avatar v-else color="primary">
                    <q-icon dark>mdi-account-circle</q-icon>
                  </q-avatar>
                </q-item-section>

                <q-item-section>
                  <q-item-label>
                    <span v-if="lineUser">{{ lineUser.name }}</span> 様
                  </q-item-label>
                </q-item-section>

                <q-item-section> </q-item-section>
              </q-item>
            </q-list>
          </q-card>
        </q-col>
      </q-row>
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeMount } from 'vue';

import { useRoute, useRouter } from 'vue-router';

import { useMainStore } from '@/stores';

const mainStore = useMainStore();

// old imports
// 旧インポート
import { GetSurveyById, GetAllSearchableSurveys, GetSurveyResult, PutSurveyResult } from "@/services/surveys.service";
import { Auth } from "../services/auth.service";
import liff from "@line/liff";

const router = useRouter();
const route = useRoute();

// data
const inputForm = ref<any>(null);

const dialog = ref<boolean>(false);
const formData = ref<FormData>(new FormData());
const error = ref<any>(null);
const isLoading = ref<boolean>(false);
const lineUser = ref<any>(null);
const surveyConfig = ref<any>(null);
const surveyInput = ref<any>(null);
const surveyResult = ref<any>(null);
const isPushing = ref<boolean>(false);
const pushError = ref<any>(null);
const hasError = ref<boolean>(false);
const pushDone = ref<boolean>(false);
const isLastest = ref<boolean>(true);
const answerCode = ref<any>(null);
const isShowAnswerCode = ref<boolean>(false);
const surveysToSelect = ref<any>([]);
const selectedSurvey = ref<any>(null);

// methods
const setSelectedSurvey = (): void => {
  mainStore.surveyConfig.value = surveysToSelect.value.filter((a: { surveyId: any; }) => a.surveyId == selectedSurvey.value)[0];
};
const onClickNext = (): void => {
  router.push("ConfirmSearchForm");
};
const reloadPage = (): void => {
  window.location.reload();
};
const getTitle = (key: any): any => {
  let _item = configJson.value.surveySchema.find((obj: { itemKey: any; }) => obj.itemKey === key);
  return _item ? _item.title : "";
};
const getType = (key: any): any => {
  let _item = configJson.value.surveySchema.find((obj: { itemKey: any; }) => obj.itemKey === key);
  return _item ? _item.type : "";
};
const hasType = (findType: any): boolean => {
  let _item = configJson.value.surveySchema.find((obj: { type: any; }) => obj.type === findType);
  return _item ? true : false;
};
const initSurvey = async (authSession: any): Promise<void> => {
  try {
    mainStore.authSession = authSession;

    isLoading.value = true;

    let _surveys = await GetAllSearchableSurveys();

    if (_surveys && _surveys.result === "OK") {
      //surveysToSelect.value = _surveys.data.map(a => a.surveyTitle);
      surveysToSelect.value = _surveys.data;
    } else {
      error.value = japaneseMessageConvert(_surveys);
    }

    if (surveysToSelect.value.length == 1) {
      mainStore.surveyConfig.value = surveysToSelect.value[0];
      router.push("ConfirmSearchForm");
    }
  } catch (err) {
    error.value = err;
  } finally {
    isLoading.value = false;
  }
};
const pushSurveyAnswer = async (): Promise<void> => {
  try {
    isPushing.value = true;
    pushError.value = null;
    const session = await Auth.currentSession().catch((e) => {
      return null;
    });
    let _pushRes = await PutSurveyResult(surveyResult.value, surveyConfig.value, session);
    if (_pushRes.result === "OK") {
      pushDone.value = true;
      dialog.value = false;
      isLastest.value = true;
      if (_pushRes.answercode) {
        answerCode.value = _pushRes.answercode;
        isShowAnswerCode.value = true;
      } else {
        isShowAnswerCode.value = false;
      }
    } else {
      const sysErr = "不明なエラーです。";
      const devErrMsg =
        "登録申請された帳票の処理ができません。<br />\
            内容が現在のバージョンではご利用になれないか開発中の可能性があります。<br />\
            一定期間経過後も改善されないようであれば管理者へお問い合わせください。";
      let hasGrpHeadOrDt = hasType("groupheader") || hasType("date");
      let jpMsg = japaneseMessageConvert(_pushRes.errorMessage);
      let msg = jpMsg ? jpMsg : hasGrpHeadOrDt ? devErrMsg : sysErr;
      pushError.value = msg;
      if (_pushRes.needRefresh === true) {
        isLastest.value = false;
      }
    }
  } catch (error) {
    pushError.value = error.value;
  } finally {
    isPushing.value = false;
  }
  hasError.value = pushError.value !== null;
};
const japaneseMessageConvert = (msg: string | number): any => {
  let japaneseMessagesList = {
    "no survey config.":
      "●タイムアウトしました。再度やり直してください。\n●何度もこの表示が出る場合は、本帳票が無効になっている可能性があります。管理者にお問い合わせください。",
    "const Error = store.request failed with status code 404":
      "●タイムアウトしました。再度やり直してください。\n●何度もこの表示が出る場合は、本帳票が無効になっている可能性があります。管理者にお問い合わせください。",
  };
  let result = japaneseMessagesList[msg] || msg;
  return result;
};
const handleSurveyResults = (): any => {
  const formData = inputForm.value.getFormData();
  if (formData.value) {
    let surveyResults = [];
    for (var pair of formData.value.entries()) {
      surveyResults.push({
        itemKey: pair[0],
        title: getTitle(pair[0]),
        value: getType(pair[0]) === "date" && pair[1] === "null" ? null : pair[1],
      });
    }

    return {
      surveyId: route.params.id,
      userId: lineUser.value.identities[0].userId,
      surveyResults,
    };
  }
  return null;
};
const closeWindow = (): void => {
  liff.closeWindow();
};

// computed
const configJson = computed((): any => {
  if (surveyConfig.value) {
    let isSubmitted = surveyInput.value.length > 0;

    const inputs = surveyConfig.value.surveySchema.reduce((map: { [x: string]: any; }, obj: { itemKey: string | number; type: string; }) => {
      let _values = surveyInput.value
        .filter((item: { itemKey: any; value: any; }) => item.itemKey === obj.itemKey && item.value != null)
        .map((item: { value: any; }) => item.value);
      if (obj.type !== "checkboxes") {
        _values = _values.join("");
        if (_values) {
          map[obj.itemKey] = _values;
        }
      } else {
        if (_values.length > 0) {
          map[obj.itemKey] = _values;
        }
      }
      return map;
    }, {});
    if (Object.keys(inputs).length > 0) {
      return {
        ...surveyConfig.value,
        inputs: inputs,
      };
    } else {
      if (isSubmitted) {
        // empty submitted
        return { ...surveyConfig.value, inputs: [] };
      }
      return { ...surveyConfig.value };
    }
  } else {
    return {};
  }
});
const isShowCloseButton = computed((): boolean => {
  return liff.isInClient();
});

// hooks

onBeforeMount(async () => {
  const session = await Auth.currentSession().catch(e => {
    // 未ログイン
    return null;
  });
  if (!session) {
    // alert("auth error.value"); // /loginを通ってきてればあるはず
    window.sessionStorage.setItem("liffState", route.path);
    router.push({
      name: "Login"
    });
    return;
  }
  const userInfo = session.getIdToken().payload;
  lineUser.value = userInfo;
  mainStore.lineUser.value = userInfo;
  await initSurvey(session);
});

</script>

<style lang="less">
.form-design-code .CodeMirror {
  height: 80vh !important;
}

.confirmation-table {
  border: 1px solid rgba(0, 0, 0, 0.12);

  .confirm-title {
    background: rgba(0, 0, 0, 0.04);
  }
}
</style>
