<template>
  <div style="height: 200px;">
    <div class="full-height" v-if="surveyResultsHandle.length === 0">
      <div class="text-center flex flex-center column full-height">
        <q-icon name="mdi-information" size="100px" color="grey-5" />
        <div class="text-h6">回答がありません</div>
      </div>
    </div>
    <q-card v-else :value="all" :readonly="readonly" multiple tile>
      <div v-for="item in surveyResultsHandle" :key="item.itemKey">
        <q-expansion-item 
          expand-icon="mdi-check" expanded-icon="mdi-check" expand-icon-class="text-teal-5"
          :default-opened="true"
        >
          <template v-slot:header>
            <q-item-section class="full-width">
              <span>{{ item.title }}</span>
            </q-item-section>
          </template>
            <ul>
              <li v-for="input in item.value" :key="`${item.itemKey}_${input}`" class="q-ml-sm">
                <span v-if="item && item.type != undefined && item.type == 'reservation'">{{ item.display_label }}</span>
                <span v-else-if="item.type === 'files'">
                  添付ファイル
                  <span class="q-ml-md">{{ fileData?.length ?? 0 }}件</span>
                  <ul>
                    <li v-for="file in fileData" :key="file.name">
                      <span>{{ file.name }}</span>
                    </li>
                  </ul>
                </span>
                <span v-else class="line-break">{{ input }}</span>
              </li>
            </ul>
        </q-expansion-item>
        <q-separator />
      </div>
      <div v-if="hasPaymentData">
        <q-expansion-item expand-icon="mdi-check" expanded-icon="mdi-check" expand-icon-class="text-teal-5"
          :default-opened="true"
          >
          <template v-slot:header>
            <q-item-section class="full-width">
              <span>購入内容</span>
            </q-item-section>
          </template>
          <q-expansion-item default-opened dense>
            <SelectedProduct :order="paymentData" />
          </q-expansion-item>
        </q-expansion-item>
      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { PropType, ref, computed, watch, } from 'vue';



// old imports
// 旧インポート
//import type { PaymentData } from '@/utils/paymentResultUtils';
import SelectedProduct from './SelectedProduct.vue';
import { cloneDeep, isEmpty } from 'lodash';


// props
const props = defineProps({
  dataProp: { type: Object as PropType<any> },
  surveyConfig: { type: Object as PropType<any> },
  paymentData: { type: Object as PropType<any>, required: false },
  fileData: { type: Object as PropType<File[]>, required: false },
});

// data
const panel = ref<any>([-1]);
const readonly = ref<boolean>(false);
const dataLocal = ref<any>(null);

// methods
const getItem = (itemKey: any): string => {
  for (let item in props.surveyConfig.surveySchema) {
    if ((item as any).itemKey == itemKey) {
      return item;
    }
  }

  return null;
};

// computed
const all = computed((): any => {
  const itemLength = surveyResultsHandle.value.length + (hasPaymentData.value ? 1 : 0);
  return [...Array(itemLength).keys()].map((k, i) => i);
});
const surveyResultsHandle = computed((): any => {
  let _result = [];

  if (dataLocal.value && dataLocal.value.surveyResults.length > 0) {
    let _questionKey = null;
    dataLocal.value.surveyResults.forEach((obj, index) => {
      let value = obj.value;

      // 分類の処理
      for (let i = 0; i < props.surveyConfig.surveySchema.length; i++) {
        let currentItem = props.surveyConfig.surveySchema[i];

        if (currentItem.itemKey == obj.itemKey) {
          obj.type = currentItem.type;
          obj.display_label = value;

          if (currentItem.type === "reservation") {
            let display_label = "";

            {
              let bunrui_id = value;
              let bunrui_id_properties = { length: bunrui_id.length, index: -1 };
              bunrui_id_properties.index = bunrui_id.indexOf("|");
              if (bunrui_id_properties.index > 0) {
                bunrui_id = bunrui_id.substr(0, bunrui_id_properties.index);
              }

              if (bunrui_id != undefined && bunrui_id != null && bunrui_id != "") {
                for (let i = 0; i < currentItem.categories_tree.tree.length; i++) {
                  let daibunrui = currentItem.categories_tree.tree[i];
                  for (let chubunrui_index = 0; chubunrui_index < daibunrui.children.length; chubunrui_index++) {
                    let chubunrui = daibunrui.children[chubunrui_index];
                    if (chubunrui.id !== undefined) {
                      if (chubunrui.id === bunrui_id) {
                        display_label = daibunrui.name + " / " + chubunrui.name;
                      }
                    } else {
                      for (
                        let shobunrui_index = 0;
                        shobunrui_index < chubunrui.children.length;
                        shobunrui_index++
                      ) {
                        let shobunrui = chubunrui.children[shobunrui_index];
                        if (shobunrui.id !== undefined) {
                          if (shobunrui.id === bunrui_id) {
                            display_label = daibunrui.name + " / " + chubunrui.name + " / " + shobunrui.name;
                          }
                        }
                      }
                    }
                  }
                }
              }
            }

            obj.display_label = display_label;
          }
        }
      }

      if (obj.itemKey !== _questionKey) {
        _result.push({
          itemKey: obj.itemKey,
          title: obj.title,
          value: [value],
          type: obj.type,
          display_label: obj.display_label,
        });
        _questionKey = obj.itemKey;
      } else {
        let _findIndex = _result.findIndex((item) => item.itemKey === obj.itemKey);
        _result[_findIndex].value.push(value);
      }
    });
  }

  return _result.filter(r => r.type !== 'selectProducts');
});
const hasPaymentData = computed((): boolean => {
  return !isEmpty(props.paymentData);
});

// watch
watch(() => props.dataProp, (val) => {
  dataLocal.value = cloneDeep(val);
},
  { immediate: true, deep: true });
</script>

<style scoped>
.line-break {
  white-space: pre-line;
}
</style>