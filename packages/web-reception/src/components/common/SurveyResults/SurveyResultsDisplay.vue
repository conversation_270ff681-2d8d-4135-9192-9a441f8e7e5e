<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.date-display {
  font-weight: bold;
  display: flex;
  font-size: large;
  padding-bottom: 0.5em;
}
</style>

<template>
  <div>
    <div v-if="isLoading">
      <ContentLoading />
    </div>
    <div v-else>
      <div v-if="localError">
        <ErrorComponent :errorMessage="localError" />
      </div>
      <div v-else-if="allSurveyResults.length === 0">
        選んだ連携帳票に回答・予約がありませんでした。
      </div>
      <div v-else>
        <div v-if="!configReservationItem">
          <template v-for="(surveyResult, index) in allSurveyResults" :key="`survey_result_${index}`">
            <div >
              <SurveyResultsCard :surveyConfig="props.surveyConfig" :surveyResults="surveyResult" :showEditControls="true" />
            </div>
          </template>
        </div>
        <div v-else>
          <template v-for="(dateAndPartitionKeyTuple, index) in Array.from(mapForDisplay)" :key="`reservation_block_${index}`">
            <span class="date-display" >{{
              getDisplayDate(dateAndPartitionKeyTuple[0]) }}</span>
            <template v-for="(partitionKey, resultIndex) in dateAndPartitionKeyTuple[1]" :key="`survey_result_${index}_${resultIndex}`">
              <SurveyResultsCard :surveyConfig="props.surveyConfig"
                :surveyResults="getSurveyResultByPartitionKey(partitionKey)" :showEditControls="true"
                 />
            </template>
          </template>
        </div>

        <BackButtonMessage />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, ref, computed, watch, onBeforeMount } from 'vue';

import { useMainStore } from '@/stores';
import { useSurveyResultsStore } from '@/stores/modules/surveyResults';


// old imports
// 旧インポート
import moment from "moment";
import SurveyResultsCard from "@/components/common/SurveyResults/SurveyResultsCard.vue";
import ContentLoading from "@/components/common/ContentLoading.vue";
import ErrorComponent from "@/components/common/ErrorComponent.vue";
import BackButtonMessage from "@/components/BackButtonMessage.vue";
import SurveyConstants from "@/store/modules/surveyResults/surveyResults.constants";
/* import {
  SET_SHOW_ERROR_COMPONENT,
} from "@/store/mutation-types";
import {
    FETCH_SURVEY_RESULTS_BY_SURVEY_ID_AND_USER_ID,
} from "@/store/action-types"; */

const mainStore = useMainStore();
const surveyResultsStore = useSurveyResultsStore();

// emits 
const emits = defineEmits<{
  (event: 'loadingResults', payload: any): void;
}>();

// props
const props = defineProps({
  surveyConfig: {
    default: null as PropType<null>,
    type: Object as PropType<any>
  }
});

// data
const isLoading = ref<boolean>(false);
const localError = ref<any>(null);
const allSurveyResults = ref<any>([]);
const mapForDisplay = ref<any>(null);
const authSession = ref<any>(null);

// methods
const fetchSurveyResultsBySurveyIdAndLineUserId = surveyResultsStore.fetchSurveyResultsBySurveyIdAndUserId;
const setShowErrorComponent = mainStore.setShowErrorComponent;
const getSurveyResultsForDisplay = async (surveyConfig: any, lineUserId: any): Promise<void> => {
  isLoading.value = true;
  emits('loadingResults', true);
  localError.value = null;

  try {
    let response = await fetchSurveyResultsBySurveyIdAndLineUserId({ survey: props.surveyConfig, lineUserId: lineUserId });
    if (response.result === 'OK') {
      allSurveyResults.value = response.items;
    } else {
      localError.value = response.errorMessage ? response.errorMessage : '不明なエラー発生しました。管理者に問い合わせください。';
      return;
    }

    if (configReservationItem.value && allSurveyResults.value.length !== 0) {
      setUpChronologicalDisplayOrder();
    }
  } catch (error: any) {
    localError.value = error.message + 'っというエラー発生しました。管理者に問い合わせ下さい。'
  } finally {
    if (localError.value) {
      setShowErrorComponent(true);
    }
    isLoading.value = false;
    emits('loadingResults', false);
  }
};
const getSurveyResultByPartitionKey = (partitionKey: any): any => {
  return allSurveyResults.value.find((obj: { partitionKey: any; }) => obj.partitionKey === partitionKey);
};
const setUpChronologicalDisplayOrder = (): void => {
  const localReservationDayMap = new Map();
  const reservationQuestion = configReservationItem.value;

  //Create a map which contains lists of partitionKeys grouped by reservation day
  for (const result of allSurveyResults.value) {
    const reservationItem = result.fields.find((obj: { itemKey: any; }) => obj.itemKey === reservationQuestion.itemKey);

    if (reservationItem && reservationItem.value.split('|')[1]) {
      const splitReservationItem = reservationItem.value.split('|');
      localReservationDayMap.set(splitReservationItem[1], localReservationDayMap.has(splitReservationItem[1]) ?
        [...localReservationDayMap.get(splitReservationItem[1]), result.partitionKey] : [result.partitionKey]);
    } else {
      localReservationDayMap.set(SurveyConstants.SURVEY_DISPLAY_NO_RESERVATION_VARIABLE, localReservationDayMap.has(SurveyConstants.SURVEY_DISPLAY_NO_RESERVATION_VARIABLE) ?
        [...localReservationDayMap.get(SurveyConstants.SURVEY_DISPLAY_NO_RESERVATION_VARIABLE), result.partitionKey] : [result.partitionKey]);
    }
  }

  //Sort individual day lists by coma id
  localReservationDayMap.forEach(function (dayList, day) {
    if (day !== SurveyConstants.SURVEY_DISPLAY_NO_RESERVATION_VARIABLE) {
      dayList.sort((objectA: any, objectB: any) => {
        const objectAReservationResult = getSurveyResultByPartitionKey(objectA)
          .fields.find((obj: { itemKey: any; }) => obj.itemKey === reservationQuestion.itemKey);
        const objectBReservationResult = getSurveyResultByPartitionKey(objectB)
          .fields.find((obj: { itemKey: any; }) => obj.itemKey === reservationQuestion.itemKey);

        return objectAReservationResult.value.split('|')[2] - objectBReservationResult.value.split('|')[2];
      })
    }
  });

  mapForDisplay.value = new Map([...localReservationDayMap.entries()].sort());
};
const getDisplayDate = (dateToFormat: moment.MomentInput): any => {
  moment.locale("ja");
  const momentDate = moment(dateToFormat);

  return dateToFormat === SurveyConstants.SURVEY_DISPLAY_NO_RESERVATION_VARIABLE ?
    SurveyConstants.SURVEY_DISPLAY_NO_RESERVATION_TEXT : momentDate.format("Y年M月D日 (ddd)");
};

// computed

const getUserId = computed((): any => {
  // userId を取得する
  //let authSession = authSession;
  const userInfo = authSession.value.getIdToken().payload;
  return userInfo.identities[0].userId;
});
const configReservationItem = computed((): any => {
  return props.surveyConfig.surveySchema.find((obj: { type: string; }) => obj.type === 'reservation');
});

// watch
watch(() => props.surveyConfig, (newVal) => {
  getSurveyResultsForDisplay(newVal, getUserId.value);
});

// hooks

onBeforeMount(async () => {
  if (props.surveyConfig) {
    await getSurveyResultsForDisplay(props.surveyConfig, getUserId.value);
    authSession.value = await mainStore.fetchSession();
  }
});

</script>
