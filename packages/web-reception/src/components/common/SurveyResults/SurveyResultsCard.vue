<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.display-text-item {
    margin-left: 0.5em;
}

.error-block {
    margin: 0.5em;
}

.information-block {
    padding-top: 0.5em;
    padding-bottom: 0.5em;
}

.cancelled-reservation {
    color: darkred;
}

.action-button {
    margin-left: 0.25em;
    margin-right: 0.25em;
}
</style>
<template>
    <div>
        <q-card class="mb-8 appointment-card">
            <div :opacity="0.2" v-if="loadingData">
                <content-loading :size="50" text="" />
            </div>
            <q-card-section v-if="props.cardTitle">{{ props.cardTitle }}</q-card-section>
            <div v-if="error" class="error-block information-block">
                <ErrorComponent :errorMessage="error" :errorDetails="errorDetails" />
            </div>
            <div v-else class="information-block">
                <div align="left" class="display-text-item" v-for="item in displayData" :key="item.itemKey">
                    <div v-if="item.itemKey === lineBreakConstant">
                        <br />
                    </div>
                    <div v-else>
                        {{ item.text }}：<span :class="{ 'cancelled-reservation': isCancelledText(item.value) }">{{
                            item.value }}</span>
                    </div>
                </div>
                <q-card-actions v-if="props.showEditControls" class="mt-5">
                    <q-space></q-space>
                    <div v-if="controlLimitExceeded">
                        <p>予約変更・キャンセルの期限を過ぎています。<br />予約窓口へ直接ご連絡をお願い致します。</p>
                    </div>
                    <div v-else>
                        <q-btn class="action-button" color="primary" @click="onEditClick">
                            {{ editButtonText }}
                        </q-btn>
                        <q-btn class="action-button" color="error" v-if="showCancelButton" @click="onCancelClick">
                            キャンセル
                        </q-btn>
                    </div>
                    <q-space></q-space>
                </q-card-actions>
            </div>
        </q-card>
        <CancelConfirmPopUp :visible="showCancelConfirmPopup" @close="cancelConfirm" />
    </div>
</template>

<script setup lang="ts">
import { PropType, ref, computed, onBeforeMount, ComputedRef } from 'vue';

import { useMainStore } from '@/stores';
import { useSurveyResultsStore } from '@/stores/modules/surveyResults';
import { useCalendarsStore } from '@/stores/modules/calendar';
import { useRoute, useRouter } from 'vue-router';

// old imports
// 旧インポート
import ErrorComponent from "@/components/common/ErrorComponent.vue";
import CancelConfirmPopUp from "@/components/CancelConfirmPopUp.vue";
/* import {
  SET_SHOW_ERROR_COMPONENT,
  SET_EDITING_APPENDING_MODE,
  SET_UNALTERED_SURVEY_CONFIG,
} from "@/store/mutation-types";
import {
    CANCEL_SURVEY_RESULTS,
    GET_DAY_OFF_COUNT,
} from "@/store/action-types"; */
import SurveyConstants from "@/store/modules/surveyResults/surveyResults.constants";
import moment from "moment";

const mainStore = useMainStore();
const surveyResultsStore = useSurveyResultsStore();
const calendarsStore = useCalendarsStore();

const router = useRouter();
const route = useRoute();

// props
const props = defineProps({
    surveyConfig: Object as PropType<any>,
    surveyResults: Object as PropType<any>,
    showEditControls: {
        type: Boolean as PropType<boolean>,
        default: true
    },
    cardTitle: {
        type: String as PropType<string>,
        default: ''
    },
    isMemberResults: {
        type: Boolean as PropType<boolean>,
        default: false
    }
});

// data
const loadingData = ref<boolean>(false);
const displayData = ref<any>([]);
const error = ref<any>(null);
const errorDetails = ref<any>(null);
const showCancelConfirmPopup = ref<boolean>(false);
const cancelledReservation = ref<boolean>(false);
const controlLimitExceeded = ref<boolean>(false);
const authSession = ref<any>(null);

// methods
const cancelSurveyResult = surveyResultsStore.cancelSurveyResults;
const getDayOffCount = calendarsStore.getDayOffCount;
const setShowErrorComponent = mainStore.setShowErrorComponent;
const setEditingAppendingMode = mainStore.setEditingAppendingMode;
const setSelectedSurveyConfig = mainStore.setUnalteredSurveyConfig;
const processResultsForDisplay = async (): Promise<void> => {
    loadingData.value = true;
    try {
        let reservationQuestion = null;

        //Set up display values for results
        for (let surveyQuestion of props.surveyConfig.surveySchema) {
            if (surveyQuestion.isAdminItem) {
                continue;
            }
            if (surveyQuestion.type === "reservation") {
                reservationQuestion = surveyQuestion;
                continue;
            } else if (surveyQuestion.type === "checkboxes") {
                setUpCheckboxDisplay(surveyQuestion);
            } else {
                let item = props.surveyResults.fields.find((obj: { itemKey: any; }) =>
                    obj.itemKey == surveyQuestion.itemKey
                )
                if (item) {
                    displayData.value.push({
                        itemKey: surveyQuestion.itemKey,
                        text: surveyQuestion.title,
                        value: item.value,
                    })
                }
            }
        }

        //Set up display values for reservation info
        if (reservationQuestion && getSurveyResultByItemKey(reservationQuestion.itemKey)) {
            setUpDisplayValuesForReservation(getSurveyResultByItemKey(reservationQuestion.itemKey));
        }

        //Check to see if edit/cancel buttons can be displayed
        await checkForControlLimitExceeded();
    } catch (error: any) {
        displayError("画面表示中にエラーが発生しました。管理者にお問い合わせください。", error.value.message);
    } finally {
        loadingData.value = false;
    }
};
const checkForControlLimitExceeded = async (): Promise<void> => {
    // キャンセル変更期限設定が0の場合はチェック無し
    // 設定値の-1日の日から変更不可（1⇒当日、2⇒前日、3⇒2日前・・・）
    // 休日は営業日としてカウントしない
    const cancelLimit = props.surveyResults.calendarInfo && "reservationCancelLimit" in props.surveyResults.calendarInfo ?
        props.surveyResults.calendarInfo.reservationCancelLimit : 1;
    if (cancelLimit !== 0 && reservationDate.value && reservationCategoryId.value) {
        // 休日数を取得
        const fromDate = moment().format('YYYYMMDD');
        const dayOffCount = await getDayOffCount({
            categoryId: reservationCategoryId.value,
            fromDate: fromDate,
            toDate: reservationDate.value
        });
        // キャンセル・変更可能日の算出
        const calcDays = cancelLimit + dayOffCount - 1;
        let reservationDateLocal: any = moment(reservationDate.value).subtract(calcDays, 'days').format('YYYY/MM/DD 00:00:00');
        reservationDateLocal = new Date(reservationDate.value);
        let nowDate: any = moment().format('YYYY/MM/DD 00:00:00');
        nowDate = new Date(nowDate);
        if (reservationDateLocal <= nowDate && props.surveyResults.check !== "キャンセル") {
            // 予約変更、キャンセル不可
            controlLimitExceeded.value = true;
        }
    }
};
const setUpDisplayValuesForReservation = (reservationItem: { value: string; }): void => {
    const splitReservationValue = reservationItem.value.split('|');

    displayData.value.push({
        itemKey: SurveyConstants.SURVEY_CARD_LINE_BREAK_VARIABLE,
    });
    if (splitReservationValue[2]) {
        displayData.value.push({
            itemKey: SurveyConstants.SURVEY_CARD_RESERVATION_COMA_VARIABLE,
            text: SurveyConstants.SURVEY_CARD_RESERVATION_COMA_TEXT,
            value: props.surveyResults.check === 'キャンセル' ?
                SurveyConstants.RESERVATION_CANCELLED :
                props.surveyResults.calendarInfo.comaList[splitReservationValue[2]].start,
        });
    }
    if (props.surveyResults.categoryInfo && props.surveyResults.categoryInfo.tag1) {
        displayData.value.push({
            itemKey: getLargeCategoryTitle.value,
            text: getLargeCategoryTitle.value,
            value: props.surveyResults.categoryInfo.tag1
        })
    }
    if (props.surveyResults.categoryInfo && props.surveyResults.categoryInfo.tag2) {
        displayData.value.push({
            itemKey: getMediumCategoryTitle.value,
            text: getMediumCategoryTitle.value,
            value: props.surveyResults.categoryInfo.tag2
        })
    }
    if (props.surveyResults.categoryInfo && props.surveyResults.categoryInfo.tag3) {
        displayData.value.push({
            itemKey: getSmallCategoryTitle.value,
            text: getSmallCategoryTitle.value,
            value: props.surveyResults.categoryInfo.tag3
        })
    }
};
const setUpCheckboxDisplay = (surveyQuestion: { itemKey: any; options: string | any[]; title: any; }): void => {
    const fields = props.surveyResults.fields.filter((obj: { itemKey: any; }) => obj.itemKey === surveyQuestion.itemKey);
    fields.sort((optionA: { value: any; }, optionB: { value: any; }) => {
        return surveyQuestion.options.indexOf(optionA.value) - surveyQuestion.options.indexOf(optionB.value);
    });
    let displayText = '';
    for (const checkbox of fields) {
        if (checkbox.value) {
            displayText += checkbox.value + ', ';
        }
    }
    if (displayText !== '') {
        displayData.value.push({
            itemKey: surveyQuestion.itemKey,
            text: surveyQuestion.title,
            value: displayText.length > 0 ? displayText.substring(0, displayText.length - 2) : displayText,
        })
    }
};
const getSurveyResultByItemKey = (itemKey: any): any => {
    return props.surveyResults.fields.find((obj: { itemKey: any; }) => obj.itemKey === itemKey);
};
const displayError = (mainError: any, errorDetails: any): void => {
    error.value = mainError;
    errorDetails.value = errorDetails.value;
    setShowErrorComponent(true);
};
const onEditClick = async (): Promise<void> => {
    setSelectedSurveyConfig(props.surveyConfig);
    if (props.isMemberResults) {
        await router.push({
            path: `/member/${props.surveyConfig.surveyId}`
        });
        return;
    } else {
        if (props.surveyConfig.isAppending && props.surveyConfig.isAppending.value) {
            setEditingAppendingMode(true);
        }
        router.push({
            name: "LiffSurvey",
            params: { id: props.surveyConfig.surveyId },
            query: { partitionKey: props.surveyResults.partitionKey },
        });
    }
};
const onCancelClick = (): void => {
    showCancelConfirmPopup.value = true;
};
const cancelConfirm = async (value: any): Promise<void> => {
    showCancelConfirmPopup.value = false;
    if (value) {
        loadingData.value = true;
        try {
            const cancelResultsPayload = {
                surveyId: props.surveyConfig.surveyId,
                userId: getUserId.value,
                partitionKey: props.surveyResults.partitionKey,
            };

            let _pushRes = await cancelSurveyResult(cancelResultsPayload);
            if (_pushRes.result === 'ERROR') {
                displayError(_pushRes.errorMessage, null);
            } else {
                for (const item of displayData.value) {
                    if (item.itemKey === SurveyConstants.SURVEY_CARD_RESERVATION_COMA_VARIABLE) {
                        item.value = SurveyConstants.RESERVATION_CANCELLED;
                    }
                }
                cancelledReservation.value = true;
            }
        } catch (error: any) {
            displayError('エラー発生しました管理者にお問い合わせください', error.value.message);
        } finally {
            loadingData.value = false;
        }
    }
};
const isCancelledText = (value: any): boolean => {
    return value === SurveyConstants.RESERVATION_CANCELLED;
};

// computed
const calendarDisplaySettings: ComputedRef<any> = computed(() => surveyResultsStore.calendarDisplaySettings);
const getUserId = computed((): any => {
    // userId を取得する
    //let authSession = authSession;
    const userInfo = authSession.value.getIdToken().payload;
    return userInfo.identities[0].userId;
});
const showEditButton = computed((): boolean => {
    //「予約・編集」ボタン見せるのロジック
    return true;
});
const editButtonText = computed((): string => {
    return props.surveyConfig.surveySchema.find((obj: { type: string; }) => obj.type === 'reservation') ? '予約・変更' : '変更';
});
const showCancelButton = computed((): boolean => {
    //「キャンセル」ボタン見せるのロジック
    const reservationQuestion = configReservationItem.value;
    if (!reservationQuestion) {
        return false;
    }
    const reservationItem = props.surveyResults.fields.find((obj: { itemKey: any; }) => obj.itemKey === reservationQuestion.itemKey);
    return reservationItem && reservationItem.value && reservationItem.value.split('|').length > 1 && props.surveyResults.check !== 'キャンセル' && !cancelledReservation.value;
});
const configReservationItem = computed((): any => {
    return props.surveyConfig.surveySchema.find((obj: { type: string; }) => obj.type === 'reservation');
});
const reservationDate = computed((): any => {
    const reservationQuestion = configReservationItem.value;
    if (!reservationQuestion) {
        return null;
    }
    const reservationItem = props.surveyResults.fields.find((obj: { itemKey: any; }) => obj.itemKey === reservationQuestion.itemKey);
    if (!reservationItem) {
        return null;
    }

    return reservationItem.value.split('|')[1];
});
const reservationCategoryId = computed((): any => {
    return "categoryInfo" in props.surveyResults ? props.surveyResults.categoryInfo.sortKey : null;
});
const lineBreakConstant = computed((): any => {
    return SurveyConstants.SURVEY_CARD_LINE_BREAK_VARIABLE;
});
const getLargeCategoryTitle = computed((): any => {
    //大項目名/tag1
    const reservationQuestion = configReservationItem.value;
    if (!reservationQuestion) {
        return null;
    }
    return reservationQuestion.setLargeCategoryTitle ? reservationQuestion.setLargeCategoryTitle :
        calendarDisplaySettings.value.tag1 ? calendarDisplaySettings.value.tag1 : SurveyConstants.LARGE_CATEGORY_DEFAULT;
});
const getMediumCategoryTitle = computed((): any => {
    //中項目名/tag2
    const reservationQuestion = configReservationItem.value;
    if (!reservationQuestion) {
        return null;
    }
    return reservationQuestion.setMediumCategoryTitle ? reservationQuestion.setMediumCategoryTitle :
        calendarDisplaySettings.value.tag2 ? calendarDisplaySettings.value.tag2 : SurveyConstants.MEDIUM_CATEGORY_DEFAULT;
});
const getSmallCategoryTitle = computed((): any => {
    //小項目名/tag3
    const reservationQuestion = configReservationItem.value;
    if (!reservationQuestion) {
        return null;
    }
    return reservationQuestion.setSmallCategoryTitle ? reservationQuestion.setSmallCategoryTitle :
        calendarDisplaySettings.value.tag3 ? calendarDisplaySettings.value.tag3 : SurveyConstants.SMALL_CATEGORY_DEFAULT;
});

// hooks

onBeforeMount(async () => {
    processResultsForDisplay();
    authSession.value = await mainStore.fetchSession();
});

</script>
