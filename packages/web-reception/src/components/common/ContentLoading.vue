<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-row>
      <q-col class="mx-auto" cols="auto">
        <fulfilling-bouncing-circle-spinner :animation-duration="1500" :size="size" :color="color" />
      </q-col>
    </q-row>
    <div class="text-center my-1 primary--text">{{ text }}</div>
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';



// old imports
// 旧インポート
import { FulfillingBouncingCircleSpinner } from "epic-spinners";


// props
const props = defineProps({
  size: {
    type: Number as PropType<number>,
    default: 35
  },
  text: {
    type: String as PropType<string>,
    default: "ロード中..."
  },
  color: {
    type: String as PropType<string>,
    default: "#1b76d1"
  }
});
</script>

<style></style>
