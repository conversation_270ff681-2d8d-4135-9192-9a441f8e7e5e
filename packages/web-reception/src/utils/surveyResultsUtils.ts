/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */
import _ from 'lodash';

export const transformCompiledDataToArray = (compiledResult: any, surveyConfig: any): any => {
  const result = [];
  const compilation = _.cloneDeep(compiledResult);
  delete compilation.fields;

  compiledResult.fields.forEach((elem: any) => {
    const tempElem = _.cloneDeep(compilation);
    tempElem.itemKey = elem.itemKey;
    tempElem.value = elem.value;
    const schemaElem = surveyConfig.surveySchema.find((schema: any) => schema.itemKey === elem.itemKey);
    if (schemaElem) {
      tempElem.title = schemaElem.title;
    }
    result.push(tempElem);
  })

  return result;
}

export const createAppendingPayload = (results: { surveyId: any; userId: any; items: any[]; partitionKey?: any; }) => {
  const data: any = {};
  const partitionKey = `${results.surveyId}#${results.userId}#${Date.now()}`; // 追加できるようにする
  results.items.forEach(function (item: { itemKey: any; value: any; }, index: any) {
    const sortKey = `${item.itemKey}#${item.value}`;
    const userSearchKey = `${item.itemKey}#${item.value}`;
    const tmp = {
      userId: results.userId,
      surveyId: results.surveyId,
      value: item.value,
      partitionKey: partitionKey,
      sortKey: sortKey,
      itemKey: item.itemKey,
      userSearchKey: userSearchKey,
      check: "未対応",
    };
    data[tmp.sortKey] = tmp;
  });

  //oldPartitionKey
  if (results.partitionKey !== undefined && results.partitionKey) {
    data.oldPartitionKey = {
      oldPartitionKey: results.partitionKey,
    };
  }
  return Object.values(data);
}

// ログインを作動して、5秒後にログインページに移動する
export const triggerLogin = ($q, router, route, timedOut = true) => {
  const message = timedOut ? 'ログインしてください' : 'セッションがタイムアウトしました。'
  let seconds = 5
  const notif = $q.notify({
    group: false,
    spinner: true,
    message: message,
    caption: `${seconds}秒後にログインページに移動します。`,
    color: 'yellow-9',
    position: 'bottom',
    timeout: 0,
    textColor: 'white',
    classes: 'text-body1'
  })

  const interval = setInterval(() => {
    seconds--
    notif({
      caption: `${seconds}秒後にログインページに移動します。`,
      textColor: 'white',
      classes: 'text-body1'
    })

    if (seconds === 0) {
      notif({
        icon: 'done',
        color: 'green-14',
        spinner: false,
        message: 'ログインページに移動します。',
        timeout: 1500,
        textColor: 'white',
        classes: 'text-body1'
      })
      clearInterval(interval)
      window.sessionStorage.clear()
      window.sessionStorage.setItem('liffState', route.path)
      router.push({ name: 'Login' })
    }
  }, 1000)
}