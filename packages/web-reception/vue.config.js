/*
 * Copyright 2022 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */
import { BundleAnalyzerPlugin } from 'webpack-bundle-analyzer';
const analyzerMode = process.env.BUNDLE_ANALYZE ? 'server' : 'disabled';
const hints = process.env.BUNDLE_ANALYZE ? 'warning' : false

import { resolve } from 'path';

module.exports = {
  configureWebpack: {
    performance: {
      hints: hints
    },
    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
        '@utils': resolve(__dirname, './src/utils'),
        '@components': resolve(__dirname, './src/components'),
        '@stores': resolve(__dirname, './src/stores'),
        '@services': resolve(__dirname, './src/services'),
        '@pages': resolve(__dirname, './src/pages'),
        '@constants': resolve(__dirname, './src/constants'),
        '@assets': resolve(__dirname, './src/assets')
      }
    },

    optimization: {
      runtimeChunk: 'single',
      splitChunks: {
        chunks: 'all',
        maxInitialRequests: Infinity,
        minSize: 0,
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name(module) {
              const packageName = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/)[1];
              return `${packageName.replace('@', '')}`;
            },
          },
        },
      },
    },
    
    plugins: [
      new BundleAnalyzerPlugin({
        analyzerMode: analyzerMode,
      })
    ]
  },

  productionSourceMap: process.env.NODE_ENV != "production",
};