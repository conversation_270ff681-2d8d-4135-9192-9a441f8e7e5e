# Client setup

.env.template にならって、.env を作って、自身の環境情報を設定する

## ローカルで実行する場合

.env の VUE_APP_LIFF_WEB_URL -> http://localhost:8888

AWS Cognito -> <環境名-liff-users> -> アプリの統合 -> アプリクライアントの設定 -> Callback URL
-> http://localhost:8888/login

※ログインできない場合は下記「ngrokを使用したローカル実行方法」を参照

## setup & run
```
npm config set @bit:registry https://node.bit.dev
```

```
npm install
```

```
npm run serve
```

# setup
[aws_back/survey_api/](https://github.com/linefukuoka/line-smart-city/tree/main/aws_back/survey_api) が先に deploy してあることを前提とする.

## Line Developer

* LINE LoginのCallback URL: `https://<環境名-surveyスタックの出力"LiffAuthDomain">/oauth2/idpresponse`
* LIFF作成
   * Endpoint URL: `https://<環境名-surveyスタックの出力"VUE_APP_LIFF_WEB_URL">/login`
   * Scopes: profile, openid (必要なら chat_message.write)

## Deploy(仮)

* [.env.template](.env.template) にならって、`.env` を作って、自身の環境情報を設定する
* `./deploy.sh <環境名>` を実行
* 必要ならcloudfrontのキャッシュもクリアする
   * `aws cloudfront create-invalidation --distribution-id XXXXXXXXXX --paths '/*' --profile lsc-dev`

## ngrokを使用したローカル実行方法

### 準備

ngrokに登録 https://dashboard.ngrok.com

セットアップ https://dashboard.ngrok.com/get-started/setup

### 実行

`./ngrok http -host-header=rewrite 8888`

Forwardingのホスト名をコピーする

### その他の設定

#### LINE Developers

LINE Login -> LIFF -> LIFF detail -> Endpoint URL の値を先ほどのForwardingに変更

#### Secrets Manager

Secrets Managerの値を直接Forwardingのホスト名に変更

Key|Value
----- | -----
VUE_APP_LIFF_WEB_URL|http://localhost:8888
SURVEY_LIFF_CLOUDFRONT_DOMAIN|http://localhost

#### Cognito （Cognito認証を使用している場合）

環境名-liff-users -> アプリの統合 -> アプリクライアントの設定

コールバック URL (", http://localhost:8888/login") 、サインアウト URL (", http://localhost:8888") にForwardingのホスト名を追加


## ローカルで実行する場合(Congnito認証を使用しない場合)
### ローカル環境設定

* `.env` の VUE_APP_LIFF_WEB_URL -> `http://localhost:8888` に変更

### LINEDeveloper設定

* LINE LoginのCallback URL: `http://localhost:8888/login`に変更

### Lambda環境設定

* AWSコンソールより、関数名`<環境名-SurveyAccessor>`のLambda関数に対して、`環境変数`->`編集`を選択
* `LINELOGIN_REDIRECT_URL`を`http://localhost:8888/login`に変更
