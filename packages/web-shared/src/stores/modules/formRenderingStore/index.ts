
import { isHankakuOnly } from '../../../components/FormRendering/utils/stringUtil';
import { isBlank, requireFormat, requireTextCharacterType, requireTextIsEmail, requireTextIsPhoneNumber, requireTextIsPostCode, requireTextMaxLength, requireTextMinLength } from '../../../components/FormRendering/utils/validationUtils';
import { IME_TYPE } from '../../../constants';
import { defineStore } from 'pinia';

export const useFormRenderingStore = defineStore("formRenderingStore", {

  state: () => ({
    // main
    isAdminMode: false,
    isLiffMode: false,
    forceNotEditable: false,
    isConfirmSurveyMode: false,
    errorMessage: '',
    isSaibanExisted: true,
    surveySchema1:null
  }),
  actions: {
    // setters
    setAdminMode(value) {
      this.isAdminMode = value;
    },

    setLiffMode(value) {
      this.isLiffMode = value;
    },

    setForceNotEditable(value) {
      this.forceNotEditable = value;
    },

    setConfirmSurveyMode(value) {
      this.isConfirmSurveyMode = value;
    },

    setSaibanExisted(value) {
      this.isSaibanExisted = value;
    },

    setSurveySchema1(value) {
      this.surveySchema1 = value;
    },
    // getters

    getSaibanExisted() {
      return this.isSaibanExisted;
    },
    validateRequired(input: any, required: boolean, isRequiredForUser: boolean): true | string {
      const isEmpty = isBlank(input);
      const errorMessage = "必須です";
      
      let isValidationRequired = false;
      
      if (this.isAdminMode) {
        isValidationRequired = required;
      } else if (this.isLiffMode) {
        isValidationRequired = required || isRequiredForUser;
      } else {
        isValidationRequired = true;
      }
      
      const result = (isValidationRequired && isEmpty) ? errorMessage : true;
      
      this.errorMessage = result === true ? '' : result as string;
      return result;
    },
    validateLength(input: any, length: { min?: number, max?: number, limitation?: number }): true | string {
      if (!input) {
        return true;
      }
      let validMin =
        !length.min || length.min === 0 || requireTextMinLength(input, length.min);
      let validMax =
        length.max === null || requireTextMaxLength(input, length.max);
      let validLimitation =
        !length.limitation ||
        ((length.min === null || length.min <= length.limitation) &&
        (length.max === null || length.max <= length.limitation));

      if (validMin && validMax && validLimitation) {
        return true;
      } else {
        let message = "";
    
        if (
          length.limitation < length.min ||
          length.limitation < length.max ||
          length.limitation < input.length
        ) {
          message += length.limitation + "文字以下で";
          if (length.limitation < length.min) {
            message += "最小文字数を設定して下さい。";
          } else if (length.limitation < length.max) {
            message += "最大文字数を設定して下さい。";
          } else if (length.limitation < input.length) {
            message += "入力して下さい。";
          }
        } else {
          if (length.min) {
            message += length.min + "文字以上";
          }
          if (length.max) {
            if (message.length > 0) {
              message += "かつ";
            }
            message += length.max + "文字以下";
          }
          message += "で入力してください";
        }
        return message;
      }
    },
    
    validateType(input: any, characterTypeList): true | string {
      if (!input) {
        return true;
      }
      const validCharType =
        characterTypeList.indexOf("all") >= 0 ||
        requireTextCharacterType(input, characterTypeList);
      if (validCharType) {
        return true;
      } else {
        return "不正な文字が入力されました。";
      }
    },
    
    validateFormat(input: any, inputFormat): true | string {
      if (!input) {
        return true;
      }
      const validFormat =
        inputFormat === "any" || requireFormat(input, inputFormat);
      if (validFormat) {
        return true;
      } else {
        return "不正なフォーマットが入力されました。";
      }
    },
    
    validateImeInput(input: any, imeType) {
      if (!input) {
        return true;
      }
    
      let result: any = true;
      switch (imeType) {
        case IME_TYPE.hankaku_only:
          result = isHankakuOnly(input);
          if (!result) {
            result = "半角のみを入力してください。";
          }
          break;
      }
    
      return result;
    },

    validateIsEmail(input: any) {
      let is_valid = !input || requireTextIsEmail(input);

      if (is_valid) {
        return true;
      } else {
        let message = "正しいメールを入力してください。";
        return message;
      }
    },

    validateIsPhoneNumber(input: any) {
      let is_valid = !input || requireTextIsPhoneNumber(input);
      if (is_valid) {
        return true;
      } else {
        let message = "半角数字10桁-11桁で入力してください。";
        return message;
      }
    },

    validateIsPostCode(input: any) {
      let is_valid = !input || requireTextIsPostCode(input);
      if (is_valid) {
        return true;
      } else {
        let message = "正しい郵便番号を入力してください。";
        return message;
      }
    },
  }
});