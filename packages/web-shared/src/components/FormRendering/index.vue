<template>
  <div>
    <span id="render"></span>
    <DataConfirm v-if="isConfirmMode" :data="dataSurvey" />
    <template v-else>
      <template v-if="isLiffMode">
        <div class="q-mb-lg">
          <div class="col">
            <q-card class="userInfoTitle">
              <q-list>
                <q-item>
                  <div>
                    <q-avatar v-if="userInfo != null && userInfo.picture">
                      <q-img :src="userInfo.picture" />
                    </q-avatar>
                    <q-avatar v-else color="primary text-white">
                      <span class="text-uppercase" v-if="userInfo.name">
                        {{ userInfo.name.substr(0, 2) }}
                      </span>
                    </q-avatar>
                  </div>

                  <q-item-section class="q-pl-md">
                    <q-toolbar-title class="flex items-center">
                      <span v-if="userInfo">{{ userInfo.name }}</span> 様
                    </q-toolbar-title>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card>
          </div>
        </div>
        <div v-if="configJson?.headerImageUrl && JSON.stringify(configJson.headerImageUrl) != '{}'"
          class="row q-my-md q-py-none">
          <div v-if="headerImgDisplay" class="q-mb-md col">
            <q-card style="border:none;">
              <q-list class="q-py-none">
                <q-item class="q-pa-none">
                  <q-img
                    :src="configJson.headerImageUrl.headerUrl ? configJson.headerImageUrl.headerUrl : configJson.headerImageUrl"
                    style="border-radius:4px;"  @error="setHeaderDefaultDisplay"></q-img>
                </q-item>
              </q-list>
            </q-card>
          </div>
        </div>
        <div class="row q-pb-md q-mt-md">
          <div class="col">
            <q-card class="border-top-primary">
              <q-card-section>
                <div class="survey-title text-h5 tw-font-medium tw-mb-2">
                  {{ configJson?.surveyTitle }}
                </div>
                <div>
                    <div class="survey-description" v-html="renderToLink(configJson?.description)">
                  </div>
                  <span v-if="required" class="text-negative">
                    *必須
                  </span>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </template>
      <!-- TODO: fix validation when error -->
      <q-form v-model="isFormValid" ref="allFormValid" @submit.prevent="redirect" :no-error-focus="true"
        @validation-success="updateIsFormValid(true)" greedy>
        <div class="row" v-show="false">
          <div class="col">{{ surveySchemaUpdatedFlag }}</div>
        </div>

        <div v-if="isConfirmSurveyMode && configJson.usePayment === 1">
          <div class="row">
            <div class="col">
              <q-card class="q-pa-md">
                <div class="text-weight-bold">
                  受付番号
                  <span class="text-negative">*</span>
                </div>
                <div class="text-caption text-grey-8">受付番号は領収書ページよりご確認ください。</div>
                <InputOrderId ref="inputOrderId" />
              </q-card>
            </div>
          </div>
        </div>
        <div v-for="(item, sIndex) in surveySchema1" :key="sIndex">
          <div class="row">
            <div v-if="item.type !== 'linkbutton'" class="col">
              <q-card>
                <div v-if="item.type === 'groupheader'">
                  <q-expansion-item accordion tile multiple class="groupheader q-mt-md"
                    v-model="groupheaderPanelValues[item.itemKey]" :disable="item.disabled" v-if="!item.disabled"
                    >
                    <template v-slot:header>
                      <q-item class="column q-pl-none full-width">
                        <q-item-section>
                          <q-item-label>
                            <span class="text-weight-bold">{{ item.title }}</span>
                          </q-item-label>

                        </q-item-section>
                        <q-item-section class="q-mt-md" style="margin-left: 0px;">
                          <div>
                            {{ item.description }}
                          </div>
                        </q-item-section>
                      </q-item>
                    </template>

                    <q-card>
                      
                      <q-card-section v-for="(value, index) in item.items" :key="index" eager>
                        <div v-if="value.type !== 'linkbutton'">
                          <div>
                            <span class="text-weight-bold">
                              {{ value.title }}
                            </span>
                            <span>

<!--                          <InputChoiceGroupheader :params="value" @choice-groupheader-radio-click="
                                choiceGroupheaderRadioClick
                                " :ref="el => assignRef(el, value.itemKey)" :key="value.itemKey" /> -->
                            </span>
                            <span v-if="
                              (isAdminMode &&
                                value.isRequired.admin === true) ||
                              (isAdminMode &&
                                (!('admin' in value.isRequired) ||
                                  value.isRequired.admin === null) &&
                                value.isRequired.value) ||
                              (!isAdminMode &&
                                (value.isRequired.value ||
                                  (value.isRequiredForUser && value.isRequiredForUser.value)))
                            " class="v-messages theme--light error--text text-negative">
                              *
                            </span>
                          </div>
                          <div v-if="value.description">
                            <span class="caption grey--text text--darken-3" v-html="renderToLink(value.description)">
                            </span>
                          </div>

                          <div v-if="!isEmpty(value.imageUrl)" class="q-my-md">
                            <q-img class="liffItemImg"
                              :src="value.imageUrl.itemUrl ? value.imageUrl.itemUrl : value.imageUrl"
                              style="border-radius:4px;" />
                          </div>

                          <div v-if="
                            value.type !== 'groupheader' &&
                            value.type !== 'guide'
                          " v-bind:class="disabledFormRenderer">
                            <Suspense>
                              <template #default>
                                <component :configJson="configJson" :ref="el => assignRef(el, value.itemKey)"
                                  :is="getComponent(value.type)" :params="value" :isAdminMode="isAdminMode"
                                  :isLiffMode="isLiffMode" :isNotEditable="value.isNotEditable"
                                  :forceNotEditable="forceNotEditable" :isConfirmSurveyMode="isConfirmSurveyMode"
                                  :isSurveyRegistering="isSurveyRegistering" :categoriesTree="categoriesTree"
                                  :isNewAnswer="isNewAnswer"
                                  @fill-address="fillAddress" @choice-groupheader-radio-click="
                                    choiceGroupheaderRadioClick
                                  " @handleCheckSaibanExisting="
                                    handleCheckSaibanExisting
                                  " @handleCheckSaibanExistingForUpdate="
                                    handleCheckSaibanExistingForUpdate
                                  " :paymentResult="paymentResult"
                                  :taxRate="taxRate" :paymentService="paymentService" :products="products"
                                  :isFetchingPaymentInfo="isFetchingPaymentInfo" @setPaymentInfo="handleSetPaymentInfo"
                                  v-model="answers[value.itemKey]"
                                  >
                                </component>
                              </template>
                            </Suspense>
                          </div>
                        </div>
                      </q-card-section>
                    </q-card>
                  </q-expansion-item>
                </div>
                <div v-else>
                  <div class="q-my-md">
                    <div class="col-12 q-pa-md">
                      <div>
                        <span class="text-weight-bold">{{ item.title }}</span>
                        <span v-if="
                          (isAdminMode && item.isRequired.admin === true) ||
                          (isAdminMode &&
                            (!('admin' in item.isRequired) ||
                              item.isRequired.admin === null) &&
                            item.isRequired.value) ||
                          (!isAdminMode &&
                            (item.isRequired.value ||
                              (item.isRequiredForUser && item.isRequiredForUser.value)))
                          || (!isAdminMode && item.type === 'previousVaccineMaker')
                          || (!isAdminMode && item.type === 'previousVaccineDate')
                        " class="text-negative">
                          *
                        </span>
                      </div>
                      <div v-if="item.description">
                        <span class="
                            text-caption
                            text-grey
                            text--darken-3
                            survey-description
                          " v-html="renderToLink(item.description)" />
                      </div>
                      <div v-if="!isEmpty(item.imageUrl)" class="q-mt-md">
                        <q-img class="liffItemImg"
                          :src="!isEmpty(item.imageUrl?.itemUrl) ? item.imageUrl?.itemUrl : item.imageUrl"
                          style="border-radius:4px;" />
                      </div>

                      <div v-if="
                        item.type !== 'groupheader' && item.type !== 'guide'
                      " :class="disabledFormRenderer">
                        <Suspense>
                          <template #default>
                            <component :configJson="configJson" :hasCountVaccines="hasCountVaccines"
                              :ref="el => assignRef(el, item.itemKey)" :params="item" :isAdminMode="isAdminMode"
                              :isLiffMode="isLiffMode" :isNotEditable="isEditable(item)"
                              :forceNotEditable="forceNotEditable" :isConfirmSurveyMode="isConfirmSurveyMode"
                              :isSurveyRegistering="isSurveyRegistering" :categoriesTree="categoriesTree"
                              :isValidatePreviousVaccineDate="isValidatePreviousVaccineDate"
                              :isValidatePreviousVaccineMaker="isValidatePreviousVaccineMaker"
                              :vaccinationIntervalType="vaccinationIntervalType" @fill-address="fillAddress"
                              :isNewAnswer="isNewAnswer"
                              @choice-groupheader-radio-click="
                                choiceGroupheaderRadioClick
                              " @handleCheckSaibanExisting="handleCheckSaibanExisting"
                              @handleCheckSaibanExistingForUpdate="
                                handleCheckSaibanExistingForUpdate
                              "
                              @handleCategoryChange="(value) => emit('onCategoryChange', value)"
                              :paymentResult="paymentResult" :taxRate="taxRate" :paymentService="paymentService"
                              :products="products" :isFetchingPaymentInfo="isFetchingPaymentInfo"
                              @setPaymentInfo="handleSetPaymentInfo" :is="getComponent(item.type)"
                              :model-value="answers[item.itemKey]"
                              @update:model-value="(val) => answers[item.itemKey] = val"
                              :key="item.itemKey"
                              v-if="getComponent(item.type)">
                            </component>
                          </template>
                          <template #fallback>
                            <div class="text-h6">読み込み中。。。</div>
                          </template>
                        </Suspense>

                      </div>
                    </div>
                  </div>
                </div>
              </q-card>
            </div>
          </div>
        </div>
        <div v-if="linkButtonObject && calendarButtonDisplay === true">
          <div>
            <div>
              <component :ref="linkButtonObject.itemKey" :is="getComponent(linkButtonObject.type)"
                :params="linkButtonObject" style="width: 100%">
              </component>
            </div>
          </div>
        </div>
      </q-form>
    </template>
  </div>
</template>

<script setup lang="ts">
import markdown from "markdown-it";
import markdownLinkAttributes from "markdown-it-link-attributes";
import { computed, defineAsyncComponent, onBeforeMount, onMounted, ref, watch } from "vue";
import DataConfirm from "./components/DataConfirm.vue";
import { isEmpty } from "lodash";
import InputOrderId from './components/InputOrderId.vue';
import { useFormRenderingStore } from "./../../stores/modules/formRenderingStore/index";
import { cloneDeep } from "lodash";
import { setActivePinia } from "pinia";
import { nextTick,reactive } from 'vue';
// import {useMainStore} from '../../../../web-reception/src/stores'
// store
let store = null;

const RENDER_COMPONENTS = {
  text: () => import('./components/InputShortAnswer.vue'),
  textarea: () => import('./components/InputParagraph.vue'),
  email: () => import('./components/InputEmail.vue'),
  phone: () => import('./components/InputPhoneNumber.vue'),
  postcode: () => import('./components/InputPostCode.vue'),
  address: () => import('./components/InputAddress.vue'),
  radio: () => import('./components/InputChoice.vue'),
  dropdown: () => import('./components/InputDropdown.vue'),
  suggest: () => import('./components/InputSuggest.vue'),
  date: () => import('./components/InputDatePicker.vue'),
  birthday: () => import('./components/InputBirthdayPicker.vue'),
  dateDropdown: () => import('./components/InputDateDropdown.vue'),
  checkboxes: () => import('./components/InputCheckbox.vue'),
  choicegroupheader: () => import('./components/InputChoiceGroupheader.vue'),
  number: () => import('./components/InputNumber.vue'),
  linearscale: () => import('./components/InputLinearscale.vue'),
  guide: () => import('./components/Guide.vue'),
  groupheader: () => import('./components/GroupHeader.vue'),
  reservation: () => import('./components/InputBunrui.vue'),
  linkbutton: () => import('./components/InputLinkButton.vue'),
  sesshuJisshiDate: () => import('./components/InputSesshuJisshiDatePicker.vue'),
  memberNumber: () => import('./components/InputNumbering.vue'),
  sesshuVaccineMaker: () => import('./components/InputSesshuVaccineMaker.vue'),
  countVaccines: () => import('./components/InputCountVaccines.vue'),
  previousVaccineDate: () => import('./components/InputPreviousVaccineDatePicker.vue'),
  previousVaccineMaker: () => import('./components/InputPreviousVaccineMaker.vue'),
  selectProducts: () => import('./components/InputSelectProducts/index.vue'),
  selectPayType: () => import('./components/InputSelectPayType.vue'),
  files: () => import('./components/FileAttachment.vue'),
};

// emit
const emit = defineEmits([
  'redirect',
  'handleCheckSaibanExisting',
  'handleCheckSaibanExistingForUpdate',
  'setPaymentInfo',
  'onCategoryChange',
  'updateIsFormValid'
]);

const props = withDefaults(
  defineProps<{
    configJson: any,
    userInfo?: any,
    isLiffMode: boolean,
    isConfirmMode?: boolean,
    pinia: any,
    //     userId: String,
    isSurveyRegistering?: boolean,
    dataSurvey?: any,
    //     isAppending: Boolean,
    isAdminMode?: boolean,
    isConfirmSurveyMode?: boolean,
    forceNotEditable?: boolean,

    categoryRequiredFlag?: boolean,
    calendarLabelDisplay?: boolean,
    calendarButtonDisplay?: boolean,

    isSaibanExisted?: boolean,
    categoriesTree?: any,

    isValidatePreviousVaccineDate?: boolean,
    isValidatePreviousVaccineMaker?: boolean,
    
    isNewAnswer: boolean,
    // for payment
    paymentResult?: any,
    paymentService?: any,
    taxRate?: number,
    products?: any[],
    isFetchingPaymentInfo?: boolean,
    accent?: string,
  }>(),
  {
    userInfo: {
      identities: [
        {
          dateCreated: "1234567890123",
          issuer: null,
          primary: "true",
          providerName: "Line",
          providerType: "OIDC",
          userId: "KdalfhASfljoaidjfaLfljadiojf64656",
        },
      ],
      name: "お客",
    },
    isConfirmMode: false,
    isConfirmSurveyMode: false,
    isAdminMode: false,
    isNewAnswer: true,
    forceNotEditable: false,
    isSurveyRegistering: true,
    categoriesTree: null,
    isSaibanExisted: false,
    paymentResult: null,
    taxRate: 0,
    paymentService: null,
    products: [] as any,
    isFetchingPaymentInfo: false,
    isValidatePreviousVaccineDate: true,
    isValidatePreviousVaccineMaker: true,
    accent: "#1976D2"
  }
);

// data

const forceUpdateKey = ref<number>(0);
const refs = ref<any>({});

const required = ref<boolean>(false); // TODO: requiredが見当たらなかったので一旦追加 - honda
const allFormValid = ref();
const isFormValid = ref(true);
const surveySchema1 = ref([]);
const surveySchemaTemp = ref();
const surveySchemaUpdatedFlag = ref(0); // surveySchema1が変更した時に、画面の表示が更新になるため。
const radioGroupheaderData = ref({});
const groupheaderData = ref({});
const groupheaderPanelValues = ref({});
const surveySchema = ref();
const answers = reactive<Record<string, any>>({});
const inputOrderId = ref();

const headerImgDisplay = ref(true);
// const receptionStore = useMainStore()
// methods

// Updates the refs object with the given key and element
// DO NOT console.log here unless you want a spam of logs
// refs列を指定されたキーと要素で更新します
// ここでconsole.logを使用しないでください。ログがスパムされます
const assignRef = (el, key) => {
  if (el === null || el === undefined) return;
  refs.value[key] = el;
};

const isEditable = (item: any) => {
  if (item.input === "対象外") {
    return true;
  }
  return item.isNotEditable;
};

// computed
const disabledFormRenderer = computed((): any => {
  return {
    "v-input--is-disabled": false,
  };
});

const linkButtonObject = computed((): any => {
  // TODO: surveySchemaが不明 - honda
  return surveySchema.value.find((obj: { type: string; }) => obj.type === "linkbutton");
});

const hasCountVaccines = computed((): boolean => {
  const countVaccines = props.configJson.surveySchema.find(item => {
    return item.type === 'countVaccines';
  });
  return !!countVaccines;
});

const vaccinationIntervalType = computed((): string => {
  const vaccinationIntervalType = props.configJson.vaccinationIntervalType?.input;
  return vaccinationIntervalType || '';
});

const markdownIt = computed((): any => {
  const md = new markdown();
  md.disable([
    "table",
    "code",
    "smartquotes",
    "normalize",
    "fence",
    "blockquote",
    "hr",
    "list",
    "reference",
    "backticks",
    "html_block",
    "heading",
    "lheading",
    "strikethrough",
    "emphasis"
  ]);
  md.use(markdownLinkAttributes, {
    attrs: {
      target: "_blank",
      rel: "noopener",
      style: "text-decoration: underline;"
    }
  });

  return md;
});

// methods
const setPropsData = () => {
  //console.log('setPropsData - start');
  store.setAdminMode(props.isAdminMode);
  store.setLiffMode(props.isLiffMode);
  store.setForceNotEditable(props.forceNotEditable);
  store.setConfirmSurveyMode(props.isConfirmSurveyMode);
  //console.log('setPropsData - end');
}

const initOrder = (): void => {
  // NOTE: 親コンポーネントから呼ばれる関数
  const doesUserPayment = props.configJson.usePayment === 1;
  if (!doesUserPayment) {
    return;
  }
  const targetItem = props.configJson.surveySchema.find(item => ['reservation', 'selectProducts'].includes(item.type));
  if (!targetItem) {
    return;
  }
  refs[targetItem.itemKey]?.initOrder();
}

const fillAddress = (input: any, postCodeItemKey: any): void => {
  // TODO: this.$refs[addressItemKey]が不明 - honda
  let postCodeItemFlg = false;
  let addressItemKey: string | number;
  for (let element of props.configJson.surveySchema) {
    if (element.itemKey === postCodeItemKey) {
      postCodeItemFlg = true;
    }
    if (postCodeItemFlg && element.type === "address") {
      addressItemKey = element.itemKey;
      break;
    }
  }

  //console.log('fillAddress - addressItemKey:', refs);

  if (addressItemKey != null && refs.value[addressItemKey] != null) {
    refs.value[addressItemKey].setValue(input);
  }
};

const redirect = async (): Promise<void> => {
  //console.log('redirect');
  await emit("redirect");
};

const getComponent = (name: any) => {
  //console.log('getComponent - name:', name);
  //console.log("RENDER",getFormData RENDER_COMPONENTS[name]);
  return defineAsyncComponent(RENDER_COMPONENTS[name]);
};

const automaticallyOpenInvalidGroupHeaders = (): void => {
  for (const item of surveySchema1.value) {
    if (item.type === 'groupheader' && item.items && Array.isArray(item.items) && item.items?.length > 0) {
      for (const groupHeaderItem of item.items) {
        const domElementList = refs[groupHeaderItem.itemKey];
        if (domElementList && Array.isArray(domElementList) && domElementList.length > 0) {
          const domElement = domElementList[0];
          try {
            const validateResponse = domElement.validateRequired();
            if (validateResponse !== true) {
              groupheaderPanelValues.value[item.itemKey] = false;
            }
          } catch (err) {
            //console.error(err);
            // Ignore the error
          }
        }
      }
    }
  }
};

const getFormData = async (isRegisterPayment = false): Promise<FormData | undefined> => {
  // No longer needed as of Vue3
  //$forceUpdate();

  // NOTE: check implementation for FE in future
  const isValid = await allFormValid.value.validate();
  if (!isValid) {
    automaticallyOpenInvalidGroupHeaders();
    return undefined;
  }

  updateIsFormValid(true);
  //console.log("REFS", props.configJson.surveySchema);

  let formData = new FormData();
  let paymentData;
  //console.log("radioGroupheaderData", radioGroupheaderData.value);
  for (let index = 0; index < props.configJson.surveySchema.length; index++) {
    let item = props.configJson.surveySchema[index];
    //console.log("item", item);
    if (item.type === "guide" || item.type === "groupheader") {
      continue;
    }
    let value;
    try {
      /*       console.table({
              item: item,
              itemKey: item.itemKey,
              type: item.type,
              refValue: refs.value[item.itemKey]
            }); */

      // skip if the item is not found in refs
      if (refs.value[item.itemKey] == undefined) {
        continue;
      }

      if(item.type === 'radio') {
        // check if itemKey is inside of any of the groupheaderData -> items[0]
        //console.log("groupheaderData", groupheaderData.value);
        const foundItem = Object.keys(groupheaderData.value).find(key => {
          return groupheaderData.value[key]?.items[0].itemKey === item.itemKey;
        });
       // console.log("foundItem", foundItem);
        // if parent is disabled, skip
        if (foundItem && groupheaderData.value[foundItem].disabled) {
          continue;
        }
      }
      
      value = refs.value[item.itemKey].getValue();
      
      if (props.configJson.usePayment && ['reservation', 'selectProducts'].includes(item.type)) {
        const isReservation = item.type === 'reservation';
        const paymentDataJson = isReservation ? refs.value[item.itemKey].getPaymentData() : value;        
        paymentData = paymentDataJson ? JSON.parse(paymentDataJson) : undefined;
        if (paymentData && paymentData.details?.length > 0) {
          if (!isReservation) {
            continue;
          }
        } else if (isRegisterPayment) {
          continue;
        } else {
          formData = null;
          break;
        }
      }
    } catch (error: any) {
      console.error(error);
      const find = surveySchemaTemp.value.find(
        (element: { itemKey: any; }) => element.itemKey === item.itemKey
      );
      value = find.input;
    }
    if (isEmpty(value)) {
      //console.log(`item.itemKey: ${item.itemKey} is empty, skipping...`);
      continue;
    }
    //console.log(`item.itemKey: ${item.itemKey}, value: ${typeof value}`);
    if (value instanceof Array) {
      value.forEach((val) => {
        formData.append(item.itemKey, val);
      });
    } else {
      formData.append(item.itemKey, value);
    }
  }

  if (formData && paymentData && props.configJson.usePayment) {
    const selectedPayType = getSelectedPayType(formData);
    if (selectedPayType !== undefined) {
      paymentData.selectedPayType = selectedPayType;
    }
    formData.append('paymentData', JSON.stringify(paymentData));
  }
  //console.log("formData", formData);

  return formData;
};

const getSelectedPayType = (formData: FormData) => {
  const selectPayTypeItem = props.configJson.surveySchema.find(item => item.type === 'selectPayType');
  if (!selectPayTypeItem) {
    return undefined;
  }
  const selectedPayTypeInput = formData.get(selectPayTypeItem.itemKey);

  const option = selectPayTypeItem.payTypeOptions.find(o => o.input === selectedPayTypeInput)
  return option ? option.payType : undefined;
}

const getFormDataJson = async (userId: any, isRegisterPayment = false): Promise<any> => {
  const formData = await getFormData(isRegisterPayment);
  //console.log(formData);
  if (formData) {
    let paymentData;
    let fileData;
    const surveyResults: any[] = [];
    for (const pair of formData.entries()) {
      if (pair[0] === 'paymentData') {
        paymentData = JSON.parse(pair[1] as any);
        continue;
      }
      else if (pair[1] === 'attachedFiles') {
        // 添付帳票のデータ取得
        fileData = refs.value[pair[0]].getFiles();
      }
      surveyResults.push({
        itemKey: pair[0],
        title: getTitle(pair[0]),
        value: pair[1],
      });
    }
    const orderId = props.isConfirmSurveyMode && props.configJson.usePayment === 1 && inputOrderId.value.getValue();

    let surveyId = props.configJson.surveyId;
    if (!surveyId) {
      surveyId = props.configJson._surveyConfig.surveyId ? props.configJson._surveyConfig.surveyId : props.configJson._surveyConfig.memberSurveyId;
    }

    return {
      surveyId,
      userId,
      surveyResults,
      paymentData,
      orderId,
      fileData,
    };
  }
  return null;
};

const getTitle = (key: any): any => {
  let _item = props.configJson.surveySchema.find(
    (obj: { itemKey: any; }) => obj.itemKey === key
  );
  return _item ? _item.title : "";
};

const choiceGroupheaderRadioClick = (input: any, params: any): void => {
  //console.log("choiceGroupheaderRadioClick", input, params);

  //surveySchemaUpdatedFlag.value++;

  let radio_selected_value = input;

  let section_options_keys = Object.keys(params.sectionOptions);

  section_options_keys.forEach((section_options_key) => {
    let sectionOption = params.sectionOptions[section_options_key];

    if (radio_selected_value == sectionOption.option.value) {
      sectionOption.groupheader.disabled = false;
    } else {
      sectionOption.groupheader.disabled = true;
    }
  });

  //console.log("section_options_keys.value", section_options_keys);

  let surveySchemaKeys = Object.keys(surveySchema1.value);
  surveySchemaKeys.forEach((schema_key) => {
    let schemaElement = surveySchema1.value[schema_key];
    if (schemaElement.type === "groupheader") {
      section_options_keys.forEach((section_options_key) => {
        let sectionOption = params.sectionOptions[section_options_key];
        if (schemaElement.title === sectionOption.groupheader.value) {
          schemaElement.disabled = sectionOption.groupheader.disabled;

          // inside groupheader, there is/are ChoiceGroupheader need to be disabled/enabled
          let subElements = schemaElement.items;
          subElements.forEach((subElement: { type: string; sectionOptions: any[]; }) => {
            if (subElement.type === "choicegroupheader") {
              if (
                subElement.sectionOptions &&
                Array.isArray(subElement.sectionOptions)
              ) {
                subElement.sectionOptions.forEach((subSectionOption: { groupheader: { disabled: any; }; }) => {
                  subSectionOption.groupheader.disabled =
                    schemaElement.disabled;
                });
              }
            }
          });
        }
      });
    }
  });
  updateGroupheader();
};

const updateGroupheader = (): void => {
  surveySchema1.value.forEach((element: { type: string; title: any; itemKey: string | number; disabled: any; items: any[]; }) => {
    if (element.type === "groupheader") {
      let radioGroupheaderDataArray = Object.values(
        radioGroupheaderData.value
      );
      radioGroupheaderDataArray.forEach((elementGroupheaderData) => {
        let elementGroupheaderDataArray = Object.values(
          elementGroupheaderData
        );
        elementGroupheaderDataArray.forEach((groupheaderData) => {
          if (groupheaderData.value == element.title) {
            groupheaderData.itemKey = element.itemKey;
            element.disabled = groupheaderData.disabled;

            groupheaderPanelValues.value[element.itemKey] == element.disabled
              ? []
              : [0];

            return;
          }
        });
      });

      //console.log("element.disabled", element);

      if (element.disabled) {
        element.items.forEach((subElement: { type: any; input: string | any[]; }) => {
          switch (subElement.type) {
            case "checkboxes":
              subElement.input = [];
              break;
            case "radio":
              subElement.input = undefined;
              break;
            default:
              subElement.input = "";
              break;
          }
        });
      }
    }
  });
  if (store) {
    store.setSurveySchema1(surveySchema1.value)
  }
  //params.input = input;
  //console.log("params", params.input);
};

const handleCheckSaibanExisting = (payload: any): void => {
  emit("handleCheckSaibanExisting", payload);
};

const handleCheckSaibanExistingForUpdate = (payload: any): void => {
  emit("handleCheckSaibanExistingForUpdate", payload);
};

const handleSetPaymentInfo = (categoryId: string | null, itemKey: string): void => {
  emit('setPaymentInfo', categoryId, itemKey);
};

const renderToLink = (text: string): string => {
  if (text) {
    if (text.includes('\n')) {
      return markdownIt.value.render(text).replace(/\n/g, '<br>');
    }
    return markdownIt.value.render(text)
  }
  return '';
}

const updateIsFormValid = (valid: boolean) => {
  emit('updateIsFormValid', valid);
}

const setHeaderDefaultDisplay = () => {
  headerImgDisplay.value = false;
};

// watch
watch(() => props.configJson, (newVal) => {
  // Reset reactive surveySchema1, since it gets appended with new data instead of updating it
  // 新しいデータを追加する代わりに、リアクティブなsurveySchema1をリセットします。
  surveySchema1.value = [];

  const configJson = cloneDeep(props.configJson);
  if (
    configJson.inputs === undefined ||
    configJson.inputs === null ||
    configJson.inputs.length === 0
  ) {
    surveySchema.value = configJson.surveySchema?.map(function (item: { default: any; input: any; type: string }) {
      if (item.default !== undefined && item.default !== null && item.type !== 'selectProducts') {
        item.input = item.default;
      }
      return item;
    });
  }
  else {
    surveySchema.value = configJson.surveySchema.map(function (item: {
      itemKey: string | number;
      input: any;
      reservationDate: any;
      fileData: string;
      type: string
    }) {
      let input: any;

      if (configJson.inputs.fields) {
        const matchedValues = configJson.inputs.fields.filter((elm) => elm.itemKey === item.itemKey);

        if (item.type === "checkboxes") {
          input = matchedValues.map((val) => val.value).filter((val) => val !== null); // Get all values and remove null
        } else if(item.type === "radio") {
          input = matchedValues.map((val) => val.value).filter((val) => val !== null)[0]; // Get first non null value
        } else if (item.type === "choicegroupheader") {
          input = matchedValues.map((val) => val.value).filter((val) => val !== null)[0]; // Get first non null value

        } else {
          input = matchedValues.length > 0 ? matchedValues[0].value ?? undefined : undefined; // Take first value if exists
          //console.log("matchedValues", matchedValues);  
        }

        item.fileData = matchedValues.length > 0 ? matchedValues[0].files ?? undefined : undefined;
      } else {
        input = configJson.inputs[item.itemKey];
      }

      // Ensure input is not null if it's an array
      if (Array.isArray(input)) {
        input = input.filter((val) => val !== null);
      }

      item.input = input;

      if (typeof input === "string" && input.startsWith("category#")) {
        if (configJson.inputs.reservationDate !== undefined) {
          item.reservationDate = configJson.inputs.reservationDate;
        }
      }

      return item;
    });
  }

  surveySchemaTemp.value = cloneDeep(surveySchema.value);

  let groupHeaderFlag = false;
  let elementTemp: any = {};
  const countVaccineInput = configJson.surveySchema?.find(item => item.type === 'countVaccines')?.default;
  const isFirstVaccination = parseInt(countVaccineInput) === 1;
  const previousVaccineItemTypes = ['previousVaccineMaker', 'previousVaccineDate'];

  surveySchemaTemp.value?.forEach((element: { isAdminItem?: any; type?: any; categoryRequiredFlag?: any; calendarLabelDisplay?: any; sectionOptions?: any; input?: any; default?: any; itemKey?: any; disabled?: any; }) => {
    if (
      (props.isLiffMode &&
        element.isAdminItem !== undefined &&
        element.isAdminItem) ||
      (isFirstVaccination && previousVaccineItemTypes.includes(element.type))
    ) {
      return;
    }
    switch (element.type) {
      case "reservation":
        element.categoryRequiredFlag = props.categoryRequiredFlag;
        element.calendarLabelDisplay = props.calendarLabelDisplay;
        break;

      case "choicegroupheader":
        if (element.sectionOptions && Array.isArray(element.sectionOptions)) {
          element.sectionOptions.forEach((sectionOption: { groupheader: any; option: { value: string | number; }; }) => {
            let groupheader = sectionOption.groupheader;
            if (groupheader) {
              groupheader.disabled = false;
              groupheader.itemKey = null;
              if (groupheader.value) {
                groupheader.key = sectionOption.option.value;
                if (
                  (element.input &&
                    groupheader.key &&
                    element.input == groupheader.key) ||
                  (!element.input &&
                    element.default &&
                    groupheader.key &&
                    element.default == groupheader.key)
                ) {
                  groupheader.disabled = false;
                } else {
                  groupheader.disabled = true;
                }
              } else {
                groupheader.key = null;
              }

              if (!radioGroupheaderData.value[element.itemKey]) {
                radioGroupheaderData.value[element.itemKey] = {};
              }
              radioGroupheaderData.value[element.itemKey][
                sectionOption.option.value
              ] = sectionOption.groupheader;
            }
          });
        }
        break;

      case "groupheader":
        groupheaderData.value[element.itemKey] = element;
        // Auto-open/close expansion panels
        groupheaderPanelValues.value[element.itemKey] = true;

        break;

      default:
        break;
    }

    if (element.type !== "groupheader") {
      if (element.type === "choicegroupheader") {
        groupHeaderFlag = false;
        if (elementTemp.items !== undefined) {
          surveySchema1.value.push(elementTemp);
          elementTemp = {};
        }
      }
      if (groupHeaderFlag == false) {
        surveySchema1.value.push(element);
      } else {
        if (elementTemp.items === undefined) {
          elementTemp.items = [];
        }
        elementTemp.items.push(element);
      }
    } else {
      //groupHeader in the first time;
      if (groupHeaderFlag == false) {
        groupHeaderFlag = true;

        element.disabled = false;

        elementTemp = element;
        elementTemp.items = [];
      } else {
        // groupHeader from second times
        // push the elementTemp to the last surveySchema
        surveySchema1.value.push(elementTemp);

        // reset elementTemp
        elementTemp = {};

        // set element to elementTemp
        elementTemp = element;
        elementTemp.items = [];
      }
    }
  });
  //case: the group header in the last of surveySchema -> add elementTemp to the last of surveySchema.value
  if (elementTemp.items !== undefined) {
    surveySchema1.value.push(elementTemp);
  }

  {
    // Check inside groupheader
    surveySchema1.value.forEach((element: { type: string; input: any; default: any; sectionOptions: string | any[]; itemKey: string | number; }) => {
      if (element.type === "choicegroupheader") {
        let input =
          element.input || (!element.input ? element.default : null);
        if (
          input == null &&
          Array.isArray(element.sectionOptions) &&
          element.sectionOptions.length > 0 &&
          element.sectionOptions[0].option
        ) {
          input = element.sectionOptions[0].option.value;
          element.input = input;
        }
        if (input) {
          choiceGroupheaderRadioClick(input, {
            sectionOptions: element.sectionOptions,
          });

          //console.log("radioGroupheaderData", radioGroupheaderData.value);

          if (
            radioGroupheaderData.value[element.itemKey].disabled ===
            false
          ) {
            let groupheader_itemKey =
              radioGroupheaderData.value[element.itemKey][input].itemKey;
            groupheaderPanelValues.value[groupheader_itemKey] = true;
          }
        }
      }
    });
  }

  required.value = props.configJson.surveySchema?.some(
    (item: { isRequired: { value: any; admin: any; }, type: string, isRequiredForUser: any }) =>
      (props.isAdminMode === false &&
        (item.isRequired.value || (item.isRequiredForUser && item.isRequiredForUser.value))) ||
      (props.isAdminMode === true &&
        (!("admin" in item.isRequired) || item.isRequired.admin === null) &&
        item.isRequired.value) ||
      (props.isAdminMode === true && item.isRequired.admin)
      || (!props.isAdminMode && item.type === 'previousVaccineMaker')
      || (!props.isAdminMode && item.type === 'previousVaccineDate')
  );

}, { deep: true, immediate: true });

watch(() => props.isSaibanExisted, (newVal) => {
  if(store) {
    store.setSaibanExisted(newVal);
  }
},
{ immediate: true }
);

// hooks

onBeforeMount(() => {
  setActivePinia(props.pinia);
  store = useFormRenderingStore();
  setPropsData();
  refs.value = [];
});

// mount
onMounted(async () => {

});

// Update the answers object when a child component emits an updated value
const updateAnswer = (key, newValue) => {
  nextTick(() => {
    answers.value[key] = newValue;
  });
};
const updateSaibanExisted = (saibanExisted: boolean) => {
  store.setSaibanExisted(saibanExisted);
}

const validateForm = async () => {
  const isValid = await allFormValid.value.validate();
  return isValid;
};

defineExpose({
  getFormData,
  getFormDataJson,
  validateForm,
  initOrder,
  updateSaibanExisted
});
</script>

<style scoped>
.border-top-primary {
  border-top: 10px solid v-bind(accent);
}

.print-applicant-details-item {
  display: none;
}

.liffItemImg .v-image__image {
  max-height: 300px !important;
  background-size: contain !important;
}

.liffItemImg .v-responsive__content,
.v-image.v-responsive.liffItemImg {
  max-height: 300px !important;
}

.userInfoTitle {
  border-radius: 0px;
  border-top-left-radius: 0.8rem;
  border-bottom-right-radius: 0.8rem;
}

.survey-title {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.survey-description ::v-deep(a) {
  color: var(--q-primary);
}
::v-deep(.survey-description p) {
  margin-bottom: 16px;
}
</style>

<style scoped></style>