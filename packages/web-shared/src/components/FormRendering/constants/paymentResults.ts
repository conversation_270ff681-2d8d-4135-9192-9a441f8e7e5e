
export const TAX_TYPES = {
  OUTER: 0, // 外税
  INNER: 1 // 内税
};

export const TAX_TYPE_TEXTS = {
  [TAX_TYPES.OUTER]: '外',
  [TAX_TYPES.INNER]: '内',
};

export const PAYMENT_STATES = {
  COMPLETED: 0,
  NOT_APPLICABLE: 1,
  REFUNDED: 2,
  COMPLETED_CREDIT: 3,
  CANCELED_CREDIT: 4,
};

export const PAYMENT_STATUS_TEXTS = {
  [PAYMENT_STATES.COMPLETED]: '決済完了',
  [PAYMENT_STATES.NOT_APPLICABLE]: '決済対象外',
  [PAYMENT_STATES.REFUNDED]: '返金済み',
  [PAYMENT_STATES.COMPLETED_CREDIT]: '与信完了',
  [PAYMENT_STATES.CANCELED_CREDIT]: '与信取消',
};

export const MODAL_ACTIONS = {
  CREATE: 'CREATE',
  EDIT: 'EDIT',
};

export const TAXABLE_TYPES = {
  TAXABLE: 0, // 課税対象
  TAX_FREE: 1, // 非課税
};

export const ROUNDING_TYPES = {
  FLOOR: 0, // 切捨て
  CEIL: 1, // 切上げ
  ROUND: 2, // 四捨五入
};

export const CALCULATION_TYPES = {
  FOR_EACH: 0, // 明細ごと
  RESULT: 1, // 合計
};

export const RESERVATION_COST_TYPE = {
  FIXED_ONE: 0, // 1固定
  ADDTION: 1, // 合算
};

const PAY_METHODS = {
  CREDIT: 'credit',
  LINE_PAY: 'linepay',
  PAYPAY: 'paypay',
} as const;

export const PAY_METHOD_TEXTS = {
  [PAY_METHODS.CREDIT]: 'クレジットカード',
  [PAY_METHODS.LINE_PAY]: 'LINE Pay',
  [PAY_METHODS.PAYPAY]: 'PayPay'
}