<script lang="ts">
import { computed, ref, reactive } from "vue";
import { Params } from "@/types";

export function useInputBase() {
  // data
  const params = reactive<Params>(null);
  const isNotEditable = ref(false);
  const forceNotEditable = ref(false);
  const isLiffMode = ref(false);
  const isAdminMode = ref(false);
  const isConfirmSurveyMode = ref(false);
  const isQuiryKey = ref(false);

  // computed
  const title = computed(() => params.title);
  const description = computed(() => params.description);
  const disabled = computed(() => {
    if (forceNotEditable) {
      return true;
    } else if (isConfirmSurveyMode) {
      return false;
    }
    return isNotEditable && isLiffMode;
  });

  return {
    params,
    isNotEditable,
    forceNotEditable,
    isLiffMode,
    isAdminMode,
    isConfirmSurveyMode,
    isQuiry<PERSON>ey,
    title,
    description,
    disabled,
  };
}

export default {
  setup() {
    return useInputBase();
  }
}
</script>
