<template>
  <div>
    <q-page-container>
      <div
        v-for="(sectionOption, index) in sectionOptions"
        no-gutters
        :key="index"
        class="row"
      >
        <div :class="'col-' + cols.col1">
          <q-radio
            v-model="input"
            :val="sectionOption.option.value"
            :class="store.isLiffMode ? 'tw-my-1' : ''"
            :label="sectionOption.option.value"
            style="display: flex"
            @click="radioClick"
            :disable="disabled"
          />
        </div>
        <div v-if="store.isAdminMode && !hasCountVaccines" :class="'col' + cols.col2">
          <q-input
            v-model="sectionOption.groupheader.value"
            :label="label(index)"
            dense
            flat
            type="number"
            suffix="日"
            disable
          />
        </div>
      </div>
    </q-page-container>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, ref } from "vue";
import { useFormRenderingStore } from '../../../stores/modules/formRenderingStore/index';

// store
const store = useFormRenderingStore();

// props
const props = defineProps<{
  params?: any,
  isNotEditable?: boolean,
  hasCountVaccines?: boolean,
}>();

// emit
const emit = defineEmits(['choice-groupheader-radio-click']);

// data
const cols = ref({
  col1: 8,
  col2: 4,
});
const sectionOptions = ref();
const input = ref();

// mounted
onBeforeMount(() => {
  let prm = Object.assign(
    {
      sectionOptions: [{option:{value:'a'}}, {option:{value:'b'}}],
      input: 'a',
    },
    props.params,
  );

  sectionOptions.value = prm.sectionOptions;
  input.value = prm.input;

  if (store.isAdminMode) {
    cols.value.col1 = 8;
  } else {
    cols.value.col1 = 12;
  }
});

// methods
const label = (index: number) => {
  return index === 0 ? "1回目接種翌日からの接種間隔日数" : null;
};

const radioClick = (): void => {
  emit(
    "choice-groupheader-radio-click",
    input.value,
    props.params
  );
};

const disabled = computed(() => {
  if (store.forceNotEditable) {
    return true;
  } else if (store.isConfirmSurveyMode) {
    return false;
  }
  return props.isNotEditable && store.isLiffMode;
});

const getValue = () => {
  return input.value;
};

defineExpose({
  getValue,
});
</script>
