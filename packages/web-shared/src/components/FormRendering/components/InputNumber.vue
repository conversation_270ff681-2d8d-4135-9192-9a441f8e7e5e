<template>
  <div>
    <q-input
      v-model="input"
      :rules="[
        validateRequired,
        validateIsNumber,
        validateRange,
        (v) => store.validateLength(v, length),
      ]"
      inputmode="numeric"
      :disable="disabled"
      :maxlength="length.limitation || 16"
      counter
      @change="handleBlur"
    ></q-input>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, ref, onMounted, watch } from "vue";
import { useFormRenderingStore } from '../../../stores/modules/formRenderingStore/index';
import { requireNumberGreaterThanOrEqualTo, requireNumberLessThanOrEqualTo, requireTextIsNumber } from "../utils/validationUtils";

// store
const store = useFormRenderingStore();

// props
const props = defineProps<{
  modelValue?: string | null,
  params?: any,
  isNotEditable?: boolean,
}>();

// emit
const emit = defineEmits(['change', "update:modelValue"]);

// data 
const range = ref();
const input = ref(props.modelValue ?? null);
const itemKey = ref();
const options = ref();

// computed
const required = computed(() => props.params.isRequired?.value ?? false);
const isRequiredForUser = computed(() => props.params.isRequiredForUser?.value ?? false);
watch(() => props.modelValue, (val) => {
  input.value = val;
});
// onMounted
onBeforeMount(() => {
  let prm = Object.assign(
    {
      range: { min: null, max: null },
      input: "",
    },
    props.params,
  );

  itemKey.value = prm.itemKey;
  options.value = prm.options;
  range.value = {
    min:
      prm.range.min === null ||
      prm.range.min === "" ||
        isNaN(prm.range.min)
          ? null
          : parseInt(prm.range.min),
    max:
      prm.range.max === null ||
      prm.range.max === "" ||
        isNaN(prm.range.max)
          ? null
          : parseInt(prm.range.max),
  };
});

onMounted(() => {
  if (props.modelValue) {
    input.value = props.modelValue
  } else {
    if(props.params.input) {
    input.value = props.params.input;
  } else if(props.params.default) {
    input.value = props.params.default;
  }
  }
});

//　methods
const getValue = () => {
  return !input.value ? undefined : input.value;
};

const validateRequired = (input: any) => {
  const validateResult = !!input || "必須です";
  if (store.isAdminMode) {
    return !required.value || validateResult;
  } else if (store.isLiffMode) {
    return (!required.value && !isRequiredForUser.value) || validateResult;
  }
};

const validateIsNumber = (input: any) => {
  if (!input) {
    return true;
  }

  let textisnumber = requireTextIsNumber(input);

  if (textisnumber) {
    return true;
  } else {
    let message = "0~9以外が入力されています";
    return message;
  }
};

const validateRange = (input: any) => {
  if (!input) {
    return true;
  }

  const rng = range.value;

  let validMin =
    rng.min === null || rng.min == ""
      ? true
      : requireNumberGreaterThanOrEqualTo(input, rng.min);

  let validMax =
    rng.max === null || rng.max == ""
      ? true
      : requireNumberLessThanOrEqualTo(input, rng.max);

  if (validMin && validMax) {
    return true;
  } else {
    let message = "範囲内の数値を入力してください。";
    return message;
  }
};

const length = computed(() => {
  if(props.params?.length) {
    let result = props.params.length;

    result.limitation = 16;

    result.max =
      result.max === null || result.max === undefined
        ? result.limitation
        : result.max >= result.limitation
        ? result.limitation
        : result.max;
    
    result.min = result.min === null ? 0 : result.min;

    return result;
  }
  else {
    return { min: null, max: 16, limitation: 16 };
  }
});

const disabled = computed(() => {
  if (store.forceNotEditable) {
    return true;
  } else if (store.isConfirmSurveyMode) {
    return false;
  }
  return props.isNotEditable && store.isLiffMode;
});
const handleBlur = () => {
  emit("update:modelValue", input.value);
};
defineExpose({
  getValue,
});
</script>
