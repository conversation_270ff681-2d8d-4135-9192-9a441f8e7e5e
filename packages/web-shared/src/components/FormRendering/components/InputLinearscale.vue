<template>
  <div>
    <ul class="linearscale" style="padding-left: 0">
      <li>{{ params?.scaleMin.message }}</li>
      <li v-for="scale in scales" :key="scale">
        {{ scale }}
        <span>
          <q-radio 
            v-model="input" 
            :val="scale" 
            :rules="[validateRequired]" 
            hide-details
          >
          </q-radio>
        </span>
      </li>
      <li>{{ params?.scaleMax.message }}</li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { onBeforeMount, ref } from "vue";
import { useFormRenderingStore } from '../../../stores/modules/formRenderingStore/index';

// store
const store = useFormRenderingStore();

// props
const props = defineProps<{
  params?: any,
  isNotEditable?: boolean,
}>();

// data
const required = ref();
const scales = ref();
const isRequiredForUser = ref();
const scaleMax = ref();
const scaleMin = ref();
const input = ref();
const itemKey = ref();

onBeforeMount(() => {
  let prm = Object.assign(
    {
      isRequired: { value: false },
      isRequiredForUser: { value: false },
      scaleMax: { value: 10, message: "" },
      scaleMin: { value: 0, message: "" },
      input: undefined,
    },
    props.params,
  );

  scales.value = [];
  for (
    let i = prm.scaleMin.value;
    i <= prm.scaleMax.value;
    i++
  ) {
    scales.value.push(i);
  }
  required.value = prm.isRequired.value;
  isRequiredForUser.value = prm.isRequiredForUser.value;
  scaleMax.value = prm.scaleMax.value;
  scaleMin.value = prm.scaleMin.value;
  input.value = prm.input;
  itemKey.value = prm.itemKey;
});

// methods
const validateRequired = (input: any): boolean => {
  const isValid = !!input;
  if (store.isAdminMode) {
    return !required.value || isValid;
  } else if (store.isLiffMode) {
    return (!required.value && !isRequiredForUser.value) || isValid;
  }
};

const getValue = () => {
  return input.value;
};

defineExpose({
  getValue,
});
</script>

<style>
.v-radio {
  display: block;
}
ul.linearscale {
  display: table;
  table-layout: fixed;
  padding-left: 0;
  margin: auto;
  width: 100%;
}
ul.linearscale li {
  display: table-cell;
  vertical-align: middle;
  text-align: center;
}
.v-application--is-ltr .v-input--selection-controls__input {
  margin-right: 0;
}
</style>
