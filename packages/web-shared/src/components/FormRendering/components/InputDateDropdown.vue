<template>
  <div>
    <div class="row">
      <div class="col-4 tw-px-1">
        <q-select
          clearable
          dense
          :label="yearLabel"
          v-model="selectedYear"
          :options="years"
          :rules="[validateRequiredYear]"
          @change="updateSelectedDate"
        ></q-select>
      </div>
      <div class="col-4 tw-px-1">
        <q-select
          clearable
          dense
          :label="monthLabel"
          v-model="selectedMonth"
          :options="months"
          :rules="[validateRequiredMonth]"
          @change="updateSelectedDate"
        ></q-select>
      </div>
      <div class="col-4 tw-px-1">
        <q-select
          clearable
          dense
          :label="dayLabel"
          v-model="selectedDay"
          :options="days"
          :rules="[validateRequiredDay]"
          @change="updateSelectedDate"
        ></q-select>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { onMounted,ref } from "vue";
import { useFormRenderingStore } from '../../../stores/modules/formRenderingStore/index';

// store
const store = useFormRenderingStore();

const props = withDefaults(
  defineProps<{
    // The lowest year option
    minYear: number,
    // The highest year option
    maxYear: number,
    // The format of the date string
    defaultDateFormat: string,
  }>(), 
  {
    minYear: 1900,
    maxYear: dayjs().year(),
    defaultDateFormat: "YYYY-MM-DD",
  }
);

// data
const yearLabel = ref("年");
const monthLabel = ref("月");
const dayLabel = ref("日");
const years = ref([]);
const months = ref([]);
const days = ref([]);

const dateInputVal = ref(null);
const dateInputDt = ref(null);
const selectedDate = ref(null);

const selectedYear = ref(dayjs().format("YYYY")); // The selected year
const selectedMonth = ref(dayjs().format("MM")); // The selected month
const selectedDay = ref(dayjs().format("DD")); // The selected day

const formValid = ref(true)
// TODO
const required = ref(null);
const isRequiredForUser = ref(null);
const itemKey = ref(null);

// methods
const padStart2 = (value: any): string => {
  return String(value).padStart(2, "0");
};

const getDaysInMonth = (year: any, month: any): Array<string> => {
  let dt = dayjs(year + month, "YYYYMM");
  let days = Array.from({ length: dt.daysInMonth() }, (x, i) =>
    dt.startOf("month").add(i, "days").format("DD")
  );
  return days;
};

const setYearSelect = (): void => {
  // init years
  for (let i = props.minYear; i <= props.maxYear; i++) {
    years.value.push(String(i));
  }
};

const setMonthSelect = (): void => {
  // init months
  for (let i = 1; i <= 12; i++) {
    months.value.push(padStart2(i));
  }
};

const setDaySelect = (): void => {
  days.value = getDaysInMonth(selectedYear.value, selectedMonth.value);
};

const updateSelectedDate = (): void => {
  if (!selectedYear.value || !selectedMonth.value || !selectedDay.value) {
    selectedDate.value = null;
    return;
  }

  let dt = dayjs(
    selectedYear.value + selectedMonth.value + selectedDay.value,
    "YYYYMMDD",
    true
  ).format("YYYY-MM-DD");
  selectedDate.value = dt;
};

const getValue = (): any => {
  return selectedDate.value;
};

const validateRequiredYear = (): any => {
  const validateResult = selectedYear.value || "年を選択してください。";
  if (store.isAdminMode) {
    return !required.value || validateResult;
  } else if (store.isLiffMode) {
    return (!required.value && !isRequiredForUser.value) || validateResult;
  }
};

const validateRequiredMonth = (): any => {
  const validateResult = selectedMonth.value || "月を選択してください。";
  if (store.isAdminMode) {
    return !required.value || validateResult;
  } else if (store.isLiffMode) {
    return (!required.value && !isRequiredForUser.value) || validateResult;
  }
};

const validateRequiredDay = (): any => {
  const validateResult = selectedDay.value || "日を選択してください。";
  if (store.isAdminMode) {
    return !required.value || validateResult;
  } else if (store.isLiffMode) {
    return (!required.value && !isRequiredForUser.value) || validateResult;
  }
};

// onMounted
onMounted(() => {
  var params = Object.assign(
    {
      options: [],
      isRequired: { value: false },
      isRequiredForUser: { value: false },
      input: undefined,
    },
    params
  );

  itemKey.value = params.itemKey;
  required.value = params.isRequired.value;
  isRequiredForUser.value = params.isRequiredForUser.value;
  dateInputVal.value = params.input;

  // set select  year, month, day by input date
  if (dateInputVal.value) {
    let dt = dayjs(dateInputVal.value, props.defaultDateFormat);
    if (dt.isValid()) {
      dateInputDt.value = dt;
      selectedYear.value = dt.format("YYYY");
      selectedMonth.value = dt.format("MM");
      selectedDay.value = dt.format("DD");
    }
  }

  setYearSelect();  // init years
  setMonthSelect(); // init months
  setDaySelect();   // init days
  updateSelectedDate();
});

defineExpose({
  getValue,
});
</script>
