<template>
  <q-dialog
    v-model="isShowModal"
    transition="dialog-bottom-transition"
    max-width="400"
  >
    <q-card class="q-pa-sm" style="width: 100%;">
      <q-form v-model="isValid" ref="formOrderDetail" @submit="onSaveProduct" novalidate>
        <div class="q-pb-sm">
          <h3 class="header-text q-my-none">{{ headerTitle }}</h3>
        </div>
        <div class="q-mb-sm">
          <div class="q-pb-sm">商品分類</div>
          <ProductCategorySelector
            :product-category-id="detail.productCategoryId"
            :product-category-name="detail.productCategoryName"
            :productCategories="productCategories"
            :detailIndex="editingIndex"
            :model="model"
            @updateOrderDetail="(categoryId) => {
              detail.productCategoryId = categoryId;
              if (categoryId) {
                detail.productCategoryName = productCategories.find(c => c.id === categoryId).name;
              }
            }"
          />
        </div>
        <div class="q-mb-sm">
          <div class="q-pb-sm">商品</div>
          <ProductSelector
            :product-category-id="detail.productCategoryId"
            :product-id="detail.productId"
            :products="products"
            :detailIndex="editingIndex"
            :has-payment-result="false"
            :selectedProductIds="selectedProductIds"
            @update-product="onProductSelected"
          />
        </div>
        <div class="q-mb-sm">
          <div class="q-pb-sm">数量</div>
          <ProductCountInput
            :product-count="detail.count"
            :detailIndex="editingIndex"
            :isOverPurchaseLimit="isOverPurchaseLimit"
            :purchaseLimit="purchaseLimit"
            @update-count="onCountChanged"
          />
        </div>
        <div class="q-mb-sm">
          <div class="q-pb-sm">金額(円)</div>
          <div>{{ amount }}</div>
        </div>
        <div class="flex justify-end">
          <q-btn
            depressed
            color="primary"
            outlined
            @click="isShowModal = false"
          >
            キャンセル
          </q-btn>
          <q-btn
            v-if="!isCreateMode"
            class="q-ml-sm"
            depressed
            color="negative"
            @click="onRemoveOrderDetail"
          >
            削除
          </q-btn>
          <q-btn
            class="q-ml-sm"
            depressed
            color="primary"
            type="submit"
            :disable="isOverPurchaseLimit || !isValid"
          >
            {{ buttonText }}
          </q-btn>
        </div>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { cloneDeep } from 'lodash';

import { Products } from '../../../../types/index';
import { MODAL_ACTIONS, TAXABLE_TYPES } from '../../constants/paymentResults';

import ProductCategorySelector from './ProductCategorySelector.vue';
import ProductSelector from './ProductSelector.vue';
import ProductCountInput from './ProductCountInput.vue';
import { ModalOrderDetail } from "@/components/FormRendering/components/InputSelectProducts/types";

const props = withDefaults(
  defineProps<{
    visible: boolean,
    selectedDetail: any,
    editingIndex?: number,
    action: string,
    products: Products[],
    productCategories: any[],
    model: any,
    purchaseLimit: number,
    totalCount: number,
    selectedProductIds: string[]
  }>(),
  {
    editingIndex: -1,
  }
);

// emit
const emit = defineEmits([
  'close', 
  'addOrderDetail', 
  'updateOrderDetail', 
  'removeOrderDetail',
]);

// data
const isValid = ref(true);
const detail = ref({
  productCategoryName: '',
  productId: null,
  productCategoryId: null,
  productName: null,
  count: 0,
  amount: 0,
  tax: 0,
  amountWithTax: 0,
  taxableType: null,
  price: 0,
} as ModalOrderDetail)

const countBackUp =  ref(0);
const formOrderDetail = ref(null);

// computed
const isShowModal = computed({
  get() {
    return props.visible;
  },
  set(value: boolean) {
    if (!value) {
      closeModal();
    }
  }
});

const isCreateMode = computed(() => props.action === MODAL_ACTIONS.CREATE);

const isOverPurchaseLimit = computed(() => {
  if (props.purchaseLimit === 0) {
    return false;
  }
  switch (props.action) {
    case MODAL_ACTIONS.CREATE: {
      return (props.totalCount + detail.value.count) > props.purchaseLimit;
    }
    case MODAL_ACTIONS.EDIT: {
      return (props.totalCount - countBackUp.value + detail.value.count) > props.purchaseLimit;
    }
    default: {
      return false;
    }
  }
});

const buttonText = computed(() => isCreateMode.value ? '追加' : '保存');

const headerTitle = computed(() => {
  const baseText = '商品明細';
  return baseText + (isCreateMode.value ? '追加' : '編集');
});

const amount = computed(() => {
  const amount = detail.value.amount;
  return amount ? amount.toLocaleString() : 0;
});

const onProductSelected = (id: string) => {
  const toUpdate: ModalOrderDetail = {
    productCategoryName: '',
    productCategoryId: '',
    ...detail.value,
    productId: id,
    productName: '',
    count: 1,
    amount: 0,
    amountWithTax: 0,
    tax: 0,
    price: 0,
    taxableType: null,
  };
  const targetProduct = props.products.find(c => c.productId === id);
  if (targetProduct) {
    const isTaxFree = targetProduct.taxableType === TAXABLE_TYPES.TAX_FREE;
    toUpdate.productName = targetProduct.productName;
    toUpdate.price = targetProduct.price;
    toUpdate.taxableType = targetProduct.taxableType;
    toUpdate.amount = toUpdate.price * toUpdate.count;
    toUpdate.tax = isTaxFree ? 0 : props.model.calculateTax(toUpdate.amount);
    toUpdate.amountWithTax = props.model.calculateDetailAmountWithTax(toUpdate.amount, toUpdate.tax);
  }
  Object.assign(detail.value, toUpdate)
}

const onCountChanged = (count: number) => {
  const { price, taxableType } = detail.value;
  const isTaxFree = taxableType === TAXABLE_TYPES.TAX_FREE;
  const _count = count
  const amount =  price * _count;
  const tax = isTaxFree ? 0 : props.model.calculateTax(amount);
  const amountWithTax = props.model.calculateDetailAmountWithTax(amount, tax);
  const toUpdate = {
    ...detail.value,
    amount,
    amountWithTax,
    tax,
    count: _count,
  }
  Object.assign(detail.value, toUpdate)
}

// methods
const closeModal = () => {
  detail.value = {
    productCategoryName: '',
    productId: null,
    productCategoryId: null,
    productName: null,
    count: 0,
    amount: 0,
    tax: 0,
    amountWithTax: 0,
    taxableType: null,
    price: 0
  };
  emit('close')
};

const onSaveProduct = () => {
  if (!formOrderDetail.value.validate()) {
    return;
  }
  if (isCreateMode.value) {
    emit('addOrderDetail', detail.value);
  } else {
    emit('updateOrderDetail', detail.value, props.editingIndex);
  }
  closeModal();
};

const onRemoveOrderDetail = () => {
  emit('removeOrderDetail', props.editingIndex);
  closeModal();
};


// watch
watch(
  () => props.visible,
  (newVal) => {
    Object.assign(detail, props.selectedDetail);
    if (newVal && formOrderDetail.value) {
      formOrderDetail.value.resetValidation();
    }
  }
);

watch(
  () => props.selectedDetail,
  (newVal) => {
    detail.value = cloneDeep(newVal);
    countBackUp.value = newVal.count;
  }
);
</script>

<style scoped>
.header-text {
  font-weight: 700;
  font-size: 26px !important;
  line-height: 150%;
}
</style>