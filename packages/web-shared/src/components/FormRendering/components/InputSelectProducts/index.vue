<template>
  <div v-if="isFetchingPaymentInfo">
    <div class="row">
      <div class="tw-flex tw-justify-center col">
        <q-spinner color="primary" />
      </div>
    </div>
  </div>
  <div v-else class="q-pa-none q-pt-sm">
    <div class="row" v-if="orderError" no-gutters>
      <div class="text-negative text-caption col">{{ orderError }}</div>
    </div>
    <ServiceNameViewer :serviceName="order.serviceName" :isLiffMode="store.isLiffMode" />
    <DetailLabel :isLiffMode="store.isLiffMode" />
    <div class="row" no-gutters>
      <div class="col">
        <q-separator />
      </div>
    </div>
    <template v-for="(item, index) in order.details" :key="index">
      <div
          no-gutters
          class="detail-mobile q-mx-0 q-py-sm row"
          @click.prevent="showDetailEditModal(item, index)"
      >
        <div class="q-pl-sm col-5">
          {{ item.productName }}
        </div>
        <div class="q-pl-sm col-3">
          {{ item.count }}
        </div>
        <div class="q-pl-sm flex items-center col-4">
          <span>{{ item.amount.toLocaleString() }}</span>
          <span class="q-pl-sm">{{ isTaxFree(item.taxableType) ? '※' : '' }}</span>
        </div>
      </div>
      <div class="row" no-gutters>
        <div class="col">
          <q-separator />
        </div>
      </div>
    </template>
    <div no-gutters class="q-pt-sm row">
      <div class="col">
        <q-btn
          :disable="!isEditableOrder || isOverOrSamePurchaseLimit || !!taxError"
          color="primary"
          unelevated
          size="sm"
          class="q-mb-sm"
          @click="onClickAddDetail"
        >
          商品明細を追加
        </q-btn>
      </div>
    </div>
    <OrderResults :order="order" :isLiffMode="store.isLiffMode" />
    <OrderDetailInputModal
      :visible="isShowInputOrderDetailModal"
      :selectedDetail="selectedDetail"
      :editingIndex="selectedDetailIndex"
      :action="modalAction"
      :products="products"
      :productCategories="productCategories"
      :model="model"
      :purchaseLimit="purchaseLimit"
      :totalCount="totalCount"
      :selectedProductIds="selectedProductIds"
      @close="closeDetailInputModal"
      @addOrderDetail="addOrderDetail"
      @updateOrderDetail="updateOrderDetail"
      @removeOrderDetail="removeOrderDetail"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, toRaw } from 'vue';
import { cloneDeep } from 'lodash';
import PaymentResultsModel from '../../models/paymentResults';
import { Products } from '../../../../types';

import ServiceNameViewer from './ServiceNameViewer.vue';
import DetailLabel from './DetailLabel.vue';
import OrderResults from './OrderResults.vue';
import OrderDetailInputModal from './OrderDetailInputModal.vue';
import { useFormRenderingStore } from '../../../../stores/modules/formRenderingStore/index';
import { MODAL_ACTIONS, TAXABLE_TYPES } from '../../constants/paymentResults';
import { ModalOrderDetail } from './types';

// store
const store = useFormRenderingStore();

const DEFAULT_DETAIL: ModalOrderDetail = {
  detailId: 1,
  productCategoryId: '',
  productCategoryName: '',
  productId: '',
  productName: '',
  price: 0,
  count: 1,
  tax: 0,
  amount: 0,
  taxableType: 0,
  amountWithTax: 0,
  cost: 0,
};

const DEFAULT_ORDER = {
  serviceSortKey: '',
  serviceId: '',
  serviceName: '',
  details: [],
  tax: 0,
  amount: 0,
  amountWithTax: 0,
  taxRate: 0,
  calculationType: 0,
  roundingType: 0,
  taxType: 0,
  reservationCostType: 0,
};

const props = defineProps<{
  paymentResult?: any,
  taxRate?: number,
  paymentService?: any,
  products?: Products[],
  isInputCategory?: boolean,
  isFetchingPaymentInfo?: boolean,
}>();

// data
const order = ref(cloneDeep(DEFAULT_ORDER));
const productCategories = ref<any[]>([]);
const isShowInputOrderDetailModal = ref(false);
const selectedDetailIndex = ref(-1);
const selectedDetail = ref({});
const modalAction = ref('CREATE');
const model = ref<PaymentResultsModel>(null);

// computed
const hasPaymentResult = computed(() => !!props.paymentResult);
const isEditableOrder = computed(() => props.paymentService && !hasPaymentResult.value);
const totalCount = computed(() => order.value.details.reduce((total, d) => total += d.count, 0));
const purchaseLimit = computed(() => props.paymentService ? props.paymentService.purchaseLimit : 0);
const isOverPurchaseLimit = computed(() => purchaseLimit.value === 0 ? false : totalCount.value > purchaseLimit.value);
const isOverOrSamePurchaseLimit = computed(() => purchaseLimit.value === 0 ? false : totalCount.value >= purchaseLimit.value);

const taxError = computed(() => {
  if (!props.paymentResult && !props.taxRate) {
    return '消費税設定が未設定です。';
  }
  return null;
});

const orderDetailError = computed(() => {
  if (order.value.details.length === 0) {
    return '商品明細を追加してください';
  }
  if (isOverPurchaseLimit.value) {
    return `購入可能上限数を超えています\n全ての商品合わせて${purchaseLimit.value}個までです`;
  }
  return '';
});

const orderError = computed(() => taxError.value || orderDetailError.value);
const selectedProductIds = computed(() => order.value.details.map(d => d.productId));

// methods
const getValue = () => JSON.stringify(order.value);

const setModel = () => {
  model.value = new PaymentResultsModel(
    order.value.taxRate,
    order.value.roundingType,
    order.value.calculationType,
    order.value.taxType,
    order.value.reservationCostType,
  );
};

const initOrder = () => {
  order.value = cloneDeep(DEFAULT_ORDER);
  setModel();
};

const setProductCategories = () => {
  const pdc: any[] = [];
  props.products?.forEach(product => {
    product.productCategories.forEach(category => {
      const isDuplicate = pdc.some(c => c.id === category.id);
      if (!isDuplicate) {
        pdc.push(category);
      }
    });
  });
  productCategories.value = pdc.sort((a, b) => a.name > b.name ? 1 : -1);
};

const setOrder = () => {
  const serviceSortKey = props.paymentService?.serviceSortKey || '';
  if (props.paymentResult) {
    const paymentResult = cloneDeep(props.paymentResult);
    order.value = {
      serviceSortKey,
      serviceId: paymentResult.serviceId,
      serviceName: paymentResult.serviceName,
      details: paymentResult.details,
      tax: paymentResult.tax,
      amount: paymentResult.amount,
      amountWithTax: paymentResult.amountWithTax,
      taxRate: paymentResult.taxRate,
      calculationType: paymentResult.calculationType,
      roundingType: paymentResult.roundingType,
      taxType: paymentResult.taxType,
      reservationCostType: props.paymentService ? props.paymentService.reservationCostType : 0,
      status: paymentResult.status,
      completedDate: paymentResult.completedDate,
      canceledDate: paymentResult.canceledDate,
      orderId: paymentResult.orderId,
      reservationCost: paymentResult.reservationCost || undefined,
      payMethod: paymentResult.payMethod || undefined,
    }
  } else if (props.paymentService) {
    order.value = {
      serviceSortKey,
      serviceId: props.paymentService.serviceId,
      serviceName: props.paymentService.serviceName,
      details: [],
      tax: 0,
      amount: 0,
      amountWithTax: 0,
      taxRate: props.taxRate,
      calculationType: props.paymentService.calculationType,
      roundingType: props.paymentService.roundingType,
      taxType: props.paymentService.taxType,
      reservationCostType: props.paymentService.reservationCostType,
      reservationCost : 0,
    }
  } else {
    initOrder();
  }
  setModel();
  onSyncOrderData();
};

const onClickAddDetail = () => {
  if (!isEditableOrder.value || isOverOrSamePurchaseLimit.value) {
    return;
  }
  showDetailAddModal();
};

const addOrderDetail = (detail = cloneDeep(DEFAULT_DETAIL)) => {
  detail.detailId = order.value.details.length + 1;
  if (props.isInputCategory) {
    updateReservationInfoToDetail(detail);
  }
  order.value.details.push(detail);
  onSyncOrderData();
};

const updateOrderDetail = (toUpdate: ModalOrderDetail, index: number) => {
  if (props.isInputCategory) {
    updateReservationInfoToDetail(toUpdate);
  }
  order.value.details[index] = toUpdate;
  onSyncOrderData();
};

const showDetailAddModal = () => {
  selectedDetailIndex.value = - 1;
  selectedDetail.value = cloneDeep(DEFAULT_DETAIL);
  modalAction.value = MODAL_ACTIONS.CREATE;
  isShowInputOrderDetailModal.value = true;
};

const showDetailEditModal = (detail: ModalOrderDetail, index: number) => {
  const copy = cloneDeep(toRaw(detail))
  if (!isEditableOrder.value) {
    return;
  }
  selectedDetailIndex.value = index;
  selectedDetail.value = copy;
  modalAction.value = MODAL_ACTIONS.EDIT;
  isShowInputOrderDetailModal.value = true;
};

const closeDetailInputModal = () => {
  selectedDetail.value = {};
  isShowInputOrderDetailModal.value = false;
};

const onSyncOrderData = () => {
  order.value.tax = model.value.calculateTotalTax(order.value.details);
  order.value.amount = model.value.calculateTotalAmount(order.value.details);
  if (props.isInputCategory) {
    order.value.reservationCost = model.value.calculateTotalCost(order.value.details);
  }
};

const removeOrderDetail = (index) => {
  order.value.details.splice(index, 1);
  const arr = cloneDeep(order.value.details);
  order.value.details = arr.map((x, i) => ({...x, detailId: i + 1}));
  onSyncOrderData();
};

const isTaxFree = (taxableType: number) => {
  return taxableType === TAXABLE_TYPES.TAX_FREE;
};

const updateReservationInfoToDetail = (detail: ModalOrderDetail) => {
  if (detail.productId) {
    const product = props.products.find(p => p.productId === detail.productId);
    detail.cost = product.cost * detail.count;
    detail.reservationPaymentItemId = product.reservationPaymentItemId;
  } else {
    detail.cost = 0;
    detail.reservationPaymentItemId = null;
  }
}

// watch
watch(
  () => props.paymentService,
  (newVal) => {
    if (newVal) {
      setOrder();
    } else {
      initOrder();
    }
    setModel();
  }
);

watch(() => props.products, setProductCategories);

watch(
  () => props.paymentResult,
  (newVal) => {
    if (newVal) {
      setOrder();
      setModel();
    }
  }
);

// onMounted
onMounted(() => {
  setOrder();
  setProductCategories();
});

defineExpose({
  getValue,
  initOrder
});
</script>

<style scoped>
.detail-mobile:hover {
  background-color: #EEEEEE;
}
</style>