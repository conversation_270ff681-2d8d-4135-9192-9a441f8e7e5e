<template>
  <div class="tw-py-4 row">
    <div class="tw-py-0 col">
      <div class="tw-pb-1 text-caption text-grey text-darken-3">サービス名</div>
      <div class="tw-flex tw-align-center col">
        <span>{{ serviceName }}</span>
        <q-icon 
          class="tw-mx-1" 
          size="18"
          name="mdi-information-outline"
          v-if="!store.isLiffMode"
        >
          <q-tooltip right>
            <span>※SBペイメントサービスでは請求情報の商品名となります</span>
          </q-tooltip>
        </q-icon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useFormRenderingStore } from '../../../../stores/modules/formRenderingStore/index';

// store
const store = useFormRenderingStore();

const props = withDefaults(
  defineProps<{
    serviceName: string,
  }>(),
  {
    serviceName: '',
  }
);
</script>