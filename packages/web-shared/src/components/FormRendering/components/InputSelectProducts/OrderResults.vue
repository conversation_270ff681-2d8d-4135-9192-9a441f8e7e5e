<template>
  <div no-gutters class="text-caption text-grey text-darken-3 row">
    <div class="tw-py-2 col-12">
      <q-separator />
    </div>
    <div class="col-8">
      小計
    </div>
    <div class="col-4">
      {{ subTotal }}
    </div>
    <div class="tw-py-2 col-12">
      <q-separator />
    </div>
    <div class="col-8">
      消費税 (<span>{{ taxTypeText }}</span>) {{ order.taxRate }}%
      <div v-if="hasTaxFreeInOrderDetails">※非課税商品</div>
    </div>
    <div class="col-4">
      {{ order.tax?.toLocaleString() }}
    </div>
    <div class="tw-py-2 col-12">
      <q-separator />
    </div>
    <div class="col-8">
      合計金額
    </div>
    <div class="col-4">
      {{ order.amount?.toLocaleString() }}
    </div>
    <template v-if="!store.isLiffMode">
      <div class="tw-py-2 col-12">
        <q-separator />
      </div>
      <div class="col-8">
        支払方法
      </div>
      <div class="col-4">
        {{ payMethodText }}
      </div>
      <div class="tw-py-2 col-12">
        <q-separator />
      </div>
      <div class="col-8">
        決済ステータス
      </div>
      <div class="col-4">
        {{ paymentStatusText }}
      </div>
      <div class="tw-py-2 col-12">
        <q-separator />
      </div>
      <div class="col-8">
        決済完了日時
      </div>
      <div class="col-4">
        {{ completedDate }}
      </div>
      <div class="tw-py-2 col-12">
        <q-separator />
      </div>
      <div class="col-8">
        キャンセル日時
      </div>
      <div class="col-4">
        {{ canceledDate }}
      </div>
      <div class="tw-py-2 col-12">
        <q-separator />
      </div>
      <div class="col-8">
        <span>受付番号</span>
        <q-icon
          class="mx-1"
          size="18"
          name="mdi-information-outline"
        >
          <q-tooltip v-if="!store.isLiffMode" right>
            <span>※SBペイメントサービスでは受注IDとなります</span>
          </q-tooltip>
        </q-icon>
      </div>
      <div class="col-4">
        {{ orderId }}
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { DateTime } from 'luxon';
import { PAY_METHOD_TEXTS, PAYMENT_STATUS_TEXTS, TAX_TYPE_TEXTS } from '../../constants/paymentResults';
import { useFormRenderingStore } from '../../../../stores/modules/formRenderingStore/index';

// store
const store = useFormRenderingStore();

const props = defineProps<{
  order: any,
}>();

// computed
const subTotal = computed(() => {
  const subTotal = props.order.details.reduce((total: number, detail) => {
    return total += detail.amount;
  }, 0);
  return subTotal.toLocaleString();
});

const hasTaxFreeInOrderDetails = computed(() => {
  const taxFreeValue = 1;
  return props.order.details.some(d => d.taxableType === taxFreeValue);
});

const taxTypeText = computed(() => TAX_TYPE_TEXTS[props.order.taxType] || '');

const completedDate = computed(() => {
  const date = props.order.completedDate;
  return formatDate(date);
});

const canceledDate = computed(() => {
  const date = props.order.canceledDate;
  return formatDate(date);
});

const orderId = computed(() => props.order.orderId || '--');

const paymentStatusText = computed(() => {
  const statusText = PAYMENT_STATUS_TEXTS[props.order.status];
  return statusText || '--';
});

const payMethodText = computed(() => PAY_METHOD_TEXTS[props.order.payMethod] || '--');

// methods
const formatDate = (value: number) => {
  return value ? DateTime.fromSeconds(value).toFormat('yyyy-MM-dd HH:mm:ss') : '--';
};
</script>