<template>
  <div>
    <q-input type="textarea"
      autogrow
      v-model="input"
      counter
      :rules="[
        (v) => store.validateRequired(v, required, isRequiredForUser),
        (v) => store.validateLength(v, length),
        (v) => store.validateType(v, characterTypeList),
        (v) => store.validateFormat(v, inputFormat),
      ]"
      :maxlength="maxLength || 300"
      :disable="disabled"
      lazy-rules
      @change="handleBlur"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { useFormRenderingStore } from '../../../stores/modules/formRenderingStore/index';

// store
const store = useFormRenderingStore();

// props
const props = defineProps<{
  modelValue: string | null,
  params?: any,
  isNotEditable?: boolean,
}>();

// data
const input = ref<string | null>(props.modelValue ?? null);
const emit = defineEmits(["update:modelValue"]);

// computed
const required = computed(() => props.params?.isRequired.value ?? false);
const characterTypeList = computed(() => props.params?.characterType ?? ["all"]);
const inputFormat = computed(() => props.params?.inputFormat ?? "any");
const isRequiredForUser = computed(() => props.params?.isRequiredForUser?.value ?? false);

watch(() => props.modelValue, (val) => {
  input.value = val;
});

const length = computed(() => {
  if(props.params?.length) {
    let result = props.params.length;

    result.limitation = 300;

    result.max =
      result.max === null || result.max === undefined
        ? result.limitation
        : result.max >= result.limitation
        ? result.limitation
        : result.max;
    
    result.min = result.min === null ? 0 : result.min;

    return result;
  }
  else {
    return { min: null, max: 300, limitation: 300 };
  }
});

const maxLength = computed((): string | number => {
  return !!length.value?.max ? length.value.max : 300;
});

const disabled = computed(() => {
  if (store.forceNotEditable) {
    return true;
  } else if (store.isConfirmSurveyMode) {
    return false;
  }
  return props.isNotEditable && store.isLiffMode;
});

const getValue = () => {
  return input.value;
}

onMounted(() => {
  if(props.params.input) {
    input.value = props.params.input;
  } else if(props.params.default) {
    input.value = props.params.default;
  }
});

const handleBlur = () => {
  emit("update:modelValue", input.value);
};

defineExpose({
  getValue,
});
</script>
