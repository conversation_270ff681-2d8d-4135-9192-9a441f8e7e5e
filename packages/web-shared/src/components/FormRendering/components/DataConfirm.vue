<template>
  <div>
    <q-list v-model="localState.panel" multiple tile>
      <template v-for="item in surveyResultsHandle" :key="item.itemKey">
        <q-expansion-item
          :label="item.title"
          icon="mdi-check"
        >
          <!-- <q-expansion-panel-header>
            {{ item.title }}
            <template v-slot:actions>
              <q-icon color="teal"></q-icon>
            </template>
          </q-expansion-panel-header> -->
          <q-card>
            <ul>
              <li v-for="input in item.value" :key="`${item.itemKey}_${input}`">
                {{ input }}
              </li>
            </ul>
          </q-card>
        </q-expansion-item>
      </template>
    </q-list>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, ref, watch } from "vue";

const props = defineProps<{
  data: any,
}>();

// data
const localState = ref({
  panel: [-1],
  readonly: false,
  dataLocal: null,
});

// watch
watch(
  () => props.data,
  (newVal) => {
    localState.value.dataLocal = newVal;
  },
  { deep: true, immediate: true,}
);

// computed
const all = computed(() => {
  return [...Array(surveyResultsHandle.value.length).keys()].map(
    (k, i) => i
  );
});

const surveyResultsHandle = computed(() => {
  let _result = [];
  if (localState.value.dataLocal && localState.value.dataLocal.surveyResults.length > 0) {
    let _questionKey:any = null;

    localState.value.dataLocal.surveyResults.forEach((obj: { itemKey: any; title: any; value: any; }) => {
      if (obj.itemKey !== _questionKey) {
        _result.push({
          itemKey: obj.itemKey,
          title: obj.title,
          value: [obj.value],
        });
        _questionKey = obj.itemKey;
      } else {
        let _findIndex = _result.findIndex(
          (item) => item.itemKey === obj.itemKey
        );
        _result[_findIndex].value.push(obj.value);
      }
    });
  }

  return _result;
});

onBeforeMount(() => {
  localState.value.dataLocal = props.data;
});
</script>