<template>
  <div class="row">
    <q-checkbox
      v-for="(option, index) in options"
      v-model="checkboxes[index]"
      :key="option"
      :label="option"
      :rules="[validateRequired, validateMinSelection, validateMaxSelection]"
      :disable="isDisabled(option) || disabled"
      hide-details
      @update:model-value="emit('change')"
      class="col-12"
    ></q-checkbox>

    <span class="text-negative q-pt-sm" style="font-size: small">{{ errorMessage }}</span>

    <q-input
      v-show="false"
      v-model="errorMessage"
      :rules="[
          validateRequired,
          validateMinSelection,
          validateMaxSelection,
        ]"
    >
    </q-input>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeMount, onUnmounted } from "vue";
import { useFormRenderingStore } from '../../../stores/modules/formRenderingStore/index';
import { isEmpty } from 'lodash';

// store
const store = useFormRenderingStore();

// props
const props = defineProps<{
  params?: any,
  isNotEditable?: boolean,
}>();

// emit
const emit = defineEmits(['change']);

// data
const checkboxes = ref([]);
const itemKey = ref(null);
const options = ref();
const selection = ref({min: 0, max: 0});
const errorMessage = ref<string>('');

//computed
const required = computed(() => props.params?.isRequired.value ?? false);
const isRequiredForUser = computed(() => props.params?.isRequiredForUser?.value ?? false);

// onMounted
onBeforeMount(() => {
  let prm = Object.assign(
    {
      options: [],
      selection: { min: 0, max: Infinity },
      input: [],
    },
    props.params
  );

  options.value = prm.options;
  selection.value = prm.selection;
  itemKey.value = prm.itemKey;

  if (selection.value.min == null) {
    selection.value.min = 0;
  }

  if (selection.value.max == null) {
    selection.value.max = Infinity;
  }
  if (prm.input === undefined || prm.input === null || isEmpty(prm.input)) {
    checkboxes.value = Array(options.value.length).fill(false);
  } 
  else if (Array.isArray(prm.input)) {
    checkboxes.value = [];
    // Redone saved result load to a more simple way
    // 回答データを読み込む
    options.value.forEach((option) => {
      checkboxes.value.push(prm.input.indexOf(option) !== -1);
    });
  } 
  else {
    checkboxes.value = [];
  }
});

onUnmounted(() => {
  checkboxes.value = [];
});

// methods
const validateRequired = (): boolean | string => {
  const isValid = checkboxes.value.find((v) => v === true) || '必須です';
  let result;

  if (store.isAdminMode) {
    result = !required.value || isValid;
  } else if (store.isLiffMode) {
    result = (!required.value && !isRequiredForUser.value) || isValid;
  }

  if (result !== true) {
    errorMessage.value = result;
  } else {
    errorMessage.value = '';
  }

  return result;
};

const validateMinSelection = (): boolean | string => {
  const checkedCount = checkboxes.value.filter(Boolean).length;
  if (checkedCount >= selection.value.min) {
    return checkedCount >= selection.value.min
  } else {
     errorMessage.value = `少なくとも${selection.value.min}個を選択してください`
     return
  }
};

const validateMaxSelection = (): boolean | string => {
  const checkedCount = checkboxes.value.filter(Boolean).length;
  if (checkedCount <= selection.value.max) {
    return checkedCount >= selection.value.min
  } else {
     errorMessage.value = `${selection.value.max}個以下で選択してください`
     return 
  }
};

const isDisabled = (input: any): boolean => {
  if (checkboxes.value === undefined) {
    return false;
  }
  return (
    checkboxes.value.filter(Boolean).length >= selection.value.max &&
    !checkboxes.value[options.value.indexOf(input)]
  );
};

const disabled = computed(() => {
  if (store.forceNotEditable) {
    return true;
  } else if (store.isConfirmSurveyMode) {
    return false;
  }
  return props.isNotEditable && store.isLiffMode;
});

const getValue = () => {
  return options.value.filter((_, index) => checkboxes.value[index]);
};

defineExpose({
  getValue,
});
</script>
