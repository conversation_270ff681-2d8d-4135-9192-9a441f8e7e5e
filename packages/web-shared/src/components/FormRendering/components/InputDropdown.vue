<template>
  <q-select
    v-model="input"
    :rules="[(v) => store.validateRequired(v, isRequired, isRequiredForUser)]"
    :options="options"
    option-label="text"
    option-value="value"
    :label="title"
    :disable="disabled"
    @update:model-value="handleBlur"
  ></q-select>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { useFormRenderingStore } from '../../../stores/modules/formRenderingStore/index';

// store
const store = useFormRenderingStore();

// props
const props = defineProps<{
  modelValue?: string | null,
  params?: any,
  isNotEditable?: boolean,
}>();

// emit
const emit = defineEmits(['change',"update:modelValue"]);

// data
const options = ref();
const input = ref();
const itemKey = ref();
const dropdown = ref();
const title = ref();

// computed
const isRequired = computed(() => props.params?.isRequired.value ?? false);
const isRequiredForUser = computed(() => props.params?.isRequiredForUser?.value ?? false);
const disabled = computed(() => {
  if (store.forceNotEditable) {
    return true;
  } else if (store.isConfirmSurveyMode) {
    return false;
  }
  return props.isNotEditable && store.isLiffMode;
});

// onMounted
onMounted(() => {
  let prm = Object.assign(
    {
      options: [],
      input: undefined,
    },
    props.params,
  );
  
  options.value = prm.options;
if (props.modelValue) {
    input.value = props.modelValue
  } else {
    if(props.params.input) {
    input.value = props.params.input;
  } else if(props.params.default) {
    input.value = props.params.default;
  }
  }
  itemKey.value = prm.itemKey;
  dropdown.value = prm.input;
  title.value = prm.title;
});

const handleBlur = () => {
  emit("update:modelValue", input.value);
};


const getValue = () => {
  return input.value;
};

defineExpose({
  getValue,
});
</script>
