<template>
  <div>
    <q-input
      type="email"
      v-model="input"
      counter
      :rules="[
        (v) => store.validateIsEmail(v),
        (v) => store.validateRequired(v, required, isRequiredForUser),
      ]"
      :maxlength="maxLength || 300"
      :disable="disabled"
      @update:model-value="emit('change')"
      @blur="handleBlur"
    />
  </div>
</template>

<script setup lang="ts">
import { useFormRenderingStore } from "../../../stores/modules/formRenderingStore";
import { computed, ref, watch, onMounted } from "vue";

// store
const store = useFormRenderingStore();

// props
const props = defineProps<{
  modelValue?: string | null,
  params?: any,
  isNotEditable?: boolean,
}>();

// emit
const emit = defineEmits(['change',"update:modelValue"]);

// data
const input = ref<string | null>(props.modelValue ?? null);

// computed
const required = computed(() => props.params?.isRequired.value ?? false);
const isRequiredForUser = computed(() => props.params?.isRequiredForUser.value ?? false);

watch(() => props.modelValue, (val) => {
  input.value = val;
});

onMounted(() => {
  if (props.modelValue) {
    input.value = props.modelValue
  } else {
    if(props.params.input) {
    input.value = props.params.input;
  } else if(props.params.default) {
    input.value = props.params.default;
  }
  }
});


const lengthModel = computed(() => {
  if(props.params?.length) {
    let result = props.params.length;

    result.limitation = 300;

    result.max =
      result.max === null || result.max === undefined
        ? result.limitation
        : result.max >= result.limitation
        ? result.limitation
        : result.max;
    
    result.min = result.min === null ? 0 : result.min;

    return result;
  }
  else {
    return { min: null, max: 300, limitation: 300 };
  }
});

const maxLength = computed((): string | number => {
  return !!lengthModel.value?.max ? lengthModel.value.max : 300;
});

const disabled = computed(() => {
  if (store.forceNotEditable) {
    return true;
  } else if (store.isConfirmSurveyMode) {
    return false;
  }
  return props.isNotEditable && store.isLiffMode;
});

const getValue = () => {
  return input.value;
};
const handleBlur = () => {
  emit("update:modelValue", input.value);
};
defineExpose({
  getValue,
})
</script>
