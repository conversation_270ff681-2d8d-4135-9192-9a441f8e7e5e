<template>
  <q-input
    v-model="date"
    :clearable="!disabled"
    persistent-hint
    :rules="[(v) => store.validateRequired(v, isRequired, isRequiredForUser)]"
    @update:model-value="onChange"
    :disable="disabled"
    :readonly="!disabled"
  >
    <q-menu
      ref="menu1"
      transition="scale-transition"
      offset-y
    >
      <q-date
        v-model="date"
        year-icon="mdi-calendar-blank"
        mask="YYYY-MM-DD"
        :locale="locale"
        first-day-of-week="0"
        color="blue"
        class="my-datepicker"
        :disable="disabled"
        @update:model-value="emit('change')"
      ></q-date>
    </q-menu>
    <template #append>
      <q-btn
        v-if="!disabled"
        flat
        round
        dense
        icon="mdi-close-circle"
        @click.stop="clearDate"
      />
    </template>
  </q-input>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { useFormRenderingStore } from '../../../stores/modules/formRenderingStore/index';

// store
const store = useFormRenderingStore();

// props
const props = defineProps<{
  modelValue?:any,
  params?: any,
  isNotEditable?: boolean,
}>();

// emit
const emit = defineEmits(['change', 'update:modelValue']);

//data
const date = ref();
const menu1 = ref(false);
const options = ref();
const input = ref();
const itemKey = ref();
const locale = ref();
const dateFormatted = ref();

// computed
const isRequired = computed(() => props.params?.isRequired.value ?? false);
const isRequiredForUser = computed(() => props.params?.isRequiredForUser?.value ?? false);

// watch
watch(
  () => date.value,
  (newVal) => {
    emit('update:modelValue', newVal);
    dateFormatted.value = formatDate(date.value);
  }
);

// onMounted
onMounted(() => {
  let prm = Object.assign(
    {
      options: [],
      input: undefined,
    },
    props.params,
  );

  options.value = prm.options;
  itemKey.value = prm.itemKey;
  locale.value = navigator.language;
  if (props.modelValue || props.modelValue === null) {
     date.value = props.modelValue
  } else {
  date.value = props.params.input ? props.params.input : undefined
  }
  dateFormatted.value = formatDate(prm.input);
});

// methods
const formatDate = (date: string): string => {
  if (!date) return null;

  const [year, month, day] = date.split("-");
  return `${year}/${month}/${day}`;
};

const parseDate = (date: string): string => {
  if (!date) return null;

  const [year, month, day] = date.split("/");
  return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
};

const onChange = (): void => {
  date.value = parseDate(dateFormatted.value);
};

const clearDate = (): void => {
  date.value = null;
  dateFormatted.value = null;
  menu1.value = false;
};


const disabled = computed(() => {
  if (store.forceNotEditable) {
    return true;
  } else if (store.isConfirmSurveyMode) {
    return false;
  }
  return props.isNotEditable && store.isLiffMode;
});

const getValue = () => {
  return date.value;
};

defineExpose({
  getValue,
});
</script>

<style scoped>
.my-datepicker >>> .q-date-header__value button[type="button"]::after {
  content: " ▼";
}
</style>
