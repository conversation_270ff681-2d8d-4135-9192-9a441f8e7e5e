<template>
    <q-option-group
        :options="chikuOptions"
        :option-label="option => option"
        :option-value="option => option"
        v-model="input">
    </q-option-group>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeMount } from 'vue';
import { useQuasar } from 'quasar';

// props
const props = defineProps<{
    chikuOptions?: any;
    selectedChiku?: any;
}>();

// data
const input = ref(null);



// method

onBeforeMount(() => {
    input.value = undefined;
});

const getValue = () => {
    return input.value;
}

// defineExpose
defineExpose({
    getValue,
});

</script>