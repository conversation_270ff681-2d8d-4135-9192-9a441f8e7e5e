<template>
  <div class="row">
    <div class="col-3">
      <q-select
        v-model="input" 
        outlined
        dense 
        :options="countVaccines"
        :required="true"
        :disable="true"
        hide-details
      ></q-select>
    </div>
    <div class="lebel-count-vaccines tw-pl-4">
      <span>回目接種</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onBeforeMount } from 'vue';

const props = defineProps<{
  params?: any,
}>();

// data
const input = ref(null);
const itemKey = ref(null);

// computed
const countVaccines = computed(() => {
  // 1 ~ 99の数値
  return [...Array(99)].map((_, i) => i + 1);
});

// mounted
onBeforeMount(() => {
  itemKey.value = props.params?.itemKey;
  input.value = Number(props.params?.default ?? 0);
});

// methods
const setValue = (value: any): void => {
  input.value = value;
};

const getValue = () => {
  return input.value;
};

defineExpose({
  getValue,
});
</script>

<style scoped>
.lebel-count-vaccines {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
</style>
