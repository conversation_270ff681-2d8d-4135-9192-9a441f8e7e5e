<template>
  <div>
    <q-input
      v-if="params?.isAutomaticNumbering"
      v-model="inputComp"
      :maxlength="inputMaxLength || 300"
      :disable="inputDisabled && !isConfirmSurveyMode"
      :append-outer-icon="inputIcon"
      :rules="[
        validateRequired,
        validateLength,
        validateAllowedCharacters,
      ]"
    >
    </q-input>

    <div v-else>
      <div class="row">
        <q-input
          v-model="shudouValue"
          clearable
          counter
          :maxlength="inputMaxLength"
          :rules="[
            validateRequired,
            validateLength,
            validateAllowedCharacters,
          ]"
          :max="inputMaxNumber"
          :append-outer-icon="inputIcon"
          :required="required"
          class="col"
        >
        </q-input>
        <div class="col-auto flex items-center q-pl-sm">
          <q-btn :icon="inputIcon" size="md" flat :color="inputButtonCheckExistingColor" @click="handleCheckSaibanExistingShudou"/>
        </div>
      </div>

      <div :class="inputCheckExistingColor">
        <span :class="'text-' + inputCheckExistingColor">{{ input }}</span>
        <q-icon size="xs" class="q-mx-xs" color="grey-7" :name="shudouValidFlagIcon" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, ref, watch } from "vue";
import { useFormRenderingStore } from '../../../stores/modules/formRenderingStore/index';
//import { useMemberStore } from "../../../stores/modules/member/index";
import { isBlank } from "../utils/validationUtils";

// store
const store = useFormRenderingStore();

interface Shudou {
  originalValue: string;
  isAddedNew: boolean;
}

const props = withDefaults(defineProps<{
  params?: any,
  isNotEditable?: boolean,
  configJson?: any,
  isSurveyRegistering?: boolean,
  isConfirmSurveyMode?: boolean,
  isSaibanExisted?: boolean,
}>(),
{
  isSurveyRegistering: true,
  isConfirmSurveyMode: false,
  isSaibanExisted: true,
});

// emit
const emit = defineEmits(['change', 'handleCheckSaibanExisting', 'handleCheckSaibanExistingForUpdate']);

// data
const shudouValue = ref();
const shudouValidFlagIcon = ref<string>('mdi-close-thick');
const shudou = ref<Shudou>({
  originalValue: '',
  isAddedNew: false,
});
const validInput = ref<boolean>(false);
const required = ref();
const itemKey = ref();
const options = ref();

const isSaibanExisted = ref(true);

//const memberStore = useMemberStore();

// computed
const input = ref("");

const inputComp = computed({
  get() {
    let value = props.params.input;
    if (props.params?.isAutomaticNumbering) {
      if (!props.isConfirmSurveyMode) {
        value = props.isSurveyRegistering ? "自動採番" : value;
      } else {
        value = value || "";
      }
    }
    return value;
  },
  set(newValue) {
    input.value = newValue;
  },
});

const inputIcon = computed (
  (): string => props.params?.isAutomaticNumbering
    ? "mdi-auto-fix"
    : "mdi-database-search"
);

//入力文字数表示
const inputMaxLength = computed((): number => {
  let max_length = 11;
  if (!props.params?.isAutomaticNumbering) {
    max_length = 7;
  }
  return max_length;
});

const inputMaxNumber = computed((): number => {
  let limitation = 99999999999; // 11桁数
  if (!props.params?.isAutomaticNumbering) {
    limitation = 9999999; // 7桁数
  }
  return limitation;
});

const inputDisabled = computed((): boolean => {
  let parent_disabled = disabled.value;

  if (parent_disabled || props.params.isAutomaticNumbering) {
    return true;
  }

  return !props.isNotEditable;
});

const inputMemberSurveySubId = computed((): string => {
  let value = ("" + (props.configJson?.memberSurveySubId ?? '')).padStart(
    4,
    "0"
  );
  return value;
});

const inputCheckExistingColor = computed(
  (): string => isValidInput.value
    ? "positive" 
    : "negative"
);

const inputButtonCheckExistingColor = computed((): string => {
  let value = "primary";
  if (!props.isSurveyRegistering) {
    value = "grey";
  }
  return value;
});

const isValidInput = computed(() => {
   if (props.params?.isAutomaticNumbering) {
    return true;
  }
  return !isSaibanExisted.value && validInput.value});

// watch


watch(
  isValidInput,
  (valid): void => {
    if (valid) {
      shudouValidFlagIcon.value = "mdi-check-bold";
    } else {
      shudouValidFlagIcon.value = "mdi-close-thick";
    }
  }
);

watch(
  () => shudouValue.value,
  (newVal) => {
    if(newVal) {
      let num = newVal?.padStart(7, '0');
      input.value = inputMemberSurveySubId.value + num;
    } else {
      input.value = inputMemberSurveySubId.value + "0000000";
    }
    
  }
);

watch(
  () => store.isSaibanExisted,
  (newVal) => {
    isSaibanExisted.value = newVal;
  },
  { immediate: true }
);

// Note: This implementaiton is a placeholder. The actual implementation should call the API to check if the saiban exists inside.
// めも: この実装はプレースホルダです。実際の実装は、APIを呼び出して採番が存在するかどうかを確認する必要があります。
/* const handleCheckSaibanExisting = async (payload: any = { saibanString: "" }): Promise<void> => {
  if (payload.saibanString === "") {
    isSaibanExisted.value = false;
    return;
  }

  let memberSurveyId = props.configJson.surveyId;
  let memberSurveySubId = props.configJson.memberSurveySubId;
  let params = {
    memberSurveyId,
    memberSurveySubId,
    ...payload,
  };
  //let saibanExistResult = await memberStore.checkSaibanExist(params);
  if (saibanExistResult === null || saibanExistResult == undefined || saibanExistResult === false) {
    isSaibanExisted.value = true;
  } else {
    if (
      saibanExistResult.data &&
      (saibanExistResult.data.saibanExistFlag === true ||
        saibanExistResult.data.saibanExistFlag === null ||
        saibanExistResult.data.saibanExistFlag === undefined)
    ) {
      isSaibanExisted.value = true;
    } else {
      isSaibanExisted.value = false;
    }
  }
}; */

// onMounted
onBeforeMount(() => {
  let prm = Object.assign(
    {
      isRequired: { value: false },
      input: '',
    },
    props.params,
  );

  

  isSaibanExisted.value = true;
  required.value = prm.isRequired.value;
  itemKey.value = prm.itemKey;
  options.value = prm.options;

  handleCheckSaibanExistingShudou();


  if (props.params?.isAutomaticNumbering) {
    input.value = prm.input;
  } else {
    shudou.value.originalValue = prm.input?.originalValue;
    shudou.value.isAddedNew = !prm.input?.isAddedNew;
    setShudouValueBySaibanFull(prm.input);
  }
});

// methods
const setShudouValueBySaibanFull = (saibanFull: string): void => {
  if (!saibanFull || typeof saibanFull !== "string") {
    saibanFull = "";
  }
  let saibanString = saibanFull; // for some reason if you substring it, only 3 digits are shown. | なぜかsubstringすると3桁しか表示されない (PNLOSS-429)
  let saibanNumber = saibanString.replace(/^0+/, "");
  shudouValue.value = saibanNumber;
};

const validateRequired = (input: any) => {
  if (props.params?.isAutomaticNumbering) {
    return true;
  }
  return !required.value || !isBlank(input) || "必須です";
};

const validateLength = (input: string | any[]) => {
  if (props.params?.isAutomaticNumbering) {
    validInput.value = true; 
    return true;
  }

  validInput.value = true;

  if (!input) {
    return true;
  }

  const validMax = inputMaxLength.value;
  if (input.length > validMax) {
    validInput.value = false;
    return `${validMax}桁英数字以下で入力してください`;
  }

  return true;
};

const validateAllowedCharacters = (input: string) => {
  if (props.params?.isAutomaticNumbering) {
    validInput.value = true; 
    return true;
  }

  validInput.value = true;

  if (input === "") {
    return true;
  }

  const isValid = /^[0-9a-zA-Z]+$/.test(input);
  if (!isValid) {
    validInput.value = false;
    return "半角の英数字を記入してください。";
  }

  return true;
};


const handleCheckSaibanExistingShudou = (): void => {
  if (shudou.value.isAddedNew) {
    //console.log("handleCheckSaibanExisting", input.value);
    let payload = {
      saibanString: input.value,
      exceptSaibanString: shudou.value.originalValue,
    };
    emit("handleCheckSaibanExisting", payload);
  } else {
    //console.log("handleCheckSaibanExistingShudou", shudou.value.originalValue);
    let payload = {
      saibanString: input.value,
      exceptSaibanString: shudou.value.originalValue,
    };
    emit("handleCheckSaibanExistingForUpdate", payload);
  }
};

const disabled = computed(() => {
  if (store.forceNotEditable) {
    return true;
  } else if (store.isConfirmSurveyMode) {
    return false;
  }
  return props.isNotEditable && store.isLiffMode;
});

const getValue = () => {
  return props.params?.isAutomaticNumbering ? inputComp.value : shudouValue.value;
};

defineExpose({
  getValue,
});
</script>
