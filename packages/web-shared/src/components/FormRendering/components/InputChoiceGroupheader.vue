<template>
  <div>
  <q-option-group
    v-if="type === 'checkboxes' || type === 'radio' || type === 'choicegroupheader'"
    :options="options"
    v-model="inp"
    @update:model-value="radioClick"
    :disable="disabled"
    :rules="[validateRequired]"
    :type="type === 'checkboxes' ? 'checkbox' : 'radio'"
  />
  <q-input
  v-else-if="type === 'date' || type === 'birthday'"
  v-model="inp"
  :disable="disabled"
  clearable
  mask="####-##-##"
  fill-mask
  placeholder="YYYY-MM-DD"
  ref="date-input"
  :rules="[() => true]" 
  @validation-error="() => {}"
  @validation-success="() => {}"
  
>
<!-- TODO: readd validation here too. -->
  <template v-slot:append>
    <q-icon name="event" class="cursor-pointer" @click="showDatePicker = false" />
  </template>
  <q-popup-proxy v-model="showDatePicker" transition-show="scale" transition-hide="scale">
      <q-date v-model="inp" mask="YYYY-MM-DD"></q-date>
  </q-popup-proxy>
</q-input>
</div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import { useFormRenderingStore } from '../../../stores/modules/formRenderingStore/index';

// store
const store = useFormRenderingStore();

// props
const props = defineProps<{
  params?: any,
  isNotEditable?: boolean,
}>();

// emit
const emit = defineEmits(['change', 'choice-groupheader-radio-click']);

// data
const options = ref();
const inp = ref(null);
const itemKey = ref();
const dropdown = ref();
const type = ref();
const showDatePicker = ref(false);

// computed
const required = computed(() => props.params?.isRequired.value ?? false);
const isRequiredForUser = computed(() => props.params?.isRequiredForUser.value ?? false);

// mount
onMounted(() => {
  let prm = Object.assign(
    {
      options: [],
      input: undefined,
    },
    props.params,
  );

  type.value = prm.type;

  //console.log("prm", prm)

  if(type.value !== "choicegroupheader") {
    options.value = prm.options.map(option => ({
      label: option,
      value: option,
    }));
  } else {
    options.value = prm.sectionOptions.map(opt => ({
      label: opt.option.value,
      value: opt.option.value,
    }));
  }


  //console.log('options', prm.input);

  inp.value = prm.input || prm.default || [];

if (props.modelValue) {
    inp.value = props.modelValue
  } else {
    if(props.params.input) {
    inp.value = props.params.input;
  } else if(props.params.default) {
    inp.value = props.params.default;
  }
  }
  // TODO IF needed
  // if(prm.default && prm.default.option) {
  //   inp.value = prm.default.option.value;
  // } else if(prm.input.option) {
  //   inp.value = prm.input.option.value;
  // }
  itemKey.value = prm.itemKey;
  dropdown.value = prm.input;

  // set initial selection for default
  emit('choice-groupheader-radio-click', inp.value, props.params);
});

// methods
const validateRequired = (): boolean => {
  const isValid = inp.value !== undefined;
  if (store.isAdminMode) {
    return !required.value || isValid;
  } else if (store.isLiffMode) {
    return (!required.value && !isRequiredForUser.value) || isValid;
  }
};

const radioClick = (): void => {
  //console.log('input', inp.value);
  if (type.value !== 'choicegroupheader') return;
  //console.log("section", inp.value);
  
  emit('choice-groupheader-radio-click', inp.value, props.params);
  // console.log('params', props.params);

};

const disabled = computed(() => {
  if (store.forceNotEditable) {
    return true;
  } else if (store.isConfirmSurveyMode) {
    return false;
  }
  return props.isNotEditable && store.isLiffMode;
});

const getValue = () => {
  //console.log('inp', inp.value);
  return inp.value;
};

const setValue = (value: any) => {
  inp.value = value;
};

defineExpose({
  getValue
});
</script>
