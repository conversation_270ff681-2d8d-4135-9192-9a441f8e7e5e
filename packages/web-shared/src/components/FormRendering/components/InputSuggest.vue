<template>
  <q-select
    v-model="input"
    :rules="[(v) => store.validateRequired(v, isRequired, isRequiredForUser)]"
    :options="options"
    option-label="text"
    option-value="value"
    :label="title"
    clearable
    use-input
    hide-selected
    fill-input
    @filter="filterFn"
    @update:model-value="handleBlur"
  >
    <template v-slot:no-option>
      <div class="tw-p-4">
        {{
          $q.lang.isoName === 'ja'
            ? '検索結果が見つかりません。'
            : 'No data available'
        }}
      </div>
    </template>
  </q-select>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { useFormRenderingStore } from '../../../stores/modules/formRenderingStore/index';

// store
const store = useFormRenderingStore();
const emit = defineEmits(["update:modelValue"]);

// props
const props = defineProps<{
  modelValue?: string | null,
  params?: any,
}>();

// data
const options = ref();
const originOption = ref();
const input = ref();
const itemKey = ref();
const title = ref();

// computed
const isRequired = computed(() => props.params?.isRequired.value ?? false);
const isRequiredForUser = computed(() => props.params?.isRequiredForUser?.value ?? false);

// method
const filterFn = (value: string, update: any) => {
  if (value === '') {
    update(() => {
      options.value = originOption.value;
    });
    return;
  }
  else {
    update(() => {
      value = value.toLowerCase();
      options.value = originOption.value.filter((elm: any) => 
        elm.toLowerCase().indexOf(value) > -1
      );
    });
  }
};

// onMounted
onMounted(() => {
  let prm = Object.assign(
    {
      options: [],
      input: undefined,
    },
    props.params,
  );

  options.value = prm.options;
  originOption.value = prm.options;
  itemKey.value = prm.itemKey;
  title.value = prm.title;
  if (props.modelValue || props.modelValue === null) {
    input.value = props.modelValue
  } else {
    if(props.params.input) {
    input.value = props.params.input;
  } else if(props.params.default) {
    input.value = props.params.default;
  }
  }
});

const getValue = () => {
  return input.value;
};

const handleBlur = () => {
  emit("update:modelValue", input.value);
};

defineExpose({
  getValue,
});
</script>
