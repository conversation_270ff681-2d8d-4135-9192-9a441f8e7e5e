<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <!-- {{ 'Payment type selector ' }} -->
  <!-- <q-radio v-for="(option, index) in pparams.payTypeOptions" :key="index" :val="option" :disable="isDisabled"
    :label="option" , style="display: flex" v-model="selected" :rules="[validateRequired]" /> -->
    <div>
      <q-radio v-for="(option, index) in pparams.payTypeOptions" :key="index" :val="option.input" :disable="isDisabled"
      :label="option.input" style="display: flex" v-model="selected" :rules="[validateRequired]" />
    </div>
</template>

<script lang="ts" setup>
import Vue, { onMounted, ref, defineProps, defineExpose, computed } from "vue";
import { isBlank } from "../utils/validationUtils";
import { useFormRenderingStore } from '../../../stores/modules/formRenderingStore/index';

// store
const store = useFormRenderingStore();

const PAY_TYPES = {
  CASHLESS: 0,
  CASH: 1
} as const;

const props = defineProps<{
  isLiffMode: boolean,
  isAdminMode: boolean,
  isNewAnswer: boolean,
  params: any,
  disabled?: boolean,
}>();
const selected = ref<string>('');

const pparams = computed<any>(() => {
  return props.params;
});

const isDisabled = computed(() => {
  if (store.forceNotEditable || !props.isNewAnswer) {
    return true;
  } else if (store.isConfirmSurveyMode) {
    return false;
  }
  return pparams.value.isNotEditable;
});

onMounted(() => {
  if (props.isNewAnswer) {
    if (props.isAdminMode) {
      // NOTE: 管理画面かつ新規回答時は初期値に「現金」セットする
      selected.value = pparams.value.input || getOptionByType(PAY_TYPES.CASH).input || pparams.value.default;
    } else {
      // NOTE: LIFFかつ新規回答時は管理画面で設定した初期値をセットする
      selected.value = pparams.value.input || pparams.value.default;
    }
  } else {
    // NOTE: 既に回答されている場合はその値をセットする
    selected.value = pparams.value.input || pparams.value.default;
  }
});

function getOptionByType(payType: number) {
  return pparams.value.payTypeOptions.find(o => o.payType === payType);
}
function validateRequired(): boolean {
  return !isBlank(selected.value);
}

defineExpose({
  getValue() {
    return selected.value;
  },
})

</script>
