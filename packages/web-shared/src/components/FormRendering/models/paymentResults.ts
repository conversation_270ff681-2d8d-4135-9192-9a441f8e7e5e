
import {
  TAXABLE_TYPES,
  TAX_TYPES,
  ROUNDING_TYPES,
  CALCULATION_TYPES,
  RESERVATION_COST_TYPE,
} from '../constants/paymentResults';

type OrderDetail = {
  detailId: number,
  productCategoryId: string | null,
  productCategoryName: string,
  productId: string | null,
  productName: string,
  price: number,
  count: number,
  tax: number,
  amount: number,
  taxableType: number,
  cost?: number;
}

class PaymentResultsModel {
  private roundingType: number;
  private taxRate: number;
  private calculationType: number;
  private taxType: number;
  private reservationCostType: number;

  constructor(
    taxRate: number,
    roundingType: number,
    calculationType: number,
    taxType: number,
    reservationCostType: number,
  ) {
    this.intialValidate(taxRate, roundingType, calculationType, taxType, reservationCostType);
    this.taxRate = taxRate;
    this.roundingType = roundingType;
    this.calculationType = calculationType;
    this.taxType = taxType;
    this.reservationCostType = reservationCostType;
  }

  private intialValidate(taxRate: number, roundingType: number = 2, calculationType: number = 0, taxType: number = 0, reservationCostType: number = 0): void {
    this.validateTaxRate(taxRate);
    this.validateRoundingType(roundingType);
    this.validateCalculationType(calculationType);
    this.validateTaxType(taxType);
    this.validateReservationCostType(reservationCostType);
  }

  private validateTaxRate(value: any): void {
    const isValid = /^[0-9a-zA-Z]+$/.test(value);
    if (!isValid) {
      this.throwError(value, 'taxRate');
    }
  }

  private validateRoundingType(value: any): void {
    const isValid = Object.values(ROUNDING_TYPES).includes(value);
    if (!isValid) {
      this.throwError(value, 'roundingType');
    }
  }

  private validateCalculationType(value: any): void {
    const isValid = Object.values(CALCULATION_TYPES).includes(value);
    if (!isValid) {
      this.throwError(value, 'calculationType');
    }
  }

  private validateTaxType(value: any): void {
    const isValid = Object.values(TAX_TYPES).includes(value);
    if (!isValid) {
      this.throwError(value, 'taxType');
    }
  }

  private validateReservationCostType(value: any): void {
    const isValid = Object.values(RESERVATION_COST_TYPE).includes(value);
    if (!isValid) {
      this.throwError(value, 'reservationCostType');
    }
  }

  private throwError(value: any, targetName: string): void {
    throw new Error(`[${targetName}] is invalid value. The specified value is [${value}]`);
  }

  toRoundingNumber(value: number): number {
    switch (this.roundingType) {
      case ROUNDING_TYPES.FLOOR: {
        return Math.floor(value);
      }
      case ROUNDING_TYPES.CEIL: {
        return Math.ceil(value);
      }
      case ROUNDING_TYPES.ROUND: {
        return Math.round(value);
      }
      default: {
        return value;
      }
    }
  }

  calculateTotalTax(details: OrderDetail[]): number {
    switch (this.calculationType) {
      // 明細ごと
      case CALCULATION_TYPES.FOR_EACH: {
        return details.reduce((tax, detail) => tax + detail.tax ,0);
      }
      // 合計
      case CALCULATION_TYPES.RESULT: {
        const totalAmount = details.reduce((total, d) => {
          const isTaxFree = d.taxableType === TAXABLE_TYPES.TAX_FREE;
          return isTaxFree ? total : total + d.count * d.price;
        }, 0);
        return this.calculateTax(totalAmount);
      }
    }
  }

  calculateTax(amount: number): number {
    let tax = 0;
    switch (this.taxType) {
      // 外税
      case TAX_TYPES.OUTER: {
        tax = this.calculateOuterTax(amount);
        break;
      }
      // 内税
      case TAX_TYPES.INNER: {
        tax = this.calculateInnerTax(amount);
        break;
      }
    }
    return this.toRoundingNumber(tax);
  }

  private calculateOuterTax(amount: number): number {
    return amount * (this.taxRate / 100);
  }

  private calculateInnerTax(amount: number): number {
    return amount - amount / (1 + this.taxRate / 100);
  }

  calculateDetailAmountWithTax(amount: number, tax: number): number {
    switch (this.taxType) {
      case TAX_TYPES.OUTER: {
        if (this.calculationType === CALCULATION_TYPES.FOR_EACH) {
          return amount + tax;
        }
        return amount;
      }
      case TAX_TYPES.INNER: {
        return amount;
      }
    }
  }

  calculateTotalAmount(details: OrderDetail[]): number {
    const amount = details.reduce((total, detail) => total += detail.amount , 0);
    switch (this.taxType) {
      // 外税
      case TAX_TYPES.OUTER: {
        const totalTax = this.calculateTotalTax(details);
        return amount + totalTax;
      }
      // 内税
      case TAX_TYPES.INNER: {
        return amount;
      }
    }
  }

  calculateTotalCost(details: OrderDetail[]): number {
    switch(this.reservationCostType) {
      case RESERVATION_COST_TYPE.FIXED_ONE: {
        return 1;
      }
      case RESERVATION_COST_TYPE.ADDTION: {
        return details.reduce((totalCost, detail) => totalCost += detail.cost , 0);
      }
    }
  }
}

export default PaymentResultsModel;