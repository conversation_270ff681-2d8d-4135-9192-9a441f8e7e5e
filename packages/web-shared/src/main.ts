import { createApp } from "vue";
import App from "./App.vue";
import { createPinia } from "pinia";
import { Quasar, Dialog, Notify } from "quasar";
import "@quasar/extras/material-icons/material-icons.css";
import "quasar/dist/quasar.css";

const app = createApp(App);

app.use(Quasar, {
  plugins: {
    Dialog,
    Notify,
  },
}); // Add Quasar

app.use(createPinia()); // Add Pinia

app.mount("#app");
