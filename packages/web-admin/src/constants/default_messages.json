{"text": {"text": "Hello!"}, "confirm": {"text": "Continue?", "actionLeft": {"type": "postback", "label": "Yes", "text": "Yes", "data": ""}, "actionRight": {"type": "postback", "label": "No", "text": "No", "data": ""}}, "buttons": {"thumbnailImageUrl": "", "title": "Title", "text": "Text", "imageSize": "cover", "actionCount": 2, "actions.0": {"type": "postback", "label": "Action 1", "text": "Action 1", "data": ""}, "actions.1": {"type": "postback", "label": "Action 2", "text": "Action 2", "data": ""}, "actions.2": {"type": "postback", "label": "Action 3", "text": "Action 3", "data": ""}, "actions.3": {"type": "postback", "label": "Action 4", "text": "Action 4", "data": ""}}, "carousel": {"actionCount": 2, "columnCount": 1, "imageAspectRatio": "none", "imageSize": "cover", "useThumbnailImage": false, "useTitle": false, "title.0": "Text", "title.1": "Text", "title.2": "Text", "title.3": "Text", "title.4": "Text", "title.5": "Text", "title.6": "Text", "title.7": "Text", "title.8": "Text", "title.9": "Text", "text.0": "Text", "text.1": "Text", "text.2": "Text", "text.3": "Text", "text.4": "Text", "text.5": "Text", "text.6": "Text", "text.7": "Text", "text.8": "Text", "text.9": "Text", "thumbnail.0": "", "thumbnail.1": "", "thumbnail.2": "", "thumbnail.3": "", "thumbnail.4": "", "thumbnail.5": "", "thumbnail.6": "", "thumbnail.7": "", "thumbnail.8": "", "thumbnail.9": "", "action.0.0": {"type": "postback", "label": "Action 1", "text": "Action 1", "data": ""}, "action.0.1": {"type": "postback", "label": "Action 2", "text": "Action 2", "data": ""}, "action.0.2": {"type": "postback", "label": "Action 3", "text": "Action 3", "data": ""}, "action.1.0": {"type": "postback", "label": "Action 1", "text": "Action 1", "data": ""}, "action.1.1": {"type": "postback", "label": "Action 2", "text": "Action 2", "data": ""}, "action.1.2": {"type": "postback", "label": "Action 3", "text": "Action 3", "data": ""}, "action.2.0": {"type": "postback", "label": "Action 1", "text": "Action 1", "data": ""}, "action.2.1": {"type": "postback", "label": "Action 2", "text": "Action 2", "data": ""}, "action.2.2": {"type": "postback", "label": "Action 3", "text": "Action 3", "data": ""}, "action.3.0": {"type": "postback", "label": "Action 1", "text": "Action 1", "data": ""}, "action.3.1": {"type": "postback", "label": "Action 2", "text": "Action 2", "data": ""}, "action.3.2": {"type": "postback", "label": "Action 3", "text": "Action 3", "data": ""}, "action.4.0": {"type": "postback", "label": "Action 1", "text": "Action 1", "data": ""}, "action.4.1": {"type": "postback", "label": "Action 2", "text": "Action 2", "data": ""}, "action.4.2": {"type": "postback", "label": "Action 3", "text": "Action 3", "data": ""}, "action.5.0": {"type": "postback", "label": "Action 1", "text": "Action 1", "data": ""}, "action.5.1": {"type": "postback", "label": "Action 2", "text": "Action 2", "data": ""}, "action.5.2": {"type": "postback", "label": "Action 3", "text": "Action 3", "data": ""}, "action.6.0": {"type": "postback", "label": "Action 1", "text": "Action 1", "data": ""}, "action.6.1": {"type": "postback", "label": "Action 2", "text": "Action 2", "data": ""}, "action.6.2": {"type": "postback", "label": "Action 3", "text": "Action 3", "data": ""}, "action.7.0": {"type": "postback", "label": "Action 1", "text": "Action 1", "data": ""}, "action.7.1": {"type": "postback", "label": "Action 2", "text": "Action 2", "data": ""}, "action.7.2": {"type": "postback", "label": "Action 3", "text": "Action 3", "data": ""}, "action.8.0": {"type": "postback", "label": "Action 1", "text": "Action 1", "data": ""}, "action.8.1": {"type": "postback", "label": "Action 2", "text": "Action 2", "data": ""}, "action.8.2": {"type": "postback", "label": "Action 3", "text": "Action 3", "data": ""}, "action.9.0": {"type": "postback", "label": "Action 1", "text": "Action 1", "data": ""}, "action.9.1": {"type": "postback", "label": "Action 2", "text": "Action 2", "data": ""}, "action.9.2": {"type": "postback", "label": "Action 3", "text": "Action 3", "data": ""}}, "sticker": {}, "imagemap": {"image2": {"type": "file", "file": {"cloned": false, "cloneId": null, "id": "", "name": "", "type": "", "hash": ""}}, "baseWidth": 1040, "autoDetectedBaseHeight": 0, "actionCount": 0, "action.0": {"quadrant": 0, "type": "message", "text": "アクション 1"}, "action.1": {"quadrant": 0, "type": "message", "text": "アクション 2"}, "action.2": {"quadrant": 0, "type": "message", "text": "アクション 3"}, "action.3": {"quadrant": 0, "type": "message", "text": "アクション 4"}, "action.4": {"quadrant": 0, "type": "message", "text": "アクション 5"}, "action.5": {"quadrant": 0, "type": "message", "text": "アクション 6"}, "action.6": {"quadrant": 0, "type": "message", "text": "アクション 7"}, "action.7": {"quadrant": 0, "type": "message", "text": "アクション 8"}, "action.8": {"quadrant": 0, "type": "message", "text": "アクション 9"}, "action.9": {"quadrant": 0, "type": "message", "text": "アクション 10"}, "action.10": {"quadrant": 0, "type": "message", "text": "アクション 11"}, "action.11": {"quadrant": 0, "type": "message", "text": "アクション 12"}, "action.12": {"quadrant": 0, "type": "message", "text": "アクション 13"}, "action.13": {"quadrant": 0, "type": "message", "text": "アクション 14"}, "action.14": {"quadrant": 0, "type": "message", "text": "アクション 15"}, "action.15": {"quadrant": 0, "type": "message", "text": "アクション 16"}, "action.16": {"quadrant": 0, "type": "message", "text": "アクション 17"}, "action.17": {"quadrant": 0, "type": "message", "text": "アクション 18"}, "action.18": {"quadrant": 0, "type": "message", "text": "アクション 19"}, "action.19": {"quadrant": 0, "type": "message", "text": "アクション 20"}, "action.20": {"quadrant": 0, "type": "message", "text": "アクション 21"}, "action.21": {"quadrant": 0, "type": "message", "text": "アクション 22"}, "action.22": {"quadrant": 0, "type": "message", "text": "アクション 23"}, "action.23": {"quadrant": 0, "type": "message", "text": "アクション 24"}, "action.24": {"quadrant": 0, "type": "message", "text": "アクション 25"}, "action.25": {"quadrant": 0, "type": "message", "text": "アクション 26"}, "action.26": {"quadrant": 0, "type": "message", "text": "アクション 27"}, "action.27": {"quadrant": 0, "type": "message", "text": "アクション 28"}, "action.28": {"quadrant": 0, "type": "message", "text": "アクション 29"}, "action.29": {"quadrant": 0, "type": "message", "text": "アクション 30"}, "action.30": {"quadrant": 0, "type": "message", "text": "アクション 31"}, "action.31": {"quadrant": 0, "type": "message", "text": "アクション 32"}, "action.32": {"quadrant": 0, "type": "message", "text": "アクション 33"}, "action.33": {"quadrant": 0, "type": "message", "text": "アクション 34"}, "action.34": {"quadrant": 0, "type": "message", "text": "アクション 35"}, "action.35": {"quadrant": 0, "type": "message", "text": "アクション 36"}, "action.36": {"quadrant": 0, "type": "message", "text": "アクション 37"}, "action.37": {"quadrant": 0, "type": "message", "text": "アクション 38"}, "action.38": {"quadrant": 0, "type": "message", "text": "アクション 39"}, "action.39": {"quadrant": 0, "type": "message", "text": "アクション 40"}, "action.40": {"quadrant": 0, "type": "message", "text": "アクション 41"}, "action.41": {"quadrant": 0, "type": "message", "text": "アクション 42"}, "action.42": {"quadrant": 0, "type": "message", "text": "アクション 43"}, "action.43": {"quadrant": 0, "type": "message", "text": "アクション 44"}, "action.44": {"quadrant": 0, "type": "message", "text": "アクション 45"}, "action.45": {"quadrant": 0, "type": "message", "text": "アクション 46"}, "action.46": {"quadrant": 0, "type": "message", "text": "アクション 47"}, "action.47": {"quadrant": 0, "type": "message", "text": "アクション 48"}, "action.48": {"quadrant": 0, "type": "message", "text": "アクション 49"}, "action.49": {"quadrant": 0, "type": "message", "text": "アクション 50"}}, "bubbleFlex": {"type": "bubble", "hero": {"type": "image", "url": "https://scdn.line-apps.com/n/channel_devcenter/img/fx/01_1_cafe.png", "size": "full", "aspectRatio": "20:13", "aspectMode": "cover", "action": {"type": "uri", "uri": "http://linecorp.com/"}}, "body": {"type": "box", "layout": "vertical", "contents": [{"type": "text", "text": "Brown Cafe", "weight": "bold", "size": "xl"}, {"type": "box", "layout": "baseline", "margin": "md", "contents": [{"type": "icon", "size": "sm", "url": "https://scdn.line-apps.com/n/channel_devcenter/img/fx/review_gold_star_28.png"}, {"type": "icon", "size": "sm", "url": "https://scdn.line-apps.com/n/channel_devcenter/img/fx/review_gold_star_28.png"}, {"type": "icon", "size": "sm", "url": "https://scdn.line-apps.com/n/channel_devcenter/img/fx/review_gold_star_28.png"}, {"type": "icon", "size": "sm", "url": "https://scdn.line-apps.com/n/channel_devcenter/img/fx/review_gold_star_28.png"}, {"type": "icon", "size": "sm", "url": "https://scdn.line-apps.com/n/channel_devcenter/img/fx/review_gray_star_28.png"}, {"type": "text", "text": "4.0", "size": "sm", "color": "#999999", "margin": "md", "flex": 0}]}, {"type": "box", "layout": "vertical", "margin": "lg", "spacing": "sm", "contents": [{"type": "box", "layout": "baseline", "spacing": "sm", "contents": [{"type": "text", "text": "Place", "color": "#aaaaaa", "size": "sm", "flex": 1}, {"type": "text", "text": "<PERSON>ina Tower, 4-1-6 Shinjuku, Tokyo", "wrap": true, "color": "#666666", "size": "sm", "flex": 5}]}, {"type": "box", "layout": "baseline", "spacing": "sm", "contents": [{"type": "text", "text": "Time", "color": "#aaaaaa", "size": "sm", "flex": 1}, {"type": "text", "text": "10:00 - 23:00", "wrap": true, "color": "#666666", "size": "sm", "flex": 5}]}]}]}, "footer": {"type": "box", "layout": "vertical", "spacing": "sm", "contents": [{"type": "button", "style": "link", "height": "sm", "action": {"type": "uri", "label": "CALL", "uri": "https://linecorp.com"}}, {"type": "button", "style": "link", "height": "sm", "action": {"type": "uri", "label": "WEBSITE", "uri": "https://linecorp.com"}}], "flex": 0}}}