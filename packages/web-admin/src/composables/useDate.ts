import * as dayjs from 'dayjs'
import 'dayjs/locale/ja' // import locale
import relativeTime from 'dayjs/plugin/relativeTime'
import localizedFormat from 'dayjs/plugin/localizedFormat';
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'

dayjs.extend(relativeTime)
dayjs.extend(localizedFormat);
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.locale('ja') // use locale

function formatDateJP(date) {
  let d = dayjs.unix(date).tz('Asia/Tokyo')
  return d.isValid() ? d.format('lll') : ''
}

export function useDate() {
  return {
    formatDateJP
  }
}