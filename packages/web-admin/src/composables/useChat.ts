import { CHAT_FILE_TYPE, CHAT_ROLES, FILE_UPLOAD_SIZE_LIMIT } from '../constants/chat.constants'
import { createGroup, getPresignedUrl, getGroup, getMessages, updateUnReadMessage } from '../services/chat.service'
import type { ChatMessage, ChatRoom, ChatSendFile, ChatUser } from '../types/chat'
import { generateUUID } from '../utils/uuidUtils'
import { useI18n } from 'vue-i18n'

import dayjs from 'dayjs'
import locale from 'dayjs/locale/ja'
import customParseFormat from 'dayjs/plugin/customParseFormat'
// import plugin
import relativeTime from 'dayjs/plugin/relativeTime'
import { computed } from 'vue'

dayjs.locale(locale)
dayjs.extend(relativeTime)
dayjs.extend(customParseFormat)

// #region msg process
function convertWebSocketMessageToChatMessage(message) {
  const msg: ChatMessage = {
    _id: message.id,
    content: message.content,
    senderId: message.sender?.id,
    timestamp: new Date(message.timestamp).toString().substring(16, 21),
    date: formatStringToJapanDateWithoutTime(message.timestamp),
    replyMessage: message.replyMessage,
    files: message.files?.map(item => ({
      ...item,
      type: item.type ? item.content_type : CHAT_FILE_TYPE.IMAGE,
      name: item.file_name,
    })),
  }
  return msg
}
function convertAPIMessagesToChatMessage(message, managerLatestTimeRead: number) {
  const msg: ChatMessage = {
    _id: message.id,
    content: message.content,
    senderId: message.sender?.id,
    timestamp: new Date(message.timestamp).toString().substring(16, 21),
    date: formatStringToJapanDateWithoutTime(message.timestamp),
    replyMessage: message.replyMessage,
    files: message.files?.map(item => ({
      ...item,
      type: item.type ? item.content_type : CHAT_FILE_TYPE.IMAGE,
      url: item.url,
      name: item.file_name,
    })),
    distributed: false,
    saved: true,
  }
  if (message.sender?.role === CHAT_ROLES.LINE_USER) {
    msg.seen = message.timestamp < managerLatestTimeRead
  }
  else {
    // manager msg
    msg.seen = true
  }
  return msg
}
function formatStringToJapanDateWithoutTime(value) {
  const v = dayjs(value)
  return v.isValid() ? v.format('YYYY年MM月DD日 (ddd)') : ''
}
// #endregion

// #region chat service call
async function queryMessageFromAPI(roomID, lastMessageFetchKey?) {
  const query = {
    gid: roomID,
    ...lastMessageFetchKey
      ? {
        lastEvaluatedKey: lastMessageFetchKey,
      }
      : {},
    expiresIn: 60 * 60 * 24,
  }
  try {
    const { items, lastEvaluatedKey }: any = await getMessages(query)
    return {
      items,
      nextEvaluatedKey: lastEvaluatedKey,
    }
  }
  catch (err) {
    throw new Error('chat.err.GET_NEW_MESSAGE')
  }
}
async function queryChatRoom(roomID, userID) {
  let isNewRoom = false
  let roomManagerLastReadTime
  try {
    const result: any = await getGroup({
      uid: userID,
      gid: roomID,
    })
    if (!result) {
      isNewRoom = true
    }
    if (Array.isArray(result?.members)) {
      roomManagerLastReadTime = (result.members as any[]).find(x => x.role === CHAT_ROLES.MANAGER)?.latest_time_read
    }
    return { isNewRoom, roomManagerLastReadTime }
  }
  catch (error) {
    throw new Error('chat.err.QUERY_CHAT_ROOM')
  }
}
async function updateMessageUnread(roomID, userID) {
  try {
    await updateUnReadMessage({
      uid: userID,
      gid: roomID,
      latestTimeRead: new Date().getTime(),
    })
  }
  catch (err) {
    throw new Error('chat.err.UPDATE_UNREAD_MESSAGE')
  }
}
async function createNewRoom(roomId: ChatRoom["roomId"]) {
  try {
    return await createGroup({
      gid: roomId,
    })
  }
  catch (error) {
    throw new Error('chat.err.CREATE_NEW_ROOM')
  }
}
// #endregion

// #region upload file
async function processMessageFilesToS3(files: ChatSendFile[]) {
  let uploadsResult
  let uploadSuccess = false
  try {
    // validate files
    let totalSize = 0
    const uploadPromises: any[] = []
    for (let i = 0; i < files.length; i++) {
      const file = {
        name: files[i].name,
        size: files[i].size,
        type: files[i].type,
        extension: files[i].extension || files[i].type,
        url: files[i].url || files[i].localUrl,
      }
      totalSize += (file.size || 0)
      if (['mp4', 'mov', 'wmv', 'avi'].includes(file.extension)) {
        throw new Error('chat.err.UPLOAD_WRONG_EXTENSION')
      }
      if (totalSize > FILE_UPLOAD_SIZE_LIMIT) {
        throw new Error('chat.err.UPLOAD_FILE_SIZE_LIMIT')
      }

      uploadPromises.push(uploadFilesToS3(file))
    }

    const uploadResponse = await Promise.allSettled(uploadPromises)
    uploadsResult = uploadResponse.filter(res => res.status === 'fulfilled').map((res) => {
      const v = res.value
      return {
        key: v.uploadResponse.key,
        url: v.urlLinkS3?.preSignedUrl,
        content_type: v.uploadResponse?.type_file,
        file_name: v.uploadResponse?.file_name,
        type: v.uploadResponse?.key?.split('/')[0] === 'files' ? 'file' : 'image',
      }
    })
    if (uploadsResult?.length === uploadResponse.length) {
      uploadSuccess = true
    }
  }
  catch (error) {
    throw new Error('chat.err.UPLOAD_FILES')
  }

  return { uploadSuccess, uploadsResult }
}
async function uploadFilesToS3(file) {
  try {
    const uploadResponse: any = await getPresignedUrl({
      expiresIn: 60 * 60 * 24,
      key: generateUUID(),
      fileName: `${file.name}.${file.extension}`,
      method: 'PUT',
      type: file.type.includes('image') ? 'image' : 'file',
      contentType: file.type,
    })
    const response = await fetch(file.url)
    const blob = await response.blob()

    const formData = new FormData()
    for (const [key, val] of Object.entries(uploadResponse.fields)) {
      formData.append(key, val as any)
    }
    formData.append('Content-Type', file.type)
    formData.append('file', blob)
    await fetch(uploadResponse.preSignedUrl, {
      method: 'POST',
      body: formData,
    })

    const urlLinkS3 = await getPresignedUrl({
      expiresIn: 60 * 60 * 24,
      key: uploadResponse.key,
      method: 'GET',
      type: file.type.includes('image') ? 'image' : 'file',
      contentType: file.type,
      fileName: `${file.name}.${file.extension}`,
    })

    uploadResponse.file_name = `${file.name}.${file.extension}`
    uploadResponse.type_file = file.type

    return { uploadResponse, urlLinkS3 }
  }
  catch (error) {
    console.error(error)
    throw new Error('chat.err.UPLOAD_FILES')
  }
}
// #endregion

// #region export
export function useChat() {
  const { t } = useI18n()
  const CHAT_UI_MESSAGE = computed(() => ({
    ROOMS_EMPTY: t('chat.ui.ROOMS_EMPTY'),
    ROOM_EMPTY: t('chat.ui.ROOM_EMPTY'),
    NEW_MESSAGES: t('chat.ui.NEW_MESSAGES'),
    MESSAGE_DELETED: t('chat.ui.MESSAGE_DELETED'),
    MESSAGES_EMPTY: t('chat.ui.MESSAGES_EMPTY'),
    CONVERSATION_STARTED: t('chat.ui.CONVERSATION_STARTED'),
    TYPE_MESSAGE: t('chat.ui.TYPE_MESSAGE'),
    SEARCH: t('chat.ui.SEARCH'),
    IS_ONLINE: t('chat.ui.IS_ONLINE'),
    LAST_SEEN: t('chat.ui.LAST_SEEN'),
    IS_TYPING: t('chat.ui.IS_TYPING'),
    CANCEL_SELECT_MESSAGE: t('chat.ui.CANCEL_SELECT_MESSAGE'),
  }))
  const CHAT_UI_ACTION_MESSAGE = computed(() => ([
    {
      name: 'replyMessage',
      title: t('chat.ui.REPLY_MESSAGE'),
    },
  ]))

  return {
    CHAT_UI_MESSAGE,
    CHAT_UI_ACTION_MESSAGE,

    convertWebSocketMessageToChatMessage,
    convertAPIMessagesToChatMessage,
    queryMessageFromAPI,
    updateMessageUnread,
    queryChatRoom,
    createNewRoom,
    formatStringToJapanDateWithoutTime,
    processMessageFilesToS3,
  }
}
// #endregion
