/// <reference types="vite/client" />
interface ImportMetaEnv {
  readonly VITE_ADMIN_WEB_URL: string
  readonly VITE_AMPLIFY_ADMIN_API_ENDPOINT_URL: string
  readonly VITE_AMPLIFY_AUTH_USER_POOL_WEB_CLIENT_ID: string
  readonly VITE_AMPLIFY_AUTH_USER_POOL_ID: string
  readonly VITE_AMPLIFY_SCENARIO_API_ENDPOINT_URL: string
  readonly VITE_CQ_API_ENDPOINT_URL: string
  readonly VITE_AMPLIFY_BOSAI_API_ENDPOINT_URL: string
  readonly VITE_SCENARIO_CLOUDFRONT_DOMAIN_NAME: string
  readonly VITE_USE_PAYMENT: string
  readonly VITE_DAMAGE_REPORT_API_ENDPOINT_URL: string
  readonly VITE_DAMAGE_REPORT_CHAT_WS_ENDPOINT: string
  readonly VITE_GOOGLE_MAP_API_KEY: string
  readonly VITE_I18N_ENABLE: string
  readonly VITE_RESOURCES_URL: string
  readonly VITE_DISTRIBUTION_RESOURCES_CLOUDFRONT_DOMAIN_NAME: string
  readonly VITE_MEMBER_TAB: string
  readonly VITE_IS_USING_SPOT: string
}
interface ImportMeta {
  readonly env: ImportMetaEnv
}