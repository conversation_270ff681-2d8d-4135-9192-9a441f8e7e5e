/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { useQuasar } from "quasar";
import ability from "@/services/ability";


export function usePermissionHelper() {
  const $q = useQuasar();
  const showActionPermissionError = () => {
    $q.notify({
      message: "アクション権限がありません",
      type: "error",
    });
  };

  const hideButtonPermissionStyle = () => {
    return "visibility: hidden";
  };

  const displayNoneButtonPermissionStyle = () => {
    return "display: none";
  };

  const hasActionPermission = (action: any, item: any) => {
    // console.log("ability can ->" + ability.can(action, item))
    return ability.can(action, item);
  };

  const permission_buttonClass = (item: any) => {
    return "";
  };

  return {
    showActionPermissionError,
    hideButtonPermissionStyle,
    displayNoneButtonPermissionStyle,
    hasActionPermission,
    permission_buttonClass,
  }
}
