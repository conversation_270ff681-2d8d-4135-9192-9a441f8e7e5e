import { isNullOrEmpty } from "../utils/stringUtils";
import { ref } from 'vue';

import dayjs from "dayjs";
import relativeTime from 'dayjs/plugin/relativeTime';
dayjs.locale("ja");
dayjs.extend(relativeTime);

export function useDatetimeFormatter() {
  const dateFormatOptions = ref({
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
  const labelTodayButton = ref("本日を選ぶ");
  const labelCloseButton = ref("閉じる");
  const labelResetButton = ref("リセット");
  const locale = ref("ja");

  const getCurrentYear = () => {
    return dayjs().year();
  };

  const formatUnixToHHmm = (value: number) => {
    return dayjs.unix(value).format("HH:mm");
  };

  const formatUnixToYYYYMMDD = (value: number) => {
    return dayjs.unix(value).format("YYYY-MM-DD");
  };

  const formatUnixToYYYYMMDHHmmss = (value: number) => {
    return dayjs.unix(value).format("YYYY-MM-DD HH:mm:ss");
  };

  const formatUnixToYYYYMMDDHHmmss = (value: number) => {
    return dayjs.unix(value).format("YYYY-MM-DD HH:mm:ss");
  };

  const formatYYYYMMDDToUnix = (value: any) => {
    return dayjs(value).unix();
  };

  const formatToYYYYMMDD = (value: any) => {
    return dayjs(value).format("YYYY-MM-DD");
  };

  const formatToYYYYMM = (value: any) => {
    return dayjs(value).format("YYYY-MM");
  };

  const formatToYYYYMMDDStartOf = (value: any) => {
    return dayjs(value).startOf("month").format("YYYY-MM-DD");
  };

  const formatToYYYYMMDDEndOf = (value: any) => {
    return dayjs(value).endOf("month").format("YYYY-MM-DD");
  };

  const formatToPrettyYYYYMMDHHmmss = (value: any) => {
    return dayjs(value).format("YYYY/MM/DD\xa0HH:mm:ss");
  };

  const formatToYYYYMMDHHmmss = (value: any) => {
    return dayjs(value).format("YYYY-MM-DD HH:mm:ss");
  };

  const formatStringToYYYYMMDD = (value: any) => {
    return value == 0 || value == null || !value.trim().length ? "" : dayjs(value, "YYYYMMDD").format("YYYY-MM-DD");
  };

  const formatStringToYYYYMMDDHHmm = (value: any) => {
    return dayjs(value, "YYYYMMDDHHmm").format("YYYY-MM-DD\xa0\xa0\xa0\xa0\xa0\xa0\xa0HH:mm");
  };

  const formatStringToYYYYMMDDHHmmss = (value: any) => {
    return dayjs(value, "YYYYMMDDHHmm").format("YYYY-MM-DD HH:mm:ss");
  };

  const formatStringToYYYYMMDDHHmmssfff = (value: any) => {
    let now = dayjs(value).format("YYYYMMDDHHmmss");
    let ms = String(dayjs().valueOf()).substr(0, 3);
    return now + ms;
  };

  const formatStringToHHmm = (value: any) => {
    return dayjs(value, "YYYYMMDDHHmm").format("HH:mm");
  };

  const formatStringToPrettyYYYYMMDD = (value: any) => {
    return dayjs(value, "YYYYMMDD").format("YYYY/MM/DD");
  };

  const formatStringToPrettyYYYYMMDDHHmm = (value: any) => {
    return dayjs(value, "YYYYMMDDHHmm").format("YYYY/MM/DD\xa0\xa0\xa0HH:mm");
  };

  const formatToShortDate = (value: any) => {
    return value === null || value === "" ? null : dayjs(value).format("YYYYMMDD");
  };

  const formatToLongTime = (value: any) => {
    return dayjs(value).format("THHmmss");
  };

  const formatCalendar = (timestamp: any) => {
    let _value = timestamp;
    if (_value) {
      if (_value.length > 10) _value = Math.round(_value / 1000);
      if (dayjs().subtract(5, "days").unix() <= timestamp) {
        return customCalendar(dayjs.unix(_value));
      } else {
        return dayjs.unix(_value).format("YYYY-MM-DD HH:mm:ss");
      }
    } else {
      return "";
    }
  };

  const addOneDayToUNIX = (value: number) => {
    return value + 86400;
  };

  const minusOneDayFromUNIX = (value: number) => {
    return value - 86400;
  };

  const allowedDateFrom = (dateFrom: any, dateTo: any) => {
    if (!isNullOrEmpty(dateTo)) {
      if (formatYYYYMMDDToUnix(dateFrom) <= formatYYYYMMDDToUnix(dateTo)) {
        return dateFrom;
      }
    } else {
      return dateFrom;
    }
  };

  const allowedDateTo = (dateFrom: any, dateTo: any) => {
    if (!isNullOrEmpty(dateFrom)) {
      if (formatYYYYMMDDToUnix(dateTo) >= formatYYYYMMDDToUnix(dateFrom)) {
        return dateTo;
      }
    } else {
      return dateTo;
    }
  };

  // カスタムカレンダーフォーマット関数
  // moment.calendarの代わりに作成　必要に応じて修正してください
  function customCalendar(date: any) {
    const now = dayjs();
    const diffDays = now.diff(date, 'day');

    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays === -1) {
      return 'Tomorrow';
    } else if (diffDays > 1 && diffDays < 7) {
      return `${diffDays} days ago`;
    } else if (diffDays < -1 && diffDays > -7) {
      return `In ${Math.abs(diffDays)} days`;
    }

    return date.format('MM/DD/YYYY');
  }

  return {
    getCurrentYear,
    formatUnixToYYYYMMDDHHmmss,
    formatToYYYYMMDD,
    formatUnixToYYYYMMDHHmmss,
    formatToYYYYMMDHHmmss,
    allowedDateFrom,
    allowedDateTo,
    formatStringToYYYYMMDDHHmmssfff,
    formatToPrettyYYYYMMDHHmmss,
    formatYYYYMMDDToUnix,
  };
}