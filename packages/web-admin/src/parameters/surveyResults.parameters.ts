/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import cloneDeep from "lodash/cloneDeep";

class SurveyResultsRequestParameters {

  public userId: string;
  public surveyId: string;
  public filterCriteria: any;
  public partitionKeys: any;
  public selectedSurveyResults: any;
  public displaySettings: any;

  constructor(builder: any) {
    this.userId = builder.userId;
    this.surveyId = builder.surveyId;
    this.filterCriteria = builder.filterCriteria;
    this.partitionKeys = builder.partitionKeys;
    this.selectedSurveyResults = builder.selectedSurveyResults;
    this.displaySettings = builder.displaySettings;
  }
}
class SurveyResultsRequestParametersBuilder {

  public state: any;
  public payload: any;

  public userId?: string;
  public surveyId?: string;
  public countOnly?: boolean;
  public filterCriteria: any;
  public partitionKeys: any;
  public selectedSurveyResults: any;
  public displaySettings: any;
  public isMember: boolean;

  constructor(state: any = undefined, payload: any = undefined) {
    this.state = state;
    this.payload = payload;
    this.isMember = state.isMember;
  }

  setUserId() {
    this.userId = this.payload.userId;
    return this;
  }

  setSurveyId() {
    let survey;
    if(this.state.selectedSurvey) {
      survey = this.state.selectedSurvey;
    } else if(this.state.selectedMemberConfig) {
      survey = this.state.selectedMemberConfig;
    } else {
      survey = undefined;
    }
    this.surveyId = survey.surveyId;
    return this;
  }

  setCountOnly() {
    this.countOnly = true;
    return this;
  }

  setFilterCriteriaParameters() {
    const { commonSearchCriteria, dateSearchCriteria } = this.state;

    const filterCommon = cloneDeep(commonSearchCriteria);
    Object.values(filterCommon).forEach((value: any) => {
      if (value && value.displayValue) {
        delete value.displayValue;
      }
    });

    this.filterCriteria = {
      filterCommon,
      filterDate: dateSearchCriteria,
      sortBy: ["updatedAt"],
      sortDesc: [true],
      countOnly: this.countOnly ? true : false
    };
    return this;
  }

  setPartitionKeysParameters() {
    const { nextRequestPartitionKeys } = this.state;

    this.partitionKeys = {
      keys: nextRequestPartitionKeys,
    };
    return this;
  }

  setLastEvaluatedKeyParameters() {
    const {
      surveyResultsLastEvaluatedKey,
      totalCountLastEvaluatedKey,
      isFetchingSurveyResultsPartitionKeys,
      isFetchingSurveyResultsTotalCount,
    } = this.state;

    var lastEvaluatedKey = {};

    if (isFetchingSurveyResultsPartitionKeys) lastEvaluatedKey = surveyResultsLastEvaluatedKey;
    if (isFetchingSurveyResultsTotalCount) lastEvaluatedKey = totalCountLastEvaluatedKey;

    this.filterCriteria = {
      ...this.filterCriteria,
      ...{
        lastEvaluatedKey: lastEvaluatedKey,
      },
    };
    return this;
  }

  setDisplayCountLastEvaluatedKeyParameters() {
    const {
      displayCountLastEvaluatedKey,
    } = this.state;

    this.filterCriteria = {
      ...this.filterCriteria,
      ...{
        lastEvaluatedKey: displayCountLastEvaluatedKey,
      },
    };
    return this;
  }

  setUpdateParameters() {}

  setDeleteParameters() {
    const { selectedSurveyResults } = this.state;

    this.selectedSurveyResults = {
      items: selectedSurveyResults,
    };
    return this;
  }

  setDisplaySettingsParameters() {
    const { setting } = this.payload;

    this.displaySettings = {
      userId: this.userId,
      surveyId: this.surveyId,
      setting: setting,
    };
    return this;
  }

  build() {
    return new SurveyResultsRequestParameters(this);
  }
}

export { SurveyResultsRequestParametersBuilder };
