/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { OptionsModel } from "@/types";

class UsersRequestParameters {

  public filterCriteria: any;
  public username: string;

  constructor(builder) {
    this.filterCriteria = {
      paginationToken: builder.pagination ? builder.pagination.token : undefined,
      limit: builder.pagination ? builder.pagination.limit : undefined,
      filter: builder.filter,
    };

    this.username = builder.username;
  }
}
class UsersRequestParametersBuilder {

  public state: any;
  public username: string;
  public filter: any;
  public pagination: any;
  
  constructor(state) {
    this.state = state;
  }

  setUsername() {
    const { username } = this.state;
    this.username = username;

    return this;
  }

  setFilterCriteriaParameters(isSearch = true) {
    const { searchAttribute, searchKeyword } = this.state;

    let obj = {} as any;
    if (isSearch && searchAttribute && searchKeyword) {
      obj[searchAttribute.value] = searchKeyword.value;
    }

    this.filter = obj;

    return this;
  }

  setPaginationParameters() {
    const { paginationToken, dataTableOptions } = this.state;

    const found = paginationToken.find((obj: any) => obj.page == dataTableOptions.page);

    this.pagination = {
      token: found.token,
      // limit: dataTableOptions.itemsPerPage,
    };

    return this;
  }

  setUpdateParameters() {}

  setDeleteParameters() {}

  build() {
    return new UsersRequestParameters(this);
  }
}

export { UsersRequestParametersBuilder };
