import { createI18n } from 'vue-i18n'

async function loadLocaleMessages() {
  const messages = {};
  // @ts-ignore
  const locales = Object.entries(import.meta.glob("../locales/**/*.json", { eager: true })).map(async ([path, loadLocale]) => {
    const localeName = path.match(/locales\/(\w+).json/)?.[1]
    messages[localeName] = await loadLocale()
  })
  await Promise.all(locales)
  return messages;
}

export default ({ app }) => {
  const localeSaved = localStorage.getItem('locale')
  loadLocaleMessages().then(messages => {
    // Create I18n instance
    const i18n = createI18n({
      locale: localeSaved || 'ja',
      legacy: false, // comment this out if not using Composition API
      messages: messages,
      fallbackWarn: false,
      missingWarn: false,
      fallbackLocale: "dev",
      missing: (lang, key) => `$${key}$`,
    })

    // Tell app to use the I18n instance
    app.use(i18n)
  })

}