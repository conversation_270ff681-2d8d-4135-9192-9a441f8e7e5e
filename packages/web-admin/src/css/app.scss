// app global css in SCSS form
@tailwind base;
@tailwind components;
@tailwind utilities;

body.body--dark {
  background: #000;
}

.second-menu-tabs {
  .q-tab__icon {
    width: 18px !important;
    height: 18px !important;
    font-size: 18px !important;
  }
}

.hidden-scrollbar {
  -ms-overflow-style: none; /* IE, Edge 対応 */
  scrollbar-width: none; /* Firefox 対応 */
}
.hidden-scrollbar::-webkit-scrollbar {
  /* Chrome, Safari 対応 */
  display: none;
}

// Component Related SCSS

.bg-light {
  background-color: #f8f9fa !important;
}

.bg-white {
  background-color: #fff !important;
}

// Form Editor

.active-left-border {
  border-left: 6px solid var(--q-primary);
}

.inactive-left-border {
  border-left: 6px solid transparent;
}

.active-top-border {
  border-top: 6px solid var(--q-primary);
}

.top-border-delete {
  border-top: 6px solid var(--q-error);
}


// Items Separator look not so natural so small adjustment
// アイテム Separator は自然に見えないので、少し調整
.fe-cont {
  .q-separator {
    background: #eeeeee !important;
    //margin-left: -10px !important;
    padding-right: 10px !important;
  }
}