/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

export type MessaginAPIActionPayload = {
  "type": "uri" | "message",
  "linkUri"? : string,
  "text"? : string,
  "area": {
    "width": number,
    "height": number,
    "x": number,
    "y": number,
  }
}

export type MessaginAPIPayload = {
  "type": "imagemap",
  "baseUrl": string,
  "altText": string,
  "baseSize": {
      "width": number,
      "height": number
  },
  "actions": MessaginAPIActionPayload[];
}

export type ActionObject = {
  "width": number,
  "height": number,
  "x": number,
  "y": number,
  "type": "uri" | "message",
  "isAreaEditMode": boolean,
  "quadrant": number,
  "text"?: string,
  "uri"?: string
}

export default class ItemImageMapMessage {
  public baseUrl: string;
  public baseWidth: number;
  public autoDetectedBaseHeight: number;
  public image2: any;
  public actions: ActionObject[];

  constructor() {
    this.image2 = {};
    this.actions = [];
  }

  static setup(payload: any): ItemImageMapMessage {
    const _this = new ItemImageMapMessage();
    _this.baseUrl = payload.baseUrl;
    _this.baseWidth = payload.baseWidth;
    _this.image2 = payload.image2;
    _this.autoDetectedBaseHeight = payload.autoDetectedBaseHeight;
    for (let index =  0; index < payload.actionCount; index++) {
      _this.actions.push(payload[`action.${index}`] as ActionObject);
    }
    return _this;
  }

  static parseMessagingAPIPayload(payload: any): ItemImageMapMessage {
    const typedPayload = payload as MessaginAPIPayload;
    const _this = new ItemImageMapMessage();
    _this.baseUrl = typedPayload.baseUrl;
    _this.baseWidth = typedPayload.baseSize.width;
    _this.autoDetectedBaseHeight = typedPayload.baseSize.height;
    _this.actions = typedPayload.actions.map((action) => {
      return {
        "width": action.area.width,
        "height": action.area.height,
        "x": action.area.x,
        "y": action.area.y,
        "type": action.type,
        "isAreaEditMode": false,
        "quadrant": 0,
        "text": action.text || "",
        "uri": action.linkUri || ""
      };
    });
    return _this;
  }

  toMessagingAPIPayload(): any {
    const apiPayload = {
      "type": "imagemap",
      "baseUrl": this.baseUrl,
      "altText": "新着メッセージがあります。",
      "baseSize": {
          "width": this.baseWidth,
          "height": this.autoDetectedBaseHeight
      }
    };
    apiPayload["actions"] = this.actions.map((item) => {
      const actionData = {
        "type": item.type,
        "area": {
          "width": item.width,
          "height": item.height,
          "x": item.x,
          "y": item.y
        }
      };
      if (item.type === "uri") {
        actionData["linkUri"] = item.uri;
      } else {
        actionData["text"] = item.text;
      }
      return actionData;
    });
    return apiPayload;
  }

  toImageMapMessagePayload(): any {
    const imageMapMessagePayload = {
      "actionCount": this.actions.length,
      "baseUrl": this.baseUrl,
      "baseWidth": this.baseWidth,
      "autoDetectedBaseHeight": this.autoDetectedBaseHeight,
      "image2": this.image2
    };
    this.actions.forEach((action, idx) => {
      imageMapMessagePayload[`action.${idx}`] = action;
    });
    return imageMapMessagePayload;
  }
}
