/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */
export default class DeleteValidateOfCategories{  
  private categories: any;
  private calendarAllCategories: any;
  private fixedCategories: any;
  private surveyResultCategoriesList: any;
  
  constructor(categories: any, calendarAllCategories: any, fixedCategories: any, surveyResultCategoriesList: any) {
    if(categories){
      this.categories = categories;
    }else{
      throw new Error("categoriesが存在しません");
    }
    if(calendarAllCategories){
      this.calendarAllCategories = calendarAllCategories;
    }else{
      throw new Error("calendarAllCategoriesが存在しません");
    }
    if(fixedCategories){
      this.fixedCategories = fixedCategories;
    }else{
      throw new Error("fixedCategoriesが存在しません");
    }
    if(surveyResultCategoriesList){
      this.surveyResultCategoriesList = surveyResultCategoriesList;
    }else{
      throw new Error("surveyResultCategoriesListが存在しません");
    }
  }

  public countCategoriesByLargeCategory(targetLargeCategory: string): number {
    const count = this.calendarAllCategories.reduce((acc, category) => {
      if (category.tag1 === targetLargeCategory) {
          acc += 1;
      }
      return acc;
    }, 0);
    return count;
  }

  public countCategoriesByLargeMediumCategory(targetLargeCategory: string, targetMediumCategory: string): number {
    const count = this.calendarAllCategories.reduce((acc, category) => {
      if (category.tag1 === targetLargeCategory && category.tag2 === targetMediumCategory) {
          acc += 1;
      }
      return acc;
    }, 0);
    return count;
  }

  public haveErrorToDeleteCategories(): string {
    for(let i=0;i < this.categories.length;i++){
      if (this.fixedCategories[this.categories[i].tag1]) {
        if (this.countCategoriesByLargeCategory(this.categories[i].tag1) === 1) {
          if (this.fixedCategories[this.categories[i].tag1].includes(this.categories[i].tag2) && this.countCategoriesByLargeMediumCategory(this.categories[i].tag1, this.categories[i].tag2) === 1) {
            return "大分類・中分類固定されている分類は削除できません"
          }else{
            return "大分類固定されている分類は削除できません"
          }
        }
      }
      if (this.categories[i].id !== undefined && this.surveyResultCategoriesList.length > 0) {
        if (this.surveyResultCategoriesList.includes(this.categories[i].id)) {
          return "予約済の分類は削除できません"
        }
      }
      if(this.categories[i].hasCalendar){
        return "カレンダーが作成済の分類は削除できません"
      }
    }
    return null
  }
}


