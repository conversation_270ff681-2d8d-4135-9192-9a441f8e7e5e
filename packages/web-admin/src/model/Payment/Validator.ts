/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import * as iconv from 'iconv-lite';

const exceptionStrings = ['―', '＼', '〜', '‖', '－', '¢', '£', '¬'];

export const validateIgnoreStringForSjis = (value: string): boolean => {
  for (const char of value) {
    const buffer: Buffer = toHex(char);
    if (
      (!isSingleByteString(buffer) &&
      !isMultiByteStringWithOutChineseCharacters(buffer) &&
      !isMultiByteStringWithFirstLevel(buffer) &&
      !isMultiByteStringWithSecondLevel(buffer) &&
      // NOTE: JISの第一水準漢字だがisMultiByteStringWithFirstLevelでチェックできない文字列
      !char.match(/[亜-腕]/) &&
      // NOTE: JISの第二水準漢字だがisMultiByteStringWithFirstLevelでチェックできない文字列
      !char.match(/[弌-熙]/)) ||
        exceptionStrings.includes(char)
    ) {
      return false;
    }
  }
  return true;
}

const isSingleByteString = (buffer: Buffer) => {
  // NOTE: シングルバイト文字列かどうかチェック
  return buffer.length === 1;
}

const isMultiByteStringWithOutChineseCharacters = (buffer: Buffer) => {
  // NOTE: JISの非漢字
  return  (buffer[0] == 129 && 65 <= buffer[1])
    || (130 <= buffer[0] && buffer[0] <= 131) 
    || (buffer[0] == 132 && buffer[1] <= 190);
};

const isMultiByteStringWithFirstLevel = (buffer: Buffer) => {
  // NOTE: JISの第一水準漢字
  return  (buffer[0] == 136 && 159 <= buffer[1])
    || (137 <= buffer[0] && buffer[0] <= 151) 
    || (buffer[0] == 152 && buffer[1] <= 114);
};

const isMultiByteStringWithSecondLevel = (buffer: Buffer) => {
  // NOTE: JISの第二水準漢字
  return  (buffer[0] == 152 && 159 <= buffer[1])
    || (153 <= buffer[0] && buffer[0] <= 233) 
    || (buffer[0] == 234 && buffer[1] <= 164);
};

const toHex = (value:string) => {
  return iconv.encode(value, 'Shift_JIS');
}