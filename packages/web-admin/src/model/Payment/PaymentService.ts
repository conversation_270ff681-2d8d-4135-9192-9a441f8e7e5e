/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import {
  TAX_TYPES,
  CALCULATION_TYPES,
  ROUNDING_TYPES,
  RESERVATION_SERVICE_TYPES,
  RESERVATION_COST_TYPE,
  INVALID_SERVICE_MESSAGES,
  PAYMENT_PURCHASE_QUANTITY_LIMIT
} from '@/stores/modules/payment/payment.constants';
import { Service } from '@/stores/modules/payment/payment.types';
import { validateIgnoreStringForSjis } from './Validator';

export class PaymentServiceValidatorModel {
  private isValidLength(value: string, minLength: number, maxLength: number, doesTrim = false): boolean {
    if (typeof value !== 'string') {
      return false;
    }
    const target = doesTrim ? value.trim() : value;
    return target.length >= minLength && target.length <= maxLength;
  }

  isValidServiceId(value: string): boolean {
    const pattern = /^\d*$/;
    return pattern.test(value) && this.isValidLength(value, 3, 3);
  }

  isValidServiceName(value: string): boolean {
    return this.isValidLength(value, 1, 40, true) && validateIgnoreStringForSjis(value) && !value.includes('（') && !value.includes('）');
  }

  isValidReceiptInfo(value: string): boolean {
    return this.isValidLength(value, 1, 300, true);
  }

  isValidTaxType(value: string): boolean {
    return Object.values(TAX_TYPES).includes(value);
  }

  isValidCalculationType(value: string): boolean {
    return Object.values(CALCULATION_TYPES).includes(value);
  }

  isValidRoundingType(value: string): boolean {
    return Object.values(ROUNDING_TYPES).includes(value);
  }

  isValidReservationServiceType(value: string): boolean {
    return Object.values(RESERVATION_SERVICE_TYPES).includes(value);
  }

  isValidReservationCostType(value: string): boolean {
    return Object.values(RESERVATION_COST_TYPE).includes(value);
  }

  isValidPurchaseLimit(value: string): boolean {
    const pattern = /^([1-9]\d*|0)$/;
    return pattern.test(value) 
          && Number(value) <= PAYMENT_PURCHASE_QUANTITY_LIMIT.MAX
          && Number(value) >= PAYMENT_PURCHASE_QUANTITY_LIMIT.MIN;
  }

  private validateServiceId(value: string): void {
    if (!this.isValidServiceId(value)) {
      throw INVALID_SERVICE_MESSAGES.SERVICE_ID;
    }
  }

  private validateServiceName(value: string): void {
    if (!this.isValidServiceName(value)) {
      throw INVALID_SERVICE_MESSAGES.SERVICE_NAME;
    }
  }

  private validateReceiptCreatorName(value: string): void {
    if (!this.isValidReceiptInfo(value)) {
      throw INVALID_SERVICE_MESSAGES.RECEIPT_CREATOR_NAME;
    }
  }

  private validateReceiptDisplayAdress(value: string): void {
    if (!this.isValidReceiptInfo(value)) {
      throw INVALID_SERVICE_MESSAGES.RECEIPT_DISPLAY_ADRESS;
    }
  }

  private validateTaxType(value: string): void {
    if (!this.isValidTaxType(value)) {
      throw INVALID_SERVICE_MESSAGES.TAX_TYPE;
    }
  }

  private validateCalculationType(value: string): void {
    if (!this.isValidCalculationType(value)) {
      throw INVALID_SERVICE_MESSAGES.CALCULATION_TYPE;
    }
  }

  private validateRoundingType(value: string): void {
    if (!this.isValidRoundingType(value)) {
      throw INVALID_SERVICE_MESSAGES.ROUNDING_TYPE;
    }
  }

  private validateReservationServiceType(value: string): void {
    if (!this.isValidReservationServiceType(value)) {
      throw INVALID_SERVICE_MESSAGES.RESERVATION_SERVICE_TYPE;
    }
  }

  private validateReservationCostType(value: string): void {
    if (!this.isValidReservationCostType(value)) {
      throw INVALID_SERVICE_MESSAGES.RESERVATION_COST_TYPE;
    }
  }

  private validatePurchaseLimit(value: string): void {
    if (!this.isValidPurchaseLimit(value)) {
      throw INVALID_SERVICE_MESSAGES.PURCHASE_LIMIT;
    }
  }

  validateService(service: Service<string>): void {
    this.validateServiceId(service.serviceId);
    this.validateServiceName(service.serviceName);
    this.validateReceiptCreatorName(service.receiptCreatorName);
    this.validateReceiptDisplayAdress(service.receiptDisplayAddress);
    this.validateTaxType(service.taxType);
    this.validateCalculationType(service.calculationType);
    this.validateRoundingType(service.roundingType);
    this.validateReservationServiceType(service.reservationServiceType);
    this.validateReservationCostType(service.reservationCostType);
    this.validatePurchaseLimit(service.purchaseLimit);
  }
}

export class PaymentServiceModel {
  toInput(service: Service<number>): Service<string> {
    return {
      ...service,
      taxType: String(service.taxType),
      calculationType: String(service.calculationType),
      roundingType: String(service.roundingType),
      reservationServiceType: String(service.reservationServiceType),
      reservationCostType: String(service.reservationCostType),
      purchaseLimit: String(service.purchaseLimit),
    };
  }

  toPayload(service: Service<string>): Service<number> {
    return {
      ...service,
      taxType: Number(service.taxType),
      calculationType: Number(service.calculationType),
      roundingType: Number(service.roundingType),
      reservationServiceType: Number(service.reservationServiceType),
      reservationCostType: Number(service.reservationCostType),
      purchaseLimit: Number(service.purchaseLimit),
    };
  }
}