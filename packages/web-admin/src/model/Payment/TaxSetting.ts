/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import { TaxRateSetting, DisplayTaxRateSetting } from '@/stores/modules/payment/payment.types';
import { TAX_RATE_RANGE } from '@/stores/modules/payment/payment.constants'
import { DateTime } from 'luxon';

const dateFormat = 'yyyy-MM-dd';

export class TaxRateSettingModel {
  toInput(taxRateSettings: TaxRateSetting[]): DisplayTaxRateSetting[] {
    const applyingIndex = this.getApplyingIndex(taxRateSettings);
    const applyingSetting = taxRateSettings[applyingIndex];
    return taxRateSettings.map((s, i) => {
      return {
        ...s,
        isShowDatePicker: false,
        isApplying: i === applyingIndex,
        isNewItem: false,
        isEditable: applyingSetting ? DateTime.fromISO(applyingSetting.applyDate) < DateTime.fromISO(s.applyDate) : true,
      }
    });
  }

  toPayload(taxRateSettings: DisplayTaxRateSetting[]): TaxRateSetting[] {
    return taxRateSettings.map(s => {
      return {
        value: s.value,
        applyDate: s.applyDate
      }
    });
  }

  private getApplyingIndex(taxRateSettings: TaxRateSetting[]): number {
    /**
     * 処理内容メモ
     * 1.今日以前の日付を持つ要素を探す
     * 2.その中で日付が最も大きい要素のインデックスを返す(同じ日付を持つ要素があることは考慮しない)
     */
    const now = DateTime.now();
    let findIndex = -1;
    let findDate: DateTime | null = null;
    taxRateSettings.forEach((s, i) => {
      const applyDate = DateTime.fromFormat(s.applyDate, dateFormat);
      const isFuture = applyDate > now;
      if (isFuture) {
        return;
      }

      if (findDate === null || findDate < applyDate) {
        findDate = applyDate;
        findIndex = i;
      }
    });
    return findIndex;
  }

  getApplyingRate(taxRateSettings: TaxRateSetting[]): number | null {
    const applyingIndex = this.getApplyingIndex(taxRateSettings);
    const isExistApplyingRate = applyingIndex !== -1;
    return isExistApplyingRate ? taxRateSettings[applyingIndex].value : null;
  }
}

export class TaxRateSettingValidatorModel {
  private validateTaxRateValue(value: string | number): void {
    const pattern = /^([1-9]\d*|0)$/;
    if (!pattern.test(String(value))) {
      throw '税率は半角数値を入力してください。';
    }
    if (Number(value) > TAX_RATE_RANGE.MAX || Number(value) < TAX_RATE_RANGE.MIN) {
      throw `税率は${TAX_RATE_RANGE.MIN}以上${TAX_RATE_RANGE.MAX}以下の数値を入力してください。`;
    }
  }

  private validateApplyDate(value: string): void {
    if (!value) {
      throw '適用開始日には正しい日付を入力してください。';
    }
    const applyDate = DateTime.fromFormat(value, dateFormat);
    if (!applyDate.isValid) {
      throw '適用開始日には正しい日付を入力してください。';
    }
  }

  private validatePastFromApplyingDate(applyDate: string, applyingDate: string): void {
    const _applyDate = DateTime.fromFormat(applyDate, dateFormat);
    const _applyingDate = DateTime.fromFormat(applyingDate, dateFormat);
    if (_applyDate < _applyingDate || _applyDate === _applyingDate) {
      throw '適用開始日には適用中の設定日より未来の日付を入力してください。';
    }
  }

  private validateDulicateApplyDate(taxRateSettings: DisplayTaxRateSetting[]): void {
    const isDuplicate = taxRateSettings.some((s1, index1) => {
      return taxRateSettings.some((s2, index2) => {
        if (index1 === index2) {
          return false;
        }
        return s1.applyDate === s2.applyDate;
      })
    });
    if (isDuplicate) {
      throw '適用開始日が重複しています。';
    }
  }

  validate(taxRateSettings: DisplayTaxRateSetting[]): void {
    const applyingIndex = taxRateSettings.findIndex(s => s.isApplying);
    taxRateSettings.forEach((s, i) => {
      if (!s.isEditable) {
        return;
      }
      this.validateTaxRateValue(s.value)
      this.validateApplyDate(s.applyDate);
      if (applyingIndex > -1 && applyingIndex !== i) {
        this.validatePastFromApplyingDate(s.applyDate, taxRateSettings[applyingIndex].applyDate)
      }
    });
    this.validateDulicateApplyDate(taxRateSettings);
  }

  doTaxRateVallidateRule(value: string): boolean {
    try {
      this.validateTaxRateValue(value);
      return true
    } catch(e: any) {
      return e
    }
  }
}