/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

export default class ItemImageUrl{
  private file: any;
  private imgWidth: number;
  public chkErr: string[];
  
  constructor(file: any , imgWidth: number) {
    this.chkErr = [];
    if(file){
      this.file = file;
    }else{
      throw new Error("ファイルが存在しません");
    }
    if(imgWidth){
      this.imgWidth = imgWidth;
    }else{
      throw new Error("imgWidthが存在しません");
    }
  }

  static async createObject(file: any) {
    const loadImage = (src) => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve(img);
        img.onerror = (e) => reject(e);
        img.src = src;
      });
    };
    
    let imgWidth:number;
    let imgHeight:number;
    await loadImage(URL.createObjectURL(file))
      .then((res:any) => {
        imgWidth = res.width; //横幅
        imgHeight = res.height; //縦幅
      })
      .catch(e => {
        console.error('onload error', e);
      });
    const newImage = new ItemImageUrl(file, imgWidth);
    
      // uncomment only if needed in the future
      // 将来的に必要な場合はコメントを外してください
/*     const aspectRatio = imgWidth / imgHeight;
    if (Math.abs(aspectRatio - 4) > 0.01) { // Allow a small tolerance
      newImage.chkErr.push('画像比率は4:1のファイルのみです。');
    } */
    if(imgWidth > 1200){
      newImage.chkErr.push('画像サイズは横幅1200pxまでです。');
    }
    if (file.type != "image/png" && file.type != "image/jpeg" && file.type != "image/jpg") {
      newImage.chkErr.push('対応するファイル形式は jpeg, jpg, png です。');
    }
    if(3145728 < file.size){
      newImage.chkErr.push('画像容量は3MB以下のファイルのみです。');
    }
    return newImage;
  }
}


