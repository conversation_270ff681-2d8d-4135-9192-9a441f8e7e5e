/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

import {Option} from "@/model/Form/SelectPayTypeItem";

export default class Item {

  public type: any;
  public title: any;
  public description: any;
  public attributes: any;

  constructor(type: any = undefined, title: any = undefined, description: any = undefined) {
    this.type = type;
    this.title = title;
    this.description = description;
  }

  init() {
    return {
      type: this.type,
      title: this.title,
      description: this.description,
    };
  }
}

export type ItemModelType = Partial<{
  isIndexable: {
    value: boolean,
  },
  isNotEditable: boolean,
  isRequiredForUser: {
    value: false,
  },
  isAdminItem: boolean,
  isRequired: {
    value: boolean,
  },
  isPersonalInfo: {
    value: boolean
  },
  duplicateKeyCheck: {
    value: boolean
  },
  length: {
    min: number,
    max: number,
  }
  attributes: {
    isSesshuKaisuu?: boolean
  },
  isSearchable: {
    value: boolean,
  },
  payTypeOptions: Option[],
  imageUrl: any
}> & {
  itemKey: string,
  type: string,
  title: string,
  input?: string | number,
  description?: string,
  default?: string | number,
} & {
  visable: boolean,
  title_disabled?: boolean,
  isNewItem: boolean,
  isAutomaticNumbering: boolean,
}

type ReservationCategory = {
  name: string,
  children: {
    name: string, id: string, calendarId?: string
  }[]
}

export type ReservationItemModelType = {
  categories_tree: {
    tree: any,
    display: any,
  },
  isFixedLargeCategory: boolean,
  isFixedMediumCategory: boolean,
  isFixedSmallCategory: boolean,
  reservationCheckBox: boolean,
  reservationSupCheckBox: boolean,
  selectedLargeCategory?: ReservationCategory,
  selectedMediumCategory?: ReservationCategory,
  setLargeCategoryTitle?: string,
  setMediumCategoryTitle?: string,
  setSmallCategoryTitle?: string,
  type: 'reservation',
} & ItemModelType