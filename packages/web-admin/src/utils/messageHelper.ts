import { cloneDeep } from 'lodash'
import emojis from 'src/assets/emojis.json'

const isValidEmoji = (emojiId: string) => {
  return emojis?.paths?.some((emoji) => emoji?.path?.includes(emojiId))
}

export const formatMessage = (content: any): string => {
  const formatContent = cloneDeep(content)
  let text = content?.text || ''
  const emojiRegex = /<<\$[a-zA-Z0-9_]+\$>>/g
  const emojiMatches = text.match(emojiRegex)

  if (emojiMatches) {
    const _emojis = []
    emojiMatches.forEach((emoji) => {
      const emojiId = emoji.replace('<<$', '').replace('$>>', '')
      if (isValidEmoji(emojiId)) {
        // index of emoji in text
        const index = text.indexOf(emoji)
        text = text.replace(emoji, '$')

        // path: emojis/5ac1bfd5040ab15980c9b435_004.png
        // get 004
        const rows = emojiId.split('_')
        _emojis.push({
          index,
          productId: rows[0],
          emojiId: rows[1],
        })
      }
    })

    if (_emojis.length > 0) {
      formatContent.emojis = _emojis
    }
  }
  return {
    ...formatContent,
    text,
  }
}
