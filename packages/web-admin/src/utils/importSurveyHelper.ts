import <PERSON> from "papaparse";
import dayjs from "dayjs";
import * as jschardet from "jschardet";

dayjs.locale("ja");

// for papaparse
const DELIMITER = ",";
const ENCODING = "SJIS";

// 2-type of items: update item OR create item
// update item must have PARTITION_KEY, USER_ID, CREATED_AT, UPDATED_AT
// create item must NOT have (OR have empty value of) PARTITION_KEY, CREATED_AT, UPDATED_AT

// csv file headers (fields name)
const PARTITION_KEY = "パーティションキー"; // valid input: ({surveyId}#{userId}#{timestamp in ms NOT s}) OR empty
const USER_ID = "ユーザーID"; // valid input: can be anything
const ANSWER_CODE = "#"; // valid input: can be anything
const CREATED_AT = "作成日"; // valid input: YYYY-MM-DD HH:mm:ss OR empty
const UPDATED_AT = "更新日"; // valid input: YYYY-MM-DD HH:mm:ss OR empty
const CHECK = "状態"; // valid input: CHECK_LIST
const NOTE = "管理者メモ"; // valid input: can be anything

// validation constants
const EN = "en";
const JA = "ja";

const REQUIRED_HEADERS = [
  { value: "partitionKey", text: PARTITION_KEY },
  { value: "userId", text: USER_ID },
  { value: "answerCode", text: ANSWER_CODE },
];
const ITEM_TYPE_NOT_REQUIRED = ["guide"];
const PARTITION_KEY_PARTS = 3;
const LANGUAGE_HEADERS = [
  { ja: PARTITION_KEY, en: "partitionKey" },
  { ja: USER_ID, en: "userId" },
  { ja: NOTE, en: "note" },
  { ja: CREATED_AT, en: "createdAt" },
  { ja: UPDATED_AT, en: "updatedAt" },
  { ja: CHECK, en: "check" },
];
const CHECK_LIST = ["未対応", "処理中", "処理済み", "完了"]; // -> for 更新型 = will NOT be used. Because cannot import 更新型 survey
const CHECK_LIST_APPENDING = ["未対応", "処理中", "処理済み", "完了", "保留", "キャンセル"];

const convertUnixToYYYYMMDDHHmmss = (value: number): string => {
  return dayjs.unix(value).format("YYYYMMDDHHmmss");
};
const convertYYYYMMDDHHmmssToUnix = (value: string | Date | number): number => {
  return dayjs(value).unix();
};
const validYYYYMMDDHHmmss = (value: string | Date | number): boolean => {
  return dayjs(value, "YYYY-MM-DD HH:mm:ss", true).isValid();
};
const convertPapaparseCSVError = (error: { row: number; type: any; }): string => {
  const row = error.row + 1;
  let japaneseError = "不明な CSV エラーが発生しました。";
  switch (error.type) {
    case "Quotes":
      japaneseError = "第 (" + row + ") 行目 - CSV クォーテーションエラーが発生しました。";
      break;
    case "Delimiter":
      japaneseError = "第 (" + row + ") 行目 - CSV デリミターエラーが発生しました。";
      break;
    case "FieldMismatch":
      japaneseError = "第 (" + row + ") 行目 - 項目のデータ型が一致されてません。";
      break;
    default:
      japaneseError = "第 (" + row + ") 行目 - 不明な CSV エラーが発生しました。";
      break;
  }
  return japaneseError;
};

export const localCsvToJson = (file: any, isSJISEncoding: any): Promise<any> => {
  return new Promise((resolve, reject) => {
    let config = {
      header: false,
      skipEmptyLines: "greedy",
      delimiter: DELIMITER,
      complete(result: any) {
        result.errors = result.errors.map((error: any) => convertPapaparseCSVError(error));
        resolve(result);
      },
      error(error: any) {
        reject(error);
      },
    } as any;
    if (isSJISEncoding) {
      config["encoding"] = ENCODING;
    }
    Papa.parse(file, config);
  });
};

export const checkFileType = async (file: Blob): Promise<any> => {
  let isSJISEncoding = true;
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      var rawCSVText: any = e.target?.result;
      var checkDecoding = jschardet.detect(rawCSVText);
      // console.log(checkDecoding.encoding);
      if (checkDecoding.encoding === "SHIFT_JIS" || checkDecoding.encoding === "ISO-8859-2") {
        isSJISEncoding = true;
      } else {
        isSJISEncoding = false;
      }
      resolve(isSJISEncoding);
    };
    // reader.readAsBinaryString(file);
    reader.readAsArrayBuffer(file);
  });
};

class ImportSurveyHelper {

  private surveyId: string;
  private schema: Array<any>;
  private formData: any;
  private jsonData: any;
  private schemaByGroupheader: Array<any>;
  private schemaByBunki: Array<any>;
  private headers: Array<any>;
  private records: Array<any>;
  private errors: Array<any>;
  private schemaRecordsProcess: Array<any> = [];

  constructor(formData: { surveyId: string; surveySchema: Array<any>; }, jsonData: any) {
    this.surveyId = formData.surveyId;

    this.schema = formData.surveySchema.filter((item: { type: string; }) => !ITEM_TYPE_NOT_REQUIRED.includes(item.type));

    this.formData = formData;
    this.jsonData = jsonData;

    this.schemaByGroupheader = [];
    this.schemaByBunki = [];

    this.headers = []; // will be filled later
    this.records = []; // will be filled later

    this.errors = [];
  }

  getErrors() {
    return this.errors;
  }
  putErrorMessage(lineNumber: any, title: any, message: string) {
    this.errors.push(`${lineNumber} [${title}] ${message}`);
  }
  isValid() {
    return this.errors.length === 0;
  }

  cleanString(s: string | Array<any>) {
    let l = 0;
    let r = s.length;
    if (l < r && s[l] === "=") ++l;
    if (l < r - 1 && s[l] === '"' && s[r - 1] === '"') {
      ++l;
      --r;
    }
    return s.slice(l, r);
  }
  hasValidUpdateFields(lang: string, item: { [x: string]: string | Array<any>; }) {
    // partitionKey, userId, createdAt, updatedAt all 4 has to be in item
    let fields = [PARTITION_KEY, USER_ID, CREATED_AT, UPDATED_AT];
    if (lang === EN) {
      // convert fields to EN
      for (let header of LANGUAGE_HEADERS) {
        for (let i = 0; i < fields.length; ++i) {
          if (header[JA] === fields[i]) {
            fields[i] = header[EN];
          }
        }
      }
    }
    for (let field of fields) {
      if (!(field in item) || item[field].length === 0) {
        return false;
      }
    }
    return true;
  }
  hasValidCreateFields(item: { [x: string]: string | Array<any>; }) {
    // partitionKey, createdAt, updatedAt all 3 has to be EMPTY/UNDEFINED in item
    const fields = [PARTITION_KEY, CREATED_AT, UPDATED_AT];
    for (let field of fields) {
      if (field in item && item[field].length > 0) {
        return false;
      }
    }
    return true;
  }

  validateFormData() {
    if (
      !this.surveyId ||
      this.surveyId.length === 0 ||
      !this.schema ||
      !Array.isArray(this.schema) ||
      this.schema.length === 0
    ) {
      this.errors.push("帳票を選択してください。");
      return;
    }
  }
  validateHeaders() {
    // data is still in json
    if (this.jsonData.length === 0) {
      this.errors.push("csvファイルにヘッダが必須です。");
      return;
    }

    // save to class object
    this.headers = this.jsonData[0].map((key: any) => this.cleanString(key));

    for (let requiredHeader of REQUIRED_HEADERS) {
      if (!this.headers.includes(requiredHeader.text)) {
        this.errors.push(`タイトル行の内容が違います。確認してください。`);
        return;
      }
    }

    let indexShift = REQUIRED_HEADERS.length;
    for (let i = 0; i < this.schema.length; ++i) {
      let question = this.schema[i];

      switch (question.type) {
        case "reservation":
          let bunruiDisplay = { ...question.categories_tree.display };

          if (
            this.headers[indexShift + i + 1] + this.headers[indexShift + i + 2] + this.headers[indexShift + i + 3] !==
            bunruiDisplay.tag1 + bunruiDisplay.tag2 + bunruiDisplay.tag3
          ) {
            this.errors.push(`タイトル行の内容が違います。確認してください。`);
            return;
          }
          indexShift += 3;

          if (this.headers[indexShift + i + 1] === "予約時間") ++indexShift;
          if (this.headers[indexShift + i + 1] === "LINE/外部") ++indexShift;
          break;

        case "linkbutton":
          --indexShift;
          break;

        case "groupheader":
          --indexShift;
          break;

        default:
          if (this.headers[indexShift + i] !== question.title) {
            this.errors.push(`タイトル行の内容が違います。確認してください。`);
            return;
          }
          break;
      }
    }

  }
  convertJsonToListOfObjects() {
    // i = 0 is for headers (already assigned)
    for (let i = 1; i < this.jsonData.length; ++i) {
      const lineNumber = `第${i}行目：`;
      if (this.headers.length !== this.jsonData[i].length) {
        this.errors.push(`${lineNumber} length mismatch. Check for comma characters.`);
        return;
      }

      let item = {} as any;
      for (let j = 0; j < this.jsonData[i].length; ++j) {
        item[this.headers[j]] = this.cleanString(this.jsonData[i][j]);
      }

      // because surveyAnswer value is assigned at the very end, even if there is a header name = "surveyAnswer", it will be overwritten!
      let surveyAnswer = [];
      let indexShift = REQUIRED_HEADERS.length;
      for (let j = 0; j < this.schema.length; ++j) {
        let cleanedString: any = this.cleanString(this.jsonData[i][indexShift + j]);

        let question = this.schema[j];
        switch (question.type) {
          case "reservation":
            indexShift += 3;

            if (this.headers[indexShift + j + 1] === "予約時間") ++indexShift;
            if (this.headers[indexShift + j + 1] === "LINE/外部") ++indexShift;

            // bunrui answer (if exist) has to be sent to backend = 'category#....'
            surveyAnswer.push(cleanedString);

            break;

          case "linkbutton":
            --indexShift;
            surveyAnswer.push("");
            break;

          case "groupheader":
            --indexShift;
            surveyAnswer.push("");
            break;

          default:
            if (this.schema[j].type === "checkboxes") {
              if (cleanedString.length === 0) {
                surveyAnswer.push([]);
              } else {
                surveyAnswer.push(cleanedString.split(","));
              }
            } else {
              surveyAnswer.push(cleanedString);
            }
            break;
        }
      }
      item["surveyAnswer"] = surveyAnswer;
      this.records.push(item);
    }
  }

  validateRecords() {
    // all non-empty user id has to be unique -> no. same user can answer several times
    // should check for partitionKey!
    let pkIndex = new Map();
    this.schemaRecordsProcess = [];
    for (let i = 0; i < this.records.length; ++i) {
      this.schemaRecordsProcess = [...this.schema];

      const lineNumber = `第${i + 2}行目：`;
      const item = this.records[i];

      if (!this.hasValidCreateFields(item) && !this.hasValidUpdateFields(JA, item)) {
        this.errors.push(
          `${lineNumber} 新規の場合は[${PARTITION_KEY}], [${CREATED_AT}], [${UPDATED_AT}]が空でなければいけません。`
        );
        this.errors.push(
          `${lineNumber} 更新の場合は[${PARTITION_KEY}], [${CREATED_AT}], [${UPDATED_AT}]が最新のデータでなければいけません。`
        );
        return;
      }

      // validation for non-survey-question fields
      if (CHECK in item && item[CHECK].length > 0 && !CHECK_LIST_APPENDING.includes(item[CHECK])) {
        this.errors.push(`${lineNumber} [${CHECK}]の値が指定とは異なります。`);
        return;
      }

      // special check for update item
      if (this.hasValidUpdateFields(JA, item)) {
        if (pkIndex.has(item[PARTITION_KEY])) {
          this.errors.push(
            `${lineNumber} 同じ[${PARTITION_KEY}]を持つアイテムが第${pkIndex.get(item[PARTITION_KEY])}行目にあります。`
          );
          return;
        }
        pkIndex.set(item[PARTITION_KEY], i + 1);

        const pk = item[PARTITION_KEY].split("#");
        if (pk.length !== PARTITION_KEY_PARTS) {
          this.errors.push(`${lineNumber} [${PARTITION_KEY}]が間違いました。`);
          return;
        }

        if (pk[0] !== this.surveyId) {
          this.errors.push(`${lineNumber} [${PARTITION_KEY}]の[surveyId]が間違いました。`);
          return;
        }
        if (pk[1] !== item[USER_ID]) {
          this.errors.push(`${lineNumber} [${PARTITION_KEY}]の[userId]が間違いました。`);
          return;
        }

        if (!validYYYYMMDDHHmmss(item[CREATED_AT])) {
          this.errors.push(
            `${lineNumber} [${CREATED_AT}]のフォーマットが違います。YYYY-MM-DD HH:mm:ss形式で作成してください。`
          );
          return;
        }
        if (!validYYYYMMDDHHmmss(item[UPDATED_AT])) {
          this.errors.push(
            `${lineNumber} [${UPDATED_AT}]のフォーマットが違います。YYYY-MM-DD HH:mm:ss形式で作成してください。`
          );
          return;
        }

      }

      // merge input into schema
      {
        let formData = this.formData;

        for (let j = 0; j < this.schemaRecordsProcess.length; ++j) {
          let schemaItem = this.schemaRecordsProcess[j];

          schemaItem = {
            ...schemaItem,
            input: item.surveyAnswer[j],
            lineNumber: lineNumber,
          };

          if (formData && formData.surveySchema && Array.isArray(formData.surveySchema)) {
            formData.surveySchema.forEach((item: { itemKey: any; }, index: string | number) => {
              if (schemaItem.itemKey == item.itemKey) {
                formData.surveySchema[index] = schemaItem;
              }
            });
          }

          if (schemaItem.type == "linkbutton") {
            continue;
          }
        }
      }

      this.reformatSchemaByBunki();

      // validation for survey-question fields
      for (let j = 0; j < this.schemaRecordsProcess.length; ++j) {
        let schemaItem = this.schemaRecordsProcess[j];

        if (!schemaItem.isBunkiValid) {
          continue;
        }

        if (schemaItem.type == "linkbutton") {
          continue;
        }

        this.validateCommon(schemaItem);
        if (!this.isValid()) return;

        this.validateItem(schemaItem);

        if (!this.isValid()) return;
      }

      if (!this.isValid()) return;
    }
  }

  reformatSchemaByBunki() {
    let formData = this.formData;
    let groupheaderItems:any = null;
    let choiceGroupheaderItems: any = {};

    this.schemaRecordsProcess = [];
    this.schemaByGroupheader = [];
    this.schemaByBunki = [];

    if (formData && formData.surveySchema && Array.isArray(formData.surveySchema)) {
      formData.surveySchema.forEach((originalItem: any) => {
        let item = { ...originalItem, isBunkiValid: false };

        // schemaByGroupheader
        if (item.type === "groupheader") {
          this.schemaByGroupheader.push(item);

          if (groupheaderItems === null || groupheaderItems.length > 0) {
            groupheaderItems = [];

            item.items = groupheaderItems;
          }
        } else {
          // choiceGroupheaderItems
          if (item.type === "choicegroupheader") {
            choiceGroupheaderItems[item.itemKey] = item;
          }

          if (groupheaderItems !== null && Array.isArray(groupheaderItems)) {
            groupheaderItems.push(item);
          } else {
            this.schemaByGroupheader.push(item);
          }
        }
      });
    }

    let choiceGroupheaderArray = Object.values(choiceGroupheaderItems);

    this.schemaByGroupheader.forEach((item: { type: string; title: any; isBunkiValid: boolean; items: Array<any>; }) => {
      if (item.type === "groupheader") {
        let groupheaderStatus = {
          exist: false,
          checked: false,
        };

        choiceGroupheaderArray.forEach((choiceGroupheaderItem: any) => {
          if (choiceGroupheaderItem["input"]) {
            let selectedGroupheaderValue = choiceGroupheaderItem.input;
            choiceGroupheaderItem.sectionOptions.forEach((sectionOption: { groupheader: { value: any; }; option: { value: any; }; }) => {
              if (sectionOption.groupheader.value === item.title) {
                groupheaderStatus.exist = true;

                if (selectedGroupheaderValue === sectionOption.option.value) {
                  groupheaderStatus.checked = true;
                  item.isBunkiValid = true;
                  return;
                }
              }
            });
          }
        });

        if (item.isBunkiValid) {
          this.schemaByBunki.push(item);
        } else {
          if (groupheaderStatus.exist === false) {
            item.isBunkiValid = true;
            this.schemaByBunki.push(item);
          }
        }

        if (item.isBunkiValid) {
          item.items.forEach((subItem: { isBunkiValid: any; type: string; }) => {
            subItem.isBunkiValid = item.isBunkiValid;

            if (!ITEM_TYPE_NOT_REQUIRED.includes(subItem.type)) {
              this.schemaRecordsProcess.push(subItem);
            }
          });
        }
      } else {
        item.isBunkiValid = true;
        this.schemaByBunki.push(item);

        if (!ITEM_TYPE_NOT_REQUIRED.includes(item.type)) {
          this.schemaRecordsProcess.push(item);
        }
      }
    });

    return this.schemaByBunki;
  }

  validateItem(item: any) {
    let obj = item;

    switch (obj.type) {
      case "text":
        this.validateText(obj);
        break;
      case "checkboxes":
        this.validateCheckboxes(obj);
        break;
      case "dropdown":
        this.validateDropdown(obj);
        break;
      case "date":
        this.validateDate(obj);
        break;
      case "email":
        this.validateEmail(obj);
        break;
      case "phone":
        this.validatePhone(obj);
        break;
      case "number":
        this.validateNumber(obj);
        break;
      case "textarea":
        this.validateTextarea(obj);
        break;
      case "radio":
        this.validateRadio(obj);
        break;
      case "postcode":
        this.validatePostcode(obj);
        break;

      default:
        break;
    }
  }

  validate() {
    this.validateFormData();
    if (!this.isValid()) return false;

    this.validateHeaders();
    if (!this.isValid()) return false;

    this.convertJsonToListOfObjects();
    if (!this.isValid()) return false;

    this.validateRecords();
    if (!this.isValid()) return false;

    return true;
  }
  preparePayload() {
    this.records = this.records.map((item: { [x: string]: any; }) => {
      if (CREATED_AT in item && item[CREATED_AT].length > 0) {
        item[CREATED_AT] = convertYYYYMMDDHHmmssToUnix(item[CREATED_AT]);
      }
      if (UPDATED_AT in item && item[UPDATED_AT].length > 0) {
        item[UPDATED_AT] = convertYYYYMMDDHHmmssToUnix(item[UPDATED_AT]);
      }
      for (let header of LANGUAGE_HEADERS) {
        if (header[JA] in item) {
          item[header[EN]] = item[header[JA]];
          delete item[header[JA]];
        }
      }
      return item;
    });

    // if now is declared here, cannot create item with same userId in 1 import (only the last one will be created? because partitionkey will be equal)
    // solve: use {now + i} instead. now is in ms, 20000 items -> delta t = 20s. User probably will not import in 20s interval. For now.
    const now = Date.now();

    let toUpdate = [];
    let toCreate = [];
    let status = {
      bunruiUpdatedFlag: false,
    };
    for (let i = 0; i < this.records.length; ++i) {
      const answerItem = this.records[i];
      let surveyResult = [];

      for (let j = 0; j < this.schema.length; ++j) {
        // if result is filled by matching the order of headers with order of questions
        // order of answerItem.answer has to match the order of questionItem!
        const generatedUserId = `I${convertUnixToYYYYMMDDHHmmss(Math.floor(now / 1000))}n${i + 1}`;
        const questionItem = this.schema[j];

        if (questionItem.type === "reservation" || questionItem.type === "linkbutton") {
          status.bunruiUpdatedFlag = true;
        }

        if (questionItem.type === "checkboxes") {
          for (let option of questionItem.options) {
            if (this.hasValidUpdateFields(EN, answerItem)) {
              surveyResult.push({
                answerCode: answerItem.answerCode,
                check: answerItem.check,
                createdAt: answerItem.createdAt,
                itemKey: questionItem.itemKey,
                note: answerItem.note,
                partitionKey: answerItem.partitionKey,
                sortKey: `${questionItem.itemKey}#${option}`,
                surveyId: this.surveyId,
                title: questionItem.title,
                updatedAt: answerItem.updatedAt,
                userId: answerItem.userId,
                userSearchKey: answerItem.surveyAnswer[j].includes(option)
                  ? `${questionItem.itemKey}#${option}#${option}`
                  : `${questionItem.itemKey}#${option}#`,
                value: answerItem.surveyAnswer[j].includes(option) ? option : "",
              });
            } else {
              surveyResult.push({
                answerCode: answerItem.answerCode,
                check: answerItem.check,
                itemKey: questionItem.itemKey,
                note: answerItem.note,
                partitionKey:
                  answerItem.userId.length > 0
                    ? `${this.surveyId}#${answerItem.userId}#${now + i}`
                    : `${this.surveyId}#${generatedUserId}#${now + i}`,
                sortKey: `${questionItem.itemKey}#${option}`,
                surveyId: this.surveyId,
                userId: answerItem.userId.length > 0 ? answerItem.userId : generatedUserId,
                userSearchKey: answerItem.surveyAnswer[j].includes(option)
                  ? `${questionItem.itemKey}#${option}#${option}`
                  : `${questionItem.itemKey}#${option}#`,
                value: answerItem.surveyAnswer[j].includes(option) ? option : "",
              });
            }
          }
        } else {
          if (answerItem.surveyAnswer[j].length === 0) {
            // don't include empty answer value to payload
            continue;
          }
          if (this.hasValidUpdateFields(EN, answerItem)) {
            surveyResult.push({
              answerCode: answerItem.answerCode,
              check: answerItem.check,
              createdAt: answerItem.createdAt,
              itemKey: questionItem.itemKey,
              note: answerItem.note,
              partitionKey: answerItem.partitionKey,
              sortKey: `${questionItem.itemKey}#${answerItem.surveyAnswer[j]}`,
              surveyId: this.surveyId,
              title: questionItem.title,
              updatedAt: answerItem.updatedAt,
              userId: answerItem.userId,
              userSearchKey: `${questionItem.itemKey}#${answerItem.surveyAnswer[j]}`,
              value: answerItem.surveyAnswer[j],
            });
          } else {
            surveyResult.push({
              answerCode: answerItem.answerCode,
              check: answerItem.check,
              itemKey: questionItem.itemKey,
              note: answerItem.note,
              partitionKey:
                answerItem.userId.length > 0
                  ? `${this.surveyId}#${answerItem.userId}#${now + i}`
                  : `${this.surveyId}#${generatedUserId}#${now + i}`,
              sortKey: `${questionItem.itemKey}#${answerItem.surveyAnswer[j]}`,
              surveyId: this.surveyId,
              userId: answerItem.userId.length > 0 ? answerItem.userId : generatedUserId,
              userSearchKey: `${questionItem.itemKey}#${answerItem.surveyAnswer[j]}`,
              value: answerItem.surveyAnswer[j],
            });
          }
        }
      }
      if (this.hasValidUpdateFields(EN, answerItem)) {
        toUpdate.push({
          partitionKey: answerItem.partitionKey,
          updatedAt: answerItem.updatedAt,
          surveyResult: surveyResult,
        });
      } else {
        toCreate.push({
          surveyResult: surveyResult,
        });
      }
    }
    return {
      surveyId: this.surveyId,
      toUpdate: toUpdate,
      toCreate: toCreate,
      status: status,
    };
  }
  // each type validation

  validateText(item: { lineNumber: any; title: any; input: any; length: any; }) {
    // length
    this.validateLength(item.lineNumber, item.title, item.input, item.length);
  }

  validateEmail(item: { lineNumber: any; title: any; input: any; }) {
    // format
    this.validateEmailString(item.lineNumber, item.title, item.input);
  }

  validatePhone(item: { lineNumber: any; title: any; input: any; }) {
    // phone string
    this.validatePhoneString(item.lineNumber, item.title, item.input);
  }

  validateCheckboxes(item: { lineNumber: any; title: any; input: any; options: any; selection: any; }) {
    // options
    this.validateOptions(item.lineNumber, item.title, item.input, item.options);
    if (!this.isValid()) return;
    // selection
    this.validateSelectionCount(item.lineNumber, item.title, item.input, item.selection);
  }

  validateDropdown(item: { input: any; lineNumber: any; title: any; options: any; }) {
    // options
    if (item.input) {
      this.validateOptions(item.lineNumber, item.title, item.input, item.options);
    }
  }

  validateDate(item: { includeYear: any; lineNumber: any; title: any; input: any; }) {
    // date
    const format = item.includeYear ? "YYYY-MM-DD" : "MM-DD";
    this.validateDateString(item.lineNumber, item.title, item.input, format);
  }

  validateNumber(item: { lineNumber: any; title: any; input: any; range: any; }) {
    // number
    this.isNumber(item.lineNumber, item.title, item.input);

    // range
    this.validateNumRange(item.lineNumber, item.title, item.input, item.range);
  }

  validateTextarea(item: { lineNumber: any; title: any; input: any; length: any; }) {
    // length
    this.validateLength(item.lineNumber, item.title, item.input, item.length);
  }

  validateRadio(item: { lineNumber: any; title: any; input: any; options: any; }) {
    // options
    this.validateOptions(item.lineNumber, item.title, item.input, item.options);
  }

  validatePostcode(item: { lineNumber: any; title: any; input: any; }) {
    this.validatePostcodeString(item.lineNumber, item.title, item.input);
  }

  // validation detail
  isInputEmpty(item: { input: any; }) {
    if (!("input" in item) || item.input === "" || (Array.isArray(item.input) && item.input.length === 0)) {
      return true;
    }

    return false;
  }

  // common validation

  validateCommon(item: any) {
    // require
    this.validateRequired(item);
  }

  validateRequired(item: any) {
    if ("admin" in item.isRequired) {
      if (item.isRequired.admin === true) {
        if (this.isInputEmpty(item)) {
          this.putErrorMessage(item.lineNumber, item.title, "必須項目です。");
          return;
        }
      }
    } else {
      if (item.isRequired.value) {
        if (this.isInputEmpty(item)) {
          this.putErrorMessage(item.lineNumber, item.title, "必須項目です。");
          return;
        }
      }
    }
  }

  validateLength(lineNumber: any, title: any, input: string | Array<any>, length: { min: string; max: string; }) {
    if (!input || !length) {
      return;
    }

    const min = parseInt(length.min);
    const max = parseInt(length.max);
    let localErrorMessage = "";
    if (min && input.length < min) {
      localErrorMessage += `${min}文字以上`;
    }
    if (max && input.length > max) {
      localErrorMessage += `${max}文字以内`;
    }
    if (localErrorMessage.length > 0) {
      localErrorMessage += "で入力してください";
      this.putErrorMessage(lineNumber, title, localErrorMessage);
      return;
    }
  }

  validateEmailString(lineNumber: any, title: any, input: any) {
    if (!input) {
      return;
    }

    /* eslint-disable max-len */
    // from https://stackoverflow.com/questions/46155/how-to-validate-an-email-address-in-javascript
    const re =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if (!re.test(String(input).toLowerCase())) {
      this.putErrorMessage(lineNumber, title, "正しい形式で入力してください");
      return;
    }
    /* eslint-enable */
  }

  validatePhoneString(lineNumber: any, title: any, input: any) {
    if (!input) {
      return;
    }

    const re = /^([0-9]{1,15})$/;
    if (!re.test(String(input))) {
      this.putErrorMessage(lineNumber, title, "正しい形式で入力してください");
      return;
    }
  }

  validatePostcodeString(lineNumber: any, title: any, input: string) {
    if (!input) {
      return;
    }

    const pattern = /^[0-9]{7}$/;
    if (!pattern.test(input)) {
      this.putErrorMessage(lineNumber, title, "正しい郵便番号を入力してください。");
      return;
    }
  }

  isNumber(lineNumber: any, title: any, input: any, errorMessage: any = undefined) {
    if (!input) {
      return;
    }

    if (Number.isNaN(Number(input))) {
      this.putErrorMessage(lineNumber, title, errorMessage || "正しい形式で入力してください");
      return;
    }
  }

  validateNumRange(lineNumber: any, title: any, input: string, range: { min: string; max: string; }) {
    if (!input || !range) {
      return;
    }

    const inputInt = parseInt(input);

    const min = parseInt(range.min);
    const max = parseInt(range.max);
    if (min && !max) {
      if (inputInt < min) {
        this.putErrorMessage(lineNumber, title, `${min}以上で入力してください`);
        return;
      }
    } else if (!min && max) {
      if (max < inputInt) {
        this.putErrorMessage(lineNumber, title, `${max}以下で入力してください`);
        return;
      }
    } else {
      if (inputInt < min || max < inputInt) {
        this.putErrorMessage(lineNumber, title, `${min}〜${max}の範囲で入力してください`);
        return;
      }
    }
  }

  validateSelectionCount(lineNumber: any, title: any, input: string | Array<any>, selection: { min: string; max: string; }) {
    if (!input || !selection || this.isInputEmpty({ input: input })) {
      // [] is not falsy
      return;
    }

    const min = parseInt(selection.min);
    const max = parseInt(selection.max);
    const selectionCount = input.length;

    if (min && !max) {
      if (selectionCount < min) {
        this.putErrorMessage(lineNumber, title, `${min}個以上選択してください`);
        return;
      }
    } else if (!min && max) {
      if (max < selectionCount) {
        this.putErrorMessage(lineNumber, title, `${max}個以下で選択してください`);
        return;
      }
    } else if (min && max) {
      if (selectionCount < min || max < selectionCount) {
        this.putErrorMessage(lineNumber, title, `${min}〜${max}個選択してください`);
        return;
      }
    }
  }

  validateOptions(lineNumber: any, title: any, input: string | Array<any>, options: string | Array<any>) {
    if (!input || this.isInputEmpty({ input: input })) {
      // [] is not falsy
      return;
    }

    if (typeof input === 'string') {
      input = [input];
    }
    for (let i = 0; i < input.length; i++) {
      if (!options.includes(input[i])) {
        this.putErrorMessage(lineNumber, title, "不正なデータです");
        return;
      }
    }
  }

  validateDateString(lineNumber: any, title: any, input: string | Date | number, format: any) {
    if (!input) {
      return;
    }

    if (!dayjs(input, format).isValid()) {
      this.putErrorMessage(lineNumber, title, "正しい形式で入力してください");
      return;
    }
  }
}

export { ImportSurveyHelper };
