import { API } from "aws-amplify";

export const loadAllPages = async (method: string, apiName: string, path: string, 
  response: { lastEvaluatedKey: any; }, payload: { [x: string]: any; fromDate?: any; toDate?: any; dataType?: any; logType?: any; surveyId?: any; }): Promise<any> => {
  if (method === "GET") {
    payload["lastEvaluatedKey"] = response.lastEvaluatedKey ? JSON.stringify(response.lastEvaluatedKey) : "";
    return await getRequestLoad(apiName, path, payload);
  } else if (method === "POST") {
    payload["lastEvaluatedKey"] = response.lastEvaluatedKey ? response.lastEvaluatedKey : {};
    return await postRequestLoad(apiName, path, payload);
  }
};

export const getRequestLoad = async (apiName: any, path: any, payload: any): Promise<any> => {
  return await API.get(apiName, path, {
    queryStringParameters: payload,
  });
};

export const postRequestLoad = async (apiName: any, path: any, payload: any): Promise<any> => {
  return await API.post(apiName, path, {
    body: payload,
  });
};
