import { detect } from 'jschardet';

export const checkIsSJISEncoding = async (file: Blob): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e: any) => {
      const rawCSVText: any = e.target.result;
      const checkDecoding = detect(rawCSVText);
      const encodingTypes = ['SHIFT_JIS', 'ISO-8859-2'];
      const isSJISEncoding = encodingTypes.includes(checkDecoding.encoding);
      resolve(isSJISEncoding);
    };
    // reader.readAsBinaryString(file);
    reader.readAsArrayBuffer(file);
  });
};

export const readFileAsync = async (file: Blob) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      resolve(reader.result);
    };
    reader.onerror = reject;
    reader.readAsArrayBuffer(file);
  });
};