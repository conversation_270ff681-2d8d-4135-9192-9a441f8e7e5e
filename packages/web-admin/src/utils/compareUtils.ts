import dayjs from "dayjs";

export const compareString = (data: any, keyword: any): any => {
  return data.toString().trim() === keyword.toString().trim();
};

export const includesKeyword = (data: any, keyword: any): boolean => {
  if (keyword) {
    return data.toString().trim().includes(keyword);
  }
  return true;
};
//Greater than or equal with date
export const gteWithDate = (data: any, compareWithDate: any): boolean => {
  if (compareWithDate) {
    return data >= dayjs(compareWithDate).startOf("day").unix();
  }
  return true;
};

//Less than or equal with date
export const lteWithDate = (data: any, compareWithDate: any): boolean => {
  if (compareWithDate) {
    return data <= dayjs(compareWithDate).endOf("day").unix();
  }
  return true;
};
