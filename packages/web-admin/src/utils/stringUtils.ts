// import MaskData from "maskdata";

export const convertAuthErrorMessages = (error: { code: any; message: string | string[]; }): string => {
  const code = error.code ? error.code : undefined;
  var message = undefined;

  switch (code) {
    case "NotAuthorizedException":
    case "InvalidParameterException":
      if (error.message.includes("disabled")) {
        message = "アカウントが無効化されています。";
      } else {
        message = "ユーザ名またはパスワードが間違っているか登録されていません。";
      }
      break;
    case "InvalidPasswordException":
      message = "認証コードが間違っているか、パスワードポリシーを満たしていません。";
      break;
    case "CodeMismatchException":
      message = "認証コードが正しくありません。";
      break;
    case "LimitExceededException":
      message =
        "アカウント入力回数の上限を超えたため、ご利用いただけません。しばらく経ってからもう一度お試しください。";
      break;
    case "PasswordResetRequiredException":
      message = "パスワードはリセットされました。";
      break;
    default:
      message = "不明なエラーが発生しました。";
      break;
  }

  return message;
};

export const isNullOrEmpty = (str: any): boolean => {
  if (Number.isInteger(str)) {
    str = str.toString();
  }
  return str === null || str === undefined || str.toString().trim() === "";
};

export const convertGroupNames = (name: any) => {
  switch (name) {
    case "admins":
      return "アドミン";
    case "members":
      return "メンバー";
    case "guests":
      return "ゲスト";
    case "operators":
      return "オペレーター";
  }
};

export const trim = (str: string): string => {
  if (str === null || str === undefined) {
    return str;
  }
  return str.replace(/^[\s　\uFEFF\xA0]+|[\s　\uFEFF\xA0]+$/g, "");
};

export const convertSecretKeyToJapanese = (key: string | number): string => {
  let secretKeyList = {
    LINEMESSAGING_CHANNEL_ACCESS_TOKEN: "チャネルアクセストークン",
    LINEMESSAGING_CHANNEL_ID: "チャネルID",
    LINEMESSAGING_CHANNEL_SECRET: "チャネルシークレット",
    SB_LINEMESSAGING_CHANNEL_ACCESS_TOKEN: "チャネルアクセストークン",
    SB_LINEMESSAGING_CHANNEL_ID: "チャネルID",
    SB_LINEMESSAGING_CHANNEL_SECRET: "チャネルシークレット",
    EMAIL_CHATBOT_DAMAGE_REPORT1: "損傷報告先メールアドレス1 (EMAIL_CHATBOT_DAMAGE_REPORT1)",
    EMAIL_CHATBOT_DAMAGE_REPORT2: "損傷報告先メールアドレス2 (EMAIL_CHATBOT_DAMAGE_REPORT2)",
    EMAIL_CHATBOT_DAMAGE_REPORT3: "損傷報告先メールアドレス3 (EMAIL_CHATBOT_DAMAGE_REPORT3)",
    EMAIL_CHATBOT_DAMAGE_REPORT4: "損傷報告先メールアドレス4 (EMAIL_CHATBOT_DAMAGE_REPORT4)",
    SES_EMAIL_DOMAIN: "損傷報告送信メールドメイン (SES_EMAIL_DOMAIN)",
    CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH: "ゴミ分別のあいまい検索",
    SB_CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH: "ゴミ分別のあいまい検索",
    CHATBOT_FORWARD: "外部WebHook転送",
    SB_CHATBOT_FORWARD: "外部WebHook転送",
    CHATBOT_FORWARD_URL: "外部WebHook URL",
    SB_CHATBOT_FORWARD_URL: "外部WebHook URL",
  } as any;
  return secretKeyList[key] || key;
};

export const copyTextToClipboard = (elementRef: any) => {
  if (!elementRef?.value) return false;

  const inputElement = elementRef.value.getNativeElement(); // Get actual input element
  if (!inputElement) return false;

  inputElement.select();
  inputElement.setSelectionRange(0, 99999);

  navigator.clipboard.writeText(inputElement.value).then(() => {
    console.log("Copied to clipboard:", inputElement.value);
  }).catch(err => {
    console.error("Failed to copy:", err);
  });

  return true;
};

export const isValidUrlHost = (str: string, allowHttp = false): boolean => {
  let url;
  try {
    url = new URL(str);
  } catch {
    return false;
  }
  const protocolFilter = allowHttp ? ["http:", "https:"] : ["https:"];
  if (!protocolFilter.includes(url.protocol)) {
    return false;
  }
  const hostParams = decodeURIComponent(url.host).split(".");
  if (hostParams.length === 1 || !hostParams[hostParams.length - 1] || !!decodeURIComponent(url.host).match(/[\s　\uFEFF\xA0]+/)) {
    return false;
  }
  return true;
};

export const stringTruncate = (str: string, length: number): string => {
  if (str.length > length) {
    return str.substring(0, length);
  }
  return str;
}

export const replaceValueInJson = (jsonObject: any, textToFind: string, valueToReplace: string): any => {
  for(var x in jsonObject) {
    if (typeof jsonObject[x] === typeof {}) {
      
      replaceValueInJson(jsonObject[x], textToFind, valueToReplace);
      continue;
    } else if (Array.isArray(jsonObject[x])) {
      for(var item in jsonObject[x]){
      replaceValueInJson(item, textToFind, valueToReplace);
      }
      continue;
    } else if (typeof jsonObject[x] === typeof "" && jsonObject[x].includes(textToFind)) {
      jsonObject[x] = jsonObject[x].replace(textToFind, valueToReplace);
    }
  }
}