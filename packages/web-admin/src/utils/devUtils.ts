export const DEV_FEATURES_KEYS = {
    ENV_KEY: 'ENV_KEY',
    ID_TOKEN: 'DEV_ID_TOKEN_OVERRIDE',
    USERNAME: 'DEV_USERNAME',
}

export const ENV_VALUES = {
    VUE_APP_AMPLIFY_AUTH_IDENTITY_POOL_ID: process.env.VUE_APP_AMPLIFY_AUTH_IDENTITY_POOL_ID,
    VUE_APP_AMPLIFY_AUTH_USER_POOL_ID: process.env.VUE_APP_AMPLIFY_AUTH_USER_POOL_ID,
    VUE_APP_AMPLIFY_AUTH_USER_POOL_WEB_CLIENT_ID: process.env.VUE_APP_AMPLIFY_AUTH_USER_POOL_WEB_CLIENT_ID,
    VUE_APP_AMPLIFY_ADMIN_API_ENDPOINT_URL: process.env.VUE_APP_AMPLIFY_ADMIN_API_ENDPOINT_URL,
    VUE_APP_AMPLIFY_SCENARIO_API_ENDPOINT_URL: process.env.VUE_APP_AMPLIFY_SCENARIO_API_ENDPOINT_URL,
    VUE_APP_DAMAGE_REPORT_API_ENDPOINT_URL: process.env.VUE_APP_DAMAGE_REPORT_API_ENDPOINT_URL,
    VUE_APP_CQ_API_ENDPOINT_URL: process.env.VUE_APP_CQ_API_ENDPOINT_URL,
    VUE_APP_AMPLIFY_BOSAI_API_ENDPOINT_URL: process.env.VUE_APP_AMPLIFY_BOSAI_API_ENDPOINT_URL,
    VITE_DISTRIBUTION_RESOURCES_CLOUDFRONT_DOMAIN_NAME: import.meta.env.VITE_DISTRIBUTION_RESOURCES_CLOUDFRONT_DOMAIN_NAME
} as any

function envKey() {
    return window.localStorage.getItem(DEV_FEATURES_KEYS.ENV_KEY) || ''
}

/**
 * Retrieves a local environment variable based on the provided key.
 * Lookup order: 1 - local storage (key, prefixed with the value of ENV_KEY) 2 - local storage (key as provided)
 * 3 - ENV_VALUES map in memory (collects process.env.{...} vars)
 *
 * @param {string} key - The key of the environment variable to retrieve.
 *
 * @return {string | undefined} - The value of the environment variable. Returns undefined if the variable does not exist.
 */
export function getEnvLocalVar(key: string) {
    return window.localStorage.getItem(envKey() + ':' + key)
        || window.localStorage.getItem(key)
        || ENV_VALUES[key]
        || undefined
}

export function getDevUserPoolId() {
    return getEnvLocalVar('VUE_APP_AMPLIFY_AUTH_USER_POOL_ID')
}

export function getDevUserPoolClientId() {
    return getEnvLocalVar('VUE_APP_AMPLIFY_AUTH_USER_POOL_WEB_CLIENT_ID')
}

export function getPlatformApiUrl() {
    return getEnvLocalVar('VUE_APP_AMPLIFY_ADMIN_API_ENDPOINT_URL')
}

export function getTokenOverride() {
    return getEnvLocalVar(DEV_FEATURES_KEYS.ID_TOKEN)
}

export function getMockUsername() {
    return getEnvLocalVar(DEV_FEATURES_KEYS.USERNAME)
}

export function getDevAuthorizationHeaders() {
    const res = {
        'x-dev-username': getMockUsername(),
    } as any
    const tokenOverride = getTokenOverride()
    if (tokenOverride) {
        res['Authorization'] = 'Bearer ' + tokenOverride
    }

    return res;
}

// cache Cognito connection params in memory
export function storeCognitoParams(poolId: string, clientId: string) {
    ENV_VALUES.VUE_APP_AMPLIFY_AUTH_USER_POOL_ID = poolId
    ENV_VALUES.VUE_APP_AMPLIFY_AUTH_USER_POOL_WEB_CLIENT_ID = clientId
}

window.DEV_LIST_VARS = () => {
    console.log(Object.keys(ENV_VALUES).reduce((prv, key) => {
        return prv + `${key}=${ENV_VALUES[key]||''}\n`
    }, ''))
}
