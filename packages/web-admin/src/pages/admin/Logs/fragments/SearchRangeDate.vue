<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <div class="tw-p-1 justify-center row">
      <div class="tw-p-1 col-12 col-sm-auto">
        <q-select 
          use-input
          :options="fromYearOption"
          option-label="text"
          option-value="value"
          v-model="fromYear"
          outlined
          dense
          style="max-width: 110px"
          hide-selected
          fill-input
          @filter="(val, update) => filterYear(val, update, 'from')"
        ></q-select>
      </div>
      <div class="tw-p-1 col-12 col-sm-auto">
        <q-select 
          use-input
          :options="monthList"
          option-label="text"
          option-value="value"
          v-model="fromMonth"
          outlined
          dense
          style="max-width: 110px"
          hide-selected
          fill-input
          @filter="(val, update) => filterMonth(val, update, 'from')"
        ></q-select>
      </div>
      <div class="tw-p-1 col-12 col-sm-auto">
        <q-select 
          use-input
          :options="fromDayList"
          option-label="text"
          option-value="value"
          v-model="fromDay"
          outlined
          dense
          style="max-width: 110px"
          hide-selected
          fill-input
          @filter="(val, update) => filterDay(val, update, 'from')"
        ></q-select>
      </div>
      <div class="tw-pt-3 col-12 col-sm-auto">ー</div>
      <div class="tw-p-1 col-12 col-sm-auto">
        <q-select use-input
          :options="yearList"
          option-label="text"
          option-value="value"
          v-model="toYear"
          outlined
          dense
          style="max-width: 110px"
          hide-selected
          fill-input
          @filter="(val, update) => filterYear(val, update, 'to')"
        ></q-select>
      </div>
      <div class="tw-p-1 col-12 col-sm-auto">
        <q-select use-input
          :options="monthList"
          option-label="text"
          option-value="value"
          v-model="toMonth"
          outlined
          dense
          style="max-width: 110px"
          hide-selected
          fill-input
          @filter="(val, update) => filterMonth(val, update, 'to')"
        ></q-select>
      </div>
      <div class="tw-p-1 col-12 col-sm-auto">
        <q-select use-input
          :options="toDayList"
          option-label="text"
          option-value="value"
          v-model="toDay"
          outlined
          dense
          style="max-width: 110px"
          hide-selected
          fill-input
          @filter="(val, update) => filterDay(val, update, 'to')"
        ></q-select>
      </div>
      <div class="tw-p-1 col-12 col-sm-auto">
        <q-btn color="primary" @click="reloadDateData" :disabled="isFetchingBiData" min-width="80">表示 </q-btn>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useLogsStore } from '@/stores/modules/logs';
import { ref, computed, onBeforeMount } from 'vue';
import { cloneDeep } from "lodash";
import dayjs from "dayjs";

// store
const logsStore = useLogsStore();

// emits 
const emits = defineEmits<{
  (event: 'isUpdateSearchCriteria', payload: any): void;
}>();

// data
const commonSearchCriteria = ref<any>({});
const fromYearOption = ref([]);
const fromMonthOption = ref([]);
const fromDayOption = ref([]);
const toYearOption = ref([]);
const toMonthOption = ref([]);
const toDayOption = ref([]);

// methods
const reloadDateData = (): void => {
  logsStore.setSearchRangeDateCriteria(cloneDeep(commonSearchCriteria.value));
  emits("isUpdateSearchCriteria", true);
};

const getDayList = (type: any): Array<any> => {
  let _dayList = [];
  var days = dayjs().daysInMonth();

  if (commonSearchCriteria.value.fromMonth || commonSearchCriteria.value.toMonth) {
    switch (type) {
      case "from":
        days = dayjs(
          commonSearchCriteria.value.fromYear + "-" + commonSearchCriteria.value.fromMonth,
          "YYYY-MM"
        ).daysInMonth();
        break;
      case "to":
        days = dayjs(
          commonSearchCriteria.value.toYear + "-" + commonSearchCriteria.value.toMonth,
          "YYYY-MM"
        ).daysInMonth();
        break;
    }
  }

  for (var x = 1; x <= days; x++) {
    _dayList.push({
      value: x,
      text: x + "日",
    });
  }
  return _dayList;
};

const filterYear = (value: string, update: any, type: 'from' | 'to') => {
  if (type === 'from') {
    searchFilter(value, update, fromYearOption, yearList.value);
  }
  else {
    searchFilter(value, update, toYearOption, yearList.value);
  }
};

const filterMonth = (value: string, update: any, type: 'from' | 'to') => {
  if (type === 'from') {
    searchFilter(value, update, fromMonthOption, monthList.value);
  }
  else {
    searchFilter(value, update, toMonthOption, monthList.value);
  }
};

const filterDay = (value: string, update: any, type: 'from' | 'to') => {
  if (type === 'from') {
    searchFilter(value, update, fromDayOption, fromDayList.value);
  }
  else {
    searchFilter(value, update, toDayOption, toDayList.value);
  }
};

const searchFilter = (value: string, update: any, currentOptions: any, options: any) => {
  if (value === '') {
    update(() => {
      currentOptions.value = options;
    });
    return;
  }
  else {
    update(() => {
      value = value.toLowerCase();
      currentOptions.value = options.filter((elm: any) => 
        elm.text.toLowerCase().indexOf(value) > -1
      );
    });
  }
};

// computed
const searchRangeDateCriteria = computed(() => logsStore.searchRangeDateCriteria);
const isFetchingBiData = computed(() => logsStore.isFetchingBiData);
const fromDayList = computed((): any => {
  return getDayList("from");
});

const toDayList = computed((): any => {
  return getDayList("to");
});

const monthList = computed((): Array<any> => {
  let _monthList = [];
  for (let month = 1; month <= 12; month++) {
    _monthList.push({
      value: month,
      text: month + "月",
    });
  }
  return _monthList;
});

const yearList = computed((): Array<any> => {
  let _yearList = [];
  const this_year = dayjs().year();
  for (let year = 2019; year <= this_year; year++) {
    _yearList.push({
      value: year,
      text: year + "年",
    });
  }
  return _yearList;
});

const fromYear = computed({
  get: () => {
    return {
      text: commonSearchCriteria.value.fromYear + '年',
      value: commonSearchCriteria.value.fromYear,
    };
  },
  set: (newVal) => {
    commonSearchCriteria.value.fromYear = newVal.value;
    updateFromDay();
  }
});

const toYear = computed({
  get: () => {
    return {
      text: commonSearchCriteria.value.toYear + '年',
      value: commonSearchCriteria.value.toYear,
    };
  },
  set: (newVal) => {
    commonSearchCriteria.value.toYear = newVal.value;
    updateToDay();
  }
});

const fromMonth = computed({
  get: () => {
    return {
      text: commonSearchCriteria.value.fromMonth + '月',
      value: commonSearchCriteria.value.fromMonth,
    }
  },
  set: (newVal) => {
    commonSearchCriteria.value.fromMonth = newVal.value;
    updateFromDay();
  }
});

const toMonth = computed({
  get: () => {
    return {
      text: commonSearchCriteria.value.toMonth + '月',
      value: commonSearchCriteria.value.toMonth,
    };
  },
  set: (newVal) => {
    commonSearchCriteria.value.toMonth = newVal.value;
    updateToDay();
  }
});

const fromDay = computed({
  get: () => {
    return {
      text: commonSearchCriteria.value.fromDay + '日',
      value: commonSearchCriteria.value.fromDay,
    };
  },
  set: (newVal) => {
    commonSearchCriteria.value.fromDay = newVal.value;
  }
});

const toDay = computed({
  get: () => {
    return {
      text: commonSearchCriteria.value.toDay + '日',
      value: commonSearchCriteria.value.toDay,
    };
  },
  set: (newVal) => {
    commonSearchCriteria.value.toDay = newVal.value;
  }
});

const updateFromDay = () => {
  if (fromDayList.value.length < commonSearchCriteria.value.fromDay) {
    commonSearchCriteria.value.fromDay = fromDayList.value.length;
  }
};

const updateToDay = () => {
  if (toDayList.value.length < commonSearchCriteria.value.toDay) {
    commonSearchCriteria.value.toDay = toDayList.value.length;
  }
};

// hooks
onBeforeMount(() => {
  commonSearchCriteria.value = cloneDeep(searchRangeDateCriteria.value);
});
</script>