<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-card elevation="2">
      <q-toolbar dense flat class="mt-1">
        <div class="body-2 primary--text">「システム」ログイン</div>

        <q-space></q-space>
        <q-chip class="ml-2" outlined color="primary" dark> mngLogin </q-chip>
        <q-chip class="ml-2" color="orange" dark> 2020年06月 </q-chip>
      </q-toolbar>
      <div class="px-2 pb-2">
        <BarChart :labels="labels" :dataSet="getFriendData" />
      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { computed,  } from 'vue';

import { useLogsStore } from '@stores/modules/logs';

// old imports
// 旧インポート
import BarChart from "@/components/charts/BarChart.vue";

const logsStore = useLogsStore();

// computed
const labels = computed(() => {
  let _labels = [];
  for (let day = 1; day <= 31; day++) {
    _labels.push(day + "日");
  }
  return _labels;
});
const getFriendData = computed(() => logsStore.getFriendData);
const isFetchingBiData = computed(() => logsStore.isFetchingBiData);
</script>