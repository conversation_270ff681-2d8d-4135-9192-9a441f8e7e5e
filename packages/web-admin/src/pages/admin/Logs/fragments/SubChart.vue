<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-card elevation="2">
      <q-toolbar dense flat class="mt-1">
        <q-chip class="ml-2" label color="primary" dark small> 5日: 79 </q-chip>
        <q-chip class="ml-2" label color="orange" dark small> 10日: 169 </q-chip>
        <q-chip class="ml-2" label outlined color="green" dark small>
          <q-icon small left>mdi-arrow-up-drop-circle</q-icon>
          81,54%
        </q-chip>
        <q-space></q-space>
        <q-chip class="ml-2" label outlined color="primary" dark> mngLogin </q-chip>
        <q-chip class="ml-2" label color="orange" dark> 2020年06月 </q-chip>
      </q-toolbar>
      <div class="px-2 pb-2">
        <LineChart :labels="labels" :dataSet="dataSet" />
      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { computed,  } from 'vue';



// old imports
// 旧インポート
/*import LineChart from "@/components/charts/LineChart.vue";*/


// methods
const getRandomInt = (): number => {
      return Math.floor(Math.random() * (50 - 5 + 1)) + 5;
    };

// computed
const labels = computed((): Array<any> => {
      let _labels = [];
      for (let hour = 0; hour <= 23; hour++) {
        _labels.push(hour + "時");
      }
      return _labels;
    });
const dataSet = computed((): Array<any> => {
      let _dataSet = [];
      for (let day = 1; day <= 31; day++) {
        _dataSet.push(getRandomInt());
      }
      return _dataSet;
    });
</script>