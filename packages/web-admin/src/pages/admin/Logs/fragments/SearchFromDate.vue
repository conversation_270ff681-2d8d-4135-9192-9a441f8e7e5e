<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <div no-gutters class="tw-p-1 tw-justify-center row">
      <div class="tw-p-1 col-12 col-sm-auto">
        <q-select 
          :options="yearOption"
          option-label="text"
          option-value="value"
          v-model="year"
          use-input
          outlined
          dense
          background-color="white"
          style="max-width: 110px"
          hide-selected
          fill-input
          @filter="filterYear"
        ></q-select>
      </div>
      <div class="tw-p-1 col-12 col-sm-auto">
        <q-select 
          :options="monthOption"
          option-label="text"
          option-value="value"
          v-model="month"
          use-input
          outlined
          dense
          background-color="white"
          style="max-width: 110px"
          hide-selected
          fill-input
          @filter="filterMonth"
        ></q-select>
      </div>
      <div class="tw-p-1 col-12 col-sm-auto">
        <q-select 
          v-if="showDay"
          :options="dayOption"
          option-label="text"
          option-value="value"
          v-model="day"
          use-input
          outlined
          dense
          background-color="white"
          style="max-width: 110px"
          hide-selected
          fill-input
          @filter="filterDay"
        ></q-select>
      </div>
      <div class="tw-p-1 col-12 col-sm-auto">
        <q-btn color="primary" @click="reloadDateData" :disabled="isFetchingBiData">表示</q-btn>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useLogsStore } from '@/stores/modules/logs';
import { PropType, ref,  computed, onBeforeMount } from 'vue';
import { cloneDeep } from "lodash";
import dayjs from "dayjs";

// store
const logsStore = useLogsStore();

// props
defineProps({
  showDay: Boolean as PropType<boolean>
});

// data
const commonSearchCriteria = ref<any>({});
const yearOption = ref([]);
const monthOption = ref([]);
const dayOption = ref();

// methods
const reloadDateData = (): void => {
  logsStore.setSearchFromDateCriteria(cloneDeep(commonSearchCriteria.value));
};

const filterYear = (value: string, update: any) => {
  searchFilter(value, update, yearOption, yearList.value);
};

const filterMonth = (value: string, update: any) => {
  searchFilter(value, update, monthOption, monthList.value);
};

const filterDay = (value: string, update: any) => {
  searchFilter(value, update, dayOption, dayList.value);
};

const searchFilter = (value: string, update: any, currentOptions: any, options: any) => {
  if (value === '') {
    update(() => {
      currentOptions.value = options;
    });
    return;
  }
  else {
    update(() => {
      value = value.toLowerCase();
      currentOptions.value = options.filter((elm: any) => 
        elm.text.toLowerCase().indexOf(value) > -1
      );
    });
  }
};

// computed
const searchFromDateCriteria = computed(() => logsStore.searchFromDateCriteria);
const isFetchingBiData = computed(() => logsStore.isFetchingBiData);

const dayList = computed((): Array<any> => {
  let _dayList = [];
  let days = dayjs().daysInMonth();
  if (commonSearchCriteria.value.month) {
    days = dayjs(commonSearchCriteria.value.year + "-" + commonSearchCriteria.value.month, "YYYY-MM").daysInMonth();
  }
  for (let x = 1; x <= days; x++) {
    _dayList.push({
      value: x,
      text: x + "日",
    });
  }
  return _dayList;
});

const monthList = computed((): Array<any> => {
  let _monthList = [];
  for (let month = 1; month <= 12; month++) {
    _monthList.push({
      value: month,
      text: month + "月",
    });
  }
  return _monthList;
});

const yearList = computed((): Array<any> => {
  let _yearList = [];
  const this_year = dayjs().year();
  for (let year = 2019; year <= this_year; year++) {
    _yearList.push({
      value: year,
      text: year + "年",
    });
  }
  return _yearList;
});

const year = computed({
  get: () => {
    return {
      text: commonSearchCriteria.value.year + '年',
      value: commonSearchCriteria.value.year
    };
  },
  set: (newVal) => {
    commonSearchCriteria.value.year = newVal.value;
    updateDay();
  }
});

const month = computed({
  get: () => {
    return {
      text: commonSearchCriteria.value.month + '月',
      value: commonSearchCriteria.value.month
    };
  },
  set: (newVal) => {
    commonSearchCriteria.value.month = newVal.value;
    updateDay();
  }
});

const day = computed({
  get: () => {
    return {
      text: commonSearchCriteria.value.day + '日',
      value: commonSearchCriteria.value.day
    };
  },
  set: (newVal) => {
    commonSearchCriteria.value.day = newVal.value;
  }
});

const updateDay = () => {
  if (dayList.value.length < commonSearchCriteria.value.day) {
    commonSearchCriteria.value.day = dayList.value.length;
  }
};

// hooks
onBeforeMount(() => {
  commonSearchCriteria.value = cloneDeep(searchFromDateCriteria.value);
});
</script>