<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-card flat bordered class="tw-m-3">
      <SearchFromDate class="tw-py-4" :showDay="showDay" />
    </q-card>
    <q-card flat bordered class="tw-m-3">
      <div v-if="!isOverlay" class="tw-mt-4">
        <StaticFragment :summary="summary" :max="max" :min="min" :average="average" :key="staticKey" />
        <q-toolbar flat class="tw-pt-4">
          <div class="body-2 text-primary">{{ title }}</div>
          <q-space></q-space>
          <q-select 
            v-model="dataType"
            :options="displayOptions"
            option-label="text"
            option-value="value"
            outlined
            dense
            style="max-width: 150px"
          ></q-select>
        </q-toolbar>
        <div class="tw-px-2 tw-pb-2">
          <BarChart :labels="dataCollection.labels" :dataSet="dataSet" :key="chartKey" />
        </div>
      </div>
      <div v-else class="tw-flex tw-justify-center tw-items-center" style="height: 350px;">
        <div v-if="isFetchingBiData" class="text-primary" >
          <div class="tw-flex tw-justify-center tw-mb-3">
            <q-spinner :size="50" color="primary" />
          </div>
          {{ title + 'を取得中...' }}
        </div>
        <div v-if="fetchBiError">
          <ContentLoadError :color="'grey'" :error="fetchBiError" />
        </div>
      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { useLogsStore } from '@/stores/modules/logs';
import { PropType, ref,  computed, watch, onBeforeMount } from 'vue';
import { cloneDeep } from "lodash";
import dayjs from "dayjs";
import SearchFromDate from "../../fragments/SearchFromDate.vue";
import StaticFragment from "@/pages/admin/Logs/fragments/StaticFragment.vue";
import BarChart from "@/components/charts/BarChart.vue";
import ContentLoadError from "@/components/common/ContentLoadError.vue";

// store
const logsStore  = useLogsStore();

// props
const props = defineProps({
  title: String as PropType<string>
});

enum Period {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY'
}

// data
const dataCollection = ref<any>({ datasets: [] });
const dataType = ref<any>({ text: "日次", value: Period.DAILY });
const displayOptions = ref<any>([
  { text: "日次", value: Period.DAILY },
  { text: "週次", value: Period.WEEKLY },
  { text: "月次", value: Period.MONTHLY },
]);
const fromDate = ref<any>(null);
const toDate = ref<any>(null);
const showDay = ref<boolean>(true);
const payload = ref<any>({});
const summary = ref<any>([]);
const max = ref<any>([]);
const min = ref<any>([]);
const average = ref<any>([]);
const staticKey = ref<number>(0);
const chartKey = ref<number>(0);
const dataSet = ref<any>({});

// methods
const getFromDate = (): Array<any> => {
  return [searchFromDateCriteria.value.year, searchFromDateCriteria.value.month, searchFromDateCriteria.value.day];
};

const getToDate = (): Array<any> => {
  var year = searchFromDateCriteria.value.year;
  var month = searchFromDateCriteria.value.month;
  var day = searchFromDateCriteria.value.day;

  switch (dataType.value.value) {
    case Period.DAILY:
      if (month === 12 || month.toString() === "12") {
        year = year + 1;
        month = 1;
        break;
      }
      year = year;
      month = month + 1;
      break;
    case Period.WEEKLY:
      if (month === 1 || month.toString() === "1") {
        month = 12;
        break;
      }
      year = year + 1;
      month = month - 1;
      break;
    case Period.MONTHLY:
      showDay.value = false;
      if (month === 1 || month.toString() === "1") {
        year = year + 2;
        month = 12;
        break;
      }
      year = year + 3;
      month = month - 1;
      break;
  }

  return [year, month, day];
};

const setPayload = async (fromDate: any, toDate: any, dataType: any): Promise<void> => {
  payload.value = {
    fromDate: fromDate,
    toDate: toDate,
    dataType: dataType,
  };
    
  await logsStore.fetchSegmentActivationsData(payload.value);
};

// computed
const segmentActivationsData = computed(() => logsStore.segmentActivationsData);
const isFetchingBiData = computed(() => logsStore.isFetchingBiData);
const fetchBiError = computed(() => logsStore.fetchBiError);
const searchFromDateCriteria = computed(() => logsStore.searchFromDateCriteria);

const isOverlay = computed(() => {
  return isFetchingBiData.value || fetchBiError.value;
});

// watch
watch(
  () => segmentActivationsData.value,
  (value) => {
    dataCollection.value = cloneDeep(value);

    dataSet.value.labels = cloneDeep(value.labels);
    dataSet.value.datasets = cloneDeep(value.datasets[0]);

    summary.value = [];
    max.value = [];
    min.value = [];
    average.value = [];

    let length = dataCollection.value.labels.length;
    let datasetTemp = dataCollection.value.datasets[0];

    let summaryItem: any = {};
    let averageItem: any = {};

    let summaryValue = 0;
    let maxValue = 0;
    let minValue = 0;

    let segmentValueList = [];

    for (let i = 0; i < datasetTemp.length; i++) {
      let segmentValue = 0;
      for (let j = 0; j < datasetTemp[i].data.length; j++) {
        if (datasetTemp[i].data[j]) {
          segmentValue += datasetTemp[i].data[j];
        }
      }

      summaryValue += segmentValue;
      if (i === 0) {
        maxValue = segmentValue;
        minValue = segmentValue;
      }

      if (segmentValue > maxValue) {
        maxValue = segmentValue;
      }
      if (segmentValue < minValue) {
        minValue = segmentValue;
      }
      segmentValueList.push(segmentValue);
    }

    summaryItem.value = summaryValue;
    summary.value.push(summaryItem);

    if (length > 0) {
      averageItem.value = Number((summaryValue / length).toFixed(2));
      average.value.push(averageItem);
    }

    let maxLabel = "";
    let minLabel = "";
    for (let i = 0; i < segmentValueList.length; i++) {
      if (segmentValueList[i] === maxValue) {
        if (maxLabel === "") {
          maxLabel = datasetTemp[i].label;
        } else {
          maxLabel = maxLabel + ", " + datasetTemp[i].label;
        }
      }

      if (segmentValueList[i] === minValue) {
        if (minLabel === "") {
          minLabel = datasetTemp[i].label;
        } else {
          minLabel = minLabel + ", " + datasetTemp[i].label;
        }
      }
    }

    let maxItem: any = {};
    maxItem.label = maxLabel;
    maxItem.value = maxValue;
    max.value.push(maxItem);

    let minItem: any = {};
    minItem.label = minLabel;
    minItem.value = minValue;
    min.value.push(minItem);
  }
);

watch(
  () => searchFromDateCriteria.value,
  async (): Promise<void> => {
    fromDate.value = getFromDate();
    toDate.value = getToDate();
    await setPayload(dayjs(fromDate.value).format("YYYYMMDD"), dayjs(toDate.value).format("YYYYMMDD"), dataType.value.value);
  }
);

watch(
  () => dataType.value,
  async (value: any): Promise<void> => {
    showDay.value = true;
    fromDate.value = getFromDate();
    toDate.value = getToDate();
    await setPayload(dayjs(fromDate.value).format("YYYYMMDD"), dayjs(toDate.value).format("YYYYMMDD"), value.value);
  }
);

watch(
  () => isFetchingBiData.value,
  (value: any): void => {
    if (!value) {
      staticKey.value++;
      chartKey.value++;
    }
  }
);

// hooks
onBeforeMount(async () => {
  if (searchFromDateCriteria.value) {
    fromDate.value = getFromDate();
    toDate.value = getToDate();
    await setPayload(dayjs(fromDate.value).format("YYYYMMDD"), dayjs(toDate.value).format("YYYYMMDD"), dataType.value.value);
  } else {
    await setPayload(fromDate.value, toDate.value, dataType.value.value);
  }
});
</script>
