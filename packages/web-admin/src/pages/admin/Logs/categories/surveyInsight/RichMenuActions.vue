<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-card flat bordered class="tw-m-3 tw-py-4">
      <SearchFromDate :showDay="showDay" />
    </q-card>
    <q-card flat bordered class="tw-m-3">
      <div v-if="!isOverlay">
        <StaticFragment :summary="summary" :max="max" :min="min" :average="average" :key="staticKey" class="tw-mt-4" />
        <q-toolbar class="tw-pt-4">
          <div class="text-primary">{{ title }}</div>
          <q-space></q-space>
          <q-select 
            v-model="dataType"
            :options="displayOptions"
            option-label="text"
            option-value="value"
            outlined
            dense
            style="max-width: 150px"
          ></q-select>
        </q-toolbar>
        <div class="tw-px-2 tw-pb-2">
          <BarChart :labels="dataCollection.labels" :dataSet="dataCollection" :key="chartKey" />
        </div>
      </div>
      <div v-else class="tw-flex tw-justify-center tw-items-center" style="height: 350px;">
        <div v-if="isFetchingBiData">
          <div class="tw-flex tw-justify-center tw-mb-3">
            <q-spinner :size="50" color="primary" />
          </div>
          <span class="text-primary col-12">{{ title + 'を取得中...' }}</span>
        </div>
        <div v-if="fetchBiError" class="tw-flex tw-justify-center tw-m-3">
          <ContentLoadError :color="'grey'" :error="fetchBiError" />
        </div>
      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { useLogsStore } from '@/stores/modules/logs';
import { PropType, ref,  computed, watch, onBeforeMount } from 'vue';
import { cloneDeep } from "lodash";
import dayjs from "dayjs";
import SearchFromDate from "../../fragments/SearchFromDate.vue";
import StaticFragment from "@/pages/admin/Logs/fragments/StaticFragment.vue";
import BarChart from "@/components/charts/BarChart.vue";
import ContentLoadError from "@/components/common/ContentLoadError.vue";

// store
const logsStore = useLogsStore();

enum Period {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY'
}

// props
const props = defineProps({
  title: String as PropType<string>
});

// data
const dataCollection = ref<any>({});
const dataType = ref<any>({ text: "日次", value: Period.DAILY });
const displayOptions = ref<any>([
  { text: "日次", value: Period.DAILY },
  { text: "週次", value: Period.WEEKLY },
  { text: "月次", value: Period.MONTHLY },
]);
const fromDate = ref<any>(null);
const toDate = ref<any>(null);
const showDay = ref<boolean>(true);
const payload = ref<any>({});
const summary = ref<any>([]);
const max = ref<any>([]);
const min = ref<any>([]);
const average = ref<any>([]);
const staticKey = ref<number>(0);
const chartKey = ref<number>(0);

// methods
const getFromDate = (): Array<any> => {
  return [searchFromDateCriteria.value.year, searchFromDateCriteria.value.month, searchFromDateCriteria.value.day];
};

const getToDate = (): Array<any> => {
  let year = searchFromDateCriteria.value.year;
  let month = searchFromDateCriteria.value.month;
  let day = searchFromDateCriteria.value.day;

  switch (dataType.value.value) {
    case Period.DAILY:
      if (month === 12 || month.toString() === "12") {
        year = year + 1;
        month = 1;
        break;
      }
      year = year;
      month = month + 1;
      break;
    case Period.WEEKLY:
      if (month === 1 || month.toString() === "1") {
        month = 12;
        break;
      }
      year = year + 1;
      month = month - 1;
      break;
    case Period.MONTHLY:
      showDay.value = false;
      if (month === 1 || month.toString() === "1") {
        year = year + 2;
        month = 12;
        break;
      }
      year = year + 3;
      month = month - 1;
      break;
  }

  return [year, month, day];
};

const setPayload = (fromDate: any, toDate: any, dataType: any): void => {
  payload.value = {
    fromDate: fromDate,
    toDate: toDate,
    dataType: dataType,
  };

  logsStore.fetchRichmenuActionsData(payload.value);
};

// computed
const richmenuActionsData = computed(() => logsStore.richmenuActionsData);
const isFetchingBiData = computed(() => logsStore.isFetchingBiData);
const fetchBiError = computed(() => logsStore.fetchBiError);
const searchFromDateCriteria = computed(() => logsStore.searchFromDateCriteria);

const isOverlay = computed(() => {
  return isFetchingBiData.value || fetchBiError.value;
});

// watch
watch(
  () => richmenuActionsData.value,
  (value) => {
    dataCollection.value = cloneDeep(value);

    let length = dataCollection.value.labels.length;

    summary.value = [];
    max.value = [];
    min.value = [];
    average.value = [];

    let maxValue = 0;
    let minValue = 0;
    let summaryValue = 0;

    let summaryItem: any = {};
    let averageItem: any = {};

    for (let index = 0; index < dataCollection.value.datasets[0].data.length; index++) {
      if (dataCollection.value.datasets[0].data[index] === undefined) {
        continue;
      }
      if (maxValue === undefined) {
        maxValue = dataCollection.value.datasets[0].data[index];
      }
      if (minValue === undefined) {
        minValue = dataCollection.value.datasets[0].data[index];
      }

      summaryValue += dataCollection.value.datasets[0].data[index];

      if (dataCollection.value.datasets[0].data[index] > maxValue) {
        maxValue = dataCollection.value.datasets[0].data[index];
      }

      if (dataCollection.value.datasets[0].data[index] < minValue) {
        minValue = dataCollection.value.datasets[0].data[index];
      }
    }

    summaryItem.value = summaryValue;
    summary.value.push(summaryItem);

    let maxItem: any = {};
    maxItem.value = maxValue;
    max.value.push(maxItem);

    let minItem: any = {};
    minItem.value = minValue;
    min.value.push(minItem);

    if (length > 0) {
      averageItem.value = Number((summaryValue / length).toFixed(2));
      average.value.push(averageItem);
    }
  }
);

watch(
  () => searchFromDateCriteria.value,
  (): void => {
    fromDate.value = getFromDate();
    toDate.value = getToDate();
    setPayload(dayjs(fromDate.value).format("YYYYMMDD"), dayjs(toDate.value).format("YYYYMMDD"), dataType.value.value);
  }
);

watch(
  () => dataType.value,
  (value: any): void => {
    showDay.value = true;
    fromDate.value = getFromDate();
    toDate.value = getToDate();
    setPayload(dayjs(fromDate.value).format("YYYYMMDD"), dayjs(toDate.value).format("YYYYMMDD"), value.value);
  }
);

watch(
  () => isFetchingBiData.value,
  (value: boolean): void => {
    if (!value) {
      staticKey.value++;
      chartKey.value++;
    }
  }
);

// hooks
onBeforeMount(() => {
  if (searchFromDateCriteria.value) {
    fromDate.value = getFromDate();
    toDate.value = getToDate();
    setPayload(dayjs(fromDate.value).format("YYYYMMDD"), dayjs(toDate.value).format("YYYYMMDD"), dataType.value.value);
  } else {
    setPayload(fromDate.value, toDate.value, dataType.value.value);
  }
});
</script>
