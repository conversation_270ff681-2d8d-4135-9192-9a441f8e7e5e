<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-card flat bordered class="tw-m-3">
      <SearchRangeDate @isUpdateSearchCriteria="isUpdateSearchCriteria" class="tw-py-4" />
    </q-card>
    <q-card :disable="isFetchingBiData && showDetailChart" flat bordered class="tw-m-3">
      <div v-if="!isOverlayGeneralCard">
        <StaticFragment :summary="summary" :max="max" :min="min" :average="average" :key="staticKey" class="tw-mt-4" />
        <q-toolbar flat>
          <div class="body-2 text-primary">{{ liffTitle }}</div>
          <q-space></q-space>
          <div class="text-caption text-grey tw-font-italic">別各項目をタップすると、割合グラフが表示されます。</div>
        </q-toolbar>
        <div class="tw-px-2 tw-pb-2">
          <MultiBarChart :labels="liffDataCollection.labels" :dataSet="liffDataCollection" @onBarClick="onBarClick" />
        </div>
      </div>
      <div v-else class="tw-flex tw-justify-center tw-items-center" style="height: 350px;">
        <div v-if="isFetchingBiData && !showDetailChart" class="text-primary">
          <div class="tw-flex tw-justify-center tw-mb-3">
            <q-spinner :size="50" color="primary" />
          </div>
          {{ liffTitle + 'を取得中...' }}
        </div>
        <div v-if="fetchBiError">
          <ContentLoadError :color="'grey'" :error="fetchBiError" />
        </div>
      </div>
    </q-card>
    <q-card flat bordered v-if="showDetailChart" class="tw-m-3">
      <div v-if="!isOverlayDetailCard">
        <q-toolbar flat>
          <div class="body-2 text-primary">「{{ detailTitle }}」別各項目割合</div>
        </q-toolbar>
        <div class="tw-px-2 tw-pb-2">
          <BarChart :labels="detailDataCollection.labels" :dataSet="detailDataCollection" :key="chartKey" />
        </div>
      </div>
      <div v-else class="tw-flex tw-justify-center tw-items-center" style="height: 350px;">
        <div v-if="isFetchingBiData" class="text-primary">
          <div class="tw-flex tw-justify-center tw-mb-3">
            <q-spinner :size="50" color="primary" />
          </div>
          別各項目割合を取得中...
        </div>
        <div v-if="fetchBiError">
          <ContentLoadError :color="'grey'" :error="fetchBiError" />
        </div>

      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { useLogsStore } from '@/stores/modules/logs';
import { PropType, ref,  computed, watch, onBeforeMount } from 'vue';
import dayjs from "dayjs";
import { cloneDeep } from "lodash";
import SearchRangeDate from "../../fragments/SearchRangeDate.vue";
import StaticFragment from "@/pages/admin/Logs/fragments/StaticFragment.vue";
import MultiBarChart from "@/components/charts/MultiBarChart.vue";
import ContentLoadError from "@/components/common/ContentLoadError.vue";
import BarChart from "@/components/charts/BarChart.vue";

// store
const logsStore = useLogsStore();

// props
const props = defineProps({
  liffTitle: String as PropType<string>
});

enum Period {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY'
}

// data
const liffDataCollection = ref<any>({});
const detailDataCollection = ref<any>({});
const dataType = ref<string>(Period.DAILY);
const fromDate = ref<any>(null);
const toDate = ref<any>(null);
const showDetailChart = ref<boolean>(false);
const detailTitle = ref<string>("");
const summary = ref<any>([]);
const max = ref<any>([]);
const min = ref<any>([]);
const average = ref<any>([]);
const staticKey = ref<number>(0);
const chartKey = ref<number>(0);

// methods
const getFromDate = (): Array<any> => {
  return [
    searchRangeDateCriteria.value.fromYear,
    searchRangeDateCriteria.value.fromMonth,
    searchRangeDateCriteria.value.fromDay,
  ];
};

const getToDate = (): Array<any> => {
  return [
    searchRangeDateCriteria.value.toYear,
    searchRangeDateCriteria.value.toMonth,
    searchRangeDateCriteria.value.toDay,
  ];
};

const setPayload = async (fromDate: any, toDate: any, dataType: any): Promise<void> => {
  let payload = {
    fromDate: fromDate,
    toDate: toDate,
    dataType: dataType,
  };

  await logsStore.fetchLiffActivationsData(payload);
};

const onBarClick = (index: any): void => {
  showDetailChart.value = true;
  detailTitle.value = liffActivationsData.value.labels[index];
  const payload = {
    fromDate: dayjs(fromDate.value).format("YYYYMMDD"),
    toDate: dayjs(toDate.value).format("YYYYMMDD"),
    surveyId: liffIds.value[index],
    dataType: dataType.value,
  };
  logsStore.fetchLiffDetailData(payload);
};

const isUpdateSearchCriteria = (value: any): void => {
  if (value) {
    showDetailChart.value = false;
  }
};

// computed
const liffIds = computed(() => logsStore.liffIds);
const liffActivationsData = computed(() => logsStore.liffActivationsData);
const liffDetailData = computed(() => logsStore.liffDetailData);
const isFetchingBiData = computed(() => logsStore.isFetchingBiData);
const fetchBiError = computed(() => logsStore.fetchBiError);
const chartLabels = computed(() => logsStore.chartLabels);
const chartDataSet = computed(() => logsStore.chartDataSet);
const searchRangeDateCriteria = computed(() => logsStore.searchRangeDateCriteria);

const isOverlayGeneralCard = computed(() => {
  return (isFetchingBiData.value && !showDetailChart.value) || fetchBiError.value;
});

const isOverlayDetailCard = computed(() => {
  return isFetchingBiData.value || fetchBiError.value;
});

// watch
watch(
  () => liffActivationsData.value,
  (value) => {
    liffDataCollection.value = cloneDeep(value);
    if (liffDataCollection.value.datasets[0] === undefined) {
      return;
    }
    let dataSets = liffDataCollection.value.datasets[0];

    summary.value = [];
    max.value = [];
    min.value = [];
    average.value = [];

    let length = dataSets.data.length;

    let summaryValue = 0;
    let maxValue = 0;
    let minValue = 0;

    let summaryItem: any = {};
    let averageItem: any = {};

    for (let index = 0; index < length; index++) {
      if (dataSets.data[index] === undefined) {
        continue;
      }
      if (maxValue === undefined) {
        maxValue = dataSets.data[index];
      }
      if (minValue === undefined) {
        minValue = dataSets.data[index];
      }
      summaryValue += dataSets.data[index];

      if (dataSets.data[index] > maxValue) {
        maxValue = dataSets.data[index];
      }

      if (dataSets.data[index] < minValue) {
        minValue = dataSets.data[index];
      }
    }

    summaryItem.value = summaryValue;
    summary.value.push(summaryItem);

    let maxItem: any = {};
    maxItem.value = maxValue;
    max.value.push(maxItem);

    let minItem: any = {};
    minItem.value = minValue;
    min.value.push(minItem);

    if (length > 0) {
      averageItem.value = Number((summaryValue / length).toFixed(2));
      average.value.push(averageItem);
    }
  }
);

watch(
  () => liffDetailData.value,
  (value) => {
    detailDataCollection.value = cloneDeep(value);
  }
);

watch(
  () => searchRangeDateCriteria.value,
  (value) => {
    var startDate = dayjs(value.fromYear + "-" + value.fromMonth + "-" + value.fromDay);
    var endDate = dayjs(value.toYear + "-" + value.toMonth + "-" + value.toDay);

    var payload = {
      fromDate: startDate.format("YYYYMMDD"),
      toDate: endDate.format("YYYYMMDD"),
      dataType: Period.DAILY,
    };
    logsStore.fetchLiffActivationsData(payload);
  }
);

watch(
  () => isFetchingBiData.value,
  (value) => {
    if (!value) {
      staticKey.value++;
      chartKey.value++;
    }
  }
);

// TODO: 参照データ該当なし - honda
// watch(
//   () => chartDataSource,
//   (value) => {
//     detailDataCollection.value = cloneDeep(value);
//   }
// );

// hooks
onBeforeMount(() => {
  if (searchRangeDateCriteria.value) {
    fromDate.value = getFromDate();
    toDate.value = getToDate();
    setPayload(dayjs(fromDate.value).format("YYYYMMDD"), dayjs(toDate.value).format("YYYYMMDD"), dataType.value);
  } else {
    setPayload(fromDate.value, toDate.value, dataType.value);
  }
});
</script>
