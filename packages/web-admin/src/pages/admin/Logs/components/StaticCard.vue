<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-card :class="'bg-' + color + '-1 text-' + color" class="tw-text-center" flat bordered>
    <div class="tw-p-3">
      <div v-if="itemsHasLabel">
        <h4 class="font-weight-light" v-for="item in itemsHasLabel" :key="item.id" v-bind:title="item.title">
          {{ item.value }}
        </h4>
      </div>
  
      <div v-if="itemsNoLabel">
        <h4 class="font-weight-light" v-for="item in itemsNoLabel" :key="item.id">
          {{ item.value }}
        </h4>
      </div>
      <div>{{ title }}</div>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from 'vue';

// props
const props = defineProps<{
  icon?: string,
  color: string,
  title: string,
  items: any[],
}>();

// data
const itemsHasLabel = ref<any>([]);
const itemsNoLabel = ref<any>([]);

// hooks
onBeforeMount(() => {
  for (let index = 0; index < props.items.length; index++) {
    if (props.items[index].label !== undefined) {
      let itemHasLabel: any = {};
      itemHasLabel.title = props.items[index].label;
      itemHasLabel.value = props.items[index].value;
      itemsHasLabel.value.push(itemHasLabel);
    } else {
      itemsNoLabel.value.push(props.items[index]);
    }
  }
});
</script>