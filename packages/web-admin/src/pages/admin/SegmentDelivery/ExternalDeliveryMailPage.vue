<template>
  <div>
    <div>
      <div
        class="tw-w-full tw-pl-6 tw-pr-5 tw-py-4 tw-flex tw-flex-row tw-items-center tw-justify-between"
      >
        <div class="text-h4 flex flex-inline items-baseline">
          {{ mailSearchResultCount }}<small class="tw-text-sm">件</small>
        </div>
        <div>
          <div class="tw-flex tw-flex-inline tw-items-center tw-space-x-2">
            <div>
              <q-btn
                @click="search"
                size="md"
                class="tw-ml-2"
                color="primary"
                label="データ更新"
                icon="refresh"
                push
                :loading="loadings['fetchDeliveryExternalConfigs']"
              />
            </div>
            <q-separator vertical inset />
            <q-btn
              push
              icon="filter_list"
              label="条件検索"
              @click="showDetailSearch = !showDetailSearch"
            />
          </div>
        </div>
      </div>
      <q-separator />
      <template v-if="showDetailSearch">
        <q-card-section class="tw-px-10 tw-space-y-6 tw-bg-gray-50">
          <div class="tw-flex tw-flex-inline tw-items-center tw-space-x-4">
            <div class="tw-w-24 tw-mr-4 text-right">有効/無効</div>
            <div>
              <div class="q-gutter-sm">
                <q-radio
                  v-model="
                    externalDeliveryConfig.mailDelivery.filterCondition.enabled
                  "
                  :val="true"
                  label="有効"
                />
                <q-radio
                  v-model="
                    externalDeliveryConfig.mailDelivery.filterCondition.enabled
                  "
                  :val="false"
                  label="無効"
                />
              </div>
            </div>
          </div>
          <div class="tw-flex tw-flex-inline tw-items-center tw-space-x-4">
            <div class="tw-w-24 tw-mr-4 text-right">帳票</div>
            <div class="">
              <q-select
                v-model="
                  externalDeliveryConfig.mailDelivery.filterCondition.surveyId
                "
                lazy-rules
                dense
                outlined
                use-input
                placeholder="帳票"
                :options="surveyConfigsListTypeSorted"
                option-value="surveyId"
                option-label="surveyTitle"
                emit-value
                map-options
                :loading="loadings['fetchSurveyConfigsList']"
                :disabled="loadings['fetchSurveyConfigsList']"
                input-debounce="0"
                @filter="filterSurvey"
              />
            </div>
          </div>
          <div class="tw-flex tw-flex-inline tw-items-center tw-space-x-4">
            <div class="tw-w-24 tw-mr-4 text-right">配信名</div>
            <div class="tw-flex-1">
              <q-input
                dense
                hide-bottom-space
                filled
                v-model="
                  externalDeliveryConfig.mailDelivery.filterCondition
                    .distributionName
                "
              />
            </div>
          </div>

          <q-card-actions
            class="tw-w-full tw-justify-center tw-py-4 tw-flex tw-space-x-4"
          >
            <q-btn
              @click="segmentsStore.filterMailDeliveries()"
              size="md"
              class="tw-w-44"
              color="primary"
              label="検索"
              icon="filter_alt"
              rounded
              push
            />
            <q-btn
              @click="segmentsStore.filterMailDeliveries(true)"
              size="md"
              class="tw-w-44"
              label="リセット"
              icon="restart_alt"
              rounded
              push
            />
          </q-card-actions>
        </q-card-section>

        <q-separator />
      </template>
      <q-table
        :rows="externalDeliveryConfig.mailDelivery.filteredMailData"
        :columns="externalDeliveryConfig.mailDelivery.tableColumns"
        row-key="name"
        no-data-label="データがありません"
        class="tw-shadow-none"
        :loading="loadings['fetchDeliveryExternalConfigs']"
        :pagination="{ rowsPerPage: 10, sortBy: 'updatedAt', descending: true}"
        binary-state-sort
        top
      >
      <template v-slot:body-cell-distributionName="props">
          <q-td :props="props">
            <div :style="{ cursor: 'pointer' }" @click="onDistributionNameClick(props.row)">
              {{ props.row.distributionName }}
            </div>
          </q-td>
        </template>
        <template v-slot:body-cell-enabled="props">
          <q-td :props="props">
            <div :style="{ color: props.row.enabled ? 'green' : 'red' }">
              {{ props.row.enabled ? '有効' : '無効' }}
            </div>
          </q-td>
        </template>
        <template v-slot:body-cell-distributionCondition="props">
          <q-td :props="props">
            <router-link :to="`/segment-delivery/condition/${props.row.id}?type=mail`">
              <q-btn class="bg-primary text-white">設定</q-btn>
            </router-link>
          </q-td>
        </template>
        <template v-slot:body-cell-updatedAt="props">
          <q-td key="updatedAt" :props="props">
              {{ formatDateTime(props.row.updatedAt) }}
          </q-td>
        </template>
      </q-table>
    </div>
    <SegmentDeliveryTalkCreation
      :visible="showTalkModal"
      @close="showTalkModal = false"
    />
    <SegmentDeliveryMailCreation
      :visible="showMailModal"
      :item="editMailConfig"
      @close="closeMailCreation()"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useSegmentsStore } from '../../../stores/modules/segments'
import { useFormsStore } from '../../../stores/modules/forms'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import SegmentDeliveryTalkCreation from '../../../components/SegmentDelivery/SegmentDeliveryTalkCreation.vue'
import SegmentDeliveryMailCreation from '../../../components/SegmentDelivery/SegmentDeliveryMailCreation.vue'
import { MailConfig } from '../../../types/index'
import { formatDateTime } from '../../../utils/dateTimes'
const router = useRouter()

const segmentsStore = useSegmentsStore()
const formsStore = useFormsStore()

const { loadings, externalDeliveryConfig, mailSearchResultCount } =
  storeToRefs(segmentsStore)

const { surveyConfigsListTypeSorted } = storeToRefs(formsStore)

onMounted(async () => {
  segmentsStore.fetchDeliveryExternalConfigs()
  formsStore.fetchSurveyConfigsList()
})
const showDetailSearch = ref(false)

const editMailConfig = ref<MailConfig | null>(null)
const closeMailCreation = () => {
  showMailModal.value = false
  editMailConfig.value = null
}
const onDistributionNameClick = (row: MailConfig) => {
  showMailModal.value = true
  editMailConfig.value = row
}

const search = async () => {
  await segmentsStore.fetchDeliveryExternalConfigs()
  segmentsStore.filterDeliveryHistories()
}

const filterSurvey = (val, update) => {
  update(() => {
    if (val === '') {
      return surveyConfigsListTypeSorted
    }
    const needle = val.toLowerCase()
    return surveyConfigsListTypeSorted.value.filter(
      (v) => v.surveyTitle.toLowerCase().indexOf(needle) > -1
    )
  })
}

const showTalkModal = ref(false)
const onCreateTalk = () => {
  showTalkModal.value = true
}

const showMailModal = ref(false)
const onCreateMail = () => {
  showMailModal.value = true
}
</script>
