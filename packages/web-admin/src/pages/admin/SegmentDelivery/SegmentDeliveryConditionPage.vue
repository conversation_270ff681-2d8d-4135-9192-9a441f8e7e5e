<template>
  <div class="tw-m-10">
    <div class="tw-flex tw-justify-between tw-mb-2">
      <router-link :to="`/segment-delivery/external-configs${ isMailConfig ? '' : '/talk'}`">
        <q-btn class="bg-primary text-white">
          <q-icon name="arrow_back" color="white" size="1.5em" />
          配信リストに戻る
        </q-btn>
      </router-link>
      <div>
        <q-btn @click="setSelectionMode(true)" :class="`${isSelectionMode ? 'tw-font-bold' : ''}`"> 抽出条件 </q-btn>
        <q-btn v-if="isMailConfig" @click="setSelectionMode(false)" :class="`tw-ml-3 ${isSelectionMode ? '' : 'tw-font-bold'}`" > 変更条件 </q-btn>
      </div>
    </div>
    <div v-show="isSelectionMode" class="tw-p-8 tw-my-8 tw-bg-white tw-border tw-rounded">
      <div class="tw-flex tw-justify-arround">
        <div class="tw-flex tw-flex-col tw-flex-1 tw-mx-2">
          サブジェクト抽出条件
          <q-input
            class="tw-mb-1"
            outlined
            v-model="
              editConfig.settings.mailTriggerSettings!.condition
                .subjectExtractionCondition
            "
            type="text"
            placeholder="正規表現"
          />
          <div class="tw-flex tw-mb-3">
            <q-btn @click="checkSubject" class="tw-flex-1 bg-primary text-white"
              >チェック</q-btn
            >
            <q-icon
              v-if="subjectMatched"
              class="tw-mx-1 self-center"
              name="check_circle"
              color="green"
              size="1.5em"
            />
            <q-icon
              v-else
              class="tw-mx-1 self-center"
              name="cancel"
              color="red"
              size="1.5em"
            />
          </div>
          本文抽出条件
          <q-input
            class="tw-mb-1"
            outlined
            v-model="
              editConfig.settings.mailTriggerSettings!.condition
                .bodyExtractionCondition
            "
            type="text"
            placeholder="正規表現"
          />
          <div class="flex">
            <q-btn @click="checkBody" class="tw-flex-1 bg-primary text-white"
              >チェック</q-btn
            >
            <q-icon
              class="tw-mx-1 self-center"
              v-if="bodyMatched"
              name="check_circle"
              color="green"
              size="1.5em"
            />
            <q-icon
              class="tw-mx-1 self-center"
              v-else
              name="cancel"
              color="red"
              size="1.5em"
            />
          </div>
        </div>
        <div class="tw-flex tw-flex-col tw-flex-1 tw-mx-2">
          <div class="tw-mb-5">
            テスト
            <q-input
              outlined
              v-model="editConfig.settings.mailTriggerSettings!.condition.subjectTest"
              type="text"
              placeholder="サブジェクト"
            />
          </div>
          テスト
          <q-input
            type="textarea"
            filled
            v-model="editConfig.settings.mailTriggerSettings!.condition.bodyTest"
            placeholder="本文"
          />
        </div>
      </div>
    </div>
    <div v-show="!isSelectionMode" class="tw-p-8 tw-my-8 tw-bg-white tw-border tw-rounded">
      <div class="tw-flex tw-flex-col">
        本文変更条件
        <q-input
            v-model="editConfig.settings.mailTriggerSettings!.content.bodyChangeCondition"
            type="textarea"
            filled
            placeholder="正規表現"
          />
          <q-btn @click="onChangeContent" :loading="segmentsStore.loadings.changeBodyContent" class="tw-my-5 tw-ml-auto">変更</q-btn>
      </div>
      <div class="tw-flex">
        <div class="tw-flex-1 tw-mr-2">
          テスト
          <q-input
            v-model="editConfig.settings.mailTriggerSettings!.content.bodyTest"
            type="textarea"
            filled
            placeholder="原文の本文"
          />
        </div>
        <div class="tw-flex-1 tw-ml-2">
          結果
          <q-input
            v-model="bodyChangeResult"
            type="textarea"
            filled
            placeholder="変更後の本文"
          />
        </div>
      </div>
    </div>
    <div class="tw-flex tw-justify-end tw-m-1">
      <q-btn 
        class="tw-m-1" 
        @click="segmentsStore.clearEditConfig(isSelectionMode)"
        :style="hasActionPermission('hideButton', 'SegmentDelivery_ConditionDetail_Clear') ? hideButtonPermissionStyle() : null"
      >
        クリア
      </q-btn>
      <q-btn 
        @click="hasActionPermission('click', 'backendRequest') ? onSave() : null" 
        class="tw-m-1 bg-primary text-white"
        :style="hasActionPermission('hideButton', 'SegmentDelivery_ConditionDetail_Save') ? hideButtonPermissionStyle() : null"
      >
        保存
      </q-btn>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useSegmentsStore } from '@/stores/modules/segments';
import { storeToRefs } from 'pinia';
import { useRoute } from 'vue-router';
import {
  CheckBodyCondition,
  CheckSubjectCondition,
} from '@/services/reminder.service';
import { useQuasar } from 'quasar';
import { usePermissionHelper } from '@/mixins/PermissionHelper';
const $q = useQuasar();
const segmentsStore = useSegmentsStore();
const { editConfig } = storeToRefs(segmentsStore);
const route = useRoute();

// other
const { hasActionPermission, hideButtonPermissionStyle } = usePermissionHelper();

const id = route.params.id;

const bodyChangeResult = ref('');
const bodyMatched = ref(false);
const subjectMatched = ref(false);
const isMailConfig = ref(false);

const isSelectionMode = ref(true);
const setSelectionMode = (b: boolean) => { isSelectionMode.value = b; };

onMounted(async () => {
  if (typeof id === 'string') await segmentsStore.fetchConfigByID(id);
  bodyMatched.value =
    editConfig.value.settings.mailTriggerSettings.condition
      .bodyExtractionCondition ===
    editConfig.value.settings.mailTriggerSettings.condition.bodyTest;
  subjectMatched.value =
    editConfig.value.settings.mailTriggerSettings.condition
      .subjectExtractionCondition ===
    editConfig.value.settings.mailTriggerSettings.condition.subjectTest;
  if (!editConfig.value.settings.talkSettings) {
    isMailConfig.value = true;
  }
});

const checkSubject = async () => {
  try {
    subjectMatched.value = await CheckSubjectCondition({
      subjectExtractionCondition:
        editConfig.value.settings.mailTriggerSettings.condition
          .subjectExtractionCondition,
      subjectTest:
        editConfig.value.settings.mailTriggerSettings.condition.subjectTest,
    });
  } catch (e) {
    console.error(e);
  }
};
const checkBody = async () => {
  try {
    bodyMatched.value = await CheckBodyCondition({
      bodyExtractionCondition:
        editConfig.value.settings.mailTriggerSettings.condition
          .bodyExtractionCondition,
      bodyTest: editConfig.value.settings.mailTriggerSettings.condition.bodyTest,
    });
  } catch (e) {
    console.error(e);
  }
};

const onChangeContent = async () => {
  const result = await segmentsStore.getBodyChangeResult({
    bodyChangeCondition: editConfig.value.settings.mailTriggerSettings.content.bodyChangeCondition,
    bodyTest: editConfig.value.settings.mailTriggerSettings.content.bodyTest
  });
  bodyChangeResult.value = result;
};

const onSave = async () => {
  const resp = await segmentsStore.saveConfigContent(editConfig.value);
  if (resp) {
    $q.notify({
      message: '保存は成功しました。',
    });
  }
  // if (isSelectionMode.value) {
  //   // const resp = await segmentsStore.updateMailCondition({
  //   //   distributionConfigId: id,
  //   //   subjectExtractionCondition:
  //   //   editConfig.value.settings.mailTriggerSettings.condition
  //   //     .subjectExtractionCondition,
  //   //   subjectTest: editConfig.value.settings.mailTriggerSettings.condition.subjectTest,
  //   //   bodyExtractionCondition:
  //   //   editConfig.value.settings.mailTriggerSettings.condition
  //   //     .bodyExtractionCondition,
  //   //   bodyTest: editConfig.value.settings.mailTriggerSettings.condition.bodyTest,
  //   // })
  // }
};
</script>
