<template>
  <q-form @submit="hasActionPermission('click', 'backendRequest') ? onSubmit() : null">
    <div class="q-pa-md q-gutter-y-sm">
      <div class="tw-relative tw-flex tw-flex-row tw-gap-6 tw-h-full">
        <div class="tw-flex-1 tw-relative tw-pb-96">
          <q-card class="my-card" flat bordered>
            <q-toolbar class="text-primary">
              <q-btn
                @click="backToDeliveryList()"
                flat
                dense
                rounded
                icon="arrow_back"
                label="配信リストに戻る"
              />
              <q-toolbar-title> </q-toolbar-title>
            </q-toolbar>
            <q-separator />
            <q-card-section class="scroll tw-flex tw-flex-col tw-space-y-3">
              <div>
                <div>配信名</div>
                <q-input
                  v-model="createDelivery.settings.distributionName"
                  lazy-rules
                  :rules="hasCreateType === createType.sendTestDelivery ? undefined : rules.deliveryTitleRule"
                  dense
                  outlined
                  placeholder="配信名"
                  @click="hasCreateType = ''"
                />
              </div>
              <q-separator />
              <SegmentDeliveryFormDestination />
              <q-separator />
              <SegmentDeliveryFormSchedule :instant="isInstantDelivery" />
              <q-separator />
              <SegmentDeliveryFormMessage />
            </q-card-section>
          </q-card>
          <div
            class="tw-sticky tw-bottom-0 tw-w-full tw-pt-5 tw-text-center tw-items-center tw-flex"
          >
            <div
              v-if="isInstantDelivery && !(testDeliveryStyle && deliveryStyle)"
              class="tw-flex tw-flex-row tw-gap-3 tw-justify-center tw-bg-gray-300/50 tw-w-fit tw-py-4 tw-px-6 tw-mx-auto tw-rounded-xl"
            >
              <q-btn
                color="white"
                text-color="black"
                align="around"
                rounded
                push
                icon="science"
                type="submit"
                label="テスト配信"
                @click="hasCreateType = createType.sendTestDelivery"
                :loading="loadings.sendTestDelivery"
                :style="testDeliveryStyle"
              />

              <q-btn
                align="around"
                rounded
                color="primary"
                push
                icon-right="send"
                type="submit"
                label="配信"
                class="tw-w-36"
                @click="hasCreateType = createType.sendDelivery"
                :loading="loadings.createNewDelivery"
                :style="deliveryStyle"
              />
            </div>
            <div
              v-else-if="!(testDeliveryStyle && deliveryStyle && testDeliveryStyle)"
              class="tw-flex tw-flex-row tw-gap-3 tw-justify-center tw-bg-gray-300/50 tw-w-fit tw-py-4 tw-px-6 tw-mx-auto tw-rounded-xl"
            >
              <q-btn
                color="white"
                text-color="black"
                align="around"
                rounded
                push
                icon="science"
                type="submit"
                label="テスト配信"
                @click="hasCreateType = createType.sendTestDelivery"
                :loading="loadings.sendTestDelivery"
                :style="testDeliveryStyle"
              />
              <q-btn
                color="white"
                text-color="black"
                align="around"
                rounded
                push
                icon="edit_note"
                type="submit"
                label="下書き保存"
                @click="hasCreateType = createType.draftDelivery"
                :loading="loadings.createNewDelivery"
                :style="draftStyle"
              />

              <q-btn
                align="around"
                rounded
                color="primary"
                push
                icon-right="save"
                type="submit"
                label="作成"
                class="tw-w-36"
                @click="sendDelivery"
                :loading="loadings.createNewDelivery"
                :style="deliveryStyle"
              />
            </div>
          </div>
        </div>
        <div class="tw-flex-shrink tw-relative">
          <div class="tw-sticky tw-top-4 tw-h-screen">
            <SegmentDeliveryFormMessagesReview />
          </div>
        </div>
      </div>
    </div>
  </q-form>

</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useSegmentsStore } from '@/stores/modules/segments';
import { storeToRefs } from 'pinia';
import SegmentDeliveryFormDestination from '@/components/SegmentDeliveryForm/SegmentDeliveryFormDestination.vue';
import SegmentDeliveryFormSchedule from '@/components/SegmentDeliveryForm/SegmentDeliveryFormSchedule.vue';
import SegmentDeliveryFormMessage from '@/components/SegmentDeliveryForm/SegmentDeliveryFormMessage.vue';
import SegmentDeliveryFormMessagesReview from '@/components/SegmentDeliveryForm/SegmentDeliveryFormMessagesReview.vue';
import { useQuasar } from 'quasar';
import { usePermissionHelper } from '@/mixins/PermissionHelper';

const $q = useQuasar();

const segmentssStore = useSegmentsStore();
const { createDelivery, loadings, } = storeToRefs(segmentssStore);
const route = useRoute();

// other
const { hasActionPermission, hideButtonPermissionStyle } = usePermissionHelper();

const id = route.params.id;
const editMode = route.params.editMode;
const isCreateFromCopy = ref<boolean>(false);
const hasCreateType = ref<string>("");

const createType = ref({
  sendDelivery : "sendDelivery",
  draftDelivery : "draftDelivery",
  sendTestDelivery : "sendTestDelivery"
});

const rules = ref({
  deliveryTitleRule: [(v: any) => !!v || '配信名は必須入力です。'],
});

const isInstantDelivery = computed(() => {
  return route.query.type === 'instant';
});

const testDeliveryStyle = computed(() => { 
  return hasActionPermission('hideButton', 'SegmentDelivery_DistributionCreation_TestBroadcast') ? hideButtonPermissionStyle() : null;
});

const deliveryStyle = computed(() => {
  return hasActionPermission('hideButton', 'SegmentDelivery_DistributionCreation_SendDistribution') ? hideButtonPermissionStyle() : null;
});

const draftStyle = computed(() => {
  return hasActionPermission('hideButton', 'SegmentDelivery_DistributionCreation_SaveDraft') ? hideButtonPermissionStyle() : null;
});

onMounted(async () => {
  if (id !== undefined) {
    isCreateFromCopy.value = true;
    await segmentssStore.fetchCopyCreateConfig(id.toString());
  }
});

const router = useRouter();

const createNewDelivery = async (isDraft = false) => {
  if (createDelivery.value.messages?.length === 0){
    $q.notify({
      message: 'メッセージを追加してください。',
      color: 'red',
    });
    return;
  }
  if (createDelivery.value.settings.distributionName === ""){
    $q.notify({
      message: '配信名を入力してください。',
      color: 'red',
    });
    return;
  }
  if(createDelivery.value.settings.surveyConditions.surveyId == "" && !createDelivery.value.settings.surveyConditions.pickAll){
    $q.notify({
      message: '帳票を選択してください。',
      color: 'red',
    });
    return;
  }
  const resp = await segmentssStore.createNewDelivery(!!editMode, isDraft);
  // If we get a list of ids that means that the delivery was created successfully, if not, we see if there's an status)
  // メッセージIDのリストが返ってきたら、配信が正常に作成されたことを意味します。そうでない場合は、ステータスを確認します。
  if (resp.messageIds?.length > 0) {
    $q.notify({
      message: '新配信を作りました。',
      color: 'green',
      icon: 'done',
    });
    backToDeliveryList();
  } else if (resp?.status === 429) {
    $q.notify({
      message: 'LINE公式アカウントの通数制限に達したため配信エラーになりました。',
      color: 'red',
      icon: 'error',
    });
  } else {
    $q.notify({
      message: `エラーが発生しました。${resp.data?.message ? resp.data?.message : ''}`,
      color: 'red',
      icon: 'error',
    });
  }
};

const sendTestDelivery = async () => {
  if (createDelivery.value.messages?.length === 0){
    $q.notify({
      message: 'メッセージを追加してください。',
      color: 'red',
    });
    return;
  }

  const resp = await segmentssStore.sendTestDelivery();
  if (resp === null) {
    $q.notify({
      message: 'エラーが発生しました。',
      color: 'red',
    });
  } else if (resp.status === 429) {
    $q.notify({
      message: 'LINE公式アカウントの通数制限に達したため配信エラーになりました。',
      color: 'red',
    });
  } else if (resp.result === "ERROR"){
    $q.notify({
      message: 'エラーが発生しました。',
      color: 'red',
    });
  } else {
    $q.notify({
      message: 'テスト配信を送りました。',
    });
  }
};

const sendDelivery = () => {
  const fromDate = segmentssStore.createDelivery.settings.repeatingSettings.fromDate;
  const toDate = segmentssStore.createDelivery.settings.repeatingSettings.toDate;
  if (fromDate && toDate && fromDate > toDate) {
    $q.notify({
      message: '終了日は開始日より後に設定してください。',
      color: 'red-14',
      icon: 'error',
    });
    return;
  }
  hasCreateType.value = createType.value.sendDelivery
};

const backToDeliveryList = () => {
  segmentssStore.resetCreateDelivery();
  if (isInstantDelivery.value) {
    router.push({ path: '/segment-delivery' });
  } else {
    router.push({ path: '/segment-delivery/manual-delivery' });
  }
};

const onSubmit = () => {
  switch(hasCreateType.value){
    case createType.value.sendDelivery:
      createNewDelivery();
      break;
    case createType.value.draftDelivery:
      createNewDelivery(true);
      break;
    case createType.value.sendTestDelivery:
      sendTestDelivery();
      break;
    default:
  }
};

onUnmounted(() => {
  segmentssStore.resetCreateDelivery();
});
</script>
