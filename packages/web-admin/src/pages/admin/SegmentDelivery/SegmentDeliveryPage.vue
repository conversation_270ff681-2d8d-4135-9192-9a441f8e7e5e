<template>
  <div>
    <q-card flat bordered class="tw-rounded-lg tw-mt-4">
      <q-toolbar class="tw-bg-gray-200 tw-items-end tw-pt-3">
        <q-tabs
          align="justify"
          inline-label
          no-caps
          class="second-menu-tabs tw-w-full tw-max-w-screen-lsc tw-mx-auto"
          active-class="tw-bg-gray-50 text-primary tw-border-t-4 tw-border-gray-700"
          switch-indicator
          indicator-color="transparent"
        >
          <q-route-tab
            name="/segment-delivery"
            icon="history"
            label="配信履歴"
            to="/segment-delivery/"
            class="tw-py-3 tw-px-10 tw-rounded-t-2xl"
          />
          <q-route-tab
            name="manual-delivery"
            icon="perm_data_setting"
            label="手動配信設定"
            to="/segment-delivery/manual-delivery"
            class="tw-py-3 tw-px-10 tw-rounded-t-2xl"
          />
          <q-route-tab
            name="external-configs"
            icon="settings_suggest"
            label="外部配信設定"
            to="/segment-delivery/external-configs"
            class="tw-py-3 tw-px-6 tw-rounded-t-2xl"
          />
        </q-tabs>
      </q-toolbar>
      <div class="tw-pt-4">
        <router-view />
      </div>
    </q-card>
    <SegmentDeliveryTalkCreation
      :visible="showTalkModal"
      @close="showTalkModal = false"
    />
    <SegmentDeliveryMailCreation
      :visible="showMailModal"
      @close="showMailModal = false"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useSegmentsStore } from '@/stores/modules/segments';
import { storeToRefs } from 'pinia';

// component
import SegmentDeliveryTalkCreation from '@/components/SegmentDelivery/SegmentDeliveryTalkCreation.vue';
import SegmentDeliveryMailCreation from '@/components/SegmentDelivery/SegmentDeliveryMailCreation.vue';

// store
const segmentsStore = useSegmentsStore();

// data
const showTalkModal = ref(false);
const showMailModal = ref(false);
const {
  searchCondition,
} = storeToRefs(segmentsStore);

// mount
onMounted(async () => {
  await segmentsStore.fetchDeliveryHistories(searchCondition.value);
  segmentsStore.filterDeliveryHistories();
});
</script>
