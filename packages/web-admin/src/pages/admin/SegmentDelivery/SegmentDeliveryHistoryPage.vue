<template>
  <div>
    <div>
      <q-card-section>
        <div
          class="tw-flex tw-flex-inline tw-items-center tw-justify-center text-h6 text-weight-bold text-center"
        >
          <q-icon name="history" class="tw-mr-1" size="1.5em" />配信履歴
        </div>
      </q-card-section>
      <q-separator />
      <q-toolbar class="">
        <div></div>
        <q-space />
        <q-separator vertical />
        <q-btn
          stretch
          flat
          icon="add_circle"
          label="新規配信作成"
          size="md"
          color="primary"
          @click="onCreate"
        />
      </q-toolbar>
      <q-separator />
      <div
        class="tw-w-full tw-pl-6 tw-pr-5 tw-py-4 tw-flex tw-flex-row tw-items-center tw-justify-between"
      >
        <div class="text-h4 flex flex-inline items-baseline">
          {{ filteredDeliveryHistoriesCount }}<small class="tw-text-sm">件</small>
        </div>
        <div>
          <div class="tw-flex tw-flex-inline tw-items-center tw-space-x-2">
            <div>
              <q-btn
                @click="search"
                size="md"
                class="tw-ml-2"
                color="primary"
                label="データ更新"
                icon="refresh"
                push
                :loading="loadings['fetchDeliveryHistories']"
                :disable="
                  !isFromDateSameOrBeforeToDate ||
                  !isToDateSameOrAfterFromDate ||
                  !isFromDateValid ||
                  !isToDateValid
                "
              />
            </div>
            <q-separator vertical inset />
            <q-btn
              push
              icon="filter_list"
              label="条件検索"
              @click="showDetailSearch = !showDetailSearch"
            />
          </div>
        </div>
      </div>
      <q-separator />
      <template v-if="showDetailSearch">
        <q-card-section class="tw-px-10 tw-space-y-6 tw-bg-gray-50">
          <div class="tw-flex tw-flex-inline tw-items-center tw-space-x-4">
            <div class="tw-w-24 tw-mr-4 text-right">期間</div>
            <div class="tw-flex tw-flex-row tw-gap-4 tw-items-center">
              <div class="">
                <q-input
                  dense
                  filled
                  v-model="searchCondition.fromDate"
                  hide-bottom-space
                  :error="!isFromDateSameOrBeforeToDate || !isFromDateValid"
                  :error-message="fromDateErrorMessage"
                >
                  <template v-slot:append>
                    <q-icon name="event" class="cursor-pointer">
                      <q-popup-proxy
                        cover
                        transition-show="scale"
                        transition-hide="scale"
                      >
                        <q-date
                          v-model="searchCondition.fromDate"
                          mask="YYYY-MM-DD"
                          :options="shouldBeforeToDate"
                        >
                          <div class="row items-center justify-end">
                            <q-btn v-close-popup label="Close" color="primary" flat />
                          </div>
                        </q-date>
                      </q-popup-proxy>
                    </q-icon>
                  </template>
                </q-input>
              </div>
              <q-icon name="east" size="1.5em" />
              <div class="">
                <q-input
                  dense
                  hide-bottom-space
                  filled
                  v-model="searchCondition.toDate"
                  :error="!isToDateSameOrAfterFromDate || !isToDateValid"
                  :error-message="toDateErrorMessage"
                >
                  <template v-slot:append>
                    <q-icon name="event" class="cursor-pointer">
                      <q-popup-proxy
                        cover
                        transition-show="scale"
                        transition-hide="scale"
                      >
                        <q-date
                          v-model="searchCondition.toDate"
                          mask="YYYY-MM-DD"
                          :options="shouldAfterFromDate"
                        >
                          <div class="row items-center justify-end">
                            <q-btn v-close-popup label="Close" color="primary" flat />
                          </div>
                        </q-date>
                      </q-popup-proxy>
                    </q-icon>
                  </template>
                </q-input>
              </div>
            </div>
          </div>
          <div class="tw-flex tw-flex-inline tw-items-center tw-space-x-4">
            <div class="tw-w-24 tw-mr-4 text-right">種別</div>
            <div>
              <q-option-group
                v-model="filterConditions.distributionType"
                :options="types"
                inline
                color="primary"
                type="checkbox"
              />
            </div>
          </div>
          <div class="tw-flex tw-flex-inline tw-items-center tw-space-x-4">
            <div class="tw-w-24 tw-mr-4 text-right">ステータス</div>
            <div class="q-gutter-xs">
              <q-chip
                v-for="item in statusList"
                :key="item.value"
                v-model:selected="filterConditions.state[item.value]"
                :color="filterConditions.state[item.value] ? getColor(item.value) : ''"
                :text-color="filterConditions.state[item.value] ? 'white' : 'black'"
                icon-selected="task_alt"
                icon="radio_button_unchecked"
              >
                {{ item.label }}
              </q-chip>
            </div>
          </div>
          <div class="tw-flex tw-flex-inline tw-items-center tw-space-x-4">
            <div class="tw-w-24 tw-mr-4 text-right">配信名</div>
            <div class="tw-flex-1">
              <q-input dense hide-bottom-space filled v-model="filterConditions.name" />
            </div>
          </div>

          <q-card-actions
            class="tw-w-full tw-justify-center tw-py-4 tw-flex tw-space-x-4"
          >
            <q-btn
              @click="search()"
              size="md"
              class="tw-w-44"
              color="primary"
              label="検索"
              icon="filter_alt"
              rounded
              push
              :loading="loadings['fetchDeliveryHistories']"
              :disable="
                !isFromDateSameOrBeforeToDate ||
                !isToDateSameOrAfterFromDate ||
                !isFromDateValid ||
                !isToDateValid
              "
            />
            <q-btn
              @click="segmentsStore.filterDeliveryHistories(true)"
              size="md"
              class="tw-w-44"
              label="リセット"
              icon="restart_alt"
              rounded
              push
            />
          </q-card-actions>
        </q-card-section>

        <q-separator />
      </template>
      <q-table
        :rows="filteredDeliveryHistories"
        :columns="tableColumns"
        row-key="name"
        no-data-label="データがありません"
        class="tw-shadow-none"
        :loading="loadings['fetchDeliveryHistories']"
        :pagination="{ rowsPerPage: 10, sortBy: 'createdAt', descending: true }"
        binary-state-sort
        top
      >
        <template v-slot:body="props">
          <q-tr :props="props" class="tw-cursor-pointer">
            <q-td key="distributionName" :props="props" class="tw-max-w-96">
              <div class="tw-flex tw-flex-col">
                <router-link :to="`/segment-delivery/${props.row.id}`">
                  <div class="tw-font-semibold tw-truncate">
                    {{ props.row.distributionName }}
                  </div>
                  <div class="tw-text-gray-400">{{ props.row.id }}</div>
                </router-link>
              </div>
              <q-tooltip :delay="1000" anchor="top middle" self="top middle">
                {{ props.row.distributionName }}
              </q-tooltip>
            </q-td>
            <q-td key="createdAt" :props="props">
              {{ formatDateTime(props.row.createdAt) }}
            </q-td>
            <q-td key="distributionType" :props="props">
              {{ getDistributionTypeLabel(props.row.distributionType) }}
            </q-td>
            <q-td key="state" :props="props">
              <q-badge
                :color="getColor(props.row.state)"
                class="tw-w-full tw-flex tw-items-center tw-justify-center tw-text-xs"
                rounded
              >
                {{ getLabel(props.row.state) }}
              </q-badge>
            </q-td>
            <q-td key="targetsCount" :props="props">
              {{ props.row.targetsCount }}/ {{ props.row.deliveriesCount }}
            </q-td>
          </q-tr>
        </template>
      </q-table>
    </div>
    <SegmentDeliveryTalkCreation :visible="showTalkModal" @close="showTalkModal = false" />
    <SegmentDeliveryMailCreation :visible="showMailModal" @close="showMailModal = false" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import { useSegmentsStore } from '@/stores/modules/segments';
import { PAGES } from '@/constants/pages';
import { formatDateTime } from '@/utils/dateTimes';
import { getDistributionTypeLabel } from '@/utils/labelsHelpers';

// component
import SegmentDeliveryTalkCreation from '@/components/SegmentDelivery/SegmentDeliveryTalkCreation.vue';
import SegmentDeliveryMailCreation from '@/components/SegmentDelivery/SegmentDeliveryMailCreation.vue';

dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);

// route
const router = useRouter();

// sotre
const segmentsStore = useSegmentsStore();

// data
const {
  loadings,
  tableColumns,
  filterConditions,
  filteredDeliveryHistories,
  searchCondition,
} = storeToRefs(segmentsStore);

const statusList = [
  { label: 'すべて', value: 'ALL', color: 'indigo' },
  { label: '処理中', value: 'IN_PROGRESS', color: 'orange' },
  { label: '完了', value: 'FINISHED', color: 'green' },
  { label: 'エラー', value: 'ERROR', color: 'red' },
  { label: '送信対象者無し', value: 'NOT_STARTED', color: 'grey' },
];

const types = [
  { label: '即時配信', value: 'instant' },
  { label: '外部配信', value: 'external' },
  { label: '繰り返し配信', value: 'repeating' },
  { label: '予約配信', value: 'postponed' },
];

const showDetailSearch = ref(true);
const showTalkModal = ref(false);
const showMailModal = ref(false);

// computed
const isFromDateValid = computed(() => {
  return dayjs(searchCondition.value.fromDate).isValid()
});

const isToDateValid = computed(() => {
  return dayjs(searchCondition.value.toDate).isValid()
});

const isFromDateSameOrBeforeToDate = computed(() => {
  return dayjs(searchCondition.value.fromDate).isSameOrBefore(
    dayjs(searchCondition.value.toDate)
  )
});

const isToDateSameOrAfterFromDate = computed(() => {
  return dayjs(searchCondition.value.toDate).isSameOrAfter(
    dayjs(searchCondition.value.fromDate)
  )
});

const fromDateErrorMessage = computed(() => {
  if (!isFromDateValid.value) {
    return '開始日が不正です'
  }
  if (!isFromDateSameOrBeforeToDate.value) {
    return '終了日より後の日付は選択不可'
  }
  return ''
});

const toDateErrorMessage = computed(() => {
  if (!isToDateValid.value) {
    return '終了日が不正です'
  }
  if (!isToDateSameOrAfterFromDate.value) {
    return '開始日より前の日付は選択不可'
  }
  return ''
});

const filteredDeliveryHistoriesCount = computed(() => segmentsStore.filteredDeliveryHistories?.length || 0);

// method
const getColor = (status: string) => {
  const item = statusList.find((item) => item.value === status)
  return item ? item.color : ''
}

const getLabel = (status: string) => {
  const item = statusList.find((item) => item.value === status)
  return item ? item.label : ''
}

const search = async () => {
  // console.log(searchCondition.value)
  await segmentsStore.fetchDeliveryHistories(searchCondition.value)
  segmentsStore.filterDeliveryHistories()
}

const onCreate = () => {
  segmentsStore.resetCreateDelivery()
  router.push({ name: PAGES.SEGMENT_DELIVERY_CREATION, query: { type: 'instant' } })
}

const shouldBeforeToDate = (date: string) => {
  return date <= searchCondition.value.toDate?.replace(/-/g, '/')
}

const shouldAfterFromDate = (date: string) => {
  return date >= searchCondition.value.fromDate?.replace(/-/g, '/')
}

// mount
onMounted(async () => {
  await segmentsStore.fetchDeliveryHistories(searchCondition.value)
  segmentsStore.filterDeliveryHistories()
});
</script>
