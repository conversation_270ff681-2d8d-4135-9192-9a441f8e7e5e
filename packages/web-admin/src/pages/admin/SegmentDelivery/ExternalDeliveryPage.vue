<template>
  <div>
    <q-card-section>
      <div
        class="tw-flex tw-flex-inline tw-items-center tw-justify-center text-h6 text-weight-bold text-center"
      >
        <q-icon
          name="settings_suggest"
          class="tw-mr-1"
          size="1.5em"
        />外部配信設定一覧
      </div>
    </q-card-section>
    <q-separator />
    <q-toolbar class="">
      <q-tabs
        align="left"
        inline-label
        no-caps
        class="second-menu-tabs tw-max-w-screen-lsc tw-mx-auto tw-text-black"
        active-class="bg-primary-6 text-primary"
      >
        <q-route-tab
          name="manual-delivery"
          icon="perm_data_setting"
          label="メール配信"
          class="tw-py-3 tw-px-4"
          to="/segment-delivery/external-configs"
        />
        <q-route-tab
          name="external-configs-talk"
          icon="settings_suggest"
          label="トーク配信"
          class="tw-py-3 tw-px-4"
          to="/segment-delivery/external-configs/talk"
        />
      </q-tabs>
      <q-space />
      <q-separator vertical />
      <q-btn
        @click="showMailModal = true"
        stretch
        flat
        icon="add_circle"
        label="メール配信作成"
        size="md"
        color="primary"
      />
      <q-separator vertical />
      <q-btn
        @click="showTalkModal = true"
        stretch
        flat
        icon="add_circle"
        label="トーク配信作成"
        size="md"
        color="primary"
      />
    </q-toolbar>
    <q-separator />
    <div class="tw-pt-4">
      <router-view />
    </div>
    <SegmentDeliveryTalkCreation
      :visible="showTalkModal"
      @close="showTalkModal = false"
    />
    <SegmentDeliveryMailCreation
      :visible="showMailModal"
      @close="showMailModal = false"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useSegmentsStore } from '../../../stores/modules/segments'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { formatDateTime } from '../../../utils/dateTimes'
import { getDistributionTypeLabel } from '../../../utils/labelsHelpers'
import SegmentDeliveryTalkCreation from '../../../components/SegmentDelivery/SegmentDeliveryTalkCreation.vue'
import SegmentDeliveryMailCreation from '../../../components/SegmentDelivery/SegmentDeliveryMailCreation.vue'
const router = useRouter()

const segmentsStore = useSegmentsStore()
const {
  loadings,
  tableColumns,
  externalDeliveryConfig,
  filteredDeliveryHistoriesCount,
  filterConditions,
  filteredDeliveryHistories,
  searchCondition,
  currentExternalDeliveryConfig,
  currentExternalDeliveryConfigData,
} = storeToRefs(segmentsStore)

watch(
  () => externalDeliveryConfig.value.mode,
  (value) => {
    router.push({ query: { mode: value } })
  },
  {
    immediate: true,
    deep: true,
  }
)

onMounted(async () => {
  const { query } = router.currentRoute.value
  if (query.mode) {
    externalDeliveryConfig.value.mode = query.mode as string
  }

  segmentsStore.fetchDeliveryExternalConfigs()
})
const showDetailSearch = ref(false)

const status = reactive({
  ALL: false,
  IN_PROGRESS: false,
  FINISHED: false,
  ERROR: false,
  NOT_STARTED: false,
})

const statusList = [
  { label: 'すべて', value: 'ALL', color: 'indigo' },
  { label: '処理中', value: 'IN_PROGRESS', color: 'orange' },
  { label: '完了', value: 'FINISHED', color: 'green' },
  { label: 'エラー', value: 'ERROR', color: 'red' },
  { label: '送信対象者無し', value: 'NOT_STARTED', color: 'grey' },
]

const getColor = (status: string) => {
  const item = statusList.find((item) => item.value === status)
  return item ? item.color : ''
}

const getLabel = (status: string) => {
  const item = statusList.find((item) => item.value === status)
  return item ? item.label : ''
}
const types = [
  { label: '即時配信', value: 'instant' },
  { label: '外部配信', value: 'external' },
  { label: '繰り返し配信', value: 'repeating' },
  { label: '予約配信', value: 'postponed' },
]

const search = async () => {
  await segmentsStore.fetchDeliveryExternalConfigs()
  segmentsStore.filterDeliveryHistories()
}

const showTalkModal = ref(false)
const onCreateTalk = () => {
  showTalkModal.value = true
}

const showMailModal = ref(false)
const onCreateMail = () => {
  showMailModal.value = true
}
</script>
