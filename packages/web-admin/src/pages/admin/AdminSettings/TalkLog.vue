<template>
  <div fluid>
    <q-form ref="form">
      <div class="row flex-center">
        <q-item-label class="text-grey">期間</q-item-label>
        <div class="col-10">
          <div>
            <div class="row flex-center">
              <div class="col col-sm-12 col-lg-9">
                <div class="row flex-center">
                  <q-input v-model="startDate" outlined dense filled hide-bottom-space>
                    <template v-slot:prepend>
                      <q-icon name="event" class="cursor-pointer">
                        <q-popup-proxy ref="qDateProxy" cover transition-show="scale" transition-hide="scale">
                          <q-date :ref="startDateRef" v-model="startDate" default-view="Months" years-in-month-view
                            minimal mask="YYYY-MM-DD" @input="showStartDatePicker = false">
                            <div>
                              <q-btn v-close-popup label="Close" color="primary" flat />
                            </div>
                          </q-date>
                        </q-popup-proxy>
                      </q-icon>
                    </template>
                  </q-input>
                  <q-banner class="mx-2">〜</q-banner>
                  <q-input v-model="endDate" outlined dense filled hide-bottom-space>
                    <template v-slot:prepend>
                      <q-icon name="event" class="cursor-pointer">
                        <q-popup-proxy ref="qDateProxy" cover transition-show="scale" transition-hide="scale">
                          <q-date :ref="endDateRef" v-model="endDate" default-view="Months" years-in-month-view minimal
                            mask="YYYY-MM-DD" @input="showEndDatePicker = false">
                            <div>
                              <q-btn v-close-popup label="Close" color="primary" flat />
                            </div>
                          </q-date>
                        </q-popup-proxy>
                      </q-icon>
                    </template>
                  </q-input>
                  <div class="col-3">
                    <q-btn class="q-ml-md" color="primary" min-width="150" :elevation="0" :style="`${hasActionPermission('hideButton', 'AdminSettings_Search_SystemLogs')
                        ? hideButtonPermissionStyle()
                        : ''}`
                      " :disabled="disableSearchButton" @click="
                        `${hasActionPermission('click', 'backendRequest')
                          ? search()
                          : showActionPermissionError()}`
                        " label="出力" />
                  </div>
                </div>

              </div>

            </div>
          </div>
        </div>
      </div>
      <div class="row q-gutter-md">
        <div class="col">
          ※ 最大31日分の範囲指定が可能です<br />
          ※ ダウンロードリンクは生成から15分で失効します。<br />
          ※ 50MB以上のファイルはダウンロードできません。ファイルサイズの上限を超過する場合は期間を絞り込んで再度出力してください<br />
        </div>
      </div>
      <q-hover>
        <div class="row q-col-gutter-md">
          <div class="col-12">
            <q-separator />
          </div>
          <div class="col-3">
            <q-btn class="mr-3" color="primary" min-width="150" :elevation="0" :style="`${hasActionPermission('hideButton', 'AdminSettings_Search_SystemLogs')
                ? hideButtonPermissionStyle()
                : ''}`
              " @click="
                `${hasActionPermission('click', 'backendRequest')
                  ? refresh()
                  : showActionPermissionError()}`
                " :icon="loadingFlg ? 'mdi-cached mdi-spin' : 'mdi-cached'" label="データ更新" />
          </div>
          <div class="col-12">
            <q-table 
             :rows-per-page-options="[20, 50, 100, 0]"
                 v-model:pagination="pagination"
            v-if="talkLogLists" class="system-log-table" :columns="tableHeaders" :rows="talkLogLists.item"
              :must-sort="true" sort-by="Date" :sort-desc="true" :loading="loadingFlg"
              :items-per-page="tableItemsPerPage" color="primary">
              <template v-slot:body="props">
                <q-tr :props="props">
                  <q-td key="period" :props="props">
                    {{ props.row.period || 'ーー' }}
                  </q-td>
                  <q-td key="user" :props="props">
                    {{ props.row.user || 'ーー' }}
                  </q-td>
                  <q-td key="status" :props="props">
                    <div v-if="props.row.status === 'SUCCEEDED' && props.row.isExpired">
                      ダウンロードリンク失効
                    </div>
                    <div v-else-if="props.row.status === 'SUCCEEDED'">
                      <a  class="text-primary cursor-pointer" @click="csvDownloadChk(props.row.url, props.row.rowNum)">ダウンロード</a>
                    </div>
                    <div v-else-if="props.row.status === 'CAPACITYEXCESS'">
                      エラー (ファイルサイズ50MB超過)
                    </div>
                    <div v-else>
                      <div v-if="props.row.status === 'QUEUED' || props.row.status === 'RUNNING'">
                        ファイル出力中
                      </div>
                      <div v-else>
                        取得エラー
                      </div>
                    </div>
                  </q-td>
                  <q-td key="Date" :props="props">
                    {{ props.row.Date || 'ーー' }}
                  </q-td>
                </q-tr>
              </template>
            </q-table>
          </div>
        </div>
      </q-hover>
    </q-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onBeforeMount } from 'vue';
import { useScenariosStore } from '@/stores/modules/scenarios';
import { useSystemLogsStore } from '@stores/modules/systemLogs';
import { useAuthStore } from '@stores/modules/auth.module';
import { useQuasar } from 'quasar';
import dayjs from 'dayjs';
import { usePermissionHelper } from '@/mixins/PermissionHelper';

const $q = useQuasar();
const authStore = useAuthStore();
const scenariosStore = useScenariosStore();
const systemLogsStore = useSystemLogsStore();
const { hasActionPermission, hideButtonPermissionStyle, showActionPermissionError } = usePermissionHelper();
const pagination = ref({
  page: 1,
  rowsPerPage: 0, // show all rows
  sortBy: 'Date',
  descending: true
});
// data
const loadingFlg = ref<boolean>(false);
const overlay = ref<boolean>(false);
const startDate = ref<any>(dayjs().subtract(30, "days").format("YYYY-MM-DD"));
const endDate = ref<any>(dayjs().format("YYYY-MM-DD"));
const showStartDatePicker = ref<boolean>(false);
const showEndDatePicker = ref<boolean>(false);
const tableHeaders = [
  { name: 'period', label: '取得期間', field: 'period', align: 'left' as const },
  { name: 'user', label: '実行者', field: 'user', align: 'left' as const },
  { name: 'status', label: 'ダウンロード', field: 'status', sortable: false, align: 'left' as const },
  { name: 'Date', label: '日時', field: 'Date', align: 'left' as const },
];
const tableItemsPerPage = ref<number>(-1);
const startDateRef = ref();
const endDateRef = ref();

// methods
const searchTalkLogLists = () => scenariosStore.fetchTalkLogLists();
const aggregateTalkLog = (val: { startDate: string, endDate: string, user: string }) => scenariosStore.aggregateTalkLog(val);
const saveSystemLog = (val: { url: string, filename: string, user: string, period: string }) => systemLogsStore.saveSystemLogDownloadTalklog(val);
const search = async (): Promise<void> => {
  $q.notify({ message: "トークログ出力を受け付けました。データ更新ボタン押下で随時最新の情報をご確認ください。" });
  aggregateTalkLog({
    startDate: startDate.value,
    endDate: endDate.value,
    user: userStore.value.username
  });
  const waitingFirst = () => new Promise(resolve => {
    setTimeout(() => {
      refresh();
    }, 250);
  });
  await waitingFirst();
};
const refresh = async (): Promise<void> => {
  loadingFlg.value = true;
  await searchTalkLogLists();
  loadingFlg.value = false;
};
const csvDownloadChk = (link: string, rowNum: number) => {
  const currentDate = dayjs();

  if (currentDate.isAfter(talkLogLists.value.item[rowNum].s3expiryDite)) {
    $q.notify({ message: "ダウンロード対象のファイルは、有効期限切れです。" });
  } else {
    window.open(link);
    var searchStr = 'scenario-log_'
    var url = link.replace(/\?.*$/, '');
    var fileName = url.substr(url.indexOf(searchStr));
    saveSystemLog({
      url: url,
      filename: fileName,
      user: talkLogLists.value.item[rowNum].user,
      period: talkLogLists.value.item[rowNum].period,
    });
  }
};

// computed
const agregateTalkLogError = computed(() => scenariosStore.agregateTalkLogError);
const isFetchingTalkLogListsError = computed(() => scenariosStore.isFetchingTalkLogListsError);
const userStore = computed(() => authStore.user);
const talkLogLists: any = computed(() => scenariosStore.isFetchingTalkLogLists);
const disableSearchButton = computed((): any => {
  const currentDate = dayjs().format('YYYY-MM-DD');
  if (startDate.value > endDate.value) {
    return true;
  } else if (endDate.value > currentDate) {
    return true;
  } else if (!startDate.value || !endDate.value) {
    return true;
  }
  const mStart = dayjs(startDate.value);
  const mEnd = dayjs(endDate.value);
  return mStart.diff(mEnd, 'days') < -30 || mStart.diff(mEnd, 'days') > 0;
});

// watch
watch(() => agregateTalkLogError.value, (val) => {
  if (val) {
    $q.notify({ type: "error", message: val });
  }
});
watch(() => isFetchingTalkLogListsError.value, (val) => {
  if (val) {
    $q.notify({ type: "error", message: val });
  }
});

// hooks

onBeforeMount(() => {
  if (hasActionPermission('click', 'backendRequest')) {
    refresh();
  }
});

</script>

<style>
.system-log-table tr:hover {
  background-color: inherit !important;
  cursor: inherit !important;
}
</style>