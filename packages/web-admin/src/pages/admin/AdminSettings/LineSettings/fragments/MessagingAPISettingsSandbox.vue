<template>
  <div>
    <q-card flat>
      <div>
        <div class="text-right">
          <q-btn
            class="q-mr-md q-mt-sm white--text"
            color="blue-grey"
            min-width="136"
            min-height="36"
            elevation="0"
            @click="handleFetch"
            :ripple="false"
            :icon="isLoading(isFetchingEditableSettings)"
            label="データ更新"
          />
        </div>
        <q-form ref="form">
          <div class="col" v-for="(item, index) in settings" :key="index + 1000">
            <div v-if="isWebHookSettings(item.key)">
              <div class="col-12">
                <q-item-label class="body-2 text-blue-grey-5 text-weight-bold q-my-sm">Webhook URL</q-item-label>
                <div class="row q-col-gutter-md">
                  <div class="col-10 ml-4 mr-2 webhook-url-text">
                    <q-input
                      ref="sandboxWebHookSettingId"
                      :model-value="getWebHookURL(item)"
                      background-color="#F5F7F8"
                      solo
                      single-line
                      outlined
                      flat
                      readonly
                      dense
                      hide-details
                    />
                  </div>
                  <div class="col-2">
                    <q-btn
                      color="primary"
                      width="103"
                      height="40"
                      elevation="0"
                      @click="handleCopyURL()"
                      icon="mdi-clipboard-text-multiple-outline"
                      label="コピー"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row" v-for="(item, index) in settingsItems" :key="index">
            <div class="full-width">
              <div class="col-12">
                <div class="row q-col-gutter-md">
                  <div class="col-12">
                    <q-item-label class="body-2 text-blue-grey-5 text-weight-bold q-my-sm">
                      {{ getSecretKey(item) }}
                    </q-item-label>
                  </div>
                </div>
                <div class="row q-col-gutter-md">
                  <div v-if="item.key == 'SB_CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH'" class="col mx-4">
                    <div class="chatbot-trash-separation-fuzzy-search" row dense>
                      <q-radio
                        :model-value="messagingAPISettings['SB_CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH']"
                        label="利用する"
                        val="1"
                        class="body-2 blue-grey--text text--darken-4 px-0"
                      />
                      <q-radio
                        :model-value="messagingAPISettings['SB_CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH']"
                        label="利用しない"
                        val="0"
                        class="body-2 blue-grey--text text--darken-4 px-0"
                      />
                    </div>
                  </div>
                  <div v-else-if="item.key == 'SB_CHATBOT_FORWARD'" class="col-12 mx-4">
                    <span class="caption text-blue-grey-5 text-weight-bold q-my-sm">
                      ゴミ分別トークとの併用は非推奨です。<br />
                      併用する場合は、ゴミ分別登録用のCSVから「TRASH_NOT_FOUND_DEFAULT_MESSAGE」の設定を削除し、ゴミ分別のあいまい検索を「利用しない」に設定してください。
                    </span>
                    <div
                      class="chatbot-forward"
                      row
                      dense
                    >
                      <q-radio
                        :model-value="messagingAPISettings['SB_CHATBOT_FORWARD']"
                        label="利用する"
                        val="1"
                        class="body-2 blue-grey--text text--darken-4 px-0"
                        @update:model-value="(val: string) => (messagingAPISettings['SB_CHATBOT_FORWARD'] = val)"
                      />
                      <q-radio
                        :model-value="messagingAPISettings['SB_CHATBOT_FORWARD']"
                        label="利用しない"
                        val="0"
                        class="body-2 blue-grey--text text--darken-4 px-0"
                        @update:model-value="(val: string) => (messagingAPISettings['SB_CHATBOT_FORWARD'] = val)"
                      />
                    </div>
                  </div>
                  <div v-else-if="item.key == 'SB_CHATBOT_FORWARD_URL'" class="col-12 mx-4">
                    <q-input
                      v-model="messagingAPISettings[item.key]"
                      type="text"
                      solo
                      single-line
                      outlined
                      dense
                      flat
                      hide-details="auto"
                      :rules="[
                        (val) =>
                        messagingAPISettings['SB_CHATBOT_FORWARD'] === '0'
                          ? true
                          : validate(messagingAPISettings[item.key]),
                      ]"
                      :disable="messagingAPISettings['SB_CHATBOT_FORWARD'] === '0'"
                      :counter="true"
                    />
                  </div>
                  <div v-else class="col-10 mx-4 full-width">
                    <q-input
                      :model-value="messagingAPISettings[item.key]"
                      :type="item.key === 'SB_LINEMESSAGING_CHANNEL_SECRET' ? 'password' : 'text'"
                      solo
                      single-line
                      outlined
                      dense
                      flat
                      hide-details="auto"
                    ></q-input>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="text-right">
            <q-btn
              color="primary"
              class="mr-4"
              min-width="63"
              min-height="44"
              elevation="0"
              @click="hasActionPermission('click', 'backendRequest') ? handleUpdate() : showActionPermissionError()"
              :style="
                hasActionPermission('hideButton', 'AdminSettings_AwsSettings_UpdateMessagingAPISettingsPro')
                  ? hideButtonPermissionStyle()
                  : ''
              "
              :loading="isUpdatingEditableSettings"
              :disable="!canSave"
            >
              保存
            </q-btn>
          </div>
        </q-form>
      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onBeforeMount } from 'vue';

import { useSettingsStore } from '@stores/modules/settings';
import { useQuasar } from 'quasar';
import { usePermissionHelper } from '@/mixins/PermissionHelper';
import { convertSecretKeyToJapanese, copyTextToClipboard, isValidUrlHost } from '@/utils/stringUtils';
import { cloneDeep } from 'lodash';

// old imports
// 旧インポート
/*import { convertSecretKeyToJapanese, copyTextToClipboard, isValidUrlHost } from "@/utils/stringUtils";
import MaskData from "maskdata";
import { FETCH_EDITABLE_SETTINGS, UPDATE_EDITABLE_SETTINGS } from "@/store/action-types";
import cloneDeep from "lodash/cloneDeep";*/

const $q = useQuasar();
const settingsStore = useSettingsStore();
const { hasActionPermission, showActionPermissionError, hideButtonPermissionStyle } = usePermissionHelper();

// data
const sandboxWebHookSettingId = ref<any>(null);

const settings = ref<any>([]);
const webHookSettings = ref<any>({
  VITE_AMPLIFY_SCENARIO_API_ENDPOINT_URL: "",
});
const messagingAPISettings = ref<any>({
  LINEMESSAGING_CHANNEL_ACCESS_TOKEN: "",
  LINEMESSAGING_CHANNEL_ID: "",
  LINEMESSAGING_CHANNEL_SECRET: "",
  SB_LINEMESSAGING_CHANNEL_ACCESS_TOKEN: "",
  SB_LINEMESSAGING_CHANNEL_ID: "",
  SB_LINEMESSAGING_CHANNEL_SECRET: "",
  CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH: "",
  SB_CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH: "",
  CHATBOT_FORWARD: "",
  SB_CHATBOT_FORWARD: "",
  CHATBOT_FORWARD_URL: "",
  SB_CHATBOT_FORWARD_URL: "",
});
const messagingAPISettingsSandbox = ref<any>({
  SB_LINEMESSAGING_CHANNEL_ACCESS_TOKEN: "",
  SB_LINEMESSAGING_CHANNEL_ID: "",
  SB_LINEMESSAGING_CHANNEL_SECRET: "",
  SB_CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH: "",
  SB_CHATBOT_FORWARD: "",
  SB_CHATBOT_FORWARD_URL: "",
});
const isMaskedChannelSecretSandbox = ref<boolean>(true);
const canSave = ref<boolean>(false);

// methods
const isLoading = (value: boolean) => {
  return value ? "mdi-cached mdi-spin" : "mdi-cached";
};
const fetchEditableSettings = () => settingsStore.fetchEditableSettings();
const updateEditableSettings = (value: any) => settingsStore.updateEditableSettings(value);
const isWebHookSettings = (key: any) => {
  return Object.keys(webHookSettings.value).includes(key);
};
const isMessagingAPISettings = (key: any) => {
  return Object.keys(messagingAPISettings.value).includes(key);
};
const isMessagingAPISettingsSandbox = (key: any) => {
  return Object.keys(messagingAPISettingsSandbox.value).includes(key);
};
const getSecretKey = (item: any) => {
  return convertSecretKeyToJapanese(item.key);
};
const getWebHookURL = (item: any) => {
  return item.value ? item.value + "/chatbot_sandbox/callback" : "ーー";
};
const handleFetch = async (): Promise<void> => {
  await fetchEditableSettings();
  if (fetchEditableSettingsError.value) {
    $q.notify({
      message: fetchEditableSettingsError.value,
    });
  }
};
const handleUpdate = (): void => {
  for (var index in settings.value) {
    if (isMessagingAPISettingsSandbox(settings.value[index].key)) {
      settings.value[index].value = messagingAPISettings.value[settings.value[index].key];
    }
  }
  handleUpdateAll(settings.value);
};
const handleUpdateAll = async (payload: any): Promise<void> => {
  await updateEditableSettings(payload);
  if (updateEditableSettingsError.value) {
    $q.notify({
      message: updateEditableSettingsError.value,
    });
  } else {
    handleFetch();
    $q.notify({
      message: "設定を更新しました。",
    });
  }
};
const handleCopyURL = () => {
  const webHookInput = sandboxWebHookSettingId.value;
  const webHookURL = webHookInput ? webHookInput[0].modelValue : null;
  if (webHookURL) {
    navigator.clipboard.writeText(webHookURL);
    $q.notify({
      message: "コピーしました。",
      color: "green-6",
      icon: "mdi-check",
    });
  } else {
    $q.notify({
      message: "URLが見つかりません。",
      color: "red-6",
      icon: "mdi-alert",
    });
  }
};
const initializeSettings = (): void => {
  for (var item of settings.value) {
    if (isMessagingAPISettings(item.key)) {
      messagingAPISettings.value[item.key] = item.value;
    }
  }
};
const validate = (target: string): boolean | string => {
  const result =
    [validateNotEmpty, validateURL, validateLength]
      .map((func) => func(target))
      .filter((result) => result !== true)
      .shift() || true;
  canSave.value = result === true;
  return result;
};
const validateNotEmpty = (target: string): boolean | string => {
  if (!target) {
    return "転送先URLを入力してください。";
  }
  return true;
};
const validateURL = (target: string): boolean | string => {
  if (isValidUrlHost(target)) {
    return true;
  }
  return "URLの形式が正しくありません。有効なHTTPS URLで500文字以下であることを確認してください。";
};
const validateLength = (target: string): boolean | string => {
  if (target.length > 500) {
    return "URLの形式が正しくありません。有効なHTTPS URLで500文字以下であることを確認してください。";
  }
  return true;
};

// computed
const editableSettings = computed(() => settingsStore.editableSettings);
const isFetchingEditableSettings = computed(() => settingsStore.isFetchingEditableSettings);
const fetchEditableSettingsError = computed(() => settingsStore.fetchEditableSettingsError);
const isUpdatingEditableSettings = computed(() => settingsStore.isUpdatingEditableSettings);
const updateEditableSettingsError = computed(() => settingsStore.updateEditableSettingsError);
const settingsItems = computed(() => {
  return settings.value
    .filter((item: { key: string }) => {
      return isMessagingAPISettingsSandbox(item.key);
    })
    .sort((a: { key: string }, b: { key: string }) => {
      if (a.key.startsWith("SB_LINEMESSAGING") && !b.key.startsWith("SB_LINEMESSAGING")) {
        return -1;
      }

      if (b.key.startsWith("SB_LINEMESSAGING") && !a.key.startsWith("SB_LINEMESSAGING")) {
        return 1;
      }

      return 0;
    });
});

// watch
watch(() => editableSettings.value, (value) => {
  if (value) {
    settings.value = cloneDeep(value);
    initializeSettings();
  }
});
watch(() => messagingAPISettings.value.SB_CHATBOT_FORWARD, (value) => {
  canSave.value = value === 0 ? true : (validate(messagingAPISettings.value.SB_CHATBOT_FORWARD_URL) as boolean);
});

// hooks

onBeforeMount(() => {
  handleFetch();
});

</script>
