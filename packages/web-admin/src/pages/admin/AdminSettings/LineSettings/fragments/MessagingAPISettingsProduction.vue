<template>
  <div class="q-gutter-md">
    <q-card class="q-pa-md" flat>
      <div>
        <div class="text-right">
          <q-btn
            class="q-mr-md q-mt-sm white--text"
            color="blue-grey"
            min-width="136"
            min-height="36"
            elevation="0"
            @click="handleFetch"
            :ripple="false"
            :icon="isLoading(isFetchingEditableSettings)"
            label="データ更新"
          />
        </div>
        <q-form ref="form">
          <div class="column" v-for="(item, index) in settings" :key="index + 1000">
            <div v-if="isWebHookSettings(item.key)">
              <div class="col-10">
                <q-item-label class="body-2 text-blue-grey-5 text-weight-bold q-my-sm">Webhook URL</q-item-label>
                <div class="row">
                  <div class="full-width q-ml-none q-mr-sm webhook-url-text">
                    <q-input
                      ref="productionWebHookSettingId"
                      v-model="webHookURLComp"
                      background-color="#F5F7F8"
                      solo
                      single-line
                      outlined
                      flat
                      readonly
                      dense
                      hide-details
                    />
                  </div>
                  <div class="col">
                    <q-btn
                      color="primary"
                      width="103"
                      height="40"
                      elevation="0"
                      class="q-mx-md"
                      @click="handleCopyURL()"
                      icon="mdi-clipboard-text-multiple-outline"
                      label="コピー"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row full-width" v-for="(item, index) in settingsItems" :key="index">
            <div class="col-12">
              <div class="col-12">
                <div class="row q-col-gutter-md">
                  <div class="col-12">
                    <q-item-label class="body-2 text-blue-grey-5 text-weight-bold q-my-sm">
                      {{ getSecretKey(item) }}
                    </q-item-label>
                  </div>
                </div>
                <div class="row q-col-gutter-md">
                  <div v-if="item.key == 'CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH'" class="col mx-4">
                    <div class="chatbot-trash-separation-fuzzy-search" row dense>
                      <q-radio
                        v-model="messagingAPISettings['CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH']"
                        label="利用する"
                        val="1"
                        class="body-2 blue-grey--text text--darken-4 px-0"
                        @update:model-value="(val: string) => (messagingAPISettings['CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH'] = val)"
                      />
                      <q-radio
                        v-model="messagingAPISettings['CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH']"
                        label="利用しない"
                        val="0"
                        class="body-2 blue-grey--text text--darken-4 px-0"
                        @update:model-value="(val: string) => (messagingAPISettings['CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH'] = val)"
                      />
                    </div>
                  </div>
                  <div v-else-if="item.key == 'CHATBOT_FORWARD'" class="col-10 mx-4">
                    <span class="caption text-blue-grey-5 text-weight-bold q-my-sm">
                      ゴミ分別トークとの併用は非推奨です。<br />
                      併用する場合は、ゴミ分別登録用のCSVから「TRASH_NOT_FOUND_DEFAULT_MESSAGE」の設定を削除し、ゴミ分別のあいまい検索を「利用しない」に設定してください。
                    </span>
                    <div class="chatbot-forward" row dense>
                      <q-radio
                        v-model="messagingAPISettings['CHATBOT_FORWARD']"
                        label="利用する"
                        val="1"
                        class="body-2 blue-grey--text text--darken-4 px-0"
                        @update:model-value="(val: string) => (messagingAPISettings['CHATBOT_FORWARD'] = val)"
                      />
                      <q-radio
                        v-model="messagingAPISettings['CHATBOT_FORWARD']"
                        label="利用しない"
                        val="0"
                        class="body-2 blue-grey--text text--darken-4 px-0"
                        @update:model-value="(val: string) => (messagingAPISettings['CHATBOT_FORWARD'] = val)"
                      />
                    </div>
                  </div>
                  <div v-else-if="item.key == 'CHATBOT_FORWARD_URL'" class="col-12 mx-4">
                    <q-input
                      v-model="messagingAPISettings[item.key]"
                      type="text"
                      solo
                      single-line
                      outlined
                      dense
                      flat
                      hide-details="auto"
                      :rules="[
                        (item) =>
                        messagingAPISettings['CHATBOT_FORWARD'] === '0'
                          ? true
                          : validate(messagingAPISettings[item.key]),
                      ]"
                      :disable="messagingAPISettings['CHATBOT_FORWARD'] === '0'"
                    ></q-input>
                  </div>
                  <div v-else class="col-12 mx-4">
                    <q-input
                      v-model="messagingAPISettings[item.key]"
                      :type="item.key === 'LINEMESSAGING_CHANNEL_SECRET' ? 'password' : 'text'"
                      solo
                      single-line
                      outlined
                      dense
                      flat
                      hide-details="auto"
                      @update:model-value="(val: string) => (messagingAPISettings[item.key] = val)"
                    ></q-input>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="text-right">
            <q-btn
              color="primary"
              class="mr-4"
              min-width="63"
              min-height="44"
              elevation="0"
              @click="hasActionPermission('click', 'backendRequest') ? handleUpdate() : showActionPermissionError()"
              :style="
                hasActionPermission('hideButton', 'AdminSettings_AwsSettings_UpdateMessagingAPISettingsPro')
                  ? hideButtonPermissionStyle()
                  : ''
              "
              :loading="isUpdatingEditableSettings"
              :disable="!canSave"
              label="保存"
            />
          </div>
        </q-form>
      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onBeforeMount } from 'vue';
import { useSettingsStore } from '@stores/modules/settings';
import { useQuasar } from 'quasar';
import { usePermissionHelper } from '@/mixins/PermissionHelper';
import { convertSecretKeyToJapanese, copyTextToClipboard, isValidUrlHost } from '@/utils/stringUtils';
import { cloneDeep } from 'lodash';

const settingsStore = useSettingsStore();
const { hasActionPermission, showActionPermissionError, hideButtonPermissionStyle } = usePermissionHelper();

const $q = useQuasar();

// data
const productionWebHookSettingId = ref();

const webhookURL = ref('');

const settings = ref<any>([]);
const webHookSettings = ref<any>({
  VITE_AMPLIFY_SCENARIO_API_ENDPOINT_URL: "",
});
const messagingAPISettings = ref<any>({
  LINEMESSAGING_CHANNEL_ACCESS_TOKEN: "",
  LINEMESSAGING_CHANNEL_ID: "",
  LINEMESSAGING_CHANNEL_SECRET: "",
  SB_LINEMESSAGING_CHANNEL_ACCESS_TOKEN: "",
  SB_LINEMESSAGING_CHANNEL_ID: "",
  SB_LINEMESSAGING_CHANNEL_SECRET: "",
  CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH: "",
  SB_CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH: "",
  CHATBOT_FORWARD: "",
  SB_CHATBOT_FORWARD: "",
  CHATBOT_FORWARD_URL: "",
  SB_CHATBOT_FORWARD_URL: "",
});
const messagingAPISettingsProduction = ref<any>({
  LINEMESSAGING_CHANNEL_ACCESS_TOKEN: "",
  LINEMESSAGING_CHANNEL_ID: "",
  LINEMESSAGING_CHANNEL_SECRET: "",
  CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH: "",
  CHATBOT_FORWARD: "",
  CHATBOT_FORWARD_URL: "",
});
const isMaskedChannelSecretProduction = ref<boolean>(true);
const canSave = ref<boolean>(false);

// methods
const isLoading = (value: boolean) => {
  return value ? "mdi-cached mdi-spin" : "mdi-cached";
};
const fetchEditableSettings = settingsStore.fetchEditableSettings;
const updateEditableSettings = settingsStore.updateEditableSettings;
const isWebHookSettings = (key: any) => {
  return Object.keys(webHookSettings.value).includes(key);
};
const isMessagingAPISettings = (key: any) => {
  return Object.keys(messagingAPISettings.value).includes(key);
};
const isMessagingAPISettingsProduction = (key: any) => {
  return Object.keys(messagingAPISettingsProduction.value).includes(key);
};
const getSecretKey = (item: any) => {
  return convertSecretKeyToJapanese(item.key);
};
const getWebHookURL = (item: any) => {
  return item.value ? item.value + "/chatbot/callback" : "ーー";
};
const handleFetch = async (): Promise<void> => {
  await fetchEditableSettings();
  if (fetchEditableSettingsError.value) {
    $q.notify({
      message: fetchEditableSettingsError.value,
    });
  }
};
const handleUpdate = async (): Promise<void> => {
  for (var index in settings.value) {
    if (isMessagingAPISettingsProduction(settings.value[index].key)) {
      settings.value[index].value = messagingAPISettings.value[settings.value[index].key];
    }
  }
  //console.log("settings.value", settings.value);  
  await handleUpdateAll(settings.value);
};
const handleUpdateAll = async (payload: any): Promise<void> => {
  await updateEditableSettings(payload);
  if (updateEditableSettingsError.value) {
    $q.notify({
      message: updateEditableSettingsError.value,
    });
  } else {
    await handleFetch();
    $q.notify({
      message: "設定を更新しました。",
    });
  }
};
const handleCopyURL = () => {
  const webHookInput = productionWebHookSettingId.value;
  const webHookURL = webHookInput ? webHookInput[0].modelValue : null;
  if (webHookURL) {
    navigator.clipboard.writeText(webHookURL);
    $q.notify({
      message: "コピーしました。",
      color: "green-6",
      icon: "mdi-check",
    });
  } else {
    $q.notify({
      message: "URLが見つかりません。",
      color: "red-6",
      icon: "mdi-alert",
    });
  }
};

const initializeSettings = (): void => {
  for (var item of settings.value) {
    if (isMessagingAPISettings(item.key)) {
      messagingAPISettings.value[item.key] = item.value;
    }
  }
};
const validate = (target: string): boolean | string => {
  const result =
    [validateNotEmpty, validateURL, validateLength]
      .map((func) => func(target))
      .filter((result) => result !== true)
      .shift() || true;
  canSave.value = result === true;
  return result;
};
const validateNotEmpty = (target: string): boolean | string => {
  if (!target) {
    return "転送先URLを入力してください。";
  }
  return true;
};
const validateURL = (target: string): boolean | string => {
  //console.log("target", target);
  if (isValidUrlHost(target)) {
    return true;
  }
  return "URLの形式が正しくありません。有効なHTTPS URLで500文字以下であることを確認してください。";
};
const validateLength = (target: string): boolean | string => {
  if (target.length > 500) {
    return "URLの形式が正しくありません。有効なHTTPS URLで500文字以下であることを確認してください。";
  }
  return true;
};

// computed
const editableSettings = computed(() => settingsStore.editableSettings);
const isFetchingEditableSettings = computed(() => settingsStore.isFetchingEditableSettings);
const fetchEditableSettingsError = computed(() => settingsStore.fetchEditableSettingsError);
const isUpdatingEditableSettings = computed(() => settingsStore.isUpdatingEditableSettings);
const updateEditableSettingsError = computed(() => settingsStore.updateEditableSettingsError);
const settingsItems = computed(() => {
  return settings.value
    .filter((item) => {
      return isMessagingAPISettingsProduction(item.key);
    })
    .sort((a, b) => {
      if (a.key.startsWith("LINEMESSAGING") && !b.key.startsWith("LINEMESSAGING")) {
        return -1;
      }

      if (b.key.startsWith("LINEMESSAGING") && !a.key.startsWith("LINEMESSAGING")) {
        return 1;
      }

      return 0;
    });
});

// watch
watch(() => editableSettings.value, (value) => {
  if (value) {
    settings.value = cloneDeep(value);
    initializeSettings();
  }
});
watch(() => messagingAPISettings.value.CHATBOT_FORWARD, (value) => {
  canSave.value = value === 0 ? true : (validate(messagingAPISettings.value.CHATBOT_FORWARD_URL) as boolean);
});


// comp

const webHookURLComp = computed({
  get() {
    return getWebHookURL(webhookURL.value);
  },
  set(value) {
    webhookURL.value = value;
  }
});


// hooks

onBeforeMount(() => {
  handleFetch();
});

</script>
