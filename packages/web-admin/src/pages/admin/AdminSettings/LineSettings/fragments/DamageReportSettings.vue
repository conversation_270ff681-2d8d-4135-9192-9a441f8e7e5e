<template>
  <div>
    <q-card flat>
      <div>
        <div class="text-right">
          <q-btn class="q-mr-md q-mt-md white--text" color="blue-grey" min-width="136" min-height="36" elevation="0"
            @click="handleFetch" :ripple="false" :icon="isLoading(isFetchingEditableSettings)" label="データ更新" />
        </div>

        <q-form ref="form" @no-error-focus="true">
          <div class="row" v-for="(item, index) in settings" :key="index">
            <div class="full-width q-pa-md q-px-md" v-if="isDamageReportSettings(item.key)">
              <div class="col-10">
                <q-item-label class="body-2 text-blue-grey-5 text-weight-bold q-my-sm">{{ getSecretKey(item) }}
                </q-item-label>
                <q-input v-model="damageReportSettings[item.key]" class="mx-4" solo single-line outlined dense flat
                  hide-details="auto" type="email" :ref="(el) => refs[item.key] = el"
                  :rules="[val => /.+@.+\..+/.test(val) || '有効なメールアドレスを入力してください']"
                  />
              </div>
            </div>
          </div>
          <div class="text-right">
            <q-btn color="primary" class="q-mt-md q-mr-md" min-width="63" min-height="44" elevation="0" label="保存"
              @click="hasActionPermission('click', 'backendRequest') ? handleUpdate() : showActionPermissionError()"
              :style="hasActionPermission('hideButton', 'AdminSettings_AwsSettings_UpdateMessagingAPISettingsPro')
                  ? hideButtonPermissionStyle()
                  : ''
                " 
                :loading="isUpdatingEditableSettings" 
                />
          </div>
        </q-form>
      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onBeforeMount } from 'vue';
import { useSettingsStore } from '@stores/modules/settings';
import { useQuasar } from 'quasar';
import { usePermissionHelper } from '@/mixins/PermissionHelper';
import { convertSecretKeyToJapanese } from '@/utils/stringUtils';
import { cloneDeep } from 'lodash';

const $q = useQuasar();
const settingsStore = useSettingsStore();
const { hasActionPermission, showActionPermissionError, hideButtonPermissionStyle } = usePermissionHelper();

// data
const settings = ref<any>([]);
const damageReportSettings = ref<any>({
  EMAIL_CHATBOT_DAMAGE_REPORT1: "",
  EMAIL_CHATBOT_DAMAGE_REPORT2: "",
  EMAIL_CHATBOT_DAMAGE_REPORT3: "",
  EMAIL_CHATBOT_DAMAGE_REPORT4: "",
  SES_EMAIL_DOMAIN: "",
});

const refs = ref<any>([]);
const isMaskedChannelSecretProduction = ref<boolean>(true);

// methods
const isLoading = (value: boolean) => {
  return value ? "mdi-cached mdi-spin" : "mdi-cached";
};
const fetchEditableSettings = () => settingsStore.fetchEditableSettings();
const updateEditableSettings = (val: any) => settingsStore.updateEditableSettings(val);
const isDamageReportSettings = (key: any) => {
  return Object.keys(damageReportSettings.value).includes(key);
};
const handleFetch = async (): Promise<void> => {
  await fetchEditableSettings();
  if (fetchEditableSettingsError.value) {
    $q.notify({
      message: fetchEditableSettingsError.value,
    });
  }
};

const validateAllEmailFields = (): string | boolean => {
  let valid = true;
  Object.keys(damageReportSettings.value).forEach(key => {
    if (!/.+@.+\..+/.test(damageReportSettings.value[key])) {
      refs.value[key]?.validate();
      valid = false;
    }
  });
  return valid;
};

const handleUpdate = (): void => {

  if(typeof validateAllEmailFields() === "string") {
    $q.notify({
      message: "有効なメールアドレスを入力してください。",
    });
    return;
  }

  for (var index in settings.value) {
    if (isDamageReportSettings(settings.value[index].key)) {
      settings.value[index].value = damageReportSettings.value[settings.value[index].key];
    }
  }
  handleUpdateAll(settings.value);
};
const handleUpdateAll = async (payload: any): Promise<void> => {
  await updateEditableSettings(payload);
  if (updateEditableSettingsError.value) {
    $q.notify({
      message: updateEditableSettingsError.value,
    });
  } else {
    handleFetch();
    $q.notify({
      message: "設定を更新しました。",
    });
  }
};
const getSecretKey = (item: any) => {
  return convertSecretKeyToJapanese(item.key);
};
const initializeSettings = (): void => {
  for (var item of settings.value) {
    if (isDamageReportSettings(item.key)) {
      damageReportSettings.value[item.key] = item.value;
    }
  }
};

// computed
const editableSettings = computed(() => settingsStore.editableSettings);
const isFetchingEditableSettings = computed(() => settingsStore.isFetchingEditableSettings);
const fetchEditableSettingsError = computed(() => settingsStore.fetchEditableSettingsError);
const isUpdatingEditableSettings = computed(() => settingsStore.isUpdatingEditableSettings);
const updateEditableSettingsError = computed(() => settingsStore.updateEditableSettingsError);

// watch
watch(() => editableSettings.value, (value) => {
  if (value) {
    settings.value = cloneDeep(value);
    initializeSettings();
  }
});

// hooks

onBeforeMount(async () => {
  await handleFetch();
});

</script>