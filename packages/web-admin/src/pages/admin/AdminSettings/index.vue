<template>
  <div>
    <SubAppBar :full="false" :followContainer="true" :dense="false" :class="`tw-mb-5 ${isMenuVertical ? 'tw-mt-0' : 'tw-mt-16'}`">
      <div style="display: flex; justify-content: space-between">
        <div>
          <q-btn-toggle
            v-model="tab"
            unelevated
            outline
            toggle-color="primary"
            toggle-text-color="primary"
            color="grey"
            :options="[
              {label: '一般設定', value: 'common-settings'},
              {label: 'LINE設定', value: 'line-settings'},
              {label: 'メール設定', value: 'email-settings'},
              {label: '操作ログ出力', value: 'system-logs'},
              {label: 'トークログ出力', value: 'talk-logs'},
            ]"
          />
        </div>
      </div>
    </SubAppBar>
    <q-card outlined class="my-4">
      <q-tab-panels v-model="tab">
        <q-tab-panel name="common-settings">
          <div fluid>
            <CommonSettings />
          </div>
        </q-tab-panel>
        <q-tab-panel name="line-settings">
          <div fluid>
            <LineSettings />
          </div>
        </q-tab-panel>
        <q-tab-panel name="email-settings">
          <div fluid>
            <EmailSettings />
          </div>
        </q-tab-panel>
        <q-tab-panel name="system-logs">
          <div fluid>
            <SystemLogs />
          </div>
        </q-tab-panel>
        <q-tab-panel name="talk-logs">
          <div fluid>
            <TalkLog />
          </div>
        </q-tab-panel>
      </q-tab-panels>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import SubAppBar from '@/components/common/SubAppBar.vue';
import CommonSettings from './CommonSettings.vue';
import LineSettings from './LineSettings/index.vue';
import EmailSettings from './EmailSettings.vue';
import SystemLogs from './SystemLogs.vue';
import TalkLog from "./TalkLog.vue";

import { useSettingsStore } from '@/stores/modules/settings';

const settingsStore = useSettingsStore();

// data
const tab = ref<string>('common-settings');
const isMenuVertical = ref(settingsStore.commonSettings.menuStyle === "vertical");
</script>
