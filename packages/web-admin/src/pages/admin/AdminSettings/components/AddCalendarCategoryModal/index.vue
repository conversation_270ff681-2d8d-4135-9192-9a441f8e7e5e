<template>
  <q-dialog v-model="show" max-width="700">
    <q-card class="tw-w-full">
      <q-bar class="bg-primary tw-h-1"></q-bar>
      <q-toolbar>
        <q-toolbar-title class="tw-font-semibold tw-p-0">{{ action == "Create" ? "新規追加" : "分類を編集" }}</q-toolbar-title>
        <q-space></q-space>
      </q-toolbar>

      <div>
        <q-form ref="formRef" v-model="valid">
          <q-item-label class="tw-px-6 tw-font-semibold row tw-items-center">
            <span class="mandatoryLabel text-negative tw-p-1 tw-mr-1">必須</span>
            <span class="text-blue-grey">大分類</span>
          </q-item-label>
          <div class="row">
            <div class="tw-px-6 tw-pt-2 tw-pb-3 col">
              <q-input
                v-model="item.tag1"
                :rules="tag1Rules"
                :readonly="isFixed()"
                outlined
                dense
              />
              <span v-if="isFixed()" class="red--text">この大分類は「帳票の分類」に固定されています。</span>
            </div>
          </div>
          <q-item-label class="tw-px-6 tw-font-semibold row tw-items-center">
            <span class="mandatoryLabel text-negative tw-p-1 tw-mr-1">必須</span>
            <span class="text-blue-grey">中分類</span>
          </q-item-label>
          <div class="row">
            <div class="tw-px-6 tw-pt-2 tw-pb-3 col">
              <q-input
                v-model="item.tag2"
                :readonly="isSupFixed()"
                :rules="tag2Rules"
                outlined
                dense
              />
              <span v-if="isSupFixed()" class="red--text">この中分類は「帳票の分類」に固定されています。</span>
            </div>
          </div>
          <div class="row">
            <div class="tw-p-0 col">
              <q-item-label class="tw-px-6 tw-font-semibold">
                <span class="text-blue-grey">小分類</span>
              </q-item-label>
            </div>
          </div>
          <div class="row">
            <div class="tw-px-6 tw-pt-2 tw-pb-3 col">
              <q-input
                v-model="item.tag3"
                outlined
                dense
                @update:model-value="itemTag3Edited"
              />
            </div>
          </div>
          <div class="row">
            <div class="tw-p-0 col">
              <q-item-label class="tw-px-6 tw-font-semibold text-blue-grey">カレンダー有無</q-item-label>
            </div>
          </div>
          <div class="row">
            <div class="tw-px-6 tw-pt-0 tw-pb-3 col">
              <div :class="item.hasCalendar ? 'text-primary' : 'text-negative'" class="tw-mt-2">
                {{ item.hasCalendar ? '作成済' : '未作成' }}
              </div>
            </div>
          </div>
        </q-form>
      </div>

      <div class="pt-0">
        <Alert 
          v-if="showError" 
          class="tw-mt-4 tw-mx-2" 
          type="error" 
          :message="apiError.message ? apiError.message : apiError">
        </Alert>

        <div class="row">
          <div class="tw-justify-end tw-flex tw-px-6 tw-pb-3 col">
            <q-btn
              color="primary"
              outline
              @click="show = false"
              class="tw-m-2"
            >
              キャンセル
            </q-btn>
            <q-btn
              color="primary"
              @click="permissionHelper.hasActionPermission('click', 'backendRequest') ? handleSave() : permissionHelper.showActionPermissionError()"
              :loading="isLoading"
              :disable="isClickable"
              class="tw-m-2 tw-mr-0"
            >
              <!-- :style="
                permissionHelper.hasActionPermission('hideButton', 'AdminSettings_AddCalendarCategoryModal_Save')
                  ? permissionHelper.hideButtonPermissionStyle()
                  : ''
              " -->
              保存
            </q-btn>
          </div>
        </div>
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useQuasar } from 'quasar';
import cloneDeep from "lodash/cloneDeep";
import { useSettingsStore } from '@/stores/modules/settings';
import { useCalenderStore } from '@/stores/modules/calendar';
import { isNullOrEmpty } from "@/utils/stringUtils";
import { usePermissionHelper } from '@/mixins/PermissionHelper';
import Alert from '@/components/common/Alert.vue';

// store
const settingsStore = useSettingsStore();
const calendarStore = useCalenderStore();

const permissionHelper = usePermissionHelper();

// quasar
const $q = useQuasar();

// props
const props = defineProps<{
  modelValue: any,
  visible: boolean,
  action: string,
  selectedItem: any,
  fixedCategories: any,
  surveyResultCategoriesList: any[],
  isOnlyFixedLargeCategory: boolean,
  isOnlyFixedMediumCategory: boolean,
}>();

// emits 
const emits = defineEmits(['close', 'update:modelValue']);

// data
const item = ref<any>(props.modelValue);
const originTag1 = ref<string>("");
const originTag2 = ref<string>("");
const tag1Rules = ref<any>([(v: any) => (!!v && v.trim().length > 0) || "大分類は必須入力です。"]);
const tag2Rules = ref<any>([(v: any) => (!!v && v.trim().length > 0) || "中分類は必須入力です。"]);
const valid = ref<boolean>(true);
const apiError = ref<any>(null);
const showError = ref<boolean>(false);

const formRef = ref();

// computed
const isCreatingCalendarCategories = computed(() => settingsStore.isCreatingCalendarCategories);
const createCalendarCategoriesError = computed(() => settingsStore.createCalendarCategoriesError);
const isUpdatingCalendarCategory = computed(() => settingsStore.isUpdatingCalendarCategory);
const updateCalendarCategoryError = computed(() => settingsStore.updateCalendarCategoryError);
const deleteCalendarCategoryError = computed(() => settingsStore.deleteCalendarCategoryError);
const deleteCalendarInfoError = computed(() => settingsStore.deleteCalendarInfoError);
const isFetchingCategoriesTree = computed(() => calendarStore.isFetchingCategoriesTree);

const show = computed({
  get() {
    return props.visible;
  },
  set(value) {
    if (!value) {
      item.value = {};
      emits("close");
    }
  },
});

const isLoading = computed((): boolean => {
  return isCreatingCalendarCategories.value || isUpdatingCalendarCategory.value　|| isFetchingCategoriesTree.value;
});

const isClickable = computed((): boolean => {
  return isNullOrEmpty(item.value.tag1) || isNullOrEmpty(item.value.tag2);
});

// watch
watch(
  () => props.visible,
  () => {
    showError.value = false;
    apiError.value = null;
  }
);

// 元コード　エラーが出たら確認してください - honda
// watch(
//   () => props.selectedItem,
//   (newVal) => {
//     item.value = cloneDeep(newVal);
//     originTag1.value = item.value.tag1;
//     originTag2.value = item.value.tag2;
//     nextTick(() => {
//       formRef.value.resetValidation();
//     });
//   }
// );

watch(
  () => item.value,
  (newVal) => {
    // console.log(newVal);
    emits('update:modelValue', newVal);
  },
  { deep: true }
);

watch(
  () => createCalendarCategoriesError.value,
  (newVal) => {
    if (newVal) {
      showError.value = true;
      apiError.value = newVal;
    } else {
      showError.value = false;
    }
  }
);

watch(
  () => updateCalendarCategoryError.value,
  (newVal) => {
    if (newVal) {
      showError.value = true;
      apiError.value = newVal;
    } else {
      showError.value = false;
    }
  }
);

watch(
  () => deleteCalendarCategoryError.value,
  (newVal) => {
    if (newVal) {
      showError.value = true;
      apiError.value = newVal;
    } else {
      showError.value = false;
    }
  }
);

watch(
  () => deleteCalendarInfoError.value,
  (newVal) => {
    if (newVal) {
      showError.value = true;
      apiError.value = newVal;
    } else {
      showError.value = false;
    }
  }
);

watch(
  () => props.modelValue,
  (newVall) => {
    item.value = newVall;
  }
);

// methods
const isFixed = (): boolean => {
  if (props.fixedCategories[originTag1.value]) {
    return true;
  } else {
    return false;
  }
};

const isSupFixed = (): boolean => {
  if (props.fixedCategories[originTag1.value] && props.fixedCategories[originTag1.value].includes(originTag2.value)) {
    return true;
  } else {
    return false;
  }
};

const handleSave = (): void => {
  switch (props.action) {
    case "Create":
      create();
      break;
    case "Update":
      update();
      break;
  }
};

const itemTag3Edited = (val: any): void => {
  formRef.value.validate();
};

const create = async (): Promise<void> => {
  try {
    let payload = cloneDeep(item.value);
    const response = await settingsStore.createCalendarCategories([payload]);
    if (response) {
      if (response[0].code == "already_exists") {
        showError.value = true;
        apiError.value = response[0];
      } else {
        // 検索条件の分類選択項目の選択肢一覧を更新する為に実行
        await calendarStore.actionSetCalendarDataOfCategoriesCriteriaTree();
        show.value = false;
        $q.notify({
          message: "カレンダー分類を追加しました。",
          color: "positive",
          icon: "check_circle",
        });
        await onSearch();
      }
    }
  } catch (error: any) {
    console.error(error);
  }
};

const update = async (): Promise<void> => {
  try {
    let payload = cloneDeep(item.value);
    await settingsStore.updateCalendarCategory(payload);
    if (!updateCalendarCategoryError.value) {
      // 検索条件の分類選択項目の選択肢一覧を更新する為に実行
      await calendarStore.actionSetCalendarDataOfCategoriesCriteriaTree();
      show.value = false;
      $q.notify({
        message: "カレンダー分類を更新しました。",
        color: "positive",
        icon: "check_circle",
      });
      await onSearch();
    }
  } catch (error: any) {
    console.error(error);
  }
};

const onSearch = async (): Promise<void> => {
  let payload: any = {};
  const selectedLargeCategory: any = cloneDeep(calendarStore.selectedLargeCategoryCriteria);
  const selectedMediumCategory: any = cloneDeep(calendarStore.selectedMediumCategoryCriteria);
  const selectedSmallCategory: any = cloneDeep(calendarStore.selectedSmallCategoryCriteria);

  if (hasNameProperty(selectedLargeCategory)) {
    payload.tag1 = selectedLargeCategory.name;
  }
  if (hasNameProperty(selectedMediumCategory)) {
    payload.tag2 = selectedMediumCategory.name;
  }
  if (hasNameProperty(selectedSmallCategory)) {
    payload.tag3 = selectedSmallCategory.name;
  }
  payload = {...payload, ...{isSearch: true}};
  await settingsStore.fetchCalendarCategories(payload);
};

const hasNameProperty = (obj: any): boolean => {
  if (!obj) { return false; }
  if (!('name' in obj)) { return false; }
  if (!obj.name) { return false; }
  return true;
};
</script>

<style>
.mandatoryLabel{
  background-color:#FFEBEE;
  display: block;
}
</style>