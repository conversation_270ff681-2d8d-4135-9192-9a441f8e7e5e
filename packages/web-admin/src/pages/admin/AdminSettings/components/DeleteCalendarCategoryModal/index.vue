<template>
  <q-dialog v-model="show" max-width="700">
    <q-card class="tw-w-full">
      <q-bar class="bg-primary tw-h-1"></q-bar>
      <q-toolbar dennse>
        <q-toolbar-title class="tw-font-semibold">削除</q-toolbar-title>
        <q-space></q-space>
      </q-toolbar>

      <q-page-container>
        <div class="row">
          <div class="tw-p-0 col">
            <q-item-label header class="tw-px-6 tw-pt-4">
              選択した分類を削除します。<br>この操作は元に戻すことはできません。<br>よろしいですか？
            </q-item-label>
          </div>
        </div>
      </q-page-container>

      <q-page-container class="tw-pt-0">
        <div class="row">
          <div class="tw-justify-end tw-flex tw-px-6 tw-pt-0 col">
            <q-btn
              color="primary"
              outline
              @click="show = false"
              class="tw-m-2"
            >
              キャンセル
            </q-btn>
            <q-btn
              color="negative"
              class="tw-m-2 tw-mr-0"
              @click="permissionHelper.hasActionPermission('click', 'backendRequest') ? deleteFromModal() : permissionHelper.showActionPermissionError()"
            >
              <!-- :style="
                permissionHelper.hasActionPermission('hideButton', 'AdminSettings_AddCalendarCategoryModal_Save')
                  ? permissionHelper.hideButtonPermissionStyle()
                  : ''
              " -->
              削除
            </q-btn>
          </div>
        </div>
      </q-page-container>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { usePermissionHelper } from '@/mixins/PermissionHelper';
import { computed,  } from 'vue';

const permissionHelper = usePermissionHelper();

// emits 
const emits = defineEmits<{
  (event: 'deleteFromModal'): void;
  (event: 'close'): void;
}>();

// props
const props = defineProps<{
  visible: boolean,
}>();

// methods
const deleteFromModal = (): void => {
  emits("deleteFromModal");
  emits("close");
};

// computed
const show = computed({
  get() {
    return props.visible;
  },
  set(value) {
    if (!value) {
      emits("close");
    }
  },
});
</script>
<style>
.mandatoryLabel{
  background-color:#FFEBEE;
  display: block;
}
</style>