<template>
  <div width="300px">
    <div>{{ source.label }}</div>
    <q-input
      v-model="source.color"
      outlined
      dense
      @focus="openPicker(source.index)"
    >
      <template v-slot:prepend>
        <q-icon name="mdi-brightness-1" :style="{ color: source.color }" />
      </template>
      <q-popup-proxy
        v-model="showColorPicker"
        ref="colorPickerPopup"
        cover
        transition-show="scale"
        transition-hide="scale"
      >
        <q-color
          v-model="source.color"
          :name="source.label"
          default-view="palette"
          @update:model-value="() => (showColorPicker = false)"
        />
      </q-popup-proxy>
    </q-input>
  </div>
</template>

<script setup lang="ts">
import { PropType, ref, onMounted, watch, reactive } from 'vue';

// props
const props = defineProps({
  source: Object as PropType<any>,
  disabled: Boolean as PropType<boolean>
});

const emit = defineEmits(['update:source']);
const colorPickerPopup = ref(false);
const showColorPicker = ref(false);

const showPicker = ref<{ [key: number]: boolean }>({
  1: false,
  2: false,
  3: false,
  4: false,
  5: false,
  6: false,
  7: false,
  8: false,
  9: false,
});

const openPicker = (index: number) => {
  if (index >= 1 && index <= 9) {
    showPicker.value[index] = true;
  }
};

onMounted(() => {
  //console.log(props.source);
});

// data
const modal = ref(false);
</script>