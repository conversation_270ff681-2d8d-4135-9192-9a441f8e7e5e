<template>
  <q-dialog v-model="show" max-width="700">
    <q-card class="tw-w-full">
      <q-bar class="bg-primary tw-h-1"></q-bar>
      <q-toolbar flat dennse>
        <q-toolbar-title class="tw-font-semibold tw-p-0">CSVインポート</q-toolbar-title>
        <q-space></q-space>
      </q-toolbar>

      <q-page-container>
        <div class="row">
          <div class="tw-px-6 tw-pt-2 tw-pb-6 col">
            <div class="blue-grey lighten-5 py-6 text-center">
              <div v-if="!!fileObj">
                <q-icon @click="emits('clearFileObj')" color="negative" size="sm" left name="mdi-close-circle-outline"></q-icon>
                {{ fileObj.name }} ( {{ fileSizeDisplayMB }}MB )
              </div>
              <q-btn
                v-else
                color="primary"
                @click="permissionHelper.hasActionPermission('click', 'backendRequest') ? csvImportFromModal() : permissionHelper.showActionPermissionError()"
              >
                <!-- :style="
                  permissionHelper.hasActionPermission('hideButton', 'AdminSettings_AddCalendarCategoryModal_Save')
                    ? permissionHelper.hideButtonPermissionStyle()
                    : ''
                " -->
                <q-icon left name="mdi-upload-outline"></q-icon>
                CSVファイルを選択
              </q-btn>
            </div>
          </div>
        </div>
      </q-page-container>

      <q-page-container class="tw-pt-0">
        <div class="row">
          <div class="tw-justify-end tw-flex tw-px-6 tw-pt-0 col">
            <q-btn
              color="primary"
              outline
              @click="show = false"
              class="tw-m-2"
            >
              キャンセル
            </q-btn>
            <q-btn
              v-if="fileObj!=null"
              color="primary"
              @click="emits('importCSVCalendarCategoriesRegister')"
              class="tw-m-2 tw-mr-0"
            >
              登録
            </q-btn>
          </div>
        </div>
      </q-page-container>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { usePermissionHelper } from '@/mixins/PermissionHelper';
import { ref, computed, watch, } from 'vue';

const permissionHelper = usePermissionHelper();

// emits 
const emits = defineEmits([
  'csvImportFromModal', 
  'close', 
  'importCSVCalendarCategoriesRegister', 
  'clearFileObj'
]);

// props
const props = defineProps<{
  visible: boolean,
  fileObj: File | null,
}>();

// data
const fileSizeDisplayMB = ref<number | null>(null);

// methods
const csvImportFromModal = (): void => {
  emits("csvImportFromModal");
};

// computed
const show = computed({
  get() {
    return props.visible;
  },
  set(value) {
    if (!value) {
      emits("close");
    }
  },
});

// watch
watch(
  () => props.fileObj,
  (newVal) => {
    if(newVal != null) {
      fileSizeDisplayMB.value = Math.ceil((newVal.size / 1048576) * 100) / 100;
    }
  }
);
</script>
<style>
.mandatoryLabel{
  background-color:#FFEBEE;
  display: block;
}
</style>