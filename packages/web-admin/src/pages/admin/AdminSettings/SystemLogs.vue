<template>
  <div fluid class="px-0 py-0">
    <q-form ref="form">
      <div class="row q-gutter-none align-bottom px-1 py-0" style="height:30px;">
        <div class="col">
          <q-item-label class="q-px-md text-blue-grey-5 text-weight-bold">期間</q-item-label>
        </div>
      </div>
      <q-toolbar class="full-width">
        <div class="full-width">
          <div class="row px-3 ">
            <q-input
              v-model="startDate"
              readonly
              outlined
              dense
              filled
              hide-bottom-space
              class="col-5"
              @focus="() => (showStartDatePicker = true)"
            >
              <template v-slot:prepend>
                <q-icon name="event" class="cursor-pointer" />
              </template>
              <q-popup-proxy
                ref="startDatePickerPopup"
                cover
                transition-show="scale"
                transition-hide="scale"
                v-model="showStartDatePicker"
              >
                <q-date
                  v-model="startDate"
                  mask="YYYY-MM-DD"
                  default-view="Calendar"
                  years-in-month-view
                  minimal
                  @update:model-value="() => (showStartDatePicker = false)"
                >
                  <div>
                    <q-btn v-close-popup label="Close" color="primary" flat />
                  </div>
                </q-date>
              </q-popup-proxy>
            </q-input>
            <q-banner class="mx-2">〜</q-banner>
            <q-input
              v-model="endDate"
              outlined
              dense
              filled
              hide-bottom-space
              readonly
              class="col-5"
              @focus="() => (showEndDatePicker = true)"
            >
              <template v-slot:prepend>
                <q-icon name="event" class="cursor-pointer" />
                </template>
                  <q-popup-proxy
                    ref="endDatePickerPopup"
                    cover
                    transition-show="scale"
                    transition-hide="scale"
                    v-model="showEndDatePicker"
                  >
                    <q-date
                      v-model="endDate"
                      mask="YYYY-MM-DD"
                      default-view="Calendar"
                      years-in-month-view
                      minimal
                      @update:model-value="() => (showEndDatePicker = false)"
                    >
                      <div>
                        <q-btn v-close-popup label="Close" color="primary" flat />
                      </div>
                    </q-date>
                  </q-popup-proxy>
            </q-input>
            <div class="col">
              <q-btn
                class="q-ml-sm"
                color="primary"
                width="93"
                height="40"
                :elevation="0"
                :style="
                  hasActionPermission('hideButton', 'AdminSettings_Search_SystemLogs')
                    ? hideButtonPermissionStyle()
                    : ''
                "
                :loading="isFetchingSystemLogLists"
                :disabled="disableSearchButton"
                @click="
                  hasActionPermission('click', 'backendRequest')
                    ? search()
                    : showActionPermissionError()
                "
                label="絞り込み"
              />
            </div>
          </div>
        </div>
      </q-toolbar>

      <div class="row q-col-gutter-md">
        <div class="col px-4 text-blue-grey-5" style="font-size: 14px">
          最大31日分の範囲指定が可能です。※当日のログは出力できません。一部の操作ログは出力されません。
        </div>
      </div>
        <div class="row q-col-gutter-md">
          <div class="col-12 align-center px-3 pt-5 pb-0">
            <q-separator />
          </div>
          <div class="col-12 px-6">
            <q-table
                class="system-log-table"
                :columns="tableHeaders"
                :rows="systemLogLists"
                :loading="isFetchingSystemLogLists"
                :must-sort="true"
                sort-by="'filename'"
                sort-decs="true"
                :items-per-page="tableItemsPerPage"
                :rows-per-page="0"
                :rows-per-page-options="[20, 50, 100, 0]"
                 v-model:pagination="pagination"
                flat
            >
              <template v-slot:body="props">
                <q-tr :props="props">
                  <q-td key="filename" :props="props">
                   <a  class="text-primary" :href="props.row.url" @click="logDownloadClicked(props.row)">{{ props.row.filename }}</a>
                  </q-td>
                  <q-td key="size" :props="props">
                    {{ props.row.size }}
                  </q-td>
                </q-tr>
              </template>
            </q-table>
          </div>
        </div>
    </q-form>
  </div>
</template>

<script setup lang="ts">
import { ref,  computed, onBeforeMount } from 'vue';
import { usePermissionHelper } from '@/mixins/PermissionHelper';
import { useSystemLogsStore } from '@/stores/modules/systemLogs';
import dayjs from 'dayjs';

const systemLogsStore = useSystemLogsStore();
const { hasActionPermission, hideButtonPermissionStyle, showActionPermissionError } = usePermissionHelper();

const pagination = ref({
  page: 1,
  rowsPerPage: 0, // show all rows
  sortBy: 'filename',
  descending: true
});

// data
const overlay = ref<boolean>(false);
const startDate = ref<any>(dayjs().subtract(31, "days").format("YYYY-MM-DD"));
const endDate = ref<any>(dayjs().subtract(1, "days").format("YYYY-MM-DD"));
const showStartDatePicker = ref<boolean>(false);
const showEndDatePicker = ref<boolean>(false);
const tableHeaders = [
        { name: 'filename', label: 'ファイル名',align: 'left', field: 'filename', class: "blue-grey--text", width: 569},
        { name: 'size', label: 'ファイルサイズ',align: 'left', field: 'size', class: "blue-grey--text"},
      ];
const tableItemsPerPage = ref<number>(-1);
const startDatePickerPopup = ref<any>();
const endDatePickerPopup = ref<any>();

// methods
const searchSystemLogLists = (value) => systemLogsStore.fetchSystemLogLists(value);
const saveSystemlogDownloadLog = (value) => systemLogsStore.saveSystemLogDownloadLog(value);
const search = (): void => {
      searchSystemLogLists({
        startDate: startDate.value,
        endDate: endDate.value,
      });
    };
const logDownloadClicked = (item): void => {
      saveSystemlogDownloadLog({
        url: item.url.replace(/\?.*$/, ''),
        filename: item.filename
      })
    };
const toFileSize = (val: any): string => {
      const units = ['B', 'KB', 'MB', 'GB'];
      let num = val;
      let unit = 0;
      for (let i = 1; i < units.length; i++) {
        if (num < 1024) {
          break;
        }
        num /= 1024;
        unit = i;
      }
      if (num !== 0 && unit !== 0) {
        // NOTE: 余りが出たら小数とする（ちょっと決め打ちすぎるかも）
        num = num.toFixed(3);
      }
      return `${num} ${units[unit]}`;
    };

// computed
const systemLogLists = computed(() => systemLogsStore.systemLogLists);
const isFetchingSystemLogLists = computed(() => systemLogsStore.isFetchingSystemLogLists);
const disableSearchButton = computed((): any => {
      if (!startDate.value || !endDate.value) {
        return true;
      }

      const mStart = dayjs(startDate.value);
      const mEnd = dayjs(endDate.value);
      return mStart.diff(mEnd, 'days') < -30 || mStart.diff(mEnd, 'days') > 0;
    });

// hooks

onBeforeMount(() => {
  if (hasActionPermission('click', 'backendRequest')) {
    search();
  }
});

</script>

<style>
.system-log-table tr:hover {
  background-color: inherit !important;
  cursor: inherit !important;
}
.system-log-table>.v-data-table__wrapper>table>.v-data-table-header>tr>th>span{
    font-size:14px;
}
</style>
