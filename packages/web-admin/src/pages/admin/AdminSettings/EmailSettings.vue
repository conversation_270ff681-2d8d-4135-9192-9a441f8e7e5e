<template>
  <div class="px-0">
    <div v-if="loadingPage">
      <div class="row" justify="center" align="center">
        <div class="col" md="1">
          <q-circular-progress size="50" color="primary" indeterminate/>
        </div>
      </div>
    </div>
    <div v-else>
      <Alert
        v-model="firstTime"
        type="info"
        title="初期設定"
        colorText="black"
        colorBg="white"
        :message="message"
        icon="none"
      >
        <div class="text-right">
          <q-btn
            color="primary"
            min-width="150"
            @click="
              hasActionPermission('click', 'backendRequest') ? onUpdateEmailSettings() : showActionPermissionError()
            "
            :style="
              hasActionPermission('hideButton', 'AdminSettings_EmailSettings_Update') ? hideButtonPermissionStyle() : ''
            "
            :loading="firstTime && isUpdatingMessageTemplates"
            icon="mdi-cog-sync-outline"
            label="初期設定を行う"
          />
        </div>
      </Alert>
        <q-tabs v-model="tab" active-color="primary" :align="`justify`" grow class="outer-distribution-tabs">
          <q-tab name="invite" label="招待メール設定" href="#invite" />
          <q-tab name="verification" label="認証メール設定" href="#verification" />
        </q-tabs>
      <div class="text-right">
        <q-btn
          class ="mx-3 my-5 white--text"
          color="blue-grey"
          @click="reloadSettings()"
          elevation="0"
          :icon="isLoading(isFetchingMessageTemplates)"
          label="データ更新"
        />
      </div>
      <q-tab-panels :model-value="tab" class="mx-3">
        <q-tab-panel name="invite">
          <div class="row">
            <div class="col q-my-md">
              <div class="row text-blue-grey-5 text-font-weight-bold ml-0 mb-1" style="font-size: 14px;"><strong>招待メールの件名</strong></div>
              <q-input
                class="system-email-settings-textarea"
                :model-value="inviteModel.EmailSubject"
                @update:model-value="(val: string) => (inviteModel.EmailSubject = val)"
                solo
                single-line
                outlined
                dense
                flat
                hide-details="auto"
                :disabled="firstTime"
              ></q-input>
            </div>
          </div>
          <div class="row">
            <div class="col q-my-md">
              <div class="row text-blue-grey-5 text-font-weight-bold ml-0 mb-1" style="font-size: 14px;"><strong>招待メールのメッセージ</strong></div>
              <q-input textarea
                :model-value="inviteModel.EmailMessage"
                @update:model-value="(val: string) => (inviteModel.EmailMessage = val)"
                hint="上記のメッセージはカスタマイズし、HTML タグを含めることができますが、「{username}」および「{####}」プレースホルダーを含める必要があります。このプレースホルダーはそれぞれユーザー名および仮パスワードで置き換えられます。"
                persistent-hint
                outlined
                auto-grow
                :error-messages="codeInviteErrorMessages"
                :disabled="firstTime"
                class="messageCtrl"
              />
            </div>
          </div>
          <!-- <Alert
            v-if="hasUpdateInviteEmailSettingsError"
            class="mt-2"
            color="red"
            border="left"
            elevation="1"
            dismissible
            colored-border
            type="error"
            message="updateMessageTemplatesError.message"
          >
            {{ updatingInviteEmailSettings && updateMessageTemplatesError && updateMessageTemplatesError.message }}
          </Alert> -->

          <div v-if="!firstTime" class="text-right">
            <q-btn
              color="primary"
              width="63"
              height="44"
              @click="
                hasActionPermission('click', 'backendRequest')
                  ? onUpdateInviteEmailSettings()
                  : showActionPermissionError()
              "
              :loading="updatingInviteEmailSettings"
              :disable="disableInvite"
              label="更新"
            />
          </div>
        </q-tab-panel>
        <q-tab-panel name="verification">
          <div class="row">
            <div class="col q-my-md">
              <div class="row text-blue-grey-5 text-weight-bold ml-0 mb-1" style="font-size: 14px;"><strong>認証メールの件名</strong></div>
              <template v-if="verificationModel.DefaultEmailOption === 'CONFIRM_WITH_CODE'">
                <q-input
                  :model-value="verificationModel.EmailSubject"
                  @update:model-value="(val: string) => (verificationModel.EmailSubject = val)"
                  solo
                  single-line
                  outlined
                  dense
                  flat
                  hide-details="auto"
                  :disabled="firstTime"
                />
              </template>
            </div>
          </div>
          <div class="row">
            <div class="col q-my-md">
              <div class="row text-blue-grey-5 text-weight-bold ml-0 mb-1" style="font-size: 14px;"><strong>認証メールのメッセージ</strong></div>
              <template v-if="verificationModel.DefaultEmailOption === 'CONFIRM_WITH_CODE'">
                <q-input textarea
                  :model-value="verificationModel.EmailMessage"
                  @update:model-value="(val: string) => (verificationModel.EmailMessage = val)"
                  :hint="verificationHint"
                  persistent-hint
                  outlined
                  auto-grow
                  :error-messages="codeVerificationErrorMessages"
                  :disabled="firstTime"
                  class="messageCtrl"
                />
              </template>
            </div>
          </div>

          <!-- <Alert
            v-model="hasUpdateVerificationEmailSettingsError"
            class="mt-2"
            color="red"
            border="left"
            elevation="1"
            dismissible
            colored-border
            type="error"
          >
            {{
              updatingVerificationEmailSettings && updateMessageTemplatesError && updateMessageTemplatesError.message
            }}
          </Alert> -->

          <div v-if="!firstTime" class="text-right">
            <q-btn
              color="primary"
              width="63"
              height="44"
              @click="
                hasActionPermission('click', 'backendRequest')
                  ? onUpdateVerificationEmailSettings()
                  : showActionPermissionError()
              "
              :loading="updatingVerificationEmailSettings"
              :disabled="disableVerification"
              label="更新"
            />
          </div>
        </q-tab-panel>
      </q-tab-panels>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref,  computed, watch, onBeforeMount } from 'vue';
import { usePermissionHelper } from '@/mixins/PermissionHelper';
import { useSettingsStore } from '@/stores/modules/settings';
import { useQuasar } from 'quasar';
import Alert from '@/components/common/Alert.vue';

const $q = useQuasar();
const settingsStore = useSettingsStore();
const { hasActionPermission, showActionPermissionError, hideButtonPermissionStyle } = usePermissionHelper();

// data
const firstTime = ref<boolean>(false);
const isCheckingFirstTime = ref<boolean>(false);
const tab = ref<string>("invite");
const defaultValues = ref<any>({
        via: ["email"],
        verification: {
          DefaultEmailOption: "CONFIRM_WITH_CODE",
          EmailMessage: "あなたの認証コードは{####}です。",
          EmailSubject: "あなたの認証コード",
          SmsMessage: "あなたの認証コードは{####}です。",
        },
        invite: {
          EmailMessage: "あなたのユーザー名は{username}で、仮パスワードは{####}です。",
          EmailSubject: "あなたの仮パスワード",
          SMSMessage: "あなたのユーザー名は{username}で、仮パスワードは{####}です。",
        },
      });
const localSettings = ref<any>({
        via: [],
        verification: {
          DefaultEmailOption: "",
          EmailMessage: "",
          EmailSubject: "",
          SmsMessage: "",
        },
        invite: {
          EmailMessage: "",
          EmailSubject: "",
          SMSMessage: "",
        },
      });
const verificationModel = ref<any>({
        DefaultEmailOption: "",
        EmailMessage: "",
        EmailSubject: "",
        SmsMessage: "",
      });
const inviteModel = ref<any>({
        EmailMessage: "",
        EmailSubject: "",
        SMSMessage: "",
      });
const updatingInviteEmailSettings = ref<boolean>(false);
const updatingVerificationEmailSettings = ref<boolean>(false);
const message = "このページからメールテンプレートを変更出来るように、まず以下のデフォルト値で設定する必要があります。尚、AmazonCognitoから直接メールテンプレートを設定することがあった場合は以下のデフォルト値で上書きしますのでご注意ください。";

// methods
const isLoading = (value: boolean) => {
      return value ? "mdi-cached mdi-spin" : "mdi-cached";
    };
const fetchMessageTemplates = () => settingsStore.fetchMessageTemplates();
const updateMessageTemplates = (value: { [key: string]: any }) => settingsStore.updateMessageTemplates(value);
const onUpdateInviteEmailSettings = (): void => {
      updatingInviteEmailSettings.value = true;
      for (var key of Object.keys(inviteModel.value)) {
        localSettings.value.invite[key] = inviteModel.value[key];
      }
      onUpdateEmailSettings();
    };
const onUpdateVerificationEmailSettings = (): void => {
      updatingVerificationEmailSettings.value = true;
      for (var key of Object.keys(verificationModel.value)) {
        localSettings.value.verification[key] = verificationModel.value[key];
      }
      onUpdateEmailSettings();
    };
const onUpdateEmailSettings = (): void => {
      const payload = {
        AutoVerifiedAttributes: localSettings.value.via,
        InviteMessageTemplate: localSettings.value.invite,
        VerificationMessageTemplate: localSettings.value.verification,
      };
      updateMessageTemplates(payload);
    };
const reloadSettings = (): void => {
      localSettings.value = defaultValues.value;
      fetchMessageTemplates();
    };
const checkFirstTime = (): boolean => {
      let obj = messageTemplates.value;
      if (!obj) {
        return true;
      }
      if (!("InviteMessageTemplate" in obj)) {
        return true;
      }
      if (!("VerificationMessageTemplate" in obj)) {
        return true;
      }
      if (!("AutoVerifiedAttributes" in obj)) {
        return true;
      }
      for (let key of Object.keys(localSettings.value.invite)) {
        if (!(key in (obj.InviteMessageTemplate as Record<string, any>))) {
          return true;
        }
      }
      for (let key of Object.keys(localSettings.value.verification)) {
        if (!(key in (obj.VerificationMessageTemplate as Record<string, any>))) {
          return true;
        }
      }
      return false;
    };

// computed
const messageTemplates = computed(() => settingsStore.messageTemplates);
const isFetchingMessageTemplates = computed(() => settingsStore.isFetchingMessageTemplates);
const fetchMessageTemplatesError = computed(() => settingsStore.fetchMessageTemplatesError);
const isUpdatingMessageTemplates = computed(() => settingsStore.isUpdatingMessageTemplates);
const updateMessageTemplatesError = computed(() => settingsStore.updateMessageTemplatesError);
const verificationHint = computed((): string => {
      if (verificationModel.value.DefaultEmailOption === "CONFIRM_WITH_CODE") {
        return "上記のメッセージはカスタマイズし、HTML タグを含めることができますが、「{####}」プレースホルダーを含める必要があります。このプレースホルダーはコードで置き換えられます。";
      }
      return "error";
    });
const codeInviteErrorMessages = computed((): any => {
      var value = inviteModel.value.EmailMessage;
      if (!value) {
        return ["必須"];
      }
      if (!value.includes("{username}") && !value.includes("{####}")) {
        return ["「{username}」と「{####}」プレースホルダーを含める必要があります。"];
      }
      if (!value.includes("{username}")) {
        return ["「{username}」プレースホルダーを含める必要があります。"];
      }
      if (!value.includes("{####}")) {
        return ["「{####}」プレースホルダーを含める必要があります。"];
      }
      return [];
    });
const codeVerificationErrorMessages = computed((): Array<string> => {
      var value = verificationModel.value.EmailMessage;
      if (value && value.includes("{####}")) {
        return [];
      }
      return ["「{####}」プレースホルダーを含める必要があります。"];
    });
const disableInvite = computed((): boolean => {
      return codeInviteErrorMessages.value.length > 0;
    });
const disableVerification = computed((): boolean => {
      if (
        verificationModel.value.DefaultEmailOption === "CONFIRM_WITH_CODE" &&
        codeVerificationErrorMessages.value.length === 0
      ) {
        return false;
      }
      return true;
    });
const hasUpdateInviteEmailSettingsError = computed((): any => {
      return updatingInviteEmailSettings.value && updateMessageTemplatesError.value;
    });
const hasUpdateVerificationEmailSettingsError = computed((): any => {
      return updatingVerificationEmailSettings.value && updateMessageTemplatesError.value;
    });
const loadingPage = computed((): any => {
      return isFetchingMessageTemplates.value || isCheckingFirstTime.value;
    });

// watch
watch(() => messageTemplates.value, (value) => {
      if (value) {
        const obj = value as {
          InviteMessageTemplate: Record<string, any>,
          VerificationMessageTemplate: Record<string, any>,
          AutoverifiedAttributes: any };
        if ("InviteMessageTemplate" in obj) {
          for (let key of Object.keys(localSettings.value.invite)) {
            if (key in obj.InviteMessageTemplate) {
              localSettings.value.invite[key] = obj.InviteMessageTemplate[key];
              inviteModel.value[key] = obj.InviteMessageTemplate[key];
            }
          }
        }
        if ("VerificationMessageTemplate" in obj) {
          for (let key of Object.keys(localSettings.value.verification)) {
            if (key in obj.VerificationMessageTemplate) {
              localSettings.value.verification[key] = obj.VerificationMessageTemplate[key];
              verificationModel.value[key] = obj.VerificationMessageTemplate[key];
            }
          }
        }
        if ("AutoVerifiedAttributes" in obj) {
          localSettings.value.via = obj.AutoVerifiedAttributes;
        }
      }
    });
watch(() => isUpdatingMessageTemplates.value, (value) => {
      if (!value) {
        updatingInviteEmailSettings.value = false;
        updatingVerificationEmailSettings.value = false;
        $q.notify({
          message: "設定を更新しました。リロードして下さい。",
        });
      }
    });
watch(() => isFetchingMessageTemplates.value, (value) => {
      if (!value) {
        isCheckingFirstTime.value = true;
        firstTime.value = checkFirstTime();
        isCheckingFirstTime.value = false;
      }
    });

// hooks

onBeforeMount(() => {
  localSettings.value = defaultValues.value;
  inviteModel.value = localSettings.value.invite;
  verificationModel.value = localSettings.value.verification;
  fetchMessageTemplates();
});

</script>
<style>
.system-email-settings-textarea>.v-input__control>.v-input__slot>.v-text-field__slot>input{
    font-size:16px;
}
.messageCtrl .v-messages__message{
  line-height: 1.7;
  color:#607d8b!important;
}
</style>