<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.side-margin {
  margin-left: 1em;
  margin-right: 1em;
}
</style>
<template>
  <q-dialog fullscreen persistent v-model="show" transition="dialog-bottom-transition">
    <q-card>
      <q-toolbar dark color="primary">
        <q-btn icon dark @click="show = false">
          <q-icon>mdi-close</q-icon>
        </q-btn>
        <q-toolbar-title>カレンダー設定</q-toolbar-title>
      </q-toolbar>
      <q-overlay :opacity="0.2" v-if="isFetchingCalendarDisplaySettings">
        <content-loading :size="50" text="" />
      </q-overlay>
      <v-expansion-panels v-model="panel">
        <q-expansion-item>
          <v-expansion-panel-header> カレンダー全般 </v-expansion-panel-header>
          <v-expansion-panel-content>
            <div class="side-margin">
              <CalendarDisplaySettingsFragment />
            </div>
          </v-expansion-panel-content>
        </q-expansion-item>

        <q-expansion-item>
          <v-expansion-panel-header> 分類・カテゴリ </v-expansion-panel-header>
          <v-expansion-panel-content>
            <div class="side-margin">
              <CalendarCategorySearchFragment />
              <q-separator class="my-4"></q-separator>
              <CalendarCategoryListFragment />
            </div>
          </v-expansion-panel-content>
        </q-expansion-item>

        <q-expansion-item>
          <v-expansion-panel-header> 個別カレンダー設定 </v-expansion-panel-header>
          <v-expansion-panel-content>
            <div class="side-margin">
              <IndividualCalendarSettings />
            </div>
          </v-expansion-panel-content>
        </q-expansion-item>
      </v-expansion-panels>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { PropType, ref,  computed, onBeforeMount } from 'vue';

import { useCalenderStore } from '@stores/modules/calendar';
import { useSettingsStore } from '@stores/modules/settings';

// old imports
// 旧インポート
//import { FETCH_CALENDAR_DISPLAY_SETTINGS } from "@/store/action-types";
import CalendarDisplaySettingsFragment from "@/pages/admin/Calendar/components/Settings/CalendarDisplaySettingsFragment.vue";
import CalendarCategorySearchFragment from "@/pages/admin/Calendar/components/Settings/CalendarCategorySearchFragment.vue";
import CalendarCategoryListFragment from "@/pages/admin/Calendar/components/Settings/CalendarCategoryListFragment.vue";
import IndividualCalendarSettings from "@/pages/admin/Calendar/components/Settings/IndividualCalendarSettings.vue";

const calendarStore = useCalenderStore();
const settingsStore = useSettingsStore();

// emits 
const emits = defineEmits<{
  (event: 'close'): void;
}>();

// props
const props = defineProps({
  visible: {
    type: Boolean as PropType<boolean>,
    default: false
  }
});

// data
const panel = ref<number>(0);

// methods
const fetchCalendarDisplaySettings = settingsStore.fetchCalendarDisplaySettings;
const onFetchSettings = async (): Promise<void> => {
      await fetchCalendarDisplaySettings();
    };

// computed
const categoriesTree = computed(() => calendarStore.categoriesTree.value);
const calendarDisplaySettings = computed(() => settingsStore.calendarDisplaySettings);
const isFetchingCalendarDisplaySettings = computed(() => settingsStore.isFetchingCalendarDisplaySettings);
const fetchCalendarDisplaySettingsError = computed(() => settingsStore.fetchCalendarDisplaySettingsError);
const isUpdatingCalendarDisplaySettings = computed(() => settingsStore.isUpdatingCalendarDisplaySettings);
const updateCalendarDisplaySettingsError = computed(() => settingsStore.updateCalendarDisplaySettingsError);
const show = computed({
  get(): boolean {
    return props.visible;
  },
  set(value: boolean): void {
    if (!value) {
      emits("close");
    }
  },
});

// hooks

onBeforeMount(() => {
  onFetchSettings();
});

</script>
