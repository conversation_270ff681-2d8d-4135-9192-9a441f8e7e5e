<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-card class="tw-p-4" flat>
      <div v-if="isSettingInAdmin">
        <div class="row">
          <div class="col-11 tw-my-3">
            <div class="row">
              <div class="col-4 tw-pr-3">
                <LargeCategorySelect />
              </div>
              <div class="col-4 tw-pr-3">
                <MediumCategorySelect />
              </div>
              <div class="col-4 tw-pr-3">
                <SmallCategorySelect />
              </div>
            </div>
          </div>
          <div class="col-auto tw-my-3">
            <q-btn color="primary" @click="onSearchData" :disable="isShowCalendarNotPermission || compIsCategoryNotSelected">
              検索
            </q-btn>
          </div>
        </div>
      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch,onMounted, onUnmounted } from 'vue';

import eventBus from '@utils/eventBus';
import { useQuasar } from 'quasar';
import { useCalenderStore } from '@/stores/modules/calendar';
import { useAuthStore } from '@/stores/modules/auth.module';
import { isEmpty } from "lodash";
import { SHOW_CONTENT_MODE } from "@/stores/modules/calendar/calendar.constants";

// components
import LargeCategorySelect from "@/pages/admin/Calendar/components/Search/LargeCategorySelect.vue";
import MediumCategorySelect from "@/pages/admin/Calendar/components/Search/MediumCategorySelect.vue";
import SmallCategorySelect from "@/pages/admin/Calendar/components/Search/SmallCategorySelect.vue";

const $q = useQuasar();

// store
const calendarStore = useCalenderStore();
const authStore = useAuthStore();

// data
const isSettingInAdmin = ref<boolean>(true);
const saveConfirmTitle = ref<string>("このページを離れてもよろしいですか？");
const saveConfirmText = ref<string>("行った変更は破棄されます。");
const saveBtnConfirmTitle = ref<string>("このページを離れる");
const viewableSurveyIds = ref<any>([]);
const fixedCategoryPermissions = ref<any>({});
const notFixedCategoryPermissions = ref<any>({});

// computed
const msCategoriesTree = computed(() => calendarStore.categoriesTree);
const msSelectedLargeCategory = computed(() => calendarStore.selectedLargeCategory);
const msSelectedMediumCategory = computed(() => calendarStore.selectedMediumCategory);
const msSelectedSmallCategory = computed(() => calendarStore.selectedSmallCategory);
const msIsReloadSchedulesResult = computed(() => calendarStore.isReloadSchedulesResult);
const msReloadSchedulesStartDate = computed(() => calendarStore.reloadSchedulesStartDate);
const schedulesResult = computed(() => calendarStore.schedulesResult);
const selectedDateStart = computed(() => calendarStore.selectedStartDay);
const selectedDateEnd = computed(() => calendarStore.selectedEndDay);
const editScheduleMode = computed(() => calendarStore.editScheduleMode);
const mapShowContentMode = computed(() => calendarStore.showContentMode);
const surveyAndCalendarPermissionsList = computed(() => calendarStore.surveyAndCalendarPermissionsList);
const permissionsFromTeam = computed(() => authStore.permissionsFromTeam);
const isFetchingSurveyAndCalendarPermissions = computed(() => calendarStore.isFetchingSurveyAndCalendarPermissions);
const selectedLargeCategoryName = computed(() => calendarStore.selectedLargeCategoryName);
const selectedMediumCategoryName = computed(() => calendarStore.selectedMediumCategoryName);
const selectedSmallCategoryName = computed(() => calendarStore.selectedSmallCategoryName);
const msGetSelectedCategory = computed(() => calendarStore.getSelectedCategory);

const compIsCategoryNotSelected = computed((): boolean => {
  return msGetSelectedCategory.value ? false : true;
});

const isShowCalendarNotPermission = computed((): boolean => {
  return mapShowContentMode.value === SHOW_CONTENT_MODE.SHOW_CALENDAR_NOT_PERMISSION;
});

// watch
// watch(
//   () => schedulesResult, 
//   (newVal) => {
//     dataCollection = cloneDeep(newVal);
//   }
// );

watch(
  () => msIsReloadSchedulesResult.value, 
  (newVal) => {
    if (newVal) {
      try {
        searchAction(msReloadSchedulesStartDate.value);
      } catch (error: any) {
        console.error(error);
      } finally {
        calendarStore.actionResetCalendarReloadSchedules();
      }
    }
  }
);

watch(
  () => msGetSelectedCategory.value,
  (value) => {
    if (value && value.id) {
      const hasPermission = checkIsPermissionTeam();
      if (hasPermission) {
        calendarStore.actionSetCalendarDisplayContentMode(SHOW_CONTENT_MODE.SHOW_SELECT_CONDITIONS);
      } else {
        calendarStore.actionSetCalendarDisplayContentMode(SHOW_CONTENT_MODE.SHOW_CALENDAR_NOT_PERMISSION);
      }
    } else {
      calendarStore.actionSetCalendarDisplayContentMode(SHOW_CONTENT_MODE.SHOW_SELECT_CONDITIONS);
    }
  }
);

watch (
  () => isFetchingSurveyAndCalendarPermissions.value,
  (newVal) => {
    if (newVal) { $q.loading.show(); }
    else { $q.loading.hide(); }
  }
);

// methods
const searchData = async (): Promise<any> => {
  calendarStore.actionSetCalendarEditMode(false);
  // 分類が選択されない場合
  if (!msGetSelectedCategory.value) {
    calendarStore.actionSetCalendarDisplayContentMode(SHOW_CONTENT_MODE.SHOW_SELECT_CONDITIONS);
  } else {
    // カレンダーが存在する場合
    if (msGetSelectedCategory.value.calendarId) {
      await calendarStore.getAllCalendars();
      let rslt = await searchAction();
      eventBus.emit("onSearchDataEvent");
      return rslt;
    } else {
      // カレンダーが存在しない場合
      calendarStore.actionSetCalendarDisplayContentMode(SHOW_CONTENT_MODE.SHOW_CALENDAR_NOT_EXIST);
    }
  }
};

const onSearchData = async (): Promise<void> => {
  if (editScheduleMode.value) {
    $q.dialog({
      // type: "confirm",
      title: saveConfirmTitle.value,
      message: saveConfirmText.value,
      ok : {
        label: saveBtnConfirmTitle.value,
      },
    }).onOk(async () => {
      searchData();
    });
  } else {
    searchData();
  }
};

const searchAction = async (startDate: any = undefined): Promise<void> => {
  let nowDate = startDate ? new Date(startDate) : new Date();
  nowDate.setDate(nowDate.getDate());
  let targetDate = nowDate;
  let d = new Date(targetDate.toISOString());
  let dayDiff = d.getDay() === 0 ? 6 : -1;
  let mondayMillisecs = d.getTime() - (d.getDay() + dayDiff) * 86400000;
  let dates = [];
  targetDate = new Date(mondayMillisecs);
  for (let i = 0; i < 7; i++) {
    targetDate = new Date(targetDate.setDate(targetDate.getDate() + (i === 0 ? 0 : 1)));
    dates.push({
      fullDate:
        targetDate.getFullYear() +
        "" +
        (targetDate.getMonth() + 1 < 10 ? "0" + (targetDate.getMonth() + 1) : targetDate.getMonth() + 1) +
        "" +
        (targetDate.getDate() < 10 ? "0" + targetDate.getDate() : targetDate.getDate()),
    });
  }
  calendarStore.actionSetCalendarSelectedStartDate(dates[0].fullDate);
  calendarStore.actionSetCalendarSelectedEndDate(dates[6].fullDate);
  let payload = {
    start_day: selectedDateStart.value,
    end_day: selectedDateEnd.value,
    id: msGetSelectedCategory.value.id,
  };
  let titleValue = {
    large: msSelectedLargeCategory.value,
    medium: msSelectedMediumCategory.value,
    small: msSelectedSmallCategory.value,
  };
  calendarStore.actionSetCalendarSelectedTitle(titleValue);
  calendarStore.actionSetDisplayCategoryId(msGetSelectedCategory.value.id);
  calendarStore.setCalendarDisplayCategory(msGetSelectedCategory.value);

  calendarStore.actionSetCalendarTargetDate({
    date: targetDate,
  });

  calendarStore.actionSetCalendarDisplayContentMode(SHOW_CONTENT_MODE.SHOW_SCHEDULE_RESULT);
  calendarStore.actionSetCalendarSchedulesResults(payload);
};

const searchForCalendarId = (node: any, categoryIdToSearch: any): any => {
  if ("id" in node) {
    return node.id == categoryIdToSearch ? node.calendarId : false;
  } else if ("children" in node) {
    let finalResult = false;
    for (let child of node.children) {
      let result = searchForCalendarId(child, categoryIdToSearch);
      if (result) {
        finalResult = result;
      }
    }
    return finalResult;
  } else {
    return false;
  }
};

const checkIsPermissionTeam = (): any => {
  // categoryIdが存在しない or ログインユーザーがAdministrator or 帳票が1件も存在しない
  // -> 閲覧可能
  if (!(msGetSelectedCategory.value && msGetSelectedCategory.value.id) ||
    permissionsFromTeam.value.isAdministrator) {
    return true;
  }
  // 帳票が1件以上ある場合
  // 全ての帳票で閲覧権限がない -> 閲覧不可
  // 分類設定されているカレンダーが1件もない -> 閲覧不可
  if (surveyAndCalendarPermissionsList.value.length !== 0) {
    const hasVieableSurvey = viewableSurveyIds.value.length !== 0;
    const isFixedSurvey = Object.keys(fixedCategoryPermissions.value).length !== 0;
    const isNotFixeSurvey = Object.keys(notFixedCategoryPermissions.value).length !== 0;
    if (!hasVieableSurvey ||
      (!isFixedSurvey && !isNotFixeSurvey)) {
        return false;
    }
    // -> 閲覧権限のある帳票で閲覧可能なカレンダーのチェック
    const hasPermissionResult = checkCalendarPermission();
    return hasPermissionResult;
  }

  return false;
};

const createPermissionTeams = async (): Promise<void> => {
  try {
    calendarStore.setIsFetchingSurveyAndCalendarPermissions(true);
    await calendarStore.fetchSurveyAndCalendarPermissions('allPermissions'); // SurveyConfigから帳票・カレンダーの閲覧権限を取得
    createViewableSurveyIdList(); // ログインユーザーが閲覧可能な帳票ID(Array)を作成
    formatCategoriesPermissions(); // カレンダーの閲覧権限設定を整形
  } catch(error) {
    console.error(error);
  } finally {
    calendarStore.setIsFetchingSurveyAndCalendarPermissions(false);
  }
};

const createViewableSurveyIdList = (): void => {
  // ログインユーザーが閲覧可能な帳票のSurveyId(Array)を作成
  const userTeamIds = permissionsFromTeam.value.teamIds;
  surveyAndCalendarPermissionsList.value.forEach((config: any) => {
    const { surveyTeams, surveyId } = config;
    if (!surveyTeams || surveyTeams.length === 0) { // 未設定 -> 閲覧可能
      viewableSurveyIds.value.push(surveyId);
      return;
    }
    const isAdministratorOnly = surveyTeams.length === 1 && surveyTeams[0].teamId === 'Administrator';
    if (isAdministratorOnly) { // Administratorのみ -> 閲覧可能
      viewableSurveyIds.value.push(surveyId);
      return;
    }
    // 帳票閲覧権限が設定済み -> ログインユーザーが閲覧可能かチェック
    const teamIdList = surveyTeams.map((team: any) => team.teamId);        
    const isViewable = teamIdList.some((teamId: any) => userTeamIds.includes(teamId));
    isViewable && viewableSurveyIds.value.push(surveyId);
  });
};

const formatCategoriesPermissions = (): void => {
  // surveyId毎のcategoryPermissionsのオブジェクトを作成。
  // 例: { surveyId1: categoryPermissions, surveyId2: categoryPermissions, ... }
  surveyAndCalendarPermissionsList.value.forEach((config: any) => {
    const { categoriesPermissions, surveyId, surveySchema } = config;
    if (!categoriesPermissions) {
      return;
    }
    // 分類が固定されている・されていないデータを分割する
    const surveyItem = surveySchema.find((item: any) => item.type === "reservation");
    if (!surveyItem) {
      return;
    }
    if (surveyItem.selectedLargeCategory) {
      fixedCategoryPermissions.value[surveyId] = categoriesPermissions;
    } else {
      notFixedCategoryPermissions.value[surveyId] = categoriesPermissions;
    }
  });
};

const createFixedCalendarPermissions = (selectedCategoryId: any): any => {
  let isFoundEmpty = false;
  const viewableFixedCategoryPermissions = viewableSurveyIds.value.reduce((accum: any, surveyId: any) => {
    const targetPermissions = fixedCategoryPermissions.value[surveyId];
    if (targetPermissions) {
      accum[surveyId] = fixedCategoryPermissions.value[surveyId];
    }
    return accum;
  }, {});

  const fixedCalendarPermissions = Object.keys(viewableFixedCategoryPermissions).reduce((accum, surveyId) => {
    if (isFoundEmpty) {
      return accum;
    }

    const permissions = viewableFixedCategoryPermissions[surveyId];
    permissions.forEach((permission: any) => {
      if (isFoundEmpty) {
        return;
      }
      const { list } = permission;
      const target = list.find((element: any) => element.categoryId === selectedCategoryId);
      if (target) {
        accum = target.teamIds.length > 0 ? accum.concat(target.teamIds) : [];
        target.teamIds.length === 0 && (isFoundEmpty = true);
      }
    });

    return accum;
  }, []);

  return {
    isFoundEmpty,
    fixedCalendarPermissions,
  };
};

const checkCalendarPermission = (): any => {
  const selectedCategoryId = msGetSelectedCategory.value.id;
  const { teamIds } = permissionsFromTeam.value;

  // 閲覧権限のある帳票のカレンダー閲覧権限を取得
  const viewableNotFixedCalendarPermissions = viewableSurveyIds.value.reduce((accum: any, surveyId: any) => {
    const targetPermissions = notFixedCategoryPermissions.value[surveyId];
    if (targetPermissions) {
      accum[surveyId] = notFixedCategoryPermissions.value[surveyId];
    }
    return accum;
  }, {});

  const notFixedSurveyIds = Object.keys(viewableNotFixedCalendarPermissions);
  if (notFixedSurveyIds.length !== 0) {
    // 分類が固定されていない帳票が1件以上ある場合、その中で選択しているカレンダーの閲覧権限が設定されているかチェック
    const notFixedCalendarPermissions = notFixedSurveyIds.reduce((accum, surveyId, index) => {
      if (accum.length === 0 && index > 0) {
        return accum;
      }
      const permissions = viewableNotFixedCalendarPermissions[surveyId];

      permissions.forEach((permission: any, index2: any) => {
        if ((accum.length === 0 && index2 > 0) || accum.length > 0) {
          return;
        }
        const { list } = permission;
        const target = list.find((element: any) => element.categoryId === selectedCategoryId);
        if (target && target.teamIds.length > 0) {
          accum = accum.concat(target.teamIds);
        } else {
          accum = [];
        }
      });

      return accum;
    }, []);

    // 分類が固定されていない帳票の中で、選択しているカレンダーの閲覧権限が設定されていない帳票が1件でもある -> 閲覧可能
    if (notFixedCalendarPermissions.length === 0) {
      return true;
    }

    // 分類が固定されていない帳票全てで選択しているカレンダーの閲覧権限が設定されている場合 -> 分類が固定されている帳票もチェック
    const { fixedCalendarPermissions, isFoundEmpty } = createFixedCalendarPermissions(selectedCategoryId);
    // 選択しているカレンダーの分類が固定されているが閲覧権限が設定されていない場合 -> 閲覧可能
    if (fixedCalendarPermissions.length === 0 && isFoundEmpty) {
      return true;
    }

    // 選択しているカレンダーの閲覧権限設定している帳票が1件以上が存在する or 選択しているカレンダーの分類固定をどの帳票でもしてない
    // -> notFixedCalendarPermissions, fixedCalendarPermissions両方を参照して、ログインユーザーが閲覧可能なチームに所属しているかチェック
    if (!isFoundEmpty) {
      const integratedCalendarPermissions = notFixedCalendarPermissions.concat(fixedCalendarPermissions);
      return integratedCalendarPermissions.some(permission => teamIds.includes(permission));
    }
  } else {
    // 全ての帳票で分類が固定されている場合 -> 閲覧可能な全ての帳票で閲覧可否をチェック
    const { fixedCalendarPermissions, isFoundEmpty } = createFixedCalendarPermissions(selectedCategoryId);
    
    // 選択しているカレンダーの分類が固定されているが全ての帳票で閲覧権限が設定されていない場合 -> 閲覧可能
    // 選択しているカレンダーの分類がどの帳票でも固定されていない場合 -> 閲覧不可
    if (fixedCalendarPermissions.length === 0) {
      return isFoundEmpty;
    } else {
      // 選択しているカレンダーの分類が固定されており、固定している帳票全てで閲覧権限が設定されている場合
      // -> ログインユーザーが閲覧可能チームに所属しているかチェック
      return fixedCalendarPermissions.some((permission: any) => teamIds.includes(permission));
    }
  }
};

const restoreSelectedCategory = async (): Promise<void> => {
  let msSelectedLargeCategory=[];
  let msSelectedMediumCategory=[];
  let msSelectedSmallCategory=[];
  if (!selectedLargeCategoryName.value) {
    return;
  }
  msSelectedLargeCategory = msCategoriesTree.value.tree.find((obj: any) => {
    return obj.name == selectedLargeCategoryName.value;
  });
  await Promise.all([
    await calendarStore.actionSetCalendarSearchSelectedLargeCategory(msSelectedLargeCategory),
    await calendarStore.actionSetCalendarDataOfCategoriesMedium(msSelectedLargeCategory),
    await calendarStore.actionResetCalendarDataOfCategoriesSmall(),
    await calendarStore.actionResetCalendarSelectedMediumCategory(),
    await calendarStore.actionResetCalendarSelectedSmallCategory()
  ]);
  
  if(!selectedMediumCategoryName.value) {
    return;
  }
  msSelectedMediumCategory = msSelectedLargeCategory.children.find((obj: any) => {
    return obj.name == selectedMediumCategoryName.value;
  });
  await Promise.all([
    await calendarStore.actionSetCalendarSearchSelectedMediumCategory(msSelectedMediumCategory),
    await calendarStore.actionSetCalendarDataOfCategoriesSmall(msSelectedMediumCategory),
    await calendarStore.actionResetCalendarSelectedSmallCategory()
  ]);

  if(selectedSmallCategoryName.value) {
    msSelectedSmallCategory = msSelectedMediumCategory.children.find((obj: any) => {
      return obj.name == selectedSmallCategoryName.value;
    });
    await calendarStore.actionSetCalendarSearchSelectedSmallCategory(msSelectedSmallCategory);
  }

  if(msSelectedLargeCategory&&msSelectedMediumCategory){
    await searchData();
  }
  await calendarStore.actionSetSelectedCategoryNames({
    selectedLargeCategoryName: '',
    selectedMediumCategoryName: '',
    selectedSmallCategoryName: '',
  });
};

// hooks
onMounted(async () => {
  if (!msCategoriesTree.value || isEmpty(msCategoriesTree.value)) {
    await calendarStore.actionSetCalendarDataOfCategoriesTree();
  }
  await createPermissionTeams();
  await restoreSelectedCategory();
});


onUnmounted(() => {
  calendarStore.actionResetCalendarDataOfCategoriesTree();
});

</script>