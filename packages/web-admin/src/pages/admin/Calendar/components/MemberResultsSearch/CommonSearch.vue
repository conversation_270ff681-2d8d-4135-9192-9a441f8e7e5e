<template>
  <div class="tw-pb-0">
    <div v-if="isShowAdvancedSearch" class="row">
      <div class="col-3 tw-m-2" v-if="!isMemberSearch">
        <CommonSearchSelector itemKey="check" label="状態" :options="statusList" />
      </div>
      <div v-for="item in multipleSelectOfSelectedSchema" :key="item.itemKey" class="col-3">
        <RadioSectionSearchSelector
          v-if="item.type == 'choicegroupheader'"
          :itemKey="item.itemKey"
          :label="item.title"
          :sectionOptions="item.sectionOptions || []"
        />
        <CommonSearchSelector v-else :itemKey="item.itemKey" :label="item.title" :options="item.options" />
      </div>
    </div>
    <div class="d-flex justify-space-between pb-0 row">
      <div v-for="(item, index) in indexableObjectsOfSelectedSchema" :key="item.itemKey" class="row" style="max-width:52%;">
        <div class="col-auto tw-mr-2">
          <CommonSearchTextInput :itemKey="item.itemKey" :label="item.title" :index="index" :autofocus="true" />
        </div>
      </div>
      <div class="row tw-mx-0" >
        <div class="tw-pl-0">
          <div class="tw-mb-1">
            キーワード
          </div>
          <q-input
            class="tw-pb-2"
            outlined
            dense
            single-line
            hide-details
            placeholder="キーワードを入力"
            v-model="searchKeyword"
          />
          <div class="tw-mb-2">
            <span class="text-grey tw-italic tw-text-xs">
              ※ 検索対象レコードの数が多い場合には、検索に時間がかかる場合が御座います。
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { trim } from "@/utils/stringUtils";
import { useDataManagementStore } from "@/stores/modules/dataManagement";

// components
import RadioSectionSearchSelector from "@/pages/admin/Calendar/components/MemberResultsSearch/RadioSectionSearchSelector.vue";
import CommonSearchSelector from "@/pages/admin/Calendar/components/MemberResultsSearch/CommonSearchSelector.vue";
import CommonSearchTextInput from "@/pages/admin/Calendar/components/MemberResultsSearch/CommonSearchTextInput.vue";

// store
const dataManagementStore = useDataManagementStore();

// props
const props = defineProps<{
  isMemberSearch: boolean,
}>();

// data
const statusList = ref(["未対応", "処理中", "処理済み", "完了"]);

// computed
const searchCriteria = computed(() => dataManagementStore.searchCriteria);
const isShowAdvancedSearch = computed(() => dataManagementStore.isShowAdvancedSearch);
const multipleSelectOfSelectedSchema = computed(() => dataManagementStore.multipleSelectOfSelectedSchema);
const indexableObjectsOfSelectedSchema = computed(() => dataManagementStore.indexableObjectsOfSelectedSchema);
const searchKeyword = computed({
  get(): any {
    return searchCriteria.value["searchKeyword"] || "";
  },
  set(val: any): void {
    dataManagementStore.updateDataManagerSearchCriteriaCommon({ itemKey: "searchKeyword", value: trim(val) });
  },
});

// methods
const test = () => {
  // console.log(searchCriteria.value);
};
</script>