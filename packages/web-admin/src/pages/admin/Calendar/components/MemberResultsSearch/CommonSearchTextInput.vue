<template>
  <div>
    <div class="tw-mb-1">
      {{ label }}
    </div>
    <q-input
      outlined
      dense
      single-line
      hide-details
      :placeholder="placeholder"
      v-model="searchKeyword"
      :autofocus="index == 0 && autofocus"
    ></q-input>
    <div class="q-mt-sm">
      <span class="text-caption text-grey q-mt-md text-italic">※ 完全一致・単一項目検索のみ可能です。</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useDataManagementStore } from "@/stores/modules/dataManagement";

const dataManagementStore = useDataManagementStore();

const props = defineProps<{
  label: string,
  itemKey: string,
  index: number,
  autofocus: boolean,
}>();

// computed
const searchCriteria = computed(() => dataManagementStore.searchCriteria);

const placeholder = computed((): string => {
  return props.label + "を入力";
});

const searchKeyword = computed({
  get(): any {
    return searchCriteria.value[props.itemKey] || [];
  },
  //update search criteria
  set(val: any): void {
    dataManagementStore.updateDataManagerSearchCriteriaCommon({ itemKey: props.itemKey, value: val });
  },
});

// methods
// TODO: selectedsの参照先がわからない - honda
// const isActiveItem = (key: any): any => {
//   return this.selecteds && this.selecteds.includes(key);
// };
</script>