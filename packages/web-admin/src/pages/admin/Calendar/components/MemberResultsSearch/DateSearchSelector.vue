<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <div class="row tw-mb-1">
      <div class="col-auto tw-pt-2 tw-mr-4">
        {{ label }}
      </div>
    </div>
    <div class="row tw-mb-1">
      <div class="col tw-py-0">
        <InputCalendar :props-model="calendarFrom" @update-value="updateValueFrom"></InputCalendar>
        <!-- <q-menu ref="menuFrom" v-model="menuFrom" transition="scale-transition" offset-y min-width="290px">
          <template v-slot:activator="{ on, attrs }">
            <v-text-field
              v-model="dateFrom"
              prepend-inner-icon="mdi-calendar"
              readonly
              v-bind="attrs"
              v-on="on"
              hide-details
              single-line
              outlined
              dense
              clearable
            ></v-text-field>
          </template>
          <v-date-picker v-model="dateFrom" no-title scrollable> </v-date-picker>
        </q-menu> -->
      </div>
      <div class="col-auto tw-pt-2 tw-px-2">〜</div>
      <div class="col tw-py-0">
        <InputCalendar :props-model="calendarTo" @update-value="updateValueTo"></InputCalendar>
        <!-- <v-menu ref="menuTo" v-model="menuTo" transition="scale-transition" offset-y min-width="290px">
          <template v-slot:activator="{ on, attrs }">
            <v-text-field
              v-model="dateTo"
              prepend-inner-icon="mdi-calendar"
              readonly
              v-bind="attrs"
              v-on="on"
              hide-details
              single-line
              outlined
              dense
              clearable
            ></v-text-field>
          </template>
          <v-date-picker v-model="dateTo" no-title scrollable> </v-date-picker>
        </v-menu> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useDataManagementStore } from "@/stores/modules/dataManagement";
import InputCalendar from "@/components/common/InputCalendar.vue";
import { InputCalender } from "@/types/index";

// store
const dataManagement = useDataManagementStore();

const props = defineProps<{
  label: string,
  itemKey: string,
}>();

// data
const menuFrom = ref(false);
const menuTo = ref(false);

// computed
const searchCriteriaDate = computed(() => dataManagement.searchCriteriaDate);

// const isSelectedAll = computed((): boolean => {
//   return this.selecteds.length === this.options.length;
// });

const dateFrom = computed({
  get(): any {
    return searchCriteriaDate.value[props.itemKey] ? searchCriteriaDate.value[props.itemKey].from : null;
  },
  //update search criteria
  set(val: any): void {
    dataManagement.updateDataManagerSearchCriteriaDate({
      itemKey: props.itemKey,
      type: "from",
      value: val,
    });
  },
});

const dateTo = computed({
  get(): any {
    return searchCriteriaDate.value[props.itemKey] ? searchCriteriaDate.value[props.itemKey].to : null;
  },
  //update search criteria
  set(val: any): void {
    dataManagement.updateDataManagerSearchCriteriaDate({
      itemKey: props.itemKey,
      type: "to",
      value: val,
    });
  },
});

const calendarFrom = ref<InputCalender>({
  date: dateFrom.value,
  isMinimal: true,
} as InputCalender);

const calendarTo = ref<InputCalender>({
  date: dateTo.value,
  isMinimal: true,
} as InputCalender);

 // methods
// const toggleSelectAll = (): void => {
//   if (this.isSelectedAll) {
//     this.selecteds = [];
//   } else {
//     this.selecteds = this.options;
//   }
// };

// const isActiveItem = (key: any): any => {
//   return this.selecteds && this.selecteds.includes(key);
// };

watch(
  () => searchCriteriaDate.value,
  (newVal) => {
    calendarFrom.value.date = newVal[props.itemKey] ? newVal[props.itemKey].from : null;
    calendarTo.value.date = newVal[props.itemKey] ? newVal[props.itemKey].to : null;
  }
)

const updateValueFrom = (value: any) => {
  dateFrom.value = value;
};

const updateValueTo = (value: any) => {
  dateTo.value = value;
};
</script>

<style lang="less">
.common-search-selector__content {
  border: 1px solid #999;
  height: 107px;
  overflow-y: auto;
}
.common-search-selector__active {
  border-left: 2px solid var(--v-primary-base);
}
</style>
