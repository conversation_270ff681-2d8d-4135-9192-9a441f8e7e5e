<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <div class="row tw-mb-1">
      <div class="col">
        {{ label }}
      </div>
      <div class="col-auto">
        <a @click="toggleSelectAll" class="text-primary">{{ isSelectedAll ? "全解除" : "全選択" }}</a>
      </div>
    </div>
    <div class="">
      <q-list dense bordered class="tw-py-2 rounded-borders">
        <q-item
          clickable
          v-ripple
          v-for="option in options"
          :key="option"
          style="min-height: 20px"
          :active="isSelect(option)"
          active-class="bg-primary tw-text-white"
          :class="{ 'common-search-selector__active': isActiveItem(option) }"
          @click="clickSelecter(option)"
        >
          <q-item-section>
            <q-item-label>{{ option }}</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { UPDATE_DATA_MANAGER_SEARCH_CRITERIA_COMMON } from "@/stores/mutation-types";
import { useDataManagementStore } from "@/stores/modules/dataManagement";

// store
const dataManagementStore = useDataManagementStore();

// props
const props = defineProps<{
  label: string,
  options: any[],
  itemKey: string,
}>();

// computed
const searchCriteria = computed(() => dataManagementStore.searchCriteria);

const isSelectedAll = computed((): boolean => {
  return selecteds.value.length === props.options.length;
});

const selecteds = computed({
  get(): any {
    return searchCriteria.value[props.itemKey] || [];
  },
  //update search criteria
  set(val: any): any {
    dataManagementStore.updateDataManagerSearchCriteriaCommon({ itemKey: props.itemKey, value: val });
  },
});

// methods
const toggleSelectAll = (): void => {
  if (isSelectedAll.value) {
    selecteds.value = [];
  } else {
    selecteds.value = props.options;
  }
};

const isActiveItem = (key: any): any => {
  return selecteds.value && selecteds.value.includes(key);
};

const clickSelecter = (value: string) => {
  let isDelete = false;
  selecteds.value = selecteds.value.filter((item: any) => {
    if (item === value) {
      isDelete = true;
    }
    return item !== value;
  });

  if (!isDelete) {
    selecteds.value.push(value);
  }
};

const isSelect = (value: string) => {
  return selecteds.value.filter((item: any) => item === value).length > 0;
};
</script>

<style lang="less">
.common-search-selector__content {
  border: 1px solid #999;
  height: 107px;
  overflow-y: auto;
}
.common-search-selector__active {
  border-left: 2px solid var(--v-primary-base);
}
</style>
