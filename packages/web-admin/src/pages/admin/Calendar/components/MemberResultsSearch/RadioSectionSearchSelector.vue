<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <div class="row tw-mb-1">
      <div class="col">
        {{ label }}
      </div>
      <div class="col-auto">
        <a @click="toggleSelectAll">{{ isSelectedAll ? "全解除" : "全選択" }}</a>
      </div>
    </div>
    <div class="common-search-selector__content">
      <q-list dense class="tw-py-2">
        <!-- <v-list-item-group v-model="selecteds" multiple color="primary"> -->
        <q-item
          v-for="sectionOption in sectionOptions"
          :key="sectionOption.option.value"
          :value="sectionOption.option.value"
          style="min-height: 20px"
          :class="{ 'common-search-selector__active': isActiveItem(sectionOption.option.value) }"
        >
          <q-item-section>
            <q-item-label>{{ sectionOption.option.value }}</q-item-label>
          </q-item-section>
        </q-item>
        <!-- </v-list-item-group> -->
      </q-list>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { UPDATE_DATA_MANAGER_SEARCH_CRITERIA_COMMON } from "@/stores/mutation-types";
import { useDataManagementStore } from "@/stores/modules/dataManagement";

const dataManagementStore = useDataManagementStore();

const props = defineProps<{
  label: string,
  sectionOptions: any[],
  itemKey: string,
}>();

// computed
const searchCriteria = computed(() => dataManagementStore.searchCriteria);

const isSelectedAll = computed((): boolean => {
  return selecteds.value.length === props.sectionOptions.length;
});

const selecteds = computed({
  get(): any {
    return searchCriteria.value[props.itemKey] || [];
  },
  //update search criteria
  set(val: any): void {
    let sectionOptions: any[] = [];
    val.forEach((element: any) => {
      if (typeof element == "string") {
        sectionOptions.push(element);
      } else if (element && element.option && element.option.value) {
        sectionOptions.push(element.option.value);
      }
    });
    dataManagementStore.updateDataManagerSearchCriteriaCommon({ itemKey: props.itemKey, value: sectionOptions });
  },
});

 // methods
const toggleSelectAll = (): void => {
  if (isSelectedAll.value) {
    selecteds.value = [];
  } else {
    selecteds.value = props.sectionOptions;
  }
};

const isActiveItem = (key: any): any => {
  return selecteds.value && selecteds.value.includes(key);
};
</script>

<style lang="less">
.common-search-selector__content {
  border: 1px solid #999;
  height: 107px;
  overflow-y: auto;
}
.common-search-selector__active {
  border-left: 2px solid var(--v-primary-base);
}
</style>
