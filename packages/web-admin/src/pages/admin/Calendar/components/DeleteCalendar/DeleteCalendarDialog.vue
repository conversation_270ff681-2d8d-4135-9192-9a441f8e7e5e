<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-dialog v-model="show" width="480">
    <q-card class="tw-w-full">
      <q-bar class="bg-primary tw-h-1"></q-bar>
      <q-toolbar flat>
        <q-toolbar-title>カレンダーを削除</q-toolbar-title>
      </q-toolbar>
      <q-card-section class="tw-my-4 text-grey-8">
        このカレンダーを削除します。<br />
        この操作は元に戻すことはできません。<br />
        よろしいですか？<br />
      </q-card-section>
      <q-card-actions>
        <q-space></q-space>
        <q-btn
          color="blue-grey"
          outline
          @click="show=false"
        >
          キャンセル
        </q-btn>
        <q-btn
          color="warning"
          @click="onConfirm"
        >
          削除
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// props
const props = defineProps<{
  visible: boolean,
}>();

const emit = defineEmits(['onConfirm', 'close']);

// computed
const show = computed({
  get(): boolean {
    return props.visible;
  },
  set(val: boolean): void {
    if (!val) {
      emit("close");
    }
  },
});

// methods
const onConfirm = (): void => {
  emit('onConfirm');
  show.value = false
};
</script>
