<template>
  <q-dialog v-model="show" width="600">
    <q-card class="tw-w-full">
      <q-bar class="bg-primary tw-h-1"></q-bar>
      <q-toolbar flat>
        <q-toolbar-title>CSV出力</q-toolbar-title>
      </q-toolbar>
      <div class="tw-px-3 text-grey-8  tw-flex tw-justify-center">
        カレンダーの期間をご指定ください。
        <span style="color: #f44; margin-left: 15px">
          ※設定されてない日付は出力されません。
        </span>
      </div>
      <q-form @submit="onExport">
        <q-card-section>
          <div class="tw-mb-10 row tw-flex tw-justify-center">
            <div class="col-auto">
              <input-calendar :props-model="fromDateCalendar" @update-value="updateFromDate"></input-calendar>
            </div>
            <div class="col-2"></div>
            <div class="col-auto">
              <input-calendar :props-model="toDateCalendar" @update-value="updateToDate"></input-calendar>
            </div>
          </div>
        </q-card-section>
        <q-separator></q-separator>
        <q-card-actions align="right" class="tw-mr-2">
          <q-btn color="primary" class="tw-w-16" type="submit" :loading="mapIsFetchingExportCalendarCsv">
            OK
          </q-btn>
          <q-btn color="grey-8" outline @click="show = false"> キャンセル </q-btn>
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, } from 'vue';
import { useQuasar } from 'quasar';
import { useCalenderStore } from '@/stores/modules/calendar';
import dayjs from "dayjs";
import InputCalendar from '@/components/common/InputCalendar.vue';
import { InputCalender } from '@/types/index';

const $q = useQuasar();

// store
const calendarStore = useCalenderStore();

// props
const props = defineProps<{
  visible: boolean,
}>();

const emit = defineEmits(['close']);

// data
const required = ref<any>([(value: any) => !!value || "必須です"]);
const fromDateFormatted = ref<any>();
const toDateFormatted = ref<any>();

const fromDateCalendar = ref<InputCalender>({
  placeholder: 'YYYY/MM/DD',
  isOutline: false,
  label: 'From',
  rule: required,
} as InputCalender);

const toDateCalendar = ref<InputCalender>({
  placeholder: 'YYYY/MM/DD',
  isOutline: false,
  label: 'To',
  rule: required,
} as InputCalender);

// computed
const mapIsFetchingExportCalendarCsv = computed(() => calendarStore.isFetchingExportCalendarCsv);
const mapExportCalendarCsvUrl = computed(() => calendarStore.exportCalendarCsvUrl);
const mapExportCalendarCsvError = computed(() => calendarStore.exportCalendarCsvError);
const msGetSelectedCategory = computed(() => calendarStore.getSelectedCategory);

const show = computed({
  get(): boolean {
    return props.visible;
  },
  set(value: boolean): void {
    if (!value) {
      refresh();
      emit("close");
    }
  },
});

// watch
watch(
  () => fromDateCalendar.value.date,
  (newVal) => {
    fromDateFormatted.value = formatDate(newVal);
  }
);

watch(
  () => toDateCalendar.value.date,
  (newVal) => {
    toDateFormatted.value = formatDate(newVal);
  }
);

watch(
  () => mapExportCalendarCsvUrl.value,
  (newVal, oldVal) => {
    if (newVal != null && newVal !== oldVal) {
      const link = document.createElement("a");
      link.download = newVal.split("/").pop();
      link.href = newVal;
      link.click();
    }
  }
);

// methods
const formatDate = (date: any): string | null => {
  if (!date) return null;
  const [year, month, day] = date.split("-");
  return `${year}/${month}/${day}`;
};

const onExport = async (): Promise<void> => {
  $q.notify({ message: "CSVファイルを出力しています。" });
  let payload = {
    categoryId: msGetSelectedCategory.value?.id,
    startDay: dayjs(fromDateCalendar.value.date, "YYYY-M-D").format("YYYYMMDD"),
    endDay: dayjs(toDateCalendar.value.date, "YYYY-M-D").format("YYYYMMDD"),
  };
  await calendarStore.actionExportCalendarCsv(payload).then(() => {
    if (mapExportCalendarCsvError.value) {
      $q.notify({ message: mapExportCalendarCsvError.value, type: "negative" });
    }
  });
  
  show.value = false;
  refresh();
};

const updateFromDate = (value: any): void => {
  fromDateCalendar.value.date = value;
};

const updateToDate = (value: any): void => {
  toDateCalendar.value.date = value;
};

const refresh = () => {
  fromDateCalendar.value.date = new Date().toISOString().substring(0, 10).replaceAll('-', '/');
  fromDateFormatted.value = formatDate(new Date().toISOString().substring(0, 10));
  toDateCalendar.value.date = new Date().toISOString().substring(0, 10);
  toDateFormatted.value = formatDate(new Date().toISOString().substring(0, 10));  
};

// hooks
onMounted(() => {
  refresh();
});
</script>
