<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div class="row tw-flex tw-justify-center">
    <q-dialog v-model="show" scrollable max-width="638">
      <q-card v-if="surveyConfig" class="tw-w-full">
        <q-bar class="bg-primary tw-h-1"></q-bar>
        <q-toolbar flat dense class="tw-px-4 tw-py-4">
          <q-toolbar-title  class="font-weight-bold" style="font-size:24px;">
            {{ surveyConfig.surveyTitle }}
          </q-toolbar-title>
          <q-space/>
        </q-toolbar>
        <q-card-section style="max-height: 90vh overflow-y:auto" class="tw-px-4">
          <FormRendering
            :key="newUUID"
            :configJson="surveyDataWithConfig"
            :isLiffMode="false"
            :isAdminMode="true"
            ref="inputFormRef"
            isSurveyRegistering
            @handleCheckSaibanExisting="handleCheckSaibanExisting"
            :isSaibanExisted="isSaibanExisted"
            :pinia="getActivePinia()"
          />
        </q-card-section>
        <q-card-actions  class="d-flex justify-end tw-pb-4">
          <q-btn
            color="primary"
            outline
            @click="show = false"
          >
            キャンセル
          </q-btn>
          <q-btn
            color="primary"
            @click="
              permissionHelper.hasActionPermission('click', 'backendRequest') ? onCreateSurveyData() : permissionHelper.showActionPermissionError()
            "
            :style="
              permissionHelper.hasActionPermission('hideButton', 'Applicants_NewSurveyModal_Save') ? permissionHelper.hideButtonPermissionStyle() : ''
            "
          >
            保存
          </q-btn>
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { cloneDeep } from "lodash";
import { generateUUID } from "@/utils/uuidUtils";
import { useDataManagementStore } from "@/stores/modules/dataManagement";
import { useQuasar } from "quasar";
import { usePermissionHelper } from "@/mixins/PermissionHelper";
import { getActivePinia } from "pinia";

// components
import FormRendering from "@/../../web-shared/src/components/FormRendering/index.vue";

// quasar
const $q = useQuasar();

// store
const dataManagementStore = useDataManagementStore();

const permissionHelper = usePermissionHelper();

// props emit
const props = defineProps<{
  visible: boolean,
  formData: any,
}>();

const emit = defineEmits(['close']);

// data
const note = ref('');
const newUUID = ref('');
const isSaibanExisted = ref(true);

// ref data
const inputFormRef = ref();

// computed
const surveyConfig = computed(() => dataManagementStore.selectedSurvey);
const isUpdatingData = computed(() => dataManagementStore.isUpdatingData);
const updateDataError = computed(() => dataManagementStore.updateDataError);
const checkSaibanExistError = computed(() => dataManagementStore.checkSaibanExistError);

const surveyDataWithConfig = computed((): any => {
  let _surveyConfig = cloneDeep(surveyConfig.value);
  return _surveyConfig;
});

const show = computed({
  get(): boolean {
    return props.visible;
  },
  set(value: boolean): void {
    if (!value) {
      emit("close");
    }
  },
});

const isBunruiCalendar = computed((): boolean => {
  if (props.formData && props.formData.surveySchema) {
    let surveySchema = props.formData.surveySchema;
    for (let i = 0; i < surveySchema.length; i++) {
      if (surveySchema[i].type && surveySchema[i].type === "linkbutton") {
        return true;
      }
    }
  }
  return false;
});

// watch
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      newUUID.value = generateUUID();
      note.value = "";
    }
  }
);

watch(
  () => updateDataError.value,
  (newVal) => {
    //if value already updated, update current data
    if (newVal !== null) {
      if (newVal.needRefresh !== undefined && newVal.needRefresh === true) {
        if (newVal.result === "ERROR") {
          $q.dialog({
            // type: "warning",
            title: "変更を保存できません",
            // hideBtnCancel: true,
            message: "帳票データが、他の人によって変更されました。\nページを再読み込みしてください。",
            ok: {
              lebel: '再読み込み',
            }
          }).onOk(async () => {
            reloadPage();
          });
        }
      } 
      // else {
      //   $q.notify({
      //     color: "negative",
      //     message: newVal.errorMessage,
      //   });
      // }
    }
  }
);

watch(
  () => checkSaibanExistError.value,
  (newVal) => {
    if (newVal !== null) {
      if (newVal.needRefresh !== undefined && newVal.needRefresh === true) {
        if (newVal.result === "ERROR") {
          $q.dialog({
            // type: "warning",
            title: "会員番号の存在チェック",
            // hideBtnCancel: true,
            message: "不明なエラー",
            ok: {
              label: '再読み込み',
            }
          }).onOk(async () => {
            reloadPage();
          });
        }
      } else {
        // console.log("checkSaibanExistError, value");
        // console.log(newVal);
        $q.notify({
          type: "error",
          message: "会員番号の存在チェック",
          caption: newVal.errorMessage ?? "不明なエラー",
        });
      }
    }
  }
);

watch(
  () => isUpdatingData.value,
  (newVal) => {
    if (newVal) {
      $q.loading.show();
    }
    else {
      $q.loading.hide();
    }
  }
);

// methods
const createNewUUID = (): string => {
  return generateUUID();
};

const reloadPage = (): void => {
  window.location.reload();
};

const getTitle = (key: any): any => {
  let _item = props.formData.surveySchema.find((obj: any) => obj.itemKey === key);
  return _item ? _item.title : "";
};

const getType = (key: any): any => {
  let _item = props.formData.surveySchema.find((obj: any) => obj.itemKey === key);
  return _item ? _item.type : "";
};

const handleSurveyResults = async (): Promise<any> => {


  // Validate FormRendering before proceeding
  const isValid = await inputFormRef.value.validateForm();

  if (!isValid) {
    return null
  }

  // If validation passes, proceed with getting form data
  const formData = await inputFormRef.value.getFormData();
  if (!formData) {
    return null;
  }

  let surveyResults = [];
  for (var pair of formData.entries()) {
    let value = getType(pair[0]) === "date" && pair[1] === "null" ? null : pair[1];
    value = value === "null" ? null : value;
    if (value) {
      surveyResults.push({
        itemKey: pair[0],
        title: getTitle(pair[0]),
        value: value,
      });
    }
  }

  return {
    surveyId: props.formData.surveyId,
    surveyResults,
  };
};


const onCreateSurveyData = async (): Promise<void> => {
  let formData = await handleSurveyResults();
  if (!formData) {
    $q.dialog({
      // type: "warning",
      title: "エラー",
      message: "入力内容を確認してください。",
      // hideBtnConfirm: true,
    });
  } else {
    formData.surveyResults.forEach(function (data: any, index: any) {
      if (getType(data.itemKey) === "linkbutton") {
        formData.surveyResults.splice(index, 1);
      }
    });
    $q.dialog({
      // type: "confirm",
      title: "データの登録",
      message: "この入力内容でデータ登録してもよろしいですか？",
      ok: {
        label: '保存',
      }
    }).onOk(async () => {
      let surveyResult = {
        ...formData,
        surveyConfig: surveyConfig.value,
        note: note.value,
      };
      let _result = await dataManagementStore.createMemberSurvey(surveyResult);

      //if value already updated, update current data
      if (typeof _result === "object") {
        $q.dialog({
          // type: "warning",
          title: "変更を保存できません。",
          message: "編集していたデータは、他の人によって変更されました。\n もう一度編集する前に、データを更新してください。",
          ok: {
            label: 'データ更新',
          },
        }).onOk(async () => {
          show.value = false;
          await dataManagementStore.fetchDataOfSelectedMemberList();
          $q.notify({
            message: "最新データが更新されました。",
          });
        });
      }

      if (_result === true) {
        await dataManagementStore.updateIsResearchData(true);
        $q.notify({
          message: "データの保存が成功しました。",
        });
        show.value = false;
      }
    });
  }
};

const handleCheckSaibanExisting = async (saibanString: any): Promise<void> => {
  if (saibanString === "") {
    return;
  }

  let memberSurveyId = surveyConfig.value.memberSurveyId;
  let memberSurveySubId = surveyConfig.value.memberSurveySubId;
  let params = {
    memberSurveyId,
    memberSurveySubId,
    saibanString,
  };
  let saibanExistResult = await dataManagementStore.checkSaibanExist(params);
  if (saibanExistResult === null || saibanExistResult == undefined || saibanExistResult === false) {
    isSaibanExisted.value = true;
  } else {
    if (
      saibanExistResult.data &&
      (saibanExistResult.data.saibanExistFlag === true ||
        saibanExistResult.data.saibanExistFlag === null ||
        saibanExistResult.data.saibanExistFlag === undefined)
    ) {
      isSaibanExisted.value = true;
    } else {
      isSaibanExisted.value = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.selectCaldndarValue {
  display: inline;
  font-size: 17px;
}
</style>
