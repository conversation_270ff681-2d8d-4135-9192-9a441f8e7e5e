<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
    <q-dialog v-model="show" style="max-width: 800px">
      <q-card v-if="memberConfig" class="tw-w-full" style="max-width: 800px">
        <q-bar class="bg-primary tw-h-1" dark> </q-bar>
        <q-toolbar flat>
          <q-toolbar-title>
            {{ memberConfig.surveyTitle }}
          </q-toolbar-title>

          <q-space/>
          <div class="tw-mr-2">
            作成日：{{ datetimeFormatter.formatUnixToYYYYMMDHHmmss(detailDataLocal.createdAt) }}
            <span v-if="detailDataLocal.createdAt !== detailDataLocal.updatedAt">
              （更新日：{{ datetimeFormatter.formatUnixToYYYYMMDHHmmss(detailDataLocal.updatedAt) }}）
            </span>
          </div>
          <q-btn icon="mdi-close" flat round @click="show = false">
          </q-btn>
        </q-toolbar>
        <div class="row tw-pb-2">
          <div class="col">
            <div class="tw-mx-4">
              {{ detailDataLocal.userId }}
            </div>
          </div>
        </div>
        <q-card-section style="max-height: 90vh" :id="`print-details-target-${detailDataLocal.id}`">
          <FormRendering
            :key="detailDataLocal.id + detailDataLocal.updatedAt"
            :configJson="surveyDataWithConfig"
            :isLiffMode="false"
            :isAdminMode="true"
            ref="inputFormRef"
            :isSurveyRegistering="false"
            @handleCheckSaibanExistingForUpdate="handleCheckSaibanExistingForUpdate"
            @handleCheckSaibanExisting="handleCheckSaibanExisting"
            :isSaibanExisted="isSaibanExisted"
            :pinia="getActivePinia()"
          />
        </q-card-section>

        <LinkedSurveyData
          :linkedSurveyConfigIds="formData?.linkedForms"
          :userId="detailDataLocal.userId"
          @onSurveyConfigSelected="onSurveyConfigSelected"
        />

        <q-separator />
        <q-card-actions>
          <div class="row tw-py-2 full-width">
            <q-space />
            <div class="flex justify-between tw-pr-2">
              <q-btn
                color="secondary"
                class="tw-px-8 tw-mr-5"
                width="200"
                @click="onResetLineUserId()"
                :style="
                  permissionHelper.hasActionPermission('hideButton', 'Applicants_MemberDataModal_ResetId') ? permissionHelper.hideButtonPermissionStyle() : ''
                "
              >
                LINEユーザーIDリセット
              </q-btn>
              <q-btn
                color="secondary"
                class="tw-px-8 tw-mr-5"
                width="120"
                @click="onPrintSurveyData()"
                :loading="isLoadingPrint"
              >
                <q-icon left name="mdi-printer-check"></q-icon>
                印刷
              </q-btn>
              <q-btn
                color="primary"
                class="tw-px-8 tw-mr-5"
                width="120"
                @click="
                  permissionHelper.hasActionPermission('click', 'backendRequest') ? onUpdateSurveyData() : permissionHelper.showActionPermissionError()
                "
                :style="
                  permissionHelper.hasActionPermission('hideButton', 'Applicants_DataModal_Save') ? permissionHelper.hideButtonPermissionStyle() : ''
                "
              >
                <q-icon left name="mdi-content-save-outline"></q-icon>
                保存
              </q-btn>
              <q-btn color="grey lighten-2" class="tw-px-6" width="120" @click="show = false">
                <q-icon left name="mdi-close-box-outline"></q-icon>
                閉じる
              </q-btn>
            </div>
          </div>
        </q-card-actions>
      </q-card>
      <q-inner-loading v-if="dataManagementStore.isUpdatingData">
        <q-spinner size="50px" />
      </q-inner-loading>
    </q-dialog>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { useDatetimeFormatter } from "@/mixins/DatetimeFormatter";
import dayjs from "dayjs";
import { cloneDeep } from "lodash";
import { DATA_CHECK_COLOR } from "@/stores/modules/dataManagement/dataManagement.constants";
import { useDataManagementStore } from "@/stores/modules/dataManagement";
import { useQuasar } from "quasar";
import { useMemberResultsStore } from "@/stores/modules/memberResults";
import { usePermissionHelper } from "@/mixins/PermissionHelper";
import { getActivePinia } from "pinia";

// components
import FormRendering from "@/../../web-shared/src/components/FormRendering/index.vue";
import LinkedSurveyData from "./LinkedSurveyData.vue";
import { useVueToPrint } from "vue-to-print";

// interface LocalState {
//   detailDataLocal: any;
//   isSaibanExisted: boolean;
//   shudou: any;
//   linkedSurveyConfig: any;
// }

// quasar
const $q = useQuasar();

// store
const dataManagementStore = useDataManagementStore();
const memberResultsStore = useMemberResultsStore();

const datetimeFormatter = useDatetimeFormatter();
const permissionHelper = usePermissionHelper();

// props emit
const props = defineProps<{
  visible: boolean,
  detailData: any,
  formData: any,
  isAppending: boolean,
}>();

const emit = defineEmits(['close']);

// data
const detailDataLocal = ref();
const isSaibanExisted = ref(false);
const shudou = ref({
  originalValue: null,
});
const linkedSurveyConfig = ref(null);
const isLoadingPrint = ref(false);

// ref data
const inputFormRef = ref();

// computed
    // ...mapState({
const memberConfig = computed(() => dataManagementStore.selectedSurvey);
    //   isUpdatingData: (state: any) => state.dataManagement.isUpdatingData,
const updateDataError = computed(() => dataManagementStore.updateDataError);
    //   checkSaibanExistError: (state: any) => state.dataManagement.checkSaibanExistError,
    // }),

const surveyDataWithConfig = computed((): any => {
  let _memberConfig = cloneDeep(memberConfig.value);
  return {
    ..._memberConfig,
    inputs: detailDataLocal.value,
  };
});

const show = computed({
  get(): boolean {
    return props.visible;
  },
  set(value: boolean): void {
    if (!value) {
      emit("close");
    }
  },
});

const isBunruiCalendar = computed((): boolean => {
  if (props.formData && props.formData.surveySchema) {
    let surveySchema = props.formData.surveySchema;
    for (let i = 0; i < surveySchema.length; i++) {
      if (surveySchema[i].type && surveySchema[i].type === "linkbutton") {
        return true;
      }
    }
  }
  return false;
});

// watch
watch(
  () => props.detailData,
  (newVal) => {
    detailDataLocal.value = cloneDeep(newVal);
  }
);

watch(
  () => updateDataError.value,
  (newVal) => {
    //if value already updated, update current data
    if (newVal !== null) {
      if (newVal.needRefresh !== undefined && newVal.needRefresh === true) {
        if (newVal.result === "ERROR") {
          $q.dialog({
            // type: "warning",
            title: "変更を保存できません",
            // hideBtnCancel: true,
            message: "帳票データが、他の人によって変更されました。\nページを再読み込みしてください。",
            ok: {
              label: '再読み込み',
            }
          }).onOk(async () => {
            reloadPage();
          });
        }
      } else {
        $q.notify({
          type: "error",
          message: newVal.errorMessage,
        });
      }
    }
  }
);

watch(
  () => props.visible,
  (newVal) => {
    if (!newVal) {
      shudou.value.originalValue = null;
    }
  }
);

// mounted
onMounted(() => {
  detailDataLocal.value = cloneDeep(props.detailData);
});

// methods
    // ...mapActions({
    //   checkSaibanExistForUpdate: CHECK_SAIBAN_EXIST_FOR_UPDATE,
    //   updateSurveyResult: UPDATE_MEMBER_SURVEY,
    //   syncDataSurveyResults: FETCH_DATA_OF_SELECTED_MEMBER_LIST,
    //   resetLineUserId: RESET_MEMBER_LINE_USER_ID,
    // }),
const reloadPage = (): void => {
  window.location.reload();
};

const onDelete = (): void => {
  let that = this;
  $q.dialog({
    // type: "error",
    title: "データの削除",
    message: "このデータを削除してもよろしいですか？",
    ok: {
      label: '削除',
    }
  }).onOk(() => {
    $q.notify({ message: "開発中。。。" });
  });
};

const onResetLineUserId = (): void => {
  $q.dialog({
    // type: "confirm",
    title: "LINEユーザーIDリセット",
    message: "LINEユーザーIDをリセットしてもよろしいですか？",
    ok: {
      label: 'リセット',
    }
  }).onOk(async () => {
    try {
      const partitionKey = detailDataLocal.value.partitionKey;
      const now = dayjs();
      const newUserId = `I${datetimeFormatter.formatStringToYYYYMMDDHHmmssfff(now)}n1`;

      await memberResultsStore.resetMemberLineUserId({
        partitionKey: partitionKey,
        userId: newUserId
      });

      detailDataLocal.value.id = newUserId;
      detailDataLocal.value.userId = newUserId;
      $q.notify({
        message: "LINEユーザーIDリセットが成功しました。",
      });
    } catch (error: any) {
      $q.notify({
        type: "negative",
        message: "システムエラーが発生しました。システム管理者にお問い合わせください。",
      });}
  });
};

const getCheckColor = (check: any): any => {
  return DATA_CHECK_COLOR[check];
};

const onUpdateSurveyData = async (): Promise<void> => {
  const formData = await inputFormRef.value.getFormDataJson(detailDataLocal.value.id);

  if (!formData) {
    $q.dialog({
      // type: "warning",
      title: "エラー",
      message: "入力内容を確認してください。",
      // hideBtnConfirm: true,
    });
  } else {
    if (!formData.surveyId) {
      formData.surveyId = detailDataLocal.value.partitionKey.split("#")[0];
    }
    
    formData.surveyResults.forEach(function (data: any, index: any) {
      if (getType(data.itemKey) === "linkbutton") {
        formData.surveyResults.splice(index, 1);
      }
      if (getType(data.itemKey) === "memberNumber") {
        if (!shudou.value.originalValue) {
          shudou.value.originalValue = data.value;
        }
        data.originalValue = shudou.value.originalValue;
      }
    });

    $q.dialog({
      // type: "confirm",
      title: "データの更新",
      message: "この入力内容でデータ更新してもよろしいですか？",
      ok: {
        label: '保存',
      }
  }).onOk(async () => {
      let surveyResult = {
        ...formData,
        partitionKey: detailDataLocal.value.partitionKey,
        originalData: detailDataLocal.value,
        surveyConfig: memberConfig.value,
        check: detailDataLocal.value.check,
      };

      let _result = await dataManagementStore.updateMemberSurvey(surveyResult);

      shudou.value.originalValue = null;

      //if value already updated, update current data
      if (typeof _result === "object") {
        $q.dialog({
          // type: "warning",
          title: "変更を保存できません",
          message: "編集していたデータは、他の人によって変更されました。\n もう一度編集する前に、データを更新してください。",
          ok: {
            label: 'データ更新',
          }
        }).onOk(async () => {
          show.value = false;
          await dataManagementStore.fetchDataOfSelectedMemberList();
          $q.notify({
            message: "最新データが更新されました。",
          });
        });
      }

      if (_result === true) {
        $q.notify({
          message: "データの保存が成功しました。",
        });
        await dataManagementStore.fetchDataOfSelectedMemberList();
        show.value = false;
      }
    });
  }
};

// vue-to-print
const { handlePrint } = useVueToPrint({
  content: inputFormRef,
  documentTitle: props.formData?.surveyTitle,
});


const onPrintSurveyData = (): void => {
  isLoadingPrint.value = true;
  handlePrint();
  setTimeout(() => {
    isLoadingPrint.value = false;
  }, 2000);
};

const getTitle = (key: any): any => {
  let _item = props.formData.surveySchema.find((obj: any) => obj.itemKey === key);
  return _item ? _item.title : "";
};

const getType = (key: any): any => {
  let _item = props.formData.surveySchema.find((obj: any) => obj.itemKey === key);
  return _item ? _item.type : "";
};

const handleSurveyResults = (): any => {
  const formData = inputFormRef.value.getFormData();
  if (!formData) {
    return null;
  }
  let surveyResults = [];
  for (var pair of formData.entries()) {
    let value = getType(pair[0]) === "date" && pair[1] === "null" ? null : pair[1];
    value = value === "null" ? null : value;
    if (value) {
      surveyResults.push({
        itemKey: pair[0],
        title: getTitle(pair[0]),
        value: value,
      });
    }
  }

  return {
    surveyId: props.formData.surveyId,
    surveyResults,
  };
};

const handleCheckSaibanExistingForUpdate = async (payload: any = { saibanString: "", exceptSaibanString: "" }): Promise<void> => {
  shudou.value.originalValue = payload.exceptSaibanString;
  if (payload.saibanString === "" || payload.exceptSaibanString === "") {
    isSaibanExisted.value = false;
    return;
  }

  let memberSurveyId = memberConfig.value.surveyId;
  let memberSurveySubId = memberConfig.value.memberSurveySubId;
  let params = {
    memberSurveyId,
    memberSurveySubId,
    ...payload,
  };
  let saibanExistResult = await dataManagementStore.checkSaibanExistForUpdate(params);
  if (saibanExistResult === null || saibanExistResult == undefined || saibanExistResult === false) {
    isSaibanExisted.value = true;
  } else {
    if (
      saibanExistResult.data &&
      (saibanExistResult.data.saibanExistFlag === true ||
        saibanExistResult.data.saibanExistFlag === null ||
        saibanExistResult.data.saibanExistFlag === undefined)
    ) {
      isSaibanExisted.value = true;
    } else {
      isSaibanExisted.value = false;
    }
  }
};

const handleCheckSaibanExisting = async (payload: any = { saibanString: "" }): Promise<void> => {
  if (payload.saibanString === "") {
    isSaibanExisted.value = false;
    return;
  }

  let memberSurveyId = memberConfig.value.surveyId;
  let memberSurveySubId = memberConfig.value.memberSurveySubId;
  let params = {
    memberSurveyId,
    memberSurveySubId,
    ...payload,
  };
  let saibanExistResult = await dataManagementStore.checkSaibanExist(params);
  if (saibanExistResult === null || saibanExistResult == undefined || saibanExistResult === false) {
    isSaibanExisted.value = true;
  } else {
    if (
      saibanExistResult.data &&
      (saibanExistResult.data.saibanExistFlag === true ||
        saibanExistResult.data.saibanExistFlag === null ||
        saibanExistResult.data.saibanExistFlag === undefined)
    ) {
      isSaibanExisted.value = true;
    } else {
      isSaibanExisted.value = false;
    }
  }
};


const onSurveyConfigSelected = (config: any): void => {
  linkedSurveyConfig.value = config;
};
</script>

<style lang="less">
.selectCaldndarValue {
  display: inline;
  font-size: 17px;
}
@media print {
  .print-applicant-details-item {
    display: block !important;
  }
  .not-print-applicant-details-item {
    display: none !important;
  }
  @page {
    size: A4;
    margin: 20mm 0 10mm 0;
  }
  html,
  body {
    margin: 0;
    padding: 0;
    width: 210mm; // A4 is 210mm
    height: 0mm; // A4 is 297mm
    overflow: visible;
  }
  body > #app {
    display: none;
  }
  #print-applicant-details-section {
    max-height: initial !important;
  }
  .v-form {
    break-inside: always !important;
  }
  .v-form > .row {
    break-before: always !important;
    break-inside: avoid !important;
    break-after: always !important;
  }
}
</style>
