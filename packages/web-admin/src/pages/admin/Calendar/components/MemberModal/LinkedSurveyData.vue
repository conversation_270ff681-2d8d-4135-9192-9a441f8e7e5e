<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div class="tw-py-2">
    <div class="row tw-flat tw-items-center tw-my-4">
      <div class="tw-py-2 tw-px-4 col-auto">
        <label>帳票</label>
      </div>
      <div class="col-9">
        <q-select 
          v-model="selectedSurvey" 
          :options="surveyOptions" 
          option-label="text"
          option-value="value"
          outlined 
          dense
          hide-details>
        </q-select>
      </div>
      <div class="col-auto tw-mx-2">
        <q-btn flat round :ripple="false" @click="handleFetchSurveyConfigs()">
          <q-icon color="primary" :name="isFetchingFormConfigs ? 'mdi-cached mdi-spin' : 'mdi-cached'"></q-icon>
        </q-btn>
      </div>
    </div>
    <div class="tw-py-6">
      <div style="max-height:30vh; overflow-y:auto" class="tw-w-full tw-pb-2">
        <q-table
          :columns="surveyResultHeaders"
          :rows="linkedSurveyResults"
          :loading="isFetchingSurveyResultsItems"
          item-key="partitionKey"
          class="tw-mx-4"
          @row-click="onSurveyResultClicked"
        >
          <template v-slot:body-cell="cell">
            <q-td :props="cell">
              <div v-if="cell.col.field === 'check'">
                <q-chip :color="getCheckColor(cell.row.check)" class="status text-center full-width" dark small>
                  <div class="full-width">
                    {{ cell.row.check ? cell.row.check : "未対応" }}
                  </div>
                </q-chip>
              </div>
              <div v-else-if="cell.col.field === 'memberType'">
                <span>{{ cell.row.memberType }}</span>
              </div>
              <div v-else-if="cell.row.fields?.find(field => field.itemKey === cell.col.field)">
                {{ cell.row.fields.find(field => field.itemKey === cell.col.field).value }}
              </div>
              <div v-else-if="cell.col.field === 'bunruiIdFull'">
                <span>{{ cell.row.bunrui_id }}</span>
              </div>
              <div v-else-if="cell.col.field === 'tag1'">
                <span>{{ cell.row.tag1 }}</span>  
              </div>
              <div v-else-if="cell.col.field === 'tag2'">
                <span>{{ cell.row.tag2 }}</span>
              </div>
              <div v-else-if="cell.col.field === 'tag3'">
                <span>{{ cell.row.tag3 }}</span>
              </div>
              <div v-else-if="cell.col.field === 'reservationItem'">
                <span>{{ cell.row.reservationItem }}</span>
              </div>
              <div v-else-if="cell.col.field === 'reservationDate'">
                <span>{{ cell.row.reservationDate }}</span>
              </div>
              <div v-else-if="cell.col.field === 'createdAt'">
                <span>{{ formatUnixToYYYYMMDHHmmss(cell.row.createdAt) }}</span>
              </div>
              <div v-else-if="cell.col.field === 'updatedAt'">
                <span>{{ formatUnixToYYYYMMDHHmmss(cell.row.updatedAt) }}</span>
              </div>
              <div v-else-if="cell.col.field === 'answerCode'">
                <span>{{ cell.row.answerCode }}</span>
              </div>
            </q-td>
          </template>
        </q-table>
      </div>
    </div>

    <DataModal
      :formData="selectedSurveyConfig"
      :detailData="selectedSurveyResult"
      :visible="showDetailModal"
      :isAppending="isAppendingSurvey"
      :isMemberResults="true"
      @close="showDetailModal = false"
      @onOpenLineUnicastModal="onOpenLineUnicastModal"
      @onSurveyDataUpdated="onSurveyDataUpdated"
    />

    <LineUnicast
      :formData="selectedSurveyConfig"
      :detailData="selectedSurveyResult"
      :visible="showLineUnicastModal"
      @close="showLineUnicastModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { SURVEY_RESULTS_CHECK, DEFAULT_RESULTS_CHECK_COLOR } from "@/stores/modules/surveyResults/surveyResults.constants";
import {
  formatSurveyResultData,
  makeSurveyResultsListHeaders,
  useSurveyResultsStore
} from "@/stores/modules/surveyResults";
import { useDatetimeFormatter } from "@/mixins/DatetimeFormatter";
import { useFormsStore } from "@/stores/modules/forms";
import { useMemberStore } from "@/stores/modules/member";
import { useQuasar } from "quasar";

// components
import DataModal from "@/pages/admin/Applicants/components/DataModal/index.vue";
import LineUnicast from "@/pages/admin/Applicants/components/DataModal/LineUnicast.vue";

// quasar
const $q = useQuasar();

// store
const formsStore = useFormsStore();
const surveyResultsStore = useSurveyResultsStore();
const memberStore = useMemberStore();

const { formatUnixToYYYYMMDHHmmss } = useDatetimeFormatter();

// props emit
const props = defineProps<{
  linkedSurveyConfigIds: any[],
  userId: string,
}>();

const emit = defineEmits(['onSurveyConfigSelected']);

// data
const selectedSurvey = ref();
const linkedSurveyResults = ref([]);
const selectedSurveyResult = ref({});
const showDetailModal = ref(false);
const showLineUnicastModal = ref(false);

// computed
const surveyConfigs = computed(() => formsStore.surveyConfigsList);
const isFetchingFormConfigs = computed(() => formsStore.isFetchingFormConfigs);
const fetchFormConfigsError = computed(() => formsStore.fetchFormConfigsError);
const isFetchingSurveyResultsItems = computed(() => surveyResultsStore.isFetchingSurveyResultsItems);
const allCalendars = computed(() => surveyResultsStore.allCalendars);
const allCalendarCategories = computed(() => surveyResultsStore.allCalendarCategories);

const surveyOptions = computed((): any => {
  // Only consider linked survey configs
  const filteredConfigs = surveyConfigs.value.filter((config: any) => {
    return props.linkedSurveyConfigIds.includes(config.surveyId);
  });

  let options = filteredConfigs.map((obj: any) => {
    return {
      value: obj.surveyId,
      text:
        obj.surveyTitle !== undefined && obj.surveyTitle.length <= 50
          ? obj.surveyTitle
          : obj.surveyTitle.substring(0, 50).concat("..."),
    };
  });

  return options;
});

const selectedSurveyConfig = computed((): any => {
  return surveyConfigs.value.find((config: any) => {
    return config.surveyId === selectedSurvey.value?.value;
  });
});

const isAppendingSurvey = computed((): any => {
  let surveyConfig = surveyConfigs.value.find((config: any) => {
    return config.surveyId === selectedSurvey.value;
  });

  return surveyConfig && surveyConfig.isAppending && surveyConfig.isAppending.value;
});

const surveyResultHeaders = computed((): Array<any> => {
  let surveyHeaders = [];

  if (selectedSurveyConfig.value) {
    surveyHeaders = makeSurveyResultsListHeaders(
      surveyResultsStore,
      memberStore,
      selectedSurveyConfig.value,
      true
    );
  }

  return surveyHeaders;
});

// watch
watch(
  () => selectedSurvey.value,
  async (newVal) => {
    if (newVal) {
      // Fetch survey results linked to this form / user
      await handleFetchLinkedSurveyResults({
        surveyFormId: newVal.value,
        userId: props.userId,
      });
    } else {
      linkedSurveyResults.value = [];
    }
  }
);

watch(
  () => selectedSurveyConfig.value,
  (newVal) => {
    emit("onSurveyConfigSelected", newVal);
  }
);

// mounted
onMounted(async () => {
  if (surveyConfigs.value.length === 0) {
    await formsStore.fetchFormConfigs();
  }

  selectedSurvey.value = surveyOptions.value[0];

  if (allCalendars.value.length === 0 || allCalendarCategories.value.length === 0) {
    surveyResultsStore.fetchCalendarInfo();
  }
});

// methods
const handleFetchSurveyConfigs = async (): Promise<void> => {
  await formsStore.fetchFormConfigs();
  if (fetchFormConfigsError.value) {
    $q.notify({
      type: "error",
      message: fetchFormConfigsError.value,
    });
  } else {
    $q.notify({
      message: "帳票リストを更新しました。",
    });
  }
};

const handleFetchLinkedSurveyResults = async (payload: any): Promise<void> => {
  const groupedSurveyResults = await surveyResultsStore.fetchSurveyResultsByFormUserId(payload);

  if(surveyResultsStore.allCalendarCategories.length === 0) {
    await surveyResultsStore.fetchCalendarInfo();
  }

  if (groupedSurveyResults.length > 0) {
    linkedSurveyResults.value = groupedSurveyResults.filter((result: any) => {
      return (result.check !== '取り消し');
    }).map((result: any) => {
      result["memberType"] = props.userId[0] == "U" ? "LINE" : "WEB";
      
      return formatSurveyResultData(
        result,
        surveyResultHeaders.value,
        surveyResultsStore,
        selectedSurveyConfig.value.surveySchema
      );
    });
  } else {
    linkedSurveyResults.value = [];
  }
};

const onSurveyResultClicked = (_, row: any): void => {
  selectedSurveyResult.value = row;
  showDetailModal.value = true;
};

const getCheckColor = (value: any): string => {
  const list = SURVEY_RESULTS_CHECK;
  const find = list.find((obj) => obj.text === value);
  return find ? find.color : DEFAULT_RESULTS_CHECK_COLOR;
};

const onOpenLineUnicastModal = (): void => {
  showLineUnicastModal.value = true;
};

const onSurveyDataUpdated = async (): Promise<void> => {
  await handleFetchLinkedSurveyResults({
    surveyFormId: selectedSurvey.value.value,
    userId: props.userId,
  });
};
</script>
