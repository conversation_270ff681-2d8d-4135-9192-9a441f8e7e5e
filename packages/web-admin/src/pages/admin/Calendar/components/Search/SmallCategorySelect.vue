<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-select 
      use-input
      v-model="compSelectedSmallCategory"
      :options="options"
      option-label="name"
      option-value="id"
      outlined
      dense
      hide-details
      :placeholder="placeholder"
      :disable="compIsSmallCategoryDisabled"
      @filter="filterFn"
      @input-value="inputFn"
      fill-input
      hide-selected
      clearable
      clear-icon="close"
    >
    </q-select>
  </div>
</template>

<script setup lang="ts">
import { useCalenderStore } from '@/stores/modules/calendar';
import { computed, onMounted, ref } from 'vue';
import { isEmpty } from "lodash";

// store
const calendarStore = useCalenderStore();

// data
const options = ref();

// computed
const msCalendarDisplayTag3 = computed(() =>
  calendarStore.categoriesTree &&
  calendarStore.categoriesTree.display &&
  calendarStore.categoriesTree.display.tag3
    ? calendarStore.categoriesTree.display.tag3
    : null
);

const msCategoriesTreeSmall = computed(() => calendarStore.categoriesTreeSmall);
const msSelectedSmallCategory = computed(() => calendarStore.selectedSmallCategory);

const compSelectedSmallCategory = computed({
  get(): any {
    return msSelectedSmallCategory.value ? msSelectedSmallCategory.value : null;
  },
  set(newValue: any): void {
    calendarStore.actionSetCalendarSearchSelectedSmallCategory(newValue);
  },
});

const compIsSmallCategoryDisabled = computed((): boolean => {
  return msCategoriesTreeSmall.value && !isEmpty(msCategoriesTreeSmall.value) ? false : true;
});

const placeholder = computed((): string => {
  return msCalendarDisplayTag3.value ? msCalendarDisplayTag3.value + "を選択" : "ーー";
});

// methods
const filterFn = (value: string, update: any) => {
  if (value === '') {
    update(() => {
      options.value = msCategoriesTreeSmall.value;
    });
    return;
  }
  else {
    update(() => {
      value = value.toLowerCase();
      options.value = msCategoriesTreeSmall.value.filter((elm: any) => 
        elm.name.toLowerCase().indexOf(value) > -1
      );
    });
  }
};

const inputFn = (val: any) => {
  if (!val) {
    compSelectedSmallCategory.value = val;
  }
};

// hooks
onMounted(() => {
  options.value = msCategoriesTreeSmall.value;
});
</script>