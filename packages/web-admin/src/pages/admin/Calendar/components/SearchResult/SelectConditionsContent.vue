<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div class="caption font-italic">
    {{ getTagName }}を選択し、検索をクリックしてください。<br />
    スケジュールをまとめて登録する場合は、一括登録をクリックしてください。
  </div>
</template>

<script setup lang="ts">
import { computed,  } from 'vue';



// old imports
// 旧インポート
/**/


// computed
const msCalendarDisplayTag1 = computed((state: any) => state.calendar.categoriesTree &&
        state.calendar.categoriesTree.display &&
        state.calendar.categoriesTree.display.tag1
          ? state.calendar.categoriesTree.display.tag1
          : null);
const msCalendarDisplayTag2 = computed((state: any) => state.calendar.categoriesTree &&
        state.calendar.categoriesTree.display &&
        state.calendar.categoriesTree.display.tag2
          ? state.calendar.categoriesTree.display.tag2
          : null);
const msCalendarDisplayTag3 = computed((state: any) => state.calendar.categoriesTree &&
        state.calendar.categoriesTree.display &&
        state.calendar.categoriesTree.display.tag3
          ? state.calendar.categoriesTree.display.tag3
          : null);
const getTagName = computed((): any => {
      let tagName = msCalendarDisplayTag1.value ? msCalendarDisplayTag1.value : "";
      tagName += tagName && msCalendarDisplayTag2.value ? "、" + msCalendarDisplayTag2.value : "";
      tagName += tagName && msCalendarDisplayTag3.value ? "、" + msCalendarDisplayTag3.value : "";
      return tagName;
    });
</script>
