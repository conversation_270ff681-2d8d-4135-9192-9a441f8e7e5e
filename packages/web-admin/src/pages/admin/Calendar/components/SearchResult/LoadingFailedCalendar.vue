<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-page-container>
    <div class="row">
      <div class="text-center col">
        <q-icon :color="color" class="tw-mb-2" name="mdi-alert-circle" size="sm"></q-icon>
        <div class="title">エラーが発生しました。</div>
        <div class="body-2 tw-mt-3" v-bind:style="{ color: color }">
          <span v-html="errorMsg()"></span>
        </div>
      </div>
    </div>
  </q-page-container>
</template>

<script setup lang="ts">
import { useCalenderStore } from '@/stores/modules/calendar';
import { computed } from 'vue';

// store
const calendarStore = useCalenderStore();

// props
defineProps<{
  error?: any,
  color: string,
}>();

// methods
const errorMsg = (): string => {
  if (String(isLoadingFailedCalendar.value).includes("'comaList' of null")) {
    return "カレンダーが存在しません。";
  }
  return "ページの再読み込み実施を実施してください。それでも表示されない場合は、<br/>サーバへアクセスが混み合っているか、メンテナンス中の可能性があります。<br/>しばらく時間をおいてから、もう一度アクセスしてください。<br/>";
};

// computed
const isLoadingFailedCalendar = computed((): any => {
  return calendarStore.fetchScheduleError;
});

</script>