<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div :style="cssProps">
    <div>
      <div class="row tw-items-center">
        <div class="col-3 tw-pl-4">
          <div class="row">
            <q-btn
              color="blue-grey settigns-btn"
              @click="onCalendarSettingsClick"
            >
              <!-- TODO: 上に追加(q-btn) - honda
              :style="permissionHelper.hasActionPermission('hideButton', 'Calendar_Index_CalendarSettings') ? permissionHelper.hideButtonPermissionStyle() : ''" 
              -->
              <q-icon left name="mdi-cog-outline"></q-icon>
              詳細設定
            </q-btn>
          </div>
        </div>
        <div class="col-6">
          <div class="row tw-items-center">
            <div class="col-3 text-grey-8">
              <q-btn flat class="float-left settigns-btn" :ripple="false" v-on:click="prevWeek">
                <q-icon size="sm" class="tw-mr-1" name="mdi-chevron-left"></q-icon>前の週
              </q-btn>
            </div>
            <div class="text-center col-6">
              <div style="font-weight:bold;font-size:16px;">{{ targetDates[0]?.year }}</div>
              <div class="targetweek">{{ targetDates[0]?.displayDate }} - {{ targetDates[6]?.displayDate }}</div>
            </div>
            <q-col class="col-3 text-grey-8">
              <q-btn flat class="float-right settigns-btn" :ripple="false" v-on:click="nextWeek">
                次の週<q-icon size="sm" class="tw-ml-1" name="mdi-chevron-right"></q-icon>
              </q-btn>
            </q-col>
          </div>
        </div>
        <div class="col-3" align-self="center">
          <div class="row">
            <div class="text-right tw-pr-0 col-12">
              <q-btn 
                :color="isCalendarOff ? 'primary' : 'blue-grey'" 
                @click="onCalendarOffClick" 
                class="settigns-btn white--text tw-mx-1"
              >
                <q-icon medium left :name="calendarOffIcon"></q-icon>
                {{ calendarOffLabel }}
              </q-btn>
              <q-btn v-if="!editScheduleMode" color="primary" dark v-on:click="startChangeData" class="settigns-btn tw-mx-1 tw-my-1">
                編集
              </q-btn>
              <q-btn v-else color="primary" dark v-on:click="saveChangedData" class="settigns-btn">
                <q-icon medium class="ml-1" name="mdi-checkbox-marked-circle-outline"></q-icon> 保存
              </q-btn>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12">
          <q-card class="tw-p-3" flat>
            <div class="sticky-table-wrapper">
              <table>
                <thead>
                  <tr>
                    <th class="text-center data-head pt-3 pb-3">開始</th>
                    <th class="text-center data-head pt-3 pb-3">終了</th>
                    <th
                      v-for="targetDate in targetDates"
                      :key="targetDate.day"
                      class="text-center data-cell pt-3 pb-3"
                      :class="getClassForDayOfWeek(targetDate)"
                      :style="editScheduleMode? 'height:100px;' : ''"
                    >
                      <span>{{ targetDate.youbi }}</span><br />
                      {{ getTextForDayOfWeek(targetDate) }}<br />
                      <q-btn
                        class="tw-my-2 tw-px-3"
                        :color="isDayOff(targetDate)?'secondary':'blue-grey'"
                        :class="shouldDayOffActivate(targetDate) ?'':'hide-dayoff'"
                        outline
                        size="sm"
                        dense
                        @click="onChangeDayOff(targetDate)"
                      >
                        <span v-if="isDayOff(targetDate)">営業日に変更</span>
                        <span v-else>休日に変更</span>
                      </q-btn>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-if="newDatas.length === 0">
                    <td class="text-center" colspan="9">
                      <p>この週のスケジュール情報がありません。ファイルをインポートして下さい。</p>
                    </td>
                  </tr>
                  <tr v-for="item in newDatas" :key="Object.keys(item.timezone)[0]">
                    <td class="text-center data-head">
                      {{ (Object.values(item.timezone)[0] as any).start }}
                    </td>
                    <td class="data-head">{{ (Object.values(item.timezone)[0] as any).end }}</td>
                    <td
                      v-for="elem in item.data"
                      :key="elem.id"
                      :colspan="getTdColspan(elem)"
                      :rowspan="getTdRowspan(elem)"
                      :valign="getTdValign(elem)"
                      :class="getTdClass(elem)"
                      v-on:click="selectTime(elem)"
                    >
                      <p v-if="isShowNonData(elem)">{{ scheduleNotExistLbl }}</p>
                      <q-btn
                        v-else-if="isShowChangeComa(elem)"
                        color="primary"
                        dark
                        v-on:click="comaOnChange"
                      >
                        <q-icon medium class="ml-1" name="mdi-refresh"></q-icon> 表示
                      </q-btn>
                      <div v-else-if="isShowData(elem)">
                        <q-input
                          v-if="isShowTdText(elem)"
                          class="mx-auto"
                          type="number"
                          :min="elem.resValue"
                          max="9999"
                          v-model="elem.value"
                          single-line
                          hide-details
                          dense
                          @change="onChangeValue(elem)"
                        >
                        </q-input>
                        <p v-else :class="getTdPTagClass(item)">
                          {{ elem.resValue }}/{{ elem.value }}
                        </p>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </q-card>
        </div>
      </div>
      <div class="row tw-flex tw-justify-between tw-pt-3">
        <div>
          <q-btn
            color="primary"
            class="tw-mx-3"
            @click="onCollectiveRegistration"
          >
            <q-icon left name="mdi-import"></q-icon>
            CSVインポート
          </q-btn>
          <q-btn
            color="primary"
            class="tw-mx-3"
            outline
            @click="onExportCalendarCsv"
          >
            <q-icon left name="mdi-export"></q-icon>
            CSVエクスポート
          </q-btn>
        </div>
        <q-btn color="negative" dark outline v-on:click="onShowDeleteCalendarDialog">
          <q-icon left name="mdi-trash-can-outline"></q-icon> カレンダーを削除
        </q-btn>
      </div>
    </div>
    <SelectPeriodDialog :visible="showSelectPeriodDialog" @close="showSelectPeriodDialog = false" />
    <DeleteCalendarDialog :visible="showDeleteCalendarDialog" @close="showDeleteCalendarDialog = false" @onConfirm="onDelete"/>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeMount } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { CALENDAR_DISPLAY_MODE } from "@/stores/modules/calendar/calendar.constants";
import { cloneDeep, get } from "lodash";
import { useCalenderStore } from '@/stores/modules/calendar';
import { useSettingsStore } from '@/stores/modules/settings';
import { pSBC } from "@/utils/colorUtils";
import { SHOW_CONTENT_MODE } from "@/stores/modules/calendar/calendar.constants";
import { usePermissionHelper } from '@/mixins/PermissionHelper';
import SelectPeriodDialog from "@/pages/admin/Calendar/components/ExportCsv/SelectPeriodDialog.vue";
import DeleteCalendarDialog from "@/pages/admin/Calendar/components/DeleteCalendar/DeleteCalendarDialog.vue";

const router = useRouter();
const $q = useQuasar();

// store
const calendarStore = useCalenderStore();
const settingsStore = useSettingsStore();

const permissionHelper = usePermissionHelper();

// emits
const emits = defineEmits(["onShowImportCsvContent"]);

// props
const props = defineProps<{
	canSave?: boolean,
}>();

interface Data {
  data: any;
  timezone: any;
}

// data
const selectedDayOfWeek = ref<any>(null);
const selectId = ref<any>(null);
const textSections = ref<any>(["datachange"]);
const editList = ref<any>([]);
const chartKey = ref<number>(0);
const allDatas = ref<any>([]);
const newDatas = ref<Data[]>([]);
const comaMode = ref<boolean>(false);
const isComa = ref<number>(0);
const primaryColor = ref<string>("#07B53B");
const secondaryColor = ref<string>("#3B9EF4");
const accentColor = ref<string>("#FFEB3B");
const warningColor = ref<string>("#B71C1C");
const saveConfirmTitle = ref<string>("このページを離れてもよろしいですか？");
const saveConfirmText = ref<string>("行った変更は破棄されます。");
const saveBtnConfirmTitle = ref<string>("このページを離れる");
const scheduleNotExistLbl = ref<string>("スケジュール情報がありません。");
const showSelectPeriodDialog = ref<boolean>(false);
const showDeleteCalendarDialog = ref<boolean>(false);

// computed
const calendarsList = computed(() => calendarStore.calendarsList);
const commonSettings = computed(() => settingsStore.commonSettings);
const selectedDateStart = computed(() => calendarStore.selectedStartDay);
const selectedDateEnd = computed(() => calendarStore.selectedEndDay);
const calendarSchedules = computed(() => calendarStore.calendarSchedules);
const targetDates = computed(() => calendarStore.targetDates);
const startDate = computed(() => calendarStore.startDate);
const datas = computed(() => calendarStore.datas);
const timezones = computed(() => calendarStore.timezones);
const dayOff = computed(() => calendarStore.dayOff);
const editScheduleMode = computed(() => calendarStore.editScheduleMode);
const isFetchingScheduleData = computed(() => calendarStore.isFetchingScheduleData);
const isDeletedCalendar = computed(() => calendarStore.isDeletedCalendar);
const displayingCategoryId = computed(() => calendarStore.displayingCategoryId);
const displayingCategory = computed(() => calendarStore.displayingCategory);
const msSelectedLargeCategory = computed(() => calendarStore.selectedLargeCategory);
const msSelectedMediumCategory = computed(() => calendarStore.selectedMediumCategory);
const msSelectedSmallCategory = computed(() => calendarStore.selectedSmallCategory);

const cssProps = computed((): any => {
  return {
    "--primary-color": primaryColor.value,
    "--primary-light-color": pSBC(0.8, primaryColor.value, false, true),
    "--primary-dark-color": pSBC(-0.6, primaryColor.value, false, true),
    "--secondary-color": secondaryColor.value,
    "--secondary-light-color": pSBC(0.8, secondaryColor.value, false, true),
    "--secondary-dark-color": pSBC(-0.6, secondaryColor.value, false, true),
    "--accent-color": accentColor.value,
    "--accent-light-color": pSBC(0.8, accentColor.value, false, true),
    "--accent-dark-color": pSBC(-0.6, accentColor.value, false, true),
    "--warning-color": warningColor.value,
    "--warning-light-color": pSBC(0.8, warningColor.value, false, true),
    "--warning-dark-color": pSBC(-0.6, warningColor.value, false, true),
  };
});

const isCalendarOff = computed((): boolean => {
  let findCalendar = (!displayingCategory.value || !displayingCategory.value.calendarId) 
    ? false
    : calendarsList.value?.find((o: any) => o.sortKey == displayingCategory.value.calendarId);
  return !!findCalendar && findCalendar.calendarOff === 1;
});

const calendarOffIcon = computed((): string => {
  return isCalendarOff.value ? "mdi-play-circle-outline" : "mdi-pause-circle-outline";
});

const calendarOffLabel = computed((): string => {
  return isCalendarOff.value ? "予約を再開" : "予約を停止";
});

// watch
watch(
  () => calendarsList.value, 
  () => {
    otherWeek(0);
  },
  { deep: true }
);

watch(
  () => props.canSave, 
  (newVal) => {
    if (newVal) {
      for (const section of textSections.value) {
        // TODO:isValidText どこから？ - honda
        // isValidText[section] = true; // reset back to true
      }
    }
  }
);

watch(
  () => isFetchingScheduleData.value,
  (newVal) => {
      if (!newVal) {
        chartKey.value++;
      }
    }
);

watch(
  () => datas.value,
  (newVal) => {
    allDatas.value = cloneDeep(newVal);
    newDatas.value = cloneDeep(allDatas.value[0]);
  }
);

watch(
  () => timezones.value,
  (newVal) => {
    comaMode.value = cloneDeep(newVal).length > 1;
    isComa.value = 0;
  }
);

watch(
  () => isFetchingScheduleData.value,
  (newVal) => {
    if (newVal) { $q.loading.show(); }
    else { $q.loading.hide(); }
  },
);

// methods
const isShowNonData = (item: any): boolean => {
  return item.displayMode === CALENDAR_DISPLAY_MODE.NON_DATA;
};

const isShowChangeComa = (item: any): boolean => {
  return item.displayMode === CALENDAR_DISPLAY_MODE.SHOW_CHANGE_COMA;
};

const isShowData = (item: any): boolean => {
  return item.displayMode === CALENDAR_DISPLAY_MODE.HAS_DATA;
};

const hasReservationFrame = (item: any): boolean => {
  return item.value !== 0;
};

const isFullBooked = (item: any): boolean => {
  return (hasReservationFrame(item) && (item.value == item.resValue));
};

const shouldDayOffActivate = (targetDate: any) => {
  const firstSlotInDatas = newDatas.value[0]?.data;
  if (!Array.isArray(firstSlotInDatas)) {
    return false;
  }
  const matchedInDatas = firstSlotInDatas.find((data) => {
    return data.fullDate == targetDate.fullDate;
  });
  if (!matchedInDatas) {
    return false;
  }
  if (isShowNonData(matchedInDatas) || isShowChangeComa(matchedInDatas)) {
    return false;
  }
  return true;
};

const getTdColspan = (item: any): any => {
  if (isShowNonData(item)) {
    return item.nonData.colSpan;
  } else if (isShowChangeComa(item)) {
    return item.changeComa.colSpan;
  }
  return 1;
};

const getTdRowspan = (item: any): any => {
  if (isShowNonData(item)) {
    return item.nonData.rowSpan;
  } else if (isShowChangeComa(item)) {
    return item.changeComa.rowSpan;
  }
  return 1;
};

const getTdValign = (item: any): "middle" | "top" => {
  return isShowData(item) ? "middle" : "top";
};

const getTdClass = (item: any): any => {
  if (isShowData(item)) {
    const cls = "text-center data-cell pt-3 pb-3";
    const isSelect = selectId.value === item.id;
    const isSelectInEditScheduleMode = isSelect && editScheduleMode.value;
    let dayCls = {
      onlyView: !editScheduleMode.value,
      active: item.active,
      inactive: !item.active,
      selected: isSelectInEditScheduleMode,
      fullBooked: isFullBooked(item),
      noReservationFrame: !hasReservationFrame(item),
      "day-off": !isSelectInEditScheduleMode && isDayOff(item),
    };
    return [dayCls, cls];
  } else {
    return "text-center non-data";
  }
};

const selectTime = (item: any): void => {
  if (isShowData(item)) {
    if (item.value >= 0) {
      selectId.value = item.id;
    }
  }
};

const isShowTdText = (item: any): boolean => {
  return editScheduleMode.value && selectId.value === item.id;
};

const getTdPTagClass = (item: any): any => {
  return { "full-title": editScheduleMode.value && item.active && selectId.value != item.id && item.value === 9999 };
};

const isDayOff = (targetDate: any): boolean => {
  let findDayOff = dayOff.value[targetDate.fullDate];
  return findDayOff === 1;
};

const getClassForDayOfWeek = (targetDate: any): Array<string> => {
  const classes: string[] = [];
  if (targetDate.saturday) {
    classes.push("saturday");
  }
  if (targetDate.sunday) {
    classes.push("sunday");
  }
  if (isDayOff(targetDate)) {
    classes.push("day-off");
  }
  return classes;
};

const getTextForDayOfWeek = (targetDate: any): string => {
  let lbl = targetDate.displayDate;
  if (isDayOff(targetDate)) {
    lbl += " (休)";
  }
  return lbl;
};

const onChangeDayOff = (targetDate: any): void => {
  selectedDayOfWeek.value = targetDate;
  let chkIsDayOff = isDayOff(selectedDayOfWeek.value);
  $q.dialog({
    // type: "confirm",
    title: chkIsDayOff ? "営業日に変更" : "休日に変更",
    message: chkIsDayOff ? "営業日に変更してよろしいですか？" : "休日に変更してよろしいですか？",
    ok: {
      label: 'データ更新',
    },
  }).onOk(async () => {
    await changeDayOff();
  });
};

const changeDayOff = async (): Promise<void> => {
  let chkIsDayOff = isDayOff(selectedDayOfWeek.value);
  let successMsg = chkIsDayOff ? "営業日に変更しました。" : "休日に変更しました。";
  let errMsg = chkIsDayOff ? "営業日に変更できませんでした。" : "休日に変更できませんでした。";
  let payload = {
    calendarId: displayingCategory.value.calendarId,
    dt: selectedDayOfWeek.value.fullDate,
    dayOff: chkIsDayOff ? 0 : 1,
  };
  try {
    await calendarStore.actionUpdateDayOff(payload);
    reloadData();
    $q.notify({ message: successMsg, color: "positive", icon: "mdi-check" });
  } catch (error: any) {
    if (error.response && error.response.data?.code === 400) {
      $q.notify({ message: errMsg, type: "negative", icon: "mdi-alert-circle-outline" });
    } else if(error.response && error.response.data?.code === 403) {
      $q.notify({ 
        message: "この操作を行う場合は、権限を管理者にお問い合わせください", 
        type: "negative",
        icon: "mdi-alert-circle-outline",
      });
    } else {
      $q.notify({
        type: "negative",
        message: "システムエラーが発生しました。システム管理者にお問い合わせください。",
        icon: "mdi-alert-circle-outline",
      });
    }
  }
};

const reloadData = async (): Promise<void> => {
  let reload = {
    start_day: selectedDateStart.value,
    end_day: selectedDateEnd.value,
    id: displayingCategoryId.value,
  };
  await calendarStore.actionSetCalendarSchedulesResults(reload);
};

const onResetSelectedDayOfWeek = (): void => {
  selectedDayOfWeek.value = null;
};

const onChangeValue = (keyValue: any): void => {
  if (editList.value?.find((elem) => elem.fullDate === keyValue.fullDate && elem.id === keyValue.id)) {
    editList.value.map((elem) => {
      if (elem.fullDate === keyValue.fullDate && elem.id === keyValue.id) {
        elem.value = keyValue.value;
      }
    });
  }
  else {
    editList.value.push(keyValue);
  }
};

const startChangeData = (): void => {
  calendarStore.actionSetCalendarEditMode(true);
  editList.value = [];
};

const saveChangedData = (): void => {
  $q.dialog({
    // type: "confirm",
    title: "データ保存",
    message: "変更した内容で保存しますか？",
    ok: {
      label: '保存',
    },
  }).onOk(async () => {
    savedData();
  });
};

const savedData = async (): Promise<void> => {
  calendarStore.actionSetCalendarEditMode(false);
  if (editList.value.length > 0) {
    const payloadArray = createPayloadArray();
    const payload = {
      bodyInfo: payloadArray,
      id: displayingCategoryId.value,
    };
    await calendarStore.actionSetUpdatedCalendarSchedules(payload);
    if (calendarSchedules.value.length > 0) {
      if (calendarSchedules.value[0] === 403) {
        $q.notify({ message: "この操作を行う場合は、権限を管理者にお問い合わせください", type: "negative" });
      } else {
        $q.notify({ message: "予約日時を編集できませんでした。" + calendarSchedules.value, type: "negative" });
      }
    } else {
      $q.notify({ message: "予約日時を編集しました。", color: "positive" });
    }
    const reload = {
      start_day: selectedDateStart.value,
      end_day: selectedDateEnd.value,
      id: displayingCategoryId.value,
    };
    await calendarStore.actionSetCalendarSchedulesResults(reload);
  }
};

const createPayloadArray = (): any => {
  const _coma = isComa.value;
  let payloadArray = [];
  for (let x = 0; x < 7; x++) {
    if (editList.value.find((elem) => elem.date === targetDates.value[x].date)) {
      const tempData = {
        slots: {},
      } as any;
      tempData["date"] = targetDates.value[x].fullDate;
      for (let y = 0; y < timezones.value[_coma].length; y++) {
        const timeSlot = Object.keys(timezones.value[_coma][y])[0];
        const _data = newDatas.value[y].data.find((obj: any) => obj.date === targetDates.value[x].date);
        const _check = checkInputNumber(_data.value);
        if (!_check) {
          return $q.notify({
            message: `${targetDates.value[x].date}の${(Object.values(timezones.value[_coma][y]) as any)[0].start}~${
              (Object.values(timezones.value[_coma][y]) as any)[0].end
            }に予約枠は半角数字のみ入力できます。`,
          });
        }
        tempData.slots[timeSlot] = parseInt(_data.value);
      }
      payloadArray.push(tempData);
    }
  }
  return payloadArray;
};

const checkInputNumber = (value: any): boolean => {
  let regex = /^[0-9]+$/;
  if (String(value) && String(value).match(regex)) {
    return true;
  } else {
    return false;
  }
};

const comaOnChange = (): any => {
  if (editScheduleMode.value) {
    $q.dialog({
      // type: "confirm",
      title: saveConfirmTitle.value,
      message: saveConfirmText.value,
      ok: {
        label: saveBtnConfirmTitle.value,
      },
    }).onOk(async () => {
      calendarStore.actionSetCalendarEditMode(false);
      comaChanged();
    });
  } else {
    return comaChanged();
  }
};

const comaChanged = (): void => {
  if (isComa.value === 0) {
    newDatas.value = cloneDeep(allDatas.value[1]);
    isComa.value = 1;
  } else {
    newDatas.value = cloneDeep(allDatas.value[0]);
    isComa.value = 0;
  }
};

const prevWeek = (): void => {
  if (editScheduleMode.value) {
    $q.dialog({
      // type: "confirm",
      title: saveConfirmTitle.value,
      message: saveConfirmText.value,
      ok: {
        label: saveBtnConfirmTitle.value,
      },
    }).onOk(async () => {
      calendarStore.actionSetCalendarEditMode(false);
      otherWeek(-6);
    });
  } else {
    otherWeek(-6);
  }
  onResetSelectedDayOfWeek();
};

const nextWeek = (): void => {
  if (editScheduleMode.value) {
    $q.dialog({
      // type: "confirm",
      title: saveConfirmTitle.value,
      message: saveConfirmText.value,
      ok: {
        label: saveBtnConfirmTitle.value,
      },
    }).onOk(async () => {
      calendarStore.actionSetCalendarEditMode(false);
      otherWeek(7);
    });
  } else {
    otherWeek(7);
  }
  onResetSelectedDayOfWeek();
};

const otherWeek = async (value: any): Promise<void> => {
  let wkDate = startDate.value;
  await calendarStore.actionSetCalendarInfo(displayingCategory.value.id);
  calendarStore.actionSetCalendarTargetDate({
    date: wkDate ? new Date(wkDate.setDate(wkDate.getDate() + value)) : null,
  });
  let payload = {
    start_day: selectedDateStart.value,
    end_day: selectedDateEnd.value,
    id: displayingCategoryId.value,
  };
  calendarStore.actionSetCalendarSchedulesResults(payload);
};

const setColorsForCalendar = (): void => {
  if ("themes" in commonSettings.value) {
    if ("primaryColor" in commonSettings.value.themes)
      primaryColor.value = commonSettings.value.themes.primaryColor.color;
    if ("secondaryColor" in commonSettings.value.themes)
      secondaryColor.value = commonSettings.value.themes.secondaryColor.color;
    if ("accentColor" in commonSettings.value.themes)
      accentColor.value = commonSettings.value.themes.accentColor.color;
    if ("warningColor" in commonSettings.value.themes)
      warningColor.value = commonSettings.value.themes.warningColor.color;
  }
};

const searchForCalendarId = (node: any, categoryIdToSearch: any): any => {
  if ("id" in node) {
    return node.id == categoryIdToSearch ? node.calendarId : false;
  } else if ("children" in node) {
    let finalResult = false;
    for (let child of node.children) {
      let result = searchForCalendarId(child, categoryIdToSearch);
      if (result) {
        finalResult = result;
      }
    }
    return finalResult;
  } else {
    return false;
  }
};


const onShowDeleteCalendarDialog = (): void => {
  showDeleteCalendarDialog.value = true;
};

const onDelete = async (): Promise<void> => {
  await submitDelete();
};

const submitDelete = async (): Promise<void> => {
  let payload = {
    id: displayingCategoryId.value,
  };
  await calendarStore.actionDeleteCalendar(payload);
  await isDeleted(isDeletedCalendar.value);
};

const isDeleted = async (value: any): Promise<void> => {

  if (value?.length === 0) {
    await calendarStore.actionSetCalendarDataOfCategoriesTree();
    await calendarStore.actionSetCalendarDisplayContentMode(SHOW_CONTENT_MODE.SHOW_SELECT_CONDITIONS);
    $q.notify({ message: "指定されたカレンダーを削除しました。", color: "positive", icon: "mdi-check" });
  } else {
    if (!value) {
      $q.notify({ message: "予期せぬエラーが発生しました。", type: "negative", icon: "mdi-alert-circle-outline" });
    }
    else if (value[0] === 403) {
      $q.notify({ message: "この操作を行う場合は、権限を管理者にお問い合わせください", type: "negative", icon: "mdi-alert-circle-outline" });
    } 
    else {
      $q.notify({ message: "既に予約の入っているカレンダーは削除できません。", type: "negative", icon: "mdi-alert-circle-outline" });
    }
  }
  $q.loading.hide();
};

const calendarOffChange = async (): Promise<void> => {
  let successMsg = isCalendarOff.value ? "予約停止を解除しました。" : "予約停止を実行しました。";
  let errMsg = isCalendarOff.value ? "予約停止の解除ができませんでした。" : "予約停止ができませんでした。";
  let payload = {
    calendarId: displayingCategory.value.calendarId,
    calendarOff: isCalendarOff.value ? 0 : 1,
  };
  try {
    await calendarStore.actionSetCalendarOff(payload);
    calendarStore.setStateCalendarOff(payload);
    $q.notify({ message: successMsg, color: "positive", icon: "mdi-check" });
  } catch (error: any) {
    if (error.response && error.response.status === 400) {
      $q.notify({ message: errMsg, type: "negative", icon: "mdi-alert-circle-outline" });
    } else {
      $q.notify({
        type: "negative",
        message: "システムエラーが発生しました。システム管理者にお問い合わせください。",
        icon: "mdi-alert-circle-outline",
      });
    }
  }
};

const onCalendarOffClick = (): void => {
  $q.dialog({
    // type: "confirm",
    title: isCalendarOff.value ? "予約を再開" : "予約を停止",
    message: isCalendarOff.value ? "予約を再開してよろしいでしょうか？" : "予約を停止してよろしいでしょうか？",
    ok: {
      label: 'データ更新',
    },
  }).onOk(async () => {
    await calendarOffChange();
  });
};

const onCalendarSettingsClick = (): void => {
  const categoryId = displayingCategoryId.value;
  const selectedLargeCategory = msSelectedLargeCategory.value;
  const selectedMediumCategory = msSelectedMediumCategory.value;
  const selectedSmallCategory = msSelectedSmallCategory.value;
  router.push({
    name: 'CalendarSettings',
    params: { 
      categoryId,
      selectedLargeCategory, 
      selectedMediumCategory,
      selectedSmallCategory,
    },
  });
};

const onExportCalendarCsv = (): void => {
  showSelectPeriodDialog.value = true;
};

const onCollectiveRegistration = (): void => {
  if (editScheduleMode.value) {
    $q.dialog({
      // type: "confirm",
      title: saveConfirmTitle.value,
      message: saveConfirmText.value,
      ok: {
        label: saveBtnConfirmTitle.value,
      },
    }).onOk(async () => {
      collectiveRegistration();
    });
  } else {
    collectiveRegistration();
  }
};

const collectiveRegistration = (): void => {
  calendarStore.actionSetCalendarEditMode(false);
  emits("onShowImportCsvContent");
};

const convertNewData = (newDatas: any): any => {
  const data = Object.values(newDatas).map;
  return data;
};

// hooks
onBeforeMount(async () => {
  setColorsForCalendar();
  newDatas.value = convertNewData(newDatas.value);
  await calendarStore.getAllCalendars();
});
</script>

<style lang="scss" scoped>
.calendar-header {
  background-color: let(--primary-color);
}
.calendar-header-text {
  color: white;
}
.month-cell {
  border: solid 1px lightgray !important;
}
.sticky-table-wrapper {
  max-height: 75vh;
  overflow-y: scroll;
}
table {
  width: 100%;
  border-spacing: 0;
  font-family: 'Poppins' !important;
}
table>thead>tr>th {
  position: sticky;
  top: 0;
  z-index: 2;
  user-select: none;
  font-size: .75rem;
  height: 74px;
}
thead>tr:nth-child(2)>th {
  top: 48px;
}
td,
th {
  background-color: white;
  border-bottom: thin solid rgba(0,0,0,.12)!important;
  font-size: .875rem;
  height: 48px;
}
th.data-cell {
  border-top: solid 1px lightgray !important;
  border-left: solid 1px lightgray !important;
  border-bottom: solid 3px lightgray !important;
  border-color: lightgray !important;
  font-weight: bold;
  padding: 0px !important;
  min-width: 25px;
  width: 11%;
  cursor: default;
}
th.data-head {
  border-top: solid 1px lightgray !important;
  border-bottom: solid 3px lightgray !important;
  border-color: lightgray !important;
  padding: 0px !important;
  width: 11.5%;
}
th.saturday {
  color: #2196F3 !important;
}
th.sunday {
  color: #F44336 !important;
}
.day-off {
  background-color: #E0E0E0 !important;
}
td.non-data {
  border-left: solid 1px lightgray !important;
  background-color: #F5F7F8;
  border-color: lightgray !important;
  padding: 0px !important;
  cursor: default;
}
td.data-cell {
  border-left: solid 1px lightgray !important;
  border-color: lightgray !important;
  padding: 0px !important;
  vertical-align: middle !important;
}
tr td:first-child {
  border-color: lightgray !important;
  text-align: center;
  padding: 4px !important;
}
tr td:nth-child(2){
  border-right: solid 3px lightgray !important;
}
p {
  text-align: center !important;
  vertical-align: middle !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}
.full-title::after {
  position: absolute;
  margin-left: 10px;
  color: let(--secondary-dark-color);
}
.v-text-field {
  width: 35px !important;
}
.active {
  font-size: 14px !important;
  cursor: pointer;
}
.inactive {
  // TODO 予約受付の営業開始日に休日の考慮を入れた後で背景色を適用する
  // background-color: #F5F7F8 !important;
  font-size: 14px !important;
  cursor: default;
}
.onlyView {
  font-size: 14px !important;
  cursor: default;
}
.selected {
  background-color: let(--accent-light-color) !important;
  font-size: 18px !important;
}
.dateSelected {
  background-color: let(--accent-light-color) !important;
  font-size: 14px !important;
}
.noReservationFrame {
  background-color: #F5F7F8;
}
.fullBooked {
  background-color: #FFEBEE;
  color: #B71C1C;
}
.cell-hyphens {
  max-width: 1px;
  -webkit-hyphens: auto; /* iOS 4.2+ */
  -moz-hyphens: auto; /* Firefox 5+ */
  -ms-hyphens: auto; /* IE 10+ */
  hyphens: auto;
  word-break: break-all;
}
.settigns-btn {
  font-size: 12px!important;
}
.targetweek {
  font-family: 'Poppins';
  font-weight: bold;
  font-size: 28px;
  letter-spacing:-0.1em;
}
.hide-dayoff {
  display: none;
  cursor: default;
}
</style>
