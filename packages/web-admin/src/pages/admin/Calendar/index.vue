<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div class="tw-mt-4">
    <SubAppBar>
      <div class="tw-flex tw-justify-between tw-mt-3">
        <q-btn-toggle v-model="tab" :options="menuOption" dense outline color="grey" toggle-color="primary" class="bg-grey-2">
        </q-btn-toggle>
        <div v-if="tab=='calendar-setting'">
          <q-btn 
            color="primary" 
            @click="callCategorySettings = true" 
            :style="permissionsFromTeam.isAdministrator ? '' : permissionHelper.hideButtonPermissionStyle()"
          > 
            分類設定 
          </q-btn>
        </div>
      </div>
    </SubAppBar>
    <q-card class="tw-mb-4 tw-mt-4" flat style="background-color:transparent;border:none;">
      <div v-if="memberTabDisplay">
        <q-tab-panels v-model="tab">
          <q-tab-panel name="members-list" fluid class="tw-p-0" style="background-color:white">
            <MembersListFragment
              :dispOptionsModalFlg="dispOptionsModalFlg"
              @dispOptionsModalClose="dispOptionsModalClose"
            />
          </q-tab-panel>
          <q-tab-panel name="calendar-setting" fluid class="tw-p-0">
            <CalendarFragment
              :callCategorySettings = "callCategorySettings"
            />
          </q-tab-panel>
        </q-tab-panels>
      </div>
      <div v-else>
        <div class="tw-p-4">
          <CalendarFragment
            :callCategorySettings = "callCategorySettings"
          />
        </div>
      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router';
import { useQuasar } from 'quasar';
import { useAuthStore } from '@/stores/modules/auth.module';
import { useCalenderStore } from '@/stores/modules/calendar';
import { useSettingsStore } from '@/stores/modules/settings';
import { usePermissionHelper } from '@/mixins/PermissionHelper';

// components
import SubAppBar from '@/components/common/SubAppBar.vue';
import MembersListFragment from "@/pages/admin/Calendar/fragments/MembersListFragment.vue";
import CalendarFragment from "@/pages/admin/Calendar/fragments/CalendarFragment.vue";

const router = useRouter();
const route = useRoute();
const $q = useQuasar();

// store
const calenderStore = useCalenderStore();
const authStore = useAuthStore();
const settingsStore = useSettingsStore();

const permissionHelper = usePermissionHelper();

// data
const dispOptionsModalFlg = ref<boolean>(false);
const callCategorySettings = ref<boolean>(false);

// methods
const dispOptionsModalClose = (): void => {
  dispOptionsModalFlg.value = false;
};

// computed
const editScheduleMode = computed(() => calenderStore.editScheduleMode);
const permissionsFromTeam = computed(() => authStore.permissionsFromTeam);
const memberTabDisplay = computed(() => import.meta.env.VITE_MEMBER_TAB === "1");
const tab = computed({
  get(): any {
    if (!route.params.tab) {
      if (memberTabDisplay.value) {
        return 'members-list';
      }
      else {
        return 'calendar-setting';
      }
    }
    return route.params.tab;
  },
  set(tab: any): void {
    router.replace({ params: { ...route.params, tab } });
  }
});
const menuOption = computed(() => {
  if (memberTabDisplay.value) {
    return [
      {label: '会員一覧', value: 'members-list'},
      {label: 'カレンダー設定', value: 'calendar-setting'},
    ];
  }
  return [
    {label: 'カレンダー設定', value: 'calendar-setting'},
  ];
});

// hooks
onMounted(() => {
  // console.log('mounted');
  settingsStore.fetchCalendarDisplaySettings();
  calenderStore.actionGetReservationItemInfo("all");
});

onBeforeRouteLeave((to: any, from: any, next: any) => {
  if (editScheduleMode.value) {
    $q.dialog({
      title: "このページを離れてもよろしいですか？",
      message: "行った変更は破棄されます。",
      ok: {
        label: 'このページを離れる',
      }
    }).onOk(() => {
      calenderStore.actionSetCalendarEditMode(false);
      next();
    });
  } else {
    next();
  }
});
</script>

<style scoped>
.calendar-settings-col {
  padding-bottom: 0px;
}
</style>

<style>
.toolBarGroup.d-flex.justify-space-between>.v-toolbar__content{
  display: flex;
  justify-content: space-between;
  width: 100%;
}
</style>