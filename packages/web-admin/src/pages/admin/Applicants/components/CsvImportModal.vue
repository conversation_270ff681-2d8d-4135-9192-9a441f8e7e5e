<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
    <q-dialog v-model="show" width="600">
      <q-card>
        <q-bar class="bg-primary tw-h-1" dark></q-bar>
        <q-toolbar flat>
          <q-toolbar-title> CSV インポート </q-toolbar-title>
          <q-space/>
          <q-btn 
            flat round
            icon="mdi-close" 
            @click="cancelImport"
          />
        </q-toolbar>
        <template v-if="!isImportingCsvData">
          <div fluid>
            <div class="tw-mx-4 text-caption">
              個人情報が含まれる場合、該当項目が管理項目となっているか確認してください。
            </div>
            <q-file v-model="fileData" accept=".csv" class="tw-mx-4">
              <template v-slot:prepend>
                <q-icon name="attach_file" />
              </template>
            </q-file>
          </div>
        </template>

        <!-- TODO: 以下 v-alert に使用していた要素 - honda
          border="left"
          colored-border
          style="white-space: pre-line" 
        -->
        <q-card v-if="!!importCsvDataError" flat class="bg-error tw-mx-4">
          <q-card-section>
            <q-icon name="mdi-close"></q-icon>
          </q-card-section> 
          <q-card-section>
            {{ importCsvDataErrorMessage }}
          </q-card-section>
        </q-card>

        <!-- TODO: 以下 v-alert に使用していた要素 - honda
          border="left"
          colored-border
        -->
        <q-card v-if="!!finishedImportingLocalCsv && status.bunruiUpdatedFlag" flat class="tw-mx-4 tw-my-4" :style="`${getBorder('warning')}`">
          <q-card-section>
            <q-icon size="" name="mdi-alert-circle"></q-icon>
            <span>
              分類の変更、または予約・予約変更・再予約・キャンセルを伴う登録や更新はできませんのでご注意ください。
            </span>
          </q-card-section> 
        </q-card>

        <!-- TODO: 以下 v-alert に使用していた要素 - honda
          border="left"
          colored-border
        -->
        <q-card v-if="!!finishedImportingLocalCsv" flat class="tw-mx-4 tw-my-4" :style="`${getBorder('primary')}`">
          <q-card-section>
            <q-icon name="mdi-check-circle"></q-icon>
            <span>
              CSVファイルがアップロード出来ます。<br />
              確認ボタンをクリックしてください。
            </span>
          </q-card-section> 
        </q-card>

        <q-card-actions class="tw-my-2">
          <q-space />
          <q-btn
            :color="!isDisableOkBtn ? 'primary' : 'grey'"
            :disable="isDisableOkBtn"
            :loading="isUploadingImportedAppendTypeSurveyResultsCsv"
            @click="confirmImportCsvData"
          >
            <q-icon left name="mdi-file-upload-outline"></q-icon>
            確認
          </q-btn>
          <q-btn outline color="grey" @click="cancelImport">
            <q-icon left name="mdi-close-box-outline"></q-icon>
            閉じる
          </q-btn>
        </q-card-actions>
      </q-card>
    </q-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { checkFileType } from "@/utils/importSurveyHelper";
import { useDataManagementStore } from "@/stores/modules/dataManagement";
import { useQuasar } from "quasar";
import { colors } from 'quasar';

// quasar
const $q = useQuasar();

// store
const dataManagementStore = useDataManagementStore();

// props emit
const props = defineProps<{
  formData: any,
  visible: boolean,
}>();

const emit = defineEmits(['close', 'closeAndReload']);

// data
const isImportingCsvData = ref(false);
const fileData = ref();
const importCsvDataError = ref(false);
const importCsvDataErrorMessage = ref("");
const importingLocalCsv = ref(false);
const finishedImportingLocalCsv = ref(false);
const status = ref({
  bunruiUpdatedFlag: false,
});
const convertedJsonBucketKey = ref(null);
const isReadyForImport = ref(false);

// computed
const isUploadingImportedAppendTypeSurveyResultsCsv = computed(() => dataManagementStore.isUploadingImportedAppendTypeSurveyResultsCsv);
const isInitializingImportingAppendTypeSurveyResults = computed(() => dataManagementStore.isInitializingImportingAppendTypeSurveyResults);
const initializeImportingAppendTypeSurveyResultsError = computed(() => dataManagementStore.initializeImportingAppendTypeSurveyResultsError);

const show = computed({
  get(): boolean {
    return props.visible;
  },
  set(value: boolean): void {
    fileData.value = null;
    if (!value) {
      emit("close");
    }
  },
});

const isDisableOkBtn = computed(() => {
  return (!fileData.value ||
    !isReadyForImport.value ||
    importingLocalCsv.value ||
    importCsvDataError.value ||
    isUploadingImportedAppendTypeSurveyResultsCsv.value);
});

// watch
watch(
   () => fileData.value,
   async (newValue: any, oldValue: any): Promise<void> => {
    importCsvDataError.value = false;
    importCsvDataErrorMessage.value = "default val";
    importingLocalCsv.value = false;
    finishedImportingLocalCsv.value = false;
    convertedJsonBucketKey.value = null;
    isReadyForImport.value = false;

    if (newValue && newValue !== oldValue) {
      importingLocalCsv.value = true;
      let isSJISEncoding = await checkFileType(newValue);
      const csvFile = newValue;

      //send csv file to backend as is and do transformation/validation there
      let uploadResponse = await dataManagementStore.uploadImportedAppendTypeSurveyResultsCsv({
        surveyId: props.formData.surveyId,
        survey: props.formData,
        file: csvFile,
        isSJISEncoding: isSJISEncoding,
      });

      //received errors parsing csv
      if (uploadResponse.result == "ERROR") {
        if (uploadResponse.errorType == "CSV_ERROR") {
          if (Array.isArray(uploadResponse.errorMessage)) {
            let errorMessages = uploadResponse.errorMessage;
            if (errorMessages.length > 10) {
              errorMessages = errorMessages.slice(0, 10);
              errorMessages.push("...", `${uploadResponse.errorMessage.length}個のエラーが発生しました。`);
            }
            errorMessages.push("インプットファイルは.csvですか？");
            importCsvDataErrorMessage.value = errorMessages.join("\n");
            importCsvDataError.value = true;
            importingLocalCsv.value = false;
            return;
          } else {
            importCsvDataErrorMessage.value = uploadResponse.errorMessage;
            importCsvDataError.value = true;
            importingLocalCsv.value = false;
            return;
          }
        } else if (uploadResponse.errorType == "VALIDATION_ERROR") {
          importCsvDataErrorMessage.value = uploadResponse.errorMessage;
          importCsvDataError.value = true;
          importingLocalCsv.value = false;
          return;
        } else {
          importCsvDataErrorMessage.value = uploadResponse.errorMessage
            ? uploadResponse.errorMessage
            : "CSVファイルバリデーションながら不明エラーが発生しました。";
          importCsvDataError.value = true;
          importingLocalCsv.value = false;
          return;
        }
      }

      if (uploadResponse.result == "OK") {
        isReadyForImport.value = true;
        importCsvDataErrorMessage.value = "";
        importCsvDataError.value = false;
        importingLocalCsv.value = false;
        finishedImportingLocalCsv.value = true;
        status.value.bunruiUpdatedFlag = uploadResponse.bunruiUpdatedFlag;
        convertedJsonBucketKey.value = uploadResponse.jsonFileBucketKey;
      } else {
        importCsvDataErrorMessage.value = "CSVファイルバリデーションながら不明エラーが発生しました。";
        importCsvDataError.value = true;
        importingLocalCsv.value = false;
        return;
      }
    } else {
      importCsvDataError.value = false;
      importCsvDataErrorMessage.value = "";
      importingLocalCsv.value = false;
      finishedImportingLocalCsv.value = false;
      status.value.bunruiUpdatedFlag = false;
    }
  }
);

watch(
  () => isInitializingImportingAppendTypeSurveyResults.value,
  (value: any): void => {
    if (value) {
      $q.notify({
        message: 'CSVインポート処理を開始しました。結果は通知情報に表示されます。',
      });
      fileData.value = null;
      emit("closeAndReload");
    }
  }
);

watch(
  () => initializeImportingAppendTypeSurveyResultsError.value,
  (value: any): void => {
    if (value) {
      $q.notify({
        type: 'error',
        message: initializeImportingAppendTypeSurveyResultsError.value ?? '',
      })
      fileData.value = null;
    }
  }
);

// methods
const cancelImport = (): void => {
  fileData.value = null;
  emit("close");
};

const confirmImportCsvData = async(): Promise<void> => {
  await dataManagementStore.initializeImportingAppendTypeSurveyResults({
    bucketKey: convertedJsonBucketKey.value,
    surveyId: props.formData.surveyId,
    fileName: fileData.value.name,
  });
};

const getBorder = (type: string) => {
  const types = {
    warning: colors.getPaletteColor('warning'),
    error: colors.getPaletteColor('negative'),
    primary: colors.getPaletteColor('primary'),
  };
  return `border-radius: 4px; border-left: 5px solid ${types[type]};`;
};
</script>
