<template>
  <div>
    <q-dialog v-model="show" scrollable class="tw-w-full">
      <q-card class="tw-w-full" style="min-width: 100%;">
        <q-bar class="bg-primary tw-h-1"></q-bar>
        <q-toolbar flat>
          <q-toolbar-title> 会員選択 </q-toolbar-title>
          <q-space/>
          <q-btn flat round icon="mdi-close" @click="show = false">
          </q-btn>
        </q-toolbar>
        <div fluid class="tw-p-4">
          <SearchFragment :memberSelectMode="true" />
          <ContentFragment :memberSelectMode="true" @onMemberSelected="onMemberSelected" />
        </div>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, watch } from "vue";
import { useMemberStore } from "@/stores/modules/member";
import { useDataManagementStore } from "@/stores/modules/dataManagement";
import { useQuasar } from "quasar";

// components
import SearchFragment from "@/pages/admin/Calendar/fragments/memberList/SearchFragment.vue";
import ContentFragment from "@/pages/admin/Calendar/fragments/memberList/ContentFragment.vue";

// quasar
const $q = useQuasar();

// store
const memberStore = useMemberStore();
const dataManagementStore = useDataManagementStore();

// props
const props = defineProps<{
  visible: boolean,
  memberConfigId: string,
}>();

const emit = defineEmits(['close', 'onMemberDetailSelected']);

// computed
const memberConfigs = computed(() => memberStore.memberConfigs);
const isFetchingData = computed(() => dataManagementStore.isFetchingData);
const stateSelectedSurvey = computed(() => dataManagementStore.selectedSurvey);

const selectedMemberConfig = computed(() => {
  return memberConfigs.value.find((obj) => obj.surveyId === props.memberConfigId);
});

const show = computed({
  get(): boolean {
    return props.visible;
  },
  set(value: boolean): void {
    if (!value) {
      emit("close");
    }
  },
});

// watch
watch(
  () => selectedMemberConfig.value,
  (newVal) => {
    if (newVal) {
      dataManagementStore.selectFormToWork(newVal);
    }
  }
);

watch(
  () => show,
  (newVal) => {
    if (newVal) {
      // Check if selected survey in state differs from the linked member config
      // If it does, call selectFormToWork again
      if (
        selectedMemberConfig.value &&
        selectedMemberConfig.value.memberSurveyId != stateSelectedSurvey.value.memberSurveyId
      ) {
        dataManagementStore.setIsFetchingDataOfSelectedForm(true);
        dataManagementStore.selectFormToWork(selectedMemberConfig.value);
        dataManagementStore.setIsFetchingDataOfSelectedForm(false);
      }
    }
  }
);

watch(
  () => isFetchingData.value,
  (newVal) => {
    if (newVal) {
      $q.loading.show();
    }
    else {
      $q.loading.hide();
    }
  }
)

// mounted
onMounted(async () => {
  // If member configs is empty, fetch all member configs
  if (!memberConfigs.value || memberConfigs.value.length == 0) {
    await memberStore.fetchAllMemberFormConfigs();
  }
});

// methods
const onMemberSelected = (detail_data: any): void => {
  emit("onMemberDetailSelected", { memberConfig: selectedMemberConfig.value, memberDetail: detail_data });
};
</script>