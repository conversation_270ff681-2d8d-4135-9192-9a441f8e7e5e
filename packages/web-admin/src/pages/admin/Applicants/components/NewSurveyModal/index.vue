<template>
  <div class="row" justify="center">
    <q-dialog v-model="show" scrollable>
      <q-card v-if="surveyConfig" class="tw-w-full" style="max-width: 800px;">
        <q-card class="tw-sticky tw-top-0 tw-z-10">
          <q-bar class="bg-primary tw-h-1" dark></q-bar>
          <q-toolbar flat>
            <q-toolbar-title>
              {{ surveyConfig.surveyTitle }}
            </q-toolbar-title>
            <q-space/>
            <q-btn icon="mdi-close" flat round @click="show = false">
            </q-btn>
          </q-toolbar>
          <div class="row tw-pb-2" v-if="countVaccinesItem">
            <div class="col">
              <div class="tw-mx-4">
                {{ vaccinationTimeLabel }}
              </div>
            </div>
          </div>
        </q-card>
        <q-card-section ref="formContentRef">
          <template v-if="memberDetailData && memberConfigData">
            <FormRendering
              :key="memberDetailData.id + memberDetailData.updatedAt"
              :configJson="memberDataWithConfig"
              :isLiffMode="false"
              :isAdminMode="true"
              :calendarLabelDisplay="false"
              :calendarButtonDisplay="false"
              :isSurveyRegistering="false"
              :forceNotEditable="true"
              :categoriesTree="categoriesTree"
              ref="inputFormRef"
              :pinia="getActivePinia()"
              @handleCheckSaibanExisting="handleCheckSaibanExisting"
            />
          </template>
          <FormRendering
            :key="newUUID"
            :configJson="surveyDataWithConfig"
            :isLiffMode="false"
            :isAdminMode="true"
            ref="inputFormRef"
            :categoryRequiredFlag="true"
            :calendarLabelDisplay="false"
            :calendarButtonDisplay="false"
            :categoriesTree="categoriesTree"
            :isValidatePreviousVaccineDate="isValidatePreviousVaccineDate"
            :isValidatePreviousVaccineMaker="isValidatePreviousVaccineMaker"
            :taxRate="taxRate"
            :paymentService="paymentService"
            :products="productList"
            :isFetchingPaymentInfo="isFetchingPaymentInfo"
            :pinia="getActivePinia()"
            @setPaymentInfo="hendleSetPaymentInfo"
            @updateIsFormValid="updateIsFormValid"
          />

          <q-card v-if="isBunruiCalendar" class="tw-my-4">
            <q-card-actions class="tw-flex tw-items-center">
              <q-btn color="primary" class="tw-my-2" @click="showCalender()" :loading="calendarDisplay">
                <q-icon left name="mdi-calendar-clock"></q-icon>予約日時指定
              </q-btn>
              <div class="selectCaldndarValue tw-ml-4">{{ calendarValue.label }}</div>
            </q-card-actions>
          </q-card>

          <q-card class="tw-my-4">
            <div class="tw-p-4">
              <span class="tw-font-semibold">管理者メモ</span>
              <q-input v-model="note" type="textarea"></q-input>
            </div>
          </q-card>
        </q-card-section>
        <q-card class="tw-sticky tw-bottom-0">
          <q-separator/>
          <div v-if="isCompleteFirstReservation()">
            <q-card-actions class="tw-px-4">
              <q-checkbox
                v-model="autoSecondReservation"
                :label="`2回目を自動予約する`"
                :messages="`※接種間隔を空けた日になります。コマ指定はできません。`"
              >
              </q-checkbox>
            </q-card-actions>
            <q-separator/>
          </div>
          <q-card-actions align="right">
            <div class="row tw-p-2">
              <q-btn
                color="primary"
                class="tw-w-32 tw-mr-4"
                :disable="!isFormValid"
                @click="
                  permissionHelper.hasActionPermission('click', 'backendRequest') ? onCreateSurveyData() : permissionHelper.showActionPermissionError()
                "
                :style="
                  permissionHelper.hasActionPermission('hideButton', 'Applicants_NewSurveyModal_Save') ? permissionHelper.hideButtonPermissionStyle() : ''
                "
              >
                <q-icon left name="mdi-content-save-outline"></q-icon>
                保存
              </q-btn>
              <q-btn color="grey lighten-2" class="tw-w-32" @click="show = false">
                <q-icon left name="mdi-close-box-outline"></q-icon>
                閉じる
              </q-btn>
            </div>
          </q-card-actions>
        </q-card>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { useDatetimeFormatter } from "@/mixins/DatetimeFormatter";
import { cloneDeep, isEmpty } from "lodash";
import FormRendering from "@/../../web-shared/src/components/FormRendering/index.vue";
import { generateUUID } from "@/utils/uuidUtils";
import {
  PAYMENT_STATES,
} from '@/stores/modules/payment/payment.constants';
import { useQuasar } from "quasar";
import { useDataManagementStore } from "@/stores/modules/dataManagement";
import { useSurveyResultsStore } from "@/stores/modules/surveyResults";
import { nextTick } from "process";
import { useCalenderStore } from "@/stores/modules/calendar";
import { useFormEditorStore } from "@/stores/modules/formEditor";
import { usePermissionHelper } from "@/mixins/PermissionHelper";
import { getActivePinia } from "pinia";
import { useSettingsStore } from "@/stores/modules/settings";
import { GetCategoriesTrees } from "@/services/calendar.service";

// quasar
const $q = useQuasar();

// store
const dataManagementStore = useDataManagementStore();
const surveyResultsStore = useSurveyResultsStore();
const calendarStore = useCalenderStore();
const formEditorStore = useFormEditorStore();
const settingsStore = useSettingsStore();

const datetimeFormatter = useDatetimeFormatter();
const permissionHelper = usePermissionHelper();

// props
const props = defineProps<{
  visible: boolean,
  formData: any,
  memberConfigData: any,
  memberDetailData: any,
}>();

const emit = defineEmits(['close']);

// data
const note = ref("");
const newUUID = ref("");
const newSurveyConfig = ref();
const confirmShow = ref(false);
const autoSecondReservation = ref(false);
const isValidatePreviousVaccineDate = ref(true);
const isValidatePreviousVaccineMaker = ref(true);
const hasCategory = ref(false);
const isFormValid = ref(false);

// ref data
const inputFormRef = ref();
const formContentRef = ref();

const surveyConfig = computed(() => surveyResultsStore.selectedSurvey);
const isUpdatingData = computed(() => dataManagementStore.isUpdatingData);
const updateDataError = computed(() => dataManagementStore.updateDataError);
const calendarDisplay = computed(() => dataManagementStore.isCalendarDisplay);
    //   calendarConfig: (state: any) => state.dataManagement.calendarConfig,
const calendarValue = computed(() => dataManagementStore.calendarValue);
const categoriesTree = computed(() => dataManagementStore.categoriesTree);
const reservationItemData = computed(() => calendarStore.reservationItemInfo);
    //   reservationItemInfoWorking: (state: any) => state.calendar.reservationItemInfoWorking,
    //   autoReservation: (state: any) => state.surveyResults.autoReservation,
const autoReservationResult = computed(() => dataManagementStore.autoReservationResult);
const reservationPaymentServiceMap = computed(() => dataManagementStore.reservationPaymentServiceMap);
const isFetchingReservationPaymentServcie = computed(() => dataManagementStore.isFetchingReservationPaymentServcie);
const paymentService = computed(() => dataManagementStore.paymentService);
const isFetchingPaymentService = computed(() => dataManagementStore.isFetchingPaymentService);
const productList = computed(() => dataManagementStore.productList);
const taxRate = computed(() => dataManagementStore.taxRate);
const isFethingTaxRate = computed(() => dataManagementStore.isFethingTaxRate);
/* 
  Note: Both `surveyDataWithConfig` and `memberDataWithConfig` were set as computed and in Vue3 that is not the best practice (in this specific case) 
  since it can lead to rerendering issues due to how Vue3 handles reactivity. 
  So I changed it to ref and used `watch` to set the value, this way it is way safer to use.

  メモ：`surveyDataWithConfig`と`memberDataWithConfig`は両方ともcomputedとして設定されており、Vue3ではこれは最良のプラクティスではありません（この特定のケースでは）。
  Vue3がリアクティビティを処理する方法のため、再レンダリングの問題を引き起こす可能性があります。
  したがって、refに変更し、watchを使用して値を設定しました。この方法では、使用するのがはるかに安全です。
*/

const surveyDataWithConfig = ref(null);
const memberDataWithConfig = ref(null);

watch(newSurveyConfig, (newVal) => {
  if (newVal) {
    surveyDataWithConfig.value = cloneDeep(newVal);
  }
});

watch(
  [() => props.memberConfigData, () => props.memberDetailData],
  ([newMemberConfigData, newMemberDetailData]) => {
    if (newMemberConfigData && newMemberDetailData) {
      const _memberConfig = cloneDeep(newMemberConfigData);
      memberDataWithConfig.value = {
        ..._memberConfig,
        inputs: newMemberDetailData,
      };
    } else {
      memberDataWithConfig.value = null;
    }
  }
);



const show = computed({
  get(): boolean {
    return props.visible;
  },
  set(value: boolean): void {
    if (!value) {
      autoSecondReservation.value = false;
      emit("close");
    }
  },
});

const isBunruiCalendar = computed((): boolean => {
  if (props.formData && props.formData.surveySchema) {
    let surveySchema = props.formData.surveySchema;
    for (let i = 0; i < surveySchema.length; i++) {
      if (surveySchema[i].type && surveySchema[i].type === "linkbutton") {
        return true;
      }
    }
  }
  return false;
});

const isSetReservationDate = computed((): boolean => {
  return !!calendarValue.value?.label;
});

const countVaccinesItem = computed((): any => {
  const surveySchema = surveyConfig.value.surveySchema;
  const result = surveySchema?.find(item => item.type === 'countVaccines');
  return result;
});

const vaccinationTimeLabel = computed((): string => {
  const vaccinationTime = countVaccinesItem.value ? countVaccinesItem.value.default : '';
  return `新型コロナワクチン${vaccinationTime}回目接種`;
});

const isFetchingPaymentInfo = computed((): boolean => {
  return isFetchingPaymentService.value || isFetchingReservationPaymentServcie.value || isFethingTaxRate.value;
});

// watch
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      nextTick(() => {
        scrollToTop();
      });
      newUUID.value = generateUUID();
      note.value = "";
      dataManagementStore.setCalendarValue({});
      resetIsValidPreviousVaccineItems();

      let _surveyConfig = JSON.parse(JSON.stringify(surveyConfig.value));
      let _reservationItemData = reservationItemData.value.data;
      _surveyConfig.surveySchema.forEach(function (ss: any) {
        if (ss.type === "reservation") {
          ss["reservationItemData"] = _reservationItemData;
        }
      });
      newSurveyConfig.value = _surveyConfig;
      const categoryItem = _surveyConfig.surveySchema.find((item: any) => item.type === 'reservation');
     
      //console.log('categoryItem', categoryItem);
      if (categoryItem) {
        hasCategory.value = true;
      } else {
        hasCategory.value = false;
      }
    } else {
      if (props.formData.usePayment === 1) {
        if (hasCategory.value) {
          resetPaymentInfo();
        }
        if (inputFormRef.value) {
          inputFormRef.value.initOrder();
        }
      }
    }
  }
);

watch(
  () => updateDataError.value,
  (newVal) => {
    //if value already updated, update current data
    if (newVal !== null) {
      if (newVal.needRefresh !== undefined && newVal.needRefresh === true) {
        if (newVal.result === "ERROR") {
          $q.dialog({
            // type: "warning",
            title: "変更を保存できません",
            // hideBtnCancel: true,
            message: "帳票データが、他の人によって変更されました。\nページを再読み込みしてください。",
            ok: {
              label: '再読み込み',
            }
          }).onOk(async () => {
            await reloadPage();
          });
        }
      } else {
        $q.notify({
          type: "error",
          message: newVal.errorMessage,
        });
      }
    }
  }
);

watch(
  () => isUpdatingData.value,
  (newVal) => {
    if (newVal) {
      $q.loading.show();
    }
    else {
      $q.loading.hide();
    }
  }
);

// mounted
onMounted(async() => {
  //await calendarStore.actionSetCalendarDataOfCategoriesTree();
  //console.log("settingsStore.categoriesTree", settingsStore.calendarCategories);
  const tree = await GetCategoriesTrees();
  //console.log('tree', tree);
  dataManagementStore.setCalendarDataOfCategoriesTree1(tree);
});

// methods
const hasType = (findType: any): boolean => {
  let _item = props.formData.surveySchema.find((obj: { type: any; }) => obj.type === findType);
  return _item ? true : false;
};

const createNewUUID = (): string => {
  return generateUUID();
};

const reloadPage = (): void => {
  window.location.reload();
};

const getTitle = (key: any): any => {
  let _item = props.formData.surveySchema.find((obj: any) => obj.itemKey === key);
  return _item ? _item.title : "";
};

const getType = (key: any): any => {
  let _item = props.formData.surveySchema.find((obj: any) => obj.itemKey === key);
  return _item ? _item.type : "";
};

const handleSurveyResults = async (): Promise<any> => {
  const formData: any = await inputFormRef.value?.getFormData();
  if (!formData) {
    return null;
  }
  let surveyResults = [];
  let paymentData = undefined;
  for (const [key, formValue] of formData.entries()) {
    //console.log(key, formValue);
    if (key === 'paymentData') {
      paymentData = JSON.parse(formValue);
      continue;
    }
    const type = getType(key);
    let value = type === "date" && formValue === "null" ? null : formValue;
    value = value === "null" ? null : value;
    if (value) {
      const result = {
        itemKey: key,
        title: type,
        value: value,
      } as any;

      // If member data is linked, also set userId to be the same
      if (props.memberDetailData && props.memberDetailData.userId) {
        result["userId"] = props.memberDetailData.userId;
      }

      surveyResults.push(result);
    }
  }

  return {
    surveyId: props.formData.surveyId,
    surveyResults,
    paymentData,
  };
};

const isFirstTimeVaccination = (formData: any, surveyResults: any): boolean => {
  // only for corona vaccination
  if (formData.surveyType && formData.surveyType === "corona") {
    // check has countVaccines item
    const countVaccinesItem = formData.surveySchema.find((item: any) => item.type === 'countVaccines');
    if (countVaccinesItem) {
      return false;
    }
    // check is first time
    const vaccinationTimes = formData.surveySchema.find(
      (item: any) => item.attributes && item.attributes.isSesshuKaisuu === true
    );
    const isFirstTime =
      surveyResults.find((item: any) => vaccinationTimes && item.itemKey === vaccinationTimes.itemKey).value === "1回目";
    //check first time vaccinated
    const vaccinationDate = formData.surveySchema.find((item: any) => item.type === "sesshuJisshiDate");
    const vaccinationMaker = formData.surveySchema.find((item: any) => item.type === "sesshuVaccineMaker");
    const isFirstTimeVaccinated =
      typeof surveyResults.find((item: any) => vaccinationDate && item.itemKey === vaccinationDate.itemKey) !==
        "undefined" &&
      typeof surveyResults.find((obj: any) => vaccinationMaker && obj.itemKey === vaccinationMaker.itemKey) !==
        "undefined";
    if (isFirstTime && isFirstTimeVaccinated) {
      return true;
    }
    return false;
  }
  return false;
};

const isCompleteFirstReservation = (): boolean => {
  let formData: any = [];
  try {
    formData = handleSurveyResults();
  } catch (err: any) {
    return false;
  }

  if (!formData || isEmpty(formData)) {
    return false;
  }

  formData.surveyResults = formData.surveyResults.filter((data: any) => {
    if (getType(data.itemKey) === "linkbutton") {
      return false;
    }
    if (getType(data.itemKey) === "reservation") {
      if (calendarValue.value.key) {
        data.value = calendarValue.value.key;
      }
    }
    return true;
  });
  return isFirstTimeVaccination(props.formData, formData.surveyResults);
};

const autoReservationConfirm = async (): Promise<void> => {
  confirmShow.value = true;
};

const validatePreviousVaccineItems = (surveyResults: any) => {
  const surveySchema = surveyDataWithConfig.value.surveySchema;
  const countVaccinesItem = surveySchema.find((item: any) => item.type === 'countVaccines');
  const isFirstReservation = Number(countVaccinesItem?.default) === 1;
  if (
    !isSetReservationDate.value || // 予約日時が指定されていない
    !countVaccinesItem || // ワクチンN回目接種固有アイテムが存在しない
    isFirstReservation // 接種回数が1回目
  ) {
    return true;
  }
  
  const previousVaccineItemTypes = ['previousVaccineDate', 'previousVaccineMaker'];
  surveySchema.forEach((item: any) => {
    if (previousVaccineItemTypes.includes(item.type)) {
      const previousVaccineItem = surveyResults.find((result: any) => result.itemKey === item.itemKey);
      if (item.type === 'previousVaccineDate') {
        isValidatePreviousVaccineDate.value = !!previousVaccineItem;
      }
      if (item.type === 'previousVaccineMaker') {
        isValidatePreviousVaccineMaker.value = !!previousVaccineItem;
      }
    }
  });
  const isValid = isValidatePreviousVaccineDate.value && isValidatePreviousVaccineMaker.value;
  return isValid;
};

const preparePaymentDataForCreate = (formData: any): void => {
  const { paymentData, surveyId } = formData;
  if (!paymentData) {
    return;
  }
  paymentData.surveyId = surveyId;
  paymentData.status = PAYMENT_STATES.NOT_APPLICABLE;
  paymentData.partitionKey = paymentService.value.sortKey || paymentService.value.serviceSortKey;
};

const onCreateSurveyData = async (): Promise<void> => {
  let formData: any = await handleSurveyResults();
  if (!formData) {
    $q.dialog({
      // type: "warning",
      title: "エラー",
      message: "入力内容を確認してください。",
      // hideBtnConfirm: true,
    });
  } else {
    const isValidPreviousVaccineItem = validatePreviousVaccineItems(formData.surveyResults);
    if (!isValidPreviousVaccineItem) {
      $q.dialog({
        // type: "warning",
        title: "エラー",
        message: "入力内容を確認してください。",
        // hideBtnConfirm: true,
      });
      return;
    }
    formData.surveyResults = formData.surveyResults.filter((data: any) => {
      if (getType(data.itemKey) === "linkbutton") {
        return false;
      }
      if (getType(data.itemKey) === "reservation") {
        if (calendarValue.value.key) {
          data.value = calendarValue.value.key;
        }
      }
      return true;
    });
    if (formData.paymentData) {
      preparePaymentDataForCreate(formData);
    }

    await updateSurveyResultDialog(
      formData,
      isFirstTimeVaccination(props.formData, formData.surveyResults) && autoSecondReservation.value
    );
  }
};

const updateSurveyResultDialog = async (formData: any, autoReservation = false): Promise<void> => {
  $q.dialog({
    // type: "confirm",
    title: "データの登録",
    message: "この入力内容でデータ登録してもよろしいですか？",
    ok: {
      label: '保存',
    },
    cancel: true,
  }).onOk(async () => {
    let surveyResult = {
      ...formData,
      surveyConfig: newSurveyConfig.value,
      note: note.value,
      memberData: props.memberDetailData,
      autoReservation: autoReservation,
    };
    // check if second times reservation date is within the waiting period
    // check check if the reservation date is within the allowed time
    let inTerm:any = null;
    let _subItem = newSurveyConfig.value.surveySchema.find((item: any) => item.type === "reservation");
    let _bunrui = surveyResult.surveyResults.find((item: any) => _subItem && item.itemKey === _subItem.itemKey);
    if (_bunrui && _bunrui.value && _bunrui.value.split("|")[1]) {
      inTerm = await dataManagementStore.checkReservable({...surveyResult, ...{changeStatus: false}});
    }
    if (inTerm && !inTerm.check) {
      warningDialog(inTerm, surveyResult, autoReservation);
    } else {
      await onSaveData(surveyResult, autoReservation);
    }
  });
};

const warningDialog = (inTerm: any, surveyResult: any, autoReservation: any): void => {
  let text = inTerm.warningMessage;
  $q.dialog({
    color: "warning",
    title: "警告",
    message: text + "\n予約を行いますか？",
    focus: 'none',
    ok: {
      label: '保存',
    },
    cancel: true,
  }).onOk(() => {
    onSaveData(surveyResult, autoReservation);
  });
};

const onSaveData = async (surveyResult: any, autoReservation: any): Promise<void> => {
  let _result = await dataManagementStore.createDataSurvey(surveyResult);
  //if value already updated, update current data
  if (typeof _result === "object") {
    $q.dialog({
      // type: "warning",
      title: "変更を保存できません。",
      message: "編集していたデータは、他の人によって変更されました。\n もう一度編集する前に、データを更新してください。",
      ok: {
        label: 'データ更新',
      }
    }).onOk(async () => {
      show.value = false;
      await surveyResultsStore.fetchSurveyResultsItems();
      $q.notify({
        message: "最新データが更新されました。",
      });
    });
  }

  if (_result === true) {
    let _doneText = "データの保存が成功しました。";
    if (autoReservation) {
      _doneText = _doneText + autoReservationResult.value;
    }
    $q.notify({
      message: _doneText,
    });
    //show.value = false;
    emit("close", true);
  }
};

const showCalender = async (): Promise<void> => {
  const formData: any = await handleSurveyResults();
  if (formData) {
    const categoryAnswer = formData.surveyResults.find((result: any) => getType(result.itemKey) === "reservation");
    const categoryId = categoryAnswer?.value ? categoryAnswer.value.split('_')[0] : null;
    const itemId = categoryAnswer?.value ? categoryAnswer.value.split('_')[1] : null;
    const itemKey = categoryAnswer?.itemKey || null;
    // find reservation cost
    let itemCost = 1;
    if (formData.paymentData) {
      itemCost = formData.paymentData.reservationCost;
    } else if (itemId) {
      const reservationItem = reservationItemData.value.data.find(item => {
        return item.categoryId === categoryId && Number(item.itemId) === Number(itemId);
      });
      reservationItem && (itemCost = reservationItem.cost);
    }
    let userId = formData.userId ? formData.userId : "";
    // If member data is linked, also set userId to be the same
    if (props.memberDetailData && props.memberDetailData.userId) {
      userId = props.memberDetailData.userId;
    }
    dataManagementStore.setCalendarConfig({
      categoryId: categoryId,
      surveyId: formData.surveyId,
      itemKey: itemKey,
      userId: userId,
      itemId: itemId,
      itemCost: itemCost,
      oldCategoryKey: null,
    });
    dataManagementStore.setIsCalendarDisplay(true);
  } else {
    $q.dialog({
      // type: "warning",
      title: "エラー",
      message: "入力内容を確認してください。",
      // hideBtnConfirm: true,
    });
  }
};

const scrollToTop = (): void => {
  formContentRef.value.scrollTop = 0;
};

const resetIsValidPreviousVaccineItems = () => {
  isValidatePreviousVaccineMaker.value = true;
  isValidatePreviousVaccineDate.value = true;
};

const hendleSetPaymentInfo = async (categoryId: string | null, itemKey: string) => {
  if (!categoryId || categoryId === '') {
    // console.log('IDの入力値がないため却下')
    return;
  };
  
  if (dataManagementStore.surveyInputRegisterPayment && dataManagementStore.surveyInputRegisterPayment[itemKey] === categoryId) {
    // console.log('現在の入力値と同じため却下');
    return;
  }

  await inputFormRef.value.getFormDataJson(undefined, true).then((formData) => {
    let inputs = {};
    formData?.surveyResults?.forEach((elem) => {
      inputs[elem.itemKey] = elem.value;
    });
    
    if (!inputs[itemKey]) inputs[itemKey] = categoryId;
    // console.log(inputs)

    dataManagementStore.surveyInputRegisterPayment = inputs;
    newSurveyConfig.value.inputs = inputs;
  });

  if (!categoryId) {
    resetPaymentInfo();
    return;
  }
  if (!reservationPaymentServiceMap.value[categoryId]) {
    await dataManagementStore.fetchReservationPaymentService(categoryId);
  }
  const service = reservationPaymentServiceMap.value[categoryId];
  dataManagementStore.setPaymentService(service || null);
  dataManagementStore.setProdutListForDataManagement(service ? service.reservationPaymentItems : []);
};

const resetPaymentInfo = (): void => {
  dataManagementStore.setPaymentService(null);
  dataManagementStore.setProdutListForDataManagement([]);
};

const updateIsFormValid = (valid: boolean) => {
  isFormValid.value = valid;
};

const handleCheckSaibanExisting = (value) => {
  // 今後会員帳票関連で使う可能性があるので追加しておきます - honda
  // console.log('handleCheckSaibanExisting', value)
}
</script>

<style lang="scss" scoped>
.selectCaldndarValue {
  display: inline;
  font-size: 17px;
}
</style>
