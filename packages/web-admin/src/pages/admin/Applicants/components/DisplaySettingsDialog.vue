<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-dialog v-model="show">
    <q-card :loading="isFetchingSurveyResultsPartitionKeys || isFetchingSurveyResultsItems" class="tw-w-full text-grey-8" style="max-width: 800px">
      <q-bar class="bg-primary tw-h-1" dark > </q-bar>
      <q-toolbar flat>
        <q-toolbar-title>表示設定</q-toolbar-title>
        <q-space/>
        <q-btn icon="mdi-close" @click="show = false" round flat>
        </q-btn>
      </q-toolbar>

      <q-card-section>
        <div>表示件数</div>
        <q-radio
          v-for="(option, index) in itemsPerPageOptions"
          v-model="selectedItemsPerPage"
          class="tw-mr-4"
          :key="index"
          :label="option.text"
          :val="option.value"
        />
      </q-card-section>
      <q-card-section>
        <div class="body-1">表示項目</div>
        <q-item multiple>
          <div class="row">
            <div v-for="(item, i) in filterdHeadersOptions" :key="i" class="col-12 col-md-6">
              <q-checkbox v-model="selectedHeaders" :val="item.name" :label="item.label" hide-details></q-checkbox>
            </div>
          </div>
        </q-item>
      </q-card-section>
      <q-card-actions>
        <q-space/>
        <q-btn
          class="tw-min-w-36 tw-w-36"
          color="primary"
          @click="handleSaveDisplaySettings()"
          :loading="isUpdatingSurveyResultsDisplaySettings"
        >
          保存
        </q-btn>
        <q-btn color="grey lighten-2" class="tw-min-w-36 tw-w-36" @click="show = false">
          閉じる
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, watch } from "vue";

import { DEFAULT_ALL_HEADERS, ITEMS_PER_PAGE_OPTIONS } from "@/stores/modules/surveyResults/surveyResults.constants";

import { useSurveyResultsStore } from "@/stores/modules/surveyResults";
import { useMemberResultsStore } from "@/stores/modules/memberResults";
import { useAuthStore } from "@/stores/modules/auth.module";
import { useQuasar } from "quasar";

// quasar
const $q = useQuasar();

// store
const surveyResultsStore = useSurveyResultsStore();
const memberResultsStore = useMemberResultsStore();
const authStore = useAuthStore();

// props
const props = defineProps<{
  visible: boolean,
}>();

const emit = defineEmits(['close']);

// computed
const currentUser = computed(() => authStore.user);

const selectedSurvey = computed(() => surveyResultsStore.selectedSurvey);
const displaySettings = computed(() => surveyResultsStore.displaySettings);
const allHeaders = computed(() => surveyResultsStore.allHeaders);

const isFetchingSurveyResultsPartitionKeys = computed(() => surveyResultsStore.isFetchingSurveyResultsPartitionKeys);
const isFetchingSurveyResultsItems = computed(() => surveyResultsStore.isFetchingSurveyResultsItems);
const isUpdatingSurveyResultsDisplaySettings = computed(() => surveyResultsStore.isUpdatingSurveyResultsDisplaySettings);

const fetchSurveyResultsDisplayCountError = computed(() => surveyResultsStore.fetchSurveyResultsDisplayCountError);
const fetchSurveyResultsPartitionKeysError = computed(() => surveyResultsStore.fetchSurveyResultsPartitionKeysError);
const fetchSurveyResultsItemsError = computed(() => surveyResultsStore.fetchSurveyResultsItemsError);

const fetchSurveyResultsDisplaySettingsError = computed(() => surveyResultsStore.fetchSurveyResultsDisplaySettingsError);
const updateSurveyResultsDisplaySettingsError = computed(() => surveyResultsStore.updateSurveyResultsDisplaySettingsError);

const headerOptions = computed((): any => {
  const defaultHeaders = DEFAULT_ALL_HEADERS;
  return allHeaders.value.filter((item: any) => !defaultHeaders.includes(item));
});

const itemsPerPageOptions = computed((): any => {
  return ITEMS_PER_PAGE_OPTIONS;
});

const show = computed({
  get(): boolean {
    return props.visible;
  },
  set(value: boolean): void {
    if (!value) {
      emit("close");
    }
  },
});

const selectedHeaders = computed({
  get(): any {
    return displaySettings.value.headers.map((obj) => obj.name);
  },
  set(value: any): void {
    const headers = allHeaders.value.filter((item: any) => value.includes(item.name));
    surveyResultsStore.setSurveyResultsDisplaySettings({
      headers: headers,
      itemsPerPage: selectedItemsPerPage.value
    });
  },
});

const selectedItemsPerPage = computed({
  get(): number {
    return displaySettings.value.itemsPerPage;
  },
  set(value: number): void {
    surveyResultsStore.setSurveyResultsDisplaySettings({
      headers: displaySettings.value.headers,
      itemsPerPage: value
    });
  },
});

const filterdHeadersOptions = computed((): any => {
  if (!selectedSurvey.value || headerOptions.value.length === 0) {
    return headerOptions.value;
  }

  const surveySchema = selectedSurvey.value.surveySchema;
  const countVaccinesInput = surveySchema.find((item) => item.type === 'countVaccines')?.default;
  // ワクチンN回目接種の「接種回数」が1以外の場合、そのままのヘッダーを返却する
  if (Number(countVaccinesInput) !== 1) {
    return headerOptions.value;
  }

  // 「前回の接種ワクチンのメーカー」「前回の接種実施日」を削除
  const previousVaccineItemTypes = ['previousVaccineMaker', 'previousVaccineDate'];
  const filterTitles = surveySchema.reduce((titles: any, item) => {
    const { type, title } = item;
    if (previousVaccineItemTypes.includes(type)) {
      titles.push(title);
    }
    return titles;
  }, []);
  const filterdOptions = headerOptions.value.filter((header: any) => {
    return !filterTitles.includes(header.text);
  });
  return filterdOptions;
});

// watch
watch(() => show.value,
  () => {
    surveyResultsStore.fetchSurveyResultsDisplaySettings({
      userId: currentUser.value.username
    });

    if (fetchSurveyResultsDisplaySettingsError.value) {
      // emitSnackbar(this.$snackbar, fetchSurveyResultsDisplaySettingsError.value, "error");
      $q.notify({
        message: fetchSurveyResultsDisplaySettingsError.value,
      });
    }
  },
  { deep: true }
);

// methods
const handleFetchSurveyResults = async (): Promise<void> => {
  await Promise.all([
    await surveyResultsStore.fetchSurveyResultsPartitionKeys(), 
    await surveyResultsStore.fetchSurveyResultsItems(),
    await memberResultsStore.fetchMemberResultsPartitionKeys(),
    await memberResultsStore.fetchMemberResultsItems()
  ]);

  await surveyResultsStore.fetchSurveyResultsDisplayCount();
  await memberResultsStore.fetchMemberResultsDisplayCount();
  
  if (fetchSurveyResultsPartitionKeysError.value) {
    // emitSnackbar(this.$snackbar, fetchSurveyResultsPartitionKeysError.value, "error");
    $q.notify({
      message: fetchSurveyResultsPartitionKeysError.value,
    });
  }
  if (fetchSurveyResultsItemsError.value) {
    // emitSnackbar(this.$snackbar, fetchSurveyResultsItemsError.value, "error");
    $q.notify({
      message: fetchSurveyResultsItemsError.value,
    });
  }
  if (fetchSurveyResultsDisplayCountError.value) {
    // emitSnackbar(this.$snackbar, fetchSurveyResultsDisplayCountError.value, "error");
    $q.notify({
      message: fetchSurveyResultsDisplayCountError.value,
    });
  }
};

const handleSaveDisplaySettings = async (): Promise<void> => {
  await surveyResultsStore.updateSurveyResultsDisplaySettings({
    userId: currentUser.value.username,
    setting: displaySettings.value
  }).then(async (response) => {
    show.value = false;
    surveyResultsStore.resetSurveyResultsList();
    memberResultsStore.resetMemberResultsList();
    await handleFetchSurveyResults();
    // emitSnackbar(this.$snackbar, response.message);
    $q.notify({
      message: response.message,
    });
  });

  if (updateSurveyResultsDisplaySettingsError.value) {
    // emitSnackbar(this.$snackbar, updateSurveyResultsDisplaySettingsError.value, "error")
    $q.notify({
      message: updateSurveyResultsDisplaySettingsError.value,
    });
  }
};
</script>
