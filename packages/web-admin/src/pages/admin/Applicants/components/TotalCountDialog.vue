<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
  .dialogue-error {
    width: 100%,
  }
</style>
<template>
  <q-dialog v-model="show" max-width="800" :persistent="isFetchingSurveyResultsTotalCount">
    <q-card class="tw-w-full" style="min-width: 700px;">
      <q-bar class="tw-h-1 bg-primary"> </q-bar>
      <q-toolbar flat>
        <q-toolbar-title class="tw-font-semibold">全体件数を取得</q-toolbar-title>
        <q-space/>
        <q-btn icon="mdi-close" round flat @click="show = false" :disabled="isFetchingSurveyResultsTotalCount">
        </q-btn>
      </q-toolbar>
      <div class="tw-p-4">
        <q-card-section class="tw-py-0 tw-my-2">
          [全体件数を取得] ボタンをクリックすると、検索条件に該当する全データの件数を取得します。
        </q-card-section>
        <q-card flat bordered class="tw-mx-3">
          <q-list three-line>
            <q-item class="tw-px-4 tw-py-6">
              <q-icon size="md" color="warning" name="mdi-alert-outline"></q-icon>
              <q-item-section>
                <q-item-label class="tw-mx-3 text-grey-8">
                  データ量が多い場合、数分ほど時間がかかる場合があります。 [キャンセル]
                  ボタンを押して、いつでもアクションを中止することができます。
                </q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-card>
      </div>
      <div fluid class="tw-p-4">
        <div v-if="fetchSurveyResultsTotalCountError" class="tw-mx-3 tw-pb-3">
          <Alert 
            v-if="wasErrorInterrupted" 
            type="error" 
            class="dialogue-error"
          >
            {{ fetchSurveyResultsTotalCountError }}
          </Alert>
        </div>
        <div align="center" class="tw-mx-3 row">
          <div class="col-auto">
            <q-btn
              color="primary"
              class="tw-mr-3"
              min-width="150"
              @click="handleScan"
              :loading="isFetchingSurveyResultsTotalCount"
            >
              全体件数を取得
            </q-btn>
            <q-btn
              color="grey lighten-2"
              min-width="150"
              :disable="!isFetchingSurveyResultsTotalCount"
              @click="handleCancel"
            >
              キャンセル
            </q-btn>
          </div>
          <q-space/>
          <q-separator vertical/>
          <div class="col-3 tw-p-3 tw-text-start">
            総件数：<span class="tw-font-semibold">{{ surveyResultsTotalCount }}</span>
          </div>
          <q-space/>
          <q-separator vertical/>
          <div class="col-3 tw-p-3 tw-text-start">
            ステータス：<span class="tw-font-semibold">{{ status }}</span>
          </div>
        </div>
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { useSurveyResultsStore } from "@/stores/modules/surveyResults";
import Alert from "@/components/common/Alert.vue";

// store
const surveyResultsStore = useSurveyResultsStore();

// props
const props = defineProps<{
  visible: boolean,
}>();

const emit = defineEmits(['close']);

// data
const isTerminated = ref(false);
const isFinished = ref(false);
const wasErrorInterrupted = ref(false);

 // computed
const surveyResultsTotalCount = computed(() => surveyResultsStore.surveyResultsTotalCount);
const isFetchingSurveyResultsTotalCount = computed(() => surveyResultsStore.isFetchingSurveyResultsTotalCount);
const fetchSurveyResultsTotalCountError = computed(() => surveyResultsStore.fetchSurveyResultsTotalCountError);

const show = computed({
  get(): boolean {
    return props.visible;
  },
  set(value: boolean): void {
    if (!value) {
      emit("close");
      isTerminated.value = false;
      isFinished.value = false;
      wasErrorInterrupted.value = false;
    }
  },
});

const status = computed((): string => {
  if (isFetchingSurveyResultsTotalCount.value) {
    return "取得中";
  } else if (isTerminated.value) {
    return "中止";
  } else if (wasErrorInterrupted.value) {
    return "エラー";
  } else if (isFinished.value) {
    return "完了";
  } else {
    return "ーー";
  }
});

 // methods
const handleScan = async (): Promise<void> => {
  isTerminated.value = false;
  isFinished.value = false;
  wasErrorInterrupted.value = false;

  surveyResultsStore.setSurveyResultsTotalCount({
    totalCount: 0,
  });

  var response = await surveyResultsStore.fetchSurveyResultsTotalCount({
    resetCount: true,
  });

  while (response.result != 'ERROR' && response.data.lastEvaluatedKey && !isTerminated.value && !response.data.isTimedOut) {
    response = await surveyResultsStore.fetchSurveyResultsTotalCount();
    surveyResultsStore.setIsTotalCountTerminated(isTerminated.value);

    if (!response.data.lastEvaluatedKey) {
      isFinished.value = true;
      surveyResultsStore.setIsTotalCountFinished(isFinished.value);
    }
  }

  if (response.result === 'ERROR' || response.data.isTimedOut) {
    wasErrorInterrupted.value = true;
  }
};

const handleCancel = (): void => {
  isTerminated.value = true;
};
</script>
