<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <div v-if="isShowAdvancedSearch" class="row tw-p-3">
      <div class="col">
        <DateSearchSelector itemKey="createdAt" label="作成日" />
      </div>
    </div>
    <div class="row tw-p-3">
      <div class="col">
        <DateSearchSelector itemKey="updatedAt" label="更新日" />
      </div>
    </div>
    <div v-if="!isMemberSearch && isReservationSurvey" class="row tw-p-3">
      <div class="col">
        <DateSearchSelector itemKey="appointment_date" label="予約日" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSurveyResultsStore } from "@/stores/modules/surveyResults";
import { computed } from "vue";
import DateSearchSelector from "@/pages/admin/Applicants/components/SurveyResultsSearch/DateSearchSelector.vue";

// store
const surveyResultsStore = useSurveyResultsStore();

// props
const props = defineProps<{
  isMemberSearch: boolean,
}>();

// computed
const isReservationSurvey = computed(() => surveyResultsStore.isReservationSurvey);
const isShowAdvancedSearch = computed(() => surveyResultsStore.isShowAdvancedSearch);
</script>