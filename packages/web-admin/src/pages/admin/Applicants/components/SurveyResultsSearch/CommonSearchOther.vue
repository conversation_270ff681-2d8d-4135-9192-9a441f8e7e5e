<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <div class="row tw-p-3">
      <div class="tw-pr-3 col-3">
        <span class="body-2 grey--text text--darken-3">項目</span>
        <q-select
          class="tw-py-1"
          v-model="selectedItemKey"
          :options="fieldOptions"
          option-label="text"
          option-value="value"
          flat
          outlined
          dense
          clearable
          background-color="white"
          :label-slot="selectedItemKey ? false : true"
        >
          <template v-slot:label>
            項目
          </template>
        </q-select>
      </div>
      <div class="tw-pr-3 col-3">
        <span class="body-2 grey--text text--darken-3">条件</span>
        <q-select
          class="tw-py-1"
          v-model="selectedMode"
          :options="modeOptions"
          option-label="text"
          option-value="value"
          map-options emit-value
          flat
          outlined
          dense
          clearable
          background-color="white"
          :label-slot="selectedMode ? false : true"
        >
          <template v-slot:label>
            条件
          </template>
        </q-select>
      </div>
      <div class="col-6">
        <span class="body-2 grey--text text--darken-3">キーワード</span>
        <q-input
          v-model="keyword"
          class="tw-py-1"
          :placeholder="placeholder"
          :disable="!selectedItemKey || !selectedMode"
          outlined
          dense
          single-line
          hide-details
          :type="input_type"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, watch } from "vue";
import { isEmpty } from "lodash";
import { IME_TYPE } from "@/constants/index";
import { useSurveyResultsStore } from "@/stores/modules/surveyResults";
import { useMemberResultsStore } from "@/stores/modules/memberResults";

// store
const surveyResultsStore = useSurveyResultsStore();
const memberResultsStore = useMemberResultsStore();

// computed
const surveyType = computed(() => {
  if(surveyResultsStore.selectedSurvey !== null) {
    return 'normal';
  } else {
    return 'member';
  }
});
const otherItemType = computed(() => {
  if(surveyType.value === "normal") {
    return surveyResultsStore.otherItemType;
  } else {
    return memberResultsStore.otherItemType;
  }
});
const commonSearchCriteria = computed(() => {
  if(surveyType.value === "normal") {
    return surveyResultsStore.commonSearchCriteria;
  } else {
    return memberResultsStore.commonSearchCriteria;
  }
});
const selectedItemKey = computed({
  get: () => {
    return surveyResultsStore.otherItemKey?.value ? surveyResultsStore.otherItemKey: null;
  },
  set: (value) => {
    if (!value) {
      // Clear store for this itemKey
      if (surveyType.value === "normal") {
        surveyResultsStore.updateSurveyResultsCommonSearchCriteria({ itemKey: selectedItemKey.value?.value, condition: null });
      } else {
        memberResultsStore.updateSurveyResultsCommonSearchCriteria({ itemKey: selectedItemKey.value?.value, condition: null });
      }
    }
    surveyResultsStore.setOtherItemKey(value);
  }
});

const fieldOptions = computed((): any[] => {
  return otherItemType.value.map((obj: any) => {
    return {
      text: obj.title,
      value: obj.itemKey,
    };
  });
});

const modeOptions = computed((): any => {
  return [
    { text: "完全一致", value: "exact" },
    { text: "部分一致", value: "contains" },
  ];
});

const placeholder = computed((): string => {
  if (selectedItemKey.value) {
    const field = fieldOptions.value.find((obj: any) => obj.value === selectedItemKey.value.value);
    return field && field.text + "を入力";
  }
  return "キーワードを入力";
});

const selectedMode = computed({
  get(): any {
    return commonSearchCriteria.value[selectedItemKey.value?.value]
      ? commonSearchCriteria.value[selectedItemKey.value?.value].mode
      : null;
  },
  set(value: any): void {
    if (!value) {
      // Clear store for this condition
      if(surveyType.value === "normal") {
        surveyResultsStore.updateSurveyResultsCommonSearchCriteria({ itemKey: selectedItemKey.value?.value, condition: null });
      } else {
        memberResultsStore.updateSurveyResultsCommonSearchCriteria({ itemKey: selectedItemKey.value?.value, condition: null });
      }
      return;
    }
    const condition = {
      values: keyword.value,
      mode: value,
    };
    if(surveyType.value === "normal") {
      surveyResultsStore.updateSurveyResultsCommonSearchCriteria({ itemKey: selectedItemKey.value?.value, condition: condition });
    } else {
      memberResultsStore.updateSurveyResultsCommonSearchCriteria({ itemKey: selectedItemKey.value?.value, condition: condition });
    }
  },
});

const keyword = computed({
  get(): any {
    return commonSearchCriteria.value[selectedItemKey.value?.value]
      ? commonSearchCriteria.value[selectedItemKey.value?.value].values
      : [];
  },
  set(value: any): void {
    const condition = {
      values: value ? [value] : [],
      mode: selectedMode.value,
    };
    if(surveyType.value === "normal") {
      surveyResultsStore.updateSurveyResultsCommonSearchCriteria({ itemKey: selectedItemKey.value.value, condition: condition });
    } else {
      memberResultsStore.updateSurveyResultsCommonSearchCriteria({ itemKey: selectedItemKey.value.value, condition: condition });
    }
  },
});

const input_type = computed(() => {
  let currentItem = otherItemType.value.find((item: any) => selectedItemKey.value === item.itemKey);

  if (currentItem) {
    if (currentItem.imeType in IME_TYPE) {
      return IME_TYPE[currentItem.imeType].input_type;
    }
  }
  return "text";
});

// watch
watch(
  commonSearchCriteria.value,
  (newVal: any) => {
    if (isEmpty(newVal)) {
      selectedItemKey.value = null;
      selectedMode.value = null;
    }
  }
);

onBeforeMount(() => {
  fieldOptions.value.forEach((item) => {
    if (surveyType.value === "normal") {
      if (surveyResultsStore.commonSearchCriteria[item.value]) {
        selectedItemKey.value = item;
      }
    } else {
      if (memberResultsStore.commonSearchCriteria[item.value]) {
        selectedItemKey.value = item;
      }
    }
  });
});
</script>
