<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <span>{{ label }}</span>
    <q-card flat bordered class="common-search-selector__container">
      <q-list dense class="tw-py-2">
        <q-item 
          clickable 
          v-ripple 
          v-for="option in options" 
          :key="option" 
          :active="isSelect(option)"
          active-class="bg-primary tw-text-white"
          @click="clickSelecter(option)"
        >
          <q-item-section>
            <q-item-label>{{ option }}</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </q-card>
    <div class="tw-py-1 text-right">
      <q-btn @click="toggleSelectAll" flat color="primary">{{ isSelectedAll ? "全解除" : "全選択" }}</q-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, ref, watch } from "vue";
import { useSurveyResultsStore } from "@/stores/modules/surveyResults";

// store
const surveyResultsStore = useSurveyResultsStore();

// props
const props = defineProps<{
  label: string,
  options: any[],
  itemKey: string,
}>();

// data
const selectItem = ref<string[]>();

// computed
const isSelectedAll = computed((): boolean => {
  return selectItem.value?.length === props.options.length;
});

// watch
watch(
  () => selectItem.value,
  (newVal) => {
    surveyResultsStore.updateSurveyResultsCommonSearchCriteria({ itemKey: props.itemKey, condition: newVal });
  }
);

// methods
const toggleSelectAll = (): void => {
  if (isSelectedAll.value) {
    selectItem.value = [];
  } else {
    selectItem.value = props.options;
  }
};

const clickSelecter = (value: string) => {
  let isDelete = false;

  if (!selectItem.value) selectItem.value = [];

  selectItem.value = selectItem.value.filter((item) => {
    if (item === value) {
      isDelete = true;
    }
    return item !== value;
  });

  if (!isDelete) {
    selectItem.value.push(value);
  }
};

const isSelect = (value: string) => {
  return selectItem.value?.filter(item => item === value).length > 0;
};

watch(() => surveyResultsStore.commonSearchCriteria,
  () => {
    if(props.itemKey) {
      selectItem.value = surveyResultsStore.commonSearchCriteria[props.itemKey];
    } else {
      selectItem.value = [];
    }
  }
);

onBeforeMount(() => {
  if (props.itemKey && props.itemKey.length > 0) {
    selectItem.value = surveyResultsStore.commonSearchCriteria[props.itemKey];
  }
  else {
    selectItem.value = [];
  }
});
</script>

<style lang="less">
.common-search-selector__container {
  height: 100px;
  overflow-y: auto;
}
.common-search-selector__active {
  border-left: 2px solid var(--v-primary-base);
}
</style>
