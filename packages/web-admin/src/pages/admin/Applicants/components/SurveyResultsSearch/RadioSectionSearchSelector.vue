<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <span class="body-2 grey--text text--darken-3">{{ label }}</span>
    <q-card class="common-search-selector__container" flat bordered>
      <q-list dense class="tw-py-2">
        <q-item 
          v-for="sectionOption in options" 
          :key="sectionOption.option.value"
          clickable 
          v-ripple 
          :active="isSelect(sectionOption.option.value)"
          active-class="bg-primary tw-text-white"
          @click="clickSelecter(sectionOption.option.value)"
        >
          <q-item-section>
            <q-item-label>{{ sectionOption.option.value }}</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </q-card>
    <div class="tw-py-1 text-right">
      <q-btn class="body-2" flat color="primary" @click="toggleSelectAll">{{ isSelectedAll ? "全解除" : "全選択" }}</q-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useSurveyResultsStore } from "@/stores/modules/surveyResults";

const surveyResultsStore = useSurveyResultsStore();

// props
const props = defineProps<{
  label: string,
  options: any[],
  itemKey: string,
}>();

// computed
const commonSearchCriteria = computed(() => surveyResultsStore.commonSearchCriteria);
const isSelectedAll = computed((): boolean => {
  return selected.value.length === props.options.length;
});

const selected = computed({
  get(): any {
    return commonSearchCriteria.value[props.itemKey] || [];
  },
  set(value: any): void {
    let options: any[] = [];
    value.forEach((element: any) => {
      if (typeof element == "string") {
        options.push(element);
      } else if (element && element.option && element.option.value) {
        options.push(element.option.value);
      }
    });
    surveyResultsStore.updateSurveyResultsCommonSearchCriteria({ itemKey: props.itemKey, condition: options });
  },
});

// methods
const toggleSelectAll = (): void => {
  if (isSelectedAll.value) {
    selected.value = [];
  } else {
    selected.value = props.options;
  }
};

const isActiveItem = (key: any): any => {
  return selected.value && selected.value.includes(key);
};

const clickSelecter = (value: string) => {
  let isDelete = false;
  selected.value = selected.value.filter((item: any) => {
    if (item === value) {
      isDelete = true;
    }
    return item !== value;
  });

  if (!isDelete) {
    selected.value.push(value);
  }
};

const isSelect = (value: string) => {
  return selected.value.filter((item: any) => item === value).length > 0;
};
</script>

<style lang="less">
.common-search-selector__container {
  height: 100px;
  overflow-y: auto;
}
.common-search-selector__active {
  border-left: 2px solid var(--v-primary-base);
}
</style>
