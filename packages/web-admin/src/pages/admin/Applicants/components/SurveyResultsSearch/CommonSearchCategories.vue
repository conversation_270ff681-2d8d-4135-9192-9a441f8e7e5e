<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <div class="row">
      <div class="tw-pr-3 col-4">
        <span class="body-2 text-grey-8">{{ calendarDisplaySettings.tag1 || "ーー" }}</span>
        <q-select
          class="q-py-1"
          v-model="tag1"
          :options="categoriesTag1"
          flat
          outlined
          dense
          clearable
          background-color="white"
          placeholder="ーー"
        />
      </div>
      <div class="tw-pr-3 col-4">
        <span class="body-2 text-grey-8">{{ calendarDisplaySettings.tag2 || "ーー" }}</span>
        <q-select
          class="q-py-1"
          v-model="tag2"
          :options="categoriesTag2"
          flat
          outlined
          dense
          clearable
          background-color="white"
          placeholder="ーー"
          :disabled="isDisabledCategoryTag2"
        />
      </div>
      <div class="tw-pr-3 col-4">
        <span class="body-2 text-grey-8">{{ calendarDisplaySettings.tag3 || "ーー" }}</span>
        <q-select
          class="q-py-1"
          v-model="tag3"
          :options="categoriesTag3"
          flat
          outlined
          dense
          clearable
          background-color="white"
          placeholder="ーー"
          :disabled="isDisabledCategoryTag3"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { map, find, isEmpty } from "lodash";
import { UPDATE_SURVEY_RESULTS_CATEGORY_SEARCH_CRITERIA } from "@/stores/modules/surveyResults/mutations-types";
import { isNullOrEmpty } from "@/utils/stringUtils";
import { useSurveyResultsStore } from "@/stores/modules/surveyResults";

// store
const surveyResultsStore = useSurveyResultsStore();

const props = defineProps<{
  label: string,
  itemKey: string,
  index: number,
}>();

// computed
    // ...mapState({
const selectedSurvey = computed(() => surveyResultsStore.selectedSurvey);
    //   commonSearchCriteria: (state: any) => state.surveyResults.commonSearchCriteria,
const categorySearchCriteria = computed(() => surveyResultsStore.categorySearchCriteria);
const allCalendarCategories = computed(() => surveyResultsStore.allCalendarCategories);
const allCategoriesTree = computed(() => surveyResultsStore.allCategoriesTree);
const calendarDisplaySettings = computed(() => surveyResultsStore.calendarDisplaySettings);
    // }),
const reservationOptions = computed(() => {
  return allCalendarCategories.value.map((obj) => {
    return {
      text: obj.id,
      value: obj.id,
    };
  });
});

const tag1 = computed({
  get(): any {
    return categorySearchCriteria.value ? categorySearchCriteria.value.tag1 : undefined;
  },
  set(value: any): void {
    const condition = {
      tag1: value ? value : undefined,
    };
    surveyResultsStore.updateSurveyResultsCategorySearchCriteria({ itemKey: props.itemKey, condition: condition });
    if (!value) {
      tag2.value = undefined;
      tag3.value = undefined;
    }
  },
});

const tag2 = computed({
  get(): any {
    return categorySearchCriteria.value ? categorySearchCriteria.value.tag2 : undefined;
  },
  set(value: any): void {
    const condition = {
      tag2: value ? value : undefined,
    };
    surveyResultsStore.updateSurveyResultsCategorySearchCriteria({ itemKey: props.itemKey, condition: condition });
    if (!value) {
      tag3.value = undefined;
    }
  },
});

const tag3 = computed({
  get(): any {
    return categorySearchCriteria.value ? categorySearchCriteria.value.tag3 : undefined;
  },
  set(value: any): void {
    const condition = {
      tag3: value ? value : undefined,
    };
    surveyResultsStore.updateSurveyResultsCategorySearchCriteria({ itemKey: props.itemKey, condition: condition });
  },
});

const categoriesTag1 = computed((): any => {
  let fixTag1 = getFixedSelectedLargeCategory();
  if (fixTag1) {
    return [fixTag1];
  }
  if (allCategoriesTree.value && allCategoriesTree.value.tree) {
    return map(allCategoriesTree.value.tree, "name");
  }
  return [];
});

const categoriesTag2 = computed((): any => {
  if (!tag1.value) {
    return [];
  }

  let fixTag2 = getFixedSelectedMediumCategory();
  if (fixTag2) {
    return [fixTag2];
  }
  if (allCategoriesTree.value && allCategoriesTree.value.tree) {
    let cTag1 = find(allCategoriesTree.value.tree, { name: tag1.value });
    if (cTag1 && cTag1.children) {
      return map(cTag1.children, "name");
    }
  }
  return [];
});

const categoriesTag3 = computed((): any => {
  if (allCategoriesTree.value && allCategoriesTree.value.tree) {
    let cTag1 = find(allCategoriesTree.value.tree, { name: tag1.value });
    if (cTag1 && cTag1.children) {
      let cTag2 = find(cTag1.children, { name: tag2.value });
      if (cTag2 && cTag2.children) {
        return map(cTag2.children, "name");
      }
    }
  }
  return [];
});

const isDisabledCategoryTag2 = computed((): boolean => {
  return categoriesTag2.value && !isEmpty(categoriesTag2.value) ? false : true;
});

const isDisabledCategoryTag3 = computed((): boolean => {
  return categoriesTag3.value && !isEmpty(categoriesTag3.value) ? false : true;
});

  // methods
    // ...mapMutations({
    //   updateSearchCriteria: UPDATESURVEYRESULTSCATEGORYSEARCHCRITERIA,
    // }),
// const isActiveItem = (key: any): any => {
//   return selecteds.value && selecteds.value.includes(key);
// };

const placeholder = (value: any): string => {
  return value + "を入力";
};

const getFixedSelectedLargeCategory = (): any => {
  if (selectedSurvey.value) {
    let surveySchema = selectedSurvey.value.surveySchema;
    for (var i = 0; i < surveySchema.length; i++) {
      if (surveySchema[i].type === "reservation") {
        if (surveySchema[i].selectedLargeCategory && surveySchema[i].selectedLargeCategory.name) {
          return surveySchema[i].selectedLargeCategory.name;
        }
      }
    }
  }
  return null;
};

const getFixedSelectedMediumCategory = (): any => {
  if (selectedSurvey.value) {
    let surveySchema = selectedSurvey.value.surveySchema;
    for (var i = 0; i < surveySchema.length; i++) {
      if (surveySchema[i].type === "reservation") {
        if (surveySchema[i].selectedMediumCategory && surveySchema[i].selectedMediumCategory.name) {
          return surveySchema[i].selectedMediumCategory.name;
        }
      }
    }
  }
  return null;
};
</script>
