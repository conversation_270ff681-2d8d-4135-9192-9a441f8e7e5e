<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <span class="text-body2 text-grey-8">{{ label }}</span>
    <q-menu
      ref="menu_date"
      :close-on-content-click="false"
      v-model="menu_date"
      transition="scale-transition"
      offset-y
      min-width="290px"
    >
      <!-- <template v-slot:activator="{ on }"> -->
        <q-input
          class="q-py-xs"
          v-model="keyword"
          prepend-inner-icon="mdi-calendar-outline"
          placeholder="YYYY-MM-DD"
          readonly
          hide-details
          outlined
          dense
          clearable
          />
          <!-- v-on="on" -->
      <!-- </template> -->
      <q-date
        ref="picker"
        v-model="keyword"
        :picker-date="pickerDate"
        no-title
        scrollable
        @change="saveDate"
        @input="menu_date = false"
      >
      </q-date>
    </q-menu>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import dayjs from "dayjs";
import { useSurveyResultsStore } from "@/stores/modules/surveyResults";
import { useMemberResultsStore } from "@/stores/modules/memberResults";

// interface LocalState {
//   menu_date: any;
//   pickerDate: string;
// }

// store
const surveyResultsStore = useSurveyResultsStore();
const memberResultsStore = useMemberResultsStore();

const props = defineProps<{
  label: string,
  itemKey: string,
  index: number,
  // index: {
  //   type: number,
  //   default: null,
  // },
}>();

// data
const menu_date = ref(false);
const pickerDate = ref("");


// computed
const surveyType = computed(() => {
  if (surveyResultsStore.selectedSurvey !== null) {
    return "normal";
  } else {
    return "member";
  }
});

// ref data
const picker = ref();

// computed
const commonSearchCriteria = computed(() => {
  if (surveyType.value === "normal") {
    return surveyResultsStore.commonSearchCriteria;
  } else {
    return memberResultsStore.commonSearchCriteria;
  }
});

// watch
watch(
  () => menu_date.value,
  (newVal) => {
    newVal && setTimeout(() => {
      if (picker.value) {
        picker.value.activePicker = "YEAR";
        pickerDate.value = "";
      }
    });
  }
);

watch(
  () => commonSearchCriteria,
  (newVal) => {
    if(!newVal.value.hasOwnProperty(props.itemKey)) {
      pickerDate.value = "";
    }
  }, { deep: true }
);

// computed
    // ...mapState({
    //   commonSearchCriteria: (state: any) => state.surveyResults.commonSearchCriteria,
    // }),

const placeholder = computed((): string => {
  return props.label + "を入力";
});

const keyword = computed({
  get(): any {
    return pickerDate.value || null;
  },
  set(value: any): void {
    let values: any[] = [];
    if (value) {
      const date = dayjs(value);
      values = [
        date.format("YYYY-MM-DD"),
        date.format("YYYY/MM/DD"),
        date.format("YYYY/M/D"),
        date.format("YYYYMMDD"),
      ]
    }

    const condition = {
      values,
      displayValue: value,
      mode: "exact",
    };
    if(surveyType.value === "normal") {
      surveyResultsStore.updateSurveyResultsCommonSearchCriteria({ itemKey: props.itemKey, condition: condition });
    } else {
      memberResultsStore.updateSurveyResultsCommonSearchCriteria({ itemKey: props.itemKey, condition: condition });
    }
  },
});

// methods
    // ...mapMutations({
    //   updateSearchCriteria: UPDATESURVEYRESULTSCOMMONSEARCHCRITERIA,
    // }),
const saveDate = (value: any): void => {
  pickerDate.value = value;
};
</script>
