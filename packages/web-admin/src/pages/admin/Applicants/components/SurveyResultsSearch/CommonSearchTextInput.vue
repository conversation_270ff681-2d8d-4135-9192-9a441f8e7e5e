<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <span class="text-body2 text-grey-8">{{ label }}</span>
    <q-input
      v-model="keyword"
      outlined
      dense
      hide-bottom-space
      :placeholder="placeholder"
      :autofocus="index == 0 && autofocus"
      :type="input_type"
    />
    <span class="q-mx-xs text-caption text-grey-6 text-italic">※ 完全一致・単一項目検索のみ可能です。</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { IME_TYPE } from "@/constants/index";
import { useSurveyResultsStore } from "@/stores/modules/surveyResults";
import { useMemberResultsStore } from "@/stores/modules/memberResults";

// store
const surveyResultsStore = useSurveyResultsStore();
const memberResultsStore = useMemberResultsStore();

// props
const props = defineProps<{
  item: any,
  label: string,
  itemKey: string,
  index: number,
  autofocus: boolean,
}>();

// computed
const surveyType = computed(() => {
  if(surveyResultsStore.selectedSurvey !== null) {
    return 'normal';
  } else {
    return 'member';
  }
});

const commonSearchCriteria = computed(() => {
  if(surveyType.value === 'normal') {
    return surveyResultsStore.commonSearchCriteria;
  } else {
    return memberResultsStore.commonSearchCriteria;
  }
});

const placeholder = computed((): string => {
  return props.label + "を入力";
});

const keyword = computed({
  get(): any {
    return commonSearchCriteria.value[props.itemKey] ? commonSearchCriteria.value[props.itemKey].values : [];
  },
  set(value: any): void {
    const condition = {
      values: value ? [value] : [],
      mode: "exact",
    };
    if(surveyType.value === 'normal') {
      surveyResultsStore.updateSurveyResultsCommonSearchCriteria({ itemKey: props.itemKey, condition: condition });
    } else {
      memberResultsStore.updateSurveyResultsCommonSearchCriteria({ itemKey: props.itemKey, condition: condition });
    }
  },
});

const input_type = computed(() => {
  if (props.item.imeType in IME_TYPE) {
    return IME_TYPE[props.item.imeType].input_type;
  }
  return "text";
});

// methods
// const isActiveItem = (key: any): any => {
//   return selecteds.value && selecteds.value.includes(key);
// };
</script>
