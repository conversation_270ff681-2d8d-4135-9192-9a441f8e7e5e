<template>
  <div class="row" justify="center">
    <q-dialog v-model="show" width="600">
      <q-card class="tw-w-full">
        <q-bar class="bg-primary tw-h-1" dark></q-bar>
        <q-toolbar flat>
          <q-toolbar-title> 管理者メモ編集 </q-toolbar-title>
          <q-space/>
          <q-btn icon="mdi-close" @click="show = false" round flat>
          </q-btn>
        </q-toolbar>
        <div fluid v-if="selectedSurvey && selectedSurvey.isAppending && selectedSurvey.isAppending.value">
          <q-input 
            v-model="localDetailData.note" 
            class="tw-mx-4"
            outlined hide-details
            type="textarea"
          />
        </div>
        <div fluid>
          <div class="row tw-py-4">
            <div class="col tw-px-2">
              <q-btn
                color="primary"
                class="tw-mr-3 tw-w-full"
                block
                @click="permissionHelper.hasActionPermission('click', 'backendRequest') ? handleUpdate() : permissionHelper.showActionPermissionError()"
                :loading="isUpdatingData"
              >
                保存
              </q-btn>
            </div>
            <div class="col tw-pr-2">
              <q-btn color="grey lighten-2" class="tw-w-full" block @click="show = false"> 閉じる </q-btn>
            </div>
          </div>
        </div>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, defineEmits } from "vue";
import { cloneDeep } from "lodash";
import { useSurveyResultsStore } from "@/stores/modules/surveyResults";
import { useDataManagementStore } from "@/stores/modules/dataManagement";
import { useQuasar } from "quasar";
import { usePermissionHelper } from "@/mixins/PermissionHelper";

// quasar
const $q = useQuasar();

// store
const surveyResultsStore = useSurveyResultsStore();
const dataManagementStore = useDataManagementStore();

const permissionHelper = usePermissionHelper();

// props
const props = defineProps<{
  visible: boolean,
  detailData: any,
  selectedSurvey: any,
}>();
// emits

const emit = defineEmits(["close", "reloadSR"]);

// data
const localDetailData = ref(cloneDeep(props.detailData));

// computed
const surveyConfig = computed(() => surveyResultsStore.selectedSurvey);
const isUpdatingData = computed(() => dataManagementStore.isUpdatingData);
const updateDataError = computed(() => dataManagementStore.updateDataError);

const show = computed({
  get(): boolean {
    return props.visible;
  },
  set(value: boolean): void {
    if (!value) {
      emit("close");
    }
  },
});

// watch
watch(
  () => props.detailData,
  (newVal) => {
    localDetailData.value = cloneDeep(newVal);
  }
);

// methods
const handleUpdate = async (): Promise<void> => {
  let surveySchema = cloneDeep(props.selectedSurvey.surveySchema);
  let surveyResults: any = [];
  if (surveySchema) {
    surveySchema.forEach((element: any) => {
      const findData = localDetailData.value.fields.filter((field) => field.itemKey === element.itemKey);
      if (!findData) {
        return;
      }
      if (element.type === "groupheader" || element.type === "guide") {
        return;
      }
      if (element.type === "checkboxes") {
        if (Array.isArray(findData)) {
          findData.forEach((answer: any) => {
            let surveyResult: any = {};
            surveyResult.itemKey = element.itemKey;
            surveyResult.title = element.title;
            surveyResult.value = answer.value;
            surveyResults.push(surveyResult);
          });
        }
        return;
      }
      let surveyResult: any = {};
      surveyResult.itemKey = element.itemKey;
      surveyResult.title = element.title;
      surveyResult.value = findData[0]?.value;
      surveyResults.push(surveyResult);
    });
  }
  // データベースを更新するため、formDataを作ります。
  let formData: any = {
    surveyId: cloneDeep(props.selectedSurvey.surveyId),
    surveyResults: surveyResults,
    userId: cloneDeep(localDetailData.value.id),
  };

  let surveyResult: any = {};
  if (props.selectedSurvey && props.selectedSurvey.isAppending && props.selectedSurvey.isAppending.value) {
    surveyResult = {
      ...formData,
      partitionKey: localDetailData.value.partitionKey,
      originalData: localDetailData.value,
      surveyConfig: surveyConfig.value,
      check: localDetailData.value.check ? localDetailData.value.check : "未対応",
      note: localDetailData.value.note,
      answerCode: localDetailData.value.answerCode,
    };
  } else {
    surveyResult = {
      ...props.selectedSurvey,
      partitionKey: localDetailData.value.partitionKey,
      originalData: localDetailData.value,
      surveyConfig: surveyConfig.value,
      check: localDetailData.value.check ? localDetailData.value.check : "未対応",
    };
  }

  await dataManagementStore.updateDataSurvey(surveyResult);
  if (updateDataError.value) {
    if (updateDataError.value.errorMessage && updateDataError.value.errorMessage.includes(403)) {
      $q.notify({
        message: "この操作を行う場合は、権限を管理者にお問い合わせください",
      });
    } else {
      $q.notify({
        message: updateDataError.value,
      });
    }
  } else {
    $q.notify({
      message: "管理者メモを更新しました。",
    });
    show.value = false;
    emit("reloadSR");
  }
};
</script>
