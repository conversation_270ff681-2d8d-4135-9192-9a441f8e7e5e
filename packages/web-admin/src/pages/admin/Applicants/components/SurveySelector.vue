<template>
  <div class="row">
    <div class="col-auto tw-py-2 tw-px-4">
      <label>帳票</label>
    </div>
    <div class="col-10">
      <q-select
        v-model="selected" 
        :options="options"
        option-label="text"
        option-value="value"
        :disable="isLoadingSurvey"
        outlined 
        dense 
        hide-selected
        use-input
        @filter="filterFn"
        fill-input
        clear-icon="close"
        @input-value="inputFn"
      />
    </div>
    <div class="col-auto col-mx-2">
      <q-btn 
        flat
        color="primary"
        style="width: 20px;"
        :ripple="false" 
        :disabled="isLoadingSurvey" 
        :icon="isFetchingSurveyConfigsListType || isLoadingSurvey ? 'mdi-cached mdi-spin' : 'mdi-cached'"
        @click="handleFetchSurveyConfigs(true)"
      >
      </q-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, onMounted, onUnmounted, ref, watch } from "vue";

import { useFormsStore } from "@/stores/modules/forms";
import { useMemberStore } from "@/stores/modules/member";
import { useSurveyResultsStore } from "@/stores/modules/surveyResults";
import { useMemberResultsStore } from "@/stores/modules/memberResults";
import { useQuasar } from "quasar";
import { useAuthStore } from "@/stores/modules/auth.module";
import { useDataManagementStore } from "@/stores/modules/dataManagement";
import _, { cloneDeep } from "lodash";

// quasar
const $q = useQuasar();

// store
const formsStore = useFormsStore();
const memberStore = useMemberStore();
const surveyResultsStore = useSurveyResultsStore();
const memberResultsStore = useMemberResultsStore();
const authStore = useAuthStore();
const dataManagementStore = useDataManagementStore();

const emit = defineEmits(['onResetAll']);

// data
const defaultBrowserTabTitle = ref("LINE OSS");
const options = ref();
const surveyConfigsListType = ref([]);
const selectedSurvey = ref(null);


// computed
const currentUser = computed(() => authStore.user);

const selectedSurveyType = computed(() => {
  if(selectedSurvey.value && selectedSurvey.value.surveyType === 'member') {
    return 'member';
  } else {
    return 'normal';
  }
})

const isFetchingSurveyConfigsListType = computed(() => formsStore.isFetchingSurveyConfigsList);
const isFetchingSurveyConfigsById = computed(() => formsStore.isFetchingSurveyConfigsById);

const isFetchingSurveyResultsDisplayCount = computed(() => surveyResultsStore.isFetchingSurveyResultsDisplayCount);
const isFetchingSurveyResultsPartitionKeys = computed(() => surveyResultsStore.isFetchingSurveyResultsPartitionKeys);
const isFetchingSurveyResultsItems = computed(() => surveyResultsStore.isFetchingSurveyResultsItems);

const fetchSurveyResultsDisplaySettingsError = computed(() => surveyResultsStore.fetchSurveyResultsDisplaySettingsError);
const fetchSurveyConfigsListTypeError = computed(() => formsStore.fetchSurveyConfigsListError);
const fetchSurveyConfigsByIdError = computed(() => formsStore.fetchSurveyConfigsByIdError);

const isLoadingSurvey = computed((): any => {
  return (isFetchingSurveyConfigsById.value 
    || isFetchingSurveyResultsPartitionKeys.value 
    || isFetchingSurveyResultsItems.value 
    || isFetchingSurveyResultsDisplayCount.value
  );
});

const selected = ref(null); // Replace computed with ref

// Watcher to handle changes in the selected value
watch(selected, async (newValue, oldValue) => {
  //console.log(`newValue: ${JSON.stringify(newValue, null, 2)}, oldValue: ${oldValue}`);
  
 if (newValue.value && newValue.value !== null) {
    if(oldValue.value === undefined) {
      //console.log(`oldValue is undefined`);
      await handleFetchSurveyConfigsById(newValue.value);
    }

    if(newValue.value !== oldValue.value) {
      //console.log(`newValue is not equal to oldValue`);
      
      // Fetch survey configurations by ID when a valid value is selected
      await handleFetchSurveyConfigsById(newValue.value);
    }

    changeBrowserTabTitle(newValue.value);
  } else {
    //console.log(`selected value is null`);
    
    surveyResultsStore.selectedSurvey = undefined;
    memberResultsStore.selectedMemberConfig = undefined;
    // Reset all values when the selected value is null
    emit("onResetAll");
  }
}, { deep: true });

const surveyOptions = computed((): any => {
  let options = [
    {
      value: null,
      text: "ーー",
    },
  ];

  options.push(
    ...surveyConfigsListType.value.map((obj: any) => {
      return {
        value: obj.surveyId,
        text:
          obj.surveyTitle !== undefined && obj.surveyTitle.length <= 50
            ? obj.surveyTitle
            : obj.surveyTitle.substring(0, 50).concat("..."),
      };
    })
  );

  return options;
});

// watch
watch(
  () => surveyConfigsListType.value,
  async (newVal) => {
    if (newVal) {
      if (!selectedSurvey.value) {
        // NOTE: set selected survey to first one on list (unused).
        //await handleFetchSurveyConfigsById(newVal[0].surveyId);
      }
    }
  }
);


// methods
const handleFetchSurveyConfigs = async (isReload = false): Promise<void> => {
  await formsStore.fetchSurveyConfigs({download: "list"});
  await memberStore.fetchAllMemberFormConfigs();

  surveyConfigsListType.value = [
    ...formsStore.surveyConfigsList,
    ...memberStore.memberConfigs,
  ];

  const myTeamIdList = authStore.user.groups;

  if (!myTeamIdList.find((team) => team.split(':')[0] === 'Administrator')) {
    surveyConfigsListType.value = surveyConfigsListType.value.filter((elm) => {  
      const surveyTeams = elm.surveyTeams?.filter((team) => team.teamId !== 'Administrator') ?? undefined;
      if (!surveyTeams || surveyTeams.length === 0) {
        return true;
      }
      
      surveyTeams.forEach((team) => {
        const findData = myTeamIdList?.find((myTeamId) => {
          return team.teamId === myTeamId.split(':')[0];
        });
  
        if (findData) return true;
      });
  
      return false;
    });
  }
  
  if (fetchSurveyConfigsListTypeError.value) {
    $q.notify({
      type: "negative",
      message: fetchSurveyConfigsListTypeError.value,
    });
  } else {
    if (isReload) emit("onResetAll");
  }
};

const handleFetchSurveyConfigsById = async (surveyId: any): Promise<void> => {
  const form = surveyConfigsListType.value.find((obj: any) => obj.surveyId === surveyId);
  if(form.surveyType === 'member') {
    //console.log("%cMEMBER", "font-size: 20px; font-weight: bold; color: red;")
    memberStore.fetchMemberFormConfig(surveyId).then(response => {
      if (response && response.data) {
        const selectedSur = cloneDeep(response.data);
        selectedSurvey.value = selectedSur;
        // set survey selected survey to null
        surveyResultsStore.setSelectedSurvey(null);
        memberResultsStore.setSelectedMemberConfig(selectedSur);
        memberResultsStore.setMemberResultsListAllHeaders();
      }
    })
   
  } else {
    //console.log("%cNORMAL", "font-size: 20px; font-weight: bold; color: blue;")
    formsStore.fetchSurveyConfigsById(surveyId).then(response => {
      if (response && response.data) {
        selectedSurvey.value = response.data;
        // set member selected survey to null
        memberResultsStore.setSelectedMemberConfig(null);
        surveyResultsStore.setSelectedSurvey(response.data);
        surveyResultsStore.setSurveyResultsListAllHeaders();
      } 
    })
  }

  if (fetchSurveyConfigsByIdError.value) {
    $q.notify({
      type: "error",
      message: fetchSurveyConfigsByIdError.value ?? '',
    });
  }
};

const handleFetchSurveyResultsDisplaySettings = async (): Promise<void> => {
  //console.log(currentUser.value.username);
  if(selectedSurveyType.value === 'member') {
    //console.log("currentUser", currentUser.value);
    await memberResultsStore.fetchMemberResultsDisplaySettings({
      userId: currentUser.value.username,
      surveyId: selectedSurvey.value.surveyId
    })
  } else {
    await surveyResultsStore.fetchSurveyResultsDisplaySettings({
      userId: currentUser.value.username,
      surveyId: selectedSurvey.value.surveyId
    })
  }

  if (fetchSurveyResultsDisplaySettingsError.value) {
    $q.notify({
      message: fetchSurveyResultsDisplaySettingsError.value,
      type: 'error',
    });
  }
};

const handleFetchPaymentConfig = async (): Promise<void> => {
  // TODO: Survey config does not include payment service ID on any item
  // 日本語: Survey設定には支払いサービスIDが含まれていない
  const selectProductsItem = selectedSurvey.value.surveySchema.find(item => item.type === 'selectProducts');
  /* if (selectedSurvey.value.usePayment === 1) {
    const serviceId = selectProductsItem.input;
    console.log("Fetching payment service", serviceId);
    await dataManagementStore.fetchPaymentService(serviceId);
  } */
  await dataManagementStore.fetchTaxRate();
};

const changeBrowserTabTitle = (surveyId: any) => {
  if (surveyId) {
    const selectOption = surveyOptions.value.filter((obj: any) => {
      return obj.value === surveyId;
    });
    document.title = selectOption[0].text;
  } else {
    document.title = defaultBrowserTabTitle.value;
  }
};

const filterFn = (value: string, update: any) => {
  if (value === '') {
    update(() => {
      options.value = surveyOptions.value;
    });
    return;
  }
  else {
    update(() => {
      value = value.toLowerCase();
      options.value = surveyOptions.value.filter((elm: any) => 
        elm.text.toLowerCase().indexOf(value) > -1
      );
    });
  }
};

const inputFn = (val: any) => {
  if (!val) {
    selected.value = val;
  }
}
  
// mounted
onMounted(async () => {
  if (!surveyConfigsListType.value || surveyConfigsListType.value.length === 0) {
    await handleFetchSurveyConfigs();
  }

  if (selectedSurvey.value && !_.isEmpty(selectedSurvey.value)) {
    selected.value = {
      text: selectedSurvey.value.surveyTitle,
      value: selectedSurvey.value.surveyId,
    };
  } else {
    selected.value = { value: null, text: "ーー" }; // Set default to "ーー"
  }

});

watch(
  () => selectedSurvey.value,
  (newVal) => {
    if (newVal) {
      surveyResultsStore.setSurveyResultsListAllHeaders();
      surveyResultsStore.clearAllSurveyResultsSearchCriteria();
      surveyResultsStore.resetSurveyResultsList();
      memberResultsStore.setMemberResultsListAllHeaders();
      memberResultsStore.clearAllMemberResultsSearchCriteria();
      memberResultsStore.resetMemberResultsList(null);
      handleFetchSurveyResultsDisplaySettings();
      if (newVal.usePayment === 1) {
        handleFetchPaymentConfig();
      }
    }
  }
);

onBeforeMount(() => {
  defaultBrowserTabTitle.value = document.title;
  if (null !== selected.value) {
    changeBrowserTabTitle(selectedSurvey.value.surveyId)
  }
})

onUnmounted(() => {
  document.title = defaultBrowserTabTitle.value
});

onBeforeMount(() => {
  defaultBrowserTabTitle.value = document.title;
  if (null !== selected.value) {
    changeBrowserTabTitle(selectedSurvey.value.surveyId)
  }
});

onUnmounted(() => {
  document.title = defaultBrowserTabTitle.value
});
</script>
