<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div class="row tw-flex tw-justify-center">
    <q-dialog v-model="show" scrollable max-width="500">
      <q-card class="tw-w-full">
        <q-bar class="bg-primary tw-h-1" dark></q-bar>
        <q-toolbar flat>
          <q-toolbar-title> 通知情報（最新のみ） </q-toolbar-title>
          <q-btn @click="fetchData" flat round :ripple="false">
            <q-icon color="primary" :name="isFetchingHomeNotification ? 'mdi-cached mdi-spin' : 'mdi-cached'"></q-icon>
          </q-btn>
          <q-space />
          <q-btn icon="mdi-close" flat round @click="show = false">
          </q-btn>
        </q-toolbar>
        <q-separator />
        <q-tabs grow v-model="currentTab" active-color="primary">
          <q-tab v-for="tab in tabs" :key="tab.title.en" :name="tab.title.en" :label="tab.title.ja">
          </q-tab>
        </q-tabs>
        <q-separator />
        <q-tab-panels v-model="currentTab" v-for="tab in tabs" :key="tab.title.en">
          <q-tab-panel :name="tab.title.en">
            <q-card flat style="overflow: auto; height: 400px;" class="text-grey-8">
              <q-card-section>
                <template v-for="(v, k) in homeNotification[tab.title.en]">
                  <template v-if="keyToJapanese(k)">
                    <div class="row" :key="'row-' + k">
                      <div :class="'col-' + keyColsSize" class="tw-text-right tw-p-3">
                        {{ keyToJapanese(k) }}
                      </div>
                      <div :class="errorMessageClass(k) + ', ' + 'col-' + valueColsSize" class="tw-p-3">
                        <template v-if="k.toString() === 'status'">
                          {{ statusToJapanese(v) }}
                        </template>
                        <template v-else-if="k.toString() === 'downloadUrl'">
                          <div v-if="v">
                            <a
                              @click="
                                handleDownloadClick(
                                  v,
                                  tab.title.en == 'exportCsvAppendingStatus'
                                    ? homeNotification[tab.title.en]['startedAt'] + s3expiryTimeShort
                                    : homeNotification[tab.title.en]['startedAt'] + s3expiryTime,
                                  tab.title.en,
                                  homeNotification[tab.title.en].username
                                )
                              "
                              class="tw-underline text-primary tw-cursor-pointer"
                              >ダウンロード</a
                            >
                            <div v-if="tab.title.en == 'exportCsvAppendingStatus'">
                              ({{
                                datetimeFormatter.formatUnixToYYYYMMDHHmmss(
                                  homeNotification[tab.title.en]["startedAt"] + s3expiryTimeShort
                                )
                              }}
                              まで)
                            </div>
                            <div v-else>
                              ({{
                                datetimeFormatter.formatUnixToYYYYMMDHHmmss(homeNotification[tab.title.en]["startedAt"] + s3expiryTime)
                              }}
                              まで)
                            </div>
                          </div>
                          <div v-else>ーー</div>
                        </template>
                        <template v-else-if="k.toString() === 'startedAt'">
                          {{ v > 0 ? datetimeFormatter.formatUnixToYYYYMMDHHmmss(v) : "ーー" }}
                        </template>
                        <template v-else-if="k.toString() === 'resultReportUrl' && v && v != ''">
                          <a
                            @click="
                              handleDownloadClick(
                                v,
                                homeNotification[tab.title.en]['finishedAt'] + s3expiryTime,
                                tab.title.en,
                                homeNotification[tab.title.en].username
                              )
                            "
                            class="tw-underline text-primary tw-cursor-pointer"
                          >
                            ダウンロード
                          </a>
                          ({{ datetimeFormatter.formatUnixToYYYYMMDHHmmss(homeNotification[tab.title.en]["finishedAt"] + s3expiryTime) }}
                          まで)
                        </template>
                        <template v-else>
                          {{ v ? v : "ーー" }}
                        </template>
                      </div>
                    </div>
                    <div v-if="k.toString() === 'resultReportUrl'" :key="'row-' + k + '-description'" class="row">
                      <div :offset="1" class="text-center result-report-warning col-10">
                        <span class="text-caption">
                          ＊ステータスが「完了」してから結果レポートが表示されるまでに数秒かかる場合があります。
                        </span>
                      </div>
                    </div>
                    <div class="row"
                      v-if="k.toString() === 'downloadUrl' && tab.title.en === 'exportCsvAppendingStatus'"
                      :key="'row-' + k + '-caution'"
                    >
                      <div :offset="1" class="text-center result-report-warning col-10">
                        <span class="text-caption"> ＊エクスポートを実行したユーザーのみダウンロード可能です </span>
                      </div>
                    </div>
                  </template>
                </template>
                <template v-if="'stats' in homeNotification[tab.title.en]">
                  <div v-for="statsKey in statsKeys" :key="'row-' + statsKey.en">
                    <div class="row">
                      <div :class="'col-' + keyColsSize" class="tw-text-right tw-p-3">{{ statsKey.ja }}</div>
                      <div :class="'col-' + valueColsSize" class="tw-p-3">{{ homeNotification[tab.title.en]["stats"][statsKey.en] }}</div>
                    </div>
                  </div>
                </template>
              </q-card-section>
            </q-card>
          </q-tab-panel>
        </q-tab-panels>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useDatetimeFormatter } from "@/mixins/DatetimeFormatter";
import dayjs from "dayjs";
import { useSurveyResultsStore } from "@/stores/modules/surveyResults";
import { useQuasar } from "quasar";
import { useAuthStore } from "@/stores/modules/auth.module";

// quasar
const $q = useQuasar();

// store
const surveyResultsStore = useSurveyResultsStore();
const authStore = useAuthStore();

const datetimeFormatter = useDatetimeFormatter();

// props emit
const props = defineProps<{
  visible: boolean,
  formData: any,
}>();

const emit = defineEmits(['close']);

// data
const currentTab = ref('importCsvAppendingStatus');
const tabs = ref([
  {
    title: { en: "importCsvAppendingStatus", ja: "CSVインポート" },
  },
  {
    title: { en: "exportCsvAppendingStatus", ja: "CSVエクスポート" },
  },
]);
const s3expiryTime = ref(60 * 60);
const s3expiryTimeShort = ref(60 * 15);
const keyColsSize = ref(4);
const valueColsSize = ref(8);
const statsKeys = ref([
  { en: "successCount", ja: "成功数" },
  { en: "errorCount", ja: "失敗数" },
  { en: "total", ja: "合計数" },
]);

// computed
const userStore = computed(() => authStore.user);
const homeNotification = computed(() => surveyResultsStore.homeNotification);
const isFetchingHomeNotification = computed(() => surveyResultsStore.isFetchingHomeNotification);
const fetchHomeNotificationError = computed(() => surveyResultsStore.fetchHomeNotificationError);

const show = computed({
  get(): boolean {
    return props.visible;
  },
  set(value: boolean): void {
    if (!value) {
      emit("close");
    }
  },
});

// watch
watch(
  () => show.value,
  (value) => {
    if (value) {
      fetchData();
    }
  }
);

// methods
const fetchData = async (): Promise<void> => {
  await surveyResultsStore.fetchHomeNotification();
  if (fetchHomeNotificationError.value) {
    $q.notify({
      type: "error",
      message: fetchHomeNotificationError.value.message,
    });
  }
};

const keyToJapanese = (k: any): string | null => {
  if (k === "status") return "ステータス";
  if (k === "startedAt") return "リクエスト日時";
  if (k === "username") return "ユーザ名";
  if (k === "errorMessage") return "エラーメッセージ";
  if (k === "downloadUrl") return "ファイル";
  if (k === "resultReportUrl") return "結果レポート";
  return null;
};

const errorMessageClass = (k: any): string => {
  if (k === "errorMessage") return "error--text font-weight-bold";
  return "";
};

const statusToJapanese = (v: any): string => {
  if (v === "STARTED") return "処理中";
  if (v === "FINISHED") return "完了";
  if (v === "ERROR") return "エラー";
  return "ーー";
};

const handleDownloadClick = (link: any, expirationTime: any, tabTitle: any, userName: any): void => {
  if (tabTitle === "exportCsvAppendingStatus" && userName !== userStore.value.username) {
    $q.notify({ message: "エクスポートを実行したユーザーのみダウンロード可能です。" });
    return;
  }
  let currentMoment = dayjs();
  let expirationMoment = dayjs.unix(expirationTime);
  if (currentMoment.isAfter(expirationMoment)) {
    $q.notify({ message: "ダウンロード対象のファイルは、有効期限切れです。" });
  } else {
    window.open(link);
  }
};
</script>

<style scoped>
.result-report-warning {
  padding: 0;
}
.font-weight-bold {
  font-weight: bold;
}
</style>
