<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-chip
      v-for="status in finalStatusList"
      :key="status"
      class="tw-m-1"
      @click="updateStatus(status)"
      :dark="isActive(status)"
      :disable="isDisabled(status) || isDisabledWithPaymentStatus(status)"
      :color="isActive(status) ? getCheckColor(status) : ''"
      clickable
    >
      <q-avatar left>
        <q-icon v-if="isActive(status)" small color="white" name="mdi-checkbox-marked-circle"></q-icon>
        <q-icon v-else small :color="getCheckColor(status)" name="mdi-checkbox-blank-circle"></q-icon>
      </q-avatar>
      {{ status }}
    </q-chip>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { SURVEY_RESULTS_CHECK, DEFAULT_RESULTS_CHECK_COLOR } from "@/stores/modules/surveyResults/surveyResults.constants";
import { PAYMENT_STATES } from "@/stores/modules/payment/payment.constants";

// data
const statusList = ref(["未対応", "対応中", "対応済", "完了"]);
const statusListAppending = ref(["未対応", "対応中", "対応済", "完了", "保留", "キャンセル"]);
const statusDeleted = ref(["取り消し"]);
const activeItem = ref();

// props
const props = defineProps<{
  data: string,
  isAppending: boolean,
  paymentStatus: number | null,
}>();

const emit = defineEmits(['updateStatus']);

// computed
const finalStatusList = computed((): string[] => {
  if (statusDeleted.value.includes(props.data)) {
    return statusDeleted.value;
  } else if (props.isAppending) {
    return statusListAppending.value;
  } else {
    return statusList.value;
  }
});

// mounted
onMounted(() => {
  activeItem.value = props.data;
});

// methods
const getCheckColor = (value: any): string => {
  const list = SURVEY_RESULTS_CHECK;
  const find = list.find((obj) => obj.text === value);
  return find ? find.color : DEFAULT_RESULTS_CHECK_COLOR;
};

const updateStatus = (status: any): void => {
  activeItem.value = status;
  emit("updateStatus", status);
};

const isActive = (status: any): boolean => {
  return status === activeItem.value;
};

const isDisabled = (value: any): boolean => {
  return props.data === "取り消し" && value !== props.data;
};

const isDisabledWithPaymentStatus = (surveyStatus: string): boolean => {
  // NOTE
  // 返金済み・与信取消の場合はキャンセル・取り消し以外非活性
  // 上記以外の場合は活性化
  switch(props.paymentStatus) {
    case PAYMENT_STATES.REFUNDED:
    case PAYMENT_STATES.CANCELED_CREDIT: {
      return !['キャンセル', '取り消し'].includes(surveyStatus);
    }
    default: {
      return false;
    }
  }
};

// watch
watch(
  () => props.data,
  (value) => {
    activeItem.value = value;
  }
);
</script>
