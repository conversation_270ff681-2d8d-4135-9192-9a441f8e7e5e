<template>
  <div class="tw-mt-8">
    <SearchFragment :survey-configs="[]" :loading="false" ref="searchRef" />
    <ContentFragment @triggerSearch="triggerSearch"/>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";

import { useFormsStore } from "@/stores/modules/forms";
import { useSettingsStore } from "@/stores/modules/settings";
import { useCalenderStore } from "@/stores/modules/calendar";
import { useQuasar } from "quasar";

// components
import SearchFragment from "@/pages/admin/Applicants/fragments/SearchFragment.vue";
import ContentFragment from "@/pages/admin/Applicants/fragments/ContentFragment.vue";

// quasar
const $q = useQuasar();

// data
const searchRef = ref<any>(null);

// store
const formsStore = useFormsStore();
const settingsStore = useSettingsStore();
const calendarStore = useCalenderStore();

// computed
const isFetchingFormConfigs = computed(() => formsStore.isFetchingFormConfigs);

// method
const onFetchCalendarTags = async (): Promise<void> => {
  await settingsStore.fetchCalendarDisplaySettings();
}

const triggerSearch = (): void => {
  searchRef.value?.handleSearch();
}

// watch
watch(
  () => isFetchingFormConfigs.value,
  (newVal) => {
    if (newVal) {
      $q.loading.show({
        message: '更新中...',
      });
    }
    else {
      $q.loading.hide();
    }
  }
)

// mounted
onMounted(() => {
  onFetchCalendarTags();
  calendarStore.actionGetReservationItemInfo("all");
});

</script>
