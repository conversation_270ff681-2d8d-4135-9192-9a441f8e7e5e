<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <div class="row tw-pb-6">
      <div class="col-auto">
        <q-btn color="primary" @click="toggleResultsAdvancedSearch">
          <q-icon left large :name="isShowAdvancedSearch ? 'mdi-chevron-up' : 'mdi-chevron-down'"></q-icon>
          検索条件
        </q-btn>
      </div>
      <div class="col-auto col-sm-8 tw-pb-4">
        <SurveySelector @onResetAll="handleResetAll"/>
      </div>
      <q-space/>
      <div class="col-auto">
        <q-btn-toggle 
          v-model="searchMode" 
          :options="searchModeOptions" 
          class="bg-grey-2"
          outline
          color="grey"
          toggle-color="primary"
          toggle-text-color="primary"
        >
          <template v-slot:commonSearch>
            <q-icon left name="mdi-text-box-outline"></q-icon>
            <span class="hidden-search">基本</span>
          </template>

          <template v-slot:dateSearch>
            <q-icon left name="mdi-calendar-outline"></q-icon>
            <span class="hidden-sm-and-down">日付</span>
          </template>
        </q-btn-toggle>
      </div>
    </div>
    <template v-if="fetchFormConfigsError">
      <div class="row">
        <div class="col">
          <q-card flat bordered>
            <ContentLoadError :error="fetchFormConfigsError" />
          </q-card>
        </div>
      </div>
    </template>
    <template v-else>
      <div v-if="isShowAdvancedSearch">
        <div class="row">
          <div class="col">
            <q-card class="tw-p-3" flat bordered :disable="isSearchDisabled">
              <CommonSearch v-if="searchMode === 'commonSearch'" :isMemberSearch="surveyType === 'member'" class="tw-px-4" />
              <DateSearch v-if="searchMode === 'dateSearch'" :isMemberSearch="surveyType === 'member'" class="tw-px-4" />
            </q-card>
          </div>
        </div>
        <div align="center" class="tw-py-3">
          <q-btn
            class="tw-mr-3"
            color="primary"
            @click="handleSearch"
            :loading="isLoading"
            :disable="isSearchDisabled"
          >
            検索
          </q-btn>
          <q-btn class="tw-mr-3" color="grey lighten-2" @click="handleClearAllSearchCriteria" :disabled="isSearchDisabled">
            検索条件をクリア
          </q-btn>
          <q-btn
            v-if="isAppendingSurvey"
            :color="creatingCsvAppendingBoolean ? 'grey' : 'secondary'"
            @click="handleShowCSVDownloadDialog"
            :loading="creatingCsvAppendingBoolean"
            :disabled="isSearchDisabled || creatingCsvAppendingBoolean"
            rounded
            :style="
              permissionHelper.hasActionPermission('hideButton', 'Applicants_SearchFragment_ExportCSVAsync')
                ? permissionHelper.displayNoneButtonPermissionStyle()
                : null
            "
          >
            検索条件のCSV出力処理
          </q-btn>
          <CSVDownloadDialog :visible="showCSVDownloadDialog" @close="closeCsvDownloaddialog()" />
        </div>
      </div>
      <div class="row">
        <div :class="'col-' + gridCols?.full">
          <span>
            <span class="text-primary tw-mr-2">[帳票]</span>
            <span class="secondary-text">{{ selectedSurvey?.surveyTitle || "ーー" }}</span>
          </span>
          <template v-for="(searchValue, searchKey) in commonSearchCriteria">
            <span
              class="tw-ml-6"
              :key="searchKey"
              v-if="searchValue && (searchValue.length > 0 || searchValue.values.length > 0)"
            >
              <span class="text-primary tw-mr-2">[{{ surveyResultsItemTitle(searchKey)  || "ーー" }}]</span>
              <span class="secondary-text">{{ convertSearchCriteriaString(searchValue) || "ーー" }}</span>
            </span>
          </template>
          <template v-if="selectedSurvey" v-for="(searchValue, searchKey) in dateSearchCriteria">
            <span class="tw-ml-6" :key="searchKey" v-if="searchValue.from || searchValue.to">
              <span class="text-primary tw-mr-2">[{{ surveyResultsItemTitle(searchKey) }}]</span>
              <span class="secondary-text">{{ convertSearchCriteriaDateString(searchValue) }}</span>
            </span>
          </template>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { useDataManagementStore } from "@/stores/modules/dataManagement";
import { useSurveyResultsStore } from "@/stores/modules/surveyResults";
import { useMemberResultsStore } from "@/stores/modules/memberResults";
import { GridCols } from "@/types/index";
import { computed, ref, watch } from "vue";

import ContentLoadError from "@/components/common/ContentLoadError.vue";

import SurveySelector from "@/pages/admin/Applicants/components/SurveySelector.vue";
import CommonSearch from "@/pages/admin/Applicants/components/SurveyResultsSearch/CommonSearch.vue";
import DateSearch from "@/pages/admin/Applicants/components/SurveyResultsSearch/DateSearch.vue";
import CSVDownloadDialog from "@/pages/admin/Applicants/components/CSVDownloadDialog.vue";

import { useQuasar } from "quasar";
import { useFormsStore } from "@/stores/modules/forms";
import { usePermissionHelper } from "@/mixins/PermissionHelper";
import _ from "lodash";

// quasar
const $q = useQuasar();

// store
const dataManagementStore = useDataManagementStore();
const surveyResultsStore = useSurveyResultsStore();
const memberResultsStore = useMemberResultsStore();
const formsStore = useFormsStore();

const permissionHelper = usePermissionHelper();

defineOptions({
  name: "SearchFrament",
});

// props
const props = defineProps<{
  surveyConfigs: any[],
  loading: boolean,
}>();

// data
const gridCols = ref<GridCols>();
const isSearching = ref(false);
const searchMode = ref("commonSearch");
const showCSVDownloadDialog = ref(false);

const searchModeOptions = ref([
  {value: 'commonSearch', slot: 'commonSearch'},
  {value: 'dateSearch', slot: 'dateSearch'},
]);
  
// computed
const selectedSurvey = computed(() => {
  return memberResultsStore.selectedMemberConfig 
    || surveyResultsStore.selectedSurvey;
});

const surveyType = computed(() => selectedSurvey.value?.surveyType);

const isAppendingSurvey = computed(() => {
  return surveyType.value === 'member' 
    ? memberResultsStore.isAppendingSurvey 
    : surveyResultsStore.isAppendingSurvey;
});

const surveyResultsItemTitle = computed(() => {
  return surveyType.value === 'member' 
    ? memberResultsStore.memberResultsItemTitle 
    : surveyResultsStore.surveyResultsItemTitle;
});

const commonSearchCriteria = computed(() => {
  return surveyType.value === 'member' 
    ? memberResultsStore.commonSearchCriteria 
    : surveyResultsStore.commonSearchCriteria;
});

const dateSearchCriteria = computed(() => {
  return surveyType.value === 'member' 
    ? memberResultsStore.dateSearchCriteria 
    : surveyResultsStore.dateSearchCriteria;
});

const isShowAdvancedSearch = computed(() => {
  return surveyResultsStore.isShowAdvancedSearch;
});

const isFetchingSurveyResultsPartitionKeys = computed(() => {
  return surveyType.value === 'member' 
    ? memberResultsStore.isFetchingMemberResultsPartitionKeys 
    : surveyResultsStore.isFetchingSurveyResultsPartitionKeys;
});

const isFetchingSurveyResultsItems = computed(() => {
  return surveyType.value === 'member' 
    ? memberResultsStore.isFetchingMemberResultsItems 
    : surveyResultsStore.isFetchingSurveyResultsItems;
});

const isFetchingSurveyResultsDisplayCount = computed(() => {
  return surveyType.value === 'member' 
    ? memberResultsStore.isFetchingMemberResultsDisplayCount
    : surveyResultsStore.isFetchingSurveyResultsDisplayCount;
});

const fetchSurveyResultsDisplayCountError = computed(() => {
  return surveyType.value === 'member' 
    ? memberResultsStore.fetchMemberResultsDisplayCountError 
    : surveyResultsStore.fetchSurveyResultsDisplayCountError;
});

const fetchSurveyResultsPartitionKeysError = computed(() => {
  return surveyType.value === 'member' 
    ? memberResultsStore.fetchMemberResultsPartitionKeysError  
    : surveyResultsStore.fetchSurveyResultsPartitionKeysError;
});

const fetchSurveyResultsItemsError = computed(() => {
  return surveyType.value === 'member' 
    ? memberResultsStore.fetchMemberResultsItemsError 
    : surveyResultsStore.fetchSurveyResultsItemsError;
});

const fetchFormConfigsError = computed(() => formsStore.fetchFormConfigsError);

const creatingCsvAppendingBoolean = computed(() => {
  return surveyType.value === 'member' 
    ? memberResultsStore.isCreatingCsvAppendingSearchCriteriaForMemberResults 
    : surveyResultsStore.isCreatingCsvAppendingSearchCriteriaForSurveyResults;
});

const isFetchingPaymentResults = computed(() => {
  return surveyType.value === 'member' 
    ? memberResultsStore.isFetchingPaymentResults 
    : surveyResultsStore.isFetchingPaymentResults;
});

const fetchPaymentResultsError = computed(() => {
  return surveyType.value === 'member' 
    ? memberResultsStore.fetchPaymentResultsError 
    : surveyResultsStore.fetchPaymentResultsError;
});

const datatableOptions = computed(() => {
    return dataManagementStore.dataTableOptions;
});

const isSearchDisabled = computed((): boolean => {
  return !selectedSurvey.value
  || isFetchingSurveyResultsPartitionKeys.value
  || isFetchingSurveyResultsItems.value
  || isFetchingPaymentResults.value
  || isFetchingSurveyResultsDisplayCount.value
  || _.isEmpty(selectedSurvey.value)
});

const isLoading = computed((): boolean => {
  return isFetchingSurveyResultsPartitionKeys.value
    || isFetchingSurveyResultsItems.value
    || isFetchingSurveyResultsDisplayCount.value
    || isFetchingPaymentResults.value;
});

// watch
watch(
  () => creatingCsvAppendingBoolean.value,
  (newVal, oldVal) => {
    if (newVal === false && oldVal === true) {
      $q.notify({ message: "CSV出力処理が完了しました。" });
    }
  }
);

const closeCsvDownloaddialog = (): void => {
  showCSVDownloadDialog.value = false;
};

const handleSearch = async (): Promise<void> => {
  // 検索条件がない場合は検索しない
  if(!selectedSurvey.value || _.isEmpty(selectedSurvey.value)) return;
  surveyResultsStore.resetSurveyResultsList();
  memberResultsStore.resetMemberResultsList(null);
  if(selectedSurvey.value.surveyType === 'member') {
    await memberResultsStore.searchMemberResults();
  } else {
    await surveyResultsStore.searchSurveyResults();
  }

  if (fetchSurveyResultsPartitionKeysError.value) {
    $q.notify({
      message: fetchSurveyResultsPartitionKeysError.value,
      type: 'error',
    });
  }

  if (fetchSurveyResultsItemsError.value) {
    $q.notify({
      message: fetchSurveyResultsItemsError.value,
      type: 'error'
    });
  }

  if (fetchSurveyResultsDisplayCountError.value) {
    if (typeof(fetchPaymentResultsError.value) === 'object') {
      $q.notify({
        message: 'エラー',
        type: 'error',
      });
    }
    else {
      $q.notify({
        message: fetchSurveyResultsDisplayCountError.value,
        type: 'error',
      });
    }
  }

  if (fetchPaymentResultsError.value) {
    if (typeof(fetchPaymentResultsError.value) === 'object') {
      $q.notify({
        message: 'エラー',
        type: 'error',
      });
    }
    else {
      $q.notify({
        message: fetchPaymentResultsError.value,
        type: 'error',
      });
    }
  }
};

const handleClearAllSearchCriteria = (): void => {
  surveyResultsStore.clearAllSurveyResultsSearchCriteria();
  memberResultsStore.clearAllMemberResultsSearchCriteria();
};

const handleResetAll = async (): Promise<void> => {
  handleClearAllSearchCriteria();
  if(!selectedSurvey.value) return;
  await handleSearch();
};

const handleShowCSVDownloadDialog = (): void => {
  showCSVDownloadDialog.value = true;
};

const toggleResultsAdvancedSearch = (): void => {
  surveyResultsStore.toggleSurveyResultAdvancedSearch();
};

const convertSearchCriteriaString = (value: any): any => {
  if (typeof value === "string") {
    return value;
  }

  if (Array.isArray(value)) {
    value = value.map((item) => {
      if (item.includes("tag1:")) item = item.replace("tag1:", "");
      if (item.includes("tag2:")) item = item.replace("tag2:", "");
      if (item.includes("tag3:")) item = item.replace("tag3:", "");
      return item;
    });

    return value.length > 2
      ? [...value].splice(0, 2).join(" / ") + ` (+${value.length - 2}その他)`
      : value.join(" / ");
  } else if (value.displayValue) {
    return value.displayValue;
  } else {
    return value.values.join(" / ");
  }
};

const convertSearchCriteriaDateString = (value: any): string => {
  let fromDate = value.from || "";
  let toDate = value.to || "";
  return `${fromDate} - ${toDate}`;
};

defineExpose({
  handleSearch,
})
</script>
