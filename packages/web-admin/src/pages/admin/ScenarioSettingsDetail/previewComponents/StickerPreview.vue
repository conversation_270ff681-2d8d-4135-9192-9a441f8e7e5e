<template>
  <q-page-container>
    <q-img
      :src="url"
      :error-src="errorImage"
      class="preview-sticker-message"
    />
  </q-page-container>
</template>

<script setup lang="ts">
import { onBeforeMount, PropType, ref } from 'vue';
import errorImage from 'assets/images/logo.png';

// props
const props = defineProps({
  message: Object as PropType<any>
});

const stickers = import.meta.glob('@/assets/line-stickers/**/*.png');
const url = ref();

onBeforeMount(async () => {
  url.value = await getStickerUrl();
});

const getStickerUrl = async () => {
  const path = `../../../../assets/line-stickers/${props.message.sticker.packageId}/${props.message.sticker.stickerId}.png`;
  const result = await stickers[path]?.().then((mod) => mod.default) ?? 'no sticker';
  return result;
};
</script>

<style lang="less">
.preview-sticker-message {
  height: 10em;
  width: 10em;
}
</style>
