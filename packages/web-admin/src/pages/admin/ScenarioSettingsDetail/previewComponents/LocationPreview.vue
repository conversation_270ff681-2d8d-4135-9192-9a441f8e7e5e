<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.preview-text-message {
  border-radius: 0.25em;
  background-color: white;
  width: 14em;
}
.preview-text-content {
  padding: 0.25em;
  font-size: small;
  position: relative;
  z-index: 2;
  margin-bottom: 0em !important;
}
.tri-right.left-top:after {
  content: " ";
  position: relative;
  width: 0;
  height: 0;
  left: -10px;
  right: auto;
  top: -40px;
  bottom: auto;
  border: 22px solid;
  border-color: white transparent transparent transparent;
  z-index: 1;
}
</style>

<template>
  <q-page-container>
    <div class="preview-text-message tri-right left-top">
      <div class="preview-text-content">
        <div class="row">
          <div class="col-3">
            <q-icon name="mdi-flag-triangle"></q-icon>
          </div>
          <div class="col-9">
            <div class="row">{{ message.title }}</div>
            <div class="row">{{ message.address }}</div>
          </div>
        </div>
      </div>
    </div>
  </q-page-container>
</template>

<script setup lang="ts">
import { PropType } from 'vue';

// props
const props = defineProps({
  message: Object as PropType<any>
});
</script>
