<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<style lang="less">
.preview-image-map-message {
  border-radius: 0.25em;
  height: 10em;
  width: 14em;
}
</style>

<template>
  <q-page-container>
    <q-img
      v-if="message && message.baseUrl && message.baseUrl.startsWith('data:image')"
      :src="message.baseUrl"
      :error-src="errorImage"
      @click="window.open(message.baseUrl)"
      class="preview-image-map-message bg-white"
    />
    <q-img
      v-else
      :src="message.baseUrl + '/240?x-request=html'"
      :error-src="errorImage"
      @click="window.open(message.baseUrl + '/240?x-request=html')"
      class="preview-image-map-message" fit="contain"
    />
  </q-page-container>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import errorImage from 'assets/images/logo.png';

// props
const props = defineProps({
  message: Object as PropType<any>
});
</script>
