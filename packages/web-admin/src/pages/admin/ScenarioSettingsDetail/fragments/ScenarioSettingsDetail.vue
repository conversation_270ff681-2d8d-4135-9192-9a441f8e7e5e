<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.user-input {
  text-decoration: underline;
  cursor: pointer;
}
.user-text {
  word-break: break-word;
}
.preview-image {
  max-width: 50px;
  max-height: 50px;
}
</style>
<style>
.scenario-table.v-data-table tbody tr:hover {
  cursor: initial !important;
}
</style>
<template>
  <div>
    <div @click="clickOnMainContent">
      <q-card outlined class="tw-my-4">
        <div class="tw-p-3">
          <div v-if="(activeScenarioData.envMapping && route.params.versionId === activeScenarioData.envMapping.production)" class="tw-p-3">
            <Alert type="error">
              <div>
                このバージョンは公開中です。編集後すぐに変更内容が反映されますのでご注意ください。
              </div>
            </Alert>
          </div>
          <div class="row tw-justify-between">
            <div class="row tw-px-3 col-12">
              <h2
                v-if="hasActionPermission('disableButton', 'ScenarioSettings_ChangeTalkName_Click')"
                class="active-talk-name"
              >
                {{ getTalkNameFromId }}
              </h2>
              <h2
                v-else
                class="active-talk-name clickable"
                @click="showTalkNameChangeModal = true"
              >
                {{ getTalkNameFromId }}
              </h2>
            </div>
            <div class="row tw-p-3">
              <div class="col-12 row items-center" v-if="hasDamageReportTalk">
                <span>
                  <q-btn class="tw-mx-1 tw-mb-4" color="primary"
                    :disabled="hasActionPermission('disableButton', 'ScenarioSettings_CreateDynamicTalk_Click')"
                    @click="onCreateTalkClick">
                    トーク作成 <!-- Create dynamic talk -->
                  </q-btn>
                </span>
                <span>
                  <q-select class="tw-mx-1 tw-pb-4" v-model="currentDamageReportTalk" :options="damageReportTalkVersion"
                    option-value="value" option-label="text" bg-color="white" outlined dense clearable fill-input
                    use-input hide-selected @update:model-value="changeDamageReportActiveVersion"
                    :disable="hasActionPermission('disableButton', 'ScenarioSettings_SelectActiveTalk_Click')"
                  >
                    <template v-slot:option="{ itemProps, opt }">
                      <q-item v-bind="itemProps">
                        <q-item-section>
                          <q-item-label>
                            {{ opt.text }}
                            <span v-if="opt.dataId === activeDamageTalkVersion"> ｜ active</span>
                          </q-item-label>
                        </q-item-section>
                      </q-item>
                    </template>
                  </q-select>
                  <!-- <q-select class="mr-2" height="36px" style="width: 180px;" v-model="currentDamageReportTalk"
                    :items="damageReportTalkVersion" outlined dense hide-details
                    @change="changeDamageReportActiveVersion" return-object>
                    <template slot="item" slot-scope="data">
                      <div>
                        <div style="line-height: 1;">{{ data?.item?.text }}</div>
                        <div style="font-size: 0.85rem; color: #9ca3af" v-if="data?.item?.dataId === activeDamageTalkVersion">
                          active
                        </div>
                      </div>
                    </template>
                  </q-select> -->
                </span>
                <span>
                  <q-btn class="tw-mx-1 tw-mb-4" color="primary"
                    :disabled="activeDamageTalkVersion === currentDamageReportTalk?.dataId || hasActionPermission('disableButton', 'ScenarioSettings_SetActiveTalk_Click')"
                    @click="onSetActiveTalkClick">
                    トーク利用 <!-- Set active talk -->
                  </q-btn>
                </span>
                <span>
                  <q-btn class="tw-mx-1 tw-mb-4" color="negative" :loading="isDeletingScenarioVersion"
                    :disabled="[activeDamageTalkVersion, 'DAMAGE_REPORT_TALK'].includes(currentDamageReportTalk?.dataId)
                      || hasActionPermission('disableButton', 'ScenarioSettings_DeleteActiveTalk_Click')"
                    @click="onDeleteTalkClick">
                    トーク削除 <!-- Delete talk -->
                  </q-btn>
                </span>
              </div>
  
              <div class="col-12 col-md-auto row">
                <q-btn color="negative" outline class="tw-mx-1 tw-mb-4"
                  :disable="hasActionPermission('disableButton', 'ScenarioSettings_DeleteMessage_Click') || disableDeleteButton()"
                  @click="hasActionPermission('click', 'backendRequest') ? (showDeleteModal = true) : showActionPermissionError()"
                >
                  <q-icon size="sm" left name="mdi-trash-can-outline" />
                  選択項目を削除
                  <q-tooltip bottom v-if="route.params.talkId === 'DAMAGE_REPORT_TALK' || hasDamageReportTalk">
                    <span>
                      "{{ getTalkNameFromId }}" はメッセージを削除できません
                    </span>
                  </q-tooltip>
                </q-btn>
  
                <span v-if="fmVisible">
                  <q-btn
                    class="tw-mx-1 tw-mb-4"
                    color="primary"
                    :disabled="hasActionPermission('disableButton', 'ScenarioSettings_CreateCompositeMessage_Click')"
                    @click="openCompositeMessage"
                  >
                    複合メッセージを追加
                  </q-btn>
                </span>
                <span v-if="isVisible">
                  <q-btn
                    class="tw-mx-1 tw-mb-4"
                    color="primary"
                    :disabled="hasActionPermission('disableButton', 'ScenarioSettings_ImportGarbageCsv_Click')"
                    @click="createSpecialScenarios('ゴミ分別')"
                  >
                    CSVインポート
                  </q-btn>
                  <q-btn
                    class="tw-mx-1 tw-mb-4"
                    color="primary"
                    :loading="showLoadingExport"
                    @click="exportTrashCSV()"
                  >
                    CSVエクスポート
                  </q-btn>
                </span>
                <span v-if="csvVisible">
                  <q-btn
                    class="tw-mx-1 tw-mb-4"
                    color="primary"
                    :disabled="hasActionPermission('disableButton', 'ScenarioSettings_ImportZipCodeCsv_Click')"
                    @click="openZipCodeCSVUploader"
                  >
                    郵便番号CSVインポート
                  </q-btn>
                  <q-btn
                    class="tw-mx-1 tw-mb-4"
                    color="primary"
                    :disabled="hasActionPermission('disableButton', 'ScenarioSettings_ZipCodeList_Click')"
                    @click="openZipCodeList"
                  >
                    郵便番号リスト
                  </q-btn>
                </span>
              </div>
            </div>
          </div>
          <q-separator class="tw-mb-4"></q-separator>
          <q-table
            class="scenario-table"
            flat
            :columns="headers"
            :rows="filteredTableItems"
            :pagination="pagination"
            :pagination-label="(start, end, total) => `${ start }-${ end } 件目 /  ${ total }件`"
            rows-per-page-label="1ページあたりの行数"
            :rows-per-page-options="rowsPerPageOption"
            :show-select="true"
            :single-select="false"
            :loading="loadingMessageSearch"
            sort-by="nameLBD"
            :sort-desc="false"
            row-key="dataId"
            v-model:selected="tableSelected"
            selection="multiple"
            @selection="selectTableRow"
          >
            <!-- <template v-slot:item.data-table-select="{ isSelected, select, item }">
              <v-simple-checkbox
                  color="gray"
                  :value="tableSelected.map((selected) => selected.dataId).includes(item.dataId)"
                  @input="select($event)"
              />
            </template> -->
            <template v-slot:body-cell-nameLBD="item">
              <td>
                <div class="tw-flex tw-items-center">
                  <div v-if="svgIconConstants[item.row.dataType]" class="tw-mr-2 tw-flex" v-html="svgIconConstants[item.row.dataType]"></div>
                  <q-icon v-else class="tw-mr-2" :name="item.row.previewIcon"></q-icon>
                  <span>{{ item.row.nameLBD || "-" }}</span>
                </div>
              </td>
            </template>
            <template v-slot:body-cell-userInput="item">
              <td>
                <div
                  v-if="
                    hideTextMapping.includes(item.row.dataType) ||
                    (item.row.params &&
                      'specialScenarioTalk' in item.row.params &&
                      !specialTalkScenariosToDisplay.includes(item.row.params.specialScenarioTalk))
                  "
                >
                  -
                </div>
                <div v-else>
                  <div v-if="item.row.userInput.length > 1" class="user-input primary--text">
                    <span @click="onEditUserText(item.row)">{{ item.row.userInput[0] }} (+{{ item.row.userInput.length - 1 }})</span>
                  </div>
                  <div v-else-if="item.row.userInput.length === 1" class="user-input primary--text">
                    <span @click="onEditUserText(item.row)">{{ item.row.userInput[0] }}</span>
                  </div>
                  <div v-else>
                    <q-icon @click="onEditUserText(item.row)" name="mdi-plus" />
                  </div>
                </div>
              </td>
            </template>
            <template v-slot:body-cell-dataId="item">
              <td>
                <q-btn color="primary" @click="onEditProperties(item.row)">編集</q-btn>
              </td>
            </template>
            <template v-slot:body-cell-previewValue="item">
              <td>
                <div v-if="item.row && item.row.previewType === 'text'">
                  {{ item.row.previewValue }}
                </div>
                <div v-if="item.row && item.row.previewType === 'icon'">
                  <q-icon>{{ item.row.previewValue }}</q-icon>
                </div>
                <div v-if="item.row && item.row.previewType === 'image'">
                  <img v-bind:src="item.row.previewValue" class="preview-image" />
                </div>
              </td>
            </template>
            <template v-slot:body-cell-dataType="item">
              <td>{{ typeTitle(item.row.dataType) }}</td>
            </template>
          </q-table>
        </div>
      </q-card>
    </div>

    <ItemProperties
      :visible="showItemProperties"
      :model="selectedItem"
      :messages="tableItems"
      :scenarioId="scenarioId"
      :versionId="versionId"
      :talkName="selectedTalk"
      :parentClicked="cardClicked"
      @updateTextMapping="save"
      :isDamageReportTalk="hasDamageReportTalk"
      @close="onCloseEditProperties"
    />
    <UserTextModal :visible="showUserTextEdit" :message="selectedItem" @save="save" @close="showUserTextEdit = false" />
    <CompositeMessageModal
        :visible="showCompositeMessage"
        :scenarioId="scenarioId"
        :versionId="versionId"
        :displayTalkOptions="displayTalkOptions"
        @close="showCompositeMessage = false"
    />
    <SpecialTalkModal
        :visible="showSpecialTalk"
        :existingTalks="displayTalkOptions"
        :versionName="versionId"
        @onCreateTalks="createSpecialScenarios"
        @onDeleteTalks="deleteSpecialScenarios"
        @toggleTalkState="toggleSpecialScenarios"
        @close="showSpecialTalk = false"
    />
    <TalkNameChangeModal
      :visible="showTalkNameChangeModal"
      :existingTalks="displayTalkOptions"
      :talkId="route.params.talkId.toString()"
      :originalTalkName="getTalkNameFromId"
      @updateTalk="updateTalkName"
      @close="showTalkNameChangeModal = false"
    />
    <TrashCSVImportModal
        :visible="showImportTalk"
        :existingTalks="displayTalkOptions"
        :versionName="versionId"
        :scenarioName="scenarioId"
        :isLocationCSV="isLocationCSV"
        @close="showImportTalk = false"
    />
    <ZipCodeList
        :visible="showZipCodeModal"
        :scenarioName="scenarioId"
        :versionName="versionId"
        @close="showZipCodeModal = false"
    />
    <DeleteMessageModal
      :visible="showDeleteModal"
      :tableSelected="tableSelected"
      @onDeleteItem="onDeleteItem"
      @close="showDeleteModal = false"
    />
    <NewTalkModal :visible="showCreateDamageReportTalkModal" :scenarioId="scenarioId" :versionId="versionId"
      specialFlow="damage-report" @close="showCreateDamageReportTalkModal = false" />
  </div>
</template>

<script setup lang="ts">
import { PropType, ref,  computed, watch, onBeforeMount } from 'vue';
import { useQuasar } from 'quasar';
import {
  BOT_ITEM_TYPES,
  SCENARIO_ENV_TYPES,
  SPECIAL_TALK_TYPES_MAPPING,
  SVG_ICON_CONSTANTS,
} from "@/stores/modules/scenarios/scenarios.constants";
import { useScenariosStore } from '@/stores/modules/scenarios';
import { cloneDeep } from "lodash";
import { useRoute, useRouter } from 'vue-router';
import { usePermissionHelper } from '@/mixins/PermissionHelper';
import Alert from '@/components/common/Alert.vue';
import ItemProperties from "@/components/BotDesigner/ItemProperties.vue";
import UserTextModal from "@/pages/admin/ScenarioSettingsDetail/components/UserTextModal.vue";
import CompositeMessageModal from "@/pages/admin/ScenarioSettingsDetail/components/CompositeMessageModal.vue";
import NewTalkModal from "@/pages/admin/ScenarioVersionSettings/components/NewTalkModal.vue";
import SpecialTalkModal from "@/pages/admin/ScenarioSettingsDetail/components/SpecialTalkModal.vue";
import TrashCSVImportModal from "@/pages/admin/ScenarioSettingsDetail/components/TrashCSVImportModal.vue";
import TalkNameChangeModal from "@/pages/admin/ScenarioSettingsDetail/components/TalkNameChangeModal.vue";
import ZipCodeList from "@/pages/admin/ScenarioSettingsDetail/components/ZipCodeList.vue";
import DeleteMessageModal from "@/pages/admin/ScenarioSettingsDetail/components/DeleteMessageModal.vue";
import { IDamageReportStorePlugins } from '@/stores/modules/scenarios/scenarios.damage-report.plugin';
import { storeToRefs } from 'pinia';

// old imports
// 旧インポート
/*import {
  FETCH_SCENARIO_DETAIL,
  UPDATE_TEXT_MAPPING,
  SAVE_ACTIVE_SCENARIO,
  FETCH_ALL_SCENARIOS,
  FETCH_ACTIVE_SCENARIO_DATA,
  CREATE_SIMPLE_SPECIAL_SCENARIO,
  DELETE_SPECIAL_SCENARIO,
  DOWNLOAD_CSV_FILE,
  GET_ZIP_CODES,
  DELETE_TALK, DELETE_SCENARIO_MESSAGE,
  UPDATE_SCENARIO_TALK_NAME,
  SET_DAMAGE_REPORT_TALK_VERSION_ACTIVE,
  DELETE_DAMAGE_REPORT_TALK_VERSION,
} from "@/store/action-types";
import {
  DELETE_SCENARIO_TALK_SUCCESS,
  EXPORT_FINISH_SUCCESS, SET_IS_SAVING_ACTIVE_SCENARIO,
  SET_SCENARIO_MINDMAP_MESSAGES
} from "@/store/mutation-types";
import { DISPLAY_MESSAGES, SET_USER_MESSAGES } from "@/stores/mutation-types";
*/

// route
const route = useRoute();
const router = useRouter();

// store with plugin typed
const scenariosStoreUnTyped = useScenariosStore();
export type ScenarioStoreWithPlugin = typeof scenariosStoreUnTyped & IDamageReportStorePlugins;
const scenariosStore = scenariosStoreUnTyped as ScenarioStoreWithPlugin;

// other
const { hasActionPermission, showActionPermissionError } = usePermissionHelper();

// emits 
const emits = defineEmits<{
  (event: 'onExportFinishSuccess'): void;
}>();

const $q = useQuasar();

// props
const props = defineProps({
  searchCriteria: Object as PropType<any>
});

// data
const scenarioId = ref<any>(null);
const versionId = ref<any>(null);
const basicPayload = ref<any>({
  scenarioId: null,
  versionId: null,
});
const hideTextMapping = ref<any>(["jsonTemplate"]);
const displayTalkOptions = ref<any>(null);
const isVisible = ref<boolean>(false);
const fmVisible = ref<boolean>(true);
const csvVisible = ref<boolean>(false);
const isLocationCSV = ref<boolean>(false);
const showLoadingExport = ref<boolean>(false);
const showItemProperties = ref<boolean>(false);
const showCompositeMessage = ref<boolean>(false);
const showUserTextEdit = ref<boolean>(false);
const showSpecialTalk = ref<boolean>(false);
const showImportTalk = ref<boolean>(false);
const showZipCodeModal = ref<boolean>(false);
const showDeleteModal = ref<boolean>(false);
const showTalkNameChangeModal = ref<boolean>(false);
const selectedItem = ref<any>(null);
const selectedTalk = ref<any>(null);
const searchCriteriaLocal = ref<any>(null);
const tableItems = ref<any>([]);
const filteredTableItems = ref<any>([]);
const tableSelected = ref<any>([]);
const searchContentsPhrase = ref<any>(null);
const searchContentsPhraseLowercase = ref<any>(null);
const loadingMessageSearch = ref<boolean>(false);
const textPreviews = ref<any>(["text"]);
// const iconPreviews = ref<any>([
//   "video",
//   "audio",
//   "file",
//   "sticker",
//   "location",
//   "pie",
//   "compositeMessage",
//   "carousel",
//   "confirm",
//   "bubbleFlex",
//   "carouselFlex",
//   "buttons",
//   "jsonTemplate",
// ]);
const imagePreviews = ref<any>(["image", "imagemap"]);
// const specialTalks = ref<any>(["損傷報告", "ゴミ分別", "防災", "防災検索"]);
const specialTalkScenariosToDisplay = ref<any>(["ゴミ分別"]);
  const pagination = ref({
  rowsPerPage: 20,
});
const rowsPerPageOption = [20, 50, 100];
const headers = ref<any>([
  {
    name: "nameLBD",
    label: "メッセージ名",
    field: "nameLBD",
    sortable: true,
    width: "30%",
    align: 'left',
  },
  {
    name: "userInput",
    label: "ユーザー入力",
    field: "userInput",
    sortable: false,
    width: "30%",
    align: 'left',
  },
  {
    name: "previewValue",
    label: "メッセージプレビュー",
    field: "previewValue",
    sortable: false,
    width: "30%",
    align: 'left',
  },
  {
    name: "dataId",
    label: "",
    field: "dataId",
    sortable: false,
    width: "10%",
    align: undefined,
  },
]);
const svgIconConstants = ref<any>(SVG_ICON_CONSTANTS);
const cardClicked = ref<boolean>(false);

// methods
const getDataForDetails = scenariosStore.fetchScenarioDetail;
const updateTextMapping = scenariosStore.updateTextMapping;
const createScenarioTalk = scenariosStore.createSimpleSpecialScenario;
const deleteMessagesForSpecialScenario = scenariosStore.deleteSpecialScenario;
// const deleteMessagesScenario = scenariosStore.deleteTalk;
const updateActiveScenario = scenariosStore.saveActiveScenario;
const downloadCSVFile = scenariosStore.downloadCsvFile;
const exportFinishSuccess = scenariosStore.exportFinishSuccess;
const fetchZipCodes = scenariosStore.getZipCodes;
const deleteScenarioMessage = scenariosStore.deleteScenarioMessage;
const updateScenarioTalkName = scenariosStore.updateScenarioTalkName;
const updateMindMapMessages = scenariosStore.setScenarioMindmapMessages;
const setDeleteScenarioTalkSuccess = scenariosStore.setDeleteScenarioTalkSuccess;
const setIsSavingActiveScenario = scenariosStore.setIsSavingActiveScenario;
const clickOnMainContent = (): void => {
  if (showItemProperties.value) {
    cardClicked.value = !cardClicked.value;
  }
};

const saveTextMappingIntoMindMap = (): void => {
  const payload = {
    versionName: versionId.value,
    valueName: "textMapping",
    value: scenarioTextMap.value.textMapping,
  };
  updateMindMapMessages(payload);
};

const saveScenarioMessagesIntoMindMap = (): void => {
  const payload = {
    versionName: versionId.value,
    valueName: selectedTalk.value,
    value: tableItems.value,
  };
  updateMindMapMessages(payload);
};

const setMessagePreviewItems = (message: any): void => {
  if (textPreviews.value.includes(message.dataType)) {
    message["previewType"] = "text";
    message["previewValue"] =
        message.params.text.length > 20 ? message.params.text.slice(0, 20) + "..." : message.params.text;
  } else if (imagePreviews.value.includes(message.dataType)) {
    message["previewType"] = "image";
    message["previewValue"] = message.params[BOT_ITEM_TYPES[message.dataType].image];
    if (message.dataType === "imagemap") {
      message["previewValue"] = message["previewValue"] + "/240";
    }
  } else {
    message["previewType"] = "text";
    message["previewValue"] = "-";
  }
  if (BOT_ITEM_TYPES[message.dataType] && BOT_ITEM_TYPES[message.dataType].icon) {
    message["previewIcon"] = BOT_ITEM_TYPES[message.dataType].icon;
  } else {
    message["previewIcon"] = "mdi-border-none-variant";
  }
};

const talkOptions = (): any => {
  return scenarioTalks.value ? scenarioTalks.value.map((a: any) => a.params.name).sort() : [];
};

const isLbdWebTalk = (talkName: any): any => {
  if (!scenarioTalks.value) {
    return false;
  }
  return (
      scenarioTalks.value.findIndex((talk: any) => talk.params.name === talkName && talk.params.editor === "lbd-web") !== -1
  );
};

// const specialScenarioTalkSelected = (selectedTalk: any): any => {
//   return selectedTalk.value === null || SPECIAL_TALK_TYPES.includes(selectedTalk.value);
// };

const onEditProperties = (item: any): void => {
  selectedItem.value = cloneDeep(item);
  showItemProperties.value = true;
};

const onCloseEditProperties = (): void => {
  showItemProperties.value = false;
  selectedItem.value = {};
};

// const onEditPropertiesById = (itemId: any): void => {
//   let _item = scenarioMessages.value.find((obj: any) => obj.dataId === itemId);
//   if (_item) {
//     selectedItem.value = _item;
//     showItemProperties.value = true;
//   }
// };

const onEditUserText = (item: any): void => {
  selectedItem.value = item;
  showUserTextEdit.value = true;
};

const onExportFinishSuccess = (): void => {
  //showScenarioVersion = true;
  exportFinishSuccess(false);
};

const openCompositeMessage = (): void => {
  showCompositeMessage.value = true;
};

const openZipCodeCSVUploader = (): void => {
  isLocationCSV.value = true;
  showImportTalk.value = true;
};

const typeTitle = (value: any): void => {
  return BOT_ITEM_TYPES[value] ? BOT_ITEM_TYPES[value].text : "";
};

const save = (result: any, originalInput: any): void => {
  const payload = {
    dataId: result.dataId,
    userInput: result.userInput,
    textMappingData: scenarioTextMap.value,
    talkName: getTalkNameFromId.value,
  } as any;

  //Replace the existing userMessage in talks if editing over
  let existingTalkChange = false;
  const userMessageEdit = cloneDeep(userMessages.value);
  scenarioTalks.value.forEach((talk: any) => {
    talk.params.messages.forEach((msg: any) => {
      if (
          userMessages.value.params &&
          msg.messageId in userMessages.value.params &&
          originalInput.includes(userMessages.value.params[msg.messageId].params.text)
      ) {
        existingTalkChange = true;
        const indexOfNewValue = originalInput.indexOf(userMessages.value.params[msg.messageId].params.text);
        if (indexOfNewValue !== -1 && !(indexOfNewValue >= result.userInput.length)) {
          userMessageEdit.params[msg.messageId].params.text = result.userInput[indexOfNewValue];
        }
      }
    });
  });
  if (existingTalkChange) {
    payload["userMessages.value"] = userMessageEdit;
    scenariosStore.setUserMessages(userMessageEdit);
  }
  if (hasDamageReportTalk.value) {
    // also need to update talk message versions
    const textMappingMsg = cloneDeep(scenarioTextMap.value)
    console.error(result.userInput);
    for (const key in scenarioTextMap.value.textMapping) {
      if (scenarioTextMap.value.textMapping[key] === result.dataId) {
        delete textMappingMsg.textMapping[key]
        for (const element of result.userInput) {
          textMappingMsg.textMapping[element] = result.dataId
        }
      }
    }
    const payloadDR: any = {
      model: textMappingMsg,
      scenario: {
        scenarioId: scenarioId.value,
        versionId: versionId.value,
      },
    }
    payloadDR.talkMessages = scenarioTalks.value.find(msg => msg.dataType === "talk" && msg.params.name === selectedTalk.value)
    scenariosStore.UpdateDamageReportScenarioData(payloadDR)
  }
  else {
    updateTextMapping(payload);
  }
};

const selectTalk = (talk: any): any => {
  if (talk) {
    const result = scenarioTalks.value.filter((obj: any) => {
      return obj.params.name === talk;
    });

    isVisible.value = route.params.talkId === "TRASH_SEPARATION_TALK";
    fmVisible.value = !(route.params.talkId === "DAMAGE_REPORT_TALK" || hasDamageReportTalk.value || route.params.talkId === "TRASH_SEPARATION_TALK");
    csvVisible.value = route.params.talkId === "DAMAGE_REPORT_TALK" || hasDamageReportTalk.value;

    const talkValue = result[0];
    const listOfIds:any[] = [];
    talkValue.params.messages.forEach((message: any) => {
      if (message.sender === "BOT") {
        listOfIds.push(message.messageId);
      }
    });
    displayScenarioMessages.value.forEach((message: any) => {
      if ("talk" in message && message.talk === talk) {
        listOfIds.push(message.dataId);
      }
    });
    tableItems.value = displayScenarioMessages.value.filter((message: any) => {
      return listOfIds.includes(message.dataId);
    });
  } else {
    tableItems.value = displayScenarioMessages.value;
  }
};

const createSpecialScenarios = (talk: any): void => {
  const payload = cloneDeep(basicPayload.value);
  payload.talkName = talk;
  switch (talk) {
    case "ゴミ分別":
      showSpecialTalk.value = false;
      isLocationCSV.value = false;
      showImportTalk.value = true;
      break;
    case "防災":
      showSpecialTalk.value = false;
      createScenarioTalk(payload);
      break;
    case "防災検索":
      showSpecialTalk.value = false;
      createScenarioTalk(payload);
      break;
    case "防災（大雨・台風）":
      showSpecialTalk.value = false;
      createScenarioTalk(payload);
      break;
    case "防災（地震）":
      showSpecialTalk.value = false;
      createScenarioTalk(payload);
      break;
    default:
      createScenarioTalk(payload);
      break;
  }
  setScenarioSpecialTalkData(talk, true);
};

const toggleSpecialScenarios = (talk: any): void => {
  const value = activeScenario.value["versions"][versionId.value]["specialTalks"][SPECIAL_TALK_TYPES_MAPPING[talk]];
  setScenarioSpecialTalkData(talk, !value);
};

const deleteSpecialScenarios = (talk: any): void => {
  if (talk === selectedTalk.value) {
    selectedTalk.value = null;
    selectTalk(null);
  }
  const payload = cloneDeep(basicPayload.value);
  payload["specialTalkName"] = talk;
  $q.dialog({
    title: "特別なシナリオトーク削除確認",
    message: "この特別なシナリオトークを削除してもよろしいですか？",
    // type: "error",
    ok: {
      label: "はい",
    },
    cancel: true,
  }).onOk(async () => {
    deleteMessagesForSpecialScenario(payload);
    setScenarioSpecialTalkData(talk, false);
    if (talk === "ゴミ分別") isVisible.value = false;
  });
};

const setScenarioSpecialTalkData = (talk: any, valueToSet: any): void => {
  const talkName = SPECIAL_TALK_TYPES_MAPPING[talk];
  const scenarioDataToSave = cloneDeep(activeScenario.value);
  if (talkName != null) {
    if ("specialTalks" in activeScenario.value["versions"][versionId.value]) {
      scenarioDataToSave["versions"][versionId.value]["specialTalks"][talkName] = valueToSet;
    } else {
      const data = {} as any;
      data[talkName] = valueToSet;
      scenarioDataToSave["versions"][versionId.value]["specialTalks"] = data;
    }
    updateActiveScenario(scenarioDataToSave);
  }
};

const exportTrashCSV = (): void => {
  showLoadingExport.value = true;
  const payload = {
    scenario: scenarioId.value + "#" + versionId.value,
  };
  downloadCSVFile(payload);
  emits("onExportFinishSuccess");
};

const filterTableItems = (): void => {
  loadingMessageSearch.value = true;

  const {
    messageName,
    userInput,
    text,
    messageType,
  } = searchCriteriaLocal.value || {};

  let results = [...tableItems.value];

  if (messageName) {
    results = results.filter((item) => {
      return item.nameLBD.includes(messageName);
    });
  }
  if (userInput) {
    results = results.filter((item) => {
      if (!item.userInput) {
        return false;
      }
      return item.userInput.findIndex((input: any) => input.includes(userInput)) > -1;
    });
  }
  if (text) {
    results = results.filter((item) => {
      const { params } = item || {};
      const keys = Object.keys(params).filter((key) => key.startsWith("text"));
      for (const key of keys) {
        const value = params[key];
        if (value.includes(text)) {
          return true;
        }
      }
      return false;
    });
  }
  if (messageType) {
    results = results.filter((item) => item.dataType === messageType);
  }
  filteredTableItems.value = results;

  loadingMessageSearch.value = false;
};

// const checkTextComponentSearchMatch = (item: any, textProperty: any): any => {
//   return (
//       item.params &&
//       item.params[textProperty] &&
//       item.params[textProperty].toLowerCase().includes(searchContentsPhraseLowercase.value)
//   );
// };

// const checkNameLBDSearchMatch = (item: any): any => {
//   return item.nameLBD && item.nameLBD.toLowerCase().includes(searchContentsPhraseLowercase.value);
// };

const checkActionComponentSearchMatch = (action: any): any => {
  return action && action.label && action.label.toLowerCase().includes(searchContentsPhraseLowercase.value);
};

// const checkButtonActionsSearchMatch = (item: any): boolean => {
//   let actionsMatch = false;
//   if (item.params && item.params.actionCount) {
//     for (let index = 0; index < item.params.actionCount; index++) {
//       const tempAction = item.params["actions." + index];
//       if (checkActionComponentSearchMatch(tempAction)) {
//         actionsMatch = true;
//         break;
//       }
//     }
//   }
//   return actionsMatch;
// };

// const checkCarouselActionsSearchMatch = (item: any): boolean => {
//   let actionsMatch = false;
//   if (item.params && item.params.columnCount && item.params.actionCount) {
//     const columnCount = item.params.columnCount;
//     const actionCount = item.params.actionCount;
//     for (let j = 0; j < columnCount; j++) {
//       if (actionsMatch) {
//         break;
//       } else {
//         for (let k = 0; k < actionCount; k++) {
//           const tempAction = item.params["action." + j + "." + k];
//           if (checkActionComponentSearchMatch(tempAction)) {
//             actionsMatch = true;
//             break;
//           }
//         }
//       }
//     }
//   }
//   return actionsMatch;
// };

// const checkBubbleFlexSearchMatch = (item: any): boolean => {
//   let foundInBody = false;
//   if (item.params && item.params.body) {
//     if (item.params.body.contents) {
//       for (let index = 0; index < item.params.body.contents.length; index++) {
//         foundInBody = componentSearchMatch(item.params.body.contents[index]);
//         if (foundInBody) {
//           break;
//         }
//       }
//     }
//   }
//   let foundInHeader = false;
//   if (item.params && item.params.header) {
//     if (item.params.header.contents) {
//       for (let index = 0; index < item.params.header.contents.length; index++) {
//         foundInHeader = componentSearchMatch(item.params.header.contents[index]);
//         if (foundInHeader) {
//           break;
//         }
//       }
//     }
//   }
//   let foundInFooter = false;
//   if (item.params && item.params.footer) {
//     if (item.params.footer.contents) {
//       for (let index = 0; index < item.params.footer.contents.length; index++) {
//         foundInFooter = componentSearchMatch(item.params.footer.contents[index]);
//         if (foundInFooter) {
//           break;
//         }
//       }
//     }
//   }
//   return foundInBody || foundInHeader || foundInFooter;
// };

const componentContentsSearchMatch = (contents: any): boolean => {
  let tempResult = false;
  for (let index = 0; index < contents.length; index++) {
    if (componentSearchMatch(contents[index])) {
      tempResult = true;
      break;
    }
  }
  return tempResult;
};

const componentSearchMatch = (component: any): any => {
  if (!component.type) {
    return false;
  }
  if (component.type === "span") {
    return component.text && component.text.toLowerCase().includes(searchContentsPhraseLowercase.value);
  } else if (component.type === "button") {
    return checkActionComponentSearchMatch(component.action);
  } else if (component.type === "text") {
    if (component.text && component.text.toLowerCase().includes(searchContentsPhraseLowercase.value)) {
      return true;
    } else if (component.contents) {
      return componentContentsSearchMatch(component.contents);
    }
  } else if (component.contents) {
    return componentContentsSearchMatch(component.contents);
  } else {
    return false;
  }
};

// const checkCarouselTextSearchMatch = (item: any): boolean => {
//   let foundMatch = false;
//   if (item.params) {
//     for (let index = 0; index < item.params.columnCount; index++) {
//       if (
//           item.params["text." + index] &&
//           item.params["text." + index].toLowerCase().includes(searchContentsPhraseLowercase.value)
//       ) {
//         foundMatch = true;
//         break;
//       }
//     }
//   }
//   return foundMatch;
// };

// const checkCarouselTitleSearchMatch = (item: any): boolean => {
//   let foundMatch = false;
//   if (item.params && item.params.useTitle) {
//     for (let index = 0; index < item.params.columnCount; index++) {
//       if (
//           item.params["title." + index] &&
//           item.params["title." + index].toLowerCase().includes(searchContentsPhraseLowercase.value)
//       ) {
//         foundMatch = true;
//         break;
//       }
//     }
//   }
//   return foundMatch;
// };

const openZipCodeList = (): void => {
  showZipCodeModal.value = true;
  //other data to be added
};

const disableDeleteButton = (): any => {
  if (tableSelected.value.length === 0) {
    return true;
  }

  return tableSelected.value.some((modelLocal: any) => {
    //Check for message being part of special scenario talk
    if (modelLocal.params && "specialScenarioTalk" in modelLocal.params) {
      if (!("userCreatedSpecialTalkComposite" in modelLocal.params)) {
        return true;
      }
    }

    //Check for message in other composite messages
    let modelInCompositeMessage = false;
    scenarioMessages.value.forEach((msg: any) => {
      if (msg.dataType === "compositeMessage") {
        msg.messages.forEach((dataId: any) => {
          if (modelLocal.dataId === dataId) {
            modelInCompositeMessage = true;
          }
        });
      }
    });
    return modelInCompositeMessage;
  });
};

const onDeleteItem = async (): Promise<void> => {
  setIsSavingActiveScenario(true);
  try {
    for (const modelLocal of tableSelected.value) {
      const payload = {
        dataId: modelLocal.dataId,
        dataType: modelLocal.dataType,
        scenarioId: scenarioId.value,
        versionId: versionId.value,
        itemParams: modelLocal.params,
      };
      await deleteScenarioMessage(payload);

      // Update text mapping
      modelLocal.userInput = [];
      await updateTextMapping({
        ...modelLocal,
        textMappingData: scenarioTextMap.value
      });
    }
    showDeleteModal.value = false;
  } finally {
    setIsSavingActiveScenario(false);
  }
};

const updateTalkName = async (newTalkName: any): Promise<void> => {
  setIsSavingActiveScenario(true);
  try {
    let response = await updateScenarioTalkName({
      scenarioId: route.params.scenarioId,
      versionId: route.params.versionId,
      talkObject: scenarioTalks.value.find((talk: any) =>
        talk.params && talk.params.name === getTalkNameFromId.value
      ),
      newTalkName: newTalkName,
    });
    if (response) {
      selectedTalk.value = newTalkName;
    }
  } finally {
    setIsSavingActiveScenario(false);
  }
};

const setUpTalkData = async (): Promise<void> => {
  await getDataForDetails(basicPayload.value);

  selectedTalk.value = getTalkNameFromId.value;
  selectTalk(getTalkNameFromId.value);
};

const selectTableRow = (details: any) => {
  if (tableSelected.value.length === 0) {
    tableSelected.value = details.rows;
  }
  else if (details.rows.length !== 1) {
    if (tableSelected.value.length !== details.rows.length) {
      tableSelected.value = details.rows;
    }
    else {
      tableSelected.value = [];
    }
  }
  else {
    let isfilter = false;
    tableSelected.value = tableSelected.value.filter((item: any) => {
      if (item.dataId === details.rows[0].dataId) {
        isfilter = true;
      }
      return item.dataId !== details.rows[0].dataId;
    });

    if (!isfilter) {
      tableSelected.value.push(details.rows[0]);
    }
  }
};

// computed
const scenarioMessages = computed(() => scenariosStore.scenarioMessages);
// const scenarioMindmap = computed(() => scenariosStore.scenarioMindmap);
const isExportingScenarioData = computed(() => scenariosStore.isExportingScenarioData);
const scenarioTextMap = computed(() => scenariosStore.scenarioTextmap);
const activeScenario = computed(() => scenariosStore.activeScenario);
const scenarioTalks = computed(() => scenariosStore.scenarioTalks);
const userMessages = computed(() => scenariosStore.userMessages);
const activeScenarioData = computed(() => scenariosStore.activeScenarioData);
// const isFetchingScenarioDetail = computed(() => scenariosStore.isFetchingScenarioDetail);
const fetchScenarioDetailError = computed(() => scenariosStore.fetchScenarioDetailError);
// const isSavingActiveScenario = computed(() => scenariosStore.isSavingActiveScenario);
const exportingScenarioDataError = computed(() => scenariosStore.exportingScenarioDataError);
const deleteScenarioTalkSuccess = computed(() => scenariosStore.deleteScenarioTalkSuccess);
const changingTalkNameError = computed(() => scenariosStore.changingTalkNameError);

const getTalkNameFromId = computed((): any => {
  const talk = scenarioTalks.value.find(elem => elem.dataId == route.params.talkId);
  return talk ? talk.params.name : "トーク名";
});

const displayScenarioMessages = computed((): any => {
  const displayData = cloneDeep(scenarioMessages.value);
  displayData.forEach((message) => {
    const idToFind = message.dataId;
    const listOfTexts = scenarioTextMap.value.textMapping || {} as any;
    const userText: any[] = [];
    Object.keys(listOfTexts).forEach((key) => {
      if (listOfTexts[key] === idToFind) {
        userText.push(key);
      }
    });
    message["userInput"] = userText;
    setMessagePreviewItems(message);
  });
  return displayData;
});

const environment = computed((): any => {
  return SCENARIO_ENV_TYPES[route.params.env.toString().toLowerCase()].text;
});

// #region damage-report scenario edit
const { isSavingTalkNodes, isDeletingScenarioVersion } = storeToRefs(scenariosStore);
const currentDamageReportTalk = ref();
const showCreateDamageReportTalkModal = ref(false);

const damageReportTalkVersion = computed((): any => {
  return scenarioTalks.value?.filter(x => x.versionOf === 'damage-report' || x.dataId === 'DAMAGE_REPORT_TALK')?.map(x => {
    return {
      ...x,
      value: x.dataId,
      text: x.params?.name,
      versionId: x.versionId
    };
  });
});

const hasDamageReportTalk = computed((): any => {
  return damageReportTalkVersion.value?.find(x => x.value === route.params.talkId.toString());
});

watch(hasDamageReportTalk, (oldVal, newVal) => {
  if (!currentDamageReportTalk.value) {
    currentDamageReportTalk.value = newVal;
  }
});

const activeDamageTalkVersion = computed((): any => {
  return scenarioTalks.value.find(x => x.dataId === "DAMAGE_REPORT_TALK")?.versionActive || "DAMAGE_REPORT_TALK";
});

function onCreateTalkClick() {
  showCreateDamageReportTalkModal.value = true;
}

async function onSetActiveTalkClick(e) {
  const payload = {
    "scenario": currentDamageReportTalk.value.scenario,
    "versionActive": currentDamageReportTalk.value !== "DAMAGE_REPORT_TALK" ? currentDamageReportTalk.value.value : null
  };
  await scenariosStore.SetDamageReportTalkVersionActive(payload);
}

async function onDeleteTalkClick(e) {
  const payload = {
    "scenario": currentDamageReportTalk.value.scenario,
    "versionId": currentDamageReportTalk.value.value
  };
  try {
    await scenariosStore.DeleteDamageReportTalkVersion(payload);
  } catch (error) {
    console.error(error);
  }
}

function changeDamageReportActiveVersion(e) {
  selectTalk(currentDamageReportTalk.value.text);
  selectedTalk.value = currentDamageReportTalk.value.text;
  router.push({
    name: "ScenarioSettingsDetailPage",
    params: {
      scenarioId: scenarioId.value,
      versionId: versionId.value,
      talkId: currentDamageReportTalk.value.value
    },
  });
}

// watch
watch(
  () => scenarioTextMap.value, 
  () => {
    if (selectedTalk.value) {
      selectTalk(selectedTalk.value);
    } else {
      tableItems.value = displayScenarioMessages.value;
    }
  }
);

watch(
  () => isExportingScenarioData.value, 
  (value) => {
    if (!value) {
      showLoadingExport.value = false;
    }
  }
);

watch(
  () => scenarioTalks.value, 
  () => {
    displayTalkOptions.value = talkOptions();
  }
);

watch(
  () => scenarioMessages.value, 
  () => {
    if (selectedTalk.value) {
      selectTalk(selectedTalk.value);
    } else {
      tableItems.value = displayScenarioMessages.value;
    }
  }
);

watch(
  () => props.searchCriteria, 
  (val) => {
    searchCriteriaLocal.value = val;
    filterTableItems();
  }
);

watch(
  () => tableItems.value, 
  () => {
    tableSelected.value = [];
    filterTableItems();

    saveTextMappingIntoMindMap();
    saveScenarioMessagesIntoMindMap();
  }
);

watch(
  () => filteredTableItems.value, 
  (val) => {
    scenariosStore.setDisplayMessages(cloneDeep(val));
  }
);

watch(
  () => fetchScenarioDetailError.value, 
  (val) => {
    if (val) {
      if (val instanceof String) {
        $q.notify({ message: val.toString(), type: "error" });
      } else {
        $q.notify({ message: val.message, type: "error" });
      }
    }
  }
);

watch(
  () => exportingScenarioDataError.value, 
  (val) => {
    if (val) {
      if (val instanceof String) {
        $q.notify({ message: val.toString(), type: "error" });
      } else {
        $q.notify({ message: val.message, type: "error" });
      }
    }
  }
);

watch(
  () => changingTalkNameError.value, 
  (val) => {
    if (val) {
      $q.notify({ message: val, type: "error" });
    }
  }
);

watch(
  () => searchContentsPhrase.value, 
  (val) => {
    if (val != null) {
      searchContentsPhraseLowercase.value = val.toLowerCase();
    }
  }
);

watch(
  () => deleteScenarioTalkSuccess.value, 
  (val) => {
    if (val) {
      if (damageReportTalkVersion.value.length > 0) {
        setTimeout(() => {
          setDeleteScenarioTalkSuccess(null);
          selectTalk(damageReportTalkVersion.value[0].text);
          selectedTalk.value = damageReportTalkVersion.value[0].text;
          currentDamageReportTalk.value = damageReportTalkVersion.value[0];
          router.push({
            name: "ScenarioSettingsDetailPage",
            params: {
              scenarioId: scenarioId.value,
              versionId: versionId.value,
              talkId: currentDamageReportTalk.value.value
            },
          });
        }, 800);
      }
      else {
        setDeleteScenarioTalkSuccess(null);
        selectTalk(null);
        selectedTalk.value = null;
      }
    }
  }
);

// hooks
onBeforeMount(() => {
  scenarioId.value = route.params.scenarioId;
  versionId.value = route.params.versionId;
  basicPayload.value = {
    scenarioId: route.params.scenarioId,
    versionId: route.params.versionId
  };
  displayTalkOptions.value = talkOptions();
  searchCriteriaLocal.value = props.searchCriteria;
  if (!activeScenario.value.scenarioId) {
    scenariosStore.fetchAllScenarios();
  }
  if (!activeScenarioData.value.activeScenarioId) {
    scenariosStore.fetchActiveScenarioData();
  }
  setUpTalkData();
  fetchZipCodes(basicPayload.value);
});

</script>

<style scoped>
.active-talk-name {
  font-size: 20px;
  font-weight: bold;
}
.active-talk-name.clickable {
  text-decoration: underline;
  cursor: pointer;
}
</style>

<style>
.scenario-table.v-data-table tbody tr {
  height: 64px;
}
</style>
