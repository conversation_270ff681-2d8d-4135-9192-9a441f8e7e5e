<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div class="tw-mt-4">
    <div 
      v-if="(isFetchingScenarioDetail || isSavingActiveScenario)" 
      class="tw-flex tw-justify-center tw-items-center" 
      style="height: 450px;"
    >
      <q-spinner :size="50" color="primary" />
    </div>
    <SubAppBar :class="`${isMenuVertical ? 'tw-pt-12' : ''}`" v-else>
      <div class="tw-flex tw-justify-between tw-items-center">
        <div class="">
          <router-link class="text-primary tw-underline" :to="{name: 'ScenarioSettingsPage'}">シナリオ一覧</router-link>
          <span class="text-blue-grey" style="opacity: 0.6">
            -
          </span>
          <router-link
              class="text-primary tw-underline"
              :to="{ 
                name: 'ScenarioVersionSettingsPage', 
                params: { env: route.params.env, scenarioId: route.params.scenarioId, versionId: route.params.versionId } 
              }"
            >
              {{ getActiveDisplayName(route.params.versionId, 'version') || routeValues.scenerioId }}
            </router-link>

          <span class="text-blue-grey" style="opacity: 0.6">
            -
          </span>
          <span class="text-blue-grey">
            {{ getTalkNameFromId }}
          </span>
        </div>
        <div class="d-flex">
          <q-btn color="primary" @click="toggleShowSearch()">
            検索条件
            <q-icon right v-if="showSearch" name="mdi-chevron-up" />
            <q-icon right v-else name="mdi-chevron-down" />
          </q-btn>
          <q-btn @click="onMindMap" class="tw-ml-2" :disabled="route.params.talkId === 'TRASH_SEPARATION_TALK'">
            マップ表示
            <q-tooltip bottom v-if="route.params.talkId === 'TRASH_SEPARATION_TALK'">
              <span>
                "ゴミ分別" トークはマップ表示に対応していません
              </span>
            </q-tooltip>
          </q-btn>
        </div>
      </div>
      <div v-if="showSearch" class="tw-mt-4">
        <q-separator class="tw-mb-4" />
        <q-form @submit.prevent>
          <div class="row">
            <div class="col-12 col-lg-6 tw-py-3">
              <div class="text-blue-grey tw-font-semibold">
                メッセージ名
              </div>
              <q-input
                v-model="messageName"
                dense
                outlined
                hide-details="auto"
                clearable
                class="tw-mx-1"
              />
            </div>
            <div class="col-12 col-lg-6 tw-py-3">
              <div class="text-blue-grey tw-font-semibold">
                ユーザー入力
              </div>
              <q-input
                v-model="userInput"
                dense
                outlined
                hide-details="auto"
                clearable
                class="tw-mx-1"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-12 col-lg-6 tw-py-3">
              <div class="text-blue-grey tw-font-semibold">
                テキスト
              </div>
              <q-input
                v-model="text"
                dense
                outlined
                hide-details="auto"
                clearable
                class="tw-mx-1"
              />
            </div>
            <div class="col-12 col-lg-6 tw-py-3">
              <div class="text-blue-grey tw-font-semibold">
                種別
              </div>
              <q-select
                v-model="messageType"
                :options="messageTypes"
                dense
                outlined
                hide-details="auto"
                class="tw-mx-1"
              />
            </div>
          </div>
          <div class="tw-flex tw-justify-end tw-py-4">
            <q-btn
              outline
              color="warning"
              @click="clearSearchCriteria()"
            >
              検索条件をクリア
            </q-btn>
            <q-btn
              class="tw-ml-2"
              color="primary"
              type="submit"
              @click="onClickSearch()"
            >
              この条件で検索
            </q-btn>
          </div>
        </q-form>
      </div>
    </SubAppBar>
    <ScenarioSettingsDetail
      v-show="!(isFetchingScenarioDetail || isSavingActiveScenario)"
      :searchCriteria="searchCriteria"
    />
  </div>
</template>

<script setup lang="ts">
import { useScenariosStore } from '@/stores/modules/scenarios';
import { ref, computed, onBeforeMount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { BOT_ITEM_TYPES } from "@/stores/modules/scenarios/scenarios.constants";
import SubAppBar from '@/components/common/SubAppBar.vue';
import ScenarioSettingsDetail from "@/pages/admin/ScenarioSettingsDetail/fragments/ScenarioSettingsDetail.vue";
import { useSettingsStore } from '@/stores/modules/settings';

// old imports
// 旧インポート
/*
import {FETCH_ALL_SCENARIOS} from "@/store/action-types";*/

// store
const scenariosStore = useScenariosStore();
const settingsStore = useSettingsStore();

// route
const router = useRouter();
const route = useRoute();

// props
const props = defineProps<{
  scenario: any,
  emptySelectValue?: any,
  excludeMessageType?: any,
}>();

// emit
const emit = defineEmits(['showStartDateMenu', 'showEndDateMenu']);

// data
const showSearch = ref<boolean>(false);
const searchCriteria = ref<any>({});
const messageName = ref<string>("");
const userInput = ref<string>("");
const text = ref<string>("");
const isMenuVertical = ref(settingsStore.commonSettings.menuStyle === "vertical");
const messageType = ref<any>(props.emptySelectValue || '－－－－－');
const routeValues = ref({
  scenerioId : '',
  versionId: '' as string | string[],
  versions: {}
});
const messageTypes = ref<any>([
  props.emptySelectValue || '－－－－－',
  ...Object
      .keys(BOT_ITEM_TYPES)
      .filter((key) => !props.excludeMessageType?.includes(key))
      .map((key) => BOT_ITEM_TYPES[key].text),
]);

const messageTypeValues = ref<any>(Object.keys(BOT_ITEM_TYPES).reduce((obj: any, key) => {
    const value = BOT_ITEM_TYPES[key];
    obj[value.text] = key;
    return obj;
  }, {})
);

// methods
const getActiveDisplayName = (versionId: any, type: string): string => {
  if (!activeScenario.value || !activeScenario.value.versions) {
    return '';
  }

  const displayNameField = type === 'scenario' ? 'displayScenarioName' : 'displayVersionName';
  const displayName = activeScenario.value.versions[versionId]?.[displayNameField] || '';
  return displayName;
};

const onMindMap = (): void => {
  const {
    scenarioId,
    versionId,
  } = route.params;
  const talkName = getTalkNameFromId.value;
  router.push({
    name: "ScenarioMindmapPage",
    params: {
      scenarioId,
      versionId,
      talkName,
    },
  });
};

const toggleShowSearch = (show: any = null): void => {
  emit('showStartDateMenu', false);
  emit('showEndDateMenu', false);
  if (show != null) {
    showSearch.value = show;
  } else {
    showSearch.value = !showSearch.value;
  }
};

const clearSearchCriteria = (): void => {
  messageName.value = "";
  userInput.value = "";
  text.value = "";
  messageType.value = props.emptySelectValue;
  onClickSearch();
};

const onClickSearch = (): void => {
  searchCriteria.value = {
    messageName: messageName.value,
    userInput: userInput.value,
    text: text.value,
    messageType: messageType.value !== props.emptySelectValue ? messageTypeValues.value[messageType.value] : undefined,
  };
};

// computed
const isFetchingScenarioDetail = computed(() => scenariosStore.isFetchingScenarioDetail);
const isSavingActiveScenario = computed(() => scenariosStore.isSavingActiveScenario);
const scenarioTalks = computed(() => scenariosStore.scenarioTalks);
const activeScenario = computed(() => {
  return scenariosStore.activeScenario || {} as any;
});

const getTalkNameFromId = computed((): any => {
  const talk = scenarioTalks.value.find(elem => elem.dataId == route.params.talkId);
  return talk ? talk.params.name : "トーク名";
});

// hooks
onBeforeMount(() => {
  if (!activeScenario.value.scenarioId) {
    scenariosStore.fetchAllScenarios();
  }
  if (route.params) {
    routeValues.value = {
      scenerioId: route.params.scenarioId as string,
      versionId: route.params.versionId
    };
  }
});
</script>
