<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.modal-contents {
  padding-bottom: 0em !important;
}
</style>

<template>
  <q-dialog scrollable v-model="show" max-width="600">
    <q-card class="tw-w-full">
      <q-bar class="bg-primary tw-h-[5px]"></q-bar>
      <q-toolbar flat>
        <q-toolbar-title>トーク名変更</q-toolbar-title>
        <q-space></q-space>
        <q-btn icon="mdi-close" flat round @click="show = false" />
      </q-toolbar>

      <q-card-section class="modal-contents tw-mx-4">
        <q-row v-if="newTalkName != originalTalkName && inputAlreadyExists">
          <span class="red--text">トーク名を既存のトークと同じにすることはできません。</span>
        </q-row>
        <q-row>
          <q-col cols="10">
            <q-input
              outlined
              single-line
              hide-details
              dense
              :label-slot="!newTalkName ? true : false"
              v-model="newTalkName"
            >
              <template v-slot:label>
                トーク名
              </template>
            </q-input>
          </q-col>
        </q-row>
        <div style="color: black; font-size: 0.8em;" v-if="!isTemplateTalk" class="tw-py-4">
          ※このトーク名を変更すると、このトークに対するトーク配信設定が解除されます。<br>
          トーク配信設定を維持する場合、トーク配信設定を再度設定し直してください。
        </div>
      </q-card-section>
      <q-card-actions class="tw-p-4" align="right">
        <div class="row">
          <q-btn
            color="primary"
            elevation="4"
            class="tw-mr-2 tw-w-24"
            :disable="saveDisabled"
            @click="updateTalkName"
            icon="mdi-import"
            label="変更"
          />
          <q-btn
            class="text-blue-grey tw-w-32"
            @click="show = false"
            icon="mdi-cancel"
            label="キャンセル"
            outline
          />
        </div>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { useScenariosStore } from '@/stores/modules/scenarios';
import { PropType, ref,  computed, watch, onBeforeMount } from 'vue';
import { TEMPLATE_TALK_IDS } from "@/stores/modules/scenarios/scenarios.constants";
import { isNullOrEmpty } from "@/utils/stringUtils";
import { cloneDeep } from "lodash";

// store
const scenariosStore = useScenariosStore();

// emits 
const emits = defineEmits<{
  (event: 'updateTalk', payload: any): void;
  (event: 'close'): void;
}>();

// props
const props = defineProps({
  visible: Boolean as PropType<boolean>,
  existingTalks: Array as PropType<any>,
  talkId: String as PropType<string>,
  originalTalkName: String as PropType<string>
});

// data
const newTalkName = ref<any>(null);

// methods
const updateTalkName = () => {
    emits("updateTalk", newTalkName.value);
    show.value = false;
};

// computed
const scenarioMindmap = computed(() => scenariosStore.scenarioMindmap);
const activeScenario = computed(() => scenariosStore.activeScenario);
const scenarioTextMap = computed(() => scenariosStore.scenarioTextmap);
const scenarioTalks = computed(() => scenariosStore.scenarioTalks);

const isTemplateTalk = computed(() => {
  return TEMPLATE_TALK_IDS.includes(props.talkId ?? '');
});

const inputAlreadyExists = computed(() => {
    return props.existingTalks.includes(newTalkName.value);
});

const saveDisabled = computed(() => {
  return isNullOrEmpty(newTalkName.value) ||
      newTalkName.value === props.originalTalkName ||
      inputAlreadyExists.value;
});

const show = computed({
  get() {
    return props.visible;
  },
  set(value) {
    if (!value) {
      emits("close");
    }
  },
});

// watch
watch(
  () => props.visible, 
  (value) => {
    if (value) {
      newTalkName.value = cloneDeep(props.originalTalkName);
    }
  }
);

// hooks

onBeforeMount(() => {
  newTalkName.value = cloneDeep(props.originalTalkName);
});

</script>
