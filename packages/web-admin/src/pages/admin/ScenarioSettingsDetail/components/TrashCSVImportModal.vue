<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.gomi-import-instruction-line {
  background: linear-gradient(#000, #000) no-repeat center/1px 100%;
}
.gomi-import-instruction-bubble {
  text-align: center;
}
</style>
<template>
  <q-dialog scrollable persistent v-model="show" :max-width="maxWidthDialog">
    <q-card class="tw-w-full" style="max-width: 900px;">
      <q-card class="tw-sticky tw-top-0 tw-z-10">
        <q-bar class="bg-primary tw-h-1"></q-bar>
        <q-toolbar flat>
          <q-toolbar-title v-if="!isLocationCSV" class="tw-font-semibold text-blue-grey"> ゴミ分別</q-toolbar-title>
          <q-toolbar-title v-else class="tw-font-semibold text-blue-grey"> 郵便番号のcsv登録</q-toolbar-title>
  
          <q-space></q-space>
  
          <q-btn @click="endImport" flat round>
            <q-icon name="mdi-close"></q-icon>
          </q-btn>
        </q-toolbar>
      </q-card>
      <q-card-section v-if="!isLocationCSV && !existingTalks.includes('ゴミ分別')">
        <div>
          <p class="text-h5 text-primary">まだゴミ分別についての情報が追加されていないようです。</p>
          <p class="tw-py-3">以下の手順に治って、3ステップでゴミの情報をご登録いただけます。</p>
          <div class="row tw-py-3">
            <div class="gomi-import-instruction-bubble col-1">
              <q-icon size="md" color="secondary" name="mdi-numeric-1-circle"></q-icon>
            </div>
            <div class="col">
              <p class="tw-font-semibold">ゴミ分別登録用のテンプレート・ファイルをダウンロード</p>
            </div>
          </div>
          <div class="row tw-py-3">
            <div class="gomi-import-instruction-line col-1"></div>
            <div class="col">
              <p class="tw-py-3">CSVファイルにて、ゴミ分別登録用のテンプレートをご用意しております。</p>
              <p class="tw-pb-3">以下のボタンよりダウンロードいただき、ご記入を進めてください。</p>
              <q-btn :href="baseSampleFileUrl + sampleFileName" target="_blank" download>
                <q-icon name="mdi-file-delimited"></q-icon>テンプレートをダウンロードする
              </q-btn>
            </div>
          </div>

          <div class="row tw-py-3">
            <div class="gomi-import-instruction-bubble col-1">
              <q-icon size="md" color="secondary" name="mdi-numeric-2-circle"></q-icon>
            </div>
            <div class="col">
              <p class="tw-font-semibold">テンプレートにゴミ情報を記載する</p>
            </div>
          </div>
          <div class="row tw-py-3">
            <div class="gomi-import-instruction-line col-1"></div>
            <div class="col-4">
              <q-img
                @click="imageDisplay(require('@/assets/scenario-assets/gomiSample.jpg'))"
                :src="gomiSample"
                max-width="250"
              >
              </q-img>
            </div>
            <div class="col tw-pl-3">
              <p class="tw-py-3">テンプレートはエクセルやNumbersで開くことができます。</p>
              <p class="tw-pb-3">記載の方法にしたがって、記入を行って下さい。</p>
            </div>
          </div>
          <div class="row tw-py-3">
            <div class="gomi-import-instruction-bubble col-1">
              <q-icon size="md" color="secondary" name="mdi-numeric-3-circle"></q-icon>
            </div>
            <div class="col">
              <p class="tw-font-semibold">記載したファイルをアップロードする</p>
            </div>
          </div>
          <div class="row tw-py-3">
            <div class="gomi-import-instruction-line col-1"></div>
            <div class="col-10">
              <p>テンプレートをこちらにアップロードすると、ゴミの種類の登録が完了します。</p>
              <q-file v-model="fileData" accept=".csv" clearable>
                <template v-slot:prepend>
                  <q-icon name="attach_file"></q-icon>
                </template>
              </q-file>
            </div>
          </div>
          <div class="row tw-justify-center">
            <span class="text-caption">ファイル形式：CSV</span>
          </div>
        </div>
      </q-card-section>
      <q-card-section v-else-if="isLocationCSV && !hasZipCodes">
        <div class="text-grey-8">
          <p class="text-h5 text-primary">まだ郵便番号が追加されていないようです。</p>
          <p class="tw-py-3">以下の手順に治って、3ステップで郵便番号をご登録いただけます。</p>
          <div class="row tw-py-3">
            <div class="gomi-import-instruction-bubble col-1">
              <q-icon size="md" color="secondary" name="mdi-numeric-1-circle"></q-icon>
            </div>
            <div class="col">
              <p class="tw-font-semibold">郵便番号登録用のテンプレート・ファイルをダウンロード</p>
            </div>
          </div>
          <div class="row tw-py-3">
            <div class="gomi-import-instruction-line col-1"> </div>
            <div class="col">
              <p class="tw-py-3">CSVファイルにて、郵便番号登録用のテンプレートをご用意しております。</p>
              <p class="tw-pb-3">以下のボタンよりダウンロードいただき、ご記入を進めてください。</p>
              <q-btn :href="baseSampleFileUrl + sampleZipCodeFileName" target="_blank" download>
                <q-icon name="mdi-file-delimited"></q-icon>テンプレートをダウンロードする
              </q-btn>
            </div>
          </div>

          <div class="row tw-py-3">
            <div class="gomi-import-instruction-bubble col-1">
              <q-icon size="md" color="secondary" name="mdi-numeric-2-circle"></q-icon>
            </div>
            <div class="col">
              <p class="tw-font-semibold">テンプレートに郵便番号を記載する</p>
            </div>
          </div>
          <div class="row">
            <div class="gomi-import-instruction-line col-1"></div>
            <div class="col">
              <p class="tw-py-3">テンプレートはエクセルやNumbersで開くことができます。</p>
              <p class="tw-pb-3">各行は1つの郵便番号に対応します。また、ヘッダーはありません。</p>
            </div>
          </div>
          <div class="row tw-py-3">
            <div class="gomi-import-instruction-bubble col-1">
              <q-icon size="md" color="secondary" name="mdi-numeric-3-circle"></q-icon>
            </div>
            <div class="col">
              <p class="tw-font-semibold">記載したファイルをアップロードする</p>
            </div>
          </div>
          <div class="row">
            <div class="gomi-import-instruction-line col-1"></div>
            <div class="col-10">
              <p class="tw-py-3">テンプレートをこちらにアップロードすると、郵便番号の登録が完了します。</p>
              <q-file v-model="fileData" accept=".csv" clearable>
                <template v-slot:prepend>
                  <q-icon name="attach_file"></q-icon>
                </template>
              </q-file>
            </div>
          </div>
          <div class="tw-justify-center row tw-py-4">
            <span class="text-caption">ファイル形式：CSV</span>
          </div>
        </div>
      </q-card-section>
      <q-card-section v-else>
        <div class="row">
          <div class="col">
            <q-file v-model="fileData" accept=".csv" clearable>
              <template v-slot:prepend>
                <q-icon name="attach_file"></q-icon>
              </template>
            </q-file>
          </div>
        </div>
      </q-card-section>
      <q-card-actions class="tw-p-4" align="center">
        <div>
          <q-btn
            color="primary"
            class="tw-px-6 tw-mr-2"
            @click="importFile"
            :loading="isFetchingScenarioDetail"
            :disable="!fileData"
          >
            <q-icon left name="mdi-import"></q-icon>
            登録する
          </q-btn>
          <q-btn class="tw-px-6 tw-mr-2" @click="endImport">
            <q-icon left name="mdi-cancel"></q-icon>
            キャンセル
          </q-btn>
        </div>
      </q-card-actions>
    </q-card>
    <ImageDisplayModal :visible="showImageDisplay" :imgSrc="imageSource" @close="showImageDisplay = false" />
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onBeforeMount } from 'vue';
import { useQuasar } from 'quasar';
import { useScenariosStore } from '@/stores/modules/scenarios';
import gomiSample from '@/assets/scenario-assets/gomiSample.jpg';

// old imports
// 旧インポート
/*import { UPLOAD_CSV_FILE, UPLOAD_LOCATION_CSV_FILE } from "@/store/action-types";
import { cloneDeep } from "lodash";
import ImageDisplayModal from "@/components/common/ImageDisplayModal.vue";*/

// store
const scenariosStore = useScenariosStore();

// emits 
const emits = defineEmits<{
  (event: 'close'): void;
  (event: 'onImportFinishSuccess'): void;
}>();

const $q = useQuasar();

const props = defineProps<{
  visible: boolean,
  scenario?: any,
  scenarioName: string,
  versionName: string,
  versionList?: any[],
  isLocationCSV: boolean,
  existingTalks: any[],
}>();

// data
const fileData = ref<any>(undefined);
const disableImport = ref<boolean>(true);
const scenarioVersion = ref<string>("");
const rules = ref<any>(null);
const showImageDisplay = ref<boolean>(false);
const imageSource = ref<any>(null);
const sampleFileName = ref<string>("/gomibunbetsu-template.csv");
const sampleZipCodeFileName = ref<string>("/損傷報告郵便番号登録_テンプレート.csv");
const baseSampleFileUrl = ref<any>(window.location.origin);

// methods
const uploadCSVFile = scenariosStore.uploadCsvFile;
const uploadLocationCSV = scenariosStore.uploadLocationCsvFile;

const endImport = (): void => {
  show.value = false;
  fileData.value = undefined;
};

const importFile = async (): Promise<void> => {
  let payload = {
    fileData: fileData.value,
    scenarioId: props.scenarioName,
    versionId: props.versionName,
  };
  if (props.isLocationCSV) {
    await uploadLocationCSV(payload);
  } else {
    await uploadCSVFile(payload);
  }
  endImport();
  checkError(importingScenarioDataError.value, "保存しました");
};

const checkValidVersionName = (value: any): boolean => {
  return !props.versionList.includes(value); // valid === not in the version list
};

const imageDisplay = (src: any): void => {
  imageSource.value = src;
  showImageDisplay.value = true;
};

const checkError = (errorMessage: any, successMessage: any): void => {
  if (errorMessage) {
    $q.notify({
      message: errorMessage,
      type: "error",
    });
  } else {
    $q.notify({
      message: successMessage,
    });
  }
};

// computed
const isFetchingScenarioDetail = computed(() => scenariosStore.isFetchingScenarioDetail);
const importingScenarioDataError = computed(() => scenariosStore.importingScenarioDataError);
const importFinishSuccess = computed(() => scenariosStore.importFinishSuccess);
const activeScenarioData = computed(() => scenariosStore.activeScenarioData);
const activeScenario = computed(() => scenariosStore.activeScenario);
const zipCodes = computed(() => scenariosStore.zipCodes);
const show = computed({
  get(): boolean {
    return props.visible ?? false;
  },
  set(value: boolean): void {
    if (!value) {
      emits("close");
    }
  },
});

const maxWidthDialog = computed((): number => {
  if (props.isLocationCSV && hasZipCodes.value) return 700;
  else if (props.existingTalks.includes("ゴミ分別")) return 700;
  return 1000;
});

const hasZipCodes = computed((): boolean => {
  return zipCodes.value.length > 0;
});

// watch
watch(
  () => scenarioVersion.value, 
  () => {
    if (fileData.value && scenarioVersion.value && checkValidVersionName(scenarioVersion.value)) {
      disableImport.value = false;
    } else {
      disableImport.value = true;
    }
  }
);

watch(
  () => importFinishSuccess.value, 
  (val) => {
    if (val) {
      emits("onImportFinishSuccess");
      fileData.value = undefined;
    }
  }
);

// hooks
onBeforeMount(() => {
  rules.value = {
    isValidVersionName: (value: any) => {
      return checkValidVersionName(value) || "同名のバージョンが存在しています";
    }
  };
});
</script>
