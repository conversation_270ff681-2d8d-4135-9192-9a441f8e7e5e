<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.tab-root {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}
.tab-header {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.tab-header-title {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.tab-header-title-item,
.tab-header-indicator {
  display: flex;
  justify-content: center;
}
.tab-header-indicator-item {
  width: 0.333em;
  opacity: 0.4;
}
.tab-header-indicator-item-active {
  opacity: 1;
}
.tab-header-move-button {
  min-width: 0 !important;
  padding: 0 0.5em !important;
}
</style>

<template>
  <div class="tab-root">
    <div class="tab-header">
      <q-btn class="tab-header-move-button" color="#FFFFFF" :disabled="index === 0" @click="onClickBackButton()">
        <q-icon>mdi-chevron-left</q-icon>
      </q-btn>
      <div class="tab-header-title">
        <div class="tab-header-title-item">{{ title }}</div>
        <div class="tab-header-indicator">
          <span v-for="(tab, i) in tabs" :key="i">
            <q-icon
              :class="
                i === index ? 'tab-header-indicator-item tab-header-indicator-item-active' : 'tab-header-indicator-item'
              "
              >mdi-circle-small</q-icon>
          </span>
        </div>
      </div>
      <q-btn
        class="tab-header-move-button"
        color="#FFFFFF"
        :disabled="index === tabs.length - 1"
        @click="onClickNextButton()"
      >
        <q-icon>mdi-chevron-right</q-icon>
      </q-btn>
    </div>
    <div class="tab-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref,  onBeforeMount, onMounted } from 'vue';



// old imports
// 旧インポート
/**/


// data
const index = ref<number>(-1);
const tabs = ref<any>([]);
const title = ref<string>("");

// methods
const onClickBackButton = (): void => {
      if (index.value !== 0) {
        index.value = index.value - 1;
        select();
      }
    };
const onClickNextButton = (): void => {
      if (index.value < tabs.value.length) {
        index.value = index.value + 1;
        select();
      }
    };
const select = (index: any = -1): void => {
      if (index.value < 0) {
        index.value = index.value;
      }
      tabs.value.forEach((tab, i) => {
        // console.log(tab);
        if (i === index.value) {
          tab.child.active = true;
          title.value = tab.child.title.value;
        } else {
          tab.child.active = false;
        }
      });
    };

// hooks

onBeforeMount(() => {
  tabs.value = $slots.default;
  if (tabs.value.length > 0) {
    index.value = 0;
  }
});


onMounted(() => {
  if (index.value >= 0) {
    select();
  }
});

</script>
