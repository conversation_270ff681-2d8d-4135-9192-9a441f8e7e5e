<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-dialog scrollable v-model="show" max-width="600">
    <q-card class="tw-w-full">
      <q-bar class="bg-primary tw-h-1" dark> </q-bar>
      <q-toolbar flat>
        <q-toolbar-title class="tw-font-semibold">
          シナリオを有効にする
        </q-toolbar-title>
      </q-toolbar>
      <div>
        <div v-if="fetchingTalkDistributionError" class="row tw-mx-2">
          <div class="col-12 tw-mb-4">
            <Alert type="warning" :message="fetchingTalkDistributionError"></Alert>
          </div>
        </div>
        <div class="row tw-mx-6 tw-pb-3">
          <div class="col-12" style="padding-bottom: 0;">
            <span>
              "{{ 
                (
                  activeScenario.versions
                  && activeScenario.versions[newVersion]
                  && activeScenario.versions[newVersion].displayVersionName
                )
                ? activeScenario.versions[newVersion].displayVersionName
                : newVersion 
              }}"
              を{{ getEnvironmentDisplayText(environment) }}環境で有効にします。
            </span>
          </div>
        </div>
        <div v-if="talkDistributionsThatBecomeInactive.length > 0" class="row tw-mx-6 tw-pb-3">
          <div class="col-12" style="display: inline-grid; padding-top: 0; padding-bottom: 0;">
            <span style="padding-bottom: 0.5em;">設定変更後、以下のトーク配信設定が解除されます。</span>
            <span :key="dist.distributionConfigId" v-for="dist in talkDistributionsThatBecomeInactive">{{ dist.deliveryTitle }}</span>
          </div>
        </div>
        <div class="row tw-mx-6">
          <div class="col-12">
            <span>よろしいですか？</span>
          </div>
        </div>
      </div>
      <q-card-actions class="tw-p-4 tw-flex tw-justify-end">
        <q-btn class="tw-px-6 tw-mr-2 text-blue-grey" outline @click="cancelChange">
          キャンセル
        </q-btn>
        <q-btn
          color="primary"
          class="tw-px-6 tw-mr-2"
          :disabled="loadingTalkDistributions"
          :loading="loadingTalkDistributions"
          @click="permissionHelper.hasActionPermission('click', 'backendRequest') ? confirmChange() : permissionHelper.showActionPermissionError()"
        >
          OK
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { TEMPLATE_TALK_IDS, SCENARIO_ENV_TYPES } from "@/stores/modules/scenarios/scenarios.constants";
import { useReminderStore } from "@/stores/modules/reminder";
import { useScenariosStore } from "@/stores/modules/scenarios";
import Alert from "@/components/common/Alert.vue";
import { usePermissionHelper } from "@/mixins/PermissionHelper";

// store
const segmentsStore = useReminderStore();
const scenariosStore = useScenariosStore();

const permissionHelper = usePermissionHelper();

// props emit
const props = defineProps<{
  visible: boolean,
  currentVersion: string,
  newVersion: string,
  environment: string,
  scenarioId: string,
}>();

const emit = defineEmits(['close', 'onChangeConfirm']);

// data
const loadingTalkDistributions = ref(false);
const talkDistributionsThatBecomeInactive = ref<any[]>([]);
const fetchingTalkDistributionError = ref('');

// computed
const talkDeliveryList = computed(() => segmentsStore.talkDeliveryList);
const activeScenario = computed(() => scenariosStore.activeScenario);

const show = computed({
  get(): boolean {
    return props.visible;
  },
  set(value: boolean): void {
    if (!value) {
      resetLocalData();
      emit("close");
    }
  },
});

// watch
watch(
  () => props.visible,
  (newVal) => {
    if(newVal) {
      setupDataForModal();
    }
  }
);

// methods
const getEnvironmentDisplayText = (envName: any): any => {
  return envName in SCENARIO_ENV_TYPES ? SCENARIO_ENV_TYPES[envName].text : "不明";
};

const resetLocalData = (): void => {
  loadingTalkDistributions.value = false;
  talkDistributionsThatBecomeInactive.value = [];
  fetchingTalkDistributionError.value = '';
};

const confirmChange = (): void => {
  show.value = false;
  emit("onChangeConfirm");
};

const cancelChange = (): void => {
  show.value = false;
};

const findValidDistributionsFromTalks = (talkList: any, distributionList: any): Array<any> => {
  const validDistributions:any[] = [];
  for (const dist of distributionList) {
    if (!dist.enabled || dist.environment !== props.environment) {
      continue;
    }
    if (TEMPLATE_TALK_IDS.includes(dist.talkId)) {
      const talk = talkList.find((talk: any) => talk.dataId === dist.talkId);
      if (talk) {
        validDistributions.push(dist);
      }
    } else {
      const talk = talkList.find((talk: any) => 
        talk.params && talk.params.name && talk.params.name === dist.talkName
      );
      if (talk) {
        validDistributions.push(dist);
      }
    }
  }
  return validDistributions;
};

const findDifferencesInValidDistributions = (currentDistributions: any, newDistributions: any): void => {
  for (const dist of currentDistributions) {
    const distInNewDistributions = newDistributions.find((config: any) => config.distributionConfigId === dist.distributionConfigId);
    if (!distInNewDistributions) {
      talkDistributionsThatBecomeInactive.value.push(dist);
    }
  }
};

const setupDataForModal = async (): Promise<void> => {
  loadingTalkDistributions.value = true;
  try {
    if (talkDeliveryList.value.length === 0) {
      await segmentsStore.fetchTalkDeliveryList();
    }

    const currentVersionTalksRequest = await scenariosStore.fetchScenarioSpecificTalks({
      scenarioId: props.scenarioId,
      versionId: props.currentVersion
    });
    if (currentVersionTalksRequest.result === "ERROR") {
      fetchingTalkDistributionError.value = "紐づいたトーク配信取得ながら、エラーが発生しました。管理者と問い合わせください。";
      return;
    }
    const currentVersionTalks = currentVersionTalksRequest.talks;

    const newVersionTalksRequest = await scenariosStore.fetchScenarioSpecificTalks({
      scenarioId: props.scenarioId,
      versionId: props.newVersion
    });
    if (newVersionTalksRequest.result === "ERROR") {
      fetchingTalkDistributionError.value = "紐づいたトーク配信取得ながら、エラーが発生しました。管理者と問い合わせください。";
      return;
    }
    const newVersionTalks = newVersionTalksRequest.talks;

    const validCurrentTalkDistributions = findValidDistributionsFromTalks(currentVersionTalks, talkDeliveryList.value);
    const validNewTalkDistributions = findValidDistributionsFromTalks(newVersionTalks, talkDeliveryList.value);
    findDifferencesInValidDistributions(validCurrentTalkDistributions, validNewTalkDistributions);
  } catch (err: any) {
    console.error(err);
    fetchingTalkDistributionError.value = "紐づいたトーク配信取得ながら、エラーが発生しました。管理者と問い合わせください。";
  } finally {
    loadingTalkDistributions.value = false;
  }
};
</script>
