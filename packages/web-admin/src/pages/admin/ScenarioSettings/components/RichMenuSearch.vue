<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-separator class="tw-mb-4" />
    <div class="row">
      <q-form @submit.prevent style="width: 100%">
        <div class="row">
          <div class="col-6 tw-my-2">
            <div class="body-2 text-blue-grey tw-font-semibold tw-mx-2">
              メニュー名
            </div>
            <q-input
              class="tw-mx-2"
              dense
              outlined
              hide-details="auto"
              clearable
              v-model="richMenuName"
            />
          </div>
          <div class="col-3 tw-my-2">
            <div class="body-2 text-blue-grey tw-font-semibold tw-mx-2">
              通常モード
            </div>
            <q-select
              class="tw-mx-2"
              dense
              outlined
              hide-details="auto"
              :options="statuses"
              option-label="text"
              option-value="value"
              v-model="normalMode"
            />
          </div>
          <div class="col-3 tw-my-2">
            <div class="body-2 text-blue-grey tw-font-semibold tw-mx-2">
              災害時モード
            </div>
            <q-select
              class="tw-mx-2"
              dense
              outlined
              hide-details="auto"
              :options="statuses"
              option-label="text"
              option-value="value"
              v-model="bosaiMode"
            />
          </div>
        </div>
        <div class="tw-flex tw-justify-end tw-py-4 tw-mr-2">
          <q-btn
              outline
              color="warning"
              @click="clearSearchCriteria()"
          >
            検索条件をクリア
          </q-btn>
          <q-btn
              class="tw-ml-4"
              color="primary"
              type="submit"
              :disabled="isFetchingAllRichMenus"
              @click="onClickSearch()"
          >
            この条件で検索
        </q-btn>
        </div>
      </q-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useScenariosStore } from '@/stores/modules/scenarios';
import { computed, ref } from 'vue';

// store
const scenariosStore = useScenariosStore();

// const
const emptySelectValue = '－－－－－';

// emit
const emit = defineEmits(['searchCriteriaUpdated']);

// data
const statuses = ref([
  emptySelectValue,
  '適用中',
  '未適用',
]);

const richMenuName = ref('');
const normalMode = ref(emptySelectValue);
const bosaiMode = ref(emptySelectValue);
const searchCriteria = ref({});

// computed
const isFetchingAllRichMenus = computed(() => scenariosStore.isFetchingAllRichMenus);

// methods
const clearSearchCriteria = () => {
  richMenuName.value = '';
  normalMode.value = emptySelectValue;
  bosaiMode.value = emptySelectValue;
  onClickSearch();
};

const onClickSearch = () => {
  searchCriteria.value = {
    richMenuName: richMenuName.value,
    normalMode: normalMode.value,
    bosaiMode: bosaiMode.value,
  }
  emit("searchCriteriaUpdated", searchCriteria.value);
};
</script>