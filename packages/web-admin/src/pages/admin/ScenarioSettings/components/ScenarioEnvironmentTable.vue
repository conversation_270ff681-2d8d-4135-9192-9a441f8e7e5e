<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <div class="row tw-flex tw-justify-between">
      <div class="col-auto" md="auto">
        <div class="active-bot text-blue-grey">シナリオ名</div>
        <h2 class="active-bot-name" v-if="activeScenarioData && activeScenario && !!activeScenario.scenarioId">
          {{ activeScenario.scenarioId }}
        </h2>
        <h2 class="active-bot-name text-blue-grey" v-else>
          －
        </h2>
      </div>
      <div class="col-auto">
        <q-btn
          color="negative"
          outline
          :disable="permissionHelper.hasActionPermission('disableButton', 'ScenarioSettings_DeleteVersion_Click') || disableDeleteButton"
          @click="onDeleteClick"
        >
          <q-icon left name="mdi-trash-can"></q-icon>選択項目を削除
          <q-tooltip v-if="tableSelected.length > 0 && disableDeleteButton" bottom>
            <span>
              適用中のシナリオを選択している場合は削除できません
            </span>
          </q-tooltip>
        </q-btn>
        <q-btn
          class="tw-ml-2"
          color="primary"
          @click="onCreateClick"
          :disable="permissionHelper.hasActionPermission('disableButton', 'ScenarioSettings_CreateVersion_Click')"
        >
          <q-icon left name="mdi-plus"></q-icon>新規シナリオ作成
        </q-btn>
      </div>
    </div>
    <q-separator class="tw-my-4"></q-separator>
    <q-table
      class="scenario-table tw-mb-4"
      :columns="tableHeaders"
      :rows="filteredTableItems"
      :show-select="true"
      :single-select="false"
      v-model:selected="tableSelected"
      flat
      selection="multiple"
      row-key="_version"
      @selection="selectTableRow"
      :pagination="pagination"
      :rows-per-page-options="[5, 10, 15, 0]"
      :pagination-label="(start, end, total) => `${ start }-${ end } 件目 /  ${ total }件`"
      rows-per-page-label="1ページあたりの行数"
    >
      <template v-slot:body-cell-_productionButton="item">
        <td>
          <q-btn
              color="secondary"
              :disable="permissionHelper.hasActionPermission('disableButton', 'ScenarioSettings_EnableVersion_Click') || item.row._version === activeScenarioData.envMapping.production"
              @click="onActivate(item.row, 'production')"
          >
            <div v-if="item.row._version === activeScenarioData.envMapping.production">
              適用中
            </div>
            <div v-else>
              適用
            </div>
          </q-btn>
        </td>
      </template>
      <template v-slot:body-cell-_sandboxButton="item">
        <td>
          <q-btn
              color="secondary"
              :disabled="permissionHelper.hasActionPermission('disableButton', 'ScenarioSettings_EnableVersion_Click') || item.row._version === activeScenarioData.envMapping.sandbox"
              @click="onActivate(item.row, 'sandbox')"
          >
            <div v-if="item.row._version === activeScenarioData.envMapping.sandbox">
              適用中
            </div>
            <div v-else>
              適用
            </div>
          </q-btn>
        </td>
      </template>
      <template v-slot:body-cell-_editButton="item">
        <td>
          <q-btn color="primary" @click="toEdit(item.row)">
            編集
          </q-btn>
        </td>
      </template>
    </q-table>
    <q-btn
        class="tw-mr-2"
        color="primary"
        outline
        @click="onExportClick"
        :loading="showLoadingExport"
        :disable="permissionHelper.hasActionPermission('disableButton', 'ScenarioSettings_ExportVersion_Click') || tableSelected.length !== 1"
    >
      LBDエクスポート
    </q-btn>
    <q-btn
        color="primary"
        outline
        @click="onImportClick"
        :disable="permissionHelper.hasActionPermission('disableButton', 'ScenarioSettings_ImportVersion_Click') || tableSelected.length !== 0"
    >
      LBDインポート
    </q-btn>
    <VersionChangeModal
        :visible="showVersionChange"
        :scenarioId="activeScenario.scenarioId"
        :currentVersion="currentVersion"
        :newVersion="selectedVersion"
        :environment="selectedEnv"
        @onChangeConfirm="clickChange"
        @close="showVersionChange = false"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { useDatetimeFormatter } from "@/mixins/DatetimeFormatter";
import { SCENARIO_ENV_TYPES } from "@/stores/modules/scenarios/scenarios.constants";
import dayjs from 'dayjs';
import { get } from 'lodash';
import { useScenariosStore } from "@/stores/modules/scenarios";
import { useQuasar } from "quasar";
import { usePermissionHelper } from "@/mixins/PermissionHelper";
import { useRouter } from "vue-router";

// component
import VersionChangeModal from "@/pages/admin/ScenarioSettings/components/VersionChangeModal.vue";

// quasar
const $q = useQuasar();

// router
const router = useRouter();

// store
const scenariosStore = useScenariosStore();

// other
const datetimeFormatter = useDatetimeFormatter();
const permissionHelper = usePermissionHelper();

// props emit
const props = defineProps<{
  environment: string,
  searchCriteria: any,
}>();

const emit = defineEmits(['onImportTrigger', 'onCreateClick', 'onDeleteTrigger', 'onExportFinishSuccess']);

// data
const versionList = ref([]);
const environmentInfo = ref<any>({});
const scenarioInfo = ref(null);
const selectedVersion = ref();
const selectedEnv = ref();
const activeScenarioExists = ref<any>(null);
const showLoadingExport = ref(false);
const showVersionChange = ref(false);
const sortBy = ref('_updateDate');
const sortDesc = ref(true);
const tableHeaders = ref([
  {
    name: "_displayVersion",
    label: "シナリオバージョン",
    field: "_displayVersion",
    sortable: true,
    width: "40%",
    align: 'left' as "left" | "right" | "center" | undefined,
    classes: '',
    headerClasses: '',
  },
  {
    name: "_productionButton",
    label: "本番",
    field: "_productionButton",
    sortable: true,
    align: "left" as "left" | "right" | "center" | undefined,
    classes: '',
    headerClasses: '',
  },
  {
    name: "_sandboxButton",
    label: "サンドボックス",
    field: "_sandboxButton",
    sortable: true,
    align: "left" as "left" | "right" | "center" | undefined,
    classes: '',
    headerClasses: '',
  },
  {
    name: "_editButton",
    label: '',
    field: "_editButton",
    sortable: false,
    align: undefined as "left" | "right" | "center" | undefined,
    classes: '',
    headerClasses: '',
  },
  { 
    name: '_updateDate', 
    label: 'Hidden Value', 
    field: row => row._updateDate, 
    align: undefined as "left" | "right" | "center" | undefined, 
    sortable: true, 
    classes: "tw-hidden",
    headerClasses: 'tw-hidden',
  },
]);
const pagination = ref({
  rowsPerPage: 10,
  sortBy: '_updateDate', // 初期ソート対象のカラム名
  descending: true, // 初期ソート順 (false = 昇順, true = 降順)
});
const tableItems = ref<any[]>([]);
const tableItemsPerPage = ref(10);
const filteredTableItems = ref<any[]>([]);
const tableSelected = ref<any[]>([]);
const searchCriteriaLocal = ref<any>(null);

// computed
const activeScenario = computed(() => scenariosStore.activeScenario);
const activeScenarioData = computed(() => scenariosStore.activeScenarioData);
const scenariosList = computed(() => scenariosStore.scenariosList);
const isExportingScenarioData = computed(() => scenariosStore.isExportingScenarioData);
const selectedEditScenario = computed(() => scenariosStore.selectedEditScenario);
const exportingScenarioDataError = computed(() => scenariosStore.exportingScenarioDataError);
const fetchScenarioDetailError = computed(() => scenariosStore.fetchScenarioDetailError);
const deleteFinishSuccess = computed(() => scenariosStore.deleteFinishSuccess);

const disableDeleteButton = computed((): any => {
  if (tableSelected.value.length === 0) {
    return true;
  }

  const selectedVersions = tableSelected.value.map((selected) => selected._version);
  if (selectedVersions.includes(activeScenarioData.value.envMapping.production)) {
    return true;
  }
  return selectedVersions.includes(activeScenarioData.value.envMapping.sandbox);
});

const currentVersion = computed(() => {
  return get(activeScenarioData.value, `envMapping.${selectedEnv.value}`, '');
});

// watch
watch(
  () => isExportingScenarioData.value,
  (newVal) => {
    if (!newVal) {
      showLoadingExport.value = false;
    }
  }
);

watch(
  () => scenariosList.value,
  (newVal) => {
    scenarioInfo.value = getScenarioInfo();
    if (newVal[0] != null && !Object.keys(newVal[0].versions).includes(selectedVersion.value ?? '')) {
      selectedVersion.value = getDefaultSelectedVersion();
    } else if (newVal[0] == null) {
      selectedVersion.value = null;
    }
  }
);

watch(
  () => activeScenarioData.value,
  () => {
    activeScenarioExists.value = checkActiveScenario();
    tableItems.value = populateTableItems();
    filterTableItems();
  }
);

watch(
  () => selectedVersion.value,
  (newVal) => {
    const payload = {
      environment: props.environment,
      value: newVal,
    };
    scenariosStore.setSelectedEditScenario(payload);
  }
);

watch(
  () => selectedEditScenario.value,
  (newVal) => {
    selectedVersion.value = newVal[props.environment];
  },
  { deep: true }
);

watch(
  () => exportingScenarioDataError.value,
  (newVal) => {
    if (newVal) {
      if (newVal instanceof String) {
        $q.notify({ message: newVal.toString(), type: "error" });
      } else {
        $q.notify({ message: newVal.message, type: "error" });
      }
    }
  }
);

watch(
  () => fetchScenarioDetailError.value,
  (newVal) => {
    if (newVal) {
      if (newVal instanceof String) {
        $q.notify({ message: newVal.toString(), type: "error" });
      } else {
        $q.notify({ message: newVal.message, type: "error" });
      }
    }
  }
);

watch(
  () => props.searchCriteria,
  (newVal) => {
    searchCriteriaLocal.value = newVal;
    filterTableItems();
  }
);

watch(
  () => deleteFinishSuccess.value,
  (newVal) => {
    if (newVal) {
      tableSelected.value = [];
    }
  }
);

// mounted
onMounted(() => {
  activeScenarioExists.value = checkActiveScenario;
  searchCriteriaLocal.value = props.searchCriteria;
  environmentInfo.value = SCENARIO_ENV_TYPES[props.environment];
  if (activeScenarioData.value && activeScenarioData.value.envMapping) {
    scenarioInfo.value = getScenarioInfo();
  } else {
    scenarioInfo.value = null;
  }
  tableItems.value = populateTableItems();
  filterTableItems();
  if (selectedVersion.value == null) {
    if (selectedEditScenario.value[props.environment] != null) {
      selectedVersion.value = selectedEditScenario.value[props.environment];
    } else {
      selectedVersion.value = getDefaultSelectedVersion();
    }
  }
});

// methods
const getScenarioInfo = (): any => {
  const value =
    "envMapping" in activeScenarioData.value ? activeScenarioData.value.envMapping[Number(props.environment)] : null;
  if (value) {
    if (activeScenario.value && activeScenario.value.versions) {
      return activeScenario.value.versions[value];
    } else {
      return null;
    }
  } else {
    return null;
  }
};

const getDefaultSelectedVersion = (): any => {
  if (activeScenarioData.value && "envMapping" in activeScenarioData.value) {
    return activeScenarioData.value.envMapping[Number(props.environment)];
  } else {
    return null;
  }
};

const populateTableItems = (): Array<any> => {
  if (activeScenario.value && activeScenario.value.versions) {
    const items = Object.keys(activeScenario.value.versions).map((version) => {
      const item = activeScenario.value.versions[version];
      return {
        ...item,
        _version: version,
        _displayVersion: ((item.displayVersionName ? item.displayVersionName : version) || "").trim(),
        _productionButton: version === activeScenarioData.value.envMapping.production ? 1 : 0,
        _sandboxButton: version === activeScenarioData.value.envMapping.sandbox ? 1 : 0,
        _updateDate: datetimeFormatter.formatToPrettyYYYYMMDHHmmss(item.updateDate),
        _updateUnix: datetimeFormatter.formatYYYYMMDDToUnix(item.updateDate),
        _editButton: null,
      };
    });
    return items;
  } else {
    return [];
  }
};

const customSort = (items: any, index: any, isDesc: any): any => {
  items.sort((a: any, b: any) => {
    if (index[0] === "_productionButton" || index[0] === "_sandboxButton") {
      if (!isDesc[0]) {
        return b[index] - a[index];
      } else {
        return a[index] - b[index];
      }
    } else {
      if (!isDesc[0]) {
        return a[index] < b[index] ? -1 : 1;
      } else {
        return b[index] < a[index] ? -1 : 1;
      }
    }
  });
  return items;
};

const checkActiveScenario = (): any => {
  if (activeScenarioData.value) {
    return activeScenarioData.value.activeScenarioId;
  }
  return false;
};

const onImportClick = (): void => {
  emit("onImportTrigger");
};

const onCreateClick = (): void => {
  emit("onCreateClick");
};

const onDeleteClick = (): void => {
  if (!activeScenarioData.value || !activeScenarioData.value.envMapping) {
    return;
  }

  emit("onDeleteTrigger",
      tableSelected.value
          .map((selected) => selected._version));
};

const onActivate = (item: any, env: any): void => {
  selectedVersion.value = item._version;
  selectedEnv.value = env;
  if (permissionHelper.hasActionPermission('click', 'backendRequest')) {
    showVersionChange.value = true;
  } else {
    permissionHelper.showActionPermissionError();
  }
};

const clickChange = (): void => {
  const payload = {
    activeScenarioData: activeScenarioData.value,
    scenarioName: activeScenarioData.value.activeScenarioId,
    versionName: selectedVersion.value,
    environment: selectedEnv.value,
  };
  scenariosStore.changeActiveScenario(payload);
};

const onExportClick = async (): Promise<void> => {
  showLoadingExport.value = true;
  const payload = {
    scenario: activeScenarioData.value.activeScenarioId + "#" + tableSelected.value[0]._version,
    environment: environmentInfo.value.value,
  };
  await scenariosStore.downloadExportFile(payload);
  emit("onExportFinishSuccess");
};

const toEdit = (item: any): void => {
  router.push({
    name: "ScenarioVersionSettingsPage",
    params: {
      env: props.environment,
      scenarioId: activeScenario.value ? activeScenario.value.scenarioId : "",
      versionId: item._version,
    },
  });
};

const filterTableItems = (): void => {
  const {
    scenarioVersion,
    productionStatus,
    sandboxStatus,
    startDate,
    endDate,
  } = searchCriteriaLocal.value || {};

  let results = [...tableItems.value];
  if (scenarioVersion && scenarioVersion !== '') {
    results = results.filter((result) => result._displayVersion.includes(scenarioVersion));
  }
  switch (productionStatus) {
    case '適用中':
      results = results.filter((result) => result._productionButton === 1);
      break;
    case '未適用':
      results = results.filter((result) => result._productionButton === 0);
      break;
  }
  switch (sandboxStatus) {
    case '適用中':
      results = results.filter((result) => result._sandboxButton === 1);
      break;
    case '未適用':
      results = results.filter((result) => result._sandboxButton === 0);
      break;
  }
  if (startDate && startDate !== '') {
    const unix = dayjs(startDate).hour(0).minute(0).second(0).unix();
    results = results.filter((result) => result._updateUnix >= unix);
  }
  if (endDate && endDate !== '') {
    const unix = dayjs(endDate).hour(23).minute(59).second(59).unix();
    results = results.filter((result) => result._updateUnix <= unix);
  }

  filteredTableItems.value = results;
};

const selectTableRow = (details: any) => {
  if (tableSelected.value.length === 0) {
    tableSelected.value = details.rows;
  }
  else if (details.rows.length !== 1) {
    if (tableSelected.value.length !== details.rows.length) {
      tableSelected.value = details.rows;
    }
    else {
      tableSelected.value = [];
    }
  }
  else {
    let isfilter = false;
    tableSelected.value = tableSelected.value.filter((item: any) => {
      if (item._version === details.rows[0]._version) {
        isfilter = true;
      }
      return item._version !== details.rows[0]._version;
    });

    if (!isfilter) {
      tableSelected.value.push(details.rows[0]);
    }
  }
};
</script>

<style scoped>
.active-bot {
  font-size: 12px;
}
.active-bot-name {
  font-size: 20px;
  font-weight: bold;
}
</style>

<style>
.scenario-table.v-data-table tbody tr {
  height: 64px;
}
.scenario-table.v-data-table tbody tr:hover {
  cursor: initial !important;
}
</style>
