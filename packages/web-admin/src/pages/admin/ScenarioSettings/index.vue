<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div :class="`tw-pt-3`">
    <SubAppBar :class="`${isMenuVertical ? 'tw-pt-10' : ''}`">
      <div style="display: flex; justify-content: space-between">
        <div>
          <q-btn-toggle
            v-model="tab"
            :options="toggleMenuOption"
            class="bg-grey-2"
            outline
            color="grey"
            toggle-color="primary"
            toggle-text-color="primary"
          >
          </q-btn-toggle>
        </div>
        <div v-if="tab !== 'spot'">
          <q-btn color="primary" @click="toggleShowSearch()">
            検索条件
            <q-icon right v-if="showSearch" name="mdi-chevron-up"> </q-icon>
            <q-icon right v-else name="mdi-chevron-down"> </q-icon>
          </q-btn>
        </div>
      </div>
      <div v-show="showSearch">
        <br />
        <div v-if="tab === 'scenario-setting'">
          <ScenarioSearch @searchCriteriaUpdated="updateSearchCriteria" />
        </div>
        <div v-else-if="tab === 'rich-menu-list'">
          <RichMenuSearch @searchCriteriaUpdated="updateSearchCriteria" />
        </div>
      </div>
    </SubAppBar>
    <q-card outlined class="tw-my-4">
      <q-tab-panels v-model="tab">
        <q-tab-panel name="scenario-setting">
          <div fluid>
            <ScenarioSettings :searchCriteria="searchCriteria" />
          </div>
        </q-tab-panel>
        <q-tab-panel name="rich-menu-list">
          <div fluid>
            <RichMenuList :searchCriteria="searchCriteria" />
          </div>
        </q-tab-panel>
        <q-tab-panel name="spot">
          <div fluid>
            <ScenerioSpot :searchCriteria="searchCriteria" />
          </div>
        </q-tab-panel>
      </q-tab-panels>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";

// components
import ScenarioSettings from './fragments/ScenarioSettings.vue';
import ScenarioSearch from '@/pages/admin/ScenarioSettings/components/ScenarioSearch.vue';
import RichMenuList from './fragments/RichMenuList.vue';
import RichMenuSearch from '@/pages/admin/ScenarioSettings/components/RichMenuSearch.vue';
import SubAppBar from '@/components/common/SubAppBar.vue';
import ScenerioSpot from '@/pages/admin/ScenarioSpot/SpotGroupList.vue';
import { useRoute, useRouter } from "vue-router";

// stores
import { useSettingsStore } from "@/stores/modules/settings";
const settingsStore = useSettingsStore();


const isMenuVertical = ref(settingsStore.commonSettings.menuStyle === "vertical");

// data
const showSearch = ref(false);
const searchCriteria = ref();
const route = useRoute();
const router = useRouter();
const tab = ref(route.query.Tab || 'scenario-setting');

const toggleMenuOption = computed(() => {
  return (import.meta.env.VITE_IS_USING_SPOT && ["true", "1"].includes(import.meta.env.VITE_IS_USING_SPOT.toString()))
    ? [
        { label: "シナリオ設定", value: "scenario-setting", icon: "mdi-application-cog" },
        { label: "リッチメニュー", value: "rich-menu-list", icon: "mdi-message-bulleted" },
        { label: "スポット", value: "spot", icon: "mdi-map-marker-multiple" },
      ]
    : [
        { label: "シナリオ設定", value: "scenario-setting", icon: "mdi-application-cog" },
        { label: "リッチメニュー", value: "rich-menu-list", icon: "mdi-message-bulleted" },
      ];
});

// methods
const toggleShowSearch = (show: any = null): void => {
  if (show != null) {
    showSearch.value = show;
  } else {
    showSearch.value = !showSearch.value;
  }
};

const updateSearchCriteria = (value: any): void => {
  searchCriteria.value = value;
};
watch(tab, (newTab) => {
  router.replace({ query: {} }); 
});
</script>
