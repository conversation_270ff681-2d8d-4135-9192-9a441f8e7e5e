<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div fluid v-if="!(isFetchingScenarios || isFetchingScenarioDetail)">
    <div v-if="!fetchScenariosError">
      <ScenarioEnvironmentTable
        environment="sandbox"
        :searchCriteria="searchCriteriaLocal"
        @onImportTrigger="onImportTrigger"
        @onDeleteTrigger="onDeleteTrigger"
        @onCreateClick="onCreateClick"
        @onShowVisualization="onShowVisualization('sandbox')"/>
    </div>
    <div class="tw-flex tw-justify-center tw-pb4" v-if="fetchScenariosError">
      <ContentLoadError class="mt-10" :error="fetchScenariosError" />
    </div>
    <VisualizationModal
      :visible="showVisualizationModal"
      @close="showVisualizationModal = false"
      :selectedTalk="null"
    />
    <VersionImportModal
      :visible="showImportModal"
      :versionList="versionList"
      @close="showImportModal = false"
      @onImportFinishSuccess="onImportFinishSuccess"
    />
    <VersionCreateModal
      :visible="showCreateModal"
      @close="showCreateModal = false"
      @onCreateFinishSuccess="onCreateFinishSuccess"
    />
    <VersionDeleteModal
      :visible="showDeleteModal"
      :scenario="checkmarkedScenario"
      :active="activeScenarioData"
      @close="showDeleteModal = false"
      @on-delete-finish-success="onDeleteFinishSuccess"
    />
  </div>
  <div v-else class="tw-flex tw-justify-center">
    <q-spinner color="primary" :size="50"></q-spinner>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, onMounted, ref, watch } from "vue";
import { useScenariosStore } from "@/stores/modules/scenarios";
import { useQuasar } from "quasar";

// components
import ScenarioEnvironmentTable from "@/pages/admin/ScenarioSettings/components/ScenarioEnvironmentTable.vue";
import VersionImportModal from "@/pages/admin/ScenarioSettings/components/VersionImportModal.vue";
import VersionCreateModal from "@/pages/admin/ScenarioSettings/components/VersionCreateModal.vue";
import VersionDeleteModal from "@/pages/admin/ScenarioSettings/components/VersionDeleteModal.vue";
import ContentLoadError from "@/components/common/ContentLoadError.vue";
import VisualizationModal from "@/pages/admin/ScenarioSettingsDetail/components/VisualizationModal.vue";

// quasar
const $q = useQuasar();

// store
const scenariosStore = useScenariosStore();

// props
const props = defineProps<{
  searchCriteria: any,
}>();

// data
const showScenarioVersion = ref(false);
const showImportModal = ref(false);
const showCreateModal = ref(false);
const showDeleteModal = ref(false);
const checkmarkedScenario = ref({
  scenarioName: "default_scenario",
  versionNames: ["default_version"],
  lengthOfScenarioVersions: 0,
});
const showVisualizationModal = ref(false);
const resetSelection = ref(true);
const searchCriteriaLocal = ref(null);

// computed
const scenariosList = computed(() => scenariosStore.scenariosList);
const isFetchingScenarios = computed(() => scenariosStore.isFetchingScenarios);
const activeScenarioData = computed(() => scenariosStore.activeScenarioData);
const activeScenario = computed(() => scenariosStore.activeScenario);
const fetchScenariosError = computed(() => scenariosStore.fetchScenariosError);
const importingScenarioDataWarning = computed(() => scenariosStore.importingScenarioDataWarning);
const isFetchingScenarioDetail = computed(() => scenariosStore.isFetchingScenarioDetail);

const versionList = computed((): any => {
  if (!activeScenario.value || !activeScenario.value.versions) {
    return [];
  }

  const verList = Object.keys(activeScenario.value.versions).reduce((array: any, version: any) => {
    array.push(activeScenario.value.versions[version].displayVersionName || version);
    return array;
  }, []);

  const compare = (a: any, b: any) => {
    if (a < b) return -1;
    if (a > b) return 1;
    return 0;
  };
  return verList.sort(compare);
});

// watch
watch(
  () => importingScenarioDataWarning.value,
  (newVal) => {
    if (newVal) {
      if (newVal instanceof String) {
        $q.notify({ message: newVal.toString(), type: "error" });
      } else {
        $q.notify({ message: newVal.message, type: "error" });
      }
    }
  }
);

watch(
  () => fetchScenariosError.value,
  (newVal) => {
    if (newVal) {
      if (newVal instanceof String) {
        $q.notify({ message: newVal.toString(), type: "error" });
      } else {
        $q.notify({ message: newVal.message, type: "error" });
      }
    }
  }
);

watch(
  () => props.searchCriteria,
  (newVal) => {
    searchCriteriaLocal.value = newVal;
  }
);

// mounted
onBeforeMount(async () => {
  searchCriteriaLocal.value = props.searchCriteria;

  if (!scenariosList.value || (scenariosList.value && scenariosList.value.length === 0)) {
    await scenariosStore.fetchAllScenarios();
  }
});

// methods
const onImportTrigger = (): void => {
  showImportModal.value = true;
  showScenarioVersion.value = false;
};

const onImportFinishSuccess = async (): Promise<void> => {
  showImportModal.value = false;
  scenariosStore.setImportFinishSuccess(false);
  await scenariosStore.fetchAllScenarios();
};

const onCreateFinishSuccess = async (): Promise<void> => {
  showCreateModal.value = false;
  scenariosStore.setCreateFinishSuccess(false);
  await scenariosStore.fetchAllScenarios();
};

const onCreateClick = (): void => {
  resetSelection.value = true;
  showCreateModal.value = true;
};

const onDeleteTrigger = (val: any): void => {
  checkmarkedScenario.value = {
    ...checkmarkedScenario.value,
    scenarioName: activeScenario.value?.scenarioId,
    versionNames: val,
    lengthOfScenarioVersions: versionList.value.length,
  };
  showDeleteModal.value = true;
  showScenarioVersion.value = false;
};

const onDeleteFinishSuccess = async (): Promise<void> => {
  showDeleteModal.value = false;
  showScenarioVersion.value = false;
  scenariosStore.setDeleteFinishSuccess(false);
  await scenariosStore.fetchAllScenarios();
};

const onShowVisualization = async (env: any): Promise<void> => {
  await scenariosStore.fetchScenarioDetailTalk({
    scenarioId: activeScenarioData.value.activeScenarioId,
    versionId: activeScenarioData.value.envMapping[env],
  });
  showVisualizationModal.value = true;
};
</script>


