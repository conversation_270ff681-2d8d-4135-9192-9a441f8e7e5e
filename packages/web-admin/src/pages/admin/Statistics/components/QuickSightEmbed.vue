<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div v-once ref="dashboardContainer"></div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useAuthStore } from '@/stores/modules/auth.module';
import { useStatisticsStore } from '@/stores/modules/statistics';
// import { embedDashboard } from "amazon-quicksight-embedding-sdk";

// old imports
// 旧インポート
/*import { FETCH_BUTTON_EMBED_URL } from "@/store/action-types";
*/

// store
const authStore = useAuthStore();
const statisticsStore = useStatisticsStore();

// data
const dashboardContainer = ref();

// methods
const getButtonEmbedUrl = statisticsStore.fetchButtonEmbedUrl;
const embedButtonDashboard = (buttonId: any, containerId: any, userSub: any, userEmail: any) => {
  getButtonEmbedUrl({
    buttonId: buttonId,
    userSub: userSub,
    userEmail: userEmail,
  }).then(function (embedUrl) {
    var containerDiv = dashboardContainer.value;
    var options = {
      url: embedUrl,
      container: containerDiv,
      parameters: {
        country: "Japan",
      },
      scrolling: "no",
      height: "700px",
      width: "1000px",
    };
    containerDiv.innerHTML = "";
    // TODO
    // embedDashboard(options);
  });
};

// computed
const userStore = computed(() => authStore.user);
const buttonEmbedUrl = computed(() => statisticsStore.buttonEmbedUrl);

// watch
watch(
  () => userStore.value, 
  (val) => {
    let tempButtonId = "demoDashboard";
    let tempDashboardContainerId = "dashboardContainer";
    userStore.value && embedButtonDashboard(tempButtonId, tempDashboardContainerId, val.sub, val.email);
  }
);
</script>