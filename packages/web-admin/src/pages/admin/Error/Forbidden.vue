<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-page-container class="fill-height" fluid>
    <div class="row tw-flex tw-justify-center">
      <div class="col-12 col-sm-8 col-md-6">
        <div class="display-4 text-center">403</div>
        <div class="display-2 tw-font-thin text-center">
          <q-icon class="display-2 tw-mb-2" name="mdi-lock-alert"></q-icon>
          Forbidden
        </div>
        <div class="tw-font-thin text-center">申し訳ございません！このページにはアクセス権限がありません。</div>
        <div class="text-center tw-mt-2">
          <q-btn class="tw-m-2" dark @click="handleSignOut"> サインインページに戻る </q-btn>
          <q-btn class="tw-m-2" tile color="primary" @click="goTop"> トップページに戻る </q-btn>
        </div>
      </div>
    </div>
  </q-page-container>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/stores/modules/auth.module';
import { useRouter } from 'vue-router';
import ability from "@/services/ability";

const router = useRouter();

const authStore = useAuthStore();

// methods
// const signOut = authStore.authSignout;
const handleSignOut = async (): Promise<void> => {
  await authStore.AUTH_SIGNOUT();
  router.push({name: 'Signin'});
};

const goTop = (): void => {
  let pages = ability.rules.filter((rule: any) => rule.action === "access");
  if (pages && pages.length > 0) {
    // 元のコード　不具合になったらすみません - honda
    // router.push({ name: pages[0].subject });
    router.push({ name: "ApplicantsPage" });
  }
};
</script>
