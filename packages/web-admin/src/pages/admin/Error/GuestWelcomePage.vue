<template>
  <q-card class="mx-auto mt-4" max-width="400">
    <q-card-section class="text--primary">
      <div class="text-center title mb-4">ゲストモードでブラウジング中</div>
      <div class="text-center subtitle mb-4">
        <!-- このブラウザで開いたページはブラウザの履歴に記録されません。また、開いているゲストブラウザをすべて閉じると、Cookie などのデータはパソコンから消去されます。ただし、ダウンロードしたファイルは保持されます。-->
        ゲストユーザは「作成」や「削除」等のアクション権限がありません。ご注意ください。
      </div>
    </q-card-section>
    <q-card-actions>
      <q-btn class="mx-auto my-auto" tile color="primary" elevation="4" @click="backToAdmin">
        <q-icon left name="mdi-desktop-mac-dashboard"></q-icon>
        トップページへ
      </q-btn>
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { defineEmits } from "vue";

const emit = defineEmits(['backToAdmin']);

// methods
const backToAdmin = (): void => {
  emit("backToAdmin");
};
</script>
