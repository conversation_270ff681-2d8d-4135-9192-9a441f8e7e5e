<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div class="flex q-mt-md">
    <q-tabs class="flex justify-start z-index-3" active-color="primary" v-model="tab" v-if="memberTabDisplay">
      <q-tab name="form-list" toggle-value="form-list" class="q-px-md">
        各種帳票
      </q-tab>
      <q-tab name="member-form-list" toggle-value="member-form-list" class="q-px-md">
        会員情報登録
      </q-tab>
    </q-tabs>
    <div v-if="memberTabDisplay" class="q-mt-md full-width">
      <q-tab-panels v-model="tab" animated class="shadow-2">
        <q-tab-panel name="form-list" class="q-pa-none">
          <div>
            <FormListFragment :formConfigsProps="formConfigs" :loading="isFetching" :error="fetchFormConfigsError" />
          </div>
        </q-tab-panel>
        <q-tab-panel name="member-form-list">
          <div>
            <MemberListFragment :memberConfigs="memberConfigs" :loading="isFetchingMemberConfigs"
              :error="fetchMemberConfigsError" />
          </div>
        </q-tab-panel>
      </q-tab-panels>
    </div>
    <div class="q-mt-md full-width" v-else>
      <FormListFragment :formConfigsProps="formConfigs" :loading="isFetching" :error="fetchFormConfigsError" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useFormsStore } from "@stores/modules/forms/index";
import { useMemberStore } from "@stores/modules/member/index";
import FormListFragment from "./fragments/FormListFragment.vue";
import MemberListFragment from "./fragments/MemberListFragment.vue";
import { useQuasar } from "quasar";
import { useRoute } from "vue-router";

const $q = useQuasar();
const route = useRoute();

const formsStore = useFormsStore();
const memberStore = useMemberStore();

const formConfigs = ref([]);
const isFetching = ref(false);
const fetchFormConfigsError = ref();
const memberConfigs = ref([]);
const isFetchingMemberConfigs = ref(false);
const fetchMemberConfigsError = ref();
const tab = ref("form-list");

const memberTabDisplay = import.meta.env.VITE_MEMBER_TAB === "1";
//const memberTabDisplay = true;
//console.log("memberTabDisplay", memberTabDisplay);

const fetchFormConfigs = async () => {
  isFetching.value = true;
  try {
    //await formsStore.FETCH_FORM_CONFIGS();
    formConfigs.value = [];
  } catch (error: any) {
    fetchFormConfigsError.value = error.message;
  } finally {
    isFetching.value = false;
  }
};

const fetchMemberConfigs = async () => {
  isFetchingMemberConfigs.value = true;
  try {
    await memberStore.fetchAllMemberFormConfigs();
  } catch (error: any) {
    fetchMemberConfigsError.value = error.message;
  } finally {
    isFetchingMemberConfigs.value = false;
  }
};

onMounted(async () => {
  //console.log("mount()");

  // On load load tab based on route param
  if(route.params.tab) {
    tab.value = route.params.tab as string;
  }

  if (formConfigs.value.length === 0) {
    await fetchFormConfigs();
  }

  if (memberConfigs.value.length === 0) {
    await fetchMemberConfigs();
  }
  //console.log("finish mount()");

  $q.loading.hide();
});
</script>

<style>
.card-hover {
  cursor: pointer;
}

.tabBtn {
  background-color: #f5f5f5;
  border-color: rgba(0, 0, 0, .12);
  min-width: 48px;
  min-height: 36px;
}
</style>
