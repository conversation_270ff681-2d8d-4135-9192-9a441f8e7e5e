<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-page-container class="pt-4" fluid>
    <q-row>
      <q-col><div>新しい会員情報帳票を作成</div></q-col>
    </q-row>
    <q-row>
      <q-col cols="6" sm="2">
        <FormTemplate :template="memberTemplate" />
      </q-col>
    </q-row>
  </q-page-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';

import FormTemplate from "./../components/FormTemplate.vue";

import { getCssVar } from 'quasar';

// data
const memberTemplate = ref<any>({ id: "member", name: "会員登録帳票", thumbnails: "", formType: "member" });

// computed
const primaryColor = computed(() => {
  return getCssVar('primary');
});
// hooks

onMounted(() => {});

</script>

<style>
.card-hover {
  cursor: pointer;
}
</style>
