<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <div>
      <q-row no-gutters class="btnGroup q-py-md">
        <q-input v-model="search" filled 
        dense clearable placeholder="タイトルで検索" 
        class="col-4 q-mr-md text-blue-grey-3"
        style="background-color: white; 
        width: 320px; max-width: 320px; height: 40px;"></q-input>
        <div class="rightBtnGroup">
          <q-btn v-model="isFetching" outlined dense unelevated :loading="isFetching" width="118" class="ma-0 pa-0"
            color="blue-grey-6" @click="refetchMemberConfigs">
            <q-icon size="sm" left name="mdi-cached" />
            データ更新
          </q-btn>
          <router-link :event="!isAdministrator ? '' : 'click'"
            :to="{ name: 'FormCreateNewPage', params: { template: 'member', formType: 'member' } }">
            <q-btn :disable="!isAdministrator || isCreating" :loading="isCreating" @click="toggleIsCreating" color="primary" elevation="0"><q-icon name="mdi-plus" /> 新規作成 </q-btn>
          </router-link>
        </div>
      </q-row>
    </div>
    <q-table v-model="selected" flat 
    :columns="columns" 
    :rows="filteredData" 
    :rows-per-page-options="[5, 10, 15, 0]"
    :pagination="pagination" 
    :search="search" 
    :sort-desc="sortDesc" 
    :loading="loading"
    selection="multiple"
    v-model:selected="selected"
    color="primary"
    row-key="surveyId" indeterminate 
    class="elevation-0">
      <template v-slot:body-cell-surveyTitle="props">
          <q-td :props="props">
            <div>{{ props.row.surveyTitle }}</div>
          </q-td>
        </template>
        <template v-slot:body-cell-updatedAt="props">
          <q-td :props="props">
            <div>{{ datetimeFormatter.formatUnixToYYYYMMDDHHmmss(props.row.updatedAt) }}</div>
          </q-td>
        </template>
        <template v-slot:body-cell-updatedBy="props">
          <q-td :props="props">
            <div>{{ props.row.updatedBy }}</div>
          </q-td>
        </template>
        <template v-slot:body-cell-registrationScreen="props">
          <q-td :props="props">
            <q-btn @click="copyUrlToClipboard(props.row.endpointURL, 'member/', props.row.surveyId)" color="primary"
              flat dense style="border:solid 1px #2196F3!important;font-size:12px;" width="81px">
              URLコピー
            </q-btn>
          </q-td>
        </template>
        <template v-slot:body-cell-inquiryScreen="props">
          <q-td :props="props">
            <q-btn @click="copyUrlToClipboard(props.row.endpointURL, 'member-confirm/', props.row.surveyId)" color="primary"
              flat dense style="border:solid 1px #2196F3!important;font-size:12px;" width="81px">
              URLコピー
            </q-btn>
          </q-td>
        </template>
        <template v-slot:body-cell-surveyStatus="props">
          <q-td :props="props">
            <div>
              <q-chip v-if="props.row.surveyStatus == 'enable'" small text-color="primary" color="transparent"
                opacity="1">
                有効
              </q-chip>
              <q-chip v-else small text-color="error" color="transparent" opacity="1">
                無効
              </q-chip>
            </div>
          </q-td>
        </template>
        <template v-slot:body-cell-edit="props">
          <q-td :props="props">
            <q-btn @click="onEditMember(props.row.surveyId)" color="primary" dense unelevated style="font-size:12px;"
              :disable="!isAdministrator" class="q-px-md">
              編集
            </q-btn>
          </q-td>
        </template>
        <template v-slot:body-cell-formSetting="props">
          <q-td :props="props">
            <q-btn @click="onShowEndOfSurveyMessage(props.row.surveyId)" color="primary" outline dense
              style="font-size:12px;" :disable="!isAdministrator">
              帳票設定
            </q-btn>
          </q-td>
        </template>
      <template v-slot:no-data>
        <div class="q-my-md flex flex-center row q-gutter-sm full-width" v-if="search.length > 0">
          <div class="flex column flex-center " v-if="!isFetching">
            <div>
              <q-icon size="4em" name="mdi-circle-off-outline" color="grey-7" />
            </div>
            <div class="q-my-sm text-grey-7 text-body1">
              検索結果が見つかりません。
            </div>
          </div>
        </div>
        <div class="tw-w-full" v-else-if="isFetching">
          <div>
            <q-skeleton v-for="i in 3" :key="i" ref="skeleton" class="mx-auto q-my-sm"></q-skeleton>
          </div>
        </div>
        <div v-else>
          <div class="q-my-md flex flex-center row q-gutter-sm full-width">
            <div>
              <q-icon size="4em" name="warning" color="grey-7" />
            </div>
            <div class="text-grey-7" v-if="!isFetching">
              データはありません。
            </div>
            <div v-else>
              <q-skeleton v-for="i in 3" :key="i" ref="skeleton" class="mx-auto q-my-sm"></q-skeleton>
            </div>
          </div>
        </div>
      </template>
    </q-table>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, Ref, ref, watch } from "vue";
import { useMemberStore } from "@stores/modules/member";
import { useAuthStore } from "@stores/modules/auth.module";
import { useRouter } from "vue-router";
import { useDatetimeFormatter } from "@/mixins/DatetimeFormatter";
import { is, useQuasar } from "quasar";

const col_align: any = "left";

const columns = [
  { name: "surveyTitle", required: true, label: "タイトル", align: col_align, field: "surveyTitle" },
  { name: "updatedAt", required: true, label: "更新日時", align: col_align, field: "updatedAt" },
  { name: "updatedBy", required: true, label: "更新者", align: col_align, field: "updatedBy" },
  { name: "registrationScreen", required: true, label: "登録画面", align: col_align, field: "registrationScreen" },
  { name: "inquiryScreen", required: true, label: "照会画面", align: col_align, field: "inquiryScreen" },
  { name: "surveyStatus", required: true, label: "有効/無効", align: col_align, field: "surveyStatus" },
  { name: "edit", required: true, label: "編集", align: col_align, field: "edit" },
  { name: "formSetting", required: true, label: "帳票設定", align: col_align, field: "formSetting" }
];

const props = defineProps({
  memberConfigs: Array,
  loading: Boolean,
  sortOptions: Array as any,
  sortBy: String,
  error: Error,
});

const rows: Ref<any[]> = ref(props.memberConfigs);
const search = ref("");

const sortDesc = true;
const itemsPerPage = 10;

const pagination = ref({
  sortBy: props.sortBy,
  descending: sortDesc,
  page: 1,
  rowsPerPage: itemsPerPage,
});

const memberStore = useMemberStore();
const authStore = useAuthStore();

const router = useRouter();

const isAdministrator = computed(() => authStore.isAdministrator);
const isCreating = ref(false);

const $q = useQuasar();

const selected = ref([]);

const sortByTitle = computed(() => {
  return props.sortOptions?.find((obj: any) => obj.value === props.sortBy).title || "";
});

const datetimeFormatter = useDatetimeFormatter();

const isFetching = ref(false);

const onEditMember = (surveyId: string) => {
  router.push({
    name: "FormDetailPage",
    params: {
      surveyId: surveyId,
      formType: "member",
    },
  });
};

const filteredData = computed(() => {
  // Apply search filter
  let data = rows.value;
  if (search.value) {
    const filterLowerCase = search.value.toLowerCase();
    data = rows.value.filter((row) => {
      return (
        row.surveyTitle.toLowerCase().includes(filterLowerCase) ||
        String(row.surveyTitle).includes(search.value)
      );
    });
  }
  // Handle pagination
  const start = (pagination.value.page - 1) * pagination.value.rowsPerPage;
  const end = start + rows.value.length;
  return data.slice(start, end);
});

const totalPages = ref(1);

// Watch rows and search to update totalPages
watch(
  [rows, search],
  () => {
    const filteredLength = rows.value.filter((row) => {
      const filterLowerCase = search.value.toLowerCase();
      return (
        row.surveyTitle.toLowerCase().includes(filterLowerCase) ||
        String(row.surveyTitle).includes(search.value)
      );
    }).length;
    totalPages.value = Math.ceil(filteredLength / pagination.value.rowsPerPage);
  },
  { immediate: true }
);

/* const onSortBy = (sortBy: string) => {
  props.sortBy = sortBy;
}; */

const sortMethod = (rows: any, sortBy: string, descending: boolean) => {
  return rows.slice().sort((a: any, b: any) => {
    const sortA = a[sortBy];
    const sortB = b[sortBy];
    if (sortA < sortB) {
      return descending ? 1 : -1;
    }
    if (sortA > sortB) {
      return descending ? -1 : 1;
    }
    return 0;
  });
};

const refetchMemberConfigs = async () => {
  isFetching.value = true;
  rows.value = [];
  await memberStore.fetchAllMemberFormConfigs();
  rows.value = memberStore.memberConfigs;
  $q.notify("データを更新しました。");
  isFetching.value = false;
};
const copyUrlToClipboard = (endpointURL: string, memberOrConfirm: string, memberSurveyId: string) => {
  navigator.clipboard.writeText(endpointURL + memberOrConfirm + memberSurveyId);
  // this.$snackbar.show({ text: "クリップボードにコピーしました。" });
};

const toggleIsCreating = () => {
  isCreating.value = !isCreating.value;
  setTimeout(() => {
    isCreating.value = false;
  }, 5000);
};

const onShowEndOfSurveyMessage = (surveyId: string) => {
  router.push({
    name: "SettingsIndex",
    params: {
      surveyId: surveyId,
      formType: "member",
    },
  });
};

// computed

const memberConfigs = computed(() => {
  return props.memberConfigs;
});

onMounted(async () => {
  isFetching.value = true;
  await memberStore.fetchAllMemberFormConfigs();
  rows.value = memberStore.memberConfigs;
  isFetching.value = false;
});


</script>

<style>
.card-hover {
  cursor: pointer;
}

.rightBtnGroup {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.btnGroup {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.btnGroup>.rightBtnGroup>.DataUpdateBtn {
  min-width: 130px;
  background-color: #607D8B !important;
  border-radius: 4px;
  height: 36px;
}

.DataUpdateBtn>.v-btn {
  min-width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}

.DataUpdateBtn>.v-btn>.v-btn__content {
  color: #fff !important;
}

.btnGroup>.v-input {
  border: solid 1px #CFD8DC;
}

.btnGroup>.v-input>.v-input__control>.v-input__slot {
  box-shadow: none !important;
}

.theme--light.v-chip:hover:before {
  content: none;
}
</style>
