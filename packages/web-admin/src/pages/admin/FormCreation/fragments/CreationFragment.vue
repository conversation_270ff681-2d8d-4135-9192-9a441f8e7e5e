<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-page-container class="pt-4" fluid>
    <q-row>
      <q-col><div>新しい帳票を作成</div></q-col>
    </q-row>
    <q-row>
      <q-col cols="6" sm="2">
        <FormBlankCard />
      </q-col>
      <!-- <q-col cols="6" sm="2">
        <GoogleFormCard />
      </q-col> -->
      <q-col v-for="item in formTemplates" cols="6" sm="2" :key="item.id">
        <FormTemplate :template="item" />
      </q-col>
    </q-row>
  </q-page-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useQuasar, getCssVar } from 'quasar';

import FormTemplate from "./../components/FormTemplate.vue";
import FormBlankCard from "./../components/FormBlankCard.vue";
import { get } from 'lodash';


const $q = useQuasar();


// data
const formTemplates = ref<any>([
        { id: "delivery-setting", name: "配信設定", thumbnails: "" },
        { id: "contact-form", name: "ご連絡フォーム", thumbnails: "" },
        { id: "survey", name: "調査", thumbnails: "" },
        { id: "corona-NTimesVaccination", name: "新型コロナワクチン接種（N回目接種）", thumbnails: "" },
        { id: "corona-1-2TimesVaccination", name: "新型コロナワクチン接種（1・2回目）", thumbnails: "" },
      ]);

// computed
const primaryColor = (): any => {
  return getCssVar('primary');
};

// hooks

onMounted(() => {});

</script>

<style>
.card-hover {
  cursor: pointer;
}
</style>
