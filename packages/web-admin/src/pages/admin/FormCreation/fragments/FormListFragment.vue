<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-card class="tw-p-4">
      <div>
        <q-row no-gutters class="btnGroup align-center u-flex justify-space-between">
          <q-input v-model="search" filled dense clearable placeholder="タイトルで検索" class="col-4 q-mr-md text-blue-grey-3"
            style="background-color: white; width: 320px; max-width: 320px; height: 40px;"></q-input>
          <div class="rightBtnGroup align-center u-flex justify-end">
            <q-btn color="red" dense width="167" elevation="0" outline @click="showDeleteConfirmPopup"
              :disabled="!selected.length || !isAdministrator">
              <q-icon size="sm" left name="mdi-trash-can-outline" />
              選択項目を削除
            </q-btn>
            <q-btn v-model="isFetching" outlined dense unelevated :loading="isFetching" width="118" class="ma-0 pa-0"
              color="blue-grey-6" @click="refetchFormConfigs">
              <q-icon size="sm" left name="mdi-cached" />
              データ更新
            </q-btn>
            <router-link :event="!isAdministrator ? '' : 'click'" :to="{
              name: 'FormCreateNewPage',
              params: { template: 'blank', formType: 'survey' },
            }">
              <q-btn :disable="!isAdministrator || isCreating" :loading="isCreating" @click="toggleIsCreating" color="white" class="bg-primary" flat>
                <q-icon size="sm" left name="mdi-plus" />
                新規作成
              </q-btn>
            </router-link>
          </div>
        </q-row>
      </div>
      <q-table :rows="filteredData" :rows-per-page-options="[5, 10, 15, 0]" :pagination="pagination"
        :sort-method="sortMethod" row-key="surveyId" selection="multiple" :selected-rows-ripple="false"
        :selected.sync="selected" :columns="columns" :no-data-label="'データがありません'" :loading="isFetching" color="primary"
        :hide-default-footer="true" @update:selected="onSelectionChange">
        <template v-slot:body-cell-surveyTitle="props">
          <q-td :props="props">
            <div>{{ props.row.surveyTitle }}</div>
          </q-td>
        </template>
        <template v-slot:body-cell-updatedAt="props">
          <q-td :props="props">
            <div>{{ datetimeFormatter.formatUnixToYYYYMMDDHHmmss(props.row.updatedAt) }}</div>
          </q-td>
        </template>
        <template v-slot:body-cell-updatedBy="props">
          <q-td :props="props">
            <div>{{ props.row.updatedBy }}</div>
          </q-td>
        </template>
        <template v-slot:body-cell-registrationScreen="props">
          <q-td :props="props">
            <q-btn @click="copyUrlToClipboard(props.row.endpointURL, 'survey/', props.row.surveyId)" color="primary"
              flat dense style="border:solid 1px #2196F3!important;font-size:12px;" width="81px">
              URLコピー
            </q-btn>
          </q-td>
        </template>
        <template v-slot:body-cell-inquiryScreen="props">
          <q-td :props="props">
            <q-btn @click="copyUrlToClipboard(props.row.endpointURL, 'confirm/', props.row.surveyId)" color="primary"
              flat dense style="border:solid 1px #2196F3!important;font-size:12px;" width="81px">
              URLコピー
            </q-btn>
          </q-td>
        </template>
        <template v-slot:body-cell-surveyStatus="props">
          <q-td :props="props">
            <div>
              <q-chip v-if="props.row.surveyStatus == 'enable'" small text-color="primary" color="transparent"
                opacity="1">
                有効
              </q-chip>
              <q-chip v-else small text-color="error" color="transparent" opacity="1">
                無効
              </q-chip>
            </div>
          </q-td>
        </template>
        <template v-slot:body-cell-edit="props">
          <q-td :props="props">
            <q-btn @click="onEditSurvey(props.row.surveyId)" color="primary" dense unelevated style="font-size:12px;"
              :disable="!isAdministrator" class="q-px-md">
              編集
            </q-btn>
          </q-td>
        </template>
        <template v-slot:body-cell-formSetting="props">
          <q-td :props="props">
            <q-btn @click="onShowEndOfSurveyMessage(props.row.surveyId)" color="primary" outline dense
              style="font-size:12px;" :disable="!isAdministrator">
              帳票設定
            </q-btn>
          </q-td>
        </template>
        <template v-if="error">
          <ContentLoadError :error="error" />
        </template>
        <template v-slot:no-data>
          <div class="q-my-md flex flex-center row q-gutter-sm full-width" v-if="search.length > 0">
            <div class="flex column flex-center " v-if="!isFetching">
              <div>
                <q-icon size="4em" name="mdi-circle-off-outline" color="grey-7" />
              </div>
              <div class="q-my-sm text-grey-7 text-body1">
                検索結果が見つかりません。
              </div>
            </div>
          </div>
          <div class="tw-w-full" v-else-if="isFetching">
            <div>
              <q-skeleton square />
            </div>
          </div>
          <div v-else>
            <div class="q-my-md flex flex-center row q-gutter-sm full-width">
              <div>
                <q-icon size="4em" name="warning" color="grey-7" />
              </div>
              <div class="text-grey-7 ">
                データはありません。
              </div>
            </div>
          </div>
        </template>
      </q-table>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, Ref, ref, watch } from "vue";
import { useFormsStore } from "@stores/modules/forms";
import { useSurveyResultsStore } from "@stores/modules/surveyResults"
import { useAuthStore } from "@/stores/modules/auth.module";
import ContentLoadError from "@components/common/ContentLoadError.vue";
import { FORM_HEADERS } from "@stores/modules/users/users.constants";
import { useDatetimeFormatter } from "@/mixins/DatetimeFormatter";
import { useRouter } from "vue-router";
import { is, useQuasar } from "quasar";

const props = defineProps({
  formConfigsProps: Array,
  loading: Boolean,
  error: Error,
});

// Table alignment override, do not delete.
// テーブルのアライメントを上書き。削除しないでください。
const col_align = "left" as const;

const columns = [
  { name: "surveyTitle", required: true, label: "タイトル", align: col_align, field: "surveyTitle", sortable: true},
  { name: "updatedAt", required: true, label: "更新日時", align: col_align, field: "updatedAt", sortable: true },
  { name: "updatedBy", required: true, label: "更新者", align: col_align, field: "updatedBy", sortable: true },
  { name: "registrationScreen", required: true, label: "登録画面", align: col_align, field: "registrationScreen" },
  { name: "inquiryScreen", required: true, label: "照会画面", align: col_align, field: "inquiryScreen" },
  { name: "surveyStatus", required: true, label: "有効/無効", align: col_align, field: "surveyStatus", sortable: true},
  { name: "edit", required: true, label: "編集", align: col_align, field: "edit" },
  { name: "formSetting", required: true, label: "帳票設定", align: col_align, field: "formSetting" }];

const sortMethod = (rows: any, sortBy: string, descending: boolean) => {
  return rows.slice().sort((a: any, b: any) => {
    const sortA = a[sortBy];
    const sortB = b[sortBy];
    if (sortA < sortB) {
      return descending ? 1 : -1;
    }
    if (sortA > sortB) {
      return descending ? -1 : 1;
    }
    return 0;
  });
};

const rows = ref([]);

const $q = useQuasar();

const search = ref("");
const sortDesc = true;
const itemsPerPage = 10; //all
const sortBy = ref("updatedAt");
const selected: Ref<any> = ref([]);

const formsStore = useFormsStore();
const surveyResultStore = useSurveyResultsStore();
const authStore = useAuthStore();
const router = useRouter();

const isAdministrator = computed(() => authStore.isAdministrator);
const isCreating = ref(false);

const isFetching = computed(() => formsStore.isFetchingSurveyConfigsList);

const pagination = ref({
  sortBy: sortBy.value,
  descending: sortDesc,
  page: 1,
  rowsPerPage: itemsPerPage,
});

const filteredData = computed(() => {
  // Apply search filter
  let data = rows.value;
  if (search.value) {
    const filterLowerCase = search.value.toLowerCase();
    data = rows.value.filter((row) => {
      return (
        row.surveyTitle.toLowerCase().includes(filterLowerCase) ||
        String(row.surveyTitle).includes(search.value)
      );
    });
  }
  // Handle pagination
  const start = (pagination.value.page - 1) * pagination.value.rowsPerPage;
  const end = start + rows.value.length;
  return data.slice(start, end);
});

const totalPages = ref(1);

// Watch rows and search to update totalPages
watch(
  [rows, search],
  () => {
    const filteredLength = rows.value.filter((row) => {
      const filterLowerCase = search.value.toLowerCase();
      return (
        row.surveyTitle.toLowerCase().includes(filterLowerCase) ||
        String(row.surveyTitle).includes(search.value)
      );
    }).length;
    totalPages.value = Math.ceil(filteredLength / pagination.value.rowsPerPage);
  },
  { immediate: true }
);


const datetimeFormatter = useDatetimeFormatter();

const refetchFormConfigs = async () => {
  rows.value = [];
  await formsStore.fetchFormConfigs();
  rows.value = formsStore.surveyConfigsList;
  $q.notify("データを更新しました。");
};

const formConfigs = computed(() => formsStore.surveyConfigsList);
const updateSurveyConfigError = computed(() => formsStore.updateSurveyConfigError);

const resetSelectedSurvey = (surveyId: string) => {
  surveyResultStore.resetSelectedSurvey(surveyId);
};

const updateSurveyConfig = async (surveyConfig: any) => {
  await formsStore.updateSurveyConfig(surveyConfig);
};

const onEditSurvey = (surveyId: string) => {
  router.push({
    name: "FormDetailPage",
    params: {
      surveyId: surveyId,
      formType: "survey",
    },
  });
};

const onSelectionChange = (selectedRows: any) => {
  selected.value = selectedRows;
};

const copyUrlToClipboard = (endpointURL: string, surveyOrConfirm: string, surveyId: string) => {
  navigator.clipboard.writeText(endpointURL + surveyOrConfirm + surveyId);
  /* Copy the text inside the text field */
  document.execCommand("copy");
  $q.notify("クリップボードにコピーしました。");
};

const onShowEndOfSurveyMessage = (surveyId: string) => {
  router.push({
    name: "SettingsIndex",
    params: {
      surveyId: surveyId,
      formType: "survey",
    },
  });
};

const showDeleteConfirmPopup = () => {
  let surveyTitleArray = [];
  let surveyIdArray = [];
  if (selected.value.length != 0) {
    for (let i = 0; selected.value.length > i; i++) {
      surveyTitleArray.push(selected.value[i].surveyTitle);
      surveyIdArray.push(selected.value[i].surveyId);
      if (selected.value[i].surveyStatus == "enable") {
        $q.notify({
          message: "有効な帳票は削除できません",
          type: "error",
          icon: "warning",
          timeout: 0,
          actions: [
            {
              label: "閉じる",
              color: "white",
              textColor: "primary",
              handler: () => { }
            },
          ],
        });
        return;
      }
    }
  }
  let isUpdatingLocal = ref(false);
  $q.dialog({
    title: "以下の帳票を削除してもよろしいですか？",
    message: `${surveyTitleArray.join()}`,
    ok: {
      color: "red",
      textColor: "white",
      label: "はい",
    },
  }).onOk(async () => {
    try {
      $q.loading.show();
      isUpdatingLocal.value = true;
      for (let j = 0; selected.value.length > j; j++) {
        await updateSurveyConfig({
          ...(selected.value[j] as object),
          isDisplay: false,
        });
        await resetSelectedSurvey(surveyIdArray[j]);
      }
      if (updateSurveyConfigError.value) {
        isUpdatingLocal.value = false;
        $q.notify({ message: updateSurveyConfigError.value });
      } else {
        isUpdatingLocal.value = false;
        $q.notify({ message: `${surveyTitleArray.join()}の帳票を削除しました。` });
        refetchFormConfigs();
      }
    } catch (error) {
      console.error(error);
      $q.notify({
        type: "error",
        message: "システムエラーが発生しました。システム管理者にお問い合わせください。",
      });
      $q.loading.hide();
    } finally {
      selected.value = [];
      $q.loading.hide();
    }
  });
};

const toggleIsCreating = () => {
  isCreating.value = !isCreating.value;
  setTimeout(() => {
    isCreating.value = false;
  }, 5000);
};

onMounted(async () => {
  await formsStore.fetchFormConfigs();
  rows.value = formConfigs.value;
});

onUnmounted(() => {
  formsStore.resetSurveyConfigsList();
});
</script>

<style lang="less">
.card-hover {
  cursor: pointer;
}

.rightBtnGroup .q-btn {
  margin-left: 10px;

  padding: 6px;
  padding-right: 12px;
}

.rightBtnGroup .v-btn__content {
  font-size: 14px !important;
  align-items: flex-start !important;
}

.btnGroup>.rightBtnGroup>.DataUpdateBtn {
  min-width: 130px;
  background-color: #607D8B !important;
  border-radius: 4px;
  height: 36px;
}

.DataUpdateBtn>.v-btn {
  min-width: 100% !important;
}

.btnGroup>.v-input {
  border: solid 1px #CFD8DC;
}

.btnGroup>.v-input>.v-input__control>.v-input__slot {
  box-shadow: none !important;
}

.theme--light.v-chip:hover:before {
  content: none;
}
</style>
