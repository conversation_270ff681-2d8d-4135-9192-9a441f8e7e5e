<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-hover v-slot:default="{ hover }">
      <q-card
        :style="hover ? `border-color: ${primaryColor}` : ''"
        class="card-hover mx-auto d-flex align-center justify-center"
        outlined
        height="100"
        :disabled="hasActionPermission('disableButton', 'FormCreation_FormTemplate_Click')"
        @click="hasActionPermission('disableButton', 'FormCreation_FormTemplate_Click') ? '' : onGotoTemplatePage()"
        @keypress="
          hasActionPermission('disableButton', 'FormCreation_FormTemplate_Keypress')
            ? ''
            : templatePageOnKeyPress($event)
        "
      >
        <q-icon style="font-size: 50px" color="grey" name="mdi-card-text" ></q-icon>
      </q-card>
    </q-hover>
    <div class="pa-2">{{ template.name }}</div>
  </div>
</template>

<script setup lang="ts">
import { PropType,  computed, onBeforeMount } from 'vue';

import { useRoute, useRouter } from 'vue-router';

import { getCssVar } from 'quasar';

import { usePermissionHelper } from '@/mixins/PermissionHelper';

const { hasActionPermission } = usePermissionHelper();

// old imports
// 旧インポート
/**/

const router = useRouter();
const route = useRoute();

// props
const props = defineProps({
  template: Object as PropType<any>
});

// methods
const onGotoTemplatePage = (): void => {
      router.push({
        name: "FormCreateNewPage",
        params: {
          template: props.template.id,
          formType: formType.value,
        },
      });
    };
const templatePageOnKeyPress = (event: any): void => {
      if (event.keyCode === 13) {
        // onGotoTemplatePage()
        router.push({
          name: "FormCreateNewPage",
          params: {
            template: props.template.id,
            formType: formType.value,
          },
        });
      }
    };

// computed
const primaryColor = computed((): any => {
  return getCssVar('primary');
});
const formType = computed((): any => {
  return props.template.formType.value ? props.template.formType.value : "survey";
});

// hooks

onBeforeMount(() => {});

</script>

<style>
.card-hover {
  cursor: pointer;
}
</style>
