<template>
  <div>
    <div class="tw-p-4 tw-pb-2 row justify-between">
      <DamageReportBreadcrumbs>
        <q-breadcrumbs-el :label="t('breadcrumbs.Map')" class="tw-h-9" />
      </DamageReportBreadcrumbs>
    </div>
    <q-card flat bordered class="tw-rounded-lg tw-mt-2">
      <DamageReportMapView></DamageReportMapView>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import DamageReportMapView from '../../../components/DamageReport/DamageReportMapView.vue'
import DamageReportBreadcrumbs from '../../../components/DamageReport/DamageReportBreadcrumbs.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
</script>
