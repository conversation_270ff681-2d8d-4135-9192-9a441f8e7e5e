<template>
  <div>
    <div class="tw-p-4 tw-pb-2 row justify-between">
      <DamageReportBreadcrumbs> </DamageReportBreadcrumbs>
      <q-btn
        color="primary"
        @click="toggleShowFilterForm"
        :icon-right="
          isShowFilterForm ? 'keyboard_arrow_up' : 'keyboard_arrow_down'
        "
        :label="t('btn.ToggleFilterForm')"
      >
      </q-btn>
    </div>
    <DamageReportFilterForm
      v-show="isShowFilterForm"
      @submitFilter="submitFilter"
    >
    </DamageReportFilterForm>
    <DamageReportTable ref="tableRef"></DamageReportTable>
  </div>
</template>

<script lang="ts" setup>
import DamageReportFilterForm from '../../../components/DamageReport/DamageReportFilterForm.vue'
import DamageReportTable from '../../../components/DamageReport/DamageReportTable.vue'
import DamageReportBreadcrumbs from '../../../components/DamageReport/DamageReportBreadcrumbs.vue'
import { useDamageReportStore } from '../../../stores/modules/damage-report'

import { storeToRefs } from 'pinia'
import { useI18n } from 'vue-i18n'
import { ref } from 'vue'

const { t } = useI18n()
const reportStore = useDamageReportStore()
const { isShowFilterForm } = storeToRefs(reportStore)
const tableRef = ref()
function toggleShowFilterForm() {
  isShowFilterForm.value = !isShowFilterForm.value
}

async function submitFilter() {
  await reportStore.triggerFilter()
  await tableRef.value.tableRequest()
}
</script>
