<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div class="smartphone q-my-xl">
    <div class="line-header">
      <q-banner dark color="primary" :style="'min-height: 24px;'">
        <q-row class="row">
        <q-col>
          <q-icon name="mdi-signal-cellular-outline"></q-icon>
          <span>Line mobile</span>
        </q-col>
        <q-space></q-space>
        <q-col>
          <q-icon name="mdi-wifi-strength-4"></q-icon>
          <q-icon name="mdi-battery"></q-icon>
          <span>12:30</span>
        </q-col>
      </q-row>
      </q-banner>
      <q-card flat>
        <q-toolbar color="primary">
          <q-btn flat round dense icon="mdi-chevron-left" @click="fakeClick" />
          <q-toolbar-title>LINE画面プレビュー</q-toolbar-title>
          <q-btn flat round dense icon="mdi-close" @click="fakeClick" />
        </q-toolbar>
      </q-card>
    </div>
    <div class="content">
      <div class="liff-content q-mx-sm q-my-md">
        <template v-if="step1SurveyFormatValue">
          <FormRendering
            :key="liffPreviewKey"
            :configJson="step1SurveyFormatValue"
            :isLiffMode="true"
            :userInfo="{ name: user.username }"
            ref="inputForm"
            :categoriesTree="categoriesTree"
            :categoryRequiredFlag="true"
            :calendarLabelDisplay="false"
            :calendarButtonDisplay="true"
            :accent="primaryColor"
            :isConfirmMode="false"
            :dataSurvey="[]"
            :pinia="getActivePinia()"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSettingsStore } from "@/stores/modules/settings";
import { useAuthStore } from "@/stores/modules/auth.module";
import FormRendering from "@oss/web-shared/src/components/FormRendering/index.vue";
import { useFormEditorStore } from '@stores/modules/formEditor';
import { useUsersStore } from '@stores/modules/users';
import { computed, onMounted, ref, watch } from "vue";
import { getActivePinia } from "pinia";

const authStore = useAuthStore();
const userStore = useUsersStore();
const formEditorStore = useFormEditorStore();
const settingsStore = useSettingsStore();
const step1SurveyFormat = ref({});
const liffPreviewKey = formEditorStore.previewComponentKey;
const user = computed(() => authStore.user);

const categoriesTree = ref([]);

const primaryColor = ref('#fff');

const fakeClick = () => { };

watch(() => formEditorStore.step1SurveyFormat, (newVal) => {
  step1SurveyFormat.value = newVal;
}, { deep: true, immediate: true });

const step1SurveyFormatValue = computed(() => {
  return formEditorStore.step1SurveyFormat;
});

onMounted(async () => {
  //await settingsStore.fetchCommonSettings();
  //console.log('settingsStore.commonSettings.primaryColor', settingsStore.commonSettings.primaryColor);
  primaryColor.value = settingsStore.commonSettings.primaryColor || '#1976D2';

  // get categories tree from formEditorStore
  await formEditorStore.setCalendarDataOfCategoriesTree1()
  categoriesTree.value = formEditorStore.categoriesTree;

  //console.log("username", user.value.username);
});

</script>

<style lang="less">
/* The device with borders */
.smartphone {
  position: relative;
  width: 360px;
  //height: 640px;
  margin: auto;
  border: 16px black solid;
  border-top-width: 60px;
  border-bottom-width: 60px;
  border-radius: 36px;
}

/* The horizontal line on the top of the device */
.smartphone:before {
  content: "";
  display: block;
  width: 60px;
  height: 5px;
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #333;
  border-radius: 10px;
}

/* The circle on the bottom of the device */
.smartphone:after {
  content: "";
  display: block;
  width: 35px;
  height: 35px;
  position: absolute;
  left: 50%;
  bottom: -65px;
  transform: translate(-50%, -50%);
  background: #333;
  border-radius: 50%;
}

/* The screen (or content) of the device */
.smartphone .content {
  width: 100%;
  height: 450px;
  overflow-y: auto;
  background: white;
}

.line-header {
  background: white;
}

.liff-content {
  background-color: rgb(250, 251, 252);
}
</style>