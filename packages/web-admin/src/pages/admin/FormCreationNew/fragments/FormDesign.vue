<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div :class="`${isMenuVertical ? 'q-ml-lg' : ''}`">
    <div class="cont-row">
      <div class="cont-fe-ui" >
        <FormEditorUI :formSchema="initFormSchema" :duplicateFlg="duplicateFlg"/>
      </div>
      <div class="q-ml-md" ref="previewElement" id="liff-preview">
        <div :class="{ 'fixed-preview': fixedPreview }">
          <SurveyPreview />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { getCssVar } from 'quasar';

import FormEditorUI from "@/components/FormEditor/FormEditorUI.vue";
import SurveyPreview from "@/pages/admin/FormCreationNew/fragments/SurveyPreview.vue";

import { useSettingsStore } from "@/stores/modules/settings";

import debounce from "lodash/debounce";

const settingsStore = useSettingsStore();

// props
const props = defineProps({
	isBlank: Boolean,
	duplicateFlg: Boolean,
});

// data
const model = ref<any>({});
const editMode = ref<number>(1);
const panel = ref<number>(0);
const sourceJson = ref<any>([]);
const formData = ref<any>({});
const selectedLang = ref<string>("ja");
const dialog = ref<boolean>(false);
const initFormSchema = ref<any>([]);
const fixedPreview = ref<boolean>(false);

const previewElement = ref<any>(null);

const handleDebouncedScroll = ref<any>();

const isMenuVertical = ref(settingsStore.commonSettings.menuStyle === "vertical");

// methods
const onScroll = (e: any): void => {
  if (window.scrollY > previewElement.value.offsetTop + 65) {
    fixedPreview.value = true;
  } else {
    fixedPreview.value = false;
  } 
};

// computed
const primaryColor = computed(() => {
  return getCssVar('primary');
});

// hooks

onMounted(() => {
  if (props.isBlank) {
    initFormSchema.value = [];
  }
    handleDebouncedScroll.value = debounce(onScroll, 0);
    window.addEventListener("scroll", handleDebouncedScroll.value);
});


onUnmounted(() => {
  window.removeEventListener("scroll", handleDebouncedScroll.value);
});

</script>

<style lang="less">

.cont-row {
  display: flex;
  justify-content: center;

  .cont-fe-ui {
    width: 100%; // Control width of FormEditorUI | FormEditorUIの幅を制御
  }
}

.confirmation-table {
  border: 1px solid rgba(0, 0, 0, 0.12);
  .confirm-title {
    background: rgba(0, 0, 0, 0.04);
  }
}
#liff-preview {
  width: 360px;
}

.fixed-preview {
  position: sticky;
  top: 60px;
}
</style>
