<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-dialog v-show="show" max-width="790">
      <q-card>
        <q-banner color="primary" dark height="10"> </q-banner>
        <q-card-section class="headline">設定ファイル入力</q-card-section>

        <q-card-section>
          <q-uploader @change="onFileChange" label="File input"></q-uploader>
          <div v-if="fileContent">
            <codemirror v-model="fileContent" :options="cmOptions" />
          </div>
        </q-card-section>

        <q-card-actions>
          <q-space></q-space>
          <q-btn color="primary" @click="onImport"> ファイル入力 </q-btn>
          <q-btn @click="show = false"> キャンセル </q-btn>
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed  } from 'vue';
import eventBus from '@utils/eventBus';

// data
const dialog = ref<boolean>(true);
let fileContent = ref<any>(null);
const cmOptions = ref<any>({
        tabSize: 4,
        mode: "text/yaml",
        theme: "darcula",
        lineNumbers: true,
        line: true,
        // more CodeMirror options...
      });

// methods
const onFileChange = (file: any): void => {
      if (file) {
        const reader = new FileReader();
        reader.onload = (e: any) => (fileContent.value = e.target.result);
        reader.readAsText(file);
      } else {
        fileContent.value = null;
      }
    };
const onImport = (): void => {
      //console.log(YAML.parse(fileContent))
      eventBus.emit("import", fileContent);
      show.value = false;
    };

// computed

const show = computed({
    get: () => dialog.value,
    set: () => {
      if (!dialog.value) {
          eventBus.emit("close");
        }
    },
})
</script>