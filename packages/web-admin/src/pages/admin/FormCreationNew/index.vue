<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div class="q-mx-auto col-8">
    <SubAppBar :followContainer="true">
      <div class="flex flex-center row justify-between">
        <div class="q-gutter-md">
          <q-breadcrumbs separator="-" class="q-py-sm">
            <q-breadcrumbs-el v-for="(breadcrumb, index) in breadcrumbs" :key="index" :label="breadcrumb.text"
              :to="breadcrumb.to" :tag="breadcrumb.to ? 'router-link' : 'span'" class="text-primary"></q-breadcrumbs-el>
          </q-breadcrumbs>
        </div>
        <div>
          <q-btn v-if="isEditMode && !duplicateFlg" flat color="primary" class="q-mr-sm"
            @click="duplicateConfig()"><q-icon name="mdi-checkbox-multiple-blank-outline" left /> フォームを複製 </q-btn>
          <q-btn dense color="primary" :style="'width: 54px; height: 36px;'" class="" @click="saveForm()"> 保存 </q-btn>
        </div>
      </div>
    </SubAppBar>
    <div class="q-pt-none">
      <q-inner-loading :showing="isRegistingSurveyConfig || isUpdatingSurveyConfig" :opacity="0.2" style="z-index: 2;">
        <q-spinner
          color="primary"
          :size="50"
          :thickness="5"
          :speed="0.6"
        />
      </q-inner-loading>
      <ContentLoadError v-if="fetchFormConfigDetailError" :error="fetchFormConfigDetailError" />
      <FormDesign v-else :isBlank="true" :duplicateFlg="duplicateFlg" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onBeforeMount, onMounted, onUnmounted } from 'vue';
import eventBus from '@utils/eventBus';
import FormDesign from '@pages/admin/FormCreationNew/fragments/FormDesign.vue';
import ContentLoadError from '@components/common/ContentLoadError.vue';
import SubAppBar from '@/components/common/SubAppBar.vue';
import { cloneDeep } from 'lodash';
import ValidateSurveyForm from '@/model/Form/ValidateSurveyForm';
import DuplicateEditToNewConfig from '@/model/Form/DuplicateEditToNewConfig';
import { useFormsStore } from '@stores/modules/forms';
import { useFormEditorStore } from '@stores/modules/formEditor';
import { useMemberStore } from '@stores/modules/member';
import { useQuasar } from 'quasar';
import router from '@/router';
import { onBeforeRouteLeave, useRoute } from 'vue-router';
import { RESERVATION_SERVICE_TYPES } from '@/stores/modules/payment/payment.constants';
import { useAuthStore } from '@/stores/modules/auth.module';
import { Auth } from 'aws-amplify';

// props 

const props = defineProps({
  templateId: String,
});

// emits

const emit = defineEmits(['onActiveItem', 'updateDataLocalToStore', 'moveKeySetting']);

const route = useRoute();

const $q = useQuasar();

const tab = ref(null);
const componentKey = ref(0);
const showImportDialog = ref(false);
const error = ref(null);
const breadcrumbs = ref([
  {
    text: '各種帳票一覧',
    disabled: false,
    to: '/forms/form-list',
  },
  {
    text: '',
    disabled: true,
    href: '/',
  },
]);
const duplicateFlg = ref(false);

const formsStore = useFormsStore();
const formEditorStore = useFormEditorStore();
const memberStore = useMemberStore();
const authStore = useAuthStore();

const step1SurveyFormat = computed(() => {
  return formEditorStore.step1SurveyFormat;
});

const loadingNewSurveyId = ref(false);

const isRegistingSurveyConfig = computed(() => {
  return isMemberForm.value
    ? memberStore.isRegisteringMemberConfig
    : formsStore.isRegistingSurveyConfig;
});
const registeredSurveyConfigId = computed(() => {
  return isMemberForm.value
    ? memberStore.registeredMemberConfigId
    : formsStore.registeredSurveyConfigId;
});
const isUpdatingSurveyConfig = computed(() => {
  return isMemberForm.value
    ? memberStore.isUpdatingMemberConfig
    : formsStore.isUpdatingSurveyConfig;
});
const fetchFormConfigDetailError = computed(() => {
  return isMemberForm.value
    ? memberStore.fetchMemberConfigError
    : formsStore.fetchFormConfigDetailError;
});
const isEditMode = computed(() => {
  return formEditorStore.isEditMode;
});

const isLoading = computed(() => {
  return loadingNewSurveyId.value || 
  (!formEditorStore.step1SurveyFormat || formEditorStore.step1SurveyFormat.length === 0) 
});

const paymentServiceList = computed(() => {
  return formEditorStore.paymentServiceList;
});

const isMemberForm = computed(() => {
  return formType.value === 'member' ? true : false;
});

const formType = computed(() => {
  return route.params.formType ? route.params.formType : 'survey';
});

const forceRerender = () => {
  componentKey.value += 1;
};

const saveForm = () => {
  formEditorStore.setActiveFormSchemaItem(null);
  formEditorStore.triggerLocalToSchemaUpdate();
  nextTick(async () => {
    const chkErr = await ValidateSurveyForm.checkItem((formEditorStore.formSchema[0]));
    if (chkErr !== null) {
      $q.notify({ message: chkErr, type: 'error', icon: 'mdi-alert-circle' });
      return;
    } else {
      formEditorStore.openSaveFormDialog();
    }
  });
};

const duplicateConfig = async () => {
  const copyStep1SurveyFormat = cloneDeep(step1SurveyFormat.value);
  //console.log('duplicateConfig', copyStep1SurveyFormat);
  const bkConfig = await DuplicateEditToNewConfig.createDuplicateConfig(copyStep1SurveyFormat);
  // NOTE: 商品選択アイテムがありカレンダー予約をするサービスが選択されている場合はサービスを初期化する
  const selectProductsItem = bkConfig.surveySchema.find((item: any) => item.type === 'selectProducts');
  if (selectProductsItem) {
    const service: any = paymentServiceList.value.find((s: any) => s.sortKey === selectProductsItem.input);
    service?.reservationServiceType === Number(RESERVATION_SERVICE_TYPES.APPLICABLE) &&
      (selectProductsItem.input = null);
  }
  duplicateFlg.value = true;
  breadcrumbs.value[1].text = 'フォームを複製';
  formEditorStore.setFormEditMode(false);
  router.push({
    name: 'FormCreateNewPage',
    params: {
      template: step1SurveyFormat.value.surveyType,
      formType: formType.value,
    },
  });
  formEditorStore.setFormSchema([bkConfig]);
};

onBeforeMount(async () => {
  if (route.params.isEditMode) {
    formEditorStore.setFormEditMode(route.params.isEditMode);
  }
});

onMounted(async () => {
  if (route.params.surveyId) {
    //console.log('initFormSchema: surveyID');
    loadingNewSurveyId.value = true;
    let _result = await formEditorStore.initFormSchema({
      surveyId: route.params.surveyId,
      formType: formType.value,
    });
    if (typeof _result == "object" && _result.noInspectPermission) {
      router.replace({
        path: '/PageNotFound',
      });
    }
    formEditorStore.setBackButtonCondition(true);
  } else {
    //console.log('initFormSchema: template');
    let _template = props.templateId || route.params.template;
    await formEditorStore.initFormSchema({ template: _template, formType: formType.value });
    formEditorStore.setBackButtonCondition(!props.templateId);
  }

  breadcrumbs.value[0].text = isMemberForm.value ? '会員情報登録一覧' : '各種帳票一覧';
  breadcrumbs.value[0].to = isMemberForm.value ? '/forms/member-form-list' : '/forms/form-list';
  if (isEditMode.value) {
    breadcrumbs.value[1].text = '編集';
  } else {
    breadcrumbs.value[1].text = '新規作成';
  }
  loadingNewSurveyId.value = false;
});

onBeforeRouteLeave((to, from, next) => {

  if (
    fetchFormConfigDetailError.value &&
    fetchFormConfigDetailError.value.message &&
    fetchFormConfigDetailError.value.message.indexOf('閲覧設定') > -1
  ) {
    next();
    return;
  }
  if (!duplicateFlg.value && !registeredSurveyConfigId.value) {
    //$snackbar.hide();
    $q.dialog({
      title: 'このページを離れてもよろしいですか？',
      message: '行った変更は破棄されます。',
      ok: {
        label: 'このページを離れる',
      },
      cancel: {
        label: 'キャンセル',
        outline: true
      },
    }).onOk(async () => {

      $q.loading.show();
      
      // Double check on this since this cannot be performed if the user is signed out by expired token
      // これをダブルチェックが必要です。ユーザーが期限切れのトークンでサインアウトされている場合、これは実行できません
      await memberStore.fetchAllMemberFormConfigs();
      await formsStore.fetchFormConfigs();
      $q.loading.hide();
      //console.log('next');
      next(true);
    }).onCancel(() => {
      next(false);
    });
  } else {
    next();
  }
});


// Reset editor on unmount to clear previous data
onUnmounted(() => {
  formEditorStore.setFormSchema([]);
  formEditorStore.setActiveFormSchemaItem(null);
  formEditorStore.setActiveFormTab(null);
  formEditorStore.setFormEditMode(false);
  formEditorStore.setBackButtonCondition(false);
});

watch(tab, (val) => {
  forceRerender();
  formEditorStore.setActiveFormTab(val);
});
</script>

<style lang="less">
.bg {
  background-color: var(--q-bg);
}
</style>