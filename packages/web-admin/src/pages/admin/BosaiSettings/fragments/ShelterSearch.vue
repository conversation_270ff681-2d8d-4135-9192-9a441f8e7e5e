<style scoped>
.search-row-content {
  height: 15em;
}

.search-col-content {
  margin-left: 0.5em;
  margin-right: 0.5em;
  height: inherit;
}

.shelter-checkbox-search {
  border: 1px solid lightgray;
  overflow-y: scroll;
  height: 10em;
}

.search-selectall-checkbox>>>label {
  font-weight: bold;
}
</style>

<template>
  <div>
    <div class="row">
      <div class="search-col-content col">
        <q-input label="キーワード検索" placeholder="フリーワード" v-model="keywordSearch"> </q-input>
      </div>
    </div>
    <div class="row tw-w-full tw-my-4">
      <div class="col-6 tw-w-full tw-py-4">
        <div class="search-col-content tw-pb-4">
          <div class="tw-mb-1">情報公開</div>
          <q-btn-toggle :options="toggleSwitchTypeOne" v-model="infoDisclosure" toggle-color="primary" />
        </div>
        <div class="search-col-content tw-pb-4">
          <div class="tw-mb-1">ネットワーク環境の有無</div>
          <q-btn-toggle :options="toggleSwitchTypeTwo" v-model="network" />
        </div>
      </div>
      <div class="col-6 tw-w-full tw-py-4">
        <div class="search-col-content tw-pb-4">
          <div class="tw-mb-1">各携帯キャリアの電波状況等</div>
          <q-btn-toggle :options="toggleSwitchTypeTwo" v-model="cellPhone" />
        </div>
        <div class="search-col-content col">
          <div class="tw-mb-1">COVID-19対応備蓄設備の有無</div>
          <q-btn-toggle :options="toggleSwitchTypeTwo" v-model="equipmentCovid" />
        </div>
      </div>
    </div>

    <div class="search-row-content row tw-pt-4">
      <div class="search-col-content col">
        <div no-gutters class="mb-1 row">
          <div class="col"> 災害種別 </div>
          <div class="col-auto text-primary">
            <a @click="selectAllDisasterTypeSelected()">{{ isSelectedAllDisasters ? "全解除" : "全選択" }}</a>
          </div>
        </div>
        <div class="shelter-checkbox-search">
          <q-list dense class="tw-py-2">
            <q-item v-for="(item, index) in disasterTypeItems" :key="index" clickable
              :active="isActive('disaster', index)" active-class="bg-primary text-white"
              @click="selectItemList('disasterType', index)">
              <q-item-section>
                <q-item-label>{{ item }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </div>
      <div class="search-col-content col">
        <div no-gutters class="tw-mb-1 row justify-between">
          <div> 避難所種別 </div>
          <div class="col-auto">
            <a class="text-primary cursor-pointer non-selectable" @click="selectAllShelterTypeSelected()">{{
              isSelectedAllShelters ? "全解除" : "全選択" }}</a>
          </div>
        </div>
        <div class="shelter-checkbox-search">
          <q-list dense class="py-2">
            <q-item v-for="(item, index) in shelterTypeItems" :key="index" clickable
              :active="isActive('shelter', index)" active-class="bg-primary text-white"
              @click="selectItemList('shelterType', index)">
              <q-item-section>
                <q-item-label>{{ item }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </div>
      <div class="search-col-content col">
        <div no-gutters class="tw-mb-1 row">
          <div class="col"> 隔離可能場所の有無 </div>
          <div class="col-auto">
            <a class="text-primary cursor-pointer non-selectable" @click="selectAllQuarantineValuesSelected()">{{
              isSelectedAllQuarantineValues ? "全解除" : "全選択"
              }}</a>
          </div>
        </div>
        <div class="shelter-checkbox-search">
          <q-list dense class="tw-py-2">
            <q-item v-for="(item, index) in quarantineValues" :key="index" clickable
              :active="isActive('quarantine', index)" active-class="bg-primary text-white"
              @click="selectItemList('quarantineValue', index)">
              <q-item-section>
                <q-item-label>{{ item }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </div>
      <div class="search-col-content col">
        <div class="tw-mb-1 row">
          <div class="col"> ステータス </div>
          <div class="col-auto">
            <a class="text-primary cursor-pointer non-selectable justify-end"
              @click="selectAllShelterStatusSelected()">{{ isSelectedAllShelterStatuses ? "全解除" : "全選択" }}</a>
          </div>
        </div>
        <div class="shelter-checkbox-search">
          <q-list dense class="tw-py-2">
            <q-item v-for="(item, index) in shelterStatuses" :key="index" clickable
              :active="isActive('shelterStatus', index)" active-class="bg-primary text-white"
              @click="selectItemList('shelterStatus', index)">
              <q-item-section>
                <q-item-label>{{ item.displayText }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col tw-flex tw-justify-center">
        <q-btn @click="searchForShelters()" class="shelter-search-button tw-mr-8 tw-w-32" color="primary">検索</q-btn>
        <q-btn @click="clearSearch()">クリア</q-btn>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useBosaiStore } from '@/stores/modules/bosai';
import { ref, computed, onBeforeMount } from 'vue';
import {
  TYPE_OF_DISASTERS,
  TYPE_OF_SHELTERS,
  SHELTER_STATUS,
  QUARANTINE_VALUES,
} from "@/stores/modules/bosai/bosai.constants";
import { cloneDeep } from "lodash";

// store
const bosaiStore = useBosaiStore();

// emits 
const emits = defineEmits<{
  (event: 'searching', payload: any): void;
}>();

// data
const keywordSearch = ref<string>("");
const selectedDisasterTypes = ref<number[]>([]);
const selectedShelterTypes = ref<number[]>([]);
const selectedShelterStatuses = ref<number[]>([]);
const selectedQuarantineValues = ref<number[]>([]);
const selectAllDisasterType = ref<boolean>(false);
const selectAllShelterType = ref<boolean>(false);
const selectAllShelterStatus = ref<boolean>(false);
const selectAllQuarantineValues = ref<boolean>(false);
const disasterTypeItems = ref<any>(TYPE_OF_DISASTERS);
const shelterTypeItems = ref<any>(TYPE_OF_SHELTERS);
const shelterStatuses = ref<any>(SHELTER_STATUS);
const quarantineValues = ref<any>(QUARANTINE_VALUES);
const infoDisclosure = ref<string>("設定しない");
const network = ref<string>("設定しない");
const cellPhone = ref<string>("設定しない");
const equipmentCovid = ref<string>("設定しない");
const toggleSwitchTypeOne = ref<any>([
  { label: "可", value: "ok" },
  { label: "否", value: "ng" },
  { label: "設定しない", value: "設定しない" }
]);
const toggleSwitchTypeTwo = ref<any>([
  { label: "有", value: "有" },
  { label: "無", value: "無" },
  { label: "設定しない", value: "設定しない" }
]);

// methods
const isActive = (type: string, key: number): boolean => {
  key = Number(key);
  switch (type) {
    case "disaster":
      return selectedDisasterTypes.value.includes(key);
    case "shelter":
      return selectedShelterTypes.value.includes(key);
    case "quarantine":
      return selectedQuarantineValues.value.includes(key);
    case "shelterStatus":
      return selectedShelterStatuses.value.includes(key);
    default:
      return false;
  }
};

const getSearchState = (): any => {
  return {
    keywordSearch: keywordSearch.value,
    selectedDisasterTypes: selectedDisasterTypes.value,
    selectedShelterTypes: selectedShelterTypes.value,
    selectedShelterStatuses: selectedShelterStatuses.value,
    selectedQuarantineValues: selectedQuarantineValues.value,
    selectAllDisasterType: selectAllDisasterType.value,
    selectAllShelterType: selectAllShelterType.value,
    selectAllShelterStatus: selectAllShelterStatus.value,
    selectAllQuarantineValues: selectAllQuarantineValues.value,
    infoDisclosure: infoDisclosure.value,
    network: network.value,
    cellPhone: cellPhone.value,
    equipmentCovid: equipmentCovid.value,
  };
};

const disasterTypeSelected = (): void => {
  selectAllDisasterType.value = selectedDisasterTypes.value.length == Object.keys(TYPE_OF_DISASTERS).length;
};

const selectAllDisasterTypeSelected = (): void => {
  if (!isSelectedAllDisasters.value) {
    selectedDisasterTypes.value = [];
    for (var key in TYPE_OF_DISASTERS) {
      selectedDisasterTypes.value.push(parseInt(key));
    }
  } else {
    selectedDisasterTypes.value = [];
  }
};

const shelterTypeSelected = (): void => {
  selectAllShelterType.value = selectedShelterTypes.value.length == Object.keys(TYPE_OF_SHELTERS).length;
};

const selectAllShelterTypeSelected = (): void => {
  if (!isSelectedAllShelters.value) {
    selectedShelterTypes.value = [];
    for (var key in TYPE_OF_SHELTERS) {
      selectedShelterTypes.value.push(parseInt(key));
    }
  } else {
    selectedShelterTypes.value = [];
  }
};

const quarantineValueSelected = (): void => {
  selectAllQuarantineValues.value = selectedQuarantineValues.value.length == Object.keys(QUARANTINE_VALUES).length;
};

const selectAllQuarantineValuesSelected = (): void => {
  if (!isSelectedAllQuarantineValues.value) {
    selectedQuarantineValues.value = [];
    for (var key in QUARANTINE_VALUES) {
      selectedQuarantineValues.value.push(parseInt(key));
    }
  } else {
    selectedQuarantineValues.value = [];
  }
};

const shelterStatusSelected = (): void => {
  selectAllShelterStatus.value = selectedShelterStatuses.value.length == Object.keys(SHELTER_STATUS).length;
};

const selectAllShelterStatusSelected = (): void => {
  if (!isSelectedAllShelterStatuses.value) {
    selectedShelterStatuses.value = [];
    for (var key in SHELTER_STATUS) {
      selectedShelterStatuses.value.push(parseInt(key));
    }
  } else {
    selectedShelterStatuses.value = [];
  }
};

const searchForShelters = async (): Promise<void> => {
  bosaiStore.setSearchShelterCriteria(getSearchState());
  if (
    keywordSearch.value == "" &&
    infoDisclosure.value == "設定しない" &&
    cellPhone.value == "設定しない" &&
    network.value == "設定しない" &&
    equipmentCovid.value == "設定しない" &&
    selectedDisasterTypes.value.length == 0 &&
    selectedShelterTypes.value.length == 0 &&
    selectedShelterStatuses.value.length == 0 &&
    selectedQuarantineValues.value.length == 0
  ) {
    await bosaiStore.fetchShelterList();
  } else {
    var searchPayload: any = {
      facility: {},
    };

    if (keywordSearch.value != "") {
      searchPayload.freeword = keywordSearch.value;
    }
    if (infoDisclosure.value != "設定しない") {
      searchPayload.publicDisclosure = infoDisclosure.value == "可";
    }
    if (network.value != "設定しない") {
      searchPayload.facility.network = network.value == "有";
    }
    if (cellPhone.value != "設定しない") {
      searchPayload.facility.cellPhone = cellPhone.value == "有";
    }
    if (equipmentCovid.value != "設定しない") {
      searchPayload.facility.equipmentCovid = equipmentCovid.value == "有";
    }
    if (selectedDisasterTypes.value.length != 0) {
      searchPayload.disasterTypes = selectedDisasterTypes.value;
    }
    if (selectedShelterTypes.value.length != 0) {
      searchPayload.shelterTypes = selectedShelterTypes.value;
    }
    if (selectedShelterStatuses.value.length != 0) {
      searchPayload.status = selectedShelterStatuses.value;
    }
    if (selectedQuarantineValues.value.length != 0) {
      searchPayload.facility.quarantine = selectedQuarantineValues.value;
    }

    bosaiStore.setSearchParams(searchPayload);
    await bosaiStore.searchAllShelters(searchPayload);
    emits("searching", true);
  }
};

const clearSearch = (): void => {
  keywordSearch.value = "";
  selectedDisasterTypes.value = [];
  selectedShelterTypes.value = [];
  selectedShelterStatuses.value = [];
  selectedQuarantineValues.value = [];
  selectAllDisasterType.value = false;
  selectAllShelterType.value = false;
  selectAllShelterStatus.value = false;
  selectAllQuarantineValues.value = false;
  equipmentCovid.value = "設定しない";
  cellPhone.value = "設定しない";
  network.value = "設定しない";
  infoDisclosure.value = "設定しない";

  bosaiStore.setSearchShelterCriteria(getSearchState());

  bosaiStore.fetchShelterList();
  emits("searching", false);
};

// computed
const shelterSearchFields = computed(() => bosaiStore.shelterSearchFields);

const isSelectedAllDisasters = computed((): boolean => {
  return selectedDisasterTypes.value.length === Object.values(disasterTypeItems.value).length;
});

const isSelectedAllShelters = computed((): boolean => {
  return selectedShelterTypes.value.length === Object.values(shelterTypeItems.value).length;
});

const isSelectedAllShelterStatuses = computed((): boolean => {
  return selectedShelterStatuses.value.length === Object.values(shelterStatuses.value).length;
});

const isSelectedAllQuarantineValues = computed((): boolean => {
  return selectedQuarantineValues.value.length === Object.values(quarantineValues.value).length;
});

const selectItemList = (type: string, index: number) => {
  switch (type) {
    case "shelterType":
      changeItemListValue(selectedShelterTypes.value, index);
      break;
    case "shelterStatus":
      changeItemListValue(selectedShelterStatuses.value, index);
      break;
    case "disasterType":
      changeItemListValue(selectedDisasterTypes.value, index);
      break;
    case "quarantineValue":
      changeItemListValue(selectedQuarantineValues.value, index);
      break;
  }
};

const changeItemListValue = (list: number[], index: number) => {
  index = parseInt(String(index));
  if (list.includes(index)) {
    list.splice(list.indexOf(index), 1);
  } else {
    list.push(index);
  }
};

// hooks
onBeforeMount(() => {
  keywordSearch.value = cloneDeep(shelterSearchFields.value.keywordSearch);
  selectedDisasterTypes.value = cloneDeep(shelterSearchFields.value.selectedDisasterTypes);
  selectedShelterTypes.value = cloneDeep(shelterSearchFields.value.selectedShelterTypes);
  selectedShelterStatuses.value = cloneDeep(shelterSearchFields.value.selectedShelterStatuses);
  selectedQuarantineValues.value = cloneDeep(shelterSearchFields.value.selectedQuarantineValues);
  selectAllDisasterType.value = cloneDeep(shelterSearchFields.value.selectAllDisasterType);
  selectAllShelterType.value = cloneDeep(shelterSearchFields.value.selectAllShelterType);
  selectAllShelterStatus.value = cloneDeep(shelterSearchFields.value.selectAllShelterStatus);
  selectAllQuarantineValues.value = cloneDeep(shelterSearchFields.value.selectAllQuarantineValues);
  infoDisclosure.value = cloneDeep(shelterSearchFields.value.infoDisclosure);
  network.value = cloneDeep(shelterSearchFields.value.network);
  cellPhone.value = cloneDeep(shelterSearchFields.value.cellPhone);
  equipmentCovid.value = cloneDeep(shelterSearchFields.value.equipmentCovid);
});
</script>

<style lang="less">
.common-search-selector-content {
  border: 1px solid #999;
  height: 107px;
  overflow-y: auto;
}

.common-search-selector__active {
  border-left: 2px solid var(--v-primary-base);
}
</style>
