<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.shelter-card {
  background-color: #f2f2f2 !important;
  margin: 0.3em;
}
</style>

<template>
  <q-card class="mx-auto shelter-card" max-width="130" outlined>
    <q-item three-line>
      <q-item-section>
        <q-item-label class="text-primary">
          {{ shelter.shelterName }}
        </q-item-label>
        <q-item-label>
          {{ shelter.address }}
        </q-item-label>
      </q-item-section>
    </q-item>
  </q-card>
</template>

<script setup lang="ts">
// props
const props = defineProps<{
  shelter: any,
}>();
</script>
