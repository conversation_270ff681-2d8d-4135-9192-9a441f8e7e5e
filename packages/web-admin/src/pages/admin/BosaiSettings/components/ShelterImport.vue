<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
  .import-card {
    height: max-content !important;
  }
</style>

<template>
  <q-dialog scrollable persistent v-model="show">
    <q-card class="import-card tw-w-full" :style="'min-width:' + dialogSize">
      <q-bar class="bg-primary tw-h-1"></q-bar>
      <q-toolbar flat>
        <q-toolbar-title>施設登録</q-toolbar-title>

        <q-space></q-space>

        <q-btn icon="mdi-close" round flat @click="closeImport">
        </q-btn>
      </q-toolbar>
      <div class="tw-px-4">
        <ShelterImportContent
          :deleteSelectedFile="!visible"
          v-on:importSuccess="closeImport"
          v-on:fileSelectStart="dialogSize = 600"
          v-on:fileSelectSuccess="dialogSize = '-webkit-fill-available'"
        />
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed,  } from 'vue';
import ShelterImportContent from "@/pages/admin/BosaiSettings/components/ShelterImportContent.vue";

// emits 
const emits = defineEmits<{
  (event: 'close'): void;
}>();

// props
const props = defineProps<{
  visible: boolean,
}>();

// data
const dialogSize = ref<number | string>(600);

// methods
const closeImport = (): void => {
  dialogSize.value = 600;
  show.value = false;
};

// computed
const show = computed({
  get(): boolean {
    return props.visible;
  },
  set(value: boolean): void {
    if (!value) {
      emits("close");
    }
  },
});
</script>
