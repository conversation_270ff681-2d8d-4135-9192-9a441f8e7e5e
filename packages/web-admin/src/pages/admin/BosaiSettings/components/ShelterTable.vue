<style lang="less" scoped>
.shelter-info-subscript {
  font-weight: bold;
}
.shelter-info-box {
  display: grid;
}
.shelter-disaster-type {
  display: list-item;
  list-style-type: disc;
}
.shelter-disaster-type-false {
  display: none;
}
.shelter-status-chip {
  width: -webkit-fill-available;
}

.shelter-status-chip-green,
.shelter-status-chip-red,
.shelter-status-chip-black {
  color: white !important;
}
.shelter-table-div {
  width: 100%;
}
.shelter-select-checkbox {
  width: 3em;
}
</style>

<template>
  <div class="shelter-table-div">
    <q-table
      :columns="headers"
      :rows="shelters"
      :loading="isFetchingShelters"
      row-key="uniqueId"
      :rows-per-page-options="[20, 50, 100]"
      :rows-per-page="20"
      v-model:selected="selectedShelters"
      :selection="showFunctions ? 'multiple' : 'none'"
      :selected-rows-label="() => ''"
      :sort-method="customSort"
    >
      <!-- <template v-slot:header>
        <q-tr>
          <q-th class="text-grey-8" v-for="header in filteredHeaders" :key="header.name" :header="header">
            {{ header.label }}
          </q-th>
        </q-tr>
      </template> -->
      <template v-slot:body-cell-shelterName="props">
        <q-td>
          <div class="shelter-info-box">
            <span class="shelter-info-subscript text-primary">{{ props.row.shelterName }}</span>
            <span>{{ props.row.address }}</span>
          </div>
        </q-td>
      </template>
      <template v-slot:body-cell-typeDisaster="props">
        <q-td>
          <ul>
            <li
              v-for="(disaster, index) in props.value"
              :key="index"
              :class="`shelter-disaster-type shelter-disaster-type-${containedInDisasterTypes(disaster)}`"
            >
              {{ getDisasterTypeDisplay(disaster) }}
            </li>
          </ul>
        </q-td>
      </template>
      <template v-slot:body-cell-status="props">
        <q-td>
          <div v-if="containedInShelterStatus(props.row.status)">
            <q-chip
              :class="`text-center q-mx-none q-px-none shelter-status-chip shelter-status-chip-${
                getShelterStatusDisplay(props.row.status).color
              }`"
              :color="getShelterStatusDisplay(props.row.status).color"
            >
              <span class="self-center full-width">{{ getShelterStatusDisplay(props.row.status).displayText }}</span>
            </q-chip>
          </div>
          <div v-else>
            <q-chip class="text-center shelter-status-chip shelter-status-chip-gray q-mx-none q-px-none" color="gray"> 
              <span class="self-center full-width">
                不明
              </span>
            </q-chip>
          </div>
        </q-td>
      </template>
    </q-table>
    <div v-if="showFunctions" class="row">
      <q-btn 
        class="tw-mt-4 tw-mr-6" 
        color="primary" 
        :loading="isDownloadingShelters" 
        @click="downloadShelters"
      >
        <q-icon left name="mdi-file-delimited"></q-icon>
        CSV出力
      </q-btn>
      <q-space></q-space>
      <q-btn
        class="tw-mt-4 tw-mr-6"
        color="negative"
        @click="deleteAllShelters"
        :style="hasActionPermission('hideButton', 'BosaiSettings_ShelterTable_DeleteAllShelters') ? hideButtonPermissionStyle() : ''"
      >
        <q-icon left name="mdi-delete-forever-outline"></q-icon>
        全削除
      </q-btn>
      <q-btn
        class="tw-mt-4 tw-mr-1"
        color="negative"
        @click="deleteSelectedShelters"
        :style="hasActionPermission('hideButton', 'BosaiSettings_ShelterTable_DeleteSelectedShelters') ? hideButtonPermissionStyle() : ''"
      >
        <q-icon left name="mdi-delete-outline"></q-icon> 
        削除
      </q-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useQuasar } from 'quasar';
import { useBosaiStore } from '@/stores/modules/bosai';
import { TYPE_OF_DISASTERS, SHELTER_STATUS } from "@/stores/modules/bosai/bosai.constants";
import { usePermissionHelper } from '@/mixins/PermissionHelper';
import { isNullOrEmpty } from '@/utils/stringUtils';

// store 
const bosaiStore = useBosaiStore();

// emits 
const emits = defineEmits<{
  (event: 'selected', payload: any): void;
}>();

const $q = useQuasar();

// other
const { hasActionPermission, hideButtonPermissionStyle } = usePermissionHelper();

// props
const props = withDefaults(
  defineProps<{
    shelters: any[],
    showFunctions?: boolean,
  }>(),
  {
    showFunctions: false,
  }
);

// data
const selectedShelters = ref<any>([]);
const headers = ref<any>([
  {
    label: "施設名称 (住所)",
    name: "shelterName",
    field: 'shelterName',
    sortable: true,
    width: "25%",
    align: 'left',
  },
  {
    label: "対応災害",
    name: "typeDisaster",
    field: "typeDisaster",
    sortable: false,
    width: "15%",
    align: 'left',
  },
  {
    label: "緯度",
    name: "latitude",
    field: "latitude",
    sortable: false,
    width: "10%",
    align: 'left',
  },
  {
    label: "経度",
    name: "longitude",
    field: "longitude",
    sortable: false,
    width: "10%",
    align: 'left',
  },
  {
    label: "備考",
    name: "comments",
    field: "comments",
    sortable: true,
    width: "20%",
    align: 'left',
  },
  {
    label: "ステータス",
    name: "status",
    field: "status",
    sortable: true,
    classes: 'tw-w-32',
    align: 'left',
  },
]);

// methods
const downloadShelters = (): void => {
  bosaiStore.downloadShelterList();
};

const getDisasterTypeDisplay = (disasterValue: any): any => {
  return TYPE_OF_DISASTERS[disasterValue];
};

const containedInDisasterTypes = (disasterValue: any): boolean => {
  return Object.keys(TYPE_OF_DISASTERS).includes(disasterValue + "");
};

const getShelterStatusDisplay = (statusValue: string): any => {
  return SHELTER_STATUS[Number(statusValue)];
};

const containedInShelterStatus = (status: string): boolean => {
  return SHELTER_STATUS[Number(status)] !== undefined;
};

const deleteSelectedShelters = (): void => {
  if (selectedShelters.value.length > 0) {
    $q.dialog({
      title: "施設削除確認",
      message: "この施設データを削除してもよろしいですか？",
      // type: "error",
      ok: { label: 'はい', color: 'negative' },
      cancel: true
    }).onOk(async () => {
      bosaiStore.deleteShelters(selectedShelters.value);
    });
  } else {
    $q.notify({
      type: "error",
      message: "施設情報をえらんでください！",
    });
  }
};

const deleteAllShelters = (): void => {
  $q.dialog({
    title: "施設削除確認",
    message: "全施設データを削除してもよろしいですか？",
    // type: "error",
    ok: { label: "はい", color: 'negative' },
    cancel: true,
  }).onOk(async () => {
    let response = await bosaiStore.deleteAllShelterData();
    if (response.result === 'OK') {
      $q.notify({
        message: '全削除の処理を開始しました。通知情報で実施ステータスをご確認ください。'
      });
    } else {
      $q.notify({
        type: 'error',
        message: response.error_message,
      });
    }
  });
};

const customSort = (items: any, sortBy: any, descending: any): any => {
  if (!isNullOrEmpty(sortBy)) {
    switch (sortBy) {
      case "shelterName":
        items.sort((a: any, b: any) => {
          if (!descending) {
            return a.shelterName.localeCompare(b.shelterName);
          } else {
            return b.shelterName.localeCompare(a.shelterName);
          }
        });
      break;
      case "comments":
        items.sort((a: any, b: any) => {
          if (!descending) {
            return a.comments.localeCompare(b.comments);
          } else {
            return b.comments.localeCompare(a.comments);
          }
        });
      break;
      case "status":
        items.sort((a: any, b: any) => {
          if (!descending) {
            return a.status > b.status ? 1 : a.status < b.status ? -1 : 0;
          } else {
            return a.status > b.status ? -1 : a.status < b.status ? 1 : 0;
          }
        });
      break;
      default:
      break;
    }
  }
  return items;
};

// computed
const isFetchingShelters = computed(() => bosaiStore.isFetchingShelters);
const isDownloadingShelters = computed(() => bosaiStore.isDownloadingShelters);

// const filteredHeaders = computed(() => {
//   return headers.value.filter(header => header.name !== 'select' || props.showFunctions);
// });

// watch
watch(
  () => selectedShelters.value, 
  (newVal) => {
    emits("selected", newVal);
  },
  { deep: true, }
);
</script>
