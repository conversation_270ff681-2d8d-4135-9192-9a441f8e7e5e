<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.shelter-number-row {
  display: table;
  text-align: center;
}
.shelter-number-row-content {
  display: table-cell;
}
.shelter-edit-content {
  margin-left: 1em;
  margin-right: 1em;
  max-height: 50em;
  overflow-y: scroll;
}
.shelter-status-select-field {
  margin-left: 8em;
  margin-right: 0.5em;
}
</style>

<template>
  <q-dialog scrollable persistent v-model="show" :max-width="600">
    <q-card class="tw-w-full">
      <q-bar class="bg-primary tw-h-1"></q-bar>
      <q-toolbar flat>
        <q-toolbar-title>避難所ステータス変更</q-toolbar-title>

        <q-space></q-space>

        <q-btn icon="mdi-close" flat round @click="closePopup">
        </q-btn>
      </q-toolbar>
      <div class="row">
        <div class="shelter-number-row-content col tw-text-center">
          <span class="text-h4">{{ shelters.length }}</span>
          <span>件の避難所が選択されています。</span>
        </div>
      </div>
      <div>
        <div class="justify-center shelter-edit-content row">
          <v-flex xs3 md3 lg3 v-for="(shelter, index) in shelters" v-bind:key="index">
            <ShelterDisplayBox :shelter="shelter" />
          </v-flex>
        </div>

        <q-card-actions class="tw-p-4" align="between">
          <div>
            <q-btn class="px-6 mr-2" @click="closePopup"> キャンセル </q-btn>
          </div>
          <div class="row">
            <q-select
              class="tw-w-32"
              :options="shelterStatuses"
              option-value="value"
              option-label="text"
              emit-value map-options
              v-model="selectedStatus"
              label="ステータス"
              dense
            >
            </q-select>
            <q-btn
              class="tw-mx-2 tw-w-24"
              color="primary"
              @click="changeShelterStatus"
              :disable="selectedStatus == null || selectedStatus == undefined || shelters.length == 0"
            >
              変更する
            </q-btn>
          </div>
        </q-card-actions>
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed,  } from 'vue';
import { cloneDeep } from "lodash";
import { useBosaiStore } from '@/stores/modules/bosai';
import ShelterDisplayBox from "@/pages/admin/BosaiSettings/components/ShelterDisplayBox.vue";

// store
const bosaiStore = useBosaiStore();

// emits 
const emits = defineEmits<{
  (event: 'close'): void;
}>();

// props
const props = defineProps<{
  visible: boolean,
  shelters: any[],
}>();

// data
const selectedStatus = ref<any>(null);
const shelterStatuses = ref<any>([
  {
    value: 0,
    text: "未開設",
  },
  {
    value: 1,
    text: "開設",
  },
  {
    value: 2,
    text: "満",
  },
]);

// methods
const changeShelterStatus = (): void => {
  var newShelters = cloneDeep(props.shelters);
  newShelters.forEach((elem) => {
    elem.status = selectedStatus.value;
  });
  var payload = {
    shelters: newShelters,
  };
  bosaiStore.editShelters(payload);
  closePopup();
};

const closePopup = (): void => {
  show.value = false;
  selectedStatus.value = null;
};

// computed
const show = computed({
  get(): any {
    return props.visible;
  },
  set(value: boolean): void {
    if (!value) {
      emits("close");
    }
  },
});
</script>
