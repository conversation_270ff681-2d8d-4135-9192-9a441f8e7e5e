<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.caption-text {
  color: gray;
}

.shelter-import-filepicker-box,
.shelter-loading,
.shelter-import-actions {
  display: grid;
}

.shelter-import-filepicker {
  margin-left: 1em;
  margin-right: 1em;
}

.shelter-preview-table-display {
  margin: 0.5em;
  max-height: 20em;
  overflow-y: scroll;
}
</style>

<template>
  <div>
    <div v-if="!isImportingShelters">
      <div class="shelter-import-filepicker-box" v-cloak @drop.prevent="addDropFile" @dragover.prevent>
        <q-file ref="fileInput" class="shelter-file-input" v-model="fileData" label="ファイルを選択" accept=".csv" clearable>
          <template v-slot:prepend>
            <q-icon name="attach_file" />
          </template>
        </q-file>
        <div class="justify-center row">
          <span class="text-caption caption-text">ファイル形式：CSV</span>
        </div>
      </div>
      <q-card class="shelter-preview-table-display" v-if="tempImportResults.length > 0">
        <ShelterTable :shelters="tempImportResults" />
      </q-card>
      <div v-if="showSample" class="justify-center row tw-pt-4">
        <a class="text-primary tw-underline" :href="baseSampleFileUrl + sampleFileName" target="_blank"
          download>サンプルファイルのダウンロード</a>
      </div>
      <q-card-actions class="shelter-import-actions tw-p-4" align="center">
        <div v-if="showSample" class="justify-center row">
          <span class="text-caption caption-text">上書きを保存されます</span>
        </div>
        <div>
          <q-btn color="primary" class="tw-w-32 tw-mt-4" @click="importFile" :disable="disableImport">
            <!-- :style="
              hasActionPermission('hideButton', 'BosaiSettings_ShelterImportContent_ImportFile')
                ? hideButtonPermissionStyle()
                : ''
            " -->
            登録する
          </q-btn>
        </div>
      </q-card-actions>
    </div>
    <div v-else>
      <div class="justify-center shelter-loading row">
        <div md="1" class="col tw-pb-4">
          <q-spinner color="primary" :size="50"></q-spinner>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, } from 'vue';
import { useQuasar } from 'quasar';
import { useBosaiStore } from '@/stores/modules/bosai';
import { SHELTER_CSV_TYPES } from "@/stores/modules/bosai/bosai.constants";
import { Buffer } from "buffer";
import * as jschardet from "jschardet";
import iconv from 'iconv-lite';
import papa from "papaparse";
import ShelterTable from "@/pages/admin/BosaiSettings/components/ShelterTable.vue";
import languageEncoding from "detect-file-encoding-and-language";


// store
const bosaiStore = useBosaiStore();

// emits 
const emits = defineEmits<{
  (event: 'fileSelectStart'): void;
  (event: 'fileSelectSuccess'): void;
  (event: 'importSuccess'): void;
}>();

const $q = useQuasar();

// props
const props = withDefaults(defineProps<
  {
    showSample: boolean,
    deleteSelectedFile: boolean,
  }>(),
  {
    showSample: true,
    deleteSelectedFile: false,
  });

// data
const fileData = ref<any>(null);
const tempImportResults = ref<any>([]);
const disableImport = ref<boolean>(true);
const sampleFileName = ref<string>("/施設サンプルファイル.csv");
const baseSampleFileUrl = ref<string>(window.location.origin);
const csvHeader = ref<any>(null);
const csvErrors = ref<any>([]);

// methods
// const uploadShelterCSV = store.uploadShelterCsvFile;
const addDropFile = (e: any): void => {
  fileData.value = e.dataTransfer.files[0];
};

const fileParsingError = (): void => {
  fileData.value = null;
  tempImportResults.value = [];
  let dataFormattingError = false;
  let dataError = null;

  for (let errorIndex = 0; errorIndex < csvErrors.value.length; errorIndex++) {
    const errorMessage = csvErrors.value[errorIndex].toString();

    if (
        errorMessage.includes("住所") ||
        errorMessage.includes("最終更新日時") ||
        errorMessage.includes("ステータス") ||
        errorMessage.includes("団体コード") ||
        errorMessage.includes("ＩＤ1") ||
        errorMessage.includes("ＩＤ2") ||
        errorMessage.includes("避難所")
    ) {
      dataFormattingError = true;
      dataError = errorMessage;
      break;
    }
  }

  bosaiStore.setImportingSheltersError(dataFormattingError ? dataError : "ファイル読み込んでながらエラー発生しました。ファイルの形式は正しくない。");
  bosaiStore.setIsImportingShelters(false);
};

const mapCSVItemToDisplay = (elem: any): any => {
  if (elem.length > 0 && elem[0] == "") {
    return null;
  }
  let keysArray = Object.keys(SHELTER_CSV_TYPES);
  let validIntAlts = ["", "-"];
  //Change the value in if statement to dynamically get index
  let shelterName =
    csvHeader.value.indexOf("避難先名称（避難所・避難場所）") == -1
      ? keysArray.indexOf("避難先名称（避難所・避難場所）")
      : csvHeader.value.indexOf("避難先名称（避難所・避難場所）");
  let shelterAddress =
    csvHeader.value.indexOf("住所") == -1 ? keysArray.indexOf("住所") : csvHeader.value.indexOf("住所");
  let latitude = csvHeader.value.indexOf("緯度") == -1 ? keysArray.indexOf("緯度") : csvHeader.value.indexOf("緯度");
  let longitude = csvHeader.value.indexOf("経度") == -1 ? keysArray.indexOf("経度") : csvHeader.value.indexOf("経度");
  let disasterTypes =
    csvHeader.value.indexOf("災害種別") == -1 ? keysArray.indexOf("災害種別") : csvHeader.value.indexOf("災害種別");
  let status =
    csvHeader.value.indexOf("ステータス") == -1
      ? keysArray.indexOf("ステータス")
      : csvHeader.value.indexOf("ステータス");
  let comments = csvHeader.value.indexOf("備考") == -1 ? keysArray.indexOf("備考") : csvHeader.value.indexOf("備考");

  let disasterValuesSplit = elem[disasterTypes].split("");
  let governmentCode =
      csvHeader.value.indexOf("ＩＤ1(団体コード)") == -1
          ? keysArray.indexOf("ＩＤ1(団体コード)")
          : csvHeader.value.indexOf("ＩＤ1(団体コード)");
  let localShelterId =
      csvHeader.value.indexOf("ＩＤ2(避難所の通し番号)") == -1
          ? keysArray.indexOf("ＩＤ2(避難所の通し番号)")
          : csvHeader.value.indexOf("ＩＤ2(避難所の通し番号)");
  let lastModified =
      csvHeader.value.indexOf("最終更新日時") == -1
          ? keysArray.indexOf("最終更新日時")
          : csvHeader.value.indexOf("最終更新日時");

  if (!elem[shelterAddress]?.trim()) {
    throw new Error("住所が空です。住所は必須項目です。");
  }

  if (!elem[governmentCode]?.trim()) {
    throw new Error("ＩＤ1(団体コード)が空です。ＩＤ1(団体コード)は必須項目です。");
  }

  // Validate 最終更新日時 (Last Modified Date) format is yyyy/MM/dd HH:mm
  const dateRegex = /^\d{4}\/\d{2}\/\d{2} \d{2}:\d{2}$/;
  if (elem[lastModified] && !dateRegex.test(elem[lastModified])) {
    throw new Error(`最終更新日時の形式が正しくありません。形式は yyyy/MM/dd HH:mm である必要があります。入力値: ${elem[lastModified]}`);
  }

  // Validate ステータス (Status) is 0, 1, or 2
  if (elem[status] && ![0, 1, 2].includes(parseInt(elem[status]))) {
    throw new Error(`ステータスの値が無効です。0, 1, または 2 である必要があります。入力値: ${elem[status]}`);
  }

  // Validate ＩＤ1(団体コード) and ＩＤ2(避難所の通し番号) are numeric
  if (elem[governmentCode] && !/^\d+$/.test(elem[governmentCode])) {
    throw new Error(`ＩＤ1(団体コード)は数字である必要があります。入力値: ${elem[governmentCode]}`);
  }
  if (elem[localShelterId] && !/^\d+$/.test(elem[localShelterId])) {
    throw new Error(`ＩＤ2(避難所の通し番号)は数字である必要があります。入力値: ${elem[localShelterId]}`);
  }

  //Run validation for front end files
  for (let index = 0; index < keysArray.length; index++) {
    let columnIndex =
      csvHeader.value.indexOf(keysArray[index]) == -1 ? index : csvHeader.value.indexOf(keysArray[index]);
    switch (SHELTER_CSV_TYPES[keysArray[index]]) {
      case "string":
        if (typeof elem[columnIndex] !== "string") {
          throw new Error(elem[shelterName] + "という避難所の" + csvHeader.value[columnIndex] + "値は正しくない。");
        }
        break;
      case "float":
        try {
          let result = parseFloat(elem[columnIndex]);
          if (isNaN(result)) {
            throw new Error(
              elem[shelterName] + "という避難所の" + csvHeader.value[columnIndex] + "値は正しくない。"
            );
          }
        } catch (err: any) {
          throw new Error(elem[shelterName] + "という避難所の" + csvHeader.value[columnIndex] + "値は正しくない。");
        }
        break;
      case "int":
        try {
          if (!validIntAlts.includes(elem[columnIndex])) {
            let result = parseInt(elem[columnIndex]);
            if (isNaN(result)) {
              throw new Error(
                elem[shelterName] + "という避難所の" + csvHeader.value[columnIndex] + "値は正しくない。"
              );
            }
          }
        } catch (err: any) {
          throw new Error(elem[shelterName] + "という避難所の" + csvHeader.value[columnIndex] + "値は正しくない。");
        }
        break;
      case "intReq":
        try {
          if (elem[columnIndex] == "") {
            throw new Error(
              elem[shelterName] + "という避難所の" + csvHeader.value[columnIndex] + "値は正しくない。"
            );
          } else {
            let result = parseInt(elem[columnIndex]);
            if (isNaN(result)) {
              throw new Error(
                elem[shelterName] + "という避難所の" + csvHeader.value[columnIndex] + "値は正しくない。"
              );
            }
          }
        } catch (err: any) {
          throw new Error(elem[shelterName] + "という避難所の" + csvHeader.value[columnIndex] + "値は正しくない。");
        }
        break;
      case "date":
        try {
          let result = Date.parse(elem[columnIndex]);
          if (isNaN(result)) {
            throw new Error(
              elem[shelterName] + "という避難所の" + csvHeader.value[columnIndex] + "値は正しくない。"
            );
          }
        } catch (err: any) {
          throw new Error(elem[shelterName] + "という避難所の" + csvHeader.value[columnIndex] + "値は正しくない。");
        }
        break;
    }
  }

  let disasterValues = [] as any;
  disasterValuesSplit.forEach((element: any) => {
    disasterValues.push(parseInt(element));
  });
  let seenValues = {} as any;

  let mappedItem = {
    shelterName: elem[shelterName],
    address: elem[shelterAddress],
    latitude: elem[latitude],
    longitude: elem[longitude],
    comments: elem[comments],
    status: elem[status],
    typeDisaster: disasterValues.filter(function (item: any) {
      return seenValues.hasOwnProperty(item) ? false : (seenValues[item] = true);
    }),
  };

  return mappedItem;
};

const importFile = (): void => {
  let payload = {
    fileData: fileData.value,
  };
  bosaiStore.uploadShelterCsvFile(payload);
};

const startingFileSelect = (): void => {
  emits("fileSelectStart");
};

const fileSelectSuccess = (): void => {
  emits("fileSelectSuccess");
};

const importSuccess = (): void => {
  fileData.value = null;
  tempImportResults.value = [];
  disableImport.value = true;
  emits("importSuccess");
};

// computed
const isImportingShelters = computed(() => bosaiStore.isImportingShelters);
const importingSheltersError = computed(() => bosaiStore.importingSheltersError);
const importingSheltersSuccess = computed(() => bosaiStore.importingSheltersSuccess);

// watch
watch(
  () => props.deleteSelectedFile,
  (val) => {
    if (val) {
      fileData.value = null;
      tempImportResults.value = [];
    }
  });

watch(
  () => fileData.value,
  (val) => {
    startingFileSelect();
    tempImportResults.value = [];
    disableImport.value = true;
    csvErrors.value = [];
    if (val) {
      bosaiStore.setIsImportingShelters(true);
      try {
        let reader = new FileReader();
        reader.onload = async (e) => {
          try {
            let rawCSVText: any = e.target?.result;
            let checkDecoding = await languageEncoding(fileData.value);
            if (checkDecoding.encoding == "UTF-8") {
              let utfReader = new FileReader();
              utfReader.onload = (ev) => {
                try {
                  let rawCSVTextUtf8: any = ev.target?.result;
                  let csvParseResult = papa.parse(rawCSVTextUtf8, {
                    header: false,
                    skipEmptyLines: true
                  });
                  let count = 0;
                  let parseResults = [];
                  for (const elem of csvParseResult.data) {
                    if (count != 0) {
                      let tempResult = mapCSVItemToDisplay(elem);
                      if (tempResult) {
                        parseResults.push(tempResult);
                      }
                    } else {
                      csvHeader.value = elem;
                    }
                    count++;
                  }
                  tempImportResults.value = parseResults;
                  disableImport.value = false;

                  bosaiStore.setIsImportingShelters(false);
                  fileSelectSuccess();
                } catch (err: any) {
                  csvErrors.value.push(err);
                  fileParsingError();
                }
              };
              utfReader.readAsText(val, "utf_8");
            } else {
              let shift_jis_text = new TextDecoder("shift-jis").decode(rawCSVText);
              // console.log(shift_jis_text);
              let csvParseResult: any = papa.parse(shift_jis_text, {
                header: false,
                skipEmptyLines: true,
              });
              let count = 0;
              let parseResults = [];
              for (const elem of csvParseResult.data) {
                if (count != 0) {
                  let tempResult = mapCSVItemToDisplay(elem);
                  if (tempResult) {
                    parseResults.push(tempResult);
                  }
                } else {
                  csvHeader.value = elem;
                }
                count++;
              }
              tempImportResults.value = parseResults;
              disableImport.value = false;

              bosaiStore.setIsImportingShelters(false);
              fileSelectSuccess();
            }
          } catch (err: any) {
            console.error(err);
            csvErrors.value.push(err);
            fileParsingError();
          }
        };
        reader.readAsArrayBuffer(val);
      } catch (error: any) {
        fileParsingError();
      }
    }
  });

watch(
    () => importingSheltersError.value,
    (val) => {
      if (val) {
        if (val.toString().includes("CSVファイルの処理中にエラーが発生しました：")) {
          $q.notify({
            message: val,
            type: "error",
            icon: "warning",
            iconColor: "yellow",
            timeout: 0,
            actions: [
              {
                label: "閉じる",
                color: "white",
                textColor: "primary",
                handler: () => { }
              },
            ],
          });
        } else {
          $q.notify({
            message: val,
            type: "error",
          });
        }
        bosaiStore.setImportingSheltersError(null); // Clear the error after displaying
      }
    }
);

watch(
  () => importingSheltersSuccess.value,
  (val) => {
    if (val) {
      importSuccess();
    }
  });
</script>
