<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<style scoped>
.no-padding {
  padding: 0;
}
</style>

<template>
  <div class="tw-py-4 col-12">
    <Alert v-if="errorMessage != ''" :message="errorMessage" type="error"/>
    <!-- 見出し -->
    <div class="tw-pl-3 tw-pt-0 row">
      <span class="text-blue-grey tw-font-semibold">公開設定</span>
      <q-icon
        class="tw-mx-1 text-grey-7"
        name="mdi-information-outline"
        size="sm"
      >
      <q-tooltip right>
        <span>予約可能なカレンダーの期間および予約受付開始のタイミングを設定することができます。</span>
      </q-tooltip>
      </q-icon>
    </div>

    <!-- 公開設定のラジオボタン -->
    <div class="row">
      <div class="tw-pt-2 col-auto">
        <q-radio 
          v-model="reservationControlType"
          hide-details 
          label="全期間" 
          val="0" 
        />
      </div>
      <div class="tw-pt-2 col-auto">
        <q-radio
          v-model="reservationControlType"
          hide-details
          label="期間設定" 
          val="1" 
        >
        </q-radio>
      </div>
    </div>

    <!-- 公開設定の設定テーブル -->
    <div class="row tw-pt-4" v-show="reservationControlType === '1'">
      <div class="no-padding col-12">
        <!-- 追加・削除ボタン -->
        <div class="row">
          <div class="tw-pr-0 col-auto">
            <q-btn 
              color="primary"
              outline
              @click="newControl"
            >
              <q-icon size="20" left name="mdi-plus"></q-icon>
              予約可能日制御を追加
            </q-btn>
          </div>
          <div class="tw-pl-2 col-auto">
            <q-btn 
              color="negative"
              outline
              @click="doneDelete"
            >
              <q-icon size="20" left name="mdi-trash-can-outline"></q-icon>
              実行済みを削除
            </q-btn>
          </div>
        </div>
        <!-- テーブル -->
        <div class="row">
          <div style="max-width:1240px;" class="tw-pt-3 col-12">
            <div style="position:relative;">
              <div class="controlListDiv">
                <q-table 
                  v-model="reservationControlList"
                  :rows="reservationControlList" 
                  dense flat hide-bottom virtual-scroll
                  :rows-per-page-options="[0]"
                  style="max-height: 300px;"
                >
                  <template v-slot:top>
                    <q-card class="settingDateRange col-12 bg-blue-1" flat>
                      <q-icon
                        color="blue-9"
                        class="tw-mx-1"
                        name="mdi-information-outline"
                        size="sm"
                      >
                        <q-tooltip right color="blue">
                          <span>予約可能なカレンダーの期間および予約受付開始のタイミングを設定することができます。</span>
                        </q-tooltip>
                      </q-icon>
                      <span>現在設定中の予約可能日：{{ settingDateRange }}</span>
                    </q-card>
                  </template>

                  <template v-slot:header>
                    <td></td>
                    <td class="tw-px-1 text-blue-grey">受付開始日</td>
                    <td class="tw-px-1 text-blue-grey">開始時間</td>
                    <td class="tw-px-1 text-blue-grey" colspan="5">公開期間</td>
                  </template>

                  <template v-slot:body="item">
                    <tr>

                      <td class="">
                        <div
                          class="itemIcon"
                          v-bind:class="{
                            deleteItem: item.row.delete,
                            normalItem: !item.row.delete,
                            addItem: item.row.isAdding,
                            executedIcon: item.row.isExecuted == 1 && !item.row.delete
                          }"
                        >
                          {{ itemIcon(item.row) }}
                        </div>
                      </td>
                      <td class="">
                        <q-input
                          v-model="item.row.execDate"
                          outlined dense clearable hide-details="auto"
                          :disable="isExecuted(item.row)"
                        ></q-input>
                        <q-menu>
                          <q-date
                            v-model="item.row.execDate"
                            :options="receptionDateOption"
                          ></q-date>
                        </q-menu>
                      </td>
                      <td class="">
                        <q-input
                          v-model="item.row.execTime"
                          outlined dense clearable hide-details="auto"
                          :disabled="isExecuted(item.row)"
                          type="time"
                        ></q-input>
                      </td>
                      <td class="">
                        <q-input
                          v-model="item.row.startDate"
                          outlined dense clearable hide-details="auto"
                          :disabled="isExecuted(item.row)"
                        ></q-input>
                        <q-menu>
                          <q-date
                            v-model="item.row.startDate"
                            :options="(date) => openDateFromOption(date, item.row.endDate)"
                          >
                          </q-date>
                        </q-menu>
                      </td>
                      <td>～</td>
                      <td class="">
                        <q-input
                          v-model="item.row.endDate"
                          outlined dense clearable hide-details="auto"
                          :disabled="isExecuted(item.row)"
                        ></q-input>
                        <q-menu>
                          <q-date
                            v-model="item.row.endDate"
                            :options="(date) => openDateToOption(date, item.row.startDate)"
                          >
                          </q-date>
                        </q-menu>
                      </td>
                      <td class="">
                        <q-btn
                          color="negative"
                          style="max-width:40px;min-width:40px;"
                          @click="deleteControl(item.rowIndex);">
                          <q-icon size="18" class="icon_button" name="mdi-close"></q-icon>
                        </q-btn>
                      </td>
                    </tr>
                  </template>
                </q-table>
                <div v-if="reservationControlCount() == 0" class="tw-ml-10 tw-mt-3" style="color:#aaa;">設定がありません</div>
                <q-separator></q-separator>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 見出し -->
    <div class="tw-pl-3 tw-pt-6 row">
      <span class="text-blue-grey tw-font-semibold">受付条件</span>
      <q-icon
        class="tw-mx-1 text-grey-7"
        name="mdi-information-outline"
        size="sm"
      >
        <q-tooltip right>
          <span>公開設定で設定された予約可能なカレンダーの期間で、予約・キャンセル・変更の操作ができる条件を設定することができます。</span>
        </q-tooltip>
      </q-icon>
    </div>

    <!-- 受付条件の入力値 -->
    <div class="row tw-px-3">
      <div class="tw-pt-2 tw-pb-0 col">
        <div class="tw-pt-0">
          <div v-show="reservationControlType === '1'" class="row">
            <label>予約受付</label>
          </div>
          <div v-show="reservationControlType === '1'" class="row">
            <div class="tw-pt-2 tw-px-0 col-2">
              <q-input
                v-model="reservationPossibleStart"
                :clearable="false"
                type="number"
                :min="0"
                :max="99"
                @keydown="numberInputOnKeyDown"
                outlined
                background-color="white"
                dense
                hide-details>
              </q-input>
            </div>
            <div class="tw-pt-2 tw-px-0 tw-mt-n1 col-auto">
              <q-item-label header class="tw-px-1">営業日後</q-item-label>
            </div>
            <div class="tw-pt-2 tw-px-0 tw-mt-n1 col-auto">
              <q-item-label header class="pl-3">～</q-item-label>
            </div>
            <div class="tw-pt-2 tw-px-0 col-2">
              <q-input
                v-model="reservationPossibleMonths"
                :clearable="false"
                type="number"
                :min="0"
                :max="99"
                @keydown="numberInputOnKeyDown"
                outlined
                background-color="white"
                dense
                hide-details>
              </q-input>
            </div>
            <div class="tw-pt-2 tw-px-0 tw-mt-n1 col-auto">
              <q-item-label header class="tw-px-1">ヶ月末日まで</q-item-label>
            </div>
          </div>
          <div :class="reservationCancelLimit != 0  ?'tw-pb-2':'' " class="row">
            <label>キャンセル・変更</label>
          </div>
          <div class="row">
            <q-item-label header v-if="reservationCancelLimit == 0" class="tw-px-0">※0で登録された場合は変更・キャンセル期限のチェックは行われません</q-item-label>
          </div>  
          <div class="row">
            <div class="tw-pl-0 tw-pr-1 tw-pt-0 col-2">
              <q-input
                v-model="reservationCancelLimit"
                :clearable="false"
                type="number"
                :min="0"
                :max="99"
                @keydown="numberInputOnKeyDown"
                outlined
                background-color="white"
                dense
                hide-details>
              </q-input>
            </div>
            <div class="tw-px-0 tw-pt-0 tw-mt-n1 col-3">
              <q-item-label header class="tw-px-0">営業日前まで</q-item-label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 保存ボタン -->
    <div class="tw-flex tw-justify-end tw-pr-3 row">
      <q-btn
        color="primary"
        :loading="isLoading"
        :disable="isClickable"
        @click="onUpdateReservationPeriod(false)"
      >保存
      </q-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onBeforeMount } from 'vue';
import { useQuasar } from 'quasar';
import { useCalenderStore } from '@/stores/modules/calendar';
import { CALENDAR_SETTING_KEY } from '@/stores/modules/calendar/calendar.constants';
import dayjs from "dayjs";
import { cloneDeep } from "lodash";
import Alert from '@/components/common/Alert.vue';

// store
const calendarStore = useCalenderStore();

const $q = useQuasar();

// data
const reservationControlList = ref<any[]>([]);
const reservationControlType = ref<string>(CALENDAR_SETTING_KEY.RESERVATION_CONTROL_TYPE);
const reservationPossibleStart = ref<any>(CALENDAR_SETTING_KEY.RESERVATION_POSSIBLE_START);
const reservationPossibleMonths = ref<any>(CALENDAR_SETTING_KEY.RESERVATION_POSSIBLE_MONTHS);
const reservationCancelLimit = ref<any>(CALENDAR_SETTING_KEY.RESERVATION_CANCEL_LIMIT);
const selectedCalendarId = ref<string>('');
const settingDateRange = ref<string>('');
const workId = ref<number>(0);
const errorMessage = ref<string>("");

// computed
const updatingCalendar = computed(() => calendarStore.updatingCalendar);
const reservationControlData = computed(() => calendarStore.reservationControlInfo);
const currentCalendar = computed(() => calendarStore.currentCalendar);
const updatingCalendarError = computed(() => calendarStore.updatingCalendarError);
const upsertReservationItemInfoError = computed(() => calendarStore.upsertReservationItemInfoError);

const isLoading = computed((): boolean => {
  return updatingCalendar.value;
});

const isClickable = computed((): boolean => {
  const cancelLimitIsPositiveNumber = checkIsPositiveNumber(reservationCancelLimit.value);
  if (reservationControlType.value === '0') {
    return !cancelLimitIsPositiveNumber;
  } else {
    const possibleStartIsPositiveNumber = checkIsPositiveNumber(reservationPossibleStart.value);
    const possibleMonthsIsPositiveNumber = checkIsPositiveNumber(reservationPossibleMonths.value);
    return !possibleStartIsPositiveNumber || !possibleMonthsIsPositiveNumber || !cancelLimitIsPositiveNumber;
  }
});

// watch
watch(
  () => currentCalendar.value,
  (calendar) => {
    setData(calendar);
  }
);

watch(
  () => reservationControlData.value,
  (newVal) => {
    reservationControlList.value = JSON.parse(JSON.stringify(newVal.data));
    workId.value = newVal.id;
  }
);

watch(
  () => upsertReservationItemInfoError.value,
  (newVal) => {
    if (newVal) {
      errorMessage.value = newVal;
      $q.notify({ message: newVal, type: "error" });
    }
  }
);

watch(
  () => updatingCalendarError.value,
  (newVal) => {
    if (newVal) {
      errorMessage.value = newVal;
      $q.notify({ message: newVal, type: "error" });
    }
  }
);

// methods
const numberInputOnKeyDown = (e: any): boolean => {
  var char = e.key;
  if (char === '-' || char === '.' || char === 'e') {
    e.preventDefault();
    return false;
  } else {
    return true;
  }
};

const onUpdateReservationPeriod = async (isDoneDelete: any) => {
  try {
    if (reservationControlType.value === '1') {
      let params: any = [];
      let errMsg = '';
      let newControlCnt = 0;
      reservationControlList.value.forEach((rc: any, index: any) => {
        if (errMsg !== '') {
          return;
        }
        errMsg = validateReservationControl(rc, index);

        let setWorkId = rc.workId;
        if (!isDoneDelete && !setWorkId && !rc.delete) {
          newControlCnt += 1;
          setWorkId = workId.value + newControlCnt;
        } else {
          if (isDoneDelete) {
            if (!(Number(rc.isExecuted) === 1 && rc.delete)) {
              setWorkId = null;
            }
          }
        }
        if (setWorkId) {
          params.push({
            sortKey: `${selectedCalendarId.value}#${setWorkId}`,
            calendarId: selectedCalendarId.value,
            workId: setWorkId,
            execDate: rc.execDate,
            execTime: rc.execTime,
            startDate: rc.startDate,
            endDate: rc.endDate,
            isExecuted: rc.isExecuted,
            delete: rc.delete,
          });
        }
      });
      if (errMsg === '') {
        const isContinuous = validateConsecutiveDate();
        !isContinuous && (errMsg = '公開開始日と公開終了日が連続した日付になっていません。');
      }
      if (errMsg !== '') {
        errorMessage.value = errMsg;
        $q.notify({ message: errMsg });
        return false;
      }
      params.length > 0 && (await calendarStore.actionUpsertReservationControlInfo(params));
    }
    const payload = createPayloadOfUpdateCalendarSettings();
    await calendarStore.updateCalendarSettings(payload);
    if (!updatingCalendarError.value) {
      errorMessage.value = "";
      $q.notify({
        message: "カレンダー設定を変更しました。"
      });
    }
  } catch (error: any) {
    console.error("an error");
    console.error(error);
  }
};

const validateReservationControl = (rc: any, index: any): string => {
  if (!rc.delete && !rc.deletedAt && !rc.isExecuted) {
    if (!rc.execDate) { return '受付開始日は必須項目です。'; }
    if (!rc.execTime) { return '開始時間は必須項目です。'; }
    if (!rc.startDate) { return '公開期間は必須項目です。'; }
    const startDateTimeIsPast = validatePastDateTime(rc);
    if (startDateTimeIsPast) { return '開始時間は現在より後の時間を指定してください。'; }
  }

  if (rc.startDate && rc.endDate) {
    const startDt = dayjs(rc.startDate);
    const endDt = dayjs(rc.endDate);
    if (startDt.isValid() && endDt.isValid() && endDt.isBefore(startDt)) {
      return '公開期間の終了日には開始日以降の日付を指定して下さい。';
    }
  }

  const haveDuplicateReservation = reservationControlList.value.some((rc2: any, index2: any) => {
    const isDuplicate = rc.startDate <= rc2.endDate && rc.endDate >= rc2.startDate;
    return (index !== index2)
      && (!rc.delete && !rc2.delete)
      && (isDuplicate);
  });

  if (haveDuplicateReservation) {
    return '重複している予約可能期間があります。';
  }

  return '';
};

const validatePastDateTime = (rc: any): boolean => {
  // 開始時間が過去日になっているかチェック
  const execDateTime = dayjs(`${rc.execDate} ${rc.execTime}:00`).toDate();
  const now = new Date();
  const isPast = execDateTime < now;

  return isPast;
};

const newControl = (): void => {
  reservationControlList.value.unshift(
    {
      sortKey: null,
      calendarId: selectedCalendarId.value,
      workId: null,
      execDate: '',
      execTime: '00:00',
      startDate: '',
      endDate: '',
      isExecuted: 0,
      updatedAt: null,
      deletedAt: null,
      isAdding: true,
      delete: false
    }
  );
};

const doneDelete = (): void => {
  for (let control of reservationControlList.value) {
    if (control.isExecuted === 1) {
      control.delete = true;
    }
  }
  onUpdateReservationPeriod(true);
};

const isExecuted = (rc: any): boolean => {
  return rc.isExecuted === 1;
};

const itemIcon = (rc: any): string => {
  if (rc.isAdding) {
    return '新規追加';
  } else {
    if (rc.isExecuted === 1 && !rc.delete) {
      return '実行済み';
    } else {
      return '削除';
    }
  }
};

const deleteControl = (index: any): void => {
  reservationControlList.value[index].delete = reservationControlList.value[index].delete ? false : true;
  if (reservationControlList.value[index].isAdding && reservationControlList.value[index].delete) {
    reservationControlList.value.splice(index, 1);
  }
};

const reservationControlCount = (): number => {
  let ControlList = reservationControlList.value;
  let wkCount = 0;
  ControlList.forEach(function(data: any){
    if (data.deletedAt == null && !data.delete) {
      wkCount += 1;
    }
  });
  return wkCount;
};

const validateConsecutiveDate = (): true => {
  const rcList = cloneDeep(reservationControlList.value);
  let isContinuous = true;
  
  if (rcList.length <= 1) {
    return isContinuous;
  }
  rcList.forEach((rc: any, index: any) => {
    let isFound = false;
    let targetIndex = index + 1;
    // 比較対象のレコードを探す
    while (!isFound && rcList[targetIndex]) {
      if (!rcList[targetIndex].deletedAt && !(rcList[targetIndex].delete && rcList[targetIndex].isAdding)) {
        isFound = true;
      } else {
        targetIndex ++;
      }
    }
    // 連続した日付になっているかチェック
    if (isFound) {
      const startDate = dayjs(rc.startDate);
      const endDate = dayjs(rcList[targetIndex].endDate);
      isContinuous = startDate.diff(endDate, 'days') === 1;
    }
  });
  return isContinuous;
};

const countReservationControlList = (): number => {
  let reservatinControlCount = 0;
  if (reservationControlList.value.length >= 0) {
    reservationControlList.value.forEach((rcItem: any) => {
      if (!rcItem.deletedAt && !rcItem.delete) {
        reservatinControlCount ++;
      }
    });
  }

  return reservatinControlCount;
};

const createPayloadOfUpdateCalendarSettings = (): any => {
  const payload = cloneDeep(currentCalendar.value);
  payload['reservationControlType'] = parseInt(reservationControlType.value);
  checkIsPositiveNumber(reservationCancelLimit.value) &&
    (payload['reservationCancelLimit'] = parseInt(reservationCancelLimit.value));
  checkIsPositiveNumber(reservationPossibleStart.value) &&
    (payload['reservationPossibleStart'] = parseInt(reservationPossibleStart.value));
  checkIsPositiveNumber(reservationPossibleMonths.value) &&
    (payload['reservationPossibleMonths'] = parseInt(reservationPossibleMonths.value));
  if (countReservationControlList() === 0) {
    payload["startDate"] = null;
    payload["endDate"] = null;
  }

  return payload;
};

const checkIsPositiveNumber = (value: any): boolean => {
  return !isNaN(value) && value >= 0 && value !== '' && value !== null;
};

const receptionDateOption = (date: any) => {
  return date >= new Date().toLocaleDateString();
};

const openDateFromOption = (date: any, toDate: any) => {
  if (!toDate) return receptionDateOption(date);
  return date <= toDate;
};

const openDateToOption = (date: any, fromDate: any) => {
  if (!fromDate) return receptionDateOption(date);
  return date >= fromDate;
};

const setData = (calendar: any) => {
  reservationControlType.value = calendar['reservationControlType'] ?
      calendar['reservationControlType'].toString() : CALENDAR_SETTING_KEY.RESERVATION_CONTROL_TYPE;
    reservationControlType.value === "2" && (reservationControlType.value = '1');
    reservationPossibleStart.value = calendar['reservationPossibleStart'] ?
    calendar['reservationPossibleStart'] : CALENDAR_SETTING_KEY.RESERVATION_POSSIBLE_START;
    reservationPossibleMonths.value = calendar['reservationPossibleMonths'] ?
    calendar['reservationPossibleMonths'] : CALENDAR_SETTING_KEY.RESERVATION_POSSIBLE_MONTHS;
    reservationCancelLimit.value = calendar['reservationCancelLimit'] ?
    calendar['reservationCancelLimit'] : CALENDAR_SETTING_KEY.RESERVATION_CANCEL_LIMIT;
    // 予約可能日(開始)
    if ("startDate" in calendar) {
      settingDateRange.value = calendar.startDate ? calendar.startDate + ' ～ ' : '設定なし';
    } else {
      settingDateRange.value = '設定なし';
    }
    // 予約可能日(終了)
    if ("endDate" in calendar) {
      settingDateRange.value += calendar.endDate ? calendar.endDate : '';
    }
    selectedCalendarId.value = calendar.sortKey;
    calendarStore.actionGetReservationControlInfo(selectedCalendarId.value);
};

// hooks
onBeforeMount(() => {
  setData(currentCalendar.value);
});
</script>

<style scoped>
  .settingDateRange {
    color: #0D47A1;
    font-size:14px;
    padding:8px 0;
  }
  .itemIcon {
    padding:4px 3px;
    border-radius: 12px;
    width: 75px;
    text-align: center;
    color: #fff;
  }
  .deleteItem {
    background-color: #f5554a;
  }
  .normalItem {
    background-color: #e5e5e5;
  }
  .addItem {
    background-color: #E3F2FD;
    color: #0D47A1;
  }
  .executedIcon {
    background-color: #E9FAED;
    color: #0A6426;
  }
</style>

<style>
  #transparentBg{
    display: none;
    position: fixed;
    z-index:998;
    min-height:100vh;
    min-width:100vw;
    background-color: transparent;
    top: 0;
    left: 0;
  }
  .dispBg{
    display: block!important;
  }
</style>