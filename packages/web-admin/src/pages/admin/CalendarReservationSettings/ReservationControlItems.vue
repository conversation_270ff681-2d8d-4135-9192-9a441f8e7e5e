<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div class="col-12">
    <div v-if="usePayment" class="tw-py-3 row">
      <div class="tw-pb-2 col-12">
        <label class="text-blue-grey tw-font-semibold">
          決済機能連携
        </label>
      </div>
      <div class="col-12">
        <RadioRaymentAlignmentComponent :paymentAlignment="paymentAlignment" :disabled="hasSomeItem || hadSelectService" @change="v => paymentAlignment = v" />
      </div>
    </div>

    <div v-if="isPaymentAlignment" class="tw-py-3 row">
      <div class="tw-pb-2 col-12">
        <label class="text-blue-grey tw-font-semibold">
          連携対象サービス
        </label>
      </div>
      <div class="col-4">
        <SelectService :selectedService="selectedService" :serviceList="serviceList" @change="v => selectedService = v" />
      </div>
    </div>

    <div class="tw-pt-3 row" :class="{ 'tw-pb-3': hasContorlItems }">
      <div class="tw-pb-2 col-12" >
        <label class="text-blue-grey tw-font-semibold">
          予約管理項目
        </label>
        <q-icon
          class="text-grey-7"
          name="mdi-information-outline"
          size="sm"
        >
          <q-tooltip right>
            <span>予約時に予約枠を複数押さえるための設定ができます。<br>例）ジムを予約する場合に、プールを4人分予約する。</span>
          </q-tooltip>
        </q-icon>
      </div>
      <div class="col-12">
        <div style="position:relative;">
          <div class="control-list-div">
            <q-table 
              :rows="displayItems"
              hide-header
              hide-bottom
              dense
              :pagination="{ rowsPerPage: 0 }"
            >
              <template v-slot:body="item">
                <tr>
                  <td class="tw-pl-0 tw-pr-2 col">
                    <SelectProduct v-if="isPaymentAlignment" :selected-product="item.row.productId" :productList="productList" :notSelectableIds="alreadySelectedProductIds"
                    @change="v => item.row.productId = v.productId"/>
                    <InputItemName v-else v-model="item.row.itemName" />
                  </td>
                  <td class="px-2"  width="90px">
                    <IconStatus :item="item" />
                  </td>
                  <td class="px-2 blue-grey--text" width="72px">
                    消費枠数
                  </td>
                  <td class="pr-2 pl-0">
                    <InputCost :cost="item.row.cost" @input="item.row.cost = $event" />
                  </td>
                  <td class="pl-2 pr-0" width="40px">
                    <q-btn
                      color="negative"
                      class="px-0 my-2 d-block mx-0"
                      style="max-width:40px;min-width:40px;"
                      @click="deleteItem(item.rowIndex)">
                      <q-icon size="18" class="icon_button" name="mdi-close"></q-icon>
                    </q-btn>
                  </td>
                </tr>
              </template>
            </q-table>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div>
        <q-btn color="primary"
          outline
          @click="newItem"
        >
          <q-icon left name="mdi-plus"></q-icon>
          予約項目を追加
        </q-btn>
      </div>
      <div v-if="isPaymentAlignment" class="tw-ml-2">
        <q-btn color="primary"
          :disable="isDisabledBulkSetButton"
          outline
          @click="bulkSetItems"
        >
          <q-icon left name="mdi-plus"></q-icon>
          予約項目一括セット
        </q-btn>
      </div>
    </div>

    <div class="tw-justify-end tw-pr-3 tw-pb-3 tw-pt-3 row">
      <q-btn
        color="default"
        outline
        :loading="isLoading"
        @click="reset"
        class="tw-mx-3 text-grey-7"
      >
        リセット
      </q-btn>

      <q-btn
        color="primary"
        :loading="isLoading"
        :disable="isDisabledSaveButton"
        @click="onUpdateCalendarItemSettings"
      >
        保存
      </q-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch,onBeforeMount } from 'vue';
import { useQuasar } from 'quasar';
import { useCalenderStore } from '@/stores/modules/calendar';
import { PaymentAlignment } from '@/stores/modules/calendar/calendar.types';
import { CALENDAR_SETTING_KEY } from "@/stores/modules/calendar/calendar.constants";
import { isNullOrEmpty } from "@/utils/stringUtils";
import { useRoute } from 'vue-router';
import _, { cloneDeep } from 'lodash';
import dayjs from 'dayjs';
import RadioRaymentAlignmentComponent from './components/ReservationControlItems/RadioPaymentAlignment.vue';
import SelectService from './components/ReservationControlItems/SelectService.vue';
import SelectProduct from './components/ReservationControlItems/SelectProduct.vue';
import InputItemName from './components/ReservationControlItems/InputItemName.vue';
import IconStatus from './components/ReservationControlItems/IconStatus.vue';
import InputCost from './components/ReservationControlItems/InputCost.vue';

// store
const calendarStore = useCalenderStore();

// route
const route = useRoute();

const $q = useQuasar();

// data
const reservationItemSetting = ref<any>([]);
const selectedCalendarId = ref<string>('');
const itemId = ref<number>(0);
const paymentItemId = ref<number>(0);
const paymentAlignment = ref<any>(CALENDAR_SETTING_KEY.PAYMENT_ALIGNMENT as PaymentAlignment);
const selectedService = ref<string | null>(null);
const reservationPaymentItems = ref<any>([]);
const productList = ref<any>([]);

// computed
const updatingCalendar = computed(() => calendarStore.updatingCalendar);
const currentCalendar = computed(() => calendarStore.currentCalendar);
const reservationItemData = computed(() => calendarStore.reservationItemInfo);
const reservationPaymentItemsData = computed(() => calendarStore.reservationPaymentItemsInfo);
const updatingCalendarError = computed(() => calendarStore.updatingCalendarError);
const upsertReservationItemInfoError = computed(() => calendarStore.upsertReservationItemInfoError);
const serviceList = computed(() => calendarStore.serviceList);
const productListMapByService = computed(() => calendarStore.productListMapByService);

const hasSomeItem = computed((): boolean => {
  return items.value.length > 0;
});

const isLoading = computed((): boolean => {
  return updatingCalendar.value;
});

const alreadySelectedProductIds = computed(() => {
  return reservationPaymentItems.value
    .filter((item: any) => !item.deletedAt)
    .map((item: any) => item.productId)
});

const isPaymentAlignment = computed((): boolean => {
  return paymentAlignment.value === '1' && usePayment.value;
});

const usePayment = computed((): boolean => {
  return import.meta.env.VITE_USE_PAYMENT === '1';
});

const items = computed(() => {
  return isPaymentAlignment.value ? reservationPaymentItems.value : reservationItemSetting.value;
});

const displayItems = computed((): any[] => {
  return items.value.filter((item: any) => !item.deletedAt && !(item.delete && item.isAdding));
});

const hasContorlItems = computed((): boolean => {
  return displayItems.value.length > 0;
});

const isDisabledBulkSetButton = computed((): boolean => {
  return !selectedService.value || alreadySelectedProductIds.value.length === productList.value;
});

const isDisabledSaveButton = computed((): boolean => {
  if (!isPaymentAlignment.value && items.value.length === 0) {
    return true;
  }
  return items.value.some((item: any) => {
    const target = isPaymentAlignment.value ? item?.productId : item?.itemName;
    return (isNullOrEmpty(target) || item.cost <= 0) && !item?.delete;
  });
});

const defaultItem = computed(() => {
  return {
    sortKey: null,
    calendarId: selectedCalendarId.value,
    categroyId: categoryId.value,
    itemId: null,
    itemName: '',
    cost: 1,
    updatedAt: null,
    deletedAt: null,
    isAdding: true,
    delete: false
  };
});

const categoryId = computed(() => {
  return route.params.categoryId;
});

const hadSelectService = computed(() => {
  return Boolean(currentCalendar.value.paymentServiceSortKey)
});

// watch
watch(
  () => paymentAlignment.value,
  (newVal) => {
  if (newVal === '0') {
    reservationItemSetting.value = []
  }
});

watch(
  () => reservationItemData.value,
  (newVal) => {
    if (newVal) {
      const data = JSON.parse(JSON.stringify(newVal.data));
      reservationItemSetting.value = _.clone(data).sort(orderByItemIdAsk);
      itemId.value = newVal.id;
    }
  }
);

watch(
  () => reservationPaymentItemsData.value,
  (newVal) => {
    if (newVal) {
      const data = _.cloneDeep(newVal.data);
      reservationPaymentItems.value = data;
      paymentItemId.value = newVal.maxSerialNumber;
    }
  }
);

watch(
  () => updatingCalendarError.value,
  (newVal) => {
    if (newVal) {
      $q.notify({ message: newVal, type: "error" });
    }
  }
);

watch(
  () => selectedService.value,
  async (newVal) => {
    console.log('Selected service ID:', newVal);
    const now = dayjs().unix();
    reservationPaymentItems.value = reservationPaymentItems.value
      .filter((item: any) =>  !item.isAdding)
      .map((item: any) => ({ ...item, delete: true, deletedAt: now }));
    const cachedList = newVal ? productListMapByService.value[newVal] : null
    if (newVal && (!cachedList || cachedList.length === 0)) {
      console.log('Loading products for service', newVal);
      await calendarStore.actionGetPorductList(newVal);
    }
    setProductsList();
  }
);

watch(
  () => productListMapByService.value,
  () => {
    setProductsList();
  }
);

// methods
const newItem = (): void => {
  const item = cloneDeep(defaultItem.value);
  items.value.push(item);
};

const deleteItem = (index: any): void => {
  items.value[index].delete = !items.value[index].delete;
  if (items.value[index].isAdding && items.value[index].delete) {
    items.value.splice(index, 1);
  }
};

const bulkSetItems = (): void => {
  if (productList.value.length === 0) {
    $q.notify({
      message: '「連携対象サービス」に販売中の商品が設定されていません。',
      type: 'error',
    });
    return;
  }
  const selectedProductIds = alreadySelectedProductIds.value;
  productList.value.forEach((product: any) => {
    // NOTE: 予約項目に設定済みの商品はセットしない
    if (selectedProductIds.includes(product.productId)) {
      return;
    }
    const item = cloneDeep(defaultItem.value);
    reservationPaymentItems.value.push({ ...item, productId: product.productId });
  });
};

const validateExistCategory = (categoryId: any): void => {
  if (!categoryId) {
    throw 'このカレンダーは分類が設定されていない為、登録できません。';
  }
};

const validateItem = (item: any, index: number): void => {
  const isValid = isPaymentAlignment.value ? !!item.productId : !!item.itemName;
  if (!isValid) {
    throw isPaymentAlignment.value ? '予約項目名を選択してください。' : '予約項目名を入力してください。';
  }
  if (isPaymentAlignment.value) {
    validateDuplicateProduct(item, index);
  }
};

const validateDuplicateProduct = (item: any, index: number): void => {
  const isDuplicate = reservationPaymentItems.value.some((_item: any, i: any) => {
    return item.productId === _item.productId && index !== i;
  });
  if (isDuplicate) {
    throw '商品が重複しています。';
  }
};

const validateResevationItems = (): void => {
  items.value.forEach((item: any, i: any) => validateItem(item, i));
};

const validateForUpdateItems = (categoryId: any): void => {
  validateExistCategory(categoryId);
  validateResevationItems();
};

const createPayloadForUpdateItems = (): any[] => {
  const payload:any[] = [];
  let newItemCnt = 0;

  validateForUpdateItems(categoryId.value);
  items.value.forEach((item: any) => {
    let setItemId = item.id ? item.id.split('_')[1] : item.itemId;
    if (setItemId === null && !item.delete) {
      newItemCnt += 1;
      setItemId = (isPaymentAlignment.value ? paymentItemId.value : itemId.value) + newItemCnt;
    }
    if (setItemId) {
      const additionalParams = isPaymentAlignment.value
        ? { id: `${categoryId.value}_${setItemId}`, productId: item.productId, isDeleted: item.delete || false }
        : { sortKey: `${categoryId.value}_${setItemId}`, itemId: setItemId, itemName: item.itemName, delete: item.delete };
      payload.push({
        categoryId: categoryId.value,
        calendarId: selectedCalendarId.value,
        cost: Number(item.cost),
        serviceId: selectedService.value,
        updatedAt: item.updatedAt,
        deletedAt: item.deletedAt,
        ...additionalParams,
      });
    }
  });
  return payload;
};

const validateForUpdateCalendar = (): void => {
  if (!["0","1"].includes(paymentAlignment.value)) {
    throw '「決済機能連携」は「利用しない」か「利用する」のどちらを選択してください。';
  }
};

const createPayloadForUpdateCalendar = (): any => {
  validateForUpdateCalendar();
  const payload = {
    ...currentCalendar.value,
    usePaymentService: Number(paymentAlignment.value),
  };
  if (isPaymentAlignment.value) {
    payload['paymentServiceSortKey'] = selectedService.value;
  }
  return payload;
};

const onUpdateCalendarItemSettings = async (): Promise<void> => {
  try {
    if (usePayment.value) {
      const payloadForUpdateCalendar = createPayloadForUpdateCalendar();
      await calendarStore.updateCalendarSettings(payloadForUpdateCalendar);
    }
    const payloadForUpdateItems = createPayloadForUpdateItems();
    if (payloadForUpdateItems.length > 0) {
      isPaymentAlignment.value
        ? await calendarStore.actionUpsertCalenderPaymentItems(payloadForUpdateItems)
        : await calendarStore.actionUpsertReservationItemInfo(payloadForUpdateItems);

      const error = updatingCalendarError.value || upsertReservationItemInfoError.value;
      if (error) {
        throw error;
      }
      isPaymentAlignment.value
        ? await calendarStore.actionGetReservationPaymentItemsInfo(categoryId.value.toString())
        : await calendarStore.actionGetReservationItemInfo(categoryId.value.toString());
    }
    $q.notify({
      message: "カレンダー設定を変更しました。"
    });
  } catch (error: any) {
    $q.notify({
      message: error,
      type: 'error',
    });
  }
};

const orderByItemIdAsk = (a: any, b: any): number => {
  const idA = Number(a.itemId.value);
  const idB = Number(b.itemId.value);

  let comparison = 0;
  if (idA > idB) {
    comparison = 1;
  } else if (idA < idB) {
    comparison = -1;
  }
  return comparison;
};

const init = async (calendar: any) => {
  selectedCalendarId.value = calendar.sortKey;
  selectedService.value = cloneDeep(calendar.paymentServiceSortKey) || null;
  paymentAlignment.value = calendar.usePaymentService ? String(calendar.usePaymentService) : CALENDAR_SETTING_KEY.PAYMENT_ALIGNMENT;
  await calendarStore.actionGetReservationItemInfo(categoryId.value.toString());
  if (usePayment.value) {
    await calendarStore.actionGetServiceList();
    if (selectedService.value) {
      await calendarStore.actionGetPorductList(selectedService.value);
    }
    await calendarStore.actionGetReservationPaymentItemsInfo(categoryId.value.toString());
  }
  setProductsList();
};

const reset = async () => {
  await init(currentCalendar.value);
};

const setProductsList = () => {
  const map = productListMapByService.value || {};
  productList.value = selectedService.value ? (map[selectedService.value] || []) : []
};

// hooks
onBeforeMount(async () => {
  await init(currentCalendar.value);
});
</script>

<style scoped>
  .control-list-div {
    overflow-y :auto;
  }
</style>