<template>
  <div>
    <div class="tw-pl-3 tw-pt-3 row">
      <span class="text-blue-grey tw-font-semibold">予約確定時の補足メッセージ</span>
      <q-icon
        class="tw-mx-1 text-grey-7"
        name="mdi-information-outline"
        size="sm"
      >
        <q-tooltip right>
          <span>予約確定時にLINEのトークに送信される予約内容の通知メッセージに、<br>補足のメッセージを追加できます。</span>
        </q-tooltip>
      </q-icon>
    </div>

    <!-- 補足メッセージ入力項目 -->
    <div class="row">
      <div class="tw-py-2 col-12">
        <div v-for="(item, index) in calendarMessage" :key="index">
          <q-card class="tw-mb-7" flat bordered>
            <q-toolbar style="background-color:#F5F7F8;" dense>
              <q-btn-toggle 
                v-model="item.type" 
                flat 
                :options="[
                  { value: 'text', icon: 'mdi-message-text-outline'},
                  { value: 'image', icon: 'mdi-image-outline'},
                ]"
              >
              </q-btn-toggle>
              <q-space></q-space>
              <q-btn class="text-blue-grey" :value="3" flat @click="removeMessage(index)">
                <q-icon name="mdi-close"></q-icon>
              </q-btn>
            </q-toolbar>
            <q-input 
              type="textarea"
              outlined
              v-if="item.type === 'text'"
              class="tw-px-0 tw-my-0 tw-pb-0 reservationMessageTextArea"
              placeholder="メッセージを入力してください。"
              solo
              :rules="[rules.validTextLength]"
              counter
              maxlength="5000"
              v-model="calendarMessage[index].text"
              hint="文字数上限は5000文字です。"
            ></q-input>
            <input
              v-show="false"
              type="file"
              ref="inputFileRef"
              class="d-none"
              accept="image/png, image/jpeg"
              @change="onChangeImageMessage($event, index)"
            />
            <q-card v-if="item.type === 'image'" class="tw-m-0 reservationMessageImageArea" flat bordered>
              <div class="tw-mt-2 tw-p-3">
                <q-item-label class="text-grey-7 tw-flex tw-justify-center">
                  ファイル形式：JPG、JPEG、PNG／ファイルサイズ：1MB以下
                </q-item-label>
              </div>
              <q-page-container class="tw-p-3 tw-flex tw-justify-center row">
                <q-btn
                    v-if="!item.previewImageUrl"
                    color="primary"
                    class="tw-my-3"
                    @click="uploadImage(index)"
                >
                  <q-icon left name="mdi-upload-outline"></q-icon>
                  画像アップロード
                </q-btn>
                <Alert v-if="item.error" :message="item.error" class="tw-my-3 body-2 col-12" type="error">
                </Alert>
                <div v-if="item.previewImageUrl && !item.error" class="col-12">
                  <q-img class="tw-my-4" :src="item.previewImageUrl" style="max-height: 150px;" fit="contain"></q-img>
                  <div class="tw-flex tw-justify-center">
                    <q-btn color="negative" class="tw-my-2" @click="removeImage(index)">画像を削除 </q-btn>
                  </div>
                </div>
              </q-page-container>
            </q-card>
          </q-card>
        </div>
        <q-btn class="tw-mt-4" color="primary" outline @click="addMessage()" :disable="calendarMessage.length === 2">
          <q-icon name="mdi-plus"></q-icon>
          メッセージを追加
        </q-btn>
      </div>
    </div>

    <!-- 保存ボタン -->
    <div class="text-right flex-row-reverse tw-pr-3 tw-pb-3 tw-pt-5">
      <q-btn
        color="primary"
        :loading="isLoading"
        :disable="!canSave||isEmptyOfCalendarMessage"
        @click="onUpdateReservationMessage()"
      >保存
      </q-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch,onBeforeMount } from 'vue';
import { useQuasar } from 'quasar';
import { useCalenderStore } from '@/stores/modules/calendar';
import { cloneDeep, isEmpty } from "lodash";
import Alert from '@/components/common/Alert.vue';
const $q = useQuasar();

// store
const calendarStore = useCalenderStore();

// data
const calendarMessage = ref<any>([]);
const rules = ref<any>({
  validTextLength: (value: any) => (value || "").length <= 5000 || "5000文字以内で入力してください。",
});
const canSave = ref<boolean>(true);
const inputFileRef = ref();

// methods
const onUpdateReservationMessage = async (): Promise<void> => {
  try {
    let payload = cloneDeep(currentCalendar.value);
    if (Array.isArray(calendarMessage.value)) {
      payload["calendarMessage"] = calendarMessage.value;
    } else {
      payload["calendarMessage"] = [{type: "text", text: calendarMessage.value}];
    }

    await calendarStore.updateCalendarSettings(payload);
    if (!updatingCalendarError.value) {
      $q.notify({
        message: "カレンダー設定を変更しました。"
      });
      calendarStore.setCurrentCalendar(payload);
    }
  } catch (error: any) {
    console.error(error);
  }
};

const addMessage = (): void => {
  calendarMessage.value.push({type: "text", text: ""})
};

const removeMessage = (index: number): void => {
  calendarMessage.value = calendarMessage.value.filter((v: any, i: any) => index !== i);
};

const onChangeImageMessage = (event:any, index:number): void => {
  const file = event.target.files[0];
  const totalSizeMB = (file.size / Math.pow(1024, 2));
  let image = {};

  if (totalSizeMB > 1.0) {
    image = {
      originalContentUrl: "",
      previewImageUrl: "",
      file: null,
      error: "1MB 以下のファイルを選んでください。",
    };
  } else {
    if (file.type !== "image/png" && file.type !== "image/jpeg" && file.type !== "image/jpg") {
      image = {
        originalContentUrl: "",
        previewImageUrl: "",
        file: null,
        error: "JPG、JPEG、PNG の写真ファイルを選んでください。",
      };
    } else {
      image = {
        originalContentUrl: URL.createObjectURL(file),
        previewImageUrl: URL.createObjectURL(file),
        file: file,
        error: null,
      };
    }
  }
  calendarMessage.value[index] = {
    ...calendarMessage.value[index],
    ...image,
    type: "image",
  };
  inputFileRef.value[index].value = "";
};

const uploadImage = (index:number): void => {
  inputFileRef.value[index].click();
};

const removeImage = (index:number): void => {
  const image = {
    originalContentUrl: "",
    previewImageUrl: "",
    file: null,
    error: null,
  };
  calendarMessage.value[index] = {
    ...calendarMessage.value[index],
    ...image,
    type: "image",
  };
};

// computed
const updatingCalendar = computed(() => calendarStore.updatingCalendar);
const currentCalendar = computed(() => calendarStore.currentCalendar);
const updatingCalendarError = computed(() => calendarStore.updatingCalendarError);

const isLoading = computed((): any => {
  return updatingCalendar.value;
});

const isEmptyOfCalendarMessage = computed((): boolean => {
  if(calendarMessage.value.length!=0){
    return calendarMessage.value.find((obj: any) => obj.type=='text' && !obj.text) 
      || calendarMessage.value.find((obj: any) => obj.type == 'image' && (!obj.originalContentUrl || obj.originalContentUrl == null)) 
        ? true 
        : false;
  }else{
    return false;
  }
});

// watch
watch(
  () => currentCalendar.value,
  (calendar) => {
    if ("calendarMessage" in calendar) {
      if (Array.isArray(calendar['calendarMessage'])) {
        calendarMessage.value = cloneDeep(calendar['calendarMessage']);
      } else {
        calendarMessage.value = [{type: "text", text: ""}]
      }
    } else {
      calendarMessage.value = [];
    }
  }
);

watch(
  () => updatingCalendarError.value,
  (value) => {
    if (value) {
      $q.notify({message: value, type: "error"});
    }
  }
);

watch(
  () =>calendarMessage.value,
  (value) => {
    canSave.value = value.map((message: any) => {
      if (message.type === "text" && rules.value.validTextLength(message.text) === true) {
        return true;
      }
      if (message.type === "image" && !isEmpty(message.previewImageUrl) && !isEmpty(message.originalContentUrl)) {
        return true;
      }
      return false;
    }).some((valid: any) => valid === false) === false;
  },
  { deep: true }
);

// hooks
onBeforeMount(() => {
  if ("calendarMessage" in currentCalendar.value) {
    if (Array.isArray(currentCalendar.value['calendarMessage'])) {
      calendarMessage.value = cloneDeep(currentCalendar.value['calendarMessage']);
    } else {
      calendarMessage.value = [{
        type: "text",
        text: ""
      }];
    }
  } else {
    calendarMessage.value = [];
  }
});

</script>
<style>
.v-btn-toggle>.v-btn.v-btn--active.primary--text.activeToggle::before{
  content: none!important;
}

.reservationMessageTextArea .v-input__slot,.v-card.v-sheet.reservationMessageImageArea {
  border: solid 1px #CFD8DC;
  border-radius: 0px;
}
</style>