<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div class="col-12">
    <!-- 同時予約可能数ラベル -->
    <div class="tw-pl-3 tw-pt-3 row">
      <span class="text-blue-grey tw-font-semibold">同時予約可能数</span>
      <q-icon
        class="tw-mx-1 text-grey-7"
        name="mdi-information-outline"
        size="sm"
      >
        <q-tooltip right>
          <span>1人ユーザーが予約できる枠の上限を設定することができます。</span>
        </q-tooltip>
      </q-icon>
    </div>
    <div class="tw-pl-0 tw-pt-1 row">
      <span class="description-text">※上限を設定しない場合は「0」としてください。</span>
    </div>

    <!-- 同時予約可能数入力項目 -->
    <div class="row tw-px-3">
      <div class="tw-pb-0 col">
        <div class="tw-py-4">
          <div class="row text-blue-grey tw-pb-4">
            <div class="row col-5 tw-items-center">
              <div class="tw-px-0 col-4">
                <label>予約可能期間中</label>
              </div>
              <div class="tw-px-4 col-5">
                <q-input
                  v-model="reservationMaxCount"
                  :clearable="false"
                  type="number"
                  :min="0"
                  :max="99"
                  @keydown="numberInputOnKeyDown"
                  outlined
                  dense
                  hide-details
                >
                </q-input>
              </div>
              <div class="tw-px-0 tw-mt-n1 col-auto">
                <label class="tw-px-0">件</label>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-5 row tw-items-center">
              <div class="tw-px-0 align-center tw-flex tw-mt-n1 text-blue-grey col-4">
                <label>同日内</label>
              </div>
              <div class="tw-px-4 col-5">
                <q-input
                  v-model="reservationMaxCountOfDay"
                  :clearable="false"
                  type="number"
                  :min="0"
                  :max="99"
                  @keydown="numberInputOnKeyDown"
                  outlined
                  background-color="white"
                  dense
                  hide-details>
                </q-input>
              </div>
              <div class="tw-px-0 tw-mt-n1 col-auto">
                <label class="tw-px-0 text-blue-grey">件</label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 予約可能年齢制限ラベル -->
    <div class="tw-pl-3 tw-pt-3 row">
      <span class="text-blue-grey tw-font-semibold">予約可能年齢制限</span>
      <q-icon
        class="tw-mx-1 text-grey-7"
        name="mdi-information-outline"
        size="sm"
      >
        <q-tooltip right>
          <span>予約可能な年齢を設定することができます。</span>
        </q-tooltip>
      </q-icon>
    </div>

    <!-- 予約可能年齢制限入力項目 -->
    <div class="row tw-pt-2">
      <div class="tw-pb-0 col">
        <q-radio
          v-for="(key, index) in Object.keys(controlTypes)"
          :key="index"
          v-model="reservableAge.controlType"
          :label="controlTypes[key]"
          :val="key"
          class="tw-mr-3 text-grey-8"
          row
          hide-details
        >
        </q-radio>
        <div class="tw-py-1 flex-column">
          <p class="description-text">{{ descriptionOfReservableAge.baseText }}</p>
          <p class="description-text">{{ descriptionOfReservableAge.detailedText }}</p>
          <p class="description-text">{{ descriptionOfReservableAge.exampleText }}</p>
        </div>
        <div v-show="isShowReservableAgeSettings" class="tw-py-4">
          <div class="row">
            <label class="text-blue-grey">予約可能年齢</label>
          </div>
          <div v-for="(setting, index) in reservableAge.settings" :key="index" align="center" class="row tw-items-center">
            <div class="tw-pl-0 tw-pr-1 col-2">
              <q-input
                dense
                hide-details
                outlined
                type="number"
                v-model="setting.start"
                :clearable="false"
                :min="0"
                :max="200"
                @keydown="numberInputOnKeyDown"
              ></q-input>
            </div>
            <span class="description-text pa-0">歳</span>
            <div class="tw-px-4 col-auto">
              <q-item-label header class="px-0">~</q-item-label>
            </div>
            <div class="tw-pl-0 tw-pr-1 col-2">
              <q-input
                dense
                hide-details
                outlined
                type="number"
                v-model="setting.end"
                :clearable="false"
                :min="0"
                :max="200"
                @keydown="numberInputOnKeyDown"
              ></q-input>
            </div>
            <span class="description-text pa-0">歳まで</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 保存ボタン -->
    <div class="tw-flex tw-justify-end tw-pr-3 tw-pb-3">
      <q-btn
        color="primary"
        height="44"
        :loading="isLoading"
        :disabled="!isClickable"
        @click="onUpdateReservationLimits()"
      >保存
      </q-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch,onBeforeMount } from 'vue';
import { useQuasar } from 'quasar';
import { useCalenderStore } from '@/stores/modules/calendar';
import { ReservableAge } from '@/stores/modules/calendar/calendar.types';
import { CALENDAR_SETTING_KEY } from '@/stores/modules/calendar/calendar.constants';
import { ReservationLimits, ControlTypeOfReservableAge } from '@/stores/modules/calendar/calendar.model';
import { cloneDeep } from 'lodash';

const $q = useQuasar();

// store
const calendarStore = useCalenderStore();

// data
const reservationMaxCount = ref<any>(CALENDAR_SETTING_KEY.RESERVATION_MAX_COUNT);
const reservationMaxCountOfDay = ref<any>(CALENDAR_SETTING_KEY.RESERVATION_MAX_COUNT_OF_DAY);
const reservableAge = ref<ReservableAge<number | string>>(CALENDAR_SETTING_KEY.RESERVABLE_AGE as ReservableAge<number | string>);
const controlTypes = ref<any>({
  '2': '特殊設定',
  '1': '満年齢',
  '0': '設定なし',
});

// computed
const updatingCalendar = computed(() => calendarStore.updatingCalendar);
const currentCalendar = computed(() => calendarStore.currentCalendar);
const updatingCalendarError = computed(() => calendarStore.updatingCalendarError);

const isLoading = computed((): any => {
  return updatingCalendar.value;
});

const isClickable = computed((): boolean => {
  const reservationLimits: ReservationLimits = buildReservationLimits();
  return reservationLimits.isValid();
});

const isShowReservableAgeSettings = computed((): boolean => {
  const reservationLimits: ReservationLimits = buildReservationLimits();
  return !reservationLimits.controlTypeIs('allAge');
});

const descriptionOfReservableAge = computed((): { baseText: string, detailedText: string, exampleText: string } => {
  let baseText = '※0~200まで設定可能です。予約当日（予定日）の満年齢でチェックします。';
  let detailedText = '';
  let exampleText = '';
  switch (Number(reservableAge.value.controlType)) {
    case ControlTypeOfReservableAge.fullAge: {
      detailedText = '※誕生日前日の満了をもって加齢するとみなします。';
      exampleText = '　例: 1歳-1歳で設定した場合は1歳の誕生日から2歳の誕生日の前日まで予約可能です。';
      break;
    }
    case ControlTypeOfReservableAge.specialSetting: {
      detailedText = '※誕生日前日の開始をもって加齢するとみなします。新型コロナワクチン接種予約での推奨設定です。';
      exampleText = '　例: 1歳-1歳で設定した場合は1歳の誕生日の前日から2歳の誕生日の前々日まで予約可能です。';
      break;
    }
    default: {
      baseText = '';
    }
  }
  return { baseText, detailedText, exampleText };
});

// watch
watch(
  () => currentCalendar.value,
  (calendar) => {
    const maxCount = calendar.reservationMaxCount ?
      calendar.reservationMaxCount : CALENDAR_SETTING_KEY.RESERVATION_MAX_COUNT;
    const maxCountOfDay = calendar.reservationMaxCountOfDay ?
      calendar.reservationMaxCountOfDay : CALENDAR_SETTING_KEY.RESERVATION_MAX_COUNT_OF_DAY;
    const age = cloneDeep(calendar.reservableAge) || CALENDAR_SETTING_KEY.RESERVABLE_AGE;

    const reservationLimits = new ReservationLimits({
      reservationMaxCount: maxCount,
      reservationMaxCountOfDay: maxCountOfDay,
      reservableAge: age,
    });

    const toInput = reservationLimits.toInput();
    reservationMaxCount.value = toInput.reservationMaxCount;
    reservationMaxCountOfDay.value = toInput.reservationMaxCountOfDay;
    reservableAge.value = toInput.reservableAge ?? CALENDAR_SETTING_KEY.RESERVABLE_AGE;
  }
);

watch(
  () => updatingCalendarError.value,
  (value) => {
    if(value) {
      $q.notify({ message: value, type: "error" });
    }
  }
);

// methods
const numberInputOnKeyDown = (e: any): boolean => {
  const char = e.key;
  if (char === '-' || char === '.' || char === 'e') {
    e.preventDefault();
    return false;
  } else {
    return true;
  }
};

const onUpdateReservationLimits = async (): Promise<void> => {
  try {
    const reservationLimits: ReservationLimits = buildReservationLimits();
    reservationLimits.validate();
    const payload = {
      ...currentCalendar.value,
      ...reservationLimits.toPayload(currentCalendar.value.reservableAge),
    };

    await calendarStore.updateCalendarSettings(payload);
    if (!updatingCalendarError.value) {
      $q.notify({
        message: "カレンダー設定を変更しました。"
      });
      calendarStore.setCurrentCalendar(payload);
    }
  } catch (error: any) {
    $q.notify({
      type: 'error',
      message: error.errorMessage || error
    });
  }
};

const buildReservationLimits = (): ReservationLimits => {
  return new ReservationLimits({
    reservationMaxCount: reservationMaxCount.value,
    reservationMaxCountOfDay: reservationMaxCountOfDay.value,
    reservableAge: reservableAge.value,
  });
};

// hooks
onBeforeMount(() => {
  const maxCount = currentCalendar.value.reservationMaxCount
    ? currentCalendar.value.reservationMaxCount 
    : CALENDAR_SETTING_KEY.RESERVATION_MAX_COUNT;
  const maxCountOfDay = currentCalendar.value.reservationMaxCountOfDay
    ? currentCalendar.value.reservationMaxCountOfDay 
    : CALENDAR_SETTING_KEY.RESERVATION_MAX_COUNT_OF_DAY;
  const age = cloneDeep(currentCalendar.value.reservableAge) || CALENDAR_SETTING_KEY.RESERVABLE_AGE;

  const reservationLimits = new ReservationLimits({
    reservationMaxCount: maxCount,
    reservationMaxCountOfDay: maxCountOfDay,
    reservableAge: age
  });

  const toInput = reservationLimits.toInput();
  reservationMaxCount.value = toInput.reservationMaxCount;
  reservationMaxCountOfDay.value = toInput.reservationMaxCountOfDay;
  reservableAge.value = toInput.reservableAge ?? CALENDAR_SETTING_KEY.RESERVABLE_AGE;
});
</script>

<style scoped>
  .description-text {
    color: rgba(0,0,0,.6);
    font-size: .875rem;
    font-weight: 400;
    padding-left: 12px;
  }
</style>

