<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div>
    <q-radio
      v-for="(key, index) in Object.keys(paymentAlignmentTypes)"
      :key="index"

      v-model="paymentAlignmentInput"
      :val="key"
      :label="paymentAlignmentTypes[key]"
      :disable="disabled"
      hide-details
    >
    </q-radio>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

// emits 
const emits = defineEmits<{
  (event: 'change', payload: any): void;
}>();

// props
const props = withDefaults(
  defineProps<{
    disabled?: boolean,
    paymentAlignment: string,
  }>(),
  {
    disabled: false,
  }
);

// data
const paymentAlignmentTypes = ref<any>({
  '0': '利用しない',
  '1': '利用する',
});

// computed
const paymentAlignmentInput = computed({
  get(): string {
    return props.paymentAlignment;
  },
  set(value: string): void {
    emits('change', value);
  },
});
</script>