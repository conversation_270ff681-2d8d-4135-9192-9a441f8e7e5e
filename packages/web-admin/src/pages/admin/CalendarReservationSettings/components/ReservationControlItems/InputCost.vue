<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <q-input
    :class="classes"
    :clearable="false"
    :min="1"
    :max="99"
    v-model="costInput"
    dense
    hide-details="auto"
    outlined
    type="number"
    @keypress="rangeInputKeyDown($event)"
  ></q-input>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// emits 
const emits = defineEmits<{
  (event: 'input', payload: any): void;
}>();

// props
const props = withDefaults(
  defineProps<{
    cost: any,
    classes?: string,
  }>(),
  {
    classes: '',
  }
);
// methods
const rangeInputKeyDown = (evt: any): void => {
  ["e", "E", "+", "-", "."].includes(evt.key) && evt.preventDefault();
};

// computed
const costInput = computed({
  get(): string {
    return props.cost;
  },
  set(cost: string) {
    emits('input', cost);
  }
});
</script>