<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <q-input
    v-model="itemNameInput"
    clearable
    dense
    hide-details="auto"
    outlined
  ></q-input>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// emits 
const emits = defineEmits<{
  (event: 'input', payload: any): void;
}>();

// props
const props = withDefaults(
  defineProps<{
    itemName?: string,
  }>(),
  {
    itemName: '',
  }
);

// computed
const itemNameInput = computed({
  get(): string | null {
    return props.itemName;
  },
  set(itemName: string | null): void {
    emits('input', itemName);
  },
});
</script>