<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <q-select
    :options="selectableProducts"
    option-label="productName"
    option-value="productId"
    v-model="selectedProductInput"
    dense
    elevation="0"
    hide-details
    outlined
    solo
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';

// emits 
const emits = defineEmits<{
  (event: 'change', payload: any): void;
}>();

// props
const props = withDefaults(
  defineProps<{
    selectedProduct?: any | null,
    productList: any[],
    notSelectableIds?: any[],
  }>(),
  {
    selectedProduct: null,
    notSelectableIds: [] as any,
  }
);

// computed
const selectableProducts = computed(() => {
  return props.productList.filter(p => {
    const id = p.productId;
    return !props.notSelectableIds?.includes(id) || id === props.selectedProduct?.productId
  });
});

const selectedProductInput = computed({
  get() {
    return props.productList.find(v => v.productId === props.selectedProduct);
  },
  set(value: any): void {
    emits('change', value || null);
  },
});
</script>