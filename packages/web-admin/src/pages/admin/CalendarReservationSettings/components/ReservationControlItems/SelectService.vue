<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <q-select
    :options="selectableServiceList"
    option-label="serviceName"
    option-value="sortKey"
    v-model="selectedServiceInput"
    clearable
    dense
    hide-details
    outlined
    solo
    :disable="disabled"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { RESERVATION_SERVICE_TYPES } from '@/stores/modules/payment/payment.constants';



// old imports
// 旧インポート
/*import { Service } from '@/store/modules/payment/payment.types';
*/

// emits 
const emits = defineEmits<{
  (event: 'change', payload: any): void;
}>();

// props
const props = withDefaults(
  defineProps<{
    selectedService?: string | null,
    serviceList: any[],
    disabled?: boolean,
  }>(),
  {
    selectedService: null,
  }
);

// computed
const selectedServiceInput = computed({
  get(): string | null {
    return props.selectedService ? props.serviceList.find(item => item.sortKey === props.selectedService) : null;
  },
  set(service: any): void {
    emits('change', service?.sortKey || null);
  },
});

const selectableServiceList = computed(() => {
  return props.serviceList.filter(s => s.sortKey === props.selectedService || s.reservationServiceType === Number(RESERVATION_SERVICE_TYPES.APPLICABLE));
});
</script>