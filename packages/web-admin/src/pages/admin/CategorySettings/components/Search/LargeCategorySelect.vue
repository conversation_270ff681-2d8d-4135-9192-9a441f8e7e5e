<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div>
    <q-select 
      v-model="compSelectedLargeCategory"
      :options="msCategoriesTreeLarge"
      option-label="name"
      option-value="id"
      class="tw-my-2"
      outlined use-input dense 
      :placeholder="placeholder"
      @filter="filterFn"
      @input-value="inputFn"
      fill-input
      hide-selected
      clearable
      clear-icon="close"
    >
    </q-select>
  </div>
</template>

<script setup lang="ts">
import { useCalenderStore } from '@/stores/modules/calendar';
import { computed, onMounted, ref, watch, } from 'vue';

// store
const calendarStore = useCalenderStore();

// data
const options = ref();

// computed
const msCategoriesTreeLarge = computed(() => calendarStore.categoriesCriteriaTreeLarge);
const selectedLargeCategoryCriteria = computed(() => calendarStore.selectedLargeCategoryCriteria);

const placeholder = computed(() => {
  return msCategoriesTreeLarge.value.length ? "大分類を選択" : "-----"
});

const compSelectedLargeCategory = computed({
  get(): any {
    return selectedLargeCategoryCriteria.value || null;
  },
  set(newValue: any): void {
    calendarStore.setCategoriesLargeCriteria(newValue);
    calendarStore.actionSetCalendarDataOfCategoriesMediumCriteria(newValue);
    calendarStore.setCalendarDataOfCategoriesCriteriaSmall([])
    calendarStore.setCategoriesMediumCriteria(null);
    calendarStore.setCategoriesSmallCriteria(null)
  },
});

// watch
watch(
  () => msCategoriesTreeLarge.value,
  (newVal) => {
    // 大分類一覧が更新されたときに大分類が選択されている場合は、中分類の選択肢一覧を更新する
    if (newVal && selectedLargeCategoryCriteria.value) {
      calendarStore.actionSetCalendarDataOfCategoriesMediumCriteria(selectedLargeCategoryCriteria.value);
    }
    options.value = newVal;
  }
);

// methods
const filterFn = (value: string, update: any) => {
  if (value === '') {
    update(() => {
      options.value = msCategoriesTreeLarge.value;
    });
    return;
  }
  else {
    update(() => {
      value = value.toLowerCase();
      options.value = msCategoriesTreeLarge.value.filter((elm: any) => 
        elm.name.toLowerCase().indexOf(value) > -1
      );
    });
  }
};

const inputFn = (val: any) => {
  if (!val) {
    compSelectedLargeCategory.value = val;
  }
};

// hooks
onMounted(async () => {
  await calendarStore.actionSetCalendarDataOfCategoriesCriteriaTree();
  options.value = msCategoriesTreeLarge.value;
});
</script>