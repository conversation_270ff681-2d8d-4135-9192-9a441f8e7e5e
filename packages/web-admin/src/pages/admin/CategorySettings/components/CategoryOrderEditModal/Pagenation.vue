<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div
    align="center"
    class="tw-px-6 tw-py-2 tw-m-0 pagenation row tw-flex tw-justify-between"
  >
    <!-- 大分類一覧表示時 -->
    <p v-if="isEditingLargeCategoryOrder" class="pagenation-properties">大分類一覧</p>
    <!-- 中分類一覧表示時 -->
    <p v-else-if="isEditingMediumCategoryOrder" class="pagenation-properties">
      <a @click="setEditingCategoryTypeToLarge()">{{ selectedLargeCategoryNameProp }}</a>
      <span class="tw-px-3">/</span>
      <span>中分類一覧</span>
    </p>
    <!-- 小分類一覧表示時 -->
    <p v-else-if="isEditingSmallCategoryOrder" class="pagenation-properties">
      <a @click="setEditingCategoryTypeToLarge()">{{ selectedLargeCategoryNameProp }}</a>
      <span class="tw-px-3">/</span>
      <a @click="setEditingCategoryTypeToMedium()">{{ selectedMediumCategoryNameProp }}</a>
      <span class="tw-px-3">/</span>
      <span>小分類一覧</span>
    </p>
    <q-btn
      class="button-sort tw-px-3 tw-py-2"
      color="primary"
      @click="onClickOrderByAlphabetical()"
    >
      50音順で並び替え
    </q-btn>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { EDITING_CATEGORY_TYPES } from "@/stores/modules/calendar/calendar.constants";

// emits 
const emits = defineEmits<{
  (event: 'setEditingCategoryTypeToLarge'): void;
  (event: 'setEditingCategoryTypeToMedium'): void;
  (event: 'onClickOrderByAlphabetical'): void;
}>();

// props
const props = defineProps<{
  editingCategoryTypeProp: string,
  selectedLargeCategoryNameProp: string,
  selectedMediumCategoryNameProp: string,
}>();

// computed
const isEditingLargeCategoryOrder = computed((): boolean => {
  return props.editingCategoryTypeProp === EDITING_CATEGORY_TYPES.LARGE;
});

const isEditingMediumCategoryOrder = computed((): boolean => {
  return props.editingCategoryTypeProp === EDITING_CATEGORY_TYPES.MEDIUM;
});

const isEditingSmallCategoryOrder = computed((): boolean => {
  return props.editingCategoryTypeProp === EDITING_CATEGORY_TYPES.SMALL;
});

// methods
const setEditingCategoryTypeToLarge = (): void => {
  emits('setEditingCategoryTypeToLarge');
};

const setEditingCategoryTypeToMedium = (): void => {
  emits('setEditingCategoryTypeToMedium');
};

const onClickOrderByAlphabetical = (): void => {
  emits('onClickOrderByAlphabetical');
};
</script>

<style scoped>
.button-sort {
  width: 140px;
  height: 28px !important;
  font-size: 12px;
  line-height: 100%;
  text-align: center;
  letter-spacing: 0.533333px;
  color: #FFFFFF;
}
.pagenation {
  height: 44px;
  width: 560px;
  background: #F5F7F8;
}
.pagenation-properties {
  font-size: 14px;
  line-height: 170%;
  letter-spacing: 0.285714px;
  margin: 0;
  color: #607D8B;
}
.pagenation-properties a {
  text-decoration-line: underline;
}
.button-sort,
.pagenation-properties {
  font-family: Poppins;
}
</style>