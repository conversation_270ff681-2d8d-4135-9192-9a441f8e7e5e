<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div class="col">
    <div class="tw-pl-3 tw-pt-1 row tw-items-center">
      <span>項目名を設定</span>
      <q-icon
        class="tw-mx-1"
        name="mdi-information-outline"
        size="sm"
        color="grey-7"
      >
        <q-tooltip>
          <span>ユーザーが予約作業をする際の帳票上で、分類選択の際に表示される名称を設定できます。</span>
        </q-tooltip>
      </q-icon>
    </div>
    <div class="row tw-pt-4">
      <div :class="'col-' + gridCols.input" class="tw-px-2">
        <q-input
          v-model="tag1"
          class="tw-my-2"
          :rules="tag1Rules"
          solo
          single-line
          outlined
          dense
          flat
          hide-details="auto"
          :disable="false"
          clearable
        >
        </q-input>
      </div>
 
      <div :class="'col-' + gridCols.input" class="tw-px-2">
        <q-input
          v-model="tag2"
          class="my-2"
          :rules="tag2Rules"
          solo
          single-line
          outlined
          dense
          flat
          hide-details="auto"
          :disable="false"
          clearable
        ></q-input>
      </div>

      <div :class="'col-' + gridCols.input" class="tw-px-2">
        <q-input
          v-model="tag3"
          class="my-2"
          solo
          single-line
          outlined
          dense
          flat
          hide-details="auto"
          :disable="false"
          clearable
        ></q-input>
      </div>
    </div> 
    
    <Alert
      v-if="showError"
      class="tw-mt-4"
      type="error"
      :message="apiError && apiError.message"
    >
    </Alert>

    <div class="row tw-p-2">
      <q-space></q-space>
      <div class="tw-flex tw-justify-end col">
        <q-btn 
          color="primary"
          @click="onUpdate"
          :loading="isLoading"
          :disable="isClickable"
          >
          <!-- TODO: 上に追加(q-btn) - honda
           :style="hasActionPermission('hideButton', 'AdminSettings_CalendarDisplaySettingsFragment_Update') ? 'visibility: hidden' : ''" 
           -->
          保存
        </q-btn>
      </div>
    </div> 
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch,onBeforeMount } from 'vue';
import { useQuasar } from 'quasar';
import { useSettingsStore } from '@/stores/modules/settings';
import cloneDeep from "lodash/cloneDeep";
import { useCalenderStore } from '@/stores/modules/calendar';
import { isNullOrEmpty } from "@/utils/stringUtils";
import Alert from '@/components/common/Alert.vue';

const $q = useQuasar();

// store
const settingsStore = useSettingsStore();
const calendarStore = useCalenderStore();

// data
const gridCols = ref<any>({
  input: 4,
  button: 12,
});

const tag1Rules = ref<any>([
  (v: any) => (!!v && v.trim().length > 0) || "大分類項目名は必須入力です。",
]);

const tag2Rules = ref<any>([
  (v: any) => (!!v && v.trim().length > 0) || "中分類項目名は必須入力です。",
]);

const apiError = ref<any>(null);
const showError = ref<boolean>(false);

// computed
const calendarDisplaySettings = computed(() => settingsStore.calendarDisplaySettings);
const isUpdatingCalendarDisplaySettings = computed(() => settingsStore.isUpdatingCalendarDisplaySettings);
const updateCalendarDisplaySettingsError = computed(() => settingsStore.updateCalendarDisplaySettingsError);

const isLoading = computed((): boolean => {
  return isUpdatingCalendarDisplaySettings.value;
});

const isClickable = computed((): boolean => {
  return isNullOrEmpty(tag1.value) || isNullOrEmpty(tag2.value);
});

const tag1 = computed({
  get(): any {
    return calendarDisplaySettings.value.tag1;
  },
  set(value: any): void {
    handleUpdate({ tag1: value });
  }
});

const tag2 = computed({
  get(): any {
    return calendarDisplaySettings.value.tag2;
  },
  set(value: any): void {
    handleUpdate({ tag2: value });
  }
});

const tag3 = computed({
  get(): any {
    return calendarDisplaySettings.value.tag3;
  },
  set(value: any): void {
    handleUpdate({ tag3: value });
  }
});

// watch
watch(
  () => updateCalendarDisplaySettingsError.value,
  (value) => {
    if (value) {
      showError.value = true;
      apiError.value = value;
    } else {
      showError.value = false;
    }
  }
);

// methods
const handleUpdate = (value: any): void => {
  settingsStore.setCalendarDisplaySettings({ ...calendarDisplaySettings.value, ...value });
};

const onFetchSettings = async (): Promise<void> => {
  await settingsStore.fetchCalendarDisplaySettings();
};

const onUpdate = async (): Promise<void> => {
  try {
    let payload = cloneDeep(calendarDisplaySettings.value);
    await settingsStore.updateCalendarDisplaySettings(payload);
    if (!updateCalendarDisplaySettingsError.value) {
      $q.notify({
        message: "分類項目名を変更しました。"
      });
      calendarStore.setCalendarDisplayData(payload);
    }
  } catch (error: any) {}
};

const resetDisplay = async (): Promise<void> => {
  try{
    let payload: any = cloneDeep(calendarDisplaySettings.value);
    payload['tag1'] = "";
    payload['tag2'] = "";
    payload['tag3'] = "";
    await settingsStore.updateCalendarDisplaySettings(payload);
    if (!updateCalendarDisplaySettingsError.value) {
      $q.notify({
        message: "分類項目名を変更しました。"
      });
      calendarStore.setCalendarDisplayData(payload);
      settingsStore.setCalendarDisplaySettings(payload);
    }
  } catch(error){}
};

// hooks
onBeforeMount(() => {
  onFetchSettings();
});

</script>