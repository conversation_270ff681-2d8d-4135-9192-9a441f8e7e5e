<template>
  <div @scroll="onScroll">
    <div opacity="1" v-if="loadingCommonSettings" class="loader flex flex-col items-center justify-center h-full">
      <AtomSpinner :animation-duration="1400" :size="100" color="primary" class="mx-auto my-auto" />
      <div class="loader-txt text-white">処理中ですしばらく、お待ちください。</div>
    </div>
    <div v-else>
      <adminGuestWelcomePage v-if="showGuestWelcomePage" @backToAdmin="backToAdmin" />
      <div v-else style="display: flex; flex-direction: column;">
        <div v-if="isMenuVertical">
          <!-- Menu Sidebar -->
          <MenuSide/>

          <div class="fixed-bottom" style="background-color: white;">
                <adminFooter class="fixed-bottom" style="background-color: white;" />
                <adminButtonGoToTop class="fixed-bottom" style="z-index: 6;" />
                <adminLoader />
            </div>
        </div>
        <!-- Menu Top Bar -->
        <div v-else>
          <QLayout>
            <div class="tw-justify-center tw-flex tw-pb-4">
              <adminHeader style="padding-bottom: 0; " />
            </div>
            <adminMenu :fixed="fixedMenu && !loadingCommonSettings" class="tw-px-[5%] bg-primary" />
          </QLayout>

          <q-layout>
            <q-page-container :style="styleContent" class="flex justify-center">
              <q-page :fluid="isMenuVertical" class="tw-w-full tw-ml-[5%] tw-mr-[5%] tw-max-w-[90%]">
                <div>
                  <router-view :class="fixedMenu ? 'mt-12' : ''" />
                </div>
              </q-page>
            </q-page-container>
          </q-layout>
          <adminButtonGoToTop class="tw-z-10" style="z-index: 6;" />
          <adminFooter class="tw-z-0" />
          <adminLoader />
        </div>
      </div>
    </div>


    <q-dialog v-model="showTimeoutError" persistent width="500">
      <q-card>
        <q-header color="warning" dark height="10"> </q-header>
        <q-list three-line>
          <q-item>
            <q-avatar class="self-center">
              <q-icon size="xl" name="mdi-alert-outline" color="warning" />
            </q-avatar>
            <q-item-section class="q-pa-sm">
              <q-item-label class="text-bold text-subtitle1 q-mb-xs">セッションタイムアウト</q-item-label>
              <q-item-label class="mt-2">
                セッションタイムアウトが発生しました。<br />
                お手数ですが、再度サインインしてください。
              </q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
        <q-separator />
        <q-card-actions>
          <q-space />
          <q-btn color="primary darken-2" outlined @click="forceSignOut()"> サインイン画面へ </q-btn>
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from "vue";
import { Auth } from "aws-amplify";
import { AtomSpinner } from "epic-spinners";

import debounce from "lodash/debounce";
import { AmplifyService } from "@/services/amplify.service";
import { useAuthStore } from "@/stores/modules/auth.module";
import { useSettingsStore } from "@/stores/modules/settings";
import { useSurveyResultsStore } from "@/stores/modules/surveyResults";
import { useRoute, useRouter } from "vue-router";

// components
import adminHeader from "@/components/layout/Header.vue";
import adminMenu from "@/components/layout/Menu.vue";
import MenuSide from "@/components/layout/MenuSide.vue";
import adminFooter from "@/components/layout/Footer.vue";
import adminLoader from "@/components/layout/Loader.vue";
import adminButtonGoToTop from "@/components/common/ButtonGoToTop.vue";
import adminGuestWelcomePage from "./Error/GuestWelcomePage.vue";
import { nextTick } from "process";

defineOptions({
  name: "AdminCom",
});

// store
const authStore = useAuthStore();
const settingsStore = useSettingsStore();
const surveyResultsStore = useSurveyResultsStore();

// route
const route = useRoute();
const router = useRouter();

// data 
const fixedMenu = ref(false);
const showTimeoutError = ref(false);
const showGuestWelcomePage = ref(false);
const interval = ref<any>();
const colorOfSelectedOldVaccineSurvey = ref('#E3F2FD');
const windowWidth = ref(window.innerWidth);
const maxWidth = ref(900);
// TODO
const handleDebouncedScroll = ref();

// computed
const isAccountUnusable = computed(() => authStore.isAccountUnusable);
const user = computed(() => authStore.user);
const commonSettings = computed(() => settingsStore.commonSettings);
const loadingCommonSettings = computed(() => settingsStore.isFetchingCommonSettings);
const selectedSurvey = computed(() => surveyResultsStore.selectedSurvey);

const styleContent = computed((): string => {
  let _contentBgColor = commonSettings.value.themes ? commonSettings.value.themes.contentBgColor.color : "#FFF";
  if (isSelectedOldVaccineSurvey.value && isShowApplicantsPage.value) {
    _contentBgColor = colorOfSelectedOldVaccineSurvey.value;
  }
  return `background-color: ${_contentBgColor}; flex: 1`;
});

const styleHeader = computed((): string => {
  let _headerBgColor = commonSettings.value.themes ? commonSettings.value.themes.headerBgColor.color : "#FFF";
  return `background-color: ${_headerBgColor}`;
});

const isMenuVertical = computed((): boolean => {
  return commonSettings.value.menuStyle === "vertical";
});

const isGuestUser = computed((): boolean => {
  return (
    user.value &&
    user.value.groups &&
    user.value.groups.length === 1 &&
    user.value.groups[0] === "guests"
  );
});

const isSelectedOldVaccineSurvey = computed((): boolean => {
  if (!selectedSurvey.value || selectedSurvey.value.surveyType !== 'corona') {
    return false;
  }

  const surveySchema = selectedSurvey.value.surveySchema;
  const countVaccinesItem = surveySchema.find((item: any) => item.type === 'countVaccines');
  return !countVaccinesItem;
});

const isShowApplicantsPage = computed((): boolean => {
  const currentPath = route.path;
  const applicationtsPagePath = '/';
  return currentPath === applicationtsPagePath;
});

// watch
watch(
  isAccountUnusable,
  (newVal) => {
    if (newVal) {
      forceSignOut();
    }
  }
);

watch(
  () => commonSettings.value.themes,
  (newVal) => {
    document.documentElement.style.setProperty('--q-primary', newVal.primaryColor.color);
    document.documentElement.style.setProperty('--q-secondary', newVal.secondaryColor.color);
    document.documentElement.style.setProperty('--q-accent', newVal.secondaryColor.color);
    document.documentElement.style.setProperty('--q-info', newVal.infoColor.color);
    document.documentElement.style.setProperty('--q-warning', newVal.warningColor.color);
    document.documentElement.style.setProperty('--q-error', newVal.errorColor.color);
    document.documentElement.style.setProperty('--q-success', newVal.successColor.color);
  },
  { deep: true }
);

//  watch(
//    () => windowWidth.value,
//   (newVal) => {
//      if (newVal < 1264) {
//      maxWidth.value = 900;
//     } else if (newVal < 1350) {
//         maxWidth.value = 1185;
//  }
//     else {
//        maxWidth.value = 1785;
//     }
//    },
//   { immediate: true }
// );

// watch

watch(() => authStore.triggerTimeout, async () => {
  if (!showTimeoutError.value) {
    showTimeoutError.value = true;
  }
});

// onMounted
onMounted(() => {
  settingsStore.fetchPublicConfiguration().then(() => {
    AmplifyService.configure();
  });

  settingsStore.fetchCommonSettings();

  handleDebouncedScroll.value = debounce(onScroll, 0);
  window.addEventListener("scroll", handleDebouncedScroll.value);

  if (isGuestUser.value) {
    showGuestWelcomePage.value = true;
  }
  sessionInterval();

  if (route.path === '/') {
    router.push({ name: "ApplicantsPage" });
  }

  window.addEventListener("resize", updateSize);
});

// onUnmounted
onUnmounted(() => {
  window.removeEventListener("scroll", handleDebouncedScroll.value);
});

// methods
const onScroll = (e: any): void => {
  if (!isMenuVertical.value && window.scrollY > 120) {
    fixedMenu.value = true;
  } else {
    fixedMenu.value = false;
  }
};

const forceSignOut = async (): Promise<void> => {
  await authStore.AUTH_SIGNOUT();
  router.push({ name: 'Signin' });
};

const sessionInterval = (): void => {
  const milliseconds = 10000;
  interval.value = setInterval(
    function () {
      getSession();
    }.bind(this),
    milliseconds
  );
};

const backToAdmin = (): void => {
  showGuestWelcomePage.value = false;
};

const getSession = async (): Promise<void> => {
  await Auth.currentSession()
    .then(() => { })
    .catch((error) => {
      if (error) {
        console.error(error);
        showTimeoutError.value = true;
        clearInterval(interval.value);
      }
    });
};

const updateSize = () => {
  windowWidth.value = window.innerWidth;
};

defineExpose({
  handleDebouncedScroll,
});

</script>

<style lang="css">
.loader {
  flex-direction: column;
  height: 100vh;

  background-color: rgb(33, 33, 33);
}

.loader-txt {
  font-size: 1rem;
  margin-top: 24px;
  text-align: center;
}
</style>