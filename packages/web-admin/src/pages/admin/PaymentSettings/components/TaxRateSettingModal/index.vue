<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-dialog v-model="show" max-width="608">
    <q-card v-if="!isOverlay" class="tw-w-full">
      <q-bar class="bg-primary tw-h-1"></q-bar>
      <q-toolbar>
        <q-toolbar-title class="tw-font-semibold">消費税設定</q-toolbar-title>
      </q-toolbar>
      <div class="tw-px-6 tw-pt-4 tw-pb-0">
        <div class="tw-mx-0 tw-pb-6 row">
          <div class="tw-flex tw-justify-between tw-p-0 col">
            <span class="tw-pl-0 tw-font-semibold text-blue-grey">消費税設定</span>
            <div v-if="!hasMaxRateSettingsLength" class="tw-pr-3 tw-underline text-primary">
              <a @click="addSetting" class="tw-cursor-pointer">設定を追加</a>
            </div>
            <span v-else class="tw-pr-3">設定を追加</span>
          </div>
        </div>

        <div v-for="(rateSetting, index) in localRateSettings" :key="index" class="tw-pb-6 tw-mx-0 row tw-flex tw-justify-center tw-items-center text-grey-8">
          <div class="tw-p-0 col-1">
            <q-item-label class="tw-p-0 tw-pl-2">税率</q-item-label>
          </div>
          <div class="tw-p-0 col-3">
            <q-input
              v-model.number="rateSetting.value"
              type="number"
              outlined
              dense
              hide-details="auto"
              :disable="!rateSetting.isEditable"
              :rules="[rule]"
              :min="taxRateRange.MIN"
              :max="taxRateRange.MAX"
              @keypress="rangeInputKeyDown"
              class="tw-p-0"
            ></q-input>
          </div>
          <div class="tw-p-0 tw-pl-2 col-1">
            <q-item-label class="tw-p-0">%</q-item-label>
          </div>
          <div class="tw-p-0 col-2">
            <q-item-label class="tw-p-0 tw-pr-2 tw-text-end">適用開始日</q-item-label>
          </div>
          <div class="tw-p-0 col-4">
            <q-input
              v-if="rateSetting.isEditable"
              v-model="rateSetting.applyDate"
              readonly
              outlined
              dense
              prepend-inner-icon="mdi-calendar-outline"
              placeholder="YYYY-MM-DD"
              clearable
              hide-details
            >
              <template v-slot:prepend>
                <q-icon name="mdi-calendar-outline" />
              </template>
              <template v-slot:append v-if="rateSetting.applyDate">
                <q-icon name="close" @click="rateSetting.applyDate = null;" class="cursor-pointer" />
              </template>
              <q-menu
                v-model="rateSetting.isShowDatePicker"
                transition="scale-transition"
                offset-y
                :close-on-content-click="false"
              >
                <q-date 
                  v-model="rateSetting.applyDate" 
                  color="primary" 
                  :options="allowedDate" 
                  minimal
                  @update:model-value="(v) => {
                    rateSetting.applyDate = v.replaceAll('/', '-')
                    rateSetting.isShowDatePicker = false;
                  }"
                ></q-date>
              </q-menu>
            </q-input>
            
            <q-input
              v-else
              v-model="rateSetting.applyDate"
              outlined
              dense
              prepend-inner-icon="mdi-calendar-outline"
              placeholder="YYYY-MM-DD"
              hide-details
              :disable="!rateSetting.isEditable || rateSetting.isApplying"
            ></q-input>
          </div>
          <div class="tw-p-0 tw-pl-3 col-1">
            <q-icon 
              color="negative" 
              @click="removeSetting(index)" 
              v-show="!rateSetting.isApplying" 
              name="mdi-delete"
              size="sm"
            >
            </q-icon>
          </div>
        </div>
      </div>

      <div class="tw-pt-3 tw-pr-6 tw-pb-6">
        <div class="tw-mx-0 row">
          <div class="tw-justify-end tw-flex tw-p-0 col">
            <q-btn
              color="primary"
              outline
              @click="show = false"
              class="tw-mr-2"
            >
              キャンセル
            </q-btn>
            <q-btn
              color="primary"
              :disable="!hasRateSettings"
              @click="showRegistConfirm"
            >
              <!-- :style="
                hasActionPermission('hideButton', 'PaymentSettings_RateSetting_Update')
                  ? hideButtonPermissionStyle()
                  : ''
              " -->
              登録
            </q-btn>
          </div>
        </div>
      </div>
    </q-card>
    <div v-else>
      <q-spinner :size="50" color="primary" />
    </div>
  </q-dialog>
</template>

<script setup lang="ts">
import { PropType, ref,  computed, watch,  } from 'vue';
import { useQuasar } from 'quasar';
import { usePaymentStore } from '@/stores/modules/payment';
import { TaxRateSettingModel, TaxRateSettingValidatorModel } from '@/model/Payment/TaxSetting';
import { DEFAULT_RATE_SETTING, TAX_RATE_RANGE } from '@/stores/modules/payment/payment.constants';
import { cloneDeep } from 'lodash';
import { DateTime } from 'luxon';
import { TaxRateSetting } from '@/stores/modules/payment/payment.types';

// store
const paymentStore = usePaymentStore();

// emits 
const emits = defineEmits<{
  (event: 'close'): void;
}>();

const $q = useQuasar();

// props
const props = defineProps({
  visible: Boolean as PropType<boolean>
});

// data
const localRateSettings = ref<any>([]);
const model = ref(new TaxRateSettingModel());
const validator = ref(new TaxRateSettingValidatorModel());
const isLoading = ref(false);
const taxRateRange = ref(TAX_RATE_RANGE);

// methods
const addSetting = (): void => {
  localRateSettings.value.push(cloneDeep(DEFAULT_RATE_SETTING));
};

const removeSetting = (index: number): void => {
  localRateSettings.value.splice(index, 1);
};

const allowedDate = (value: string): boolean => {
  const dateFormat = 'yyyy-MM-dd';
  const now = DateTime.now();
  const result = now.toFormat(dateFormat);
  return result <= value;
};

const showRegistConfirm = (): void => {
  $q.dialog({
    title: '登録内容の確認',
    message: '入力内容で登録してもよろしいですか？',
    cancel: true,
  }).onOk(async () => {
    await regist();
  });
};

const rule = (value: string): boolean | string => {
  return (validator.value as TaxRateSettingValidatorModel).doTaxRateVallidateRule(value);
};

const regist = async () => {
  try {
    isLoading.value = true;
    (validator.value as TaxRateSettingValidatorModel).validate(localRateSettings.value);
    const payload = (model.value as TaxRateSettingModel).toPayload(localRateSettings.value);
    await paymentStore.upsertTaxRateSettings(payload);
    $q.notify({ message: '登録が成功しました。' });
  } catch (error: any) {
    if (typeof(error) === "string") {
      $q.notify({ message: error, type: 'error' });
    } 
    else {
      console.log(error)
      $q.notify({ message: error.message, type: 'error' });
    }
  } finally {
    isLoading.value = false;
  }
};

const rangeInputKeyDown = (evt: any): void => {
  ['e', 'E', '+', '-', '.'].includes(evt.key) && evt.preventDefault();
};

// computed
const taxRateSettings = computed(() => paymentStore.taxRateSettings);

const show = computed({
  get() {
    return props.visible;
  },
  set(value) {
    if (!value) {
      emits("close");
    }
  },
});

const hasRateSettings = computed((): boolean => {
  return localRateSettings.value.length > 0;
});

const hasMaxRateSettingsLength = computed((): boolean => {
  const maxLength = 5; // 仮置きの上限
  return localRateSettings.value.length === maxLength;
});

const isOverlay = computed(() => {
  return isLoading.value;
});

// watch
watch(
  () => taxRateSettings.value,
  (value: TaxRateSetting[]) => {
    // NOTE: taxRateSettings.valueが更新されるタイミング：fetchTaxRateSettings実行時 / upsertRateSettings実行時
    localRateSettings.value = (model.value as TaxRateSettingModel).toInput(value);
  }
);

watch(
  () => props.visible,
  (value) => {
    if (value) {
      paymentStore.fetchTaxRateSettings();
    }
  }
);
</script>

<style scoped>
.align-items-center {
  display: flex;
  align-items: center;
}
</style>