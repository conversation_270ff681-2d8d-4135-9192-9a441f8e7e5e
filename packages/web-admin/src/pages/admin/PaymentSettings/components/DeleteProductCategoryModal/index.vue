<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-dialog v-model="show" max-width="500">
    <q-card v-if="!isDeleting" class="tw-w-full">
      <q-bar class="tw-h-1 bg-negative"></q-bar>
      <q-toolbar>
        <q-toolbar-title class="tw-font-semibold">削除</q-toolbar-title>
      </q-toolbar>

      <q-page-container class="tw-py-4 tw-px-6 text-grey-8">
        <div class="tw-mx-0 tw-mb-1 tw-font-semibold row">
          {{ targetNames }}を削除します。
        </div>
        <div class="tw-mx-0 row">
          商品に紐付けられている商品分類は削除できません。
          <br>削除したら元に戻すことはできません。
          <br>よろしいですか？
        </div>
      </q-page-container>
       <q-page-container class="tw-pt-3 tw-pr-6 tw-pb-6">
        <div class="tw-mx-0 row">
          <div class="tw-justify-end tw-flex tw-p-0 col">
            <q-btn
              color="blue-grey"
              outline
              @click="show = false"
              class="tw-mr-2"
            >
              キャンセル
            </q-btn>
            <q-btn
              color="negative"
              @click="handleDeleteProductCategories"
            >
              <!-- :style="
                hasActionPermission('hideButton', 'PaymentSettings_ProductCategory_Delete')
                  ? hideButtonPermissionStyle()
                  : ''
              " -->
              削除
            </q-btn>
          </div>
        </div>
      </q-page-container>
    </q-card>
    <div v-else>
      <q-spinner :size="50" color="primary" />
    </div>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useQuasar } from 'quasar';
import { useRoute } from 'vue-router';
import { usePaymentStore } from '@/stores/modules/payment';

// store
const paymentStore = usePaymentStore();

// emits 
const emits = defineEmits<{
  (event: 'removeToSelected', payload: any): void;
  (event: 'close'): void;
}>();

const $q = useQuasar();

// route
const route = useRoute();

// props
const props = defineProps<{
  visible: boolean,
  selectedItems: any[],
}>();

// data
const isDeleting = ref(false);

// methods
const showSnackMessage = (deleteFailItems: any[]) => {
  if (deleteFailItems.length == 0) {
    return $q.notify({ message: '削除が完了しました。' });
  }
  const snackMessage = `${deleteFailItems.map(i => i.name).join(',')}の削除に失敗しました。`;
  return $q.notify({ type: 'error', message: snackMessage });
};

const handleDeleteProductCategories = async (): Promise<void> => {
  try {
    isDeleting.value = true;
    const selectedIds = props.selectedItems.map(item => item.id);
    const serviceId = route.params.serviceId.toString();
    const deletedIds: string[] = await paymentStore.deleteProductCategories({
      ids: selectedIds,
      serviceId,
    });
    const deleteFailItems = props.selectedItems.filter(item => !deletedIds.includes(item.id)) 
    showSnackMessage(deleteFailItems);
    show.value = false;
    emits('removeToSelected', selectedIds);
  } catch (error: any) {
    if (typeof(error) === 'object') {
      $q.notify({ message: error.message, type: 'error' });
    }
    else {
      $q.notify({ message: error, type: 'error' });
    }
  } finally {
    isDeleting.value = false;
  }
};

// computed
const show = computed({
  get() {
    return props.visible;
  },
  set(value) {
    if (!value) {
      emits("close");
    }
  },
});

const targetNames = computed((): string => {
  const maxDisplayLength = 5;
  const diff = props.selectedItems.length - maxDisplayLength;
  if (diff > 0) {
    const displayItems = [...props.selectedItems];
    displayItems.splice(maxDisplayLength);
    return displayItems.map((item) => `"${item.name}"`).join(" , ") + ` + その他${diff}件`;
  }
  return props.selectedItems.map((item) => `"${item.name}"`).join(" , ");
});
</script>