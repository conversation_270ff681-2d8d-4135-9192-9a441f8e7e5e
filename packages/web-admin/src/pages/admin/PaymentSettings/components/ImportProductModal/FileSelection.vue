<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div class="tw-mx-0 row">
    <div class="blue-grey tw-text-center tw-p-0 col">
      <div class="tw-py-6">
        <div v-if="file !== null">
          <q-icon @click="removeSelectedFile" color="negative" size="sm" left name="mdi-close-circle-outline"></q-icon>
          {{ fileName }}({{ fileSize }})MB
        </div>
        <q-btn
          v-else
          color="primary"
          @click="onClickSelectFile"
        >
          <!-- :style="
            hasActionPermission('hideButton', 'PaymentSettings_Product_ImportCsvFile')
              ? hideButtonPermissionStyle()
              : ''
          " -->
          <q-icon left name="mdi-upload-outline"></q-icon>
          CSVファイルを選択
        </q-btn>
      </div>
      <input
        v-show="false"
        type="file"
        ref="inputFileRef"
        class="d-none"
        accept=".csv"
        @change="onChangeFileInput"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

// emits 
const emits = defineEmits<{
  (event: 'change', payload: any): void;
}>();

// props
const props = defineProps<{
  file: File | null,
}>();

// data
const inputFileRef = ref();

// methods
const onClickSelectFile = (): void => {
  inputFileRef.value.click();
};

const onChangeFileInput = async (event: Event): Promise<void> => {
  if (event.target instanceof HTMLInputElement && event.target.files) {
    const file = event.target.files[0];
    file && emits('change', file)
    inputFileRef.value.value = null;
  }
};

const removeSelectedFile = (): void => {
  emits('change', null);
};

// computed
const fileSize = computed(() => {
  return props.file ? Math.ceil((props.file.size/1048576) * 100) / 100 : '';
});

const fileName = computed((): string => {
  return props.file ? props.file.name : '';
});
</script>