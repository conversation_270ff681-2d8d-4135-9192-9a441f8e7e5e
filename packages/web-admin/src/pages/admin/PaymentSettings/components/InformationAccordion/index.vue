<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <q-list flat bordered class="bg-white tw-mb-3">
    <q-expansion-item expand-separator header-class="tw-flex tw-justify-between">
      <template v-slot:header>
        <p class="tw-mb-0">
          <span class="text-h6 tw-mr-3">決済機能を利用するステップ</span>
          ※ ステップを表示する場合はこちらをクリックしてください。
        </p>
      </template>

      <q-card>
        <!-- STEP1 -->
        <div class="row tw-items-center">
          <div class="instruction-contents col-1">
            <q-icon size="md" color="secondary" name="mdi-numeric-1-circle"></q-icon>
          </div>
          <div class="col">
            <p class="tw-font-semibold">基本設定</p>
          </div>
        </div>
        <div class="row tw-py-4">
          <div class="instruction-line col-1"> </div>
          <div class="col">
            <div class="row tw-py-1" dense>
              <div class="font-weight-light col-1">1-1.</div>
              <div class="col">消費税を設定します。</div>
            </div>
            <div class="row tw-py-1" dense>
              <div class="font-weight-light col-1">1-2.</div>
              <div>サービスを登録します。</div>
            </div>
            <div class="row tw-py-1" dense>
              <div class="font-weight-light col-1">1-3.</div>
              <div class="col">
                商品分類を登録します。
                <span class="tw-ml-2 tw-font-thin">※ 任意</span>
              </div>
            </div>
            <div class="row tw-py-1" dense>
              <div class="font-weight-light col-1">1-4.</div>
              <div class="col">商品を登録します。</div>
            </div>
          </div>
        </div>

        <!-- STEP2 -->
        <div class="row tw-items-center">
          <div class="instruction-contents col-1">
            <q-icon size="md" color="secondary" name="mdi-numeric-2-circle"></q-icon>
          </div>
          <div class="col">
            <p class="tw-font-semibold">カレンダー設定（カレンダー予約する場合のみ設定）</p>
          </div>
        </div>
        <div class="row tw-py-4">
          <div class="instruction-line col-1"></div>
          <div class="col">
            <div class="row tw-py-1" dense>
              <div class="font-weight-light col-1">2-1.</div>
              <div class="col">
                <router-link 
                  to="/calendar/calendar-setting" 
                  target="_blank"
                  class="tw-underline text-secondary"
                >
                  カレンダーページ
                </router-link>
                から分類・カレンダーを作成して、詳細設定画面を開きます。
              </div>
            </div>
            <div class="row tw-py-1" dense>
              <div class="font-weight-light col-1">2-2.</div>
              <div class="col">「カレンダー予約項目」タブで「決済機能連携」を「利用する」にします。</div>
            </div>
            <div class="row tw-py-1" dense>
              <div class="font-weight-light col-1">2-3.</div>
              <div class="col">「連携対象サービス」を選択して予約項目を追加・保存します。</div>
            </div>
          </div>
        </div>

        <!-- STEP3 -->
        <div class="row tw-py-4 tw-items-center">
          <div class="instruction-contents col-1">
            <q-icon size="md" color="secondary" name="mdi-numeric-3-circle"></q-icon>
          </div>
          <div class="col">
            <p class="tw-font-semibold">帳票設定</p>
          </div>
        </div>
        <div class="row">
          <div class="instruction-line col-1"></div>
          <div class="col">
            <div class="row tw-py-1" dense>
              <div class="font-weight-light col-1">3-1.</div>
              <div class="col">
                <router-link 
                  to="/forms/survey/new/template_blank" 
                  target="_blank"
                  class="tw-underline text-secondary"
                >
                帳票新規作成ページ
                </router-link>
                を開きます。
              </div>
            </div>
            <div class="row tw-py-1" dense>
              <div class="font-weight-light col-1">3-2.</div>
              <div class="col">「決済機能を利用する」をONにします。</div>
            </div>
            <div class="row tw-py-1" dense>
              <div class="font-weight-light col-1">3-3.</div>
              <div class="col">予約をしない場合 : 「商品選択（カレンダー予約なし）」の質問項目を設定します。</div>
            </div>
            <div class="row tw-py-1" dense>
              <div class="col-1"></div>
              <div class="col">予約をする場合 : 「分類」「カレンダーボタン」の質問項目を設定します。</div>
            </div>
            <div class="row tw-py-1" dense>
              <div class="font-weight-light col-1">3-4.</div>
              <div class="col">保存をします。</div>
            </div>
          </div>
        </div>

        <!-- マニュアル -->
        <p class="tw-p-4 tw-mt-5">
          マニュアルは
          <a 
            target="_blank" 
            href="https://liberating-circle-e6c.notion.site/LINE-SMART-CITY-d578ea0b46354288a14fa9cfc5a000dd"
            class="tw-underline text-secondary"
          >
            こちら
          </a>
        </p>
      </q-card>
    </q-expansion-item>
  </q-list>
</template>
<style scoped>
.instruction-line {
  background: linear-gradient(#000, #000) no-repeat center/1px 100%;
}
.instruction-contents {
  text-align: center;
}
</style>