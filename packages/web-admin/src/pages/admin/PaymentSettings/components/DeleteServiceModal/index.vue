<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-dialog v-model="show" max-width="500">
    <q-card v-if="!isDeleting" class="tw-w-full">
      <q-bar class="bg-negative tw-h-1"></q-bar>
      <q-toolbar>
        <q-toolbar-title class="tw-font-semibold">削除</q-toolbar-title>
      </q-toolbar>

      <q-page-container class="tw-py-4 tw-px-6 text-grey-8">
        <div class="tw-p-0 tw-m-0 tw-font-semibold">
          {{ targetServiceNames }}を削除します。
        </div>
        削除したら元に戻すことはできません。
      </q-page-container>

      <q-page-container class="tw-pt-3 tw-pr-6 tw-pb-6">
        <div class="tw-mx-0 row">
          <div class="tw-justify-end tw-flex tw-p-0 col">
            <q-btn
              color="blue-grey"
              outline
              class="tw-mr-2"
              @click="show = false"
            >
              キャンセル
            </q-btn>
            <q-btn
              color="negative"
              @click="deleteServices"
            >
              <!-- :style="
                hasActionPermission('hideButton', 'PaymentSettings_Service_Delete')
                  ? hideButtonPermissionStyle()
                  : ''
              " -->
              削除
            </q-btn>
          </div>
        </div>
      </q-page-container>
    </q-card>
    <div v-else>
      <q-spinner :size="50" color="primary" />
    </div>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useQuasar } from 'quasar';
import { usePaymentStore } from '@/stores/modules/payment';

// store
const paymentStore = usePaymentStore();

// emits 
const emits = defineEmits<{
  (event: 'removeToSelected', payload: any): void;
  (event: 'close'): void;
}>();

const $q = useQuasar();

// props
const props = defineProps<{
  visible: boolean,
  selectedItems: any[],
}>();

// data
const isDeleting = ref(false);

// methods
const deleteServices = async (): Promise<void> => {
  try {
    isDeleting.value = true;
    const selectedIds = props.selectedItems.map(item => item.sortKey);
    const deletedIds: { success: string[], error: string[] } = await paymentStore.deletePaymentServices(selectedIds);
    showDeleteMessage(deletedIds.error);
    emits('removeToSelected', deletedIds.success);
    show.value = false;
  } catch (error: any) {
    if (typeof(error) === 'object') {
      $q.notify({ message: error.message, type: 'error' });
    }
    else {
      $q.notify({ message: error, type: 'error' });
    }
  } finally {
    isDeleting.value = false;
  }
};

const showDeleteMessage = (deleteFailIds: string[]) => {
  if (deleteFailIds.length > 0) {
    const serviceNames = deleteFailIds
      .map(id => {
        const service = props.selectedItems.find(service => service.sortKey === id);
        return `"${service.serviceName}"`;
      })
      .join(' , ');
    $q.notify({
      type: 'error',
      message: `${serviceNames}の削除に失敗しました。商品が登録されているか、帳票もしくはカレンダーと連携されています。`
    });
  } else {
    $q.notify({ message: '削除が完了しました。' });
  }
};

// computed
const show = computed({
  get() {
    return props.visible;
  },
  set(value) {
    if (!value) {
      emits("close");
    }
  },
});

const targetServiceNames = computed((): string => {
  let targetServiceNameString = '';
  const DISPLAY_ITEM_COUNT = 5;
  for (let i = 0; i < Math.min(props.selectedItems.length, DISPLAY_ITEM_COUNT + 1); i++) {
    if (i == DISPLAY_ITEM_COUNT) {
      targetServiceNameString += ` +その他${props.selectedItems.length - DISPLAY_ITEM_COUNT}件`
    } else {
      targetServiceNameString += `${i != 0 ? ' ,' : ''}"${props.selectedItems[i].serviceName}"`
    }
  }
  return targetServiceNameString
});
</script>