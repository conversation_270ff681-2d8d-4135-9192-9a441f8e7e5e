<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div class="tw-flex tw-items-center">
    <span class="tw-mr-2">ステータス</span>
    <q-select 
      v-model="displayStatusInput"
      :options="items"
      option-value="value"
      option-label="text"
      outlined
      dense
    />
  </div>
</template>

<script setup lang="ts">
import { PropType, ref, computed } from 'vue';

// emits 
const emits = defineEmits<{
  (event: 'change', payload: any): void;
}>();

// props
const props = defineProps({
  displayStatus: { type: Number as PropType<number>, required: true }
});

// data
const items = ref<any>([
  {
    text: '販売中の商品',
    value: 0,
  },
  {
    text: '販売停止の商品',
    value: 1,
  },
  {
    text: 'すべての商品',
    value: 2,
  },
]);

// computed
const displayStatusInput = computed({
  get() {
    let result = items.value.find((v: any) => {
      return props.displayStatus === v.value;
    });
    return result;
  },
  set(newVal): void {
    emits('change', newVal.value);
  }
});
</script>