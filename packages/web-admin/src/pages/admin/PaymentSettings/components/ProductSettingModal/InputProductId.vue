<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div class="tw-mx-0 row">
    <div class="tw-p-0 tw-pb-1 col">
      <q-input
        v-model="productIdInput"
        :rules="[rule]"
        :maxlength="validLength"
        :disable="isDisabled"        
        solo
        single-line
        outlined
        dense
        flat
        hide-details="auto"
      ></q-input>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, ref,  computed,  } from 'vue';
import { INVALID_PRODUCT_MESSAGES, PRODUCT_ID_VALID_LENGTH } from '@/stores/modules/payment/payment.constants'
import { ProductValidatorModel } from '@/model/Payment/Product';

// emits 
const emits = defineEmits<{
  (event: 'change', payload: any): void;
}>();

// props
const props = defineProps({
  productId: { type: String as PropType<string>, required: true },
  isDisabled: { type: Boolean as PropType<boolean>, required: true }
});

const validLength = PRODUCT_ID_VALID_LENGTH.MAX;
const validator = new ProductValidatorModel();

// methods
const rule = (value: string): boolean | string => {
  return validator.isValidProductId(value) || INVALID_PRODUCT_MESSAGES.PRODUCT_ID;
};

// computed
const productIdInput = computed({
  get(): string {
    return props.productId;
  },
  set(value: string): void {
    emits('change', value);
  },
});
</script>
