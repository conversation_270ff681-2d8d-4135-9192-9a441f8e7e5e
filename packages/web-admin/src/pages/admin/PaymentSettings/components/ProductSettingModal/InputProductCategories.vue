<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div class="tw-mx-0 row">
    <div class="tw-p-0 tw-pb-1 col">
      <q-select use-input
        v-model="productCategoriesInput"
        :options="productCategoryList"
        option-value="id"
        option-label="name"
        multiple
        :rules="[rule]"
        outlined
        dense
      >
        <template v-slot:selected-item="data">
          <q-chip
            v-bind="data.opt.attrs"
            :input-value="data.selected"
            removable
            close
            color="blue-grey"
            text-color="white"
            @remove="remove(data.opt)"
          >
            {{ data.opt.name }}
          </q-chip>
        </template>
      </q-select>
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePaymentStore } from '@/stores/modules/payment';
import { computed } from 'vue';
import { ProductCategory } from '@/stores/modules/payment/payment.types';
import { ProductValidatorModel } from '@/model/Payment/Product';
import { INVALID_PRODUCT_MESSAGES } from '@/stores/modules/payment/payment.constants';

// store
const paymentStore = usePaymentStore();

// emits 
const emits = defineEmits<{
  (event: 'change', payload: any): void;
}>();

// props
const props = defineProps<{
  productCategories: ProductCategory[],
}>();

// data
const validator = new ProductValidatorModel();

// methods
const remove = (item: ProductCategory): void => {
  productCategoriesInput.value = (productCategoriesInput.value as ProductCategory[]).filter(c => c.id !== item.id);
};

const rule = (value: ProductCategory[]): boolean | string => {
  return validator.isValidProductCategories(value) || INVALID_PRODUCT_MESSAGES.PRODUCT_CATEGORIES;
};

// computed
const productCategoryList = computed(() => paymentStore.productCategoryList);

const productCategoriesInput = computed({
  get(): ProductCategory[] {
    return props.productCategories;
  },
  set(value: ProductCategory[]): void {
    emits('change', value);
  },
});
</script>
