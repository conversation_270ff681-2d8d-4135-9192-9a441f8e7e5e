<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div class="tw-mx-0 row">
    <div class="tw-p-0 tw-pb-4 col">
      <q-radio v-model="taxableTypeInput" label="課税" val="0" class="tw-flex tw-ml-2 tw-mr-5 tw-mb-3" :rules="[rule]"></q-radio>
      <q-radio v-model="taxableTypeInput" label="非課税" val="1" class="tw-flex" :rules="[rule]"></q-radio>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, computed } from 'vue';
import { ProductValidatorModel } from '@/model/Payment/Product';
import { INVALID_PRODUCT_MESSAGES } from '@/stores/modules/payment/payment.constants'

// emits 
const emits = defineEmits<{
  (event: 'change', payload: any): void;
}>();

// props
const props = defineProps({
  taxableType: { type: String as PropType<string>, required: true }
});

const validator = new ProductValidatorModel();

// methods
const rule = (value: string): boolean | string => {
  return validator.isValidTaxableType(value) || INVALID_PRODUCT_MESSAGES.TAXABLE_TYPE;
};

// computed
const taxableTypeInput = computed({
  get(): string {
    return props.taxableType;
  },
  set(value: string): void {
    emits('change', value);
  }
});
</script>
