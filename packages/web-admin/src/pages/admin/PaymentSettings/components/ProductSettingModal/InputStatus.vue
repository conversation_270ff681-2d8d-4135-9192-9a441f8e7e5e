<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div class="tw-mx-0 row">
    <div class="tw-p-0 col">
      <q-radio v-model="statusInput" :rules="[rule]" label="販売中" val="0" class="tw-flex tw-ml-2 tw-mr-5 tw-mb-3"></q-radio>
      <q-radio v-model="statusInput" :rules="[rule]" label="販売停止" val="1" class="tw-flex"></q-radio>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, computed } from 'vue';
import { INVALID_PRODUCT_MESSAGES } from '@/stores/modules/payment/payment.constants'
import { ProductValidatorModel } from '@/model/Payment/Product';

// emits 
const emits = defineEmits<{
  (event: 'change', payload: any): void;
}>();

// props
const props = defineProps({
  status: { type: String as PropType<string>, required: true }
});

const validator = new ProductValidatorModel();

// methods
const rule = (value: string): boolean | string => {
  return validator.isValidStatus(value) || INVALID_PRODUCT_MESSAGES.STATUS;
};

// computed
const statusInput = computed({
  get(): string {
    return props.status;
  },
  set(value: string): void {
    emits('change', value);
  }
});
</script>
