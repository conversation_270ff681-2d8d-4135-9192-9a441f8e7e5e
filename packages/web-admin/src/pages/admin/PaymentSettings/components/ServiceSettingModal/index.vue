<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <q-dialog v-model="show" max-width="700" scrollable>
    <q-card v-if="!isSaving" class="tw-w-full">
      <q-card class="tw-sticky tw-top-0 tw-right-0 tw-left-0">
        <q-bar class="bg-primary tw-h-1"></q-bar>
        <q-card-section class="tw-p-3">
          <q-toolbar-title class="tw-font-semibold">{{ isCreateMode ? "新規登録" : "編集" }}</q-toolbar-title>
        </q-card-section>
      </q-card>

      <q-card-section class="tw-pb-4 tw-px-6">
        <q-form ref="formRef">
          <div class="tw-p-0">
            <FormLabel>サービスID</FormLabel>
            <InputServiceId :service-id="item.serviceId" :isDisabled="!isCreateMode" @change="(v) => item.serviceId = v" />

            <FormLabel>
              <div>サービス名</div>
              <template #caution>※SBペイメントサービスでは請求情報の商品名となります</template>
            </FormLabel>
            <InputServiceName :serviceName="item.serviceName" @change="(v) => item.serviceName = v" />

            <FormLabel>領収書発行者名</FormLabel>
            <InputReceiptCreatorName :receiptCreatorName="item.receiptCreatorName" @change="(v) => item.receiptCreatorName = v" />

            <FormLabel>領収書表示住所</FormLabel>
            <InputReceiptDisplayAdress :receiptDisplayAddress="item.receiptDisplayAddress" @change="(v) => item.receiptDisplayAddress = v" />

            <FormLabel>税区分</FormLabel>
            <InputTaxType :taxType="item.taxType" @change="(v) => item.taxType = v" />

            <FormLabel>消費税計算方法</FormLabel>
            <InputCalculationType :calculationType="item.calculationType" @change="(v) => item.calculationType = v" />

            <FormLabel>端数処理</FormLabel>
            <InputRoundingType :roundingType="item.roundingType" @change="(v) => item.roundingType = v" />

            <FormLabel>カレンダー予約</FormLabel>
            <InputReservationServiceType :reservationServiceType="item.reservationServiceType" @change="(v) => item.reservationServiceType = v" />

            <FormLabel>カレンダー予約消費枠を利用</FormLabel>
            <ImputReservationCostType 
              :reservationCostType="item.reservationCostType" 
              :isDisabled="isDisabledReservationCost" 
              @change="(v) => item.reservationCostType = v"
            />

            <FormLabel>購入数上限</FormLabel>
            <InputPurchaseLimit :purchaseLimit="item.purchaseLimit" @change="(v) => item.purchaseLimit = v" />
            <div class="tw-mx-0 row">
              <div class="tw-pa-0 col">
              <span class="text-caption text-grey-7">※一度の購入で購入可能な商品数を設定します</span> 
              </div>
            </div>
          </div>
        </q-form>
      </q-card-section>

      <q-card class="tw-sticky tw-bottom-0 tw-right-0 tw-left-0">
        <q-separator></q-separator>
        <div class="tw-pt-3 tw-pr-6 tw-pb-6 tw-mx-0 row">
          <div class="tw-justify-end tw-flex tw-p-0 col">
            <q-btn
              color="primary"
              outline
              class="tw-mr-2"
              @click="show = false"
            >
              キャンセル
            </q-btn>
            <q-btn
              color="primary"
              @click="showSaveConfirm"
            >
              <!-- :style="
                hasActionPermission('hideButton', 'PaymentSettings_Service_Update')
                  ? hideButtonPermissionStyle()
                  : ''
              " -->
              {{ isCreateMode ? "登録" : "保存" }}
            </q-btn>
          </div>
        </div>
      </q-card>
    </q-card>
    <div v-else class="tw-flex tw-justify-center tw-items-center">
      <q-spinner :size="50" color="primary" />
    </div>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useQuasar } from 'quasar';
import { Service, ModalAction } from '@/stores/modules/payment/payment.types';
import {
  MODAL_ACTIONS,
  DEFAULT_SERVICE_VALUES,
  RESERVATION_SERVICE_TYPES,
} from '@/stores/modules/payment/payment.constants';
import { PaymentServiceValidatorModel, PaymentServiceModel } from '@/model/Payment/PaymentService';
import { usePaymentStore } from '@/stores/modules/payment';
import { cloneDeep } from "lodash";
import FormLabel from '../FormLabel.vue';
import InputServiceId from './InputServiceId.vue'
import InputServiceName from './InputServiceName.vue';
import InputReceiptCreatorName from './InputReceiptCreatorName.vue';
import InputReceiptDisplayAdress from './InputReceiptDisplayAddress.vue';
import InputTaxType from './InputTaxType.vue';
import InputCalculationType from './InputCalculationType.vue';
import InputRoundingType from './InputRoundingType.vue';
import InputReservationServiceType from './InputReservationServiceType.vue';
import ImputReservationCostType from './InputReservationCostType.vue';
import InputPurchaseLimit from './InputPurchaseLimit.vue';

// old imports
// 旧インポート
/*import {
  SAVE_PAYMENT_SERVICE,
} from "@/store/action-types";
import ModalHeader from '../ModalHeader.vue';
*/

// store
const paymentStore = usePaymentStore();

// emits 
const emits = defineEmits<{
  (event: 'close'): void;
}>();

const $q = useQuasar();

// props
const props = defineProps<{
  visible: boolean,
  action: ModalAction,
  selectedItem: Service<number> | null,
}>();

// data
const item = ref<Service<string>>(DEFAULT_SERVICE_VALUES);
const model = new PaymentServiceModel;
const validator = new PaymentServiceValidatorModel;
const isSaving = ref<boolean>(false);
const formRef = ref();

// methods
// const savePaymentService = store.savePaymentService;
const showSaveConfirm = () => {
  $q.dialog({
    title: '保存内容の確認',
    message: '入力内容で保存してもよろしいですか？',
    cancel: true,
  }).onOk(async () => {
    await save();
  });
};

const save = async () => {
  try {
    validator.validateService(item.value);
    const payload = model.toPayload(item.value);
    isSaving.value = true;
    await paymentStore.savePaymentService({
      payload,
      action: props.action,
    });
    $q.notify({ message: '保存が成功しました。' });
    show.value = false;
  } catch (error: any) {    
    const responseCode = error.response?.data?.code;
    if (responseCode === "duplicate_service"){
      $q.notify({ message: 'サービスID は既に存在しています。', type: 'error' });
    } else if (typeof(error) === 'object') {
      console.log(error);
      $q.notify({ message: error.message, type: 'error' });
    } else {
      console.log(error);
      $q.notify({ message: error, type: 'error' });
    }
  } finally {
    isSaving.value = false;
  }
};

// computed
const show = computed({
  get() {
    return props.visible;
  },
  set(value) {
    if (!value) {
      emits("close");
    }
  },
});

const isCreateMode = computed((): boolean => {
  return props.action === MODAL_ACTIONS.CREATE
});

const isDisabledReservationCost = computed((): boolean => {
  return item.value.reservationServiceType === RESERVATION_SERVICE_TYPES.NOT_APPLICABLE;
});

// watch
watch(
  () => props.visible,
  () => {
    item.value = props.selectedItem ? model.toInput(props.selectedItem) : cloneDeep(DEFAULT_SERVICE_VALUES);
    formRef.value && formRef.value.resetValidation();
  }
);
</script>