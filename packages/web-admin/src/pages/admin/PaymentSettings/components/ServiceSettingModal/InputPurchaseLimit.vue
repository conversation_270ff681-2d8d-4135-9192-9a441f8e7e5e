<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div class="tw-mx-0 row">
    <div class="tw-py-0 tw-px-0 col">
      <q-input
        v-model="purchaseLimitInput"
        @keydown="numberInputOnKeyDown"
        type="number"
        style="max-width:100px;"
        placeholder="0"
        outlined
        dense
        :clearable="false"
        :min="quantityLimit.min"
        :max="quantityLimit.max"
        :error="!!error"
      ></q-input>
      <div class="tw-mt-[-15px] tw-text-red-500 tw-text-xs">
        {{ error }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, ref, computed, watch } from 'vue';
import { INVALID_SERVICE_MESSAGES, PAYMENT_PURCHASE_QUANTITY_LIMIT } from '@/stores/modules/payment/payment.constants'
import { PaymentServiceValidatorModel } from '@/model/Payment/PaymentService';

// emits 
const emits = defineEmits<{
  (event: 'change', payload: any): void;
}>();

// props
const props = defineProps({
  purchaseLimit: { type: String as PropType<string>, required: true }
});

const validator = new PaymentServiceValidatorModel();

const error = ref('')

watch(() => props.purchaseLimit, (value) => {
  const validationResult = rule(value)
  error.value = typeof validationResult === 'string' ? validationResult : ''
});

// methods
const rule = (value: string): boolean | string => {
  return validator.isValidPurchaseLimit(value) || INVALID_SERVICE_MESSAGES.PURCHASE_LIMIT;
};

const numberInputOnKeyDown = (e: any): boolean => {
  const char = e.key;
  if (['-', '.', 'e'].includes(char)) {
    e.preventDefault();
    return false;
  } else {
    return true;
  }
};

// computed
const purchaseLimitInput = computed({
  get(): string {
    return props.purchaseLimit;
  },
  set(value: string): void {
    emits('change', value);
  }
});

const quantityLimit = computed(() => {
  return {
    min: PAYMENT_PURCHASE_QUANTITY_LIMIT.MIN,
    max: PAYMENT_PURCHASE_QUANTITY_LIMIT.MAX,
  }
});
</script>
