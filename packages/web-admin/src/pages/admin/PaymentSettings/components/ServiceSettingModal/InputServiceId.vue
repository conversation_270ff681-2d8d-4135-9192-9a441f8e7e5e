<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div class="tw-mx-0 row">
    <div class="tw-px-0 tw-pt-0 col">
      <q-input
        v-model="serviceIdInput"
        :rules="[rule]"
        :maxlength="validLength"
        :disable="isDisabled"
        outlined
        dense
      ></q-input>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, ref,  computed,  } from 'vue';
import { PaymentServiceValidatorModel } from '@/model/Payment/PaymentService';
import { INVALID_SERVICE_MESSAGES } from '@/stores/modules/payment/payment.constants'

// emits 
const emits = defineEmits<{
  (event: 'change', payload: any): void;
}>();

// props
const props = defineProps({
  serviceId: { type: String as PropType<string>, required: true },
  isDisabled: { type: Boolean as PropType<boolean>, required: true }
});

const validLength = 3;
const validator = new PaymentServiceValidatorModel();

// methods
const rule = (value: string): boolean | string => {
  return validator.isValidServiceId(value) || INVALID_SERVICE_MESSAGES.SERVICE_ID;
};

// computed
const serviceIdInput = computed({
  get(): string {
    return props.serviceId;
  },
  set(value: string): void {
    emits('change', value);
  },
});
</script>
