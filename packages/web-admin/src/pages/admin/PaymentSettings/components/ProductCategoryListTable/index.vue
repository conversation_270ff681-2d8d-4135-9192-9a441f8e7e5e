<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div>
    <div class="tw-px-4 tw-py-0">
      <div class="tw-py-4 tw-px-3 tw-justify-end row">
        <div class="tw-flex tw-align-center">
          <q-btn
            class="tw-mr-2"
            color="negative"
            :disabled="!isDeletable"
            @click="showDeleteProductCategoryModal"
          >
            <!-- :style="
              hasActionPermission('hideButton', 'PaymentSettings_ProductCategory_Delete')
                ? hideButtonPermissionStyle()
                : ''
            " -->
            <q-icon size="20" left name="mdi-trash-can-outline"></q-icon>削除
          </q-btn>
          <q-btn
            color="primary"
            @click="$emit('showProductCategoryRegistModal')"
          >
            <!-- :style="
              hasActionPermission('hideButton', 'PaymentSettings_ProductCategory_Update')
                ? hideButtonPermissionStyle()
                : ''
            " -->
            <q-icon size="20" left name="mdi-plus"></q-icon>新規登録
          </q-btn>
        </div>
      </div>
      <q-separator class="tw-mb-3" />
      <div class="tw-mx-0 row">
        <div class="tw-flex tw-items-center">
          <span class="text-h4 tw-pl-4 tw-mr-1">{{ productCategoryList.length }}</span>
          <span>件</span>
          <q-separator vertical class="tw-mx-4" />
          <q-btn class="text-white" color="blue-grey" :loading="isLoading" @click="handleFetchProductCategoryList">
            <q-icon left name="mdi-reload"></q-icon>
            データ更新
          </q-btn>
        </div>
      </div>
      <q-separator class="tw-mt-3" />
    </div>

    <div class="tw-px-3 tw-py-0">
      <q-table
        :columns="headers"
        :rows="productCategoryList"
        :loading="isLoading"
        show-select
        row-key="id"
        class="tw-mt-3"
        flat
        selection="multiple"
        :selected="selected"
        @selection="selectTableRow"
        :pagination="{ rowsPerPage: perOnPage }"
      >
        <template v-slot:body-cell-copy="item">
          <td>
            <q-btn
              color="secondary"
              outline
              style="font-size:12px;"
              @click="onCopy(item.row.id)"
            >
              商品分類IDをコピー
            </q-btn>
          </td>
        </template>
        <template v-slot:body-cell-edit="item">
          <td>
            <q-btn
              :color="item.row ? 'primary' : ''"
              style="font-size:12px;"
              @click="$emit('showProductCategoryEditModal', item.row)"
            >
              編集
            </q-btn>
          </td>
        </template>
      </q-table>
    </div>

    <DeleteProductCategoryModal
      :selectedItems="selected"
      :visible="isShowDeleteProductCategoryModal"
      @close="isShowDeleteProductCategoryModal = false"
      @removeToSelected="removeToSelected"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useQuasar } from 'quasar';
import { usePaymentStore } from '@/stores/modules/payment';
import { useRoute } from 'vue-router';
import DeleteProductCategoryModal from '../DeleteProductCategoryModal/index.vue';

// store
const paymentStore = usePaymentStore();

const $q = useQuasar();

// route
const route = useRoute();

// data
const headers = ref<any>([
  {
    name: 'name',
    label: '商品分類名',
    field: 'name',
    sortable: true,
    width: '50%',
    align: 'left'
  },
  {
    name: 'id',
    label: '商品分類ID',
    field: 'id',
    sortable: true,
    width: '20%',
    align: 'left'
  },
  {
    name: "copy",
    label: "",
    field: "copy",
    sortable: false,
    width: "20%",
    align: 'left'
  },
  {
    name: "edit",
    label: "",
    field: "edit",
    sortable: false,
    width: "10%",
    align: 'left'
  },
]);

const perOnPage = ref(10);
const isShowDeleteProductCategoryModal = ref(false);
const isLoading = ref(false);
const selected = ref<any>([]);

// methods
const showDeleteProductCategoryModal = (): void => {
  isShowDeleteProductCategoryModal.value = true;
};

const removeToSelected = async (ids: string[]) => {
  await handleFetchProductCategoryList();
  selected.value = selected.value.filter((s: any) => !ids.includes(s.id));
};

const onCopy = (text: string): void => {
  navigator.clipboard.writeText(text);
  $q.notify({ message: 'クリップボードにコピーしました。' });
};

const handleFetchProductCategoryList = async (): Promise<void> => {
  try {
    isLoading.value = true;
    const serviceId = route.params.serviceId.toString();
    await paymentStore.fetchProductCategoryList(serviceId);
  } catch (error: any) {
    if (typeof(error) === 'object') {
      $q.notify({ message: error.message, type: 'error' });
    }
    else {
      $q.notify({ message: error, type: 'error' });
    }
  } finally {
    isLoading.value = false;
  }
};

const selectTableRow = (details: any) => {
  if (selected.value.length === 0) {
    selected.value = details.rows;
  }
  else if (details.rows.length !== 1) {
    if (selected.value.length !== details.rows.length) {
      selected.value = details.rows;
    }
    else {
      selected.value = [];
    }
  }
  else {
    let isfilter = false;
    selected.value = selected.value.filter((item: any) => {
      if (item.id === details.rows[0].id) {
        isfilter = true;
      }
      return item.id !== details.rows[0].id;
    });

    if (!isfilter) {
      selected.value.push(details.rows[0]);
    }
  }
};

// computed
const productCategoryList = computed(() => paymentStore.productCategoryList);

const isDeletable = computed((): boolean => {
  return selected.value.length > 0;
});
</script>