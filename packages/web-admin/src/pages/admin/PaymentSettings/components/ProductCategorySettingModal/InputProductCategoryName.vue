<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div class="mx-0 row">
    <div class="tw-p-0 col">
      <q-input
        v-model="productCategoryNameInput"
        :rules="[rule]"
        outlined
        dense
      ></q-input>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, computed } from 'vue';
import { ProductCategoryValidatorModel } from '@/model/Payment/ProductCategory';
import { INVALID_PRODUCT_CATEGORY_MESSAGES } from '@/stores/modules/payment/payment.constants'

// emits 
const emits = defineEmits<{
  (event: 'change', payload: any): void;
}>();

// props
const props = defineProps({
  productCategoryName: { type: String as PropType<string>, required: true }
});

const validator = new ProductCategoryValidatorModel();

// methods
const rule = (value: string): boolean | string => {
  return validator.isValidProductCategoryName(value)
    || INVALID_PRODUCT_CATEGORY_MESSAGES.PRODUCT_CATEGORY_NAME;
};

// computed
const productCategoryNameInput = computed({
  get(): string {
    return props.productCategoryName;
  },
  set(value: string): void {
    emits('change', value);
  },
});
</script>
