<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div class="tw-mt-4">
    <SubAppBar>
      <div class="tw-flex tw-justify-between tw-align-center">
        <div class="body-2 text-blue-grey tw-flex tw-align-center">
          <q-icon size="xs" class="tw-pr-2" name="mdi-cart-outline"></q-icon>
          <span>決済：</span>
          <span v-if="isServiceListView">サービス一覧</span>
          <span v-else>
            <router-link :to="{ name: 'PaymentSettingsPage' }" class="text-primary">サービス一覧</router-link>
            <span class="tw-px-2">-</span>
            <span>商品設定（サービス名:{{ serviceName }}</span>
            <span class="tw-px-2">/</span>
            <span>サービスID:{{ serviceId }}）</span>
          </span>
        </div>
        <div>
          <q-btn color="primary" @click="showTaxRateSettingModal" v-show="isServiceListView">
            <q-icon left name="mdi-calculator"></q-icon>
            消費税設定
          </q-btn>
        </div>
      </div>
      <TaxRateSettingModal
        :visible="isShowTaxRateSettingModal"
        @close="isShowTaxRateSettingModal = false"
      />
    </SubAppBar>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, defineProps } from 'vue';
import { Service } from '@/stores/modules/payment/payment.types';
import SubAppBar from '@/components/common/SubAppBar.vue';
import TaxRateSettingModal from '@/pages/admin/PaymentSettings/components/TaxRateSettingModal/index.vue';

// props
const props = defineProps<{
  service?: Service<number> | null,
}>();

// data
const isShowTaxRateSettingModal = ref(false);

// computed
const isServiceListView = computed(() => {
  return !props.service
});

const serviceId = computed(() => {
  return props.service ? props.service.serviceId : '';
});

const serviceName = computed(() => {
  return props.service ? props.service.serviceName : '';
});

// methods
const showTaxRateSettingModal = (): void => {
  isShowTaxRateSettingModal.value = true;
};
</script>