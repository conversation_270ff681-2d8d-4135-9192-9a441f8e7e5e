<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div v-if="!isLoading">
    <Navigation :service="service" />
    <InformationAccordion />
    <q-card flat bordered>
      <q-tabs v-model="activeComponent" active-color="primary">
        <q-tab v-for="(menu, i) in tabMenues" :key="i" :name="menu.component" class="tw-px-0 col-6">
          <span
            :color="menu.component === activeComponent ? 'primary' : 'blue-grey'"
            :key="i"
            class="tw-px-0"
            @click="setActiveComponent(menu.component)"
          >{{ menu.title }}
          </span>
        </q-tab>
      </q-tabs>

      <q-tab-panels v-model="activeComponent" style="width: 100%; background: transparent;">
        <q-tab-panel name="ProductList">
          <ProductList />
        </q-tab-panel>
        <q-tab-panel name="ProductCategoryList">
          <ProductCategoryList />
        </q-tab-panel>
      </q-tab-panels>
    </q-card>

  </div>
  <div v-else class="tw-flex tw-justify-center tw-items-center" style="height: 500px">
    <q-spinner :size="50" color="primary" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeMount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { Service } from '@/stores/modules/payment/payment.types';
import { FetchPaymentService } from '@/services/payment.service';
import Navigation from '@/pages/admin/PaymentSettings/components/Navigation/index.vue';
import InformationAccordion from '@/pages/admin/PaymentSettings/components/InformationAccordion/index.vue';
import ProductList from './ProductList.vue';
import ProductCategoryList from './ProductCategoryList.vue';

const router = useRouter();
const route = useRoute();
const $q = useQuasar();

type ActiveComponent = 'ProductList' | 'ProductCategoryList';

// data
const activeComponent = ref<ActiveComponent>('ProductList');

const tabMenues = ref<any>([
  {
    component: 'ProductList',
    title: '商品'
  },
  {
    component: 'ProductCategoryList',
    title: '商品分類'
  }
]);

const isFetchingService = ref<boolean>(false);
const service = ref<Service<number> | null>(null);

// methods
const setActiveComponent = (componentName: ActiveComponent): void => {
  activeComponent.value = componentName;
};

const handleFetchPaymentService = async (): Promise<Service<number> | undefined> => {
  try {
    isFetchingService.value = true;
    const results = await FetchPaymentService(selectedServiceId.value.toString());
    if (results.code == 'not_found') {
      $q.notify({ message: results.errorMessage, type: 'error' });
      router.push({ name: "PaymentSettingsPage" });
    } else if (results.code !== 'success') {
      throw results.errorMessage;
    }
    return results.data;
  } catch (error: any) {
    if (typeof(error) === 'object') {
      $q.notify({ message: error.message, type: 'error' });
    }
    else {
      $q.notify({ message: error, type: 'error' });
    }
  } finally {
    isFetchingService.value = false;
  }
};

// computed
const selectedServiceId = computed(() => {
  return route.params.serviceId;
});

const isLoading = computed((): boolean => {
  return isFetchingService.value;
});

// hooks
onBeforeMount(async () => {
  const hfpService = await handleFetchPaymentService();
  service.value = hfpService || null;
});
</script>