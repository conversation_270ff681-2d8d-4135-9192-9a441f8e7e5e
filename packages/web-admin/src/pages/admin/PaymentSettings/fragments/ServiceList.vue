<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div v-if="!isLoading">
    <div class="tw-px-1 tw-py-0">
      <div class="tw-mx-0 tw-py-4 tw-px-3 tw-justify-end row">
        <div class="tw-flex tw-align-center">
          <q-btn
            class="tw-mr-2"
            color="negative"
            :disable="!selected.length"
            @click="showDeleteServiceModal"
          >
            <!-- :style="
              hasActionPermission('hideButton', 'PaymentSettings_Service_Delete')
                ? hideButtonPermissionStyle()
                : ''
            " -->
            <q-icon size="20" left name="mdi-trash-can-outline"></q-icon>削除
          </q-btn>
          <q-btn
            color="primary"
            @click="showServiceSettingModal(null)"
          >
            <!-- :style="
              hasActionPermission('hideButton', 'PaymentSettings_Service_Update')
                ? hideButtonPermissionStyle()
                : ''
            " -->
            <q-icon size="20" left name="mdi-plus"></q-icon>新規登録
          </q-btn>
        </div>
      </div>
      <q-separator class="tw-mb-3" />
      <div class="tw-mx-0 row">
        <div class="tw-flex tw-items-center">
          <span class="text-h4 tw-pl-4 tw-mr-1">{{ paymentServiceList.length }}</span>
          <span>件</span>
          <q-separator vertical class="tw-mx-4" />
          <q-btn class="text-white" color="blue-grey" @click="handleFetchPaymentServiceList">
            <q-icon left name="mdi-reload"></q-icon>
            データ更新
          </q-btn>
        </div>
      </div>
      <q-separator class="tw-mt-3" />
    </div>

    <div class="tw-px-3 tw-py-0 tw-pb-1">
      <q-table
        :columns="headers"
        :rows="paymentServiceList"
        row-key="serviceId"
        :loading="isLoading"
        flat
        class="tw-mt-3"
        selection="multiple"
        :selected="selected"
        @selection="selectTableRow"
        :pagination="{ rowsPerPage: perOnPage }"

      >
        <template v-slot:body-cell-purchaseLimit="item">
          <td>
            <div>{{ purchaseLimit(item.row.purchaseLimit) }}</div>
          </td>
        </template>
        <template v-slot:body-cell-taxType="item">
          <td>
            <div>{{ taxTypeToText(item.row.taxType) }}</div>
          </td>
        </template>
        <template v-slot:body-cell-calculationType="item">
          <td>
            <div>{{ calculationTypeToText(item.row.calculationType) }}</div>
          </td>
        </template>
        <template v-slot:body-cell-reservationServiceType="item">
          <td>
            <div>{{ reservationServiceTypeToText(item.row.reservationServiceType) }}</div>
          </td>
        </template>
        <template v-slot:body-cell-productSettings="item">
          <td>
            <router-link v-if="item.row.sortKey" :to="{ name: 'ProductSettingsPage', params: { serviceId: item.row.sortKey } }">
              <q-btn :color="item.row ? 'primary' : ''" style="font-size:12px;">
                商品設定
              </q-btn>
            </router-link>
            <q-btn v-else disable color="primary" style="font-size:12px;">
              商品設定
            </q-btn>
          </td>
        </template>
        <template v-slot:body-cell-edit="item">
          <td>
            <q-btn
              :color="item.row ? 'primary' : ''"
              style="font-size:12px;"
              @click="showServiceSettingModal(item.row)"
            >
              編集
            </q-btn>
          </td>
        </template>
      </q-table>
    </div>

    <DeleteServiceModal
      :visible="isShowDeleteServiceModal"
      :selectedItems="selected"
      @close="isShowDeleteServiceModal = false"
      @removeToSelected="removeToSelected"
    />
  </div>
  <div v-else class="tw-flex tw-justify-center tw-items-center" style="min-height: 200px;">
    <q-spinner :size="50" color="primary" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed,  } from 'vue';
import { useQuasar } from 'quasar';
import { usePaymentStore } from '@/stores/modules/payment';
import { cloneDeep } from 'lodash';
import DeleteServiceModal from "@/pages/admin/PaymentSettings/components/DeleteServiceModal/index.vue";

// store
const paymentStore = usePaymentStore();

// emits 
const emits = defineEmits<{
  (event: 'showServiceSettingModal', payload: any): void;
}>();

const $q = useQuasar();

// data
const headers = ref<any>([
  {
    name: "serviceId",
    label: "サービスID",
    field: "serviceId",
    sortable: true,
    width: "13%",
    align: 'left'
  },
  {
    name: "serviceName",
    label: "サービス名",
    field: "serviceName",
    sortable: true,
    width: "30%",
    align: 'left'
  },
  {
    name: "purchaseLimit",
    label: "購入数上限",
    field: "purchaseLimit",
    sortable: true,
    width: "13%",
    align: 'left'
  },
  {
    name: "reservationServiceType",
    label: "カレンダー予約",
    field: "reservationServiceType",
    sortable: true,
    width: "15%",
    align: 'left'
  },
  {
    name: "taxType",
    label: "税区分",
    field: "taxType",
    sortable: true,
    width: "11%",
    align: 'left'
  },
  {
    name: "calculationType",
    label: "消費税計算方法",
    field: "calculationType",
    sortable: true,
    width: "25%",
    align: 'left'
  },
  {
    name: "productSettings",
    label: "",
    field: "productSettings",
    sortable: false,
    width: "8%",
  },
  {
    name: "edit",
    label: "",
    field: "edit",
    sortable: false,
    width: "8%",
  },
]);

const perOnPage = ref<number>(10);
const selected = ref<any>([]);
const isShowDeleteServiceModal = ref<boolean>(false);
const isLoading = ref<boolean>(false);

// methods
const showServiceSettingModal = (item: any): void => {
  emits("showServiceSettingModal", cloneDeep(item));
};

const showDeleteServiceModal = (): void => {
  isShowDeleteServiceModal.value = true;
};

const removeToSelected = (): void => {
  selected.value = [];
};

const taxTypeToText = (taxType: number): string => {
  return taxType === 0 ? '外税' : '内税';
};

const calculationTypeToText = (calculationType: number): string => {
  return calculationType === 0 ? '明細ごと' : '合計';
};

const purchaseLimit = (purchaseLimit: number): string | number => {
  return purchaseLimit === 0 ? 'ー' : purchaseLimit;
};

const reservationServiceTypeToText = (reservationServiceType: number): string => {
  return reservationServiceType === 0 ? 'する' : 'しない';
};

const handleFetchPaymentServiceList = async (): Promise<void> => {
  try {
    isLoading.value = true;
    await paymentStore.fetchPaymentServiceList();
  } catch (error: any) {
    if (typeof(error) === 'object') {
      $q.notify({ message: error.message, type: 'error' });
    }
    else {
      $q.notify({ message: error, type: 'error' });
    }
  } finally {
    isLoading.value = false;
  }
};

const selectTableRow = (details: any) => {
  if (selected.value.length === 0) {
    selected.value = details.rows;
  }
  else if (details.rows.length !== 1) {
    if (selected.value.length !== details.rows.length) {
      selected.value = details.rows;
    }
    else {
      selected.value = [];
    }
  }
  else {
    let isfilter = false;
    selected.value = selected.value.filter((item: any) => {
      if (item.serviceId === details.rows[0].serviceId) {
        isfilter = true;
      }
      return item.serviceId !== details.rows[0].serviceId;
    });

    if (!isfilter) {
      selected.value.push(details.rows[0]);
    }
  }
};

// computed
const paymentServiceList = computed(() => paymentStore.paymentServiceList);
</script>