<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->

<template>
  <div>
    <ProductCategoryListTable
      v-if="productCategoryList.length > 0"
      @showProductCategoryRegistModal="showProductCategoryRegistModal"
      @showProductCategoryEditModal="showProductCategoryEditModal"
    />
    <ProdcutCategoryUnregistered
      v-else
      @showProductCategorySettingModal="showProductCategoryRegistModal"
    />
    <ProductCategorySettingModal
      :visible="isShowProductCategorySettingModal"
      :selectedItem="selectedProductCategory"
      :action="action"
      @close="isShowProductCategorySettingModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { usePaymentStore } from '@/stores/modules/payment';
import {ref,  computed,  } from 'vue';
import { ProductCategory, ModalAction } from '@/stores/modules/payment/payment.types';
import { MODAL_ACTIONS } from '@/stores/modules/payment/payment.constants';
import { cloneDeep } from 'lodash';
import ProductCategoryListTable from '../components/ProductCategoryListTable/index.vue';
import ProdcutCategoryUnregistered from './ProductCategoryUnregistered.vue';
import ProductCategorySettingModal from '../components/ProductCategorySettingModal/index.vue';

// store
const paymentStore = usePaymentStore();

// data
const isShowProductCategorySettingModal = ref<boolean>(false);
const selectedProductCategory = ref<ProductCategory | null>(null);
const action = ref<ModalAction>(MODAL_ACTIONS.CREATE);

// methods
const showProductCategorySettingModal = (): void => {
  isShowProductCategorySettingModal.value = true;
};

const showProductCategoryRegistModal = (): void => {
  selectedProductCategory.value = null;
  action.value = MODAL_ACTIONS.CREATE;
  showProductCategorySettingModal();
};

const showProductCategoryEditModal = (productCategroy:ProductCategory): void => {
  selectedProductCategory.value = cloneDeep(productCategroy);
  action.value = MODAL_ACTIONS.EDIT;
  showProductCategorySettingModal();
};

// computed
const productCategoryList = computed(() => paymentStore.productCategoryList);
</script>