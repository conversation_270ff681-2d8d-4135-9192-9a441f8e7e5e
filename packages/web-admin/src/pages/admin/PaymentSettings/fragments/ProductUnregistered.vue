<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div class="tw-p-6">
    <q-card flat class="tw-p-6">
      <p class="tw-text-center tw-mb-6">
        商品がまだありません。
        <br>商品を新規登録してください。
        <br><span class="text-red">登録できる商品数の上限は1,000件です。</span>
      </p>
      <div class="tw-flex tw-justify-center">
        <q-btn
          color="primary"
          @click="$emit('showProductSettingModal')"
        >
          <!-- :style="
            hasActionPermission('hideButton', 'PaymentSettings_Service_Delete')
              ? PaymentSettings_Product_Update()
              : ''
          " -->
          <q-icon left name="mdi-plus"></q-icon>
          新規登録
        </q-btn>
        <q-btn
          color="primary"
          class="tw-ml-6 tw-mr-2"
          @click="$emit('showCsvImportModal')"
        >
          <!-- :style="
            hasActionPermission('hideButton', 'PaymentSettings_Product_ImportCsvFile')
              ? hideButtonPermissionStyle()
              : ''
          " -->
          CSVインポート
          <q-icon class="icon-csv" name="mdi-upload"></q-icon>
        </q-btn>
        <a
          target="_blank"
          download="商品サンプル.csv"
          :href="href"
        >
          <!-- :style="
            hasActionPermission('hideButton', 'PaymentSettings_Product_DownloadSampleCsvFile')
              ? hideButtonPermissionStyle()
              : ''
          " -->
          <q-btn color="primary" outline>
            CSVサンプル
            <q-icon class="icon-csv" name="mdi-download"></q-icon>
          </q-btn>
        </a>
      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';

// route
const route = useRoute();

// computed
const sampleContents = computed(() => {
  const routeParams: string = route.params.serviceId.toString();
  const serviceId = `="${routeParams.replace(/-\S+$/, "")}"`; 
  return [
    ['サービスID', '商品ID', '商品名', '商品分類ID', '単価', '課税/非課税', 'ステータス', '並び順'],
    [serviceId, '111', '商品A', 'category#000000|category#111111', '100','課税', '販売中', 1],
    [serviceId, '222', '商品B', 'category#000000', '200', '非課税', '販売停止', 2],
  ];
});

const href = computed((): string => {
  const csvContents = sampleContents.value.reduce((accContents: string, content: (number | string)[]) => {
    return accContents + content.join(',') + '\n';
  }, '');
  const bom = new Uint8Array([0xEF, 0xBB, 0xBF]);
  const blob = new Blob([bom, csvContents], { type: 'text/csv' });
  return URL.createObjectURL(blob);
});
</script>

<style scoped>
.icon-csv {
  padding-left: 9.5px;
}
</style>