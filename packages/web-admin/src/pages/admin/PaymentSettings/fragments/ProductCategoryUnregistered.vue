<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-card flat class="tw-p-5">
      <p class="tw-text-center tw-pb-6">
        商品分類がまだありません。
        <br>商品分類を登録してください。
        <br><span class="text-red">登録できる商品分類数の上限は100件です。</span>
      </p>
      <div class="tw-flex tw-justify-center">
        <q-btn
          color="primary"
          @click="$emit('showProductCategorySettingModal')"
        >
          <!-- :style="
            hasActionPermission('hideButton', 'PaymentSettings_ProductCategory_Update')
              ? hideButtonPermissionStyle()
              : ''
          " -->
          <q-icon left name="mdi-plus"></q-icon>登録する
        </q-btn>
      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
defineEmits(['showProductCategorySettingModal']);
</script>