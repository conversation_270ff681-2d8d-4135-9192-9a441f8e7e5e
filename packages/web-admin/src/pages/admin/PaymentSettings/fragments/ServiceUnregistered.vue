<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-card flat bordered class="tw-p-5 card">
      <div>
        <p class="tw-text-center">サービスがまだありません。<br>サービスを新規登録してください。</p>
      </div>
      <div class="tw-flex tw-justify-center tw-mt-3">
        <q-btn
          color="primary"
          @click="showServiceSettingModal"
        >
           <!-- :style="
              hasActionPermission('hideButton', 'PaymentSettings_Service_Update')
                ? hideButtonPermissionStyle()
                : ''
            " -->
          <q-icon left name="mdi-plus"></q-icon>新規登録
        </q-btn>
      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">

// emits 
const emits = defineEmits<{
  (event: 'showServiceSettingModal', payload: any): void;
}>();

// methods
const showServiceSettingModal = (): void => {
  emits("showServiceSettingModal", null);
};
</script>

<style scoped>
.card {
  max-width: 480px;
  width: 100%;
  margin: 0 auto;
}
</style>