<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-overlay :opacity="0.2" v-if="firstLoading">
      <content-loading :size="50" text="" />
    </q-overlay>
    <div v-if="!firstLoading">
      <div class="text-right mt-4 row">
        <router-link :to="{ name: 'User', params: { tab: 'groups' } }" class="list-item-link">
          <q-btn color="primary" class="mx-4" text>
            <q-icon left>mdi-arrow-left-circle</q-icon>
            グループに戻る
          </q-btn>
        </router-link>
        <q-space></q-space>
        <q-btn color="primary" class="mr-2" @click="onEditGroup"> グループ編集 </q-btn>
        <q-btn color="error" class="mr-3" @click="onDeleteGroup"> グループ削除 </q-btn>
        <q-btn class="mr-2" @click="onFetchGroupData" text fab small :ripple="false">
          <q-icon color="primary">
            {{ isFetchingGroupDetail || isFetchingGroupUsers ? "mdi-cached mdi-spin" : "mdi-cached" }}
          </q-icon>
        </q-btn>
      </div>

      <EditGroupModal :visible="editGroupModal" @close="editGroupModal = false" />

      <q-card outlined class="mt-3">
        <q-row no-gutters class="mt-2">
          <q-col :cols="gridCols.label">
            <q-item-label><strong>グループ名</strong></q-item-label>
          </q-col>
          <q-col :cols="gridCols.input">
            <q-item-label>{{ groupDetail.groupName }}</q-item-label>
          </q-col>
        </q-row>
        <q-row no-gutters>
          <q-col :cols="gridCols.label">
            <q-item-label><strong>説明</strong></q-item-label>
          </q-col>
          <q-col :cols="gridCols.input">
            <q-item-label>{{ groupDetail.description }}</q-item-label>
          </q-col>
        </q-row>
        <q-row no-gutters>
          <q-col :cols="gridCols.label">
            <q-item-label><strong>優先順位</strong></q-item-label>
          </q-col>
          <q-col :cols="gridCols.input">
            <q-item-label>{{ groupDetail.precedence }}</q-item-label>
          </q-col>
        </q-row>
        <q-row no-gutters>
          <q-col :cols="gridCols.label">
            <q-item-label><strong>更新日</strong></q-item-label>
          </q-col>
          <q-col :cols="gridCols.input">
            <q-item-label>{{ datetimeFormatter.formatToYYYYMMDHHmmss(groupDetail.updatedAt) }}</q-item-label>
          </q-col>
        </q-row>
        <q-row no-gutters class="mb-2">
          <q-col :cols="gridCols.label">
            <q-item-label><strong>作成日</strong></q-item-label>
          </q-col>
          <q-col :cols="gridCols.input">
            <q-item-label>{{ datetimeFormatter.formatToYYYYMMDHHmmss(groupDetail.createdAt) }}</q-item-label>
          </q-col>
        </q-row>
      </q-card>

      <div class="text-right mt-4">
        <q-btn color="primary" @click="onAddUser"> ユーザ追加 </q-btn>
      </div>
      <q-card outlined class="mt-4">
        <q-page-container>
          <q-table
            v-model="selected"
            :columns="userHeaders"
            :rows="groupUsers"
            :loading="isFetchingGroupUsers"
            :items-per-page="perOnPage"
            :page="page"
            class="elevation-0 mt-0"
          >
            <!-- <template v-slot:item.userName="{ item }" style="vertical-align: inherit">
              <div>
                <router-link v-if="item.userId" :to="onUserDetail(item)" style="text-decoration: none">
                  {{ item.userName }}
                </router-link>
              </div>
            </template>
            <template v-slot:item.enabled="{ item }">
              <div>
                <q-icon v-if="item.enabled" color="primary">mdi-check-circle-outline</q-icon>
                <q-icon v-else color="red">mdi-close-circle-outline</q-icon>
              </div>
            </template>
            <template v-slot:item.updatedAt="{ item }">
              {{ datetimeFormatter.formatToYYYYMMDHHmmss(item.updatedAt) }}
            </template>
            <template v-slot:item.createdAt="{ item }">
              {{ datetimeFormatter.formatToYYYYMMDHHmmss(item.createdAt) }}
            </template>
            <template v-slot:item.delete="{ item }">
              <q-icon
                class="mx-1"
                small
                @click="
                  hasActionPermission('click', 'backendRequest')
                    ? onRemoveUser(item.userName)
                    : showActionPermissionError()
                "
              >
                mdi-delete
              </q-icon>
            </template> -->
          </q-table>
        </q-page-container>
      </q-card>

      <Alert
        v-model="hasError"
        class="mt-4"
        color="red"
        border="left"
        elevation="1"
        dismissible
        colored-border
        type="error"
      >
        {{ errorMessage }}
      </Alert>

      <AddGroupUsersModal :groupUsers="groupUsers" :visible="addGroupUsersModal" @close="addGroupUsersModal = false" />
    </div>
    <q-dialog v-model="isFetchingUserGroups" persistent width="300">
      <q-card>
        <q-row justify="center" align="center">
          <q-col md="1">
            <q-circular-progress size="50" color="primary" indeterminate class="ml-n4"> </q-circular-progress>
          </q-col>
        </q-row>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref,  computed, onBeforeMount } from 'vue';
import { useQuasar } from 'quasar';
import { useUsersStore } from '@/stores/modules/users';
import { USER_HEADERS } from "@/stores/modules/users/users.constants";
import { useRoute } from 'vue-router';
import { useDatetimeFormatter } from "@/mixins/DatetimeFormatter";

// old imports
// 旧インポート
/*import {
  FETCH_GROUP_LIST,
  FETCH_GROUP_DETAIL,
  FETCH_GROUP_USERS,
  FETCH_USER_GROUPS,
  REMOVE_USER_FROM_GROUP,
} from "@/store/modules/users/actions-types";
import EditGroupModal from "@/pages/admin/UserSettings/components/EditGroupModal";
import AddGroupUsersModal from "@/pages/admin/UserSettings/components/AddGroupUsersModal";
*/

// quasar
const $q = useQuasar();

// route
const route = useRoute();

// store
const usersStore = useUsersStore();

// other
const datetimeFormatter = useDatetimeFormatter();

// data
const perOnPage = ref(10);
const page = ref(1);
const selected = ref<any>([]);
const userHeaders = ref<any>([...USER_HEADERS, { text: "削除", value: "delete", sortable: false }]);
const gridCols = ref<any>({
  label: 3,
  input: 9,
});
const firstLoading = ref(false);
const editGroupModal = ref(false);
const addGroupUsersModal = ref(false);
const hasError = ref(false);
const errorMessage = ref("");

// methods
const fetchGroups = usersStore.fetchGroupList;
const fetchGroupDetail = usersStore.fetchGroupDetail;
const fetchGroupUsers = usersStore.fetchGroupUsers;
const fetchUserGroups = usersStore.fetchUserGroups;
const removeGroup = usersStore.removeUserFromGroup;

const onFetchGroupData = async () => {
  await fetchGroupDetail(route.params.groupName);
  await fetchGroupUsers(route.params.groupName);

  if (!fetchGroupDetailError.value && !fetchGroupUsersError.value) {
    if (!firstLoading.value) {
      $q.notify({
        message: "グループ詳細を更新しました。",
      });
    }
    firstLoading.value = false;
  }
};

const onAddUser = () => {
  addGroupUsersModal.value = true;
};

const onRemoveUser = async (userName: any) => {
  await fetchUserGroups();

  if (!fetchUserGroupsError.value) {
    if (userGroups.value.length > 1) {
      $q.dialog({
        title: "ユーザ退会確認",
        message: "このユーザを退会してもよろしいですか？",
        ok: {
          label: "はい",
        },
        cancel: true,
      }).onOk(async () => {
        try {
          await removeGroup({
            username: userName,
            groupname: groupDetail.value.groupName,
          });

          const successMessage = "ユーザを退会しました。";
          checkError(removeUserFromGroupError.value, successMessage);
        } catch {
        } finally {
          await fetchGroupUsers(groupDetail.value.groupName);
        }
      });
    } else {
      $q.dialog({
        // type: "warning",
        title: "エラー",
        message: "最低一つのグループに所属する必要があります。",
        ok: false,
      });
    }
  }
};

const onEditGroup = () => {
  editGroupModal.value = true;
};

const onDeleteGroup = () => {
  /*  $q.dialog({
    title: "グループ削除確認",
    text: "このグループを削除してもよろしいですか？",
    btnConfirmTitle: "はい",
    onConfirm: () => {
      $q.notify({message: "開発中。。。"})
    },
  }); */
};

const onUserDetail = (item: any) => {
  return {
    name: "UserDetail",
    params: {
      userName: item.userName,
    },
  };
};

const checkError = (errorMessage: any, successMessage: any) => {
  if (errorMessage.value) {
    hasError.value = true;
    errorMessage.value = errorMessage.value;
  } else {
    hasError.value = false;
    $q.notify({
      message: successMessage,
    });
  }
};

// computed
const groupDetail = computed(() => usersStore.groupDetail);
const isFetchingGroupDetail = computed(() => usersStore.isFetchingGroupDetail);
const fetchGroupDetailError = computed(() => usersStore.fetchGroupDetailError);
const groupUsers = computed(() => usersStore.groupUsers);
const isFetchingGroupUsers = computed(() => usersStore.isFetchingGroupUsers);
const fetchGroupUsersError = computed(() => usersStore.fetchGroupUsersError);
const userGroups = computed(() => usersStore.userGroups);
const isFetchingUserGroups = computed(() => usersStore.isFetchingUserGroups);
const fetchUserGroupsError = computed(() => usersStore.fetchUserGroupsError);
const isRemovingUserFromGroup = computed(() => usersStore.isRemovingUserFromGroup);
const removeUserFromGroupError = computed(() => usersStore.removeUserFromGroupError);

// hooks
onBeforeMount(() => {
  firstLoading.value = true;
  onFetchGroupData();
});
</script>
