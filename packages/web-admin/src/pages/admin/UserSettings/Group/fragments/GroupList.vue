<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-card outlined class="mt-4">
      <q-page-container>
        <q-row>
          <q-col cols="12" sm="9">
            <span class="display-1 px-1">{{ groupList.length }}</span
            ><span>件</span>
          </q-col>
          <q-col cols="12" sm="3">
            <div align="right">
              <q-btn color="primary" class="mr-3" @click="onShowAddGroupDialog">
                <q-icon left name="mdi-account-plus-outline"></q-icon>
                新規作成
              </q-btn>
              <q-btn @click="reloadData" text fab small :ripple="false">
                <q-icon color="primary"> {{ isFetchingGroupList ? "mdi-cached mdi-spin" : "mdi-cached" }}</q-icon>
              </q-btn>
            </div>
          </q-col>
        </q-row>

        <q-table
          v-model="selected"
          :columns="headers"
          :rows="groupList"
          :loading="isFetchingGroupList"
          :items-per-page="perOnPage"
          :page="page"
          class="elevation-0"
        >
          <!-- <template v-slot:item.groupName="{ item }" style="vertical-align: inherit">
            <div>
              <router-link :to="moveToGroupDetail(item)" style="text-decoration: none">
                {{ item.groupName }}
              </router-link>
            </div>
          </template>
          <template v-slot:item.updatedAt="{ item }">
            {{ formatToYYYYMMDHHmmss(item.updatedAt) }}
          </template>
          <template v-slot:item.createdAt="{ item }">
            {{ formatToYYYYMMDHHmmss(item.createdAt) }}
          </template> -->
        </q-table>
      </q-page-container>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { useUsersStore } from '@/stores/modules/users';
import { ref, computed } from 'vue';
import { GROUP_HEADERS } from "@/stores/modules/users/users.constants";

// old imports
// 旧インポート
/*import { FETCH_GROUP_LIST } from "@/store/modules/users/actions-types";
import DatetimeFormatter from "@/mixins/DatetimeFormatter";*/

// store
const usersStore = useUsersStore();

// data
const perOnPage = ref(10);
const page = ref(1);
const selected = ref<any>([]);
const headers = ref(GROUP_HEADERS);
const addPermissionDialog = ref(false);
const hasError = ref(false);
const errorMessage = ref("");

// methods
const fetchGroupList = usersStore.fetchGroupList;
const fetchData = async () => {
  await fetchGroupList();
};

const reloadData = () => {
  fetchData();
};

const onShowAddGroupDialog = () => {
  addPermissionDialog.value = true;
};

const moveToGroupDetail = (item: any) => {
  return {
    name: "GroupDetail",
    params: {
      groupName: item.groupName,
    },
  };
};

// computed
const groupList = computed(() => usersStore.groupList);
const isFetchingGroupList = computed(() => usersStore.isFetchingGroupList);
</script>
