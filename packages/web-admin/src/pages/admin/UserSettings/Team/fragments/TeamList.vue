<template>
  <div>
    <q-toolbar>
      <q-toolbar-title class="tw-font-semibold">
        チーム一覧
      </q-toolbar-title>
      <div>
        <q-btn class ="tw-mx-3" color="blue-grey" @click="fetchData">
          <q-icon left :name="isFetchingTeamList ? 'mdi-cached mdi-spin' : 'mdi-cached'" />
          データ更新
        </q-btn>
        <q-btn v-if="isAdministrator" color="primary" @click="goToEditTeam(true , '')">
          <q-icon left name="mdi-plus" />
          新規作成
        </q-btn>
      </div>
    </q-toolbar>
    <q-toolbar class="tw-pt-3">
      <div sm="6" class="tw-px-0 col">
        <q-select
          v-model="commonSearchCriteria"
          @filter="searchAttributeFilter"
          :options="teamSearchCriteriaOptions"
          :placeholder="teamSearchPlaceholder"
          option-label="text"
          option-value="value"
          flat
          outlined
          dense
          fill-input
          use-input
          hide-selected
          clearable
          clear-icon="close"
          @input-value="setModel"
        />
      </div>
      <div sm="6" class="tw-px-0 tw-pl-3 col">
        <div 
          v-if="selectedCriteria?.value === 'updatedAt'" 
          class="row"
        >
          <div class="col">
            <q-input
              v-model="updatedDateFrom"
              readonly
              outlined
              dense
              hide-details="auto"
              prepend-inner-icon="mdi-calendar-outline"
              clearable
              :placeholder="dateSearchPlaceholder"
            >
              <template v-slot:prepend>
                <q-icon name="mdi-calendar-outline" />
              </template>
              <q-menu
                v-model="menu_updatedDateFrom"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="290px"
              >
                <q-date
                  v-model="updatedDateFrom"
                  color="primary"
                  no-title
                  show-current
                  mask="YYYY-MM-DD"
                  @update:model-value="menu_updatedDateFrom = false"
                >
                </q-date>
              </q-menu>
            </q-input>
          </div>
          <div sm="auto" class="tw-mt-2">ー</div>
          <div class="col">
            <q-input
              v-model="updatedDateTo"
              readonly
              outlined
              dense
              hide-details="auto"
              clearable
              :placeholder="dateSearchPlaceholder"
            >
              <template v-slot:prepend>
                <q-icon name="mdi-calendar-outline" />
              </template>
              <q-menu
                v-model="menu_updatedDateTo"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="290px"
              >
                <q-date
                  v-model="updatedDateTo"
                  color="primary"
                  no-title
                  show-current
                  mask="YYYY-MM-DD"
                  @update:model-value="menu_updatedDateTo = false"
                >
                </q-date>
              </q-menu>
            </q-input>
          </div>
        </div>
        <div
          v-else-if="selectedCriteria?.value === 'createdAt'"
          class="row" 
        >
          <div class="col">
            <q-input
              v-model="createdDateFrom"
              readonly
              outlined
              dense
              hide-details="auto"
              clearable
              :placeholder="dateSearchPlaceholder"
            >
              <template v-slot:prepend>
                <q-icon name="mdi-calendar-outline" />
              </template>
              <q-menu
                v-model="menu_createdDateFrom"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="290px"
              >
                <q-date
                  v-model="createdDateFrom"
                  color="primary"
                  no-title
                  show-current
                  mask="YYYY-MM-DD"
                  @update:model-value="menu_createdDateFrom = false"
                />
              </q-menu>
            </q-input>
          </div>
          <div sm="auto" class="tw-mt-2">ー</div>
          <div class="col">
            <q-input
              v-model="createdDateTo"
              readonly
              outlined
              dense
              hide-details="auto"
              clearable
              :placeholder="dateSearchPlaceholder"
            >
              <template v-slot:prepend>
                <q-icon name="mdi-calendar-outline" />
              </template>
              <q-menu
                v-model="menu_createdDateTo"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="290px"
              >
                <q-date
                  v-model="createdDateTo"
                  color="primary"
                  no-title
                  show-current
                  mask="YYYY-MM-DD"
                  @update:model-value="menu_createdDateTo = false"
                >
                </q-date>
              </q-menu>
            </q-input>
          </div>
        </div>
        <q-input
          v-else
          v-model="stringSearch"
          hide-details
          outlined
          dense
          clearable
          clear-icon="close"
          :placeholder="stringSearchPlaceholder"
        />
      </div>
    </q-toolbar>
    <q-separator class="tw-mt-3 tw-mx-3" />
    <div class="elevation-0 tw-mx-3">
      <q-table
        v-model="selected"
        flat
        :columns="setTableHeaderForAdministorator"
        :rows="teamFilterList"
        :loading="isFetchingTeamList"
        :page="page"
        class="team-list-table"
        sort-by="updatedAt"
        :sort-desc="true"
        :pagination-label="(start, end, total) => `${ start }-${ end }件 / ${ total }件`"
        rows-per-page-label="1ページあたりの行数："
        table-header-class="tw-text-cyan-800"
        :pagination="pagination"
        :rows-per-page-options="pegeOptions"
      >
        <template v-slot:body-cell-updatedAt="item">
          <td>
            {{ datetimeFormatter.formatUnixToYYYYMMDHHmmss(item.row.updatedAt) }}
          </td>
        </template>
        <template v-slot:body-cell-createdAt="item">
          <td>
            {{ datetimeFormatter.formatUnixToYYYYMMDHHmmss(item.row.createdAt) }}
          </td>
        </template>
        <template v-slot:body-cell-editButton="item">
          <td>
            <q-btn color="primary" @click="goToEditTeam(false, item.row)" >
              <div style="font-size:12px;">編集</div>
            </q-btn>
          </td>
        </template>
      </q-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import  { computed, onMounted, ref, watch } from "vue";
import { useDatetimeFormatter } from "@/mixins/DatetimeFormatter";
import { DEFAULT_SELECTED_TEAM_CRITERIA, DEFAULT_TEAM_FILTER_KEYWORD } from "@/stores/modules/users/index";
import { TEAM_SEARCH_CRITERIA_OPTIONS } from "@/stores/modules/users/users.constants";
import { useUsersStore } from "@/stores/modules/users";
import { useRouter } from "vue-router";
import { useAuthStore } from "@/stores/modules/auth.module";

// router
const router = useRouter();

// store
const usersStore = useUsersStore();
const authStore = useAuthStore();

// formatter
const datetimeFormatter = useDatetimeFormatter();

// data
const page = ref(1);
const selected = ref([]);
const menu_updatedDateFrom = ref(false);
const menu_updatedDateTo = ref(false);
const menu_createdDateFrom = ref(false);
const menu_createdDateTo = ref(false);
const pagination = ref({
  rowsPerPage: 10,
  page: 1,
});
const pegeOptions = ref([5,10,15,0]);
const teamSearchCriteriaOptions = ref();

// computed
const teamFilterList = computed(() => usersStore.getTeamFilterList);
const isAdministrator = computed(() => authStore.isAdministrator);

const isFetchingTeamList = computed(() => usersStore.isFetchingTeamList);
const selectedCriteria = computed(() => usersStore.selectedTeamCriteria);
const filterKeyword = computed(() => usersStore.teamFilterKeyword);
const teamSearchPlaceholder = computed(() => commonSearchCriteria.value ? '' : '検索項目');

const commonSearchCriteria = computed({
  get() {
    return selectedCriteria.value;
  },
  set(value) {
    usersStore.setSelectedTeamCriteria(value);
  },
});

const stringSearch = computed({
  get() {
    return filterKeyword.value.stringSearch;
  },
  set(value) {
    usersStore.setTeamFilterKeyword({
      stringSearch: value,
    });
  },
});

const updatedDateFrom = computed({
  get() {
    return filterKeyword.value.updatedDateFrom ? datetimeFormatter.formatToYYYYMMDD(filterKeyword.value.updatedDateFrom) : null;
  },
  set(value) {
    usersStore.setTeamFilterKeyword({
      updatedDateFrom: value?.replaceAll('/', '-'),
    });
  },
});

const updatedDateTo = computed({
  get() {
    return filterKeyword.value.updatedDateTo ? datetimeFormatter.formatToYYYYMMDD(filterKeyword.value.updatedDateTo) : null;
  },
  set(value) {
    usersStore.setTeamFilterKeyword({
      updatedDateTo: value?.replaceAll('/', '-'),
    });
  },
});

const createdDateFrom = computed({
  get() {
    return filterKeyword.value.createdDateFrom ? datetimeFormatter.formatToYYYYMMDD(filterKeyword.value.createdDateFrom) : null;
  },
  set(value) {
    usersStore.setTeamFilterKeyword({
      createdDateFrom: value?.replaceAll('/', '-'),
    });
  },
});

const createdDateTo = computed({
  get() {
    return filterKeyword.value.createdDateTo ? datetimeFormatter.formatToYYYYMMDD(filterKeyword.value.createdDateTo) : null;
  },
  set(value) {
    usersStore.setTeamFilterKeyword({
      createdDateTo: value?.replaceAll('/', '-'),
    });
  },
});

const stringSearchPlaceholder = computed(() => {
  const placeholder = teamSearchCriteriaOptions.value?.find((obj: any) => {
    return obj.value === selectedCriteria.value?.value;
  });

  return placeholder ? "「" + placeholder.text + "」検索" : '検索';
});

const dateSearchPlaceholder = computed(() => {
  return "YYYY-MM-DD";
});

const setTableHeaderForAdministorator = computed(() => {
  if(isAdministrator.value) {
    return [
      {
        name: 'teamName',
        label: 'チーム名',
        field: 'teamName',
        align: 'left' as "left" | "right" | "center",
        sortable: true,
      },
      {
        name: 'updatedAt',
        label: '更新日',
        field: 'updatedAt',
        align: 'left' as "left" | "right" | "center",
        sortable: true,
      },
      {
        name: 'createdAt',
        label: '作成日',
        field: 'createdAt',
        align: 'left' as "left" | "right" | "center",
        sortable: true,
      },
      {
        name: 'editButton',
        label: '',
        field: 'editButton',
        align: 'left' as "left" | "right" | "center",
        sortable: false,
        style: 'width: 18px',
      },
    ];
  }
  else {
    return [
      {
        name: 'teamName',
        label: 'チーム名',
        field: 'teamName',
        align: 'left' as "left" | "right" | "center",
        sortable: true,
      },
      {
        name: 'updatedAt',
        label: '更新日',
        field: 'updatedAt',
        align: 'left' as "left" | "right" | "center",
        sortable: true,
      },
      {
        name: 'createdAt',
        label: '作成日',
        field: 'createdAt',
        align: 'left' as "left" | "right" | "center",
        sortable: true,
      },
    ];
  }
});

// watch
watch(() => selectedCriteria,
  (newVal) => {
    if (newVal) {
      resetFilterKeywords();
    }
  }
);

const resetFilterKeywords = () => {
  usersStore.setTeamFilterKeyword({ ...DEFAULT_TEAM_FILTER_KEYWORD });
};

const fetchData = async (): Promise<void> => {
  await usersStore.fetchTeamList();
};

const goToEditTeam = (newFlag:boolean ,item: any ) => {
  const teamIdUrl = !newFlag ? item.teamId : 'new';
  router.push({
    name: "EditTeam",
    params: {
      teamId: teamIdUrl,
    },
  });
};

const setModel = (val: any) => {
  if(!val){
    commonSearchCriteria.value = val;
  }
};

// mounted
onMounted(() => {
  usersStore.setSelectedTeamCriteria(DEFAULT_SELECTED_TEAM_CRITERIA);
  resetFilterKeywords();
});

const searchAttributeFilter = (value: string, update: any) => {
  searchFilter(value, update, teamSearchCriteriaOptions, TEAM_SEARCH_CRITERIA_OPTIONS);
};

const searchFilter = (value: string, update: any, currentOptions: any, options: any) => {
  if (value === '') {
    update(() => {
      currentOptions.value = options;
    });
    return;
  }
  else {
    update(() => {
      value = value.toLowerCase();
      currentOptions.value = options.filter((elm: any) => 
        elm.text.toLowerCase().indexOf(value) > -1
      );
    });
  }
};
</script>
<style>
.team-list-table>.v-data-table__wrapper>table>.v-data-table-header>tr>th>span{
    font-size:14px;
}
.toolBarGroup.d-flex.justify-space-between>.v-toolbar__content{
  display: flex;
  justify-content: space-between;
  width: 100%;
}
</style>