<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <SubAppBar>
      <div class="tw-mt-4">
        <div>
          <router-link :to="{ name: 'Team', params: { tab: 'team-list' } }" class="list-item-link">
            <q-btn color="primary" class="tw-mx-4" flat>
              チーム一覧に戻る
            </q-btn>
          </router-link>
        </div>
      </div>
    </SubAppBar>
    <v-overlay :opacity="0.2" v-if="firstLoading">
      <content-loading :size="50" text="" />
    </v-overlay>
    <q-card class="tw-my-4">
      <q-toolbar flat>
        <q-toolbar-title 
          class="text-h5 tw-font-semibold tw-mt-4 tw-ml-4"
        > 
          {{ title }}
        </q-toolbar-title>
      </q-toolbar>
      <div>
        <q-form ref="formRef" @submit="onSubmit()" class="tw-p-4">
          <div class="row tw-ml-4">
            <div :class="'col-' + gridCols.label"> 
              <div class="body-2 text-blue-grey tw-font-semibold tw-my-4">チーム名</div>
            </div>
          </div>
          <div class="row">
            <div class="tw-mb-5 tw-mx-4 col">
              <q-input
                v-model="teamName"
                :rules="teamNameRules"
                solo
                single-line
                outlined
                dense
                flat
                :disable="!isOwnTeam && !isAdministrator"
                hide-details="auto"
              />
            </div>
          </div>
          <div v-if="isOwnTeam || isAdministrator">
            <div class="row">
              <div class="tw-mx-4 col">
                <div class="text-h6 tw-font-semibold tw-px-2 tw-mt-2">閲覧権限設定</div>
              </div>
              <div>
                <div v-if="isAdministrator" align="right" class="tw-m-4" @click="addColor()">
                  <q-btn color="primary" class="tw-mr-2 check-all-btn" elevation="0" @click="selectAllPermissions('check')">
                    全てONにする
                  </q-btn>
                  <q-btn class="white--text release-all-btn" color="blue-grey" elevation="0" @click="selectAllPermissions('clear')"> 全てOFFにする </q-btn>
                </div>
              </div>
            </div>
            <q-card flat bordered class="tw-mx-4">
              <q-table 
                :columns="columns" 
                :rows="lscFeatures" 
                key="text"
                hide-bottom
                :rows-per-page-options="[0]"
              >
                <template v-slot:body="props">
                  <q-tr>
                    <q-td>
                      <q-icon small :name="props.row.icon"/>
                      {{ props.row.text }}
                    </q-td>

                    <q-td @click="addColor">
                      <div>
                        <div v-if="selectedActions" >
                          <q-radio
                            v-model="selectedActions[props.row.value][0]"
                            :val="'read'"
                            @change="addColor"
                            :disable="props.row.value == 'ApplicantsPage' || props.row.value == 'UserSettingsPage'"
                            :color="(props.row.value == 'ApplicantsPage' || props.row.value == 'UserSettingsPage') ? 'grey' : ''"
                            dense
                          >
                            <div class="tw-px-0">ON</div>
                          </q-radio>
                          <q-radio
                            v-model="selectedActions[props.row.value][0]"
                            :val="'no'"
                            @change="addColor"
                            :disable="props.row.value == 'ApplicantsPage' || props.row.value == 'UserSettingsPage'"
                            :color="(props.row.value == 'ApplicantsPage' || props.row.value == 'UserSettingsPage') ? 'grey' : ''"
                          >
                            <div class="tw-px-0">OFF</div>
                          </q-radio>
                        </div>
                      </div>
                    </q-td>
                  </q-tr>
                </template>
              </q-table>
              <q-separator/>
            </q-card>
          </div>
          <div class="row">
            <div v-if="isAdministrator" class="col tw-flex tw-justify-end">
              <div class="tw-m-4">
                <q-btn 
                  v-if="teamDetail?.teamName != ''" 
                  class="tw-mr-2 tw-p-2 delete-btn" 
                  color="red"
                  outline
                  :loading="isDeletingTeam" 
                  type="submit"
                  :disable="isOpenDialog"
                  @click="btnType = 'delete'" 
                >
                  <q-icon left name="mdi-trash-can-outline"/>
                  削除
                </q-btn>
                <q-btn
                  class="save-btn"
                  color="primary"         
                  :disable="!valid"
                  :loading="isUpdatingTeam" 
                  type="submit"
                  @click="btnType = 'update'"
                >
                  保存
                </q-btn>
              </div>
            </div>
          </div>
        </q-form>
      </div>
    </q-card>
    <div class="tw-h-12"></div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, onUpdated, ref, watch } from "vue";
import { OSS_FEATURES, PREFIX } from "@/stores/modules/settings/settings.constants";
import { isNullOrEmpty } from "@/utils/stringUtils";
import cloneDeep from "lodash/cloneDeep";
import { GridCols } from "@/types/index";
import { useAuthStore } from "@/stores/modules/auth.module";
import { useUsersStore } from "@/stores/modules/users";

// components
import SubAppBar from '@/components/common/SubAppBar.vue';
import { useRoute, useRouter } from "vue-router";
import { useQuasar } from "quasar";

// store
const authStore = useAuthStore();
const usersStore = useUsersStore();

// quasar
const $q = useQuasar();

// route router
const route = useRoute();
const router = useRouter();

// data
const gridCols = ref<GridCols>({
  label: 3,
  input: 12,
  custom: 4,
  full: 12
});
const teamDetail = ref();
const detail = ref();
const valid = ref(true);
const teamNameRules = ref([
  (v: any) => !!v || "チーム名は必須入力です。",
  (v: any) => (v && v.length >= 3) || "チーム名は3文字以上で入力してください。",
]);
const selectedActions = ref();
const firstLoading = ref(false);
const columns = ref([
  {
    name: 'pageName',
    label: 'ページ名',
    field: 'text',
    align: 'left' as ("left" | "right" | "center" | undefined),
  },
  {
    name: 'auth',
    label: '閲覧権限',
    field: 'auth',
    align: 'left' as ("left" | "right" | "center" | undefined),
  }
]);
const btnType = ref<'update' | 'delete'>('update');  
const isOpenDialog = ref<boolean>(false);

// ref data
const formRef = ref();

// computed
const permissionsFromTeam = computed(() => authStore.permissionsFromTeam);
const teamList = computed(() => usersStore.teamList);
const updateTeamError = computed(() => usersStore.updateTeamError);
const isAdministrator = computed(() => authStore.isAdministrator);
const isDeletingTeam = computed(() => usersStore.isDeletingTeam);
const isUpdatingTeam = computed(() => usersStore.isUpdatingTeam);

const lscFeatures = computed(() => {
  const features = OSS_FEATURES;
  return features.filter((obj: any) => obj.value !== "all");
});

// const show = computed({
//   get() {
//     return false;
//     // TODO
//     // return visible.value;
//   },
//   set(value) {
//     if (!value) {
//       // TODO
//       // $emit("close");
//     }
//   },
// });

const isOwnTeam = computed(() => {
  if (isNullOrEmpty(teamId.value)) {
    return true;
  }
  return permissionsFromTeam.value.teamIds.includes(teamId.value);
});

const title = computed(() => {
  return detail.value && detail.value.teamId ? "チーム編集" : "チーム追加";
});

const teamId = computed(() => {
  return detail.value?.teamId ?? '';
});

const teamName = computed({
  get() {
    return detail.value?.teamName ?? '';
  },
  set(value) {
    detail.value.teamName = value;
  },
});

// watch
watch(
  () => teamDetail.value,
  (newValue, oldValue) => {
    if (newValue != oldValue) {
      detail.value = cloneDeep(newValue);
      if (detail.value.permissions) {
        let obj = {} as any;
        lscFeatures.value.map((item: any) => (obj[item.value] = ['no']));
        detail.value.permissions.forEach((item: any) => {
          const str = item.replace(PREFIX + ":", "").split(":");
          // If its not inside lscFeatures, then skip (we destruct the object to get the value field only)
          // lscFeaturesに含まれていない場合はスキップ (オブジェクトを分解してvalueフィールドのみを取得)
          if(!lscFeatures.value.find(({ value }) => value == str[0])) return;
          const key = str[0];
          if (!Array.isArray(obj[key])) {
            obj[key] = ['no'];
          }
          obj[key] = [str[1]];
        });
        selectedActions.value = cloneDeep(obj);
      } else {
        selectedActions.value = {};
      }
    }
    nextTick(() => {
      formRef.value?.resetValidation();
    });
  },
  { deep: true },
);

watch(
  () => firstLoading.value,
  (newVal) => {
    if (newVal) {
      $q.loading.show();
    }
    else {
      $q.loading.hide();
    }
  }
);

// mounted
onMounted(async () => {
  firstLoading.value = true;
  await fetchData();
});

onUpdated(() => {
  addColor();
});
  
// methods
const fetchData = async (): Promise<void> => {
  await Promise.all([await usersStore.fetchTeamList()]).then(() => {        
    let fullTeamList = cloneDeep(teamList.value);
    const getRusult = fullTeamList.find((obj: any) => obj.teamId == route.params.teamId);
    if((route.params.teamId&&getRusult==undefined) && route.params.teamId!='new') {
      $q.notify({
        message: "チームが存在しません",
      });
      router.push({
        name: "Team",
        params: {
          tab: 'team-list',
        },
      });
    }
    else if(!route.params.teamId) {
      router.push({
        name: "EditTeam",
        params: {
          teamId: 'new',
        },
      });
    }
    teamDetail.value = getRusult != undefined 
      ? getRusult 
      : {
          teamId: "", 
          teamName: "", 
          permissions: ["feature:ApplicantsPage:read", "feature:UserSettingsPage:read"],
        };
    firstLoading.value = false;
  })
  .catch(() => {
    router.push({
      name: "InternalServerError",
      params: {
        message: "情報の取得に失敗しました。システム管理者にお問い合わせください。",
      },
    });
  });
};

const handleUpdateTeam = async () => {
  const payload = cloneDeep(detail.value);
  if(route.params.teamId == 'new') {
    payload.teamId = '';
  }
  else {
    await usersStore.fetchTeamList();
    let currentTeamList = teamList.value;
    const chkErr = currentTeamList.find((obj: any) => obj.teamId == payload.teamId );

    if(chkErr == undefined){
      $q.notify({
        message: "削除されたチームのため更新できませんでした。",
      });
      router.push({
        name: "Team",
        params: {
          tab: 'team-list',
        },
      });
      return;
    }
  }
  
  let permissions = [] as any[];
  Object.keys(selectedActions.value).map((key) => {
    if (selectedActions.value[key].length > 0) {
      selectedActions.value[key].map((value: any) => {
        const str = PREFIX + ":" + key + ":" + value;
        permissions.push(str);
      });
    }
  });
  payload.permissions = permissions;
  await usersStore.updateTeam(payload).then(async () => {
    if (updateTeamError.value) {
      $q.notify({
        type: "error",
        message: updateTeamError.value.message,
      });
    } else {
      // show.value = false;
      $q.notify({
        message: payload.partitionKey ? "チームを更新しました。" : "チームを追加しました。",
      });
      await authStore.FETCH_PERMISSIONS_FROM_TEAM();
      router.push({
        name: "Team",
        params: {
          tab: 'team-list',
        },
      });
    }
  });
};

const handleDeleteTeam = async () => {
  await usersStore.fetchTeamList();
  let currentTeamList = teamList.value;
  const payload = cloneDeep(detail.value);
  const chkErr = currentTeamList.find((obj: any) => obj.teamId == payload.teamId );

  if(chkErr==undefined){
    $q.notify({
      message: "削除されたチームのため更新できませんでした。",
    });
    router.push({
      name: "Team",
      params: {
        tab: 'team-list',
      },
    });
    return;
  }

  isOpenDialog.value = true;

  $q.dialog({
    title: "チーム削除確認",
    message: "このチームを削除してもよろしいですか？",
    ok: {
      push: true,
      color: 'negative',
      flat: true,
      label: 'はい',
    },
    cancel: {
      push: true,
      color: 'brack',
      flat: true,
      label: 'キャンセル',
    },
  }).onOk(async () => {
    await usersStore.deleteTeam(teamId.value).then(() => {
      if (usersStore.deleteTeamError) {
        $q.notify({
          type: "error",
          message: usersStore.deleteTeamError.message,
        });
      } else {
        // show.value = false;
        $q.notify({
          message: "チームを削除しました。",
        });
        router.push({
          name: "Team",
          params: {
            tab: 'team-list',
          },
        });
      }
    });
  }).onDismiss(() => {
    isOpenDialog.value = false;
  });
};

const selectAllPermissions = (type: any) => {
  var obj = {} as any;
  if (type == "check") {
    lscFeatures.value.forEach((item) => {
      obj[item.value] = ["read"];
    });
  } else if (type == "clear") {
    lscFeatures.value.forEach((item) => {
      obj[item.value] = (item.value == "ApplicantsPage" || item.value == "UserSettingsPage") ? ["read"] : ['no'];
    });
  }
  selectedActions.value = obj;
};

const addColor = () => {
  let radioGroup = document.getElementsByClassName('mdi-radiobox-marked');
  for(let i=1;i < radioGroup.length-1;i++){
    if(!radioGroup[i].classList.contains('text-primary')){
      radioGroup[i].classList.add('text-primary');
    }
  }
};

const onSubmit = () => {
  if (btnType.value == 'update') {
    handleUpdateTeam();
  }
  else if (btnType.value == 'delete') {
    handleDeleteTeam();
  }
};
</script>

<style>
.check-all-btn {
  width: 124px;
  height: 36px !important;  
}
.release-all-btn {
  width: 130px;
  height: 36px !important;  
}
.delete-btn {
  width: 100px;
  height: 44px !important;
}
.save-btn {
  width: 62px;
  height: 44px !important;  
}
.radioSizeMin .v-input--selection-controls__input{
  width: 20px;
  height: 20px;
  margin-right: 6px;
}
.radioSizeMin .v-radio{
  display: flex;
  align-items: center;
}
</style>