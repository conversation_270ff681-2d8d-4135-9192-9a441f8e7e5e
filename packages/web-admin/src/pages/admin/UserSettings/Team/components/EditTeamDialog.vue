<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-dialog v-model="show" max-width="700">
    <q-banner color="primary" dark height="5"> </q-banner>
    <q-card>
      <q-toolbar flat>
        <q-toolbar-title> {{ title }}</q-toolbar-title>
        <q-space></q-space>
        <q-btn icon="mdi-close" @click="show = false" />
      </q-toolbar>

      <q-page-container>
        <q-form ref="form" v-model="valid" @submit.prevent>
          <q-row>
            <q-col :cols="gridCols.label"> <q-item-label>チーム名</q-item-label></q-col>
            <q-col :cols="gridCols.input">
              <q-input
                v-model="teamName"
                :rules="teamNameRules"
                solo
                single-line
                outlined
                dense
                flat
                :disabled="!isOwnTeam && !isAdministrator"
                hide-details="auto"
              />
            </q-col>
          </q-row>
          <div v-if="isOwnTeam || isAdministrator">
            <q-item-label>権限設定</q-item-label>
            <q-card outlined>
              <q-card>
                <!-- <template v-slot:default> -->
                  <thead>
                    <tr>
                      <th class="text-left" style="width: 300px">機能</th>
                      <th class="text-left pt-2" v-for="action in lscActions" :key="`col_${action.text}`">
                        {{ action.text }}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="feature in lscFeatures" :key="feature.text">
                      <td>
                        <q-icon small>
                          {{ feature.icon }}
                        </q-icon>
                        {{ feature.text }}
                      </td>

                      <td class="text-center" v-for="action in lscActions" :key="`row_${action.text}`">
                        <q-toggle
                          v-model="selectedActions[feature.value]"
                          class="mt-0 pt-0"
                          :value="action.value"
                          :disabled="!isAdministrator || feature.value == 'ApplicantsPage' || feature.value == 'User'"
                          hide-details
                        ></q-toggle>
                      </td>
                    </tr>
                  </tbody>
                <!-- </template> -->
              </q-card>
              <q-separator></q-separator>
              <div v-if="isAdministrator" align="right" class="ma-4">
                <q-btn color="primary" class="mr-3" elevation="1" @click="selectAllPermissions('check')">
                  全部チェック
                </q-btn>
                <q-btn color="grey lighten-2" elevation="1" @click="selectAllPermissions('clear')"> クリア </q-btn>
              </div>
            </q-card>
          </div>
        </q-form>
      </q-page-container>
      <q-page-container>
        <q-row>
          <template v-if="isAdministrator">
            <q-col>
              <q-btn
                color="primary"
                block
                elevation="1"
                @click="handleUpdateTeam"
                :disabled="!valid"
                :loading="isUpdatingTeam"
              >
                保存
              </q-btn>
            </q-col>
            <q-col v-if="isEdit">
              <q-btn color="error" block elevation="1" @click="handleDeleteTeam" :loading="isDeletingTeam">
                削除
              </q-btn>
            </q-col>
          </template>
          <q-col>
            <q-btn color="grey lighten-2" block elevation="1" @click="show = false"> 閉じる </q-btn>
          </q-col>
        </q-row>
      </q-page-container>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { PropType, ref,  computed, watch, nextTick,  } from 'vue';
import { useQuasar } from 'quasar';
import { useAuthStore } from '@/stores/modules/auth.module';
import { useUsersStore } from '@/stores/modules/users';
import cloneDeep from "lodash/cloneDeep";
import { OSS_FEATURES, OSS_ACTIONS, PREFIX } from "@/stores/modules/settings/settings.constants";
import { isNullOrEmpty } from "@/utils/stringUtils";

// old imports
// 旧インポート
/*import { FETCH_PERMISSIONS_FROM_TEAM } from "@/store/action-types";
import { UPDATE_TEAM, DELETE_TEAM } from "@/store/modules/users/actions-types";
*/

// store
const authStore = useAuthStore();
const usersStore = useUsersStore();

// emits 
const emits = defineEmits<{
  (event: 'close'): void;
}>();

// quasar
const $q = useQuasar();

// props
const props = defineProps({
  visible: Boolean as PropType<boolean>,
  close: Function,
  teamDetail: Object as PropType<any>
});

// data
const gridCols = ref<any>({
  label: 3,
  input: 9,
});

const detail = ref<any>({});
const valid = ref<boolean>(true);
const teamNameRules = ref<any>([
  (v: any) => !!v || "チーム名は必須入力です。",
  (v: any) => (v && v.length >= 3) || "チーム名は3文字以上で入力してください。",
]);
const selectedActions = ref<any>({});
const form = ref();

// methods
const updateTeam = usersStore.updateTeam;
const deleteTeam = usersStore.deleteTeam;
const fetchPermissionsFromTeam = authStore.FETCH_PERMISSIONS_FROM_TEAM;
const handleUpdateTeam = async (): Promise<void> => {
      const payload = cloneDeep(detail.value);
      let permissions = [] as any;
      Object.keys(selectedActions.value).map((key) => {
        if (selectedActions.value[key].length > 0) {
          selectedActions.value[key].map((value: any) => {
            const str = PREFIX + ":" + key + ":" + value;
            permissions.push(str);
          });
        }
      });

      payload.permissions = permissions;

      await updateTeam(payload).then(async () => {
        if (updateTeamError.value) {
          $q.notify({
            type: "error",
            message: updateTeamError.value.message,
          });
        } else {
          show.value = false;
          $q.notify({
            message: payload.partitionKey ? "チームを更新しました。" : "チームを追加しました。",
          });
          await fetchPermissionsFromTeam();
        }
      });
    };
const handleDeleteTeam = async (): Promise<void> => {
  $q.dialog({
    title: "チーム削除確認",
    message: "このチームを削除してもよろしいですか？",
    // type: "error",
    ok: {
      label: "はい",
    },
  }).onOk(async () => {
    await deleteTeam(teamId.value).then(() => {
      if (deleteTeamError.value) {
        $q.notify({
          type: "error",
          message: deleteTeamError.value.message,
        });
      } else {
        show.value = false;
        $q.notify({
          message: "チームを削除しました。",
        });
      }
    });
  });
};

const selectAllPermissions = (type: any): void => {
  let obj = {} as any;
  if (type == "check") {
    lscFeatures.value.map((item: any) => (obj[item.value] = ["read"]));
  } else if (type == "clear") {
    lscFeatures.value.map((item: any) => {
      if (item.value == "ApplicantsPage" || item.value == "User") {
        obj[item.value] = ["read"];
      } else {
        obj[item.value] = [];
      }
    });
  }
  selectedActions.value = cloneDeep(obj);
};

// computed
const permissionsFromTeam = computed(() => authStore.permissionsFromTeam);
const isUpdatingTeam = computed(() => usersStore.isUpdatingTeam);
const isDeletingTeam = computed(() => usersStore.isDeletingTeam);
const updateTeamError = computed(() => usersStore.updateTeamError);
const deleteTeamError = computed(() => usersStore.deleteTeamError);
const isAdministrator = computed(() => authStore.isAdministrator);

const lscFeatures = computed((): any => {
  const features = OSS_FEATURES;
  return features.filter((obj) => obj.value !== "all");
});

const lscActions = computed((): any => {
  const actions = OSS_ACTIONS;
  return actions.filter((obj) => obj.value !== "all");
});

const show = computed({
  get(): boolean {
    return props.visible ?? false;
  },
  set(value: boolean): void {
    if (!value) {
      emits("close");
    }
  },
});

const isOwnTeam = computed((): boolean => {
  if (isNullOrEmpty(teamId.value)) {
    return true;
  }
  return permissionsFromTeam.value.teamIds.includes(teamId.value);
});

const title = computed((): string => {
  return detail.value && detail.value.teamId.value ? "チーム詳細" : "チーム追加";
});

const isEdit = computed((): boolean => {
  return detail.value.partitionKey ? true : false;
});

const teamId = computed((): string => {
  return detail.value.teamId.value;
});

const teamName = computed({
  get(): string {
    return detail.value.teamName;
  },
  set(value: string): void {
    detail.value.teamName = value;
  },
});

// watch
watch(
  () => props.teamDetail,
  (newValue, oldValue) => {
    if (newValue != oldValue) {
      detail.value = cloneDeep(newValue);

      if (detail.value.permissions) {
        let permissions = detail.value.permissions.map((item: any) => item.replace(PREFIX + ":", ""));

        let values = [] as any;
        let currentKey = "";
        let previousKey = "";

        let obj = {} as any;
        lscFeatures.value.map((item: any) => (obj[item.value] = []));

        permissions.map((item: any) => {
          const str = item.split(":");
          currentKey = str[0];
          if (currentKey !== previousKey) {
            values = [];
            values.push(str[1]);
            previousKey = currentKey;
          } else {
            values.push(str[1]);
          }
          obj[currentKey] = values;
        });

        selectedActions.value = cloneDeep(obj);
      } else {
        selectedActions.value = {};
      }
    }
    nextTick(() => {
      form.value.resetValidation();
    });
  },
  { deep: true },
);
</script>
