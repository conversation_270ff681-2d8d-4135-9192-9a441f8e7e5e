<template>
  <q-form ref="form" v-model="valid">
    <div class="row">
      <div class="col">
        <div class="text-blue-grey tw-font-semibold tw-px-3 tw-mt-3" style="height:24px;">パスワードの最小文字数</div>
      </div>
    </div>
    <div class="row">
      <div class="px-3 col">
        <q-input
          v-model="policies.minLength"
          :rules="minLengthRules"
          solo
          single-line
          outlined
          dense
          flat
          hide-details="auto"
          :disabled="isUpdatingPolicySettings"
        />
      </div>
    </div>
    <div class="row">
      <div class="col"> 
        <div class="text-blue-grey tw-font-semibold tw-px-3 tw-mt-8" style="height:24px;">パスワードのルール</div>
      </div>
    </div>
    <div class="row">
      <div class="policyPasswordCheck tw-px-3 tw-mt-0 col">
        <div class="row">
          <div class="tw-my-0 tw-py-0 col-4">
            <q-checkbox v-model="policies.numbers" label="数字を含める"></q-checkbox>
          </div>
          <div class="tw-my-0 tw-py-0 col-4">
            <q-checkbox v-model="policies.lowercase" label="小文字を含める"></q-checkbox>
          </div>
          <div class="tw-my-0 tw-py-0 col-4">
            <q-checkbox v-model="policies.uppercase" label="大文字を含める"></q-checkbox>
          </div>
          <div class="tw-my-0 tw-py-0 col-4">
            <q-checkbox v-model="policies.specialSymbols" label="特殊文字を含める"></q-checkbox>
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col"> 
        <div class="text-blue-grey tw-font-semibold tw-px-3 tw-mt-3" style="height:24px;">一時パスワードの有効期限 （日数）</div>
      </div>
    </div>
    <div class="row">
      <div class="tw-px-3 tw-mt-3 tw-mb-2 col">
        <q-input
          v-model="policies.validityDays"
          :rules="validityDaysRules"
          solo
          single-line
          outlined
          dense
          flat
          hide-details="auto"
          :disabled="isUpdatingPolicySettings"
        />
      </div>
    </div>
    <!-- <v-alert
      v-model="updateError"
      class="mt-2"
      color="red"
      border="left"
      elevation="1"
      dismissible
      colored-border
      type="error"
    >
      {{ updatePolicySettingsError && updatePolicySettingsError.message }}
    </v-alert> -->
    <div class="text-right">
      <q-btn
        color="primary"
        class="tw-m-3"
        @click="permissionHelper.hasActionPermission('click', 'backendRequest') ? onUpdatePolicySettings() : permissionHelper.showActionPermissionError()"
        :disabled="!valid"
        :loading="isUpdatingPolicySettings"
        :style="permissionHelper.hasActionPermission('hideButton', 'UserSettings_Policy_Save') ? permissionHelper.hideButtonPermissionStyle() : ''"
      >
        保存
      </q-btn>
    </div>
  </q-form>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { usePermissionHelper } from '@/mixins/PermissionHelper';

import { useUsersStore } from "@/stores/modules/users";
import { useQuasar } from "quasar";

// quasar
const $q = useQuasar();

// store
const usersStore = useUsersStore();

const permissionHelper = usePermissionHelper();

// data
const valid = ref(true);

const minLengthRules = [
  (v: any) => !!v || "パスワードの最小長は必須入力です。",
  (v: any) => {
    const pattern = /^[0-9]*$/;
    return pattern.test(v) || "パスワードの最小長は半角数字で入力してください。";
  },
  (v: any) => (v && v >= 6) || "パスワードの最小長は「6」以上とする必要があります。",
];

const validityDaysRules = [
  (v: any) => !!v || "一時パスワードの有効期限は必須入力です。",
  (v: any) => {
    const pattern = /^[0-9]*$/;
    return pattern.test(v) || "一時パスワードは半角数字で入力してください。";
  },
  (v: any) => (v && v >= 1) || "一時パスワードの有効期限は「0」より大きくする必要があります。",
];

const updateError = ref(false);

// computed
const policySettings = computed(() => usersStore.policySettings);
const policies = computed(() => {
  // policySettings.value
  let a = policySettings.value;
  return a;

});
const isUpdatingPolicySettings = computed(() => usersStore.isUpdatingPolicySettings);
const updatePolicySettingsError = computed(() => usersStore.updatePolicySettingsError);

// watch
watch(
  () => updatePolicySettingsError.value,
  (newVal) => {
    if (newVal) {
      updateError.value = true;
    } else {
      updateError.value = false;
    }
  }
);

// methos
const onUpdatePolicySettings = async (): Promise<void> => {
  await usersStore.updatePolicySettings(policies.value);

  if (!updatePolicySettingsError.value) {
    $q.notify({
      message: "ポリシーを更新しました。",
    });
  }

  await usersStore.fetchPolicySettings();
};

// mounted
onMounted(() => {
  
});
</script>

<style>
.policyPasswordCheck > .row{
  display:flex;
  justify-content: flex-start;
  align-items: center;
}
.policyPasswordCheck > .row > .col{
  margin-right:20px;
  max-width:160px;
}
.policyPasswordCheck > .row > .col:last-child{
  margin-right:0px;
}
</style>