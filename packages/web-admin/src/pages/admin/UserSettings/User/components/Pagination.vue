<template>
  <div>
    <q-separator/>
    <div class="tw-py-2" fluid>
      <div class="row" no-gutters>
        <div class="tw-flex tw-items-center">
          <span class="tw-mx-4 text-grey-7">
            {{ paginationText }}
          </span>
        </div>
        <q-space/>
        <div>
          <q-btn 
            icon="mdi-chevron-left"
            flat
            color="grey"
            :disabled="isPreviousDisabled"
            left
            @click="handlePrevious" 
          />
        </div>
        <div cols="auto" class="align-self-center">
          <q-btn 
            icon="mdi-chevron-right"
            flat
            color="grey" 
            :disabled="isNextDisabled"
            @click="handleNext" 
            left
          />
        </div>
      </div>
    </div>
    <q-separator/>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { getPaginationText } from "@/utils/pagination";
import { useUsersStore } from "@/stores/modules/users";

// store
const usersStore = useUsersStore();

// computed
const dataTableOptions = computed(() => usersStore.dataTableOptions);
const paginationRange = computed(() => usersStore.paginationRange);
const userList = computed(() => usersStore.userList);
const isFetchingUserList = computed(() => usersStore.isFetchingUserList);
const totalPages = computed(() => usersStore.totalPages);
const isLastPage = computed(() => usersStore.isLastPage);
const totalUser = computed(() => usersStore.totalUser);
const maxRow = computed(() => usersStore.dataTableOptions.itemsPerPage);

const page = computed({
  get(): any {
    return dataTableOptions.value.page;
  },
  set(value: any): void {
    usersStore.setUserListDataTableOptions({
      ...dataTableOptions.value,
      page: value,
    });
    //１ページ目に遷移すると全てのユーザが２つに増える不具合のためfetchData();をコメントアウト
    //何かに必要な場合は再検討の必要あり（新規作成も編集から戻るときも必要なさそうに見えてます。）
    //fetchData();
  },
});

const paginationText = computed((): string => {
  const { start, end } = paginationRange.value;
  const totalCount = totalUser.value + " 件";
  return getPaginationText(start, end, totalCount, userList.value);
});

const isPreviousDisabled = computed((): boolean => {
  return page.value <= 1 || isFetchingUserList.value;
});

const isNextDisabled = computed((): boolean => {
  // let a = (page.value >= totalPages.value && isLastPage.value) || isFetchingUserList.value;
  let a = page.value >= (totalUser.value / maxRow.value) || isFetchingUserList.value;
  return a;
});

// methods
const fetchData = async (): Promise<void> => {
  await usersStore.fetchUserList(true);
};

const handlePrevious = (): void => {
  page.value = page.value - 1;
};

const handleNext = (): void => {
  page.value = page.value + 1;
};
</script>