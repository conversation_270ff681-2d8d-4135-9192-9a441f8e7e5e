<template>
  <q-dialog v-model="show">
    <q-card class="tw-w-full tw-p-2" >
      <q-toolbar flat>
        <q-toolbar-title class="text-h5 tw-font-semibold tw-mt-2">
          ユーザーを作成
        </q-toolbar-title>
      </q-toolbar>

      <q-form ref="formRef" @submit="submit()">
        <div class="row tw-px-4 tw-py-3">
          <div :class="gridCols.label"> 
            <div class="text-blue-grey tw-font-semibold">ユーザー名</div>
          </div>
        </div>
        <div class="row tw-px-3 tw-my-1">
          <div :class="gridCols.input">
            <q-input
              v-model="user.username"
              :rules="usernameRules"
              solo
              single-line
              outlined
              dense
              flat
              hide-details="auto"
              placeholder="ユーザ名"
              autocomplete="off"
            />
          </div>
        </div>
        <div class="row tw-px-4 tw-pb-3">
          <div :class="gridCols.label"> 
            <div class="text-blue-grey tw-font-semibold">一時パスワード</div>
          </div>
        </div>
        <div class="tw-px-3 tw-my-1">
          <div :class="gridCols.input">
            <q-input
              v-model="user.tempPassword"
              :rules="tempPasswordRules"
              :type="showPassword ? 'text' : 'password'"
              solo
              single-line
              outlined
              dense
              flat
              hide-details="auto"
              placeholder="一時パスワード"
              autocomplete="off"
            >
            <template v-slot:append>
              <q-icon
                :name="showPassword ? 'visibility' : 'visibility_off'"
                class="cursor-pointer"
                @click="showPassword = !showPassword"
              />
            </template>
            </q-input>
          </div>
        </div>
        <div class="row tw-px-4 tw-pb-3">
          <div :class="gridCols.label"> 
            <div class="text-blue-grey tw-font-semibold">メールアドレス</div>
          </div>
        </div>
        <div class="tw-px-3 tw-my-1">
          <div :class="gridCols.input">
            <q-input
              v-model="user.email"
              :rules="emailRules"
              solo
              single-line
              outlined
              dense
              flat
              hide-details="auto"
              placeholder="メールアドレス"
            />
          </div>
        </div>
        <div>
          <div v-if="isAdministrator">
            <div class="text-blue-grey tw-font-semibold tw-px-4 tw-pb-3">アドミニストレータ権限の付与</div>
            <div class="tw-mx-2 tw-my-0 tw-pb-3">
              <q-radio v-model="grantAdministrator" :val="true" label="付与する"/>
              <q-radio v-model="grantAdministrator" :val="false" label="付与しない"/>
            </div>
          </div>
        </div>
        <div v-if="!grantAdministrator">
          <div class="row tw-px-4 tw-pb-3">
            <div :class="gridCols.label"> 
              <div class="text-blue-grey tw-font-semibold">チーム名</div>
            </div>
          </div>
          <div class="row tw-px-3 tw-pb-2 tw-my-1">
            <div :class="gridCols.input">
              <q-select
                v-model="selectedTeam"
                :options="localTeamOptions"
                option-label="text"
                option-value="value"
                flat
                outlined
                dense
                required
                background-color="white"
                hide-details="auto"
                placeholder="チーム"
              />
            </div>
          </div>
          <div class="row tw-px-4 tw-py-4">
            <div :cols="gridCols.label"> 
              <div class="text-blue-grey tw-font-semibold">権限</div>
            </div>
          </div>
          <div class="row tw-px-3 tw-my-1 tw-pb-4">
            <div :class="gridCols.input">
              <q-select
                v-model="selectedGroup"
                :options="accessLevelOptions"
                option-label="text"
                option-value="value"
                flat
                outlined
                dense
                background-color="white"
                hide-details="auto"
                placeholder="権限"
              />
            </div>
          </div>
        </div>
        <div class="tw-mt-2">
          <div class="text-blue-grey tw-font-semibold tw-px-4 tw-pb-3">招待通知</div>
          <div row hide-details="auto" class="tw-mx-2 tw-my-0" dense>
            <q-radio 
              v-model="invitation" 
              :val="true" 
              label="送信する" 
              :disable="!hasEmail" 
              :color="hasEmail ? '' : 'grey'"
            />
            <q-radio 
              v-model="invitation" 
              :val="false" 
              label="送信しない" 
              :disable="!hasEmail" 
              :color="hasEmail ? '' : 'grey'"
            />
          </div>
        </div>
        <q-card-actions class="tw-pb-3 d-flex justify-end">
          <q-btn color="primary" outline @click="show = false" class="tw-mb-2 cancel-btn"> キャンセル </q-btn>
          <q-btn
            color="primary"
            class="tw-mb-2 tw-ml-2 tw-mr-2 create-btn"
            :loading="isCreatingUser"
            type="submit"
            :style="
              hasActionPermission('hideButton', 'UserSettings_AddUserDialog_Save') ? hideButtonPermissionStyle() : ''
            "
          >
            作成
          </q-btn>
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, watch } from "vue";

import { ACCESS_LEVEL } from "@/stores/modules/users/users.constants";
import { useQuasar } from "quasar";
import { useUsersStore } from "@/stores/modules/users";
import { useAuthStore } from "@/stores/modules/auth.module";
import { usePermissionHelper } from "@/mixins/PermissionHelper";
import { OptionsModel } from "@/types/index";

interface User {
  username: string;
  tempPassword: string;
  email: string;
}

const props = defineProps<{
  visible: boolean,
}>();

// quasar
const $q = useQuasar();

// store
const usersStore = useUsersStore();
const authStore = useAuthStore();

// formatter
const permissionHelper = usePermissionHelper();

//data
const emit = defineEmits(['close']);
const gridCols = ref({
  label: 3,
  input: 'col-12',
});

// ref data
const formRef = ref();

const user = ref<User>({
  username: '',
  tempPassword: '',
  email: '',
});

const grantAdministrator = ref(false);
const selectedTeam = ref<OptionsModel | null>();
const selectedGroup = ref<OptionsModel | null>();
const invitation = ref(false);
const showPassword = ref(false);

const usernameRules = ref([
  (v: any) => !!v || "ユーザ名は必須入力です。",
  (v: any) => (v && v.length >= 5) || "ユーザ名は5文字以上で入力してください。",
  (v: any) =>
    (v &&
      v.length > 0 &&
      v.match(/[\u0000-\u00ff]/g) != null &&
      v.length == v.match(/[\u0000-\u00ff]/g).length) ||
    "ユーザ名は半角の英数文字を入力してください。",
]);

const tempPasswordRules = ref<any>([(v: any) => !!v || "一時パスワードは必須入力です。"]);

const emailRules = ref([
  (v: any) => !!v || "メールアドレスは必須入力です。",
  (v: any) => {
    const pattern =
      /^(([^<>()[\]\\.,:\s@"]+(\.[^<>()[\]\\.,:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return pattern.test(v) || "メールアドレスが正しくありません。";
  },
]);

// computed
const teamOptions = computed(() => usersStore.teamOptions);
const isAdministrator = computed(() => authStore.isAdministrator);
const policySettings = computed(() => usersStore.policySettings);
const currentUser = computed(() => authStore.user);
const createUserError = computed(() => usersStore.createUserError);
const isCreatingUser = computed(() => usersStore.isCreatingUser);

const accessLevelOptions = computed((): any => {
  return ACCESS_LEVEL;
});

const show = computed({
  get(): boolean {
    return props.visible;
  },
  set(value: boolean): void {
    if (!value) {
      resetValidation();
      emit('close');
    }
  },
});

const localTeamOptions = computed((): any => {
  return teamOptions.value.filter((obj: any) => obj.value !== "Administrator");
});

const hasEmail = computed((): string => {
  return user.value.email;
});

// watch
watch(
  () => show.value,
  (newVal) => {
  if (newVal) {
    resetValidation();
  }}
);

watch(
  () => grantAdministrator.value,
  (newVal) => {
    if (newVal) {
      selectedTeam.value = null;
      selectedGroup.value = null;
    }
  }
);

watch(
  policySettings,
  (newVal) => {
    if (newVal) {
      changePasswordRules();
    }
  }
);

// methods
const hasActionPermission = permissionHelper.hasActionPermission;
const showActionPermissionError = permissionHelper.showActionPermissionError;
const hideButtonPermissionStyle = permissionHelper.hideButtonPermissionStyle;

const handleCreateUser = async (): Promise<void> => {
  if (grantAdministrator.value) {
    selectedTeam.value = { value: "Administrator" } as OptionsModel;
    selectedGroup.value = { value: "admins" } as OptionsModel;
  }

  if(selectedTeam.value == null || selectedGroup.value == null) {
    usersStore.setCreateUserError({ message: "チームと権限を選択してください。" });
    return;
  }

  const groupName = selectedTeam.value?.value + ":" + selectedGroup.value?.value;
  const signedInUserIsAdminOfGroup = usersStore.isTeamAdmin(currentUser.value, groupName);
  if (!isAdministrator.value && !signedInUserIsAdminOfGroup) {
    usersStore.setCreateUserError({ message: "この操作を行う場合は、管理者に実行権限をお問い合わせください。" });
    return;
  }

  await usersStore.createUser({
    username: user.value.username,
    tempPassword: user.value.tempPassword,
    email: user.value.email,
    invitation: invitation.value,
    groupName: groupName,
  }).then(async () => {
    if (createUserError.value) {
      return;
    }

    show.value = false;
    $q.notify({
      message: "ユーザを作成しました。",
    });

    usersStore.resetUserList();
    await usersStore.fetchUserList();
  });
};

const changePasswordRules = (): void => {
  if (policySettings.value.minLength) {
    tempPasswordRules.value.push(
      (v: any) =>
          (v && v.length >= policySettings.value.minLength) ||
        "一時パスワードは" + policySettings.value.minLength + "文字以上で入力してください。"
    );
  }
  if (policySettings.value.numbers) {
    tempPasswordRules.value.push((v: any) => {
      const pattern = /[0-9]/;
      return pattern.test(v) || "半角数字は必須です。";
    });
  }
  if (policySettings.value.specialSymbols) {
    tempPasswordRules.value.push((v: any) => {
      const pattern = /[!@#$%^&*(),.?"`~_:{}[\]\/\\|><]/;
      return pattern.test(v) || "特殊文字は必須です。";
    });
  }
  if (policySettings.value.uppercase) {
    tempPasswordRules.value.push((v: any) => {
      const pattern = /[A-Z]/;
      return pattern.test(v) || "半角大文字は必須です。";
    });
  }
  if (policySettings.value.lowercase) {
    tempPasswordRules.value.push((v: any) => {
      const pattern = /[a-z]/;
      return pattern.test(v) || "半角小文字は必須です。";
    });
  }
};

const resetValidation = (): void => {
  user.value.username = '';
  user.value.tempPassword = '';
  user.value.email = '';
  grantAdministrator.value = false;
  selectedTeam.value = null;
  selectedGroup.value = null;
  showPassword.value = false;
  invitation.value = false;
  nextTick(() => {
    formRef.value?.resetValidation();
  });
};

watch(
  () => createUserError.value,
  (newVal) => {
    if (newVal) {
      $q.notify({
        message: newVal.message,
        color: "negative",
      });
    }
  }
);

const submit = () => {
  hasActionPermission('click', 'backendRequest') ? handleCreateUser() : showActionPermissionError();
};
</script>
