<template>
  <div>
    <SubAppBar :full="false" :followContainer="true" :dense="false" class="tw-mb-5">
      <div class="">
        <q-btn-toggle 
          class="bg-grey-2"
          v-model="tab"
          unelevated
          outline
          color="grey"
          toggle-color="primary"
          toggle-text-color="primary"
          :options="[
            {label: 'ユーザー', value: 'user-list'},
            {label: 'チーム', value: 'team-list'},
            {label: 'パスワード設定', value: 'policy-settings'}
          ]"
        />
      </div>
    </SubAppBar>
    <q-card outlined class="my-4">
      <q-tab-panels v-model="tab">
        <q-tab-panel name="user-list">
          <div fluid>
            <UserList />
          </div>
        </q-tab-panel>
        <q-tab-panel name="team-list">
          <div fluid>
            <TeamList />
          </div>
        </q-tab-panel>
        <q-tab-panel name="policy-settings">
          <div fluid>
            <PolicySettings />
          </div>
        </q-tab-panel>
      </q-tab-panels>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { useUsersStore } from "@/stores/modules/users";

// components
import SubAppBar from '@/components/common/SubAppBar.vue';
import UserList from './User/index.vue';
import TeamList from './Team/index.vue';
import PolicySettings from './Policy/index.vue';

// route
const route = useRoute();

// store
const usersStore = useUsersStore();

// data
const tab =  ref('user-list');
// const settingMenus = ref([
//   {
//     text: "ユーザ",
//     icon: "mdi-account-outline",
//     to: "User",
//   },
//   {
//     text: "チーム",
//     icon: "mdi-account-group-outline",
//     to: "Team",
//   },
//   {
//     text: "ポリシー設定",
//     icon: "mdi-clipboard-list-outline",
//     to: "Policy",
//   },
// ]);

// computed
// TODO
// const settingMenusCanAccess = computed((): any  => {
//   return settingMenus.value.filter((menu) => {
//     return this.$ability.can("access", menu.to);
//   });
// });

const isActiveMenu = (menu: any): boolean => {
  return route.name === menu.to;
};

const fetchData = async (): Promise<void> => {
  await Promise.all([await usersStore.fetchUserList(), await usersStore.fetchTeamList(), await usersStore.fetchPolicySettings()]);
};

// mounted
onMounted(async () => {
  if(route.name == "Team"){
    tab.value = 'team-list';
  }
  await fetchData();
});

</script>

<style lang="scss">
.list-item-link {
  text-decoration: none;
}
.setting-label {
  margin-bottom: 10px;
  font-weight: 600;
}
</style>
