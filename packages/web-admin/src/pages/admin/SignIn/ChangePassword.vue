<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div class="tw-h-hull">
    <q-row align="center" justify="center" class="row">
      <div class="col-12 col-sm-8 col-md-4">
        <q-card class="tw-py-10" v-if="error">
          <div class="text-center">
            <q-icon x-large color="red" dark name="mdi-alert-circle-outline"></q-icon>
            <h4 class="text-red tw-pt-4">{{ error }}</h4>
            <a>
              <router-link :to="{ name: 'signin' }">ログインページ</router-link>
            </a>に戻る。
          </div>
        </q-card>
        <q-card v-if="true">
          <q-toolbar color="primary" dark flat>
            <div class="text-h6">新しいパスワードを入力してください</div>
            <q-space />
          </q-toolbar>
          <q-card-section>
            <q-form @submit.prevent="handleChangePassword" ref="formRef">
              <q-input
                outlined
                label="ユーザ"
                dense
                v-model="userStore.username"
                disable
              >
                <template v-slot:prepend>
                  <q-icon name="mdi-account"></q-icon>
                </template>
              </q-input>
              <q-input
                outlined
                label="旧パスワード"
                dense
                v-model="oldPassword"
                type="password"
                :rules="passwordRules"
                class="tw-pt-7"
              >
                <template v-slot:prepend>
                  <q-icon name="mdi-lock"></q-icon>
                </template>
              </q-input>
              <q-input
                outlined
                label="新パスワード"
                dense
                v-model="newPassword"
                type="password"
                :rules="passwordRules"
                class="tw-pt-5"
              >
                <template v-slot:prepend>
                  <q-icon name="mdi-lock-question"></q-icon>
                </template>
              </q-input>
              <q-card-actions class="tw-pb-5" align="right">
                <q-btn type="submit" color="primary" :loading="isChanging" :disable="!validPassword" class="tw-w-16">
                  確認
                </q-btn>
              </q-card-actions>
            </q-form>
            <Alert v-if="changePasswordError" type="error">
              {{ changePasswordError }}
            </Alert>
          </q-card-section>
        </q-card>
      </div>
    </q-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { Auth } from "aws-amplify";
import { useAuthStore } from "@/stores/modules/auth.module";
import Alert from "@/components/common/Alert.vue";

// router
const router = useRouter();

// store
const authStore = useAuthStore();

// data
const display = ref(true);
const checking = ref(true);
const username = ref("");
const oldPassword = ref("");
const newPassword = ref("");
const error = ref("");
const cognitoUser = ref<any>(null);
const changePasswordError = ref<any>(null);
const isChanging = ref(false);
const passwordRules = [
  (v: string) => !!v || "パスワードの入力が必要です。",
  (v: string) => (v && v.length >= 8) || "パスワードの文字数の最小限は８。",
];
const formRef = ref();

// computed
const userStore = computed(() => authStore.user);
const validPassword = computed(() => {
  return newPassword.value.length >= 8 && oldPassword.value.length >= 8;
});

// method
const handleChangePassword = async () => {
  if (formRef.value.validate()) {
    changePasswordError.value = null;
    isChanging.value = true;
    try {
      await Auth.completeNewPassword(
        cognitoUser.value,
        newPassword.value,
        cognitoUser.value.challengeParam.requiredAttributes,
      );
      router.push("/");
    } catch (e: any) {
      if (e.code === "InvalidPasswordException") {
        changePasswordError.value = "パスワードの文字数の最小限は８。";
      } else if (e.code === "NotAuthorizedException") {
        changePasswordError.value = "セーションが切りました。ページを更新してください。";
      } else {
        changePasswordError.value = e.message;
      }
    } finally {
      isChanging.value = false;
    }
  }
};
</script>