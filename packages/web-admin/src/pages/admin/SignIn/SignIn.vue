<template>
  <q-layout id="inspire">
    <q-page-container style="background-color: #fafbfc">
      <q-page class="fill-height" fluid>
        <div class="tw-flex tw-justify-center tw-items-center">
          <div class="tw-w-2/6 tw-min-w-96 ">
            <div class="tw-grid tw-grid-cols-3 tw-place-items-center">
              <QSpace></QSpace>
              <div class="tw-w-60 tw-mt-32 tw-mb-4 tw-flex tw-items-center">
                <div name="fab-transition">
                  <q-img :src="logo" />
                </div>
              </div>
              <QSpace></QSpace>
            </div>
            <div class="">
              <div
                name="login-transition"
                enter-active-class="animate__animated animate__zoomInDown"
                :leave-active-class="`animate__animated ${
                  currentCard === 'signin' ? 'animate__zoomOutRight' : 'animate__zoomOutLeft'
                }`"
              >
                <div class="">
                  <q-card class="" v-if="currentCard === 'signin'">
                    <q-card-section class="text--primary px-8">
                      <div class="text-center tw-mb-4 tw-text-lg">サインイン</div>
                      <q-form ref="formRef" v-model="valid" @submit.prevent="handleSignIn">
                        <div class="tw-mx-4">

                          <q-input
                            v-model="username"
                            :rules="usernameRules"
                            :disabled="isSigningIn"
                            placeholder="ユーザ名"
                            required
                            single-line
                            outlined
                            dense
                          >
                            <template v-slot:prepend>
                              <q-icon name="mdi-account-circle-outline" />
                            </template>
                          </q-input>
                          <q-input
                            type="password"
                            v-model="password"
                            :rules="passwordRules"
                            :disabled="isSigningIn"
                            placeholder="パスワード"
                            required
                            single-line
                            outlined
                            dense
                          >
                            <template v-slot:prepend>
                              <q-icon name="mdi-shield-key-outline" />
                            </template>
                          </q-input>
                          <q-banner v-if="errorMessage" v-model="hasError" class="body-2 tw-text-red-600" type="error" text>
                            {{ errorMessage }}
                          </q-banner>
    
                          <q-card-actions class="tw-px-0">
                            <template v-if="!isSigningIn">
                              <a class="tw-text-sky-500 tw-underline tw-ml-2" href="#forgot-password" @click="handleForgetPassword"> 
                                パスワードをお忘れの方
                              </a>
                              <q-space></q-space>
                            </template>
                            <q-btn
                              color="primary"
                              class="button-login"
                              type="submit"
                              :dark="valid"
                              large
                              :disabled="!valid"
                              :loading="isSigningIn"
                            >
                            <q-icon left name="login" />
                            サインイン
                            </q-btn>
                          </q-card-actions>
                        </div>
  
                      </q-form>
                    </q-card-section>
                  </q-card>
                  <ForgetPasswordComponent
                    v-if="currentCard === 'forget-password'"
                    :cachedUsername="username"
                    :alreadyReset="alreadyReset"
                    @backToSignIn="backToSignIn"
                    style="position: absolute; width: 100%"
                  />
                  <SetPasswordComponent
                    v-if="currentCard === 'set-password'"
                    :cachedCognitoUser="cognitoUser"
                    @backToSignIn="backToSignIn"
                  />
                </div>
              </div>
            </div>
  
            <div v-if="!isFetchingPublicConfiguration" class="tw-font-light text-center tw-mt-8">
              {{ `PlayNext Lab Inc. © ${currentYear}. Version ${versions} ` }}
            </div>
          </div>
        </div>
      </q-page>
    </q-page-container>

    <q-dialog v-model="showAccountUnusableDialog" width="500">
      <q-card>
        <q-bar color="warning" dark height="10"> </q-bar>
        <q-list three-line>
          <q-item>
            <q-item-section>
              <q-icon x-large color="warning" name="mdi-alert-outline"></q-icon>
            </q-item-section>

            <q-item-section>
              <q-item-label>アカウントエラー</q-item-label>
              <q-item-label class="mt-2">
                申し訳ありませんが、現在お使いのアカウントは使用不可になっています。
                システム管理者にお問い合わせください。
              </q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
        <q-separator></q-separator>

        <q-card-actions>
          <q-space></q-space>
          <q-btn color="grey darken-2" outlined @click="showAccountUnusableDialog = false"> 閉じる </q-btn>
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-layout>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { convertAuthErrorMessages } from "@/utils/stringUtils";
import { useDatetimeFormatter } from "@/mixins/DatetimeFormatter";
import { AmplifyService } from "@/services/amplify.service";
import { useAuthStore } from "@/stores/modules/auth.module";
import { useSettingsStore } from "@/stores/modules/settings";
import { useFormsStore } from "@/stores/modules/forms";
import { useRoute, useRouter } from "vue-router";
import logo from '@/assets/GovTech_top.png'

// component
import ForgetPasswordComponent from "./ForgetPassword.vue";
import SetPasswordComponent from "./SetPassword.vue";

// router route
const router = useRouter();
const route = useRoute();

// store
const authStore = useAuthStore();
const settingsStore = useSettingsStore();
const formsStore = useFormsStore();

// Format
const DatetimeFormatter = useDatetimeFormatter();

// data
const valid = ref(true);
const cognitoUser = ref<any>(null);
const username = ref("");
const password = ref("");
const usernameRules = ref([
  (v: any) => !!v || "ユーザー名は必須入力です。",
  (v: any) => (v && v.length >= 5) || "ユーザー名は5文字以上で設定してください。",
]);
const passwordRules = ref([(v: any) => !!v || "パスワードは必須入力です。"]);
const currentCard = ref("signin");
const alreadyReset = ref(false);
const hasError = ref(false);
const errorMessage = ref("");
const showAccountUnusableDialog = ref(false);
const versions = ref("");

// ref data
const formRef = ref<any>(null);

// computed
const isAccountUnusable = computed(() => authStore.isAccountUnusable);
const publicConfiguration = computed(() => settingsStore.publicConfiguration);
const isFetchingPublicConfiguration = computed(() => settingsStore.isFetchingPublicConfiguration ?? false);
const isSigningIn = computed(() => authStore.isSigningIn);
const signinError = computed(() => authStore.signinError);

const logoUrl = computed((): any => {
  let url = window.sessionStorage.getItem("logoURL");
  return url === "null" || url === "undefined" ? require("@/assets/GovTech_top.png") : url;
});

const currentYear = computed((): any => {
  return DatetimeFormatter.getCurrentYear();
});

// watch
watch(
  () => currentCard.value,
  (newVal) => {
    if (newVal) {
      clearError();
    }
  }
);

watch(
  () => publicConfiguration.value,
  (newVal) => {
    if (newVal) {
      versions.value = newVal.versions.general;
    }
  }
);

// mounted
onMounted(() => {
  if (isAccountUnusable.value) {
    showAccountUnusableDialog.value = true;
  }

  if (route.hash.includes("forgot-password")) {
    currentCard.value = "forget-password";
  } else {
    currentCard.value = "signin";
  }

  settingsStore.fetchPublicConfiguration().then(() => {
    AmplifyService.configure()
  });
});

const handleSignIn = async(): Promise<void> => {
  clearError();
  const response = await authStore.AUTH_SIGNIN({
    username: username.value,
    password: password.value,
  });

  if (signinError.value) {
    if (signinError.value.code == "PasswordResetRequiredException") {
      alreadyReset.value = true;
      currentCard.value = "forget-password";
    } else {
      let message = convertAuthErrorMessages(signinError.value);
      setError(message);
    }
  } else {
    if (response && response.challengeName === "NEW_PASSWORD_REQUIRED") {
      currentCard.value = "set-password";
      cognitoUser.value = response;
    } else {
      if (response) {
        try {
          formsStore.setFormConfigs([]);
          await router.push({ name: "ApplicantsPage" });
        } catch {}
      }
    }
  }
};

const handleForgetPassword = (): void => {
  currentCard.value = "forget-password";
};

const backToSignIn = (): void => {
  currentCard.value = "signin";
};

const setError = (value: any): void => {
  hasError.value = true;
  errorMessage.value = value;
};

const clearError = (): void => {
  hasError.value = false;
  errorMessage.value = "";
};
</script>
