<template>
  <div>
    <q-card outlined class="tw-my-4">
      <q-tab-panels v-model="tab">
        <q-tab-panel name="remind-delivery">
          <div class="q-gutter-md">
            <RemindDeliveryListFragment  class="q-gutter-md"/>
          </div>
        </q-tab-panel>
      </q-tab-panels>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import RemindDeliveryListFragment from '@/pages/admin/ReminderDelivery/fragments/RemindDeliveryListFragment.vue';

const tab = ref<string>("remind-delivery");

</script>
