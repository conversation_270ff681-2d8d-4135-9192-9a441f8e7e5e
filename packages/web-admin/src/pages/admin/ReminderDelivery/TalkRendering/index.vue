<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <div v-for="(message, index) in messages" :key="index" class="balloon-left">
      <template v-if="message.contents.type === 'text'">
        <template v-if="hasContents(message.contents)">
          <div class="avatar">
            <q-avatar color="grey lighten-2">
              <q-icon dark large>mdi-account</q-icon>
            </q-avatar>
          </div>
          <div row>
            <p class="username">{{ userStore.username }}</p>
            <p :id="'textmessage' + index" class="text-preview"></p>
          </div>
        </template>
      </template>
      <template v-if="message.contents.type === 'image'">
        <template v-if="hasContents(message.contents)">
          <div class="avatar">
            <q-avatar color="grey lighten-2">
              <q-icon dark large>mdi-account</q-icon>
            </q-avatar>
          </div>
          <div row>
            <p class="username">{{ userStore.username }}</p>
            <q-img class="image-preview" :src="message.contents.previewImageUrl" max-width="180" contain> </q-img>
          </div>
        </template>
      </template>
      <template v-if="message.contents.type === 'imagemap'">
        <template v-if="hasContents(message.contents)">
          <div class="avatar">
            <q-avatar color="grey lighten-2">
              <q-icon dark large>mdi-account</q-icon>
            </q-avatar>
          </div>
          <div row>
            <p class="username">{{ userStore.username }}</p>
            <q-img class="image-preview" :src="imagemapUrl(message.contents.imagemapRenderingPayload.baseUrl)" max-width="180" contain> </q-img>
          </div>
        </template>
      </template>
      <template v-if="message.contents.type === 'flex'">
        <template v-if="hasContents(message.contents)">
          <div class="avatar">
            <q-avatar color="grey lighten-2">
              <q-icon dark large>mdi-account</q-icon>
            </q-avatar>
          </div>
          <div row class="flex-preview">
            <p class="username">{{ userStore.username }}</p>
              <template>
                <BubbleFlexPreview :message="message.contents.flexPayload.payload"/>
              </template>
          </div>
        </template>
      </template>
      <template v-if="message.contents.type === 'video'">
        <template v-if="hasContents(message.contents)">
          <div class="avatar">
            <q-avatar color="grey lighten-2">
              <q-icon dark large>mdi-account</q-icon>
            </q-avatar>
          </div>
          <div row>
            <p class="username">{{ userStore.username }}</p>
            <q-img class="image-preview" :src="message.contents.videoThumbModel.getObjectURL()" max-width="180" contain> </q-img>
          </div>
        </template>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, ref,  computed, watch, onMounted } from 'vue';

import { useAuthStore } from '@stores/authStore';

// old imports
// 旧インポート
/*import BubbleFlexPreview from "../DistributionCreation/fragments/BubbleFlexPreview.vue";*/


// props
const props = defineProps({
  messages: Array as PropType<array>,
  positionInfo: Array as PropType<array>,
  preview: Boolean as PropType<boolean>
});

// data
const elTextMessage = ref<any>("");

// methods
const hasContents = (value: any): boolean => {
      switch (value.type) {
        case "text":
          return value.text.length !== 0 && value.text.trim().length !== 0;
        case "image":
          return value.previewImageUrl.length !== 0;
        case "imagemap":
          return value.imagemapRenderingPayload && value.imagemapRenderingPayload.baseUrl;
        case "flex":
          return value.flexPayload.validateResult && value.flexPayload.payload;
        case "video":
          return value.videoModel && value.videoThumbModel;
      }
    };
const imagemapUrl = (baseUrl): string => {
      return baseUrl.startsWith("data:") ? baseUrl : baseUrl + '/1040'
    };
const createTextMessage = (): void => {
      const size = 27;
      messages.forEach((message,i) => {
        if(message.contents.type=="text"){
          elTextMessage.value = document.getElementById('textmessage' + i);
          if (!elTextMessage.value) {
            return;
          }
          elTextMessage.value.innerHTML = "";

          let chars = message.contents.text.split("");
          let index = 0;
          positionInfo[i].emojis.forEach(emoji => {
              const text = chars.slice(index, emoji.index);
              elTextMessage.value.appendChild(new Text(text.join("")));

              const img = store.htmlimageElement = document.createElement("img");
              img.src = `/emojis/${emoji.productId}_${emoji.emojiId}.png`;
              img.width = size;
              img.height = size;
              img.style.setProperty('vertical-align', 'middle');
              img.style.setProperty('image-rendering', 'high-quality');
              // non-Retinaなモニターで画像がぼやける問題の対応
              img.style.setProperty('transform', 'translate3d(0, 0, 1px)');
              elTextMessage.value.appendChild(img);

              index = emoji.index + 1;
          });
          if (index < chars.length) {
            elTextMessage.value.appendChild(new Text(chars.slice(index, chars.length).join("")));
          }
        }
      });
    };
const synchronizeEmoji = (): void => {
      messages.forEach((message,i) => {
        if(positionInfo[i]){
          createTextMessage();
        }
      });
    };

// computed
const userStore = computed(() => authStore.user);

// watch
watch(() => preview, (value) => {
  if (value) {
    synchronizeEmoji();
  }
});
watch(() => positionInfo, () => {
  synchronizeEmoji();
});

// hooks

onMounted(() => {
  synchronizeEmoji();
});

</script>

<style>
.balloon-left {
  margin: 10px 10px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}

.balloon-left {
  margin-right: 20px;
}

.avatar {
  margin: 10px 20px 0 0;
}

.username {
  color: white;
  margin: 0 !important;
}

.text-preview {
  margin: 0 !important;
  font-size: small;
  max-width: 210px;
  display: inline-block;
  flex-wrap: wrap;
  position: relative;
  word-wrap: break-word;
  padding: 10px 15px 10px 15px;
  border-radius: 12px;
  background: white;
  box-sizing: border-box;
  line-height: 1.5;
  white-space: pre-line;
}

.image-preview {
  border-radius: 12px;
}

.text-preview:after {
  content: "";
  position: absolute;
  top: 10px;
  border: 10px solid transparent;
}

.balloon-left .text-preview:after {
  left: -20px;
  border-right: 22px solid white;
}
</style>
