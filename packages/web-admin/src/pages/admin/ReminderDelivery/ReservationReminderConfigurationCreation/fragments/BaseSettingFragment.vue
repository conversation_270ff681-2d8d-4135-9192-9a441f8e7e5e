<template>
  <div fluid class="px-0 tw-py-3">
    <!-- 帳票選択 -->
     <div v-if="!isFetchingSurveyConfigs">
      <DataRelativeSurveySelector
        :isFetchingSurveyConfigsProp="isFetchingSurveyConfigs"
        :selectedSurveyIdProp="defaultSurveyId || selectedSurveyId"
        :surveyConfigsProp="surveyConfigs"
        @selectSurvey="selectSurvey"
        @getSurveyConfigs="handleFetchSurveyConfigs"
        :shouldDisable="shouldDisable"
      />
      <!-- アイテム選択 -->
      <ItemSelector
        :selectedItemKeyProp="defaultItemKey || selectedItemKey"
        :selectedSurveyIdProp="selectedSurveyId"
        :surveySchemaProp="surveySchema"
        @selectItem="selectItem"
        :shouldDisable="shouldDisable"
      />
     </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import DataRelativeSurveySelector from '../components/BaseSetting/DataRelativeSurveySelector.vue';
import ItemSelector from "../components/BaseSetting/ItemSelector.vue";
import { GetAndStockSurveyList } from '@/services/form.service';
import { sortSurveyConfigsByUpdatedAtDesc } from '@/stores/modules/reminder/reminder-utils';
import { useReminderStore } from '@/stores/modules/reminder';

const TargetDataItemTypes = ['date', 'birthday'];
const $q = useQuasar();
const segmentsStore = useReminderStore();

const props = defineProps({
  defaultItemKey: {
    type: String
  },
  defaultSurveyId: {
    type: String
  }
});

const emit = defineEmits(['select-item', 'select-survey', 'update-setting', 'is-finding']);

// data
const localDateRelativeReminderSettingsList = ref<any>([]);
const selectedItemKey = ref<string>("");
const selectedItemTitle = ref<string>("");
const selectedSurveyId = ref<string>("");
const selectedSurveyTitle = ref<string>("");
const surveyConfigs = ref<any>([]);
const shouldDisable = ref<boolean>(false);

// methods
const findReminderSettings = async(payload: any) => await segmentsStore.findRelativeReminderSettings(payload);
const clearReminderSettings = segmentsStore.clearRelativeReminderSettings;
const isFetchingSurveyConfigs = computed(() => segmentsStore.isFetchingAllSurveyItems);

const filterConfgisByNonReservedAndHasDateItem = (configs: any[]): any[] => {
  return configs.filter(config => {
    const reservationItem = config.surveySchema.find((item: any) => item.type === 'reservation');
    const dateTypeItem = config.surveySchema.find((item: any) => TargetDataItemTypes.includes(item.type));
    return !reservationItem && dateTypeItem;
  });
};

const filterSchemaByDateItem = (config: any) => {
  config.surveySchema = config.surveySchema.filter((item: any) => TargetDataItemTypes.includes(item.type));
};

const handleFetchSurveyConfigs = async() => {
  try {
    segmentsStore.updateFetchingAllSurveyInRemindarSettings(true);
    const surveyConfigsList = await GetAndStockSurveyList('all');
    const filteredConfigs = filterConfgisByNonReservedAndHasDateItem(surveyConfigsList).filter((config: any) => config.surveyStatus === 'enable');
    filteredConfigs
      .sort(sortSurveyConfigsByUpdatedAtDesc)
      .forEach(config => filterSchemaByDateItem(config));
    surveyConfigs.value = filteredConfigs;
  } catch (error: any) {
    $q.notify({
      message: error,
      type: 'error',
    });
  } finally {
    segmentsStore.updateFetchingAllSurveyInRemindarSettings(false);
  }
};

const initAll = () => {
  initImportData();
  initSelectedItem();
  initSelectedSurvey();
};

const initImportData = () => {
  localDateRelativeReminderSettingsList.value = [];
};

const initSelectedItem = () => {
  selectedItemKey.value = "";
  selectedItemTitle.value = "";
};

const initSelectedSurvey = () => {
  selectedSurveyId.value = "";
  selectedSurveyTitle.value = "";
};

const selectSurvey = (surveyId: string | null): void => {
  const surveyConfig = surveyConfigs.value.find((config: any) => config.surveyId === surveyId);
  if (surveyId === null || !surveyConfig) {
    initSelectedSurvey();
  } else {
    selectedSurveyId.value = surveyConfig.surveyId;
    selectedSurveyTitle.value = surveyConfig.surveyTitle;
  }
  initSelectedItem();
  clearReminderSettings();
  emitUpdate();
};

const selectItem = async (itemKey: string | null): Promise<void> => {
  const selectedItem = surveySchema.value.find((item: any) => item.itemKey === itemKey);
  if (itemKey === null || !selectedItem) {
    initSelectedItem();
    await clearReminderSettings();
  } else {
    selectedItemKey.value = selectedItem.itemKey;
    selectedItemTitle.value = selectedItem.title;
    await doFindReminderSettings();
  }
  emitUpdate();
};

const emitUpdate = (): void => {
  emit('update-setting', {
    itemKey: selectedItemKey.value,
    surveyId: selectedSurveyId.value,
  });
};

const doFindReminderSettings = async (): Promise<void> => {
  emit('is-finding', true);
  await findReminderSettings({surveyId: selectedSurveyId.value, dateItemKey: selectedItemKey.value});
  emit('is-finding', false);
};

// computed
const surveySchema = computed(() => {
  if (selectedSurveyId.value) {
    const surveyConfig = surveyConfigs.value.find((item: any) => item.surveyId === selectedSurveyId.value);
    // console.log('surveySchema', surveyConfig);
    return surveyConfig.surveySchema;
  } else {
    return [];
  }
});

const shouldShowLoader = computed(() => {
  return isFetchingSurveyConfigs.value;
});

// watch
watch(
  () => selectedItemKey.value, 
  () => {
    initImportData();
  }
);

watch(
  () => selectedSurveyId.value, 
  () => {
    initSelectedItem();
    initImportData();
  }
);

watch(
  () => shouldShowLoader.value, 
  (value) => {
    if(value) {
      $q.loading.show();
    }
    else {
      $q.loading.hide();
    }
  }
);

// hooks
onMounted(async() => {
  await handleFetchSurveyConfigs();
  if (props.defaultItemKey && props.defaultSurveyId) {
    selectedItemKey.value = props.defaultItemKey;
    selectedSurveyId.value = props.defaultSurveyId;
    shouldDisable.value = true;
    doFindReminderSettings();
  }
});
</script>

