<template>
  <div>
    <div fluid v-if="hideMenu" class="tw-py-3">
      <q-card flat bordered class="tw-p-4">
        <q-card-section>
          <div>帳票と質問項目を選択してください</div>
        </q-card-section>
      </q-card>
    </div>

    <div v-else  style="padding: 0px;">
      <div class="tw-mb-3 tw-flex tw-justify-end">
        <q-btn
          color="primary"
          class="d-block"
          :disabled="!canAddSetting"
          @click="onAddMessage"
          label="配信設定を追加"
        />
      </div>
      <div v-for="(setting, index) in localReminderSettings" :key="index">
        <SingleReminderContent
          :setting="setting"
          :settingIndex="index"
          :shouldHighLight="highLightReminderId == setting.reminderLocalId"
          @delete="onDelete"
          @change="onUpdate"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch,  } from 'vue';
import SingleReminderContent from "../components/MessageSetting/SingleReminderContent.vue";
import { 
  MAX_REMINDER_SETTINGS_IN_A_ITEM,
  defaultDateRelativeReminderSettingPayload
} from '@/stores/modules/reminder/reminder.model';
import { IDateRelativeReminderSettingPayload } from '@/stores/modules/reminder/reminder.types';
import { useReminderStore } from '@/stores/modules/reminder';

// store
const reminderStore = useReminderStore();

// data
const localReminderSettings = ref<IDateRelativeReminderSettingPayload[]>([]);
const props = defineProps<{
  reminderSettings: IDateRelativeReminderSettingPayload[],
  hideMenu: boolean,
  highLightReminderId?: string
}>();

// methods
const onAddMessage = () => {
  if ((localReminderSettings.value?.length ?? 0) < MAX_REMINDER_SETTINGS_IN_A_ITEM) {
    const newSetting = defaultDateRelativeReminderSettingPayload();
    reminderStore.addDateRelativeReminderSettingsItemOfSettings(newSetting);
    // localReminderSettings.value.push(newSetting);
  }
};

const onDelete = (index: number): void => {
  reminderStore.removeDateRelativeReminderSettingsItemOfSettings(index);
  // localReminderSettings.value = localReminderSettings.value.filter((val: any, i: any) => {
  //   return i !== index;
  // });
};

const onUpdate = (index: number, setting: any): void => {
  localReminderSettings.value[index] = setting;
};

// computed
const canAddSetting = computed(() =>{
  return (localReminderSettings.value?.length ?? 0) < MAX_REMINDER_SETTINGS_IN_A_ITEM;
});

// watch
watch(
  () => props.reminderSettings, 
  (newValue) => {
    localReminderSettings.value = newValue;
  },
  {
    immediate: true,
    deep: true 
  },
);

// storeに直接変更情報を登録
// watch(
//   () => localReminderSettings,
//   (newValue) => {
//     eventBus.emit('update-content', newValue);
//   },
//   { deep: true }
// );
</script>

