<template>
  <div>
    <input
      type="file"
      accept="image/png, image/jpeg"
      ref='inputFile'
      @change="onChangeImageMessage($event, index1, index2)"
      style="display: none"
    />
    <q-card v-if="message.type === 'image'" class="mx-1 my-4" flat bordered hide-details>
      <q-page-container fluid center class="tw-p-4 tw-flex tw-justify-center row">
        <q-btn
          v-if="!message.imageUrl"
          color="primary"
          class="tw-my-3"
          @click="uploadImage()"
        >
          写真をアップロード
        </q-btn>
        <Alert
          v-if="message.error.value"
          type="error"
          :message="message.error.message"
          class="tw-my-3 col-12"
        />
        <div v-if="message.imageUrl && !message.error.value" class="tw-w-full">
          <q-img class="tw-my-4" fit='contain' :src="message.imageUrl" style="max-height: 150px;"></q-img>
          <div class="tw-flex tw-justify-center">
            <q-btn color="negative" class="tw-my-2" @click="removeImage(index1, index2)" label="削除" />
          </div>
        </div>
      </q-page-container>
      <div class="text-left tw-mb-2 tw-px-4 text-grey-7">
        <q-item-label class="text-caption">
          ファイル形式：JPG、JPEG、PNG<br />
          ファイルサイズ：10MB以下 (LINEトーク上でプレビュー表示される画像は、システム内部で圧縮された画像になります)
        </q-item-label>
      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import Alert from '@/components/common/Alert.vue';
import { useReminderStore } from '@/stores/modules/reminder';
import { ref } from 'vue';

// store
const reminderStore = useReminderStore();

// props
defineProps<{
  message: any,
  index1: number,
  index2: number,
}>();

// data
const inputFile = ref();

// method
const uploadImage = (): void => {
  inputFile.value.click();
};

const removeImage = (index1: any, index2: any): void => {
  const image = {
    imageUrl: '',
    file: null,
    error: { value: false, message: '' },
  };
  reminderStore.setReminderSettingsImageMessage({
    image,
    index1,
    index2
  });
};

const onChangeImageMessage = (event: any, index1: any, index2: any): void => {
  const file = event.target.files[0];
  const totalSizeMB: any = (file.size / Math.pow(1024, 2)).toFixed(2);
  const image = {
    imageUrl: '',
    file: null,
    error: { value: true, message: '10MB 以下のファイルを選んでください。' },
  };

  if (totalSizeMB < 10) {
    if (file.type != 'image/png' && file.type != 'image/jpeg' && file.type != 'image/jpg') {
      image.error.message = 'JPG、JPEG、PNG の写真ファイルを選んでください。';
    } else {
      image.imageUrl = URL.createObjectURL(file);
      image.file = file,
      image.error = {
        value: false,
        message: '',
      };
    }
  }
  reminderStore.setReminderSettingsImageMessage({
    image,
    index1,
    index2
  });
  inputFile.value.value = '';
};
</script>