<template>
  <div class="row flex q-gutter-none items-center">
    <div class="col-auto tw-py-2 tw-px-4">
      <q-item-label>分類</q-item-label>
    </div>
    <div class="col-10">
      <q-select
        v-model="selected" 
        :options="categoryNameOptions"
        option-label="text"
        option-value="value"
        map-options emit-value
        :disable="isLoadingCategories || !isSelectedSurveyId"
        dense
        outlined
        hide-selected
        use-input
        fill-input
        @filter="searchFilter"
      />
    </div>
    <div class="col-auto tw-mx-2">
      <q-btn flat fab small
        round
        dense
        color="primary"
        :ripple="false"
        :disabled="isLoadingCategories"
        @click="fetchAllRemindCategories()"
        :icon="isLoading(isFetchingAllRemindCategories)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, } from 'vue';
import { useReminderStore } from '@/stores/modules/reminder';
import { OptionsModel } from '@/types/index';

// store
const segmentsStore = useReminderStore();

// data
const initOption = { value: null, text: 'ーー'} as OptionsModel;
const categoryNameOptions = ref<OptionsModel[]>();

// method
const isLoading = (value: boolean) => {
  return value ? "mdi-cached mdi-spin" : "mdi-cached";
};

// methods
const { 
  fetchAllRemindCategories,
  fetchReminderConfiguration,
  setSelectedCategory
} = segmentsStore;

const searchFilter = (value: string, update: any) => {
  if (value === '') {
    update(() => {
      categoryNameOptions.value = categoryNames.value;
    });
    return;
  }
  else {
    update(() => {
      value = value.toLowerCase();
      categoryNameOptions.value = categoryNames.value.filter((elm: any) => 
        elm.text.toLowerCase().indexOf(value) > -1
      );
    });
  }
};

// computed
const allReminderCategories = computed(() => segmentsStore.allReminderCategories);
const isFetchingAllRemindCategories = computed(() => segmentsStore.isFetchingAllRemindCategories);
const selectedSurveyId = computed(() => segmentsStore.selectedSurveyId);
const selectedCategoryId = computed(() => segmentsStore.selectedCategoryId);
const surveyConfigs = computed(() => segmentsStore.allSurveyConfigs);
const isLoadingCategories = computed(() => segmentsStore.isFetchingAllRemindCategories);
const isSelectedSurveyId = computed(() => segmentsStore.selectedSurveyId !== null);

const selected = computed({
  get: () => {
    return categoryNames.value.find((item) => item.value === selectedCategoryId.value) ?? null;
  },
  set: async(value) => {
    setSelectedCategory(value);
    await fetchReminderConfiguration({
      surveyId: selectedSurveyId.value,
      categoryId: selectedCategoryId.value,
    });
  },
});

const categoryNames = computed(() => {
  const options = [ initOption, ];
  const selectedSurveyConfig = surveyConfigs.value.find(config => config.surveyId === selectedSurveyId.value);
  if (!selectedSurveyConfig) {
    return options;
  }

  const reservationItem = selectedSurveyConfig.surveySchema.find(item => item.type === 'reservation');
  if (!reservationItem) {
    return options;
  }

  const { selectedLargeCategory, selectedMediumCategory } = reservationItem;
  const categoriesBySelected = allReminderCategories.value.filter((category: any) => {
    if (!selectedLargeCategory) {
      return true;
    }
    if (!selectedMediumCategory) {
      return category.tag1 === selectedLargeCategory.name;
    }
    return category.tag1 === selectedLargeCategory.name && category.tag2 === selectedMediumCategory.name;
  });

  options.push(
    ...categoriesBySelected.map((category: any) => {
      const { id, tag1, tag2, tag3 } = category;
      let displayCategoryName = `${tag1} > ${tag2}`;
      if (tag3) {
        displayCategoryName += ` > ${tag3}`;
      }
      // console.log(displayCategoryName);
      return {
        value: id,
        text: displayCategoryName.length <= 50
          ? displayCategoryName
          : displayCategoryName.substring(0, 50).concat('...'),
      };
    })
  );
  return options;
});

// hooks
onMounted(() => {
  categoryNameOptions.value = categoryNames.value;
});
</script>
