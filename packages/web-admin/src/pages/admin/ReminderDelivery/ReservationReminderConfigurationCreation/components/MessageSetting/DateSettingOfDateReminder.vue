<template>
  <div>
    <div class="tw-flex tw-items-center row tw-p-3">
      <div>
        <q-icon
          class="tw-mx-1 text-grey-7"
          name="mdi-information-outline"
          size="sm"
        >
          <q-tooltip right>
            <div>
              <p>配信時期に当たる回答を行なったユーザー宛に配信を行います。
              <br/> ※年または月に1以上を設定する場合は、日は15以下で設定してください。
              <br/> ※初回配信は配信設定の登録が配信開始時間(14:00)より前であれば当日、後であれば翌日となります。</p>
              <p>例.【質問項目】お子様の誕生日 【配信時期】1年0か月後の1日前　【メッセージ】明日はお子様の1歳のお誕生日ですね！　と設定すると、
              <br/>誕生日として2022/4/1を回答したユーザーには2023/3/31にこのメッセージが送信されます。</p>
            </div>
          </q-tooltip>
        </q-icon>
      </div>
      <a class="text-subtitle1 text-weight-thin" >質問回答の日付の</a>
      <div>
        <q-input
          v-model="yearSetting"
          :clearable="false"
          type="number"
          :max="maxDateSettings.MAX_YEAR"
          :rules="[validIntegerInBetween(0, maxDateSettings.MAX_YEAR)]"
          @keydown="numberInputOnKeyDown"
          outlined
          style="width: 5rem"
          dense
          class="tw-p-0"
        />
      </div>
      <a class="text-subtitle1 text-weight-thin" >年</a>
      <div>
        <q-input
          v-model="monthSetting"
          :clearable="false"
          type="number"
          min="0"
          :max="maxDateSettings.MAX_MONTH"
          :rules="[validIntegerInBetween(0, maxDateSettings.MAX_MONTH)]"
          @keydown="numberInputOnKeyDown"
          outlined
          background-color="white"
          style="width: 5rem"
          dense
          class="tw-p-0"
        />
      </div>
      <a class="text-subtitle1 text-weight-thin">ヶ月</a>

      <div>
        <q-select
          v-model="yearMonthTimingSettingShow"
          class="mt-2"
          :options="beforeOrAfterOption"
          option-label="text"
          option-value="value"
          single-line
          outlined
          dense
          style="width: 4.5rem"
          hide-details
        />
      </div>

      <a class="text-subtitle1 text-weight-thin" >の</a>

      <div>
        <q-input
          v-model="dateSetting"
          :clearable="false"
          type="number"
          min="0"
          :max="maxDateSettings.MAX_DATE"
          :rules="[validIntegerInBetween(0, maxDateSettings.MAX_DATE)]"
          @keydown="numberInputOnKeyDown"
          outlined
          style="width: 5rem"
          dense
          class="tw-p-0"
        />
      </div>
      <a class="text-subtitle1 text-weight-thin" >日</a>

      <div>
        <q-select
          v-model="dateTimingSettingShow"
          class="mt-2"
          :options="beforeOrAfterOption"
          option-label="text"
          option-value="value"
          single-line
          outlined
          dense
          style="width: 4.5rem"
          hide-details
        />
      </div>

      <a class="text-subtitle1 tw-font-thin" >に配信する</a>
    </div>
    <a class="text-subtitle1 tw-font-thin"  style="height: initial; margin-left: 32px;">※ 配信時間帯は14:00-17:00となります</a>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { 
  IDateRelativeReminderSettingPayload,
  BEFORE_AFTER_OPTIONS,
  BEFORE_AFTER_OPTIONS_TYPE,
  BEFORE_AFTER_SLECT_OPTIONS
} from '@/stores/modules/reminder/reminder.types';
import { MAX_DATE_SETTINGS } from '@/stores/modules/reminder/reminder.model';
import { useReminderStore } from '@/stores/modules/reminder';

const DEFAULT_YEAR = '',
      DEFAULT_MONTH = '',
      DEFAULT_DATE = '';

const props = defineProps<{
  setting: IDateRelativeReminderSettingPayload,
  index: number,
}>();

// store
const reminderStore = useReminderStore();

// data
const initSelected = { text: "後", value: BEFORE_AFTER_OPTIONS.After };
const yearSetting = ref<string | number>(DEFAULT_YEAR);
const monthSetting = ref<string>(DEFAULT_MONTH);
const dateSetting = ref<string>(DEFAULT_DATE);
const yearMonthTimingSetting = ref<BEFORE_AFTER_OPTIONS_TYPE>(BEFORE_AFTER_OPTIONS.After);
const dateTimingSetting = ref<BEFORE_AFTER_OPTIONS_TYPE>(BEFORE_AFTER_OPTIONS.After);
const beforeOrAfterOption = ref(BEFORE_AFTER_SLECT_OPTIONS);

// methods
const validIntegerInBetween = (min: number, max: number) => {
  return (text: string) => {
    if (!/^\d+$/.test(text)) { return '数値を入力してください'; }
    const asInt = parseInt(text);
    if (asInt < min) { return `${min}以上を入力してください`; }
    if (asInt > max) { return `${max}以下を入力してください`; }
    return true;
  };
};

const numberInputOnKeyDown = (e: any): boolean => {
  let char = e.key;
  if (char === '-' || char === '.' || char === 'e' || char === '+') {
    e.preventDefault();
    return false;
  }
  return true;
};

const isNumberLike = (val: any) => /^-?\d+$/.test(val);
const isNegative = (val: any) => /^-/.test(val);

const removeNegative = (val: string | number) =>  {
  if (typeof val === 'number') {
    return Math.abs(val);
  }
  return val.replace(/^-/, '');
};

const onUpdate = (): void => {
  const newSetting: IDateRelativeReminderSettingPayload  = {...props.setting};
  // newSetting.yearsAfter = isNumberLike(yearSetting.value) ? Number(yearSetting.value) : yearSetting.value;
  // newSetting.monthsAfter = isNumberLike(monthSetting.value) ? Number(monthSetting.value) : monthSetting.value;
  // newSetting.daysAfter = isNumberLike(dateSetting.value) ? Number(dateSetting.value) : dateSetting.value;
  newSetting.yearsAfter = Number(yearSetting.value);
  newSetting.monthsAfter = Number(monthSetting.value);
  newSetting.daysAfter = Number(dateSetting.value);
  newSetting.yearMonthTimingSetting = yearMonthTimingSetting.value;
  newSetting.dateTimingSetting = dateTimingSetting.value;
  // eventBus.emit('change', newSetting);
  reminderStore.setDateRelativeReminderSettingsItemOfSettings(newSetting, props.index);
};
  
// computed
const maxDateSettings = computed(() => {
  return MAX_DATE_SETTINGS;
});

const yearMonthTimingSettingShow = computed({
  get: () => {
    let result = beforeOrAfterOption.value.find((elm) => (elm.value === yearMonthTimingSetting.value)) 
      ?? initSelected;
    return result;
  },
  set: (setValue) => {
    yearMonthTimingSetting.value = setValue.value;
  }
});

const dateTimingSettingShow = computed({
  get: () => {
    let result = beforeOrAfterOption.value.find((elm) => (elm.value === dateTimingSetting.value)) 
      ?? initSelected;
    return result;
  },
  set: (setValue) => {
    dateTimingSetting.value = setValue.value;
  }
});

// watch
watch(
  () => props.setting,
  (newValue: IDateRelativeReminderSettingPayload) => {
    if (isNumberLike(newValue.yearsAfter)) {
          yearSetting.value = String(newValue.yearsAfter);
        } else {
          yearSetting.value = DEFAULT_YEAR;
        }

        if (isNumberLike(newValue.monthsAfter)) {
          monthSetting.value = String(newValue.monthsAfter);
        } else {
          monthSetting.value = DEFAULT_MONTH;
        }

        if (isNumberLike(newValue.daysAfter)) {
          dateSetting.value = newValue.daysAfter.toString();
        } else {
          dateSetting.value = DEFAULT_DATE;
        }
        yearMonthTimingSetting.value = newValue.yearMonthTimingSetting ?? BEFORE_AFTER_OPTIONS.After;
        dateTimingSetting.value = newValue.dateTimingSetting ?? BEFORE_AFTER_OPTIONS.After;
  },
  {
    immediate: true,
    deep: true
  },
);

 // いずれの要素でも変更あれば、親コンポーネントに通知する
watch(
  () => yearSetting.value, 
  () => {
    onUpdate();
  }
);

watch(
  () => monthSetting.value, 
  () => {
    onUpdate();
  }
);

watch(
  () => dateSetting.value, 
  () => {
    onUpdate();
  }
);

watch(
  () => yearMonthTimingSetting.value, 
  () => {
    onUpdate();
  }
);

watch(
  () => dateTimingSetting.value, 
  () => {
    onUpdate();
  }
);
</script>

