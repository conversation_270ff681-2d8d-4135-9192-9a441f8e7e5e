<template>
  <q-card flat bordered class="tw-m-4">
    <q-toolbar color="grey lighten-2" dense class="bg-grey-4">
      <q-btn-toggle 
        v-model="messageType"  
        :options="[
          { value: 'text', icon: 'mdi-chat-outline' },
          { value: 'image', icon: 'mdi-image-outline' },
        ]"
        @change="onChangeMessageTypes($event)"
        flat
      >
      </q-btn-toggle>
      <q-space />
      <q-btn
        flat round
        :disable="!canDeleteMe"
        :val="3"
        @click="removeMessage()"
        icon="mdi-close"
      />
    </q-toolbar>
    <div>
      <q-input
        v-if="messageType === 'text'"
        v-model="tempMessageText"
        type="textarea"
        class="tw-p-4"
        maxlength="5000"
        outlined
        placeholder="テキストを入力"
        :rules="textMessageRules"
        @update:model-value="onChangeTextMessage($event)"
      />
      <input
        type="file"
        accept="image/png, image/jpeg"
        ref="inputFileRef"
        @change="onChangeImageMessage($event, settingIndex, messageIndex)"
        style="display: none;"
      />
      <q-card v-if="messageType === 'image'" class="tw-m-4" flat bordered>
        <div fluid center class="tw-flex tw-justify-center row">
          <q-btn
            v-if="!imageDisplayUrl"
            color="primary"
            class="tw-mt-3"
            @click="uploadImage()"
            label="写真をアップロード"
          />
          <div class="col-12 tw-px-4">
            <Alert v-if="tempMessageData.image.error.value" class="tw-my-3 body-2" type="error" text>
              {{ tempMessageData.image.error.message }}
            </Alert>
          </div>
          <div v-if="imageDisplayUrl && !tempMessageData.image.error.value" class="tw-w-full">
            <q-img class="tw-my-4" :src="imageDisplayUrl" style="max-height: 150px;" fit='contain'></q-img>
            <div class="tw-flex tw-justify-center">
              <q-btn color="negative" class="tw-my-2" @click="removeImage()" label="削除"/>
            </div>
          </div>
        </div>
        <div class="text-left mb-2">
          <q-item-label header class="text-caption">
            ファイル形式：JPG、JPEG、PNG<br />
            ファイルサイズ：10MB以下 (LINEトーク上でプレビュー表示される画像は、システム内部で圧縮された画像になります)
          </q-item-label>
        </div>
      </q-card>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import Alert from '@/components/common/Alert.vue';
import { useReminderStore } from '@/stores/modules/reminder';

// store
const reminderStore = useReminderStore();

// emit
const emit = defineEmits(['update', 'delete']);

type MessageType = 'text' | 'image'
type TempMessageDataTpe = {
  text: string,
  image: {
    imageUrl?: string,
    originalContentUrl?: string,
    previewImageUrl?: string,
    error: { value: boolean, message: string },
    file?: any
  }
}

// data
const props = defineProps({
  messageIndex: {
    required: true,
    type: Number,
  },
  settingIndex: {
    required: true,
    type: Number,
  },
  messagesLength: {
    required: true,
    type: Number
  },
  message: {
    required: true,
    type: Object,
  }
});
const textMessageRules = ref([(v: any) => !!v || '']);
const messageType = ref<MessageType>('image');
const tempMessageData = ref<TempMessageDataTpe>({
  image: { error: { value: false, message: '' } },
  text: '',
} as TempMessageDataTpe);
const refs = ref<any>({});

// methods
const onChangeMessageTypes = (value: MessageType): void => {
  messageType.value = value;
  onUpdateMessage();
};

const onChangeTextMessage = (value: any): void => {
  if (tempMessageData.value.text) tempMessageData.value.text = value;
  onUpdateMessage();
};

const onChangeImageMessage = (event: any, index1: any, index2: any): void => {
  const file = event.target.files[0];
  const totalSizeMB: any = (file.size / Math.pow(1024, 2)).toFixed(2);
  const image = {
    imageUrl: '',
    file: null,
    error: { value: true, message: '10MB 以下のファイルを選んでください。' },
  };

  if (totalSizeMB < 10) {
    if (file.type != 'image/png' && file.type != 'image/jpeg' && file.type != 'image/jpg') {
      image.error.message = 'JPG、JPEG、PNG の写真ファイルを選んでください。';
    } else {
      image.imageUrl = URL.createObjectURL(file);
      image.file = file,
      image.error = {
        value: false,
        message: '',
      };
    }
  }
  tempMessageData.value.image = image;
  inputFileRef.value.value = '';
  onUpdateMessage();
};

const uploadImage = (): void => {
  // const refName = inputFileRef.value;
  // refs.value[refName].click();
  inputFileRef.value?.click();
};

const removeImage = (): void => {
  const image = {
    imageUrl: '',
    file: null,
    error: { value: false, message: '' },
  };
  tempMessageData.value.image = image;
  onUpdateMessage();
};

const onUpdateMessage = (): void => {
  // emit('update', {
  //   index: props.messageIndex,
  //   type: messageType.value,
  //   ...tempMessageData.value[messageType.value]
  // });
  let setData;
  if (messageType.value === 'text') {
    setData = {
      type: messageType.value,
      text: tempMessageData.value[messageType.value],
    };
  }
  else if (messageType.value === 'image') {
    setData = {
      type: messageType.value,
      ...tempMessageData.value[messageType.value],
    };
  }

  reminderStore.setDateRelativeReminderSettingsItemOfMessages(setData, props.settingIndex, props.messageIndex);
};

const removeMessage = (): void => {
  emit('delete', props.messageIndex);
},

// computed
canDeleteMe = computed(() => {
  return props.messagesLength > 1;
});

// const inputFileRef = computed(() => {
//   return `inputFile${props.settingIndex}${props.messageIndex}`;
// });
const inputFileRef = ref();

const imageDisplayUrl = computed(() => {
  const tempImageItem: any | undefined = tempMessageData.value.image;
  return tempImageItem.imageUrl || tempImageItem.originalContentUrl || '';
});

// watch
watch(
  () => props.message,  
  (newValue) => {
    const { type, ...others } = newValue;
    messageType.value = type || 'text';
    
    if (messageType.value === 'text') {
      tempMessageData.value.text = others.value ?? (others.text ? others.text : '');
    }
    else if (messageType.value === 'image') {
      tempMessageData.value.image = {
        file: others.file,
        error: others.error,
        ...others,
      };
    }

    if (messageType.value && !tempMessageData.value.image?.error) {
      tempMessageData.value.image.error = { value: false, message: '' };
    }
  },
  {
    immediate: true,
    deep: true
  }
);

// computed
const tempMessageText = computed({
  get: (): string => {
    return tempMessageData.value.text ?? '';
  },
  set: (setValue) => {
    tempMessageData.value.text = setValue;
  }
});
</script>