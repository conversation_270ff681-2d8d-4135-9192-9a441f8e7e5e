<template>
  <div class="tw-mt-4">
    <div class="text-right tw-m-4 row">
      <router-link :to="{ name: 'ReminderDeliveryPage' }" class="list-item-link">
        <q-btn 
          flat
          text-color="primary"
          icon="mdi-arrow-left-circle"
          label="配信リストに戻る"
        />
      </router-link>
      <q-space />
      <q-btn
        color="primary"
        class="tw-mr-3 button-update"
        v-show="isSelectedSurveyId"
        :disabled="!canUpdate"
        :loading="isLoading"
        :style="
          hasActionPermission('hideButton', 'SegmentDelivery_DistributionCreation_SendDistribution')
            ? hideButtonPermissionStyle()
            : ''
        "
        @click="hasActionPermission('click', 'backendRequest') ? handleUpsertReminder() : showActionPermissionError()"
        icon="mdi-content-save-outline"
        label="保存"
      />
      <q-btn
        color="negative"
        class="button-update"
        v-show="isSelectedSurveyId"
        :disable="!canDelete"
        :loading="isLoading"
        @click="hasActionPermission('click', 'backendRequest') ? handleDeleteReminder() : showActionPermissionError()"
        :style="
          hasActionPermission('hideButton', 'SegmentDelivery_DistributionCreation_SendDistribution')
            ? hideButtonPermissionStyle()
            : ''
        "
        icon="mdi-delete-forever-outline"
        label="設定削除"
      />
    </div>
    <SurveySelector class="row flex q-gutter-md items-center" />
    <CateorySelector class="row flex q-gutter-md items-center" />
    <div class="row" v-if="!hasPermissionForViewingReminderConfig && !isFetchingReminderConfig">
      <div class="col ma-4">選択中の帳票・分類のリマインド配信設定を閲覧する権限がありません。</div>
    </div>
    <q-form ref="form" lazy-validation v-else-if="isSelectedSurveyId && !isFetchingReminderConfig">
      <div class="tw-mt-4">
        <q-btn color="primary"
          icon="mdi-playlist-plus"
          class="mt-5 tw-ml-4"
          :disabled="isMaxSettings()"
          :min-width="150"
          @click="addNewSettings"
          label="配信設定追加"
        />
      </div>
      <div v-for="(settings, index1) in displayReminderSettings" :key="index1" class="tw-mt-4">
        <div class="row q-gutter-none mb-2">
          <div class="col-2 row tw-items-center tw-px-4">
            <q-item-label header class="col tw-px-0">
              リマインド配信日時
            </q-item-label>
            <q-icon class="mx-1 col-auto text-grey-7" name="mdi-information-outline" size="sm">
              <q-tooltip bottom>
                <span>予約日の0日前に設定した場合は、予約日当日にリマインド配信されます。</span>
              </q-tooltip>
            </q-icon>
          </div>
          <div class="col-1 align-items-center">
            <span class="tw-ml-4">予約日の</span>
          </div>
          <div class="col-1 align-items-center tw-mx-1">
            <q-input
              class="tw-p-0 tw-w-full"
              v-model="settings.daysBefore"
              dense
              hide-details="auto"
              outlined
              type="number"
              :min="0"
              :max="9"
              :rules="daysBeforRules"
            ></q-input>
          </div>
          <div class="col-1 align-items-center">
            <span class="ml-2">日前</span>
          </div>
          <div class="col-2 align-items-center">
            <q-input
              v-model="displayReminderSettings[index1].sendTime"
              mask="time"
              outlined
              dense
              type="time"
              class="tw-w-full"
              @change="selectTime(index1)"
            >
          </q-input>
          </div>
          <div class="align-items-center">
            <q-icon 
              class="tw-ml-4 text-grey-6" 
              :disable="isMinSettings()" 
              @click="removeReminderSettings(index1)" 
              name="mdi-trash-can-outline" 
              size="sm"
            />
          </div>
        </div>
        <div class="row">
          <div class="col-2">
            <q-item-label header>配信メッセージ</q-item-label>
          </div>
          <ReservationMessageAreas :settings="settings" :index1="index1"></ReservationMessageAreas>
        </div>
        <q-separator class="tw-mb-4" />
      </div>
    </q-form>
    <div class="row" v-else-if="!isFetchingReminderConfig">
      <div class="col tw-m-4">帳票を選択してください。</div>
    </div>
    <div class="tw-py-3">
      <div class="text-right mx-2">
        <q-btn
          color="primary"
          class="tw-mr-3 button-update"
          v-show="isSelectedSurveyId"
          :disabled="!canUpdate"
          :loading="isLoading"
          :style="
            hasActionPermission('hideButton', 'SegmentDelivery_DistributionCreation_SendDistribution')
              ? hideButtonPermissionStyle()
              : ''
          "
          @click="hasActionPermission('click', 'backendRequest') ? handleUpsertReminder() : showActionPermissionError()"
        >
          <q-icon left name="mdi-content-save-outline"></q-icon>
          保存
        </q-btn>
        <q-btn
          color="negative"
          class="button-update"
          v-show="isSelectedSurveyId"
          :disabled="!canDelete"
          :loading="isLoading"
          @click="hasActionPermission('click', 'backendRequest') ? handleDeleteReminder() : showActionPermissionError()"
          :style="
            hasActionPermission('hideButton', 'SegmentDelivery_DistributionCreation_SendDistribution')
              ? hideButtonPermissionStyle()
              : ''
          "
        >
          <q-icon left name="mdi-delete-forever-outline"></q-icon>
          設定削除
        </q-btn>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useReminderStore } from '@/stores/modules/reminder';
import { useAuthStore } from '@/stores/modules/auth.module';
import { useQuasar } from 'quasar';
import { cloneDeep } from 'lodash';
import CateorySelector from './components/CategorySelector.vue';
import SurveySelector from './components/SurveySelector.vue';
import { DEFAULT_REMINDER_SETTINGS } from '@/stores/modules/reminder/reminder.constants';
import { useRoute } from 'vue-router';
import { usePermissionHelper } from '@/mixins/PermissionHelper';
import ReservationMessageAreas from './components/MessageSetting/ReservationMessageAreas.vue';

const DEFAULT_SEND_TIME = "12:00";

const authStore = useAuthStore();
const segmentsStore = useReminderStore();
const $q = useQuasar();
const $route = useRoute();
const {
  hasActionPermission,
  hideButtonPermissionStyle,
  showActionPermissionError
} = usePermissionHelper();

// data
const daysBeforRules = ref([(v: any) =>  (!!v && v >= 0) || '']);
const sendTimeRules = ref([(v: any) => !!v || '']);
const reminderSettings = ref([]);
// const timePickerProperty = ref([
//   {
//     value: DEFAULT_SEND_TIME,
//     show: false,
//     targetIndex: null,
//   },
//   {
//     value: DEFAULT_SEND_TIME,
//     show: false,
//     targetIndex: null,
//   }]);  

// computed
const isFetchingReminderConfig = computed(() => segmentsStore.isFetchingReminderConfig);
const displayReminderSettings = computed(() => segmentsStore.displayReminderSettings);
const isUpdatingReminderConfig = computed(() => segmentsStore.isUpdatingReminderConfig);
const isUpdatingReminderConfigError = computed(() => segmentsStore.isUpdatingReminderConfigError);
const selectedCategoryId = computed(() => segmentsStore.selectedCategoryId);
const isLoadingCategories = computed(() => segmentsStore.isFetchingAllRemindCategories);
const selectedSurveyId = computed(() => segmentsStore.selectedSurveyId);
const isFetchingAllSurveyConfigs = computed(() => segmentsStore.isFetchingAllSurveyConfigs);
const reminderConfiguration = computed(() => segmentsStore.reminderConfiguration);
const surveyConfigs = computed(() => segmentsStore.allSurveyConfigs);
const categories = computed(() => segmentsStore.allReminderCategories);
const hasPermissionForViewingReminderConfig = computed(() => segmentsStore.hasPermissionForViewingReminderConfig);

const isAdministrator = computed(() => {
  return authStore.permissionsFromTeam.isAdministrator;
});

const isSelectedSurveyId = computed(() => {
  return selectedSurveyId.value !== null;
});

const isSelectedCategoryId = computed(() => {
  return selectedCategoryId.value !== null;
});

const isLoading = computed(() => {
  return isLoadingCategories.value
    || isFetchingAllSurveyConfigs.value
    || isFetchingReminderConfig.value
    || isUpdatingReminderConfig.value;
});

const isNewSettings = computed(() => {
  return reminderConfiguration.value === null;
});

const canUpdate = computed(() => {
  return isAdministrator.value ? isSelectedSurveyId : isSelectedSurveyId.value && isSelectedCategoryId;
});

const canDelete = computed(() => {
  if (isNewSettings.value) {
    return false;
  }
  return canUpdate;});

// watch
watch(() => selectedSurveyId, (value) => {
  if (value === null) {
    initReminderConfig();
  }
});

watch(
  () => displayReminderSettings.value, 
  (newVal) => {
    for(let i = 0; i < newVal.length ; i++) {
      displayReminderSettings.value[i].sendTime = newVal[i].sendTime;
    }
  }
);

// methods
const deleteReminderConfig = async(value: any) => {
  await segmentsStore.deleteReminderConfiguration(value);
};

const fetchReminderConfig = async(payload: any) => {
  segmentsStore.fetchReminderConfiguration(payload);
};

const fetchAllReminderCategories = async() => {
  segmentsStore.fetchAllRemindCategories();
};

const removeReminderSettings = (index: any) => segmentsStore.removeReminderSettings(index);

const addNewSettings = (): void => {
  segmentsStore.addReminderSettings(cloneDeep(DEFAULT_REMINDER_SETTINGS));
};

const isMaxSettings = (): boolean => {
  return displayReminderSettings.value.length === 2;
};

const isMinSettings = (): boolean => {
  return displayReminderSettings.value.length === 1;
};

const selectTime = (index: any): void => {
  const value = displayReminderSettings.value[index].sendTime;
  segmentsStore.setReminderSettingsSendTime({ index, value });
};

const handleUpsertReminder = async (): Promise<void> => {
  const message: any = {
    text: 'リマインド配信設定を更新しました。',
  };

  const errorMessage = validateSettings();
  if (errorMessage) {
    message.text = errorMessage;
    message.type = 'error';
    $q.notify({
      message: message.text,
      type: message.type});
    return;
  }

  await segmentsStore.upsertReminderConfiguration(displayReminderSettings.value);
  if (isUpdatingReminderConfigError.value) {
    message.text = 'リマインド配信設定の更新に失敗しました。管理者にお問い合わせください。';
    message.type = 'error';
  }
  $q.notify({
    message: message.text,
    type: message.type
  });
};

const validateSettings = (): any => {
  let errorMessage:any = null;
  for (const settings of displayReminderSettings.value) {
    const { daysBefore, sendTime, messages } = settings;
    if (!validateBeforeDays(daysBefore)) {
      errorMessage = '配信日の入力値が不正です。0 ～ 9の数値を入力してください。';
      break;
    }
    if (!validateSendTime(sendTime)) {
      errorMessage = '配信時間の入力値が不正です。hh:mmの形式で入力してください。';
      break;
    }

    for (const message of messages) {
      const type = message.type;
      if (type === 'text' && !message.text) {
        errorMessage = '配信メッセージが入力されていません。';
        break;
      }
      if(type === 'image' && !message.imageUrl) {
              errorMessage = '配信する画像が選択されていません。';
              break;
      }
    }
  }
  return errorMessage;
};

const validateSendTime = (sendTime: any): boolean => {
  if (!sendTime) {
    return false;
  }
  // sendTimeのフォーマットがhh:mmかチェック
  return sendTime.match(/^([01]?[0-9]|2[0-3]):([0-5][0-9])$/) !== null;
};

const validateBeforeDays = (daysBefore: any): boolean => {
  // daysBeforeが0以上の半角数値かチェック
  const pattern = /^([1-9]\d*|0)$/;
  if (!pattern.test(daysBefore)) {
    return false;
  }

  // daysBeforeが0 ~ 9の値かチェック
  const _daysBefore = Number(daysBefore);
  return _daysBefore >= 0 && _daysBefore <= 9;
};

const handleDeleteReminder = async (): Promise<void> => {
  const message: any = {
    text: 'リマインド配信設定を削除しました。',
  };

  if (reminderConfiguration.value) {
    await deleteReminderConfig(reminderConfiguration.value.id);
    
    if (isUpdatingReminderConfigError.value) {
      message.text = 'リマインド配信設定の削除に失敗しました。管理者にお問い合わせください。';
      message.type = 'error';
    } 
  } else {
    message.text = '未保存の設定は削除できません。',
    message.type = 'error';
  }

  $q.notify({
    message: message.text,
    type: message.type
  });
};

const initSelected = (): void => {
  segmentsStore.setSelectedSurveyId(null);
  segmentsStore.setSelectedCategory(null);
  initReminderConfig();
};

const initReminderConfig = (): void => {
  segmentsStore.setReminderConfiguration(null);
  const defaultSettings = cloneDeep(DEFAULT_REMINDER_SETTINGS);
  segmentsStore.setDisplayReminderSettings([defaultSettings]);
};

const fetchSurveyConfigsAndCategories = async (): Promise<void> => {
  await Promise.all([
    segmentsStore.fetchAllSurveyConfigs(),
    fetchAllReminderCategories()
  ]);
};

const getIdsInRouterParams = () =>  {
  const { surveyId, categoryId } = $route.query;
  if (!surveyId) {
    return null;
  }

  return {
    surveyId,
    categoryId
  };
};

const isExistSurveyAndCategory = (surveyId: any, categoryId: any): boolean => {
  const isExistSurveyConfigs = !!surveyConfigs.value.find(config => config.surveyId === surveyId);
  // surveyIdのみで予約リマインド配信設定が設定されている場合もあるので、paramsにcategoryIdが存在しない場合はtrueを代入するようにする
  const isExistCategory = categoryId ? !!categories.value.find((category: any) => category.id === categoryId) : true;
  return isExistSurveyConfigs && isExistCategory;
};

// hooks
onMounted( async() => {
  await fetchSurveyConfigsAndCategories();
  const ids = getIdsInRouterParams();
  if (ids === null) {
    initSelected();
    return;
  }
  const {
    surveyId,
    categoryId
  } = ids;
  if (!isExistSurveyAndCategory(surveyId, categoryId)) {
    $q.notify({
      message: '帳票もしくは分類が存在しませんでした。'
    });
    initSelected();
    return;
  }
  segmentsStore.setSelectedSurveyId(surveyId);
  segmentsStore.setSelectedCategory(categoryId || null);
  await fetchReminderConfig({
    surveyId: selectedSurveyId.value,
    categoryId: selectedCategoryId.value,
  });
});
</script>

<style lang="less" scoped>
.align-items-center {
  display: flex;
  align-items: center;
}
.button-update {
  min-width: 120px!important;
}
</style>
