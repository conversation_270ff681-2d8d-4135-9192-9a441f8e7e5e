<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-dialog v-model="show" max-width="1000" scrollable>
    <q-card>
      <q-banner color="primary" dark height="5"></q-banner>
      <q-toolbar flat>
        <q-toolbar-title>詳細</q-toolbar-title>
        <q-space></q-space>
        <q-btn icon @click="show = false">
          <q-icon>mdi-close</q-icon>
        </q-btn>
      </q-toolbar>
      <q-separator></q-separator>
      <q-card-section>
        <template v-for="(value, name) in objectName">
          <q-row :key="'row-' + name" class="px-4">
            <q-col :key="'col-' + name" :cols="2">
              <strong>{{ value }}</strong>
            </q-col>
            <q-col :key="'col-item-' + name">
              <template v-if="item && name === 'body'">
                <div class="mail-body-format">
                  {{ item ? item.body : "ーー" }}
                </div>
              </template>
              <template v-else-if="item && name === 'status'">
                <div :class="statusColor(item.status)">
                  {{ item ? convertToJapanese(item.status) : "ーー" }}
                </div>
              </template>
              <template v-else-if="item && name === 'createdAt'">
                {{ item.createdAt === "ーー" ? "ーー" : formatUnixToYYYYMMDHHmmss(item.createdAt) }}
              </template>
              <template v-else-if="item && name === 'finishedAt'">
                {{ item.finishedAt === "ーー" ? "ーー" : formatUnixToYYYYMMDHHmmss(item.finishedAt) }}
              </template>
              <template v-else>
                {{ item ? item[name] : "ーー" }}
              </template>
            </q-col>
          </q-row>
        </template>
      </q-card-section>
      <q-separator></q-separator>
      <q-card-actions class="px-4">
        <q-page-container>
          <Alert
            v-model="resendError"
            class="mt-4"
            color="red"
            border="left"
            elevation="1"
            dismissible
            colored-border
            type="error"
          >
            {{ resendDistributionItemError && resendDistributionItemError.message }}
          </Alert>
          <q-row>
            <q-col>
              <q-btn
                color="primary"
                block
                elevation="1"
                :loading="isResendingDistributionItem"
                :disabled="!(item && item.status === 'ERROR') || isResendingDistributionItem"
                @click="resendItem()"
              >
                <q-icon left>mdi-send</q-icon>
                再送
              </q-btn>
            </q-col>
            <q-col>
              <q-btn color="grey lighten-2" block elevation="1" @click="show = false">
                <q-icon left>mdi-close-box-outline</q-icon>
                閉じる
              </q-btn>
            </q-col>
          </q-row>
        </q-page-container>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { PropType, ref,  computed, watch,  } from 'vue';

import { useSegmentsStore } from '@stores/segmentsStore';
import { useQuasar } from 'quasar';

// old imports
// 旧インポート
/*import { RESEND_DISTRIBUTION_ITEM } from "@/store/action-types";
import { SET_IS_RESENDING_DISTRIBUTION_ITEM, SET_RESEND_DISTRIBUTION_ITEM_ERROR } from "@/store/mutation-types";
import DatetimeFormatter from "@/mixins/DatetimeFormatter";*/

// emits 
const emits = defineEmits<{
  (event: 'close'): void;
  (event: 'refreshTable'): void;
}>();
const $q = useQuasar();

// props
const props = defineProps({
  visible: Boolean as PropType<boolean>,
  close: Function,
  item: Object as PropType<any>,
  refreshTable: Function
});

// data
const objectName = ref<any>({
        id: "ID",
        subject: "件名",
        body: "本文",
        createdAt: "作成日時",
        finishedAt: "完了日時",
        status: "状況",
        error: "エラー",
        deliveriesCount: "配信成功の数",
        failuresCount: "配信失敗の数",
      });
const resendError = ref<boolean>(false);
const resendFinished = ref<boolean>(false);

// methods
const resendDistributionItem = store.resendDistributionItem;
const setResendDistributionItemError = store.setResendDistributionItemError;
const statusColor = (value: any): string => {
      switch (value) {
        case "FINISHED":
          return "primary--text";
        case "ERROR":
          return "red--text";
        default:
          return "orange--text";
      }
    };
const resendItem = (): void => {
      resendDistributionItem(item);
    };
const convertToJapanese = (value: any): any => {
      if (value === "ALL") {
        return "全て";
      }
      if (value === "NEW") {
        return "処理待機中";
      }
      if (value === "QUEUED") {
        return "処理中";
      }
      if (value === "FINISHED") {
        return "完了";
      }
      if (value === "ERROR") {
        return "エラー";
      }
      if (value === "IGNORED") {
        return "送信対象者無し";
      }
      if (value === "TALK") {
        return "ーー";
      }
      return value;
    };

// computed
const isResendingDistributionItem = computed(() => segmentsStore.isResendingDistributionItem);
const resendDistributionItemError = computed(() => segmentsStore.resendDistributionItemError);
const show = computed({
  get(): boolean {
    return props.visible ?? false;
  },
  set(value: boolean): void {
    if (!value) {
      emits("close");
    }
  },
});

// watch
watch(() => resendDistributionItemError.value, (value) => {
      if (value) {
        resendError.value = true;
      } else {
        resendError.value = false;
      }
    });
watch(() => isResendingDistributionItem.value, (value) => {
      if (!resendError.value && !value) {
        $q.notify({
          message: "再送成功しました",
        });
        show.value = false;
        emits("refreshTable");
      }
    });
</script>
<style scoped>

.mail-body-format {
  white-space: pre-line;
}
</style>
