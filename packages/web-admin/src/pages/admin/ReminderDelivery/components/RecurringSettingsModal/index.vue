<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-dialog v-model="show" max-width="700">
    <q-banner color="primary" dark height="5"> </q-banner>
    <q-card>
      <q-toolbar flat>
        <q-toolbar-title>繰り返し指定</q-toolbar-title>
        <q-space></q-space>
        <q-btn icon @click="show = false">
          <q-icon>mdi-close</q-icon>
        </q-btn>
      </q-toolbar>
      <q-page-container>
        <q-row>
          <q-col :cols="gridCols.label">
            <q-item-label>開始日</q-item-label>
          </q-col>
          <q-col :cols="gridCols.date">
            <DatePickerMenu :field="'recurringSettings'" :isEndDate="false" :enablePicker="startType === 'RECURRING'" />
          </q-col>
          <q-col :cols="gridCols.label">
            <q-item-label>終了日</q-item-label>
          </q-col>
          <q-col :cols="gridCols.date">
            <DatePickerMenu :field="'recurringSettings'" :isEndDate="true" :enablePicker="startType === 'RECURRING'" />
          </q-col>
        </q-row>
        <q-row>
          <q-col :cols="gridCols.label">
            <q-item-label>配信時間</q-item-label>
          </q-col>
          <q-col :cols="gridCols.time">
            <TimePickerMenu :field="'recurringSettings'" :enablePicker="startType === 'RECURRING'" />
          </q-col>
        </q-row>
        <q-row>
          <q-col :cols="gridCols.label">
            <q-item-label>配信区分</q-item-label>
          </q-col>
          <q-col :cols="gridCols.period">
            <q-select v-model="period" :items="periodOptions" single-line outlined dense hide-details> </q-select>
          </q-col>
        </q-row>
        <div v-if="period === 'Weekly'">
          <q-row>
            <q-col :cols="gridCols.label">
              <q-item-label>曜日</q-item-label>
            </q-col>
            <q-col>
              <v-chip-group v-model="daysOfWeek" multiple active-class="primary--text">
                <q-chip
                  v-for="day in days"
                  :key="`Weekly_DaysOfWeek_${day.value}`"
                  :value="`daysOfWeek_${day.numericValue}`"
                >
                  {{ day.text }}
                </q-chip>
              </v-chip-group>
            </q-col>
          </q-row>
        </div>
        <div v-if="period === 'Monthly'">
          <q-row>
            <q-col :cols="gridCols.label">
              <q-item-label>日付</q-item-label>
            </q-col>
            <q-col>
              <v-chip-group v-model="daysOfMonth" multiple active-class="primary--text" hide-details column>
                <div v-for="dayRowIndex in 5" :key="`dayRowIndex_${dayRowIndex}`">
                  <div v-if="dayRowIndex <= 4">
                    <q-chip
                      v-for="dayColIndex in 7"
                      :key="`Monthly_ThirtyOneDays_${dayRowIndex}${dayColIndex}`"
                      :value="`daysOfMonth_${7 * (dayRowIndex - 1) + dayColIndex}`"
                      class="px-1 mx-1"
                      style="width: 60px"
                    >
                      <q-col> {{ 7 * (dayRowIndex - 1) + dayColIndex }}日 </q-col>
                    </q-chip>
                  </div>
                  <div v-else>
                    <q-chip
                      v-for="dayColIndex in 3"
                      :key="`Monthly_ThirtyOneDays_${dayRowIndex}${dayColIndex}`"
                      :value="`daysOfMonth_${7 * (dayRowIndex - 1) + dayColIndex}`"
                      class="px-1 mx-1"
                      style="width: 60px"
                    >
                      <q-col> {{ 7 * (dayRowIndex - 1) + dayColIndex }}日 </q-col>
                    </q-chip>
                  </div>
                </div>
              </v-chip-group>
            </q-col>
          </q-row>
        </div>
        <div class="ml-2 mb-2 font-italic body-2 text-center" v-if="period == 'LastDay'">
          期間が月末の場合は、日付の月しか指定できません。
        </div>
        <div v-if="period === 'Custom'">
          <q-row>
            <q-col :cols="gridCols.label">
              <q-item-label>カスタムの種類</q-item-label>
            </q-col>
            <q-col cols="3">
              <q-select v-model="customType" :items="customTypes"> </q-select>
            </q-col>
          </q-row>
          <div v-if="customType === 'skip'">
            <q-row>
              <q-col :cols="gridCols.label">
                <q-item-label>隔週の設定</q-item-label>
              </q-col>
              <q-col :cols="gridCols.label">
                <q-select v-model="customSkipPeriod" :items="skipTypes"> </q-select>
              </q-col>
              <q-col :cols="gridCols.label">
                <q-input
                  v-model="customSkipLength"
                  single-line
                  type="number"
                  :rules="skipLengthRules[customSkipPeriod]"
                />
              </q-col>
            </q-row>
            <q-row>
              <q-col>
                <div class="ml-2 mb-2 body-2 text-center" v-if="customSkipPeriod === 'days'">
                  <div v-if="!isNaN(customSkipLength)">
                    説明：{{ Math.min(skipTypes[0].lim, customSkipLength)
                    }}{{ skipTypeToJapanese(customSkipPeriod) }}おきに1回
                  </div>
                  <div v-else>説明：{{ skipTypeToJapanese(customSkipPeriod) }}おきに1回</div>
                </div>
                <div class="ml-2 mb-2 body-2 text-center" v-if="customSkipPeriod === 'weeks'">
                  <div v-if="!isNaN(customSkipLength)">
                    説明：{{ Math.min(skipTypes[1].lim, customSkipLength)
                    }}{{ skipTypeToJapanese(customSkipPeriod) }}おきに1回
                  </div>
                  <div v-else>説明：{{ skipTypeToJapanese(customSkipPeriod) }}おきに1回</div>
                </div>
                <div class="ml-2 mb-2 body-2 text-center" v-if="customSkipPeriod === 'months'">
                  <div v-if="!isNaN(customSkipLength)">
                    説明：{{ Math.min(skipTypes[2].lim, customSkipLength) }}ヶ{{
                      skipTypeToJapanese(customSkipPeriod)
                    }}おきに1回
                  </div>
                  <div v-else>説明：ヶ{{ skipTypeToJapanese(customSkipPeriod) }}おきに1回</div>
                </div>
              </q-col>
            </q-row>
          </div>
          <div v-if="customType === 'numberedDayOfWeek'">
            <q-row>
              <q-col :cols="gridCols.label">
                <q-item-label>曜日</q-item-label>
              </q-col>
              <q-col>
                <q-row>
                  <q-col :cols="2">
                    <q-item-label>{{ weekNumbers[0].text }}</q-item-label>
                  </q-col>
                  <q-col>
                    <v-chip-group
                      v-model="customNumberedDayOfWeekFirst"
                      multiple
                      active-class="primary--text"
                      hide-details
                    >
                      <q-chip
                        v-for="day in days"
                        :key="`Custom_NumberedDayOfWeek_First_${day.value}`"
                        :value="`customNumberedDayOfWeekFirst_${day.numericValue}`"
                      >
                        {{ day.text }}
                      </q-chip>
                    </v-chip-group>
                  </q-col>
                </q-row>
                <q-row>
                  <q-col :cols="2">
                    <q-item-label>{{ weekNumbers[1].text }}</q-item-label>
                  </q-col>
                  <q-col>
                    <v-chip-group
                      v-model="customNumberedDayOfWeekSecond"
                      multiple
                      active-class="primary--text"
                      hide-details
                    >
                      <q-chip
                        v-for="day in days"
                        :key="`Custom_NumberedDayOfWeek_Second_${day.value}`"
                        :value="`customNumberedDayOfWeekSecond_${day.numericValue}`"
                      >
                        {{ day.text }}
                      </q-chip>
                    </v-chip-group>
                  </q-col>
                </q-row>
                <q-row>
                  <q-col :cols="2">
                    <q-item-label>{{ weekNumbers[2].text }}</q-item-label>
                  </q-col>
                  <q-col>
                    <v-chip-group
                      v-model="customNumberedDayOfWeekThird"
                      multiple
                      active-class="primary--text"
                      hide-details
                    >
                      <q-chip
                        v-for="day in days"
                        :key="`Custom_NumberedDayOfWeek_Third_${day.value}`"
                        :value="`customNumberedDayOfWeekThird_${day.numericValue}`"
                      >
                        {{ day.text }}
                      </q-chip>
                    </v-chip-group>
                  </q-col>
                </q-row>
                <q-row>
                  <q-col :cols="2">
                    <q-item-label>{{ weekNumbers[3].text }}</q-item-label>
                  </q-col>
                  <q-col>
                    <v-chip-group
                      v-model="customNumberedDayOfWeekFourth"
                      multiple
                      active-class="primary--text"
                      hide-details
                    >
                      <q-chip
                        v-for="day in days"
                        :key="`Custom_NumberedDayOfWeek_Fourth_${day.value}`"
                        :value="`customNumberedDayOfWeekFourth_${day.numericValue}`"
                      >
                        {{ day.text }}
                      </q-chip>
                    </v-chip-group>
                  </q-col>
                </q-row>
                <q-row>
                  <q-col :cols="2">
                    <q-item-label>{{ weekNumbers[4].text }}</q-item-label>
                  </q-col>
                  <q-col>
                    <v-chip-group
                      v-model="customNumberedDayOfWeekFifth"
                      multiple
                      active-class="primary--text"
                      hide-details
                    >
                      <q-chip
                        v-for="day in days"
                        :key="`Custom_NumberedDayOfWeek_Fifth_${day.value}`"
                        :value="`customNumberedDayOfWeekFifth_${day.numericValue}`"
                      >
                        {{ day.text }}
                      </q-chip>
                    </v-chip-group>
                  </q-col>
                </q-row>
              </q-col>
            </q-row>
          </div>
          <div v-if="customType === 'dates'">
            <q-row>
              <q-col :cols="gridCols.label">
                <q-item-label>日付</q-item-label>
              </q-col>
              <q-col>
                <q-date v-model="customDates" multiple> </q-date>
              </q-col>
            </q-row>
          </div>
        </div>
        <q-separator></q-separator>
        <div>
          <q-row>
            <q-col>
              <q-checkbox v-model="withExclude" label="除外日を設定する"></q-checkbox>
            </q-col>
          </q-row>
          <template v-if="withExclude">
            除外日設定
            <q-row>
              <q-col :cols="gridCols.label">
                <q-item-label>曜日</q-item-label>
              </q-col>
              <q-col>
                <v-chip-group v-model="excludeDaysOfWeek" multiple active-class="primary--text">
                  <q-chip v-for="day in days" :key="`Exclude_DaysOfWeek_Day_${day.value}`" :value="day.numericValue">
                    {{ day.text }}
                  </q-chip>
                </v-chip-group>
              </q-col>
            </q-row>
            <q-row>
              <q-col :cols="gridCols.label">
                <q-item-label>日付</q-item-label>
              </q-col>
              <q-col>
                <q-date v-model="excludeDates" multiple> </q-date>
              </q-col>
            </q-row>
          </template>
        </div>
      </q-page-container>
      <q-separator></q-separator>
      <q-page-container>
        <q-row>
          <q-col>
            <q-btn color="primary" block elevation="1" @click="show = false">
              <q-icon left>mdi-calendar-multiple-check</q-icon>
              指定
            </q-btn>
          </q-col>
          <q-col>
            <q-btn color="grey lighten-2" block elevation="1" @click="show = false">
              <q-icon left>mdi-close-box-outline</q-icon>
              閉じる
            </q-btn>
          </q-col>
        </q-row>
      </q-page-container>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { PropType, ref,  computed, onBeforeMount } from 'vue';

import { useSegmentsStore } from '@stores/segmentsStore';

// old imports
// 旧インポート
/*import { SET_RECURRING_SETTINGS } from "@/store/mutation-types";
import { DEFAULT_RECURRING_SETTINGS } from "@/store/modules/segments/segments.state";
import DatetimeFormatter from "@/mixins/DatetimeFormatter";
import DatePickerMenu from "../DatetimePickerMenus/DatePickerMenu.vue";
import TimePickerMenu from "../DatetimePickerMenus/TimePickerMenu.vue";
import { cloneDeep, omit } from "lodash";
import moment from "moment";*/

// emits 
const emits = defineEmits<{
  (event: 'close'): void;
}>();

// props
const props = defineProps({
  visible: Boolean as PropType<boolean>,
  close: Function
});

// data
const gridCols = ref<any>({
        label: 2,
        date: 4,
        time: 10,
        period: 10,
      });
const periodOptions = ref<any>([
        { text: "毎日", value: "Daily" },
        { text: "毎週", value: "Weekly" },
        { text: "毎月", value: "Monthly" },
        { text: "月末", value: "LastDay" },
        { text: "カスタム", value: "Custom" },
      ]);
const days = ref<any>([
        { text: "月", value: "Monday", numericValue: 1 },
        { text: "火", value: "Tuesday", numericValue: 2 },
        { text: "水", value: "Wednesday", numericValue: 3 },
        { text: "木", value: "Thursday", numericValue: 4 },
        { text: "金", value: "Friday", numericValue: 5 },
        { text: "土", value: "Saturday", numericValue: 6 },
        { text: "日", value: "Sunday", numericValue: 7 },
      ]);
const customTypes = ref<any>([
        { text: "隔週", value: "skip" },
        { text: "曜日", value: "numberedDayOfWeek" },
        { text: "カレンダー", value: "dates" },
      ]);
const weekNumbers = ref<any>([
        { text: "第1", value: "First" },
        { text: "第2", value: "Second" },
        { text: "第3", value: "Third" },
        { text: "第4", value: "Fourth" },
        { text: "第5", value: "Fifth" },
      ]);
const skipTypes = ref<any>([
        { text: "日", value: "days.value", lim: 365 },
        { text: "週", value: "weeks", lim: 52 },
        { text: "月", value: "months", lim: 12 },
      ]);
const skipLengthRules = ref<any>({
        days: [
          // v => (!!v) || '必須',
          (v) => {
            v = Number(v);
            if (isNaN(v)) return "整数";
            return (Number.isInteger(v) && 1 <= v && v <= 365) || "1から365までの整数";
          },
        ],
        weeks: [
          // v => (!!v) || '必須',
          (v) => {
            v = Number(v);
            if (isNaN(v)) return "整数";
            return (Number.isInteger(v) && 1 <= v && v <= 52) || "1から52までの整数";
          },
        ],
        months: [
          // v => (!!v) || '必須',
          (v) => {
            v = Number(v);
            if (isNaN(v)) return "整数";
            return (Number.isInteger(v) && 1 <= v && v <= 12) || "1から12までの整数";
          },
        ],
      });

// methods
const updateRecurringSettings = store.setRecurringSettings;
const handleUpdate = (value: any): void => {
      updateRecurringSettings({ ...distributionDetail.value.recurringSettings, ...value });
    };
const skipTypeToJapanese = (value: any): any => {
      if (value === "days.value") return "日";
      if (value === "weeks") return "週";
      if (value === "months") return "月";
      return value;
    };
const convertCustomNumberedDayOfWeek = (number: any, value: any): Array<any> => {
      let arr = [];
      for (let item of distributionDetail.value.recurringSettings.custom.numberedDayOfWeek) {
        if (item.number !== number) {
          arr.push(item);
        }
      }
      for (let item of value) {
        const splitResult = item.split("_");
        const dayOfWeek = Number(splitResult[splitResult.length - 1]);
        arr.push({
          dayOfWeek: dayOfWeek,
          number: number,
        });
      }
      return arr;
    };

// computed
const distributionDetail = computed(() => segmentsStore.distributionDetail.value);
const show = computed({
  get(): boolean {
    return props.visible ?? false;
  },
  set(value: boolean): void {
    if (!value) {
      emits("close");
    }
  },
});

const period = computed({
  get(): any {
    return distributionDetail.value.recurringSettings.period;
  },
  set(value: any): void {
    handleUpdate({ period: value });
  },
});

const startType = computed(() => {
  return distributionDetail.value.startType;
});

const withExclude = computed({
  get(): any {
    return distributionDetail.value.recurringSettings.withExclude;
  },
  set(value: any): void {
    handleUpdate({ withExclude: value });
  },
});

const exclude = computed({
  get(): any {
    return distributionDetail.value.recurringSettings.exclude;
  },
  set(value: any): void {
    handleUpdate({ exclude: value });
  },
});

const excludeDaysOfWeek = computed({
  get(): any {
    return distributionDetail.value.recurringSettings.exclude.daysOfWeek;
  },
  set(value: any): void {
    handleUpdate({
      exclude: {
        ...distributionDetail.value.recurringSettings.exclude,
        daysOfWeek: value,
      },
    });
  },
});

const excludeDates = computed({
  get(): any {
    return distributionDetail.value.recurringSettings.exclude.dates;
  },
  set(value: any): void {
    handleUpdate({
      exclude: {
        ...distributionDetail.value.recurringSettings.exclude,
        dates: value,
      },
    });
  },
});

const daysOfWeek = computed({
  get(): any {
    let arr = [];
    for (let item of distributionDetail.value.recurringSettings.daysOfWeek) {
      arr.push(`daysOfWeek_${item}`);
    }
    return arr;
  },
  set(value: any): void {
    let arr = [];
    for (let item of value) {
      const splitResult = item.split("_");
      const number = Number(splitResult[splitResult.length - 1]);
      arr.push(number);
    }
    handleUpdate({ daysOfWeek: arr });
  },
});

const daysOfMonth = computed({
  get(): any {
    let arr = [];
    for (let item of distributionDetail.value.recurringSettings.daysOfMonth) {
      arr.push(`daysOfMonth_${item}`);
    }
    return arr;
  },
  set(value: any): void {
    let arr = [];
    for (let item of value) {
      const splitResult = item.split("_");
      const number = Number(splitResult[splitResult.length - 1]);
      arr.push(number);
    }
    handleUpdate({ daysOfMonth: arr });
  },
});

const custom = computed({
  get(): any {
    return distributionDetail.value.recurringSettings.custom;
  },
  set(value: any): void {
    handleUpdate({ custom: value });
  },
});

const customType = computed({
  get(): any {
    return distributionDetail.value.recurringSettings.custom.type;
  },
  set(value: any): void {
    handleUpdate({
      custom: {
        ...distributionDetail.value.recurringSettings.custom,
        type: value,
      },
    });
  },
});

const customSkip = computed({
  get(): any {
    return distributionDetail.value.recurringSettings.custom.skip;
  },
  set(value: any): void {
    handleUpdate({
      custom: {
        ...distributionDetail.value.recurringSettings.custom,
        skip: value,
      },
    });
  },
});

const customSkipPeriod = computed({
  get(): any {
    return distributionDetail.value.recurringSettings.custom.skip.period;
  },
  set(value: any): void {
    handleUpdate({
      custom: {
        ...distributionDetail.value.recurringSettings.custom,
        skip: {
          ...distributionDetail.value.recurringSettings.custom.skip,
          period: value,
        },
      },
    });
  },
});

const customSkipLength = computed({
  get(): any {
    return distributionDetail.value.recurringSettings.custom.skip.length;
  },
  set(value: any): void {
    let num = Number(value);
    let length = 0;
    if (!isNaN(num)) {
      length = num;
    }
    handleUpdate({
      custom: {
        ...distributionDetail.value.recurringSettings.custom,
        skip: {
          ...distributionDetail.value.recurringSettings.custom.skip,
          length: length,
        },
      },
    });
  },
});

const customNumberedDayOfWeekFirst = computed({
  get(): any {
    let arr = [];
    for (let item of distributionDetail.value.recurringSettings.custom.numberedDayOfWeek) {
      if (item.number === 1) {
        arr.push(`customNumberedDayOfWeekFirst_${item.dayOfWeek}`);
      }
    }
    return arr;
  },
  set(value: any): void {
    handleUpdate({
      custom: {
        ...distributionDetail.value.recurringSettings.custom,
        numberedDayOfWeek: convertCustomNumberedDayOfWeek(1, value),
      },
    });
  },
});

const customNumberedDayOfWeekSecond = computed({
  get(): any {
    let arr = [];
    for (let item of distributionDetail.value.recurringSettings.custom.numberedDayOfWeek) {
      if (item.number === 2) {
        arr.push(`customNumberedDayOfWeekSecond_${item.dayOfWeek}`);
      }
    }
    return arr;
  },
  set(value: any): void {
    handleUpdate({
      custom: {
        ...distributionDetail.value.recurringSettings.custom,
        numberedDayOfWeek: convertCustomNumberedDayOfWeek(2, value),
      },
    });
  },
});

const customNumberedDayOfWeekThird = computed({
  get(): any {
    let arr = [];
    for (let item of distributionDetail.value.recurringSettings.custom.numberedDayOfWeek) {
      if (item.number === 3) {
        arr.push(`customNumberedDayOfWeekThird_${item.dayOfWeek}`);
      }
    }
    return arr;
  },
  set(value: any): void {
    handleUpdate({
      custom: {
        ...distributionDetail.value.recurringSettings.custom,
        numberedDayOfWeek: convertCustomNumberedDayOfWeek(3, value),
      },
    });
  },
});

const customNumberedDayOfWeekFourth = computed({
  get(): any {
    let arr = [];
    for (let item of distributionDetail.value.recurringSettings.custom.numberedDayOfWeek) {
      if (item.number === 4) {
        arr.push(`customNumberedDayOfWeekFourth_${item.dayOfWeek}`);
      }
    }
    return arr;
  },
  set(value: any): void {
    handleUpdate({
      custom: {
        ...distributionDetail.value.recurringSettings.custom,
        numberedDayOfWeek: convertCustomNumberedDayOfWeek(4, value),
      },
    });
  },
});

const customNumberedDayOfWeekFifth = computed({
  get(): any {
    let arr = [];
    for (let item of distributionDetail.value.recurringSettings.custom.numberedDayOfWeek) {
      if (item.number === 5) {
        arr.push(`customNumberedDayOfWeekFifth_${item.dayOfWeek}`);
      }
    }
    return arr;
  },
  set(value: any): void {
    handleUpdate({
      custom: {
        ...distributionDetail.value.recurringSettings.custom,
        numberedDayOfWeek: convertCustomNumberedDayOfWeek(5, value),
      },
    });
  },
});

const customDates = computed({
  get(): any {
    return distributionDetail.value.recurringSettings.custom.dates;
  },
  set(value: any): void {
    handleUpdate({
      custom: {
        ...distributionDetail.value.recurringSettings.custom,
        dates: value,
      },
    });
  },
});

// hooks

onBeforeMount(() => {});

</script>
