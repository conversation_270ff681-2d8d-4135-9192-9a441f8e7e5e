<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-dialog
    v-model="show"
    :persistent="isCreatingSegmentDelivery || isUpdatingSegmentDelivery"
    max-width="700"
  >
    <q-card>
      <q-toolbar flat>
        <q-toolbar-title class="text-h5" style="font-weight: bold;"> {{ isEditData ? "配信編集" : "新規作成" }}</q-toolbar-title>
        <q-space></q-space>
        <q-btn icon @click="show = false">
          <q-icon>mdi-close</q-icon>
        </q-btn>
      </q-toolbar>
      <q-form ref="form" v-model="valid">
        <q-page-container fluid>
          <q-row style="display: block; margin: 1em; margin-bottom: 1em !important;">
            <span>配信名</span>
            <q-input
                v-model="deliveryTitle"
                :rules="deliveryTitleRules"
                outlined
                dense
                hide-details
                placeholder="配信名"
            ></q-input>
          </q-row>
          <q-row style="display: block; margin: 1em; margin-bottom: 1em !important;">
            <span>環境を選択</span>
            <q-select
                v-model="selectedEnvironment"
                :items="listOfEnvironments"
                :rules="environmentRules"
                :disabled="isFetchingTalksForOutsideDistribution"
                outlined
                dense
                hide-details
                placeholder="環境名"
            ></q-select>
          </q-row>
          <q-row style="display: block; margin: 1em; margin-bottom: 1em !important;">
            <span>トークを選択</span>
            <q-select
                v-model="selectedTalk"
                :items="talksToDisplay"
                :rules="talkRules"
                :loading="isFetchingTalksForOutsideDistribution"
                :disabled="isFetchingTalksForOutsideDistribution"
                item-text="name"
                item-value="dataId"
                outlined
                dense
                hide-details
                placeholder="トーク名"
            ></q-select>
            <q-checkbox
                style="margin-top: 0;"
                hide-details="auto"
                v-model="useDisasterRichmenu"
                label="災害用リッチメニューを使用"
            ></q-checkbox>
          </q-row>
          <q-row style="display: block; margin: 1em;">
            <span>有効 / 無効</span>
            <q-radio style="margin-top: 0 !important;" v-model="enabled" row hide-details class="my-2">
                <q-radio color="primary" label="有効" :value="true"></q-radio>
                <q-radio color="primary" label="無効" :value="false"></q-radio>
            </q-radio>
          </q-row>
        </q-page-container>
        <q-card-actions class="px-4">
          <q-row>
            <q-space></q-space>
            <q-col cols="2">
              <q-btn color="primary" outlined @click="show = false"> キャンセル </q-btn>
            </q-col>
            <q-col cols="2">
              <q-btn
                color="primary"
                block
                :loading="isCreatingSegmentDelivery || isUpdatingSegmentDelivery"
                :disabled="isSaveDisabled"
                @click="onSave()"
                :style="
                  hasActionPermission('hideButton', 'SegmentDelivery_DataModal_Save') ? hideButtonPermissionStyle() : ''
                "
              >
                {{ isEditData ? "保存" : "作成" }}
              </q-btn>
            </q-col>
          </q-row>
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { PropType, ref,  computed, watch, onBeforeMount } from 'vue';

import { useSegmentsStore } from '@stores/segmentsStore';
import { useQuasar } from 'quasar';

// old imports
// 旧インポート
/*import {
  CREATE_SEGMENT_DELIVERY,
  UPDATE_SEGMENT_DELIVERY,
  FETCH_TALK_DELIVERY_LIST,
  FETCH_SCENARIO_TALKS_FOR_TALK_DISTRIBUTION,
} from "@/store/action-types";
import { SET_DISTRIBUTION_DETAIL } from "@/store/mutation-types";
import { emitSnackbar } from "@/utils/emitComponents";*/

// emits 
const emits = defineEmits<{
  (event: 'close'): void;
}>();
const $q = useQuasar();

// props
const props = defineProps({
  visible: Boolean as PropType<boolean>,
  close: Function,
  item: Object as PropType<any>,
  source: String as PropType<string>,
  getSurveyConditions: Function,
  distItem: Object as PropType<any>,
  updateTargetEstimate: Function
});

// data
const valid = ref<boolean>(false);
const isEditData = ref<boolean>(false);
const distributionConfigId = ref<any>(null);
const deliveryTitle = ref<string>("");
const enabled = ref<boolean>(true);
const selectedTalk = ref<any>(null);
const selectedEnvironment = ref<string>("sandbox");
const useDisasterRichmenu = ref<boolean>(false);
const deliveryTitleRules = ref<any>([(v) => !!v || "配信名は必須入力です。"]);
const talkRules = ref<any>([(v) => !!v || "トークは必須入力です。"]);
const environmentRules = ref<any>([(v) => !!v || "環境は必須入力です。"]);
const listOfEnvironments = ref<any>([
        {
          text: "本番",
          value: "production"
        },
        {
          text: "サンドボックス",
          value: "sandbox",
        }
      ]);
const createError = ref<boolean>(false);
const updateError = ref<boolean>(false);

// methods
const createSegmentDelivery = store.createSegmentDelivery;
const updateSegmentDelivery = store.updateSegmentDelivery;
const fetchTalkDeliveryList = store.fetchTalkDeliveryList;
const fetchActiveScenarioTalks = store.fetchScenarioTalksForTalkDistribution;
const updateDistributionDetail = store.setDistributionDetail;
const initializeValues = async (): Promise<void> => {
      if (item) {
        deliveryTitle.value = item.deliveryTitle.value;
        selectedEnvironment.value = item.environment;
        selectedTalk.value = item.talkId;
        useDisasterRichmenu.value = item.useDisasterRichmenu.value;
        enabled.value = item.enabled.value;
        distributionConfigId.value = item.distributionConfigId.value;
      }
      fetchActiveScenarioTalks();
    };
const getTalkName = (talkId: any): any => {
      return talksForOutsideDistribution.value[selectedEnvironment.value].find(elem => elem.dataId === talkId).name;
    };
const onSave = (): void => {
      let params: any = {
        name: deliveryTitle.value,
        enabled: enabled.value,
        environment: selectedEnvironment.value,
        talkId: selectedTalk.value,
        useDisasterRichmenu: useDisasterRichmenu.value
      };
      params["talkName"] = getTalkName(selectedTalk.value);
      
      if (distributionConfigId.value) {
        params.id = distributionConfigId.value;
      }

      if (!isEditData.value) {
        create(params);
      } else {
        update(params);
      }
    };
const create = async (params: any): Promise<void> => {
      if (hasActionPermission("click", "backendRequest")) {
        await createSegmentDelivery(params)
        if (createSegmentDeliveryError.value) {
          emitSnackbar($snackbar, createSegmentDeliveryError.value, "error");
        } else {
          show.value = false;
          $q.notify({
            message: "配信データを追加しました。",
          });
          await fetchTalkDeliveryList();
        }
      } else {
        showActionPermissionError();
      }
    };
const update = async (params: any): Promise<void> => {
      if (hasActionPermission("click", "backendRequest")) {
        await updateSegmentDelivery(params)
        if (updateSegmentDeliveryError.value) {
          emitSnackbar($snackbar, updateSegmentDeliveryError.value, "error");
        } else {
          show.value = false;
          $q.notify({
            message: "配信情報を更新しました。",
          });
          await fetchTalkDeliveryList();
        }
      } else {
        showActionPermissionError();
      }
    };
const resetValues = (): void => {
      deliveryTitle.value = "";
      enabled.value = true;
      createError.value = false;
      updateError.value = false;
      selectedTalk.value = null;
      environment = "sandbox";
      useDisasterRichmenu.value = false;
    };
const handleUpdate = (value: any): void => {
      updateDistributionDetail({ ...distributionDetail.value, ...value });
    };

// computed
const isCreatingSegmentDelivery = computed(() => segmentsStore.isCreatingSegmentDelivery);
const createSegmentDeliveryError = computed(() => segmentsStore.createSegmentDeliveryError);
const isUpdatingSegmentDelivery = computed(() => segmentsStore.isUpdatingSegmentDelivery);
const updateSegmentDeliveryError = computed(() => segmentsStore.updateSegmentDeliveryError);
const isFetchingTalksForOutsideDistribution = computed(() => segmentsStore.isFetchingTalksForOutsideDistribution);
const fetchTalksForOutsideDistributionError = computed(() => segmentsStore.fetchTalksForOutsideDistributionError);
const talksForOutsideDistribution = computed(() => segmentsStore.talksForOutsideDistribution);
const distributionDetail = computed(() => segmentsStore.distributionDetail);
const talksToDisplay = computed(() => {
      return selectedEnvironment.value in talksForOutsideDistribution.value ? talksForOutsideDistribution.value[selectedEnvironment.value] : [];
    });
const show = computed({
  get(): boolean {
    return props.visible;
  },
  set(value: boolean): void {
    if (!value) {
      emits("close");
    }
  },
});
const isSaveDisabled = computed((): boolean => {
      return !valid.value || !selectedTalkInAvailableTalks.value || isCreatingSegmentDelivery.value || isUpdatingSegmentDelivery.value;
    });
const selectedTalkInAvailableTalks = computed((): any => {
      return talksForOutsideDistribution.value[selectedEnvironment.value].find(elem => elem.dataId == selectedTalk.value);
    });

// watch
watch(() => visible, (value) => {
      if (value) {
        isEditData.value = item ? true : false;
        resetValues();
        initializeValues();
        if ($refs.form) {
          $refs.form.resetValidation();
        }
      }
    });
watch(() => createSegmentDeliveryError.value, (value) => {
      if (value) {
        createError.value = true;
      } else {
        createError.value = false;
      }
    });
watch(() => updateSegmentDeliveryError.value, (value) => {
      if (value) {
        updateError.value = true;
      } else {
        updateError.value = false;
      }
    });

// hooks

onBeforeMount(async () => {});

</script>