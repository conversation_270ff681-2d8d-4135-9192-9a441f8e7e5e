<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-menu
    ref="menu"
    v-model="menu_time"
    disabled
  >
    <template v-slot:activator="{ on, attrs }">
      <q-input
        v-model="time"
        :rules="timeRules"
        outlined
        dense
        hide-details="auto"
        clearable
        :filled="!enablePicker"
        :disabled="!enablePicker"
        v-bind="attrs"
        v-on="on"
        style="max-width: 120px"
        type="time"
      ></q-input>
    </template>
  </q-menu>
</template>

<script setup lang="ts">
import { PropType, ref,  computed, onBeforeMount } from 'vue';

import { useSegmentsStore } from '@stores/segmentsStore';

// old imports
// 旧インポート
/*import { SET_DISTRIBUTION_DETAIL, SET_RECURRING_SETTINGS } from "@/store/mutation-types";*/


// props
const props = defineProps({
  field: String as PropType<string>,
  enablePicker: Boolean as PropType<boolean>
});

// data
const menu_time = ref<any>(false);

// methods
const updateDistributionDetail = store.setDistributionDetail;
const updateRecurringSettings = store.setRecurringSettings;
const handleUpdate = (value: any): void => {
      field == "recurringSettings"
        ? updateRecurringSettings({ ...distributionDetail.value.recurringSettings, ...value })
        : updateDistributionDetail({ ...distributionDetail.value, ...value });
    };

// computed
const distributionDetail = computed(() => segmentsStore.distributionDetail);
const time = computed({
  get(): any {
    return field == "recurringSettings"
      ? distributionDetail.value.recurringSettings.fromTime
      : distributionDetail.value.scheduledStartTime;
  },
  set(value: any): void {
    field == "recurringSettings"
      ? handleUpdate({ fromTime: value })
      : handleUpdate({ scheduledStartTime: value });
  },
});

const startType = computed({
  get(): any {
    return distributionDetail.value.startType;
  },
});

const timeRules = computed({
  get(): any {
    if (enablePicker) {
      return [(v) => !!v || "時間は必須入力です。"];
    }
    return [];
  },
});

// hooks

onBeforeMount(() => {});

</script>
