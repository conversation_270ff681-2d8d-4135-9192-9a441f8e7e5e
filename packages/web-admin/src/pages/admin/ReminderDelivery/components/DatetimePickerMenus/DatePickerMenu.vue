<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-menu v-model="menu_date" :close-on-content-click="false" transition="scale-transition" offset-y min-width="290px">
    <template v-slot:activator="{ on, attrs }">
      <q-input
        v-model="date"
        :rules="dateRules"
        readonly
        outlined
        dense
        hide-details="auto"
        clearable
        :filled="!enablePicker"
        :disabled="!enablePicker"
        v-bind="attrs"
        v-on="on"
        style="max-width: 150px"
      ></q-input>
    </template>
    <q-date v-model="date" :type="datetype" color="primary" no-title show-current @input="menu_date = false">
    </q-date>
  </q-menu>
</template>

<script setup lang="ts">
import { PropType, ref,  computed, onBeforeMount } from 'vue';

import { useSegmentsStore } from '@stores/segmentsStore';

// old imports
// 旧インポート
/*import { SET_DISTRIBUTION_DETAIL, SET_RECURRING_SETTINGS } from "@/store/mutation-types";
import DatetimeFormatter from "@/mixins/DatetimeFormatter";*/


// props
const props = defineProps({
  field: String as PropType<string>,
  isEndDate: Boolean as PropType<boolean>,
  enablePicker: Boolean as PropType<boolean>
});

// data
const menu_date = ref<boolean>(false);
const fromDateString = ref<string>("");
const toDateString = ref<string>("");

// methods
const updateDistributionDetail = store.setDistributionDetail;
const updateRecurringSettings = store.setRecurringSettings;
const handleUpdate = (value: any): void => {
      if (field == "recurringSettings") {
        updateRecurringSettings({ ...distributionDetail.value.recurringSettings, ...value });
      } else {
        updateDistributionDetail({ ...distributionDetail.value, ...value });
      }
    };

// computed
const distributionDetail = computed(() => segmentsStore.distributionDetail);
const date = computed({
  get(): any {
    if (field == "recurringSettings") {
      if (distributionDetail.value.recurringSettings.period == "LastDay") {
        return isEndDate
          ? distributionDetail.value.recurringSettings.toDate
            ? formatToYYYYMM(distributionDetail.value.recurringSettings.toDate)
            : null
          : distributionDetail.value.recurringSettings.fromDate
          ? formatToYYYYMM(distributionDetail.value.recurringSettings.fromDate)
          : null;
      } else {
        return isEndDate
          ? distributionDetail.value.recurringSettings.toDate
            ? formatToYYYYMMDD(distributionDetail.value.recurringSettings.toDate)
            : null
          : distributionDetail.value.recurringSettings.fromDate
          ? formatToYYYYMMDD(distributionDetail.value.recurringSettings.fromDate)
          : null;
      }
    } else {
      return distributionDetail.value.scheduledStartDate;
    }
  },
  set(value: any): void {
    if (field == "recurringSettings") {
      isEndDate ? handleUpdate({ toDate: value }) : handleUpdate({ fromDate: value });
    } else {
      handleUpdate({ scheduledStartDate: value });
    }
  },
});

const datetype = computed(() => {
  if (field == "recurringSettings" && distributionDetail.value.recurringSettings.period == "LastDay") {
    return "month";
  }
  return "date";
});

const startType = computed(() => {
  return distributionDetail.value.startType;
});

const dateRules = computed(() => {
  if (enablePicker) {
    return [(v) => !!v || "日付は必須入力です。"];
  }
  return [];
});

// hooks

onBeforeMount(() => {});

</script>
