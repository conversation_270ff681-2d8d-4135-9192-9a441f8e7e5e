<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
@font-face {
  font-family: lucidaConsole;
  src: url("../../../../../assets/fonts/LUCON.TTF");
}
.json-edit-area {
  white-space: nowrap;
  font-family: lucidaConsole, monospace;
}
</style>

<template>
  <div>
    <h4 v-show="errorText">
      <span class="red--text"><q-icon>mdi-alert</q-icon>データ形式が不正なため保存できません</span>
    </h4>
    <q-input textarea
      outlined
      class="json-edit-area"
      background-color="white lighten-2"
      rows="15"
      :value="JSON.stringify(params, undefined, 2)"
      @input="onChangeText($event)"
    ></q-input>
    <q-row :elevation="0">
      <q-col sm="8">
        <q-select use-input
          v-model="selectedTemplate"
          :items="teamSearchCriteriaOptions"
          flat
          outlined
          dense
          background-color="white"
          hide-details="auto"
          :placeholder="'テンプレート'"
        ></q-select>
      </q-col>
      <q-col sm="4">
        <q-btn class ="white--text" color="primary" elevation="0" @click="setTemplate">
          反映
        </q-btn>
      </q-col>
    </q-row>
    <span><a href="https://developers.line.biz/flex-simulator/" target="_blank">Flex Message Simulator</a>を使用して、テンプレートのJSONを作成することができます。</span><br>
    <span>フレックスメッセージのサンプルは<a href="https://docs.line-smartcity.org/flex-sample" target="_blank">こちら</a>を参照してください。</span>
  </div>
</template>

<script setup lang="ts">
import { PropType, ref,  computed, onMounted, onBeforeUnmount } from 'vue';



// old imports
// 旧インポート
/*import { render } from "@/services/flexRender.ts";
import { FLEXMESSAGE_TEMPLATE_ITEMS } from "@/store/modules/segments/segments.constants";*/

// emits 
const emits = defineEmits<{
  (event: 'updateSaveStatus', payload: any): void;
}>();

// props
const props = defineProps({
  params: { type: Object as PropType<any>, required: true }
});

// data
const text = ref<string>("");
const errorText = ref<boolean>(false);
const selectedTemplate = ref<any>({});

// methods
const onChangeText = (value: string): void => {
      let validateResult = validateJSON(value);
      reportValidation(validateResult);
      if (validateResult) {
        emits("updateModelParams", {
          payload: store.json.parse(value)
        });
      }
    };
const setTemplate = (): void => {
      if (selectedTemplate.value) {
        onChangeText(JSON.stringify(selectedTemplate.value));
      }
    };
const reportValidation = (result: any): void => {
      emits("updateSaveStatus", { validateResult: result });
    };
const validateJSON = (value: string): boolean => {
      try {
        let parsedInput = JSON.parse(value);
        render(parsedInput)
        errorText.value = false;
        return true;
      } catch (error: any) {
        errorText.value = true;
        return false;
      }
    };

// computed
const teamSearchCriteriaOptions = computed(() => {
      return FLEXMESSAGE_TEMPLATE_ITEMS;
    });

// hooks

onMounted(() => {
  let validateResult = validateJSON(JSON.stringify(params));
  reportValidation(validateResult);
});


onBeforeUnmount(() => {
  emits("updateSaveStatus", {
    key: `ItemBubbleFlex`,
    value: true
  });
});

</script>
