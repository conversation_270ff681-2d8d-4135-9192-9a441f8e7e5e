<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
@font-face {
  font-family: lucidaConsole;
  src: url("../../../../../assets/fonts/LUCON.TTF");
}
.json-edit-area {
  white-space: nowrap;
  font-family: lucidaConsole, monospace;
}
</style>
<template>
  <div>
    <q-input textarea
      ref="textarea"
      class="mx-1 mt-4"
      placeholder="テキストを入力"
      single-line
      outlined
      rows="4"
      hide-details="auto"
      v-model="text"
    ></q-input>
    <q-dialog v-model="show" class="emojiModal" width="600px" height="540px">
      <div class="emojiSelectHeader px-4 py-3">
        絵文字
        <q-icon @click="show=false">mdi-close</q-icon>
      </div>
      <q-card class="emojiHistory d-flex flex-wrap pa-3" flat>
        <div v-for="(emojiHistories,key,index) in emojiHistories" :key="index" style="position-relative">
          <button type="button" class="btn border-0 bg-transparent p-0 avatar avatar-xs m-2" @click="insertImage(emojiHistories.path)">
            <q-img
              :src="baseUrl + emojiHistories.path"
            ></q-img>
          </button>
        </div>
      </q-card>
      <q-card class="emojiSelectInner d-flex flex-wrap" flat>
        <div v-for="(emojiPaths,key,index) in emojiPaths" :key="index" style="position-relative">
          <button type="button" class="btn border-0 bg-transparent p-0 avatar avatar-xs m-2" @click="insertImage(emojiPaths.path)">
            <q-img
              :src="baseUrl + emojiPaths.path"
            ></q-img>
          </button>
        </div>
      </q-card>
    </q-dialog>
    <div class="d-flex mx-1">
      <q-btn color="primary" outlined elevation="0" @click="open" class="my-4 cancel-btn"> 絵文字 </q-btn>
      <div class="blue-grey--text my-4 mx-2 flex">
        <span style="font-size: 13px;" class="pr-5 d-inline-block">&lt;&lt;$プロダクトID_絵文字ID
  <div>
    <v-textarea
      ref="textarea"
      class="mx-1 mt-4"
      placeholder="テキストを入力"
      single-line
      outlined
      rows="4"
      hide-details="auto"
      v-model="text"
    ></v-textarea>
    <v-dialog v-model="show" class="emojiModal" width="600px" height="540px">
      <div class="emojiSelectHeader px-4 py-3">
        絵文字
        <v-icon @click="show=false">mdi-close</v-icon>
      </div>
      <v-card class="emojiHistory d-flex flex-wrap pa-3" flat>
        <div v-for="(emojiHistories,key,index) in emojiHistories" :key="index" style="position-relative">
          <button type="button" class="btn border-0 bg-transparent p-0 avatar avatar-xs m-2" @click="insertImage(emojiHistories.path)">
            <v-img
              :src="baseUrl + emojiHistories.path"
            ></v-img>
          </button>
        </div>
      </v-card>
      <v-card class="emojiSelectInner d-flex flex-wrap" flat>
        <div v-for="(emojiPaths,key,index) in emojiPaths" :key="index" style="position-relative">
          <button type="button" class="btn border-0 bg-transparent p-0 avatar avatar-xs m-2" @click="insertImage(emojiPaths.path)">
            <v-img
              :src="baseUrl + emojiPaths.path"
            ></v-img>
          </button>
        </div>
      </v-card>
    </v-dialog>
    <div class="d-flex mx-1">
      <v-btn color="primary" outlined elevation="0" @click="open" class="my-4 cancel-btn"> 絵文字 </v-btn>
      <div class="blue-grey--text my-4 mx-2 flex">
        <span style="font-size: 13px;" class="pr-5 d-inline-block">&lt;&lt;$プロダクトID_絵文字ID$&gt;&gt;の文字列は絵文字埋め込み文字で、送信時に選択した絵文字に置き換えられます。</span>
        <span style="font-size: 13px;" class="pr-5 d-block">
          実際の送信内容はプレビューを確認してください。なお、プロダクトID、絵文字IDについては
          <a href="https://developers.line.biz/ja/docs/messaging-api/emoji-list/" target="_blank">こちら</a>
          を参照してください。
        </span>
        <span
          v-if="hasEmoji"
          style="font-size: 13px;"
          class="warning--text pr-5 d-inline-block">
            テキストメッセージ内に絵文字が埋め込まれています。プレビューより、絵文字の表示を確認してください。
        </span>
        <span
          v-if="hasInvalidEmoji || invalidEmojiTagPair"
          style="font-size: 13px;"
          class="warning--text pr-5 d-inline-block">
            絵文字埋め込み文字に誤りがある可能性があります。プレビューより、絵文字の表示を確認してください。
        </span>
        <span style="font-size: 13px;" class="error--text pr-5" :class="lengthErrorFlag ? 'd-inline-block' : 'u-d-none'">{{ overNumberOfCharactersCheck() }}</span>
      </div>
      <div class="blue-grey--text my-1" style="white-space: nowrap;">
        <span :class="lengthErrorFlag ? 'error--text': 'blue-grey--text'">{{ currentTextLength }} </span>/ {{ maxTextLength }}
      </div>
    </div>
  </div>
gt;&gt;の文字列は絵文字埋め込み文字で、送信時に選択した絵文字に置き換えられます。</span>
        <span style="font-size: 13px;" class="pr-5 d-block">
          実際の送信内容はプレビューを確認してください。なお、プロダクトID、絵文字IDについては
          <a href="https://developers.line.biz/ja/docs/messaging-api/emoji-list/" target="_blank">こちら</a>
          を参照してください。
        </span>
        <span
          v-if="hasEmoji"
          style="font-size: 13px;"
          class="warning--text pr-5 d-inline-block">
            テキストメッセージ内に絵文字が埋め込まれています。プレビューより、絵文字の表示を確認してください。
        </span>
        <span
          v-if="hasInvalidEmoji || invalidEmojiTagPair"
          style="font-size: 13px;"
          class="warning--text pr-5 d-inline-block">
            絵文字埋め込み文字に誤りがある可能性があります。プレビューより、絵文字の表示を確認してください。
        </span>
        <span style="font-size: 13px;" class="error--text pr-5" :class="lengthErrorFlag ? 'd-inline-block' : 'u-d-none'">{{ overNumberOfCharactersCheck() }}</span>
      </div>
      <div class="blue-grey--text my-1" style="white-space: nowrap;">
        <span :class="lengthErrorFlag ? 'error--text': 'blue-grey--text'">{{ currentTextLength }} </span>/ {{ maxTextLength }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, ref,  watch, onMounted } from 'vue';



// old imports
// 旧インポート
/*import { MAX_EMOJIS_LENGTH_IN_TEXTMESSAGE, MAX_TEXTMESASAGE_LENGTH } from "@/store/modules/segments/segments.constants";
import emojis from "@/assets/emojis.json";
import { generateShortUUID } from "@/utils/uuidUtils";
import { parseProductIdEmojiIdFromUrlOrFalse } from "@/store/modules/segments/segments-utils"*/

// emits 
const emits = defineEmits<{
  (event: 'onChangeTextMessage', payload: any): void;
}>();

// props
const props = defineProps({
  value: [Object, String],
  rules: Array as PropType<array>,
  index: Number as PropType<number>,
  passedByParentsPositionInfo: Array as PropType<array>,
  messages: Array as PropType<array>
});

// data
const text = ref<string>("");
const currentTextLength = ref<number>(0);
const errorText = ref<boolean>(false);
const show = ref<boolean>(false);
const baseUrl = import.meta.env.BASE_URL
const emojiPaths = ref<array<any>>(emojis);['paths']
const emojisNames = ref<array<any>>(emojis);['fileNames']
const positionInfo = ref<any>({});
const emojiHistories = ref<any>([]);
const maxTextLength = ref<number>(MAX_TEXTMESASAGE_LENGTH);
const maxEmojiLength = ref<number>(MAX_EMOJIS_LENGTH_IN_TEXTMESSAGE);
const lengthErrorFlag = ref<boolean>(false);
const hasEmoji = ref<boolean>(false);
const hasInvalidEmoji = ref<boolean>(false);
const invalidEmojiTagPair = ref<boolean>(false);

// methods
const overNumberOfCharactersCheck = (): string => {
      const emojisLength = positionInfo.value.emojis ? positionInfo.value.emojis.length : 0 ;
      if(currentTextLength.value > maxTextLength.value && emojisLength > maxEmojiLength.value){
        lengthErrorFlag.value = true;
        return "テキストは" + maxTextLength.value + "文字、絵文字は" + maxEmojiLength.value + "字以内で入力してください";
      }else if(currentTextLength.value > maxTextLength.value){
        lengthErrorFlag.value = true;
        return "テキストは" + maxTextLength.value + "字以内で入力してください";
      }else if(emojisLength > maxEmojiLength.value){
        lengthErrorFlag.value = true;
        return "LINE絵文字は" + maxEmojiLength.value + "字以内で入力してください";
      }else{
        lengthErrorFlag.value = false;
      }
    };
const setEmojiHistories = (): void => {
      if (!emojiHistories.value.length && localStorage.getItem("emojiHistoriesLS")) {
        emojiHistories.value = JSON.parse(localStorage.getItem("emojiHistoriesLS"));
      }
    };
const restoreEmojiText = (): void => {
      if (passedByParentsPositionInfo && passedByParentsPositionInfo[index]){
        let text = passedByParentsPositionInfo[index].text;
        positionInfo.value = passedByParentsPositionInfo[index];

        let shift = 0;
        positionInfo.value.emojis.forEach(({ index, productId, emojiId }, emojiIndex) => {
          const emojiTag = `<<$${productId}_${emojiId}$>>`;

          text.value = text.value.substr(0, index + shift) + emojiTag + text.value.substr(index + shift + 1, text.value.length + shift);

          // $をemojiTagに置換したのでemojiTagの長さ分shiftして次の$の位置を合わせる
          shift += emojiTag.length - 1;
        });
        text.value = text.value;
      }
    };
const onChangeTextMessage = (): void => {
      const emulatedText = makePositionInfo();
      currentTextLength.value = emulatedText.length;

      emits("onChangeTextMessage", positionInfo.value, index);
    };
const open = (): void => {
      show.value = true;
    };
const insertImage = (path: string): void => {
      if(!emojiHistories.value.find((obj) => obj.path === path)){
        emojiHistories.value.unshift({"path":path});
        if(emojiHistories.value.length > 10){
          emojiHistories.value.pop();
        }
      }

      const emojiTag = pathToEmojiTag(path);
      insertText(emojiTag);
    };
const pathToEmojiTag = (path: string): string => {
      const emojiTagValue = path.split("/").pop().split(".").shift();
      return `<<$${emojiTagValue}$>>`;
    };
const insertText = (text: string): void => {
      if (!text.value) {
        return;
      }

      if (typeof document.execCommand === "function") {
        // document.execCommandは確実にundo/redoできるが、MDNで非推奨なので存在するか確認してから使う
        $refs.textarea.$refs.input.focus();
        document.execCommand("insertText", false, text.value);
      } else {
        // fallback
        const input = $refs.textarea.$refs.input;
        const caretPos = input.selectionStart || 0;
        text.value = text.value.substr(0, caretPos) + text.value + text.value.substr(caretPos, text.value.length);
        $nextTick().then(() => {
          input.selectionStart = caretPos + text.value.length;
          input.selectionEnd = caretPos + text.value.length;
        });
      }
    };
const makePositionInfo = (): void => {
      const emojiPositions:any[] = [];
      const invalidEmojis:any[] = [];
      let text: any = text.value;
      let hasInvalidEmoji = false;

      let shift = 0;
      for (const allMatch of text.value.matchAll(ALL_EMOJI_REGEX)) {
        const matches = allMatch[0].match(EMOJI_EXTRACT_REGEX);
        if (!matches) {
          invalidEmojis.push(allMatch[0]);
          continue;
        }
        const emojiTag = matches[0];
        const emojiTagValue = matches[1];
        const { path } = emojis.paths.find(({ path }) => path === `emojis/${emojiTagValue}.png`) || {};
        if (!path) {
          invalidEmojis.push(allMatch[0]);
          continue;
        }
        emojiPositions.push({
          index: allMatch.index - shift,
          path: `/${path}`,
        });

        const matchTextLength = emojiTag.length - 1;
        text.value = text.value.substr(0, allMatch.index - shift) + "$" + text.value.substr(allMatch.index + matchTextLength + 1 - shift, text.value.length);
        shift += matchTextLength;
      }

      const tagStartCount = text.value.match(EMOJI_START_REGEX)?.length || 0;
      const tagEndCount = text.value.match(EMOJI_END_REGEX)?.length || 0;

      // タグの開始/終了の個数不一致
      invalidEmojiTagPair.value = tagStartCount !== tagEndCount;

      // 不明な絵文字が存在 または タグが入れ子
      hasInvalidEmoji.value = invalidEmojis.length > 0 || (tagStartCount > 0 && tagEndCount > 0);

      hasEmoji.value = emojiPositions.length > 0;
      positionInfo.value = {
        text,
        emojis: emojiPositions,
      };

      return text.value;
    };

// watch
watch(() => text.value, () => {
      onChangeTextMessage();
    });
watch(() => emojiHistories.value, (emojisArray) => {
      localStorage.setItem("emojiHistoriesLS", JSON.stringify(emojisArray));
    });

// hooks

onMounted(() => {
  setEmojiHistories();
  restoreEmojiText();
});

</script>
<style>
.tag-image{
  cursor: grab;
}
.tag-image::selection{
  background: transparent;
}
.emojiModal{
  overflow:hidden;
}
.v-dialog.v-dialog--active{
  background: #fff!important;
}
</style>
<style scoped>
.modal-body {
    position: relative;
    -webkit-box-flex: 1;
    flex: 1 1 auto;
    padding: 1rem;
}
.mh-100 {
    max-height: 100% !important;
}
.overflow-hidden {
    overflow: hidden !important;
}
.position-relative {
    position: relative !important;
}
.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 0.9375rem;
    line-height: 1.5;
    border-radius: 2px;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.border-0 {
    border: 0 !important;
}
.bg-transparent {
    background-color: transparent !important;
}
.p-0 {
    padding: 0 !important;
}
.avatar.avatar-xs {
    width: 2.25rem;
    height: 2.25rem;
}
.avatar {
    display: inline-block;
    position: relative;
    width: 4.5rem;
    height: 4.5rem;
    overflow: hidden;
    vertical-align: middle;
}
.m-2 {
    margin: 0.5rem !important;
}
.emojiSelectHeader{
  background-color: white;
  display: flex;
  justify-content: space-between;
  border-bottom:solid 1px #edeff0;
}

.emojiSelectInner{
  overflow-x : hidden;
  border-radius:0;
  border-top:1px solid #dee2e6!important;
}
.wrapper.placeholder {
    pointer-events: none;
    position: absolute;
    top: 0.5rem;
    left: 0.75rem;
    padding-left: 2px;
    color: #adb5bd;
}
.editor{
    resize: vertical;
    overflow-y: auto;
    line-height: 1.58;
    white-space: pre-wrap;
}
.form-control {
    display: block;
    width: 100%;
    height: calc(2.15625rem + 2px);
    padding: 0.375rem 0.75rem;
    font-size: 0.9375rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #cfd4da;
    border-radius: 2px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

</style>