<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div class="preview-container">
    <div class="line-header">
      <div class="line-toolbar">
        <q-row class="px-2">
          <q-col class="text-center">
            <div>プレビュー</div>
          </q-col>
        </q-row>
      </div>
    </div>
    <div class="content">
      <q-page-container class="talk-content">
        <template>
          <TalkRendering :preview="preview" :messages="messages" :positionInfo="positionInfo" ref="talkRendering"/>
        </template>
      </q-page-container>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType,  watch,  } from 'vue';



// old imports
// 旧インポート
/*import TalkRendering from "@/pages/admin/ReminderDelivery/TalkRendering/index.vue";*/


// props
const props = defineProps({
  messages: Array as PropType<array>,
  positionInfo: Array as PropType<array>,
  preview: Boolean as PropType<boolean>
});

// watch
watch(() => preview, (value) => {
  if (value) {
    $forceUpdate();
  }
});
</script>

<style lang="less">
.preview-container .content {
  max-width: 320px;
  width: 320px;
  height: 480px;
  overflow-y: auto;
  background: #666f86;
}

.line-header {
  background: white;
}

.line-toolbar {
  background-color: #353a3f;
  div {
    color: white;
  }
}

.talk-content {
  padding: 5px !important;
  background-color: #666f86;
}
</style>
