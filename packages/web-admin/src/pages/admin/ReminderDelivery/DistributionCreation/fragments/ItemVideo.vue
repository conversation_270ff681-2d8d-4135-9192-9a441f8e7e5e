<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.item-video-local-preview {
  max-width: 100%;
  max-height: 540px;
}
</style>

<template>
  <div>
    <input
      type="file"
      ref="videoInput"
      class="d-none"
      accept="video/mp4"
      @change="onChangeVideoInput($event)"
      @click="onClearTarget($event)"
    />
    <input
      type="file"
      ref="thumblInput"
      class="d-none"
      accept="image/png, image/jpeg"
      @change="onChangeThumbInput($event)"
      @click="onClearTarget($event)"
    />
    <q-page-container fluid center>
      
        <q-row>
          <q-col cols="6" align="center">
            <!-- 動画設定 -->
            <div v-if="localVideoModel">
              <div>
                <video :src="localVideoModel.getObjectURL()" controls class="item-video-local-preview"></video>
              </div>
              <q-btn
                color="error"
                class="my-3"
                elevation="0"
                @click="onDeleteVideo()"
                >動画を削除
              </q-btn>
            </div>
            <div v-else>
              <q-btn
                color="primary"
                class="my-3"
                elevation="0"
                @click="onUploadVideo()"
                >動画をアップロード
              </q-btn>
              <Alert v-if="localVideoInputError" class="my-3 body-2" color="red" type="error">
                {{ localVideoInputError }}
              </Alert>
            </div>
            <div class="text-left mb-2">
              <q-item-label class="caption">
                ファイル形式：MP4<br />
                ファイルサイズ：200MB以下
              </q-item-label>
            </div>
          </q-col>

          <!-- サムネイル設定 -->
          <q-col cols="6" align="center">
            <div v-if="localVideoThumbModel">
              <div>
                <q-img :src="localVideoThumbModel.getObjectURL()" class="item-video-local-preview"></q-img>
              </div>
              <q-btn
                color="error"
                class="my-3"
                elevation="0"
                @click="onDeleteThumb()"
                >プレビュー画像を削除
              </q-btn>
            </div>
            <div v-else>
              <q-btn
                color="primary"
                class="my-3"
                elevation="0"
                @click="onUploadThumb()"
                >プレビュー画像をアップロード
              </q-btn>
              <Alert v-if="localThumbInputError" class="my-3 body-2" color="red" type="error">
                {{ localThumbInputError }}
              </Alert>
            </div>
            <div class="text-left mb-2">
              <q-item-label class="caption" style="height: 100%">
                ファイル形式：JPG、JPEG、PNG<br />
                ファイルサイズ：1MB以下<br />
                動画と同じアスペクト比の画像を設定してください<br />
                ※ アスペクト比が異なると、動画の背面にプレビュー画像が表示されることがあります。
              </q-item-label>
            </div>
          </q-col>
        </q-row>
    </q-page-container>
  </div>
</template>

<script setup lang="ts">
import { PropType, ref,  onMounted } from 'vue';



// old imports
// 旧インポート
/*import { VideoModel, VideoThumbModel } from "@/model/SegmentDelivery/Items/ItemVideo"*/


// props
const props = defineProps({
  params: { type: Object as PropType<any>, required: true }
});

// data
const localVideoModel = ref<videomodel>(null);
const localVideoThumbModel = ref<videothumbmodel>(null);
const localVideoInputError = ref<string>('');
const localThumbInputError = ref<string>('');

// methods
const onUploadVideo = (): void => {
      $refs.videoInput.click();
    };
const onDeleteVideo = (): void => {
      localVideoModel.value = null;
      reportUpdateToParentComponent();
    };
const onUploadThumb = (): void => {
      $refs.thumblInput.click();
    };
const onDeleteThumb = (): void => {
      localVideoThumbModel.value = null;
      reportUpdateToParentComponent();
    };
const onClearTarget = (event: any): void => {
      event.target.value = ''
    };
const onChangeVideoInput = (event: any): void => {
      const file = event.target.files[0];
      try {
        localVideoModel.value = new VideoModel(file);
        localVideoInputError.value = '';
        reportUpdateToParentComponent();
      } catch(e) {
        localVideoInputError.value = e;
      }
    };
const onChangeThumbInput = (event: any): void => {
      const file = event.target.files[0];
      try {
        localVideoThumbModel.value = new VideoThumbModel(file);
        localThumbInputError.value = '';
        reportUpdateToParentComponent();
      } catch(e) {
        localThumbInputError.value = e;
      }
    };
const reportUpdateToParentComponent = (): void => {
      $emit("updateModelParams", {
          videoModel: localVideoModel.value,
          videoThumbModel: localVideoThumbModel.value
      });
    };

// hooks

onMounted(() => {
  localVideoModel.value = params.videoModel;
  localVideoThumbModel.value = params.videoThumbModel;
});

</script>
