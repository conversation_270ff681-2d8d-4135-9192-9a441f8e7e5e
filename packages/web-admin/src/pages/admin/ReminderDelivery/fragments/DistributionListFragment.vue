<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <div class="text-right ma-4 row">
      <q-btn-group tile dense mandatory color="primary" v-model="selectedCategoryToggleIndex">
        <q-btn min-width="80" v-for="(value, index) in categoryList" :key="index" @click="categoryClicked = true">
          {{ convertCategoryToJapanese(value) }}
        </q-btn>
      </q-btn-group>
      <q-space></q-space>
      <router-link :to="{ name: 'DistributionCreatePage' }" class="list-item-link">
        <q-btn color="primary">
          <q-icon left>mdi-email-plus-outline</q-icon>
          新規手動配信
        </q-btn>
      </router-link>
    </div>
    <!-- search box -->
    <q-btn text @click="showSearchBox = !showSearchBox">
      <q-icon left large color="primary">
        {{ showSearchBox ? "mdi-chevron-up" : "mdi-chevron-down" }}
      </q-icon>
      条件検索
    </q-btn>
    <v-expand-transition>
      <q-page-container v-if="showSearchBox" fluid>
        <q-form ref="form" v-model="validSearchForm">
          <q-row>
            <q-col cols="2">
              <q-item-label class="justify-end">配信名</q-item-label>
            </q-col>
            <q-col>
              <q-select use-input
                :items="initialDistributionListPerCategory.map((a) => a.name)"
                v-model="selectedName"
                outlined
                dense
                clearable
                hide-details
              >
              </q-select>
            </q-col>
          </q-row>
          <q-row>
            <q-col cols="2">
              <q-item-label class="justify-end">配信者</q-item-label>
            </q-col>
            <q-col>
              <q-select use-input
                :items="initialDistributionListPerCategory.map((a) => a.createdBy)"
                v-model="selectedCreatedBy"
                flat
                outlined
                dense
                clearable
                hide-details
                :disabled="!canIncludeCreatedBy"
                :filled="!canIncludeCreatedBy"
              >
              </q-select>
            </q-col>
          </q-row>
          <q-row>
            <q-col cols="2">
              <q-item-label class="justify-end">
                {{ selectedCategory === "ALL" ? "期間" : selectedCategory === "DRAFT" ? "作成期間" : "配信期間" }}
              </q-item-label>
            </q-col>
            <q-col cols="auto">
              <q-menu
                v-model="fromDateMenu"
                :close-on-content-click="true"
                transition="scale-transition"
                offset-y
                min-width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <q-input
                    v-model="fromDateText"
                    readonly
                    outlined
                    dense
                    prepend-inner-icon="mdi-calendar-outline"
                    placeholder="YYYY-MM-DD"
                    clearable
                    hide-details
                    v-bind="attrs"
                    v-on="on"
                  ></q-input>
                </template>
                <q-date v-model="selectedFromDate" no-title color="primary" :allowed-dates="allowedFromDates">
                </q-date>
              </q-menu>
            </q-col>
            <q-col cols="auto"><q-item-label>~</q-item-label></q-col>
            <q-col cols="auto">
              <q-menu
                v-model="toDateMenu"
                :close-on-content-click="true"
                transition="scale-transition"
                offset-y
                min-width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <q-input
                    v-model="toDateText"
                    readonly
                    outlined
                    dense
                    prepend-inner-icon="mdi-calendar-outline"
                    placeholder="YYYY-MM-DD"
                    clearable
                    hide-details
                    v-bind="attrs"
                    v-on="on"
                  ></q-input>
                </template>
                <q-date v-model="selectedToDate" no-title color="primary" :allowed-dates="allowedToDates">
                </q-date>
              </q-menu>
            </q-col>
          </q-row>
          <q-row>
            <q-col cols="2">
              <q-item-label class="justify-end">種別</q-item-label>
            </q-col>
            <q-col class="my-auto" cols="2">
              <q-checkbox
                v-model="selectedType"
                label="手動配信"
                value="MANUAL"
                hide-details
                :disabled="
                  selectedCategory === 'SCHEDULED' || selectedCategory === 'RECURRING' || selectedCategory === 'DRAFT'
                "
              ></q-checkbox>
            </q-col>
            <q-col
              v-if="
                selectedCategory !== 'SCHEDULED' && selectedCategory !== 'RECURRING' && selectedCategory !== 'DRAFT'
              "
              class="my-auto"
              cols="2"
            >
              <q-checkbox v-model="selectedType" label="外部配信" value="EXTERNAL" hide-details></q-checkbox>
            </q-col>
            <q-col
                v-if="
                selectedCategory !== 'SCHEDULED' && selectedCategory !== 'RECURRING' && selectedCategory !== 'DRAFT'
              "
                class="my-auto"
                cols="2"
            >
              <q-checkbox v-model="selectedType" label="ホームからの配信" value="HOME" hide-details></q-checkbox>
            </q-col>
          </q-row>
          <q-row v-if="selectedCategory !== 'DRAFT'">
            <q-col cols="2">
              <q-item-label class="justify-end">ステータス</q-item-label>
            </q-col>
            <q-col class="my-auto">
              <v-chip-group v-model="selectedState" mandatory active-class="primary--text">
                <q-chip v-for="(state, index) of stateList[selectedCategory]" :key="index" :value="state" active small>
                  {{ convertStateToJapanese(state) }}
                </q-chip>
              </v-chip-group>
            </q-col>
          </q-row>

          <q-row>
            <q-col align="center">
              <q-btn color="primary" @click="doSearch" :disabled="!validSearchForm" min-width="200"> 検索 </q-btn>
            </q-col>
          </q-row>
        </q-form>
      </q-page-container>
    </v-expand-transition>
    <!-- end of search box -->
    <q-separator></q-separator>
    <!-- table -->
    <q-page-container class="my-4" fluid>
      <q-row no-gutters>
        <q-col cols="auto">
          <span class="display-1">{{ items.length }}</span>
          <span>件</span>
        </q-col>
        <q-separator vertical class="mx-4"></q-separator>
        <q-btn color="primary" :loading="isFetchingDistributionList" @click="onFetchList">
          <q-icon left>mdi-reload</q-icon>
          データ更新
        </q-btn>
        <q-space></q-space>
        <q-btn @click="clearSearch" :disabled="!canClearSearch" color="gray"> 条件をクリアする </q-btn>
      </q-row>
    </q-page-container>
    <q-table
      class="table-cursor"
      :headers="headers"
      :items="items"
      single-select
      :loading="isFetchingDistributionList"
      :items-per-page="perOnPage"
      :footer-props="{ 'items-per-page-options': [10, 20, 50, 100, -1] }"
      :page.sync="currentPage"
    >
      <!-- templates for data in table (for each column) -->
      <template v-slot:[`item.id`]="{ item }">
        <div>
          <router-link v-if="item.id" :to="onDistributionDetail(item)" style="text-decoration: none">
            {{ item.id }}
          </router-link>
        </div>
      </template>
      <template v-slot:[`item.date`]="{ item }">
        {{ item.date ? formatUnixToYYYYMMDDHHmmss(item.date) : "ーー" }}
      </template>
      <template v-slot:[`item.type`]="{ item }">
        {{ convertTypeToJapanese(item.type) }}
      </template>
      <template v-slot:[`item.state`]="{ item }">
        <div :class="stateColor(item.state)">
          {{ convertStateToJapanese(item.state) }}
        </div>
      </template>
    </q-table>
    <!-- end of table -->
  </div>
</template>

<script setup lang="ts">
import {ref,  computed, watch, onBeforeMount } from 'vue';

import { useSegmentsStore } from '@stores/segmentsStore';
import { useAuthStore } from '@stores/authStore';

// old imports
// 旧インポート
/*import { FETCH_DISTRIBUTION_LIST } from "@/store/action-types";
import { UPDATE_DISTRIBUTION_LIST_FILTER } from "@/store/mutation-types";
import DatetimeFormatter from "@/mixins/DatetimeFormatter";*/


// data
const showSearchBox = ref<boolean>(true);
const categoryList = ref<any>(["ALL", "SCHEDULED", "RECURRING", "DRAFT"]);
const categoryClicked = ref<boolean>(false);
const selectedCategoryToggleIndex = ref<number>(0);
const selectedName = ref<any>(null);
const selectedCreatedBy = ref<any>(null);
const selectedFromDate = ref<any>(null);
const selectedToDate = ref<any>(null);
const selectedType = ref<any>([]);
const selectedState = ref<any>(null);
const stateList = ref<any>({
        ALL: ["ALL", "PROCESSING", "FINISHED", "ERROR", "IGNORED"],
        SCHEDULED: ["ALL", "PROCESSING", "FINISHED", "ERROR", "IGNORED"],
        RECURRING: ["ALL", "PROCESSING", "FINISHED", "ERROR", "IGNORED"], // ["ALL", "ERROR", "IGNORED"],
        DRAFT: [],
      });
const fromDateMenu = ref<boolean>(false);
const toDateMenu = ref<boolean>(false);
const canClearSearch = ref<boolean>(true);
const items = ref<any>([]);
const currentPage = ref<number>(1);
const perOnPage = ref<number>(10);

// methods
const fetchDistributionList = store.fetchDistributionList;
const updateDistributionListFilter = store.updateDistributionListFilter;
const doSearch = (): void => {
      canClearSearch.value = true;
      updateDistributionListFilter(searchFilters.value);
      currentPage.value = 1;
    };
const stateColor = (value: any): string => {
      switch (value) {
        case "FINISHED":
          return "primary--text";
        case "ERROR":
          return "red--text";
        case "TALK":
          return "black--text";
        default:
          return "orange--text";
      }
    };
const clearSearch = (): void => {
      selectedName.value = null;
      selectedCreatedBy.value = null;
      selectedFromDate.value = null;
      selectedToDate.value = null;
      selectedType.value =
        selectedCategory.value === "SCHEDULED" ||
        selectedCategory.value === "RECURRING" ||
        selectedCategory.value === "DRAFT"
          ? ["MANUAL"]
          : [];
      selectedState.value = null;

      updateDistributionListFilter(searchFilters.value);
      // canClearSearch.value = false;
      currentPage.value = 1;
    };
const onFetchList = async (): Promise<void> => {
      await fetchDistributionList();
    };
const onDistributionDetail = (item: any): any => {
      return {
        name: "DistributionDetail",
        params: {
          distributionId: item.id,
        },
      };
    };
const getTodayDate = (): string => {
      var today = new Date();
      return today.toISOString().substr(0, 10);
    };
const allowedFromDates = (value: any): boolean => {
      var allowed = true;
      if (selectedCategory.value === "DRAFT") {
        allowed = allowed && value <= getTodayDate();
      }
      if (selectedToDate.value) {
        allowed = allowed && value <= toDateText.value;
      }
      return allowed;
    };
const allowedToDates = (value: any): boolean => {
      var allowed = true;
      if (selectedCategory.value === "DRAFT") {
        allowed = allowed && value <= getTodayDate();
      }
      if (selectedFromDate.value) {
        allowed = allowed && fromDateText.value <= value;
      }
      return allowed;
    };
const convertStateToJapanese = (value: any): any => {
      if (value === "ALL") {
        return "すべて";
      }
      if (value === "PROCESSING") {
        return "処理中";
      }
      if (value === "FINISHED") {
        return "完了";
      }
      if (value === "ERROR") {
        return "エラー";
      }
      if (value === "IGNORED") {
        return "送信対象者無し";
      }
      if (value === "TALK") {
        return "ーー";
      }
      // TODO: this part might be bad for UI (confusing)
      if (value === "SCHEDULED") {
        return "予約";
      }
      if (value === "DRAFT") {
        return "下書き";
      }
      //
      return value;
    };
const convertCategoryToJapanese = (value: any): any => {
      if (value === "ALL") {
        return "すべて";
      }
      if (value === "RECURRING") {
        return "繰り返し予約";
      }
      if (value === "SCHEDULED") {
        return "予約";
      }
      if (value === "DRAFT") {
        return "下書き";
      }
      return value;
    };
const convertTypeToJapanese = (value: any): any => {
      if (value === "MANUAL") {
        return "手動配信";
      }
      if (value === "EXTERNAL") {
        return "外部配信";
      }
      if (value === "HOME") {
        return "ホームからの配信"
      }
      return value;
    };

// computed
const distributionList = computed(() => segmentsStore.distributionList);
const isFetchingDistributionList = computed(() => segmentsStore.isFetchingDistributionList);
const fetchDistributionListError = computed(() => segmentsStore.fetchDistributionListError);
const distributionListFilters = computed(() => segmentsStore.distributionListFilters);
const userStore = computed(() => authStore.user);
//filteredDistributionList: "filteredDistributionList");
//initialDistributionListPerCategory: "initialDistributionListPerCategory");
const headers = computed((): any => {
      return [
        { text: "配信ID", value: "id" },
        { text: "配信名", value: "name" },
        { text: "配信者", value: "createdBy" },
        { text: "日時", value: "date" },
        { text: "種別", value: "type" },
        { text: "ステータス", value: "state" },
        { text: "合計配信数", value: "deliveryRate" },
      ];
    });
const selectedCategory = computed({
  get(): any {
    return categoryList.value[selectedCategoryToggleIndex.value];
  },
  set(value: any): void {
    if (value && categoryList.value.indexOf(value) !== -1) {
      selectedCategoryToggleIndex.value = categoryList.value.indexOf(value);
    } else {
      selectedCategoryToggleIndex.value = 0;
    }
  },
});

const fromDateText = computed({
  get(): any {
    return selectedFromDate.value;
  },
  set(value: any): void {
    if (!value) {
      selectedFromDate.value = null;
    }
  },
});

const toDateText = computed({
  get(): any {
    return selectedToDate.value;
  },
  set(value: any): void {
    if (!value) {
      selectedToDate.value = null;
    }
  },
});

const searchFilters = computed((): any => {
  return {
    category: selectedCategory.value,
    name: selectedName.value,
    createdBy: selectedCreatedBy.value,
    dateFrom: selectedFromDate.value ? formatYYYYMMDDToUnix(selectedFromDate.value) : null,
    dateTo: selectedToDate.value ? addOneDayToUNIX(formatYYYYMMDDToUnix(selectedToDate.value)) : null,
    type: selectedType.value,
    state: selectedState.value,
  };
});

const validSearchForm = computed({
  get(): boolean {
    return true;
    /*
    if(selectedName.value || selectedCreatedBy.value || selectedFromDate.value || selectedToDate.value) {
      return true;
    }
    if(selectedType.value.length > 0) {
      return true;
    }
    if(selectedState.value && selectedState.value !== "ALL") {
      return true;
    }
    return false;
    */
  },
  set(value: any): void {},
});
const canIncludeCreatedBy = computed((): boolean => {
      return !(selectedType.value.length === 1 && selectedType.value[0] === "EXTERNAL");
    });

// watch
watch(() => distributionList.value, (value) => {
      items.value = value;
    });
watch(() => filteredDistributionList, (value) => {
      items.value = value;
    });
watch(() => selectedCategory, (newValue, oldValue) => {
      if (categoryClicked.value && oldValue !== newValue) {
        clearSearch();
        categoryClicked.value = false;
      }
      if (newValue === "SCHEDULED" || newValue === "RECURRING" || newValue === "DRAFT") {
        selectedType.value = ["MANUAL"];
      }
      updateDistributionListFilter(searchFilters.value);
    });
watch(() => selectedType.value, (newValue, oldValue) => {
      if (newValue && newValue.length === 1 && newValue[0] === "EXTERNAL") {
        selectedCreatedBy.value = "ーー";
      } else if (oldValue && oldValue.length === 1 && oldValue[0] === "EXTERNAL") {
        selectedCreatedBy.value = null;
      }
    });

// hooks

onBeforeMount(() => {
  // clearSearch();
  selectedCategory.value = distributionListFilters.value.category;
  selectedName.value = distributionListFilters.value.name;
  selectedCreatedBy.value = distributionListFilters.value.createdBy;
  selectedFromDate.value = distributionListFilters.value.dateFrom ? formatUnixToYYYYMMDD(distributionListFilters.value.dateFrom) : null;
  selectedToDate.value = distributionListFilters.value.dateTo ? formatUnixToYYYYMMDD(minusOneDayFromUNIX(distributionListFilters.value.dateTo)) : null;
  selectedType.value = distributionListFilters.value.type;
  selectedState.value = distributionListFilters.value.state;
  onFetchList();
});

</script>
