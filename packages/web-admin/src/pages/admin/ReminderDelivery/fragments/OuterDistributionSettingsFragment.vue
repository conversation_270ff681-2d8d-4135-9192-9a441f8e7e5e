<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
  .outer-distribution-tabs {
    margin-bottom: 1em;
  }
</style>
<template>
  <div>
    <q-page-container class="my-4" fluid>
      <q-tabs v-model="tab" grow class="outer-distribution-tabs">
        <v-tabs-slider></v-tabs-slider>
        <v-tab href="#mail-delivery-list">
          <span>メール転送配信</span>
        </v-tab>
        <v-tab href="#talk-delivery-list">
          <span>トーク配信</span>
        </v-tab>
      </q-tabs>

      <v-tabs-items v-model="tab">
        <!-- メール転送配信 -->
        <q-tab-panel value="mail-delivery-list">
          <q-row no-gutters>
            <q-space></q-space>
            <q-btn @click="handleReloadMailDeliveryList" class="mr-3" color="secondary">
              <q-icon left>
                {{ isFetchingSegmentDeliveries ? "mdi-cached mdi-spin" : "mdi-cached" }}
              </q-icon>
              データ更新
            </q-btn>
            <q-btn @click="openDataModal()" class="mr-3" color="primary">
              <q-icon left>mdi-plus</q-icon>
              新規作成
            </q-btn>
          </q-row>
          <q-table
            v-model="selected"
            :headers="mailHeaders"
            :items="filteredMailDistributions"
            item-key="id"
            :loading="isFetchingMailDeliveryList"
            :items-per-page="perOnPage"
            :footer-props="{ 'items-per-page-options': [10, 20, 50, 100] }"
            :page.sync="page"
          >
            <template v-slot:item.deliveryTitle="{ item }">
              <a @click="openDataModal(item)"> {{ item.deliveryTitle }} </a>
            </template>
            <template v-slot:item.distributionCondition="{ item }">
              <q-btn color="primary" :to="onConditionDetail(item)" small>設定 </q-btn>
            </template>
            <template v-slot:item.enabled="{ item }">
              <div>
                <span v-if="item.enabled" style="color: green;">有効</span>
                <span v-else style="color: red;">無効</span>
              </div>
            </template>
            <template v-slot:footer>
              <div></div>
            </template>
          </q-table>
        </q-tab-panel>
        <!-- トーク配信 -->
        <q-tab-panel value="talk-delivery-list">
          <q-row no-gutters>
            <q-space></q-space>
            <q-btn @click="handleReloadTalkDeliveryList" class="mr-3" color="secondary">
              <q-icon left>
                {{ isFetchingSegmentDeliveries ? "mdi-cached mdi-spin" : "mdi-cached" }}
              </q-icon>
              データ更新
            </q-btn>
            <q-btn @click="openTalkModal()" class="mr-3" color="primary"
              ><q-icon left>mdi-plus</q-icon>
              新規作成
            </q-btn>
          </q-row>
          <q-table
            v-model="selected"
            :headers="talkHeaders"
            :items="filteredTalkDistributions"
            item-key="id"
            :loading="isFetchingTalkDeliveryList"
            :items-per-page="perOnPage"
            :footer-props="{ 'items-per-page-options': [10, 20, 50, 100] }"
            :page.sync="page"
          >
            <template v-slot:item.deliveryTitle="{ item }">
              <a @click="openTalkModal(item)">{{ item.deliveryTitle }}</a
              >
            </template>
            <template v-slot:item.environment="{ item }">
              {{ getEnvironmentDisplay(item.environment) }}
            </template>
            <template v-slot:item.distributionCondition="{ item }">
              <q-btn color="primary" :to="onConditionDetail(item)" small>設定 </q-btn>
            </template>
            <template v-slot:item.enabled="{ item }">
              <div>
                <span v-if="item.enabled" style="color: green;">有効</span>
                <span v-else style="color: red;">無効</span>
              </div>
            </template>
            <template v-slot:footer>
              <div></div>
            </template>
          </q-table>
        </q-tab-panel>
      </v-tabs-items>
    </q-page-container>
    <DataModal :visible="showDataModal" @close="closeDataModal" :item="selectedItem" />
    <TalkModal :visible="showTalkModal" @close="closeTalkModal" :item="selectedItem" />
  </div>
</template>

<script setup lang="ts">
import {ref,  computed, watch, onBeforeMount } from 'vue';

import { useSegmentsStore } from '@stores/modules/segments';
import { useQuasar } from 'quasar';

// old imports
// 旧インポート
/*import { FETCH_MAIL_DELIVERY_LIST, FETCH_TALK_DELIVERY_LIST, FETCH_ALL_SEGMENT_DELIVERIES } from "@/store/action-types";
import { UPDATE_SEGMENT_PAGE } from "@/store/mutation-types";
import { TALK_DELIVERY_ENVIRONMENT_DISPLAY_TEXT } from "@/store/modules/segments/segments.constants"
import DataModal from "../components/DataModal/index.vue";
import TalkModal from "../components/TalkModal/index.vue";
import { emitSnackbar } from "@/utils/emitComponents";*/

// emits 
const emits = defineEmits<{
  (event: 'outerSegmentTabChange', payload: any): void;
}>();
const $q = useQuasar();

// data
const tab = ref<string>("mail-delivery-list");
const perOnPage = ref<number>(10);
const selected = ref<any>([]);
const mailHeaders = ref<any>([
        { text: "配信名", value: "deliveryTitle" },
        { text: "帳票", value: "survey" },
        { text: "配信条件", value: "distributionCondition.value" },
        { text: "有効/無効", value: "enabled" },
        { text: "次の配信日時", value: "nextDelivery" },
        { text: "前回の配信日時", value: "lastDelivery" },
      ]);
const talkHeaders = ref<any>([
        { text: "配信名", value: "deliveryTitle" },
        { text: "トーク名", value: "talkName" },
        { text: "環境", value: "environment" },
        { text: "配信条件", value: "distributionCondition.value" },
        { text: "有効/無効", value: "enabled" }
      ]);
const showDataModal = ref<boolean>(false);
const showTalkModal = ref<boolean>(false);
const selectedItem = ref<any>(null);
const distributionCondition = ref<any>(null);

// methods
const updatePage = store.updateSegmentPage;
const fetchMailDeliveryList = store.fetchMailDeliveryList;
const fetchTalkDeliveryList = store.fetchTalkDeliveryList;
const fetchSegmentDeliveries = store.fetchAllSegmentDeliveries;
const handleReloadMailDeliveryList = async (): Promise<void> => {
      await fetchMailDeliveryList().then(() => {
        emitSnackbar($snackbar, "メール配信データを更新しました。");
      })
    };
const handleReloadTalkDeliveryList = async (): Promise<void> => {
      await fetchTalkDeliveryList().then(() => {
        emitSnackbar($snackbar, "トーク配信データを更新しました。");
      })
    };
const getEnvironmentDisplay = (env: any): any => {
      return TALK_DELIVERY_ENVIRONMENT_DISPLAY_TEXT[env] ? TALK_DELIVERY_ENVIRONMENT_DISPLAY_TEXT[env] : "不明";
    };
const openDataModal = (item: any): void => {
      item ? (selectedItem.value = item) : null;
      showDataModal.value = true;
    };
const openTalkModal = (item: any): void => {
      item ? (selectedItem.value = item) : null;
      showTalkModal.value = true;
    };
const closeTalkModal = (): void => {
      selectedItem.value = null;
      showTalkModal.value = false;
    };
const closeDataModal = (): void => {
      selectedItem.value = null;
      showDataModal.value = false;
    };
const onConditionDetail = (item: any): any => {
      return {
        name: "ConditionDetail",
        params: {
          distributionConfigId: item.distributionConfigId,
        },
      };
    };

// computed
const mailDeliveryList = computed(() => segmentsStore.mailDeliveryList);
const talkDeliveryList = computed(() => segmentsStore.talkDeliveryList);
const isFetchingMailDeliveryList = computed(() => segmentsStore.isFetchingMailDeliveryList);
const isFetchingTalkDeliveryList = computed(() => segmentsStore.isFetchingTalkDeliveryList);
const fetchMailDeliveryListError = computed(() => segmentsStore.fetchMailDeliveryListError);
const fetchTalkDeliveryListError = computed(() => segmentsStore.fetchTalkDeliveryListError);
const segmentsList = computed(() => segmentsStore.segmentsList);
const isFetchingSegmentDeliveries = computed(() => segmentsStore.isFetchingSegmentDeliveries);
const fetchSegmentDeliveriesError = computed(() => segmentsStore.fetchSegmentDeliveriesError);
const currentPage = computed(() => segmentsStore.currentPage);
//filteredMailDistributions: "filteredMailDistributions");
//filteredTalkDistributions: "filteredTalkDistributions");
const page = computed({
  get(): number {
    return currentPage.value;
  },
  set(value: number): void {
    updatePage(value);
  },
});

// watch
watch(() => tab.value, async (value) => {
      switch(value) {
        case "mail-delivery-list":
          if (!mailDeliveryList.value || mailDeliveryList.value.length === 0) {
            await fetchMailDeliveryList();
          }
        break;
        case "talk-delivery-list":
          if (!talkDeliveryList.value || talkDeliveryList.value.length === 0) {
            await fetchTalkDeliveryList();
          }
        break;
      }
      emits("outerSegmentTabChange", value);
    });
watch(() => fetchMailDeliveryListError.value, (value) => {
      if (value) {
        emitSnackbar($snackbar, value, "error");
      }
    });
watch(() => fetchTalkDeliveryListError.value, (value) => {
      if (value) {
        emitSnackbar($snackbar, value, "error");
      }
    });

// hooks

onBeforeMount(() => {
  fetchMailDeliveryList();
});

</script>
