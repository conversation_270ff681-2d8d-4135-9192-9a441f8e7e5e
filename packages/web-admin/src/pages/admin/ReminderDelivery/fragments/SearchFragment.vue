<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-row>
      <q-col cols="12" md="4 pt-4">
        <q-select use-input
          v-model="selectedDeliveryTitle"
          :items="deliveryTitleOptions"
          label="配信名"
          outlined
          dense
          clearable
          hide-details
        >
        </q-select>
      </q-col>
      <q-col cols="12" md="5 pt-4">
        <q-select use-input
          v-if="searchStyle === 'mail-delivery-list'"
          v-model="selectedFormConfig"
          :items="surveyConfigOptions"
          label="帳票"
          outlined
          dense
          clearable
          hide-details
        ></q-select>
        <q-select use-input
          v-else
          v-model="selectedTalkName"
          :items="talkNameOptions"
          label="トーク名"
          outlined
          dense
          clearable
          hide-details
        ></q-select>
      </q-col>
      <q-col cols="12" md="3 pt-4">
        <q-select use-input
          v-model="selectedEnabled"
          :items="enabledTags"
          label="有効/無効"
          outlined
          dense
          clearable
          hide-details
        ></q-select>
      </q-col>
    </q-row>
  </div>
</template>

<script setup lang="ts">
import { PropType, ref,  computed, watch,  } from 'vue';

import { useSegmentsStore } from '@stores/segmentsStore';

// old imports
// 旧インポート
/*import { UPDATE_SEGMENT_FILTER } from "@/store/mutation-types";*/


// props
const props = defineProps({
  surveyConfigs: Array as PropType<array>,
  searchStyle: {
    type: String as PropType<string>,
    default: "mail-delivery-list"
  }
});

// data
const enabledTags = ref<any>([
        { text: "有効", value: true },
        { text: "無効", value: false },
      ]);

// methods
const updateSegmentFilter = store.updateSegmentFilter;
const updateData = (value: any): void => {
      updateSegmentFilter(Object.assign({}, filters.value, value));
    };

// computed
const mailDeliveryList = computed(() => segmentsStore.mailDeliveryList);
const talkDeliveryList = computed(() => segmentsStore.talkDeliveryList);
const filters = computed(() => segmentsStore.filters);
const selectedEnabled = computed({
  get(): any {
    return filters.value.enabled;
  },
  set(value: any): void {
    updateData({ enabled: value === null ? 0 : value });
  },
});

const selectedDeliveryTitle = computed({
  get(): any {
    return filters.value.deliveryTitle;
  },
  set(value: any): void {
    updateData({ deliveryTitle: value });
  },
});

const selectedFormConfig = computed({
  get(): any {
    return filters.value.surveyId;
  },
  set(value: any): void {
    updateData({ surveyId: value });
  },
});

const selectedTalkName = computed({
  get(): any {
    return filters.value.talkName;
  },
  set(value: any): void {
    updateData({ talkName: value });
  },
});
const deliveryTitleOptions = computed((): Array<any> => {
      let options = [];

      if (searchStyle === "mail-delivery-list") {
        options.push(
          ...mailDeliveryList.value.map((item) => {
            return {
              value: item.deliveryTitle,
              text: item.deliveryTitle,
            };
          })
        );
      } else {
        options.push(
          ...talkDeliveryList.value.map((item) => {
            return {
              value: item.deliveryTitle,
              text: item.deliveryTitle,
            };
          })
        );
      }

      return options;
    });
const surveyConfigOptions = computed((): Array<any> => {
      let options = [];

      options.push(
        ...surveyConfigs.map((obj) => {
          return {
            value: obj.surveyId,
            text: "[ " + obj.surveyTitle + " ] " + obj.surveyId,
          };
        })
      );

      return options;
    });
const talkNameOptions = computed((): Array<any> => {
      let options = [];

      options.push(
        ...talkDeliveryList.value.map((obj) => {
          return {
            value: obj.talkName,
            text: obj.talkName,
          };
        })
      );

      return options;
    });

// watch
watch(() => searchStyle, (value) => {
      updateData({ segmentSearchStyle: value });
    });
</script>
