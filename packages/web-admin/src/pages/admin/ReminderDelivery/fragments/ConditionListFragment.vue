<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-table :headers="headers" :items="surveySchema" :items-per-page="perOnPage" :loading="loading" :page="page">
    <template v-slot:item="{ item }">
      <tr v-if="isSelection(item)">
        <td>{{ item.itemKey }}</td>
        <td>{{ item.title }}</td>
        <td>{{ item.blockType }}</td>
        <td>
          <q-select use-input
            v-if="item.blockType === 'dropdown' || item.blockType === 'radio' || item.blockType == 'suggest'"
            v-model="selection[getIndex(item.itemKey)]"
            @change="onChangeConditionOption($event, item)"
            :items="getConditionOptions(item)"
            :placeholder="item.title"
            class="my-4 body-2"
            outlined
            dense
            clearable
            hide-details
          ></q-select>
          <q-select use-input
            v-if="item.blockType === 'checkboxes'"
            v-model="selection[getIndex(item.itemKey)]"
            @change="onChangeConditionOption($event, item)"
            :items="getConditionOptions(item)"
            :placeholder="item.title"
            class="my-4 body-2"
            multiple
            outlined
            dense
            clearable
            hide-details
          ></q-select>
        </td>
      </tr>
    </template>
  </q-table>
</template>

<script setup lang="ts">
import { PropType, ref,  computed, watch, onBeforeMount } from 'vue';

import { useSegmentsStore } from '@stores/segmentsStore';

// old imports
// 旧インポート
/*import { cloneDeep } from "lodash";*/

// emits 
const emits = defineEmits<{
  (event: 'onChangeCondition', payload: any): void;
}>();

// props
const props = defineProps({
  visible: Boolean as PropType<boolean>,
  isFormConfigChanged: Boolean as PropType<boolean>,
  conditions: Array as PropType<array>,
  loading: Boolean as PropType<boolean>,
  distItem: Object as PropType<any>
});

// data
const perOnPage = ref<number>(10);
const page = ref<number>(1);
const headers = ref<any>([
        { text: "アイテムキー", value: "itemKey", sortable: false },
        { text: "タイトル", value: "title", sortable: false },
        { text: "ブロック種別", value: "ype", sortable: false },
        { text: "配信条件", value: "options", sortable: false, width: "30em" },
      ]);
const selection = ref<any>([]);
const conditionSchema = ref<any>([]);
const initialSchema = ref<any>([]);

// methods
const isSelection = (item: any): boolean => {
      return item.blockType === "dropdown" || item.blockType === "radio" || item.blockType == "checkboxes" || item.blockType == "suggest";
    };
const getConditionOptions = (item: any): Array<any> => {
      let options = [];

      surveySchema.value.find((obj) => {
        if (obj.itemKey === item.itemKey) {
          options.push(
            ...Object.keys(obj.conditionValues).map((key) => {
              let value = obj.conditionValues[key];
              return {
                value: value,
                text: value,
              };
            })
          );
        }
      });

      return options;
    };
const setConditions = (): void => {
      conditionSchema.value = JSON.parse(JSON.stringify(surveySchema.value));
      if (conditions && conditions.length !== 0) {
        conditions.forEach((element, index) => {
          selection.value[index] = element.conditionValues;
        });
      } else {
        selection.value = [];
      }

      if (initialSchema.value.length > 0 && surveySchema.value.length > 0 && initialSchema.value[0].itemKey !== surveySchema.value[0].itemKey) {
        selection.value[0] = null;
      }
    };
const onChangeConditionOption = (value: any, item: any): void => {      
      conditionSchema.value.forEach((element, index) => {
        if (element.itemKey === item.itemKey) {
          element.conditionValues = value;
        } else {
          element.conditionValues = selection.value[index];
        }
      });

      emits("onChangeCondition", conditionSchema.value);
    };
const getIndex = (itemKey: any): any => {
      return surveySchema.value.findIndex((obj) => obj.itemKey === itemKey);
    };

// computed
const surveySchema = computed(() => segmentsStore.surveySchema);

// watch
watch(() => visible, (value) => {
      if (value) {
        setConditions();
      }
    });
watch(() => surveySchema.value, (value) => {
      if (value) {
        initialSchema.value = cloneDeep(value);
        setConditions();
      }
    });
watch(() => props.isFormConfigChanged, (new_value, old_value) => {
  if (new_value !== old_value) {
    setConditions();
  }
}, { deep: true });

// hooks

onBeforeMount(() => {});

</script>
