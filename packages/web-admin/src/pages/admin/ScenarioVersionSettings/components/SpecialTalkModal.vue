<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-dialog scrollable v-model="show">
    <q-card class="tw-w-full">
      <q-bar class="bg-primary tw-h-1"> </q-bar>
      <q-toolbar flat>
        <q-toolbar-title class="tw-font-semibold">
          テンプレートを利用
        </q-toolbar-title>
      </q-toolbar>
      <div v-for="(item, index) in specialTalks" :key="item.talkId + `.row`">
        <q-card-section
          class="tw-pb-0"
          :style="selected === index ? 'background: #eee' : ''"
          align="between"
        >
          <div class="tw-px-6 row">
            <div class="col">
              <label class="special-talk-label">{{ prefix[index] }} {{ item.displayName }}</label>
            </div>
            <div class="row tw-flex tw-content-end">
              <div v-if="inActiveMap(item.displayName)" class="tw-ml-2">
                <q-btn
                  v-if="canSetAsActive(item.displayName)"
                  color="primary"
                  outline
                  @click="
                    permissionHelper.hasActionPermission('click', 'backendRequest') ? toggleActiveState(item) : permissionHelper.showActionPermissionError()
                  "
                  :disable="permissionHelper.hasActionPermission('disableButton', 'ScenarioSettings_EnableTemplate_Click') || !templateExists(item.talkId)"
                >
                  有効
                </q-btn>
                <q-btn
                  v-else
                  color="warning"
                  outline
                  @click="
                    permissionHelper.hasActionPermission('click', 'backendRequest') ? toggleActiveState(item) : permissionHelper.showActionPermissionError()
                  "
                  :disable="permissionHelper.hasActionPermission('disableButton', 'ScenarioSettings_DeleteTemplate_Click') || !templateExists(item.talkId)"
                >
                  無効
                </q-btn>
              </div>
              <div v-if="!templateExists(item.talkId)" class="col tw-ml-2">
                <q-btn
                  color="primary"
                  :dark="!permissionHelper.hasActionPermission('disableButton', 'ScenarioSettings_CreateTemplate_Click')"
                  :disable="permissionHelper.hasActionPermission('disableButton', 'ScenarioSettings_CreateTemplate_Click')"
                  @click="
                    permissionHelper.hasActionPermission('click', 'backendRequest') ? createSpecialTalk(item.displayName) : permissionHelper.showActionPermissionError()"
                >
                  作成
                </q-btn>
              </div>
              <div v-else class="tw-ml-2">
                <q-btn
                  color="negative"
                  @click="
                    permissionHelper.hasActionPermission('click', 'backendRequest') ? deleteSpecialTalk(item) : permissionHelper.showActionPermissionError()"
                  :disable="permissionHelper.hasActionPermission('disableButton', 'ScenarioSettings_DeleteTemplate_Click') || !templateExists(item.talkId)"
                >
                  削除
                </q-btn>
              </div>
            </div>
          </div>
        </q-card-section>
        <q-separator class="tw-mt-3 tw-mx-6"/>
      </div>
      <q-card-actions class="tw-p-4 tw-flex tw-justify-end">
        <q-btn outline class="tw-px-6 tw-mr-2 text-blue-grey" @click="cancelCreate">
          閉じる
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { TEMPLATE_TALKS, SPECIAL_TALK_TYPES_MAPPING } from "@/stores/modules/scenarios/scenarios.constants";
import { useScenariosStore } from '@/stores/modules/scenarios';
import { usePermissionHelper } from '@/mixins/PermissionHelper';

// store
const scenariosStore = useScenariosStore();

const permissionHelper = usePermissionHelper();

// emits

const emits = defineEmits<{
  (event: 'close'): void;
  (event: 'onCreateTalks', payload: any): void;
  (event: 'onDeleteTalks', payload: any): void;
  (event: 'toggleTalkState', payload: any): void;
}>();


// props
const props = defineProps<{
	visible: boolean,
	existingTalks: any[],
	versionName: string,
	selected: number,
}>();

// data
const specialTalks = ref<any>(TEMPLATE_TALKS);
const prefix = ref<any>([
  "①",
  "②",
  "③",
  "④",
  "⑤",
  "⑥",
]);

// computed
const activeScenario = computed(() => scenariosStore.activeScenario);
const show = computed({
  get(): boolean {
    return props.visible;
  },
  set(value: boolean): void {
    if (!value) {
      emits("close");
    }
  },
});

// methods
const createSpecialTalk = (tlk: any): void => {
  // console.log(tlk); 
  emits("onCreateTalks", tlk);
  show.value = false;
};

const deleteSpecialTalk = (template: any): void => {
  emits("onDeleteTalks", template);
};

const cancelCreate = (): void => {
  emits("close");
  show.value = false;
};

const toggleActiveState = (tlk: any): void => {
  emits("toggleTalkState", tlk);
  show.value = false;
};

const inActiveMap = (tlk: any): boolean => {
  return Object.keys(SPECIAL_TALK_TYPES_MAPPING).includes(tlk);
};

const canSetAsActive = (tlk: any): boolean => {
  if ("versions" in activeScenario.value) {
    const versionData = activeScenario.value["versions"][props.versionName];
    const talkName = SPECIAL_TALK_TYPES_MAPPING[tlk];
    return "specialTalks" in versionData && talkName in versionData["specialTalks"]
      ? !versionData["specialTalks"][talkName]
      : false;
  } else {
    return false;
  }
};
const templateExists = (templateId: any): boolean => {
  return !!props.existingTalks.find(talk => talk.dataId === templateId);
};
</script>

<style scoped>
.container {
  padding-bottom: 0 !important;
}
</style>
