<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.gomi-import-instruction-line {
  background: linear-gradient(#000, #000) no-repeat center/1px 100%;
}
.gomi-import-instruction-bubble {
  text-align: center;
}
</style>
<template>
  <q-dialog v-model="show">
    <q-card class="tw-w-full" style="max-width: 90%;">
      <q-bar class="bg-primary tw-h-1" />
      <q-toolbar>
        <q-toolbar-title v-if="!isLocationCSV" class="tw-font-semibold">ゴミ分別</q-toolbar-title>
        <q-toolbar-title v-else class="tw-font-semibold"> 郵便番号のCSV登録</q-toolbar-title>
        <q-space></q-space>
        <q-btn icon="mdi-close" flat round @click="endImport" />
      </q-toolbar>
      <q-card-section v-if="!isLocationCSV && !existingTalks.includes('ゴミ分別')">
        <div class="text-grey-8">
          <p class="text-h5 text-primary tw-mb-4">まだゴミ分別についての情報が追加されていないようです。</p>
          <p class="tw-mb-4">以下の手順に治って、3ステップでゴミの情報をご登録いただけます。</p>
          <div class="row">
            <div class="gomi-import-instruction-bubble col-1">
              <q-icon size="md" color="secondary" name="mdi-numeric-1-circle"></q-icon>
            </div>
            <div>
              <p class="tw-font-semibold">ゴミ分別登録用のテンプレート・ファイルをダウンロード</p>
            </div>
          </div>
          <div class="row">
            <div class="gomi-import-instruction-line col-1"></div>
            <div class="col tw-my-4">
              <p class="tw-mb-4">CSVファイルにて、ゴミ分別登録用のテンプレートをご用意しております。</p>
              <p class="tw-mb-4">以下のボタンよりダウンロードいただき、ご記入を進めてください。</p>
              <q-btn :href="baseSampleFileUrl + sampleFileName" target="_blank" download>
                <q-icon name="mdi-file-delimited"></q-icon>テンプレートをダウンロードする
              </q-btn>
            </div>
          </div>

          <div class="row tw-py-2">
            <div class="gomi-import-instruction-bubble col-1">
              <q-icon size="md" color="secondary" name="mdi-numeric-2-circle"></q-icon>
            </div>
            <div class="col">
              <p class="tw-font-semibold">テンプレートにゴミ情報を記載する</p>
            </div>
          </div>
          <div class="row tw-py-4">
            <div class="gomi-import-instruction-line col-1"></div>
            <div class="col">
              <q-img
                  @click="imageDisplay(gomiSampleImg)"
                  :src="gomiSampleImg"
                  width="250px"
                >
                </q-img>
              <div class="q-my-md">
                <p>テンプレートはエクセルやNumbersで開くことができます。</p>
                <p class="tw-mt-4">記載の方法にしたがって、記入を行って下さい。</p>
              </div>
            </div>
          </div>
          <div class="row tw-py-4">
            <div class="gomi-import-instruction-bubble col-1">
              <q-icon size="md" color="secondary" name="mdi-numeric-3-circle" />
            </div>
            <div class="col">
              <p class="tw-font-semibold">記載したファイルをアップロードする</p>
            </div>
          </div>
          <div class="row">
            <div class="gomi-import-instruction-line col-1"></div>
            <div class="col-10">
              <p>テンプレートをこちらにアップロードすると、ゴミの種類の登録が完了します。</p>
              <q-file v-model="fileData" accept=".csv" class="tw-mt-4">
                <template v-slot:prepend>
                  <q-icon name="attach_file" />
                </template>
              </q-file>
            </div>
          </div>
          <div class="tw-flex tw-justify-center tw-mt-4">
            <span class="caption text-caption">ファイル形式：CSV</span>
          </div>
        </div>
      </q-card-section>
      <q-card-section v-else-if="isLocationCSV && !hasZipCodes">
        <div class="text-grey-8">
          <p class="text-h5 text-primary tw-mb-4">まだ郵便番号が追加されていないようです。</p>
          <p>以下の手順に治って、3ステップで郵便番号をご登録いただけます。</p>
          <div class="row tw-pt-4">
            <div class="gomi-import-instruction-bubble col-1">
              <q-icon size="md" color="secondary" name="mdi-numeric-1-circle"></q-icon>
            </div>
            <div class="col">
              <p class="tw-font-semibold">郵便番号登録用のテンプレート・ファイルをダウンロード</p>
            </div>
          </div>
          <div class="row tw-py-4">
            <div class="gomi-import-instruction-line col-1"></div>
            <div class="col">
              <p>CSVファイルにて、郵便番号登録用のテンプレートをご用意しております。</p>
              <p class="tw-my-4">以下のボタンよりダウンロードいただき、ご記入を進めてください。</p>
              <q-btn :href="baseSampleFileUrl + sampleZipCodeFileName" target="_blank" download>
                <q-icon name="mdi-file-delimited"></q-icon>テンプレートをダウンロードする
              </q-btn>
            </div>
          </div>

          <div class="row tw-pt-2">
            <div class="gomi-import-instruction-bubble col-1">
              <q-icon size="md" color="secondary" name="mdi-numeric-2-circle"></q-icon>
            </div>
            <div class="col">
              <p class="tw-font-semibold">テンプレートに郵便番号を記載する</p>
            </div>
          </div>
          <div class="row tw-py-4">
            <div class="gomi-import-instruction-line col-1"></div>
            <div class="col">
              <p>テンプレートはエクセルやNumbersで開くことができます。</p>
              <p class="tw-mt-4">各行は1つの郵便番号に対応します。また、ヘッダーはありません。</p>
            </div>
          </div>
          <div class="row tw-pt-2">
            <div class="gomi-import-instruction-bubble col-1">
              <q-icon size="md" color="secondary" name="mdi-numeric-3-circle"></q-icon>
            </div>
            <div class="col">
              <p class="tw-font-semibold">記載したファイルをアップロードする</p>
            </div>
          </div>
          <div class="row tw-py-4">
            <div class="gomi-import-instruction-line col-1"></div>
            <div class="col-10">
              <p>テンプレートをこちらにアップロードすると、郵便番号の登録が完了します。</p>
              <q-file v-model="fileData" accept=".csv" class="tw-mt-4">
                <template v-slot:prepend>
                  <q-icon name="attach_file" />
                </template>
              </q-file>
            </div>
          </div>
          <div class="tw-justify-center row">
            <span class="caption text-caption">ファイル形式：CSV</span>
          </div>
        </div>
      </q-card-section>
      <q-card-section v-else>
        <div class="row">
          <div class="col">
            <q-file v-model="fileData" accept=".csv" class="tw-mx-6"> 
              <template v-slot:prepend>
                <q-icon name="attach_file" />
              </template>
            </q-file>
          </div>
        </div>
      </q-card-section>
      <q-card-actions class="tw-p-4" align="center">
        <div class="row">
          <q-btn
            color="primary"
            class="tw-px-6 tw-mr-2 tw-w-32"
            @click="importFile"
            :loading="isFetchingScenarioDetail"
            :disable="!fileData"
          >
            <q-icon left name="mdi-import"></q-icon>
            登録する
          </q-btn>
          <q-btn class="tw-px-6 tw-mr-2 text-blue-grey tw-w-32" outline @click="endImport">
            <q-icon left name="mdi-cancel"></q-icon>
            キャンセル
          </q-btn>
        </div>
      </q-card-actions>
    </q-card>
    <ImageDisplayModal :visible="showImageDisplay" :imgSrc="imageSource" @close="showImageDisplay = false" />
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, } from 'vue';
import eventBus from '@utils/eventBus';
import { useQuasar } from 'quasar';
import ImageDisplayModal from "@/components/common/ImageDisplayModal.vue";
import { useScenariosStore } from '@/stores/modules/scenarios';
import gomiSampleImg from '@/assets/scenario-assets/gomiSample.jpg';

// quasar
const $q = useQuasar();

// store
const scenariosStore = useScenariosStore();

// emits
const emits = defineEmits<{
  (event: 'close'): void;
}>();

// props
const props = defineProps<{
	visible: boolean,
	scenarioName: string,
	versionName: string,
	versionList: any[],
	isLocationCSV: boolean,
	existingTalks: any[],
}>();

// data
const fileData = ref<any>(undefined);
const disableImport = ref<boolean>(true);
const scenarioVersion = ref<string>("");
const showImageDisplay = ref<boolean>(false);
const imageSource = ref<any>(null);
const sampleFileName = ref<string>("/gomibunbetsu-template.csv");
const sampleZipCodeFileName = ref<string>("/損傷報告郵便番号登録_テンプレート.csv");
const baseSampleFileUrl = ref<any>(window.location.origin);

// computed
const isFetchingScenarioDetail = computed(() => scenariosStore.isFetchingScenarioDetail);
const importingScenarioDataError = computed(() => scenariosStore.importingScenarioDataError);
const importFinishSuccess = computed(() => scenariosStore.importFinishSuccess);
const zipCodes = computed(() => scenariosStore.zipCodes);
const show = computed({
  get(): boolean {
    return props.visible;
  },
  set(value: boolean): void {
    if (!value) {
      emits("close");
    }
  },
});

const hasZipCodes = computed((): boolean => {
  return zipCodes.value.length > 0;
});

// watch
watch(
  () => scenarioVersion,
  () => {
    if (fileData.value && scenarioVersion && checkValidVersionName(scenarioVersion)) {
      disableImport.value = false;
    } else {
      disableImport.value = true;
    }
  }
);

watch(
  () => importFinishSuccess,
  (newVal) => {
    if (newVal) {
      eventBus.emit("onImportFinishSuccess");
      fileData.value = undefined;
    }
  }
);

// methods
const endImport = (): void => {
  show.value = false;
  fileData.value = undefined;
};

const importFile = async (): Promise<void> => {
  let payload = {
    fileData: fileData.value,
    scenarioId: props.scenarioName,
    versionId: props.versionName,
  };
  if (props.isLocationCSV) {
    await scenariosStore.uploadLocationCsvFile(payload);
  } else {
    await scenariosStore.uploadCsvFile(payload);
  }
  endImport();
  checkError(importingScenarioDataError.value, "保存しました");
};

const checkValidVersionName = (value: any): boolean => {
  return !props.versionList.includes(value); // valid === not in the version list
};

const imageDisplay = (src: any): void => {
  imageSource.value = src;
  showImageDisplay.value = true;
};

const checkError = (errorMessage: any, successMessage: any): void => {
  if (errorMessage) {
    const str = errorMessage.split('：');
    if (str.length > 1) {
      errorMessage = str[0] + '。' + japaneseMessege(str[1]);
    }
    $q.notify({
      message: errorMessage,
      type: "error",
    });
  } else {
    $q.notify({
      message: successMessage,
    });
  }
};

const japaneseMessege = (value: string) => {
  if (value.indexOf('field larger than field limit') !== -1) {
    return 'ファイルサイズが大きすぎます。';
  }
  else if (value.indexOf('new-line character seen in unquoted field - do you need to open the file with') !== -1) {
    return '改行文字が含まれています。';
  }
  else {
    return value;
  }
};
</script>
