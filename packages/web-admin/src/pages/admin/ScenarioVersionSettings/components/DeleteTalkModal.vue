<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-dialog scrollable persistent v-model="show" max-width="600">
    <q-card class="tw-w-full" style="overflow-x: hidden">
      <q-bar class="bg-negative tw-h-1"> </q-bar>
      <q-toolbar flat>
        <q-toolbar-title class="tw-font-semibold">
          選択項目を削除
        </q-toolbar-title>
      </q-toolbar>
      <q-card-section>
        <div v-if="!isDeletingScenarioVersion" class="tw-mx-2 row">
          <div class="text-break col-auto">
            <MultiLine v-if="payload.talkNames">
              {{ payload.talkNames.map((versionName: any) => `"${(versionName || "").trim()}"`).join(" と ") }} を削除します。
              削除したら元に戻すことはできません。
            </MultiLine>
          </div>
        </div>
        <div v-if="isDeletingScenarioVersion" class="row tw-flex tw-justify-center" align="center">
          <div md="1">
            <q-circular-progress :size="'50'" color="primary" indeterminate> </q-circular-progress>
          </div>
        </div>
      </q-card-section>
      <q-card-actions v-if="!isDeletingScenarioVersion" class="tw-p-4 tw-flex tw-justify-end">
        <q-btn class="text-blue-grey" outline @click="cancelDelete">
          キャンセル
        </q-btn>
        <q-btn
          color="negative"
          class="tw-ml-2"
          :disable="disableDelete"
          @click="permissionHelper.hasActionPermission('click', 'backendRequest') ? deleteItem() : permissionHelper.showActionPermissionError()"
        >
          削除
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, } from 'vue';
import { useQuasar } from 'quasar'
import MultiLine from "@/components/common/MultiLine.vue";
import {cloneDeep} from "lodash";
import { useScenariosStore } from '@/stores/modules/scenarios';
import { usePermissionHelper } from '@/mixins/PermissionHelper';

// quasar
const $q = useQuasar();

// store
const scenariosStore = useScenariosStore();

const permissionHelper = usePermissionHelper();

// props
const props = defineProps<{
	visible: boolean,
	payload: any,
}>();

// emit
const emit = defineEmits(['close', 'onDeleteFinishSuccess'])

// data
const disableDelete = ref<boolean>(false);

// computed
const isDeletingScenarioVersion = computed(() => scenariosStore.isDeletingScenarioVersion);
const deletingScenarioVersionError = computed(() => scenariosStore.deletingScenarioVersionError);
const show = computed({
  get(): boolean {
    return props.visible;
  },
  set(value: boolean): void {
    if (!value) {
      emit("close");
    }
  },
});

// watch
watch(
  () => deletingScenarioVersionError.value, 
  (newVal) => {
    if (newVal) {
      if (newVal instanceof String) {
        $q.notify({ message: newVal.toString(), type: "error" });
      } else {
        $q.notify({ message: newVal.message, type: "error" });
      }
    }
  }
);

// methods
const cancelDelete = (): void => {
  show.value = false;
};

const deleteItem = async (): Promise<void> => {
  const basicPayload = cloneDeep(props.payload);
  delete basicPayload.talkNames;
  const payloads = props.payload.talkNames.map((talkName: any) => ({
    ...basicPayload,
    talkName,
  }));
  for (const payload of payloads) {
    await scenariosStore.deleteTalk(payload);
  }
  show.value = false;
  emit("onDeleteFinishSuccess");
};
</script>
