<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <div v-if="!fetchScenariosError">
      <div v-if="isFetchingScenarios" class="tw-py-4 tw-flex tw-justify-center">
        <div class="row">
          <div class="col" md="1">
            <q-spinner :size="50" color="primary" indeterminate></q-spinner>
          </div>
        </div>
      </div>
      <TalkTable
        :key="route.params.versionId.toString()"
        :visible="!isFetchingScenarios"
        environment="sandbox"
        :searchCriteria="searchCriteria"
        @onTemplateClick="onTemplateClick"
        @onCreateClick="onCreateClick"
        @onDeleteTrigger="onDeleteTrigger"
      />
    </div>
    <div class="tw-pb-4 tw-flex tw-justify-center" v-if="fetchScenariosError">
      <ContentLoadError class="tw-mt-10" :error="fetchScenariosError" />
    </div>
    <NewTalkModal
      :visible="showCreateModal"
      :scenarioId="route.params.scenarioId.toString()"
      :versionId="route.params.versionId.toString()"
      @close="showCreateModal = false"
    />
    <DeleteTalkModal
      :visible="showDeleteModal"
      :payload="deletePayload"
      @close="showDeleteModal = false"
    />
    <SpecialTalkModal
      :visible="showSpecialTalkModal"
      :existingTalks="scenarioTalks"
      :versionName="route.params.versionId.toString()"
      :selected="selectedSpecialTalk"
      @onCreateTalks="createSpecialScenarios"
      @onDeleteTalks="deleteSpecialScenarios"
      @toggleTalkState="toggleSpecialScenarios"
      @close="showSpecialTalkModal = false"
    />
    <TrashCSVImportModal
      :visible="showImportTalkModal"
      :scenarioName="route.params.scenarioId.toString()"
      :versionName="route.params.versionId.toString()"
      :versionList="[]"
      :existingTalks="scenarioTalkNames"
      :isLocationCSV="false"
      @close="showImportTalkModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import {cloneDeep} from "lodash";
import {SPECIAL_TALK_TYPES_MAPPING} from "@/stores/modules/scenarios/scenarios.constants";
import { useScenariosStore } from "@/stores/modules/scenarios";
import { useQuasar } from "quasar";
import { useRoute } from "vue-router";

// components
import TalkTable from "@/pages/admin/ScenarioVersionSettings/components/TalkTable.vue";
import NewTalkModal from "@/pages/admin/ScenarioVersionSettings/components/NewTalkModal.vue";
import ContentLoadError from "@/components/common/ContentLoadError.vue";
import DeleteTalkModal from "@/pages/admin/ScenarioVersionSettings/components/DeleteTalkModal.vue";
import SpecialTalkModal from "@/pages/admin/ScenarioVersionSettings/components/SpecialTalkModal.vue";
import TrashCSVImportModal from "@/pages/admin/ScenarioVersionSettings/components/TrashCSVImportModal.vue";

// quasar
const $q = useQuasar();

// route
const route = useRoute();

// store
const scenariosStore = useScenariosStore();

const props = defineProps<{
  searchCriteria: any,
}>();

// data
const showScenarioVersion = ref(false);
const showSpecialTalkModal = ref(false);
const selectedSpecialTalk = ref(-1);
const showImportTalkModal = ref(false);
const showCreateModal = ref(false);
const showDeleteModal = ref(false);
const basicPayload = ref<any>({
  scenarioId: route.params.scenarioId,
  versionId: route.params.versionId,
  talkNames: '',
});
const deletePayload = ref({});
const resetSelection = ref(true);

// computed
const isFetchingScenarios = computed(() => scenariosStore.isFetchingScenarios);
const fetchScenariosError = computed(() => scenariosStore.fetchScenariosError);
const isFetchingScenarioDetail = computed(() => scenariosStore.isFetchingScenarioDetail);
const activeScenario = computed(() => scenariosStore.activeScenario);
const scenarioTalks = computed(() => scenariosStore.scenarioTalks);

const scenarioTalkNames = computed(() => {
  return scenarioTalks.value.map((talk) => talk.params ? talk.params.name || '' : '');
});

// watch
watch(
  () => fetchScenariosError.value,
  (newVal) => {
    if (newVal) {
      $q.notify({ message: newVal.message, type: "error" });
    }
  }
);

watch(
  () => isFetchingScenarioDetail.value,
  (newVal) => {
    if (newVal) $q.loading.show();
    else $q.loading.hide();
  }
)

// methods
const onCreateClick = (): void => {
  resetSelection.value = true;
  showCreateModal.value = true;
};

const onTemplateClick = (val: any): void => {
  showSpecialTalkModal.value = true;
  selectedSpecialTalk.value = val;
};

const onDeleteTrigger = (val: any): void => {
  deletePayload.value = {
    ...basicPayload.value,
    talkNames: val,
  };
  showDeleteModal.value = true;
  showScenarioVersion.value = false;
};

const createSpecialScenarios = (talk: any): void => {
  const payload = cloneDeep(basicPayload.value);
  payload.talkNames = talk;
  switch (talk) {
    case "ゴミ分別":
      showSpecialTalkModal.value = false;
      showImportTalkModal.value = true;
      break;
    default:
      scenariosStore.createSimpleSpecialScenario(payload);
      break;
  }
  setScenarioSpecialTalkData(talk, true);
};

const toggleSpecialScenarios = (talk: any): void => {
  const value = activeScenario.value.versions[route.params.versionId.toString()].specialTalks[SPECIAL_TALK_TYPES_MAPPING[talk.displayName]];
  setScenarioSpecialTalkData(talk.displayName, !value);
};

const deleteSpecialScenarios = (template: any): void => {
  const payload = cloneDeep(basicPayload.value);
  payload.talkNames = '';
  payload["specialTalkName"] = template.displayName;
  payload["talkId"] = template.talkId;
  $q.dialog({
    title: "テンプレート トーク削除確認",
    message: `"${template.displayName}" を削除します。\n削除したら元に戻すことはできません。`,
    // type: "error",
    ok: {
      label: 'OK',
    },
  }).onOk(async () => {
    scenariosStore.deleteSpecialScenario(payload);
    setScenarioSpecialTalkData(template.displayName, false);
    // TODO: isVisible がない - honda
    // if (template.displayName === "ゴミ分別") this.isVisible = false;
    showSpecialTalkModal.value = false;
  });
};

const setScenarioSpecialTalkData = (talk: any, valueToSet: any): void => {
  const talkName = SPECIAL_TALK_TYPES_MAPPING[talk];
  const scenarioDataToSave = cloneDeep(activeScenario.value);
  if (talkName != null) {
    if ("specialTalks" in activeScenario.value["versions"][route.params.versionId.toString()]) {
      scenarioDataToSave["versions"][route.params.versionId.toString()]["specialTalks"][talkName] = valueToSet;
    } else {
      const data = {} as any;
      data[talkName] = valueToSet;
      scenarioDataToSave["versions"][route.params.versionId.toString()]["specialTalks"] = data;
    }
    scenariosStore.saveActiveScenario(scenarioDataToSave);
  }
};

// mounted
onMounted(() => {
  // TODO: scenariosList がない - honda
  // if (this.scenariosList && this.scenariosList.length === 0) {
  //   this.$store.dispatch(FETCH_ALL_SCENARIOS);
  // }
});
</script>


