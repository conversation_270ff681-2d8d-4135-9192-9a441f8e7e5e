<template>
  <q-dialog v-model="show">
    <q-card style="width: 850px; max-width: 80vw">
      <q-bar class="bg-primary tw-h-1 tw-overflow-x-auto"></q-bar>
      <q-toolbar>
        <q-toolbar-title class="flex q-mt-md q-mb-md q-ml-md align-center items-center">
          <div class="text-weight-bold">通知情報（最新のみ）</div>
          <q-btn @click="fetchData" color="primary" flat>
            <q-icon
              :name="isFetchingCSVImportStatus ? 'mdi-cached' : 'mdi-cached'"
            />
          </q-btn>
        </q-toolbar-title>
      </q-toolbar>
      <q-container>
        <div v-if="isFetchingCSVImportStatus">
          <content-loading />
        </div>
        <div v-else class="q-ml-md">
          <q-card-section v-if="csvImportStatus">
            <div class="flex q-mb-lg">
              <div class="tw-w-[25%] text-body2">ファイル名：</div>
              <div class="col-9">
                {{
                  csvImportStatus.csvSource
                    ? getCSVFilename(csvImportStatus.csvSource)
                    : "ーー"
                }}
              </div>
            </div>

            <div class="flex q-mb-lg">
              <div class="tw-w-[25%] text-body2">ユーザ名：</div>
              <div class="col-9">
                {{
                  csvImportStatus.createdBy ? csvImportStatus.createdBy : "ーー"
                }}
              </div>
            </div>

            <div class="flex q-mb-lg">
              <div class="tw-w-[25%] text-body2">実行日：</div>
              <div class="col-9">
                {{
                  csvImportStatus.executedAt
                    ? formatDate(csvImportStatus.executedAt)
                    : "ーー"
                }}
              </div>
            </div>

            <div class="flex q-mb-lg">
              <div class="tw-w-[25%] text-body2">完了日：</div>
              <div class="col-9">
                {{
                  csvImportStatus.finishedAt
                    ? formatDate(csvImportStatus.finishedAt)
                    : "ーー"
                }}
              </div>
            </div>

            <div class="flex q-mb-lg">
              <div class="tw-w-[25%] text-body2">総件数：</div>
              <div class="col-9">
                {{
                  csvImportStatus.importSuccess +
                    csvImportStatus.importFailed !==
                  0
                    ? csvImportStatus.importSuccess +
                      csvImportStatus.importFailed
                    : 0
                }}
              </div>
            </div>

            <div class="flex q-mb-lg">
              <div class="tw-w-[25%] text-body2">失敗件数：</div>
              <div class="col-9">
                {{
                  csvImportStatus.importFailed
                    ? csvImportStatus.importFailed
                    : 0
                }}
              </div>
            </div>

            <div class="flex q-mb-lg">
              <div class="tw-w-[25%] text-body2">成功件数：</div>
              <div class="col-9">
                {{
                  csvImportStatus.importSuccess
                    ? csvImportStatus.importSuccess
                    : 0
                }}
              </div>
            </div>

            <div class="flex q-mb-lg">
              <div class="tw-w-[25%] text-body2">ステータス：</div>
              <div class="col-9">
                {{ csvImportStatus.status ? csvImportStatus.status : "ーー" }}
              </div>
            </div>

            <div class="flex q-mb-sm">
              <div class="tw-w-[25%] text-body2">エラー詳細：</div>
              <div class="tw-w-[70%] text-body2">
                <q-input
                  v-if="csvImportStatus.errors.length > 0"
                  class="full-width"
                  rows="7"
                  outlined
                  filled
                  readOnly
                  type="textarea"
                  v-model="csvErrorMessage"
                />
              </div>
            </div>
          </q-card-section>

          <q-banner
            v-else
            inline-actions
            class="text-red q-ma-md q-pa-md"
            :style="{ backgroundColor: 'rgba(254, 227, 226, 0.5)' }"
          >
            <q-icon name="warning" size="md" class="q-mr-lg" />
            CSV インポート処理はまだ開始しません。
          </q-banner>
        </div>
      </q-container>
      <q-card-actions class="q-pb-3 d-flex justify-end">
        <q-btn
          color="primary"
          class="q-mt-md q-mb-sm q-mr-sm"
          outline
          elevation="0"
          @click="show = false"
          >キャンセル</q-btn
        >
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, watchEffect } from "vue";
import { FETCH_CSV_IMPORT_STATUS } from "@/stores/actions-types";
import { SPOT_CSV_FOLDER_NAME } from "@/stores/modules/spot/spot.constants";
import ContentLoading from "@/components/common/ContentLoading.vue";
import dayjs from "dayjs";
import { emitSnackbar } from "@/utils/emitComponents";
import { useQuasar } from "quasar";
import { useRoute, useRouter } from "vue-router";

import { useSpotStore } from "@/stores/modules/spot";
const props = defineProps({
  visible: Boolean,
});
const emit = defineEmits(["close"]);
const route = useRoute();
const $q = useQuasar();

const show = ref(props.visible);
const isFetchingCSVImportStatus = computed(() => false);
const csvImportStatus = computed(() => spotStore.csvImportStatus);
const spotStore = useSpotStore();
const csvErrorMessage = computed(() => {
  let errorMessages: string[] = [];
  const dividerStr = `--------------------------------------------------------------------------------`;
  if (csvImportStatus.value.errors.length !== 0) {
    errorMessages.push(dividerStr);
    csvImportStatus.value.errors.forEach((obj) => {
      const processedError = obj.messages.map((e) => `- ${e}`).join("\r\n");
      const errorStr = `第${obj.row}行\r\n${processedError}`;
      errorMessages.push(errorStr, dividerStr);
    });
  }
  return errorMessages.join("\r\n");
});

const formatDate = (timestamp: number): string => {
  return dayjs(timestamp).format("YYYY/MM/DD HH:mm:ss");
};

// Watch for prop changes
watchEffect(() => {
  show.value = props.visible;
});

// Methods
const fetchData = async (): Promise<void> => {
  await spotStore.getCsvStatus($q, route.params.spotGroupId);
};

const getCSVFilename = (filename: string): string => {
  return filename.replace(SPOT_CSV_FOLDER_NAME + "/", "");
};

watchEffect(() => {
  if (!show.value) {
    emit("close");
  }
});
</script>
