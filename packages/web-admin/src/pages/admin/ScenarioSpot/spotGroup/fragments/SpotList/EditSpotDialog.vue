<template>
  <q-dialog v-model="isOpen" persistent>
    <q-card style="width: 980px; max-width: 80vw; " class="tw-overflow-x-auto">
      <q-bar class="bg-primary tw-h-1 tw-overflow-x-auto"></q-bar>

      <q-toolbar>
        <q-toolbar-title>{{ spotId ? "スポット編集" : "スポット作成" }}</q-toolbar-title>
        <q-space />
        <q-chip v-if="spotId" square dense>ID: {{ spotId }}</q-chip>
      </q-toolbar>

      <q-card-section class="flex tw-gap-2">
        <q-row class="q-mb-md flex" style="width: 100%">
          <q-col  class="q-pa-sm tw-w-[45%]">
            <q-form ref="formRef" v-model="isValid">
              <q-col cols="6">
                <div v-for="(attribute, index) in spotGroupAttributes" :key="attribute.attributeName">
                  <div v-if="attribute">
                    <span class="text-body2">{{ attribute.attributeName }}</span>
                    <span v-if="attribute.required" class="text-red">*</span>

                    <q-input
                      v-model="spotAttributes[index].value"
                      :rules="[() => requiredRule(spotAttributes[index]?.value, attribute.attributeName, attribute.required)]"
                      outlined
                      dense
                      filled
                      :placeholder="attribute.attributeName"
                    />
                  </div>
                </div>
              </q-col>

              <div class="q-mb-md" v-if="urlAttribute">
                <span class="text-body2">{{urlAttribute}}</span>
                 <q-input
                    v-model="urlAttributeSpot"
                    outlined
                    dense
                    placeholder="URL"
                    :rules="[validateUrl]"
                  />
              </div>

              <q-row class="flex tw-gap-4">
                <q-col class="q-pa-none tw-w-[47%]">
                  <span class="text-body2">緯度</span>
                  <span class="q-ml-xs text-red">*</span>
                  <q-input
                    v-model="latitude"
                    :rules="[validateLatitude]"
                    outlined
                    dense
                    type="number"
                    step="any"
                    placeholder="例：33.58844596786244"
                    autocomplete="off"
                  />
                </q-col>

                <q-col class="q-pa-none tw-w-[47%]">
                  <span class="text-body2">経度</span>
                  <span class="q-ml-xs text-red">*</span>
                  <q-input
                    v-model="longitude"
                    :rules="[validateLongitude]"
                    outlined
                    type="number"
                    step="any"
                    dense
                    placeholder="例：130.4190615980475"
                    autocomplete="off"
                  />
                </q-col>
              </q-row>

              <div class="q-mb-md">
                <span class="text-body2">タグ</span>
                <q-select
                  v-model="selectedTags"
                  outlined
                  dense
                  multiple
                  use-chips
                  :options="tagOptions"
                  emit-value
                  map-options
                />
              </div>

              <div class="q-my-md">
                <q-btn color="primary" unelevated @click="uploadImage">画像をアップロード</q-btn>

                <q-banner v-if="uploadError" class="q-mt-md text-white bg-red">
                  {{ uploadError }}
                </q-banner>

                <q-banner class="q-mt-md">
                  ファイル形式：JPG、JPEG、PNG<br />
                  ファイルサイズ：1MB以下 (LINEタイムライン上でプレビュー表示される画像は、システム内部で圧縮された画像になります)<br />
                  {{ imageHints }}
                </q-banner>

                <input
                  v-show="false"
                  ref="inputFileRef"
                  type="file"
                  class="d-none"
                  accept=".jpg,.jpeg,.png"
                  @change="handleImageUpload"
                />
              </div>
            </q-form>
          </q-col>

          <q-col class="q-pa-none tw-w-[55%]" v-if="messageTemplate">
            <MessagePreview class="tw-my-3" :messages="[messageTemplate]" />
          </q-col>
        </q-row>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn label="キャンセル" color="grey" flat v-close-popup @click="cancelSpotCreate" />
        <q-btn label="保存" color="primary" @click="saveSpot" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onBeforeMount } from "vue";
import { useSpotStore } from "@/stores/modules/spot";
import MessagePreview from "@/pages/admin/ScenarioSettingsDetail/components/MessagePreview.vue";
import { replaceValueInJson, copyTextToClipboard } from "@/utils/stringUtils";
import { cloneDeep } from 'lodash';
import {useQuasar} from 'quasar'
import { useRoute } from 'vue-router';

const props = defineProps({
  visible: Boolean,
  spotId: String,
});

const emit = defineEmits(["close", "save"]);
const spotStore = useSpotStore();
const isOpen = ref(props.visible);
const isValid = ref(false);
const formRef = ref(null);
const $q = useQuasar();
const selectedTags = computed({
  get() {
    return spotStore.spot.tags;
  },
  set(value) {
    spotStore.spot.tags = value;
  }
});

const uploadError = ref('')
const tagOptions = computed(() => {
  return spotStore.spotGroup.tags;
}); 
const fileData = ref<File | null>(null)
const previewImage = ref<string | null>(null);
const inputFileRef = ref();

const latitude = computed({
  get: () => spotStore.spot.latitude,
  set: (value) => {
    const parsed = parseFloat(value);
    spotStore.spot.latitude = value === '' || isNaN(parsed) ? null : parsed;
  },
});
const route = useRoute()
const previewSpot = computed(()=> spotStore.spotGroupPreview)
const messageTemplate = computed({
  get() {
    let jsonTemplate = cloneDeep(spotStore.selectedTemplate);
    if (spotStore.spotGroupPreview && spotStore.spotGroupPreview.params.image) {
      replaceValueInJson(jsonTemplate, "{画像}", spotStore.spotGroupPreview.params.image);
    }
   if (spotStore.spot.tags && spotStore.spot.tags.length > 0) {
     spotStore.spot.tags.forEach((tag, index) => {
      replaceValueInJson(jsonTemplate, `{タグ ${parseInt(index + 1)}}`, tag);
    });
   }
   if (spotStore.spot.attributes && spotStore.spot.attributes.length > 0) {
    spotStore.spot.attributes.forEach((attribute, index) => {
      let find = spotStore.spotAttributes.find(obj => obj.id === attribute.id);
      if (find) {
        let replacedValue = attribute.value ? attribute.value : `{${find.attributeName}}`;
        replaceValueInJson(jsonTemplate, `{属性 ${parseInt(index + 1)}}`, replacedValue);
      }
    });
   }
    return {
      dataType: "bubbleFlex",
      params: jsonTemplate
    };
  },
  set(newTemplate) {
    spotStore.spotGroupPreview.params.image = newTemplate;
  }
});

const longitude = computed({
  get: () => spotStore.spot.longitude,
  set: (value) => {
    const parsed = parseFloat(value);
    spotStore.spot.longitude = value === '' || isNaN(parsed) ? null : parsed;
  },
});

const urlAttribute = computed({
  get: () => spotStore.spotGroup.urlAttribute.attributeName,
  set: (value) => {
    spotStore.spotGroup.urlAttribute.attributeName = value;
  },
});
const urlAttributeSpot = computed({
  get: () => spotStore.spot.urlAttribute.value,
  set: (value) => {
    spotStore.spot.urlAttribute.value = value;
  },
});

const spotAttributes = computed(() => {
  return spotStore.spot.attributes;
});

const spotGroupAttributes = computed(() => {
  return spotStore.spotGroup.attributes;
});

const LATITUDE_REGEX = /^-?\d+(\.\d+)?$/;
const LONGITUDE_REGEX = /^-?\d+(\.\d+)?$/;

const validateLatitude = (value: number | string) => {
  if (!value) return "緯度は必須入力です。";

  const parsed = typeof value === "number" ? value : parseFloat(value);
  const minValue = 20;
  const maxValue = 46;

  if (!LATITUDE_REGEX.test(value.toString()) || parsed < minValue || parsed > maxValue) {
    return "不正な緯度が入力されました。";
  }
  return true;
};

const validateLongitude = (value: number | string) => {
  if (!value) return "経度は必須入力です。";

  const parsed = typeof value === "number" ? value : parseFloat(value);
  const minValue = 122;
  const maxValue = 154;

  if (!LONGITUDE_REGEX.test(value.toString()) || parsed < minValue || parsed > maxValue) {
    return "不正な経度が入力されました。";
  }
  return true;
};

const validateUrl = (value: string) => {
  try {
    const url = new URL(value);
    const protocol = url.protocol.toLowerCase();
    if (protocol !== 'http:' && protocol !== 'https:') {
      throw new Error();
    }
  } catch (error) {
    return `${urlAttribute.value}はリンク形式である必須です。`;
  }

  return true;
};


watch(
  () => spotStore.spot.attributes,
  (newAttributes) => {
    spotAttributes.value = newAttributes.map((attr) => ({
      ...attr,
      value: attr.value || "",
    }));
  },
  { immediate: true, deep: true }
);

const requiredRule = (value: string | undefined, name: string, required: boolean) => {
  if (!required) return true; 
  return value && value.trim() !== "" ? true : `${name}は必須入力です。`;
};

const handleImageUpload = (event: Event) => {
  if (event.target.files[0]) {
   
    let totalSizeMB: any = (event.target.files[0].size / Math.pow(1000, 2)).toFixed(2);

    if (totalSizeMB > 1) {
      uploadError.value = "1MB 以下のファイルを選んでください。";
    } else {
      uploadError.value = null;
      messageTemplate.value = URL.createObjectURL(event.target.files[0]);
      spotStore.spot.image = messageTemplate.value;
      fileData.value = event.target.files[0]
    }
  }
};

const uploadImage = (): void => {
  inputFileRef.value.click();
};

const resetValues = () => {
  longitude.value = ''
  latitude.value = ''
  previewImage.value = ""
  selectedTags.value = []
}
const saveSpot = async () => {
  const valid = await formRef.value.validate();
      if (valid) {
        if (props.spotId) {
            const spotUpdatePayload = {
              ...spotStore.spot,
              spotGroupId: route.params.spotGroupId,
              spotId: props.spotId,
            }
            if (fileData.value) {
              spotUpdatePayload.fileData = fileData.value 
            }
            await spotStore.updateSpotbyId(props.spotId, spotUpdatePayload, $q)
            await spotStore.getSpotLists(route.params.spotGroupId)
            await spotStore.getSpotGroupById(route.params.spotGroupId)
            await resetValues()
            await emit("close", false); 

        } else {  
              const payload = {
                ...spotStore.spot,
                spotGroupId: route.params.spotGroupId,
                fileData: fileData.value,
                tags: spotStore.spot.tags === null ? [] : spotStore.spot.tags
              }
              await spotStore.createSpot($q, payload)
              await spotStore.resetSpot()
              await spotStore.resetSpotGroup()
              await spotStore.getSpotGroupById(route.params.spotGroupId)
              await spotStore.getSpotLists(route.params.spotGroupId)
              await emit("close", false); 
              await resetValues()

        }
      }
};
const cancelSpotCreate = async() => {
  emit("close", false);
  await resetValues()
  await spotStore.resetSpot()
  await spotStore.resetSpotGroup()
  await spotStore.getSpotGroupById(route.params.spotGroupId)
}

watch(() => props.visible, (newVal) => {
  isOpen.value = newVal;
  if (newVal && props.spotId && route.params.spotGroupId) {
    spotStore.getSpotbyId(props.spotId, route.params.spotGroupId);
  }
});

watch(() => props.spotId, (newVal) => {
  if (newVal && newVal !== '') {
    spotStore.getSpotbyId(newVal, route.params.spotGroupId);
  }
});

</script>
