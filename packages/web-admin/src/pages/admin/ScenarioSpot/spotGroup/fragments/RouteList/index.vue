<template>
  <div>
    <q-toolbar>
      <q-toolbar-title class="font-weight-bold">  リスト一覧 </q-toolbar-title>
      <q-space />
      <q-btn
        class="q-mr-md q-px-sm"
        color="red"
        dense
        width="167"
        elevation="0"
        outline
        :disabled="selectedRoutes.length === 0"
        @click="handleBatchDelete"
      >
        <q-icon size="sm" left name="mdi-trash-can-outline" />
        選択項目を削除
      </q-btn>
      <q-btn
        class="q-mr-md q-px-sm"
        color="blue-grey"
        dense
        @click="fetchData"
        :loading="isFetchingRouteList"
      >
        <q-icon :name="isFetchingRouteList ? 'mdi-cached mdi-spin' : 'mdi-cached'" />
        データ更新
      </q-btn>
    </q-toolbar>

    <q-table
      :rows="routeList"
      :columns="headers"
      row-key="sortKey"
      selection="multiple"
      v-model:selected="selectedRoutes"
      :loading="isFetchingRouteList"
    >
      <template v-slot:body-cell-spots="props">
        <td>
          {{ props.row.spots.length }}
        </td>
      </template>
      <template v-slot:body-cell-updatedAt="props">
        <td>
          {{ dayjs(props.row.updatedAt).format("YYYY/MM/DD HH:mm") }}
        </td>
      </template>
      <template v-slot:body-cell-actions="props">
        <td class="text-center">
          <q-btn
            dense
            @click="handleShowEditSpotDialog(props.row)"
            class="q-px-sm"
            color="primary"
            size="md"
          >
            編集
          </q-btn>
        </td>
      </template>
    </q-table>

    <!-- Edit Route Dialog (リスト作成) -->
    <EditRouteDialog
      :isEdit="true"
      :routeId="routeId"
      :visible="showCreateRouteDialog"
      :selectedSpots="selectedSpots"
      @close="showCreateRouteDialog = false"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watchEffect } from "vue";
import { useRoute } from "vue-router";
import { useSpotStore } from "@/stores/modules/spot";
import { useQuasar } from "quasar";
import EditRouteDialog from "../RouteList/EditRouteDialog.vue";
import dayjs from "dayjs";
import { ROUTE_HEADERS } from "@/stores/modules/spot/spot.constants";

// Store and Quasar
const route = useRoute();
const spotStore = useSpotStore();
const $q = useQuasar();

// Dialog States
const showEditSpotDialog = ref(false);
const showCreateRouteDialog = ref(false); // ✅ Add state for route creation dialog
const showCSVImportDialog = ref(false);
const showUploadImageDialog = ref(false);
const showImportStatusDialog = ref(false);
const showPreviewView = ref(false);
const tempPreviewMessage = ref({ dataType: "bubbleFlex" });

// Selected Spots
const selectedRoutes = ref([]);
const routeList = computed(() => spotStore.routeList);
const headers = ROUTE_HEADERS;
const routeId = ref('');
const isFetchingRouteList = computed(() => spotStore.isFetchingRouteList);
const isFetchingRouteTemplates = computed(() => false);
const RouteDetailId = ref("");

// Fetch Data
const fetchData = async () => {
  await spotStore.listRoutes($q,route.params.spotGroupId);
};

// Show Dialogs
const handleShowEditSpotDialog = (row: any) => {
  routeId.value = row.sortKey
  showCreateRouteDialog.value = true;
};

const handleBatchDelete = async () => {
    
  const spotListIds = selectedRoutes.value.map((spotList: any) => spotList.sortKey);
  
  $q.dialog({
    title: "データの一括削除",
    message: "選択されたデータを削除します。よろしいですか？",
    ok: { label: "はい", color: "primary" },
    cancel: true,
  }).onOk(async () => {
    // const payload = 
    await spotStore.deleteRoutes($q,route.params.spotGroupId, spotListIds);
  });
};

const handleShowCSVImportDialog = () => (showCSVImportDialog.value = true);
const handleShowUploadImageDialog = () => (showUploadImageDialog.value = true);
const handleShowImportStatusDialog = async () => {
  await spotStore.getCsvStatus($q, route.params.spotGroupId);
  showImportStatusDialog.value = true;
};

watchEffect(() => {
  if (selectedRoutes.value.length > 0) {
    spotStore.setRouteSpots(selectedRoutes.value)
  }
});

const closeEditSpotDialog = () => {
  showEditSpotDialog.value = false
  selectedRoutes.value = [];
}
</script>
