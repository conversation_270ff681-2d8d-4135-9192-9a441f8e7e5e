<template>
  <q-dialog v-model="show" :persistent="isImportingSpotCSV">
    <q-card >
       <q-bar class="bg-primary tw-h-1 tw-overflow-x-auto"></q-bar>
       <q-toolbar>
        <q-toolbar-title class="flex q-mt-md align-center items-center">
          <div class="text-weight-bold">CSV インポート</div>
        </q-toolbar-title>
      </q-toolbar>
      <q-card-section>
        <div>
          <span class="text-subtitle1">1) スポット登録用のテンプレートをダウンロード</span>
          <div class="q-mx-none text-caption q-pa-md">
            CSVファイルにて、スポット登録用のテンプレートをご用意しております。
          </div>
          <q-btn :href="baseTemplateFileURL + templateFilename" target="_blank" download class="q-my-2 q-ml-md text-black">
                  <q-icon name="mdi-file-delimited"></q-icon>
                  テンプレートをダウンロードする
                </q-btn>
          <!-- <q-btn
            class="q-my-2 q-ml-md text-black"
            :href="baseTemplateFileURL + templateFilename"
            target="_blank"
            download
            color="grey-1"
            label="テンプレートをダウンロードする"
            icon="mdi-file-delimited"
          />  -->
        </div>
        <div class="q-mt-md">
          <span class="text-subtitle1">2) CSVファイルを修正</span>
           <div class="q-mx-none text-caption q-pa-md">
            属性 1〜属性 4の名称をスポットグループで設定した名称に合わせ、登録したいスポット情報を追記ください。
          </div>
        </div>
        <div class="q-mt-md">
          <span class="text-subtitle1">3) CSVファイルをアップロード</span>
           <div class="q-mx-none text-caption q-pa-md">

          <q-file
            v-model="fileData"
            :rules="csvRules"
            outlined
            label="CSV ファイル"
            :disable="isImportingSpotCSV"
            accept=".csv"
            clearable
          >
        <template v-slot:prepend>
          <q-icon name="attach_file" />
        </template>
      </q-file>
        </div>
        </div>
      </q-card-section>
      <q-card-actions align="right">
        <q-btn
          flat
          color="primary"
          label="キャンセル"
          @click="show = false"
          :disable="isImportingSpotCSV"
        />
        <q-btn
          color="primary"
          label="実行"
          :loading="isImportingSpotCSV"
          @click="handleCSVImport"
          :disable="!fileData"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, defineProps, defineEmits, watch, watchEffect } from 'vue';
import { emitSnackbar } from '@/utils/emitComponents';
import { useAuthStore } from "@stores/modules/auth.module";
import {useQuasar} from 'quasar'
import { useSpotStore } from "@/stores/modules/spot";
import { useRoute } from 'vue-router';
import { API } from "aws-amplify";

const spotStore = useSpotStore();
const $q = useQuasar();
const authStore = useAuthStore();
const user = authStore.user;
const route = useRoute()
// Props
const props = defineProps({
  visible: Boolean,
});

// Emits
const emit = defineEmits(['close']);

// Reactive variables
const show = ref(props.visible);
const fileData = ref<File | null>(null);

// Constants
// ref<string>("/スポットテンプレートファイル.csv");
const baseTemplateFileURL = ref<string>(window.location.origin);
const templateFilename = ref<string>("/スポットテンプレートファイル.csv");

// Validation rules for the CSV file
const csvRules = [
  (v: any) => {
    const file = v;
    if (file) {
      const totalSizeMB = (file.size / Math.pow(1024, 2)).toFixed(2);
      return totalSizeMB <= 10 || '10MB 以下のファイルを選んでください。';
    }
    return true;
  },
];

// Computed properties for store state
const isImportingSpotCSV = computed(() => false);

// Watch for visibility change and reset
watchEffect(() => {
  show.value = props.visible;
});
watchEffect(() => {
  if (!show.value) {
    emit("close");
  }
});
// Handle CSV import (empty function for now)
const handleCSVImport = async () => {
  const Payload = {
    createdBy: user.username,
    filePath:'/' + fileData.value.name,
    spotGroupId: route.params.spotGroupId
  }
  await spotStore.uploadCsv($q, fileData.value, Payload)
  await closeDialog()
  fileData.value = await null
};

// Ensure to close the dialog
const closeDialog = () => {
  show.value = false;
};
</script>
