<template>
  <div>
    <q-toolbar>
      <q-toolbar-title>
        スポットグループ一覧
      </q-toolbar-title>
      <q-space />
      <q-btn color="blue-grey" @click="fetchData" :disable="isFetchingSpotGroupList">
        <q-icon  :name="isLoading ? 'mdi-cached mdi-spin' : 'mdi-cached'" :spin="isFetchingSpotGroupList" />
        データ更新
      </q-btn>
      <q-btn class="q-ml-md" color="primary" @click="handleShowEditSpotGroupDialog">
        <q-icon name="add" />
        スポットグループ作成
      </q-btn>
    </q-toolbar>

    <q-table
      v-model:selected="selectedSpotGroups"
      :rows="spotGroupList"
      :columns="headers"
      row-key="typeId"
      :loading= "isLoading"
      flat
      :pagination="pagination"
      :rows-per-page-options="[5,10,15,0]"
    >
      <template v-slot:body-cell-updatedAt="props">
        <td> {{ dayjs(props.row.updatedAt).format('YYYY/MM/DD HH:mm:ss') }}</td>
      </template>
      <template v-slot:body-cell-postback="props">
        <td class="text-center">
          <q-btn color="primary" @click="handleShowPostbackDataDialog(props.row)">
            <q-icon name="format_list_bulleted" />
          </q-btn>
        </td>
      </template>
      <template v-slot:body-cell-actions="props">
        <td class="text-center">
          <q-btn color="primary" @click="goToSpotList(props.row)">
            編集
          </q-btn>
        </td>
      </template>
      <template v-slot:body-cell-setting="props">
        <td class="text-center">
          <q-btn outline color="primary" @click="handleShowEditSpotGroupDialog(props.row)">
            スポットグループ設定
          </q-btn>
        </td>
      </template>
      <template v-slot:body-cell-delete="props">
        <td class="text-center">
          <q-btn color="negative" @click="handleDeleteSpotGroup(props.row)">
            削除
          </q-btn>
        </td>
      </template>
    </q-table>

    <CreateEditSpotGroup :visible="showEditSpotGroupDialog" :isEdit="isEdit" @close="showEditSpotGroupDialog = false" />
    <!-- <PostbackDataDialog :visible="showPostbackDataDialog" @close="showPostbackDataDialog = false" /> -->
  </div>
</template>


<script setup lang="ts">
import { ref, onBeforeMount, computed } from 'vue';
import { useScenariosStore } from "@/stores/modules/scenarios";
import { useSpotStore } from "@/stores/modules/spot";
import CreateEditSpotGroup from '@/pages/admin/ScenarioSpot/components/CreateEditSpotGroup.vue';
// import PostbackDataDialog from './PostbackDataDialog.vue';
import { useQuasar } from 'quasar';
import { useRouter } from 'vue-router'

import dayjs from 'dayjs'
const selectedSpotGroups = ref([]);
const isFetchingSpotGroupList = ref(false);
const isFetchingSpotTemplates = ref(false);
const showEditSpotGroupDialog = ref(false);
const showPostbackDataDialog = ref(false);
const router = useRouter()
const $q = useQuasar()
const headers = ref([
  {
    name: "groupName",
    label: "スポットグループ名",
    field: "groupName",
    sortable: true,
    align: "left",
    style: "width: 30%",
  },
  {
    name: "updatedAt",
    label: "更新日",
    field: "updatedAt",
    sortable: true,
    align: "left",
    style: "width: 20%",
  },
  {
    name: "updatedBy",
    label: "更新者",
    field: "updatedBy",
    sortable: true,
    align: "left",
    style: "width: 10%",
  },
  {
    name: "actions",
    label: "",
    field: "actions",
    align: "center",
    style: "width: 5%",
  },
  {
    name: "setting",
    label: "",
    field: "setting",
    align: "center",
    style: "width: 15%",
  },
  {
    name: "delete",
    label: "",
    field: "delete",
    align: "center",
    style: "width: 5%",
  },
]);

const scenariosStore = useScenariosStore();
const spotStore = useSpotStore();

// Directly use the computed spotGroupList from the store
const spotGroupList = computed(() => spotStore.spotGroupList);
const isLoading = computed(() => spotStore.isFetchingSpotGroupList)
const isEdit = ref(false)
const pagination = ref({
  rowsPerPage: 10,
  sortBy: 'updatedAt', // 初期ソート対象のカラム名
  descending: true, // 初期ソート順 (false = 昇順, true = 降順)
});

const formatUnixToYYYYMMDHHmmss = (unixTime: number): string => {
  return dayjs(unixTime).format('YYYY/MM/DD')
};

const fetchData = async () => {
  await spotStore.getSpotGroups($q);
};

const handleShowEditSpotGroupDialog = (data? : any) => {
  if (data.sortKey) {
    isEdit.value = true
    spotStore.getSpotGroupById(data.sortKey)
  }
  showEditSpotGroupDialog.value = true
};

const handleShowPostbackDataDialog = (item: any) => {
  // Logic for showing the postback data dialog
};

const handleDeleteSpotGroup = async (item: any) => {
  
  // Logic for deleting a spot group
  $q.dialog({
      message: "このスポットグループを削除してもよろしいですか？\n削除されたデータが復旧できませんのでご注意ください。",
      color: "warning",
      cancel: true,
      ok: {
        label: "削除",
        color: "negative",
      },
    }).onOk(async () => {
      await spotStore.deleteSpotGroup($q, item.sortKey);
      await spotStore.getSpotGroups($q);
    })
  
};

const goToSpotList = (item: any) => {
  router.push(`/scenario-settings/${item.sortKey}`)
};

onBeforeMount(async () => {
  await spotStore.getSpotGroups($q);
});
</script>
