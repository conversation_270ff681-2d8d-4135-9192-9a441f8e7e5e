<template>
  <q-dialog v-model="show" persistent>
   <q-card style="width: 980px; max-width: 80vw; ">
      <q-bar class="bg-primary text-white tw-h-1" />
      <q-toolbar flat>
        <q-toolbar-title class="text-blue-grey-9 q-mr-md font-bold">
          {{ spotGroup.sortKey ? "スポットグループ設定" : "スポットグループ作成" }}
        </q-toolbar-title>
        <q-space />
        <q-item v-if="!isFetchingSpotGroupById && spotGroup.sortKey">
          ID: {{ spotGroup.sortKey }}
        </q-item>
      </q-toolbar>

      <q-card-section v-if="isFetchingSpotGroupById" class="flex justify-center">
        <q-spinner color="primary" size="xl"/>
      </q-card-section>
      <q-card-section v-else-if="spotGroup.sortKey">
        <q-card flat bordered class="overflow-auto" style="max-height: 640px;">
          <q-form ref="editForm" v-model="validEditForm">
            <q-tabs
          v-model="tab"
          dense
          class="text-grey"
          active-color="primary"
          indicator-color="primary"
          align="justify"
          narrow-indicator
        >
          <q-tab name="commonSettings" label="一般設定" />
          <q-tab name="locationSettings" label="位置情報設定" />
          <q-tab name="tagDisplaySettings" label="タグ設定" />
        </q-tabs>

        <q-separator />

        <q-tab-panels v-model="tab" animated>
          <q-tab-panel name="commonSettings">
           <CommonSettings @update:validForm="validForm = $event" /> 
          </q-tab-panel>

          <q-tab-panel name="locationSettings">
                  <LocationSettings @update:validForm="validForm = $event"/>
          </q-tab-panel>

          <q-tab-panel name="tagDisplaySettings">
                  <TagDisplaySettings/>
          </q-tab-panel>
        </q-tab-panels>
            
          </q-form>
        </q-card >
        <div class="flex justify-end q-mt-md">
          <q-btn  outline color="primary" class="q-mr-md" @click="closeDialog()">キャンセル</q-btn>
          <q-btn color="primary" :disable="!validForm" @click="handleUpdateSpotGroup">保存</q-btn>
        </div>
      </q-card-section>
      <q-card-section v-else-if="!spotGroup.sortKey">
        <q-card flat bordered class="overflow-auto" style="max-height: 640px;">
          <q-form ref="editForm" v-model="validEditForm">
            <q-stepper v-model="currentStep" flat color="primary" animated>
              <q-step 
                name="step1" 
                title="一般設定"
                icon="setting"
              >
                <q-card-section>
                <q-form> <CommonSettings @update:validForm="validForm = $event" /> </q-form>
                
                </q-card-section>
              </q-step>

              <q-step 
                name="step2" 
                title="位置情報設定"
                icon="place"
              >
                <q-card-section>
                  <LocationSettings @update:validForm="validForm = $event"/>
                </q-card-section>
              </q-step>

              <q-step 
                name="step3" 
                title="タグ設定"
                icon="label"
                class="custom-number-step"
              >
                <q-card-section>
                  <TagDisplaySettings/>
                </q-card-section>
                <div class="q-mt-md flex justify-between">
                 
                </div>
              </q-step>
            </q-stepper>
            
          </q-form>
        </q-card >
        <div class="flex justify-end q-mt-md" v-if="!spotGroup.sortKey">
          <q-btn  outline color="primary" class="q-mr-md" @click="closeDialog()">キャンセル</q-btn>
          <q-btn v-if="currentStep !== 'step1'" color="primary" class="q-mr-md" @click="goToPreviousStep">戻る</q-btn>
          <q-btn color="primary" v-if="currentStep !== 'step3'" :disable="!validForm" @click="goToNextStep">次へ</q-btn>
         <q-btn 
            color="primary" 
            v-if="currentStep === 'step3'" 
            :loading="isLoading"
            @click="handleUpdate"
          >
            <template v-slot:loading>
              <q-spinner size="sm" />
            </template>
            保存
          </q-btn>
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>


<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useQuasar } from 'quasar';
import CommonSettings from '../components/CommonSettings.vue'
import LocationSettings from '../components/LocationSettings.vue'
import TagDisplaySettings from '../components/TagDisplaySettings.vue'
import { useSpotStore } from "@/stores/modules/spot";

const spotStore = useSpotStore();
const $q = useQuasar();

const props = defineProps({
  visible: Boolean,
  isEdit: Boolean,
});
const validForm = ref(false); // Track form validity
const emits = defineEmits(['close']);

const show = ref(props.visible);
const currentStep = ref('step1');
const tab = ref('commonSettings')
const isFetchingSpotGroupById = computed(()=> {return spotStore.isFetchingSpotGroupById});
const validEditForm = ref(false);
const dialogTitle = computed(() => (props.visible ? "スポットグループ設定" : "スポットグループ作成"));
const spotGroup = computed(() => {return spotStore.spotGroup});
const isLoading = computed(() => spotStore.isCreatingSpotGroup)
watch(() => props.visible, (newValue) => {
  show.value = newValue;
});

watch(() => props.isEdit, (newValue) => {
  if (props.isEdit) {
    validForm.value = true;
  }
});

watch(() => show.value, (newValue) => {
  if (!newValue) emits('close');
});

watch(() => spotStore.isGroupUpdated, (newValue) => {
  if (newValue === true) {
     show.value = false;
     spotStore.resetSpotGroup();
  }
})

watch(() => spotStore.isGroupCreated, (newValue) => {
  if (newValue === true) {
     show.value = false;
     spotStore.resetSpotGroup();
  }
})
const handleUpdate = async() => {
    await spotStore.postSpotGroups($q)
    await spotStore.resetSpotGroup()
    await closeDialog()
};

const handleUpdateSpotGroup = async() => {
  await spotStore.updateSpotGroupById($q)
  await closeDialog()
};

const goToNextStep = () => {
  if (currentStep.value === 'step1' && validForm.value) currentStep.value = 'step2';
  else if (currentStep.value === 'step2') currentStep.value = 'step3';
};

const goToPreviousStep = () => {
  if (currentStep.value === 'step3') currentStep.value = 'step2';
  else if (currentStep.value === 'step2') currentStep.value = 'step1';
};
const closeDialog = async () => {
  show.value = await false;
  await spotStore.resetSpotGroup();
  currentStep.value = 'step1'
  validForm.value = await false
};
</script>

<style scoped lang="scss">
.font-bold {
  font-weight: bold;
}
.q-stepper--horizontal .q-stepper__step-inner { padding: 10px; }

</style>
