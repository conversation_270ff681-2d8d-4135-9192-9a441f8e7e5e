<template>
 <q-dialog v-model="props.show" >
  <q-card>
   <q-bar class="bg-primary tw-h-1" />
    <div class="q-pa-md flex">
      <q-icon name="mdi-information" class="text-negative q-mr-sm" />
      <span>{{ message }}</span>
    </div>
  </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

// Define the props to accept the dynamic message
const props = defineProps({
  show: Boolean
});

// Emit the "ok" event when deletion is confirmed
const emit = defineEmits(['ok']);
</script>
