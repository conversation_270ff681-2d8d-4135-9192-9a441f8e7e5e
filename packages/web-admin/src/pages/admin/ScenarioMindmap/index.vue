<style scoped>
#mindmap-root {
  display: flex;
  flex-direction: column;
  height: calc(
    100vh - 48px - 48px - 12px - 12px
  ); /* HACK: 100vh - navbar - copyright - container_Top - container_Bottom */
  width: 100vw;
  max-width: 100%;
}

#mindmap-wrapper {
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100vw;
  max-width: 100%;
  overflow-x: hidden;
}

.mindmap-inner {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

svg {
  cursor: move; /* fallback */
  cursor: grab;
}

svg:active {
  cursor: grabbing;
}

.editor-buttons {
  padding-left: 0;
  padding-right: 0;
  display: flex;
  align-items: center;
}

.editor-buttons.editor-buttons-left {
  padding-bottom: 0;
}

.editor-button {
  min-width: 0 !important;
  padding: 0 0.5em !important;
}

.editor-buttons > .editor-button,
.combined-buttons {
  margin-right: 12px;
}

.editor-buttons > .editor-button:last-child {
  margin-right: 0;
}

.combined-buttons {
  display: inline-block;
  overflow: hidden !important;
}

.combined-buttons.combined-buttons-split .editor-button {
  border-right: 1px solid #f2f2f2;
}

.combined-buttons.combined-buttons-split .editor-button:last-child {
  border-right: none;
}

.environment {
  display: inline;
  padding: 7px 14px;
  border-radius: 1em;
  margin-right: 12px;
}

.environment,
.environment * {
  color: #ffffff;
}

.environment.production {
  background-color: #74de74;
}

.environment.sandbox {
  background-color: #bdbdbd;
}

.header-menu-list .v-list-item {
  cursor: pointer;
  font-size: 14px;
}

.header-menu-list .v-list-item--disabled .header-menu-list-item {
  opacity: 0.6;
}

.header-menu-list .header-menu-list-item {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.header-menu-list .v-list-item span:last-child {
  margin-left: 1rem;
}

.header-menu-list .v-list-item:hover {
  background-color: rgba(0, 0, 0, 0.1);
}
</style>
<style>
body.lbd-web .router-container {
  min-height: unset !important;
}

#svgElem #node\.0,
#svgElem .node.bot {
  cursor: pointer;
}

g.node.delete-target rect {
  stroke: red;
  fill: #ffaaaa;
}

g.node.delete-target text {
  fill: red;
}


.drawer-shrink-toggle-icon {
  transform: rotate(0deg);
  transition: 0.25s all ease-in-out;
}

.drawer-shrink {
  transform: translateX(calc(100% - 38px)) !important;
}

.drawer-shrink #drawer-content {
  pointer-events: none;
  overflow: hidden;
  position: relative;
}

.drawer-shrink .drawer-shrink-toggle-icon {
  transform: rotate(180deg);
}

.editor-buttons .combined-buttons .editor-button[disabled] {
  background-color: rgba(0, 0, 0, 0.12) !important;
}
</style>
<template>
  <div id="mindmap-root">
    <div id="mindmap-wrapper">
      <SubAppBar dense :class="`${isMenuVertical ? 'tw-pt-12' : ''}`">
        <div class="tw-my-3">
          <div class="row tw-flex tw-justify-between">
            <div class="body-2 col-auto" style="display: inline-flex; align-items: center">
              <router-link class="tw-underline text-primary" :to="{name: 'ScenarioSettingsPage'}">シナリオ一覧</router-link>
              <span class="text-blue-grey tw-mx-1" style="opacity: 0.6">
              -
              </span>
                <router-link
                  class="text-primary tw-underline"
                  :to="{ name: 'ScenarioVersionSettingsPage', params: { env: route.params.environment, scenarioId: route.params.scenarioId, versionId: route.params.versionId }}"
                >
                  {{ displayVersionName }}
                </router-link>
                <span class="text-blue-grey tw-mx-1" style="opacity: 0.6">
              -
              </span>
                <span class="text-blue-grey">
                {{ route.params.talkName }}
              </span>
            </div>
            <q-space />
            <div class="editor-buttons col-auto col-md-12 tw-flex tw-justify-end ">
              <q-card class="combined-buttons" elevation="2" rounded>
                <q-btn
                    class="editor-button"
                    flat
                    :elevation="0"
                    :disable="!previousState"
                    @click="onClickUndoButton()"
                >
                  <q-icon name="mdi-undo"></q-icon>
                  <q-tooltip bottom>
                    <span>戻る</span>
                    (
                      <span v-if="isMacOS"> ⌘Z </span>
                      <span v-else> Ctrl+Z </span>
                    )
                  </q-tooltip>
                </q-btn>
                <q-btn
                    class="editor-button"
                    flat
                    :elevation="0"
                    :disable="!nextState"
                    @click="onClickRedoButton()"
                >
                  <q-icon name="mdi-redo"></q-icon>
                  <q-tooltip bottom>
                    <span>進む</span>
                    (
                      <span v-if="isMacOS"> ⌘Y </span>
                      <span v-else> Ctrl+Y </span>
                    )
                  </q-tooltip>
                </q-btn>
                <q-btn
                    class="editor-button"
                    flat
                    :elevation="0"
                    :disable="!editItem || (editItem && editItem.dataType === '__INITIAL__')"
                    @click="(v) => onCopy(v)"
                >
                  <q-icon name="mdi-content-copy"></q-icon>
                  <q-tooltip bottom>
                    <span>コピー</span>
                    (
                    <span v-if="isMacOS"> ⌘C </span>
                    <span v-else> Ctrl+C </span>
                    )
                  </q-tooltip>
                </q-btn>
                <q-btn
                    class="editor-button"
                    :disable="!copiedItems || !editItem || (editItem && editItem.dataType !== '__INITIAL__')"
                    flat
                    :elevation="0"
                    @click="(v) => onPaste(v)"
                >
                  <q-icon name="mdi-content-paste"></q-icon>
                  <q-tooltip bottom>
                    <span>ペースト</span>
                    (
                      <span v-if="isMacOS"> ⌘V </span>
                      <span v-else> Ctrl+V </span>
                    )
                  </q-tooltip>
                </q-btn>
              </q-card>
              <q-card class="combined-buttons" elevation="2" rounded>
                <q-btn
                    class="editor-button"
                    flat
                    :elevation="0"
                    @click="recenterMap(0)"
                >
                  <q-icon name="mdi-crosshairs-gps"></q-icon>
                  <q-tooltip bottom>
                    <span>ホーム</span>
                  </q-tooltip>
                </q-btn>
                <q-btn
                    class="editor-button"
                    flat
                    :elevation="0"
                    :disable="scale >= maxScale"
                    @click="onClickEnlargeButton()"
                >
                  <q-icon name="mdi-plus"></q-icon>
                  <q-tooltip bottom>
                    <span>ズームオン</span>
                  </q-tooltip>
                </q-btn>
                <q-btn
                  class="editor-button"
                  flat
                  :elevation="0"
                  :disable="scale === 100"
                  @click="onClickResetButton()"
                >
                  <q-icon name="mdi-magnify-remove-outline"></q-icon>
                  <q-tooltip bottom>
                    <span>ズームリセット</span>
                  </q-tooltip>
                </q-btn>
                <q-btn
                    class="editor-button"
                    flat
                    :elevation="0"
                    :disable="scale <= minScale"
                    @click="onClickShrinkButton()"
                >
                  <q-icon name="mdi-minus"></q-icon>
                  <q-tooltip bottom>
                    <span>ズームオフ</span>
                  </q-tooltip>
                </q-btn>
              </q-card>
              <div
                  class="tw-mr-4"
                  style="display: inline-flex; justify-content: center; margin: 0 0.5rem"
              >
                <span v-if="scale < 100" style="visibility: hidden">0</span>
                {{ scale }} %
              </div>
              <q-card class="combined-buttons combined-buttons-split" elevation="2" rounded>
                <q-btn
                    class="editor-button"
                    flat
                    :elevation="0"
                    :disable="permissionHelper.hasActionPermission('disableButton', 'ScenarioSettings_DeleteNode_Click') || activeItem == null || activeItem.mindmapId === 0"
                    @click="onClickDeleteButton()"
                >
                  <q-icon name="mdi-trash-can"></q-icon>
                  <q-tooltip bottom>
                    <span>削除</span>
                  </q-tooltip>
                </q-btn>
                <q-btn
                    class="editor-button"
                    flat
                    :elevation="0"
                    :disable="permissionHelper.hasActionPermission('disableButton', 'ScenarioSettings_SaveMap_Click') || editProperties"
                    @click="onClickSaveButton()"
                    :loading="isSavingTalkNodes"
                >
                  <q-icon name="mdi-content-save"></q-icon>
                  <q-tooltip bottom>
                    <span>保存</span>
                  </q-tooltip>
                </q-btn>
              </q-card>
              <div class="tw-flex tw-justify-end col-auto">
                <router-link :to="onScenarioSettings" class="button-link">
                  <q-btn class="text-black" >
                    リスト表示
                  </q-btn>
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </SubAppBar>
      <div class="mindmap-inner">
        <div
          ref="svgContainer"
          class="row"
          style="background-color: #f2f2f2; position: absolute; top: 0; bottom: 0; left: 0; right: 0"
          v-on:click="onClickSVGContainer"
        >
          <q-inner-loading :showing="isLoading" style="z-index: 1">
            <q-spinner
              color="primary"
              :size="50"
              :thickness="5"
              :speed="0.6"
            />
          </q-inner-loading>
          <svg
            id="svgElem"
            :height="svgHeight"
            baseProfile="full"
            style="background-color: #f2f2f2; position: absolute; outline: none; height: 100%"
            version="1.1"
            width="100%"
            xmlns="http://www.w3.org/2000/svg"
          >
            <filter id="linkhighlight" height="10000%" width="10000%" x="-5000%" y="-5000%">
              <feFlood flood-color="#e53935" flood-opacity="1" result="flood"></feFlood>
              <feComposite in="flood" in2="SourceGraphic" operator="in" result="mask"></feComposite>
              <feMorphology in="mask" operator="dilate" radius="2" result="dilated"></feMorphology>
              <feGaussianBlur in="dilated" result="blurred" stdDeviation="5"></feGaussianBlur>
              <feMerge>
                <feMergeNode in="blurred"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
              </feMerge>
            </filter>
            <filter id="botnode" height="10000%" width="10000%" x="-5000%" y="-5000%">
              <feGaussianBlur in="SourceAlpha" stdDeviation="8" />
              <feOffset dx="0" dy="0" result="offsetblur" />
              <feComponentTransfer>
                <feFuncA slope="0.05" type="linear" />
              </feComponentTransfer>
              <feMerge>
                <feMergeNode />
                <feMergeNode in="SourceGraphic" />
              </feMerge>
            </filter>
            <filter id="activenode" height="10000%" width="10000%" x="-5000%" y="-5000%">
              <feFlood flood-color="#F2F2F2" result="padding"></feFlood>
              <feMorphology in="SourceAlpha" operator="dilate" radius="2"></feMorphology>
              <feComposite in="padding" operator="in" result="padding-stroke"></feComposite>
              <feFlood flood-color="#2C9AD8" result="border"></feFlood>
              <feMorphology in="SourceAlpha" operator="dilate" radius="4"></feMorphology>
              <feComposite in="border" operator="in" result="border-stroke"></feComposite>
              <feMerge>
                <feMergeNode in="border-stroke"></feMergeNode>
                <feMergeNode in="padding-stroke"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
              </feMerge>
            </filter>
            <filter id="linknode" height="10000%" width="10000%" x="-5000%" y="-5000%">
              <feFlood flood-color="#F2F2F2" result="padding"></feFlood>
              <feMorphology in="SourceAlpha" operator="dilate" radius="2"></feMorphology>
              <feComposite in="padding" operator="in" result="padding-stroke"></feComposite>
              <feFlood flood-color="#2C9AD8" result="border"></feFlood>
              <feMorphology in="SourceAlpha" operator="dilate" radius="4"></feMorphology>
              <feComposite in="border" operator="in" result="border-stroke"></feComposite>
              <feMerge>
                <feMergeNode in="border-stroke"></feMergeNode>
                <feMergeNode in="padding-stroke"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
              </feMerge>
            </filter>
            <g id="mindMapSVG"></g>
          </svg>
        </div>
      </div>
    </div>
    <q-drawer
      v-model="showDrawer"
      overlay
      side="right"
      ref="property-drawer"
      :width="propertyTabWidth"
      style="z-index: 8"
    >
      <Property
        v-if="showPropertyDrawer"
        :model="editItem"
        :originModel="editItem"
        :scenarioId="scenarioId"
        :talkName="talkName"
        :versionId="versionId"
        :botMessages="scenarioMindmapMessages"
        @stopProperties="stopProperties"
        @updateScenarioMessage="updateScenarioMessage"
        @updateEditState="updateEditState"
        @localDataTypeUpdate="localDataTypeUpdate"
        @cancelDrawer="cancelDrawer"
      />
          <!-- :on-click-toggle-button="togglePropertyDrawer" -->
      <RootMessage
        v-if="showRootMessageDrawer"
        :model="editItem"
        :scenarioId="scenarioId"
        :versionId="versionId"
        @redrawFromRoot="redrawFromRoot"
        @stopProperties="stopProperties"
        @updateEditState="updateEditState"
      />
          <!-- :on-click-toggle-button="toggleRootMessageDrawer" -->
    </q-drawer>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, onMounted, onBeforeUnmount, onBeforeMount, ref } from 'vue';
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { useScenariosStore } from '@/stores/modules/scenarios';
import { cloneDeep, isEqual } from "lodash";
import PanZoom from "panzoom";
import {
  createSVGNode,
  deleteElements,
  drawBackGroundForCarouselGroup,
  drawExpandElement,
  drawIconForLinkedNode,
  drawIconForNode,
  drawSVGConnection,
  filterOutUnnecessaryTextMappings,
  findTheRootNode,
  generateMindMap,
  getCollapsedMindmapForDrawing,
  LINKED_NODE_DELETE_ERROR,
  replaceFirstIncompletePostbackActionInBubble,
  deletePostbackActionFromBubbleFlexParams,
} from "@/services/MindMapService";
import { TEMPLATE_TALK_IDS } from "@/stores/modules/scenarios/scenarios.constants";
import { generateUUID } from "@/utils/uuidUtils";
import { usePermissionHelper } from '@/mixins/PermissionHelper';
import Property from "@/pages/admin/ScenarioMindmap/Properties/Property.vue";
import RootMessage from "@/pages/admin/ScenarioMindmap/Properties/RootMessage.vue";
/* import {
  FETCH_ALL_SCENARIOS,
  FETCH_SCENARIO_DETAIL,
  FETCH_SPECIAL_TALK_FLOW_START,
  SAVE_TALK_NODES
} from "@/store/action-types";
import { SET_SCENARIO_MINDMAP, SET_SCENARIO_MINDMAP_MESSAGES, SET_SCENARIO_TEXTMAP } from "@/store/mutation-types"; */
import SubAppBar from "@/components/common/SubAppBar.vue";
import { IDamageReportStorePlugins } from '@/stores/modules/scenarios/scenarios.damage-report.plugin';
import { ActiveScenario } from '@/types/index';
import { useSettingsStore } from '@/stores/modules/settings';

// store with plugin typed
const scenariosStoreUnTyped = useScenariosStore();
export type ScenarioStoreWithPlugin = typeof scenariosStoreUnTyped & IDamageReportStorePlugins;
const scenariosStore = scenariosStoreUnTyped as ScenarioStoreWithPlugin

// other
const permissionHelper = usePermissionHelper();

const router = useRouter();
const route = useRoute();
const $q = useQuasar();

const settingsStore = useSettingsStore();

const DOUBLE_CLICK_TIMEOUT_MILLI_SEC = 333;
const ALLOW_LINK_DATA_TYPES = ["buttons"];

// data
const isLoading = ref(false);
const talkName = ref('');
const loadingMindMap = ref(false);
const scenarioMindmapMessages = ref();
const validTextMappings = ref();
const mindMapGenerations = ref();
const panzoom = ref();
const maxScale = ref(200);
const minScale = ref(20);
const scale = ref(100);
const rootNode = ref();
const mindMapErrorMessage = ref();
const onSaveDeleteTargetIds = ref([]);
const tempMindMapForDrawing = ref();
const activeItem = ref();
const editItem = ref();
const previousState = ref();
const nextState = ref();
const highlightedNodes = ref();
const nodeClickHandler = ref();
const linkItem = ref();
const editProperties = ref();
const isChangingEditItem = ref();
const validScenarioTextMap = ref();
const svgHeight = ref();
const lastClickPosition = ref();
const newTalkUUID = ref();
const deleteTargets = ref();
const copiedItems = ref();
const editItemDataType = ref();
const displayTalkOptions = ref();
const scenarioMindmapSnapshot = ref();
const windowWidth = ref();

const svgContainer = ref();

const isMenuVertical = ref(settingsStore.commonSettings.menuStyle === "vertical");
// const rootDrawer = ref();

// computed
const scenarioMindmap = computed(() => {
return scenariosStore.scenarioMindmap || {}});
const scenarioTextMap = computed(() => scenariosStore.scenarioTextmap);
const scenarioMessages = computed(() => scenariosStore.scenarioMessages || []);
const scenarioTalks = computed(() => scenariosStore.scenarioTalks);
const userMessages = computed(() => scenariosStore.userMessages);
const isSavingTalkNodes = computed(() => scenariosStore.isSavingTalkNodes);
const saveTalkNodesSuccess = computed(() => scenariosStore.saveTalkNodesSuccess);
const saveTalkNodesError = computed(() => scenariosStore.saveTalkNodesError);
const activeScenario = computed(() => { return scenariosStore.activeScenario || {} as ActiveScenario });
const scenarioId = computed(() => route.params.scenarioId.toString());
const versionId = computed(() => route.params.versionId.toString());
const onScenarioSettings = computed((): any => {
  if (getTalkIdFromName()) {
    return {
      name: "ScenarioSettingsDetailPage",
      params: {
        env: "sandbox",
        scenarioId: scenarioId.value,
        versionId: versionId.value,
        talkId: getTalkIdFromName(),
      },
    };
  } else {
    return {
      name: "ScenarioSettingsPage",
      params: {
        env: "sandbox",
      },
    }
  }
});
const displayVersionName = computed(() => {
  if (activeScenario.value.versions) {
    return activeScenario.value.versions[route.params.versionId.toString()]?.displayVersionName;
  }

  return route.params.versionId;
});

const onScenarioHome = computed((): any => {
  return {
    name: "ScenarioSettingsPage",
    params: {
      env: "sandbox",
    },
  };
});

const filteredScenarioTextMap = computed((): any => {
  const nextScenarioTextMap = cloneDeep(scenarioTextMap.value);
  const { textMapping } = cloneDeep(nextScenarioTextMap);
  const filteredTextMap = {} as any;
  Object.keys(textMapping || {}).forEach((key) => {
    if (onSaveDeleteTargetIds.value && !onSaveDeleteTargetIds.value.includes(textMapping[key])) {
      filteredTextMap[key] = textMapping[key];
    }
  });
  return { ...nextScenarioTextMap, textMapping: filteredTextMap };
});

const showDrawer = computed({
  get(): boolean {
    return !!(editProperties.value && editItem.value);
  },
  set(): void {},
});

const showRootMessageDrawer = computed({
  get(): boolean {
    return !!(editProperties.value && editItem.value && !editItem.value.dataId);
  },
  set(): void {},
});

const showPropertyDrawer = computed({
  get() {
    return !!(editProperties.value && editItem.value && editItem.value.dataId);
  },
  set() {},
});

const isMacOS = computed((): boolean => {
  return navigator.platform.toLowerCase().includes("mac");
});

const propertyTabWidth = computed((): number => {
  let size = ["bubbleFlex"].includes(editItemDataType.value) ? 35 : 25;
  return windowWidth.value * (size / 100) + 38;
});


// methods
const getDataForDetails = scenariosStore.fetchScenarioDetail;
const getSpecialTalkFlowStart = scenariosStore.fetchSpecialTalkFlowStart;
const saveTalkNodes = scenariosStore.saveTalkNodes;
const setScenarioTextMap = scenariosStore.setScenarioTextmap;
const updateMindMapMessages = scenariosStore.setScenarioMindmapMessages;
const updateMindMap = scenariosStore.setScenarioMindmap;

const onMount = async (force = false): Promise<void> => {
  await fetchProperties(force);
};

const talkOptions = (): any => {
  return scenarioTalks.value ? scenarioTalks.value.map((a: any) => a.params.name).sort() : [];
};

const getTalkIdFromName = (): any => {
  const talk = scenarioTalks.value.find((elem: any) => elem.params.name === talkName.value);
  return talk ? talk.dataId : '';
};

const selectTalk = (talk: any): void => {
  loadingMindMap.value = true;
  router.replace({
    name: "ScenarioMindmapPage",
    params: {
      scenarioId: scenarioId.value,
      versionId: versionId.value,
      talkName: talk,
      env: "sandbox"
    },
  });
};

const buildMindMapFromNewTalk = async (): Promise<void> => {
  scenarioMindmapMessages.value = cloneDeep(scenarioMindmap.value[versionId.value][talkName.value]);
  validTextMappings.value = cloneDeep(scenarioMindmap.value[versionId.value].textMapping);
  mindMapGenerations.value = [];
  deleteMindMapChildren();
  await buildTheMindMap();
  if (route.query.new === "true") {
    onClickSaveButton();
  }

  let mindMapSVG = document.getElementById("mindMapSVG");
  panzoom.value = PanZoom(mindMapSVG, {
    maxZoom: maxScale.value / 100,
    minZoom: minScale.value / 100,
    initialX: 50,
    initialZoom: scale.value / 100,
    zoomSpeed: 0.666, // これはトラックパッド（マウスホイールには効かない）
    pinchSpeed: 1, // これはiPadとかの2本指ズーム
    smoothScroll: false,
    zoomDoubleClickSpeed: 1,
  });
  panzoom.value.on("zoom", () => onZoom());

  recenterMap(0);
};

const onZoom = (): void => {
  if (!panzoom.value) {
    return;
  }
  const { scale: panScale} = panzoom.value.getTransform();
  
  scale.value = Math.round(panScale * 100);
};

const onClickEnlargeButton = (): void => {
  
  if (!panzoom.value) {
    return;
  }
  const { x, y } = panzoom.value.getTransform();

  let next = Math.ceil(scale.value / 10) * 10;
  if (next % 20 !== 0) {
    next += 10;
  }
  if (Math.abs(scale.value - next) === 0) {
    next += 20;
  }
  if (next >= maxScale.value) {
    next = maxScale.value;
  }
  scale.value = next;
  panzoom.value.smoothZoomAbs(x, y, next / 100);
};

const onClickShrinkButton = (): void => {
  if (!panzoom.value) {
    return;
  }
  const { x, y } = panzoom.value.getTransform();
  let next = Math.floor(scale.value / 10) * 10;
  if (next % 20 !== 0) {
    next -= 10;
  }
  if (Math.abs(scale.value - next) === 0) {
    next -= 20;
  }
  if (next <= minScale.value) {
    next = minScale.value;
  }
  scale.value = next;
  panzoom.value.smoothZoomAbs(x, y, next / 100);
};

const onClickResetButton = (): void => {
  if (!panzoom.value) {
    return;
  }
  const { x, y } = panzoom.value.getTransform();
  panzoom.value.smoothZoomAbs(x, y, 1);
};

const onInputZoomLevel = (event: any): void => {
  if (!panzoom.value || event.inputType) {
    return;
  }
  const { x, y } = panzoom.value.getTransform();
  panzoom.value.smoothZoomAbs(x, y, scale.value / 100);
};

const buildTheMindMap = async (): Promise<void> => {
  rootNode.value = null;
  mindMapErrorMessage.value = null;
  mindMapGenerations.value = [];
  loadingMindMap.value = true;
  try {
    //Filter out messages for this specific talk
    validTextMappings.value = filterOutUnnecessaryTextMappings(validTextMappings.value, scenarioMindmapMessages.value);
    
    //Find the root node of the map
    let rootNodeFound:any = null;
    let alreadyAddedRoot = false;

    //Processing for template scenarios
    const talkData = scenarioTalks.value.find((obj: any) => {
      return obj.params.name === talkName.value;
    });

    if (talkData && TEMPLATE_TALK_IDS.includes(talkData.dataId)) {
      // 保存後こちらのコードが読まれるが、ノードが表示されなくなるのでコメントアウト - honda
      // 恐らく scenarioMindmap の該当箇所にノード情報が含まれる見込みで書いたのだろうがノード情報は現状無し 
      // if (talkData.dataId in scenarioMindmap.value.specialTalkRootNodes) {
      //   alreadyAddedRoot = true;
      //   rootNodeFound = scenarioMindmap.value.specialTalkRootNodes[talkData.dataId];
      //   rootNode.value = rootNodeFound;
      //   mindMapGenerations.value.push([rootNode.value]);
      //   mindMapGenerations.value.push([scenarioMindmap.value.specialTalkFirstBotReply[talkData.dataId]]);
      // } else 
      {
        //Call the backend for the root node and save into state
        const payload = {
          talkId: talkData.dataId,
          scenario: scenarioId.value + "#" + versionId.value,
        };
        await getSpecialTalkFlowStart(payload);

        if (talkData.dataId in scenarioMindmap.value.specialTalkRootNodes) {
          alreadyAddedRoot = true;
          rootNodeFound = scenarioMindmap.value.specialTalkRootNodes[talkData.dataId];
          rootNode.value = rootNodeFound;
          mindMapGenerations.value.push(rootNode.value);
          mindMapGenerations.value.push([scenarioMindmap.value.specialTalkFirstBotReply[talkData.dataId]]);
        }
      }
    } else {
      rootNodeFound = findTheRootNode(
        validTextMappings.value,
        scenarioMindmapMessages.value,
        scenarioTalks.value,
        talkName.value,
        userMessages.value
      );
    }

    if (rootNodeFound == null) {
      mindMapErrorMessage.value = "ルートノードが見つけられませんでした。";
    } else if ("errorMessage" in rootNodeFound) {
      mindMapErrorMessage.value = rootNodeFound.errorMessage;
    } else {
      if (!alreadyAddedRoot) {
        rootNode.value = rootNodeFound;
        //push the root node as the generation 0 of the mindmap
        mindMapGenerations.value.push([rootNode.value]);
      }
      
      const filteredScenarioMessages = cloneDeep(scenarioMessages.value).filter(
        (messages: any) => onSaveDeleteTargetIds.value ? !onSaveDeleteTargetIds.value?.includes(messages.dataId) : false
      );
      mindMapGenerations.value = await generateMindMap(
        rootNode.value,
        mindMapGenerations.value,
        scenarioMindmapMessages.value,
        validTextMappings.value,
        filteredScenarioTextMap.value,
        scenarioTalks.value,
        talkName.value,
        scenarioId.value + "#" + versionId.value,
        filteredScenarioMessages
      );

      tempMindMapForDrawing.value = cloneDeep(mindMapGenerations.value);
      createTheSVGNodes();
      drawTheSVGConnections();
      drawParentSVGIcons();
    }
  } catch (error: any) {
    mindMapErrorMessage.value = "予期せぬエラーが発生しました。";
  } finally {
    loadingMindMap.value = false;
    //Saving from state into messages for new messages that were created during mindmap building
    scenarioMindmapMessages.value = cloneDeep(scenarioMindmap.value[versionId.value][talkName.value]);
    validTextMappings.value = cloneDeep(scenarioMindmap.value[versionId.value].textMapping);
  }
};

const recenterMap = (id: any): void => {
  const elem = document.getElementById("node." + id);
  if (elem) {
    panzoom.value.centerOn(elem);
  }
};

const deleteMindMapChildren = (): void => {
  const myNode = document.getElementById("mindMapSVG");
  while (myNode?.firstChild) {
    if(myNode.lastChild) myNode.removeChild(myNode.lastChild);
  }
};

const createTheSVGNodes = (): void => {
  const mindmapNodes = cloneDeep(mindMapGenerations.value).flat();
  const drawBackGroundForCarouselGroupTargets:any[] = [];
  tempMindMapForDrawing.value.forEach((gen: any, genCounter: any) => {
    gen.forEach((elem: any, index: any) => {
      const parentElement = "parentId" in elem ? document.getElementById("node." + elem.parentId) : null;
      if (parentElement != null || genCounter === 0) {
        const tempTalk = cloneDeep(scenarioMindmap.value[versionId.value][talkName.value]);
        const nodeGroup = createSVGNode(elem, genCounter, tempMindMapForDrawing.value, parentElement);
        ["dataId", "mindmapId", "parentId", "postbackId", "generation", "branchIndex", "carouselIndex", "carouselKey"].forEach(
          (key) => {
            if (elem[key] != null) {
              // NOTE: not ( null || undefined ) 0がfalsyなので回避
              nodeGroup.setAttribute(`data-${key}`, elem[key]);
            }
          }
        );

        //This is a new message generated automatically by mindmap algorithm
        //Save the message to mindmap state
        if ("newMessage" in elem && !("dataId" in elem)) {
          const parentNode = tempMindMapForDrawing.value[genCounter - 1].find(
            (obj: any) => obj.mindmapId === elem.parentId
          );

          elem["scenario"] = scenarioId.value + "#" + versionId.value;
          setValueOfNodeInMindmap(elem, "scenario", elem["scenario"]);
          elem["dataId"] = generateUUID();
          setValueOfNodeInMindmap(elem, "dataId", elem["dataId"]);
          elem["talk"] = talkName.value;
          setValueOfNodeInMindmap(elem, "talk", elem["talk"]);
          const isPostbackCustomDamageReport = isCustomDamageReportTalk.value && ["image_message", "location_message"].includes(parentNode.type)

          if (parentNode && (parentNode.type === "postback" || isPostbackCustomDamageReport)) {
            //If parent is a postback node with empty data and _shouldSetDataId, we set postback data to parent action
            const parentBotNodeIndex = tempMindMapForDrawing.value[genCounter - 2].findIndex(
              (node: any) => node.mindmapId === parentNode.parentId
            );
            if (parentBotNodeIndex > -1 && tempMindMapForDrawing.value[genCounter - 2][parentBotNodeIndex].params) {
              const { dataType, params } = tempMindMapForDrawing.value[genCounter - 2][parentBotNodeIndex];
              let targetKey;
              //Replace dataId in bubbleFlex if necessary
              if (dataType == "bubbleFlex") {
                replaceFirstIncompletePostbackActionInBubble(params, elem["dataId"])
              }
              if (params.columnCount) {
                try {
/*                   console.log("parentNode:", parentNode);
                  console.log("carouselIndex:", parentNode.carouselIndex);
                  console.log("branchIndex:", parentNode.branchIndex);
                  console.log("columnCount:", params.columnCount);
                  console.log("actionCount:", params.actionCount);
                  console.log(
                    "targetKey:",
                    `action.${parentNode.carouselIndex}.${
                      parentNode.branchIndex - params.actionCount * parentNode.carouselIndex
                    }`
                  ); */
                } catch (e: any) {}
              }
              if (
                params.columnCount &&
                params[
                  `action.${parentNode.carouselIndex}.${
                    parentNode.branchIndex - params.actionCount * parentNode.carouselIndex
                  }`
                ] &&
                params[
                  `action.${parentNode.carouselIndex}.${
                    parentNode.branchIndex - params.actionCount * parentNode.carouselIndex
                  }`
                ]._shouldSetDataId
              ) {
                targetKey = `action.${parentNode.carouselIndex}.${
                  parentNode.branchIndex - params.actionCount * parentNode.carouselIndex
                }`;
              } else if (
                params.actionCount &&
                params[`actions.${parentNode.branchIndex}`] &&
                params[`actions.${parentNode.branchIndex}`]._shouldSetDataId
              ) {
                targetKey = `actions.${parentNode.branchIndex}`;
              } else if (params.actionLeft && params.actionLeft._shouldSetDataId) {
                targetKey = "actionLeft";
              } else if (params.actionRight && params.actionRight._shouldSetDataId) {
                targetKey = "actionRight";
              }
              if (targetKey) {
                if (isPostbackCustomDamageReport) {
                  delete tempMindMapForDrawing.value[genCounter - 2][parentBotNodeIndex].params[targetKey]._shouldSetDataId;
                  let obj = tempMindMapForDrawing.value[genCounter - 2][parentBotNodeIndex].params
                  const dataKey = Object.keys(obj).find(key => obj[key].data)
                  let firstActionHasPostbackData = obj[dataKey]?.data
                  // set linkToDataId for backend to first elem id
                  if (firstActionHasPostbackData) {
                    tempMindMapForDrawing.value[genCounter - 2][parentBotNodeIndex].params.linkToDataId = firstActionHasPostbackData
                    tempMindMapForDrawing.value[genCounter - 2][parentBotNodeIndex].params[targetKey].data = firstActionHasPostbackData;
                  }
                  else {
                    tempMindMapForDrawing.value[genCounter - 2][parentBotNodeIndex].params.linkToDataId = elem["dataId"]
                    tempMindMapForDrawing.value[genCounter - 2][parentBotNodeIndex].params[targetKey].data = elem["dataId"];
                  }
                }
                else {
                  delete tempMindMapForDrawing.value[genCounter - 2][parentBotNodeIndex].params[targetKey]
                  ._shouldSetDataId;
                  tempMindMapForDrawing.value[genCounter - 2][parentBotNodeIndex].params[targetKey].data =
                  elem["dataId"];
                }
                delete tempMindMapForDrawing.value[genCounter - 2][parentBotNodeIndex].params[targetKey]
                  ._shouldSetDataId;
                tempMindMapForDrawing.value[genCounter - 2][parentBotNodeIndex].params[targetKey].data =
                  elem["dataId"];

/*                 console.log(
                  "createTheSVGNodes",
                  "parent node updated:",
                  tempMindMapForDrawing.value[genCounter - 2][parentBotNodeIndex]
                ); */
              }

              const tempParentBotNode = cloneDeep(tempMindMapForDrawing.value[genCounter - 2][parentBotNodeIndex]);
              delete tempParentBotNode.mindmapId;
              delete tempParentBotNode.parentId;
              const targetScenarioMindmapIndex = tempTalk.findIndex(
                (talk: any) => talk.dataId === tempParentBotNode.dataId
              );
              if (targetScenarioMindmapIndex > -1) {
                tempTalk[targetScenarioMindmapIndex] = tempParentBotNode;
              }

              // NOTE: we should write back to keep replaced state
              mindMapGenerations.value[genCounter - 2][parentBotNodeIndex] = cloneDeep(
                tempMindMapForDrawing.value[genCounter - 2][parentBotNodeIndex]
              );
            }
          }

          // delete elem.newMessage;
          // deleteValueOfNodeInMindmap(elem, "newMessage");

          // console.log("createTheSVGNodes", "create new element:", elem);

          //Save this message to scenarioMindMap[talkName.value] state
          //copy and get rid of the mind map only stuff
          const tempElem = cloneDeep(elem);
          delete tempElem.mindmapId;
          delete tempElem.parentId;
          // delete tempElem.newMessage;

          tempTalk.push(tempElem);
          const payload = {
            versionName: versionId.value,
            value: tempTalk,
            valueName: talkName.value,
          };
          updateMindMapMessages(payload);

          if (parentNode && parentNode.type === "message") {
            //If parent is a text node, then save text mapping to scenarioMindMap.textMapping state
            const tempTextMapping = cloneDeep(scenarioMindmap.value[versionId.value].textMapping);
            tempTextMapping[parentNode.value] = elem.dataId;
            const textMappingPayload = {
              versionName: versionId.value,
              value: tempTextMapping,
              valueName: "textMapping",
            };
            updateMindMapMessages(textMappingPayload);
          }
        }

        //If carouselIndex exists then drawing the actions of a carousel message
        //Only need to draw the base rectangle when drawing the first action of each
        //Individual bubble in the carousel
        if ("carouselIndex" in elem && elem.firstActionOfCarouselGroup) {
          // NOTE: 最終的なノード間の距離を考慮する必要があるので、計算を遅延させる
          drawBackGroundForCarouselGroupTargets.push({
            elem,
            genCounter,
            nodeGroup,
          });
        }

        //If messageLinkToOtherTalk in element, then this element has a relation to another talk
        //Create the talk icon, append to mindMapSVG and add event listener
        if ("messageLinkToOtherTalk" in elem) {
          const talkLink = drawIconForNode(nodeGroup, "messageLinkToOtherTalk");
          talkLink.addEventListener("click", moveToTalk.bind(null, elem.linkedTalk), false);

          nodeGroup.appendChild(talkLink);
        }

        //If linkToAncestor in element, then this element is a looped scenario
        //Create the icon, append to mindMapSVG and add event listener
        if ("linkToAncestor" in elem) {
          const childNode = mindmapNodes.find((node: any) => node.parentId === elem.mindmapId);
          const hasToggleButton = childNode && childNode.expandNode != null;
          const ancestorItem = drawIconForLinkedNode(nodeGroup, "linkToAncestor", hasToggleButton);
          let highlightList = [elem.linkedNode, elem.mindmapId];
          ancestorItem.setAttribute("data-links", JSON.stringify(highlightList));
          ancestorItem.addEventListener(
            "click",
            highlightListOfElems.bind(null, highlightList, highlightList.length - 1),
            false
          );
          nodeGroup.appendChild(ancestorItem);
        }

        //If linkInsideTree in element, then this element is already displayed in mindmap
        //Create the icon, append to mindMapSVG and add event listener
        if ("linkInsideTree" in elem) {
          const childNode = mindmapNodes.find((node: any) => node.parentId === elem.mindmapId);
          const hasToggleButton = childNode && childNode.expandNode != null;
          const linkedItem = drawIconForLinkedNode(nodeGroup, "linkInsideTree", hasToggleButton);
          let highlightList = [elem.linkedNode, elem.mindmapId];
          linkedItem.setAttribute("data-links", JSON.stringify(highlightList));
          linkedItem.addEventListener(
            "click",
            highlightListOfElems.bind(null, highlightList, highlightList.length - 1),
            false
          );
          nodeGroup.appendChild(linkedItem);
        }

        nodeGroup.addEventListener("click", onClickNode.bind(null, elem), false);
        document.getElementById("mindMapSVG")?.appendChild(nodeGroup);

        //Check if the node has multiple children
        if ("expandNode" in elem) {
          if (elem.dataType === "text") {
            deleteValueOfNodeInMindmap(elem, "expandNode");
          } else {
            const expandBox = drawExpandElement(nodeGroup, elem, genCounter);
            expandBox.addEventListener("click", toggleExpand.bind(null, elem), false);
            nodeGroup.appendChild(expandBox);
          }
        } else if (genCounter !== tempMindMapForDrawing.value.length - 1) {
          const childGen = tempMindMapForDrawing.value[genCounter + 1];
          const children = childGen.filter((obj: any) => obj.parentId === elem.mindmapId);
          if (children.length > 1) {
            setValueOfNodeInMindmap(elem, "expandNode", true);
            elem["expandNode"] = true;
            const expandBox = drawExpandElement(nodeGroup, elem, genCounter);
            expandBox.addEventListener("click", toggleExpand.bind(null, elem), false);
            nodeGroup.appendChild(expandBox);
          }
        }
      }
    });
    genCounter++;
  });
  drawBackGroundForCarouselGroupTargets.forEach((target) => {
    const { elem, genCounter, nodeGroup } = target;
    const background = drawBackGroundForCarouselGroup(elem, genCounter, tempMindMapForDrawing.value, nodeGroup);
    const svg = document.getElementById("mindMapSVG");
    if (svg && svg.firstChild && background) {
      svg.insertBefore(background, svg.firstChild);
    }
  });
};

const drawTheSVGConnections = (): void => {
  for (let x = tempMindMapForDrawing.value.length - 1; x > 0; x--) {
    const currentGenNodes = tempMindMapForDrawing.value[x];
    const parentGenNodes = tempMindMapForDrawing.value[x - 1];
    currentGenNodes.forEach((childNode: any) => {
      const parentNode = parentGenNodes.find((obj: any) => {
        return obj.mindmapId === childNode.parentId;
      });
      if (childNode && parentNode) {
        const childElement = document.getElementById("node." + childNode.mindmapId);
        const parentElement = document.getElementById("node." + parentNode.mindmapId);
        if (!childElement || !parentElement) {
          // NOTE: may be collapsed element.
          return;
        }
        const line = drawSVGConnection(childElement.children[0], parentElement.children[0], parentNode, childNode);
        document.getElementById("mindMapSVG")?.appendChild(line);
      }
    });
  }
};

const drawParentSVGIcons = (): void => {
  const listOfNodesWithLinks:any[] = [];
  const listOfNodesWithLoops:any[] = [];
  const listOfLinkedNodes:any[] = [];
  const listOfLoopedNodes:any[] = [];
  const mindmapNodes = cloneDeep(mindMapGenerations.value).flat();
  mindMapGenerations.value.forEach((gen: any) => {
    gen.forEach((node: any) => {
      if ("linkInsideTree" in node) {
        listOfNodesWithLinks.push(node);
        if (!listOfLinkedNodes.includes(node.linkedNode)) {
          listOfLinkedNodes.push(node.linkedNode);
        }
      }
      if ("linkToAncestor" in node) {
        listOfNodesWithLoops.push(node);
        if (!listOfLoopedNodes.includes(node.linkedNode)) {
          listOfLoopedNodes.push(node.linkedNode);
        }
      }
    });
  });
  listOfLinkedNodes.forEach((linkNodeId) => {
    const nodeGroup = document.getElementById("node." + linkNodeId);
    if (nodeGroup) {
      const currentNode = mindmapNodes.find((node: any) => node.mindmapId === linkNodeId);
      const hasToggleButton = currentNode && currentNode.expandNode != null;
      const linkIcon = drawIconForLinkedNode(nodeGroup, "linkInsideTree", hasToggleButton);
      let mappedNodeIds = listOfNodesWithLinks
        .filter((obj) => {
          return obj.linkedNode === linkNodeId;
        })
        .map((node) => {
          return node.mindmapId;
        });
      mappedNodeIds.push(linkNodeId);
      linkIcon.setAttribute("data-links", JSON.stringify(mappedNodeIds));
      linkIcon.addEventListener(
        "click",
        highlightListOfElems.bind(null, mappedNodeIds, mappedNodeIds.length - 1),
        false
      );
      nodeGroup.appendChild(linkIcon);
    }
  });
  listOfLoopedNodes.forEach((linkNodeId) => {
    const nodeGroup = document.getElementById("node." + linkNodeId);
    if (nodeGroup) {
      const currentNode = mindmapNodes.find((node: any) => node.mindmapId === linkNodeId);
      const hasToggleButton = currentNode && currentNode.expandNode != null;
      const linkIcon = drawIconForLinkedNode(nodeGroup, "linkToAncestor", hasToggleButton);
      let mappedNodeIds = listOfNodesWithLoops
        .filter((obj) => {
          return obj.linkedNode === linkNodeId;
        })
        .map((node) => {
          return node.mindmapId;
        });
      mappedNodeIds.push(linkNodeId);
      linkIcon.setAttribute("data-links", JSON.stringify(mappedNodeIds));
      linkIcon.addEventListener(
        "click",
        highlightListOfElems.bind(null, mappedNodeIds, mappedNodeIds.length - 1),
        false
      );
      nodeGroup.appendChild(linkIcon);
    }
  });
};

const setValueOfNodeInMindmap = (elem: any, attribute: any, value: any): void => {
  //helper function to set an attribute in mindmap using clone deep
  let genCounter = 0;
  mindMapGenerations.value.some((gen: any) => {
    let mappedGen = gen.map((node: any) => {
      return node.mindmapId;
    });
    let indexOfToggledNode = mappedGen.indexOf(elem.mindmapId);
    if (indexOfToggledNode >= 0) {
      const tempNode = cloneDeep(mindMapGenerations.value[genCounter][indexOfToggledNode]);
      tempNode[attribute] = value;
      mindMapGenerations.value[genCounter][indexOfToggledNode] = cloneDeep(tempNode);
      return true;
    }
    genCounter++;
  });
};

const deleteValueOfNodeInMindmap = (elem: any, attribute: any): void => {
  //helper function to delete an attribute in mindmap using clone deep
  let genCounter = 0;
  mindMapGenerations.value.some((gen: any) => {
    let mappedGen = gen.map((node: any) => {
      return node.mindmapId;
    });
    let indexOfToggledNode = mappedGen.indexOf(elem.mindmapId);
    if (indexOfToggledNode >= 0) {
      delete mindMapGenerations.value[genCounter][indexOfToggledNode][attribute];
      return true;
    }
    genCounter++;
  });
};

const toggleValueOfNodeInMindmap = (elem: any, attribute: any): void => {
  //helper function to toggle an attribute in mindmap using clone deep
  let genCounter = 0;
  mindMapGenerations.value.some((gen: any) => {
    let mappedGen = gen.map((node: any) => {
      return node.mindmapId;
    });
    let indexOfToggledNode = mappedGen.indexOf(elem.mindmapId);
    if (indexOfToggledNode >= 0) {
      const tempNode = cloneDeep(mindMapGenerations.value[genCounter][indexOfToggledNode]);
      tempNode[attribute] = !tempNode[attribute];
      mindMapGenerations.value[genCounter][indexOfToggledNode] = cloneDeep(tempNode);
      return true;
    }
    genCounter++;
  });
};

const expandMindMapToDisplayNode = (elementId: any): void => {
  let genCounter = 0;
  let indexOfElem = 0;
  mindMapGenerations.value.some((gen: any) => {
    const mindMapIds = gen.map((a: any) => a.mindmapId);
    let indexOfElement = mindMapIds.indexOf(parseInt(elementId));
    if (indexOfElement >= 0) {
      indexOfElem = indexOfElement;
      return true;
    }
    genCounter++;
  });
  let parentNodeId = mindMapGenerations.value[genCounter][indexOfElem].parentId;
  for (let x = genCounter - 1; x > 0; x--) {
    let parentNode = mindMapGenerations.value[x].find((obj: any) => obj.mindmapId === parentNodeId);
    if ("expandNode" in parentNode && !parentNode.expandNode) {
      toggleValueOfNodeInMindmap(parentNode, "expandNode");
    }
    parentNodeId = parentNode.parentId;
  }
};

const redrawMapForExpandCollapse = (elementId: any = null): void => {
  const innerSvg = document.getElementById("mindMapSVG");
  let innerRectSnapshot;
  if (innerSvg) {
    innerRectSnapshot = innerSvg.getBoundingClientRect();
  }

  deleteMindMapChildren();
  tempMindMapForDrawing.value = getCollapsedMindmapForDrawing(mindMapGenerations.value);

  createTheSVGNodes();
  drawTheSVGConnections();
  drawParentSVGIcons();

  if (activeItem.value) {
    const editItem = renderActiveNodeBorder(activeItem.value);
    if (editItem) {
      editItem.value = editItem;
    }
  }

  if (elementId != null && innerRectSnapshot) {
    const innerSvg = document.getElementById("mindMapSVG");
    const node = document.getElementById(`node.${elementId}`);
    if (!innerSvg || !node) {
      // NOTE: fallback;
      recenterMap(elementId);
      return;
    }

    const innerRect = innerSvg.getBoundingClientRect();
    const svg = document.getElementById("svgElem");
    const rect: any = node.querySelector("rect[height]");
    if (!svg || !rect) {
      // NOTE: fallback;
      recenterMap(elementId);
      return;
    }

    const svgPos = svg.getBoundingClientRect();
    const nodePos = node.getBoundingClientRect();
    const viewportOffset = {
      x: nodePos.x - svgPos.x,
      y: nodePos.y - svgPos.y,
    };
    const transform = panzoom.value.getTransform();
    panzoom.value.moveTo(
      viewportOffset.x - rect.x.baseVal.value * transform.scale,
      viewportOffset.y + (innerRectSnapshot.height - innerRect.height) / 2 - rect.y.baseVal.value * transform.scale
    );
  }
};

const redrawFromRoot = (elementRootText: any): void => {
  deleteMindMapChildren();
  mindMapGenerations.value[0][0].value = elementRootText;
  tempMindMapForDrawing.value = cloneDeep(mindMapGenerations.value);
  rootNode.value = elementRootText;

  createTheSVGNodes();
  drawTheSVGConnections();
  drawParentSVGIcons();
  recenterMap(0);
};

const updateScenarioMessage = async (
  oldState: any,
  newState: any,
  updatedMessage: any,
  isClearActiveItem = true,
  isClearLinkItem = true,
  clearLinkHighlight = true
): Promise<void> => {
  previousState.value = {
    scenarioMindmapMessages: oldState,
    onSaveDeleteTargetIds: cloneDeep(onSaveDeleteTargetIds.value),
    validTextMappings: cloneDeep(validTextMappings.value),
  };
  nextState.value = null;

  deleteMindMapChildren();

  let index = 0;
  for (index; index < mindMapGenerations.value.length; index++) {
    const tempMessageCheck = mindMapGenerations.value[index].find(
      (obj: any) => obj.mindmapId === updatedMessage.mindmapId
    );
    if (tempMessageCheck) {
      break;
    }
  }

  const innerIndex = mindMapGenerations.value[index].findIndex((x: any) => x.mindmapId === updatedMessage.mindmapId);
  mindMapGenerations.value[index][innerIndex] = updatedMessage;
  mindMapGenerations.value = mindMapGenerations.value.slice(0, index + 1);

  const filteredScenarioMessages = cloneDeep(scenarioMessages.value)?.filter(
    (messages: any) => !onSaveDeleteTargetIds.value?.includes(messages.dataId)
  );
  mindMapGenerations.value = await generateMindMap(
    rootNode.value,
    mindMapGenerations.value,
    scenarioMindmapMessages.value,
    validTextMappings.value,
    filteredScenarioTextMap.value,
    scenarioTalks.value,
    talkName.value,
    scenarioId.value + "#" + versionId.value,
    filteredScenarioMessages
  );

  tempMindMapForDrawing.value = cloneDeep(mindMapGenerations.value);

  createTheSVGNodes();
  drawTheSVGConnections();
  drawParentSVGIcons();
  recenterMap(updatedMessage.mindmapId);

  scenarioMindmapMessages.value = cloneDeep(scenarioMindmap.value[versionId.value][talkName.value]);
  validTextMappings.value = cloneDeep(scenarioMindmap.value[versionId.value].textMapping);

  if (isClearActiveItem) {
    clearActiveItem();
  }
  if (isClearLinkItem) {
    clearLinkItem();
  }
  if (clearLinkHighlight) {
    unhighightLinks();
  }
};

const toggleExpand = (elem: any, event: any): void => {
  if (event && event.stopPropagation) {
    event.stopPropagation();
  }
  if (elem.expandNode != null) {
    // console.log("toggleExpand:", elem);
    toggleValueOfNodeInMindmap(elem, "expandNode");
    // console.log("nextMindMapGenerations:", mindMapGenerations.value);
    redrawMapForExpandCollapse(elem.mindmapId);
  }
};

const highlightListOfElems = (nodeIdsToHighlight: any, nodeToCenterOn: any, event: any): void => {
  event.stopPropagation();
  unhighightLinks();

  //first expand all elements that need expanding
  nodeIdsToHighlight.forEach((id: any) => {
    const elem = document.getElementById("node." + id);
    if (!elem) {
      expandMindMapToDisplayNode(id);
    }
  });
  //redraw map
  redrawMapForExpandCollapse(nodeIdsToHighlight[nodeToCenterOn].mindmapId);
  //highlight the nodes
  nodeIdsToHighlight.forEach((id: any) => {
    const tempElemId = "node." + id;
    highlightedNodes.value.push(tempElemId);
    const elem = document.getElementById(tempElemId);
    if (elem) {
      elem.setAttribute("filter", "url(#linkhighlight)");
    }
  });
};

const clearNodeClickHandler = (): void => {
  // console.log("clearNodeClickHandler:", nodeClickHandler.value);
  clearTimeout(nodeClickHandler.value);
  nodeClickHandler.value = null;
};

const clearActiveItem = (): void => {
  // console.log("clearActiveItem:", activeItem.value);
  if (!activeItem.value) {
    return;
  }
  const targets = document.querySelectorAll('.node[filter="url(#activenode)"]');
  if (targets) {
    Array.from(targets).forEach((target) => {
      if (target.classList.contains("user")) {
        target.setAttribute("filter", "");
      } else if (target.classList.contains("bot")) {
        target.setAttribute("filter", "url(#botnode)");
      }
    });
  }
  activeItem.value = null;
};

const clearLinkItem = (): void => {
  // console.log("clearLinkItem:", linkItem.value);
  if (!linkItem.value) {
    return;
  }
  const targets = document.querySelectorAll('.node[filter="url(#linknode)"]');
  if (targets) {
    Array.from(targets).forEach((target) => {
      if (target.classList.contains("user")) {
        target.setAttribute("filter", "");
      } else if (target.classList.contains("bot")) {
        target.setAttribute("filter", "url(#botnode)");
      }
    });
  }
  linkItem.value = null;
};

const unhighightLinks = (): void => {
  // console.log("MOHEMOHE", "unhighightLinks()", "activeItem:", activeItem.value);
  const targets = document.querySelectorAll('.node[filter="url(#linkhighlight)"]');
  if (targets) {
    Array.from(targets).forEach((target) => {
      if (target.classList.contains("user")) {
        target.setAttribute("filter", "");
      } else if (target.classList.contains("bot")) {
        target.setAttribute("filter", "url(#botnode)");
      }
    });
  }
  highlightedNodes.value = [];
};

const moveToTalk = (moveToTalkName: any): void => {
  if (!(moveToTalkName in scenarioMindmap.value[versionId.value])) {
    saveTalkMessagesIntoMindmap(moveToTalkName);
  }
  talkName.value = moveToTalkName;
  router.push({
    name: "ScenarioMindmapPage",
    params: {
      scenarioId: scenarioId.value,
      versionId: versionId.value,
      talkName: moveToTalkName,
      env: "sandbox"
    },
  });
  buildMindMapFromNewTalk();
};

const onClickNode = (elem: any, event: any): any => {
  // console.log("clicking to select element", elem, event);
  event.stopPropagation();
  if (nodeClickHandler.value) {
    clearNodeClickHandler();
    return onDoubleClickNode(elem, event);
  }
  nodeClickHandler.value = setTimeout(() => {
    clearNodeClickHandler();
    onSingleClickNode(elem, event);
  }, DOUBLE_CLICK_TIMEOUT_MILLI_SEC);
};

const asyncSleep = (timeout: any) => {
  return new Promise<void>((resolve) => setTimeout(() => resolve(), timeout));
};

const onSingleClickNode = async (elem: any, event: any): Promise<any> => {
  if (event.shiftKey) {
    return onShiftSingleClickNode(elem);
  }

  if (activeItem.value && activeItem.value.mindmapId !== elem.mindmapId) {
    clearActiveItem();
    clearLinkItem();
    unmarkDeleteNode();
  }
  if (editItem.value && editItem.value.mindmapId !== elem.mindmapId && editItem.value.mindmapId !== elem.parentId) {
    unhighightLinks();
  }

  // console.log("active ->", elem);
  activeItem.value = elem;

  const editItemLocal = renderActiveNodeBorder(activeItem.value);
  const { branchIndex } = editItemLocal;

  const beforeEditItem = cloneDeep(editItemLocal || {});
  if (editItemLocal) {
    delete editItemLocal.branchIndex;
    if (editItemLocal) {
      // toggleRootMessageDrawer(true);
      // togglePropertyDrawer(true);
      if (editItemLocal.dataId !== editItemLocal.dataId) {
        editItem.value = null;
      }
      await asyncSleep(250);
    }
    editItem.value = editItemLocal;
    if (branchIndex) {
      setTimeout(
        () => {
          editItem.value = { ...editItemLocal, branchIndex };
        },
        beforeEditItem.dataId === editItemLocal.dataId ? 0 : 300
      );
    }
  }

  if (editItemLocal.dataId || editItemLocal.mindmapId === 0 || editItemLocal.mindmapId === "0") {
    if (editItemLocal.mindmapId === 0 || editItemLocal.mindmapId === "0") {
      editItemLocal["rootId"] = mindMapGenerations.value[1][0].dataId;
    }
    editProperties.value = true;
  } else {
    editProperties.value = false;
  }
};

const renderActiveNodeBorder = (elem: any): any => {
  let editItem;
  const target = document.getElementById(`node.${elem.mindmapId}`);
  if (target) {
    const elements = cloneDeep(mindMapGenerations.value).flat();
    // console.log("elements:", elements);
    if (elem.mindmapId === 0) {
      editItem = cloneDeep(elem);
      target.setAttribute("filter", "url(#activenode)");
    } else if (target.classList.contains("user")) {
      const parent = elements.find((element: any) => element.mindmapId === elem.parentId);
      if (parent) {
        editItem = { ...cloneDeep(parent), branchIndex: elem.branchIndex };
        const parentNode = document.getElementById(`node.${parent.mindmapId}`);
        if (parentNode) {
          parentNode.setAttribute("filter", "url(#activenode)");
        }
        elements
          .filter((element: any) => element.parentId === parent.mindmapId)
          .forEach((child: any) => {
            const node = document.getElementById(`node.${child.mindmapId}`);
            if (node) {
              node.setAttribute("filter", "url(#activenode)");
            }
          });
      }
    } else {
      editItem = cloneDeep(elem);
      target.setAttribute("filter", "url(#activenode)");
      elements
        .filter((element: any) => element.parentId === elem.mindmapId)
        .forEach((child: any) => {
          const node = document.getElementById(`node.${child.mindmapId}`);
          if (node) {
            node.setAttribute("filter", "url(#activenode)");
          }
        });
    }
  }
  return editItem;
};

const onShiftSingleClickNode = (elem: any): void => {
  if (elem.dataType === "__INITIAL__") {
    $q.notify({
      message: "初期状態のノードには関連付けできません",
      type: "error",
    });
    return;
  }

  const from = document.getElementById(`node.${editItem.value.mindmapId}`);
  const to = document.getElementById(`node.${elem.mindmapId}`);
  if (!from || !to || !to.classList.contains("bot")) {
    return;
  }
  if (!ALLOW_LINK_DATA_TYPES.includes(editItem.value.dataType)) {
    $q.notify({
      message:
        "関連付けの設定は元ノードが " +
        ALLOW_LINK_DATA_TYPES.join(" / ") +
        (ALLOW_LINK_DATA_TYPES.length > 1 ? " のいずれか" : " ") +
        "である必要があります",
      type: "error",
    });
    return;
  }

  let currentLinkKey;
  if (linkItem.value) {
    // NOTE: すでに選択されているもので上書きする
    currentLinkKey = Object.keys(editItem.value.params)
      .filter((key) => key.startsWith("actions"))
      .find((key) => editItem.value.params[key].data === linkItem.value.dataId);
    if (currentLinkKey) {
      clearLinkItem();
    }
  }

  let { actionCount } = editItem.value.params;
  if (!currentLinkKey && actionCount === 4) {
    $q.notify({
      message: "アクションの数が上限に達しているため関連付けできません",
      type: "error",
    });
    return;
  }

  // console.log("link ->", elem);
  linkItem.value = elem;

  to.setAttribute("filter", "url(#linknode)");
  const targetDataId = linkItem.value.dataId;
  if (currentLinkKey) {
    editItem.value.params[currentLinkKey] = {
      ...editItem.value.params[currentLinkKey],
      label: linkItem.value.nameLBD,
      text: linkItem.value.nameLBD,
      type: "postback",
      data: targetDataId,
    };
  } else {
    actionCount++;
    editItem.value.params.actionCount = actionCount;
    editItem.value.params[`actions.${actionCount - 1}`] = {
      ...activeItem.value.params[`actions.${actionCount - 1}`],
      label: linkItem.value.nameLBD,
      text: linkItem.value.nameLBD,
      type: "postback",
      data: targetDataId,
    };
  }
  // console.log("updated activeItem:", editItem.value);
  editItem.value = { ...editItem.value, branchIndex: actionCount - 1 };
};

const onDoubleClickNode = (elem: any, event: any): void => {
  // console.log("double click element", elem, event);
  if (event && event.stopPropagation) {
    event.stopPropagation();
  }
  toggleExpand(elem, event);
};

const onClickSVGContainer = (): void => {
  clearNodeClickHandler();
};

const stopProperties = (value: any): void => {
  editProperties.value = value;
  editItem.value = value ? activeItem.value : null;
  editProperties.value = false;
  //clearActiveItem();
  //clearLinkItem();
};

const cancelDrawer = (value: any): void => {
  editProperties.value = value;
  editItem.value = value ? activeItem.value : null;
  editProperties.value = false;
  clearActiveItem();
  clearLinkItem();
};

const updateEditState = (value: any): void => {
  isChangingEditItem.value = value;
};

const saveTalkMessagesIntoMindmap = (talkNameToSave: any): void => {
  //Get the messages that belong to a talk and save into mindmap

  //Fetch the talk (list of user/bot messages)
  const talkValue = scenarioTalks.value.find((obj: any) => {
    return obj.params.name === talkNameToSave;
  });

  if (talkValue) {
    const listOfIds:any[] = [];

    //Get all the ids of bot messages
    //This will become legacy code after importing from lbd is deprecated
    //Need it for existing scenarios
    talkValue.params.messages.forEach((message: any) => {
      if (message.sender === "BOT") {
        listOfIds.push(message.messageId);
      }
    });

    //Filter through the messages to look for a talk property
    //This is used when messages are created through a mindmap (ie, not import)
    scenarioMessages.value.forEach((message: any) => {
      if ("talk" in message && message.talk === talkNameToSave) {
        listOfIds.push(message.dataId);
      }
    });

    const messages = scenarioMessages.value.filter((message: any) => {
      return listOfIds.includes(message.dataId);
    });
    const payload = {
      versionName: versionId.value,
      value: messages,
      valueName: talkNameToSave,
    };

    updateMindMapMessages(payload);
  }
};

const fetchProperties = async (force: any): Promise<void> => {
  isLoading.value = true;
  const payload = {
    scenarioId: scenarioId.value,
    versionId: versionId.value,
  };
  await getDataForDetails(payload);

  if (force || !(versionId.value in scenarioMindmap.value)) {
    const textMapPayload = {
      versionName: versionId.value,
      valueName: "textMapping",
      value: (validScenarioTextMap.value || scenarioTextMap.value).textMapping,
    };
    await updateMindMapMessages(textMapPayload);

    saveTalkMessagesIntoMindmap(talkName.value);
  }

  if (talkName.value in scenarioMindmap.value[versionId.value]) {
    await buildMindMapFromNewTalk();
  } else {
    mindMapErrorMessage.value = "「" + talkName.value + "」というトークを見つけられませんでした。";
    loadingMindMap.value = false;
  }
  isLoading.value = false;
};

const onResizeWindow = (): void => {
  if (!svgContainer.value) {
    // NOTE: we need fallback to avoid blank page.
    svgHeight.value = 500;
  }
  else {
    svgHeight.value = svgContainer.value.clientHeight;
  }
  windowWidth.value = window.innerWidth;
};

// const toggleRootMessageDrawer = (open: any = null): boolean => {
//   try {
//     if (open === true) {
//       // $refs["root-drawer"].$el.classList.remove("drawer-shrink"); - 元コード honda
//       rootDrawer.value?.classList.remove("drawer-shrink");
//       return true;
//     }
//     if (open === false) {
//       // $refs["root-drawer"].$el.classList.add("drawer-shrink");
//       rootDrawer.value.classList.add("drawer-shrink");
//       return true;
//     }
//     // $refs["root-drawer"].$el.classList.toggle("drawer-shrink");
//     rootDrawer.value.classList.toggle("drawer-shrink");
//     return true;
//   } catch (e: any) {
//     console.warn("root-drawer has not been rendered:", e);
//   }
//   return false;
// };

// const togglePropertyDrawer = (open: any = null): boolean => {
//   try {
//     if (open === true) {
//       // $refs["property-drawer"].$el.classList.remove("drawer-shrink");
//       rootDrawer.value?.classList.remove("drawer-shrink");
//       return true;
//     }
//     if (open === false) {
//       // $refs["property-drawer"].$el.classList.add("drawer-shrink");
//       rootDrawer.value.classList.add("drawer-shrink");
//       return true;
//     }
//     // $refs["property-drawer"].$el.classList.toggle("drawer-shrink");
//     rootDrawer.value.classList.toggle("drawer-shrink");
//     return true;
//   } catch (e: any) {
//     console.warn("property-drawer has not been rendered:", e);
//   }
//   return false;
// };

const onSvgContainerMouseDown = (event: any): void => {
  if (event.target.id !== "svgElem") {
    return;
  }

  if (event.targetTouches && event.targetTouches.length > 0) {
    const [touch] = event.targetTouches;
    lastClickPosition.value = {
      x: touch.screenX,
      y: touch.screenY,
    };
  } else {
    lastClickPosition.value = {
      x: event.screenX,
      y: event.screenY,
    };
  }
  // console.log("onSvgContainerMouseDown:", lastClickPosition.value);
};

const onSvgContainerMouseUp = (event: any): void => {
  if (event.target.id !== "svgElem") {
    return;
  }

  let currentPosition;
  if (event.targetTouches && event.targetTouches.length > 0) {
    const [touch] = event.targetTouches;
    currentPosition = {
      x: touch.screenX,
      y: touch.screenY,
    };
  } else {
    currentPosition = {
      x: event.screenX,
      y: event.screenY,
    };
  }
  // console.log("onSvgContainerMouseUp:", lastClickPosition.value, "->", currentPosition);

  if (isEqual(currentPosition, lastClickPosition.value)) {
    if (editProperties.value && isChangingEditItem.value) {
      $q.dialog({
        title: "アクションの編集を終了してもよろしいですか？",
        message: "行った変更は破棄されます。",
        ok: {
          label: 'はい',
        },
        cancel: true,
      }).onOk(() => {
        isChangingEditItem.value = false;
        editItem.value = null;
        editProperties.value = false;
        clearActiveItem();
        clearLinkItem();
        unmarkDeleteNode();
        unhighightLinks();
      });
    } else {
      editItem.value = null;
      editProperties.value = false;
      clearActiveItem();
      clearLinkItem();
      unmarkDeleteNode();
      unhighightLinks();
    }
  }
};

const onClickSaveButton = (): void => {
  const talk = getTalkFromName()
  const originalTalkId = talk?.dataId || "";
  newTalkUUID.value = TEMPLATE_TALK_IDS.includes(originalTalkId) ? originalTalkId : generateUUID();
  if (route.query.specialFlow === 'damage-report') {
    newTalkUUID.value = originalTalkId || newTalkUUID.value
    // create damage report scenario
    const payload = {
      scenarioId: scenarioId.value,
      versionId: versionId.value,
      name: talkName.value,
      scenario: scenarioId.value + "#" + versionId.value,
      messages: scenarioMindmapMessages.value,
      generations: mindMapGenerations.value,
      onSaveDeleteTargetIds: onSaveDeleteTargetIds.value || [],
      newTalkId: newTalkUUID.value,
      action: 'create'
    };
    scenariosStore.CreateDamageReportTalkVersion(payload);
  }
  else if (talk?.versionOf === "damage-report") {
    newTalkUUID.value = originalTalkId || newTalkUUID.value
    // update damage report scenario
    const payload = {
      scenarioId: scenarioId.value,
      versionId: versionId.value,
      name: talkName.value,
      scenario: scenarioId.value + "#" + versionId.value,
      messages: scenarioMindmapMessages.value,
      generations: mindMapGenerations.value,
      onSaveDeleteTargetIds: onSaveDeleteTargetIds.value || [],
      newTalkId: newTalkUUID.value,
      action: 'save'
    };
    scenariosStore.CreateDamageReportTalkVersion(payload);
  }
  else {
    // normal
    const payload = {
      scenarioId: scenarioId.value,
      versionId: versionId.value,
      name: talkName.value,
      scenario: scenarioId.value + "#" + versionId.value,
      messages: scenarioMindmapMessages.value,
      generations: mindMapGenerations.value,
      onSaveDeleteTargetIds: onSaveDeleteTargetIds.value || [],
      newTalkId: newTalkUUID.value,
      action: 'save'
    };
    saveTalkNodes(payload);
  }
};

const onClickDeleteButton = (): any => {
  //console.log("onClickDeleteButton");
  if (!editItem.value) {
    return;
  }

  //console.log("onClickDeleteButton", "editItem:", editItem.value, "deleteTargets:", deleteTargets.value);

  if (deleteTargets.value?.length > 0) {
    return promptConfirmDeleteNode();
  }

  const nextMindmapMessages = cloneDeep(mindMapGenerations.value).flat();
  // console.log("nextMindmapMessages:", nextMindmapMessages);
  const deleteRootElem = editItem.value;
  // console.log("deleteRootElem:", deleteRootElem);
  const deleteTargetsLocal = deleteElements(nextMindmapMessages, deleteRootElem);
  // console.log("deleteTargets:", deleteTargetsLocal);
  if (deleteTargetsLocal instanceof Error) {
    if (deleteTargetsLocal.message === LINKED_NODE_DELETE_ERROR) {
      $q.notify({ message: "関連付けを含むノードは削除できません", type: "error" });
      return;
    }
    // NOTE: ここには来ないはずだが未知のErrorが返ってきたときのために定義
    $q.notify({ message: "削除範囲の検出に失敗しました", type: "error" });
    return;
  }
  deleteTargets.value = deleteTargetsLocal;

  markDeleteNode();
  $q.notify({ message: "もう一度削除ボタンをクリックすると削除します", type: "warn" });
};

const markDeleteNode = (): void => {
  if (Array.isArray(deleteTargets?.value)) {
    deleteTargets.value.forEach((deleteTarget) => {
      const selector = `#node\\.${deleteTarget.mindmapId}`; // escape the dot
      const element = document.querySelector(selector);
      if (element) {
        element.classList.add("delete-target");
      }
    });
  }
};

const unmarkDeleteNode = (): void => {
  const nodes = document.querySelectorAll(".delete-target");
  nodes.forEach((node) => {
    node.classList.remove("delete-target");
  });

  deleteTargets.value = [];
};


const promptConfirmDeleteNode = (): void => {
  if (deleteTargets.value.length === 0) {
    return;
  }

  $q.dialog({
    title: "削除確認",
    message: "選択されたノードを削除しますか？",
    cancel: {
      push: true,
      label: "いいえ",
      color: "primary",
      icon: "close",
    },
    ok: {
      push: true,
      label: "はい",
      color: "negative",
      icon: "delete",
    }
  }).onOk(() => {
    deleteNodes();
  });
};

const deleteNodes = (): void => {

  //console.log("deleteNodes", "confirmed, length", deleteTargets.value?.length);

  if (deleteTargets.value?.length === 0) {
    return;
  }

  previousState.value = {
    scenarioMindmapMessages: cloneDeep(scenarioMindmapMessages.value),
    onSaveDeleteTargetIds: cloneDeep(onSaveDeleteTargetIds.value),
    validTextMappings: cloneDeep(validTextMappings.value),
  };

  const targetBotNodeDataIds = cloneDeep(deleteTargets.value)
    .filter((target: any) => target.dataId)
    .map((target: any) => target.dataId);

  const nextScenarioMindmapMessages = cloneDeep(scenarioMindmapMessages.value).filter(
    (message: any) => !targetBotNodeDataIds.includes(message.dataId)
  );
/*   console.log(
    "nextScenarioMindmapMessages",
    "before:",
    cloneDeep(scenarioMindmapMessages.value),
    cloneDeep(nextScenarioMindmapMessages)
  );
  console.log(
    "nextScenarioMindmapMessages",
    "after:",
    cloneDeep(scenarioMindmapMessages.value),
    cloneDeep(nextScenarioMindmapMessages)
  ); */

  const firstChildNode = cloneDeep(deleteTargets.value).find((node: any) => node.parentId === 0);
  // console.log("firstChildNode:", cloneDeep(firstChildNode));
  if (firstChildNode) {
    const firstChildNodeIndex = targetBotNodeDataIds.findIndex((dataId: any) => dataId === firstChildNode.dataId);
    targetBotNodeDataIds.splice(firstChildNodeIndex, 1);
    const cleanFirstNode = {
      ...firstChildNode,
      dataType: "__INITIAL__",
      nameLBD: "",
      newMessage: true,
      params: {
        text: "",
      },
    };
    delete cleanFirstNode.expandNode;
    // console.log("first child node detected, add new node:", cleanFirstNode);
    nextScenarioMindmapMessages.push(cleanFirstNode);
  }

  const nextValidTextMappings = cloneDeep(validTextMappings.value);
  Object.keys(nextValidTextMappings).forEach((key) => {
    if (targetBotNodeDataIds.includes(nextValidTextMappings[key])) {
      delete nextValidTextMappings[key];
    }
  });
  // console.log("nextValidTextMappings:", cloneDeep(nextValidTextMappings));

  const deleteOriginNode = cloneDeep(deleteTargets.value).shift();
  // console.log("deleteOriginNode:", cloneDeep(deleteOriginNode));
  const currentMindMapFlatGenerations = cloneDeep(mindMapGenerations.value).flat();
  const parentUserNode = currentMindMapFlatGenerations.find((node: any) => node.mindmapId === deleteOriginNode.parentId);
  // console.log("parentUserNode:", cloneDeep(parentUserNode));
  if (parentUserNode && parentUserNode.type === "postback") {
    const parentBotNode = currentMindMapFlatGenerations.find((node: any) => node.mindmapId === parentUserNode.parentId);
    // console.log("parentBotNode:", cloneDeep(parentBotNode));
    const parentBotNodeIndex = nextScenarioMindmapMessages.findIndex(
      (node: any) => node.dataId === parentBotNode.dataId
    );
    if (parentBotNodeIndex > -1) {
      // console.log("nextScenarioMindmapParentBotNode:", cloneDeep(nextScenarioMindmapMessages[parentBotNodeIndex]));
      //For BubbleFlex Messages On Delete
      deletePostbackActionFromBubbleFlexParams(nextScenarioMindmapMessages[parentBotNodeIndex].params, deleteOriginNode);
      let targetKey;
      if (nextScenarioMindmapMessages[parentBotNodeIndex].params.columnCount) {
        targetKey = `action.${parentUserNode.carouselIndex}.${
          parentUserNode.branchIndex -
          nextScenarioMindmapMessages[parentBotNodeIndex].params.columnCount * parentUserNode.carouselIndex
        }`;
      } else if (nextScenarioMindmapMessages[parentBotNodeIndex].params.actionCount) {
        targetKey = `actions.${parentUserNode.branchIndex}`;
      } else if (
        nextScenarioMindmapMessages[parentBotNodeIndex].params.actionLeft ||
        nextScenarioMindmapMessages[parentBotNodeIndex].params.actionRight
      ) {
        targetKey = parentUserNode.branchIndex === 0 ? "actionLeft" : "actionRight";
      }
      if (targetKey && nextScenarioMindmapMessages[parentBotNodeIndex].params[targetKey]) {
        nextScenarioMindmapMessages[parentBotNodeIndex].params[targetKey].data = "";
        nextScenarioMindmapMessages[parentBotNodeIndex].params[targetKey]._shouldSetDataId = true;
      }
      // console.log("nextScenarioMindmapParentBotNode:", cloneDeep(nextScenarioMindmapMessages[parentBotNodeIndex]));
    }
  }
  // console.log("nextScenarioMindmapMessages", "filtered:", cloneDeep(nextScenarioMindmapMessages));

  onSaveDeleteTargetIds.value.push(...targetBotNodeDataIds);
  // console.log("targetBotNodeDataIds:", cloneDeep(targetBotNodeDataIds));

  validTextMappings.value = cloneDeep(nextValidTextMappings);
  scenarioMindmap.value[versionId.value].textMapping = cloneDeep(nextValidTextMappings);

  scenarioMindmap.value[versionId.value][talkName.value] = cloneDeep(nextScenarioMindmapMessages);
  scenarioMindmapMessages.value = cloneDeep(nextScenarioMindmapMessages);

  nextState.value = null;

  editProperties.value = false;
  unmarkDeleteNode();
  reRenderSVG();
};

const onClickUndoButton = (): void => {
  nextState.value = {
    scenarioMindmapMessages: cloneDeep(scenarioMindmapMessages.value),
    onSaveDeleteTargetIds: cloneDeep(onSaveDeleteTargetIds.value),
    validTextMappings: cloneDeep(validTextMappings.value),
  };
  // console.log("onClickUndoButton:", previousState.value);
  replaceState(previousState.value);
  previousState.value = null;

  editProperties.value = false;
  reRenderSVG();
};

const onClickRedoButton = (): void => {
  previousState.value = {
    scenarioMindmapMessages: cloneDeep(scenarioMindmapMessages.value),
    onSaveDeleteTargetIds: cloneDeep(onSaveDeleteTargetIds.value),
    validTextMappings: cloneDeep(validTextMappings.value),
  };
  // console.log("onClickRedoButton:", nextState.value);
  replaceState(nextState.value);
  nextState.value = null;

  editProperties.value = false;
  reRenderSVG();
};

const replaceState = (state: any): void => {
  validTextMappings.value = cloneDeep(state.validTextMappings);
  scenarioMindmap.value[versionId.value].textMapping = cloneDeep(state.validTextMappings);
  scenarioMindmap.value[versionId.value][talkName.value] = cloneDeep(state.scenarioMindmapMessages);
  scenarioMindmapMessages.value = cloneDeep(state.scenarioMindmapMessages);
  onSaveDeleteTargetIds.value = cloneDeep(state.onSaveDeleteTargetIds || []);
};

const reRenderSVG = (): void => {
  clearNodeClickHandler();
  clearLinkItem();
  clearActiveItem();
  deleteMindMapChildren();
  buildTheMindMap();
};

const onWindowKeyDown = (event: any): any => {
  // if (event.path?.find((path: any) => path.id === "drawer-content")) {
  //   // NOTE: keydown on drawer textarea. we should ignore event
  //   return true;
  // }

  // const isPressCtrl = isMacOS.value ? event.metaKey : event.ctrlKey;

  // if (isPressCtrl && event.key === "c") {
  //   return onCopy(event);
  // }
  // if (isPressCtrl && event.key === "v") {
  //   return onPaste(event);
  // }
  // if (isPressCtrl && event.key === "z") {
  //   return onClickUndoButton();
  // }
  // if (isPressCtrl && event.key === "y") {
  //   return onClickRedoButton();
  // }
};

const onCopy = (event: any): void => {
  // console.log("onCopy:", event);
  if (!activeItem.value) {
    return;
  }
  if (!activeItem.value.dataType) {
    $q.notify({ message: "アクションノード以外はコピーできません", type: "error" });
  }
  const copiedItemsLocal = [[cloneDeep(activeItem.value)]];
  const generations = cloneDeep(mindMapGenerations.value);
  for (let generation = activeItem.value.generation + 1; generation < generations.length; generation++) {
    const childCopyTargets:any[] = [];
    const childGeneration = cloneDeep(mindMapGenerations.value[generation]);
    copiedItemsLocal[copiedItemsLocal.length - 1].forEach((parentItem) => {
      const nextCopyTargets = childGeneration.filter((gen: any) => gen.parentId === parentItem.mindmapId);
      // console.log(parentItem, nextCopyTargets);
      if (nextCopyTargets.length > 0) {
        childCopyTargets.push(...nextCopyTargets);
      }
    });
    copiedItemsLocal.push(childCopyTargets);
  }
  // console.log("copiedItems:", copiedItemsLocal);
  copiedItems.value = copiedItemsLocal;
};

const onPaste = (event: any): void => {
  // console.log("onPaste:", event);
  if (!activeItem.value || !copiedItems.value) {
    return;
  }

  if (activeItem.value.dataType !== "__INITIAL__") {
    $q.notify({ message: "設定前のアクション以外にはペーストできません", type: "error" });
    return;
  }

  // TODO: paste Item
  previousState.value = {
    scenarioMindmapMessages: cloneDeep(scenarioMindmapMessages.value),
    onSaveDeleteTargetIds: cloneDeep(onSaveDeleteTargetIds.value),
    validTextMappings: cloneDeep(validTextMappings.value),
  };

  // NOTE: 複数回ペーストする可能性があるので、ここで新しく配列を作ってdataIdを生成し直す
  const filteredBotNodes = cloneDeep(copiedItems.value)
    .flat()
    .filter((node: any) => node.dataType);

  // NOTE: 新しいdataIdの転置インデックス
  const newDataIdIndex = {} as any;
  filteredBotNodes.forEach((node: any) => {
    const newDataId = generateUUID();
    newDataIdIndex[node.dataId] = newDataId;
    node.dataId = newDataId;
  });

  filteredBotNodes.forEach((node: any) => {
    node.nameLBD = (node.nameLBD || "新規メッセージ") + "のコピー";

    if (node.params.actionLeft) {
      node.params.actionLeft.data = newDataIdIndex[node.params.actionLeft.data] || node.params.actionLeft.data;
    }
    if (node.params.actionRight) {
      node.params.actionRight.data = newDataIdIndex[node.params.actionRight.data] || node.params.actionRight.data;
    }
    if (node.params.actionCount) {
      if (node.dataType === "buttons") {
        for (let i = 0; i < node.params.actionCount; i++) {
          if (node.params[`actions.${i}`].type === "postback") {
            node.params[`actions.${i}`].data =
              newDataIdIndex[node.params[`actions.${i}`].data] || node.params[`actions.${i}`].data;
          }
        }
      }
      if (node.dataType === "carousel") {
        for (let j = 0; j < node.params.columnCount; j++) {
          for (let i = 0; i < node.params.actionCount; i++) {
            if (node.params[`action.${j}.${i}`].type === "postback") {
              node.params[`action.${j}.${i}`].data =
                newDataIdIndex[node.params[`action.${j}.${i}`].data] || node.params[`action.${j}.${i}`].data;
            }
          }
        }
      }
    }

    delete node.generation;
    delete node.parentId;
    delete node.mindmapId;
    delete node.branchIndex;
  });

  // console.log("filteredBotNodes:", filteredBotNodes);

  const parentUserNode = mindMapGenerations.value[activeItem.value.generation - 1].find(
    (node: any) => node.mindmapId === activeItem.value.parentId
  );
  if (!parentUserNode) {
    throw new Error("invalid state on parentUserNode");
  }
  // console.log("parentUserNode:", parentUserNode);

  const parentBotNode = mindMapGenerations.value[parentUserNode.generation - 1].find(
    (node: any) => node.mindmapId === parentUserNode.parentId
  );
  if (!parentBotNode) {
    throw new Error("invalid state on parentBotNode");
  }
  // console.log("parentBotNode:", parentBotNode);

  let parentBranchParamKey;
  switch (parentBotNode.dataType) {
    case "buttons":
      parentBranchParamKey = `actions.${parentUserNode.branchIndex}`;
      break;
    case "confirm":
      parentBranchParamKey = parentUserNode.branchIndex === 0 ? "actionLeft" : "actionRight";
      break;
    case "carousel":
      parentBranchParamKey = `action.${parentUserNode.carouselIndex}.${
        parentUserNode.branchIndex - parentBotNode.params.actionCount * parentUserNode.carouselIndex
      }`;
      break;
  }
  // console.log("parentBranchParamKey:", parentBranchParamKey);
  // console.log("parentBotNode.params[parentBranchParamKey]:", parentBotNode.params[parentBranchParamKey ?? '']);

  let nextScenarioMindmapMessages = cloneDeep(scenarioMindmapMessages.value);
  const nextValidTextMappings = cloneDeep(validTextMappings.value);
  if (parentBranchParamKey) {
    if (parentBotNode.params[parentBranchParamKey].type === "postback") {
      parentBotNode.params[parentBranchParamKey].data = filteredBotNodes[0].dataId;
      nextScenarioMindmapMessages = nextScenarioMindmapMessages.filter(
        (message: any) => message.dataId !== parentBotNode.dataId
      );
      nextScenarioMindmapMessages.push(parentBotNode);
    } else if (parentBotNode.params[parentBranchParamKey].type === "message") {
      nextValidTextMappings[parentBotNode.params[parentBranchParamKey].text] = filteredBotNodes[0].dataId;
    }
  }
  nextScenarioMindmapMessages.push(...filteredBotNodes);

  validTextMappings.value = cloneDeep(nextValidTextMappings);
  scenarioMindmap.value[versionId.value].textMapping = cloneDeep(nextValidTextMappings);
  scenarioMindmap.value[versionId.value][talkName.value] = cloneDeep(nextScenarioMindmapMessages);
  scenarioMindmapMessages.value = cloneDeep(nextScenarioMindmapMessages);
  nextState.value = null;

  editProperties.value = false;
  reRenderSVG();
};

const localDataTypeUpdate = (dataType: any): void => {
  editItemDataType.value = dataType;
};


// watch
watch(
  () => route,
  async () => {
    await onMount(true);
  }
);

watch(
  () => mindMapErrorMessage.value, 
  (val) => {
    if (val) {
      $q.notify({ message: val, type: "error" });
    }
  }
);

watch(
  () => isSavingTalkNodes.value, 
  (val, old) => {
    loadingMindMap.value = val;

    const handleNavigationAndDataFetch = async () => {
      if (!val && saveTalkNodesSuccess.value) {
        scenarioMindmapSnapshot.value = cloneDeep(scenarioMindmap.value);

        if (route.query.new === "true") {
          await router.replace({
            name: "ScenarioMindmapPage",
            params: {
              scenarioId: scenarioId.value,
              versionId: versionId.value,
              talkName: talkName.value,
              env: "sandbox",
            },
          });
        } else if (getTalkIdFromName()) {
          await router.push({
            name: "ScenarioSettingsDetailPage",
            params: {
              env: "sandbox",
              scenarioId: route.params.scenarioId,
              versionId: route.params.versionId,
              talkId: newTalkUUID.value,
            },
          });
        } else {
          await router.push({
            name: "ScenarioSettingsPage",
            params: {
              env: "sandbox",
            }
          });
        }
        // #2NF: put inside if to prevent inf loop
        await onMount()
      }
    };

    handleNavigationAndDataFetch().catch(error => console.error(error));
  }
);


watch(
  () => saveTalkNodesError.value, 
  (val) => {
    $q.notify({ message: val ?? '', type: "error" });
  }
);

watch(
  () => scenarioTalks.value, 
  () => {
    displayTalkOptions.value = talkOptions();
  }
);

// watch(
//   () => scenarioMindmapMessages.value, 
//   (val) => {
//     scenarioMindmapMessages = val;
//   }
// );

// watch(
//   () => scenarioMindmap.value, 
//   (val) => {
//     scenarioMindmap.value = val;
//   }
// );

watch(
  () => editItem.value, 
  (val) => {
    editItemDataType.value = val ? val.dataType : null;
  }
);

// hooks
onMounted(async () => {
  isLoading.value = true;
  await onMount();
  document.body.classList.add("lbd-web");
  if (svgContainer.value) {
    svgContainer.value.addEventListener("mousedown", onSvgContainerMouseDown);
    svgContainer.value.addEventListener("touchstart", onSvgContainerMouseDown);
    svgContainer.value.addEventListener("mouseup", onSvgContainerMouseUp);
    svgContainer.value.addEventListener("touchend", onSvgContainerMouseUp);
  }
 
  window.addEventListener("resize", onResizeWindow);
  window.addEventListener("keydown", onWindowKeyDown);
  onResizeWindow();
  isLoading.value = false;
});


onBeforeUnmount(() => {
  document.body.classList.remove("lbd-web");
  svgContainer.value.removeEventListener("mousedown", onSvgContainerMouseDown);
  svgContainer.value.removeEventListener("touchstart", onSvgContainerMouseDown);
  svgContainer.value.removeEventListener("mouseup", onSvgContainerMouseUp);
  svgContainer.value.removeEventListener("touchend", onSvgContainerMouseUp);
  window.removeEventListener("resize", onResizeWindow);
  window.removeEventListener("keydown", onWindowKeyDown);
});


onBeforeMount(() => {
  if (!activeScenario.value.scenarioId) {
    scenariosStore.fetchAllScenarios();
  }
  displayTalkOptions.value = talkOptions();
  scenarioMindmapSnapshot.value = cloneDeep(scenarioMindmap.value);
  // TODO: - honda
  // onResizeWindow = onResizeWindow.bind(this);
  // onSvgContainerMouseDown = onSvgContainerMouseDown.bind(this);
  // onSvgContainerMouseUp = onSvgContainerMouseUp.bind(this);
  // onWindowKeyDown = onWindowKeyDown.bind(this);
  talkName.value = route.params.talkName.toString();
});


onBeforeRouteLeave((to, from, next) => {
  if (!isEqual(scenarioMindmapSnapshot.value, scenarioMindmap.value)) {
    $q.dialog({
      title: "このページを離れてもよろしいですか？",
      message: "行った変更は破棄されます。",
      ok: {
        label: "このページを離れる",
      },
    }).onOk(() => {
      //When leaving an unsaved talk,
      //Delete the talk from mindmap and delete any pending text mappings
      // let tempMindMap = cloneDeep(scenarioMindmap.value);
      // delete tempMindMap[versionId][talkName.value];
      //
      // mindMapGenerations.forEach(generation => {
      //   generation.forEach(node => {
      //     if (node.type === 'message' && !('messageLinkToOtherTalk' in node)) {
      //       delete tempMindMap[versionId].textMapping[node.value];
      //     }
      //   })
      // })

      updateMindMap(cloneDeep(scenarioMindmapSnapshot));
      next();
    });
  } else {
    next();
  }
});

// #region damage-report
function getTalkFromName() {
  const talk = scenarioTalks.value.find(elem => elem.params.name === talkName.value);
  return talk;
}
const damageReportTalkVersion = computed((): any => {
  return scenarioTalks.value?.filter(x => x.versionOf === 'damage-report' || x.dataId === 'DAMAGE_REPORT_TALK')?.map(x => {
    return {
      ...x,
      value: x.dataId,
      text: x.params?.name,
      versionId: x.versionId
    }
  });
})
const isCustomDamageReportTalk = computed((): any => {
  return talkName.value!= "損傷報告"  && damageReportTalkVersion.value.map(x=>x.text).includes(talkName.value)
})
// #endregion
</script>
