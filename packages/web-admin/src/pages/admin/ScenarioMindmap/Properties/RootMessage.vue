<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-card style="height: 100%; display: flex; flex-direction: row; overflow: hidden">
    <div style="display: flex; align-items: center">
      <div class="drawer-shrink-toggle-icon">
        <q-btn style="min-width: 0; padding: 0 0.5em" flat rounded @click="closeModal">
          <q-icon name="mdi-chevron-right"></q-icon>
        </q-btn>
      </div>
    </div>
    <div style="flex: 1; display: flex; height: 100%; flex-direction: column; overflow: hidden">
      <q-toolbar flat style="flex: 0">
        <q-btn
          color="primary"
          elevation="4"
          @click="
            permissionHelper.hasActionPermission('click', 'backendRequest') ? onUpdateItemProperties() : permissionHelper.showActionPermissionError()
          "
          :disable="!canSave"
          style="flex: 1; margin: 0 12px 0 0"
        >
          反映する
        </q-btn>
        <q-btn elevation="4" @click="closeModal" style="flex: 1"> キャンセル </q-btn>
      </q-toolbar>
      <div id="drawer-content" style="flex: 1; overflow-y: auto">
        <q-card-section class="tw-m-0 tw-px-3 bot-designer-item-properties">
          <q-card class="tw-p-4" outlined>
            <div class="row">
              <label>開始メッセージ</label>

              <q-input
                v-model="modelLocal.value"
                class="col-12"
                outlined
                dense
                @update:model-value="onChangeName($event)"
                :rules="[rules.validTextLength, rules.checkDuplicate]"
              >
              </q-input>
            </div>
          </q-card>
        </q-card-section>
      </div>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { useScenariosStore } from '@/stores/modules/scenarios';
import { PropType, ref,  computed, watch, onBeforeMount } from 'vue';
import { cloneDeep } from "lodash";
import { usePermissionHelper } from '@/mixins/PermissionHelper';

// old imports
// 旧インポート
/*import { SET_SCENARIO_MINDMAP_MESSAGES } from "@/store/mutation-types";
import OriginActionProperty from "@/components/BotDesigner/CommonProperties/ActionProperty.vue";
*/

// store
const scenariosStore = useScenariosStore();

// other
const permissionHelper = usePermissionHelper();

// emits 
const emits = defineEmits<{
  (event: 'updateEditState', payload: any): void;
  (event: 'stopProperties', payload: any): void;
  (event: 'redrawFromRoot', payload: any): void;
}>();
// (event: 'onClickToggleButton'): void;

// props
const props = defineProps({
  model: Object as PropType<any>,
  scenarioId: String as PropType<string>,
  versionId: String as PropType<string>,
  editProperties: Boolean as PropType<boolean>,
});

// data
const modelLocal = ref<any>(null);
const originalModel = ref<any>(null);
const canSave = ref<boolean>(false);
const rules = ref<any>({});

// methods
const updateMindMapMessages = scenariosStore.setScenarioMindmapMessages;
const updateSaveStatus = (value: any): void => {
  emits("updateEditState", value);
  canSave.value = value;
};

const onChangeName = (event: any): void => {
  updateSaveStatus(event.length <= 400);
};

const closeModal = (): void => {
  modelLocal.value = originalModel.value;
  updateSaveStatus(false);
  emits("stopProperties", false);
};

const onUpdateItemProperties = async (): Promise<void> => {
  let oldState = scenarioMindmap.value[props.versionId ?? '']["textMapping"];
  let old_key = Object.keys(oldState).find((key) => oldState[key] === modelLocal.value.rootId);
  let new_key = modelLocal.value.value;
  let _newState = cloneDeep(oldState);
  delete _newState[old_key ?? ''];

  let newState = { ..._newState, [new_key]: modelLocal.value.rootId };
  var payload = {
    versionName: props.versionId,
    valueName: "textMapping",
    value: newState,
  };
  await updateMindMapMessages(payload);
  // console.log(scenarioMindmap.value[props.versionId ?? '']["textMapping"]);
  updateSaveStatus(false);
  emits("stopProperties", false);

  emits("redrawFromRoot", new_key);
};

// computed
const scenarioTextMap = computed(() => scenariosStore.scenarioTextmap);
const scenarioMessages = computed(() => scenariosStore.scenarioMessages);
const scenarioMindmap = computed(() => scenariosStore.scenarioMindmap);
const maxWidthDialog = computed(() => {
      return 800;
    });

// watch
watch(
  () => props.model, 
  (value) => {
    canSave.value = false;
    modelLocal.value = cloneDeep(value);
    originalModel.value = cloneDeep(modelLocal.value);
  }
);

// hooks
onBeforeMount(() => {
  modelLocal.value = cloneDeep(props.model) || {};
  originalModel.value = cloneDeep(props.model) || {};
  rules.value = {
    validTextLength: (value: any) => {
      if (!value || value.trim().length === 0) {
        canSave.value = false;
        return "必須";
      }
      if (value && value.length > 400) {
        return "400文字の制限";
      } else {
        return true;
      }
    },
    checkDuplicate: (value: any) => {
      if (value && originalModel.value.value === value) {
        canSave.value = false;
        return true;
      }
      if (value && scenarioTextMap.value.textMapping[value]) {
        canSave.value = false;
        return "別のトークのメッセージアクションと重複しています";
      }
      if (value && !scenarioTextMap.value.textMapping[value]) {
        return true;
      }
    }
  };
});

</script>

<style lang="less">
.bot-designer-item-properties {
  label {
    margin-bottom: 5px;

    display: inline-block;
  }
}
</style>
