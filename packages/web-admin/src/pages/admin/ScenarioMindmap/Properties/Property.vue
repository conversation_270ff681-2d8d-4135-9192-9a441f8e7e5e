<style scoped>
 .no-padding-top {
   padding-top: 0 !important;
 }
</style>
<template>
  <q-card :max-width="maxWidthDialog" style="height: 100%; display: flex; flex-direction: row; overflow: hidden">
    <div style="display: flex; align-items: center">
      <div class="drawer-shrink-toggle-icon">
        <q-btn style="min-width: 0; padding: 0 0.5em" flat rounded @click="closeModal">
          <q-icon name="mdi-chevron-right"></q-icon>
        </q-btn>
      </div>
    </div>
    <div style="flex: 1; display: flex; height: 100%; flex-direction: column; overflow: hidden">
      <q-toolbar flat style="flex: 0" class="tw-flex tw-justify-end">
        <q-space/>
        <q-btn style="margin-right: 1em" outline color="primary" @click="onCancelClick"> キャンセル </q-btn>
        <q-btn
          :disable="permissionHelper.hasActionPermission('disableButton', 'ScenarioSettings_ApplyMapAction_Click') || !canSave"
          color="primary"
          @click="
            permissionHelper.hasActionPermission('click', 'backendRequest') ? onUpdateItemProperties() : permissionHelper.showActionPermissionError()
          "
        >
          適用する
        </q-btn>
      </q-toolbar>
      <div id="drawer-content" style="flex: 1; overflow-y: auto" class="tw-mt-3">
        <div class="tw-px-3" elevation="0">
          <span>メッセージタイプ</span>
          <q-select
            v-model="displayMessageType"
            :options="actionTypesOptions"
            map-options emit-value
            option-label="text"
            option-value="value"
            class="input-nowrap"
            use-input
            dense
            hide-details
            :placeholder="modelLocal.dataType === '__INITIAL__' ? 'メッセージの種類を選択してください' : ''"
            outlined
            single-line
            @update:model-value="changeInLocalModelType(modelLocal.dataType)"
          >
            <template v-slot:option="item">
              <q-item v-bind="item.itemProps">
                <q-item-section avatar>
                  <q-icon :name="item.opt.icon" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ item.opt.text }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>
          </q-select>
        </div>
        <q-card-section class="ma-0 px-3 bot-designer-item-properties">
          <label>メッセージID</label>
          <div class="row">
            <div class="col-8 tw-pb-3 tw-mr-2" style="padding-top: 0">
              <q-input id="message-id"
                v-model="modelLocal.dataId"
                dense
                disabled
                style="background-color: lightgrey"
                hide-details
                outlined
                readonly
                single-line>
              </q-input>
            </div>
            <div style="padding-top: 0">
              <q-btn color="primary" class="tw-mb-3" @click="copyIdToClipboard">コピー</q-btn>
            </div>
          </div>
          <div class="row">
            <label class="col-auto">メッセージ名</label>
            <q-input
              v-model="modelLocal.nameLBD"
              class="col-12"
              :error="modelLocal.nameLBD === ''"
              :rules="[rules.validNonEmpty, rules.validTextLength]"
              dense
              outlined
              hide-details="auto"
              single-line
              @update:model-value="onChangeName(modelLocal.nameLBD)"
            >
            </q-input>
          </div>
          <div class="row" v-if="isCustomDamageReportTalk">
            <div class="col-12">
              <DamageReportFieldRequired 
                :data-id="modelLocal.dataId"
                v-model:targetDataField="modelLocal.targetDataField"
                v-model:targetDataOrder="modelLocal.targetDataOrder"
                v-model:parentDataField="modelLocal.parentDataField" 
                :dataType="modelLocal.dataType">
              </DamageReportFieldRequired>
            </div>
          </div>
          <div class="row q-py-md">
            <div class="col-12">
              <div>
                <div v-if="modelLocal.dataType === 'text'">
                  <ItemTextMessage
                    :branchIndex="branchIndex"
                    :params="checkMessageType"
                    @updateParams="updateParams"
                    @updateSaveStatus="updateSaveStatus"
                  />
                </div>
                <div v-else-if="modelLocal.dataType === 'buttons'">
                  <ItemButtonTemplate
                    :botMessages="botMessages"
                    :branchIndex="branchIndex"
                    :canSave="canSave"
                    :params="checkMessageType"
                    :specialTalk="isSpecialTalk"
                    @updateParams="updateParams"
                    @updateSaveStatus="updateSaveStatus"
                    :dataId="modelLocal.dataId"
                    :targetDataField="modelLocal.targetDataField"
                  />
                </div>
                <div v-else-if="modelLocal.dataType === 'richmenu'">
                  <ItemRichMenu
                    :branchIndex="branchIndex"
                    :params="modelLocal.params"
                    :specialTalk="isSpecialTalk"
                    @updateSaveStatus="updateSaveStatus"
                  />
                </div>
                <div v-else-if="modelLocal.dataType === 'imagemap'">
                  <ItemImageMapMessage
                    :branchIndex="branchIndex"
                    :canSave="canSave"
                    :params="modelLocal.params"
                    :webAppId="modelLocal.dataId"
                    @fileImageMapDataUpdate="fileImageMapDataUpdate"
                    @updateParams="updateModelParams"
                    @updateSaveStatus="updateSaveStatus"
                  />
                </div>
                <div v-else-if="modelLocal.dataType === 'sticker'">
                  <ItemSticker
                    :branchIndex="branchIndex"
                    :params="modelLocal.params"
                    :webAppId="modelLocal.dataId"
                    @updateParams="updateParams"
                    @updateSaveStatus="updateSaveStatus"
                  />
                </div>
                <div v-else-if="modelLocal.dataType === 'location'">
                  <ItemLocation
                    :branchIndex="branchIndex"
                    :canSave="canSave"
                    :params="modelLocal.params"
                    :webAppId="modelLocal.dataId"
                    @updateParams="updateParams"
                    @updateSaveStatus="updateSaveStatus"
                  />
                </div>
                <div v-else-if="modelLocal.dataType === 'confirm'">
                  <ItemConfirmTemplate
                    :botMessages="botMessages"
                    :branchIndex="branchIndex"
                    :canSave="canSave"
                    :params="checkMessageType"
                    :webAppId="modelLocal.dataId"
                    :specialTalk="isSpecialTalk"
                    @updateParams="updateParams"
                    @updateSaveStatus="updateSaveStatus"
                  />
                </div>
                <div v-else-if="modelLocal.dataType === 'image'">
                  <ItemImage
                    :branchIndex="branchIndex"
                    :canSave="canSave"
                    :params="modelLocal.params"
                    :webAppId="modelLocal.dataId"
                    @fileImageDataUpdate="fileImageDataUpdate"
                    @updateParams="updateParams"
                    @updateSaveStatus="updateSaveStatus"
                  />
                </div>
                <div v-else-if="modelLocal.dataType === 'audio'">
                  <ItemAudio
                    :branchIndex="branchIndex"
                    :canSave="canSave"
                    :params="modelLocal.params"
                    :webAppId="modelLocal.dataId"
                    @fileAudioDataUpdate="fileAudioDataUpdate"
                    @updateParams="updateParams"
                    @updateSaveStatus="updateSaveStatus"
                  />
                </div>
                <div v-else-if="modelLocal.dataType === 'video'">
                  <ItemVideo
                    :branchIndex="branchIndex"
                    :canSave="canSave"
                    :params="modelLocal.params"
                    :webAppId="modelLocal.dataId"
                    @fileVideoDataUpdate="fileVideoDataUpdate"
                    @updateParams="updateParams"
                    @updateSaveStatus="updateSaveStatus"
                  />
                </div>
                <div v-else-if="modelLocal.dataType === 'carousel'">
                  <ItemCarouselTemplate
                    :botMessages="botMessages"
                    :branchIndex="branchIndex"
                    :params="modelLocal.params"
                    :webAppId="modelLocal.dataId"
                    :specialTalk="isSpecialTalk"
                    @updateParams="updateParams"
                    :canSave="canSave"
                    @updateSaveStatus="updateSaveStatus"
                    :targetDataField="modelLocal.targetDataField"
                  />
                </div>
                <div v-else-if="modelLocal.dataType === 'compositeMessage'">
                  <ItemCompositeMessage
                    :branchIndex="branchIndex"
                    :params="modelLocal"
                    :messagesToDisplay="botMessages"
                    @updateMessages="updateMessages"
                    @updateSaveStatus="updateSaveStatus"
                  />
                </div>
                <div v-else-if="modelLocal.dataType === 'bubbleFlex'">
                  <ItemBubbleFlex
                    :branchIndex="branchIndex"
                    :params="modelLocal.params"
                    @updateModelParams="updateModelParams"
                    @updateSaveStatus="updateSaveStatus"
                  />
                </div>
                <div v-else-if="modelLocal.dataType === 'carouselFlex'">
                  <ItemCarouselFlex
                    :branchIndex="branchIndex"
                    :params="modelLocal.params"
                    @updateModelBubbles="updateModelBubbles"
                    @updateSaveStatus="updateSaveStatus"
                  />
                </div>
                <div v-else-if="modelLocal.dataType === 'jsonTemplate'">
                  <ItemJsonTemplate
                    :branchIndex="branchIndex"
                    :params="modelLocal.params"
                    @updateModelParams="updateModelParams"
                    @updateSaveStatus="updateSaveStatus"
                  />
                </div>
                <div v-else-if="modelLocal.dataType === '__INITIAL__'">
                  <div />
                </div>
                <div v-else-if="modelLocal.dataType === 'default'">
                  <div class="my-5 pl-6">{{ modelLocal.dataType }} は未対応です</div>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div v-if="!hidePreviewDisplay.includes(modelLocal.dataType)" class="col-12">
              <MessagePreview :messages="[previewMessage]" />
            </div>
          </div>
        </q-card-section>
      </div>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { PropType, ref,  computed, watch, onMounted, onBeforeMount } from 'vue';
import { useQuasar } from 'quasar';
import { useScenariosStore } from '@/stores/modules/scenarios';
import default_messages from "@/constants/default_messages.json";
import { cloneDeep, isEqual } from "lodash";
import {
  addElementToBubbleFlexPostbackActions,
} from "@/services/MindMapService";
import { isNullOrEmpty } from "@/utils/stringUtils";
import {BOT_ITEM_TYPES, SPECIAL_TALK_TYPES, SPECIAL_USER_ACTIONS} from "@/stores/modules/scenarios/scenarios.constants";
import { usePermissionHelper } from '@/mixins/PermissionHelper';

// old imports
// 旧インポート
import ItemTextMessage from "@/components/BotDesigner/ItemTextMessage.vue";
import ItemImageMapMessage from "@/components/BotDesigner/ItemImageMapMessage.vue";
import ItemRichMenu from "@/components/BotDesigner/ItemRichMenu.vue";
import ItemButtonTemplate from "@/components/BotDesigner/ItemButtonTemplate.vue";
import ItemSticker from "@/components/BotDesigner/ItemSticker.vue";
import ItemLocation from "@/components/BotDesigner/ItemLocation.vue";
import ItemConfirmTemplate from "@/components/BotDesigner/ItemConfirmTemplate.vue";
import ItemImage from "@/components/BotDesigner/ItemImage.vue";
import ItemAudio from "@/components/BotDesigner/ItemAudio.vue";
import ItemVideo from "@/components/BotDesigner/ItemVideo.vue";
import ItemCarouselTemplate from "@/components/BotDesigner/ItemCarouselTemplate.vue";
import ItemCompositeMessage from "@/components/BotDesigner/ItemCompositeMessage.vue";
import MessagePreview from "@/pages/admin/ScenarioSettingsDetail/components/MessagePreview.vue";
import ItemBubbleFlex from "@/components/BotDesigner/ItemBubbleFlex.vue";
import ItemCarouselFlex from "@/components/BotDesigner/ItemCarouselFlex.vue";
import ItemJsonTemplate from "@/components/BotDesigner/ItemJsonTemplate.vue";
import DamageReportFieldRequired from "@/components/BotDesigner/DamageReportFieldRequired.vue";


// store
const scenariosStore = useScenariosStore();

// other
const permissionHelper = usePermissionHelper();

// emits 
const emits = defineEmits<{
  (event: 'stopProperties', payload: any): void;
  (event: 'updateScenarioMessage', payload: any, payload2: any, payload3: any): void;
  (event: 'updateTextMapping', payload: any): void;
  (event: 'close'): void;
  (event: 'localDataTypeUpdate', payload: any): void;
  (event: 'updateEditState', payload: any): void;
  (event: 'botMessages', payload: any): void;
  (event: 'cancelDrawer', payload: any): void;
}>();
// (event: 'onClickToggleButton'): void;

const $q = useQuasar();

// props
const props = defineProps({
  model: Object as PropType<any>,
  originModel: Object as PropType<any>,
  talkName: String as PropType<string>,
  scenarioId: String as PropType<string>,
  versionId: String as PropType<string>,
  botMessages: Array as PropType<any>
});

// data
const modelLocal = ref<any>(null);
const hidePreviewDisplay = ref<any>(["jsonTemplate", "compositeMessage", "__INITIAL__"]);
const originModelLocal = ref<any>(null);
const imageMapFiles = ref<any>([]);
const audioFiles = ref<any>([]);
const imageFiles = ref<any>([]);
const videoFiles = ref<any>([]);
const validLocalState = ref<any>({});
const childComponentValidation = ref<any>({});
const rules = ref<any>({});
const defaultMessages = ref<any>(default_messages);
const branchIndex = ref<any>(null);
const updatingMessageForceRender = ref();

// methods
const saveScenarioData = scenariosStore.updateScenarioData;
const deleteScenarioMessage = scenariosStore.deleteScenarioMessage;
const updateMindMapMessages = scenariosStore.setScenarioMindmapMessages;

const copyIdToClipboard = (): void => {
  navigator.clipboard.writeText(modelLocal.value.dataId)
    .then(() => { $q.notify({ message: "クリップボードにコピーしました。" }); });
};

const disableDeleteButton = (): boolean => {
  //Check for message being part of special scenario talk
  if (modelLocal.value.params && "specialScenarioTalk" in modelLocal.value.params) {
    if (!("userCreatedSpecialTalkComposite" in modelLocal.value.params)) {
      return true;
    }
  }

  //Check for message in other composite messages
  let modelInCompositeMessage = false;
  scenarioMessages.value.forEach((msg: any) => {
    if (msg.dataType == "compositeMessage") {
      msg.messages.forEach((dataId: any) => {
        if (modelLocal.value.dataId == dataId) {
          modelInCompositeMessage = true;
        }
      });
    }
  });
  if (modelInCompositeMessage) {
    return true;
  }

  return false;
};

const closeModal = (): void => {
  modelLocal.value = cloneDeep(originModelLocal.value);
  updateSaveStatus(false);
  emits("stopProperties", modelLocal.value);
  updatingMessageForceRender.value = false;
};

const onCancelClick = () => {
  modelLocal.value = cloneDeep(originModelLocal.value);
  updateSaveStatus(false);
  emits("cancelDrawer", modelLocal.value);
  updatingMessageForceRender.value = false;
}
const onUpdateItemProperties = (): void => {
  const oldState = scenarioMindmap.value[props.versionId ?? ''][props.talkName ?? ''];
  // console.log("🚀 ~ const file = store.property.vue ~ line 404 ~ onUpdateItemProperties ~ oldState", oldState);
  const newState = oldState.map((obj: any) => {
    if (obj.dataId === modelLocal.value.dataId) {
      const newState = {
        ...obj,
        dataType: modelLocal.value.dataType,
        nameLBD: modelLocal.value.nameLBD,
        params: modelLocal.value.params,
        // add new fields here
        targetDataField: modelLocal.value.targetDataField || modelLocal.value.dataId,
        targetDataOrder: modelLocal.value.targetDataOrder,
        parentDataField: modelLocal.value.parentDataField,
      };
      if (newState.dataType === "compositeMessage") {
        newState.messages = modelLocal.value.messages;
      }
      if (newState.dataType === "bubbleFlex") {
        addElementToBubbleFlexPostbackActions(newState.params);
      }
      if (newState.params && newState.params.actionCount) {
        if (newState.dataType === "buttons") {
          for (let i = 0; i < newState.params.actionCount; i++) {
            const isPostbackCustomDamageReport = isCustomDamageReportTalk.value &&
                  newState.params[`actions.${i}`].type === 'uri' &&
                  SPECIAL_USER_ACTIONS[newState.params[`actions.${i}`].uri]
            if (
              (newState.params[`actions.${i}`].type === "postback" || isPostbackCustomDamageReport) &&
              isNullOrEmpty(newState.params[`actions.${i}`].data)
            ) {
              if (isPostbackCustomDamageReport) {
                const dataKey = Object.keys(newState.params).find(key => newState.params[key].data)
                if (dataKey) {
                  // case add uri action  => sync actions to first actions founds that has postback data
                  newState.params.linkToDataId = newState.params[dataKey].data
                  newState.params[`actions.${i}`].data = newState.params[dataKey].data
                }
                else {
                  // case new node from scratch
                  newState.params[`actions.${i}`]._shouldSetDataId = true;
                }
              }
              else {
                // default
                newState.params[`actions.${i}`]._shouldSetDataId = true;
              }
            }
            else if (isPostbackCustomDamageReport) {
              // newState.params[`actions.${i}`].data not null => update linkToDataId
              const dataKey = Object.keys(newState.params).find(key => newState.params[key]?.data)
              if (dataKey) {
                newState.params.linkToDataId = newState.params[dataKey].data
              }
            }
          }
        }
        if (newState.dataType === "carousel") {
          for (let j = 0; j < newState.params.columnCount; j++) {
            for (let i = 0; i < newState.params.actionCount; i++) {
              const isPostbackCustomDamageReport = isCustomDamageReportTalk.value &&
                    newState.params[`action.${j}.${i}`].type === 'uri' &&
                    SPECIAL_USER_ACTIONS[newState.params[`action.${j}.${i}`].uri]
              if (
                newState.params[`action.${j}.${i}`].type === "postback" &&
                isNullOrEmpty(newState.params[`action.${j}.${i}`].data)
              ) {
                if (isPostbackCustomDamageReport) {
                  const dataKey = Object.keys(newState.params).find(key => newState.params[key].data)
                  if (dataKey) {
                    newState.params.linkToDataId = newState.params[dataKey].data
                    newState.params[`action.${j}.${i}`].data = newState.params[dataKey].data
                  }
                  else {
                    newState.params[`action.${j}.${i}`]._shouldSetDataId = true;
                  }
                }
                else {
                  newState.params[`action.${j}.${i}`]._shouldSetDataId = true;
                }
              } else if (isPostbackCustomDamageReport) {
                const dataKey = Object.keys(newState.params).find(key => newState.params[key].data)
                if (dataKey) {
                  newState.params.linkToDataId = newState.params[dataKey].data
                }
              }
            }
          }
        }
      }
      if (newState.params && newState.params.actionLeft) {
        if (newState.params.actionLeft.type === "postback" && isNullOrEmpty(newState.params.actionLeft.data)) {
          newState.params.actionLeft._shouldSetDataId = true;
        }
      }
      if (newState.params && newState.params.actionRight) {
        if (newState.params.actionRight.type === "postback" && isNullOrEmpty(newState.params.actionRight.data)) {
          newState.params.actionRight._shouldSetDataId = true;
        }
      }
      delete newState.newMessage;
      return newState;
    } else {
      return obj;
    }
  });
  // console.log("🚀 ~ const file = store.property.vue ~ line 406 ~ onUpdateItemProperties ~ newState", cloneDeep(newState));
  const payload = {
    versionName: props.versionId,
    valueName: props.talkName,
    value: newState,
  };
  switch(modelLocal.value.dataType){
    case("imagemap"):
      payload["files"] = imageMapFiles.value;
    break;
    case("audio"):
      payload["files"] = audioFiles.value;
    break;
    case("image"):
      payload["files"] = imageFiles.value;
    break;
    case("video"):
      payload["files"] = videoFiles.value;
    break;
    default:
  }
  //console.log(payload);
  updateMindMapMessages(payload);

  const updatedModel = { ...modelLocal.value };
  delete updatedModel.newMessage;
  emits("updateScenarioMessage", oldState, newState, updatedModel);
  emits("stopProperties", false);
  updatingMessageForceRender.value = false;
};

const onDeleteItem = (): void => {
  let payload = {
    dataId: modelLocal.value.dataId,
    dataType: modelLocal.value.dataType,
    scenarioId: props.scenarioId,
    versionId: props.versionId,
    itemParams: modelLocal.value.params,
  };
  deleteScenarioMessage(payload);

  // Update text mapping
  modelLocal.value.userInput = [];
  emits("updateTextMapping", modelLocal.value);

  emits("close");
};

const updateModelParams = (value: any): void => {
  //console.log("RAW VALUE", value);
  modelLocal.value.params = value;
  updatingMessageForceRender.value = true;
  modelLocal.value.params = value;
  //console.log("updateModelParams", modelLocal.value);
};

const updateModelBubbles = (value: any): void => {
  modelLocal.value.params.bubbleParam = value;
};

const updateParams = ({ key, value }: any): void => {
  if (key === "__REPLACE__") {
    modelLocal.value.params = value;
  } else {
    modelLocal.value.params[key] = value;
  }
};

const updateMessages = (value: any): void => {
  modelLocal.value.messages = value;
};

const fileImageMapDataUpdate = (value: any): void => {
  imageMapFiles.value = value;
};

const fileAudioDataUpdate = (value: any): void => {
  audioFiles.value = value;
};

const fileImageDataUpdate = (value: any): void => {
  imageFiles.value = value;
};

const fileVideoDataUpdate = (value: any): void => {
  videoFiles.value = value;
};

const updateSaveStatus = ({ key, value }: any): void => {

  if (!childComponentValidation.value[modelLocal.value.dataType]) {
    childComponentValidation.value[modelLocal.value.dataType] = {};
  }

  // NOTE: run computed method
  childComponentValidation.value = {
    ...childComponentValidation.value,
    [modelLocal.value.dataType]: {
      [key]: value,
    },
  };
};

const onChangeName = (nameLBD): void => {
  validLocalState.value.name = ![isValidNonEmpty(nameLBD), isValidTextLength(nameLBD)].includes(false);
};

const isValidNonEmpty = (value: any): boolean => {
  return value?.replace(/\n/g, "").replace(/\s/g, "").length !== 0;
};

const isValidTextLength = (value: any): boolean => {
  return value && value.length <= 400;
};

const changeInLocalModelType = (dataType: any): void => {
  updatingMessageForceRender.value = false;
  emits("localDataTypeUpdate", dataType);
};

// computed
const activeScenario = computed(() => scenariosStore.activeScenario);
const activeScenarioData = computed(() => scenariosStore.activeScenarioData);
const scenarioMessages = computed(() => scenariosStore.scenarioMessages);
const scenarioMindmap = computed(() => scenariosStore.scenarioMindmap);

const maxWidthDialog = computed((): number => {
  if (
    modelLocal.value.dataType == "compositeMessage" ||
    modelLocal.value.dataType == "carouselFlex" ||
    modelLocal.value.dataType == "bubbleFlex"
  ) {
    return 1500;
  }
  return 800;
});

const displayMessageType = computed({
  get: () => modelLocal.value.dataType === '__INITIAL__' ? null :  modelLocal.value.dataType,
  set: (value) => {
    modelLocal.value.dataType = value;
  },
});

const actionTypesOptions = computed((): Array<any> => {
  // return Object.keys(BOT_ITEM_TYPES);
  return ["text", "imagemap", "buttons", "carousel", "confirm", "bubbleFlex", "compositeMessage"].map((key) => ({
    ...BOT_ITEM_TYPES[key],
    value: key,
  }));
});

const actionTypesValues = computed((): any => {
  return actionTypesOptions.value.map((option) => BOT_ITEM_TYPES[option]);
});

const previewMessage = computed((): any => {
  let newModelLocal = modelLocal.value;
  newModelLocal["params"] = checkMessageType.value;
  return newModelLocal;
});

const checkMessageType = computed((): any => {
  if (updatingMessageForceRender.value) {
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    // updatingMessageForceRender.value = false; // ここでfalseにすると予期せぬ初期化が起きるため、機能の終わり・切替でする - honda
    return modelLocal.value.params;
  }
  if (modelLocal.value.dataType === props.model?.dataType) {
    if (modelLocal.value !== originModelLocal.value) {
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      modelLocal.value.params = cloneDeep(originModelLocal.value.params);
    }
  } else {
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    modelLocal.value.params = cloneDeep(defaultMessages.value[modelLocal.value.dataType]);
  }
  // eslint-disable-next-line vue/no-side-effects-in-computed-properties
  updatingMessageForceRender.value = false;
  return modelLocal.value.params;
});

const canSave = computed((): boolean => {
  //console.log("canSave.value");
  if (
    modelLocal.value.params &&
    modelLocal.value.params.thumbnailImageUrl &&
    typeof modelLocal.value.params.thumbnailImageUrl !== "string" &&
    Object.keys(modelLocal.value.params.thumbnailImageUrl).length === 0
  ) {
    // FIXME: thumbnailImageUrlにObjectが渡ってきてしまうからdirtyに直す
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    modelLocal.value.params.thumbnailImageUrl = "";
  }

  const isSameModel =
    isEqual(modelLocal.value.nameLBD, originModelLocal.value.nameLBD) &&
    isEqual(modelLocal.value.params, originModelLocal.value.params) &&
    isEqual(modelLocal.value.messages, originModelLocal.value.messages) &&
    isEqual(modelLocal.value.targetDataOrder, originModelLocal.value.targetDataOrder) &&
    isEqual(modelLocal.value.parentDataField, originModelLocal.value.parentDataField) &&
    isEqual(modelLocal.value.targetDataField, originModelLocal.value.targetDataField);
    //console.log("isSameModel:", isSameModel, modelLocal.value, originModelLocal.value);
  if (isSameModel) {
    //console.log("emit:", "updateEditState", false);
    emits("updateEditState", false);
    return false;
  }

  const isValid =
    !Object.values(childComponentValidation.value[modelLocal.value.dataType] || {}).includes(false) &&
    !Object.values(validLocalState.value || {}).includes(false);
  //console.log("emit:", "updateEditState", isValid);
  emits("updateEditState", isValid);
  return isValid;
});

const isSpecialTalk = computed((): boolean => {
  return SPECIAL_TALK_TYPES.includes(props.talkName ?? '');
});

// watch
watch(
  () => props.model, 
  (value) => {
    modelLocal.value = cloneDeep(value);
  }
);

watch(
  () => props.originModel, 
  (value) => {
    originModelLocal.value = cloneDeep(value);
    branchIndex.value = value.branchIndex?.value;
  }
);

watch(
  () => props.botMessages, 
  (value) => {
    emits('botMessages', value);
  }
);

// hooks
onMounted(() => {
  onChangeName(modelLocal.value.nameLBD);
});

onBeforeMount(() => {
  modelLocal.value = cloneDeep(props.model) || {};
  originModelLocal.value = cloneDeep(props.originModel) || {};
  rules.value = {
    validNonEmpty: (value: any) => {
      if (isValidNonEmpty(value)) {
        return true;
      }
      return "未入力または空白のみは使用できません";
    },
    validTextLength: (value: any) => {
      if (isValidTextLength(value)) {
        return true;
      }
      return "400文字の制限";
    }
  };
  branchIndex.value = props.originModel && props.originModel.branchIndex || -1;
});

// #region damage report
const scenarioTalks = computed(() => scenariosStore.scenarioTalks);

const damageReportTalkVersion = computed((): any => {
  return scenarioTalks.value?.filter(x => x.versionOf === 'damage-report' || x.dataId === 'DAMAGE_REPORT_TALK')?.map(x => {
    return {
      ...x,
      value: x.dataId,
      text: x.params?.name,
      versionId: x.versionId
    }
  });
})
const isCustomDamageReportTalk = computed((): any => {
  return props.talkName!= "損傷報告"  && damageReportTalkVersion.value.find(x=>x.text === props.talkName)
})
// #endregion
</script>

<style lang="less">
.bot-designer-item-properties {
  label {
    margin-bottom: 5px;
    display: inline-block;
  }
}

.input-nowrap .q-field__native {
  white-space: nowrap !important; /* Ensure no wrapping */
  overflow: hidden !important;   /* Hide overflow */
  text-overflow: ellipsis !important; /* Add ellipsis for overflow */
}
</style>
