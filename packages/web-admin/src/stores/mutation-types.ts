export const SET_USER = "SET_USER";
export const SET_COGNITO_USER = "SET_COGNITO_USER";
export const SET_IS_SIGNING_IN = "SET_IS_SIGNING_IN";
export const SET_IS_SIGNING_OUT = "SET_IS_SIGNING_OUT";
export const SET_IS_REFRESHING_USER = "SET_IS_REFRESHING_USER";
export const RESET_USER = "RESET_USER";
export const SET_SIGNIN_ERROR = "SET_SIGNIN_ERROR";
export const SET_SIGNOUT_ERROR = "SET_SIGNOUT_ERROR";
export const SET_REFRESH_USER_ERROR = "SET_REFRESH_USER_ERROR";
export const SET_FORGOT_PASSWORD_ERROR = "SET_FORGOT_PASSWORD_ERROR";
export const SET_FORGOT_PASSWORD_SUBMIT_ERROR = "SET_FORGOT_PASSWORD_SUBMIT_ERROR";
export const SET_IS_ACCOUNT_UNUSABLE = "SET_IS_ACCOUNT_UNUSABLE";
export const SET_IS_USER_ABILITIES_DEFINED = "SET_IS_USER_ABILITIES_DEFINED";
export const SET_DEFINE_PERMISSIONS_ERROR = "SET_DEFINE_PERMISSIONS_ERROR";
export const SET_PERMISSIONS_FROM_TEAM = "SET_PERMISSIONS_FROM_TEAM";

export const CHANGE_LOADING_STATUS = "CHANGE_LOADING_STATUS";
export const SET_LANGUAGE = "SET_LANGUAGE";

// Settings mutation
export const SET_ACCESS_SETTINGS = "SET_ACCESS_SETTINGS";
export const SET_COMMON_SETTINGS = "SET_COMMON_SETTINGS";
export const SET_SEARCH_SETTINGS = "SET_SEARCH_SETTINGS";
export const SET_ACCESS_SETTINGS_FETCHING = "SET_ACCESS_SETTINGS_FETCHING";
export const SET_COMMON_SETTINGS_FETCHING = "SET_COMMON_SETTINGS_FETCHING";
export const SET_ACCESS_SETTINGS_UPDATING = "SET_ACCESS_SETTINGS_UPDATING";
export const SET_COMMON_SETTINGS_UPDATING = "SET_COMMON_SETTINGS_UPDATING";

export const SET_FETCH_ACCESS_SETTINGS_ERROR = "SET_FETCH_ACCESS_SETTINGS_ERROR";
export const SET_UPDATE_ACCESS_SETTINGS_ERROR = "SET_UPDATE_ACCESS_SETTINGS_ERROR";
export const SET_FETCH_COMMON_SETTINGS_ERROR = "SET_FETCH_COMMON_SETTINGS_ERROR";
export const SET_UPDATE_COMMON_SETTINGS_ERROR = "SET_UPDATE_COMMON_SETTINGS_ERROR";

// AWS Secrets Manager mutations
export const SET_SECRETS = "SET_SECRETS";
export const SET_IS_FETCHING_SECRETS = "SET_IS_FETCHING_SECRETS";
export const SET_FETCH_SECRETS_ERROR = "SET_FETCH_SECRETS_ERROR";
export const SET_IS_UPDATING_SECRETS = "SET_IS_UPDATING_SECRETS";
export const SET_UPDATE_SECRETS_ERROR = "SET_UPDATE_SECRETS_ERROR";

// Editable settings (Line settings + Damage report email address) mutations
export const SET_EDITABLE_SETTINGS = "SET_EDITABLE_SETTINGS";
export const SET_IS_FETCHING_EDITABLE_SETTINGS = "SET_IS_FETCHING_EDITABLE_SETTINGS";
export const SET_FETCH_EDITABLE_SETTINGS_ERROR = "SET_FETCH_EDITABLE_SETTINGS_ERROR";
export const SET_IS_UPDATING_EDITABLE_SETTINGS = "SET_IS_UPDATING_EDITABLE_SETTINGS";
export const SET_UPDATE_EDITABLE_SETTINGS_ERROR = "SET_UPDATE_EDITABLE_SETTINGS_ERROR";

// Cognito invite & verification email template customization
export const SET_MESSAGE_TEMPLATES = "SET_MESSAGE_TEMPLATES";
export const SET_IS_FETCHING_MESSAGE_TEMPLATES = "SET_IS_FETCHING_MESSAGE_TEMPLATES";
export const SET_FETCH_MESSAGE_TEMPLATES_ERROR = "SET_FETCH_MESSAGE_TEMPLATES_ERROR";
export const SET_IS_UPDATING_MESSAGE_TEMPLATES = "SET_IS_UPDATING_MESSAGE_TEMPLATES";
export const SET_UPDATE_MESSAGE_TEMPLATES_ERROR = "SET_UPDATE_MESSAGE_TEMPLATES_ERROR";

// Calendar settings mutations
export const SET_CALENDAR_DISPLAY_SETTINGS = "SET_CALENDAR_DISPLAY_SETTINGS";
export const SET_IS_FETCHING_CALENDAR_DISPLAY_SETTINGS = "SET_IS_FETCHING_CALENDAR_DISPLAY_SETTINGS";
export const SET_FETCH_CALENDAR_DISPLAY_SETTINGS_ERORR = "SET_FETCH_CALENDAR_DISPLAY_SETTINGS_ERORR";
export const SET_IS_UPDATING_CALENDAR_DISPLAY_SETTINGS = "SET_IS_UPDATING_CALENDAR_DISPLAY_SETTINGS";
export const SET_UPDATE_CALENDAR_DISPLAY_SETTINGS_ERROR = "SET_UPDATE_CALENDAR_DISPLAY_SETTINGS_ERROR";

export const SET_IS_IMPORTING_CALENDAR_CATEGORIES_CSV_FILE = "SET_IS_IMPORTING_CALENDAR_CATEGORIES_CSV_FILE";
export const SET_IMPORT_CALENDAR_CATEGORIES_CSV_FILE_ERROR = "SET_IMPORT_CALENDAR_CATEGORIES_CSV_FILE_ERROR";
export const SET_CALENDAR_CATEGORIES_CSV_DATA = "SET_CALENDAR_CATEGORIES_CSV_DATA";

export const SET_CALENDAR_SEARCH_CRITERIA = "SET_CALENDAR_SEARCH_CRITERIA";
export const SET_CALENDAR_CATEGORIES = "SET_CALENDAR_CATEGORIES";
export const SET_CALENDAR_ALL_CATEGORIES = "SET_CALENDAR_ALL_CATEGORIES";
export const REMOVE_FROM_CALENDAR_ALL_CATEGORIES = "REMOVE_FROM_CALENDAR_ALL_CATEGORIES";
export const APPEND_TO_CALENDAR_CATEGORIES = "APPEND_TO_CALENDAR_CATEGORIES";
export const REMOVE_FROM_CALENDAR_CATEGORIES = "REMOVE_FROM_CALENDAR_CATEGORIES";
export const UPDATE_CALENDAR_CATEGORIES = "UPDATE_CALENDAR_CATEGORIES";
export const SET_FETCHING_CATEGORIES_CSV = "SET_FETCHING_CATEGORIES_CSV";
export const SET_CATEGORIES_CSV_URL = "SET_CATEGORIES_CSV_URL";
export const SET_FETCHING_CATEGORIES_CSV_ERROR = "SET_FETCHING_CATEGORIES_CSV_ERROR";
export const SET_IS_SEARCHING_CALENDAR_CATEGORIES = "SET_IS_SEARCHING_CALENDAR_CATEGORIES";
export const SET_SEARCH_CALENDAR_CATEGORIES_ERROR = "SET_SEARCH_CALENDAR_CATEGORIES_ERROR";
export const SET_IS_FETCHING_CALENDAR_CATEGORIES = "SET_IS_FETCHING_CALENDAR_CATEGORIES";
export const SET_FETCH_CALENDAR_CATEGORIES_ERROR = "SET_FETCH_CALENDAR_CATEGORIES_ERROR";
export const SET_IS_CREATING_CALENDAR_CATEGORIES = "SET_IS_CREATING_CALENDAR_CATEGORIES";
export const SET_CREATE_CALENDAR_CATEGORIES_ERROR = "SET_CREATE_CALENDAR_CATEGORIES_ERROR";
export const SET_IS_UPDATING_CALENDAR_CATEGORY = "SET_IS_UPDATING_CALENDAR_CATEGORY";
export const SET_UPDATE_CALENDAR_CATEGORY_ERROR = "SET_UPDATE_CALENDAR_CATEGORY_ERROR";
export const SET_IS_DELETING_CALENDAR_CATEGORY = "SET_IS_DELETING_CALENDAR_CATEGORY";
export const SET_DELETE_CALENDAR_CATEGORY_ERROR = "SET_DELETE_CALENDAR_CATEGORY_ERROR";
export const SET_IS_DELETING_CALENDAR_INFO = "SET_IS_DELETING_CALENDAR_INFO";
export const SET_DELETE_CALENDAR_INFO_ERROR = "SET_DELETE_CALENDAR_INFO_ERROR";
export const SET_FIXED_LARGE_CATEGORIES = "SET_FIXED_LARGE_CATEGORIES";
export const SET_SURVEY_RESULT_CATEGORIES = "SET_SURVEY_RESULT_CATEGORIES";
export const SET_SINGLE_CATEGORY = "SET_SINGLE_CATEGORY";
export const SET_CURRENT_CALENDAR = "SET_CURRENT_CALENDAR";
export const SET_CALENDAR_DATA_OF_CATEGORIES_CRITERIA_TREE = "SET_CALENDAR_DATA_OF_CATEGORIES_CRITERIA_TREE";
export const SET_CALENDAR_DATA_OF_CATEGORIES_CRITERIA_LARGE = "SET_CALENDAR_DATA_OF_CATEGORIES_CRITERIA_LARGE";
export const SET_CALENDAR_DATA_OF_CATEGORIES_CRITERIA_MEDIUM = "SET_CALENDAR_DATA_OF_CATEGORIES_CRITERIA_MEDIUM";
export const SET_CALENDAR_DATA_OF_CATEGORIES_CRITERIA_SMALL = "SET_CALENDAR_DATA_OF_CATEGORIES_CRITERIA_SMALL";
export const SET_CALENDAR_DATA_OF_CATEGORIES_CRITERIA_TREE_ERROR = "SET_CALENDAR_DATA_OF_CATEGORIES_CRITERIA_TREE_ERROR";
export const SET_CATEGORIES_LARGE_CRITERIA = "SET_CATEGORIES_LARGE_CRITERIA";
export const SET_CATEGORIES_MEDIUM_CRITERIA = "SET_CATEGORIES_MEDIUM_CRITERIA";
export const SET_CATEGORIES_SMALL_CRITERIA = "SET_CATEGORIES_SMALL_CRITERIA";
export const SET_IS_FETCHING_CALENDAR_SETTINGS_INFO = "SET_IS_FETCHING_CALENDAR_SETTINGS_INFO";
export const SET_SURVEY_AND_CALENDAR_PERMISSIONS = "SET_SURVEY_AND_CALENDAR_PERMISSIONS";
export const ADD_SURVEY_AND_CALENDAR_PERMISSIONS = "ADD_SURVEY_AND_CALENDAR_PERMISSIONS";
export const SET_IS_FETCHING_SURVEY_AND_CALENDAR_PERMISSIONS = "SET_IS_FETCHING_SURVEY_AND_CALENDAR_PERMISSIONS";
export const SET_CALENDAR_IMPORTING_SCHEDULES = "SET_CALENDAR_IMPORTING_SCHEDULES";
export const SET_IS_FETCHING_CATEGORIES_TREE = "SET_IS_FETCHING_CATEGORIES_TREE";

// Public configuration
export const SET_PUBLIC_CONFIGURATION = "SET_PUBLIC_CONFIGURATION";
export const SET_IS_FETCHING_PUBLIC_CONFIGURATION = "SET_IS_FETCHING_PUBLIC_CONFIGURATION";
export const SET_FETCH_PUBLIC_CONFIGURATION_ERROR = "SET_FETCH_PUBLIC_CONFIGURATION_ERROR";

//form editor
export const SET_FORM_EDIT_MODE = "SET_FORM_EDIT_MODE";
export const SET_FORM_TITLE = "SET_FORM_TITLE";
export const SET_FORM_SCHEMA = "SET_FORM_SCHEMA";
export const SET_EDIT_FORM_TYPE = "SET_EDIT_FORM_TYPE";
export const UPDATE_FORM_SCHEMA_ELEMENT = "UPDATE_FORM_SCHEMA_ELEMENT";
export const UPDATE_SURVEY_SCHEMA = "UPDATE_SURVEY_SCHEMA";
export const UPDATE_FORM_SCHEMA_ITEM = "UPDATE_FORM_SCHEMA_ITEM";
export const UPDATE_FORM_SCHEMA_PAGE_HEADER = "UPDATE_FORM_SCHEMA_PAGE_HEADER";
export const UPDATE_FORM_SCHEMA_END_OF_SURVEY_MESSAGE = "UPDATE_FORM_SCHEMA_END_OF_SURVEY_MESSAGE";
export const UPDATE_FORM_SCHEMA_DELIVERY_MESSAGE = "UPDATE_FORM_SCHEMA_DELIVERY_MESSAGE";
export const UPDATE_FORM_SCHEMA_TEAM_LIST = "UPDATE_FORM_SCHEMA_TEAM_LIST";
export const REMOVE_FORM_SCHEMA_DELIVERY_MESSAGE = "REMOVE_FORM_SCHEMA_DELIVERY_MESSAGE";
export const UPDATE_MEMBER_SCHEMA_LINKED_FORMS = "UPDATE_MEMBER_SCHEMA_LINKED_FORMS";
export const ADD_FORM_SCHEMA_ITEM = "ADD_FORM_SCHEMA_ITEM";
export const ADD_FORM_SCHEMA_SECTION = "ADD_FORM_SCHEMA_SECTION";
export const DELETE_FORM_SCHEMA_ITEM = "DELETE_FORM_SCHEMA_ITEM";
export const TOGGLE_MOVE_SECTION_MODAL = "TOGGLE_MOVE_SECTION_MODAL";
export const TOGGLE_CODE_EDITOR_MODAL = "TOGGLE_CODE_EDITOR_MODAL";
export const DUPLICATE_FORM_SCHEMA_ITEM = "DUPLICATE_FORM_SCHEMA_ITEM";
export const DUPLICATE_FORM_SCHEMA_SECTION = "DUPLICATE_FORM_SCHEMA_SECTION";
export const MERGE_FORM_SCHEMA_SECTION = "MERGE_FORM_SCHEMA_SECTION";
export const DELETE_FORM_SCHEMA_SECTION = "DELETE_FORM_SCHEMA_SECTION";
export const SET_ACTIVE_FORM_SCHEMA_ITEM = "SET_ACTIVE_FORM_SCHEMA_ITEM";
export const SET_ACTIVE_FORM_SCHEMA_SECTION = "SET_ACTIVE_FORM_SCHEMA_SECTION";
export const SET_ACTIVE_FORM_TAB = "SET_ACTIVE_FORM_TAB";
export const SET_LANGUAGE_SETTING = "SET_LANGUAGE_SETTING";
export const INIT_LANGUAGE_SETTING = "INIT_LANGUAGE_SETTING";
export const REMOVE_LANGUAGE_SETTING = "REMOVE_LANGUAGE_SETTING";
export const REMOVE_LANGUAGE_DICTIONARY_SETTING = "REMOVE_LANGUAGE_DICTIONARY_SETTING";
export const SET_BACK_BUTTON_CONDITION = "SET_BACK_BUTTON_CONDITION";
export const UPDATE_VACCINATION_INTERVAL_TYPE = "UPDATE_VACCINATION_INTERVAL_TYPE";

export const SET_CALENDAR_DATA_OF_CATEGORIES_TREE_1 = "SET_CALENDAR_DATA_OF_CATEGORIES_TREE_1";
export const SET_CALENDAR_DATA_OF_CATEGORIES_LARGE_1 = "SET_CALENDAR_DATA_OF_CATEGORIES_LARGE_1";
export const SET_CALENDAR_DATA_OF_CATEGORIES_MEDIUM_1 = "SET_CALENDAR_DATA_OF_CATEGORIES_MEDIUM_1";
export const SET_CALENDAR_DATA_OF_CATEGORIES_TREE_ERROR_1 = "SET_CALENDAR_DATA_OF_CATEGORIES_TREE_ERROR_1";
export const SET_CALENDAR_SEARCH_SELECTED_LARGE_CATEGORY_1 = "SET_CALENDAR_SEARCH_SELECTED_LARGE_CATEGORY_1";
export const SET_PAYMENT_SERVICE_LIST_FOR_FORM_EDITOR = "SET_PAYMENT_SERVICE_LIST_FOR_FORM_EDITOR";

// forms mutations
export const SET_FORM_CONFIGS = "SET_FORM_CONFIGS";
export const ADD_FORM_CONFIG = "ADD_FORM_CONFIG";

export const ADD_TO_SURVEY_CONFIGS_LIST_TYPE = "ADD_TO_SURVEY_CONFIGS_LIST_TYPE";
export const SET_IS_FETCHING_SURVEY_CONFIGS_LIST_TYPE = "SET_IS_FETCHING_SURVEY_CONFIGS_LIST_TYPE";
export const SET_IS_FETCHING_SURVEY_CONFIGS_BY_ID = "SET_IS_FETCHING_SURVEY_CONFIGS_BY_ID";
export const SET_FETCH_SURVEY_CONFIGS_LIST_TYPE_ERROR = "SET_FETCH_SURVEY_CONFIGS_LIST_TYPE_ERROR";
export const SET_FETCH_SURVEY_CONFIGS_BY_ID_ERROR = "SET_FETCH_SURVEY_CONFIGS_BY_ID_ERROR";

export const SET_IS_FETCHING_FORM_CONFIGS = "SET_IS_FETCHING_FORM_CONFIGS";
export const SET_FETCH_FORM_CONFIGS_ERROR = "SET_FETCH_FORM_CONFIGS_ERROR";
export const SET_FETCH_FORM_CONFIG_ERROR = "SET_FETCH_FORM_CONFIG_ERROR";
export const INCREASE_FORM_COMPONENT_KEY = "INCREASE_FORM_COMPONENT_KEY";
export const SET_FORM_CONFIG_DETAIL = "SET_FORM_CONFIG_DETAIL";
export const SET_IS_FETCHING_FORM_CONFIG_DETAIL = "SET_IS_FETCHING_FORM_CONFIG_DETAIL";
export const SET_FETCH_FORM_CONFIG_DETAIL_ERROR = "SET_FETCH_FORM_CONFIG_DETAIL_ERROR";

export const SET_IS_REGISTING_SURVEY_CONFIG = "SET_IS_REGISTING_SURVEY_CONFIG";
export const SET_REGISTER_SURVEY_CONFIG_ERROR = "SET_REGISTER_SURVEY_CONFIG_ERROR";
export const SET_REGISTERED_SURVEY_CONFIG_ID = "SET_REGISTERED_SURVEY_CONFIG_ID";

export const SET_IS_UPDATING_SURVEY_CONFIG = "SET_IS_UPDATING_SURVEY_CONFIG";
export const SET_UPDATE_SURVEY_CONFIG_ERROR = "SET_UPDATE_SURVEY_CONFIG_ERROR";
export const UPDATE_SURVEY_CONFIG_IN_STORE = "UPDATE_SURVEY_CONFIG_IN_STORE";
export const REMOVE_SURVEY_CONFIG_IN_STORE = "REMOVE_SURVEY_CONFIG_IN_STORE";

export const SET_IS_EXIST_SURVEY_RESULTS = "SET_IS_EXIST_SURVEY_RESULTS";
export const SET_IS_TIMED_OUT_COUNT_SURVEY_RESULTS = "SET_IS_TIMED_OUT_COUNT_SURVEY_RESULTS";
export const SET_FETCH_SURVEY_RESUTLS_COUNT_ERROR = "SET_FETCH_SURVEY_RESUTLS_COUNT_ERROR";
export const SET_EDITED_MULTIPLE_ITEM = "SET_EDITED_MULTIPLE_ITEM";

// Member forms mutations
export const SET_CHECK_SAIBAN_EXIST_ERROR = "SET_CHECK_SAIBAN_EXIST_ERROR";
export const SET_CHECK_SAIBAN_EXIST_FOR_UPDATE_ERROR = "SET_CHECK_SAIBAN_FOR_UPDATE_EXIST_ERROR";
export const SET_MEMBER_CONFIGS = "SET_MEMBER_CONFIGS";
export const SET_IS_FETCHING_MEMBER_CONFIGS = "SET_IS_FETCHING_MEMBER_CONFIGS";
export const SET_FETCH_MEMBER_CONFIGS_ERROR = "SET_FETCH_MEMBER_CONFIGS_ERROR";
export const SET_MEMBER_CONFIG = "SET_MEMBER_CONFIG";
export const SET_IS_FETCHING_MEMBER_CONFIG = "SET_IS_FETCHING_MEMBER_CONFIG";
export const SET_FETCH_MEMBER_CONFIG_ERROR = "SET_FETCH_MEMBER_CONFIG_ERROR";
export const SET_IS_REGISTERING_MEMBER_CONFIG = "SET_IS_REGISTERING_MEMBER_CONFIG";
export const SET_REGISTER_MEMBER_CONFIG_ERROR = "SET_REGISTER_MEMBER_CONFIG_ERROR";
export const SET_REGISTERED_MEMBER_CONFIG_ID = "SET_REGISTERED_MEMBER_CONFIG_ID";
export const ADD_MEMBER_CONFIG = "ADD_MEMBER_CONFIG";
export const SET_IS_UPDATING_MEMBER_CONFIG = "SET_IS_UPDATING_MEMBER_CONFIG";
export const SET_UPDATE_MEMBER_CONFIG_ERROR = "SET_UPDATE_MEMBER_CONFIG_ERROR";
export const UPDATE_MEMBER_CONFIG_IN_STORE = "UPDATE_MEMBER_CONFIG_IN_STORE";

export const UPDATE_DATA_MANAGER_SEARCH_CRITERIA_COMMON = "UPDATE_DATA_MANAGER_SEARCH_CRITERIA_COMMON";
export const UPDATE_DATA_MANAGER_SEARCH_CRITERIA_DATE = "UPDATE_DATA_MANAGER_SEARCH_CRITERIA_DATE";
export const CLEAR_DATA_MANAGER_SEARCH_CRITERIA = "CLEAR_DATA_MANAGER_SEARCH_CRITERIA";
export const TOGGLE_DATA_MANAGER_ADVANCED_SEARCH = "TOGGLE_DATA_MANAGER_ADVANCED_SEARCH";

// data managements action define
export const SET_SELECTED_SURVEY_OLD = "SET_SELECTED_SURVEY_OLD";
export const UPDATE_DISPLAY_COLUMNS_DATA_MANAGEMENT = "UPDATE_DISPLAY_COLUMNS_DATA_MANAGEMENT";
export const UPDATE_DATA_TABLE_OPTIONS_DATA_MANAGEMENT = "UPDATE_DATA_TABLE_OPTIONS_DATA_MANAGEMENT";
export const UPDATE_ITEMS_PER_PAGE_DATA_MANAGEMENT = "UPDATE_ITEMS_PER_PAGE_DATA_MANAGEMENT";
export const UPDATE_PAGE_DATA_MANAGEMENT = "UPDATE_PAGE_DATA_MANAGEMENT";
export const UPDATE_SORT_DATA_MANAGEMENT = "UPDATE_SORT_DATA_MANAGEMENT";
export const UPDATE_COLUMNS = "UPDATE_COLUMNS";
export const UPDATE_DISPLAY_COLUMNS = "UPDATE_DISPLAY_COLUMNS";
export const FILTER_DATA_SURVEY_RESULTS = "FILTER_DATA_SURVEY_RESULTS";
export const APPENDING_UPDATE_ALL_TARGET_STATUS = "APPENDING_UPDATE_ALL_TARGET_STATUS";
export const APPENDING_UPDATE_TARGET_STATUS = "APPENDING_UPDATE_TARGET_STATUS";
export const CLEAR_ALL_SURVEY_RESULTS_SEARCH_CRITERIA = "CLEAR_ALL_SURVEY_RESULTS_SEARCH_CRITERIA";
export const SET_DATA_OF_SELECTED_FORM = "SET_DATA_OF_SELECTED_FORM";
export const SET_PARTIAL_DATA_OF_SELECTED_FORM = "SET_PARTIAL_DATA_OF_SELECTED_FORM";
export const SET_DATA_OF_SEARCH_RESULT = "SET_DATA_OF_SEARCH_RESULT";
export const SET_IS_FETCHING_DATA_OF_SELECTED_FORM = "SET_IS_FETCHING_DATA_OF_SELECTED_FORM";
export const SET_IS_CONTINUING_FETCH_DATA_OF_SELECTED_FORM = "SET_IS_CONTINUING_FETCH_DATA_OF_SELECTED_FORM";
export const SET_FETCHING_DATA_UNIQUE_KEY = "SET_FETCHING_DATA_UNIQUE_KEY";
export const SET_FETCH_DATA_OF_SELECTED_FORM_ERROR = "SET_FETCH_DATA_OF_SELECTED_FORM_ERROR";
export const SET_FILTERED_SURVEY_DATA = "SET_FILTERED_SURVEY_DATA";
export const RESET_FILTERED_SURVEY_DATA = "RESET_FILTERED_SURVEY_DATA";
export const UPDATE_FILTERS = "UPDATE_FILTERS";
export const SET_DELETE_SURVEY_RESULTS_ERROR = "SET_DELETE_SURVEY_RESULTS_ERROR";
export const SET_IS_UPDATING_DATA_SURVEY = "SET_IS_UPDATING_DATA_SURVEY";
export const SET_UPDATE_DATA_SURVEY_ERROR = "SET_UPDATE_DATA_SURVEY_ERROR";
export const UPDATE_DATA_SURVEY_IN_LIST = "UPDATE_DATA_SURVEY_IN_LIST";
export const SET_CSV_URL_FOR_SURVEY_RESULTS = "SET_CSV_URL_FOR_SURVEY_RESULTS";
export const SET_CREATE_CSV_FOR_SURVEY_RESULTS_ERROR = "SET_CREATE_CSV_FOR_SURVEY_RESULTS_ERROR";
export const SET_CREATE_CSV_APPENDING_FOR_SURVEY_RESULTS_ERROR = "SET_CREATE_CSV_APPENDING_FOR_SURVEY_RESULTS_ERROR";
export const SET_IS_SENDING_SURVEY_UNICAST = "SET_IS_SENDING_SURVEY_UNICAST";
export const SET_SEND_SURVEY_UNICAST_ERROR = "SET_SEND_SURVEY_UNICAST_ERROR";
export const SET_DELETE_URL_FOR_SURVEY_RESULTS = "SET_DELETE_URL_FOR_SURVEY_RESULTS";
export const SET_IS_UPLOADING_IMPORTED_APPEND_TYPE_SURVEY_RESULTS_CSV = "SET_IS_UPLOADING_IMPORTED_APPEND_TYPE_SURVEY_RESULTS_CSV";
export const SET_UPLOAD_IMPORTED_APPEND_TYPE_SURVEY_RESULTS_CSV_ERROR = "SET_UPLOAD_IMPORTED_APPEND_TYPE_SURVEY_RESULTS_CSV_ERROR";
export const SET_IS_INITIALIZING_IMPORTING_APPEND_TYPE_SURVEY_RESULTS = "SET_IS_INITIALIZING_IMPORTING_APPEND_TYPE_SURVEY_RESULTS";
export const SET_INITIALIZE_IMPORTING_APPEND_TYPE_SURVEY_RESULTS_ERROR = "SET_INITIALIZE_IMPORTING_APPEND_TYPE_SURVEY_RESULTS_ERROR";
export const SET_IS_SENDING_MULTIPLE_SURVEY_UNICAST = "SET_IS_SENDING_MULTIPLE_SURVEY_UNICAST";
export const SET_SEND_MULTIPLE_SURVEY_UNICAST_ERROR = "SET_SEND_MULTIPLE_SURVEY_UNICAST_ERROR";
export const SET_IS_RESEARCH_DATA = "SET_IS_RESEARCH_DATA";
export const IS_CALENDAR_DISPLAY = "IS_CALENDAR_DISPLAY";
export const CALENDAR_VALUE = "CALENDAR_VALUE";
export const CALENDAR_CONFIG = "CALENDAR_CONFIG";
export const SET_HOME_NOTIFICATION_DATA = "SET_HOME_NOTIFICATION_DATA";
export const SET_IS_FETCHING_HOME_NOTIFICATION_DATA = "SET_IS_FETCHING_HOME_NOTIFICATION_DATA";
export const SET_FETCH_HOME_NOTIFICATION_DATA_ERROR = "SET_FETCH_HOME_NOTIFICATION_DATA_ERROR";
export const SET_CSV_APPENDING_SEARCH_CRITERIA_URL_FOR_SURVEY_RESULTS =
  "SET_CSV_APPENDING_SEARCH_CRITERIA_URL_FOR_SURVEY_RESULTS";
export const SET_IS_CREATING_CSV_APPENDING_SEARCH_CRITERIA_FOR_SURVEY_RESULTS =
  "SET_IS_CREATING_CSV_APPENDING_SEARCH_CRITERIA_FOR_SURVEY_RESULTS";
export const SET_CREATE_CSV_APPENDING_SEARCH_CRITERIA_FOR_SURVEY_RESULTS_ERROR =
  "SET_CREATE_CSV_APPENDING_SEARCH_CRITERIA_FOR_SURVEY_RESULTS_ERROR";
export const SET_IS_CREATING_CSV_APPENDING_SELECTED = "SET_IS_CREATING_CSV_APPENDING_SELECTED";
export const SET_IS_CREATING_CSV_SURVEY_RESULT = "SET_IS_CREATING_CSV_SURVEY_RESULT";
export const SET_AUTO_RESERVATION_RESULT = "SET_AUTO_RESERVATION_RESULT";
export const ADD_RESERVATION_PAYMNET_SERVICE_MAP = "ADD_RESERVATION_PAYMNET_SERVICE_MAP";
export const SET_IS_FETCHING_RESERVATION_PAYMENT_SERVICE = "SET_IS_FETCHING_RESERVATION_PAYMENT_SERVICE";
export const SET_PAYMENT_SERVICE = "SET_PAYMENT_SERVICE";
export const SET_PRODUT_LIST_FOR_DATA_MANAGEMENT = "SET_PRODUT_LIST_FOR_DATA_MANAGEMENT";
export const SET_IS_FETHING_PAYMENT_SERVICE = "SET_IS_FETHING_PAYMENT_SERVICE";
export const SET_TAX_RATE = "SET_TAX_RATE";
export const SET_IS_FETHING_TAX_RATE = "SET_IS_FETHING_TAX_RATE";
export const SET_PAYMENT_RESULTS_CSV_URL = "SET_PAYMENT_RESULTS_CSV_URL";
export const SET_IS_FETCHING_PAYMENT_RESULTS_CSV_URL = "SET_IS_FETCHING_PAYMENT_RESULTS_CSV_URL";
export const SET_FETCH_PAYMENT_RESULTS_CSV_URL = "SET_FETCH_PAYMENT_RESULTS_CSV_URL";

// Segment mutations (Mail and Talk)
export const SET_MAIL_DELIVERY_LIST = "SET_MAIL_DELIVERY_LIST";
export const SET_TALK_DELIVERY_LIST = "SET_TALK_DERLIVERES";
export const RESET_MAIL_DELIVERY_LIST = "RESET_MAIL_DELIVERY_LIST";
export const RESET_TALK_DELIVERY_LIST = "RESET_TALK_DELIVERY_LIST";

export const SET_IS_FETCHING_MAIL_DELIVERY_LIST = "SET_IS_FETCHING_MAIL_DELIVERY_LIST";
export const SET_IS_FETCHING_TALK_DELIVERY_LIST = "SET_IS_FETCHING_TALK_DELIVERY_LIST";

export const SET_FETCH_MAIL_DELIVERY_LIST_ERROR = "SET_FETCH_MAIL_DELIVERY_LIST_ERROR";
export const SET_FETCH_TALK_DELIVERY_LIST_ERROR = "SET_FETCH_TALK_DELIVERY_LIST_ERROR";

// segment mutations
export const SET_SEGMENTS = "SET_SEGMENTS";
export const SET_IS_FETCHING_SEGMENT_DELIVERIES = "SET_IS_FETCHING_SEGMENT_DELIVERIES";
export const SET_FETCH_SEGMENT_DELIVERIES_ERROR = "SET_FETCH_SEGMENT_DELIVERIES_ERROR";

export const SET_IS_CREATING_SEGMENT_DELIVERY = "SET_IS_CREATING_SEGMENT_DELIVERY";
export const SET_CREATE_SEGMENT_DELIVERY_ERROR = "SET_CREATE_SEGMENT_DELIVERY_ERROR";

export const SET_IS_UPDATING_SEGMENT_DELIVERY = "SET_IS_UPDATING_SEGMENT_DELIVERY";
export const SET_UPDATE_SEGMENT_DELIVERY_ERROR = "SET_UPDATE_SEGMENT_DELIVERY_ERROR";

export const SET_IS_DELETING_SEGMENT_DELIVERY = "SET_IS_DELETING_SEGMENT_DELIVERY";
export const SET_DELETE_SEGMENT_DELIVERY_ERROR = "SET_DELETE_SEGMENT_DELIVERY_ERROR";

export const UPDATE_SEGMENT_FILTER = "UPDATE_SEGMENT_FILTER";
export const UPDATE_SEGMENT_PAGE = "UPDATE_SEGMENT_PAGE";
export const REMOVE_SEGMENT_ITEM = "REMOVE_SEGMENT_ITEM";

export const SET_REMOVE_SEGMENT_ERROR = "SET_REMOVE_SEGMENT_ERROR";

export const SET_DISTRIBUTION_STATUS = "SET_DISTRIBUTION_STATUS";
export const SET_IS_FETCHING_DISTRIBUTION_STATUS = "SET_IS_FETCHING_DISTRIBUTION_STATUS";
export const SET_FETCH_DISTRIBUTION_STATUS_ERROR = "SET_FETCH_DISTRIBUTION_STATUS_ERROR";

export const SET_IS_RESENDING_DISTRIBUTION_ITEM = "SET_IS_RESENDING_DISTRIBUTION_ITEM";
export const SET_RESEND_DISTRIBUTION_ITEM_ERROR = "SET_RESEND_DISTRIBUTION_ITEM_ERROR";

export const SET_IS_DELETING_DISTRIBUTION_ITEM = "SET_IS_DELETING_DISTRIBUTION_ITEM";
export const SET_DELETE_DISTRIBUTION_ITEM_ERROR = "SET_DELETE_DISTRIBUTION_ITEM_ERROR";

// distribution delivery mutations
export const SET_IS_CREATING_DISTRIBUTION_DELIVERY = "SET_IS_CREATING_DISTRIBUTION_DELIVERY";
export const SET_CREATE_DISTRIBUTION_DELIVERY_ERROR = "SET_CREATE_DISTRIBUTION_DELIVERY_ERROR";

export const SET_IS_UPDATING_DISTRIBUTION_DELIVERY = "SET_IS_UPDATING_DISTRIBUTION_DELIVERY";
export const SET_UPDATE_DISTRIBUTION_DELIVERY_ERROR = "SET_UPDATE_DISTRIBUTION_DELIVERY_ERROR";

export const SET_IS_SAVING_DISTRIBUTION_DELIVERY_DRAFT = "SET_IS_SAVING_DISTRIBUTION_DELIVERY_DRAFT";
export const SET_SAVE_DISTRIBUTION_DELIVERY_DRAFT_ERROR = "SET_SAVE_DISTRIBUTION_DELIVERY_DRAFT_ERROR";

export const SET_IS_SENDING_BROADCAST_MESSAGES = "SET_IS_SENDING_BROADCAST_MESSAGES";
export const SET_SEND_BROADCAST_MESSAGES_ERROR = "SET_SEND_BROADCAST_MESSAGES_ERROR";

export const SET_DISTRIBUTION_STATISTICS = "SET_DISTRIBUTION_STATISTICS";
export const SET_IS_GETTING_DISTRIBUTION_STATISTICS = "SET_IS_GETTING_DISTRIBUTION_STATISTICS";
export const SET_GET_DISTRIBUTION_STATISTICS_ERROR = "SET_GET_DISTRIBUTION_STATISTICS_ERROR";

export const SET_DISTRIBUTION_LIST = "SET_DISTRIBUTION_LIST";
export const SET_IS_FETCHING_DISTRIBUTION_LIST = "SET_IS_FETCHING_DISTRIBUTION_LIST";
export const SET_FETCH_DISTRIBUTION_LIST_ERROR = "SET_FETCH_DISTRIBUTION_LIST_ERROR";
export const UPDATE_DISTRIBUTION_LIST_FILTER = "UPDATE_DISTRIBUTION_LIST_FILTER";

export const SET_DISTRIBUTION_DETAIL = "SET_DISTRIBUTION_DETAIL";
export const SET_IS_FETCHING_DISTRIBUTION_DETAIL = "SET_IS_FETCHING_DISTRIBUTION_DETAIL";
export const SET_FETCH_DISTRIBUTION_DETAIL_ERROR = "SET_FETCH_DISTRIBUTION_DETAIL_ERROR";
export const SET_SURVEY_CONDITIONS = "SET_SURVEY_CONDITIONS";
export const SET_RECURRING_SETTINGS = "SET_RECURRING_SETTINGS";

export const ADD_MESSAGE_INPUT = "ADD_MESSAGE_INPUT";
export const REMOVE_MESSAGE_INPUT = "REMOVE_MESSAGE_INPUT";
export const SET_MESSAGE_TYPES = "SET_MESSAGE_TYPES";
export const SET_TEXT_MESSAGE = "SET_TEXT_MESSAGE";
export const SET_IMAGE_MESSAGE = "SET_IMAGE_MESSAGE";
export const SET_IMAGEMAP_MESSAGE = "SET_IMAGEMAP_MESSAGE";
export const SET_FLEX_MESSAGE = "SET_FLEX_MESSAGE";
export const SET_VIDEO_MESSAGE = "SET_VIDEO_MESSAGE";

export const SET_SURVEY_SCHEMA = "SET_SURVEY_SCHEMA";

// distribution condition mutations
export const SET_DISTRIBUTION_CONFIGS = "SET_DISTRIBUTION_CONFIGS";
export const SET_IS_FETCHING_DISTRIBUTION_CONFIGS = "SET_IS_FETCHING_DISTRIBUTION_CONFIGS";
export const SET_FETCH_DISTRIBUTION_CONFIGS_ERROR = "SET_FETCH_DISTRIBUTION_CONFIGS_ERROR";

export const SET_IS_CHECKING_SUBJECT_CONDITION = "SET_IS_CHECKING_SUBJECT_CONDITION";
export const SET_CHECK_SUBJECY_CONDITION_ERROR = "SET_CHECK_SUBJECY_CONDITION_ERROR";

export const SET_IS_CHECKING_BODY_CONDITION = "SET_IS_CHECKING_BODY_CONDITION";
export const SET_CHECK_BODY_CONDITION_ERROR = "SET_CHECK_BODY_CONDITION_ERROR";

export const SET_IS_CHANGING_BODY_CONTENT = "SET_IS_CHANGING_BODY_CONTENT";
export const SET_CHANGE_BODY_CONTENT_ERROR = "SET_CHANGE_BODY_CONTENT_ERROR";

export const SET_IS_UPDATING_CONDITION = "SET_IS_UPDATING_CONDITION";
export const SET_IS_UPDATING_CONTENT = "SET_IS_UPDATING_CONTENT";
export const SET_UPDATE_CONDITION_CONTENT_ERROR = "SET_UPDATE_CONDITION_CONTENT_ERROR";

// distribution outside segment mutations
export const SET_IS_FETCHING_SCENARIO_TALKS_DISTRIBUTION = "SET_IS_FETCHING_SCENARIO_TALKS_DISTRIBUTION";
export const SET_FETCH_SCENARIO_TALKS_DISTRIBUTION_ERROR = "SET_FETCH_SCENARIO_TALKS_DISTRIBUTION_ERROR";
export const SET_SCENARIO_DISTRIBUTION_TALKS = "SET_SCENARIO_DISTRIBUTION_TALKS";

// reservation reminder mutations
export const SET_ALL_REMINDER_CATEGORIES = "SET_ALL_REMINDER_CATEGORIES";
export const SET_IS_FETCHING_ALL_REMINDER_CATEGORIES = "SET_IS_FETCHING_ALL_REMINDER_CATEGORIES";
export const SET_SELECTED_CATEGORY = "SET_SELECTED_CATEGORY";
export const SET_REMINDER_CONFIGURATION = "SET_REMINDER_CONFIGURATION";
export const SET_HAS_PERMISSION_FOR_VIEWING_REMINDER_CONFIG = "SET_HAS_PERMISSION_FOR_VIEWING_REMINDER_CONFIG";
export const SET_DISPLAY_REMINDER_SETTINGS = "SET_DISPLAY_REMINDER_SETTINGS";
export const SET_IS_FETCHING_REMINDER_CONFIG = "SET_IS_FETCHING_REMINDER_CONFIG";
export const ADD_REMINDER_SETTINGS = "ADD_REMINDER_SETTINGS";
export const REMOVE_REMINDER_SETTINGS = "REMOVE_REMINDER_SETTINGS";
export const ADD_REMINDER_SETTINGS_MESSAGE = "ADD_REMINDER_SETTINGS_MESSAGE";
export const REMOVE_REMINDER_SETTINGS_MESSAGE = "REMOVE_REMINDER_SETTINGS_MESSAGE";
export const SET_REMINDER_SETTINGS_MESSAGE_TYPE = "SET_REMINDER_SETTINGS_MESSAGE_TYPE";
export const SET_REMINDER_SETTINGS_TEXT_MESSAGE = "SET_REMINDER_SETTINGS_TEXT_MESSAGE";
export const SET_REMINDER_SETTINGS_IMAGE_MESSAGE = "SET_REMINDER_SETTINGS_IMAGE_MESSAGE";
export const SET_REMINDER_SETTINGS_SEND_TIME = "SET_REMINDER_SETTINGS_SEND_TIME";
export const SET_IS_UPDATING_REMINDER_CONFIG = "SET_IS_UPDATING_REMINDER_CONFIG";
export const SET_IS_UPDATING_REMINDER_CONFIG_ERROR = "SET_IS_UPDATING_REMINDER_CONFIG_ERROR";
export const SET_SELECTED_SURVEY_ID = "SET_SELECTED_SURVEY_ID";
export const SET_ALL_SURVEY_CONFIGS = "SET_ALL_SURVEY_CONFIGS";
export const ADD_SURVEY_CONFIGS = "ADD_SURVEY_CONFIGS";
export const SET_IS_FETCHING_ALL_SURVEY_CONFIG = "SET_IS_FETCHING_ALL_SURVEY_CONFIG";

export const SET_REMINDER_EXECUTION_HISTORY = "SET_REMINDER_EXECUTION_HISTORY";
export const SET_IS_FETCHING_REMINDER_EXECUTION_HISTORY = "SET_IS_FETCHING_REMINDER_EXECUTION_HISTORY";
export const SET_FETCH_REMINDER_EXECUTION_HISTORY_ERROR = "SET_FETCH_REMINDER_EXECUTION_HISTORY_ERROR";

export const SET_REMINDER_EXECUTION_HISTORIES = "SET_REMINDER_EXECUTION_HISTORIES";
export const SET_IS_FETCHING_REMINDER_EXECUTION_HISTORIES = "SET_IS_FETCHING_REMINDER_EXECUTION_HISTORIES";
export const SET_FETCH_REMINDER_EXECUTION_HISTORIES_ERROR = "SET_FETCH_REMINDER_EXECUTION_HISTORIES_ERROR";

// date relative reminder settings mutations
export const UPDATE_DATE_RELATIVE_REMINDER_SETTINGS_LIST = "UPDATE_DATE_RELATIVE_REMINDER_SETTINGS_LIST";
export const UPDATE_DATE_RELATIVE_REMINDER_SETTINGS_ITEM = "UPDATE_DATE_RELATIVE_REMINDER_SETTINGS_ITEM";
export const RELATIVE_REMINDER_SETTINGS_SNACK_MESSAGE = "RELATIVE_REMINDER_SETTINGS_SNACK_MESSAGE";
export const UPDATE_FETCHING_ALL_SURVEY_IN_REMINDAR_SETTINGS = "UPDATE_FETCHING_ALL_SURVEY_IN_REMINDAR_SETTINGS";

//scenarios mutation define
export const SET_SCENARIOS = "SET_SCENARIOS";
export const SET_ACTIVE_SCENARIO = "SET_ACTIVE_SCENARIO";
export const SET_ACTIVE_SCENARIO_DATA = "SET_ACTIVE_SCENARIO_DATA";
export const SET_SELECTED_SCENARIO = "SET_SELECTED_SCENARIO";
export const SET_IS_FETCHING_SCENARIOS = "SET_IS_FETCHING_SCENARIOS";
export const SET_FETCH_SCENARIOS_ERROR = "SET_FETCH_SCENARIOS_ERROR";

export const SET_IS_IMPORTING_SCENARIO_DATA = "SET_IS_IMPORTING_SCENARIO_DATA";
export const SET_IMPORTING_SCENARIO_DATA_ERROR = "SET_IMPORTING_SCENARIO_DATA_ERROR";
export const SET_IMPORTING_SCENARIO_DATA_WARNING = "SET_IMPORTING_SCENARIO_DATA_WARNING";
export const IMPORT_FINISH_SUCCESS = "IMPORT_FINISH_SUCCESS";
export const CREATE_FINISH_SUCCESS = "CREATE_FINISH_SUCCESS";

export const SET_IS_EXPORTING_SCENARIO_DATA = "SET_IS_EXPORTING_SCENARIO_DATA";
export const SET_EXPORTING_SCENARIO_DATA_ERROR = "SET_EXPORTING_SCENARIO_DATA_ERROR";
export const SET_EXPORTING_SCENARIO_DATA_WARNING = "SET_EXPORTING_SCENARIO_DATA_WARNING";
export const EXPORT_FINISH_SUCCESS = "EXPORT_FINISH_SUCCESS";

export const SET_SCENARIO_MESSAGES = "SET_SCENARIO_MESSAGES";
export const SET_SCENARIO_MINDMAP = "SET_SCENARIO_MINDMAP";
export const SET_SCENARIO_MINDMAP_MESSAGES = "SET_SCENARIO_MINDMAP_MESSAGES";
export const SET_SCENARIO_MINDMAP_SPECIAL_TALK = "SET_SCENARIO_MINDMAP_SPECIAL_TALK";
export const SET_SCENARIO_TEXTMAP = "SET_SCENARIO_TEXTMAP";
export const SET_SCENARIO_TALKS = "SET_SCENARIO_TALKS";
export const DISPLAY_MESSAGES = "DISPLAY_MESSAGES";
export const SET_USER_MESSAGES = "SET_USER_MESSAGES";
export const SET_IS_FETCHING_SCENARIO_DETAIL = "SET_IS_FETCHING_SCENARIO_DETAIL";
export const SET_FETCH_SCENARIO_DETAIL_ERROR = "SET_FETCH_SCENARIO_DETAIL_ERROR";
export const DELETE_SCENARIO_TALK_SUCCESS = "DELETE_SCENARIO_TALK_SUCCESS";

export const SET_IS_DELETING_SCENARIO_VERSION = "SET_IS_DELETING_SCENARIO_VERSION";
export const SET_DELETING_SCENARIO_VERSION_ERROR = "SET_DELETING_SCENARIO_VERSION_ERROR";
export const DELETE_FINISH_SUCCESS = "DELETE_FINISH_SUCCESS";

export const SET_SELECTED_EDIT_SCENARIO = "SET_SELECTED_EDIT_SCENARIO";
export const SET_IS_SAVING_ACTIVE_SCENARIO = "SET_IS_SAVING_ACTIVE_SCENARIO";

export const SET_IS_CREATING_RICH_MENU = "SET_IS_CREATING_RICH_MENU";
export const SET_IS_CREATING_RICH_MENU_ERROR = "SET_IS_CREATING_RICH_MENU_ERROR";

export const SET_IS_FETCHING_RICH_MENU = "SET_IS_FETCHING_RICH_MENU";
export const SET_IS_FETCHING_RICH_MENU_ERROR = "SET_IS_FETCHING_RICH_MENU_ERROR";
export const RICH_MENUS_DATA = "RICH_MENUS_DATA";

export const SET_IS_DELETING_RICH_MENU = "SET_IS_DELETING_RICH_MENU";
export const SET_ERROR_DELETING_RICH_MENU = "SET_ERROR_DELETING_RICH_MENU";

export const SET_ACTIVE_PRODUCTION_RICHMENU = "SET_ACTIVE_PRODUCTION_RICHMENU";
export const SET_ACTIVE_SANDBOX_RICHMENU = "SET_ACTIVE_SANDBOX_RICHMENU";
export const SET_DEFAULT_PRODUCTION_RICHMENU = "SET_DEFAULT_PRODUCTION_RICHMENU";
export const SET_DEFAULT_SANDBOX_RICHMENU = "SET_DEFAULT_SANDBOX_RICHMENU";
export const SET_BOSAI_PRODUCTION_RICHMENU = "SET_BOSAI_PRODUCTION_RICHMENU";
export const SET_BOSAI_SANDBOX_RICHMENU = "SET_BOSAI_SANDBOX_RICHMENU";

export const SET_PRODUCTION_RICHMENUS = "SET_PRODUCTION_RICHMENUS";
export const SET_SANDBOX_RICHMENUS = "SET_SANDBOX_RICHMENUS";
export const REMOVE_FROM_PRODUCTION_RICHMENUS = "REMOVE_FROM_PRODUCTION_RICHMENUS";
export const REMOVE_FROM_SANDBOX_RICHMENUS = "REMOVE_FROM_SANDBOX_RICHMENUS";
export const SET_IS_FETCHING_RICHMENU_INFO = "SET_IS_FETCHING_RICHMENU_INFO";
export const SET_ERROR_FETCHING_RICHMENU_INFO = "SET_ERROR_FETCHING_RICHMENU_INFO";
export const SET_IS_FETCHING_ALL_RICHMENUS = "SET_IS_FETCHING_ALL_RICHMENUS";
export const SET_ERROR_FETCHING_ALL_RICHMENUS = "SET_ERROR_FETCHING_ALL_RICHMENUS";
export const SET_ERROR_SETTING_RICHMENU = "SET_ERROR_SETTING_RICHMENU";
export const SET_SCENARIO_IS_IN_BOSAI_MODE = "SET_SCENARIO_IS_IN_BOSAI_MODE";
export const SET_SCENARIO_ACTIVE_RICHMENU = "SET_SCENARIO_ACTIVE_RICHMENU";
export const SET_BOSAI_SETTINGS_SUCCESS = "SET_BOSAI_SETTINGS_SUCCESS";
export const SET_IS_FETCHING_ZIP_CODES = "SET_IS_FETCHING_ZIP_CODES";
export const SET_IS_FETCHING_ZIP_CODES_ERROR = "SET_IS_FETCHING_ZIP_CODES_ERROR";
export const SET_ZIP_CODES = "SET_ZIP_CODES";

export const SET_IS_SAVING_TALK_NODES = "SET_IS_SAVING_TALK_NODES";
export const SET_SAVE_TALK_NODES_ERROR = "SET_SAVE_TALK_NODES_ERROR";
export const SAVE_TALK_NODES_SUCCESS = "SAVE_TALK_NODES_SUCCESS";

export const SET_IS_CHANGING_TALK_NAME = "SET_IS_CHANGING_TALK_NAME";
export const CHANGING_TALK_NAME_ERROR = "CHANGING_TALK_NAME_ERROR";
export const UPDATE_DATA_FOR_SCENARIO_TALK_NAME_CHANGE = "UPDATE_DATA_FOR_SCENARIO_TALK_NAME_CHANGE";
export const UPDATE_CHANGED_SCENARIO_VERSION_NAME = "UPDATE_CHANGED_SCENARIO_VERSION_NAME";
export const SET_IS_CHANGING_DISPLAY_VERSION_NAME = "SET_IS_CHANGING_DISPLAY_VERSION_NAME";
export const CHANGING_VERSION_NAME_ERROR = "CHANGING_VERSION_NAME_ERROR";

// Calendar mutations
export const SET_STATE_CALENDAR_OFF = "SET_STATE_CALENDAR_OFF";
export const SET_IS_FETCHING_SCHEDULE_DATA = "SET_IS_FETCHING_SCHEDULE_DATA";
export const SET_FETCH_SCHEDULE_ERROR = "SET_FETCH_SCHEDULE_ERROR";
export const SET_CALENDAR_DISPLAY_DATA = "SET_CALENDAR_DISPLAY_DATA";
export const SET_CALENDAR_DATA_OF_CATEGORIES_TREE = "SET_CALENDAR_DATA_OF_CATEGORIES_TREE";
export const SET_CALENDAR_DATA_OF_CATEGORIES_TREE_ERROR = "SET_CALENDAR_DATA_OF_CATEGORIES_TREE_ERROR";
export const SET_CALENDAR_DATA_OF_CATEGORIES_LARGE = "SET_CALENDAR_DATA_OF_CATEGORIES_LARGE";
export const SET_CALENDAR_DATA_OF_CATEGORIES_MEDIUM = "SET_CALENDAR_DATA_OF_CATEGORIES_MEDIUM";
export const SET_CALENDAR_DATA_OF_CATEGORIES_SMALL = "SET_CALENDAR_DATA_OF_CATEGORIES_SMALL";
export const RESET_CALENDAR_DATA_OF_CATEGORIES_SMALL = "RESET_CALENDAR_DATA_OF_CATEGORIES_SMALL";
export const RESET_CALENDAR_SELECTED_LARGE_CATEGORY = "RESET_CALENDAR_SELECTED_LARGE_CATEGORY";
export const RESET_CALENDAR_SELECTED_MEDIUM_CATEGORY = "RESET_CALENDAR_SELECTED_MEDIUM_CATEGORY";
export const RESET_CALENDAR_SELECTED_SMALL_CATEGORY = "RESET_CALENDAR_SELECTED_SMALL_CATEGORY";
export const SET_CALENDAR_SEARCH_SELECTED_LARGE_CATEGORY = "SET_CALENDAR_SEARCH_SELECTED_LARGE_CATEGORY";
export const SET_CALENDAR_SEARCH_SELECTED_MEDIUM_CATEGORY = "SET_CALENDAR_SEARCH_SELECTED_MEDIUM_CATEGORY";
export const SET_CALENDAR_SEARCH_SELECTED_SMALL_CATEGORY = "SET_CALENDAR_SEARCH_SELECTED_SMALL_CATEGORY";
export const SET_CALENDAR_DISPLAY_CONTENT_MODE = "SET_CALENDAR_DISPLAY_CONTENT_MODE";
export const SET_CALENDAR_DISPLAY_CATEGORY_ID = "SET_CALENDAR_DISPLAY_CATEGORY_ID";
export const SET_CALENDAR_DISPLAY_CATEGORY = "SET_CALENDAR_DISPLAY_CATEGORY";
export const SET_CALENDAR_IS_RELOAD_SCHEDULES_RESULT = "SET_CALENDAR_IS_RELOAD_SCHEDULES_RESULT";
export const SET_CALENDAR_RELOAD_SCHEDULES_START_DATE = "SET_CALENDAR_RELOAD_SCHEDULES_START_DATE";
export const SET_CALENDAR_SCHEDULES_RESULTS = "SET_CALENDAR_SCHEDULES_RESULTS";
export const SET_CALENDAR_SELECTED_START_DATE = "SET_CALENDAR_SELECTED_START_DATE";
export const SET_CALENDAR_SELECTED_END_DATE = "SET_CALENDAR_SELECTED_END_DATE";
export const SET_CALENDAR_TARGET_DATE = "SET_CALENDAR_TARGET_DATE";
export const SET_CALENDAR_MATCHING_DATA = "SET_CALENDAR_MATCHING_DATA";
export const SET_UPDATED_CALENDAR_SCHEDULES = "SET_UPDATED_CALENDAR_SCHEDULES";
export const SET_CALENDAR_SELECTED_TITLE = "SET_CALENDAR_SELECTED_TITLE";
export const SET_CALENDAR_EDIT_MODE = "SET_CALENDAR_EDIT_MODE";
export const SET_FETCHING_ALL_CALENDARS = "SET_FETCHING_ALL_CALENDARS";
export const SET_FETCHING_ALL_CALENDARS_ERROR = "SET_FETCHING_ALL_CALENDARS_ERROR";
export const SET_ALL_CALENDARS_LIST = "SET_ALL_CALENDARS_LIST";
export const SET_FETCHING_CALENDAR_COMMA_CSV = "SET_FETCHING_CALENDAR_COMMA_CSV";
export const SET_FETCHING_CALENDAR_COMMA_ERROR = "SET_FETCHING_CALENDAR_COMMA_ERROR";
export const SET_CALENDAR_COMMA_CSV_URL = "SET_CALENDAR_COMMA_CSV_URL";
export const SET_IMPORTING_CALENDAR_COMMA_CSV = "SET_IMPORTING_CALENDAR_COMMA_CSV";
export const SET_IMPORTING_CALENDAR_COMMA_ERROR = "SET_IMPORTING_CALENDAR_COMMA_ERROR";
export const SET_DELETED_CALENDAR = "SET_DELETED_CALENDAR";
export const SET_IS_UPDATING_CALENDAR = "SET_IS_UPDATING_CALENDAR";
export const SET_IS_UPDATING_CALENDAR_ERROR = "SET_IS_UPDATING_CALENDAR_ERROR";
export const SET_RESERVATION_CONTROL_INFO = "SET_RESERVATION_CONTROL_INFO";
export const SET_RESERVATION_CONTROL_INFO_WORK = "SET_RESERVATION_CONTROL_INFO_WORK";
export const SET_UPSERT_RESERVATION_CONTROL_INFO_WORK = "SET_UPSERT_RESERVATION_CONTROL_INFO_WORK";
export const SET_RESERVATION_CONTROL_INFO_ERROR = "SET_RESERVATION_CONTROL_INFO_ERROR";
export const SET_UPSERT_RESERVATION_CONTROL_INFO_ERROR = "SET_UPSERT_RESERVATION_CONTROL_INFO_ERROR";
export const SET_FETCHING_EXPORT_CALENDAR_CSV = "SET_FETCHING_EXPORT_CALENDAR_CSV";
export const SET_EXPORT_CALENDAR_CSV_URL = "SET_EXPORT_CALENDAR_CSV_URL";
export const SET_EXPORT_CALENDAR_CSV_ERROR = "SET_EXPORT_CALENDAR_CSV_ERROR";
export const SET_RESERVATION_ITEM_INFO = "SET_RESERVATION_ITEM_INFO";
export const SET_RESERVATION_ITEM_INFO_WORK = "SET_RESERVATION_ITEM_INFO_WORK";
export const SET_UPSERT_RESERVATION_ITEM_INFO_WORK = "SET_UPSERT_RESERVATION_ITEM_INFO_WORK";
export const SET_RESERVATION_ITEM_INFO_ERROR = "SET_RESERVATION_ITEM_INFO_ERROR";
export const SET_UPSERT_RESERVATION_ITEM_INFO_ERROR = "SET_UPSERT_RESERVATION_ITEM_INFO_ERROR";
export const SET_SELECTED_CATEGORY_NAMES = "SET_SELECTED_CATEGORY_NAMES";

export const SET_SERVICE_LIST = "SET_SERVICE_LIST";
export const ADD_PRODUCT_LIST_MAP_BY_SERVICE = "ADD_PRODUCT_LIST_MAP_BY_SERVICE";
export const SET_RESERVATION_PAYMENT_ITEMS_INFO = "SET_RESERVATION_PAYMENT_ITEMS_INFO";

// Bosai mutations
export const SET_SHELTER_LIST = "SET_SHELTER_LIST";
export const SET_IS_FETCHING_SHELTERS = "SET_IS_FETCHING_SHELTERS";
export const SET_SHELTER_SEARCH_ID = "SET_SHELTER_SEARCH_ID";
export const SET_FETCH_SHELTERS_ERROR = "SET_FETCH_SHELTERS_ERROR";
export const SET_SEARCH_SHELTER_CRITERIA = "SET_SEARCH_SHELTER_CRITERIA";
export const SET_IS_IMPORTING_SHELTERS = "SET_IS_IMPORTING_SHELTERS";
export const SET_IMPORTING_SHELTERS_ERROR = "SET_IMPORTING_SHELTERS_ERROR";
export const SET_IMPORTING_SHELTERS_SUCCESS = "SET_IMPORTING_SHELTERS_SUCCESS";
export const SET_DOWNLOADING_SHELTERS = "SET_DOWNLOADING_SHELTERS";
export const SET_DOWNLOADING_SHELTERS_ERROR = "SET_DOWNLOADING_SHELTERS_ERROR";
export const SET_IS_SEARCHING_SHELTER_LIST = "SET_IS_SEARCHING_SHELTER_LIST";
export const SET_OPERATION_STATUS = "SET_OPERATION_STATUS";
export const SET_FETCHING_OPERATION_STATUS = "SET_FETCHING_OPERATION_STATUS";
export const SET_FETCHING_OPERATION_STATUS_ERROR = "SET_FETCHING_OPERATION_STATUS_ERROR";

// quicksight mutation
export const SET_DASHBOARD_EMBED_URL = "SET_DASHBOARD_EMBED_URL";
export const SET_BUTTON_EMBED_URL = "SET_BUTTON_EMBED_URL";

// logs mutations
export const SET_SEARCH_RANGE_DATE_CRITERIA = "SET_SEARCH_RANGE_DATE_CRITERIA";
export const SET_SEARCH_FROM_DATE_CRITERIA = "SET_SEARCH_FROM_DATE_CRITERIA";
export const SET_IS_FETCHING_BI_DATA = "SET_IS_FETCHING_BI_DATA";
export const SET_FETCH_BI_ERROR = "SET_FETCH_BI_ERROR";
export const SET_FOLLOWERS_DATA = "SET_FOLLOWERS_DATA";
export const SET_DEMOGRAPHIC_DATA = "SET_DEMOGRAPHIC_DATA";
export const SET_MESSAGE_DELIVERED_DATA = "SET_MESSAGE_DELIVERED_DATA";
export const SET_RICHMENU_ACTIONS_DATA = "SET_RICHMENU_ACTIONS_DATA";
export const SET_RICHMENU_TABS_RATE_DATA = "SET_RICHMENU_TABS_RATE_DATA";
export const SET_SEGMENT_ACTIVATIONS_DATA = "SET_SEGMENT_ACTIVATIONS_DATA";
export const SET_SEGMENT_REGISTRANTS_DATA = "SET_SEGMENT_REGISTRANTS_DATA";
export const SET_SEGMENT_SUCCESS_ACTIVATIONS_DATA = "SET_SEGMENT_SUCCESS_ACTIVATIONS_DATA";
export const SET_LIFF_ACTIVATIONS_DATA = "SET_LIFF_ACTIVATIONS_DATA";
export const SET_LIFF_DETAIL_DATA = "SET_LIFF_DETAIL_DATA";

// system logs mutations
export const SET_SYSTEM_LOG_LISTS = "SET_SYSTEM_LOG_LISTS";
export const SET_IS_FETCHING_SYSTEM_LOG_LISTS = "SET_IS_FETCHING_SYSTEM_LOG_LISTS";
export const SET_FETCH_SYSTEM_LOG_LISTS_ERROR = "SET_FETCH_SYSTEM_LOG_LISTS_ERROR";

export const SET_AGGREGATE_TALK_LOG = "SET_AGGREGATE_TALK_LOG";
export const SET_FETCH_TALK_LOG_LISTS = "SET_FETCH_TALK_LOG_LISTS";
export const SET_AGGREGATE_TALK_LOG_ERROR = "SET_AGGREGATE_TALK_LOG_ERROR";
export const SET_FETCH_TALK_LOG_LISTS_ERROR = "SET_FETCH_TALK_LOG_LISTS_ERROR";

// payment mutations
export const SET_PAYMENT_SERVICE_LIST = "SET_PAYMENT_SERVICE_LIST";
export const SET_IS_FETCHING_PAYMENT_SERVICE_LIST = "SET_IS_FETCHING_PAYMENT_SERVICE_LIST";
export const ADD_PAYMENT_SERVICE_TO_LIST = "ADD_PAYMENT_SERVICE_TO_LIST";
export const UPDATE_PAYMENT_SERVICE_TO_LIST = "UPDATE_PAYMENT_SERVICE_TO_LIST";

export const SET_PRODUCT_LIST = "SET_PRODUCT_LIST";
export const ADD_PRODUCT_TO_LIST = "ADD_PRODUCT_TO_LIST";
export const UPDATE_PRODUCT_TO_LIST = "UPDATE_PRODUCT_TO_LIST";
export const REMOVE_PRODUCT_TO_LIST = "REMOVE_PRODUCT_TO_LIST";
export const SET_IS_FETCHING_PRODUCT_LIST = "SET_IS_FETCHING_PRODUCT_LIST";
export const SET_FETCHI_PRODUCT_LIST_ERROR = "SET_FETCHI_PRODUCT_LIST_ERROR";
export const SET_PRODUCTS_CSV_DOWNLOAD_URL = "SET_PRODUCTS_CSV_DOWNLOAD_URL";
export const SET_UPLOADED_BUCKET_KEY_OF_PRODUCTS_CSV = "SET_UPLOADED_BUCKET_KEY_OF_PRODUCTS_CSV";
export const UPDATE_PRODUCT_LIST_FILTER = "UPDATE_PRODUCT_LIST_FILTER";

export const SET_PRODUCT_CATEGORY_LIST = "SET_PRODUCT_CATEGORY_LIST";
export const ADD_PRODUCT_CATEGORY_TO_LIST = "ADD_PRODUCT_CATEGORY_TO_LIST";
export const UPDATE_PRODUCT_CATEGORY_TO_LIST = "UPDATE_PRODUCT_CATEGORY_TO_LIST";
export const REMOVE_PRODUCT_CATEGORY_TO_LIST = "REMOVE_PRODUCT_CATEGORY_TO_LIST";

export const SET_TAX_RATE_SETTINGS = "SET_TAX_RATE_SETTINGS";


// Spot group
export const SET_SPOT_GROUP_LIST = "SET_SPOT_GROUP_LIST";
export const SET_SPOT_GROUP = "SET_SPOT_GROUP";
export const SET_SPOT_TEMPLATES = "SET_SPOT_TEMPLATES";
export const ADD_TO_SPOT_GROUP_LIST = "ADD_TO_SPOT_GROUP_LIST";
export const UPDATE_SPOT_GROUP_LIST = "UPDATE_SPOT_GROUP_LIST";
export const REMOVE_FROM_SPOT_GROUP_LIST = "REMOVE_FROM_SPOT_GROUP_LIST";
export const REMOVE_MULTIPLE_FROM_SPOT_GROUP_LIST = "REMOVE_MULTIPLE_FROM_SPOT_GROUP_LIST";

export const SET_SPOT_GROUP_PREVIEW = "SET_SPOT_GROUP_PREVIEW";

export const SET_POSTBACK_DATA = "SET_POSTBACK_DATA";

export const SET_IS_FETCHING_SPOT_GROUP_LIST = "SET_IS_FETCHING_SPOT_GROUP_LIST";
export const SET_IS_FETCHING_SPOT_GROUP_BY_ID = "SET_IS_FETCHING_SPOT_GROUP_BY_ID";
export const SET_IS_FETCHING_SPOT_TEMPLATES = "SET_IS_FETCHING_SPOT_TEMPLATES";
export const SET_IS_CREATING_SPOT_GROUP = "SET_IS_CREATING_SPOT_GROUP";
export const SET_IS_UPDATING_SPOT_GROUP = "SET_IS_UPDATING_SPOT_GROUP";
export const SET_IS_DELETING_SPOT_GROUP = "SET_IS_DELETING_SPOT_GROUP";
export const SET_IS_BATCH_DELETING_SPOT_GROUP = "SET_IS_BATCH_DELETING_SPOT_GROUP";

export const SET_IS_FETCHING_SPOT_GROUP_PREVIEW = "SET_IS_FETCHING_SPOT_GROUP_PREVIEW";

export const SET_IS_FETCHING_POSTBACK_DATA = "SET_IS_FETCHING_POSTBACK_DATA";

export const SET_FETCH_SPOT_GROUP_LIST_ERROR = "SET_FETCH_SPOT_GROUP_LIST_ERROR";
export const SET_FETCH_SPOT_GROUP_BY_ID_ERROR = "SET_FETCH_SPOT_GROUP_BY_ID_ERROR";
export const SET_FETCH_SPOT_TEMPLATES_ERROR = "SET_FETCH_SPOT_TEMPLATES_ERROR";
export const SET_CREATE_SPOT_GROUP_ERROR = "SET_CREATE_SPOT_GROUP_ERROR";
export const SET_UPDATE_SPOT_GROUP_ERROR = "SET_UPDATE_SPOT_GROUP_ERROR";
export const SET_DELETE_SPOT_GROUP_ERROR = "SET_DELETE_SPOT_GROUP_ERROR";
export const SET_BATCH_DELETE_SPOT_GROUP_ERROR = "SET_BATCH_DELETE_SPOT_GROUP_ERROR";

export const SET_FETCH_SPOT_GROUP_PREVIEW_ERROR = "SET_FETCH_SPOT_GROUP_PREVIEW_ERROR";

export const SET_FETCH_POSTBACK_DATA_ERROR = "SET_FETCH_POSTBACK_DATA_ERROR";

// Spot list
export const SET_SPOT_LIST = "SET_SPOT_LIST";
export const SET_SPOT = "SET_SPOT";
export const SET_SELECTED_DISPLAY_TEMPLATE = "SET_SELECTED_DISPLAY_TEMPLATE";
export const SET_SPOT_ATTRIBUTES = "SET_SPOT_ATTRIBUTES";
export const SET_SPOT_TAG_OPTIONS = "SET_SPOT_TAG_OPTIONS";
export const SET_SPOT_TEMPLATE = "SET_SPOT_TEMPLATE";
export const SET_CSV_IMPORT_RESULT = "SET_CSV_IMPORT_RESULT";
export const SET_CSV_IMPORT_STATUS = "SET_CSV_IMPORT_STATUS";

export const ADD_TO_SPOT_LIST = "ADD_TO_SPOT_LIST";
export const UPDATE_SPOT_LIST = "UPDATE_SPOT_LIST";
export const REMOVE_FROM_SPOT_LIST = "REMOVE_FROM_SPOT_LIST";
export const REMOVE_MULTIPLE_FROM_SPOT_LIST = "REMOVE_MULTIPLE_FROM_SPOT_LIST";

export const SET_IS_FETCHING_SPOT_LIST = "SET_IS_FETCHING_SPOT_LIST";
export const SET_IS_FETCHING_SPOT_BY_ID = "SET_IS_FETCHING_SPOT_BY_ID";
export const SET_IS_CREATING_SPOT = "SET_IS_CREATING_SPOT";
export const SET_IS_UPDATING_SPOT = "SET_IS_UPDATING_SPOT";
export const SET_IS_DELETING_SPOT = "SET_IS_DELETING_SPOT";
export const SET_IS_BATCH_DELETING_SPOT = "SET_IS_BATCH_DELETING_SPOT";
export const SET_IS_IMPORTING_SPOT_CSV = "SET_IS_IMPORTING_SPOT_CSV";
export const SET_IS_BATCH_UPLOADING_SPOT_IMAGE = "SET_IS_BATCH_UPLOADING_SPOT_IMAGE";
export const SET_IS_FETCHING_CSV_IMPORT_STATUS = "SET_IS_FETCHING_CSV_IMPORT_STATUS";

export const SET_FETCH_SPOT_LIST_ERROR = "SET_FETCH_SPOT_LIST_ERROR";
export const SET_FETCH_SPOT_BY_ID_ERROR = "SET_FETCH_SPOT_BY_ID_ERROR";
export const SET_CREATE_SPOT_ERROR = "SET_CREATE_SPOT_ERROR";
export const SET_UPDATE_SPOT_ERROR = "SET_UPDATE_SPOT_ERROR";
export const SET_DELETE_SPOT_ERROR = "SET_DELETE_SPOT_ERROR";
export const SET_BATCH_DELETE_SPOT_ERROR = "SET_BATCH_DELETE_SPOT_ERROR";
export const SET_IMPORT_SPOT_CSV_ERROR = "SET_IMPORT_SPOT_CSV_ERROR";
export const SET_BATCH_UPLOAD_SPOT_IMAGE_ERROR = "SET_BATCH_UPLOAD_SPOT_IMAGE_ERROR";
export const SET_FETCH_CSV_IMPORT_STATUS_ERROR = "SET_FETCH_CSV_IMPORT_STATUS_ERROR";

// Route list
export const SET_ROUTE_LIST = "SET_ROUTE_LIST";
export const SET_ROUTE = "SET_ROUTE";
export const SET_ROUTE_POSTBACK = "SET_ROUTE_POSTBACK";
export const ADD_TO_ROUTE_LIST = "ADD_TO_ROUTE_LIST";
export const UPDATE_ROUTE_LIST = "UPDATE_ROUTE_LIST";
export const REMOVE_FROM_ROUTE_LIST = "REMOVE_FROM_ROUTE_LIST";
export const REMOVE_MULTIPLE_FROM_ROUTE_LIST = "REMOVE_MULTIPLE_FROM_ROUTE_LIST";

export const SET_IS_FETCHING_ROUTE_LIST = "SET_IS_FETCHING_ROUTE_LIST";
export const SET_IS_FETCHING_ROUTE_BY_ID = "SET_IS_FETCHING_ROUTE_BY_ID";
export const SET_IS_CREATING_ROUTE = "SET_IS_CREATING_ROUTE";
export const SET_IS_UPDATING_ROUTE = "SET_IS_UPDATING_ROUTE";
export const SET_IS_DELETING_ROUTE = "SET_IS_DELETING_ROUTE";
export const SET_IS_BATCH_DELETING_ROUTE = "SET_IS_BATCH_DELETING_ROUTE";

export const SET_FETCH_ROUTE_LIST_ERROR = "SET_FETCH_ROUTE_LIST_ERROR";
export const SET_FETCH_ROUTE_BY_ID_ERROR = "SET_FETCH_ROUTE_BY_ID_ERROR";
export const SET_CREATE_ROUTE_ERROR = "SET_CREATE_ROUTE_ERROR";
export const SET_UPDATE_ROUTE_ERROR = "SET_UPDATE_ROUTE_ERROR";
export const SET_DELETE_ROUTE_ERROR = "SET_DELETE_ROUTE_ERROR";
export const SET_BATCH_DELETE_ROUTE_ERROR = "SET_BATCH_DELETE_ROUTE_ERROR";