export const AUTH_SIGNIN = "AUTH_SIGNIN";
export const AUTH_SIGNOUT = "AUTH_SIGNOUT";
export const AUTH_FORGOT_PASSWORD = "AUTH_FORGOT_PASSWORD";
export const AUTH_FORGOT_PASSWORD_SUBMIT = "AUTH_FORGOT_PASSWORD_SUBMIT";
export const AUTH_COMPLETE_NEW_PASSWORD = "AUTH_COMPLETE_NEW_PASSWORD";
export const AUTH_GET_ATTR = "AUTH_GET_ATTR";
export const AUTH_REFRESH_USER = "AUTH_REFRESH_USER";
export const AUTH_DEFINE_USER_ABILITY = "AUTH_DEFINE_USER_ABILITY";
export const FETCH_PERMISSIONS_FROM_TEAM = "FETCH_PERMISSIONS_FROM_TEAM";

export const LOADING_START = "LOADING_START";
export const LOADING_STOP = "LOADING_STOP";

export const CHANGE_LANGUAGE = "CHANGE_LANGUAGE";

// settings actions define
export const FETCH_ACCESS_SETTINGS = "FETCH_ACCESS_SETTINGS";
export const UPDATE_ACCESS_SETTINGS = "UPDATE_ACCESS_SETTINGS";
export const FETCH_COMMON_SETTINGS = "FETCH_COMMON_SETTINGS";
export const UPDATE_COMMON_SETTINGS = "UPDATE_COMMON_SETTINGS";

// AWS Secrets Manager actions
export const FETCH_SECRETS = "FETCH_SECRETS";
export const UPDATE_SECRETS = "UPDATE_SECRETS";

// Editable Settings (Line settings + Damage report email)
export const FETCH_EDITABLE_SETTINGS = "FETCH_EDITABLE_SETTINGS";
export const UPDATE_EDITABLE_SETTINGS = "UPDATE_EDITABLE_SETTINGS";

// Cognito invite & verification email template customization
export const FETCH_MESSAGE_TEMPLATES = "FETCH_MESSAGE_TEMPLATES";
export const UPDATE_MESSAGE_TEMPLATES = "UPDATE_MESSAGE_TEMPLATES";

// Calendar settings actions
export const FETCH_CALENDAR_DISPLAY_SETTINGS = "FETCH_CALENDAR_DISPLAY_SETTINGS";
export const UPDATE_CALENDAR_DISPLAY_SETTINGS = "UPDATE_CALENDAR_DISPLAY_SETTINGS";

export const IMPORT_CALENDAR_CATEGORIES_CSV_DATA = "IMPORT_CALENDAR_CATEGORIES_CSV_DATA";
export const EXPORT_CATEGORIES_CSV_DATA = "EXPORT_CATEGORIES_CSV_DATA";
export const FETCH_CALENDAR_CATEGORIES = "FETCH_CALENDAR_CATEGORIES";
export const CREATE_CALENDAR_CATEGORIES = "CREATE_CALENDAR_CATEGORIES";
export const UPDATE_CALENDAR_CATEGORY = "UPDATE_CALENDAR_CATEGORY";
export const DELETE_CALENDAR_CATEGORY = "DELETE_CALENDAR_CATEGORY";
export const DELETE_CALENDAR_INFO = "DELETE_CALENDAR_INFO";

// Public configuration
export const FETCH_PUBLIC_CONFIGURATION = "FETCH_PUBLIC_CONFIGURATION";

// Line actions
export const INIT_FORM_SCHEMA = "INIT_FORM_SCHEMA";
export const UPDATE_SURVEY_SCHEMA = "UPDATE_SURVEY_SCHEMA";
export const ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_TREE_1 = "ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_TREE_1";
export const ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_LARGE_1 = "ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_LARGE_1";
export const ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_MEDIUM_1 = "ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_MEDIUM_1";
export const ACTION_SET_CALENDAR_SEARCH_SELECTED_LARGE_CATEGORY_1 =
  "ACTION_SET_CALENDAR_SEARCH_SELECTED_LARGE_CATEGORY_1";
  export const ACTION_FETCH_PAYMENT_SERVICE_LIST = "ACTION_FETCH_PAYMENT_SERVICE_LIST";

// forms actions define
export const FETCH_SURVEY_CONFIGS = "FETCH_SURVEY_CONFIGS";
export const FETCH_SURVEY_CONFIGS_BY_ID = "FETCH_SURVEY_CONFIGS_BY_ID";
export const FETCH_FORM_CONFIGS = "FETCH_FORM_CONFIGS";
export const REGISTER_SURVEY_CONFIG = "REGISTER_SURVEY_CONFIG";
export const UPDATE_SURVEY_CONFIG = "UPDATE_SURVEY_CONFIG";
export const FETCH_SURVEY_RESULTS_COUNT = "FETCH_SURVEY_RESULTS_COUNT";

// Member forms actions define
export const CHECK_SAIBAN_EXIST = "CHECK_SAIBAN_EXIST";
export const CHECK_SAIBAN_EXIST_FOR_UPDATE = "CHECK_SAIBAN_EXIST_FOR_UPDATE";

export const FETCH_ALL_MEMBER_FORM_CONFIGS = "FETCH_ALL_MEMBER_FORM_CONFIGS";
export const FETCH_MEMBER_FORM_CONFIG = "FETCH_MEMBER_FORM_CONFIG";
export const REGISTER_MEMBER_SURVEY_CONFIG = "REGISTER_MEMBER_SURVEY_CONFIG";
export const UPDATE_MEMBER_SURVEY_CONFIG = "UPDATE_MEMBER_SURVEY_CONFIG";

// data managements action define
export const SELECT_SURVEY_TO_WORK = "SELECT_SURVEY_TO_WORK";
export const SELECT_FORM_TO_WORK = "SELECT_FORM_TO_WORK";
export const FETCH_DATA_OF_SELECTED_MEMBER_LIST = "FETCH_DATA_OF_SELECTED_MEMBER_LIST";
export const CLEAR_CACHE_ITEMS_SURVEY_RESULTS = "CLEAR_CACHE_ITEMS_SURVEY_RESULTS";
export const FILTER_SURVEY_RESULTS = "FILTER_SURVEY_RESULTS";
export const RESET_SURVEY_RESULTS = "RESET_SURVEY_RESULTS";
export const UPDATE_DATA_SURVEY = "UPDATE_DATA_SURVEY";
export const CHECK_RESERVABLE = "CHECK_RESERVABLE";
export const UPDATE_MEMBER_SURVEY = "UPDATE_MEMBER_SURVEY";
export const RESET_LINE_USER_ID = "RESET_LINE_USER_ID";
export const CREATE_DATA_SURVEY = "CREATE_DATA_SURVEY";
export const CREATE_MEMBER_SURVEY = "CREATE_MEMBER_SURVEY";
export const UPDATE_IS_RESEARCH_DATA = "UPDATE_IS_RESEARCH_DATA";
export const CREATE_CSV_FOR_SURVEY_RESULTS = "CREATE_CSV_FOR_SURVEY_RESULTS";
export const DELETE_SURVEY_RESULTS = "DELETE_SURVEY_RESULTS";
export const DELETE_SURVEY_RESULTS_OLD = "DELETE_SURVEY_RESULTS_OLD";
export const DELETE_ALL_SURVEY_RESULTS = "DELETE_ALL_SURVEY_RESULTS";
export const CREATE_CSV_APPENDING_FOR_SURVEY_RESULTS = "CREATE_CSV_APPENDING_FOR_SURVEY_RESULTS";
export const SEND_SURVEY_UNICAST = "SEND_SURVEY_UNICAST";
export const UPLOAD_IMPORTED_APPEND_TYPE_SURVEY_RESULTS_CSV = "UPLOAD_IMPORTED_APPEND_TYPE_SURVEY_RESULTS_CSV";
export const INITIALIZE_IMPORTING_APPEND_TYPE_SURVEY_RESULTS = "INITIALIZE_IMPORTING_APPEND_TYPE_SURVEY_RESULTS";
export const SEND_MULTIPLE_SURVEY_UNICAST = "SEND_MULTIPLE_SURVEY_UNICAST";
export const SET_IS_CALENDAR_DISPLAY = "SET_IS_CALENDAR_DISPLAY";
export const SET_CALENDAR_VALUE = "SET_CALENDAR_VALUE";
export const SET_CALENDAR_CONFIG = "SET_CALENDAR_CONFIG";
export const CREATE_CSV_APPENDING_SEARCH_CRITERIA_FOR_SURVEY_RESULTS =
  "CREATE_CSV_APPENDING_SEARCH_CRITERIA_FOR_SURVEY_RESULTS";
export const FETCH_RESERVATION_PAYMENT_SERVICE = "FETCH_RESERVATION_PAYMENT_SERVICE";
export const FETCH_PAYMENT_SERVICE = "FETCH_PAYMENT_SERVICE";
export const FETCH_PRODUT_LIST = "FETCH_PRODUT_LIST";
export const FETCH_TAX_RATE = "FETCH_TAX_RATE";
export const FETCH_PAYMENT_RESULTS_CSV_URL = "FETCH_PAYMENT_RESULTS_CSV_URL";

// segment actions
export const FETCH_ALL_SEGMENT_DELIVERIES = "FETCH_ALL_SEGMENT_DELIVERIES";
export const CREATE_SEGMENT_DELIVERY = "CREATE_SEGMENT_DELIVERY";
export const UPDATE_SEGMENT_DELIVERY = "UPDATE_SEGMENT_DELIVERY";
export const DELETE_SEGMENT_DELIVERY = "DELETE_SEGMENT_DELIVERY";

export const FETCH_MAIL_DELIVERY_LIST = "FETCH_MAIL_DELIVERY_LIST";
export const FETCH_TALK_DELIVERY_LIST = "FETCH_TALK_DELIVERY_LIST";

// distribution condition actions
export const FETCH_DISTRIBUTION_CONFIGS = "FETCH_DISTRIBUTION_CONFIGS";
export const CHECK_SUBJECT_CONDITION = "CHECK_SUBJECT_CONDITION";
export const CHECK_BODY_CONDITION = "CHECK_BODY_CONDITION";
export const CHANGE_BODY_CONTENT = "CHANGE_BODY_CONTENT";

export const UPDATE_CONDITION = "UPDATE_CONDITION";
export const UPDATE_CONTENT = "UPDATE_CONTENT";

// distribution status actions
export const FETCH_DISTRIBUTION_STATUS = "FETCH_DISTRIBUTION_STATUS";
export const RESEND_DISTRIBUTION_ITEM = "RESEND_DISTRIBUTION_ITEM";

// distribution delivery actions
export const CREATE_DISTRIBUTION_DELIVERY = "CREATE_DISTRIBUTION_DELIVERY";
export const UPDATE_DISTRIBUTION_DELIVERY = "UPDATE_DISTRIBUTION_DELIVERY";
export const SAVE_DISTRIBUTION_DELIVERY_DRAFT = "SAVE_DISTRIBUTION_DELIVERY_DRAFT";
export const SEND_BROADCAST_MESSAGES = "SEND_BROADCAST_MESSAGES";
export const GET_DISTRIBUTION_STATISTICS = "GET_DISTRIBUTION_STATISTICS";

export const FETCH_DISTRIBUTION_DETAIL_BY_ID = "FETCH_DISTRIBUTION_DETAIL_BY_ID";
export const FETCH_DISTRIBUTION_LIST = "FETCH_DISTRIBUTION_LIST";
export const DELETE_DISTRIBUTION_ITEM = "DELETE_DISTRIBUTION_ITEM";

// distribution outside segment actions
export const FETCH_SCENARIO_TALKS_FOR_TALK_DISTRIBUTION = "FETCH_SCENARIO_TALKS_FOR_TALK_DISTRIBUTION";

// reservation reminder actions
export const FETCH_ALL_REMIND_CATEGORIES = "FETCH_ALL_REMIND_CATEGORIES";
export const FETCH_REMINDER_CONFIGURATION = "FETCH_REMINDER_CONFIGURATION";
export const UPSERT_REMINDER_CONFIGURATION = "UPSERT_REMINDER_CONFIGURATION";
export const DELETE_REMINDER_CONFIGURATION = "DELETE_REMINDER_CONFIGURATION";
export const FETCH_ALL_SURVEY_CONFIGS = "FETCH_ALL_SURVEY_CONFIGS";

export const FETCH_ONE_REMINDER_EXECUTION_HISTORY = "FETCH_ONE_REMINDER_EXECUTION_HISTORY";
export const FETCH_REMINDER_EXECUTION_HISTORIES = "FETCH_REMINDER_EXECUTION_HISTORIES";

// date relative reminder actions
export const DELETE_DATE_RELATIVE_REMINDER_SETTINGS = "DELETE_DATE_RELATIVE_REMINDER_SETTINGS";
export const REGIST_RELATIVE_REMINDER_SETTINGS = "REGIST_RELATIVE_REMINDER_SETTINGS";
export const FIND_RELATIVE_REMINDER_SETTINGS = "FIND_RELATIVE_REMINDER_SETTINGS";
export const CLEAR_RELATIVE_REMINDER_SETTINGS = "CLEAR_RELATIVE_REMINDER_SETTINGS";
export const DELETE_DATE_RELATIVE_REMINDER_SETTING = "DELETE_DATE_RELATIVE_REMINDER_SETTING";

//scenarios module actions
export const FETCH_ALL_SCENARIOS = "FETCH_ALL_SCENARIOS";
export const FETCH_SCENARIO_DETAIL = "FETCH_SCENARIO_DETAIL";
export const UPLOAD_IMPORT_FILE = "UPLOAD_IMPORT_FILE";
export const UPLOAD_CSV_FILE = "UPLOAD_CSV_FILE";
export const UPLOAD_LOCATION_CSV_FILE = "UPLOAD_LOCATION_CSV_FILE";
export const DOWNLOAD_CSV_FILE = "DOWNLOAD_CSV_FILE";
export const DOWNLOAD_EXPORT_FILE = "DOWNLOAD_EXPORT_FILE";
export const CHANGE_ACTIVE_SCENARIO = "CHANGE_ACTIVE_SCENARIO";
export const UPDATE_TEXT_MAPPING = "UPDATE_TEXT_MAPPING";
export const UPDATE_SCENARIO_DATA = "UPDATE_SCENARIO_DATA";
export const UPDATE_TALK_UPDATED_TIME = "UPDATE_TALK_UPDATED_TIME";
export const DELETE_SCENARIO_VERSION = "DELETE_SCENARIO_VERSION";
export const CREATE_SCENARIO_VERSION = "CREATE_SCENARIO_VERSION";
export const ADD_COMPOSITE_MESSAGE = "ADD_COMPOSITE_MESSAGE";
export const FETCH_ACTIVE_SCENARIO_DATA = "FETCH_ACTIVE_SCENARIO_DATA";
export const CREATE_SIMPLE_SPECIAL_SCENARIO = "CREATE_SIMPLE_SPECIAL_SCENARIO";
export const SET_DEFAULT_RICH_MENU = "SET_DEFAULT_RICH_MENU";
export const DELETE_TALK = "DELETE_TALK";
export const DELETE_SPECIAL_SCENARIO = "DELETE_SPECIAL_SCENARIO";
export const SAVE_ACTIVE_SCENARIO = "SAVE_ACTIVE_SCENARIO";
export const DELETE_SCENARIO_MESSAGE = "DELETE_SCENARIO_MESSAGE";
export const FETCH_SCENARIO_DETAIL_TALK = "FETCH_SCENARIO_DETAIL_TALK";

export const CREATE_RICH_MENU = "CREATE_RICH_MENU";
export const COPY_RICH_MENU = "COPY_RICH_MENU";
export const DELETE_RICH_MENU = "DELETE_RICH_MENU";

export const FETCH_ACTIVE_RICHMENUS = "FETCH_ACTIVE_RICHMENUS";
export const FETCH_ALL_RICHMENUS = "FETCH_ALL_RICHMENUS";
export const FETCH_DEFAULT_RICHMENUS = "FETCH_DEFAULT_RICHMENUS";
export const SET_ACTIVE_RICH_MENU = "SET_ACTIVE_RICH_MENU";
export const TOGGLE_BOSAI_MODE = "TOGGLE_BOSAI_MODE";
export const SAVE_BOSAI_SETTINGS_ITEM = "SAVE_BOSAI_SETTINGS_ITEM";
export const GET_ZIP_CODES = "GET_ZIP_CODES";
export const DELETE_ZIP_CODES = "DELETE_ZIP_CODES";
export const DOWNLOAD_ZIP_CSV = "DOWNLOAD_ZIP_CSV";
export const FETCH_SPECIAL_TALK_FLOW_START = "FETCH_SPECIAL_TALK_FLOW_START";
export const FETCH_ALL_RICHMENUS_ALIAS_LIST = "FETCH_ALL_RICHMENUS_ALIAS_LIST";

export const SAVE_TALK_NODES = "SAVE_TALK_NODES";

export const FETCH_SCENARIO_SPECIFIC_TALKS = "FETCH_SCENARIO_SPECIFIC_TALKS";
export const UPDATE_SCENARIO_TALK_NAME = "UPDATE_SCENARIO_TALK_NAME";
export const AGGREGATE_TALK_LOG = "AGGREGATE_TALK_LOG";
export const FETCH_TALK_LOG_LISTS = "FETCH_TALK_LOG_LISTS";
export const UPDATE_DISPLAY_SCENARIO_VERSION_NAME = "UPDATE_DISPLAY_SCENARIO_VERSION_NAME";

// Calendar actions
export const ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_TREE = "ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_TREE";
export const ACTION_RESET_CALENDAR_DATA_OF_CATEGORIES_TREE = "ACTION_RESET_CALENDAR_DATA_OF_CATEGORIES_TREE";
export const ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_TREE_ERROR = "ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_TREE_ERROR";
export const ACTION_SET_CALENDAR_SEARCH_SELECTED_LARGE_CATEGORY = "ACTION_SET_CALENDAR_SEARCH_SELECTED_LARGE_CATEGORY";
export const ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_LARGE = "ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_LARGE";
export const ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_MEDIUM = "ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_MEDIUM";
export const ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_SMALL = "ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_SMALL";
export const ACTION_RESET_CALENDAR_DATA_OF_CATEGORIES_SMALL = "ACTION_RESET_CALENDAR_DATA_OF_CATEGORIES_SMALL";
export const ACTION_RESET_CALENDAR_SELECTED_LARGE_CATEGORY = "ACTION_RESET_CALENDAR_SELECTED_LARGE_CATEGORY";
export const ACTION_RESET_CALENDAR_SELECTED_MEDIUM_CATEGORY = "ACTION_RESET_CALENDAR_SELECTED_MEDIUM_CATEGORY";
export const ACTION_RESET_CALENDAR_SELECTED_SMALL_CATEGORY = "ACTION_RESET_CALENDAR_SELECTED_SMALL_CATEGORY";
export const ACTION_SET_CALENDAR_SEARCH_SELECTED_MEDIUM_CATEGORY =
  "ACTION_SET_CALENDAR_SEARCH_SELECTED_MEDIUM_CATEGORY";
export const ACTION_SET_CALENDAR_SEARCH_SELECTED_SMALL_CATEGORY = "ACTION_SET_CALENDAR_SEARCH_SELECTED_SMALL_CATEGORY";
export const ACTION_SET_CALENDAR_ID_TO_SELECTED_CATEGORY = "ACTION_SET_CALENDAR_ID_TO_SELECTED_CATEGORY";
export const ACTION_SET_CALENDAR_DISPLAY_CONTENT_MODE = "ACTION_SET_CALENDAR_DISPLAY_CONTENT_MODE";
export const ACTION_SET_CALENDAR_IS_RELOAD_SCHEDULES_RESULT = "ACTION_SET_CALENDAR_IS_RELOAD_SCHEDULES_RESULT";
export const ACTION_SET_CALENDAR_RELOAD_SCHEDULES_START_DATE = "ACTION_SET_CALENDAR_RELOAD_SCHEDULES_START_DATE";
export const ACTION_RESET_CALENDAR_RELOAD_SCHEDULES = "ACTION_RESET_CALENDAR_RELOAD_SCHEDULES";
export const ACTION_SET_CALENDAR_SCHEDULES_IMPORT_CSV = "ACTION_SET_CALENDAR_SCHEDULES_IMPORT_CSV";
export const ACTION_SET_CALENDAR_SCHEDULES_RESULTS = "ACTION_SET_CALENDAR_SCHEDULES_RESULTS";
export const ACTION_SET_CALENDAR_SELECTED_START_DATE = "ACTION_SET_CALENDAR_SELECTED_START_DATE";
export const ACTION_SET_CALENDAR_SELECTED_END_DATE = "ACTION_SET_CALENDAR_SELECTED_END_DATE";
export const ACTION_SET_CALENDAR_TARGET_DATE = "ACTION_SET_CALENDAR_TARGET_DATE";
export const ACTION_SET_UPDATED_CALENDAR_SCHEDULES = "ACTION_SET_UPDATED_CALENDAR_SCHEDULES";
export const ACTION_SET_CALENDAR_SELECTED_TITLE = "ACTION_SET_CALENDAR_SELECTED_TITLE";
export const ACTION_SET_CALENDAR_EDIT_MODE = "ACTION_SET_CALENDAR_EDIT_MODE";
export const GET_ALL_CALENDARS = "GET_ALL_CALENDARS";
export const ACTION_UPDATE_DAY_OFF = "ACTION_UPDATE_DAY_OFF";
export const ACTION_SET_CALENDAR_OFF = "ACTION_SET_CALENDAR_OFF";
export const ACTION_GET_CALENDAR_COMMA_CSV = "ACTION_GET_CALENDAR_COMMA_CSV";
export const ACTION_IMPORT_CALENDAR_COMMA_CSV = "ACTION_IMPORT_CALENDAR_COMMA_CSV";
export const ACTION_DELETE_CALENDAR = "ACTION_DELETE_CALENDAR";
export const UPDATE_CALENDAR_SETTINGS = "UPDATE_CALENDAR_SETTINGS";
export const ACTION_SET_DISPLAY_CATEGORY_ID = "ACTION_SET_DISPLAY_CATEGORY_ID";
export const ACTION_GET_RESERVATION_CONTROL_INFO = "ACTION_GET_RESERVATION_CONTROL_INFO";
export const ACTION_UPSERT_RESERVATION_CONTROL_INFO = "ACTION_UPSERT_RESERVATION_CONTROL_INFO";
export const ACTION_EXPORT_CALENDAR_CSV = "ACTION_EXPORT_CALENDAR_CSV";
export const ACTION_GET_RESERVATION_ITEM_INFO = "ACTION_GET_RESERVATION_ITEM_INFO";
export const ACTION_UPSERT_RESERVATION_ITEM_INFO = "ACTION_UPSERT_RESERVATION_ITEM_INFO";
export const ACTION_SET_CALENDAR_INFO = "ACTION_SET_CALENDAR_INFO";
export const ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_CRITERIA_TREE =
  "ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_CRITERIA_TREE";
export const ACTION_RESET_CATEGORIES_CRITERIA = "ACTION_RESET_CATEGORIES_CRITERIA";
export const ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_MEDIUM_CRITERIA =
  "ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_MEDIUM_CRITERIA";
export const ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_SMALL_CRITERIA =
  "ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_SMALL_CRITERIA";
export const FETCH_SURVEY_AND_CALENDAR_PERMISSIONS = "FETCH_SURVEY_AND_CALENDAR_PERMISSIONS";
export const ACTION_UPDATE_CATEGORIES_ORDER = "ACTION_UPDATE_CATEGORIES_ORDER";
export const ACTION_SET_SELECTED_CATEGORY_NAMES = "ACTION_SET_SELECTED_CATEGORY_NAMES";

export const ACTION_GET_SERVICE_LIST = "ACTION_GET_SERVICE_LIST";
export const ACTION_GET_PORDUCT_LIST = "ACTION_GET_PORDUCT_LIST";
export const ACTION_GET_RESERVATION_PAYMENT_ITEMS_INFO = "ACTION_GET_RESERVATION_PAYMENT_ITEMS_INFO";
export const ACTION_UPSERT_CALENDER_PAYMENT_ITEMS = "ACTION_UPSERT_CALENDER_PAYMENT_ITEMS";

//Bosai actions
export const FETCH_SHELTER_LIST = "FETCH_SHELTER_LIST";
export const SEARCH_ALL_SHELTERS = "SEARCH_ALL_SHELTERS";
export const UPLOAD_SHELTER_CSV_FILE = "UPLOAD_SHELTER_CSV_FILE";
export const DOWNLOAD_SHELTER_LIST = "DOWNLOAD_SHELTER_LIST";
export const DELETE_SHELTERS = "DELETE_SHELTERS";
export const EDIT_SHELTERS = "EDIT_SHELTERS";
export const DELETE_ALL_SHELTERDATA = "DELETE_ALL_SHELTERDATA";
export const FETCH_BOSAI_OPERATION_STATUS = "FETCH_BOSAI_OPERATION_STATUS";

// quicksight actions
export const FETCH_DASHBOARD_EMBED_URL = "FETCH_DASHBOARD_EMBED_URL";
export const FETCH_BUTTON_EMBED_URL = "FETCH_BUTTON_EMBED_URL";

// Log actions
export const FETCH_FOLLOWERS_DATA = "FETCH_FOLLOWERS_DATA";
export const FETCH_DEMOGRAPHIC_DATA = "FETCH_DEMOGRAPHIC_DATA";

export const FETCH_RICHMENU_ACTIONS_DATA = "FETCH_RICHMENU_ACTIONS_DATA";
export const FETCH_RICHMENU_TABS_RATE_DATA = "FETCH_RICHMENU_TABS_RATE_DATA";

export const FETCH_SEGMENT_ACTIVATIONS_DATA = "FETCH_SEGMENT_ACTIVATIONS_DATA";
export const FETCH_SEGMENT_REGISTRANTS_DATA = "FETCH_SEGMENT_REGISTRANTS_DATA";
export const FETCH_SEGMENT_SUCCESS_ACTIVATIONS_DATA = "FETCH_SEGMENT_SUCCESS_ACTIVATIONS_DATA";
export const FETCH_LIFF_ACTIVATIONS_DATA = "FETCH_LIFF_ACTIVATIONS_DATA";
export const FETCH_LIFF_DETAIL_DATA = "FETCH_LIFF_DETAIL_DATA";

// system log actions
export const FETCH_SYSTEM_LOG_LISTS = "FETCH_SYSTEM_LOG_LISTS";
export const SAVE_SYSTEM_LOG_DOWNLOAD_LOG = "SAVE_SYSTEM_LOG_DOWNLOAD_LOG";
export const SAVE_SYSTEM_LOG_DOWNLOAD_TALKLOG = "SAVE_SYSTEM_LOG_DOWNLOAD_TALKLOG";

// payment actions
export const FETCH_PAYMENT_SERVICE_LIST = "FETCH_PAYMENT_SERVICE_LIST";
export const SAVE_PAYMENT_SERVICE = "SAVE_PAYMENT_SERVICE";
export const DELETE_PAYMENT_SERVICES = "DELETE_PAYMENT_SERVICES";

export const FETCH_PRODUCT_LIST = "FETCH_PRODUCT_LIST";
export const SAVE_PRODUCT = "SAVE_PRODUCT";
export const DELETE_PRODUCS = "DELETE_PRODUCS";
export const IMPORT_PRODUCTS_CSV = "IMPORT_PRODUCTS_CSV";
export const SAVE_UPLOADED_PRODUCTS_CSV = "SAVE_UPLOADED_PRODUCTS_CSV";
export const EXPORT_PRODUCTS_CSV = "EXPORT_PRODUCTS_CSV";
export const SAVE_PRODUCT_ORDERS = "SAVE_PRODUCT_ORDERS";

export const FETCH_PRODUCT_CATEGORY_LIST = "FETCH_PRODUCT_CATEGORY_LIST";
export const SAVE_PRODUCT_CATEGORY = "SAVE_PRODUCT_CATEGORY";
export const DELETE_PRODUCT_CATEGORIES = "DELETE_PRODUCT_CATEGORIES";

export const FETCH_TAX_RATE_SETTINGS = "FETCH_TAX_RATE_SETTINGS";
export const UPSERT_TAX_RATE_SETTINGS = "UPSERT_TAX_RATE_SETTINGS";

// #region damagereport
export const CREATE_DAMAGE_REPORT_TALK_VERSION = "CREATE_DAMAGE_REPORT_TALK_VERSION"
export const DELETE_SCENARIO_VERSION_NEW = "DELETE_SCENARIO_VERSION_NEW"
export const DELETE_DAMAGE_REPORT_TALK_VERSION = "DELETE_DAMAGE_REPORT_TALK_VERSION"
export const SET_DAMAGE_REPORT_TALK_VERSION_ACTIVE = "SET_DAMAGE_REPORT_TALK_VERSION_ACTIVE"
export const UPDATE_DAMAGE_REPORT_SCENARIO_DATA = "UPDATE_DAMAGE_REPORT_SCENARIO_DATA"
// #endregion


// Spot group
export const FETCH_SPOT_GROUP_LIST = "FETCH_SPOT_GROUP_LIST";
export const FETCH_SPOT_GROUP_BY_ID = "FETCH_SPOT_GROUP_BY_ID";
export const FETCH_SPOT_TEMPLATES = "FETCH_SPOT_TEMPLATES";
export const CREATE_SPOT_GROUP = "CREATE_SPOT_GROUP";
export const UPDATE_SPOT_GROUP = "UPDATE_SPOT_GROUP";
export const DELETE_SPOT_GROUP = "DELETE_SPOT_GROUP";
export const BATCH_DELETE_SPOT_GROUP = "BATCH_DELETE_SPOT_GROUP";

export const FETCH_SPOT_GROUP_PREVIEW = "FETCH_SPOT_GROUP_PREVIEW";

export const FETCH_POSTBACK_DATA = "FETCH_POSTBACK_DATA";

// Spot list
export const FETCH_SPOT_LIST = "FETCH_SPOT_LIST";
export const FETCH_SPOT_BY_ID = "FETCH_SPOT_BY_ID";
export const CREATE_SPOT = "CREATE_SPOT";
export const UPDATE_SPOT = "UPDATE_SPOT";
export const DELETE_SPOT = "DELETE_SPOT";
export const BATCH_DELETE_SPOT = "BATCH_DELETE_SPOT";

export const IMPORT_SPOT_CSV = "IMPORT_SPOT_CSV";
export const BATCH_UPLOAD_SPOT_IMAGE = "BATCH_UPLOAD_SPOT_IMAGE";
export const FETCH_CSV_IMPORT_STATUS = "FETCH_CSV_IMPORT_STATUS";

// Route list
export const FETCH_ROUTE_LIST = "FETCH_ROUTE_LIST";
export const FETCH_ROUTE_BY_ID = "FETCH_ROUTE_BY_ID";
export const CREATE_ROUTE = "CREATE_ROUTE";
export const UPDATE_ROUTE = "UPDATE_ROUTE";
export const DELETE_ROUTE = "DELETE_ROUTE";
export const BATCH_DELETE_ROUTE = "BATCH_DELETE_ROUTE";