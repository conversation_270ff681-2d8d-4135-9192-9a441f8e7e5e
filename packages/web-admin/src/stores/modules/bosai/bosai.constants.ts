export const SHELTER_STATUS = [
  {
    displayText: "未開設",
    color: "red",
  },
  {
    displayText: "開設",
    color: "green",
  },
  {
    displayText: "満",
    color: "yellow",
  },
 ] as any;

export const SHELTER_CSV_TYPES = {
  "避難先名称（避難所・避難場所）": "string",
  "避難先名称（かな）": "string",
  住所: "string",
  緯度: "float",
  経度: "float",
  "電話番号（連絡先）": "string",
  避難所種別: "intReq",
  災害種別: "intReq",
  情報入力者メールアドレス: "string",
  "ＩＤ1(団体コード)": "string",
  "ＩＤ2(避難所の通し番号)": "string",
  情報公開可否: "boolean",
  最終更新日時: "date",
  "施設面積（㎡）/ 屋外": "int",
  "施設面積（㎡）/ 屋内": "int",
  想定収容人数: "int",
  コロナ想定収容人数: "int",
  ネットワーク環境の有無: "boolean",
  各携帯キャリアの電波状況等: "boolean",
  隔離可能場所の有無: "int",
  "COVID-19対応備蓄設備の有無": "boolean",
  ステータス: "int",
  備考: "string",
  システムID: "string",
} as any;

export const TYPE_OF_SHELTERS = {
  1: "避難所",
  2: "臨時避難所",
  3: "避難所を兼ねる避難場所",
  4: "広域避難場所:開設措置なし",
  5: "一時避難場所:開設措置なし",
  9: "福祉避難所",
};

export const TYPE_OF_DISASTERS = {
  1: "洪水（風水害）",
  2: "崖崩れ、土石流及び地滑り",
  3: "高潮",
  4: "地震",
  5: "津波",
  6: "大規模な火事",
  7: "内水氾濫",
  8: "火山現象",
} as any;

export const QUARANTINE_VALUES = {
  0: "不明",
  1: "隔離場所を用意可能",
  2: "隔離場所を用意可能でない",
};
