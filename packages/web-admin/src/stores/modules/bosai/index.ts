import { defineStore } from "pinia";

import {
  GetAllShelters,
  SearchShelters,
  UploadCSVFileToS3,
  ImportShelterData,
  DownloadSheltersAsCSV,
  BatchWriteItem,
  DeleteAllShelterData,
  GetBosaiOperationStatus,
} from "../../../services/bosai.service";

import {
  SET_FETCH_SHELTERS_ERROR,
  SET_IMPORTING_SHELTERS_ERROR,
  SET_DOWNLOADING_SHELTERS_ERROR,
  SET_FETCHING_OPERATION_STATUS_ERROR,
} from "../../mutation-types";

import { generateUUID } from "../../../utils/uuidUtils";

const ERR_SKELETON = (store: any, type: any, err: any) => {
  if (err.toString() == "Error: Network Error") {
    err = new String(
      "ページの再読み込み実施を実施してください。それでも表示されない場合は、\n" +
        "サーバへアクセスが混み合っているか、メンテナンス中の可能性があります。\n" +
        "しばらく時間をおいてから、もう一度アクセスしてください。"
    );
  }
  store[type] = err;
};

export default {
  namespaced: false,
};

export const useBosaiStore = defineStore({
  id: 'bosaiStore',
  
  state: () => ({
    shelterList: [] as any,
    searchShelterList: [] as any,
    isFetchingShelters: false,
    shelterSearchProcessId: null,
    fetchingSheltersError: null,
    shelterSearchFields: {
      keywordSearch: "",
      selectedDisasterTypes: [],
      selectedShelterTypes: [],
      selectedShelterStatuses: [],
      selectedQuarantineValues: [],
      selectAllDisasterType: false,
      selectAllShelterType: false,
      selectAllShelterStatus: false,
      selectAllQuarantineValues: false,
      infoDisclosure: "設定しない",
      network: "設定しない",
      cellPhone: "設定しない",
      equipmentCovid: "設定しない",
    },
    isImportingShelters: false,
    importingSheltersError: null,
    importingSheltersSuccess: null,
    isDownloadingShelters: false,
    downloadingSheltersError: null,
    isSearchingShelters: false,
    asyncOperationStatus: {
      deleteAllSheltersStatus: {
        username: '',
        status: '',
        updatedAt: 0,
        errorMessage: '',
      }
    } as any,
    isFetchingBosaiOperationStatus: false,
    fetchBosaiOperationError: null,
    searchParams: {},
  }),
  
  actions: {
    // mudations
    setShelterList(value: any) {
      if (value && Array.isArray(value)) {
        this.shelterList.push(...value);
      } else {
        this.shelterList = [];
      }
    },
    setSearchShelterList(value: any) {
      if (value && Array.isArray(value)) {
        this.searchShelterList.push(...value);
      } else {
        this.searchShelterList = [];
      }
    },
    setIsFetchingShelters(value: any) { 
      this.isFetchingShelters = value; 
    },
    setShelterSearchId(value: any) {
      this.shelterSearchProcessId = value; 
    },
    setFetchSheltersError(value: any) {
      this.fetchingSheltersError = value;
    },
    setSearchShelterCriteria(value: any) { 
      this.shelterSearchFields = value;
    },
    setIsImportingShelters(value: any) {
      this.isImportingShelters = value;
    },
    setImportingSheltersError(value: any) {
      this.importingSheltersError = value;
    },
    setImportingSheltersSuccess(value: any) {
      this.importingSheltersSuccess = value;
    },
    setDownloadingShelters(value: any) {
      this.isDownloadingShelters = value;
    },
    setDownloadingSheltersError(value: any) {
      this.downloadingSheltersError = value;
    },
    setIsSearchingShelterList(value: any) {
      this.isSearchingShelters = value;
    },
    setOperationStatus(payload: any) {
      const {value, actionName} = payload;
      this.asyncOperationStatus[actionName] = value;
    },
    setFetchingOperationStatus(value: any) {
      this.isFetchingBosaiOperationStatus = value;
    },
    setFetchingOperationStatusError(value: any) {
      this.fetchBosaiOperationError = value;
    },
    
    // actions
    async fetchAllSheltersHelper(search = true, searchPayload = {}) {
      var responseJson: any = {};
      var interrupted = false;
      
      this.setShelterList(null);
      this.setSearchShelterList(null);
      const searchId = generateUUID();
      this.setShelterSearchId(searchId);

      // 検索条件ある場合のみ一致データと全てのデータをそれぞれ取得する
      if (search) {
        do {
          responseJson = await SearchShelters(responseJson, searchPayload);
          if (responseJson.result == "ERROR") {
            throw new Error(responseJson.exception);
          }
  
          //Break out of loop if shelter search action was called again
          if (this.shelterSearchProcessId !== searchId) {
            interrupted = true;
            break;
          }
          this.setSearchShelterList(responseJson.items);
        } while (responseJson.lastEvaluatedKey);
      }
    
      do {
        responseJson = await GetAllShelters(responseJson);
        if (responseJson.result == "ERROR") {
          throw new Error(responseJson.exception);
        }

        //Break out of loop if shelter search action was called again
        if (this.shelterSearchProcessId !== searchId) {
          interrupted = true;
          break;
        }
    
        if (!search) {
          this.setSearchShelterList(responseJson.items);
        }
        this.setShelterList(responseJson.items);
        this.setIsFetchingShelters(false);
      } while (responseJson.lastEvaluatedKey);
    
      return interrupted;
    },

    async handle_upload_timeout() {
      try {
        this.setIsFetchingShelters(true);
        this.setFetchSheltersError(null);
    
        await this.fetchAllSheltersHelper();
    
        let timeout_err = new String(
          "インポートに時間がかかるため、タイムアウトエラーが発生しました。\n" +
          "表示されている総件数がインポートされた件数と登録されていた件数の合計と一致しない場合、しばらく待ってからリロードしてください。"
        );
    
        ERR_SKELETON(this, SET_FETCH_SHELTERS_ERROR, timeout_err);
    
      } catch (err: any) {
        ERR_SKELETON(this, SET_FETCH_SHELTERS_ERROR, err);
      }
    },

    async fetchShelterList () {
      var interrupted = false;
  
      try {
        this.setIsFetchingShelters(true);
        this.setFetchSheltersError, null;
  
        interrupted = await this.fetchAllSheltersHelper();
      } catch (err: any) {
        ERR_SKELETON(this, SET_FETCH_SHELTERS_ERROR, err);
      } finally {
        if (!interrupted) {
          this.setIsFetchingShelters(false);
        }
      }
    },

    async editShelters(payload: any) {
      try {
        this.setIsFetchingShelters(true);
        this.setFetchSheltersError(null);
        const writeResponse = await BatchWriteItem(payload.shelters, null);
        if (writeResponse.result == "ERROR") {
          throw new Error(writeResponse.error_message);
        }
        await this.fetchAllSheltersHelper(true, this.searchParams);
      } catch (err: any) {
        ERR_SKELETON(this, SET_FETCH_SHELTERS_ERROR, err);
      } finally {
        this.setIsFetchingShelters(false);
      }
    },

    async deleteShelters(listOfSheltersToDelete: any) {
      try {
        this.setIsFetchingShelters(true);
        this.setFetchSheltersError(null);
  
        let itemsToDelete = <any>[];
        listOfSheltersToDelete.forEach((shelter: any) => {
          itemsToDelete.push(
            {
              uniqueId: shelter.uniqueId,
            }
          );
        });
  
        const deleteResponse = await BatchWriteItem(null, itemsToDelete);
  
        await this.fetchAllSheltersHelper(true, this.searchParams);
      } catch (err: any) {
        ERR_SKELETON(this, SET_FETCH_SHELTERS_ERROR, err);
      } finally {
        this.setIsFetchingShelters(false);
      }
    },

    async deleteAllShelterData () {
      try {
        this.setFetchSheltersError(null);
  
        let deleteResponse = await DeleteAllShelterData();
        return deleteResponse;
  
      } catch (err: any) {
        ERR_SKELETON(this, SET_FETCH_SHELTERS_ERROR, err);
      }
    },

    async searchAllShelters(payload: any) {
      var interrupted = false;
  
      try {
        this.setIsFetchingShelters(true);
        this.setIsSearchingShelterList(true);
        this.setFetchSheltersError(null);
  
        interrupted = await this.fetchAllSheltersHelper(true, payload);
      } catch (err: any) {
        console.error(err);
        ERR_SKELETON(this, SET_FETCH_SHELTERS_ERROR, err);
      } finally {
        if (!interrupted) {
          this.setIsFetchingShelters(false);
        }
      }
    },

    async uploadShelterCsvFile(payload: any) {
      try {
        this.setIsImportingShelters(true);
        this.setImportingSheltersError(null);
        this.setImportingSheltersSuccess(null);
  
        await UploadCSVFileToS3(payload.fileData);
  
        const responseJson = await ImportShelterData();
        if (responseJson.result == "ERROR") {
          this.setImportingSheltersError(responseJson.exception);
          throw new Error(responseJson.exception);
        } else {
          this.setImportingSheltersSuccess("成功");
          this.setIsFetchingShelters(true);
          await this.fetchAllSheltersHelper(true, this.searchParams);
        }
      } catch (err: any) {
        if (err.toString() == "Error: Network Error") {
          // Try fetching again due to a possible timeout error
          await this.handle_upload_timeout();
        } else {
          ERR_SKELETON(this, SET_IMPORTING_SHELTERS_ERROR, err);
        }
      } finally {
        this.setIsFetchingShelters(false);
        this.setIsImportingShelters(false);
      }
    },

    async downloadShelterList () {
      try {
        this.setDownloadingShelters(true);
        this.setDownloadingSheltersError(null);
  
        const uploadResponse = await DownloadSheltersAsCSV();
  
        const link = document.createElement("a");
        link.setAttribute("download", "");
        link.href = uploadResponse.csv_url;
        link.click();
      } catch (err: any) {
        ERR_SKELETON(this, SET_DOWNLOADING_SHELTERS_ERROR, err);
      } finally {
        this.setDownloadingShelters(false);
      }
    },

    async fetchBosaiOperationStatus () {
      try {
        this.setFetchingOperationStatus(true);
  
        const deleteAllResponse = await GetBosaiOperationStatus("deleteAllShelters");
        if(deleteAllResponse.result === 'OK') {
          this.setOperationStatus({
            actionName: "deleteAllSheltersStatus",
            value: deleteAllResponse.data,
          });
        } else {
          this.setFetchingOperationStatusError(deleteAllResponse.error_message);
          return;
        }
      } catch (err: any) {
        ERR_SKELETON(this, SET_FETCHING_OPERATION_STATUS_ERROR, err);
      } finally {
        this.setFetchingOperationStatus(false);
      }
    },

    setSearchParams(params: any) {
      this.searchParams = params;
    },
  },
});
