export const processAppendTypeSurvey = (state, payload, result) => {
  for (let categoryIdKeyIndex in state.categoryIdKeys) {
    let categoryIdKey = state.categoryIdKeys[categoryIdKeyIndex];
    if (payload.hasOwnProperty("bunrui_id") && payload.bunrui_id.hasOwnProperty("full")) {
      result[categoryIdKey] = payload.bunrui_id.full;
      result.bunruiIdFull = payload.bunrui_id.full;
    }
  }

  let originalData:any = null;
  if (payload.originalData && payload.originalData !== undefined && 
    payload.originalData.tag1 != undefined && payload.originalData.isLine != undefined
  ) {
    originalData = payload.originalData;
  }

  if (originalData) {
    result.tag1 = originalData.tag1;
    result.tag2 = originalData.tag2;
    result.tag3 = originalData.tag3;
    result.isLine = originalData.isLine;
    result.reservationDate = originalData.reservationDate;
  }

  return result
}

export const getUploadImportedAppendTypeCSVError = async (error) => {
  if (error.response && error.response.status === 409 && error.response.data &&
    error.response.data.code && error.response.data.code === "temporary_locked") {
    return {
      result: "ERROR",
      errorMessage: "複数のリクエストが送信されました。予約が正しく行われているかご確認ください。",
    };
  }

  if (error.response.status === 403) {
    return {
      result: "ERROR",
      errorMessage: "権限がないためCSVのインポートは出来ません。",
    };
  }
  
  return {
    result: "ERROR",
    errorMessage: "CSVファイルバリデーションながらエラーが発生しました。",
  };
}