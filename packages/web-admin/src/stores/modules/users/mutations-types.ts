// Users
export const SET_SEARCH_ATTRIBUTE = "SET_SEARCH_ATTRIBUTE";
export const SET_SEARCH_KEYWORD = "SET_SEARCH_KEYWORD";
export const RESET_ALL_SEARCH_CRITERIA = "RESET_ALL_SEARCH_CRITERIA";

export const SET_USER_LIST_DATA_TABLE_OPTIONS = "SET_USER_LIST_DATA_TABLE_OPTIONS";
export const SET_PAGINATION_RANGE = "SET_PAGINATION_RANGE";
export const ADD_TO_PAGINATION_TOKEN = "ADD_TO_PAGINATION_TOKEN";
export const ADD_TO_PAGE_LIST = "ADD_TO_PAGE_LIST";

export const SET_USER_LIST = "SET_USER_LIST";
export const ADD_TO_USER_LIST = "ADD_TO_USER_LIST";
export const UPDATE_USER_LIST = "UPDATE_USER_LIST";
export const RESET_USER_LIST = "RESET_USER_LIST";
export const SET_USERNAME = "SET_USERNAME";
export const SET_USER_DETAIL = "SET_USER_DETAIL";
export const SET_USER_GROUPS = "SET_USER_GROUPS";

export const SET_IS_FETCHING_USER_LIST = "SET_IS_FETCHING_USER_LIST";
export const SET_IS_FETCHING_USER_DETAIL = "SET_IS_FETCHING_USER_DETAIL";
export const SET_IS_FETCHING_USER_GROUPS = "SET_IS_FETCHING_USER_GROUPS";
export const SET_IS_RESETTING_PASSWORD = "SET_IS_RESETTING_PASSWORD";
export const SET_IS_CHANGING_PASSWORD = "SET_IS_CHANGING_PASSWORD";
export const SET_IS_DISABLING_USER = "SET_IS_DISABLING_USER";
export const SET_IS_ENABLING_USER = "SET_IS_ENABLING_USER";
export const SET_IS_ADDING_USER_TO_GROUP = "SET_IS_ADDING_USER_TO_GROUP";
export const SET_IS_REMOVING_USER_FROM_GROUP = "SET_IS_REMOVING_USER_FROM_GROUP";
export const SET_IS_CREATING_USER = "SET_IS_CREATING_USER";
export const SET_IS_DELETING_USER = "SET_IS_DELETING_USER";

export const SET_FETCH_USER_LIST_ERROR = "SET_FETCH_USER_LIST_ERROR";
export const SET_FETCH_USER_DETAIL_ERROR = "SET_FETCH_USER_DETAIL_ERROR";
export const SET_FETCH_USER_GROUPS_ERROR = "SET_FETCH_USER_GROUPS_ERROR";
export const SET_RESET_PASSWORD_ERROR = "SET_RESET_PASSWORD_ERROR";
export const SET_CHANGE_PASSWORD_ERROR = "SET_CHANGE_PASSWORD_ERROR";
export const SET_DISABLE_USER_ERROR = "SET_DISABLE_USER_ERROR";
export const SET_ENABLE_USER_ERROR = "SET_ENABLE_USER_ERROR";
export const SET_ADD_USER_TO_GROUP_ERROR = "SET_ADD_USER_TO_GROUP_ERROR";
export const SET_REMOVE_USER_FROM_GROUP_ERROR = "SET_REMOVE_USER_FROM_GROUP_ERROR";
export const SET_CREATE_USER_ERROR = "SET_CREATE_USER_ERROR";
export const SET_DELETE_USER_ERROR = "SET_DELETE_USER_ERROR";

// Teams
export const SET_SELECTED_TEAM_CRITERIA = "SET_SELECTED_TEAM_CRITERIA";
export const SET_TEAM_FILTER_KEYWORD = "SET_TEAM_FILTER_KEYWORD";

export const SET_TEAM_LIST = "SET_TEAM_LIST";
export const APPEND_TO_TEAM_LIST = "APPEND_TO_TEAM_LIST";
export const REMOVE_FROM_TEAM_LIST = "REMOVE_FROM_TEAM_LIST";
export const UPDATE_DETAIL_IN_TEAM_LIST = "UPDATE_DETAIL_IN_TEAM_LIST";
export const SET_TEAM_DETAIL = "SET_TEAM_DETAIL";

export const SET_IS_FETCHING_TEAM_LIST = "SET_IS_FETCHING_TEAM_LIST";
export const SET_IS_FETCHING_TEAM_DETAIL = "SET_IS_FETCHING_TEAM_DETAIL";
export const SET_IS_CREATING_TEAM = "SET_IS_CREATING_TEAM";
export const SET_IS_UPDATING_TEAM = "SET_IS_UPDATING_TEAM";
export const SET_IS_DELETING_TEAM = "SET_IS_DELETING_TEAM";

export const SET_FETCH_TEAM_LIST_ERROR = "SET_FETCH_TEAM_LIST_ERROR";
export const SET_FETCH_TEAM_DETAIL_ERROR = "SET_FETCH_TEAM_DETAIL_ERROR";
export const SET_CREATE_TEAM_ERROR = "SET_CREATE_TEAM_ERROR";
export const SET_UPDATE_TEAM_ERROR = "SET_UPDATE_TEAM_ERROR";
export const SET_DELETE_TEAM_ERROR = "SET_DELETE_TEAM_ERROR";

// Groups
export const SET_GROUPS = "SET_GROUPS";
export const SET_IS_FETCHING_GROUP_LIST = "SET_IS_FETCHING_GROUP_LIST";
export const SET_FETCH_GROUP_LIST_ERROR = "SET_FETCH_GROUP_LIST_ERROR";

export const SET_IS_CREATING_GROUP = "SET_IS_CREATING_GROUP";
export const SET_CREATE_GROUP_ERROR = "SET_CREATE_GROUP_ERROR";

export const SET_GROUP_DETAIL = "SET_GROUP_DETAIL";
export const SET_IS_FETCHING_GROUP_DETAIL = "SET_IS_FETCHING_GROUP_DETAIL";
export const SET_FETCH_GROUP_DETAIL_ERROR = "SET_FETCH_GROUP_DETAIL_ERROR";
export const SET_GROUP_USERS = "SET_GROUP_USERS";
export const SET_IS_FETCHING_GROUP_USERS = "SET_IS_FETCHING_GROUP_USERS";
export const SET_FETCH_GROUP_USERS_ERROR = "SET_FETCH_GROUP_USERS_ERROR";
export const SET_IS_UPDATING_GROUP_DETAIL = "SET_IS_UPDATING_GROUP_DETAIL";
export const SET_UPDATE_GROUP_DETAIL_ERROR = "SET_UPDATE_GROUP_DETAIL_ERROR";

// Policy
export const SET_POLICY_SETTINGS = "SET_POLICY_SETTINGS";

export const SET_IS_FETCHING_POLICY_SETTINGS = "SET_IS_FETCHING_POLICY_SETTINGS";
export const SET_IS_UPDATING_POLICY_SETTINGS = "SET_IS_UPDATING_POLICY_SETTINGS";

export const SET_FETCH_POLICY_SETTINGS_ERROR = "SET_FETCH_POLICY_SETTINGS_ERROR";
export const SET_UPDATE_POLICY_SETTINGS_ERROR = "SET_UPDATE_POLICY_SETTINGS_ERROR";

export const SET_DELETE_USER_FOR_LOCAL = 'SET_DELETE_USER_FOR_LOCAL';
