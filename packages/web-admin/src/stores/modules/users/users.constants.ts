export const USER_HEADERS = [
  {
    name: 'userName',
    label: 'ユーザー名',
    field: 'userName',
    required: undefined,
    align: 'left' as "left" | "right" | "center",
    sortable: true,
    classes: '',
  },
  {
    name: 'email',
    label: 'メールアドレス',
    field: 'email',
    required: undefined,
    align: 'left' as "left" | "right" | "center",
    sortable: true,
    classes: 'tw-text-sky-600',
  },
  {
    name: 'status',
    label: 'ステータス',
    field: 'status',
    required: undefined,
    align: 'left' as "left" | "right" | "center",
    sortable: true,
    classes: 'tw-text-sky-600',
  },
  {
    name: 'enabled',
    label: '有効',
    field: 'enabled',
    required: undefined,
    align: 'left' as "left" | "right" | "center",
    sortable: true,
    classes: 'tw-text-sky-600',
  },
  {
    name: 'updatedAt',
    label: '更新日',
    field: 'updatedAt',
    required: undefined,
    align: 'left' as "left" | "right" | "center",
    sortable: true,
    classes: 'tw-text-sky-600',
  },
  {
    name: 'createdAt',
    label: '作成日',
    field: 'createdAt',
    required: undefined,
    align: 'left' as "left" | "right" | "center",
    sortable: true,
    classes: 'tw-text-sky-600',
  },
  {
    name: 'editButton',
    label: '',
    field: 'editButton',
    required: undefined,
    align: 'left' as "left" | "right" | "center",
    sortable: false,
    classes: 'tw-text-sky-600',
  },
];

export const FORM_HEADERS = [
  { text: "タイトル", value: "surveyTitle" },
  { text: "更新日時", value: "updatedAt", filterable: false, width: '180px' },
  { text: "更新者", value: "updatedBy", filterable: false, width: '100px'  },
  { text: "登録画面", value: "registrationScreen", sortable: false, filterable: false, width: '120px' },
  { text: "照会画面", value: "inquiryScreen", sortable: false, filterable: false, width: '120px' },
  { text: "有効/無効", value: "surveyStatus", filterable: false, width: '120px' },
  { text: "", value: "formSetting", sortable: false, filterable: false, width: '100px' },
  { text: "", value: "edit", sortable: false, filterable: false, width: '100px' },
];

export const USER_SEARCH_ATTRIBUTE_OPTIONS = [
  { text: "ユーザー名", value: "username" },
  { text: "メールアドレス", value: "email" },
  { text: "ステータス", value: "status" },
  { text: "有効", value: "enabled" },
  { text: "チーム", value: "teamId" },
];

export const USER_ENABLED_TAGS = [
  { text: "全部", value: null, select: false },
  { text: "有効", value: "Enabled", select: false },
  { text: "無効", value: "Disabled", select: false },
];

export const TEAM_HEADERS = [
  { text: "チーム名", value: "teamName" , class: "blue-grey--text" },
  { text: "更新日", value: "updatedAt", width: 165 , class: "blue-grey--text" },
  { text: "作成日", value: "createdAt", width: 165 , class: "blue-grey--text" },
  { text: "", value: "editButton", width: 42 , class: "blue-grey--text" },
];

export const TEAM_SEARCH_CRITERIA_OPTIONS = [
  { text: "チーム名", value: "teamName" },
  { text: "更新日", value: "updatedAt" },
  { text: "作成日", value: "createdAt" },
];

export const ACCESS_LEVEL = [
  { text: "アドミン", value: "admins" },
  { text: "メンバー", value: "members" },
  { text: "ゲスト", value: "guests" },
  { text: "オペレーター", value: "operators" },
];

export const GROUP_HEADERS = [
  { name: 'groupName', label: "グループ名", field: "groupName" },
  { name: 'description', label: "説明", field: "description" }, 
  { name: 'precedence', label: "優先順位", field: "precedence" }, 
  { name: 'updatedAt', label: "更新日", field: "updatedAt" }, 
  { name: 'createdAt', label: "作成日", field: "createdAt" },
];

export const USER_STATUSES = [
  { text: "未承認", value: "UNCONFIRMED", color: "grey" },
  { text: "承認済み", value: "CONFIRMED", color: "green" },
  { text: "パスワード設定必要", value: "RESET_REQUIRED", color: "red" },
  { text: "パスワード更新必要", value: "FORCE_CHANGE_PASSWORD", color: "orange" },
];

export const USER_API_ERRORS = [
  {
    code: "UsernameExistsException",
    message: "このユーザ名は既に使用されています、別のユーザー名で作成してください。",
  },
];
