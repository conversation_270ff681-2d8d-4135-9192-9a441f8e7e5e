import { defineStore } from "pinia";
import dayjs from "dayjs";
import { Auth } from "aws-amplify";
import md5 from "md5";

import { UsersRequestParametersBuilder } from "@/parameters/users.parameters";
import { UserService } from "@/services/users.service";

import { USER_STATUSES, USER_API_ERRORS, TEAM_SEARCH_CRITERIA_OPTIONS } from "../../modules/users/users.constants";

export const DEFAULT_SELECTED_TEAM_CRITERIA = TEAM_SEARCH_CRITERIA_OPTIONS[0];

export const DEFAULT_TEAM_FILTER_KEYWORD = {
  stringSearch: null,
  updatedDateFrom: null,
  updatedDateTo: null,
  createdDateFrom: null,
  createdDateTo: null,
};

import _ from "lodash";
import { ErrorModel, OptionsModel, PolicySettings, UserDetail, UserGroups } from "@/types/index";
import { useAuthStore } from "../auth.module";
const errors = USER_API_ERRORS;
const service = new UserService();

export default {
  namespaced: false,
};

export const useUsersStore = defineStore({
  id: 'usersStore',

  state: () => ({
    // User
    searchAttribute: null as OptionsModel | null,
    searchKeyword: null as OptionsModel | null,

    // user-states
    userList: [] as any,
    username: null,
    userDetail: {} as UserDetail,
    userGroups: [] as UserGroups[],
    totalUser: 0 as number,

    dataTableOptions: {
      sortBy: ["updatedAt"],
      sortDesc: [true],
      page: 1,
      itemsPerPage: 50,
    },
    paginationToken: [{ page: 1, token: null }],
    paginationRange: {
      start: 0,
      end: 0,
    },
    pageList: [] as any,
    totalPages: 0,
    isLastPage: false,

    isFetchingUserList: false,
    isFetchingUserDetail: false,
    isCreatingUser: false,
    isDeletingUser: false,
    isFetchingUserGroups: false,
    isResettingPassword: false,
    isChangingPassword: false,
    isDisablingUser: false,
    isEnablingUser: false,
    isAddingUserToGroup: false,
    isRemovingUserFromGroup: false,

    fetchUserDetailError: null as ErrorModel | null,
    fetchUsersError: null,
    createUserError: null as any,
    fetchUserGroupsError: null,
    resetPasswordError: null,
    changePasswordError: null as ErrorModel | null,
    disableUserError: null,
    enableUserError: null,
    deleteUserError: null,
    addUserToGroupError: null,
    removeUserFromGroupError: null,

    // Team
    // team-search
    teamFilterList: {},
    selectedTeamCriteria: DEFAULT_SELECTED_TEAM_CRITERIA as OptionsModel | null,
    teamFilterKeyword: { ...DEFAULT_TEAM_FILTER_KEYWORD },

    // team-states
    teamList: [] as any,
    teamDetail: {},

    isFetchingTeamList: false,
    isFetchingTeamDetail: false,
    isCreatingTeam: false,
    isUpdatingTeam: false,
    isDeletingTeam: false,

    fetchTeamListError: null,
    fetchTeamDetailError: null,
    createTeamError: null,
    updateTeamError: null as ErrorModel | null,
    deleteTeamError: null as ErrorModel | null,

    // Group (Access-level)
    groupList: [] as any,
    groupDetail: {} as any,
    groupUsers: {} as any,

    isFetchingGroupList: false,
    isFetchingGroupDetail: false,
    isCreatingGroup: false,
    isUpdatingGroup: false,
    isFetchingGroupUsers: false,

    fetchGroupListError: null,
    fetchGroupDetailError: null,
    createGroupError: null,
    updateGroupError: null as any,
    fetchGroupUsersError: null,

    // Policy
    policySettings: {} as PolicySettings,

    isFetchingPolicySettings: false,
    isUpdatingPolicySettings: false,

    fetchPolicySettingsError: null,
    updatePolicySettingsError: null,
  }),

  getters: {
    // Team
    getTeamFilterList: (state) => {
      return state.teamList
        ? state.teamList.filter((obj: any) => {
            const criteria = state.selectedTeamCriteria;
            const { stringSearch, updatedDateFrom, updatedDateTo, createdDateFrom, createdDateTo }: any =
            state.teamFilterKeyword;

            // Date search
            if (criteria?.value === "updatedAt") {
              // NOTE: {updatedAt} is saved in seconds, not miliseconds. So instead of moment(objTime), use moment.unix(objTime)
              const objTime = obj[criteria.value];

              let isMatchingUpdatedFrom = updatedDateFrom
                ? dayjs.unix(objTime).format("YYYY-MM-DD") >= updatedDateFrom
                : true;
              let isMatchingUpdatedTo = updatedDateTo ? dayjs.unix(objTime).format("YYYY-MM-DD") <= updatedDateTo : true;
              return isMatchingUpdatedFrom && isMatchingUpdatedTo;
            } else if (criteria?.value === "createdAt") {
              // NOTE: {createdAt} is saved in seconds, not miliseconds. So instead of moment(objTime), use moment.unix(objTime)
              const objTime = obj[criteria.value];

              let isMatchingCreatedFrom = createdDateFrom
                ? dayjs.unix(objTime).format("YYYY-MM-DD") >= createdDateFrom
                : true;
              let isMatchingCreatedTo = createdDateTo ? dayjs.unix(objTime).format("YYYY-MM-DD") <= createdDateTo : true;
              return isMatchingCreatedFrom && isMatchingCreatedTo;
            }

            let converted_stringSearch = "";
            if(stringSearch) {
              converted_stringSearch = stringSearch.toLowerCase().trim();
            }

            // String search
            return stringSearch ? obj[criteria?.value ?? ''].toString().toLowerCase().trim().includes(converted_stringSearch) : true;
          })
        : [];
    },

    // Team options
    teamOptions() {
      let options: any = [];

      if (useAuthStore().isAdministrator) {
        options.push({
          value: "Administrator",
          text: "アドミニストレータ",
        });
      }

      options.push(
        ...this.teamList.map((obj: any) => {
          return {
            value: obj.teamId,
            text: obj.teamName,
          };
        })
      );

      return options;
    },

    isTeamAdmin: () => (currentUser: any, groupName: any) => {
      const [targetTeamId] = groupName.split(":");
      return currentUser.groups.some((currentUserGroup: any) => {
        const [currentUserTeamId, currentUserAccessLevel] = currentUserGroup.split(":");
        return targetTeamId === currentUserTeamId && currentUserAccessLevel === "admins";
      });
    },
  },

  actions: {
    // mutation
    // User
    setSearchAttribute(value: any) {
      this.searchAttribute = value;
    },

    setSearchKeyword(value: any) {
      this.searchKeyword = value;
    },

    resetAllSearchCriteria() {
      this.searchAttribute = null;
      this.searchKeyword = null;
    },

    setUserListDataTableOptions(value: any) {
      this.dataTableOptions = value;
    },

    setPaginationRange(value: any) {
      this.paginationRange = {
        start: value.start,
        end: value.end,
      };
    },

    addToPaginationToken(value: any) {
      const { dataTableOptions } = this;

      if (value.paginationToken) {
        const next = {
          page: dataTableOptions.page + 1,
          token: value.paginationToken,
        };
        this.paginationToken.push(next);
        this.isLastPage = false;
      } else {
        this.isLastPage = true;
      }

      this.totalPages = Math.ceil(this.userList.length / this.dataTableOptions.itemsPerPage);
    },

    addToPageList(value: any) {
      this.pageList.push(value);
    },

    setUserList(value: any) {
      value = value.map((obj: any) => {
        return convertUserData(obj);
      });
      this.userList = _.sortBy(value, ["updatedAt"]);
    },

    addToUserList(value: any) {
      this.userList=[];
      const next = value.Users.map((obj: any) => {
        return convertUserData(obj);
      });
      this.userList.push(...next);
      this.userList = _.sortBy(this.userList, ["updatedAt"]);
    },

    updateUserList(value: any) {
      const updated = value.Users.map((obj: any) => {
        return convertUserData(obj);
      });

      const { paginationRange } = this;

      this.userList.splice(paginationRange.start - 1, updated.length, ...updated);
      this.userList = _.sortBy(this.userList, ["updatedAt"]);
    },

    resetUserList() {
      this.dataTableOptions.page = 1;
      this.paginationRange = {
        start: 0,
        end: 0,
      };
      this.paginationToken = [{ page: 1, token: null }];
      this.pageList = [];
      this.totalPages = 0;
      this.userList = [];
    },

    setUsername(value: any) {
      this.username = value;
    },

    setUserDetail(value: any) {
      this.userDetail = convertUserDetailData(value);
    },

    setUserGroups(value: any) {
      this.userGroups = value.map((obj: any) => {
        return {
          ...obj,
          groupName: obj.GroupName,
        };
      });
    },

    setIsFetchingUserList(value: any) {
      this.isFetchingUserList = value;
    },

    setIsFetchingUserDetail(value: any) {
      this.isFetchingUserDetail = value;
    },

    setIsFetchingUserGroups(value: any) {
      this.isFetchingUserGroups = value;
    },

    setIsResettingPassword(value: any) {
      this.isResettingPassword = value;
    },

    setIsChangingPassword(value: any) {
      this.isChangingPassword = value;
    },

    setIsDisablingUser(value: any) {
      this.isDisablingUser = value;
    },

    setIsEnablingUser(value: any) {
      this.isEnablingUser = value;
    },

    setIsAddingUserToGroup(value: any) {
      this.isAddingUserToGroup = value;
    },

    setIsRemovingUserFromGroup(value: any) {
      this.isRemovingUserFromGroup = value;
    },

    setIsCreatingUser(value: any) {
      this.isCreatingUser = value;
    },

    setIsDeletingUser(value: any) {
      this.isDeletingUser = value;
    },

    setDeleteUserForLocal(userName: any) {
      const indexUser = this.userList.findIndex((user: any) => user.userName === userName);
      this.userList.splice(indexUser, 1);
    },

    setFetchUserListError(value: any) {
      this.fetchUsersError = value;
    },

    setFetchUserDetailError(value: any) {
      this.fetchUserDetailError = value;
    },

    setFetchUserGroupsError(value: any) {
      this.fetchUserGroupsError = value;
    },

    setResetPasswordError(value: any) {
      this.resetPasswordError = value;
    },

    setChangePasswordError(value: any) {
      this.changePasswordError = value;
    },

    setDisableUserError(value: any) {
      this.disableUserError = value;
    },

    setEnableUserError(value: any) {
      this.enableUserError = value;
    },

    setAddUserToGroupError(value: any) {
      this.addUserToGroupError = value;
    },

    setRemoveUserFromGroupError(value: any) {
      this.removeUserFromGroupError = value;
    },

    setCreateUserError(value: any) {
      if (value && value.code) {
        this.createUserError = errors.find((obj) => obj.code === value.code);
      } else {
        this.createUserError = value;
      }
    },

    setDeleteUserError(value: any) {
      this.deleteUserError = value;
    },

    // Team
    setSelectedTeamCriteria(value: any) {
      this.selectedTeamCriteria = value;
    },

    setTeamFilterKeyword(value: any) {
      this.teamFilterKeyword = Object.assign({}, this.teamFilterKeyword, value);
    },

    setTeamList(value: any) {
      this.teamList = value;
    },

    appendToTeamList(value: any) {
      this.teamList.push(value);
    },

    removeFromTeamList(value: any) {
      const index = this.teamList.findIndex((obj: any) => obj.teamId == value.teamId);
      this.teamList.splice(index, 1);
    },

    updateDetailInTeamList(value: any) {
      const index = this.teamList.findIndex((obj: any) => obj.teamId === value.teamId);
      this.teamList[index] = value;
    },

    setTeamDetail(value: any) {
      this.teamDetail = value;
    },

    setIsFetchingTeamList(value: any) {
      this.isFetchingTeamList = value;
    },

    setIsFetchingTeamDetail(value: any) {
      this.isFetchingTeamDetail = value;
    },

    setIsCreatingTeam(value: any) {
      this.isCreatingTeam = value;
    },

    setIsUpdatingTeam(value: any) {
      this.isUpdatingTeam = value;
    },

    setIsDeletingTeam(value: any) {
      this.isDeletingTeam = value;
    },

    setFetchTeamListError(value: any) {
      this.fetchTeamListError = value;
    },

    setFetchTeamDetailError(value: any) {
      this.fetchTeamDetailError = value;
    },

    setCreateTeamError(value: any) {
      this.createTeamError = value;
    },
    
    setUpdateTeamError(value: any) {
      this.updateTeamError = value;
    },

    setDeleteTeamError(value: any) {
      this.deleteTeamError = value;
    },

    // Group
    setGroups(value: any) {
      value = value.map((obj: any) => {
        return convertGroupData(obj);
      });
      value = value.filter(
        (obj: any) =>
          obj.groupName === "admins" ||
          obj.groupName === "members" ||
          obj.groupName === "guests" ||
          obj.groupName === "operators"
      );
      this.groupList = _.sortBy(value, ["updatedAt"]);
    },

    setIsFetchingGroupList(value: any) {
      this.isFetchingGroupList = value;
    },

    setFetchGroupListError(value: any) {
      this.fetchGroupListError = value;
    },

    setGroupDetail(value: any) {
      this.groupDetail = convertGroupData(value);
    },

    setIsFetchingGroupDetail(value: any) {
      this.isFetchingGroupDetail = value;
    },

    setFetchGroupDetailError(value: any) {
      this.fetchGroupDetailError = value;
    },

    setGroupUsers(value: any) {
      this.groupUsers = value.map((obj: any) => {
        return convertUserData(obj);
      });
    },

    setIsFetchingGroupUsers(value: any) {
      this.isFetchingGroupUsers = value;
    },

    setFetchGroupUsersError(value: any) {
      this.fetchGroupUsersError = value;
    },

    setIsUpdatingGroupDetail(value: any) {
      this.isUpdatingGroup = value;
    },

    setUpdateGroupDetailError(value: any) {
      this.updateGroupError = value;
    },

    setPolicySettings(value: any) {
      this.policySettings = convertPolicyData(value);
    },

    setIsFetchingPolicySettings(value: any) {
      this.isFetchingPolicySettings = value;
    },

    setIsUpdatingPolicySettings(value: any) {
      this.isUpdatingPolicySettings = value;
    },

    setFetchPolicySettingsError(value: any) {
      this.fetchPolicySettingsError = value;
    },

    setUpdatePolicySettingsError(value: any) {
      this.updatePolicySettingsError = value;
    },

    customizeErrorMessage(error: any) {
      if (error.response?.data?.code === 403) {
        error.response.data.message = "この操作を行う場合は、権限を管理者にお問い合わせください。";
      }
    },

    // action
    async fetchUserList(isReload = false) {
      try {
        this.setIsFetchingUserList(true);
        this.setFetchUserListError(null);
  
        const { dataTableOptions, pageList } = this;
        const { page } = dataTableOptions;
  
        const params = new UsersRequestParametersBuilder(this)
          .setFilterCriteriaParameters(!isReload)
          .setPaginationParameters()
          .build();
  
        if (!pageList.includes(page) || isReload) {
          let response:any = {};
          let users = [];

          do {
            response = await service.post("/users/search", params.filterCriteria);
            params.filterCriteria.paginationToken = response.paginationToken;
            users = users.concat(response.Users);
          } while (response.paginationToken);

          response.Users = users;

          this.totalUser = response.Users.length;
          if(response) {
            if(isReload) {
              this.updateUserList(response);
            } else {
              this.addToUserList(response);
              this.addToPaginationToken(response);
              this.addToPageList(page);
            }
  
            return response;
          }
        }
      } catch (error: any) {
        // TODO 一旦　error.stackに変更
        // this.setFetchUserListError(error.response.data);
        this.setFetchUserListError(error.stack);
      } finally {
        this.setIsFetchingUserList(false);
      }
    },

    async fetchUserDetail() {
      try {
        this.setIsFetchingUserDetail(true);
        this.setFetchUserDetailError(null);
  
        const params = new UsersRequestParametersBuilder(this).setUsername().build();
  
        const response = await service.get("/users/" + md5(params.username));
        if(response) {
          this.setUserDetail(response);
          return response;
        }
      } catch (error: any) {
        console.error(error);
        this.setFetchUserDetailError(error.response.data);
      } finally {
        this.setIsFetchingUserDetail(false);
      }
    },

    async fetchUserGroups() {
      try {
        this.setIsFetchingUserGroups(true);
        this.setFetchUserGroupsError(null);
  
        const params = new UsersRequestParametersBuilder(this).setUsername().build();
  
        const response = await service.get("/users/" + md5(params.username) + "/groups");
        if(response) {
          this.setUserGroups(response.Groups);
          return response;
        }
      } catch (error: any) {
        this.setFetchUserGroupsError(error.response.data);
      } finally {
        this.setIsFetchingUserGroups(false);
      }
    },
  
    async resetPassword(username: any) {
      try {
        this.setIsResettingPassword(true);
        this.setResetPasswordError(null);
        await service.post("/users/" + md5(username) + "/password/reset");
      } catch (error: any) {
        this.customizeErrorMessage(error);
        this.setResetPasswordError(error.response.data);
      } finally {
        this.setIsResettingPassword(false);
      }
    },

    async changePassword(payload: any) {
      try {
        this.setIsChangingPassword(true);
        this.setChangePasswordError(null);
  
        let response = await Auth.changePassword(payload.cognitoUser, payload.oldPassword, payload.newPassword);
        return response;
      } catch (error: any) {
        this.setChangePasswordError(error);
      } finally {
        this.setIsChangingPassword(false);
      }
    },
  
    async createUser(payload: any) {
      try {
        this.setIsCreatingUser(true);
        this.setCreateUserError(null);
        const response = await service.post("/users", {
          Username: payload.username,
          UserAttributes: [
            {
              Name: "email",
              Value: payload.email,
            },
            {
              Name: "email_verified",
              Value: "True",
            },
          ],
          TemporaryPassword: payload.tempPassword,
          MessageAction: payload.invitation ? null : "SUPPRESS",
        });
  
        await this.addUserToGroup(payload);
        return response;
      } catch (error: any) {
        this.setCreateUserError(error.response.data);
      } finally {
        this.setIsCreatingUser(false);
      }
    },

    async disableUser(payload: any) {
      try {
        this.setIsDisablingUser(true);
        this.setDisableUserError(null);
        const response = await service.post("/users/" + md5(payload.userName) + "/disable");
  
        let tmp = JSON.parse(JSON.stringify(payload));
        tmp.Enabled = response.Enabled;
        tmp.UserLastModifiedDate = response.UserLastModifiedDate;
  
        this.setUserDetail(tmp);
      } catch (error: any) {
        this.customizeErrorMessage(error);
        this.setDisableUserError(error.response.data);
      } finally {
        this.setIsDisablingUser(false);
      }
    },

    async enableUser(payload: any) {
      try {
        this.setIsEnablingUser(true);
        this.setEnableUserError(null);
        const response = await service.post("/users/" + md5(payload.userName) + "/enable");
  
        let tmp = JSON.parse(JSON.stringify(payload));
        tmp.Enabled = response.Enabled;
        tmp.UserLastModifiedDate = response.UserLastModifiedDate;
  
        this.setUserDetail(tmp);
      } catch (error: any) {
        this.customizeErrorMessage(error);
        this.setEnableUserError(error.response.data);
      } finally {
        this.setIsEnablingUser(false);
      }
    },

    async deleteUser(username: any) {
      try {
        this.setIsDeletingUser(true);
        this.setDeleteUserError(null);
        await service.delete("/users/" + md5(username));
        this.setDeleteUserForLocal(username);
      } catch (error: any) {
        this.customizeErrorMessage(error);
        this.setDeleteUserError(error.response.data);
      } finally {
        this.setIsDeletingUser(false);
      }
    },
  
    async addUserToGroup(payload: any) {
      try {
        this.setIsAddingUserToGroup(true);
        this.setAddUserToGroupError(null);
        await service.post("/users/" + md5(payload.username) + "/groups/" + payload.groupName);
      } catch (error: any) {
        this.customizeErrorMessage(error);
        this.setAddUserToGroupError(error.response.data);
      } finally {
        this.setIsAddingUserToGroup(false);
      }
    },

    async removeUserFromGroup(payload: any) {
      try {
        this.setIsRemovingUserFromGroup(true);
        this.setRemoveUserFromGroupError(null);
        await service.delete("/users/" + md5(payload.username) + "/groups/" + payload.groupName);
      } catch (error: any) {
        this.customizeErrorMessage(error);
        this.setRemoveUserFromGroupError(error.response.data);
      } finally {
        this.setIsRemovingUserFromGroup(false);
      }
    },
  
    async fetchTeamList() {
      try {
        this.setIsFetchingTeamList(true);
        this.setFetchTeamListError(null);
        const response = await service.get("/teams");
        if(response) {
          this.setTeamList(response);
          return response;
        }
      } catch (error: any) {
        console.error(error);
        // TODO 一旦error.stackに変更
        // this.setFetchTeamListError(error.response.data);
        this.setFetchTeamListError(error.stack);
      } finally {
        this.setIsFetchingTeamList(false);
      }
    },

    async fetchTeamDetail(teamId: any) {
      try {
        this.setIsFetchingTeamDetail(true);
        this.setFetchTeamDetailError(null);
        const response = await service.get("/teams/" + teamId);
        if(response) {
          this.setTeamDetail(response);
          return response;
        }
      } catch (error: any) {
        this.setFetchTeamDetailError(error.response.data);
      } finally {
        this.setIsFetchingTeamDetail(false);
      }
    },

    async updateTeam(payload: any) {
      try {
        this.setIsUpdatingTeam(true);
        this.setUpdateTeamError(null);
        if (payload.partitionKey) {
          const response = await service.put("/teams/" + payload.teamId, payload);
          if(response) {
            this.updateDetailInTeamList(response.item);
            return response;
          }
        } else {
          const response = await service.post("/teams", payload);
          if(response) {
            this.appendToTeamList(response.item);
            return response;
          }
        }
      } catch (error: any) {
        this.setUpdateTeamError(error.response.data);
      } finally {
        this.setIsUpdatingTeam(false);
      }
    },

    async deleteTeam(teamId: any) {
      try {
        this.setIsDeletingTeam(true);
        this.setDeleteTeamError(null);
        const response = await service.delete("/teams/" + teamId);
        if(response) {
          this.removeFromTeamList(response.item);
          return response;
        }
      } catch (error: any) {
        this.setDeleteTeamError(error.response.data);
      } finally {
        this.setIsDeletingTeam(false);
      }
    },
  
    async fetchGroupList() {
      try {
        this.setIsFetchingGroupList(true);
        this.setFetchGroupListError(null);
        const response = await service.get("/groups");
        if(response) {
          this.setGroups(response.Groups);
          return response;
        }
      } catch (error: any) {
        this.setIsFetchingGroupList(error.response.data);
      } finally {
        this.setIsFetchingGroupList(false);
      }
    },

    async fetchGroupDetail(groupName: any) {
      try {
        this.setIsFetchingGroupDetail(true);
        this.setFetchGroupDetailError(null);
        const response = await service.get("/groups/" + groupName);
        this.setGroupDetail(response.Group);
        return response;
      } catch (error: any) {
        this.setFetchGroupDetailError(error.response.data);
      } finally {
        this.setIsFetchingGroupDetail(false);
      }
    },

    async fetchGroupUsers(groupName: any) {
      try {
        this.setIsFetchingGroupUsers(true);
        this.setFetchGroupUsersError(null);
        const response = await service.get("/groups/" + groupName + "/users");
        this.setGroupUsers(response.Users);
        return response;
      } catch (error: any) {
        this.setFetchGroupUsersError(error.response.data);
      } finally {
        this.setIsFetchingGroupUsers(false);
      }
    },

    async updateGroupDetail(payload: any) {
      try {
        this.setIsUpdatingGroupDetail(true);
        this.setUpdateGroupDetailError(null);
        await service.put("/groups/" + payload.groupName, {
          Description: payload.description,
          Precedence: payload.precedence,
        } as any);
  
        this.fetchGroupDetail(payload.groupName);
      } catch (error: any) {
        this.setUpdateGroupDetailError(error.response.data);
      } finally {
        this.setIsUpdatingGroupDetail(false);
      }
    },
  
    async fetchPolicySettings() {
      try {
        this.setIsFetchingPolicySettings(true);
        this.setFetchPolicySettingsError(null);
        const response = await service.get("/pw_policies");
        this.setPolicySettings(response);
      } catch (error: any) {
        // TODO 一旦error.stackに変更
        // this.setFetchPolicySettingsError(error.response.data);
        this.setFetchPolicySettingsError(error.stack);
      } finally {
        this.setIsFetchingPolicySettings(false);
      }
    },
  
    async updatePolicySettings(payload: any) {
      try {
        this.setIsUpdatingPolicySettings(true);
        this.setUpdatePolicySettingsError(null);
        await service.post("/pw_policies", {
          MinimumLength: payload.minLength,
          RequireNumbers: payload.numbers,
          RequireSymbols: payload.specialSymbols,
          RequireUppercase: payload.uppercase,
          RequireLowercase: payload.lowercase,
          TemporaryPasswordValidityDays: payload.validityDays,
        });
      } catch (error: any) {
        // TODO: 戻り値にresponseがない - honda
        this.setUpdatePolicySettingsError(error.message);
      } finally {
        this.setIsUpdatingPolicySettings(false);
      }
    },
  }
});


const convertUserData = (obj: any) => {
  USER_STATUSES.map((status) => {
    if (obj.UserStatus === status.value) {
      obj.UserStatus = status.text;
    }
  });

  const attributesMap = Object.fromEntries(
    obj.Attributes.map((attr: { Name: string; Value: string }) => [attr.Name, attr])
  );

  const userId = attributesMap['sub'] ? attributesMap['sub'].Value : "ーー";
  const email = attributesMap['email'] ? attributesMap['email'].Value : "ーー";

  return {
    ...obj,
    userName: obj.Username,
    userId: userId,
    email: email,
    status: obj.UserStatus,
    teams: obj.Teams,
    enabled: obj.Enabled,
    createdAt: dayjs(obj.UserCreateDate).valueOf(),
    updatedAt: dayjs(obj.UserLastModifiedDate).valueOf(),
  };
};

const convertUserDetailData = (obj: any) => {
  USER_STATUSES.map((status) => {
    if (obj.UserStatus === status.value) {
      obj.UserStatus = status.text;
    }
  });

  const attributesMap = Object.fromEntries(
    obj.UserAttributes.map((attr: { Name: string; Value: string }) => [attr.Name, attr])
  );

  return {
    ...obj,
    userName: obj.Username,
    userId: attributesMap['sub'] ? attributesMap['sub'].Value : "ーー",
    email: attributesMap['email'] ? attributesMap['email'].Value : "ーー",
    status: obj.UserStatus,
    enabled: obj.Enabled,
    createdAt: dayjs(obj.UserCreateDate).valueOf(),
    updatedAt: dayjs(obj.UserLastModifiedDate).valueOf(),
  };
};

const convertGroupData = (obj: any) => {
  return {
    ...obj,
    groupName: obj.GroupName,
    description: obj.Description ? obj.Description : "ーー",
    precedence: obj.Precedence ? obj.Precedence : "ーー",
    createdAt: dayjs(obj.CreationDate).valueOf(),
    updatedAt: dayjs(obj.LastModifiedDate).valueOf(),
  };
};

const convertPolicyData = (obj: any) => {
  return {
    ...obj,
    minLength: obj.MinimumLength,
    numbers: obj.RequireNumbers,
    specialSymbols: obj.RequireSymbols,
    uppercase: obj.RequireUppercase,
    lowercase: obj.RequireLowercase,
    validityDays: obj.TemporaryPasswordValidityDays,
  };
};