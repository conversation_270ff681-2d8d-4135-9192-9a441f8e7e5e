import { defineStore } from "pinia";

import {
  GetAllMemberFormConfigs,
  GetMemberFormConfigById,
  RegisterMemberSurveyConfig,
  UpdateMemberSurveyConfig,
} from "../../../services/member.service";

import { UpdateSurveyConfig } from "../../../services/form.service";
import { UploadSurveyImageToS3 } from "../../../services/survey.service";

import { isEmpty, orderBy } from "lodash";
import { UploadImageToS3 } from "../../../services/scenarios.service";
import { generateUUID } from "../../../utils/uuidUtils";

import {
  SET_MEMBER_CONFIGS,
  SET_IS_FETCHING_MEMBER_CONFIGS,
  SET_FETCH_MEMBER_CONFIGS_ERROR,
  SET_FETCH_MEMBER_CONFIG_ERROR,
  SET_MEMBER_CONFIG,
  SET_IS_FETCHING_MEMBER_CONFIG,
  SET_IS_REGISTERING_MEMBER_CONFIG,
  SET_REGISTER_MEMBER_CONFIG_ERROR,
  SET_REGISTERED_MEMBER_CONFIG_ID,
  SET_IS_UPDATING_MEMBER_CONFIG,
  SET_UPDATE_MEMBER_CONFIG_ERROR,
  ADD_MEMBER_CONFIG,
  UPDATE_MEMBER_CONFIG_IN_STORE,
} from "../../mutation-types";

import {
  FETCH_ALL_MEMBER_FORM_CONFIGS,
  FETCH_MEMBER_FORM_CONFIG,
  REGISTER_MEMBER_SURVEY_CONFIG,
  UPDATE_MEMBER_SURVEY_CONFIG,
} from "../../action-types";
import { useFormsStore } from "../forms";
import { useSurveyResultsStore } from "../surveyResults";

export default {
  namespaced: false,
};

interface MemberState {
  memberConfigs: any[];
  isFetchingMemberConfigs: boolean;
  fetchMemberConfigsError: Error | null;

  memberConfig: any;
  isFetchingMemberConfig: boolean;
  fetchMemberConfigError: Error | null;

  isRegisteringMemberConfig: boolean;
  registerMemberConfigError: Error | null;
  registeredMemberConfigId: string | null;

  isUpdatingMemberConfig: boolean;
  updateMemberConfigError: Error | null;
}

export const useMemberStore = defineStore({
  id: 'MemberStore',

  state: (): MemberState => ({
    memberConfigs: [] as any,
    isFetchingMemberConfigs: false,
    fetchMemberConfigsError: null,

    memberConfig: {},
    isFetchingMemberConfig: false,
    fetchMemberConfigError: null,

    isRegisteringMemberConfig: false,
    registerMemberConfigError: null,
    registeredMemberConfigId: null,

    isUpdatingMemberConfig: false,
    updateMemberConfigError: null,
  }),

  getters: {
    memberConfigsWithoutAppendType: (state) => {
      var data = state.memberConfigs;
      data = data.filter((item) => !("isAppending" in item && item.isAppending.value));
      return data;
    },
  },

  actions: {
    // mutations

    // Member config list
/*     setMemberConfigs(value) {
      this.memberConfigs = value;
    }, */

    setMemberConfigs(value: any) {
      this.memberConfigs = value;
    },

    addMemberConfig(value: any) {
      this.memberConfigs.splice(0, 0, value);
    },

/*     setIsFetchingMemberConfigs(value) {
      this.isFetchingMemberConfigs = value;
    }, */

    setIsFetchingMemberConfigs(value: any) {
      this.isFetchingMemberConfigs = value;
    },

/*     setFetchMemberConfigsError(value) {
      this.fetchMemberConfigsError = value;
    }, */

    setFetchMemberConfigsError(value: any) {
      this.fetchMemberConfigsError = value;
    },

    // Registering new member configs
    setIsRegisteringMemberConfig(value: any) {
      this.isRegisteringMemberConfig = value;
    },

    setRegisterMemberConfigError(value: any) {
      this.registerMemberConfigError = value;
    },

/*     setRegisteredMemberConfigId(value) {
      this.registeredMemberConfigId = value;
    }, */

    setRegisteredMemberConfigId(value: any) {
      this.registeredMemberConfigId = value;
    },

    // One member config (details)
    setMemberConfig(value: any) {
      this.memberConfig = value;
    },

    setIsFetchingMemberConfig(value: any) {
      this.isFetchingMemberConfig = value;
    },

/*     setFetchMemberConfigError(value) {
      this.fetchMemberConfigError = value;
    }, */

    setFetchMemberConfigError(value: any) {
      this.fetchMemberConfigError = value;
    },

    // Updating member config
    setIsUpdatingMemberConfig(value: any) {
      this.isUpdatingMemberConfig = value;
    },

    setUpdateMemberConfigError(value: any) {
      this.updateMemberConfigError = value;
    },

    //update data in list
    updateMemberConfigInStore(value: any) {
      let _dataIndex = this.memberConfigs.findIndex((obj) => obj.surveyId === value.surveyId);
      this.memberConfigs.splice(_dataIndex, 1, value);
    },

    // actions
    async fetchAllMemberFormConfigs() {
      try {
        this.setIsFetchingMemberConfigs(true);
        this.setFetchMemberConfigsError(null);
        let response = await GetAllMemberFormConfigs();
        if (response.result == "OK") {
          this.setMemberConfigs(orderBy(response.data, ["updatedAt"], ["desc"]));
          return true;
        } else {
          console.error("error", response.errorMessage);
          this.setFetchMemberConfigsError(response.errorMessage);
        }
      } catch (error: any) {
        console.error("error", error);
        this.setFetchMemberConfigsError(error);
      } finally {
        this.setIsFetchingMemberConfigs(false);
      }
    }, 

    async fetchMemberFormConfig(memberSurveyId: any) {
      try {
        this.setIsFetchingMemberConfig(true);
        this.setFetchMemberConfigError(null);
        let response = await GetMemberFormConfigById(memberSurveyId);
        if (response.result == "OK") {
          this.setMemberConfig(response.data);
          return response;
        } else {
          this.setFetchMemberConfigError(response.errorMessage);
        }
      } catch (error: any) {
        console.error("error", error);
        this.setFetchMemberConfigError(error);
      } finally {
        this.setIsFetchingMemberConfig(false);
      }
    },

    async registerMemberSurveyConfig(payload: any) {
      try {

        // We get the formConfigs from the forms store
        const formsStore = useFormsStore();
        if (isEmpty(formsStore.surveyConfigsList)) {
          await formsStore.fetchFormConfigs();
        }
        const formConfigs = formsStore.surveyConfigsList;
        
        // We get the selectedSurvey from the surveyResults store
        const surveyResults = useSurveyResultsStore();
        const selectedSurvey = surveyResults.selectedSurvey;

        await UploadSurveyImageToS3(payload);
        this.setIsRegisteringMemberConfig(true);
        this.setRegisterMemberConfigError(null);

        await uploadMediaAndFilterMessages(payload);

        // set surveyType to member
        payload.surveyType = "member";

        let response = await RegisterMemberSurveyConfig(payload);
        if (response.result == "OK") {
          this.setRegisteredMemberConfigId(response.data.surveyId);
          this.addMemberConfig(response.data);

          // Update linked forms, if applicable
          if (response.data.linkedForms) {
            await updateLinkedSurveyConfigs({
              surveyConfigs: formConfigs,
              memberConfig: response.data,
              formsStore,
              surveyResultsStore: surveyResults,
              selectedSurvey: selectedSurvey,
            });
          }

          return response.data.surveyId;
        } else {
          this.setRegisterMemberConfigError(response.errorMessage);
          return false;
        }
      } catch (error: any) {
        console.error("error", error);
        this.setRegisterMemberConfigError(error);
      } finally {
        this.setIsRegisteringMemberConfig(false);
      }
    },

    async updateMemberSurveyConfig(payload: any) {
      try {

        // We get the formConfigs from the forms store
        const formsStore = useFormsStore();
        if (isEmpty(formsStore.surveyConfigsList)) {
          await formsStore.fetchFormConfigs();
        }
        const formConfigs = formsStore.surveyConfigsList;
        
        // We get the selectedSurvey from the surveyResults store
        const surveyResults = useSurveyResultsStore();
        const selectedSurvey = surveyResults.selectedSurvey;

        await UploadSurveyImageToS3(payload);
        this.setIsUpdatingMemberConfig(true);
        this.setUpdateMemberConfigError(null);

        await uploadMediaAndFilterMessages(payload);
        let response = await UpdateMemberSurveyConfig(payload);
        if (response.result == "OK") {
          this.setRegisteredMemberConfigId(response.data.surveyId);
          this.updateMemberConfigInStore(response.data);

          await updateLinkedSurveyConfigs({
            surveyConfigs: formConfigs,
            memberConfig: response.data,
            formsStore,
            surveyResultsStore: surveyResults,
            selectedSurvey: selectedSurvey
          });

          return response.data.surveyId;
        } else {
          this.setUpdateMemberConfigError(response.errorMessage);
          return false;
        }
      } catch (error: any) {
        console.error("error", error);
        if (error.response?.data?.code === 403) {
          this.setUpdateMemberConfigError("この操作を行う場合は、権限を管理者にお問い合わせください");
        } else {
          this.setUpdateMemberConfigError(error);
        }
      } finally {
        this.setIsUpdatingMemberConfig(false);
      }
    },
  },
});

async function updateLinkedSurveyConfigs({ surveyConfigs, memberConfig, formsStore, surveyResultsStore, selectedSurvey = null }: any) {
  const updatedSurveyConfigs = generateLinkedMemberFormLists(
    surveyConfigs,
    memberConfig.surveyId,
    memberConfig.linkedForms
  );

  // If current selectedSurvyeConfig was affected, also updated it in state
  if(selectedSurvey) {
    const changedSelectedSurvey = updatedSurveyConfigs.find((config: any) => {
      return config.surveyId === selectedSurvey.surveyId;
    });

    if(changedSelectedSurvey) {
      surveyResultsStore.setSelectedSurvey(changedSelectedSurvey);
    }
  }

  // Update member form ID attribute in correspoding survey IDs
  for(const config of updatedSurveyConfigs) {
    let response = await UpdateSurveyConfig(config);

    let tries = 0;
    while (response.result != "OK" && tries < 3) {
      await new Promise((r) => setTimeout(r, 1000)); // Sleep for 1 second
      tries++;
      response = await UpdateSurveyConfig(config);
    }

    if (response.result == "OK") {
      formsStore.updateSurveyConfigInStore(response.data);
    }
  }
}

function generateLinkedMemberFormLists(surveyConfigs: any, memberFormId: any, linkedForms: any) {
  // Returns a combination of two lists
  // One of survey configs to remove the linked member form ID
  // Another with a list of survey configs to set the linked member form ID

  if (!linkedForms) {
    // Linked forms may be empty
    linkedForms = [];
  }

  //console.log("linkedForms", linkedForms);

  const unlinkedSurveyConfigs = surveyConfigs
    .filter((obj: any) => {
      // If the survey config ID is not in the linkedForms list
      // and that survey is currently linked to this form,
      // erase the memberFormId from the survey config registry
      return (
        memberFormId &&
        obj.memberFormId &&
        obj.memberFormId === memberFormId &&
        !linkedForms.includes(obj.surveyId)
      );
    })
    .map((obj: any) => {
      let clonedObj = Object.assign({}, obj);

      clonedObj.memberFormId = "";

      return clonedObj;
    });

  // Get list of new survey configs to set the linked member form
  const linkedSurveyConfigs = surveyConfigs
    .filter((obj: any) => {
      // If the survey config has no linked member form,
      // and that survey config ID is in the list of linked forms,
      // set the memberFormId of the survey config registry
      return linkedForms.includes(obj.surveyId) && !obj.memberFormId;
    })
    .map((obj: any) => {
      let clonedObj = Object.assign({}, obj);

      clonedObj.memberFormId = memberFormId;

      return clonedObj;
    });

  return unlinkedSurveyConfigs.concat(linkedSurveyConfigs);
}

async function uploadMediaAndFilterMessages(payload: any) {
  for (const message of payload.endOfSurveyMessage) {
    if (message.type === "image" && message.file && message.file.type) {
      const generatedUUID = generateUUID();
      const uploaded = await UploadImageToS3(message.file, generatedUUID, "resources");
      const url = `https://${uploaded.cloudFrontDist}/resources/${generatedUUID}`;
      message.previewImageUrl = url;
      message.originalContentUrl = url;
      delete message.file;
      delete message.text;
    }
    if (message.type === "text") {
      delete message.originalContentUrl;
      delete message.previewImageUrl;
    }
    delete message.error;
  }
}
