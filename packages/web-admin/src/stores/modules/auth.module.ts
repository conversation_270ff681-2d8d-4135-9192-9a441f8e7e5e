import { Auth } from "aws-amplify";
import {
  AUTH_SIGNIN,
  AUTH_SIGNOUT,
  AUTH_FORGOT_PASSWORD,
  AUTH_FORGOT_PASSWORD_SUBMIT,
  AUTH_COMPLETE_NEW_PASSWORD,
  AUTH_GET_ATTR,
  AUTH_REFRESH_USER,
  AUTH_DEFINE_USER_ABILITY,
  FETCH_PERMISSIONS_FROM_TEAM,
} from "../action-types";
import {
  SET_IS_SIGNING_IN,
  SET_IS_SIGNING_OUT,
  SET_IS_REFRESHING_USER,
  SET_COGNITO_USER,
  SET_USER,
  RESET_USER,
  SET_SIGNIN_ERROR,
  SET_SIGNOUT_ERROR,
  SET_REFRESH_USER_ERROR,
  SET_FORGOT_PASSWORD_ERROR,
  SET_FORGOT_PASSWORD_SUBMIT_ERROR,
  SET_IS_ACCOUNT_UNUSABLE,
  SET_IS_USER_ABILITIES_DEFINED,
  SET_PERMISSIONS_FROM_TEAM,
} from "../mutation-types";

import { refreshUserInfo, getCognitoUser, setAmplifyUser, AmplifyService } from "../../services/amplify.service";
import { UserService } from "../../services/users.service";
import { GetUserAbilities } from "../../services/ability";
import ability from "../../services/ability";

import { AbilityBuilder, defineAbility } from "@casl/ability";

import { TABS_AND_SUBPAGES_RELATIONS } from "../../constants/permission.constants";
import { defineStore } from "pinia";
import { CognitoUser, PermissionsFromTeam } from "@/types/index";

export type InitUserPayload = {
  username: string
  accessToken: string
  idToken: string
  refreshToken: string
}

export const useAuthStore = defineStore({
  id: 'AuthStore',

  state: () => ({
    status: "",
    token: "",
    isSigningIn: false,
    isSigningOut: false,
    isRefreshingUser: false,
    cognitoUser: {} as CognitoUser,
    user: null as any,
    signinError: null as any,
    signoutError: null,
    refreshUserError: null as any,
    forgotPasswordError: null,
    forgotPasswordSubmitError: null,
    isAccountUnusable: false,
    isUserAbilitiesDefined: false,
    permissionsFromTeam: {
      isAdministrator: false,
    } as PermissionsFromTeam,
    triggerTimeout: false,
  }),

  getters: {
    isAdministrator(state) {
      return state.permissionsFromTeam.isAdministrator;
    },
  },

  actions: {
    // mutation
    [SET_IS_SIGNING_IN](value: any) {
      this.isSigningIn = value;
    },

    [SET_IS_SIGNING_OUT](value: any) {
      this.isSigningOut = value;
    },

    [SET_IS_REFRESHING_USER](value: any) {
      this.isRefreshingUser = value;
    },

    [SET_COGNITO_USER](value: any) {
      this.cognitoUser = value;
    },

    [SET_USER](value: any) {
      this.user = value;
    },
  
    [SET_SIGNIN_ERROR](value: any) {
      this.signinError = value;
    },

    [SET_SIGNOUT_ERROR](value: any) {
      this.signoutError = value;
    },

    [SET_REFRESH_USER_ERROR](value: any) {
      this.refreshUserError = value;
    },

    ResetRefreshUserError() {
      this.refreshUserError = null;
    },

    [SET_FORGOT_PASSWORD_ERROR](value: any) {
      this.forgotPasswordError = value;
    },

    [SET_FORGOT_PASSWORD_SUBMIT_ERROR](value: any) {
      this.forgotPasswordSubmitError = value;
    },

    [SET_IS_ACCOUNT_UNUSABLE](value: any) {
      this.isAccountUnusable = value;
    },

    [RESET_USER]() {
      this.status = "";
      this.token = "";
      this.isSigningIn = false;
      this.user = null;
      this.isUserAbilitiesDefined = false;
    },

    [SET_IS_USER_ABILITIES_DEFINED](value: any) {
      this.isUserAbilitiesDefined = value;
    },

    [SET_PERMISSIONS_FROM_TEAM](value: any) {
      let tabsAndSubpagesPermissions: string[] = [];

      JSON.parse(localStorage.getItem("permissionsFromTeam") || "null")?.permissions?.some(permission => {
        const [feature, page, access] = permission.split(':');
        if (access === 'read') {
          tabsAndSubpagesPermissions.push(page);
        }
      });

      // type tabsAndSubpagesRelations = typeof TABS_AND_SUBPAGES_RELATIONS;
      // type tabKey = keyof tabsAndSubpagesRelations;

      // for (let tab of Object.keys(TABS_AND_SUBPAGES_RELATIONS) as tabKey[]) {
      //   for (let permission of value.permissions) {
      //     if (permission.indexOf(tab) >= 0) {
      //       tabsAndSubpagesPermissions.push(tab);
      //       for (let subpage of TABS_AND_SUBPAGES_RELATIONS[tab]) {
      //         tabsAndSubpagesPermissions.push(subpage);
      //       }
      //     }
      //   }
      // }

      value.permissions = tabsAndSubpagesPermissions;
      this.permissionsFromTeam = value;
    },

    // action
    async [AUTH_SIGNIN](payload: any) {
      this.SET_IS_SIGNING_IN(true);
      this.SET_SIGNIN_ERROR(null);
      try {
        // TODO
        await setAmplifyUser(payload);
        AmplifyService.configure();

        const response = await Auth.signIn(payload.username, payload.password);
        if (response) {
          await this.AUTH_REFRESH_USER();
          return response;
        }
      } catch (error: any) {
        this.SET_SIGNIN_ERROR(error);
      } finally {
        this.SET_IS_SIGNING_IN(false);
      }
    },

    async [AUTH_SIGNOUT]() {
      this.SET_SIGNOUT_ERROR(null);
      try {
        await Auth.signOut();
        localStorage.clear();
        this.isUserAbilitiesDefined = false;
      } catch (error: any) {
        this.SET_SIGNOUT_ERROR = error;
      }
    },

    async [AUTH_FORGOT_PASSWORD](payload: any) {
      this.SET_FORGOT_PASSWORD_ERROR(null);
      try {
        await Auth.forgotPassword(payload.username);
      } catch (error: any) {
        this.SET_FORGOT_PASSWORD_ERROR = error;
      }
    },

    async [AUTH_FORGOT_PASSWORD_SUBMIT](payload: any) {
      this.SET_FORGOT_PASSWORD_SUBMIT_ERROR(null);
      try {
        await Auth.forgotPasswordSubmit(payload.username, payload.code, payload.password);
      } catch (error: any) {
        this.SET_FORGOT_PASSWORD_SUBMIT_ERROR = error;
      }
    },

    async [AUTH_COMPLETE_NEW_PASSWORD](payload: any) {
      this.SET_IS_SIGNING_IN(true);
      this.SET_SIGNIN_ERROR(null);
      try {
        const response = await Auth.completeNewPassword(payload.cognitoUser, payload.newPassword);
        await this.AUTH_REFRESH_USER();
        return response;
      } catch (error: any) {
        this.SET_SIGNIN_ERROR = error;
      } finally {
        this.SET_IS_SIGNING_IN(false);
      }
    },

    async [AUTH_GET_ATTR]() {
      this.SET_IS_ACCOUNT_UNUSABLE(false);
      const user = await Auth.currentAuthenticatedUser();
      if (user) {
        await Auth.userAttributes(user).catch((error) => {
          if (error.message === "User is disabled." || error.message === "User does not exist.") {
            this.SET_IS_ACCOUNT_UNUSABLE(true);
          }
        });
      }
    },

    async [AUTH_REFRESH_USER]() {
      this.SET_IS_REFRESHING_USER(true);
      this.ResetRefreshUserError();
      try {
        AmplifyService.configure();
        this.SET_USER(await refreshUserInfo());
        this.SET_COGNITO_USER(await getCognitoUser());
        Promise.all([
          await this.AUTH_DEFINE_USER_ABILITY(),
          await this.FETCH_PERMISSIONS_FROM_TEAM(),
          await this.AUTH_GET_ATTR(),
        ]);
      } catch (error: any) {
        console.error("Error refreshing user", error);
        this.SET_REFRESH_USER_ERROR = error;
      } finally {
        this.SET_IS_REFRESHING_USER(false);
      }
    },

    async [AUTH_DEFINE_USER_ABILITY]() {
      // @ts-ignore
      const { can, rules, cannot } = new AbilityBuilder();
      if (!this.isUserAbilitiesDefined) {
        let userAbilities = await GetUserAbilities(this.user.groups);
        if (userAbilities || Object.keys(userAbilities).length > 0) {
          for (const [key, values] of Object.entries(userAbilities)) {
            (values as any).forEach((value: any) => {
              can(key, value);
            });
          }
        }
        ability.update(rules as any);
        localStorage.setItem("userAbilities", JSON.stringify(userAbilities));
        // @ts-ignore
        localStorage.setItem("isUserAbilitiesDefined", true);
        this.SET_IS_USER_ABILITIES_DEFINED(true);
        return true;
      }
      if (!this.isUserAbilitiesDefined) {
        let userAbilities: any = await GetUserAbilities(this.user.groups);
    
        if (userAbilities && Object.keys(userAbilities).length > 0) {
          const rules = userAbilities.flatMap(([key, values]: [string, any[]]) => 
            values.map((value: any) => ({
              action: 'read', // 例: 'read' をアクションとして使用
              subject: key,
              conditions: value,
            }))
          );
    
          // Ability インスタンスの作成
          const ability = defineAbility((can, cannot) => {
            rules.forEach((rule: any) => can(rule.action, rule.subject, rule.conditions));
          });
    
          localStorage.setItem("userAbilities", JSON.stringify(userAbilities));
          localStorage.setItem("isUserAbilitiesDefined", "true");
          this.SET_IS_USER_ABILITIES_DEFINED(true);
    
          return true;
        }
        return false;
      }
      return false;
    },
    
    async [FETCH_PERMISSIONS_FROM_TEAM]() {
      const service = new UserService();
      const response = await service.get("/teams/own");
      if (response) {
        localStorage.setItem("permissionsFromTeam", JSON.stringify(response));
        this.SET_PERMISSIONS_FROM_TEAM(response);
        return response;
      }
    },
    triggerSignOut() {
      this.triggerTimeout = true;
    }
  },
});
