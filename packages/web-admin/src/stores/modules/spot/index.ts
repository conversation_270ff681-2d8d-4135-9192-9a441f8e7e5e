import {
    createRoute,
    createSpot,
    deleteRoutes,
    deleteSpotGroup,
    deleteSpots,
    exportSpotsCSV,
    getCsvStatus,
    getRoutebyId,
    getSpotbyId,
    getSpotGroup,
    getSpotGroupPreview,
    getSpotList,
    listRoutes,
    listSpots,
    postSpotGroupList,
    updateRoute,
    updateSpotbyId,
    updateSpotGroup,
    uploadCSV,
    UploadImageToS3,
    uploadUpdateCSV
} from "@/services/spot.service";
import {defineStore} from "pinia";
import _ from "lodash";
import {SPOT_CSV_FOLDER_NAME, SPOT_HEADERS, SPOT_IMAGE_FOLDER_NAME} from "./spot.constants";
import {Spot, SpotAttributeDefinition, SpotAttributeValue, SpotGroup, SpotRoute} from "@/types/spot";

export default {
    namespaced: false,
};
const DEFAULT_SPOT_GROUP_ATTRIBUTES: Array<SpotAttributeDefinition> = [
    { id: "attribute_00001", label: "属性 1", attributeName: "属性 1", required: true },
    { id: "attribute_00002", label: "属性 2", attributeName: "属性 2", required: false },
    { id: "attribute_00003", label: "属性 3", attributeName: "属性 3", required: false },
    { id: "attribute_00004", label: "属性 4", attributeName: "属性 4", required: false },
];

const SPOT_GROUP_ATTRIBUTES_RESET = [
    { id: "attribute_00001", label: "属性 1", attributeName: "属性 1", required: true },
    { id: "attribute_00002", label: "属性 2", attributeName: "属性 2", required: false },
    { id: "attribute_00003", label: "属性 3", attributeName: "属性 3", required: false },
    { id: "attribute_00004", label: "属性 4", attributeName: "属性 4", required: false },
];

const DEFAULT_SPOT_URL_ATTRIBUTE: SpotAttributeDefinition = {
    id: "url",
    label: "Webサイト",
    attributeName: "Webサイト",
    required: false
};

const SPOT_URL_ATTRIBUTE_RESET = {
    id: "url",
    label: "Webサイト",
    attributeName: "Webサイト",
    required: false
};

const DEFAULT_SPOT_ATTRIBUTES: Array<SpotAttributeValue> = [
    { id: "attribute_00001", value: "" },
    { id: "attribute_00002", value: "" },
    { id: "attribute_00003", value: "" },
    { id: "attribute_00004", value: "" },
];

const DEFAULT_SPOT_URL_ATTRIBUTE_VAL = {
    id: "url",
    value: ""
};

const DEFAULT_SPOT_GROUP: SpotGroup = {
    partitionKey: "spotGroup",
    sortKey: null,
    groupName: null,
    createdBy: null,
    updatedAt: null,
    updatedBy: null,
    spotTemplate: {
        id: "spotTemplate_00001",
        name: "スタイル 1"
    },
    scenarioTriggerSettings: {
        locationSearchDistance: 1000,
        locationSearchText: "現在地からスポットを探す",
        locationSearchNotFoundText: "位置を変えてもう一度探す",
        listDisplayContinueText: "リストの続きを表示します"
    },
    attributes: DEFAULT_SPOT_GROUP_ATTRIBUTES,
    urlAttribute: DEFAULT_SPOT_URL_ATTRIBUTE,
    tags: []
};

const SPOT_GROUP_RESET = {
    partitionKey: "spotGroup",
    sortKey: null,
    groupName: null,
    createdBy: null,
    updatedAt: null,
    updatedBy: null,
    spotTemplate: {
        id: "spotTemplate_00001",
        name: "スタイル 1"
    },
    scenarioTriggerSettings: {
        locationSearchDistance: 1000,
        locationSearchText: "現在地からスポットを探す",
        locationSearchNotFoundText: "位置を変えてもう一度探す",
        listDisplayContinueText: "リストの続きを表示します"
    },
    attributes: SPOT_GROUP_ATTRIBUTES_RESET,
    urlAttribute: SPOT_URL_ATTRIBUTE_RESET,
    tags: []
};
const DEFAULT_SPOT: Spot = {
    attributes: DEFAULT_SPOT_ATTRIBUTES,
    urlAttribute: DEFAULT_SPOT_URL_ATTRIBUTE_VAL,
    tags: [],
    image: "",
    csvSource: "",
    latitude: null,
    longitude: null
};

const DEFAULT_ROUTE: SpotRoute = {
    listName: null,
    spots: []
};
export const useSpotStore = defineStore({
    id: 'spotStore',
    state: () => ({
        // Spot group
        spotGroupList: [],
        spotGroup: DEFAULT_SPOT_GROUP,
        spotTemplates: null,
        spotGroupPreview: null,

        postbackData: [],

        isFetchingSpotGroupList: false,
        isFetchingSpotGroupById: false,
        isFetchingSpotTemplates: false,
        isCreatingSpotGroup: false,
        isUpdatingSpotGroup: false,
        isDeletingSpotGroup: false,
        isBatchDeletingSpotGroup: false,

        isFetchingSpotGroupPreview: false,

        isFetchingPostbackData: false,

        fetchSpotGroupListError: null,
        fetchSpotGroupByIdError: null,
        fetchSpotTemplatesError: null,
        createSpotGroupError: null,
        updateSpotGroupError: null,
        deleteSpotGroupError: null,
        batchDeleteSpotGroupError: null,

        fetchSpotGroupPreviewError: null,

        fetchPostbackDataError: null,
        isGroupUpdated: false,
        isGroupCreated: false,
        // Spot
        spotHeaders: SPOT_HEADERS,
        spotList: [],
        spot: DEFAULT_SPOT,
        selectedDisplayTemplate: null,
        spotAttributes: [],
        spotTagOptions: null,
        spotTemplate: null,
        csvImportResult: null,
        csvImportStatus: null,

        isFetchingSpotList: false,
        isFetchingSpotById: false,
        isCreatingSpot: false,
        isUpdatingSpot: false,
        isDeleteingSpot: false,
        isBatchDeletingSpot: false,
        isImportingSpotCSV: false,
        isBatchUploadingSpotImage: false,
        isFetchingCSVImportStatus: false,

        fetchSpotListError: null,
        fetchSpotByIdError: null,
        createSpotError: null,
        updateSpotError: null,
        deleteSpotError: null,
        batchDeleteSpotError: null,
        importSpotCSVError: null,
        batchUploadSpotImageError: null,
        fetchCSVImportStatusError: null,

        // Route
        routeList: [],
        route: DEFAULT_ROUTE,
        routePostback: null,

        isFetchingRouteList: false,
        isFetchingRouteById: false,
        isCreatingRoute: false,
        isUpdatingRoute: false,
        isDeletingRoute: false,
        isBatchDeletingRoute: false,

        fetchRouteListError: null,
        fetchRouteByIdError: null,
        createRouteError: null,
        updateRouteError: null,
        deleteRouteError: null,
        batchDeleteRouteError: null,
    }),
    getters: {
        // getters
        selectedTemplate() {
            if (!this.spotGroupPreview || !this.spotGroupPreview.params) {
                return null;
            }
            return this.spotGroupPreview.params;
        },

    },
    actions: {
        async getPreview(previewPayload?: any) {
            try {
                this.isFetchingSpotGroupPreview = true;
                let payload = {
                    attributes: DEFAULT_SPOT_GROUP_ATTRIBUTES,
                    urlAttribute: DEFAULT_SPOT_URL_ATTRIBUTE,
                    spotTemplate: "spotTemplate_00001"
                };
                if (previewPayload) {
                    payload = previewPayload;
                }
                const response = await getSpotGroupPreview(payload);
                this.spotGroupPreview = await {
                    params: response.data,
                    dataType: "bubbleFlex",
                };
                return response.data;
            } catch (error) {
                console.log(error);
            } finally {
                this.isFetchingSpotGroupPreview = false;
            }
        },
        async getSpotGroups($q) {
            try {
                this.isFetchingSpotGroupList = true;
                const responseJson = await getSpotList();
                this.spotGroupList = responseJson.data;
                return responseJson;
            } catch (error) {
                console.log(error);
                $q.loading.hide();
            } finally {
                this.spotGroup = DEFAULT_SPOT_GROUP;
                this.isFetchingSpotGroupList = false;
            }
        },
        async postSpotGroups($q) {
            try {
                this.isCreatingSpotGroup = true;
                const payload = this.spotGroup;
                payload.createdBy = "ーー";
                payload.updatedBy = "ーー";
                await postSpotGroupList(payload);
                $q.notify({ type: 'positive', message: '保存されました！' });
                this.spotGroup = SPOT_GROUP_RESET;
                this.isGroupCreated = true;
            } catch (error) {
                this.resetSpotGroup();
                this.isCreatingSpotGroup = false;
                $q.notify({ message: error });
            } finally {
                this.resetSpotGroup();
                this.getSpotGroups();
                this.isCreatingSpotGroup = false;
            }
        },
        async getSpotGroupById(id: string) {
            try {
                this.isFetchingSpotGroupById = true;
                const res = await getSpotGroup(id);
                this.spotGroup = res.data;
                this.spot.attributes = res.data.attributes.map((v) => {
                    return {
                        id: v.id,
                        value: ""
                    };
                });
                this.setSpotHeaders(res.data.attributes);
                setTimeout(() => {
                    const previewPayload = {
                        attributes: res.data.attributes,
                        urlAttribute: res.data.urlAttribute,
                        spotTemplate: res.data.spotTemplate.id
                    };
                    this.getPreview(previewPayload);
                }, 200);
            } catch (error) {
                console.log(error);
            } finally {
                this.isFetchingSpotGroupById = false;
            }
        },
        async updateSpotGroupById($q) {
            try {
                this.isFetchingSpotGroupById = true;
                const payload = this.spotGroup;
                const res = await updateSpotGroup(payload.sortKey, payload);
                this.spotGroup = res.data;
                setTimeout(() => {
                    this.getSpotGroups();
                    this.isGroupUpdated = true;
                }, 200);
                $q.notify({ type: 'positive', message: '更新しました。' });
            } catch (error) {
                $q.notify({ message: error });
            } finally {
                this.isFetchingSpotGroupById = false;
            }
        },
        async deleteSpotGroup($q: any, id: string) {
            try {
                $q.loading.show();
                await deleteSpotGroup(id);
                $q.notify({ type: 'positive', message: '削除しました。' });
            } catch (error) {
                $q.loading.hide();
                $q.notify({ message: error });
            } finally {
                $q.loading.hide();
            }
        },
        async createSpot($q: any, payload: any) {
            try {
                $q.loading.show();
                if (payload.fileData) {
                    let filename = payload.fileData.name;
                    let folder = "resources/" + SPOT_IMAGE_FOLDER_NAME + "/" + payload.spotGroupId;
                    await UploadImageToS3(payload.fileData, filename, folder);
                }
                const createPayload = {
                    ...payload,
                    image: payload.fileData ? payload.fileData.name : ''
                };
                delete createPayload.fileData;
                await createSpot(createPayload);
                $q.notify({ type: 'positive', message: 'スポートを作成しました。' });
            } catch (error) {
                $q.loading.hide();
                $q.notify({ message: error });
            } finally {
                await this.resetSpot();
                $q.loading.hide();
            }
        },
        async getSpotLists(id: string) {
            try {
                this.isFetchingSpotList= true;
                const responseJson = await listSpots(id);
                this.setSpotList(responseJson.data.spot);
                return responseJson;
            } catch (error) {
                console.log(error);
            } finally {
                this.isFetchingSpotList= false;
            }
        },
        async setSpotHeaders(val: any) {
            this.spotAttributes = val;
            val.map(obj => {
                const index = this.spotHeaders.findIndex(header => header.id === obj.id);
                if (index >= 0) {
                    this.spotHeaders[index].label = obj.attributeName;
                }
            });
        },
        async setSpotList(val: any) {
            function convertSpotData(obj, attributes) {
                if (!obj || !Array.isArray(obj.attributes)) {
                    return obj;
                }

                const convertedObj = { ...obj };

                obj.attributes.forEach((attr, index) => {
                    convertedObj[`attribute${index + 1}`] = attr.value;
                });

                return {
                    ...convertedObj,
                    latitudeLongitudeStr: obj.latitude && obj.longitude ? `${obj.latitude}, ${obj.longitude}` : "N/A",
                    createdBy: obj.createdBy || "ーー",
                    updatedBy: obj.updatedBy || "ーー"
                };
            }
            this.spotList = val ? val.map(obj => {
                return convertSpotData(obj, this.spotAttributes.attributes);
            }) : [];

        },
        async getSpotbyId(id: string, spotGroupId) {
            try {
                const responseJson = await getSpotbyId(id, spotGroupId);
                this.spot = responseJson.data.spot;
                this.spotGroupPreview.params.image = responseJson.data.spot.image !== '' ? responseJson.data.spot.image : null;
                return responseJson;
            } catch (error) {
                console.log(error);
            }
        },
        async updateSpotbyId(id: string, payload: any, $q: any) {
            try {
                const updatePayload = payload;
                $q.loading.show();
                if (payload.fileData) {
                    let filename = payload.fileData.name;
                    let folder = "resources/" + SPOT_IMAGE_FOLDER_NAME + "/" + payload.spotGroupId;
                    await UploadImageToS3(payload.fileData, filename, folder);
                    updatePayload.image = payload.fileData.name;
                    delete updatePayload.fileData;
                }
                const responseJson = await updateSpotbyId(id, payload);
                $q.notify({ type: 'positive', message: 'スポット更新しました。' });
                return responseJson;
            } catch (error) {
                $q.notify({ type: 'negative', message: error });
                $q.loading.hide();

            } finally {
                $q.loading.hide();
            }
        },
        async deleteSpots ($q: any, payload: any) {
            try {
                $q.loading.show();
                const responseJson = await deleteSpots(payload);
                $q.notify({ type: 'positive', message: 'スポット更新しました。' });
                return responseJson;
            } catch (error) {
                $q.notify({ type: 'negative', message: error });
                $q.loading.hide();

            } finally {
                $q.loading.hide();
            }
        },
        async uploadCsv ($q: any,filePayload:any, payload: any) {
            try {
                $q.loading.show();
                let filename = filePayload.name;
                let s3Response = await UploadImageToS3(filePayload, filename, SPOT_CSV_FOLDER_NAME);
                let responseJson;
                payload.filePath = SPOT_CSV_FOLDER_NAME + payload.filePath;
                if (s3Response) {
                  responseJson = await uploadCSV(payload);
                }
                return responseJson;
            } catch (error) {
                $q.notify({ type: 'negative', message: error });
                $q.loading.hide();

            } finally {
                $q.loading.hide();
            }
        },
        async uploadUpdateCsv ($q: any,filePayload:any, payload: any) {
            try {
                $q.loading.show();
                let filename = filePayload.name;
                let s3Response = await UploadImageToS3(filePayload, filename, SPOT_CSV_FOLDER_NAME);
                let responseJson;
                payload.filePath = SPOT_CSV_FOLDER_NAME + payload.filePath;
                if (s3Response) {
                  responseJson = await uploadUpdateCSV(payload);
                }
                $q.notify({ type: 'positive', message: 'スポット更新CSVインポートが開始されました。' });
                return responseJson;
            } catch (error) {
                $q.notify({ type: 'negative', message: error });
                $q.loading.hide();

            } finally {
                $q.loading.hide();
            }
        },
        async exportCsv ($q: any, spotGroupId: string) {
            try {
                $q.loading.show();
                const response = await exportSpotsCSV(spotGroupId);
                if (response && response.data && response.data.csvUrl) {
                    window.open(response.data.csvUrl, '_blank');
                    $q.notify({ type: 'positive', message: 'CSVエクスポートが完了しました。' });
                } else {
                    throw new Error('CSVエクスポートに失敗しました。');
                }
                return response;
            } catch (error) {
                $q.notify({ type: 'negative', message: error.message || 'CSVエクスポートに失敗しました。' });
                throw error;
            } finally {
                $q.loading.hide();
            }
        },
        async getCsvStatus ($q: any,id: string) {
            try {
                $q.loading.show();
                const responseJson = await getCsvStatus(id);
                this.csvImportStatus= responseJson.data[0];
                return responseJson;
            } catch (error) {
                $q.notify({ type: 'negative', message: error });
                $q.loading.hide();

            } finally {
                $q.loading.hide();
            }
        },
        async uploadBulkImages ($q: any,params: any) {
            try {
                $q.loading.show();
                if (params.fileData) {
                    var requests = _.chunk(params.fileData, 50);

                    for (const request of requests) {
                        const images = await Promise.all(
                            request.map(async (fileData: any) => {
                                let filename = fileData.name;
                                let folder = `resources/${SPOT_IMAGE_FOLDER_NAME}/${params.spotGroupId}`;
                                let s3Response = await UploadImageToS3(fileData, filename, folder);
                                return {
                                    key: `https://${s3Response.cloudFrontDist}/${folder}/${filename}`,
                                    filename: filename
                                };
                            })
                        );

                        let payload = { ...params, images }; // params をコピーして変更
                        delete payload.fileData;
                        // TODO uncomment below line if needed
                        // await uploadBulkImages(payload);
                    }
                }
                $q.notify({ type: 'positive', message: "一括画像登録処理を開始しました" });
            } catch (error) {
                $q.notify({ type: 'negative', message: error });
                $q.loading.hide();

            } finally {
                $q.loading.hide();
            }
        },
        async createRoute ($q: any,payload: any) {
            try {
                $q.loading.show();
                return await createRoute(payload);
            } catch (error) {
                $q.notify({ type: 'negative', message: error });
                $q.loading.hide();

            } finally {
                // this.listRoutes($q,payload.spotGroupId)
                $q.loading.hide();
            }
        },
        async listRoutes ($q: any,spotGroupId: any) {
            try {
                $q.loading.show();
                this.isFetchingRouteList = true;
                const responseJson = await listRoutes(spotGroupId);
                this.routeList = responseJson.data;
                return responseJson;
            } catch (error) {
                $q.notify({ type: 'negative', message: error });
                $q.loading.hide();
            } finally {
                this.isFetchingRouteList = false;
                $q.loading.hide();
            }
        },
        async getRouteById ($q: any,spotGroupId: any, spotId: any) {
            try {
                $q.loading.show();
                const responseJson = await getRoutebyId(spotGroupId, spotId);
                this.route = responseJson.data;
                return responseJson;
            } catch (error) {
                $q.notify({ type: 'negative', message: error });
                $q.loading.hide();
            } finally {
                $q.loading.hide();
            }
        },
        async updateRoute ($q: any,payload: any) {
            try {
                $q.loading.show();
                const responseJson = await updateRoute(payload);
                $q.notify({ type: 'positive', message: 'リスト更新しました。' });
                return responseJson;
            } catch (error) {
                $q.notify({ type: 'negative', message: error });
                $q.loading.hide();
            } finally {
                this.listRoutes($q, payload.spotGroupId);
                $q.loading.hide();
            }
        },
        async deleteRoutes ($q: any,groupId:any,payload: any) {
            try {
                $q.loading.show();
                const responseJson = await deleteRoutes(groupId, payload);
                $q.notify({ type: 'positive', message: 'リスト削除しました。' });
                return responseJson;
            } catch (error) {
                $q.notify({ type: 'negative', message: error });
                $q.loading.hide();
            } finally {
                this.listRoutes($q, groupId);
                $q.loading.hide();
            }
        },
        async setRouteSpots (value) {
           this.route.spots = value;
        },
        async setListName (value) {
            this.route.listName = value;
         },
        async resetSpotGroup() {
            this.spotGroup = {
                partitionKey: "spotGroup",
                sortKey: null,
                groupName: null,
                createdBy: null,
                updatedAt: null,
                updatedBy: null,
                spotTemplate: {
                    id: "spotTemplate_00001",
                    name: "スタイル 1"
                },
                scenarioTriggerSettings: {
                    locationSearchDistance: 1000,
                    locationSearchText: "現在地からスポットを探す",
                    locationSearchNotFoundText: "位置を変えてもう一度探す",
                    listDisplayContinueText: "リストの続きを表示します"
                },
                attributes: [
                    { id: "attribute_00001", label: "属性 1", attributeName: "属性 1", required: true },
                    { id: "attribute_00002", label: "属性 2", attributeName: "属性 2", required: false },
                    { id: "attribute_00003", label: "属性 3", attributeName: "属性 3", required: false },
                    { id: "attribute_00004", label: "属性 4", attributeName: "属性 4", required: false },
                ],
                urlAttribute: {
                    id: "url",
                    label: "Webサイト",
                    attributeName: "Webサイト",
                    required: false
                },
                tags: []
            };
            this.spotTemplates = {};
            // this.spotGroupPreview = null
            this.isGroupCreated = false;
            this.isGroupUpdated = false;
        },
        async resetSpot() {
            this.spot= DEFAULT_SPOT;
        },
        async resetRoute() {
            this.route = DEFAULT_ROUTE;
        }
    },
});