import { defineStore } from "pinia"
import { computed, ref } from "vue"
import { GetAllScenarios, GetScenarioDataByDataType } from "../../../services/scenarios.service"
import { cloneDeep, every, isEmpty } from "lodash";
import { CreateDamageReports, DeleteDamageReportsByID, ExportReportToCSV, GetAllDamageReports, GetDamageReportsByID, GetScenarioFormConfig, GetSignedImagesInfo, QueryFilterDamageReports, UpdateDamageReportsByID, UploadImagesS3 } from "../../../services/damage-report.service"
import dayjs from "dayjs";
import { useI18n } from "vue-i18n";
import { useNotify } from "../../../composables/useNotify"
import { useThrottleFn } from "@vueuse/core";
import { GetBatchGroupAdminReadStatus } from "@/services/chat.service";
import { categoryOptionsDefault, CollectionOptions, feedbackOptionsDefault, subjectOptionsDefault } from "./collectionDefault";

export const useDamageReportStore = defineStore('damage-report', () => {

  const { t } = useI18n()
  const { alertError } = useNotify()
  // #region State
  const loadings = ref<any>({})
  const filterFormData = ref<any>({})
  const isShowFilterForm = ref(false)
  const lstScenarioOptions = ref<any[]>([])

  // table data
  const tableReportData = ref<any[]>([])
  const totalReportDataCount = ref(0)
  const lastEvaluatedKey = ref()
  const curFilterCondition = ref<any>(null)
  const isClearCacheReportData = ref(false)
  // Group Chat admin read status
  const adminReadChatStatus = ref<Record<string, boolean>>({})

  // Form options
  // TODO generate from scenario
  const scenarioOptions = ref<any[]>([])
  const currentActiveScenarioVersion = ref<any>()
  const talkMsgList = ref()
  const activeTalkVersionId = ref()

  const statusOptions = computed(() => [{
    value: 'not-processed',
    label: t('status.not-processed'),
  }, {
    value: 'cancel',
    label: t('status.cancel'),
  }, {
    value: 'processing',
    label: t('status.processing'),
  }, {
    value: 'finished',
    label: t('status.finished'),
  }])
  const categoryOptions = ref<CollectionOptions[]>([])
  const subjectOptions = ref<CollectionOptions[]>([])
  const feedbackOptions = ref<CollectionOptions[]>([])

  const configCache: any = {}

  // #endregion

  // #region Actions
  // scenario
  const buildOptionsFromScenario = useThrottleFn(async () => {
    loadings.value.scenarioOptionsLoading = true
    if (scenarioOptions.value.length === 0) {
      const listScenario: any[] = []
      const scenarioData = await GetAllScenarios()
      const settingScenario = scenarioData?.items?.find(x => x.scenarioId === 'settings')
      if (settingScenario?.activeScenarioId) {
        const activeScenario = scenarioData?.items?.find(x => x.scenarioId === settingScenario?.activeScenarioId)
        const productionScenarioVersions = settingScenario?.envMapping?.production;
        currentActiveScenarioVersion.value = {
          scenarioId: settingScenario?.activeScenarioId,
          versionId: productionScenarioVersions
        }
        if (activeScenario) {
          for (const scenarioId in activeScenario.versions) {
            let el = activeScenario.versions[scenarioId]
            if (el?.specialTalks?.damageReport === true) {
              listScenario.push({
                value: `${el?.model?.meta?.name}#${scenarioId}`,
                label: el?.displayVersionName,
                production: scenarioId === productionScenarioVersions,
              })
            }
          }
        }
        talkMsgList.value = await GetScenarioDataByDataType(currentActiveScenarioVersion.value.scenarioId, currentActiveScenarioVersion.value.versionId, "talk")
        activeTalkVersionId.value = talkMsgList.value?.items?.find(x => x.dataId === "DAMAGE_REPORT_TALK")?.versionActive || "DAMAGE_REPORT_TALK"
      }
      scenarioOptions.value = listScenario
    }
    loadings.value.scenarioOptionsLoading = false
  }, 500)

  async function getDRFormConfig(scenario, talkDrVersionId?) {
    const payload = {
      scenario: scenario || `${currentActiveScenarioVersion.value.scenarioId}#${currentActiveScenarioVersion.value.versionId}`,
      talkDrVersionId: talkDrVersionId || "DAMAGE_REPORT_TALK"
    }
    if (payload.talkDrVersionId === "DAMAGE_REPORT_TALK") {
      categoryOptions.value = categoryOptionsDefault
      subjectOptions.value = subjectOptionsDefault
      feedbackOptions.value = feedbackOptionsDefault
      return
    }
    if (!configCache?.[`${payload.scenario}-${payload.talkDrVersionId}`]) {
      configCache[`${payload.scenario}-${payload.talkDrVersionId}`] = await GetScenarioFormConfig(payload)
    }
    const formConfig = configCache[`${payload.scenario}-${payload.talkDrVersionId}`]
    const { subject: subjectConfig, category: categoryConfig, feedback: feedbackConfig, images, location, ...otherConfig } = formConfig
    categoryOptions.value = categoryConfig?.items?.map(x => ({
      id: x.id,
      value: x.value,
      label: x.value,
      parentValue: x.parentValue
    })) || []
    subjectOptions.value = subjectConfig?.items?.map(x => ({
      id: x.id,
      value: x.value,
      label: x.value,
      parentValue: x.parentValue
    })) || []
    feedbackOptions.value = feedbackConfig?.items?.map(x => ({
      id: x.id,
      value: x.value,
      label: x.value,
      parentValue: x.parentValue
    })) || []
    return otherConfig
  }
  // crud report
  async function getNextDamageReports(startRow = 0, count = 0) {
    loadings.value.queryDamageReport = true
    try {
      let res: any;
      if (curFilterCondition.value !== null) {
        // apply filter
        res = await QueryFilterDamageReports({
          ...curFilterCondition.value,
          lastEvaluatedKey: lastEvaluatedKey.value,
        })
      }
      else {
        res = await GetAllDamageReports({
          lastEvaluatedKey: lastEvaluatedKey.value,
        })
      }

      if (!res?.data) {
        return
      }

      if (!lastEvaluatedKey.value) {
        // first page query
        tableReportData.value = res.data
      }
      else {
        tableReportData.value.push(...res.data)
      }
      totalReportDataCount.value = res.total
      lastEvaluatedKey.value = res.lastEvaluatedKey

      // case 1 request not get all/enough data => loop to get more
      if (count === 0) {
        while (lastEvaluatedKey.value && (startRow + count > tableReportData.value.length)) {
          if (curFilterCondition.value !== null) {
            // apply filter
            res = await QueryFilterDamageReports({
              ...curFilterCondition.value,
              lastEvaluatedKey: lastEvaluatedKey.value,
            })
          }
          else {
            res = await GetAllDamageReports({
              lastEvaluatedKey: lastEvaluatedKey.value,
            })
          }
          if (!res?.data) {
            break
          }
          tableReportData.value.push(...res.data)
          totalReportDataCount.value = res.total
          lastEvaluatedKey.value = res.lastEvaluatedKey
        }
      }
    } catch (error) {
      if (error?.response?.data?.message) {
        alertError(error?.response?.data?.message)
      }
      else {
        alertError(t(`errors.${error.code}`))
      }
    }
    loadings.value.queryDamageReport = false
  }
  async function triggerFilter() {
    loadings.value.queryDamageReport = true
    const filter = cloneDeep(filterFormData.value)
    if (filter.modifiedAtFrom) {
      filter.fromDate = dayjs(filter.modifiedAtFrom, 'YYYY/MM/DD').startOf('day').toISOString()
      delete filter.modifiedAtFrom
    }
    if (filter.modifiedAtTo) {
      filter.toDate = (dayjs(filter.modifiedAtTo, 'YYYY/MM/DD').endOf('day')).toISOString()
      delete filter.modifiedAtTo
    }

    // if no filter condition
    if (every(filter, isEmpty)) {
      curFilterCondition.value = null
    }
    else {
      curFilterCondition.value = filter
    }

    // reset table data (to get new filtered data)
    lastEvaluatedKey.value = null
    tableReportData.value = []

    loadings.value.queryDamageReport = false
    return
  }

  async function createDamageReport(reportData) {
    loadings.value.createDamageReport = true
    const result = await CreateDamageReports(reportData).catch(handleError)
    loadings.value.createDamageReport = false
    return result
  }
  async function getDamageReportsByID(id: string) {
    loadings.value.crudDamageReport = true
    const result = await GetDamageReportsByID(id).catch(handleError)
    loadings.value.crudDamageReport = false
    return result
  }
  async function deleteDamageReportsByID(id: string) {
    loadings.value.crudDamageReport = true
    const result = await DeleteDamageReportsByID(id).catch(handleError)
    loadings.value.crudDamageReport = false
    return result
  }
  async function updateDamageReportsByID(id: string, reportData) {
    loadings.value.crudDamageReport = true
    const result = await UpdateDamageReportsByID(id, reportData).catch(handleError)
    loadings.value.crudDamageReport = false
    return result
  }

  async function uploadImagesS3(files: HTMLInputElement["files"]) {
    loadings.value.uploadImagesS3 = true
    const result = await UploadImagesS3(files).catch(handleError)
    loadings.value.uploadImagesS3 = false
    return result
  }
  async function getSignedImagesInfo(payload) {
    let result = await GetSignedImagesInfo(payload).catch(handleError)
    return result
  }
  // #endregion

  async function exportReportToCSV() {
    loadings.value.exportCSV = true
    const filter = cloneDeep(filterFormData.value)
    if (filter.modifiedAtFrom) {
      filter.fromDate = dayjs(filter.modifiedAtFrom, 'YYYY/MM/DD').toISOString()
      delete filter.modifiedAtFrom
    }
    if (filter.modifiedAtTo) {
      filter.toDate = (dayjs(filter.modifiedAtTo, 'YYYY/MM/DD').add(1, 'day')).toISOString()
      delete filter.modifiedAtTo
    }
    const [scenarioName, scenarioVersion] =  filter.scenarioId.split('#')
    const talkMsg = await GetScenarioDataByDataType(scenarioName, scenarioVersion, "talk")
    const versionActive = talkMsg?.items?.find(x=>x.dataId === "DAMAGE_REPORT_TALK")?.versionActive || "DAMAGE_REPORT_TALK"
    const payload = {
      filter,
      settings: await getDRFormConfig(filter.scenarioId, versionActive)
    }
    const result = await ExportReportToCSV(payload).catch(handleError)
    loadings.value.exportCSV = false
    return result
  }


  function handleError(error) {
    if (error?.response?.data?.message) {
      alertError(error?.response?.data?.message)
    }
    else {
      alertError(t(`errors.${error.code}`))
    }
  }

  async function queryAdminChatStatus(gids: string[]) {
    const lstGroupStatusUnknown: string[] = []
    for (const gid of gids) {
      if (adminReadChatStatus.value[gid] !== undefined) {
        // skip
      }
      else {
        lstGroupStatusUnknown.push(gid)
      }
    }
    if (lstGroupStatusUnknown.length > 0) {
      const res = await GetBatchGroupAdminReadStatus({
        gid: lstGroupStatusUnknown,
        uid: 'uid-manager-common'
      })
      for (const key in res) {
        adminReadChatStatus.value[key] = res[key]
      }
    }
  }

  return {
    // state
    loadings,

    isShowFilterForm,
    filterFormData,
    curFilterCondition,
    lstScenarioOptions,

    tableReportData,
    adminReadChatStatus,
    totalReportDataCount,
    lastEvaluatedKey,
    isClearCacheReportData,


    scenarioOptions,
    statusOptions,
    categoryOptions,
    subjectOptions,
    feedbackOptions,
    activeTalkVersionId,

    // getters

    // actions
    getNextDamageReports,
    triggerFilter,
    createDamageReport,
    getDamageReportsByID,
    updateDamageReportsByID,
    deleteDamageReportsByID,
    queryAdminChatStatus,

    uploadImagesS3,
    getSignedImagesInfo,
    buildOptionsFromScenario,
    getDRFormConfig,
    exportReportToCSV,
  }
})
