export interface CollectionOptions {
  id: string
  value: string
  label: string
  parentValue?: {
    id: string,
    value: string
  }
}

export const categoryOptionsDefault: CollectionOptions[] = [
  {
    id: '01',
    value: '河川',
    label: '河川'
  },
  {
    id: '02',
    value: '公園',
    label: '公園'
  },
]


export const subjectOptionsDefault: CollectionOptions[] = [
  {
    id: '0101',
    value: '河川',
    label: '河川',
    parentValue: {
      id: '01',
      value: '河川',
    }
  },
  {
    id: '0102',
    value: 'その他',
    label: 'その他',
    parentValue: {
      id: '01',
      value: '河川',
    }
  },
  {
    id: '0201',
    // "Playground equipments"
    value: '遊具',
    label: '遊具',
    parentValue: {
      id: '02',
      value: '公園',
    }
  },
  {
    id: '0202',
    // "Lightings"
    value: '照明灯',
    label: '照明灯',
    parentValue: {
      id: '02',
      value: '公園',
    }
  },
  {
    id: '0203',
    // "Resting facilities"
    value: '休憩施設',
    label: '休憩施設',
    parentValue: {
      id: '02',
      value: '公園',
    }
  },
  {
    id: '0204',
    // "Water facilities"
    value: '水回り',
    label: '水回り',
    parentValue: {
      id: '02',
      value: '公園',
    }
  },
  {
    id: '0205',
    // "Trees"
    value: '樹木',
    label: '樹木',
    parentValue: {
      id: '02',
      value: '公園',
    }
  },
  {
    id: '0206',
    // "Others"
    value: 'その他',
    label: 'その他',
    parentValue: {
      id: '02',
      value: '公園',
    }
  }
]

export const feedbackOptionsDefault: CollectionOptions[] = [
  {
    id: '010101',
    // 'Damaged',
    value: "破損",
    label: "破損",
    parentValue: {
      id: '0101',
      value: '河川',
    }
  },
  {
    id: '010102',
    // 'Trees and grass are overgrown',
    value: "木や草が茂っている",
    label: "木や草が茂っている",
    parentValue: {
      id: '0101',
      value: '河川',
    }
  },
  {
    id: '010103',
    // 'Oil/foam floating',
    value: "油・泡が浮いている",
    label: "油・泡が浮いている",
    parentValue: {
      id: '0101',
      value: '河川',
    }
  },
  {
    id: '010104',
    // 'Other',
    value: "その他",
    label: "その他",
    parentValue: {
      id: '0101',
      value: '河川',
    }
  },

  // 'Playground equipment': ['Damaged', 'Other'],
  {
    id: '020101',
    // 'Damaged',
    value: "破損",
    label: "破損",
    parentValue: {
      id: '0201',
      value: '遊具',
    }
  },
  {
    id: '020102',
    // 'Other',
    value: "その他",
    label: "その他",
    parentValue: {
      id: '0201',
      value: '遊具',
    }
  },

  // Lighting: ['Not lit', 'Damaged', 'Other'],
  {
    id: '020201',
    value: "不点灯",
    label: "不点灯",
    parentValue: {
      id: '0202',
      value: '照明灯',
    }
  },
  {
    id: '020202',
    value: "破損",
    label: "破損",
    parentValue: {
      id: '0202',
      value: '照明灯',
    }
  },
  {
    id: '020203',
    value: "その他",
    label: "その他",
    parentValue: {
      id: '0202',
      value: '照明灯',
    }
  },

  // 'Benches, etc. (resting facilities)': ['Damaged', 'Other'],
  {
    id: '020301',
    value: "破損",
    label: "破損",
    parentValue: {
      id: '0203',
      value: '休憩施設',
    }
  },
  {
    id: '020302',
    value: "その他",
    label: "その他",
    parentValue: {
      id: '0203',
      value: '休憩施設',
    }
  },

  /* 'Water facilities (waterworks, toilets, etc.)': [
    'Water leaks',
    'Water does not come out/flow',
    'Other',
    'Damage',
  ], */
  {
    id: '020401',
    value: "水漏れ",
    label: "水漏れ",
    parentValue: {
      id: '0204',
      value: '水回り',
    }
  },
  {
    id: '020402',
    value: "水が出ない・流れない",
    label: "水が出ない・流れない",
    parentValue: {
      id: '0204',
      value: '水回り',
    }
  },
  {
    id: '020403',
    value: "破損",
    label: "破損",
    parentValue: {
      id: '0204',
      value: '水回り',
    }
  },
  {
    id: '020404',
    value: "その他",
    label: "その他",
    parentValue: {
      id: '0204',
      value: '水回り',
    }
  },

  // Trees: ['Damaged', 'Pest infestation', 'Other'],
  {
    id: '020501',
    value: "破損",
    label: "破損",
    parentValue: {
      id: '0205',
      value: '樹木',
    }
  },
  {
    id: '020502',
    value: "害虫発生",
    label: "害虫発生",
    parentValue: {
      id: '0205',
      value: '樹木',
    }
  },
  {
    id: '020503',
    value: "その他",
    label: "その他",
    parentValue: {
      id: '0205',
      value: '樹木',
    }
  },
]