/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

export const MAX_SURVEY_RESULTS_DISPLAY_COUNT = 10000;
export const MAX_SURVEY_RESULTS_ITEMS_REQUEST = 50;

export const DEFAULT_COMMON_SEARCH_CRITERIA = {
  check: ["未対応", "対応中", "対応済", "完了", "保留", "キャンセル"],
} as any;

export const DEFAULT_ALL_HEADERS = [
  { name: "check", label: "状態", field: "check", width: 120, align: "center" },
  { name: "createdAt", label: "作成日", field: "createdAt", width: 200, align: 'left' },
  { name: "updatedAt", label: "更新日", field: "updatedAt", width: 200, align: 'left' },
];

export const PAYMENT_RESULTS_HEADERS = [
  { name: "serviceName", label: "サービス名", field: "serviceName", width: 120, align: 'left' },
  { name: "orderId", label: "受付番号", field: "orderId", width: 200, align: 'left' },
  { name: "productDetails", label: "商品明細", field: "productDetails", width: 200, sortable: false, align: 'left' },
  { name: "littleSum", label: "小計", field: "littleSum", width: 120, align: 'left' },
  { name: "tax", label: "税額", field: "tax", width: 120, align: 'left' },
  { name: "amount", label: "合計金額", field: "amount", width: 200, align: 'left' },
  { name: "taxType", label: "税区分", field: "taxType", width: 120, align: 'left' },
  { name: "taxRate", label: "税率", field: "taxRate", width: 120, align: 'left' },
  { name: "calculationType", label: "消費税計算方法", field: "calculationType", width: 160, align: 'left' },
  { name: "roundingType", label: "端数処理区分", field: "roundingType", width: 160, align: 'left' },
  { name: "payMethod", label: "支払方法", field: "payMethod", width: 120, align: 'left' },
  { name: "paymentStatus", label: "決済ステータス", field: "paymentStatus", width: 160, align: 'left' },
  { name: "paymentCompletedAt", label: "決済日時", field: "paymentCompletedAt", width: 200, align: 'left' },
  { name: "paymentCanceledAt", label: "キャンセル日時", field: "paymentCanceledAt", width: 200, align: 'left' },
];

export const PAYMENT_FIELDS_BY_HEADER_KEY = {
  serviceName: "serviceName",
  orderId: "orderId",
  productDetails: "details",
  littleSum: "littleSum",
  tax: "tax",
  amount: "amount",
  taxType: "taxType",
  taxRate: "taxRate",
  calculationType: "calculationType",
  roundingType: "roundingType",
  payMethod: "payMethod",
  paymentStatus: "status",
  paymentCompletedAt: "completedDate",
  paymentCanceledAt: "canceledDate",
} as any;

export const DEFAULT_ITEMS_PER_PAGE = 50;

export const ITEMS_PER_PAGE_OPTIONS = [
  { text: "50", value: 50 },
  { text: "100", value: 100 },
];

export const SURVEY_RESULTS_CHECK = [
  { text: "未対応", color: "lime" },
  { text: "対応中", color: "blue" },
  { text: "対応済", color: "green" },
  { text: "完了", color: "orange" },
  { text: "保留", color: "purple" },
  { text: "キャンセル", color: "red" },
  { text: "取り消し", color: "grey" },
];

export const DEFAULT_RESULTS_CHECK_COLOR = 'lime';

export const DEFAULT_MULTIPLE_UNICAST_STATUS = {
  status: "ーー",
  username: "ーー",
  startedAt: 0,
  errorMessage: "ーー",
  stats: {
    successCount: 0,
    errorCount: 0,
    total: 0,
  },
};

export const DEFAULT_IMPORT_CSV_APPENDING_STATUS = {
  status: "ーー",
  username: "ーー",
  startedAt: 0,
  errorMessage: "ーー",
  stats: {
    successCount: 0,
    errorCount: 0,
    total: 0,
  },
};

export const DEFAULT_EXPORT_CSV_APPENDING_STATUS = {
  status: "ーー",
  username: "ーー",
  startedAt: 0,
  errorMessage: "ーー",
};
