import { GetMemberResultsData } from "@/services/member.service";
import { MAX_SURVEY_RESULTS_ITEMS_REQUEST } from "./surveyResults.constants";
import { SurveyResultsService } from "@/services/surveyResults.service";
import _ from 'lodash';
import { useSurveyResultsStore } from ".";

const service = new SurveyResultsService();

export const getNextRequestPartitionKeys = (dataTableOptions: any, isMergeRequired: any, surveyResultsPartitionKeys: any, surveyResultsList: any) => {
  if (surveyResultsPartitionKeys.length >= dataTableOptions.itemsPerPage) {
    const from = isMergeRequired ? surveyResultsList.length : (dataTableOptions.page - 1) * dataTableOptions.itemsPerPage;
    const to = from + dataTableOptions.itemsPerPage;
    
    const surveyResultsStore = useSurveyResultsStore();
    surveyResultsStore.setPartitionKeyFrom(from);
    surveyResultsStore.setPartitionKeyTo(to);

    return surveyResultsPartitionKeys.slice(from, to);
  } else {
    return surveyResultsPartitionKeys;
  }
};

export const downloadSurveyResultsItems = async (params: any) => {
  var requests = _.chunk(params.partitionKeys.keys, MAX_SURVEY_RESULTS_ITEMS_REQUEST);

  const downloaded = await Promise.all(
    requests.map(async (keys) => {
      const response = await service.post("/survey/admin/results/download", {
        keys: keys,
        isMember: params.isMember,
      });
      if (response && response.data) return response;
    })
  );

  return downloaded;
};

export const processMemberDetails = async (memberFormId: any, items: any) => {
  var listOfUniqueUserIds = new Set();
  items.map((surveyResults: any) => {
    listOfUniqueUserIds.add(surveyResults.userId);
  });

  let response = await GetMemberResultsData({
    memberFormId: memberFormId,
    userIds: [...listOfUniqueUserIds],
  });
  if (response.result == "OK") {
    let members = response.items;

    items.map((surveyResult: any) => {
      let matchMember = members.find(
        (obj: any) => obj.userId == surveyResult.userId
      );

      if (matchMember) {
        surveyResult.fields = surveyResult.fields.concat(matchMember.fields);
        surveyResult.memberType =
          matchMember.userId && matchMember.userId.length > 0 &&
          matchMember.userId[0] == "U" ? "LINE" : "WEB";
      }
    });
  }
};
