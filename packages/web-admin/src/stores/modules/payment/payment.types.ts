import { MODAL_ACTIONS } from './payment.constants';

export type ModalAction = typeof MODAL_ACTIONS[keyof typeof MODAL_ACTIONS];

export type Service<T> = {
  sortKey?: string;
  serviceId: string;
  serviceName: string;
  receiptCreatorName: string;
  receiptDisplayAddress: string;
  taxType: T;
  calculationType: T;
  roundingType: T;
  reservationServiceType: T;
  reservationCostType: T;
  purchaseLimit: T;
  updatedAt?: number;
}

export type Product<T> = {
  productId: string;
  productName: string;
  price: T,
  productCategories: {
    id: string,
    name: string
  } [];
  taxableType: T;
  order: number;
  status: T;
  updatedAt?: number;
}

export type CsvProductData = {
  serviceId: string;
  productId: string;
  productName: string;
  price: number,
  productCategories: string[];
  taxableType: number;
  order: number;
  status: number;
}

export type ProductCategory = {
  id?: string;
  name: string;
  service?: string;
  updatedAt?: string;
}

export type TaxRateSetting = {
  value: number;
  applyDate: string;
}

export type DisplayTaxRateSetting = {
  isShowDatePicker: boolean;
  isApplying: boolean;
  isNewItem: boolean;
  isEditable: boolean;
} & TaxRateSetting;

export type State = {
  // サービス・商品設定
  paymentServiceList: Service<number>[],

  // 商品設定
  productDisplayFilter: {
    status: number, // 0 | 1 | 2
  },
  productList: Product<number>[],
  isFetchingProductList: boolean,
  fetchProductListError: any,
  productsCsvDownloadUrl: string,
  uploadedBucketKeyOfProductsCsv: string,

  // 商品分類
  productCategoryList: ProductCategory[],

  // 消費税設定
  taxRateSettings: TaxRateSetting[],
}