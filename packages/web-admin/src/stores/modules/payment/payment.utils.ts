import { ProductCategory } from './payment.types';

type KeyForSortProductCategories = keyof Omit<ProductCategory, 'id' | 'service' | 'updatedAt'>
type Order = 'asc' | 'desc';

export const sortProductCategories = (
  categories: ProductCategory[],
  key: KeyForSortProductCategories = 'name',
  order: Order = 'asc'
): void => {
  sortItems(categories, key, order);
}

const sortItems = <T>(items: T[], key: keyof T, order: Order): void => {
  const baseIndex = order === 'asc' ? 1 : -1;
  items.sort((a, b) => {
    if (a[key] > b[key]) return baseIndex;
    if (a[key] < b[key]) return -baseIndex;
    return 0;
  });
}