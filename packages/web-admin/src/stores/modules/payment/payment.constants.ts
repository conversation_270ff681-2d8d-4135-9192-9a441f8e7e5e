import {
  Service,
  Product,
  ProductCategory,
  DisplayTaxRateSetting,
} from './payment.types';

export const MODAL_ACTIONS = {
  CREATE: 'CREATE',
  EDIT: 'EDIT',
} as const;

export const TAX_TYPES = {
  OUTER: '0', // 外税
  INNER: '1' // 内税
};

export const TAX_TYPE_TEXTS = {
  [TAX_TYPES.OUTER]: '外税',
  [TAX_TYPES.INNER]: '内税',
};

export const CALCULATION_TYPES = {
  FOR_EACH: '0', // 明細ごと
  RESULT: '1', // 合計
};

export const CALCULATION_TYPE_TEXTS = {
  [CALCULATION_TYPES.FOR_EACH]: '明細ごと',
  [CALCULATION_TYPES.RESULT]: '合計',
};

export const ROUNDING_TYPES = {
  ROUND_DOWN: '0', // 切捨て
  ROUND_UP: '1', // 切上げ
  ROUNDING: '2', // 四捨五入
};

export const ROUNDING_TYPE_TEXTS = {
  [ROUNDING_TYPES.ROUND_DOWN]: '切捨て',
  [ROUNDING_TYPES.ROUND_UP]: '切上げ',
  [ROUNDING_TYPES.ROUNDING]: '四捨五入',
};

export const RESERVATION_SERVICE_TYPES = {
  APPLICABLE: '0', // 対象
  NOT_APPLICABLE: '1', // 対象外
};

export const RESERVATION_COST_TYPE = {
  FIXED_ONE: '0', // 1固定
  ADDTION: '1', // 合算
};

// サービス購入数の上限下限
export const PAYMENT_PURCHASE_QUANTITY_LIMIT = {
  MIN: 1,
  MAX: 100
}

export const DEFAULT_SERVICE_VALUES: Service<string> = {
  serviceId: '',
  serviceName: '',
  receiptCreatorName: '',
  receiptDisplayAddress: '',
  taxType: TAX_TYPES.OUTER,
  calculationType: CALCULATION_TYPES.FOR_EACH,
  roundingType: ROUNDING_TYPES.ROUND_DOWN,
  reservationServiceType: RESERVATION_SERVICE_TYPES.APPLICABLE,
  reservationCostType: RESERVATION_COST_TYPE.FIXED_ONE,
  purchaseLimit: PAYMENT_PURCHASE_QUANTITY_LIMIT.MIN.toString(),
};



export const INVALID_SERVICE_MESSAGES = {
  SERVICE_ID: 'サービスIDは半角の数値を3桁で入力してください。',
  SERVICE_NAME: 'サービス名は1文字以上40文字以下で利用可能な文字列を入力してください。',
  RECEIPT_CREATOR_NAME: '領収書発行者名は1文字以上300文字以下で入力してください。',
  RECEIPT_DISPLAY_ADRESS: '領収書表示住所は1文字以上300文字以下で入力してください。',
  TAX_TYPE: '税区分は「外税」か「内税」を選択してください。',
  CALCULATION_TYPE: '消費税計算方法は「明細ごと」か「合計」を選択してください。',
  ROUNDING_TYPE: '端数処理は「切捨て」か「切上げ」か「四捨五入」を選択してください。',
  RESERVATION_SERVICE_TYPE: 'カレンダー予約は「する」か「しない」を選択してください。',
  RESERVATION_COST_TYPE: 'カレンダー予約消費枠を利用は「しない(1で固定)」か「する(合算)」を選択してください。',
  PURCHASE_LIMIT: `購入数上限は${PAYMENT_PURCHASE_QUANTITY_LIMIT.MIN}以上/${PAYMENT_PURCHASE_QUANTITY_LIMIT.MAX}以下の半角の整数値を入力してください。`,
} as const;

export const TAXABLE_TYPES = {
  TAXABLE: '0', // 課税対象
  TAX_FREE: '1', // 非課税
};

export const TAXABLE_TYPE_TEXTS = {
  [TAXABLE_TYPES.TAXABLE]: '課税',
  [TAXABLE_TYPES.TAX_FREE]: '非課税',
};

export const PRODUCT_STATUS_TYPES = {
  SALE: '0', // 販売中
  STOP: '1', // 販売停止
};

export const DEFAULT_PRODUCT_VALUES: Product<string> = {
  productId: '',
  productName: '',
  productCategories: [],
  price: '',
  taxableType: TAXABLE_TYPES.TAXABLE,
  order: 0,
  status: PRODUCT_STATUS_TYPES.SALE,
};

// サービス単価の上限
export const PAYMENT_PRICE_LIMIT = 1000000

export const PRODUCT_ID_VALID_LENGTH = {
  MIN: 1,
  MAX: 20
}

export const PRODUCT_CATEGORIES_VALID_COUNT = 10;

export const INVALID_PRODUCT_MESSAGES = {
  PRODUCT_ID: `商品IDは${PRODUCT_ID_VALID_LENGTH.MIN}桁以上${PRODUCT_ID_VALID_LENGTH.MAX}桁以下の半角英数字と一部記号で入力してください。`,
  PRODUCT_NAME: '商品名は1文字以上40文字以下で利用可能な文字列を入力してください。',
  PRODUCT_CATEGORIES: `選択可能な商品分類は${PRODUCT_CATEGORIES_VALID_COUNT}個までです。`,
  PRICE: `単価は0以上${PAYMENT_PRICE_LIMIT}以下の半角の整数値を入力してください。`,
  TAXABLE_TYPE: '課税は「課税」か「非課税」を選択してください。',
  STATUS: 'ステータスは「販売中」か「販売停止」を選択してください。',
} as const;

export const DEFAULT_PRODUCT_CATEGORY_VALUES: ProductCategory = {
  name: ''
};

export const PRODUCT_CATEGORY_MESSAGES_LENGTH = {
  MAX: 50,
  MIN: 1
}
export const INVALID_PRODUCT_CATEGORY_MESSAGES = {
  PRODUCT_CATEGORY_NAME: `商品分類名は${PRODUCT_CATEGORY_MESSAGES_LENGTH.MIN}文字以上${PRODUCT_CATEGORY_MESSAGES_LENGTH.MAX}文字以下入力してください。`,
}

export const DEFAULT_RATE_SETTING: DisplayTaxRateSetting = {
  value: 1,
  applyDate: '',
  isShowDatePicker: false,
  isApplying: false,
  isNewItem: true,
  isEditable: true,
};

export const TAX_RATE_RANGE = {
  MIN: 1,
  MAX: 100
} as const

export const PAYMENT_STATES = {
  COMPLETED: 0,
  NOT_APPLICABLE: 1,
  REFUNDED: 2,
  COMPLETED_CREDIT: 3,
  CANCELED_CREDIT: 4,
};

export const PAYMENT_STATUS_TEXTS = {
  [PAYMENT_STATES.COMPLETED]: '決済完了',
  [PAYMENT_STATES.NOT_APPLICABLE]: '決済対象外',
  [PAYMENT_STATES.REFUNDED]: '返金済み',
  [PAYMENT_STATES.COMPLETED_CREDIT]: '与信完了',
  [PAYMENT_STATES.CANCELED_CREDIT]: '与信取消',
};

const PAY_METHODS = {
  CREDIT: 'credit',
  LINE_PAY: 'linepay',
  PAYPAY: 'paypay',
} as const;

export const PAY_METHOD_TEXTS = {
  [PAY_METHODS.CREDIT]: 'クレジットカード',
  [PAY_METHODS.LINE_PAY]: 'LINE Pay',
  [PAY_METHODS.PAYPAY]: 'PayPay'
} as any