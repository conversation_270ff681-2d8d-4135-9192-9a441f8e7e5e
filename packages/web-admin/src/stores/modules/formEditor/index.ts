import { defineStore } from "pinia";
import _, { cloneDeep, map, find, set } from "lodash";

import { generateUUID, generateShortUUID } from "../../../utils/uuidUtils";
import { IsoLangs } from "../../../constants/index";

import { getFormTemplate } from "../forms/forms.templates/index";
import { GetFormConfigById, GetCategoriesTrees } from "../../../services/form.service";
import { GetMemberFormConfigById } from "../../../services/member.service";
import {
  FetchPaymentServiceList,
  repeatQueryFunction,
} from "../../../services/payment.service";

import { 
  DEFAULT_LANG_DICTIONARY, 
  DEFAULT_LANG, 
  INIT_ITEM, 
  INIT_SECTION, 
  OVERRIDE_ITEM_PROPS_WHEN_DUPLICATE,
  DEFAULT_END_OF_SURVEY_MESSAGE
} from "./formEditor.constants";

import {
  SET_FORM_SCHEMA,
  SET_EDIT_FORM_TYPE,
  UPDATE_FORM_SCHEMA_ITEM,
  UPDATE_FORM_SCHEMA_PAGE_HEADER,
  UPDATE_FORM_SCHEMA_END_OF_SURVEY_MESSAGE,
  UPDATE_FORM_SCHEMA_DELIVERY_MESSAGE,
  UPDATE_FORM_SCHEMA_TEAM_LIST,
  UPDATE_MEMBER_SCHEMA_LINKED_FORMS,
  REMOVE_FORM_SCHEMA_DELIVERY_MESSAGE,
  ADD_FORM_SCHEMA_ITEM,
  ADD_FORM_SCHEMA_SECTION,
  DELETE_FORM_SCHEMA_ITEM,
  DUPLICATE_FORM_SCHEMA_ITEM,
  SET_ACTIVE_FORM_SCHEMA_ITEM,
  SET_ACTIVE_FORM_SCHEMA_SECTION,
  DUPLICATE_FORM_SCHEMA_SECTION,
  DELETE_FORM_SCHEMA_SECTION,
  TOGGLE_MOVE_SECTION_MODAL,
  TOGGLE_CODE_EDITOR_MODAL,
  SET_LANGUAGE_SETTING,
  INIT_LANGUAGE_SETTING,
  SET_ACTIVE_FORM_TAB,
  REMOVE_LANGUAGE_SETTING,
  REMOVE_LANGUAGE_DICTIONARY_SETTING,
  SET_FORM_TITLE,
  UPDATE_FORM_SCHEMA_ELEMENT,
  UPDATE_SURVEY_SCHEMA,
  INCREASE_FORM_COMPONENT_KEY,
  SET_FORM_EDIT_MODE,
  SET_BACK_BUTTON_CONDITION,
  UPDATE_VACCINATION_INTERVAL_TYPE,
  SET_CALENDAR_DATA_OF_CATEGORIES_TREE_1,
  SET_CALENDAR_DATA_OF_CATEGORIES_LARGE_1,
  SET_CALENDAR_DATA_OF_CATEGORIES_MEDIUM_1,
  SET_CALENDAR_DATA_OF_CATEGORIES_TREE_ERROR_1,
  SET_CALENDAR_SEARCH_SELECTED_LARGE_CATEGORY_1,

  SET_PAYMENT_SERVICE_LIST_FOR_FORM_EDITOR,
  SET_IS_FETCHING_PAYMENT_SERVICE_LIST,

  SET_REGISTERED_SURVEY_CONFIG_ID,
  SET_REGISTERED_MEMBER_CONFIG_ID,
  SET_FETCH_FORM_CONFIG_DETAIL_ERROR,
  SET_FETCH_MEMBER_CONFIG_ERROR,
} from "../../mutation-types";

import {
  INIT_FORM_SCHEMA,
  // UPDATE_SURVEY_SCHEMA,
  ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_TREE_1,
  ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_LARGE_1,
  ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_MEDIUM_1,
  ACTION_SET_CALENDAR_SEARCH_SELECTED_LARGE_CATEGORY_1,

  ACTION_FETCH_PAYMENT_SERVICE_LIST,
} from "../../action-types";

export default {
  namespaced: false,
};

import { useFormsStore } from "@stores/modules/forms";
import { useMemberStore } from "@stores/modules/member";

const formsStore = useFormsStore();
const memberStore = useMemberStore();

export const useFormEditorStore = defineStore("formEditorStore", {

  state: () => ({
    formTitle: "無題のフォーム",
    formSchema: null as any,
    formType: "survey",
    activeItem: null as any,
    activeSection: null as any,
    showMoveSections: false,
    showCodeEditor: false,
    isEditMode: false,
    //language object
    langSettings: cloneDeep(DEFAULT_LANG_DICTIONARY) as any,
    activeTab: null,

    //force re-render liff review
    previewComponentKey: 0,
    showBackButton: true,

    categoriesTree: {} as any,
    categoriesTreeLarge: [],
    categoriesTreeMedium: [],
    getCategoriesTreeError: null,
    selectedLargeCategory: null,

    langSettingsdefaultLang: "",

    paymentServiceList: [],
    isFetchingPaymentServiceList: false,

    localToSchemaUpdate: false,
    triggerOpenSaveDialog: false,
  }),

  getters: {
    sectionLength(): any {
      return this.formSchema ? this.formSchema.length : 0;
    },
  
    defaultLangDictionary(): any {
      return Object.keys(this.langSettingsdefaultLang).map((dictKey) => {
        return {
          key: dictKey,
        };
      });
    },

    languageSettingsOptions(): any {
      let _langOptions: any = [];
      Object.keys(this.langSettings).forEach((langCode) => {
        let _langObject = IsoLangs[langCode];
        _langOptions.push({
          text: _langObject?.name + ` (${_langObject?.nativeName})`,
          value: langCode,
        });
      });
      return _langOptions;
    },

    //this step only have one section so need to convert to one section config format
    step1SurveyFormat(): any {
      return this.formSchema ? this.formSchema[0] : null;
    },
  },

  actions: {
    // mutations
/*     setFormEditMode(value: any) {
      this.isEditMode = value;
    }, */

    setFormEditMode(value: any) {
      this.isEditMode = value;
    },

/*     setFormSchema(value: any) {
      this.formSchema = value;
    }, */

    setFormSchema(value: any) {
      this.formSchema = value;
    },

    setEditFormType(value: any) {
      this.formType = value;
    },

    increaseFormComponentKey() {
      this.previewComponentKey += 1;
    },

/*     setFormTitle(value: any) {
      this.formTitle = value;
      //set language setting for default lang
      this.langSettingsdefaultLang["@[フォームのタイトル]"] = value;
    }, */

    setFormTitle(value: any) {
      this.formTitle = value;
      //set language setting for default lang
      //this.langSettingsdefaultLang["@[フォームのタイトル]"] = value;
    },

    updateFormSchemaElement(payload: any) {
      let sectionIndex = 0;

      //console.log('payload', payload);
  
      let value = {
        ...this.formSchema[sectionIndex],
      };
  
      if (payload.name && payload.name in payload) {
        value[payload.name] = payload[payload.name];
      }
  
      this.formSchema[sectionIndex] = value;
    },

/*     updateSurveySchema(value: any) {
      this.formSchema[0]["surveySchema"] = value;
      this.previewComponentKey += 1;
    }, */

    updateSurveySchema(value: any) {
      this.formSchema[0]["surveySchema"] = value;
      this.previewComponentKey 
    },

    addFormSchemaItem(type = "text") {
      let section = this.formSchema.find((obj: any) => obj.key == this.activeSection);
      let myType = type;
      const newItem: any = { itemKey: generateShortUUID(), myType, ...INIT_ITEM };
      newItem.isNewItem = true;
      if (section.surveyType === "corona") {
        newItem.isAdminItem = true;
      }
      let activeItemIndex = section.surveySchema.findIndex((obj: any) => obj.itemKey === this.activeItem);
  

      if (activeItemIndex === -1) activeItemIndex = section.surveySchema.length;
      section.surveySchema.splice(activeItemIndex + 1, 0, cloneDeep(newItem));
  
      this.activeItem = newItem.itemKey;
    },

    addFormSchemaSection() {
      let newSection = { key: generateUUID(), ...cloneDeep(INIT_SECTION) };
      let activeSectionIndex = this.formSchema.findIndex((obj: any) => obj.key === this.activeSection);
      this.formSchema.splice(activeSectionIndex + 1, 0, newSection);
      this.activeItem = newSection.key;
      this.activeSection = newSection.key;
    },

    deleteFormSchemaItem({ sectionKey, itemKey }: any) {
      let section = this.formSchema.find((obj: any) => obj.key === sectionKey);
      let itemIndex = section.surveySchema.findIndex((obj: any) => obj.itemKey === itemKey);
  
      this.activeItem = itemIndex === 0 ? sectionKey : section.surveySchema[itemIndex - 1].itemKey;
  
      section.surveySchema.splice(itemIndex, 1);
  
      this.previewComponentKey++;
    },

    deleteFormSchemaSection(sectionKey: any ) {
      let sectionIndex = this.formSchema.findIndex((obj: any) => obj.key === sectionKey);
      let nextSection = sectionIndex === 0 ? this.formSchema[1] : this.formSchema[sectionIndex - 1];
  
      this.activeSection = nextSection.key;
      this.activeItem = nextSection;
  
      this.formSchema.splice(sectionIndex, 1);
    },

/*     duplicateFormSchemaItem({ sectionKey, item }) {
      let section = this.formSchema.find((obj) => obj.key === sectionKey);
      let itemIndex = section.surveySchema.findIndex((obj) => obj.itemKey === item.itemKey);
      let newItem = cloneDeep(item);
      // アイテムコピー時、一部の属性はコピーせずにデフォルト値を引き継ぐ
      const additionalKeys = Object.keys(OVERRIDE_ITEM_PROPS_WHEN_DUPLICATE).reduce((acc, val) => {
        if (Object.keys(newItem).includes(val)) {
          acc[val] = OVERRIDE_ITEM_PROPS_WHEN_DUPLICATE[val]
        }
        return acc;
      }, {})
      delete newItem.itemKey;
      let cloneItem = {
        itemKey: generateShortUUID(),
        ...newItem,
        ...additionalKeys,
        isNewItem: true,
      };
      this.activeItem = cloneItem.itemKey;
  
      section.surveySchema.splice(itemIndex + 1, 0, cloneItem);
    },
 */
    duplicateFormSchemaItem({ sectionKey, item }: any) {
      let section = this.formSchema.find((obj: any) => obj.key === sectionKey);
      let itemIndex = section.surveySchema.findIndex((obj: any) => obj.itemKey === item.itemKey);
      let newItem = cloneDeep(item);
      // アイテムコピー時、一部の属性はコピーせずにデフォルト値を引き継ぐ
      const additionalKeys = Object.keys(OVERRIDE_ITEM_PROPS_WHEN_DUPLICATE).reduce((acc: any, val) => {
        if (Object.keys(newItem).includes(val)) {
          acc[val] = OVERRIDE_ITEM_PROPS_WHEN_DUPLICATE[val]
        }
        return acc;
      }, {})
      delete newItem.itemKey;
      let cloneItem = {
        itemKey: generateShortUUID(),
        ...newItem,
        ...additionalKeys,
        isNewItem: true,
      };
      this.activeItem = cloneItem.itemKey;
  
      section.surveySchema.splice(itemIndex + 1, 0, cloneItem);
    },

    duplicateFormSchemaSection(section: any) {
      let sectionIndex = this.formSchema.findIndex((obj: any) => obj.key === section.key);
      let newSection = cloneDeep(section);
  
      delete newSection.key;
      let cloneSection = {
        key: generateUUID(),
        ...newSection,
        surveySchema: this.formSchema[sectionIndex].surveySchema.map((item: any) => {
          let newItem = cloneDeep(item);
          delete newItem.itemKey;
  
          return {
            itemKey: generateShortUUID(),
            ...newItem,
          };
        }),
      };
      this.activeItem = cloneSection.key;
  
      this.formSchema.splice(sectionIndex + 1, 0, cloneSection);
    },

/*     setActiveFormSchemaItem(value: any) {
      this.activeItem = value;
      this.previewComponentKey++;
    }, */

    setActiveFormSchemaItem(value: any) {
      this.activeItem = value;
      this.previewComponentKey++;
    },

/*     setActiveFormTab(value: any) {
      this.activeTab = value;
    }, */

    setActiveFormTab(value: any) {
      this.activeTab = value;
    },

/*     setActiveFormSchemaSection(value: any) {
      this.activeSection = value;
    }, */

    setActiveFormSchemaSection(value: any) {
      this.activeSection = value;
    },

    toggleMoveSectionModal(value: any) {
      this.showMoveSections = value;
    },

    toggleCodeEditorModal(value: any) {
      this.showCodeEditor = value;
    },

/*     updateFormSchemaPageHeader(payload: any) {
      let sectionIndex = this.formSchema.findIndex((obj) => obj.key === payload.key);
  
      this.formSchema[sectionIndex] = {
        ...this.formSchema[sectionIndex],
        surveyTitle: payload.surveyTitle,
        description: payload.description,
        isAppending: payload.isAppending,
        isSearchable: payload.isSearchable,
        isEditDisabled: payload.isEditDisabled,
        isDeleteAllEnabled: payload.isDeleteAllEnabled,
        surveyStatus: payload.surveyStatus,
        headerImageUrl: payload.headerImageUrl, 
        usePayment: payload.usePayment,
      };
    }, */

    // PageBreak Events. Seems that from Vue 2, dispatches, and broadcasts are discouraged. So in Vue3, we use either buses, or stores to trigger events.
    // PageBreakイベント。Vue 2からは、dispatchesとbroadcastsは非推奨とされているようです。そのため、Vue3では、バスまたはストアを使用してイベントをトリガーします。

    setTriggerLocalToSchemaUpdate(state: boolean) {
      this.localToSchemaUpdate = state;
    },

    triggerLocalToSchemaUpdate() {
      this.localToSchemaUpdate = true;
    },

    openSaveFormDialog() {
      this.triggerOpenSaveDialog = true;
    },

    setTriggerOpenSaveDialog(state: boolean) {
      this.triggerOpenSaveDialog = state;
    },

    // End PageBreak Events.
    // 終了PageBreakイベント。

    updateFormSchemaPageHeader(payload: any) {
      let sectionIndex = this.formSchema.findIndex((obj: any) => obj.key === payload.key);
  
      this.formSchema[sectionIndex] = {
        ...this.formSchema[sectionIndex],
        surveyTitle: payload.surveyTitle,
        description: payload.description,
        isAppending: payload.isAppending,
        isSearchable: payload.isSearchable,
        isEditDisabled: payload.isEditDisabled,
        isDeleteAllEnabled: payload.isDeleteAllEnabled,
        surveyStatus: payload.surveyStatus,
        headerImageUrl: payload.headerImageUrl, 
        usePayment: payload.usePayment,
      };
    },

/*     updateFormSchemaItem(payload: any) {
      let section = this.formSchema.find((obj) => obj.key === payload.sectionKey);
      let itemIndex = section.surveySchema.findIndex((obj) => obj.itemKey === payload.item.itemKey);
  
      //update item at index by payload.item)
      section.surveySchema.splice(itemIndex, 1, cloneDeep(payload.item));
      this.previewComponentKey += 1;
    }, */

    updateFormSchemaItem(payload: any) {
      let section = this.formSchema.find((obj: any) => obj.key === payload.sectionKey);
      let itemIndex = section.surveySchema.findIndex((obj: any) => obj.itemKey === payload.item.itemKey);
      //update item at index by payload.item)
      section.surveySchema.splice(itemIndex, 1, cloneDeep(payload.item));
      this.previewComponentKey += 1;
    },

    updateFormSchemaEndOfSurveyMessage(payload: any) {
      let sectionIndex = this.formSchema.findIndex((obj: any) => obj.key === payload.key);
  
      this.formSchema[sectionIndex] = {
        ...this.formSchema[sectionIndex],
        endOfSurveyMessage: payload.endOfSurveyMessage,
        endOfSurveyMessageType: payload.endOfSurveyMessageType,
      };
    },

    updateFormSchemaDeliveryMessage(payload: any) {
      let sectionIndex = this.formSchema.findIndex((obj: any) => obj.key === payload.key);
      let _deliveryMessageKey = payload.deliveryMessageKey;
      if (!this.formSchema[sectionIndex].deliveryMessageSetting) {
        this.formSchema[sectionIndex] = {
          ...this.formSchema[sectionIndex],
          deliveryMessageSetting: []
        };
      }
  
      this.formSchema[sectionIndex].deliveryMessageSetting[_deliveryMessageKey] = {
        ...this.formSchema[sectionIndex].deliveryMessageSetting[_deliveryMessageKey],
        deliveryTitle: payload.deliveryTitle,
        deliveryMessage: payload.deliveryMessage,
      };
    },

    removeFormSchemaDeliveryMessage(payload: any) {
      let sectionIndex = this.formSchema.findIndex((obj: any) => obj.key === payload.key);
      let _deliveryMessageKey = payload.deliveryMessageKey;
      if (!this.formSchema[sectionIndex].deliveryMessageSetting) {
        this.formSchema[sectionIndex] = {
          ...this.formSchema[sectionIndex],
          deliveryMessageSetting: []
        };
      }
  
      this.formSchema[sectionIndex].deliveryMessageSetting.splice(_deliveryMessageKey, 1);
  
      let deliveryMessageSettingList = cloneDeep(this.formSchema[sectionIndex].deliveryMessageSetting);
  
      this.formSchema[sectionIndex] = {
        ...this.formSchema[sectionIndex],
        deliveryMessageSetting: deliveryMessageSettingList
      };
    },

    updateFormSchemaTeamList(payload: any) {
      let sectionIndex = this.formSchema.findIndex((obj: any) => obj.key === payload.key);
  
      this.formSchema[sectionIndex] = {
        ...this.formSchema[sectionIndex],
        surveyTeams: payload.surveyTeams,
      };
    },

    updateMemberSchemaLinkedForms(payload: any) {
      let sectionIndex = this.formSchema.findIndex((obj: any) => obj.key === payload.key);
  
      this.formSchema[sectionIndex] = {
        ...this.formSchema[sectionIndex],
        linkedForms: payload.linkedForms,
      };
    },

    setLanguageSetting({ langCode, langKey, langValue }: any) {
      var _langObject = cloneDeep(this.langSettings[langCode]);
      //if exists object, add or update value by key
      if(_langObject) {
        let _langObj = this.langSettings[langCode];
        _langObj[langKey] = langValue;
        this.langSettings = Object.assign({}, this.langSettings, {
          [langCode]: cloneDeep(_langObj),
        });
      } else {
        //else create new object language
        this.langSettings[langCode] = {};
        this.langSettings[langCode][langKey] = langValue;
      }
    },

    initLanguageSetting(langCode: any) {
      this.langSettings[langCode] = cloneDeep(this.langSettingsdefaultLang);
    },

    removeLanguageSetting(langCode: any) {  
      delete this.langSettings[langCode];
    },

    removeLanguageDictionarySetting(dictKey: string) {
      Object.keys(this.langSettings).forEach((langCode) => {
        let _langObj = this.langSettings[langCode];
  
        //remove dictKey inside default lang
        delete _langObj[dictKey];
        this.langSettings = Object.assign({}, this.langSettings, {
          [langCode]: cloneDeep(_langObj),
        });
      });
    },

/*     setBackButtonCondition(value: any) {
      this.showBackButton = value;
    }, */

    setBackButtonCondition(value: any) {
      this.showBackButton = value;
    },

/*     updateVaccinationIntervalType(payload: any) {
      const { sectionKey, vaccinationIntervalType } = payload;
      const section = this.formSchema.find((obj) => obj.key === sectionKey);
      section.vaccinationIntervalType.input = vaccinationIntervalType;
    }, */

    updateVaccinationIntervalType(payload: any) {
      const { sectionKey, vaccinationIntervalType } = payload;
      const section = this.formSchema.find((obj: any) => obj.key === sectionKey);
      section.vaccinationIntervalType.input = vaccinationIntervalType;
    },
  
    [SET_CALENDAR_DATA_OF_CATEGORIES_TREE_1](value: any) {
      this.categoriesTree = value;
    },

    [SET_CALENDAR_DATA_OF_CATEGORIES_LARGE_1](value: any) {
      this.categoriesTreeLarge = value;
    },

    [SET_CALENDAR_DATA_OF_CATEGORIES_MEDIUM_1](value: any) {
      this.categoriesTreeMedium = value;
    },

    setCalendarDataOfCategoriesTreeError1(value: any) {
      this.getCategoriesTreeError = value;
    },
  
    setCalendarSearchSelectedLargeCategory1(value: any) {
      this.selectedLargeCategory = value;
    },
  
    setPaymentServiceListForFormEditor(value: any) {
      this.paymentServiceList = value
    },

    setIsFetchingPaymentServiceList(value: any) {
      this.isFetchingPaymentServiceList = value
    },

    // actions
     async initFormSchema(payload: any) {
      let initForm = [];
      this.setEditFormType(payload.formType);
  
      //this.formTypeAttr("mutationSetFetchDetailError", payload.formType), null;
      //get template survey if init by template
      try {
        if (payload.template) {
          this.setFormEditMode(false);
          let _initConfig = getFormTemplate(payload.template);
          initForm = cloneDeep(_initConfig);
        } else if (payload.surveyId) {
          //get survey config from list if init by detail survey
          //already get configs and save in store
          this.setFormEditMode(true);
  
          let getConfigMethod = formTypeAttr("getFormConfigMethod", payload.formType);
          const _result = await getConfigMethod(payload.surveyId);
          if (_result.result === "OK") {
            initForm = [
              {
                key: generateUUID(),
                ..._result.data,
              },
            ];
          } else {
            console.error('error', _result.errorMessage);
            if (_result.errorMessage.indexOf("閲覧設定") > -1) {
              //this.formTypeAttr("mutationSetFetchDetailError", payload.formType), new Error(_result.errorMessage);
              return { noInspectPermission: true };
            }
            //this.formTypeAttr("mutationSetFetchDetailError", payload.formType), new Error();
            console.error(_result.errorMessage);
            return false;
          }
        }
  
        this.setFormSchema(initForm);
        this.setActiveFormSchemaSection(initForm[0].key);
        this.setActiveFormSchemaItem(initForm[0]?.surveySchema[0]?.itemKey || null);
        //this.formTypeAttr("mutationSetRegisteredConfigId", payload.formType), null;
        return true;
      } catch (error: any) {
        //this.formTypeAttr("mutationSetFetchDetailError", payload.formType), error;
        return error;
      }
    },

    /* async initFormSchema(payload: any) {
      let initForm = [];
  
      this.setFormEditMode(true);
  
      let _result = await GetFormConfigById(payload.surveyId);
      if (_result.result === "OK") {
        initForm = [
          {
            key: generateUUID(),
            ..._result.data,
          },
        ];
      } else {
        if (_result.errorMessage.indexOf("閲覧設定") > -1) {
          setFetchFormConfigDetailError(payload.formType,new Error(_result.errorMessage));
          return { noInspectPermission: true };
        }
        setFetchFormConfigDetailError(payload.formType, new Error());
        console.log(_result.errorMessage);
        return false;
      }
  
      this.setFormSchema(initForm);
      this.setActiveFormSchemaSection(initForm[0].key);
      this.setActiveFormSchemaItem(
        initForm[0] && initForm[0].surveySchema[0] && initForm[0].surveySchema[0].itemKey
          ? initForm[0].surveySchema[0].itemKey
          : null
      );

      setRegisteredConfigId(payload.formType, payload.surveyId);
      return true;
    }, */

    async setCalendarDataOfCategoriesTree1 (value: any = undefined) {
      try {
        let result = await GetCategoriesTrees();
  
        //this.calendarDataOfCategoriesTree1 = result;
        this.categoriesTree = result;
  
        if (result.tree) {
          this.setCalendarDataOfCategoriesLarge1(result.tree);
        }
      } catch (error: any) {
        this.setCalendarDataOfCategoriesTreeError1(error);
      }
    },

    async setCalendarDataOfCategoriesLarge1(value: any) {

      //this.setCalendarDataOfCategoriesLarge1(value);
      this.categoriesTreeLarge = value;
    },
  
    async setCalendarDataOfCategoriesMedium1(value: any) {
      let selectedLargeMediumName = value && value.name ? value.name : null;
      if (!selectedLargeMediumName) {
        return;
      }
  
      let categoriesMedium = this.findCategoriesTreeMedium(selectedLargeMediumName);
      if(categoriesMedium) {
        //this.setCalendarDataOfCategoriesMedium1(categoriesMedium);
        this.categoriesTreeMedium = categoriesMedium;
      }
    },
  
    async [ACTION_SET_CALENDAR_SEARCH_SELECTED_LARGE_CATEGORY_1] (value: any) {
      this.setCalendarSearchSelectedLargeCategory1(value);
    },
  
    async fetchPaymentServiceList (): Promise<void> {
      try {
        this.setIsFetchingPaymentServiceList(true);
  
        const payload = { fields: 'sortKey,serviceId,serviceName,reservationServiceType' };
        let serviceList: any[] = await repeatQueryFunction(FetchPaymentServiceList, payload);
        serviceList = serviceList.filter(s => s.reservationServiceType === 1)
        this.setPaymentServiceListForFormEditor(serviceList);
      } catch (error: any) {
        console.error(new Error(error));
      } finally {
        this.setIsFetchingPaymentServiceList(false);
      }
    },

    findCategoriesTreeMedium(largeCategory: any) {
      if (this.categoriesTree && this.categoriesTree.tree) {
        let categoriesTreeLarge = find(this.categoriesTree.tree, { name: largeCategory });
        if (categoriesTreeLarge && categoriesTreeLarge.children) {
          return categoriesTreeLarge.children;
        }
      }
      return null;
    },
  }
});


const INITIAL_FORM = [
  {
    key: generateUUID(),
    surveyTitle: "無題アンケート",
    description: "",
    endOfSurveyMessage: DEFAULT_END_OF_SURVEY_MESSAGE,
    type: "PAGE_BREAK",
    surveyStatus: "enable",
    surveySchema: [
      {
        itemKey: generateShortUUID(),
        ...INIT_ITEM,
      },
    ],
  },
];

const FORM_TYPE_ATTR_MAP: any = {
  mutationSetFetchDetailError: {
    survey: SET_FETCH_FORM_CONFIG_DETAIL_ERROR,
    member: SET_FETCH_MEMBER_CONFIG_ERROR,
  },
  mutationSetRegisteredConfigId: {
    survey: SET_REGISTERED_SURVEY_CONFIG_ID,
    member: SET_REGISTERED_MEMBER_CONFIG_ID,
  },
  getFormConfigMethod: {
    survey: GetFormConfigById,
    member: GetMemberFormConfigById,
  },
};

function formTypeAttr(attr: any, formType: any) {
  return FORM_TYPE_ATTR_MAP[attr][formType ? formType : "survey"];
}

function setFetchFormConfigDetailError(formType: string, error: Error) {
  if(formType === "survey") {
    formsStore.setFetchFormConfigDetailError(error);
  } else {
    memberStore.setFetchMemberConfigError(error);
  }
}

function setRegisteredConfigId(formType: string, id: string) {
  if(formType === "survey") {
    formsStore.setRegisteredSurveyConfigId(id);
  } else {
    memberStore.setRegisteredMemberConfigId(id);
  }
}