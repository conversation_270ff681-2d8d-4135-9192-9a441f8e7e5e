export const TALK_DELIVERY_ENVIRONMENT_DISPLAY_TEXT = {
  production: "本番",
  sandbox: "サンドボックス"
}

export const DEFAULT_REMINDER_SETTINGS = {
  daysBefore: 1,
  sendTime: '12:00',
  messages: [
    {
      text: '',
      type: 'text',
      imageUrl: '',
      file: null,
      error: {
        value: false,
        message: '',
      },
    },
  ],
}

// https://developers.line.biz/ja/reference/messaging-api/#text-message
// 最大文字数：5000
// LINE絵文字は絵文字ごとに文字サイズカウント数が異なるため、余分をもって上限を3000にする
export const MAX_TEXTMESASAGE_LENGTH = 3000

// https://developers.line.biz/ja/reference/messaging-api/#text-message
// emojis 最大個数：20
export const MAX_EMOJIS_LENGTH_IN_TEXTMESSAGE = 20

export const REMINDER_TYPES = {
  APPOINTMENT: 'appointment',
  DATE_RELATIVE: 'date_relative',
}

import hotel from "@/constants/FlexmessageSample/hotel.json";
import menu from "@/constants/FlexmessageSample/menu.json";
import realEstate from "@/constants/FlexmessageSample/realEstate.json";
import receipt from "@/constants/FlexmessageSample/receipt.json";
import restaurant from "@/constants/FlexmessageSample/restaurant.json";
import social from "@/constants/FlexmessageSample/social.json";
import ticket from "@/constants/FlexmessageSample/ticket.json";
import transit from "@/constants/FlexmessageSample/transit.json";
export const FLEXMESSAGE_TEMPLATE_ITEMS = [
  { text: "Restaurant", value: restaurant },
  { text: "Real Estate", value: realEstate },
  { text: "Transit", value: transit },
  { text: "Menu", value: menu },
  { text: "Hotel", value: hotel },
  { text: "Social", value: social },
  { text: "Receipt", value: receipt },
  { text: "Ticket", value: ticket },
]
