import { ReservableAge } from './calendar.types';

interface ReservationPeriodParams {
  reservationControlType: string | number;
  reservationPossibleStart: string | number;
  reservationPossibleMonths: string | number;
  reservationCancelLimit: string | number;
}
interface ReservationLimitsParams<T> {
  reservationMaxCount: T;
  reservationMaxCountOfDay: T;
  reservableAge?: ReservableAge<T>;
}
interface ReservationLimitsPayload {
  reservationMaxCount: number;
  reservationMaxCountOfDay: number;
  reservableAge?: ReservableAge<number>
}
export const ControlTypeOfReservableAge = {
  allAge: 0,
  fullAge: 1,
  specialSetting: 2,
} as const;
type KeyOfControlType = keyof typeof ControlTypeOfReservableAge; 

abstract class ReservationSettingsHelper {
  isValidNumber(value: string | number): boolean {
    const pattern = /^([1-9]\d*|0)$/;
    return pattern.test(String(value));
  }

  isValidBounday(min: number, max: number, value: string | number): boolean {
    if (!this.isValidNumber(value)) {
      return false;
    }
    const num = Number(value);
    return num >= min && num <= max;
  }

  validateBoundary(min: number, max: number, value: string | number, itemName: string): void {
    if (!this.isValidBounday(min, max, String(value))) {
      throw new Error(`${itemName}は半角の${min}~${max}を入力してください。`);
    }
  }

  validateRequired(settings: ReservationLimitsParams<string | number> | ReservationPeriodParams, requiredParams: string[]): void {
    requiredParams.forEach(param => {
      if (!(param in settings)) {
        throw new Error(`[${param}] is required.`);
      }
    });
  }

  validateUnnecessary(settings: ReservationLimitsParams<string | number> | ReservationPeriodParams, requiredParams: string[]): void {
    Object.keys(settings).forEach(param => {
      if (!requiredParams.includes(param)) {
        throw new Error(`[${param}] is unnecessary.`);
      }
    });
  }
}

export class ReservationLimits extends ReservationSettingsHelper {
  private reservationMaxCount: string | number;
  private reservationMaxCountOfDay: string | number;
  private reservableAge?: ReservableAge<string | number>;
  constructor(settings: ReservationLimitsParams<string | number>) {
    super();
    this.validateParams(settings);
    this.reservationMaxCount = settings.reservationMaxCount;
    this.reservationMaxCountOfDay = settings.reservationMaxCountOfDay;
    this.reservableAge = settings.reservableAge;
  }

  private isValidMaxCount(): boolean {
    return this.isValidBounday(0, 99, this.reservationMaxCount);
  }

  private isValidMaxCountOfDay(): boolean {
    return this.isValidBounday(0, 99, this.reservationMaxCountOfDay);
  }

  private isValidControlTypeForReservableAge(): boolean {
    return [0, 1, 2].includes(Number(this.reservableAge?.controlType));
  }

  private isValidReservableAgeSettings(): boolean {
    const min = 0;
    const max = 200;
    return this.reservableAge?.settings.every(({ start, end }) => {
      const isValidStart = this.isValidBounday(min, max, String(start));
      const isValidEnd = this.isValidBounday(min, max, String(end));
      const _start = Number(start);
      const _end = Number(end);
      return isValidStart && isValidEnd && _start <= _end;
    }) ?? false;
  }

  controlTypeIs(type: KeyOfControlType): boolean {
    return Number(this.reservableAge?.controlType) === ControlTypeOfReservableAge[type];
  }

  isValid(): boolean {
    const isValidReservableCount = this.isValidMaxCount() && this.isValidMaxCountOfDay();
    const isValid = this.controlTypeIs('fullAge')
      ? isValidReservableCount && this.isValidControlTypeForReservableAge() && this.isValidReservableAgeSettings()
      : isValidReservableCount && this.isValidControlTypeForReservableAge();
    return isValid;
  }

  private validateParams(settings: ReservationLimitsParams<string | number>): void {
    const requiredParams = ['reservationMaxCount', 'reservationMaxCountOfDay', 'reservableAge'];
    this.validateRequired(settings, requiredParams);
    this.validateUnnecessary(settings, requiredParams);
  }

  private validateMaxCount(): void {
    this.validateBoundary(0, 99, this.reservationMaxCount, '同時予約可能数の予約可能期間中');
  }

  private validateMaxCountOfDay(): void {
    this.validateBoundary(0, 99, this.reservationMaxCountOfDay, '同時予約可能数の同日内');
  }

  private validateControlTypeForReservableAge(): void {
    if (!this.isValidControlTypeForReservableAge()) {
      throw new Error('予約可能年齢制限を有効もしくは無効にしてください。');
    }
  }

  private validateReservableAgeSettings(): void {
    const min = 0;
    const max = 200;
    this.reservableAge?.settings.forEach(({ start, end }) => {
      this.validateBoundary(min, max, String(start), '予約可能年齢の開始値');
      this.validateBoundary(min, max, String(end), '予約可能年齢の終了値');
      const _start = Number(start);
      const _end = Number(end);
      if (_start > _end) {
        throw new Error('予約可能年齢は開始値 <= 終了値となるように入力してください。');
      }
    });
  }

  validate(): void {
    this.validateMaxCount();
    this.validateMaxCountOfDay();
    this.validateControlTypeForReservableAge();
    if (this.controlTypeIs('fullAge')) {
      this.validateReservableAgeSettings();
    }
  }

  private convertReservableAgeForPayload(reservableAge: ReservableAge<string | number> | undefined): ReservableAge<number> | undefined {
    if (!reservableAge) {
      return;
    }
    const settings = reservableAge.settings.map(setting => ({ start: Number(setting.start), end: Number(setting.end) }));
    return {
      controlType: Number(reservableAge.controlType),
      settings
    };
  }

  private convertReservableAgeForInput(): ReservableAge<string> | undefined {
    if (!this.reservableAge) {
      return;
    }
    const settings = this.reservableAge.settings.map(setting => ({ start: String(setting.start), end: String(setting.end) }));
    return {
      controlType: String(this.reservableAge.controlType),
      settings
    };
  }

  toInput(): ReservationLimitsParams<string> {
    return {
      reservationMaxCount: String(this.reservationMaxCount),
      reservationMaxCountOfDay: String(this.reservationMaxCountOfDay),
      reservableAge: this.convertReservableAgeForInput(),
    }
  }

  toPayload(currentReservableAge: ReservableAge<string | number>): ReservationLimitsPayload {
    const payload: ReservationLimitsPayload = {
      reservationMaxCount: Number(this.reservationMaxCount),
      reservationMaxCountOfDay: Number(this.reservationMaxCountOfDay),
    }
    if (!currentReservableAge || (currentReservableAge && !this.controlTypeIs('allAge'))) {
      payload.reservableAge = this.convertReservableAgeForPayload(this.reservableAge);
    } else {
      const reservableAge = {
        controlType: this.reservableAge?.controlType ?? '',
        settings: currentReservableAge.settings,
      }
      payload.reservableAge = this.convertReservableAgeForPayload(reservableAge);
    }
    return payload;
  }
}