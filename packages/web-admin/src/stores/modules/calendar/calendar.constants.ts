export const SHOW_CONTENT_MODE = {
  SHOW_SELECT_CONDITIONS: 0,
  SHOW_SCHEDULE_RESULT: 1,
  SHOW_SELECT_CSV_IMPORT: 2,
  SHOW_CALENDAR_NOT_EXIST: 3,
  SHOW_CALENDAR_NOT_PERMISSION: 4,
};

export const CATEGORIES_TREE_DATA_KEY = {
  DISPLAY: "display",
  TAG1: "tag1",
  TAG2: "tag2",
  TAG3: "tag3",
  NAME: "name",
  CHILDREN: "children",
  ID: "id",
};

export const CALENDAR_SETTING_KEY = {
  RESERVATION_START: 2,
  RESERVATION_POSSIBLE_START: '',
  RESERVATION_POSSIBLE_MONTHS: '',
  RESERVATION_MAX_COUNT: 0, // 初期値は0とし、0の場合予約件数チェックなし
  RESERVATION_MAX_COUNT_OF_DAY: 0, // 同上
  RESERVATION_CANCEL_LIMIT: 1,
  RESERVATION_CONTROL_TYPE: '0',
  RESERVABLE_AGE: {
    controlType: '0',
    settings: [{ start: '0', end: '11' }],
  },
  TIME_ZONES: [
    "0830",
    "0900",
    "0930",
    "1000",
    "1030",
    "1100",
    "1130",
    "1200",
    "1230",
    "1300",
    "1330",
    "1400",
    "1430",
    "1500",
    "1530",
    "1600",
    "1630",
  ],
  PAYMENT_ALIGNMENT: "0"
};

export const CALENDAR_JAPANESE_DATES = ["日", "月", "火", "水", "木", "金", "土"];

export const CALENDAR_DISPLAY_MODE = {
  HAS_DATA: 0,
  NON_DATA: 1,
  SHOW_CHANGE_COMA: 2,
};

export const EDITING_CATEGORY_TYPES = {
  LARGE: 'LARGE',
  MEDIUM: 'MEDIUM',
  SMALL: 'SMALL',
} as const;