import { LargeCategoryTree, MediumCategoryTree, SmallCategoryTree } from './calendar.types';

export const getChildrenTreeByCategoryName = (
  categoriesTree: LargeCategoryTree[] | MediumCategoryTree[],
  categoryName: string
): MediumCategoryTree[] | SmallCategoryTree[] => {
  const targetCategory = categoriesTree.find(categoryTree => categoryTree.name === categoryName);
  return targetCategory?.children ?? [];
};