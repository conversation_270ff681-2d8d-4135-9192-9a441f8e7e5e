import { defineStore } from "pinia";
import _, { find, keys, isEqual, findIndex, has } from "lodash";
import cloneDeep from "lodash/cloneDeep";
import { generateUUID } from "@/utils/uuidUtils";
import { UploadImageToS3 } from "@/services/scenarios.service";

import { 
  SHOW_CONTENT_MODE, 
  CALENDAR_DISPLAY_MODE, 
  CALENDAR_JAPANESE_DATES,
} from "./calendar.constants";

import { PRODUCT_STATUS_TYPES, } from '../payment/payment.constants';

import dayjs, { Dayjs } from "dayjs";

import {
  FetchPaymentServiceList,
  FetchProductList,
  repeatQueryFunction
} from "../../../services/payment.service";
import { CreateCalendarCommaCSV, DeleteCalendar, DownloadCalendarCsv, GetAllCalendars, GetCalendarSchedules, GetCategoriesTrees, getReservationControlInfo, getReservationItemInfo, GetReservationPaymentItemsInfo, GetSingleCategory, ImportComaCSV, ImportCsvCalendarSchedules, UpdateCalendarCategory, UpdateCalendarOff, UpdateCategoriesOrder, UpdateDayOff, UpdatedCalendarSchedules, upsertCalenderPaymentItems, upsertReservationControlInfo, upsertReservationItemInfo } from "@/services/calendar.service";
import { GetSurveyList } from "@/services/form.service";
import { CalendarsList, DisplayingCategory, FetchPaymentServiceListPayload, ReservationItemInfo, TargetDate } from "@/types/index";

export const useCalenderStore = defineStore({
  id: 'calenderStore',

  state: () => ({
    selectedStartDay: null,
    selectedEndDay: null,
    isFetchingScheduleData: false,
    fetchScheduleError: null as any,
    categoriesTree: {} as any,
    categoriesTreeLarge: [],
    categoriesTreeMedium: [],
    categoriesTreeSmall: [],
    getCategoriesTreeError: null,
    selectedLargeCategory: null as any,
    selectedMediumCategory: {} as any,
    selectedSmallCategory: null,
    showContentMode: SHOW_CONTENT_MODE.SHOW_SELECT_CONDITIONS,
    isReloadSchedulesResult: false,
    reloadSchedulesStartDate: null,
    schedulesResult: {},
    comaGroup: {},
    dayOff: {},
    reservationsResult: {},
    calendarSchedules: [],
    calendarTitle: {},
    displayingCategoryId: null,
    displayingCategory: {} as DisplayingCategory,
    targetDates: [] as TargetDate[],
    startDate: null as Date | null,
    datas: [],
    nonDatas: [],
    timezones: [] as any,
    editScheduleMode: false,
    fetchingCalendars: false,
    fetchingCalendarsError: null,
    calendarsList: [] as CalendarsList[],
    fetchingCalendarCommas: false,
    fetchingCalendarCommasError: null,
    calendarCommasUrl: null as any,
    importingCalendarCommas: false,
    importingCalendarCommasError: null,
    updatingCalendar: false,
    updatingCalendarError: null,
    isDeletedCalendar: null,
    reservationControlInfo: [] as any,
    reservationControlInfoWorking: false,
    upsertReservationControlInfoWorking: false,
    reservationControlInfoError: null,
    upsertReservationControlInfoError: null,
    isFetchingExportCalendarCsv: false,
    exportCalendarCsvUrl: null as any,
    exportCalendarCsvError: null,
    reservationItemInfo: {} as ReservationItemInfo,
    reservationItemInfoWorking: false,
    upsertReservationItemInfoWorking: false,
    reservationItemInfoError: null,
    upsertReservationItemInfoError: null,
    singleCategory: {} as any,
    currentCalendar: {} as any,
    categoriesCriteriaTree: {},
    categoriesCriteriaTreeLarge: [],
    categoriesCriteriaTreeMedium: [],
    categoriesCriteriaTreeSmall: [],
    getCategoriesCriteriaTreeError: null,
    selectedLargeCategoryCriteria: null as any,
    selectedMediumCategoryCriteria: null,
    selectedSmallCategoryCriteria: null,
    isFetchingCalendarSettingsInfo: false,
    surveyAndCalendarPermissionsList: [] as any,
    isFetchingSurveyAndCalendarPermissions : false,
    isImportingCalendarSchedules: false,
    isFetchingCategoriesTree: false,
    selectedLargeCategoryName: "",
    selectedMediumCategoryName: "",
    selectedSmallCategoryName: "",

    serviceList: [],
    productListMapByService: {} as any,
    reservationPaymentItemsInfo: {
      data: [],
      maxSerialNumber: 0,
    },
  }),

  getters: {
    getSelectedCategory: (state) => {
      let selectedLargeCategory = state.selectedLargeCategory;
      let selectedMediumCategory = state.selectedMediumCategory;
      let selectedSmallCategory = state.selectedSmallCategory;
  
      if (!selectedLargeCategory || !selectedMediumCategory) {
        return null;
      }
  
      if(selectedSmallCategory) {
        return selectedSmallCategory;
      } else if (selectedMediumCategory.id) {
        return selectedMediumCategory;
      }
  
      return null;
    },
  },

  actions: {
    // mutations
    setIsFetchingScheduleData(value: any) {
      this.isFetchingScheduleData = value;
    },

    setFetchScheduleError(value: any) {
      this.fetchScheduleError = value;
    },
  
    setCalendarDataOfCategoriesLarge(value: any) {
      this.categoriesTreeLarge = value;
    },
  
    setCalendarDataOfCategoriesMedium(value: any) {
      this.categoriesTreeMedium = value;
    },
  
    setCalendarDataOfCategoriesSmall(value: any) {
      this.categoriesTreeSmall = value;
    },
  
    resetCalendarDataOfCategoriesSmall() {
      this.categoriesTreeSmall = [];
    },
  
    resetCalendarSelectedLargeCategory() {
      this.selectedLargeCategory = null;
    },
  
    resetCalendarSelectedMediumCategory() {
      this.selectedMediumCategory = null;
    },
  
    resetCalendarSelectedSmallCategory() {
      this.selectedSmallCategory = null;
    },
  
    setCalendarDataOfCategoriesTree(value: any) {
      this.categoriesTree = value;
    },
  
    setCalendarDisplayData(value: any) {
      this.categoriesTree["display"] = value;
    },
  
    setCalendarDataOfCategoriesTreeError(value: any) {
      this.getCategoriesTreeError = value;
    },
  
    setCalendarSearchSelectedLargeCategory(value: any) {
      this.selectedLargeCategory = value;
    },
  
    setCalendarSearchSelectedMediumCategory(value: any) {
      this.selectedMediumCategory = value;
    },
  
    setCalendarSearchSelectedSmallCategory(value: any) {
      this.selectedSmallCategory = value;
    },
  
    setCalendarDisplayContentMode(value: any) {
      this.showContentMode = value;
    },
  
    setCalendarDisplayCategoryId(value: any) {
      this.displayingCategoryId = value;
    },
  
    setCalendarDisplayCategory(value: any) {
      this.displayingCategory = value;
    },
  
    setCalendarIsReloadSchedulesResult(value: any) {
      this.isReloadSchedulesResult = value;
    },
  
    setCalendarReloadSchedulesStartDate(value: any) {
      this.reloadSchedulesStartDate = value;
    },
  
    setCalendarSchedulesResults(value: any) {
      this.timezones = value.tz;
      this.isDeletedCalendar = null;
      // this.timezones = setTimeZones(value.tz);
      this.schedulesResult = value.data;
      // this.reservations = value.reservations;
      this.reservationsResult = value.resData;
      this.comaGroup = value.comaGroup;
      this.dayOff = value.dayOff;
    },
  
    setCalendarSelectedStartDate(value: any) {
      this.selectedStartDay = value;
    },
  
    setCalendarSelectedEndDate(value: any) {
      this.selectedEndDay = value;
    },
  
    setUpdatedCalendarSchedules(value: any) {
      this.calendarSchedules = value;
    },
  
    setCalendarSelectedTitle(value: any) {
      this.calendarTitle = value;
    },
  
    setCalendarTargetDate(value: any) {
      return setTargetDates(this, value);
    },
  
    setCalendarMatchingData() {
      return setData(this);
    },
  
    setCalendarEditMode(value: any) {
      this.editScheduleMode = value;
    },
  
    setFetchingAllCalendars(value: any) {
      this.fetchingCalendars = value;
    },
  
    setFetchingAllCalendarsError(value: any) {
      this.fetchingCalendarsError = value;
    },

    setAllCalendarsList(value: any) {
      this.calendarsList = value;
    },
  
    setFetchingCalendarCommaCsv(value: any) {
      this.fetchingCalendarCommas = value;
    },
  
    setFetchingCalendarCommaError(value: any) {
      this.fetchingCalendarCommasError = value;
    },
  
    setCalendarCommaCsvUrl(value: any) {
      this.calendarCommasUrl = value;
    },
  
    setImportingCalendarCommaCsv(value: any) {
      this.importingCalendarCommas = value;
    },
  
    setImportingCalendarCommaError(value: any) {
      this.importingCalendarCommasError = value;
    },
  
    setCalendarImportingSchedules(value: any) {
      this.isImportingCalendarSchedules = value;
    },
  
    setIsUpdatingCalendar(value: any) {
      this.updatingCalendar = value;
    },
  
    setIsUpdatingCalendarError(value: any) {
      this.updatingCalendarError = value;
    },
  
    setDeletedCalendar(value: any) {
      this.isDeletedCalendar = value;
      // if (value.length === 0) {
      //     this.isDeletedCalendar = true;
      // } else {
      //     this.isDeletedCalendar = false;
      // }
    },

    setReservationControlInfo(value: any) {
      this.reservationControlInfo = value;
    },

    setReservationControlInfoWork(value: any) {
      this.reservationControlInfoWorking = value;
    },

    setUpsertReservationControlInfoWork(value: any) {
      this.upsertReservationControlInfoWorking = value;
    },

    setReservationControlInfoError(value: any) {
      this.reservationControlInfoError = value;
    },

    setUpsertReservationControlInfoError(value: any) {
      this.upsertReservationItemInfoError = value;
    },

    setReservationItemInfo(value: any) {
      this.reservationItemInfo = value;
    },

    setReservationItemInfoWork(value: any) {
      this.reservationItemInfoWorking = value;
    },

    setUpsertReservationItemInfoWork(value: any) {
      this.upsertReservationItemInfoWorking = value;
    },

    setReservationItemInfoError(value: any) {
      this.reservationItemInfoError = value;
    },

    setUpsertReservationItemInfoError(value: any) {
      this.upsertReservationItemInfoError = value;
    },

    setStateCalendarOff(payload: any) {
      let calendars = _.cloneDeep(this.calendarsList);
      var findCalendarIndex = _.findIndex(calendars, function(o: any) {
        return o.sortKey == payload.calendarId;
      });
      if (findCalendarIndex !== -1) {
        let findCalendar = calendars[findCalendarIndex];
        Object.assign(findCalendar, { calendarOff: payload.calendarOff });
        this.calendarsList = _.cloneDeep(calendars);
      }
    },

    setFetchingExportCalendarCsv(value: any) {
      this.isFetchingExportCalendarCsv = value;
    },

    setExportCalendarCsvUrl(value: any) {
      this.exportCalendarCsvUrl = value;
    },

    setExportCalendarCsvError(value: any) {
      this.exportCalendarCsvError = value;
    },

    setSingleCategory(value: any) {
      this.singleCategory = value;
    },

    setCurrentCalendar(value: any) {
      this.currentCalendar = value;
    },

    setCalendarDataOfCategoriesCriteriaTree(value: any) {
      this.categoriesCriteriaTree = value;
    },

    setCalendarDataOfCategoriesCriteriaLarge(value: any) {
      this.categoriesCriteriaTreeLarge = value;
    },

    setCalendarDataOfCategoriesCriteriaMedium(value: any) {
      this.categoriesCriteriaTreeMedium = value;
    },

    setCalendarDataOfCategoriesCriteriaSmall(value: any) {
      this.categoriesCriteriaTreeSmall = value;
    },

    setCalendarDataOfCategoriesCriteriaTreeError(value: any) {
      this.getCategoriesCriteriaTreeError = value;
    },

    setCategoriesLargeCriteria(value: any) {
      this.selectedLargeCategoryCriteria = value;
    },

    setCategoriesMediumCriteria(value: any) {
      this.selectedMediumCategoryCriteria = value;
    },

    setCategoriesSmallCriteria(value: any) {
      this.selectedSmallCategoryCriteria = value;
    },

    setIsFetchingCalendarSettingsInfo(value: any) {
      this.isFetchingCalendarSettingsInfo = value;
    },

    addSurveyAndCalendarPermissions(value: any) {
      const next = value.data;
      this.surveyAndCalendarPermissionsList.push(...next);
      this.surveyAndCalendarPermissionsList = _.orderBy(this.surveyAndCalendarPermissionsList, ["updatedAt"], ["desc"]);
    },

    setSurveyAndCalendarPermissions(value: any) {
      this.surveyAndCalendarPermissionsList = value;
    },

    setIsFetchingSurveyAndCalendarPermissions(value: any) {
      this.isFetchingSurveyAndCalendarPermissions = value;
    },

    setIsFetchingCategoriesTree(value: any) {
      this.isFetchingCategoriesTree = value;
    },

    setSelectedCategoryNames(selectedCategoryNamesObj: any) {
      this.selectedLargeCategoryName = selectedCategoryNamesObj.selectedLargeCategoryName;
      this.selectedMediumCategoryName = selectedCategoryNamesObj.selectedMediumCategoryName;
      this.selectedSmallCategoryName = selectedCategoryNamesObj.selectedSmallCategoryName 
        ? selectedCategoryNamesObj.selectedSmallCategoryName 
        : "";
    },

    setServiceList(value: any) {
      this.serviceList = value;
    },

    addProductListMapByService(value: any) {
      this.productListMapByService[value.serviceId] = value.productList;
    },

    setReservationPaymentItemsInfo(value: any) {
      this.reservationPaymentItemsInfo = value;
    },

    //actions
    async actionExportCalendarCsv(payload: any) {
      try {
        this.setFetchingExportCalendarCsv(true);
        this.setExportCalendarCsvError(null);
        let result = await DownloadCalendarCsv(payload);
        this.setExportCalendarCsvUrl(result.data.url);
      } catch (error: any) {
        if (error.response && error.response.data?.code?.toString() === '400') {
          this.setExportCalendarCsvError(error.response.data?.message);
        } else if (error.response && error.response.data?.code?.toString() === '403') {
          this.setExportCalendarCsvError("この操作を行う場合は、権限を管理者にお問い合わせください");
        } else {
          this.setExportCalendarCsvError("システムエラーが発生しました。システム管理者にお問い合わせください。");
        }
      } finally {
        this.setFetchingExportCalendarCsv(false);
      }
    },
  
    async actionSetCalendarDataOfCategoriesTree () {
      try {
        let result = await GetCategoriesTrees();
        this.setCalendarDataOfCategoriesTree(result);
  
        if (result.tree) {
          this.setCalendarDataOfCategoriesLarge(result.tree);
        }
      } catch (error: any) {
        this.setCalendarDataOfCategoriesTreeError(error);
      }
    },
  
    async getAllCalendars () {
      try {
        this.setFetchingAllCalendars(true);
        this.setFetchingAllCalendarsError(null);
        let result = await GetAllCalendars();
  
        this.setAllCalendarsList(result);
      } catch (error: any) {
        this.setFetchingAllCalendarsError(error);
      } finally {
        this.setFetchingAllCalendars(false);
      }
    },
  
    async actionGetCalendarCommaCsv(value: any) {
      try {
        this.setFetchingCalendarCommaCsv(true);
        this.setFetchingCalendarCommaError(null);
  
        let result = await CreateCalendarCommaCSV(value);
        if (result.data) {
          this.setCalendarCommaCsvUrl(result.data.url);
        } else {
          this.setFetchingCalendarCommaError("カレンダーのコマ設定を取得できない。ページの再読み込み実施を実施してください。");
        }
      } catch (error: any) {
        this.setFetchingCalendarCommaError("カレンダーのコマ設定を取得できない。ページの再読み込み実施を実施してください。");
      } finally {
        this.setFetchingCalendarCommaCsv(false);
      }
    },
  
    async actionImportCalendarCommaCsv(payload: any) {
      try {
        this.setImportingCalendarCommaCsv(true);
        this.setImportingCalendarCommaError(null);
  
        let response = await ImportComaCSV(payload.calendarId, payload.comas);
        if (response && response.result == "ERROR") {
          this.setImportingCalendarCommaError(response.error_message);
        }
        return response;
      } catch (error: any) {
        this.setImportingCalendarCommaError(error);
      } finally {
        this.setImportingCalendarCommaCsv(false);
      }
    },
  
    async actionResetCalendarDataOfCategoriesTree () {
      this.setCalendarDataOfCategoriesTree({});
      this.setCalendarDataOfCategoriesLarge([]);
      this.setCalendarDataOfCategoriesMedium, [];
      this.setCalendarDataOfCategoriesSmall, [];
      this.resetCalendarSelectedLargeCategory();
      this.resetCalendarSelectedMediumCategory();
      this.resetCalendarSelectedSmallCategory();
      this.setCalendarDisplayContentMode(SHOW_CONTENT_MODE.SHOW_SELECT_CONDITIONS);
    },
  
    async actionSetCalendarDataOfCategoriesTreeError(value: any) {
      this.setCalendarDataOfCategoriesTreeError(value);
    },
  
    async actionSetCalendarDataOfCategoriesLarge(value: any) {
      this.setCalendarDataOfCategoriesLarge(value);
    },
  
    async actionSetCalendarDataOfCategoriesMedium(value: any) {
      let selectedLargeMediumName = value && value.name ? value.name : null;
      if (!selectedLargeMediumName) {
        return;
      }
  
      let categoriesMedium = findCategoriesTreeMedium(this, selectedLargeMediumName);
      if(categoriesMedium) {
        this.setCalendarDataOfCategoriesMedium(categoriesMedium);
      }
    },
  
    async actionSetCalendarDataOfCategoriesSmall(value: any) {
      let selectedLargeCategoryName =
        this.selectedLargeCategory && this.selectedLargeCategory.name ? this.selectedLargeCategory.name : null;
      if (!selectedLargeCategoryName) {
        return;
      }
  
      let selectedLargeMediumName = value && value.name ? value.name : null;
      if (!selectedLargeMediumName) {
        return;
      }
  
      let categoriesSmall = findCategoriesTreeSmall(this, selectedLargeCategoryName, selectedLargeMediumName);
      if(categoriesSmall) {
        this.setCalendarDataOfCategoriesSmall(categoriesSmall);
      }
    },
  
    async actionResetCalendarDataOfCategoriesSmall () {
      this.resetCalendarDataOfCategoriesSmall();
    },
  
    async actionResetCalendarSelectedLargeCategory () {
      this.resetCalendarSelectedLargeCategory();
    },

    async actionResetCalendarSelectedMediumCategory () {
      this.resetCalendarSelectedMediumCategory();
    },

    async actionResetCalendarSelectedSmallCategory () {
      this.resetCalendarSelectedSmallCategory();
    },
  
    async actionSetCalendarSearchSelectedLargeCategory(value: any) {
      this.setCalendarSearchSelectedLargeCategory(value);
    },
  
    async actionSetCalendarSearchSelectedMediumCategory(value: any) {
      this.setCalendarSearchSelectedMediumCategory(value);
    },
  
    async actionSetCalendarSearchSelectedSmallCategory(value: any) {
      this.setCalendarSearchSelectedSmallCategory(value);
    },
  
    async actionSetCalendarDisplayContentMode(value: any) {
      this.setCalendarDisplayContentMode(value);
    },
  
    async actionSetDisplayCategoryId(value: any) {
      this.setCalendarDisplayCategoryId(value);
    },
  
    async actionSetCalendarIsReloadSchedulesResult(value: any) {
      this.setCalendarIsReloadSchedulesResult(value);
    },
  
    async actionSetCalendarReloadSchedulesStartDate(value: any) {
      this.setCalendarReloadSchedulesStartDate(value);
    },
  
    async actionResetCalendarReloadSchedules () {
      this.setCalendarIsReloadSchedulesResult(false);
      this.setCalendarReloadSchedulesStartDate(null);
    },
  
    async actionSetCalendarSelectedStartDate(value: any) {
      this.setCalendarSelectedStartDate(value);
    },
  
    async actionSetCalendarSelectedEndDate(value: any) {
      this.setCalendarSelectedEndDate(value);
    },
  
    async actionSetCalendarSchedulesImportCsv(payload: any) {
      try {
        this.setCalendarImportingSchedules(true);
        let result = await ImportCsvCalendarSchedules(payload.category, payload.schedule, payload.coma);
        return result;
      } catch (err: any) {
        throw err;
      } finally {
        this.setCalendarImportingSchedules(false);
      }
    },
  
    async actionSetCalendarIdToSelectedCategory(response: any) {
      if (!response.responseCalendar) {
        return;
      }
      let calendarId = response.responseCalendar.id;
      if (calendarId === null || calendarId === undefined) {
        return;
      }
  
      let selectedLargeCategory = this.selectedLargeCategory;
      let selectedMediumCategory = this.selectedMediumCategory;
      let selectedSmallCategory = this.selectedSmallCategory;
      if (!selectedLargeCategory || !selectedMediumCategory) {
        return;
      }
  
      if(selectedSmallCategory) {
        let smallCategory = cloneDeep(selectedSmallCategory);
        Object.assign(smallCategory, { calendarId: calendarId });
        this.setCalendarSearchSelectedSmallCategory(smallCategory);
      } else if (selectedMediumCategory.id) {
        let mediumCategory = cloneDeep(selectedMediumCategory);
        Object.assign(mediumCategory, { calendarId: calendarId });
        this.setCalendarSearchSelectedMediumCategory(mediumCategory);
      }
    },
  
    async actionSetCalendarSchedulesResults(payload: any) {
      try {
        this.setIsFetchingScheduleData(true);
        this.setFetchScheduleError(null);
        const response = await GetCalendarSchedules(payload.start_day, payload.end_day, payload.id);
        this.setCalendarSchedulesResults(processSchedulesResults(this, response));
        this.setCalendarMatchingData();
      } catch (error: any) {
        this.setFetchScheduleError(error);
      } finally {
        this.setIsFetchingScheduleData(false);
      }
    },
  
    async actionSetUpdatedCalendarSchedules(payload: any) {
      try {
        this.setIsFetchingScheduleData(true);
        this.setFetchScheduleError(null);
        const response = await UpdatedCalendarSchedules(payload.bodyInfo, payload.id);
        this.setUpdatedCalendarSchedules(processUpdatedSchedules(response, "update"));
      } catch (error: any) {
        this.setUpdatedCalendarSchedules(processUpdatedSchedules(error, "error"));
        this.setFetchScheduleError(error);
      } finally {
        this.setIsFetchingScheduleData(false);
      }
    },
  
    async actionSetCalendarSelectedTitle(payload: any) {
      try {
        this.setIsFetchingScheduleData(true);
        this.setFetchScheduleError(null);
        this.setCalendarSelectedTitle(processCalendarTitle(payload));
      } catch (error: any) {
        this.setFetchScheduleError(error);
      } finally {
        this.setIsFetchingScheduleData(false);
      }
    },
  
    async actionSetCalendarTargetDate(payload: any) {
      try {
        this.setIsFetchingScheduleData(true);
        this.setFetchScheduleError(null);
        this.setCalendarTargetDate(payload);
      } catch (error: any) {
        this.setFetchScheduleError(error);
      } finally {
        this.setIsFetchingScheduleData(false);
      }
    },
  
    async actionSetCalendarEditMode(value: any) {
      this.setCalendarEditMode(value);
    },
  
    async actionDeleteCalendar(payload: any) {
      try {
        this.setIsFetchingScheduleData(true);
        this.setFetchScheduleError(null);
        const response = await DeleteCalendar(payload.id);
        this.setDeletedCalendar(processUpdatedSchedules(response, "update"));
        // 選択中の分類のcalendarIdを削除
        const { selectedMediumCategory, selectedSmallCategory } = this;
        if(selectedSmallCategory) {
          const _selectedSmallCategory: any = cloneDeep(selectedSmallCategory);
          delete _selectedSmallCategory.calendarId;
          this.setCalendarSearchSelectedSmallCategory(_selectedSmallCategory);
        } else if (selectedMediumCategory.calendarId){
          const _selectedMediumCategory = cloneDeep(selectedMediumCategory);
          delete _selectedMediumCategory.calendarId;
          this.setCalendarSearchSelectedMediumCategory(_selectedMediumCategory);
        }
      } catch (error: any) {
        if (String(error).includes("code 403")) {
          this.setDeletedCalendar(processUpdatedSchedules(error, "error"));
        } else {
          this.setFetchScheduleError(error);
        }
      } finally {
        this.setIsFetchingScheduleData(false);
      }
    },
  
    async updateCalendarSettings(payload: any) {
      try {
        this.setIsUpdatingCalendar(true);
        this.setIsUpdatingCalendarError(null);
  
        payload.calendarMessage && await uploadMediaAndFilterMessages(payload);
        const response = await UpdateCalendarCategory(payload);
        this.setCurrentCalendar(payload);
  
        return response;
      } catch (error: any) {
        this.setIsUpdatingCalendarError(error);
      } finally {
        this.setIsUpdatingCalendar(false);
      }
    },
  
    async actionUpdateDayOff(payload: any) {
      try {
        this.setIsUpdatingCalendar(true);
        this.setIsUpdatingCalendarError(null);
        const response = await UpdateDayOff(payload);
        return response;
      } catch (error: any) {
        this.setIsUpdatingCalendarError(error);
        throw error;
      } finally {
        this.setIsUpdatingCalendar(false);
      }
    },

    async actionGetReservationControlInfo(calenarId: any) {
      try {
        this.setReservationControlInfoWork(true);
        this.setReservationControlInfoError(null);
        let result = await getReservationControlInfo(calenarId);
        let maxId = 1;
        for (let i = 0; i < result.length; i++) {
          if (maxId < Number(result[i].workId)) {
            maxId = Number(result[i].workId);
          }
        }
        let data = result.filter(function(res: any) {
          res.delete = false;
          return res.deletedAt === null;
        });
        data.sort((a: any, b: any) => {
          if (a.execDate + " " + a.execTime > b.execDate + " " + b.execTime) return -1;
          if (a.execDate + " " + a.execTime < b.execDate + " " + b.execTime) return 1;
          return 0;
        });
        const setData = {
          data: data,
          id: maxId,
        };
        this.setReservationControlInfo(setData);
      } catch (error: any) {
        this.setReservationControlInfoError(error);
      } finally {
        this.setReservationControlInfoWork(false);
      }
    },
  
    async actionUpsertReservationControlInfo(params: any) {
      try {
        this.setUpsertReservationControlInfoWork(true);
        this.setUpsertReservationControlInfoError(null);
        const response = await upsertReservationControlInfo(params);
        return response;
      } catch (error: any) {
        this.setUpsertReservationControlInfoError(error);
      } finally {
        this.setUpsertReservationControlInfoWork(false);
      }
    },
  
    async actionGetReservationItemInfo (calenarId: string) {
      try {
        this.setReservationItemInfoWork(true);
        this.setReservationItemInfoError(null);
        let result = await getReservationItemInfo(calenarId);
        let maxId = 1;
        for (let i = 0; i < result.length; i++) {
          if (maxId < Number(result[i].itemId)) {
            maxId = Number(result[i].itemId);
          }
        }
        let data = result.filter(function(res: any) {
          res.delete = false;
          return res.deletedAt === null;
        });
        const setData = {
          data: data,
          id: maxId,
        };
        this.setReservationItemInfo(setData);
      } catch (error: any) {
        this.setReservationItemInfoError(error);
      } finally {
        this.setReservationItemInfoWork(false);
      }
    },
  
    async actionUpsertReservationItemInfo(params: any) {
      try {
        this.setUpsertReservationItemInfoWork(true);
        this.setUpsertReservationItemInfoError(null);
        const response = await upsertReservationItemInfo(params);
        return response;
      } catch (error: any) {
        this.setUpsertReservationItemInfoError(error);
      } finally {
        this.setUpsertReservationItemInfoWork(false);
      }
    },
  
    async actionGetReservationPaymentItemsInfo (categoryId: string) {
      try {
        this.setReservationItemInfoWork(true);
        this.setReservationItemInfoError(null);
        const response = await GetReservationPaymentItemsInfo({
          categoryId,
          isIncludeDeleted: false,
        });
        if (response.code !== 'success') {
          throw response.errorMessage || '商品予約項目の取得に失敗しました。';
        }
        this.setReservationPaymentItemsInfo({
          data: response.data,
          maxSerialNumber: response.maxSerialNumber || 0
        });
      } catch (error: any) {
        this.setReservationItemInfoError(error);
      } finally {
        this.setReservationItemInfoWork(false);
      }
    },
  
    async actionUpsertCalenderPaymentItems(payload: any) {
      try {
        this.setUpsertReservationItemInfoWork(true);
        this.setUpsertReservationItemInfoError(null);
        const response = await upsertCalenderPaymentItems(payload);
        if (response.code !== 'success') {
          throw response.errorMessage;
        }
      } catch (error: any) {
        this.setUpsertReservationItemInfoError(error);
      } finally {
        this.setUpsertReservationItemInfoWork(false);
      }
    },
  
    async actionSetCalendarOff(payload: any) {
      try {
        this.setIsUpdatingCalendar(true);
        this.setIsUpdatingCalendarError(null);
        const response = await UpdateCalendarOff(payload);
        return response;
      } catch (error: any) {
        this.setIsUpdatingCalendarError(error);
        throw error;
      } finally {
        this.setIsUpdatingCalendar(false);
      }
    },
  
    async actionSetCalendarInfo(categoryId: any) {
      try {
        this.setIsFetchingCalendarSettingsInfo(true);
        const singleCategory = await GetSingleCategory(categoryId);
        const allCalendars: any = await GetAllCalendars();
        const calendarId = singleCategory.calendarId;
        const currentCalendar = allCalendars.find((calendar: any) => {
          return calendar.sortKey === calendarId;
        });
        this.setSingleCategory(singleCategory);
        this.setCurrentCalendar(currentCalendar);
        // return response;
      } catch (error: any) {
        console.error('error');
        throw error;
      } finally {
        this.setIsFetchingCalendarSettingsInfo(false);
      }
    },
  
    async actionSetCalendarDataOfCategoriesCriteriaTree () {
      try {
        this.setIsFetchingCategoriesTree(true);
        this.setCalendarDataOfCategoriesCriteriaTreeError(null);
        let result = await GetCategoriesTrees();
        this.setCalendarDataOfCategoriesCriteriaTree(result);
        if (result.tree) {
          this.setCalendarDataOfCategoriesCriteriaLarge(result.tree);
        }
      } catch (error: any) {
        this.setCalendarDataOfCategoriesCriteriaTreeError(error);
      } finally {
        this.setIsFetchingCategoriesTree(false);
      }
    },
  
    async actionResetCategoriesCriteria () {
      this.setCalendarDataOfCategoriesCriteriaMedium([]);
      this.setCalendarDataOfCategoriesCriteriaSmall([]);
      this.setCategoriesLargeCriteria(null);
      this.setCategoriesMediumCriteria(null);
      this.setCategoriesSmallCriteria(null);
    },
  
    async actionSetCalendarDataOfCategoriesMediumCriteria(value: any) {
      let selectedLargeMediumName = value && value.name ? value.name : null;
      if (!selectedLargeMediumName) {
        return;
      }
      let categoriesMedium = findCategoriesCriteriaTreeMedium(this, selectedLargeMediumName);
      if(categoriesMedium) {
        this.setCalendarDataOfCategoriesCriteriaMedium(categoriesMedium);
      }
    },
  
    async actionSetCalendarDataOfCategoriesSmallCriteria(value: any) {
      let selectedLargeCategoryName = this.selectedLargeCategoryCriteria && this.selectedLargeCategoryCriteria.name ?
        this.selectedLargeCategoryCriteria.name : null;
      if (!selectedLargeCategoryName) {
        return;
      }
  
      let selectedLargeMediumName = value && value.name ? value.name : null;
      if (!selectedLargeMediumName) {
        return;
      }
  
      let categoriesSmall = findCategoriesCriteriaTreeSmall(this, selectedLargeCategoryName, selectedLargeMediumName);
      if(categoriesSmall) {
        this.setCalendarDataOfCategoriesCriteriaSmall(categoriesSmall);
      }
    },
  
    async fetchSurveyAndCalendarPermissions(download: any) {
      try {
        this.setSurveyAndCalendarPermissions([]); // 初期化
        let response: any = {};
        let lastEvaluatedKey:any = null;
  
        do {
          response = await GetSurveyList({
            download,
            lastEvaluatedKey,
          });
          if(response) {
            lastEvaluatedKey = response.lastEvaluatedKey;
            this.addSurveyAndCalendarPermissions(response);
          }
        } while (response.lastEvaluatedKey);
  
      } catch (error: any) {
        console.error(error);
      }
    },
  
    async actionUpdateCategoriesOrder (categoriesTree: any) {
      const response = await UpdateCategoriesOrder(categoriesTree);
      if (response.result !== 'OK') {
        throw response;
      }
    },
  
    async actionSetSelectedCategoryNames(selectedCategoryNamesObj: any) {
      this.setSelectedCategoryNames(selectedCategoryNamesObj);
    },
  
    async actionGetServiceList () {
      try {
        const payload = { fields: 'sortKey,serviceId,serviceName,reservationServiceType' } as FetchPaymentServiceListPayload;
        const serviceList = await repeatQueryFunction(FetchPaymentServiceList, payload);
        this.setServiceList(serviceList);
      } catch (e: any) {
        this.setServiceList([]);
        console.error(new Error(e));
      }
    },

    async actionGetPorductList (serviceId: string) {
      try {
        const payload = {
          status: PRODUCT_STATUS_TYPES.SALE, // 販売中のみ取得
          serviceId,
        } as any;
        const productList = await repeatQueryFunction(FetchProductList, payload);
        this.addProductListMapByService({ serviceId, productList });
      } catch (error: any) {
        console.error(new Error(error));
        this.addProductListMapByService({ serviceId, productList: [] });
      }
    },
  }
});

const setData = (store: any) => {
  let data = <any>[];
  let cntDtWithNonDataInWeek = countDateWithNonDataInWeek(store);
  let isHasTwoComa = checkIsHasTwoComa(store);
  let scheduleOfComa1:any = null;
  let scheduleOfComa2:any = null;
  let countScheduleOfComa1 = 0;
  let countScheduleOfComa2 = 0;
  if (store.schedulesResult.length > 0) {
    scheduleOfComa1 = store.schedulesResult[0];
    countScheduleOfComa1 = Object.keys(store.schedulesResult[0]).length;
  }
  if (store.schedulesResult.length > 1) {
    scheduleOfComa2 = store.schedulesResult[1];
    countScheduleOfComa2 = Object.keys(store.schedulesResult[1]).length;
  }

  for (var i = 0; i < store.timezones.length; i++) {
    var dataInput = store.schedulesResult[i];
    var resInput = store.reservationsResult[i];
    let items = <any>[];
    let rowSpan = store.timezones[i].length;
    let wkId = 1;
    let nonData = {
      colSpan: cntDtWithNonDataInWeek,
      rowSpan: rowSpan,
    };
    let changeComa = {
      colSpan: 0,
      rowSpan: rowSpan,
    };

    store.timezones[i].forEach(function (timezone: any, index: any) {
      let hasSetNoneData = false;
      let hasSetShowChangeComa = false;
      let hasPushNoneData = false;
      let hasPushShowChangeComa = false;
      let wkData = <any>[];
      store.targetDates.forEach(function (targetDate: any) {
        let displayMode = CALENDAR_DISPLAY_MODE.HAS_DATA;
        var _day = targetDate.fullDate;
        var _data = dataInput[_day];
        var _resData = resInput[_day];
        let wkVal = -1;
        let resVal = -1;
        if (_data && _resData) {
          wkVal = _data[Object.keys(timezone)[0]];
          resVal = _resData[Object.keys(timezone)[0]];
        }
        // active check
        let _checkVal = true;
        if (targetDate.bygone) {
          _checkVal = false;
        }
        if (wkVal >= 0) {
          wkData.push({
            displayMode: displayMode,
            id: wkId++,
            date: targetDate.date,
            value: wkVal,
            active: _checkVal,
            resValue: resVal,
            fullDate: targetDate.fullDate,
          });
        } else {
          // 日付のデータが無い場合
          // 最初のcomaのみ設定
          if (index === 0) {
            // comaが二つの場合
            if(isHasTwoComa) {
              // coma1の場合
              if (i === 0) {
                // coma2のスケジュールから検索する
                let findComa = scheduleOfComa2[_day];
                // coma2のスケジュールに存在する場合
                if(findComa) {
                  // 一回目のみ設定
                  if (!hasSetShowChangeComa) {
                    displayMode = CALENDAR_DISPLAY_MODE.SHOW_CHANGE_COMA;
                    changeComa.colSpan = 7 - countScheduleOfComa1 - cntDtWithNonDataInWeek;
                    hasSetShowChangeComa = true;
                  }
                } else {
                  // coma2のスケジュールに存在しない場合
                  // 一回目のみ設定
                  if (!hasSetNoneData) {
                    displayMode = CALENDAR_DISPLAY_MODE.NON_DATA;
                    hasSetNoneData = true;
                  }
                }
              } else {
                // coma2の場合
                // coma1のスケジュールから検索する
                let findComa = scheduleOfComa1[_day];
                // coma1のスケジュールに存在する場合
                if(findComa) {
                  // 一回目のみ設定
                  if (!hasSetShowChangeComa) {
                    displayMode = CALENDAR_DISPLAY_MODE.SHOW_CHANGE_COMA;
                    changeComa.colSpan = 7 - countScheduleOfComa2 - cntDtWithNonDataInWeek;
                    hasSetShowChangeComa = true;
                  }
                } else {
                  // coma2のスケジュールに存在しない場合
                  // 一回目のみ設定
                  if (!hasSetNoneData) {
                    displayMode = CALENDAR_DISPLAY_MODE.NON_DATA;
                    hasSetNoneData = true;
                  }
                }
              }
            } else {
              // comaが一つの場合
              // 一回目のみ設定
              if (!hasSetNoneData) {
                displayMode = CALENDAR_DISPLAY_MODE.NON_DATA;
                hasSetNoneData = true;
              }
            }

            if (hasSetNoneData && !hasPushNoneData) {
              wkData.push({
                id: wkId++,
                displayMode: displayMode,
                date: targetDate.date,
                fullDate: targetDate.fullDate,
                nonData: nonData,
                changeComa: {},
              });
              hasPushNoneData = true;
            }
            if (hasSetShowChangeComa && !hasPushShowChangeComa) {
              wkData.push({
                id: wkId++,
                displayMode: displayMode,
                date: targetDate.date,
                fullDate: targetDate.fullDate,
                nonData: {},
                changeComa: changeComa,
              });
              hasPushShowChangeComa = true;
            }
          }
        }
      });

      items.push({
        timezone: timezone,
        data: wkData,
      });
    });
    data.push(items);
  }
  store.datas = data;
};

const countDateWithNonDataInWeek = (store: any) => {
  let schedules = store.schedulesResult;
  if (schedules.length === 0) {
    return 0;
  } else if (schedules.length === 1) {
    return 7 - _.keys(schedules[0]).length;
  } else if (schedules.length === 2) {
    return 7 - _.keys(schedules[0]).length - _.keys(schedules[1]).length;
  }
  return 0;
};

const checkIsHasTwoComa = (store: any) => {
  return store.timezones.length >= 2;
};

const setTargetDates = (store: any, payload: any) => {
  const givenDate = payload.date ? dayjs(payload.date) : dayjs();

  const offsetDayToMonday = givenDate.day() == 0 ? 6 : givenDate.day() - 1;
  const mondayOfTheWeek = dayjs(givenDate).subtract(offsetDayToMonday, 'days');

  store.startDate = mondayOfTheWeek.toDate();

  let dates = <any>[];
  for (let i = 0; i < 7; i++) {
    const targetDate = mondayOfTheWeek.clone().add(i, 'days');

    dates.push({
      day: targetDate.date(),
      date: targetDate.format('M月D日'),
      year: targetDate.year(),
      displayDate: targetDate.format('M/D'),
      fullDate: targetDate.format('YYYYMMDD'),
      youbi: CALENDAR_JAPANESE_DATES[targetDate.day()],
      saturday: targetDate.day() === 6,
      sunday: targetDate.day() === 0,
      bygone: !isInReservationDuration(targetDate, store.currentCalendar)
    });
  }

  store.targetDates = dates;
  store.selectedStartDay = dates[0].fullDate;
  store.selectedEndDay = dates[6].fullDate;
};

const isInReservationDuration = (targetDate: Dayjs, currentCalendar: any) => {
  const hasReservationPossibleDurationSetting = currentCalendar.reservationControlType == '1';

  const reservationStartOffset = hasReservationPossibleDurationSetting && !isNaN(currentCalendar.reservationPossibleStart) ? currentCalendar.reservationPossibleStart : 0;
  const reservationStartDate = dayjs().add(reservationStartOffset, 'days').startOf('day');

  const reservationPossibleMonths = hasReservationPossibleDurationSetting && !isNaN(currentCalendar.reservationPossibleMonths) ? currentCalendar.reservationPossibleMonths : 99;
  // If the day of the month on the original date is greater than the number of days in the final month,
  // the day of the month will change to the last day in the final month.
  const reservationEndDate = dayjs().add(reservationPossibleMonths, 'months').endOf('month');

  const result = reservationStartDate.isBefore(targetDate) && targetDate.isBefore(reservationEndDate);
  return result;
};

const processSchedulesResults = (store: any, response: any) => {
  let comaList = response.comaList;
  let schedule = response.schedule;
  let timezones0 = [];
  let timezones1 = [];
  let dataInput0 = {};
  let dataInput1 = {};
  let resInput0 = {};
  let resInput1 = {};
  let comaChange = [];
  let comaGroup = {
    group0: [],
    group1: [],
  };
  let comaGrp = setComaGroup(store, schedule, comaList);
  comaChange = comaGrp.comaChange;
  comaGroup = comaGrp.comaGroup;

  // set timezones0, timezones1
  if (comaChange.length > 0) {
    timezones0 = setTimezones(comaChange[0], comaList);
  }
  if (comaChange.length > 1) {
    timezones1 = setTimezones(comaChange[1], comaList);
  }

  // 予約可能数・予約した数のデータの設定
  let slotGroup0 = setSlotData(schedule, comaGroup.group0, timezones0);
  dataInput0 = slotGroup0.slotBook;
  resInput0 = slotGroup0.slotRest;

  // 予約した数のデータの設定
  let slotGroup1 = setSlotData(schedule, comaGroup.group1, timezones1);
  dataInput1 = slotGroup1.slotBook;
  resInput1 = slotGroup1.slotRest;

  var obj = {
    tz: timezones1.length > 0 ? (timezones0.length > 0 ? [timezones0, timezones1] : [timezones1]) : [timezones0],
    data: timezones1.length > 0 ? (timezones0.length > 0 ? [dataInput0, dataInput1] : [dataInput1]) : [dataInput0],
    resData: timezones1.length > 0 ? (timezones0.length > 0 ? [resInput0, resInput1] : [resInput1]) : [resInput0],
    comaGroup: comaGroup,
    dayOff: response.dayOff,
  };
  return obj;
};

const processUpdatedSchedules = (response: any, key: any) => {
  var checkList = <any>[];
  if (key === "error") {
    if (String(response).includes("code 403")) {
      checkList.push(403);
    }
  } else {
    if (response.length > 0) {
      for (var i = 0; i < response.length; i++) {
        var _updated = response[i];
        if (_updated.code === "forbidden") {
          checkList.push(_updated.date.replace(/(\d{4})(\d{2})(\d{2})/g, "$1-$2-$3"));
        }
      }
    }
  }
  return checkList;
};

const processCalendarTitle = (payload: any) => {
  let title: any = {};
  title["large"] = payload.large.name;
  title["medium"] = payload.medium.name;
  title["small"] = payload.small ? payload.small.name : "";
  title["categoryId"] = payload.medium.id ? payload.medium.id : payload.small.id;
  return title;
};

// Memo (Mun): This function may be needed in the future
function findCategoriesTreeLarge(store: any, largeCategory: any) {
  if (store.categoriesTree && store.categoriesTree.tree) {
    let categoriesTreeLarge = find(store.categoriesTree.tree, { name: largeCategory });
    return categoriesTreeLarge ? categoriesTreeLarge : null;
  }
  return null;
}

function findCategoriesTreeMedium(store: any, largeCategory: any) {
  if (store.categoriesTree && store.categoriesTree.tree) {
    let categoriesTreeLarge = find(store.categoriesTree.tree, { name: largeCategory });
    if (categoriesTreeLarge && categoriesTreeLarge.children) {
      return categoriesTreeLarge.children;
    }
  }
  return null;
}

function findCategoriesTreeSmall(store: any, largeCategory: any, mediumCategory: any) {
  if (store.categoriesTree && store.categoriesTree.tree) {
    let categoriesTreeLarge = find(store.categoriesTree.tree, { name: largeCategory });
    if (categoriesTreeLarge && categoriesTreeLarge.children) {
      let categoriesTreeMedium = find(categoriesTreeLarge.children, { name: mediumCategory });
      if (categoriesTreeMedium && categoriesTreeMedium.children) {
        return categoriesTreeMedium.children;
      }
    }
  }
  return null;
}

function findCategoriesCriteriaTreeMedium(store: any, largeCategory: any) {
  if (store.categoriesCriteriaTree && store.categoriesCriteriaTree.tree) {
    let categoriesTreeLarge = find(store.categoriesCriteriaTree.tree, { 'name': largeCategory });
    if (categoriesTreeLarge && categoriesTreeLarge.children) {
      return categoriesTreeLarge.children;
    }
  }
  return null;
}

function findCategoriesCriteriaTreeSmall(store: any, largeCategory: any, mediumCategory: any) {
  if (store.categoriesCriteriaTree && store.categoriesCriteriaTree.tree) {
    let categoriesTreeLarge = find(store.categoriesCriteriaTree.tree, { 'name': largeCategory });
    if (categoriesTreeLarge && categoriesTreeLarge.children) {
      let categoriesTreeMedium = find(categoriesTreeLarge.children, { 'name': mediumCategory });
      if (categoriesTreeMedium && categoriesTreeMedium.children) {
        return categoriesTreeMedium.children;
      }
    }
  }
  return null;
}

/**
 * カレンダー表示上、同じコマ設定が設定された日をグルーピングする
 */
function setComaGroup(store: any, schedule: any, comaList: any) {
  let isLatterHalfOfWeekNonData = false;
  let isHasCheckedLatterHalfOfWeekNonData = false;
  let comaChange = <any>[];
  let comaGroup = {
    group0: <any>[],
    group1: <any>[],
  };
  store.targetDates.forEach((targetDate: any, i: any) => {
    const scheduleOfDay = schedule[targetDate.fullDate];
    if(scheduleOfDay) {
      // scheduleの中には、過去に設定して現在は削除済のコマIDに対するスケジュールが含まれる。
      // 削除済のコマIDの有無はカレンダー表示に無関係のため、現在有効なコマのみでグルーピング判定する
      const validComaIds = keys(comaList);
      const comaOfTheDay = keys(scheduleOfDay)
      .filter(comaId => validComaIds.includes(comaId))
      .sort((a, b) => Number(a) - Number(b));

      const sameComaIndexInComaChange = findIndex(comaChange, (o) => isEqual(o, comaOfTheDay));

      if (sameComaIndexInComaChange === -1) {
        if (comaChange.length === 0) {
          comaGroup.group0.push(targetDate.fullDate);
        } else {
          comaGroup.group1.push(targetDate.fullDate);
        }
        if (comaOfTheDay.length > 0) {
          comaChange.push(comaOfTheDay);
        }
      } else if (sameComaIndexInComaChange === 0) {
        comaGroup.group0.push(targetDate.fullDate);
      } else {
        comaGroup.group1.push(targetDate.fullDate);
      }
    } else {
      // 先頭にデータが無い
      if (comaChange.length === 0) {
        comaGroup.group0.push(targetDate.fullDate);
      } else {
        // 後半の週はデータが無いかチェック
        if (!isHasCheckedLatterHalfOfWeekNonData) {
          isLatterHalfOfWeekNonData = checkLatterHalfOfWeekNonData(schedule, store.targetDates, i);
          isHasCheckedLatterHalfOfWeekNonData = true;
        }
        if(isLatterHalfOfWeekNonData) {
          comaGroup.group1.push(targetDate.fullDate);
        }
      }
    }
  });
  return { comaChange: comaChange, comaGroup: comaGroup };
}

/** 後半の週はデータが無いかチェック */
function checkLatterHalfOfWeekNonData(schedule: any, targetDates: any, startIndex: any) {
  let count = 0;
  let nonDataCount = 0;
  for (let i = startIndex; i < targetDates.length; i++) {
    let day = targetDates[i].fullDate;
    let scheduleOfDay = schedule[day];
    if (!scheduleOfDay) {
      nonDataCount++;
    }
    count++;
  }
  return nonDataCount === count;
}

function castTime(value: any) {
  var tm = dayjs(value, "H:m", true);
  if (tm.isValid()) {
    return tm.format("HH:mm");
  }
  return value;
}

function castComaTime(coma: any) {
  if (coma.start.length === 0) {
    coma.start = "-";
  } else {
    coma.start = castTime(coma.start);
  }
  if (coma.end.length === 0) {
    coma.end = "-";
  } else {
    coma.end = castTime(coma.end);
  }
  return coma;
}

function setTimezones(comaArr: any, comaList: any) {
  let timezones = <any>[];
  for (let i = 0; i < comaArr.length; i++) {
    let cmId = comaArr[i];
    let cm = comaList[cmId];
    if(cm) {
      let coma = castComaTime(cm);
      timezones.push({ [cmId]: coma });
    }
  }
  return timezones;
}

function setSlotData(schedules: any, comaArr: any, timezonesArr: any) {
  let slotBook: any = {}; // 予約可能数
  let slotRest: any = {}; // 予約した数
  for (const [dateKey, slot] of Object.entries(schedules)) {
    let book: any = {};
    let rest: any = {};
    if (comaArr.includes(dateKey)) {
      for (const [index, comaItem] of Object.entries(timezonesArr)) {
        let comaIdArr = keys(comaItem);
        let comaId = comaIdArr.length > 0 ? comaIdArr[0] : -1;
        if (has(slot, comaId)) {
          book[comaId] = slot[comaId][0];
          rest[comaId] = slot[comaId][1];
        }
        slotBook[dateKey] = book;
        slotRest[dateKey] = rest;
      }
    }
  }
  return { slotBook: slotBook, slotRest: slotRest };
}

async function uploadMediaAndFilterMessages(payload: any) {
  for (const message of payload.calendarMessage) {
    if (message.type === "image" && message.file && message.file.type) {
      const generatedUUID = generateUUID();
      const uploaded = await UploadImageToS3(message.file, generatedUUID, "resources");
      const url = `https://${uploaded.cloudFrontDist}/resources/${generatedUUID}`;
      message.previewImageUrl = url;
      message.originalContentUrl = url;
      delete message.file;
      delete message.text;
    }
    if (message.type === "text") {
      delete message.originalContentUrl;
      delete message.previewImageUrl;
    }
    delete message.error;
  }
}