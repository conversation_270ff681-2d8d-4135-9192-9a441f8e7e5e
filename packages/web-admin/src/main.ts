import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'

import mitt from 'mitt'
import { AmplifyService } from './services/amplify.service'
import { createAbility } from '@/casl/ability'
import { abilitiesPlugin, Can } from '@casl/vue'
import router from './router/index';
import Snackbar from './plugins/snackbar'
import Dialog from './plugins/dialog'
import  * as AmplifyModules from 'aws-amplify'
import MultiSelectAreasImage from 'multi-select-areas-image'
import VueClipboard from 'vue-clipboard3';

import ContentLoading from '@/components/common/ContentLoading.vue'
import ContentNoResult from '@/components/common/ContentNoResult.vue'
import JsonCSV from 'vue-json-csv'
import PanZoom from 'panzoom'

import PermissionHelper from './mixins/PermissionHelper'

import store from './stores'
// import router from './router';

import { Quasar } from 'quasar';  // Quasarプラグイン
import quasarLang from 'quasar/lang/ja';  // Quasarの言語設定（日本語）
import quasarUserOptions from '.quasar/quasar-user-options';

const eventBus = mitt();

AmplifyService.configure();

const app = createApp(App)

const ability = createAbility();
app.use(abilitiesPlugin, ability);

app.use(router);

app.use(Snackbar)
app.provide('Snackbar', Snackbar)
app.use(Dialog)

app.use(AmplifyModules)
app.use(MultiSelectAreasImage);

app.use(VueClipboard, {
    autoSetContainer: true,
});

app.component('Can', Can);

app.component('content-loading', ContentLoading);
app.component('content-no-result', ContentNoResult);
app.component('download-csv', JsonCSV)
app.component('PanZoom', PanZoom)

app.mixin(PermissionHelper);

app.use(store);

// eventBusをグローバルプロパティとして設定
app.config.globalProperties.$eventBus = eventBus;

// Quasarをアプリに登録し、日本語設定を適用
app.use(Quasar, {
    lang: quasarLang,
});
app.use(Quasar,quasarUserOptions);

// Piniaをアプリに追加
const pinia = createPinia();
app.use(pinia);

app.mount('#app')
