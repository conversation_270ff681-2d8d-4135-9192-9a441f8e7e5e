<template>
  <div>
    <q-btn flat class="primary--text ml-3">
      <q-btn v-if="fixed" flat round class="text-primary bg-white tw-font-semibold" :size="'12px'">
        {{ userIcon }}
      </q-btn>
      <q-card v-else  max-width="320" flat ripple class="mx-auto" :style="styleCard">
        <q-item link class="tw-pl-0">
          <q-item-section class="text-left">
            <q-item-label class="tw-text-lg tw-normal-case tw-text-gray-900">{{ user.username }}</q-item-label>
            <q-item-label caption class="tw-normal-case">{{ user.email }}</q-item-label>
          </q-item-section>

          <q-item-section avatar>
            <q-avatar color="primary" size="40">
              <span class="tw-text-white text-uppercase">
                {{ userIcon }}
              </span>
            </q-avatar>
          </q-item-section>
        </q-item>
      </q-card>
      <q-menu>
        <q-list style="min-width: 100px" bordered separator>
          <div v-if="teamInfo.length > 0">
            <q-item v-close-popup>
              <q-item-section>
                <q-item-label class="tw-mb-1">チーム</q-item-label>
                <q-chip v-for="item in teamInfo" :key="item" outline clickable color="primary">
                  {{ getConvertedName(item) }}
                </q-chip>
              </q-item-section>
            </q-item>
          </div>

          <div v-if="accessLevelInfo.length > 0">
            <q-item clickable v-close-popup>
              <q-item-section>
                <q-item-label>権限</q-item-label>
                <q-chip v-for="item in accessLevelInfo" :key="item" class="my-1" color="primary" dark outlined>
                  {{ getConvertedName(item) }}
                </q-chip>
              </q-item-section>
            </q-item>
          </div>

          <q-item :to="moveToChangePassword()">
            <q-item-section>
              <q-item-label>パスワード変更</q-item-label>
            </q-item-section>
            <q-item-section avatar>
              <q-icon name="mdi-lock-reset"></q-icon>
            </q-item-section>
          </q-item>

          <!-- Goes unused for the time being / こちらは未使用 -->
<!--      <q-item href="https://feedback.line.me/enquete/public/14845-PgxSjsnU" target="_blank">
            <q-item-section>
              <q-item-label>ご意見・ご要望はこちら</q-item-label>
            </q-item-section>
            <q-item-section avatar>
              <q-icon name="mdi-comment-text-outline"></q-icon>
            </q-item-section>
          </q-item> -->

          <q-item href="https://playnext-lab.notion.site/e13c29949b854d2aa01369381248a72e" target="_blank">
            <q-item-section>
              <q-item-label>マニュアル</q-item-label>
            </q-item-section>
            <q-item-section avatar>
              <q-icon name="mdi-file-document-outline"></q-icon>
            </q-item-section>
          </q-item>

          <q-separator></q-separator>
          <div class="tw-flex tw-justify-between">
            <q-btn class="full-width tw-py-3" align="left" flat @click="handleSignOut()">
              サインアウト
            </q-btn>
            <q-avatar class="tw-mr-1" icon="mdi-shield-lock-outline" />
          </div>
          
        </q-list>
      </q-menu>
    </q-btn>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { useQuasar } from "quasar";

import { convertGroupNames } from "@/utils/stringUtils";

import { ACCESS_LEVEL } from "@/stores/modules/users/users.constants";
import { useAuthStore } from "@/stores/modules/auth.module";
import { useSettingsStore } from "@/stores/modules/settings";
import { useUsersStore } from "@/stores/modules/users";

defineOptions({
  name: "HeaderComponent",
});

const props = defineProps<{
  fixed: boolean,
}>();

// quasar
const $q = useQuasar();

// router
const router = useRouter();

// store
const authStore = useAuthStore();
const settingsStore = useSettingsStore();
const usersStore = useUsersStore();

// data
const menu = ref(false);

// computed
// TODO 取得方法がわからない
// ...mapGetters(["ownTeamList", "ownAccessList"]),

const user = computed(() => authStore.user);
const isSigningOut = computed(() => authStore.isSigningOut);
const permissionsFromTeam = computed(() => authStore.permissionsFromTeam);
const commonSettings = computed(() => settingsStore.commonSettings);
const userIcon = computed(() => user.value?.username?.substr(0, 2) ?? '');

const isMenuVertical = computed((): boolean => {
  return commonSettings.value.menuStyle === "vertical";
});

const styleCard = computed((): string => {
  return isMenuVertical.value
    ? "background: transparent;"
    : `border-bottom: 4px solid ${commonSettings.value.primaryColor}; background: transparent;`;
});

const teamInfo = computed((): any => {
  const accessLevel = ACCESS_LEVEL;
  if (permissionsFromTeam.value.groups) {
    return permissionsFromTeam.value.groups.filter((obj: string) => {
      return !accessLevel.some((item: any) => {
        return obj === item.value;
      });
    });
  }
  return [];
});

const accessLevelInfo = computed((): any => {
  const accessLevel = ACCESS_LEVEL;
  if (permissionsFromTeam.value.groups) {
    return permissionsFromTeam.value.groups.filter((obj: string) => {
      return accessLevel.some((item: any) => {
        return obj === item.value;
      });
    });
  }
  return [];
});

// mounted
onMounted(() => {
  
});

const handleSignOut = (): void => {
  $q.dialog({
    title: "サインアウト確認",
    message: "サインアウトしてもよろしいですか。",
    cancel: true,
    ok: {
      label: "サインアウト",
      color: 'negative',
    }
  }).onOk(async() => {
    await authStore.AUTH_SIGNOUT();
    // TODO 戻るなら-1
    router.push({name: 'Signin'});
  });
};

const moveToChangePassword = (): any => {
  return {
    name: "UserDetail",
    params: {
      userName: user.value.username,
    },
  };
};

const getConvertedName = (value: string): string => {
  if (value === "アドミニストレータ:admins") {
    return "アドミニストレータ";
  }
  const [teamName, accessName] = value.split(":");
  if (teamName && accessName) {
    return teamName + " - " + convertGroupNames(accessName);
  } else {
    return convertGroupNames(teamName) ?? '';
  }
};
</script>