<template>
  <div>
    <q-footer class="text-grey-7" style="background-color:transparent; z-index: 0; position: relative;">
      <div v-if="publicConfiguration.versions" class="text-center tw-py-4">
        {{ "© PlayNext Lab Corp. 2024 | Version " + publicConfiguration.versions.general }}
      </div>
    </q-footer>
  </div>
</template>

<script setup lang="ts">
import { useSettingsStore } from "@/stores/modules/settings";
import { computed } from "vue";

// store
const settingsStore = useSettingsStore();

defineOptions({
  name: "FooterComponent",
});

const commonSettings = computed(() => settingsStore.commonSettings);
const publicConfiguration = computed(() => settingsStore.publicConfiguration);
const isMenuVertical = (): boolean => {
  return commonSettings.value.menuStyle === "vertical";
};

</script>