<template>
  <div>
    <div class="image-decorator" ref="image-area">
      <div @mousemove="doMove(null, $event)">
        <img
          class="original-image tw-max-w-full"
          :src="localState?.url"
          alt="Original Image"
          v-if="localState?.url"
          :width="width"
        />
        <div
          class="select-areas--overlay"
          :style="{
            opacity: opacityOverlay,
            position: 'absolute',
            width: localState?.originImgSize.w + 'px',
            height: localState?.originImgSize.h + 'px',
            display: 'block',
          }"
        ></div>

        <div
          :style="{
            'background-color': 'rgb(0, 0, 0)',
            opacity: 0,
            position: 'absolute',
            width: localState?.originImgSize.w + 'px',
            height: localState?.originImgSize.h + 'px',
            cursor: 'crosshair',
          }"
          @mousedown="mouseDown"
          @mousemove="mouseMove"
        ></div>

        <div v-for="item in localState?.areas" :key="item.id" @click="selectAreaClicked">
          <div
            @mousedown.stop.prevent="startMove(item, $event)"
            @mousemove="doMove(item, $event)"
          >
            <div
              class="select-areas--outline"
              :style="{
                opacity: 1,
                position: 'absolute',
                cursor: 'default',
                'border-width': '2px',
                'border-style': 'solid',
                'border-color': item.selected ? '#00b900' : 'gray',
                width: item.width + 2 + 'px',
                height: item.height + 2 + 'px',
                left: item.x + localState?.posImg.left - 2 + 'px',
                top: item.y + localState?.posImg.top - 2 + 'px',
                'z-index': item.z,
              }"
            ></div>
            <div
              class="select-areas--background_area"
              :style="{
                opacity: 1,
                position: 'absolute',
                cursor: 'move',
                width: item.width + 'px',
                height: item.height + 'px',
                left: item.x + localState?.posImg.left + 'px',
                top: item.y + localState?.posImg.top + 'px',
                'z-index': item.z + 2,
              }"
              @click="changeResizable(item.id)"
            ></div>
            <!-- resize handler -->
            <Resizable
              :item="item"
              :posImg="localState?.posImg"
              @startDrag="startDrag"
              @doDrag="doDrag"
            />
          </div>
        </div>
      </div>
      <div class="c-crop--hide_main" :style="{ 'user-select': 'none' }">
        <img id="c-crop--hide_img" :src="localState?.url" alt="" :width="width" />
      </div>
    </div>
    <!-- {{ localState?.areas.find((obj) => obj.selected) }} -->
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, onUnmounted, onUpdated } from 'vue'
import Resizable from './Resizable.vue'
import cloneDeep from 'lodash/cloneDeep'
import isFunction from 'lodash/isFunction'

interface LocalState {
  mousedown: boolean;
  originImgSize: any;
  url: any;
  posImg: any;
  areas: Array<any>;
  moveTempX: number;
  moveTempY: number;
  moveCurrentX: number;
  moveCurrentY: number;
  temp: number;
  tempStartX: any;
  tempStartY: any;
  tempRightBound: any;
  tempBottomBound: any;
  dragdown: boolean;
  move: boolean;
}

// local state
const localState = ref<LocalState>({
  mousedown: false, // state mouse down event
  originImgSize: {
    // root size image
    w: 0,
    h: 0,
    r: 0,
  },
  url: null,
  posImg: {
    // position box image in screen
    top: -100000,
    left: -100000,
  },
  areas: [],
  moveTempX: 0,
  moveTempY: 0,
  moveCurrentX: 0,
  moveCurrentY: 0,
  temp: 0,
  tempStartX: null,
  tempStartY: null,
  tempRightBound: null,
  tempBottomBound: null,
  dragdown: false, // state mouse event drag,
  move: false,
})

const props = defineProps({
  cropUrl: {
    type: String,
    default: null,
  },
  width: {
    type: Number,
    default: 1500,
  },
  height: {
    type: Number,
    default: 250,
  },
  opacityOutline: {
    type: Number,
    default: 0.5,
  },
  opacityOverlay: {
    type: Number,
    default: 0.5,
  },
  displayAreas: Array,
  emitAreaEvent: {
    type: Boolean,
    default: true,
  },
  ignoreWatchArea: {
    type: Boolean,
    default: false,
  },
  scrollTop: {
    type: Number,
    default: 0,
  },
  scrollLeft: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['getListAreas', 'selectAreaClicked'])

async function setSize(): Promise<void> {
  if (!localState.value.url) {
    return
  }
  const imgSize = await getSize(localState.value.url)
  localState.value.originImgSize = imgSize
}

//     // Get the size of the src image
function getSize(src: any): any {
  const img = document.querySelector('#c-crop--hide_img') as any
  return new Promise((resolve) => {
    if (src && img) {
      img.onload = function (event) {
        const size = getSizeImg(img)
        resolve(size)
      }
      img.src = src
    } else {
      resolve({
        w: 0,
        h: 0,
        r: 0,
      })
    }
  })
}

function getSizeImg(img: any): any {
  // console.log('🚀 ~ getSizeImg ~ img:', img)
  const w = img.width
  const h = img.height
  const r = w === 0 && h === 0 ? 0 : w / h
  return {
    w,
    h,
    r,
  }
}

function calcPosOfBox(): void {
  // set posImg static
  const ref = document.querySelector('.image-decorator') as any
  if (ref && !localState.value.dragdown) {
    localState.value.posImg.top =
      ref.getBoundingClientRect().top === 0
        ? -100000
        : ref.getBoundingClientRect().top + props.scrollTop - 54
    localState.value.posImg.left =
      ref.getBoundingClientRect().left === 0
        ? -100000
        : ref.getBoundingClientRect().left + props.scrollLeft - 15
  }
}

// draw rectangle on image mouseDown mouseMove mouseUp
function mouseDown(e: any) {
  const path = isFunction(e.composedPath) ? e.composedPath() : e.path
  if (
    path &&
    path.find(
      (path) => path.classList && path.classList.contains('rich-menu-properties-col')
    )
  ) {
    // NOTE: we should ignore event
    return true
  }

  if (localState.value.areas.length < 20) {
    localState.value.mousedown = true

    localState.value.areas
      .filter((item) => item.selected)
      .map((item) => {
        item.selected = false
        item.z = 0
        item.resizable = false
        return item
      })
    if (localState.value.areas.length > 0) {
      const idArea = localState.value.areas.slice(-1).pop().id // get last areas
      if (idArea) {
        localState.value.areas.push({
          id: idArea + 1,
          x: e.pageX - localState.value.posImg.left,
          y: e.pageY - localState.value.posImg.top,
          width: 0,
          height: 0,
          z: 100,
          resizable: false,
          selected: true,
        })
        localState.value.temp = idArea + 1
      }
    } else {
      localState.value.areas.push({
        id: 1,
        x: e.pageX - localState.value.posImg.left,
        y: e.pageY - localState.value.posImg.top,
        width: 0,
        height: 0,
        z: 100,
        resizable: false,
        selected: true,
      })
      localState.value.temp = 1
    }

    const fixedYScrollOffset = window.pageYOffset || document.documentElement.scrollTop
    localState.value.tempStartX = e.pageX - localState.value.posImg.left
    localState.value.tempStartY =
      e.pageY - fixedYScrollOffset - localState.value.posImg.top
  }
}

function mouseMove(e: any): void {
  if (localState.value.mousedown) {
    drawRectangle(e)
  }
}

function mouseUp(e: any): void {
  if (!localState.value.mousedown) {
    return
  }

  drawRectangle(e)
  localState.value.tempStartX = null
  localState.value.tempStartY = null
  localState.value.mousedown = false
  localState.value.areas
    .filter((item) => item.selected)
    .map((item) => {
      item.resizable = true
      return item
    })
  // delete all point width is = 0
  localState.value.areas = localState.value.areas.filter(
    (item) => item.width !== 0 || item.height !== 0
  )
  getListAreas()
}

// after click rectangle area select active resizable and dragable
function changeResizable(id: any): void {
  localState.value.areas
    .filter((item) => item.id === id)
    .map((item) => {
      item.resizable = true
      item.z = 100
      return item
    })
  localState.value.areas
    .filter((item) => item.id !== id)
    .map((item) => {
      item.resizable = false
      item.z = 0
      return item
    })
}

function drawRectangle(e: any) {
  if (localState.value.tempStartX && localState.value.tempStartY) {
    const fixedYScrollOffset = window.pageYOffset || document.documentElement.scrollTop
    const oldX = localState.value.tempStartX
    const oldY = localState.value.tempStartY
    let newX = e.pageX - localState.value.posImg.left
    let newY = e.pageY - fixedYScrollOffset - localState.value.posImg.top
    if (newX < 0) {
      newX = 0
    }
    if (newX > localState.value.originImgSize.w) {
      newX = localState.value.originImgSize.w
    }
    if (newY < 0) {
      newY = 0
    }
    if (newY > localState.value.originImgSize.h) {
      newY = localState.value.originImgSize.h
    }
    localState.value.areas
      .filter((x) => x.id === localState.value.temp)
      .map((item) => {
        item.x = newX < oldX ? newX : oldX
        item.y = newY < oldY ? newY : oldY
        item.width = newX < oldX ? oldX - newX : newX - oldX
        item.height = newY < oldY ? oldY - newY : newY - oldY
        return item
      })
  }
}

// // delete item area
function deleteSelected(id: any): void {
  localState.value.areas = localState.value.areas.filter((item) => item.id !== id)
}
// drag point around rectangle on image startDrag doDrag endDrag
function startDrag(item: any): void {
  localState.value.dragdown = true
  localState.value.tempRightBound = item.x + item.width
  localState.value.tempBottomBound = item.y + item.height
}

function doDrag(item: any, type: any, e: any): void {
  if (!item?.selected) {
    return
  }
  if (localState.value.dragdown) {
    switch (type) {
      case 'w':
        // fix drag outside box w position
        handleDragWest(item, e)
        break
      case 'sw':
        // fix drag outside box sw position
        handleDragSouth(item, e)
        handleDragWest(item, e)
        break
      case 's':
        // fix drag outside box s position
        handleDragSouth(item, e)
        break
      case 'se':
        // fix drag outside box se position
        handleDragSouth(item, e)
        handleDragEast(item, e)
        break
      case 'e':
        // fix drag outside box e position
        handleDragEast(item, e)
        break
      case 'ne':
        // fix drag outside box ne position
        handleDragNorth(item, e)
        handleDragEast(item, e)
        break
      case 'n':
        // fix drag outside box n position
        handleDragNorth(item, e)
        break
      case 'nw':
        // fix drag outside box nw position
        handleDragNorth(item, e)
        handleDragWest(item, e)
        break
      default:
        break
    }
  }
}

function endDrag(): void {
  if (!localState.value.dragdown) {
    return
  }

  localState.value.dragdown = false
  localState.value.tempRightBound = null
  localState.value.tempBottomBound = null
  getListAreas()
}

// // Helpers for the dragging directions
function handleDragNorth(item: any, e: any): void {
  const fixedYScrollOffset = window.pageYOffset || document.documentElement.scrollTop
  const fixedPageY = e.pageY - fixedYScrollOffset
  if (fixedPageY - localState.value.posImg.top >= 0) {
    if (fixedPageY - localState.value.posImg.top <= localState.value.tempBottomBound) {
      item.height = item.height + (item.y + localState.value.posImg.top - fixedPageY)
      item.y = fixedPageY - localState.value.posImg.top
    } else {
      item.y = localState.value.tempBottomBound
      item.height = 0
    }
  } else {
    item.y = 0
    item.height = localState.value.tempBottomBound
  }

  if (item.height < 0) {
    item.height = 0
  }
}

function handleDragEast(item: any, e: any): void {
  if (e.pageX - localState.value.posImg.left <= localState.value.originImgSize.w) {
    item.width = e.pageX - localState.value.posImg.left - item.x
  } else {
    item.width = localState.value.originImgSize.w - item.x
  }

  if (item.width < 0) {
    item.width = 0
  }
}

function handleDragWest(item: any, e: any): void {
  if (e.pageX - localState.value.posImg.left >= 0) {
    if (e.pageX - localState.value.posImg.left <= localState.value.tempRightBound) {
      item.x = e.pageX - localState.value.posImg.left
      item.width =
        localState.value.tempRightBound - (e.pageX - localState.value.posImg.left) < 0
          ? 0
          : localState.value.tempRightBound - (e.pageX - localState.value.posImg.left)
    } else {
      item.x = localState.value.tempRightBound
      item.width = 0
    }
  } else {
    item.x = 0
    item.width = localState.value.tempRightBound
  }

  if (item.width < 0) {
    item.width = 0
  }
}

function handleDragSouth(item: any, e: any): void {
  const fixedYScrollOffset = window.pageYOffset || document.documentElement.scrollTop
  const fixedPageY = e.pageY - fixedYScrollOffset
  if (fixedPageY - localState.value.posImg.top <= localState.value.originImgSize.h) {
    item.height = fixedPageY - localState.value.posImg.top - item.y
  } else {
    item.height = localState.value.originImgSize.h - item.y
  }

  if (item.height < 0) {
    item.height = 0
  }
}

// move rectangle area startMove doMove endMove
function startMove(item: any, e: any): void {
  localState.value.areas
    .filter((elem) => elem.id === item.id)
    .map((elem) => {
      elem.selected = true
      elem.resizable = true
      elem.z = 100
      return elem
    })
  localState.value.areas
    .filter((elem) => elem.id !== item.id)
    .map((elem) => {
      elem.selected = false
      elem.resizable = false
      elem.z = 0
      return elem
    })
  localState.value.move = true
  localState.value.moveTempX = e.clientX
  localState.value.moveTempY = e.clientY
  localState.value.moveCurrentX = item.x
  localState.value.moveCurrentY = item.y
  getListAreas()
}

function doMove(item: any, e: any): void {
  if (localState.value.move && item && item.selected) {
    const x = localState.value.moveCurrentX + (e.clientX - localState.value.moveTempX)
    const y = localState.value.moveCurrentY + (e.clientY - localState.value.moveTempY)
    const maxX = localState.value.originImgSize.w - item.width
    const maxY = localState.value.originImgSize.h - item.height
    item.x = x < 0 ? 0 : x > maxX ? maxX : x
    item.y = y < 0 ? 0 : y > maxY ? maxY : y
  } else if (localState.value.move) {
    localState.value.areas
      .filter((item) => item.selected)
      .map((item) => {
        if (item) {
          const x =
            localState.value.moveCurrentX + (e.clientX - localState.value.moveTempX)
          const y =
            localState.value.moveCurrentY + (e.clientY - localState.value.moveTempY)
          const maxX = localState.value.originImgSize.w - item.width
          const maxY = localState.value.originImgSize.h - item.height
          item.x = x < 0 ? 0 : x > maxX ? maxX : x
          item.y = y < 0 ? 0 : y > maxY ? maxY : y
        }
        return item
      })
  } else if (localState.value.mousedown) {
    drawRectangle(e)
  }
}

function endMove(): void {
  if (!localState.value.move) {
    return
  }

  localState.value.move = false
  getListAreas()
}

// send data from child to parent $emit
function getListAreas(): void {
  if (props.emitAreaEvent) {
    emit('getListAreas', localState.value.areas)
  } else {
    localState.value.areas = []
  }
}
// send data from child to parent about area being clicked
function selectAreaClicked(): void {
  emit('selectAreaClicked')
}

watch(
  () => props.cropUrl,
  (val) => {
    localState.value.url = val
    if (val == null) {
      localState.value.areas = []
    }
    setTimeout(() => {
      setSize()
    }, 200)
  }
)

watch(
  () => localState.value.areas,
  () => {
    if (!props.ignoreWatchArea) {
      setTimeout(() => {
        getListAreas()
      }, 200)
    }
  }
)

watch(
  () => props.displayAreas,
  (val) => {
    if (val) {
      localState.value.areas = cloneDeep(val)
    }
  },
  { immediate: true, deep: true }
)

onUpdated(() => {
  setTimeout(() => {
    calcPosOfBox()
  }, 200)
})

onMounted(() => {
  localState.value.url = props.cropUrl

  setSize()
  calcPosOfBox()
  window.addEventListener('mouseup', mouseUp)
  window.addEventListener('mouseup', endMove)
  window.addEventListener('mouseup', endDrag)
})

onUnmounted(() => {
  window.removeEventListener('mouseup', mouseUp)
  window.removeEventListener('mouseup', endMove)
  window.removeEventListener('mouseup', endDrag)
})
</script>

<style lang="scss">
.c-crop {
  display: inline-block;
  * {
    box-sizing: border-box;
  }
  img {
    pointer-events: none;
  }
  .c-crop--hide_main {
    width: 0;
    height: 0;
    overflow: hidden;
  }
}
.original-image {
  position: absolute;
}
.select-areas {
  &--overlay {
    overflow: hidden;
    position: absolute;
  }
  &--delete_area {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAQAAAC1+jfqAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAAmJLR0QAAKqNIzIAAAAJcEhZcwAADdcAAA3XAUIom3gAAAAHdElNRQfjCB0SCQuXtRLQAAABRklEQVQoz4XRMUiUcQCG8V//O6MhuuEwI4VDDg9ubDCC+ILzIgcFySEnP2wOkqDRMffAa+3wpqDBSRAPp6MlC+yTFsnS0EzBursp8ECHS3AIetYXXnjfhy5B2SuJlpZPKkaEbnAJDJh33w/v7SLntpvq5uz5G69IPFWUlZGRVTQrsaK/W74o8UiftHPS+kxJVIWUkucWLHvilkO/kfdY5K2OaR+DSQfqjrWNmzFkyIxxbcdWHZpMi7xzpGNJxl29KGhY0nFk3b0gZ0cH22q2lJVtqdnGiW9ywX8Idg3qQV6sYM2aglgePQbtpDXc0avpoUhDDbFIy0vXDWuk/BH76avIpje++OW7lGs+mzBqnqAqMfWPoza9FlJOfVAy5kTTqcuuuCpnwqx9z7S7svq98MDBBVk31M3Zv7hmRMWGpqYNC0rnus8AXqJjvC9MrWIAAAAldEVYdGRhdGU6Y3JlYXRlADIwMTktMDgtMjlUMTY6MDk6MTErMDI6MDDV30hTAAAAJXRFWHRkYXRlOm1vZGlmeQAyMDE5LTA4LTI5VDE2OjA5OjExKzAyOjAwpILw7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAAASUVORK5CYII=);
    cursor: pointer;
    height: 16px;
    width: 16px;
  }
}
.delete-area {
  position: absolute;
  cursor: pointer;
  padding: 5px;
}
</style>
