<template>
  <div
    class="tw-col-span-8 tw-bg-gray-200 tw-flex tw-flex-col tw-items-center tw-h-full"
    :class="`tw-h-auto  tw-min-h-[70vh] tw-pb-6`"
  >
    <div class="tw-w-full">
      <div v-if="props.file" class="row no-wrap tw-border-b tw-border-b-gray-400">
        <q-toolbar :class="'bg-grey-3 tw-py-3'">
          <div>
            プレビュー
            <small v-if="actions.length"
              >({{ actions.length }}アクション<span v-if="actions.length === 20">
                (最大)</span
              >)
            </small>
          </div>
          <q-space />
          <q-btn
            color="white"
            text-color="black"
            icon="add_circle_outline"
            label="アクション追加"
            class="tw-mr-3"
            @click="onNew"
            :disable="!isCanAddAction"
          />
          <div
            class="tw-py-1.5 tw-border tw-rounded-sm tw-px-4 tw-bg-white tw-shadow-md tw-border-gray-300"
          >
            <q-option-group
              v-model="selectMode"
              :options="modeOptions"
              color="primary"
              inline
              dense
            />
          </div>
        </q-toolbar>
      </div>
      <div
        v-else
        class="tw-flex tw-flex-col tw-gap-2 tw-justify-center tw-items-center tw-h-96 tw-pt-20 tw-text-lg tw-text-gray-500"
      >
        <q-icon size="xl" name="image_search" />
        <div>画像が選択されていません</div>
      </div>
    </div>
    <div class="tw-p-4 tw-w-full tw-h-full tw-overflow-x-auto">
      <div class="tw-p-2 tw-relative tw-w-full tw-h-full " :class="`tw-min-w-[${imageMapImageWidthBase}px]`">
        <!-- <img :src="cropUrl" alt="crop" /> -->
        <svg
          id="editor"
          class="tw-z-50 tw-h-auto tw-absolute tw-top-0 tw-left-0"
          :height="imageHeight"
          :width="imageMapImageWidthBase"
          xmlns="http://www.w3.org/2000/svg"
        ></svg>
        <img
          ref="imageRef"
          v-if="props.file"
          :src="imageUrl"
          alt="crop"
          :width="imageMapImageWidthBase"
          class="tw-absolute tw-top-0 tw-left-0 tw-select-none tw-pointer-events-none"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import { storeToRefs } from 'pinia'
import imagemapper, { editor, view, Rectangle } from '@overlapmedia/imagemapper'
import { useElementSize } from '@vueuse/core'
import { useMessageSimulatorStore } from '../../stores/modules/message-simulator'
import { cloneDeep } from 'lodash'
const messageSimulatorStore = useMessageSimulatorStore()
const props = defineProps<{
  width: number;
  file: File;
  initActions: any;
  baseUrl: string;
}>()

const imageRef = ref(null)
const imageUrl = computed(() => {
  if (typeof props.file === 'object' && props.file instanceof Blob) {
    return URL.createObjectURL(props.file)
  }
  return `${props.baseUrl}/${props.width}`
})

const {
  actions,
  imageMapsEditor,
  selectMode,
  imageMapImageHeight,
  isCanAddAction,
  imageMapImageWidthBase
} = storeToRefs(messageSimulatorStore)

const { height: imageHeight } = useElementSize(imageRef)

const initEditor = () => {
  // Editor
  imageMapsEditor.value = imagemapper.editor(
    'editor',
    {
      width: props.width,
      height: imageHeight.value,
      selectModeHandler: () => {
        // console.log('🚀 ~ selectModeHandler:', selectMode.value)
      },
      componentDrawnHandler: (component, componentId) => {
        actions.value.push({
          id: componentId,
          area: {
            x: component?.dim?.x,
            y: component?.dim?.y,
            width: component?.dim?.width,
            height: component?.dim?.height,
          },
          type: 'message',
          text: '',
        })

        registerListeners(component.element, componentId, component)
      },
    },
    {
      component: {
        opacity: '0.3',
        stroke: 'red',
        strokeWidth: '4',
      },
      componentSelect: {
        on: {
          strokeDasharray: '4 3',
          strokeLinejoin: 'round',
        },
        off: {
          strokeDasharray: 'none', // alt. 'initial'
          strokeLinejoin: 'miter',
        },
      },
    }
  )
  // imageMapsEditor.on('mouseup', (e) => imageMapsEditor.selectMode())
  imageMapsEditor.value.selectMode() // Let user draw polygons

  imageMapsEditor.value.on('mouseover', (e) => {
    // console.log('🚀 ~ click:', e)
    syncAllComponents()
  })
}

watch(
  () => props.file,
  (newFile) => {
    if (imageMapsEditor.value) {
      return
    }

    if (newFile) {
      initEditor()
    }
  }
)

watch(
  () => selectMode.value,
  (newSelectMode) => {
    // console.log('🚀 ~ watch ~ newSelectMode:', newSelectMode)
    if (newSelectMode) {
      imageMapsEditor.value.selectMode()
    } else {
      imageMapsEditor.value.rect()
    }
  }
)

const modeOptions = computed(() => {
  return [
    { label: '選択', value: true },
    { label: '描画', value: false, disable: !isCanAddAction.value },
  ]
})

watch(
  () => isCanAddAction.value,
  (newVal) => {
    if (!newVal) {
      selectMode.value = true
      imageMapsEditor.value.selectMode()
    }
  }
)

const onNew = () => {
  const lastAction = actions.value[actions.value.length - 1]
  const firstAction = actions.value[0]
  const nextActionArea = cloneDeep(lastAction?.area) || {
    x: 0,
    y: 0,
    width: 100,
    height: 100,
  }
  // calculate next action area
  // plus x to width, if x + width > image width, set x to 0 and plus y to height
  if (nextActionArea.x + nextActionArea.width < props.width) {
    nextActionArea.x += nextActionArea.width + firstAction?.area?.x * 2
  } else {
    nextActionArea.x = firstAction?.area?.x
    nextActionArea.y += nextActionArea.height + firstAction?.area?.y * 2
  }
  // if nextActionArea is above last action area, set to x to 0 and y to height
  if (nextActionArea.x + lastAction?.area.width > props.width + 10) {
    nextActionArea.x = firstAction?.area?.x
    nextActionArea.y =
      lastAction?.area.y + lastAction?.area.height + firstAction?.area?.y * 2
  }

  if (!firstAction && !lastAction) {
    nextActionArea.x = 0
    nextActionArea.y = 0
    nextActionArea.width = 100
    nextActionArea.height = 100
  }

  const bn = imageMapsEditor.value.createRectangle(nextActionArea)
  registerListeners(bn.element, bn?.element.id, bn)
  actions.value.push({
    id: bn?.element.id,
    area: nextActionArea,
    type: 'message',
    text: '',
  })
}

const onGet = () => {
  const rectangles = imageMapsEditor.value
  // console.log('🚀 ~ rectangles:', rectangles)
}

const syncComponent = (componentId: string) => {
  const _newComponent = imageMapsEditor.value?.getComponentById(componentId)
  if (!_newComponent) return
  // check if x or y is negative, set to 0
  if (_newComponent.dim.x < 0) {
    _newComponent.move(0 - _newComponent.dim.x, 0)
    _newComponent.dim.x = 0
  }
  if (_newComponent.dim.y < 0) {
    _newComponent.move(0, 0 - _newComponent.dim.y)
    _newComponent.dim.y = 0
  }
  // check if x or y is out of bound, set to max
  if (_newComponent.dim.x + _newComponent.dim.width > props.width) {
    _newComponent.move(props.width - _newComponent.dim.width, 0)
    _newComponent.dim.x = props.width - _newComponent.dim.width
  }
  if (_newComponent.dim.y + _newComponent.dim.height > imageHeight.value) {
    _newComponent.move(0, imageHeight.value - _newComponent.dim.height)
    _newComponent.dim.y = imageHeight.value - _newComponent.dim.height
  }

  // check if x or y is out of bound, set to max
  if (_newComponent.dim.x + _newComponent.dim.width > props.width) {
    _newComponent.dim.x = props.width - _newComponent.dim.width
  }
  if (_newComponent.dim.y + _newComponent.dim.height > imageHeight.value) {
    _newComponent.dim.y = imageHeight.value - _newComponent.dim.height
  }

  // _newComponent.resize(_newComponent.dim.width, _newComponent.dim.height)
  messageSimulatorStore.updateActionArea(componentId, {
    x: _newComponent.dim.x,
    y: _newComponent.dim.y,
    width: _newComponent.dim.width,
    height: _newComponent.dim.height,
  })
}

const syncAllComponents = () => {
  // console.log('syncAllComponents')
  let hasSelected = false
  actions.value.forEach((action) => {
    syncComponent(action.id)
    const component = imageMapsEditor.value?.getComponentById(action.id)
    if (component?.isSelected) {
      hasSelected = true
    }
  })

  if (!hasSelected) {
    actions.value = actions.value.map((action) => {
      action.selected = false
      return action
    })
  }
}

const registerListeners = (element: SVGElement, componentId: string, component?: any) => {
  // listen move component
  element.onmousedown = () => {
    syncComponent(componentId)
  }

  element.onmouseup = () => {
    syncComponent(componentId)
  }

  // listen component resize
  element.addEventListener('resize', () => {
    syncComponent(componentId)
  })

  element.onmouseover = () => {
    syncComponent(componentId)
    // imageMapsEditor.value.selectMode()
  }

  element.onmouseleave = () => {
    syncComponent(componentId)
    // imageMapsEditor.value.rect()
  }

  element.onmouseenter = () => {
    syncComponent(componentId)
  }

  element.onmouseout = () => {
    syncComponent(componentId)
  }

  element.onmousemove = () => {
    syncComponent(componentId)
  }

  element.onclick = () => {}

  element.addEventListener('click', () => {
    syncComponent(componentId)
    imageMapsEditor.value.selectMode()
    selectMode.value = true
    component?.setIsSelected(true)
    messageSimulatorStore.setActionSelected(componentId)
  })
}

onMounted(() => {
  if (props.file) {
    initEditor()
  }

  if (props.initActions?.length) {
    props.initActions?.forEach((action) => {
      const bn = imageMapsEditor.value.createRectangle(action?.area, action?.id)
      registerListeners(bn.element, bn?.element.id, bn)
    })
    actions.value = props.initActions
  }
})
</script>
