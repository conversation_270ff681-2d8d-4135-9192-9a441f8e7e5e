<template>
  <div>
    <q-dialog v-model="isLoadingSettings" >
      <q-spinner
        color="primary"
        :size="50"
        :thickness="5"
        :speed="0.6" />
    </q-dialog>
    <SubAppBar :followContainer="true">
      <div class="flex justify-between items-center">
        <div class="body-2">
          <q-breadcrumbs separator="-" class="q-py-sm">
            <q-breadcrumbs-el v-for="(breadcrumb, index) in breadcrumbs" :key="index" :label="breadcrumb.text"
              :to="breadcrumb.to" :tag="breadcrumb.to ? 'router-link' : 'span'" class="text-primary"></q-breadcrumbs-el>
          </q-breadcrumbs>
        </div>
        <q-space></q-space>
        <div>
          <q-btn-group outline class="tab-btn-group">
            <q-btn @click="tab = 'settings-delivery'" class="q-px-md"
              v-if="route.params.formType != 'member' && isAppending" :style="`${getBtnStyle('settings-delivery')}`">
              配信メッセージ定型文
            </q-btn>
            <q-btn @click="tab = 'settings-end'" class="q-px-md" v-if="route.params.formType != 'member'"
              :style="`${getBtnStyle('settings-end')}`">
              登録完了メッセージ
            </q-btn>
            <q-btn @click="tab = 'settings-team'" class="q-px-md" :style="`${getBtnStyle('settings-team')}`">
              閲覧権限設定
            </q-btn>
          </q-btn-group>
        </div>
      </div>
    </SubAppBar>
    <div :opacity="0.2" v-if="firstLoading">
      <content-loading :size="50" text="" />
    </div>
    <q-tab-panels v-model="tab" bordered flat class="q-my-md" animated>
      <q-tab-panel name="settings-delivery">
        <div fluid>
          <SettingsDeliveryMessage @isDeliveryMessageInputProperlyAfter="isDeliveryMessageInputProperlyComplete" />
        </div>
      </q-tab-panel>
      <q-tab-panel name="settings-end">
        <div fluid>
          <SettingsEndOfSurveyMessage
            @isEndOfSurveyMessageInputProperlyAfter="isEndOfSurveyMessageInputProperlyComplete" />
        </div>
      </q-tab-panel>
      <q-tab-panel name="settings-team">
        <div fluid class="q-pt-none full-width">
          <SettingsTeamSelect @isTeamSelectInputProperlyAfter="isTeamSelectInputProperlyComplete"
            :sectionKey="surveyId" />
        </div>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, Ref, onBeforeMount } from "vue";
import SettingsDeliveryMessage from '@/components/FormEditor/SettingsDeliveryMessage.vue';
import SettingsEndOfSurveyMessage from '@/components/FormEditor/SettingsEndOfSurveyMessage.vue';
import SettingsTeamSelect from '@/components/FormEditor/SettingsTeamSelect.vue';
import SubAppBar from '@/components/common/SubAppBar.vue';
import { useFormsStore } from "@/stores/modules/forms";
import { useMemberStore } from "@/stores/modules/member";
import { useFormEditorStore } from "@/stores/modules/formEditor";
import { FormSchema } from "@/types/index";

import { useQuasar, colors } from 'quasar';
import { onBeforeRouteLeave, useRoute, useRouter } from "vue-router";
import { cloneDeep } from "lodash";

const $q = useQuasar();
const { lighten, getPaletteColor } = colors;

const router = useRouter();
const route = useRoute();

const tab = ref();
const formSchemaLocal = ref<FormSchema[]>([]);
const isDeliveryMessageInputProperly = ref(false);
const isEndOfSurveyMessageInputProperly = ref(false);
const isTeamSelectInputProperly = ref(false);
const firstLoading = ref(false);

const isLoadingSettings = ref(false);

const surveyId = route.params.surveyId as string;

const formsStore = useFormsStore();
const memberStore = useMemberStore()
const formEditorStore = useFormEditorStore();
const formSchema = computed(() => formEditorStore.formSchema);

const breadcrumbs: Ref<any> = ref([]);

const isAppending = computed((): boolean => {
  return formSchemaLocal.value.length > 0
    && (formSchemaLocal.value[0].isAppending ? true : false)
    && (formSchemaLocal.value[0].isAppending.value ? true : false);
});

const getSurveyTitle = computed((): string => {
  let surveyTitleLocal = formSchemaLocal.value[0] ? formSchemaLocal.value[0].surveyTitle : "";
  if (surveyTitleLocal.length > 35) {
    surveyTitleLocal = surveyTitleLocal.substring(0, 35) + " ..."
  }
  return surveyTitleLocal;
});

const isDeliveryMessageInputProperlyComplete = (value: boolean) => {
  isDeliveryMessageInputProperly.value = value;
};

const isEndOfSurveyMessageInputProperlyComplete = (value: boolean) => {
  isEndOfSurveyMessageInputProperly.value = value;
};

const isTeamSelectInputProperlyComplete = (value: boolean) => {
  isTeamSelectInputProperly.value = value;
};

const fetchData = async (formType: string): Promise<void> => {
  if (route.params.surveyId) {
    let _result: any = await formEditorStore.initFormSchema({
      surveyId: route.params.surveyId,
      formType: formType,
    });
    formSchemaLocal.value = cloneDeep(formSchema.value);
    formSchemaLocal.value["0"].key = formType == 'survey' ? formSchema.value["0"].surveyId : formSchema.value["0"].memberSurveyId;
    formEditorStore.setFormSchema(formSchemaLocal.value);
    tab.value = isAppending.value ? 'settings-delivery' : 'settings-end';
    firstLoading.value = false;
    if (_result.noInspectPermission) {
      firstLoading.value = false;
      router.replace({
        path: "/PageNotFound",
      });
    }
  } else {
    firstLoading.value = false;
    router.replace({
      path: "/PageNotFound",
    });
  }
};

onMounted(async () => {
  firstLoading.value = true;

  isLoadingSettings.value = true;

  await fetchData(route.params.formType as string);

  // set breadcrumb depending on formType
  const prevPage = {
    text: route.params.formType == 'member' ? '会員帳票一覧' : '各種帳票一覧',
    disabled: false,
    to: route.params.formType == 'member' ? '/forms/member-form-list' : '/forms/form-list',
  }

  breadcrumbs.value = [
    prevPage,
    {
      text: getSurveyTitle,
      disabled: true,
      href: '/',
    },
  ];

  // if formType is member type, set default tab to settings-team
  if (route.params.formType == 'member') {
    tab.value = 'settings-team';
  }

  isLoadingSettings.value = false;
});

const getBtnStyle = (tabName: string): string => {
  const primary = getPaletteColor('primary');
  return tab.value === tabName ? `background-color: ${lighten(primary, 80)}; color: ${lighten(primary, 0)}` : '';
};

onBeforeRouteLeave((to, from, next) => {
  let cautionTxt = "";
  let withdrawalBool = false;
  switch (tab.value) {
    case 'settings-delivery':
      withdrawalBool = isDeliveryMessageInputProperly.value;
      break;
    case 'settings-end':
      withdrawalBool = isEndOfSurveyMessageInputProperly.value;
      break;
    case 'settings-team':
      withdrawalBool = isTeamSelectInputProperly.value;
      break;
    default:
      break;
  }
  if (!withdrawalBool) {
    //this.$snackbar.hide();
    $q.dialog({
      message: "このページを離れてもよろしいですか？",
      color: "warning",
      ok: {
        label: "このページを離れる",
        color: "primary",
      },
      cancel: {
        label: "キャンセル",
        color: "negative",
      },
    }).onOk(async () => {
      $q.loading.show();
      await memberStore.fetchAllMemberFormConfigs();
      await formsStore.fetchFormConfigs();
      $q.loading.hide();
      next();
    }).onCancel(() => {
      next(false);
    });
  } else {
    next();
  }
});
</script>

<style lang="less" scoped>
.tab-btn-group {
  border: 1px solid rgba(0, 0, 0, 0.12);
}
</style>