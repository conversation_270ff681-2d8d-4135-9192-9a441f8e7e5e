<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-row no-gutters class="q-pb-md q-pl-md">
      <q-col class="col-10">
        <q-input 
          type="textarea"
          placeholder="長文回答・デフォルト設定（任意）"
          :autogrow="true"
          rows="1"
          hide-details
          :maxlength="item.length?.limitation || 300"
          counter
          :disable="!isActive"
          v-model="item.default"
        ></q-input>
      </q-col>
    </q-row>
    <template v-if="isActive">
      <q-separator class="q-my-md"></q-separator>
      <q-item-label class="q-pl-md">回答の検証を設定（任意）</q-item-label>
      <Length :item="item" :isEditMode="isEditMode" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';



// old imports
// 旧インポート
import Length from "@/components/FormEditor/ValidatorComponents/Length.vue";


// props
const props = defineProps({
  item: { type: Object as PropType<any>, required: true },
  isActive: { type: Boolean as PropType<boolean>, required: true },
  isEditMode: { type: Boolean as PropType<boolean>, required: true }
});
</script>