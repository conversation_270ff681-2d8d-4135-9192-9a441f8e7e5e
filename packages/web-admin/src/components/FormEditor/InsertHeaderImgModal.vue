<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-dialog v-model="show" eager>
    <q-card class="q-pa-sm" :style="`max-width: 640px; width: 640px;`">
      <q-toolbar flat :style="`height: 60px`">
        <q-toolbar-title class="text-bold q-pa-sm" style="font-size:26px;">ヘッダー画像設定</q-toolbar-title>
        <q-space></q-space>
        <q-btn flat rounded @click="show = false">
          <q-icon name="mdi-close"></q-icon>
        </q-btn>
      </q-toolbar>
      <div class="q-px-md text-blue-grey" style="font-size:14px;">画像比率：4:1(推奨
        1440px)<br>対応する形式：jpg,jpeg,png<br>画像容量：3MB以下</div>
      <Alert elevation="0" dense class="q-py-none q-mt-md q-mx-xl headerImgArea relative">
        <div v-if="headerImageUrl.headerUrl && !chkErr.length"
        class="flex justify-center q-mx-md">
          <q-img  fit='contain' 
            :src="headerImageUrl.headerUrl ? headerImageUrl.headerUrl : ''"
            class="flex rounded-borders" :style="`width: 100%;`" ></q-img>
        </div>
          <div v-else-if="chkErr.length > 0"
            class="flex warning--text items-center justify-center bg-red-1 text-red-9 lighten-5 q-pa-md q-mx-md">
            <q-icon size="xl" color="warning" name="mdi-alert-outline"></q-icon>
            <ul class="q-px-md">
              <li v-for="(msg, index) in chkErr" :key="index" style="font-size:14px;">
                {{ msg }}
              </li>
            </ul>
            <q-icon large color="warning" name="mdi-close" size="md"></q-icon>
          </div>
          <input type="file" id="headerImageFile" ref="inputFileHeader" accept="image/png, image/jpeg"
            @change="handleFileChange" style="display: none" />
        <div class="q-pt-md q-pb-none q-pl-none q-pr-md row">
          <q-col v-if="!headerImageUrl.headerUrl || JSON.stringify(headerImageUrl.headerUrl) == '{}'"
          class="flex justify-end full-width">
            <q-btn class="text-black q-py-sm q-px-md" elevation="0" outlined @click="closeModalCancel()" dense>キャンセル
            </q-btn>
            <q-btn class="white--text q-py-sm q-px-md q-ml-sm" :elevation="0" dense color="primary"
              @click="selectPageBreakImage">
              画像を選択
            </q-btn>
          </q-col>
          <q-col class="q-pl-md flex justify-between full-width" v-else>
            <q-btn class="white--text q-pa-sm self-start" :elevation="0" color="primary"
              @click="selectPageBreakImage">
              画像を変更
            </q-btn>
            <div class="q-gutter-md">
              <q-btn outline class="q-pa-sm q-px-md" elevation="0" color="red white--text" @click="onDeleteHeaderImageUrl">削除
              </q-btn>
              <q-btn elevation="0" color="primary white--text" class="q-pa-sm q-px-md"
                @click="saveHeader">挿入
              </q-btn>
            </div>
          </q-col>
        </div>
      </Alert>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { PropType, ref, computed, onBeforeMount } from 'vue';

import { useFormEditorStore } from '@stores/modules/formEditor/index';

const formEditorStore = useFormEditorStore();

// old imports
// 旧インポート
import HeaderImageUrl from "@/model/Form/HeaderImageUrl";
/*import { cloneDeep } from "lodash";
import { UPDATE_SURVEY_SCHEMA } from "@/store/mutation-types";
import { eventBus } from '@/main.ts'*/

const inputFileHeader = ref<any>(null);

// emits 
const emits = defineEmits<{
  (event: 'saveHeader', payload: any): void;
  (event: 'close'): void;
}>();

// props
const props = defineProps({
  visible: Boolean as PropType<boolean>,
  close: Function
});

// data
const aspectRatio = ref<any>(null);
const headerImageUrl = ref<any>({});
const chkErr = ref<any>([]);

// methods
const updateSurveySchema = formEditorStore.updateSurveySchema;
const selectPageBreakImage = (): void => {
  inputFileHeader.value.click();
};
const handleFileChange = async (e: any): Promise<void> => {
  // if file is not any of the allowed formats, show an error notification and return
  if (!e.target.files[0].type.includes('image')) {
    chkErr.value = ['画像ファイルを選択してください。'];
    return;
  }
  const headerImageUrlObject = await HeaderImageUrl.createObject(e.target.files[0]);
  const file = e.target.files[0];
  if (headerImageUrlObject.chkErr.length != 0) {
    chkErr.value = headerImageUrlObject.chkErr;
    return;
  } else {
    chkErr.value = [];
    headerImageUrl.value.file = file;
    headerImageUrl.value.headerUrl = URL.createObjectURL(file);
  }
};
const onDeleteHeaderImageUrl = (): void => {
  headerImageUrl.value = {};
  let headerImageFile: any;
  headerImageFile = document.getElementById('headerImageFile');
  headerImageFile.value = "";
  saveHeader();
};
const saveHeader = (): void => {
  //console.log('headerImageUrl.value', headerImageUrl.value);
  emits('saveHeader', headerImageUrl.value);
  emits("close");
  // reset errors on close
  chkErr.value = [];
};

const closeModalCancel = (): void => {
  emits("close");
  // reset errors on close
  chkErr.value = [];
};

// computed
const isEditMode = computed(() => formEditorStore.isEditMode);
const formType = computed(() => formEditorStore.formType);
const isMemberForm = computed((state) => {
  return (state as any).formEditor.formType.value === "member" ? true : false;
});
const show = computed({
  get() {
    return props.visible;
  },
  set(value) {
    if (!value) {
      emits("close");
      chkErr.value = [];
    }
  },
});

// hooks

onBeforeMount(() => {
  headerImageUrl.value.headerUrl = formEditorStore.step1SurveyFormat.headerImageUrl ? formEditorStore.step1SurveyFormat.headerImageUrl : '';
});

</script>
<style>
.inputHeightCtrl fieldset,
.inputHeightCtrl .v-input__control,
.inputHeightCtrl .v-input__slot,
.inputHeightCtrl .v-text-field__slot {
  height: 40px !important;
  min-height: 40px !important;
}

.inputHeightCtrl legend {
  display: none;
}

.inputHeightCtrl .v-input__append-inner {
  margin-top: 6px !important;
}

.inputHeightCtrl .v-label,
.inputHeightCtrl fieldset {
  top: 0px;
  font-size: 14px;
}

.fs-14 .v-label,
.fs-14,
.fs-14 input,
.inputHeightCtrl input,
.inputHeightCtrl .v-select__selection {
  font-size: 14px !important;
}

.v-input .v-input__control div {
  box-shadow: none !important;
}

.v-input--radio-group.radioFlex .v-input--radio-group__input {
  flex-direction: row;
  align-items: flex-start;
}

.radioFlex .v-radio label {
  font-size: 14px;
  left: 8px !important;
  top: 3px;
}

.radioFlex .mdi-radiobox-blank::before,
.radioFlex .mdi-radiobox-marked::before,
.radioFlex .v-input--selection-controls__input {
  width: 20px;
  height: 20px;
}

.radioFlex .mdi-radiobox-blank::after,
.radioFlex .mdi-radiobox-marked::after {
  width: 22px;
  height: 22px;
}

.headerImgArea .v-image__image {
  border-radius: 4px;
}

.headerImgArea .v-responsive__content {
  width: 100% !important;
}

.headerImgArea .v-alert__content {
  position: relative;
}

.headerImgArea .v-alert__content .headerDeleteBtn {
  position: absolute;
  right: 8px;
  top: 8px;
  z-index: 999;
  display: none !important;
  transition: all 0.6s;
}

.headerImgArea .v-alert__content:hover .headerDeleteBtn {
  display: inline-flex !important;
}
</style>