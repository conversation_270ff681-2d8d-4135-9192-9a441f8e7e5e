<template>
  <div>
    <q-row class="fe-cont">
      <q-col class="full-width">
        <SectionGroup
          v-for="(section, index) in formSchemaStore"
          :key="section.key"
          :section="section"
          :index="index + 1"
          :duplicateFlg="duplicateFlg"
          class="mb-5"
        />
      </q-col>
      <q-col class="q-ml-md q-mr-md">
        <FormMenuToolBar />
      </q-col>
    </q-row>
    <MoveQuestionModal
      :visible="showMoveSectionsModal"
      :questions="formSchemaStore"
      @close="toggleMoveSectionsModal(false)"
    />
    <CodeEditorModal :visible="showCodeEditorModal" :sections="formSchemaStore" @close="toggleCodeEditorModal(false)" />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import SectionGroup from "@/components/FormEditor/SectionGroup.vue";
import FormMenuToolBar from "@/components/FormEditor/FormMenuToolBar.vue";
import "@/assets/styles/form-editor.less";
import MoveQuestionModal from "@/components/FormEditor/MoveQuestionModal.vue";
import CodeEditorModal from "@/components/FormEditor/CodeEditorModal.vue";
import { useFormEditorStore } from "@/stores/modules/formEditor";

const formEditorStore = useFormEditorStore();

const duplicateFlg = ref(false);
const showMoveSectionsModal = ref(false);
const showCodeEditorModal = ref(false);
const formSchemaStore = computed(() => formEditorStore.formSchema);
const isMemberForm = ref(false);

const toggleMoveSectionsModal = (value: any) => {
  showMoveSectionsModal.value = value;
};

const toggleCodeEditorModal = (value: any) => {

  showCodeEditorModal.value = value;
};

onMounted(() => {
  // TODO: Not implemented in favor of a computed approach.
});
</script>

<style lang="less">
.fe-cont {
  display: flex;
}
</style>