<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-dialog v-model="show" max-width="500" persistent class="flex column">
    <q-card style="width: 500px;">
      <q-toolbar class="bg-primary text-white">
        <q-toolbar-title>Reorder Sections</q-toolbar-title>
        <q-btn flat round dense icon="close" @click="show = false" />
      </q-toolbar>
      <q-separator />
      <q-list ref="moveSectionsDrag" class="form-editor-drag flex column bg-white">
        <q-item v-for="(section, index) in reorderSections" :key="section.key" class="flex">
          <q-item-section>
            <q-item-label class="text-truncate" style="max-width: 300px">
              {{ section.title || "Untitled Section" }}
            </q-item-label>
            <q-item-label caption>
              Section {{ index + 1 }} of {{ reorderSections?.length }}
            </q-item-label>
          </q-item-section>
          <q-item-section side>
            <q-btn flat dense round icon="arrow_upward" :disable="index === 0" @click="moveUp(index)" />
            <q-btn flat dense round icon="arrow_downward" :disable="index + 1 === reorderSections?.length"
              @click="moveDown(index)" />
          </q-item-section>
        </q-item>
      </q-list>
      <q-separator />
      <q-card-actions align="right" class="pa-4">
        <q-btn color="primary" class="px-8" flat @click="onSaveReorderSections">
          <q-icon name="save" left />
          Save
        </q-btn>
        <q-btn color="grey-2" class="ml-3" flat @click="show = false">
          <q-icon name="cancel" left />
          Cancel
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">

import { ref, watch, onMounted, PropType } from 'vue';
import Sortable from "sortablejs";
import { cloneDeep } from 'lodash';
import { useFormEditorStore } from '@stores/modules/formEditor';

const props = defineProps({
  visible: Boolean as PropType<boolean>,
  close: Function,
  sections: Array as PropType<any[]>,
});

const formEditorStore = useFormEditorStore();

const show = ref(props.visible);
const reorderSections = ref(cloneDeep(props.sections));

const moveUp = (idx: number) => {
  moveSection(idx - 1, idx);
};

const moveDown = (idx: number) => {
  moveSection(idx + 1, idx);
};

const moveSection = (newIndex: number, oldIndex: number) => {
  const dragSection = reorderSections.value?.splice(oldIndex, 1);
  if (!dragSection || dragSection.length === 0) return;
  reorderSections.value?.splice(newIndex, 0, dragSection[0]);
};

const setupDrag = () => {
  const list = document.querySelector('.form-editor-drag') as HTMLElement;
  if (list) {
    Sortable.create(list, {
      handle: '.item-drag-handle',
      animation: 150,
      chosenClass: 'grey',
      onEnd: ({ newIndex, oldIndex }) => {
        moveSection(newIndex as number, oldIndex as number);
      },
    });
  }
};

const onSaveReorderSections = () => {
  formEditorStore.setFormSchema(reorderSections.value);
  show.value = false;
};

watch(
  () => props.sections,
  (val) => {
    reorderSections.value = cloneDeep(val);
  }
);

onMounted(() => {
  setupDrag();
});
</script>
