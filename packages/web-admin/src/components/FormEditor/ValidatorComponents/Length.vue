<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-row class="row q-pt-md q-col-gutter-md">
    <q-col class="col-3 q-pa-none flex items-center justify-center">
      <div>{{ displayLabel }}</div>
    </q-col>
    <q-col class="col-4 q-py-none q-pr-none">
      <q-input
        v-model.number="item.length.min"
        :label="minLabel"
        type="number"
        min="1"
        :max="item.length.limitation"
        required
        outlined
        dense
        hide-details
        :readonly="isEditMode"
        @keydown="rangeInputKeyDown($event, 'min')"
        @focusout="rangeInputMin($event)"
        @input="(val: any) => onInput(val, 'min')"
      ></q-input>
    </q-col>

    <q-col class="col-4 q-py-none q-pr-none">
      <q-input
        v-model.number="item.length.max"
        :label="maxLabel"
        type="number"
        min="1"
        :max="item.length.limitation"
        required
        outlined
        dense
        hide-details
        :readonly="isEditMode"
        @keydown="rangeInputKeyDown($event, 'max')"
        @focusout="rangeInputMax($event)"
        @input="(val: any) => onInput(val, 'max')"
      ></q-input>
    </q-col>
  </q-row>
</template>

<script setup lang="ts">
import { PropType,  computed, onBeforeMount } from 'vue';

import { useQuasar } from 'quasar';

// old imports
// 旧インポート
/**/

const $q = useQuasar();

let item_initial = null;

// props
const props = defineProps({
  item: {
    min: null,
    max: null,
    limitation: 300
  } as any,
  isEditMode: { type: Boolean as PropType<boolean>, required: true },
  dataType: {
    type: String as PropType<string>,
    default: "text"
  }
});

// methods
const onInput = (val: string, key: string) => {
      if(Number(val) > props.item.length.limitation) {
        $q.notify({ message: props.item.length.limitation + "以下で入力して下さい。", type: "warning" });
        props.item.length[key] = props.item.length.limitation
      }
    };
const rangeInputKeyDown = (evt: any, key: string): boolean => {
      evt = evt ? evt : window.event;
      const keyCode = evt.keyCode
      var char = evt.key;
      if (char == "-" || char == "." || char == "e") {
        evt.preventDefault();

        return false;
      } else {
        if(Number(props.item.length[key]) >= props.item.length.limitation && keyCode != 8) {
          props.item.length[key] = props.item.length.limitation
          evt.preventDefault();
          return false;
        }
        return true;
      }
    };
const rangeInputMin = (evt: any) => {
      evt = evt ? evt : window.event;
      let min_element = evt.target;
      let min_value = min_element === undefined ? "" : min_element.value;

      var char = evt.key;
      if (char == "-" || char == "." || char == "e") {
        min_element.value = "";
        props.item.length.min = null;

        return false;
      } else {
        if (min_value == "") {
          min_element.value = "";
          props.item.length.min = null;
        } else if (isNaN(min_value) === false) {
          // Is numeric
          min_value = parseInt(min_value);
          if (min_value == 0) {
            // 0
            min_element.value = "";
            props.item.length.min = null;
            $q.notify({ message: "1以上を入力して下さい。", type: "error" });

            return false;
          } else {
            // > 0
            let max_value = parseInt(props.item.length.max);

            if (min_value > max_value) {
              $q.notify({ message: "最大文字数以下の数字を入力して下さい。", type: "error" });
              min_element.value = "";
              props.item.length.min = null;

              return false;
            } else if (min_value > props.item.length.limitation) {
              $q.notify({ message: props.item.length.limitation + "以下で入力して下さい。", type: "error" });
              min_element.value = "";
              props.item.length.min = null;

              return false;
            }
            return true;
          }
        } else {
          // Is not numeric
          min_element.value = "";
          props.item.length.min = null;
          $q.notify({ message: "英数字を入力して下さい。", type: "error" });
          return false;
        }
      }
    };
const rangeInputMax = (evt: any): boolean => {
      evt = evt ? evt : window.event;
      let max_element = evt.target;
      let max_value = max_element === undefined ? "" : max_element.value;

      var char = evt.key;
      if (char == "-" || char == "." || char == "e") {
        max_element.value = "";
        props.item.length.max = null;

        return false;
      } else {
        if (max_value == "") {
          max_element.value = "";
          props.item.length.max = null;
        } else if (isNaN(max_value) === false) {
          // Is numeric
          max_value = parseInt(max_value);
          if (max_value == 0) {
            // 0
            max_element.value = "";
            props.item.length.max = null;
            $q.notify({ message: "1以上を入力して下さい。", type: "error" });

            return false;
          } else {
            // > 0
            if (max_value > props.item.length.limitation) {
              $q.notify({ message: props.item.length.limitation + "以下で入力して下さい。", type: "error" });

              max_element.value = "";
              props.item.length.max = null;

              return false;
            } else {
              let min_value = parseInt(props.item.length.min);

              if (min_value > max_value) {
                $q.notify({ message: "最小文字数以上の数字を入力して下さい。", type: "error" });

                max_element.value = "";
                props.item.length.max = null;

                return false;
              }
            }

            return true;
          }
        } else {
          // Is not numeric
          max_element.value = "";
          props.item.length.max = null;
          $q.notify({ message: "英数字を入力して下さい。", type: "error" });
          return false;
        }
      }

      return true;
    };

// computed
const displayLabel = computed(() => {
      // 数字の場合
      if (props.dataType === "number") {
        return "桁数の範囲";
      }
      return "長さ";
    });
const minLabel = computed(() => {
      // 数字の場合
      if (props.dataType === "number") {
        return "最小";
      }
      return "最小文字数";
    });
const maxLabel = computed(() => {
      // 数字の場合
      if (props.dataType === "number") {
        return "最大";
      }
      return "最大文字数";
    });

// hooks

onBeforeMount(() => {
  props.item.length.limitation = props.dataType === "number" ? String(Number.MAX_SAFE_INTEGER).length : 300;
  item_initial = props.item;
});

</script>
