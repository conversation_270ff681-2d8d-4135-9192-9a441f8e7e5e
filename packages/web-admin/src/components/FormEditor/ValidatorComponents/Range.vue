<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-row class="row q-mb-sm q-col-gutter-md">
    <q-col class="col-3">
      <div class="q-pt-sm">値の範囲</div>
    </q-col>
    <q-col class="col-4">
      <q-input v-model="props.item.range.min" label="最小" type="number" min="1" required outlined dense
        :readonly="isEditMode" @keydown="rangeInputKeyDown($event)" @focusout="rangeInputMin($event)"></q-input>
    </q-col>

    <q-col class="col-4">
      <q-input v-model="props.item.range.max" label="最大" type="number" min="1" required outlined dense
        :readonly="isEditMode" @keydown="rangeInputKeyDown($event)" @focusout="rangeInputMax($event)"></q-input>
    </q-col>
    <q-col class="col-1 q-px-none">
      <q-btn flat rounded v-if="!isEditMode" @click="clearSetting">
        <q-icon name="mdi-close"></q-icon>
      </q-btn>
    </q-col>
  </q-row>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import { PropType } from 'vue';


const $q = useQuasar();

// props
const props = defineProps({
  item: Object as PropType<any>,
  isEditMode: { type: Boolean as PropType<boolean>, required: true }
});

// methods
const clearSetting = (): void => {
  props.item.range = {
    min: null,
    max: null,
  };
};
const rangeInputKeyDown = (evt: any): boolean => {
  evt = evt ? evt : window.event;

  var char = evt.key;
  if (char == "-" || char == "." || char == "e") {
    evt.preventDefault();

    return false;
  } else {
    return true;
  }
};
const rangeInputMin = (evt: any) => {
  evt = evt ? evt : window.event;
  let min_element = evt.target;
  let min_value = min_element === undefined ? "" : min_element.value;

  var char = evt.key;
  if (char == "-" || char == "." || char == "e") {
    min_element.value = "";
    props.item.range.min = null;

    return false;
  } else {
    if (min_value == "" || min_value == undefined) {
      min_element.value = "";
      props.item.range.min = null;
    } else if (isNaN(min_value) === false) {
      // Is numeric
      min_value = parseInt(min_value);
      if (min_value == 0) {
        // 0
        min_element.value = "";
        props.item.range.min = null;
        $q.notify({ message: "英数字で1以上を入力して下さい。", type: "error" });

        return false;
      } else {
        // > 0
        let max_value = parseInt(props.item.range.max);

        if (min_value > max_value) {
          $q.notify({ message: "最大以下の数字を入力して下さい。", type: "error" });
          min_element.value = "";
          props.item.range.min = null;

          return false;
        }
        return true;
      }
    } else {
      // Is not numeric
      min_element.value = "";
      props.item.range.min = null;
      $q.notify({ message: "英数字を入力して下さい。", type: "error" });
      return false;
    }
  }
};
const rangeInputMax = (evt: any) => {
  evt = evt ? evt : window.event;
  let max_element = evt.target;
  let max_value = max_element === undefined ? "" : max_element.value;
  let min_value = props.item.range.min === null ? "" : props.item.range.min;

  var char = evt.key;
  if (char == "-" || char == "." || char == "e") {
    max_element.value = "";
    props.item.range.max = null;

    return false;
  } else {
    if (max_value == "") {
      max_element.value = "";
      props.item.range.max = null;
    } else if (isNaN(max_value) === false) {
      // Is numeric
      max_value = parseInt(max_value);
      if (max_value == 0) {
        // 0
        max_element.value = "";
        props.item.range.max = null;
        $q.notify({ message: "英数字で1以上を入力して下さい。", type: "error" });

        return false;
      } else {
        // > 0
        min_value = parseInt(min_value);

        if (min_value > max_value) {
          $q.notify({ message: "最小以上の数字を入力して下さい。", type: "error" });

          max_element.value = "";
          props.item.range.max = null;

          return false;
        }
        return true;
      }
    } else {
      // Is not numeric
      max_element.value = "";
      props.item.range.max = null;
      $q.notify({ message: "英数字を入力して下さい。", type: "error" });
      return false;
    }
  }
};
</script>