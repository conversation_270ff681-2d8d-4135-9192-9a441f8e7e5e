<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-row class="q-mb-sm q-pl-lg row q-col-gutter-md">
    <q-col class="col-3">
      <div class="body-1 q-pt-sm">選択個数</div>
    </q-col>
    <q-col class="col-3">
      <q-input
        v-model="props.item.selection.min"
        label="選択する最小個数"
        type="number"
        min="1"
        required
        outline
        dense
        hide-details
        :readonly="isEditMode"
        @keydown="rangeInputKeyDown($event)"
        @focusout="rangeInputMin($event)"
      >
    </q-input>
    </q-col>

    <q-col class="col-3">
      <q-input
        v-model="props.item.selection.max"
        label="選択する最大個数"
        type="number"
        min="1"
        required
        outline
        dense
        :readonly="isEditMode"
        @keydown="rangeInputKeyDown($event)"
        @focusout="rangeInputMax($event)"
        hide-details
      ></q-input>
    </q-col>
    <q-col class="q-pa-none flex flex-center">
      <q-btn class="q-pa-none" v-if="!isEditMode" flat text fab @click="clearSetting">
        <q-icon class="q-pa-none" name="mdi-close"></q-icon>
      </q-btn>
    </q-col>
  </q-row>
</template>

<script setup lang="ts">
import { PropType, watch } from 'vue';

import { useQuasar } from 'quasar';

// old imports
// 旧インポート
/**/

const $q = useQuasar();

// props
const props = defineProps({
  item: Object as PropType<any>,
  isEditMode: { type: Boolean as PropType<boolean>, required: true }
});

// methods
const clearSetting = (): void => {
      props.item.selection = {
        min: null,
        max: null,
      };
    };
const rangeInputKeyDown = (evt: any): boolean => {
      evt = evt ? evt : window.event;

      var char = evt.key;
      if (char == "-" || char == "." || char == "e") {
        evt.preventDefault();

        return false;
      } else {
        return true;
      }
    };
const rangeInputMin = (evt: any): boolean => {
      evt = evt ? evt : window.event;
      let min_element = evt.target;
      let min_value = min_element === undefined ? "" : min_element.value;

      var char = evt.key;
      if (char == "-" || char == "." || char == "e") {
        min_element.value = "";
        props.item.selection.min = null;

        return false;
      } else {
        if (min_value == "") {
          min_element.value = "";
          props.item.selection.min = null;
        } else if (isNaN(min_value) === false) {
          // Is numeric
          min_value = parseInt(min_value);
          if (min_value == 0) {
            // 0
            min_element.value = "";
            props.item.selection.min = null;
            $q.notify({ message: "英数字で1以上を入力して下さい。", type: "error" });

            return false;
          } else {
            // > 0
            let max_value = parseInt(props.item.selection.max);

            if (min_value > max_value) {
              $q.notify({ message: "最大個数以下の数字を入力して下さい。", type: "error" });
              min_element.value = "";
              props.item.selection.min = null;

              return false;
            }
          }

          if (props.item.options.length < min_value) {
            props.item.selection.min = null;
            $q.notify({ message: "選択個数は選択肢の数より少なく設定してください。", type: "error" });
            return false;
          }
        } else {
          // Is not numeric
          min_element.value = "";
          props.item.selection.min = null;

          $q.notify({ message: "英数字を入力して下さい。", type: "error" });
          return false;
        }
      }

      return true;
    };
const rangeInputMax = (evt: any): boolean => {
      evt = evt ? evt : window.event;
      let max_element = evt.target;
      let max_value = max_element === undefined ? "" : max_element.value;

      var char = evt.key;
      if (char == "-" || char == "." || char == "e") {
        max_element.value = "";
        props.item.selection.max = null;

        return false;
      } else {
        if (max_value == "") {
          max_element.value = "";
          props.item.selection.max = null;
        } else if (isNaN(max_value) === false) {
          // Is numeric
          max_value = parseInt(max_value);
          if (max_value == 0) {
            // 0
            max_element.value = "";
            props.item.selection.max = null;
            $q.notify({ message: "英数字で1以上を入力して下さい。", type: "error" });

            return false;
          } else {
            // > 0
            let min_value = parseInt(props.item.selection.min);

            if (min_value > max_value) {
              $q.notify({ message: "最小個数以上の数字を入力して下さい。", type: "error" });

              max_element.value = "";
              props.item.selection.max = null;

              return false;
            }
            return true;
          }
        } else {
          // Is not numeric
          max_element.value = "";
          props.item.selection.max = null;

          $q.notify({ message: "英数字を入力して下さい。", type: "error" });
          return false;
        }
      }

      return true;
    };

// watch
watch(() => props.item.selection.min, (val) => {
  if (val === "") {
    props.item.selection.min = null;
  }
}, { deep: true });
watch(() => props.item.selection.max, (val) => {
  if (val === "") {
    props.item.selection.max = null;
  }
}, { deep: true });
</script>