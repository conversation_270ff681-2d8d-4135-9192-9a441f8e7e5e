<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-dialog v-model="show" :style="`min-width: 800px; max-width: 1000px;`">
    <q-card class="q-pa-sm">
      <q-card-section class="text-h5 q-pa-md">一括設定</q-card-section>
      <q-card-section>
        <q-page-container dense>
          <q-row class="row">
            <q-col>
              <q-icon color="primary" name="mdi-alert-circle-outline" size="sm"></q-icon>
              選択した項目を閲覧できるチームを選択してください。 個別での編集も可能です。
            </q-col>
          </q-row>
          <q-row class="row q-py-md">
            <q-col class="col-4 flex self-center">一括設定</q-col>
            <q-col class="col-6">
              <q-select use-input v-model="bundleSetting.teams" :options="filteredTeamList"
                test-rules="validate.teamSelectRule" option-label="teamName" option-value="teamId" use-chips
                :max-values="maxTeamsSelectedAmount" :menu-props="bundleSetting.menuProps" return-object dense small
                multiple clearable @update:model-value="(item)=>handleBundleSettingTeamsChange(item)" @filter="itemFilter" ></q-select>
            </q-col>
          </q-row>
          <q-row v-for="(item, index) in items" :key="index + '_' + item.key" class="row">
            <q-col class="col-4 q-my-md">{{ item.chushoBunrui }}</q-col>
            <q-col class="col-6 q-my-md">
              <q-select use-input v-model="item.teams" :options="filteredTeamList" test-rules="validate.teamSelectRule"
                option-label="teamName" option-value="teamId" use-chips :max-values="maxTeamsSelectedAmount"
                :menu-props="{ ...item.menuProps, disabled: item.teams?.length >= maxTeamsSelectedAmount }" return-object dense small multiple clearable
               @update:model-value="(val)=>handleSurveyTeamsChange(val, item)" @filter="itemFilter"></q-select>
            </q-col>
          </q-row>
        </q-page-container>
      </q-card-section>
      <q-card-actions>
        <q-space></q-space>
        <q-btn v-if="false" color="primary" class="q-mx-sm">閲覧設定</q-btn>
        <q-btn color="grey" text @click="show = false" class="q-ma-sm">閉じる</q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { PropType, ref, computed, onBeforeMount } from 'vue';

import { useFormEditorStore } from '@stores/modules/formEditor/index';
import { useFormsStore } from '@stores/modules/forms/index';

// old imports
// 旧インポート
import { cloneDeep } from "lodash";


const formEditorStore = useFormEditorStore();
const formsStore = useFormsStore();
const formSchema = computed(() => formEditorStore.formSchema);

// emits 
const emits = defineEmits<{
  (event: 'close'): void;
}>();

// props
const props = defineProps({
  surveyTeams: Array as PropType<any>,
  visible: Boolean as PropType<boolean>,
  items: Array as PropType<any>,
  model: Object as PropType<any>
});

// data
const categoriesPermissions = ref<any>([]);
const categoriesPermissionsOriginal = ref<any>([]);
const maxTeamsSelectedAmount = ref<number>(10);
const validate = ref<any>({
  teamSelectRule: [(v: string) => (v && v.length >= 1) || "1つ以上のチームを選択してください。"],
});
const bundleSetting = ref<any>({
  menuProps: { disabled: false },
  team: null,
});

const filteredTeamList  = ref<any>([]);


const itemFilter = (val: string, update: any) => {
  if (!val) {
    update(() => {
      return props.surveyTeams;
    });
  }

  update(() => {
    filteredTeamList.value = props.surveyTeams.filter((item: any) => {
      return item.teamName.toLowerCase().indexOf(val.toLowerCase()) !== -1;
    });
  });
};

// methods
const updateFormSchemaElement = formEditorStore.updateFormSchemaElement;
const fetchFormConfigs = formsStore.fetchFormConfigs;

const handleChipRemoval = async (item: any): Promise<void> => {
  if (item.value && item.value.teamId) {
    props.items.forEach((categoryItem: any) => {
      const filteredTeams = categoryItem.teams.filter((tmId: any) => tmId.teamId !== item.value.teamId);
      categoryItem.teams = filteredTeams;
      assignToCategoriesPermissionsState(categoryItem);
    });
  }
}

const handleBundleSettingTeamsChange = async (item: any): Promise<void> => {
  if (bundleSetting.value.teams) {
    bundleSetting.value.menuProps.disabled = bundleSetting.value.teams.length >= maxTeamsSelectedAmount.value;
  }

  if (item && item.length > 0) {
    const lastItem = item[item.length - 1];
    props.items.forEach((categoryItem: any) => {
      const addedTeams = [...new Set([...categoryItem.teams || [], lastItem])];

      categoryItem.teams = addedTeams;

      // Assign the updated categoryItem to the state
      assignToCategoriesPermissionsState(categoryItem);
    });
  }

  // FIXME if not working well
  //  if (item.value && item.value.teamId) {
  //   props.items.forEach((categoryItem: any) => {
  //     let arr = categoryItem.teams;
  //     let bundleSettingTeams = cloneDeep(bundleSetting.value.teams);
  //     let categoryItemTeams = cloneDeep(categoryItem.teams);
  //     // console.log("props.item", bundleSettingTeams);
  //      const addedTeams = categoryItem.teams.push((tmId: any) => tmId.teamId !== item.value.teamId);
  //       categoryItem.teams = addedTeams;
  //       console.log(categoryItem);

  //       assignToCategoriesPermissionsState(categoryItem);

  //     // FIXME if not working good
  //     // bundleSettingTeams.forEach((bundleTeam: any) => {
  //     //   let catItemTeam = categoryItemTeams.find((categoryItemTeam: any) => bundleTeam.teamId === categoryItemTeam.teamId);
  //     //   if (!catItemTeam) {
  //     //     categoryItem.teams.push(cloneDeep(bundleTeam));
  //     //   }
  //     // });
  //   });
  // }
};

const assignToCategoriesPermissionsState = (item: any): void => {
  
  categoriesPermissions.value = formSchema.value[0].categoriesPermissions
  let categoriesPermissionsCopy = cloneDeep(categoriesPermissions.value);
  categoriesPermissionsCopy.forEach((categoriesPermission: any) => {
    let catPermissionListItem = categoriesPermission.list.find(
      (categoriesPermissionListItem: any) => categoriesPermissionListItem.categoryId === item.id
    );
    if (catPermissionListItem) {
      catPermissionListItem.teamIds = [];
      if (item.teams) {
        item.teams.forEach((teamItem: any) => {
          catPermissionListItem.teamIds.push(teamItem.teamId);
        });
      }
    
    }
  });
  categoriesPermissions.value = categoriesPermissionsCopy;
  
  updateFormSchemaElement({
    name: "categoriesPermissions",
    categoriesPermissions: categoriesPermissions.value,
  });
};
const handleSurveyTeamsChange = async (val: any, item: any[]): Promise<void> => {
  await assignToCategoriesPermissionsState(item);
};
const onLoadItems = async (): Promise<void> => {
  await props.items.forEach((item: any) => {
    item.menuProps = {
      disabled: item.length >= maxTeamsSelectedAmount.value,
    };
  });
};

// watch(() => bundleSetting.value.teams, (newVal) => 
// { if (!newVal) { bundleSetting.value.teams = []; } });

// watch(() => props.items, (newVal) => 
//   { newVal.forEach((item: any) => { if (!item.teams) { item.teams = []; } item.menuProps.disabled = item.teams.length >= maxTeamsSelectedAmount.value; }); }, 
// { deep: true });
// computed
const show = computed({
  get() {
    return props.visible;
  },
  set(value) {
    if (!value) {
      emits("close");
    }
  },
});

// hooks

onBeforeMount(async () => { });

</script>
