<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-dialog v-model="show" :style="`min-width: 800px; max-width: 1000px;`">
    <q-card>
      <q-card-section>
        <q-page-container dense>
          <q-row class="row">
            <q-col class="col-12">
              <q-icon color="primary" name="mdi-alert-circle-outline"></q-icon>
              この項目を閲覧できるチームを選択してください。複数選択可能です。
            </q-col>
          </q-row>
          <q-row class="row">
            <q-col class="col-12 q-mt-lg">
              <q-select use-input v-model="item.teams" :options="filteredTeams" option-label="teamName"
                option-value="teamId" use-chips stack-label input-debounce="0"
                :counter="surveyTeamsProperties.maxSelectedAmount" :menu-props="surveyTeamsProperties.menuProps"
                return-object dense small multiple clearable @filter="itemFilter" @update:model-value="handleSurveyTeamsChange"></q-select>
            </q-col>
          </q-row>
        </q-page-container>
      </q-card-section>
      <q-card-actions>
        <q-space></q-space>
        <q-btn v-if="false" color="primary" class="q-mx-sm">閲覧設定</q-btn>
        <q-btn color="grey" text @click="show = false" class="q-ma-sm">閉じる</q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { PropType, ref, computed, onBeforeMount } from 'vue';

import { useFormEditorStore } from '@stores/modules/formEditor/index';

// old imports
// 旧インポート
import { UPDATE_FORM_SCHEMA_ELEMENT } from "@stores/mutation-types";
import { INIT_FORM_SCHEMA, FETCH_FORM_CONFIGS, FETCH_ALL_MEMBER_FORM_CONFIGS } from "@stores/action-types";
import { clone, cloneDeep, isEmpty } from "lodash";
import { useFormsStore } from '@/stores/modules/forms';

const formEditorStore = useFormEditorStore();
const formsStore = useFormsStore();

// emits 
const emits = defineEmits<{
  (event: 'close'): void;
}>();


const itemFilter = (val: string, update: any) => {
  if (!val) {
    update(() => {
      return props.surveyTeams;
    });
  }
  
  update(() => {
    filteredTeams.value = props.surveyTeams.filter((item: any) => {
      return item.teamName.toLowerCase().indexOf(val.toLowerCase()) !== -1;
    });
    return;
  });
};

// props
const props = defineProps({
  surveyTeams: Array as PropType<any>,
  visible: Boolean as PropType<boolean>,
  item: Object as PropType<any>,
  model: Object as PropType<any>,
  sectionKey: String as PropType<string>,
});

// data
const categoriesPermissionsOriginal = ref<any>([]);
const surveyTeamsProperties = ref<any>({
  maxSelectedAmount: 10,
  menuProps: { disabled: false },
});

const filteredTeams = ref<any>([]);

const validate = ref<any>({
  teamSelectRule: [(v: string) => (v && v.length >= 1) || "1つ以上のチームを選択してください。"],
});

// methods
const updateFormSchemaElement = formEditorStore.updateFormSchemaElement;
const fetchFormConfigs = formsStore.fetchFormConfigs;

const handleSurveyTeamsChange = (): void => {
  surveyTeamsProperties.value.menuProps.disabled =
    props.item.teams.length >= surveyTeamsProperties.value.maxSelectedAmount;

  //console.log("handleSurveyTeamsChange", props.item.teams);

  assignToCategoriesPermissionsState();
};
const assignToCategoriesPermissionsState = (): void => {
  categoriesPermissions.value.forEach((categoriesPermission: any) => {
    let catPermissionListItem = categoriesPermission.list.find(
      (categoriesPermissionListItem: any) => categoriesPermissionListItem.categoryId === props.item.id
    );
    //console.log("catPermissionListItem", catPermissionListItem);
    if (catPermissionListItem) {
      catPermissionListItem.teamIds = [];
      props.item.teams.forEach((teamItem: any) => {
        catPermissionListItem.teamIds.push(teamItem.teamId);
      });
    }
  });


/*    console.log("assign", {
    key: props.sectionKey,
    name: "categoriesPermissions",
    categoriesPermissions: categoriesPermissions.value,
  }); */

  updateFormSchemaElement({
    key: props.sectionKey,
    name: "categoriesPermissions",
    categoriesPermissions: categoriesPermissions.value,
  });

  //console.log("formSchema", formSchema.value);
};

// computed
const formSchema = computed(() => formEditorStore.formSchema);

const show = computed({
  get() {
    return props.visible;
  },
  set(value) {
    if (!value) {
      emits("close");
    }
  },
});

const categoriesPermissions = computed(() => {
  return formSchema.value[0].categoriesPermissions;
});

// hooks

onBeforeMount(async () => {
  filteredTeams.value = cloneDeep(props.surveyTeams);

  //console.log("categoriesPermissions", categoriesPermissions.value);
  if (props.item && props.item.teams) {
    surveyTeamsProperties.value.menuProps.disabled = props.item.teams.length >= surveyTeamsProperties.value.maxSelectedAmount;
  }
});

</script>
