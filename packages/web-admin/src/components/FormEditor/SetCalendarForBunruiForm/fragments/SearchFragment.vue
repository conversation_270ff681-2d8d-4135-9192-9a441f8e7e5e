<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <div class="q-pa-md">
      <q-row class="row">
        <q-col class="col-4 q-px-none q-my-none">
          <q-row class="row">
            <q-col class="col-4">
              <div class="body-2 text-blue-grey text-bold">分類権限設定</div>
            </q-col>
            <q-col class="col-1 text-blue-grey-6">
              <q-icon name="mdi-information-outline" :style="`font-size: 14px;`">
                <q-tooltip anchor="bottom right">
                  <span>分類ごとに閲覧権限を設定する場合、以下より対象の分類を選択し、権限付与するチームを設定してください。</span>
                </q-tooltip>
              </q-icon>
            </q-col>
          </q-row>
        </q-col>
      </q-row>
      <q-row class="q-pb-none q-mb-none row q-pt-md full-width">
        <q-col class="col-8 q-pa-none q-my-none">
          <q-select use-input v-model="categoryLarges" :options="categoryLargesOptions" :rules="validate.categoryLarges"
            option-label="name" option-value="name" return-object dense multiple outlined
            :readonly="bunruiItemSchema && bunruiItemSchema.reservationCheckBox ? true : false"
            @update:model-value="updFormSchemaElement"
            placeholder="大分類を選択"></q-select>
        </q-col>
        <q-col class="col-2">
          <q-btn color="primary" class="q-ml-md" @click="handleSearch" :disable="searchButtonDisabled">
            検索
          </q-btn>
        </q-col>
        <q-col class="col-1 q-py-none flex flex-end">
          <q-btn @click="handleFetchCategories" text fab small class="q-mx-xs self-end" v-if="false">
            <q-icon color="primary" name="mdi-cached"></q-icon>
          </q-btn>
        </q-col>
      </q-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onBeforeMount } from 'vue';

import { useFormEditorStore } from '@stores/modules/formEditor';

// old imports
// 旧インポート
/*import { ACTION_SET_CALENDAR_DATA_OF_CATEGORIES_TREE_1, FETCH_FORM_CONFIGS } from "@/store/action-types";
import { UPDATE_FORM_SCHEMA_ELEMENT } from "@/store/mutation-types";
import { SHOW_CONTENT_MODE, CALENDAR_SETTING_KEY } from "@/store/modules/calendar/calendar.constants";*/
import { cloneDeep, isEmpty } from "lodash";
import { useFormsStore } from '@/stores/modules/forms';
import { CfnPolicy } from 'aws-cdk-lib/aws-iam';

const formEditorStore = useFormEditorStore();
const formsStore = useFormsStore();

// emits 
const emits = defineEmits<{
  (event: 'handleSearch', payload: any): void;
}>();

// props

const props = defineProps({
  sectionKey: String,
});

// data
const bunruiItemSchema = ref<any>(null);
const categoryLarges = ref<any>([]);
//const categoriesPermissions = ref<any>([]);
const categoriesPermissionsOriginal = ref<any>([]);
const validate = ref<any>({
  categoryLarges: [(v: any) => !!v || "1つの大分類を選択してください。"],
});

// methods
const updateFormSchemaElement = formEditorStore.updateFormSchemaElement;
const fetchCalendarCategoriesTree = formEditorStore.setCalendarDataOfCategoriesTree1;
const fetchFormConfigs = formsStore.fetchFormConfigs;
const handleSearch = async (): Promise<void> => {
  await emits("handleSearch", categoryLarges.value);
};
const handleFetchCategories = async (): Promise<void> => {
  fetchCategories();
};

const updFormSchemaElement = async (value: any): Promise<void> => {
  categoryLarges.value = value;
  await mergeCategoriesTreeToCategoriesPermissions();
};

const fetchCategories = async (): Promise<void> => {  
  if (
    categoriesTreeFull.value === undefined ||
    isEmpty(categoriesTreeFull.value) ||
    (categoriesTreeFull.value && categoriesTreeFull.value.tree && categoriesTreeFull.value.tree.length === 0)
  ) {
    await fetchCalendarCategoriesTree(null);
  }
};
const loadCategoryLarges = async (): Promise<void> => {
  categoryLarges.value = [];
  categoriesPermissions.value.forEach((categoriesPermission: any) => {
    let categoryLargesOption = categoryLargesOptions.value.find(
      (catLargeOption: any) => catLargeOption.name === categoriesPermission.categoryLarge
    );
    if (categoryLargesOption) {
      categoryLarges.value.push(categoryLargesOption);
    }
  });
};
const mergeCategoriesTreeToCategoriesPermissions = async (): Promise<void> => {
  if (categoriesTreeFull.value.tree) {
    let tmpCategoriesLarges: any[] = [];
    let searchCategoryLarges = cloneDeep(categoryLarges.value);
    categoriesTreeFull.value.tree.forEach((largeCategoryItem: any) => {
      let searchCategoryLarge = searchCategoryLarges.find(
        (categoryLarge: any) => largeCategoryItem.name === categoryLarge.name
      );
      if (searchCategoryLarge) {
        tmpCategoriesLarges.push(largeCategoryItem);
      }
    });

    let tmpCategoriesIdsList: any[] = [];
    tmpCategoriesLarges.forEach((categoriesLarge) => {
      //console.log("categoriesLarge", categoriesLarge);
      let tmpCategoriesId: any = { categoryLarge: categoriesLarge.name };
      tmpCategoriesIdsList.push(tmpCategoriesId);
      if (categoriesLarge.children) {
        tmpCategoriesId.list = [];
        categoriesLarge.children.forEach((categoriesMedium: any) => {
          if (categoriesMedium.id) {
            tmpCategoriesId.list.push({ categoryId: categoriesMedium.id, teamIds: [] });
          }
          if (categoriesMedium.children) {
            categoriesMedium.children.forEach((categoriesSmall: any) => {
              tmpCategoriesId.list.push({ categoryId: categoriesSmall.id, teamIds: [] });
            });
          }
        });
      }
    });

    categoriesPermissions.value = cloneDeep(formSchema.value[0].categoriesPermissions);
    let permissions = []
    if (categoriesPermissions.value && categoriesPermissions.value.length > 0) {
      let categoriesPermissions2 = cloneDeep(categoriesPermissions.value);

      categoriesPermissions2.forEach((categoriesPermission: any) => {
        const tmpLargeCategoriesIdsItem = tmpCategoriesIdsList.filter(categoriesIdItem => {
          return categoriesPermissions2.some(permission => permission.categoryLarge === categoriesIdItem.categoryLarge);
        });

        if (tmpLargeCategoriesIdsItem && tmpLargeCategoriesIdsItem.list) {
          tmpLargeCategoriesIdsItem.list.forEach((categoriesIdsItem: any) => {
            categoriesIdsItem.teamIds = [];
            let tmpCategoriesPermissionCategoryItem = categoriesPermission.list.find(
              (categoriesPermissionCategoryItem: any) => {
                return categoriesIdsItem.categoryId === categoriesPermissionCategoryItem.categoryId
              } 
            );

            
            if (tmpCategoriesPermissionCategoryItem && tmpCategoriesPermissionCategoryItem.teamIds) {
              categoriesIdsItem.teamIds = tmpCategoriesPermissionCategoryItem.teamIds;
            }
          });
        }
      });
      permissions = categoriesPermissions2
    } 
    categoriesPermissionsOriginal.value = cloneDeep(categoriesPermissions.value);
    
    updateFormSchemaElement({
      name: "categoriesPermissions",
      categoriesPermissions: permissions
    })
  }
};

// computed
const formSchema = computed(() => formEditorStore.formSchema);
const categoriesTreeFull = computed(() => formEditorStore.categoriesTree);
const categoriesTreeLarge = computed(() => formEditorStore.categoriesTreeLarge);
const categoriesTreeLargeTitle = computed(() => formEditorStore.categoriesTree?.display?.tag1 ?? null);
const searchButtonDisabled = computed({
  get(): any {
    let categoryLargeFixedChecked =
      bunruiItemSchema.value && bunruiItemSchema.value.reservationCheckBox ? true : false;
    let status = isEmpty(categoryLarges.value) || categoryLargeFixedChecked;
    return status;
  },
  set(value: any) {
    return value;
  },
});

const categoryLargesOptions = computed((): any => {
  return categoriesTreeLarge.value;
});

// watch
watch(() => formSchema.value, (argument) => {
  if (
    formSchema.value &&
    formSchema.value[0] &&
    formSchema.value[0].surveySchema &&
    Array.isArray(formSchema.value[0].surveySchema)
  ) {
    formSchema.value[0].surveySchema.forEach((schema: any) => {
      if (schema.type === "reservation") {
        bunruiItemSchema.value = schema;
        return;
      }
    });
  }
});
watch(() => bunruiItemSchema.value, () => {
  if (
    formSchema.value &&
    formSchema.value[0] &&
    formSchema.value[0].surveySchema &&
    Array.isArray(formSchema.value[0].surveySchema)
  ) {
    if (bunruiItemSchema.value) {
      if (bunruiItemSchema.value.reservationCheckBox === true && bunruiItemSchema.value.selectedLargeCategory) {
    /*  categoryLarges.value = []; 
        categoryLargesOptions.value.forEach((catLargeOption: any) => {
          if (catLargeOption.name === bunruiItemSchema.value.selectedLargeCategory.name) {
            categoryLarges.value.push(catLargeOption);
          }
        }); */
        

        handleSearch();
      }
    }
  }
});
watch(() => categoryLarges.value,
  async () => {
    await mergeCategoriesTreeToCategoriesPermissions();
  });

const categoriesPermissions = computed({
  get(): any {
    return formSchema.value[0].categoriesPermissions;
  },
  set(value: any) {
    formSchema.value[0].categoriesPermissions = value;
  },
})
// hooks

onBeforeMount(async () => {
  await fetchCategories();
  if (!("categoriesPermissions" in formSchema.value[0]) || isEmpty(formSchema.value[0].categoriesPermissions)) {
    fetchFormConfigs();
    if (!("categoriesPermissions" in formSchema.value[0]) || isEmpty(formSchema.value[0].categoriesPermissions)) {
      updateFormSchemaElement({
        key: props.sectionKey,
        name: "categoriesPermissions",
        categoriesPermissions: []
      });
    }
  }
  categoriesPermissionsOriginal.value = cloneDeep(categoriesPermissions.value);
  await loadCategoryLarges();
  if (formSchema.value[0].surveySchema) {
    formSchema.value[0].surveySchema.forEach((schema: any) => {
      if (schema.type === "reservation") {
        bunruiItemSchema.value = schema;
        return;
      }
    });
  }
});

defineExpose({
  mergeCategoriesTreeToCategoriesPermissions
});

</script>