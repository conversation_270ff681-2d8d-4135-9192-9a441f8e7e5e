<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-row class="row">
    <div class="q-py-sm full-width q-pa-md" fluid>
      <div>
        <q-toolbar dence class="q-pa-none q-ma-none" elevation="0" :style="`height: 20px`">
          <q-toolbar-title class="registFixedMsg text-blue-grey-8 text--darken-4 text-bold" > 登録中の定型文 </q-toolbar-title>
        </q-toolbar>
        <q-toolbar dence class="q-pt-sm registFixedPair flex justify-between" v-if="deliveryMessageSettingList
        !=null" elevation="0" height="auto">
          <div>
            <q-chip clickable v-for="( item, index ) in deliveryMessageSettingList" :key="index" :class="(tab==index&&!notSetTitle) ? 'active-chip':''" @click="dispDelivery(index)">{{ item.deliveryTitle }}</q-chip>
            <q-chip class="q-mr-sm success" v-if="deliveryMessageSettingList==null||(deliveryMessageSettingList.length < 5&&notSetTitle)">タイトル未設定</q-chip>
          </div>
          <q-btn color="primary" block :style="`height: 36px`" @click="newDeliveryMsg()" :disable="notSetTitle||deliveryMessageSettingList.length>=5">
            <q-icon name="mdi-image-plus" left></q-icon>定型文を追加
          </q-btn>
        </q-toolbar>
        <q-toolbar dence class="q-pt-sm registFixedPair flex justify-between" v-else elevation="0">
          <span v-if="!deliveryMsgFlag" class="registFixedMsg text-blue-grey-8 text--darken-4" block>定型文が登録されていません。<br><span class="text-bold">定型文を追加ボタン</span>から登録してください。</span>
          <div column v-else>
            <q-chip class="q-mr-sm primary white--text">タイトル未設定</q-chip>
          </div>
          <q-btn color="primary" block :style="`height: 36px`" @click="deliveryMsgFlag=true;tab=`delivery-message`">
            <q-icon name="mdi-image-plus" left></q-icon>定型文を追加
          </q-btn>
        </q-toolbar>
        
        <q-separator v-if="deliveryMessageSettingList" class="q-mt-md q-mx-md"></q-separator>
        <q-tab-panels animated v-model="tab" v-if="tab!=undefined||deliveryMessageSettingList" key="tab" class="flex">
          <q-tab-panel :name="index" v-for="( item, index ) in deliveryMessageSettingList" :key="index" class="full-width" > 
            <q-card-section class="q-pt-md q-pb-md">
              <div class="body-2 text-blue-grey-8 text-bold q-mb-sm">定型文タイトル</div>
              <q-input
                  outlined
                  single-line
                  placeholder="定型分のタイトルを入力してください。"
                  v-model="deliveryTitle"
                  counter
                  maxlength="15"
                  :rules="validateTitle"
                  class="msgBox"
                  hint="文字数上限15文字です。このタイトルは送信されません。"
                >
              </q-input>
              <div
                v-if="validateTitle && (validateTitle.length > 0)"
                class="error--text"
              >{{validateTitle[0]}}</div>
            </q-card-section>
            <q-card-section class="pb-0">
              <div class="body-2 text-blue-grey-8 text-bold q-mb-sm">メッセージ</div>
              <q-input type="textarea"
                v-model="deliveryMessage"
                outlined
                clearable
                counter
                maxlength="5000"
                class="msgBox"
                placeholder="メッセージを入力してください。"
                hint="文字数上限は5000文字です。"
              >
              </q-input>
            </q-card-section>
          </q-tab-panel>
        </q-tab-panels>
        <q-tab-panels v-model="tab" v-if="deliveryMessageSettingList==null && deliveryMsgFlag" key="newDeliveryMessage">
          <q-tab-panel name="delivery-message">
            <q-card-section class="q-pt-md q-pb-md">
              <div class="body-2 text-blue-grey-8 text-bold q-mb-sm">定型文タイトル</div>
              <q-input
                  outlined
                  single-line
                  placeholder="定型分のタイトルを入力してください。"
                  v-model="deliveryTitle"
                  counter
                  maxlength="15"
                  :rules="validateTitle"
                  class="msgBox"
                  hint="文字数上限15文字です。このタイトルは送信されません。"
                >
              </q-input>
              <div
                v-if="validateTitle && (validateTitle.length > 0)"
                class="error--text"
              >{{validateTitle[0]}}</div>
            </q-card-section>
            <q-card-section class="q-pb-none">
              <div class="body-2 text-blue-grey-8 text-bold q-mb-sm">メッセージ</div>
              <q-input type="textarea"
                v-model="deliveryMessage"
                outlined
                clearable
                counter
                maxlength="5000"
                class="msgBox"
                placeholder="メッセージを入力してください。"
                hint="文字数上限は5000文字です。"
              >
              </q-input>
            </q-card-section>
          </q-tab-panel>
        </q-tab-panels>
        <q-card-actions class="q-px-md">
          <q-row class="row full-width">
            <q-space></q-space>
            <q-col cols="auto q-px-none">
              <q-btn
                color="red"
                outline
                class="q-px-sm"
                block
                elevation="0"
                :style="`width: 63px; height: 44px`"
                @click="onDeleteDeliveryMessage"
                v-if="deliveryMessageSettingList!=null&&!notSetTitle"
              >削除</q-btn>
            </q-col>
            <q-col class="q-pl-sm flex justify-end">
              <q-btn
                v-if="newDeliveryMessage||deliveryMessageSettingList!=null||deliveryMsgFlag"
                color="primary"
                block
                elevation="0"
                @click="onUpdateDeliveryMessage"
                :disable="disableSaveButton"
                class="self-end"
                :style="`width: 63px; height: 44px;
                  ${hasActionPermission('hideButton', 'FormEditor_DeliveryMessageSettingModal_Save')
                    ? hideButtonPermissionStyle()
                    : '' }
                `"
              >
                保存
              </q-btn>
            </q-col>
          </q-row>
        </q-card-actions>
      </div>
    </div>

    <DeliveryMessageSettingDeleteConfirmModal
      :visible="showDeliveryMessageSettingDeleteConfirmModal"
      :sectionKey="surveyId"
      :deliveryMessageKey="deliveryMessageKey"
      :deliveryTitle="deliveryTitle"
      :deliveryMessage="deliveryMessage"
      @cancel="onDeleteModelCancel"
      @close="onDeleteModelClose"
      @isDeliveryMessageInputProperlyAfter="isDeliveryMessageInputProperlyAfter"
    />
  </q-row>
</template>

<script setup lang="ts">
import { PropType, ref,  computed, watch, onMounted, onBeforeMount } from 'vue';
import { useRouter, useRoute } from 'vue-router'; // Import the useRouter function from vue-router

import { useFormEditorStore } from '@stores/modules/formEditor/index';
import { useFormsStore } from '@stores/modules/forms/index';

import { useQuasar } from 'quasar';

import { usePermissionHelper } from '@/mixins/PermissionHelper';

// old imports
// 旧インポート
import { UPDATE_FORM_SCHEMA_DELIVERY_MESSAGE } from "@stores/mutation-types";
import DeliveryMessageSettingDeleteConfirmModal from './DeliveryMessageSettingDeleteConfirmModal.vue';
import { isNullOrEmpty } from "@/utils/stringUtils";
import {
  UPDATE_SURVEY_CONFIG,
  UPDATE_MEMBER_SURVEY_CONFIG,
  FETCH_SURVEY_CONFIGS,
  FETCH_SURVEY_CONFIGS_BY_ID,
} from "@stores/action-types";
import { cloneDeep } from "lodash";
import { useMemberStore } from '@/stores/modules/member';

const { hasActionPermission, hideButtonPermissionStyle } = usePermissionHelper();

// emits 
const emits = defineEmits<{
  (event: 'isDeliveryMessageInputProperlyAfter', payload: any): void;
  (event: 'close'): void;
}>();

const $q = useQuasar();
const router = useRouter(); // Use the useRouter function to get the $router object
const route = useRoute(); // Use the useRoute function to get the $route object

const formEditorStore = useFormEditorStore();
const formsStore = useFormsStore();
const memberStore = useMemberStore();

// props
const props = defineProps({
  visible: Boolean as PropType<boolean>
});

// data
const deliveryMessageKey = ref<number>(0);
const deliveryTitle = ref<string>("");
const deliveryMessage = ref<string>("");
const showDeliveryMessageSettingDeleteConfirmModal = ref<boolean>(false);
const validate = ref<any>({
        title: [],
      });
const deliveryMsgFlag = ref<boolean>(false);
const newDeliveryMessage = ref<boolean>(false);
const notSetTitle = ref<boolean>(false);
const tab = ref<any>(0);

const surveyId = route.params.surveyId as string;

// methods
const updateDeliveryMessage = formEditorStore.updateFormSchemaDeliveryMessage;
const updateSurveyConfig = formsStore.updateSurveyConfig;
const updateMemberConfig = memberStore.updateMemberSurveyConfig;
const fetchSurveyConfigs = formsStore.fetchSurveyConfigs;
const fetchSurveyConfigsById = formsStore.fetchSurveyConfigsById;
const onUpdateDeliveryMessage = (): void => {
      let isValidated = handleValidateTitle();
      if(!isValidated){
        return;
      }
      updateDeliveryMessage({
        key: formSchema.value[0].surveyId,
        deliveryMessageKey: deliveryMessageKey.value,
        deliveryTitle: deliveryTitle.value,
        deliveryMessage: deliveryMessage.value,
      });
      let updateFormSchema = cloneDeep(formSchema.value);
      if(deliveryMessageSettingList.value!=null){
        updateFormSchema[0].deliveryMessageSetting = deliveryMessageSettingList.value;
      }
      onSurveyRegisterConfirm(updateFormSchema);
    };
const onSurveyRegisterConfirm = async (updateFormSchema: any): Promise<void> => {
      let _result = undefined;
      await fetchSurveyConfigsById(formSchema.value[0].surveyId).then(response => {
        updateFormSchema[0].updatedAt = response.data.updatedAt;
      })

      if (isMemberForm.value) {
        _result = await updateMemberConfig(updateFormSchema[0]);
      } else {
        _result = await updateSurveyConfig(updateFormSchema[0]);
      }
      
      if (_result) {
        $q.notify({ message: "設定を保存しました" });
        notSetTitle.value = false;
        emits("isDeliveryMessageInputProperlyAfter", _result);
        deliveryMsgFlag.value = false;      } else {
        if (formsStore.updateSurveyConfigError) {
          $q.notify({ message: formsStore.updateSurveyConfigError, type: "error" });
        }
      }
    };
const showFirstDeliveryMessageInDeliveryMessageSettingList = () => {
      dispDelivery(0);
    };
const onDeleteDeliveryMessage = (): void => {
      showDeliveryMessageSettingDeleteConfirmModal.value = true;
    };
const onDeleteModelCancel = (): void => {
      showDeliveryMessageSettingDeleteConfirmModal.value = false;
    };
const onDeleteModelClose = (): void => {
      showDeliveryMessageSettingDeleteConfirmModal.value = false;
      showFirstDeliveryMessageInDeliveryMessageSettingList();
      emits("close");
    };
const handleValidateTitle = (): boolean => {
      let result = true;
      validateTitle.value = [];
      // 必須
      if(deliveryTitle.value.trim() === ""){
        validateTitle.value = ['タイトルは必須入力です。'];
        return false;
      }
      // 既存なタイトルと同じチェック
      if(deliveryMessageSettingList.value){
        let sameTitleIndex = deliveryMessageSettingList.value.findIndex((deliveryMessageSetting: any) => deliveryMessageSetting.deliveryTitle.value === deliveryTitle.value);
        if ((sameTitleIndex > -1) && (sameTitleIndex !== deliveryMessageKey.value)){
            result = false;
          }
        }
      if(result === false){
        validateTitle.value = ['タイトルが重複しています。'];
      }
      return result;
    };
const isDeliveryMessageInputProperlyAfter = (result: any) => {
      emits("isDeliveryMessageInputProperlyAfter", result);
      deliveryTitle.value = '';
      deliveryMessage.value = '';
      newDeliveryMessage.value = false;
      deliveryMsgFlag.value = false;
    };
const dispDelivery = ( index: number ) => {
      if(deliveryMessageSettingList.value){
        deliveryTitle.value = deliveryMessageSettingList.value[index].deliveryTitle;
        deliveryMessage.value = deliveryMessageSettingList.value[index].deliveryMessage;
        deliveryMessageKey.value = index;
        tab.value = index;
        notSetTitle.value = false;
      }
    };
const newDeliveryMsg = (): void => {
      deliveryMessageKey.value = deliveryMessageSettingList.value!=null ? deliveryMessageSettingList.value.length : 0;
      deliveryTitle.value = '';
      deliveryMessage.value = '';
      newDeliveryMessage.value = true;
      notSetTitle.value = true;

      tab.value = deliveryMessageSettingList.value.length - 1;
    };

// computed
const formSchema = computed(() => formEditorStore.formSchema);
const deliveryMessageSettingList = computed(() => {
  let list = null;
  if (
    formSchema.value &&
    formSchema.value.length > 0 &&
    formSchema.value[0] &&
    "deliveryMessageSetting" in formSchema.value[0] &&
    formSchema.value[0].deliveryMessageSetting.length > 0
  ) {
    list = formSchema.value[0].deliveryMessageSetting;
  }
  return list;
});
const disableSaveButton = computed(() => {
  return isNullOrEmpty(deliveryMessage.value) || isNullOrEmpty(deliveryTitle.value);
});

const validateTitle = computed({
  get() {
    return validate.value.title;
  },
  set(value) {
    validate.value.title = value;
  }
});

const isMemberForm = computed<boolean>((state) => {
  return formEditorStore.formType === "member" ? true : false;
});

const previousPageTab = computed((): string => {
      return isMemberForm.value ? "member-form-list" : "form-list";
    });
const previousPageLocation = computed((): any => {
      return {
        name: "FormCreatePage",
        params: { tab: previousPageTab.value },
      };
    });

// watch
watch(() => tab.value, (val) => {
      if(val == undefined){
        tab.value = 0;
        showFirstDeliveryMessageInDeliveryMessageSettingList();
      }
    }, { deep: true });
watch(() => deliveryMessageSettingList.value, (val) => {
      showFirstDeliveryMessageInDeliveryMessageSettingList();
    }, { deep: true });

// hooks

onMounted(() => {
  if (deliveryMsgFlag.value) {
    showFirstDeliveryMessageInDeliveryMessageSettingList();
  }
});

onBeforeMount(() => {
  deliveryMsgFlag.value = deliveryMessageSettingList.value == null ? false : true;
});

</script>
<style lang="less">

.active-chip {
  background-color: var(--q-primary);
  color: #FFF;
}

.msgBox textarea {
  max-height: 200px;
  overflow-y: auto;
}

.registFixedMsg{
  font-size:14px;
}
.registFixedPair .v-toolbar__content{
  display: flex;
  justify-content: space-between;
  height: auto!important;
  padding-bottom:0;
}
.registFixedPair .v-btn.v-btn--block {
  min-width:151px!important;
  max-width:151px;
}
.msgBox .v-text-field__details{
  padding:0!important;
  margin-top:8px;
}
</style>