<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-row justify="center">
    <q-dialog :fullscreen="hasBunruiItem" :width="hasBunruiItem ? null : 700" v-model="show">
      <q-card>
        <q-banner color="primary" dark height="5"> </q-banner>
        <q-list three-line>
          <q-item>
            <q-item-section avatar>
              <q-icon x-large color="primary">mdi-alert-circle-outline</q-icon>
            </q-item-section>

            <q-item-section>
              <q-item-label>
                この帳票を閲覧できるチームをクリックして選択してください。複数選択可能です。
              </q-item-label>
              <q-col cols="8">
                <q-select use-input v-model="currentTeam" :items="teamList" :error-messages="validateCurrentTeam"
                  item-text="teamName" item-value="teamId" return-object dense multiple></q-select>
              </q-col>
            </q-item-section>
          </q-item>
        </q-list>
        <q-separator></q-separator>

        <SetCalendarForBunruiForm v-if="hasBunruiItem" :surveyTeams="selectedTeams" :sectionKey="sectionKey" />

        <q-separator v-if="hasBunruiItem"></q-separator>

        <q-card-actions class="mb-5">
          <q-space></q-space>
          <q-btn color="primary" :disable="disableUpdate" @click="
            hasActionPermission('hideButton', 'FormEditor_TeamSelectDialog_updateTeamSetting')
              ? ''
              : onUpdateTeamSetting()
            ">
            登録確認
          </q-btn>
          <q-btn color="grey darken-2" outlined @click="show = false"> キャンセル </q-btn>
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-row>
</template>

<script setup lang="ts">
import { PropType, ref, computed, onBeforeMount, onMounted } from 'vue';

import { useFormEditorStore } from '@stores/modules/formEditor/index';
import { useUsersStore } from '@stores/modules/users/index';
import { useFormsStore } from '@stores/modules/forms/index';

import { stringTruncate } from '@/utils/stringUtils';

import { usePermissionHelper } from '@/mixins/PermissionHelper';

const store = useFormEditorStore();
const usersStore = useUsersStore();
const formsStore = useFormsStore();
const formEditorStore = useFormEditorStore();
const { hasActionPermission } = usePermissionHelper();

// old imports
// 旧インポート
/*import { FETCH_TEAM_LIST } from "@/store/modules/users/actions-types";
import { UPDATE_FORM_SCHEMA_TEAM_LIST } from "@/store/mutation-types";
import SetCalendarForBunruiForm from "./SetCalendarForBunruiForm/Index.vue";*/

interface TeamDictionary {
  [key: string]: string;
}

// emits 
const emits = defineEmits<{
  (event: 'updateTeams'): void;
  (event: 'close'): void;
}>();

// props
const props = defineProps({
  visible: Boolean as PropType<boolean>,
  sectionKey: String as PropType<string>
});

// data
const currentTeam = ref<any>(null);
const teamSelectRule = ref<any>([(v: string) => (v && v.length >= 1) || "1つ以上のチームを選択してください。"]);
const disableUpdate = ref<boolean>(false);

// methods
const updateSurveyTeams = store.updateFormSchemaTeamList;
const fetchTeamList = usersStore.fetchTeamList;
const onUpdateTeamSetting = (): void => {
  updateSurveyTeams({
    key: props.sectionKey,
    surveyTeams: currentTeam.value,
  });
  emits("updateTeams");
};
const fetchTeamListData = async () => {
  await fetchTeamList();
};
const getBytesLength = (string: string): number => {
  return new TextEncoder().encode(string).length;
};
const ruleLinkedTeams = (currentTeams: any): any => {
  // Rule for checking if required teams from linked survey configs are not selected
  // Only applicable for member forms
  let result: any = true;
  disableUpdate.value = false;

  if (isMemberForm.value) {

    let requiredTeams = [];

    for (const teamId in linkedFormsTeams) {
      let hasTeam = currentTeams.some((team: any) => {
        return team.teamId === teamId;
      });

      if (!hasTeam) {
        requiredTeams.push(linkedFormsTeams.value[teamId]);
      }
    }

    if (requiredTeams.length > 0) {
      result = "連携された帳票からチームも選択する必要があります。 不足しているチームは「" + requiredTeams.join(', ') + "」。";
      disableUpdate.value = true;
    }
  }

  return result;
};

// computed
const formSchema = computed(() => formEditorStore.formSchema);
const teamListData = computed(() => usersStore.teamList.value);
const isMemberForm = computed((state) => {
  return (state as any).formEditor.formType === "member" ? true : false;
});
const surveyConfigs = computed(() => formsStore.formConfigs);
const show = computed({
  get() {
    return props.visible;
  },
  set(value) {
    if (!value) {
      emits("close");
    }
  },
});
const selectedTeams = computed(() => {
  let result = [];
  if (currentTeam.value && Array.isArray(currentTeam.value) && currentTeam.value.length > 0) {
    currentTeam.value.forEach((team) => {
      teamList.value.forEach((fullTeamItem: any) => {
        if (team.teamId === fullTeamItem.teamId) {
          result.push(team);
          return;
        }
      });
    });
  }

  if (result.length === 0) {
    result = teamList.value;
  }

  return result;
});
const teamList = computed((): any => {
  let $this = this;
  return teamListData.value.map((x: any) => {
    let teamName = x.teamName;
    if (teamName !== undefined && getBytesLength(teamName) > 50) {
      teamName = stringTruncate(x.teamName, 50).concat("...");
    }
    return { teamId: x.teamId, teamName: teamName };
  });
});
const hasBunruiItem = computed(() => {
  let result = false;
  if (
    formSchema.value &&
    formSchema.value[0] &&
    formSchema.value[0].surveySchema &&
    Array.isArray(formSchema.value[0].surveySchema)
  ) {
    formSchema.value[0].surveySchema.forEach((schema: any) => {
      if (schema.type === "reservation") {
        return true;
      }
    });
  }
  return false;
});
const linkedForms = computed((): Array<any> => {
  let linked_forms = [];

  // Get linked IDs from state then filter list of formConfigs
  // Only applicble for member forms
  if (isMemberForm.value) {
    //let linkedFormIDs.value = linkedFormIDs.value;

    linked_forms = surveyConfigs.value.filter((config: any) => {
      return linkedFormIDs.value.has(config.surveyId);
    });
  }

  return linked_forms;
});
const linkedFormIDs = computed((): Set<any> => {
  return formEditorStore.step1SurveyFormat().linkedForms.value ? new Set(formEditorStore.step1SurveyFormat().linkedForms.value) : new Set();
});
const linkedFormsTeams = computed((): TeamDictionary => {
  // Returns a dictionary of all teams set for linked survey configs
  // Only applicable for member type forms
  // Dictionary key is team ID, mapped value is team name
  let linkedTeams = {} as TeamDictionary;

  if (isMemberForm.value) {
    for (const form of linkedForms.value) {
      if ("surveyTeams" in form) {
        for (const team of form.surveyTeams) {
          linkedTeams[team.teamId] = team.teamName;
        }
      }
    }
  }

  return linkedTeams;
});
const validateCurrentTeam = computed((): any => {
  let returnValue = ruleLinkedTeams(
    currentTeam.value
  );

  if (returnValue === true) {
    returnValue = '';
  }

  return returnValue;
});

// hooks

onMounted(async () => {
  currentTeam.value = "surveyTeams" in formSchema.value[0] ? formSchema.value[0].surveyTeams : [];
  if (!teamListData.value) {
    await fetchTeamListData();
  }
});

</script>
