<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-row class="row">
    <q-dialog v-model="show" :style="`width: 600px`">
      <q-card class="top-border-delete">

        <q-toolbar flat>
          <q-icon size="xl" name="mdi-alert-circle-outline" color="red-9"></q-icon>

          <div class="q-my-md">
            <q-toolbar-title> この項目を削除してもよろしいですか？ </q-toolbar-title>
            <div>
              この項目はすぐ削除されます。この操作は取り消せません。
            </div>
          </div>

          <q-space></q-space>
          <q-btn @click="show = false" flat>
            <q-icon name="mdi-close"></q-icon>
          </q-btn>
        </q-toolbar>

        <q-separator></q-separator>

        <q-card-section>
          <div><strong>タイトル</strong></div>
          {{title}}
        </q-card-section>
        <q-card-section>
          <div><strong>本文</strong></div>
          <div class="line-break">{{message}}</div>
        </q-card-section>
        <q-card-actions class="q-px-md">
          <q-row class="row q-pa-sm q-col-gutter-md full-width justify-end">
            <q-col>
              <q-btn color="red-8" class="q-px-md" block elevation="4"
                @click="onDeleteDeliveryMessage"
              >
                <q-icon name="mdi-trash-can-outline"></q-icon>
                この項目を削除
              </q-btn>
            </q-col>

            <q-col>
              <q-btn color="grey lighten-2" class="q-px-md" block elevation="4" @click="show = false">
                <q-icon name="mdi-close-box-outline" left></q-icon>
                キャンセル
              </q-btn>
            </q-col>

          </q-row>
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-row>
</template>

<script setup lang="ts">
import { PropType,  computed,  } from 'vue';

import { useFormEditorStore } from '@stores/modules/formEditor/index';
import { useQuasar } from 'quasar';
import { useFormsStore } from '@/stores/modules/forms';
import { useMemberStore } from '@/stores/modules/member';

import { useRoute } from 'vue-router';

const formEditorStore = useFormEditorStore();
const formsStore = useFormsStore();
const memberStore = useMemberStore();

const route = useRoute();

// old imports
// 旧インポート
/*import { REMOVE_FORM_SCHEMA_DELIVERY_MESSAGE } from "@/store/mutation-types";
import {
  UPDATE_SURVEY_CONFIG,
  UPDATE_MEMBER_SURVEY_CONFIG,
  FETCH_SURVEY_CONFIGS,
  FETCH_SURVEY_CONFIGS_BY_ID,
} from "@/store/action-types";
import { cloneDeep } from "lodash";*/

// emits 
const emits = defineEmits<{
  (event: 'close'): void;
  (event: 'isDeliveryMessageInputProperlyAfter', payload: any): void;
  (event: 'cancel'): void;
}>();
const $q = useQuasar();

// props
const props = defineProps({
  visible: Boolean as PropType<boolean>,
  sectionKey: String as PropType<string>,
  deliveryMessageKey: Number as PropType<number>,
  deliveryTitle: String as PropType<string>,
  deliveryMessage: String as PropType<string>,
  previousPageLocation: String as PropType<string>,
});

// methods
const updateSurveyConfig = formsStore.updateSurveyConfig;
const updateMemberConfig = memberStore.updateMemberSurveyConfig;
const fetchSurveyConfigs = formsStore.fetchSurveyConfigs;
const fetchSurveyConfigsById = formsStore.fetchSurveyConfigsById;
const removeDeliveryMessage = formEditorStore.removeFormSchemaDeliveryMessage;
const onDeleteDeliveryMessage = (): void => {
      removeDeliveryMessage({
        key: props.sectionKey,
        deliveryMessageKey: props.deliveryMessageKey,
      });
      onSurveyRegisterConfirm();
      emits("close");
    };
const onSurveyRegisterConfirm = async (): Promise<void> => {
      let updateFormSchema: any = [];
      formSchema.value && await fetchSurveyConfigsById(formSchema.value[0].surveyId).then(response => {
        updateFormSchema[0] = response.data;
        updateFormSchema[0].deliveryMessageSetting.splice(props.deliveryMessageKey, 1);
      });

      let _result = undefined;
      if (isMemberForm.value) {
        _result = await updateMemberConfig(updateFormSchema[0]);
      } else {
        _result = await updateSurveyConfig(updateFormSchema[0]);
      }
      
      if (_result) {
        emits("isDeliveryMessageInputProperlyAfter", _result);
        $q.notify({ message: "設定を保存しました" });
      } else {
        if (formsStore.updateSurveyConfigError) {
          $q.notify({ message: formsStore.updateSurveyConfigError, type: "error" });
        }
      }
      emits("close");
    };

// computed

const isMemberForm = computed(() => {
  return formType.value === 'member' ? true : false;
});

const formType = computed(() => {
  return route.params.formType ? route.params.formType : 'survey';
});

import { ref, watch } from "vue";
const formSchema = ref(formEditorStore.formSchema);
watch(() => formEditorStore.formSchema, (newVal) => {
  formSchema.value = newVal;
}, { immediate: true });

const show = computed({
  get() {
    return props.visible;
  },
  set(value) {
    if (!value) {
      emits("cancel");
    }
  },
});
const title = computed(() => {
  return props.deliveryTitle;
});

const message = computed(() => {
  return props.deliveryMessage;
});

const previousPageLocation = computed(() => {
      return {
        name: "FormCreatePage",
        params: { tab: props.previousPageLocation },
      };
    });
</script>

<style scoped>
.line-break{
  white-space: pre-line;
}
</style>