<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-row class="row q-mb-sm q-mt-md">
      <q-col class="col-12">
        <ul ref="itemRadioDrag" class="form-editor-drag">
          <li v-for="(option, index) in props.item.options" :key="option + index" class="row" @mouseenter="hover = index"
            @mouseleave="hover = false">
            <span style="width: 15px" v-if="isEditMode === false" class="q-px-none flex items-center">
              <q-icon v-if="hover === index && isActive" class="radio-drag-handle q-px-none" name="mdi-drag"
                size="sm"></q-icon>
            </span>
            <q-btn @click="setDefaultValue(option)" class="q-py-none q-px-sm" :disable="isFirstVaccination" flat
              :ripple="false">
              <q-icon :name="`${isDefaultValue(option) ? 'mdi-radiobox-marked' : 'mdi-radiobox-blank'}`" size="sm"
                class="self-center"></q-icon>
              <q-tooltip anchor="center right" self="center left" :offset="[2, 2]">
                <span>新規入力時の初期値</span>
              </q-tooltip>
            </q-btn>

            <q-input :key="option + index" class="deactive q-py-none col-8" hide-details :model-value="option"
              :readonly="isReadOnly(index)" @change="(value: any) => onChangeOption(value, index)">
            </q-input>

              <q-btn class="col-2 flex rounded" size="sm" flat rounded @click="removeOption(index)" v-if="isShowRemoveButton(index)">
                <q-icon name="mdi-close" size="sm" class="self-end" color="grey"></q-icon>
              </q-btn>

          </li>
        </ul>
        <q-row v-if="isShowAddOption" :class="{ 'q-mt-sm': !isMultipleOptions }">
          <q-col>
            <div class="q-pt-sm q-pl-sm">
              <q-btn @click="addOption" flat :ripple="false" class="q-pl-none">
                <q-icon name="mdi-radiobox-blank" size="sm" left></q-icon>
                <span :style="`color: var(--q-primary)`">選択肢を追加</span></q-btn>
            </div>
          </q-col>
        </q-row>
      </q-col>
    </q-row>
  </div>
</template>

<script setup lang="ts">
import { PropType, computed, onMounted, ref } from 'vue';
import { useFormEditorStore } from '@stores/modules/formEditor/index';

const formEditorStore = useFormEditorStore();

// old imports
// 旧インポート
import Sortable from "sortablejs";

const hover = ref<any>(false);

// emits 
const emits = defineEmits<{
  (event: 'addOption'): void;
  (event: 'removeOption', payload: any): void;
  (event: 'updateOption', ...payload: any[]): void;
  (event: 'moveOption', ...payload: any[]): void;
}>();

// refs

const itemRadioDrag = ref<HTMLElement | null>(null);

// props
const props = defineProps({
  item: { type: Object as PropType<any>, required: true },
  isActive: { type: Boolean as PropType<boolean>, required: true },
  isEditMode: { type: Boolean as PropType<boolean>, required: true },
  editIndex: { type: Number as PropType<number>, required: false },
  isEdited: { type: Boolean as PropType<boolean>, required: false },
  originalOptionsIndex: { type: Number as PropType<number>, required: true }
});

// methods
const setupDrag = (): void => {
  let list = itemRadioDrag.value;
  const _self = this;
  if (list) {
    Sortable.create(list, {
      handle: ".radio-drag-handle", // Use handle so user can select text
      animation: 150,
      chosenClass: "white",
      onEnd({ newIndex, oldIndex }) {
        emits("moveOption", newIndex, oldIndex);
      },
    });
  }
};
const addOption = (): void => {
  emits("addOption");
  //console.log('addOption', props.item.options);
};
const removeOption = (index: number): void => {
  emits("removeOption", index);
};
const onChangeOption = (value: any, index: number): void => {
  emits("updateOption", index, value);
};
const setDefaultValue = (value: any): void => {
  if (props.item.default === value) {
    props.item.default = null;
    props.item.input = null;
  } else {
    props.item.default = value;
    props.item.input = value;
  }
};
const isDefaultValue = (value: any): boolean => {
  return props.item.default === value;
};
const isReadOnly = (index: number) => {
  const isNotEditableIndex = props.editIndex !== index && props.originalOptionsIndex >= index && props.editIndex !== -1;
  return props.isEditMode && !props.item.isNewItem && isNotEditableIndex;
};
const isShowRemoveButton = (index: number): boolean => {
  if (props.isEditMode) {
    return props.item.isNewItem ? isMultipleOptions.value : index > props.originalOptionsIndex;
  } else {
    return isMultipleOptions.value;
  }
};

// computed
const isMultipleOptions = computed((): boolean => {
  return props.item.options.length > 1;
});
const isShowAddOption = computed((): boolean => {
  if (!props.isActive) {
    return false;
  }

  const originalOptionsLength = props.originalOptionsIndex + 1;
  if (props.isEditMode) {
    return props.item.isNewItem ? true : props.item.options.length === originalOptionsLength;
  }

  return true;
});

const isFirstVaccination = computed(() => {
  const surveySchema = step1SurveyFormat.value.surveySchema;
  const countVaccinesValue = surveySchema.find((item: any) => item.type === 'countVaccines')?.default;
  const isFirst = Number(countVaccinesValue) === 1;
  return isFirst;
});

const step1SurveyFormat = computed(() => formEditorStore.step1SurveyFormat);

// hooks

onMounted(() => {
  setupDrag();
});

</script>

<style lang="less" scoped>
.radio-drag-handle {
  cursor: move;
}
</style>
