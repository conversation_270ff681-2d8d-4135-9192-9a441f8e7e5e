<template>
  <div>
    <div class="row tw-items-center" :class="fileNumRef?.validate() ? 'tw-pb-2' : 'tw-pb-6'">
      <span>最大添付ファイル数</span>
      <q-input 
        v-model="item.maxItems"
        ref="fileNumRef"
        class="tw-ml-3 tw-pb-0 col"
        outlined dense
        type="number"
        :rules="rules"
        :readonly="!isActive"
        @update:model-value="(v) => emit('update', v)"
      />
    </div>
    <span class="text-caption text-grey-7 tw-ml-4">最大添付ファイル数は1から10まで選択可能です。</span>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';

// props
const props = defineProps<{
  modelValue: any,
  isActive: boolean,
  isEditMode: boolean,
}>();

// emit
const emit = defineEmits(['update', 'update:modelValue']);

// data
const fileNum = ref(1);
const fileNumRef = ref();
const rules = ref([
  (v) => !!v || '必須です。',
  (v) => v <= 10 || '10以下の数値を入力してください。',
  (v) => v >= 1 || '1以上の数値を入力してください。',
]);

// computed
const item = computed({
  get: () => { return props.modelValue; },
  set: (newVal) => {
    emit('update:modelValue', newVal);
  }
});
</script>