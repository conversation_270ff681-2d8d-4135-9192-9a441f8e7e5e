<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-row>
    <div fluid class="px-none q-py-none">
      <q-card-section v-if="typeList.length > 0" class="q-py-none q-py-lg">
        <div class="q-my-md">帳票回答受付時のメッセージを設定ください。</div>
        <div class="body-2 blue-grey--text text-bold q-pt-md">ユーザーに送信する内容</div>
        <q-option-group v-model="endOfSurveyMessageType" hide-details="true" class="q-mx-none q-my-none q-py-sm"
          :options="typeListOptions" :disable="!isEditMode">
        </q-option-group>
      </q-card-section>
      <q-card-section>
        <div class="body-2 blue-grey--text text-bold q-py-md">メッセージ</div>
        <div v-for="(item, index) in endOfSurveyMessage" :key="index">
          <q-card class="q-mb-md" outline bordered flat>
            <q-toolbar color="grey lighten-2" flat class="bg-grey-4 justify-between" elevation="0">
              <q-btn-group flat>
                <q-btn @click="item.type = 'text'">
                  <q-icon name="mdi-chat-outline" size="sm"></q-icon>
                </q-btn>

                <q-btn v-if="index != 0" @click="item.type = 'image'">
                  <q-icon name="mdi-image-outline" size="sm"></q-icon>
                </q-btn>

              </q-btn-group>
              <q-btn text flat @click="removeMessage(index)" class="flex justify-end"
                v-if="index != 0 || endOfSurveyMessage.length > 1"
                :disable="index == 0 || endOfSurveyMessage.length === 1">
                <q-icon name="mdi-close"></q-icon>
              </q-btn>
            </q-toolbar>

            <DynamicSlot :current-slot="item.type" class="q-pa-sm">
              <template v-slot:text>

                <q-input textarea v-if="item.type === 'text'" class="q-px-sm q-my-md" placeholder="テキストを入力" outlined
                  :rules="[rules.validTextLength]" v-model="endOfSurveyMessage[index].text" :maxlength="5000" autogrow :class="`st-input`">
                </q-input>
                <div class="text-caption q-ml-sm flex q-pa-sm">
                  {{ endOfSurveyMessage[index].text.length }} / 5000
                </div>
              </template>
              <template v-slot:image v-if="index != 0">
                <div>

                  <input type="file" ref="inputFile" class="hidden" accept="image/png, image/jpeg"
                    @change="onChangeImageMessage($event, index)" />
                  <q-card v-if="item.type === 'image'" class="q-mx-sm q-my-md" bordered flat hide-details>
                    <div fluid class="flex flex-center">
                      <q-btn v-if="!item.previewImageUrl" color="primary" class="q-my-md" elevation="0"
                        @click="uploadImage(index)">写真をアップロード
                      </q-btn>
                      <Alert v-if="item.error" v-model="item.error" class="q-my-md body-2" color="red" type="error"
                        text>
                        {{ item.error }}
                      </Alert>
                      <div v-if="item.previewImageUrl && !item.error" class="flex column full-width">
                        <q-img class="q-my-md" :src="item.previewImageUrl" :style="`max-height: 150px; height 150px; `" fit="contain"></q-img>
                        <q-btn color="warning" class="q-my-sm self-center" elevation="0" :style="`width: 60px;`" @click="removeImage(index)">削除 </q-btn>
                      </div>
                    </div>
                    <div class="text-left q-mb-sm q-ml-md">
                      <q-item-label class="caption">
                        ファイル形式：JPG、JPEG、PNG<br />
                        ファイルサイズ：1MB以下
                      </q-item-label>
                    </div>
                  </q-card>
                </div>
              </template>
            </DynamicSlot>
          </q-card>
        </div>
        <q-btn color="primary" outline @click="addMessage()" :disable="endOfSurveyMessage.length === 3">
          <q-icon name="mdi-plus"></q-icon>
          追加
        </q-btn>
      </q-card-section>
      <q-card-actions class="q-py-lg py-none">
        <q-row class="full-width">
          <q-space></q-space>
          <q-col cols="auto" class="flex justify-end">
            <q-btn class="q-mx-md" color="primary" block elevation="0"
              :disable="isEmptyInTheMiddleEndOfSurveyMessage" @click="onUpdateEndOfSurveyMessage" :style="`height: 44px; width: 63px; ${hasActionPermission('hideButton', 'FormEditor_EndOfSurveyMessageModal_Save')
                ? hideButtonPermissionStyle()
                : ''}`
                ">保存</q-btn>
          </q-col>
        </q-row>
      </q-card-actions>
    </div>
  </q-row>
</template>

<script setup lang="ts">
import { PropType, ref, computed, watch, onBeforeMount } from 'vue';

import { useFormEditorStore } from '@stores/modules/formEditor/index';
import { useQuasar } from 'quasar';

import { useFormsStore } from '@stores/modules/forms/index';

import { useMemberStore } from '@stores/modules/member/index';

import DynamicSlot from '@components/common/DynamicSlot.vue';

// old imports
// 旧インポート
import { cloneDeep, isEmpty, isEqual, isString } from "lodash";

import { usePermissionHelper } from '@/mixins/PermissionHelper';

import {
  DEFAULT_END_OF_SURVEY_MESSAGE,
  END_OF_SURVEY_MESSAGE_TYPE_LIST
} from "@stores/modules/formEditor/formEditor.constants";

const formEditorStore = useFormEditorStore();
const formsStore = useFormsStore();
const memberStore = useMemberStore();

const { hasActionPermission, hideButtonPermissionStyle } = usePermissionHelper();

const typeListOptions = computed(() => {
  return END_OF_SURVEY_MESSAGE_TYPE_LIST.map((item: any) => {
    return {
      label: item.label,
      value: item.value,
    };
  });
});


// emits 
const emits = defineEmits<{
  (event: 'isEndOfSurveyMessageInputProperlyAfter', payload: any): void;
}>();
const $q = useQuasar();

// props
const props = defineProps({
  visible: Boolean as PropType<boolean>,
  sectionKey: String as PropType<string>
});

// data

const inputFile = ref<any>([null]);
const messType = ref("image");

const messageType = ref<any>([{
  slot: "text",
  value: "text",
}, {
  slot: "image",
  value: "image",
}]);

let endOfSurveyMessage = ref<any>([{
  type: "text",
  text: DEFAULT_END_OF_SURVEY_MESSAGE,
  previewImageUrl: "",
  originalContentUrl: "",
  file: null,
  error: null,
}]);
let endOfSurveyMessageType = ref<number>(1);
let typeList = ref<any>([]);
let temporaryEndObSurveyMessage = ref<any>([{
  type: "text",
  text: DEFAULT_END_OF_SURVEY_MESSAGE,
  previewImageUrl: "",
  originalContentUrl: "",
  file: null,
  error: null,
}]);
let temporaryEndOfSurveyMessageType = ref<number>(1);
const rules = ref<any>({
  validTextLength: (value: string) => (value || "").length <= 5000 || "5000文字以内で入力してください",
});
let canSave = ref<boolean>(true);

// methods
const updateSurveyConfig = formsStore.updateSurveyConfig;
const updateMemberConfig = memberStore.updateMemberSurveyConfig;
const fetchSurveyConfigs = formsStore.fetchSurveyConfigs;
const fetchSurveyConfigsById = formsStore.fetchSurveyConfigsById;
const fetchMemberConfigsById = memberStore.fetchMemberFormConfig;
const updateFormSchema = formEditorStore.setFormSchema;
const updateEndOfSurveyMessage = formEditorStore.updateFormSchemaEndOfSurveyMessage;
const onUpdateEndOfSurveyMessage = (): void => {
  if (endOfSurveyMessage.value[0].file) {
    $q.notify({ message: "メッセージの1件目はテキストメッセージを設定してください。", type: "error" });
    endOfSurveyMessage.value[0] = { text: "", type: "text" };
    return
  }
  let type: any = null;
  if (typeList.value.length > 0) {
    type = endOfSurveyMessageType.value;
  }
  updateEndOfSurveyMessage({
    key: formSchema[0].surveyType == 'member' ? formSchema[0].memberSurveyId : formSchema[0].surveyId,
    endOfSurveyMessage: endOfSurveyMessage.value,
    endOfSurveyMessageType: type,
  });
  temporaryEndObSurveyMessage.value = cloneDeep(endOfSurveyMessage.value);
  temporaryEndOfSurveyMessageType.value = endOfSurveyMessageType.value;

  let updateFormSchema = cloneDeep(formSchema);

  updateFormSchema[0].endOfSurveyMessage = endOfSurveyMessage.value;
  updateFormSchema[0].endOfSurveyMessageType = endOfSurveyMessageType.value;
  onSurveyRegisterConfirm(updateFormSchema);
};
const onSurveyRegisterConfirm = async (updateFormSchema: any): Promise<void> => {
  let _result = undefined;
  if (formSchema[0].surveyType == 'member') {
    await fetchMemberConfigsById(formSchema[0].memberSurveyId).then(response => {
      updateFormSchema[0].updatedAt = response.data.updatedAt;
    })
    _result = await updateMemberConfig(updateFormSchema[0]);
  } else {
    await fetchSurveyConfigsById(formSchema[0].surveyId).then(response => {
      updateFormSchema[0].updatedAt = response.data.updatedAt;
    })
    $q.loading.show();
    _result = await updateSurveyConfig(updateFormSchema[0]);
  }
  if (_result) {
    $q.notify({ message: "設定を保存しました" });
    $q.loading.hide();
    emits("isEndOfSurveyMessageInputProperlyAfter", true);
  } else {
    if (formsStore.updateSurveyConfigError) {
      $q.loading.hide();
      $q.notify({ message: `エラー: ${(formsStore.updateSurveyConfigError as any).message}`, type: "error", timeout: 5000 });
    }
  }
};
const onChangeMessageTypes = (value: any, index: number) => {
  //console.log('onChangeMessageTypes', value, index);
  endOfSurveyMessage.value[index] = {
    ...endOfSurveyMessage.value[index],
    type: value,
  };
};
const addMessage = () => {
  endOfSurveyMessage.value.push({ type: "text", text: "" })
};
const removeMessage = (index: number) => {
  endOfSurveyMessage.value = endOfSurveyMessage.value.filter((v: any, i: number) => index !== i);
};
const onChangeImageMessage = (event: any, index: number) => {
  const file = event.target.files[0];
  const totalSizeMB = (file.size / Math.pow(1024, 2));
  let image = {};

  if (totalSizeMB > 1.0) {
    image = {
      originalContentUrl: "",
      previewImageUrl: "",
      file: null,
      error: "1MB 以下のファイルを選んでください。",
    };
  } else {
    if (file.type !== "image/png" && file.type !== "image/jpeg" && file.type !== "image/jpg") {
      image = {
        originalContentUrl: "",
        previewImageUrl: "",
        file: null,
        error: "JPG、JPEG、PNG の写真ファイルを選んでください。",
      };
    } else {
      image = {
        originalContentUrl: URL.createObjectURL(file),
        previewImageUrl: URL.createObjectURL(file),
        file: file,
        error: null,
      };
    }
  }
  endOfSurveyMessage.value[index] = {
    ...endOfSurveyMessage.value[index],
    ...image,
    type: "image",
  };
  inputFile.value[index].value = "";
};
const uploadImage = (index: number) => {
  inputFile.value[index].click();
};
const removeImage = (index: number) => {
  const image = {
    originalContentUrl: "",
    previewImageUrl: "",
    file: null,
    error: null,
  };
  endOfSurveyMessage.value[index] = {
    ...endOfSurveyMessage.value[index],
    ...image,
    type: "image",
  };
};

// computed
const formSchema = formEditorStore.formSchema;
const isEditMode = computed(() => formEditorStore.isEditMode);

const previousPageTab = computed((): string => {
  return formSchema[0].surveyType == 'member' ? "member-form-list" : "form-list";
});
const previousPageLocation = computed((): any => {
  return {
    name: "FormCreatePage",
    params: { tab: previousPageTab.value },
  };
});
const isEmptyInTheMiddleEndOfSurveyMessage = computed((): boolean => {
  if (endOfSurveyMessage.value.length > 1) {
    return endOfSurveyMessage.value.find((obj: any) => obj.type == 'text' && !obj.text) || endOfSurveyMessage.value.find((obj: any) => obj.type == 'image' && (!obj.originalContentUrl || obj.originalContentUrl == "")) ? true : false;
  } else {
    return false;
  }
});

// watch
watch(() => formSchema, (val) => {
  if (
    formSchema &&
    formSchema.length > 0 &&
    formSchema[0] &&
    "endOfSurveyMessage" in formSchema[0]
  ) {
    //console.log('formSchema[0].endOfSurveyMessage', formSchema[0].endOfSurveyMessage);
    let endOfSurveyMessageCopy = cloneDeep(formSchema[0].endOfSurveyMessage);
    if (!Array.isArray(endOfSurveyMessage)) {
      if (isString(endOfSurveyMessage)) {
        endOfSurveyMessageCopy = [{
          type: "text",
          text: endOfSurveyMessage.value
        }];
      } else {
        //console.log('endOfSurveyMessageCopy', endOfSurveyMessageCopy);
        endOfSurveyMessageCopy = [endOfSurveyMessage.value];
      }
    }
    endOfSurveyMessage.value = endOfSurveyMessageCopy;
    temporaryEndObSurveyMessage.value = endOfSurveyMessageCopy;
  } else {
    endOfSurveyMessage.value = [{
      type: "text",
      text: DEFAULT_END_OF_SURVEY_MESSAGE
    }];
    temporaryEndObSurveyMessage.value = [{
      type: "text",
      text: DEFAULT_END_OF_SURVEY_MESSAGE
    }];
  }
  if (formSchema && formSchema.length > 0 && formSchema[0].endOfSurveyMessageType) {
    endOfSurveyMessageType = formSchema[0].endOfSurveyMessageType;
    temporaryEndOfSurveyMessageType.value = formSchema[0].endOfSurveyMessageType;
  } else {
    endOfSurveyMessageType.value = 1;
    temporaryEndOfSurveyMessageType.value = 1;
  }
  typeList.value = [];
  if (
    formSchema &&
    formSchema.length > 0 &&
    formSchema[0].surveySchema &&
    formSchema[0].surveySchema.length > 0
  ) {
    if (!isEqual(temporaryEndObSurveyMessage, endOfSurveyMessage.value)) {
      endOfSurveyMessage.value = cloneDeep(temporaryEndObSurveyMessage);
    }
    const _item = formSchema[0].surveySchema.find((item: any) => item.type === "reservation");
    if (_item) {
      typeList.value = END_OF_SURVEY_MESSAGE_TYPE_LIST;
      if (temporaryEndOfSurveyMessageType.value !== endOfSurveyMessageType.value) {
        endOfSurveyMessageType = temporaryEndOfSurveyMessageType;
      }
    }
  }
});
watch(endOfSurveyMessage.value, (value: any) => {
  canSave.value = value.map((message: any) => {
    if (message.type === "text" && rules.value.validTextLength(message.text) === true) {
      return true;
    }
    if (message.type === "image" && !isEmpty(message.previewImageUrl) && !isEmpty(message.originalContentUrl)) {
      return true;
    }
    return false;
  }).some((valid: boolean) => valid === false) === false;
}, { deep: true });

// hooks

onBeforeMount(() => {
  if (formSchema && formSchema.length > 0 && formSchema[0] && "endOfSurveyMessage" in formSchema[0]) {
    let endOfSurveyMessage = cloneDeep(formSchema[0].endOfSurveyMessage);
    if (!Array.isArray(endOfSurveyMessage)) {
      if (isString(endOfSurveyMessage)) {
        endOfSurveyMessage = [{
          type: "text",
          text: endOfSurveyMessage
        }];
      } else {
        endOfSurveyMessage = [endOfSurveyMessage];
      }
    }
    endOfSurveyMessage = endOfSurveyMessage;
    temporaryEndObSurveyMessage.value = endOfSurveyMessage;
  } else {
    endOfSurveyMessage.value = [{
      type: "text",
      text: DEFAULT_END_OF_SURVEY_MESSAGE
    }];
    temporaryEndObSurveyMessage.value = [{
      type: "text",
      text: DEFAULT_END_OF_SURVEY_MESSAGE
    }];
  }
  if (formSchema && formSchema.length > 0 && formSchema[0].endOfSurveyMessageType) {
    endOfSurveyMessageType = formSchema[0].endOfSurveyMessageType;
    temporaryEndOfSurveyMessageType.value = formSchema[0].endOfSurveyMessageType;
  } else {
    endOfSurveyMessageType.value = 1;
    temporaryEndOfSurveyMessageType.value = 1;
  }
  if (formSchema && formSchema.length > 0 && formSchema[0].surveySchema && formSchema[0].surveySchema.length > 0) {
    if (temporaryEndObSurveyMessage.value !== endOfSurveyMessage.value) {
      endOfSurveyMessage.value = cloneDeep(temporaryEndObSurveyMessage.value);
    }
    const _item = formSchema[0].surveySchema.find((item: any) => item.type === "reservation");
    if (_item) {
      typeList.value = END_OF_SURVEY_MESSAGE_TYPE_LIST;
      if (temporaryEndOfSurveyMessageType.value !== endOfSurveyMessageType.value) {
        endOfSurveyMessageType = temporaryEndOfSurveyMessageType;
      }
    }
  }
  //console.log('endOfSurveyMessage', endOfSurveyMessage.value);
});

</script>

<style lang="less">
.st-input textarea {
    max-height: 200px !important; /* Enforce max height */
    overflow-y: auto !important;  /* Add scroll behavior if content exceeds max height */
  }
</style>