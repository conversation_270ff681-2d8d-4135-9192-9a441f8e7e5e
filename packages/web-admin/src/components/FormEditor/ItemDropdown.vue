<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-row no-gutters class="q-mb-xl q-mt-md">
      <q-col class="col-12">
        <ul ref="itemDropdownDrag" class="form-editor-drag">
          <li v-for="(option, index) in item.options" :key="option + index" @mouseenter="hover = index"
            @mouseleave="hover = false" class="row">
            <span style="width: 15px" v-if="isEditMode === false" class="col-1 flex items-center">
              <q-icon v-if="hover === index && isActive" class="dropdown-drag-handle q-pl-xs" name="mdi-drag" size="sm"></q-icon>
            </span>
            <span class="q-pt-xs col-1 flex items-center justify-center" @click="setDefaultValue(option)" >
              <span :class="{
                'item-dropdown-default': isDefaultValue(option),
              }">
                {{ index + 1 }}.
              </span>
              <q-tooltip anchor="center right">
                <span>新規入力時の初期値</span>
              </q-tooltip>  
            </span>
            <q-input :key="option + index" class="deactive q-pt-xs col-8" hide-details :model-value="option"
              @change="(value: any) => onChangeOption(value, index)" :readonly="isReadOnly(index)">
          
            </q-input>
              <q-btn class="col-2 flex rounded" size="sm" flat rounded @click="removeOption(index)"
              v-if="isShowRemoveButton(index)">
              <q-icon name="mdi-close" size="sm" class="self-end" color="grey"></q-icon>
            </q-btn>

          </li>
        </ul>

        <q-row no-gutters v-if="isShowAddOption" :class="{ 'mt-2': !isMultipleOptions }">
          <q-col>
            <div class="q-pt-sm">
              <q-btn :style="`color: var(--q-primary)`" @click="addOption" class="q-px-md flex items-center justify-center" flat>
                <span class="q-pt-xs q-pr-md">{{ item.options.length + 1 }}. </span>
                選択肢を追加</q-btn>
            </div>
          </q-col>
        </q-row>
      </q-col>
    </q-row>
  </div>
</template>

<script setup lang="ts">
import { PropType, computed, h, onMounted, ref } from 'vue';

// old imports
// 旧インポート
import Sortable from "sortablejs";

// emits 
const emits = defineEmits<{
  (event: 'addOption'): void;
  (event: 'removeOption', payload: any): void;
  (event: 'updateOption', ...payload: any[]): void;
  (event: 'moveOption', ...payload: any[]): void;
}>();

// refs

const hover = ref<any>(false);

const itemDropdownDrag = ref<HTMLElement | null>(null);

// props
const props = defineProps({
  item: { type: Object as PropType<any>, required: true },
  isActive: { type: Boolean as PropType<boolean>, required: true },
  isEditMode: { type: Boolean as PropType<boolean>, required: true },
  editIndex: { type: Number as PropType<number>, required: false },
  originalOptionsIndex: { type: Number as PropType<number>, required: true }
});

// methods
const setupDrag = (): void => {
  let list = itemDropdownDrag.value;
  const _self = this;
  if (list) {
    Sortable.create(list, {
      handle: ".dropdown-drag-handle", // Use handle so user can select text
      animation: 150,
      chosenClass: "white",
      onEnd({ newIndex, oldIndex }) {
        emits("moveOption", newIndex, oldIndex);
      },
    });
  }
};
const addOption = (): void => {
  emits("addOption");
};
const removeOption = (index: number): void => {
  emits("removeOption", index);
};
const onChangeOption = (value: any, index: number): void => {
  emits("updateOption", index, value);
};
const setDefaultValue = (value: any): void => {
  if (props.item.default === value) {
    props.item.default = null;
    props.item.input = null;
  } else {
    props.item.default = value;
    props.item.input = value;
  }
};
const isDefaultValue = (value: any): boolean => {
  return props.item.default === value;
};
const isReadOnly = (index: number) => {
  const isNotEditableIndex = props.editIndex !== index && props.originalOptionsIndex >= index && props.editIndex !== -1;
  return props.isEditMode && !props.item.isNewprops && isNotEditableIndex;
};
const isShowRemoveButton = (index: number): boolean => {
  if (props.isEditMode) {
    return props.item.isNewprops ? isMultipleOptions.value : index > props.originalOptionsIndex;
  } else {
    return isMultipleOptions.value;
  }
};

// computed
const isMultipleOptions = computed((): boolean => {
  return props.item.options.length > 1;
});
const isShowAddOption = computed((): boolean => {
  if (!props.isActive) {
    return false;
  }

  const originalOptionsLength = props.originalOptionsIndex + 1;
  if (props.isEditMode) {
    return props.item.isNewprops ? true : props.item.options.length === originalOptionsLength;
  }

  return true;
});

// hooks

onMounted(() => {
  setupDrag();
});

</script>

<style lang="less" scoped>
.dropdown-drag-handle {
  cursor: move;
}

.item-dropdown-default {
  border-bottom: 3px solid var(--q-primary);
  font-weight: 500;
}
</style>
