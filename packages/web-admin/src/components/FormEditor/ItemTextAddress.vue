<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-row no-gutters class="mb-8 pl-6">
      <q-col cols="12" md="12">
        <q-input
          type="tel"
          hide-details="true"
          :disable="!isActive"
          v-model="item.default"
          :maxlength="item.length.limitation || 300"
          counter
          placeholder="住所・デフォルト（任意）"
        ></q-input>
      </q-col>
    </q-row>
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';


// props
const props = defineProps({
  item: { type: Object as PropType<any>, required: true },
  isActive: { type: <PERSON><PERSON><PERSON> as PropType<boolean>, required: true }
});
</script>