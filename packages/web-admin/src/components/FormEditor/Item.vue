<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <InsertItemImgModal :visible="showInsertItemImgModal" @close="showInsertItemImgModal = false"
      @saveItem="saveItemImage" :itemLocalProps="itemLocal" />
    <div @mouseenter="hover = true" @mouseleave="hover = false"  @click="onActiveItem">
      <div :type="isActive && !isMemberNumberItem ? 'info' : 'default'" bordered
        :class="`${isActive ? 'active-left-border' : ''} rounded-borders q-my-md bg-white item-container`"
        :color="isActive ? 'primary' : 'white'" dense :id="item?.itemKey"
        :icon="isActive && !isMemberNumberItem ? 'mdi-pencil' : 'mdi-checkbox-blank-outline'">
        <div>
          <div class="item-drag-handle text-center">
            <q-icon :style="`visibility: ${hover ? 'visible' : 'hidden'}`" name="mdi-drag-horizontal" size="sm" />
          </div>
          <div class="row">
            <div :class="`col-${isActive ? '6' : '12'}`">
              <q-input v-if="isActive" outlined dense class="q-pb-none"
                :label="itemLocal.type === 'linkbutton' ? linkbuttonTitleQuestion : defaultTitleQuestion" autofocus
                v-model="itemLocal.title" :rules="[rules.required]" @update:model-value="onGroupHeaderChange" />
              <div v-else
                :class="itemLocal.title ? 'subtitle flex align-center' : 'subtitle flex align-center title-not-exist'">
                <span class="red--text red lighten-5 q-pa-sm q-mr-sm" style="font-size:12px;"
                  v-if="itemLocal.isRequired.value" block>必須</span>

                {{
                  itemLocal.title || (itemLocal.type === "linkbutton" ? linkbuttonTitleQuestion :
                    defaultTitleQuestion)
                }}
              </div>
            </div>
            <div class="q-pl-md col-6" v-if="isActive">
              <q-btn @click="toggleTypeMenu" :disable="(isEditMode && !itemLocal.isNewItem) ||
                itemLocal.visable === false ||
                itemLocal.title_disabled ||
                (section.surveyType === 'corona' && itemLocal.type === 'linkbutton')"
                class="btn-type-select rounded-borders cursor-pointer full-width full-height q-mx-md row justify-start"
                flat>
                <q-item-section class="col-1">
                  <q-icon :name="selectedTypeIcon" size="sm"></q-icon>
                </q-item-section>
                <q-item-section class="q-pl-sm">
                  <q-item-label class="non-selectable text-no-wrap">{{ selectedTypeTitle }}</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-icon name="arrow_drop_down"></q-icon>
                </q-item-section>
              </q-btn>
              <q-menu fit z-index="10" v-model="typeMenuComputed">
                <q-list>
                  <div v-for="(itemType, index) in itemTypeList" :key="index">
                    <q-separator class="q-my-md" v-if="!Object.keys(itemType).length" />
                    <div v-else>


                      <q-menu anchor="top end" v-if="'childs' in itemType" v-model="subMenuVisible">
                        <q-list>
                          <q-item clickable v-for="childItem in itemType.childs" :key="`child_${childItem.value}`"
                            :class="`item-menu-hover`" @click="onSelectType(childItem.value)">
                            <q-item-section class="col-2">
                              <q-icon :name="childItem.icon" size="sm"></q-icon>
                            </q-item-section>
                            <q-item-section>
                              <q-item-label>{{ childItem.title }}</q-item-label>
                            </q-item-section>
                          </q-item>
                        </q-list>
                      </q-menu>

                      <q-item :class="`item-menu-hover`" @mouseenter="onHoverParent(itemType.childs)"
                        :clickable="!usePayment ? itemType.value !== ITEM_TYPE.SELECT_PRODUCTS : true" :key="itemType.value"
                        @click="onSelectType(itemType.value)" v-if="!('visable' in itemType)">
                        <q-item-section class="col-2">
                          <q-icon :name="itemType.icon" size="sm"></q-icon>
                        </q-item-section>
                        <q-item-section>
                          <q-item-label class="non-selectable">{{ itemType.title }}</q-item-label>
                        </q-item-section>
                        <q-item-section side v-if="itemType.childs">
                          <q-icon name="mdi-menu-right" size="sm"></q-icon>
                        </q-item-section>
                      </q-item>
                    </div>
                  </div>
                </q-list>
              </q-menu>
            </div>

          </div>
          <q-space class="q-my-sm"></q-space>
          <div no-wrap>
            <div class="q-pr-none q-mt-sm" v-if="itemLocal.type !== 'linkbutton'">
              <q-input type="textarea" v-if="isActive" outlined solo autogrow rows="1"
               dense hide-details="true"
                style="font-size: 14px" label="説明" v-model="itemLocal.description"></q-input>
              <div v-else class="q-mt-md" :class="itemLocal.description ? '' : 'title-not-exist'"
                style="white-space: pre-line;font-size:14px;">
                {{ itemLocal.description || "説明" }}
              </div>
              <q-separator class="q-my-md" v-if="!isActive"></q-separator>
            </div>
          </div>
          <div v-if="!isActive && imageUrl"
            class="items-center justify-between q-mr-none q-mt-md q-mb-none q-pt-md itemImgArea">
            <q-img :src="imageUrl ? imageUrl.itemUrl : ''" fit="contain" class="itemImg">
              </q-img>
          </div>
          <div v-else-if="isActive && imageUrl"
            class="items-center justify-between q-mr-none q-my-lg q-mb-none q-pt-md itemImgArea">
            <div class="q-my-md">
              <q-img :src="imageUrl ? imageUrl.itemUrl : ''" fit="contain" class="itemImg">
                <div class="flex flex-center absolute-full itemImgBg">
                  <q-btn class="text-white q-py-md q-px-md editHeaderBtn flex flex-center" :elevation="0" dense
                    color="blue-grey" @click="showInsertItemImgModal = true">
                    <q-icon left name="mdi-image-size-select-actual"></q-icon>画像を変更する
                  </q-btn>
                </div>
              </q-img>
            </div>

          </div>
          <div style="background-color: rgba(0,0,0,.08);"
            class="flex q-mr-none q-py-md q-ml-xs q-my-sm q-pl-sm q-col-gutter-sm" v-if="
              itemLocal.type !== 'groupheader' &&
              itemLocal.type !== 'guide' &&
              itemLocal.type !== 'linkbutton' &&
              itemLocal.type !== 'reservation' &&
              itemLocal.type !== 'memberNumber' &&
              isActive
            ">
            <div class="col-6 flex items-center no-wrap" v-if="!isMemberForm &&
              ((itemLocal.type === 'text' ||
                itemLocal.type === 'email' ||
                itemLocal.type === 'phone' ||
                itemLocal.type === 'number' ||
                itemLocal.type === 'postcode' ||
                itemLocal.type === 'address' ||
                itemLocal.type === 'textarea' ||
                itemLocal.type === 'radio' ||
                itemLocal.type === 'sesshuVaccineMaker' ||
                itemLocal.type === 'checkboxes' ||
                itemLocal.type === 'dropdown' ||
                itemLocal.type === 'date' ||
                itemLocal.type === 'birthday' ||
                itemLocal.type === 'choicegroupheader' ||
                itemLocal.type === 'sesshuJisshiDate' ||
                itemLocal.type === 'previousVaccineMaker' ||
                itemLocal.type === 'previousVaccineDate') ||
                (itemLocal.type !== 'groupheader' &&
                  itemLocal.type !== 'guide' &&
                  itemLocal.type !== 'linkbutton' &&
                  itemLocal.type !== 'reservation' &&
                  !isMemberNumberItem))">
              <div style="color:rgba(0,0,0,.6);min-width:92px;">ユーザー画面</div>
              <q-select :options="isNotEditableAndIsAdminItem" option-label="text" option-value="value"
                v-model="setIsNotEditableAndIsAdminItem" outlined height="40" max-height="40" hide-details="false"
                class="white bg-white" solo dense :disable="itemLocal.type === 'selectProducts' ||
                  (isFirstVaccination &&
                    ['previousVaccineMaker', 'previousVaccineDate'].includes(itemLocal.type)) ||
                  (isEditMode && !itemLocal.isNewItem && itemLocal.isAdminItem) ||
                  ['countVaccines'].includes(itemLocal.type) ||
                  (['sesshuJisshiDate', 'sesshuVaccineMaker'].includes(itemLocal.type) && hasCountVaccinesItem)"
                style="max-width:240px;" @change="changeIsAdminItem"></q-select>
            </div>
            <div class="col-6 flex items-center fs-14">
              <div class="fs-14" style="color:rgba(0,0,0,.6);min-width:57px;">必須設定</div>
              <q-select :options="isRequired" option-label="text" option-value="value" v-model="setIsRequired" outlined
                height="40" max-height="40" hide-details="false" class="inputHeightCtrl white bg-white q-ml-sm" solo dense
                @change="handleChangeIsRequired" :disable="[ITEM_TYPE.SELECT_PRODUCTS, ITEM_TYPE.SELECT_PAY_TYPE].includes(itemLocal.type) ||
                  (isEditMode && !itemLocal.isNewItem) ||
                  itemLocal.isAdminItem ||
                  itemLocal.isSearchable.value ||
                  (isFirstVaccination && ['previousVaccineMaker', 'previousVaccineDate'].includes(itemLocal.type))
                  " :style="isMemberForm ? 'max-width:100%;' : 'max-width:240px;'"></q-select>
            </div>
          </div>
        </div>
        <div class="q-gutter-md" v-if="isMemberNumberItem">
          <div class="q-col-auto">
            <q-checkbox v-model="itemLocal.isAutomaticNumbering"
              @input="itemLocal.isAutomaticNumbering = !itemLocal.isAutomaticNumbering" label="自動採番"
              :disable="isEditMode && !itemLocal.isNewItem"  hide-details
              class="mt-0 pt-0"></q-checkbox>
          </div>
        </div>
        <DynamicSlot :current-slot="itemLocal.type" class="q-my-md">

          <template v-slot:date>
            <div style="display: none">
              <ItemDatePicker :key="componentKey" :isActive="false" :item="itemLocal"
                :isEditMode="isEditMode && !itemLocal.isNewItem" />
            </div>
          </template>
          <template v-slot:sesshuJisshiDate>
            <ItemSesshuJisshiDatePicker :key="componentKey" :isActive="false" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" />
          </template>
          <template v-slot:dateDropdown>
            <ItemDateDropdown :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" />
          </template>
          <template v-slot:birthday>
            <ItemDatePickerBirthday :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" />
          </template>
          <template v-slot:text>
            <ItemText :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" />
          </template>
          <template v-slot:linkbutton>
            <ItemLink :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" />
          </template>
          <template v-slot:calendar>
            <ItemCalendar :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" />
          </template>
          <template v-slot:email>
            <ItemTextEmail :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" />
          </template>
          <template v-slot:phone>
            <ItemTextPhone :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" />
          </template>
          <template v-slot:postcode>
            <ItemTextPostCode :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" />
          </template>
          <template v-slot:address>
            <ItemTextAddress :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" />
          </template>
          <template v-slot:number>
            <ItemTextNumber :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" />
          </template>
          <template v-slot:textarea>
            <ItemParagraphText :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" />
          </template>
          <template v-slot:radio>
            <ItemRadio :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" :isEdited="isEdited"
              :latestOptionIndex="latestOptionIndex" :editIndex="canEditAndEditIndex"
              :originalOptionsIndex="originalOptionsIndex" @addOption="addOption" @removeOption="removeOption"
              @moveOption="moveOption" @updateOption="updateOption" />
          </template>
          <template v-slot:sesshuVaccineMaker>
            <ItemSesshuVaccineMaker :key="componentKey" :isActive="isActive" :item="itemLocal" :isEdited="isEdited"
              :originalSectionOptionsIndex="originalSectionOptionsIndex"
              :isEditMode="isEditMode && !itemLocal.isNewItem" :editIndex="canEditAndEditIndex"
              :hasCountVaccinesItem="hasCountVaccinesItem" @addRadioSectionOption="addRadioSectionOption"
              @removeRadioSectionOption="removeRadioSectionOption" @updateRadioSectionOption="updateRadioSectionOption"
              @moveRadioSectionOption="moveRadioSectionOption" />
          </template>
          <template v-slot:choicegroupheader>
            <ItemRadioSection :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem"
              :originalSectionOptionsIndex="originalSectionOptionsIndex" :editIndex="canEditAndEditIndex"
              :radioGroupHeaderList="section.radioGroupHeaderList" :attributes="itemLocal.attributes"
              @addRadioSectionOption="addRadioSectionOption" @removeRadioSectionOption="removeRadioSectionOption"
              @updateRadioSectionOption="updateRadioSectionOption" @moveRadioSectionOption="moveRadioSectionOption" />
          </template>
          <template v-slot:checkboxes>
            <ItemCheckboxes :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" :editIndex="canEditAndEditIndex" :isEdited="isEdited"
              :latestOptionIndex="latestOptionIndex" :originalOptionsIndex="originalOptionsIndex" @addOption="addOption"
              @removeOption="removeOption" @moveOption="moveOption" @updateOption="updateOption" />
          </template>
          <template v-slot:dropdown>
            <ItemDropdown :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" :editIndex="canEditAndEditIndex" :isEdited="isEdited"
              :latestOptionIndex="latestOptionIndex" :originalOptionsIndex="originalOptionsIndex" @addOption="addOption"
              @removeOption="removeOption" @moveOption="moveOption" @updateOption="updateOption" />
          </template>
          <template v-slot:reservation>
            <ItemBunrui :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" @updateReservationCheckBox="updateReservationCheckBox"
              @updateSelectedCategory="updateSelectedCategory" @updateFixedCategory="updateFixedCategory"
              @updateCategoryTitle="updateCategoryTitle" />
          </template>
          <template v-slot:suggest>
            <ItemSuggest :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" :editIndex="canEditAndEditIndex"
              :latestOptionIndex="latestOptionIndex" :isEdited="isEdited" :originalOptionsIndex="originalOptionsIndex"
              @addOption="addOption" @removeOption="removeOption" @moveOption="moveOption"
              @updateOption="updateOption" />
          </template>
          <template v-slot:guide> </template>
          <template v-slot:groupheader> </template>
          <template v-slot:memberNumber>
            <ItemMemberNumber :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" />
          </template>
          <template v-slot:countVaccines>
            <ItemCountVaccines :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" @onChange="changeCountVaccines" />
          </template>
          <template v-slot:previousVaccineDate>
            <div style="display: none">
              <ItemDatePicker :key="componentKey" :isActive="false" :item="itemLocal"
                :isEditMode="isEditMode && !itemLocal.isNewItem" :isFirstVaccination="isFirstVaccination" />
            </div>
          </template>
          <template v-slot:previousVaccineMaker>
            <ItemPreviousVaccineMaker :key="componentKey" :isActive="isActive" :item="itemLocal"
              :isEditMode="isEditMode && !itemLocal.isNewItem" :isFirstVaccination="isFirstVaccination"
              :originalSectionOptionsIndex="originalSectionOptionsIndex"
              :vaccinationIntervalType="step1SurveyFormat.vaccinationIntervalType" :editIndex="canEditAndEditIndex"
              @addRadioSectionOption="addRadioSectionOption" @removeRadioSectionOption="removeRadioSectionOption"
              @updateRadioSectionOption="updateRadioSectionOption" @moveRadioSectionOption="moveRadioSectionOption"
              @setVaccinationIntervalType="setVaccinationIntervalType" />
          </template>
          <template v-slot:selectProducts>
            <ItemSelectProducts :key="componentKey" :is-edit-mode="isEditMode" :item="itemLocal" />
          </template>
          <template v-slot:selectPayType>
            <ItemSelectPayType :key="componentKey" :isEditMode="isEditMode" :isActive="isActive" :item="itemLocal"
              @updatePayTypeOption="updatePayTypeOption"
              @moveOption="moveOption" />
          </template>
          <template v-slot:files>
            <ItemFiles v-model="itemLocal" :isActive="isActive" :isEditMode="isEditMode" @update="updateFiles"></ItemFiles>
          </template>
          <template v-slot:default>
            <div class="my-5 pl-6">{{ selectedTypeTitle }} : 開発中。。。</div>
          </template>
        </DynamicSlot>
        <div class="flex column" v-if="isActive && !isMemberNumberItem">
          <q-separator class="q-my-md ml-6"></q-separator>

          <div class="q-col-gutter-md flex justify-between" style="width:100%">
            <div>
              <q-btn dense color="red" class="q-pa-sm" text-color="white" :disable="['previousVaccineMaker',
                'previousVaccineDate',
                'sesshuVaccineMaker',
                'sesshuJisshiDate',
                'countVaccines'].includes(itemLocal.type) ||
                (itemLocal.type === 'choicegroupheader' &&
                  itemLocal.attributes &&
                  itemLocal.attributes.isSesshuKaisuu) ||
                (section.surveyType === 'corona' && itemLocal.type === 'linkbutton') ||
                ([ITEM_TYPE.SELECT_PRODUCTS, ITEM_TYPE.SELECT_PAY_TYPE].includes(itemLocal.type) &&
                  isEditMode &&
                  !itemLocal.isNewItem) ||
                  (isEditMode && itemLocal.isRequired.value && !itemLocal.isNewItem)
                " @click="onDeleteItem">
                <q-icon name="mdi-trash-can-outline" left />削除
              </q-btn>
            </div>
            <div>
              <q-btn v-if="itemLocal.type != 'linkbutton'" elevation="0" class="blue-grey--text q-pa-sm"
                style="min-width:115px!important;border:solid 1px #607D8B;max-width:115px;" outline dense
                :disable="[ITEM_TYPE.LINK, ITEM_TYPE.SELECT_PRODUCTS, ITEM_TYPE.SELECT_PAY_TYPE].includes(itemLocal.type)"
                @click="showInsertItemImgModal = true">
                <q-icon name="mdi-image-plus" left />画像挿入
              </q-btn>
              <q-btn dense class="q-ml-md q-pa-sm" color="primary" text-color="white" :disable="itemLocal.title_disabled ||
                (section.surveyType === 'corona' && itemLocal.type === 'linkbutton') ||
                [ITEM_TYPE.SELECT_PRODUCTS, ITEM_TYPE.SELECT_PAY_TYPE].includes(itemLocal.type)
                " @click="onDuplicateItem">
                <q-icon name="mdi-checkbox-multiple-blank-outline" left />複製
              </q-btn>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, onMounted, PropType, reactive, Ref, ref, watch } from "vue";
import moment from "moment";
import { cloneDeep, isEmpty } from "lodash";
import { trim } from "@/utils/stringUtils";
import ItemText from "@/components/FormEditor/ItemText.vue";
import ItemCalendar from "@/components/FormEditor/ItemCalendar.vue";
import ItemLink from "@/components/FormEditor/ItemLink.vue";
import ItemTextPhone from "@/components/FormEditor/ItemTextPhone.vue";
import ItemTextAddress from "@/components/FormEditor/ItemTextAddress.vue";
import ItemTextPostCode from "@/components/FormEditor/ItemTextPostCode.vue";
import ItemTextNumber from "@/components/FormEditor/ItemTextNumber.vue";
import ItemTextEmail from "@/components/FormEditor/ItemTextEmail.vue";
import ItemParagraphText from "@/components/FormEditor/ItemParagraphText.vue";
import ItemRadio from "@/components/FormEditor/ItemRadio.vue";
import ItemSesshuVaccineMaker from "@/components/FormEditor/ItemSesshuVaccineMaker.vue";
import ItemRadioSection from "@/components/FormEditor/ItemRadioSection.vue";
import ItemCheckboxes from "@/components/FormEditor/ItemCheckboxes.vue";
import ItemDropdown from "@/components/FormEditor/ItemDropdown.vue";
import ItemSuggest from "@/components/FormEditor/ItemSuggest.vue";
import ItemDatePicker from "@/components/FormEditor/ItemDatePicker.vue";
import ItemSesshuJisshiDatePicker from "@/components/FormEditor/ItemSesshuJisshiDatePicker.vue";
import ItemDateDropdown from "@/components/FormEditor/ItemDateDropdown.vue";
import ItemDatePickerBirthday from "@/components/FormEditor/ItemDatePickerBirthday.vue";
import ItemMemberNumber from "@/components/FormEditor/ItemMemberNumber.vue";
import ItemCountVaccines from "@/components/FormEditor/ItemCountVaccines.vue";
import ItemPreviousVaccineMaker from "@/components/FormEditor/ItemPreviousVaccineMaker.vue";
import VSwitchCase from "@/components/common/VSwitchCase.vue";
import { ITEM_TYPE } from "@stores/modules/formEditor/formEditor.constants";
import MultipleChoiceItem from "@/model/Form/MultipleChoiceItem";
import SingleChoiceItem from "@/model/Form/SingleChoiceItem";
import SingleChoiceSectionItem from "@/model/Form/SingleChoiceSectionItem";
import TextItem from "@/model/Form/TextItem";
import ReservationItem from "@/model/Form/ReservationItem";
import TextNumberItem from "@/model/Form/TextNumberItem";
import Item from "@/model/Form/Item";
import TextAreaItem from "@/model/Form/TextAreaItem";
import ItemBunrui from "@/components/FormEditor/ItemBunrui.vue";
import InsertItemImgModal from "@/components/FormEditor/InsertItemImgModal.vue";
import MemberNumberItemModel from "@/model/Form/MemberNumberItem";
import eventBus from '@utils/eventBus'
import ItemSelectProducts from "@/components/FormEditor/ItemSelectProducts.vue";
import SelectProductsItem from "@/model/Form/SelectProductsItem";
import ItemSelectPayType from "@/components/FormEditor/ItemSelectPayType.vue";
import SelectPayTypeItem from "@/model/Form/SelectPayTypeItem";
import { useFormEditorStore } from "@/stores/modules/formEditor";
import { useFormsStore } from "@/stores/modules/forms";

import DynamicSlot from "@/components/common/DynamicSlot.vue";

import { useQuasar } from "quasar";
import ItemFiles from "./ItemFiles.vue";
import FilesItem from "@/model/Form/ FilesItem";
import { clearScreenDown } from "readline";

const $q = useQuasar();

let hover = ref(false);

interface SectionOption {
  option: { value: string | null };
  groupheader: { value: string | null };
}

const formEditorStore = useFormEditorStore();
const formsStore = useFormsStore();

const props = defineProps({
  item: Object as PropType<any>,
  sectionKey: String as PropType<string>,
  section: {
    type: Object as PropType<any>,
    required: true,
  },
  editedItemKey: String as PropType<string>,
  initialItem: Object as PropType<any>,
});

const subMenu: Ref<any> = ref({});
const subMenuVisible = ref(false);

const onHoverParent = (value: any) => {
  subMenu.value = value;
  subMenuVisible.value = value ? true : false;
};

let imageUrl: any = {};
const rules = {
  required: (value: string) => !!trim(value) || "*必須です",
};
const typeMenu = ref(false);
let itemLocal: Ref<any> = ref({});
const defaultTitleQuestion = "タイトル";
const linkbuttonTitleQuestion = "ボタンテキスト";
let setIsNotEditableAndIsAdminItem = ref({});
const isNotEditableAndIsAdminItem = [
  {
    value: 'bothFalse',
    text: '表示する（編集可)',
  },
  {
    value: 'isNotEditableOnlyTrue',
    text: '表示する (編集不可)',
  },
  {
    value: 'isAdminItemOnlyTrue',
    text: '表示しない',
  },
];
let setIsRequired = ref({});
const isRequired = [
  {
    value: 'isRequiredOnlyTrue',
    text: '必須',
  },
  {
    value: 'isRequiredForUserOnlyTrue',
    text: 'ユーザー画面のみ必須',
  },
  {
    value: 'bothFalse',
    text: '任意',
  },
];
const itemTypes: {
  icon?: string,
  value?: string,
  title?: string,
  visable?: boolean,
  childs?: {
    icon: string,
    value: string,
    title: string,
  }[]
}[] = [
  {
    icon: "mdi-text-short",
    value: ITEM_TYPE.TEXT,
    title: "記述式",
    childs: [
      {
        icon: "mdi-format-text-rotation-none",
        value: ITEM_TYPE.TEXT,
        title: "一行テキスト",
      },
      {
        icon: "mdi-at",
        value: ITEM_TYPE.TEXT_EMAIL,
        title: "Eメールアドレス",
      },
      {
        icon: "mdi-phone",
        value: ITEM_TYPE.TEXT_PHONE,
        title: "電話番号",
      },
      {
        icon: "mdi-numeric",
        value: ITEM_TYPE.TEXT_NUMBER,
        title: "数値",
      },
      {
        icon: "mdi-post",
        value: ITEM_TYPE.TEXT_POSTCODE,
        title: "郵便番号",
      },
      {
        icon: "mdi-home-outline",
        value: ITEM_TYPE.TEXT_ADDRESS,
        title: "住所",
      }
    ],
  },
  {
    icon: "mdi-text-subject",
    value: ITEM_TYPE.PARAGRAPH_TEXT,
    title: "段落",
  },
  {},
  {
    icon: "mdi-radiobox-marked",
    value: ITEM_TYPE.RADIO,
    title: "ラジオボタン",
  },
  {
    icon: "mdi-radiobox-marked",
    value: ITEM_TYPE.CHOICE_GROUPHEADER,
    title: "ラジオ グループヘッダ",
  },
  {
    icon: "mdi-checkbox-marked",
    value: ITEM_TYPE.CHECKBOX,
    title: "チェックボックス",
  },
  {
    icon: "mdi-arrow-down-drop-circle",
    value: ITEM_TYPE.LIST,
    title: "プルダウン",
  },
  {
    icon: "mdi-magnify-plus",
    value: ITEM_TYPE.SUGGEST,
    title: "サジェスト",
  },
  {
    icon: "mdi-calendar",
    value: ITEM_TYPE.DATE,
    title: "日付選択",
  },
  {
    icon: "mdi-calendar-text",
    value: ITEM_TYPE.DATE_PICKER_BIRTHDAY,
    title: "生年月日",
  },
  {
    icon: "mdi-television-guide",
    value: ITEM_TYPE.GUIDE,
    title: "説明文",
  },
  {
    icon: "mdi-ballot",
    value: ITEM_TYPE.GROUPHEADER,
    title: "グループヘッダ",
  },
  {
    icon: "mdi-credit-card-outline",
    value: ITEM_TYPE.SELECT_PRODUCTS,
    title: "商品選択（カレンダー予約なし）",
  },
  {
    icon: "mdi-radiobox-marked",
    value: ITEM_TYPE.SELECT_PAY_TYPE,
    title: "支払方法選択",
  },
  {
    icon: "mdi-paperclip",
    value: ITEM_TYPE.FILES,
    title: "ファイル",
  },
  {},
  {
    icon: "mdi-link",
    value: ITEM_TYPE.LINK,
    title: "カレンダーボタン",
  },

  {
    icon: "mdi-ballot",
    value: ITEM_TYPE.BUNRUI,
    title: "分類",
  },

  // {},
  {
    icon: "mdi-calendar",
    value: ITEM_TYPE.SESSHU_JISSHI_DATE,
    title: "接種実施日",
    visable: false,
  },
  {
    icon: "mdi-card-account-details",
    value: ITEM_TYPE.MEMBER_NUMBER,
    title: "会員番号",
    visable: false,
  },
  {
    icon: "mdi-radiobox-marked",
    value: ITEM_TYPE.SESSHU_VACCINE_MAKER,
    title: "接種ワクチンのメーカー",
    visable: false,
  },
  {
    icon: "mdi-numeric",
    value: ITEM_TYPE.COUNT_VACCINES,
    title: "接種回数",
    visable: false,
  },
  {
    icon: "mdi-calendar",
    value: ITEM_TYPE.PREVIOUS_VACCINE_DATE,
    title: "前回の接種実施日",
    visable: false,
  },
  {
    icon: "mdi-radiobox-marked",
    value: ITEM_TYPE.PREVIOUS_VACCINE_MAKER,
    title: "前回接種のメーカー",
    visable: false,
  },
];
const componentKey = ref(0);
const editIndex = ref(-1);
const isEdited = ref(false);
const latestOptionIndex = ref(-1);

const fixed_item_titles = [
  "接種回数",
  "前回の接種実施日",
  "前回の接種ワクチンのメーカー",
  "今回の接種実施日",
  "今回の接種ワクチンのメーカー",
  "前回接種のメーカー",
];
const showInsertItemImgModal = ref(false);

const activeItem = computed(() => formEditorStore.activeItem);
const showCodeEditor = computed(() => formEditorStore.showCodeEditor);
const activeTab = computed(() => formEditorStore.activeTab);
const isEditMode = computed(() => formEditorStore.isEditMode);
const isMemberForm = computed(() => formEditorStore.formType === 'member');
const editedMultipleItem = computed(() => formsStore.editedMultipleItem);
const step1SurveyFormat = computed(() => formEditorStore.step1SurveyFormat);

const isActive = computed(() => activeItem.value === itemLocal.value.itemKey);

const selectedTypeObj = computed(() => {
  const _itemType = itemTypes.reduce((flatResult: any[], obj: { childs?: any }) => {
    let _flat = [obj];
    if (obj.childs) {
      _flat = _flat.concat(obj.childs);
    }
    return flatResult.concat(_flat);
  }, []);

  const item = _itemType.find((obj: { value: any }) => obj.value === itemLocal.value.type);
  return _itemType.find((obj: { value: any }) => obj.value === itemLocal.value.type);
});

const selectedTypeIcon = computed(() => selectedTypeObj.value.icon);
const selectedTypeTitle = computed(() => selectedTypeObj.value.title);

const isMemberNumberItem = computed(() => itemLocal.value.type === ITEM_TYPE.MEMBER_NUMBER);

const isMemberFormButNoMemberNumberItem = computed(() => isMemberForm.value && !isMemberNumberItem.value);

const itemTypeList = computed(() => {
  let typeList = itemTypes;
  if (isMemberForm.value) {
    typeList = typeList.filter((t: any, index: number, arr: Array<any>) => {
      const excludedItems = [ITEM_TYPE.LINK, ITEM_TYPE.BUNRUI];
      let isValid = true;

      if (t === 'divider' && excludedItems.includes(arr[index + 1].value)) {
        isValid = false;
      } else {
        isValid = !excludedItems.includes(t.value);
      }

      return isValid;
    });
  }
  if (!usePayment) {
    typeList = typeList.filter((t: any) => ![ITEM_TYPE.SELECT_PRODUCTS, ITEM_TYPE.SELECT_PAY_TYPE].includes(t.value));
  }
  return typeList;
});

const typeMenuComputed = computed(() => {
  return typeMenu.value;
});

const hasCountVaccinesItem = computed(() => {
  const countVaccinesItem = props.section.surveySchema.find((item: any) => item.type === 'countVaccines');
  return !!countVaccinesItem;
});

const isFirstVaccination = computed(() => {
  const surveySchema = step1SurveyFormat.value.surveySchema;
  const countVaccinesValue = surveySchema.find((item: any) => item.type === 'countVaccines')?.default;
  const isFirst = Number(countVaccinesValue) === 1;
  return isFirst;
});

const canEditAndEditIndex = computed(() => {
  if (!props.editedItemKey || props.editedItemKey === itemLocal.value.itemKey) {
    return editIndex.value;
  }

  // NOTE: 正の値は実際のindex、-1だと編集可、-2だとロックがかかる
  return -2;
});

const originalOptionsIndex = computed(() => {
  const optionsLength = props?.initialItem?.options ? props?.initialItem.options.length : 0;
  return optionsLength > 0 ? optionsLength - 1 : 0;
});

const originalSectionOptionsIndex = computed(() => {
  const optionsLength = props?.initialItem?.sectionOptions ? props?.initialItem.sectionOptions.length : 0;
  return optionsLength > 0 ? optionsLength - 1 : 0;
});

const usePayment = import.meta.env.VITE_USE_PAYMENT === '1';


watch(props.item, (val) => {
  if(itemLocal && itemLocal.value?.isSearchable) {
    itemLocal.value.isSearchable.value = props?.item?.isSearchable;
    handleChangeIsSearchableItem();
  }
  if(itemLocal && itemLocal.value?.duplicateKeyCheck) {
    itemLocal.value.duplicateKeyCheck = props?.item?.duplicateKeyCheck;
    handleChangeDuplicateKeyCheck();
  }
});

watch(itemLocal.value, (val) => {
  if (itemLocal.value.type === 'linkbutton') {
    itemLocal.value.imageUrl = {};
    imageUrl = {};
  }
});

watch(setIsRequired, (selectedItem: any) => {
  // Needed to get the value since it returns an objet instead | 値がオブジェクトを返すため、値を取得する必要があります
  selectedItem = selectedItem.value;
  if (selectedItem) {
    switch (selectedItem) {
      case 'isRequiredOnlyTrue':
        itemLocal.value.isRequired.value = true;
        itemLocal.value.isRequiredForUser.value = false;
        break;
      case 'bothFalse':
        itemLocal.value.isRequired.value = false;
        if (isMemberFormButNoMemberNumberItem.value) {
          itemLocal.value.duplicateKeyCheck = false;
          itemLocal.value.isRequiredForUser.value = false;
        } else {
          itemLocal.value.isRequiredForUser.value = false;
        }
        if (itemLocal.value.isIndexable.value && isMemberForm.value) {
          itemLocal.value.isIndexable.value = false;
        }
        break;
      case 'isRequiredForUserOnlyTrue':
        itemLocal.value.isRequired.value = false;
        itemLocal.value.isRequiredForUser.value = true;
        if (isMemberFormButNoMemberNumberItem.value) {
          itemLocal.value.duplicateKeyCheck = false;
        }
        if (itemLocal.value.isIndexable && isMemberForm.value) {
          itemLocal.value.isIndexable.value = false;
        }
        break;
      default:
        break;
    }
  }
});

watch(setIsNotEditableAndIsAdminItem, (selectedItem: any) => {
  // Needed to get the value since it returns an objet instead | 値がオブジェクトを返すため、値を取得する必要があります
  selectedItem = selectedItem.value;
  if (selectedItem) {
    let index;
    switch (selectedItem) {
      case 'bothFalse':
        itemLocal.value.isNotEditable = false;
        itemLocal.value.isAdminItem = false;
        break;
      case 'isNotEditableOnlyTrue':
        itemLocal.value.isNotEditable = true;
        itemLocal.value.isAdminItem = false;
        break;
      case 'isAdminItemOnlyTrue':
        itemLocal.value.isNotEditable = false;
        itemLocal.value.isAdminItem = true;
        if (isMemberFormButNoMemberNumberItem.value) {
          itemLocal.value.duplicateKeyCheck = false;
        }
        index = isRequired.findIndex((el) => el.value == 'bothFalse');
        break;
      default:
        break;
    }
    if (index != undefined) {
      setIsRequired.value = isRequired[index];
    }
  }
});

watch(showCodeEditor, (val) => {
  if (val && isActive) {
    //update item data from local to store in here
    updateItemLocalToStore(null);
  }
});

watch(isFirstVaccination, (value) => {
  if (
    value &&
    isFirstVaccination &&
    ['previousVaccineMaker', 'previousVaccineDate'].includes(itemLocal.value.type)
  ) {
    itemLocal.value.isRequired.value = false;
    itemLocal.value.isNotEditable = false;

    if (itemLocal.value.type === 'previousVaccineMaker') {
      initializePreviousVaccineMakerOptions();
    }
  }
}, { immediate: true, deep: true });

watch(activeItem, (val) => {
  //console.log('activeItem', val);
  //console.log('itemLocal', itemLocal.value);
  if (val !== itemLocal.value.itemKey) {
    updateItemLocalToStore(null);
  }
}, { deep: true });

const toggleTypeMenu = () => {
  typeMenu.value = !typeMenu.value;
};

const resetIsRequired = () => {
  let index = -1;
  if (props?.item?.isRequiredForUser) {
    if (props?.item?.type == 'countVaccines') {
      index = isRequired.findIndex((el) => el.value == 'isRequiredOnlyTrue');
    } else if (props?.item?.isAdminItem) {
      index = isRequired.findIndex((el) => el.value == 'bothFalse');
    } else if (props?.item?.isRequired.value && !props?.item?.isRequiredForUser.value) {
      index = isRequired.findIndex((el) => el.value == 'isRequiredOnlyTrue');
    } else if (!props?.item?.isRequired.value && !props?.item?.isRequiredForUser.value) {
      index = isRequired.findIndex((el) => el.value == 'bothFalse');
    } else if (!props?.item?.isRequired.value && props?.item?.isRequiredForUser.value) {
      index = isRequired.findIndex((el) => el.value == 'isRequiredForUserOnlyTrue');
    }
    setIsRequired.value = isRequired[index];
  } else {
    if (props?.item?.type == 'countVaccines') {
      index = isRequired.findIndex((el) => el.value == 'isRequiredOnlyTrue');
    } else if (!props?.item?.isRequired.value || itemLocal.value.isAdminItem) {
      index = isRequired.findIndex((el) => el.value == 'bothFalse');
    } else {
      index = isRequired.findIndex((el) => el.value == 'isRequiredOnlyTrue');
    }
    setIsRequired.value = isRequired[index];
  }
};

const resetIsNotEditableAndIsAdminItem = () => {
  let index = -1;
  if (isMemberForm.value) {
    props.item.isAdminItem = false;
    props.item.isNotEditable = false;
  }
  if (props?.item?.isAdminItem && !props?.item?.isNotEditable) {
    index = isNotEditableAndIsAdminItem.findIndex((el) => el.value == 'isAdminItemOnlyTrue');
  } else if (!props?.item?.isAdminItem && !props?.item?.isNotEditable) {
    index = isNotEditableAndIsAdminItem.findIndex((el) => el.value == 'bothFalse');
  } else if (!props?.item?.isAdminItem && props?.item?.isNotEditable) {
    index = isNotEditableAndIsAdminItem.findIndex((el) => el.value == 'isNotEditableOnlyTrue');
  }
  setIsNotEditableAndIsAdminItem.value = isNotEditableAndIsAdminItem[index];
};

const saveItemImage = (itemImage: any) => {
  //console.log('saveItemImage', itemImage);
  itemLocal.value.imageUrl = itemImage;
  //console.log("itemLocal", itemLocal.value);
  imageUrl = itemImage;
};

const changeIsAdminItem = (event: any) => {
  let index;
  if (event.value == 'isAdminItemOnlyTrue') {
    itemLocal.value.isSearchable.value = false;
    itemLocal.value.isRequired.value = false;
  }
  if (event.value != 'isAdminItemOnlyTrue' && itemLocal.value.isAdminItem) {
    // OFF
    $q.notify({
      message: 'ユーザーに表示されるため、管理者が個人情報を入力しないようにしてください。',
      type: 'info',
      position: 'top',
      timeout: 3000,
    });
  }
};

const handleChangeIsSearchableItem = () => {
  if (itemLocal.value.isSearchable.value) {
    itemLocal.value.isRequired.value = true;
    itemLocal.value.isRequiredForUser.value = false;
  }
};

const handleChangeDuplicateKeyCheck = () => {
  if (itemLocal.value.duplicateKeyCheck) {
    itemLocal.value.isRequired.value = true;
    itemLocal.value.isRequiredForUser.value = false;
  }
};

const handleChangeIsRequired = () => {
  if (!itemLocal.value.isRequired.value && isMemberFormButNoMemberNumberItem) {
    itemLocal.value.duplicateKeyCheck = false;
  }
};

const isTextType = (localType: any) => {
  try {
    let textTypeChildren = itemTypes.find((obj: any) => obj?.title == '記述式')?.childs?.map((obj) => obj?.value);
    return textTypeChildren ? textTypeChildren.includes(localType) : false;
  } catch (err) {
    return false;
  }
};

const createLocalItem = () => {
  let tempItem = cloneDeep(props.item);
  if (!('isSearchable' in tempItem)) {
    tempItem['isSearchable'] = {
      value: false,
    };
  }
  if (!('isIndexable' in tempItem)) {
    tempItem['isIndexable'] = {
      value: false,
    };
  }
  if (!('isRequiredForUser' in tempItem)) {
    tempItem['isRequiredForUser'] = {
      value: false,
    };
  }
  return tempItem;
};

const updateItemLocalToStore = (event: any) => {
  if (
    itemLocal.value.type === 'reservation' ||
    itemLocal.value.type === 'groupheader' ||
    itemLocal.value.type === 'linkbutton' ||
    itemLocal.value.type === 'guide'
  ) {
    itemLocal.value.isAdminItem = false;
  }

  formEditorStore.updateFormSchemaItem({
    sectionKey: props.sectionKey,
    item: itemLocal.value,
  });
};

const onSelectType = (value: any) => {
  var item = new Item();
  const { title, description } = itemLocal.value;
  //setup item type property if needed
  switch (value) {
    case ITEM_TYPE.TEXT:
    case ITEM_TYPE.TEXT_EMAIL:
    case ITEM_TYPE.TEXT_PHONE:
    case ITEM_TYPE.TEXT_POSTCODE:
    case ITEM_TYPE.TEXT_ADDRESS:
    case ITEM_TYPE.DATE_PICKER_BIRTHDAY:
      item = new TextItem(value, title, description);
      itemLocal.value.default = undefined;
      break;
    case ITEM_TYPE.GUIDE:
    case ITEM_TYPE.GROUPHEADER:
      item = new TextItem(value, title, description);
      itemLocal.value.default = '';
      itemLocal.value.isRequired = { value: false };
      itemLocal.value.isSearchable = { value: false };
      break;
    case ITEM_TYPE.BUNRUI:
      item = new ReservationItem(value, title, description);
      itemLocal.value.default = '';
      break;
    case ITEM_TYPE.TEXT_NUMBER:
      item = new TextNumberItem(value, title, description);
      break;
    case ITEM_TYPE.PARAGRAPH_TEXT:
      item = new TextAreaItem(title, description);
      break;
    case ITEM_TYPE.RADIO:
    case ITEM_TYPE.SESSHU_VACCINE_MAKER:
      item = new SingleChoiceItem(value, title, description);
      break;
    case ITEM_TYPE.CHOICE_GROUPHEADER:
      item = new SingleChoiceSectionItem(value, title, description);
      item.attributes = { isSesshuKaisuu: false };
      break;
    case ITEM_TYPE.LIST:
      item = new SingleChoiceItem(value, title, description);
      break;
    case ITEM_TYPE.SUGGEST:
      item = new SingleChoiceItem(value, title, description);
      break;
    case ITEM_TYPE.CHECKBOX:
      item = new MultipleChoiceItem(title, description);
      break;
    case ITEM_TYPE.DATE:
      itemLocal.value.default = undefined;
      itemLocal.value.includeYear = true;
      break;
    case ITEM_TYPE.LINK:
      itemLocal.value.title = '予約日時指定に進む';
      item = new TextItem(value, itemLocal.value.title, description);
      itemLocal.value.isRequired = { value: false };
      itemLocal.value.isSearchable = { value: false };
      break;
    case ITEM_TYPE.MEMBER_NUMBER:
      item = new MemberNumberItemModel(title, description);
      break;
    case ITEM_TYPE.SELECT_PRODUCTS:
      item = new SelectProductsItem(value, '商品選択', '購入商品を選択してください');
      itemLocal.value.isRequired = { value: true };
      itemLocal.value.isSearchable = { value: false };
      itemLocal.value.isAdminItem = false;
      setIsRequired.value = isRequired[0];
      break;
    case ITEM_TYPE.SELECT_PAY_TYPE:
      item = new SelectPayTypeItem();
      itemLocal.value.isRequired = { value: true };
      itemLocal.value.isSearchable = { value: false };
      itemLocal.value.isAdminItem = false;
      setIsRequired.value = isRequired[0];
      break;
    case ITEM_TYPE.FILES:
      item = new FilesItem();
      break;
    default:
      break;
  }

  if (isEditMode.value) {
    if (value === ITEM_TYPE.BUNRUI) {
      itemLocal.value.isRequired.value = true;
    } else {
      itemLocal.value.isRequired.value = false;
    }
    itemLocal.value.isRequiredForUser.value = false;
  }

  let new_item = { ...item.init() } as any;
  if (itemLocal.value.hasOwnProperty('options')) {
    delete new_item.options;
  }
  itemLocal.value = { ...itemLocal.value, ...new_item };

  itemLocal.value.type = value;

  typeMenu.value = false;

  // groupheaderList
  if (!props?.section?.groupheaderList) {
    props.section.groupheaderList = {};
  }

  //console.log('itemLocal.value', itemLocal.value);
  if (itemLocal.value.type === ITEM_TYPE.GROUPHEADER) {
    if (!props?.section?.groupheaderList[itemLocal.value.itemKey]) {
      props.section.groupheaderList[itemLocal.value.itemKey] = itemLocal.value.title;
    }
  } else {
    delete props?.section?.groupheaderList[itemLocal.value.itemKey];
  }
};

const onActiveItem = () => {
  formEditorStore.setActiveFormSchemaItem(props.item.itemKey);
  formEditorStore.setActiveFormSchemaSection(props.sectionKey);
  if (
    props.item?.type === ITEM_TYPE.CHOICE_GROUPHEADER ||
    (props.item?.itemKey === itemLocal.value.itemKey && itemLocal.value.type === ITEM_TYPE.CHOICE_GROUPHEADER)
  ) {
    setRadioGroupHeaderList(props.item?.itemKey);
  }
};

const setRadioGroupHeaderList = (itemKey: any) => {
  if (!isEmpty(itemKey)) {
    props.section.radioGroupHeaderList = [];
    let disabledGroupHeaderList: { [key: string]: string } = {};
    for (let index = 0; index < props.section.surveySchema.length; index++) {
      const element = props.section.surveySchema[index];

      if (element.itemKey === itemKey) {
        break;
      }
      if (element.type === ITEM_TYPE.GROUPHEADER) {
        disabledGroupHeaderList[element.itemKey] = element.title;
      }
    }

    props.section.radioGroupHeaderList = [];
    for (const [key, value] of Object.entries(props.section.groupheaderList)) {
      if (!disabledGroupHeaderList[key]) {
        props.section.radioGroupHeaderList[key] = value;
      }
    }
  }
};

const onDeleteItem = () => {
  $q.dialog({
    color: 'negative',
    message: 'この項目を削除してもよろしいですか？',
    ok: {
      label: 'この項目を削除',
      color: 'red',
      icon: 'mdi-trash-can'
    },
    cancel: {
      label: 'キャンセル',
      color: 'primary',
    },
  }).onOk(() => {
    //console.log('onDeleteItem', itemLocal.value);
        if (itemLocal.value.type === ITEM_TYPE.GROUPHEADER) {
          delete props.section.groupheaderList[itemLocal.value.itemKey];
        }

        formEditorStore.deleteFormSchemaItem({
          sectionKey: props.sectionKey,
          itemKey: props.item?.itemKey,
        });
        $q.notify({ color: 'positive', message: 'この項目を削除しました。' });
  }).onCancel(() => {
    //console.log('Cancel');
  });
};

const onDuplicateItem = (activeItemOrg: any) => {
  formEditorStore.duplicateFormSchemaItem({ sectionKey: props.sectionKey, item: itemLocal.value });
};

const addOption = () => {
  let _newOption = MultipleChoiceItem.createChoice(itemLocal.value.options.length + 1);
  _newOption = checkOption(_newOption);
  itemLocal.value.options.push(_newOption);

  latestOptionIndex.value = itemLocal.value.options.indexOf(_newOption);

  isEdited.value = isEditMode.value && !itemLocal.value.isNewItem ? true : false;
};

const removeOption = (index: number) => {
  if (itemLocal.value.default) {
    const targetOption = itemLocal.value.options[index];
    const isSelectedCheckBoxOption = Array.isArray(itemLocal.value.default) && itemLocal.value.default.includes(targetOption);
    const isSelectedMultipleOption = !Array.isArray(itemLocal.value.default) && itemLocal.value.default === targetOption;
    if (isSelectedCheckBoxOption) {
      const removedOptions = itemLocal.value.default.filter((option: any) => option !== targetOption);
      itemLocal.value.default = removedOptions;
      itemLocal.value.input = removedOptions;
    } else if (isSelectedMultipleOption) {
      itemLocal.value.default = null;
      itemLocal.value.input = null;
    }
  }
  itemLocal.value.options.splice(index, 1);

  isEdited.value = isEditMode.value && !itemLocal.value.isNewItem ? true : false;
};

const updateOption = (index: number, value: any) => {
  var _updateValue = value.trim();
  if (isEmpty(_updateValue)) {
    _updateValue = MultipleChoiceItem.createChoice(index + 1);
  }
  _updateValue = checkOption(_updateValue);
  itemLocal.value.options.splice(index, 1, _updateValue);
  forceRerender();

  let editIndex = -1;
  if (props.initialItem?.options && props?.initialItem.options[index] !== value) {
    editIndex = index;
  }
  if (isEditMode.value && !itemLocal.value.isNewItem && index <= originalOptionsIndex.value) {
    updateEditIndex(editIndex);
  }
};

const updateReservationCheckBox = (event: any) => {
  var value = event.value;
  if (event.key === "Large") {
    itemLocal.value.reservationCheckBox = value;
    if (value === false) {
      itemLocal.value.selectedLargeCategory = null;
    }
  } else {
    itemLocal.value.reservationSupCheckBox = value;
    if (value === false) {
      itemLocal.value.selectedMediumCategory = null;
    }
  }
};

const updateSelectedCategory = (event: any) => {
  var value = event.value;
  //console.log('updateSelectedCategory', event);
  if (event.key === "Large") {
    itemLocal.value.selectedLargeCategory = value;
  } else {
    itemLocal.value.selectedMediumCategory = value;
  }
};

const updateFixedCategory = (val: any) => {
  let _event = val.event;
  let _value = val.value;
  let _category = `isFixed${_event}Category`;
  if (!_value) {
    if (_event === "Large") {
      let _categoryMedium = `isFixedMediumCategory`;
      itemLocal.value[_categoryMedium] = _value;

      let _categorySmall = `isFixedSmallCategory`;
      itemLocal.value[_categorySmall] = _value;
    } else if (_event === "Medium") {
      let _categorySmall = `isFixedSmallCategory`;
      itemLocal.value[_categorySmall] = _value;
    }
  }
  itemLocal.value[_category] = _value;
};

const updateCategoryTitle = (event: any) => {
  let _event = event.key;
  let _value = event.value;
  let _category = `set${_event}CategoryTitle`;

  itemLocal.value[_category] = _value;
};

const updateEditIndex = (index: number) => {
  editIndex.value = index;

  let editItemKey = null;
  if (index !== -1) {
    editItemKey = itemLocal.value.itemKey;
  }
  eventBus.emit("updateEditItemKey", editItemKey);
};

const createEditedMultipleItemPayload = (itemKey: any, editedValue: any, index: number) => {
  const originalOption = props.initialItem?.sectionOptions[index];
  if (!originalOption) {
    return;
  }
  const originalValue = originalOption.option.value;

  return {
    itemKey,
    editedValue,
    originalValue,
  };
};

const checkOption = (value: any, index = 0) => {
  let _newOption = index ? `${value} (${index})` : value;
  let _checkOptionCnt = itemLocal.value.options.filter((option: any) => option === _newOption).length;
  if (_checkOptionCnt === 0) {
    return _newOption;
  }

  return checkOption(value, index + 1);
};

const moveOption = ({ newIndex, oldIndex }: { newIndex: number; oldIndex: number }) => {
  //console.log("Indexes", newIndex, oldIndex);
  //console.log("itemLocal.value.options before", itemLocal.value);
  const options = itemLocal.value.options || itemLocal.value.payTypeOptions;
  let option = options.splice(oldIndex, 1)[0];
  if(itemLocal.value.type === ITEM_TYPE.SELECT_PAY_TYPE) {
    itemLocal.value.payTypeOptions.splice(newIndex, 0, option);
  } else {
    itemLocal.value.options.splice(newIndex, 0, option);
  }
};

const onGroupHeaderChange = () => {
  //console.log('onGroupHeaderChange', itemLocal.value);
  //console.log(props.section)
  //console.log('itemLocal.value', itemLocal.value);
  //console.log('step1SurveyFormat.value', step1SurveyFormat.value);
  //console.log('formEditorStore.formSchema', formEditorStore.formSchema);
  if (itemLocal.value.type === ITEM_TYPE.GROUPHEADER) {
    props.section.groupheaderList[itemLocal.value.itemKey] = itemLocal.value.title;
  }
};

const addRadioSectionOption = () => {
  if (!itemLocal.value.sectionOptions) {
    itemLocal.value.sectionOptions = [];
  }
  let _newOption = SingleChoiceSectionItem.newSectionOption(itemLocal.value.sectionOptions.length + 1);
  itemLocal.value.sectionOptions.push(_newOption);
  latestOptionIndex.value = itemLocal.value.sectionOptions.indexOf(_newOption);
  checkRadioSectionOption(_newOption);
};

const removeRadioSectionOption = (index: number) => {
  const targetOption = itemLocal.value.sectionOptions[index].option.value;
  if (itemLocal.value.default && itemLocal.value.default === targetOption) {
    itemLocal.value.default = null;
    itemLocal.value.input = null;
  }
  itemLocal.value.sectionOptions.splice(index, 1);
};

const updateRadioSectionOption = (index: number, key: keyof SectionOption, value: string) => {
  let _updateValue =value.value ? value.value.trim() : value.trim();
  let _sectionOption: SectionOption = { option: { value: null }, groupheader: { value: null } };

  if (index >= 0 && itemLocal.value.sectionOptions[index]) {
    _sectionOption = itemLocal.value.sectionOptions[index];
  }

  if (_sectionOption[key]) {
    _sectionOption[key]["value"] = _updateValue;
  }

  switch (key) {
    case "option":
      if (isEmpty(_updateValue)) {
        _updateValue = SingleChoiceSectionItem.newOptionValue(index + 1);
      }
      _updateValue = checkRadioSectionOptionForUpdate(_updateValue);

      _sectionOption[key]["value"] = _updateValue;
      break;

    case "groupheader":
      break;

    default:
      break;
  }

  forceRerender();
  if (isEditMode.value && !itemLocal.value.isNewItem && index <= originalSectionOptionsIndex.value) {
    updateEditIndex(index);
    const editedMultipleItem = createEditedMultipleItemPayload(itemLocal.value.itemKey, _updateValue, index);
    editedMultipleItem && formsStore.setEditedMultipleItem(editedMultipleItem);
  }
};

const checkRadioSectionOption = (option: any, index = 0) => {
  let _newOption = { ...option };
  _newOption.option.value += index ? ` (${index})` : "";

  let _checkOptionCnt = itemLocal.value.sectionOptions.filter(
    (filter_option: any) => filter_option.option.value === _newOption.option.value
  ).length;
  if (_checkOptionCnt === 1) {
    return _newOption;
  }

  return checkRadioSectionOption(option, index + 1);
};

const checkRadioSectionOptionForUpdate = (optionValue: any, index = 0) => {
  let _newOptionValue = optionValue.trim();
  _newOptionValue += index ? ` (${index})` : "";

  let _checkOptionCnt = itemLocal.value.sectionOptions.filter(
    (filter_option: any) => filter_option.option.value === _newOptionValue
  ).length;

  if (_checkOptionCnt <= 1) {
    return _newOptionValue;
  }

  return checkRadioSectionOptionForUpdate(optionValue, index + 1);
};

const moveRadioSectionOption = (newIndex: number, oldIndex: number) => {
  let option = cloneDeep(itemLocal.value.sectionOptions.splice(oldIndex, 1)[0]);
  itemLocal.value.sectionOptions.splice(newIndex, 0, option);
};

const changeCountVaccines = (newValue: any) => {
  itemLocal.value.default = newValue;
  itemLocal.value.input = newValue;
  updateItemLocalToStore(null);
};

const initializePreviousVaccineMakerOptions = () => {
  itemLocal.value.sectionOptions = [
    {
      groupheader: {
        value: 20
      },
      option: {
        value: "ファイザー"
      }
    }
  ];
  itemLocal.value.input = null;
  itemLocal.value.default = null;
};

const setVaccinationIntervalType = (newValue: any) => {
  formEditorStore.updateVaccinationIntervalType({
    sectionKey: props.sectionKey,
    vaccinationIntervalType: newValue,
  });
};

const forceRerender = () => {
  componentKey.value += 1;
};

const setPreviousVaccineMakerAndDateOrEditModeSelect = () => {
  if (itemLocal.value.type == "previousVaccineMaker" || itemLocal.value.type == "previousVaccineDate") {
    removeIsAdminItemOnlyTrueSelectionFromUserUIOption();
    removeRequiredOnlyUserSelectionFromRequiredSettingOption();
  } else if (isEditMode.value && !itemLocal.value.isAdminItem && !itemLocal.value.isNewItem) {
    removeIsAdminItemOnlyTrueSelectionFromUserUIOption();
  }
};

const removeRequiredOnlyUserSelectionFromRequiredSettingOption = () => {
  isRequired.splice(1, 1);
};

const removeIsAdminItemOnlyTrueSelectionFromUserUIOption = () => {
  isNotEditableAndIsAdminItem.pop();
};

const updatePayTypeOption = (index: number, value: any) => {
  itemLocal.value.payTypeOptions[index].input = value;
};

const updateFiles = (maxFileNum: number) => {
  itemLocal.value.maxItems = maxFileNum;
};

onBeforeMount(() => {
  itemLocal.value = createLocalItem();
  if (props.section.surveyType === "corona") {
    if (fixed_item_titles.includes(itemLocal.value.title)) {
      itemLocal.value.title_disabled = true;
    } else {
      itemLocal.value.title_disabled = null;
    }
  }
  imageUrl.itemUrl = itemLocal.value.imageUrl ? itemLocal.value.imageUrl : {};
  resetIsRequired();
  resetIsNotEditableAndIsAdminItem();
  setPreviousVaccineMakerAndDateOrEditModeSelect();
});

onMounted(() => {
  if (eventBus) {
    eventBus.on("onDuplicateItem", (params) => {
      onDuplicateItem(params);
    });
  }
});

</script>

<style lang="less">
@item-padding: 16px;

.item-container {
  padding-top: 0;
  padding-left: @item-padding;
  padding-right: @item-padding;
  padding-bottom: @item-padding;
}

.item-menu-hover:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 17px;
  font-weight: 700;
}

.title-not-exist {
  color: #a9a9a9 !important;
}

.v-input.v-input--checkbox .v-label {
  font-size: 14px !important;
}

.itemImgArea .v-responsive__content {
  width: 100% !important;
  position: relative;
}

.itemImg .q-img__container {
  position: relative;
}

.itemImg div {
  padding-bottom: 0 !important;
}

.itemImg .itemImgBg {
  background: none;
}

.itemImgArea {
  position: relative;
}

.itemImgArea .v-image__image {
  max-height: 300px;
}

.itemImgArea .v-image.v-responsive {
  max-height: 300px;
}

.itemImgArea .itemDeleteBtn {
  position: absolute;
  right: 8px;
  top: 24px;
  z-index: 999;
  display: none !important;
  transition: all 0.6s;
}

.itemImgArea:hover .itemDeleteBtn {
  display: inline-flex !important;
}

.itemImgArea .v-responsive__content .v-btn {
  position: absolute;
  display: inline-flex;
  top: 50%;
  left: 50%;
  transform: translateY(-50%) translateX(-50%);
  margin: auto;
  display: none;
  transition: all 0.6s;
}

.itemImgArea .v-responsive__content:hover .v-btn {
  display: inline-flex !important;
}



.v-input .v-input__control .v-input--switch__thumb {
  box-shadow: 0px 2px 4px -1px rgb(0 0 0 / 20%), 0px 4px 5px 0px rgb(0 0 0 / 14%), 0px 1px 10px 0px rgb(0 0 0 / 12%) !important;
}
</style>