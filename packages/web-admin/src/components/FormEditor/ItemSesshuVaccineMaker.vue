<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-row no-gutters class="mb-2 mt-4">
      <q-col cols="12">
        <ul ref="itemSVMDrag" class="form-editor-drag">
          <li v-for="(option, index) in item.sectionOptions" :key="index + '/' + option.option.value"
            @mouseenter="hover = index" @mouseleave="hover = false">
            <q-row class="row">
              <q-col :class="`col-1 flex flex-center`">
                <q-icon size="sm" v-if="hover === index && isActive && !isEditMode" class="radio-drag-handle pl-1"
                  name="mdi-drag"></q-icon>
              </q-col>
              <q-col :class="`col-1 flex items-center`">
                <q-icon size="sm" @click="setDefaultValue(option.option.value)"
                  :name="isDefaultValue(option.option.value) ? 'mdi-radiobox-marked' : 'mdi-radiobox-blank'">
                </q-icon>
                <q-tooltip anchor="center right">
                  <span>新規入力時の初期値</span>
                </q-tooltip>
              </q-col>

              <q-col>
                <q-input :key="index + '/' + option.option.value" class="deactive pt-1" hide-details
                  :model-value="option.option.value" :readonly="isReadOnly(index)"
                  @change="(value: any) => updateRadioSectionOption(index, 'option', value)">
                </q-input>
              </q-col>

              <q-col class="col-4 flex items-end">
                  <q-btn flat v-if="isActive && isShowRemoveButton(index)" @click="removeRadioSectionOption(index)">
                    <q-icon name="mdi-close"></q-icon>
                  </q-btn>
              </q-col>

              <q-col :cols="cols.col2" v-if="item.attributes.hasAllowedDays && !hasCountVaccinesItem">
                <q-input v-model="option.groupheader.value" label="1回目接種翌日からの接種間隔日数" dense type="number" max-length="3"
                  max="999" min="1" step="1" suffix="日"></q-input>
              </q-col>
            </q-row>
          </li>
        </ul>
        <q-row v-if="isShowAddOption" no-gutters :class="{ 'mt-2': !isMultipleOptions }">
          <q-col>
            <div class="pt-2 pl-6">
              <q-btn color="primary" flat @click="addRadioSectionOption">
                <q-icon left name="mdi-radiobox-blank"></q-icon>選択肢を追加
              </q-btn>
            </div>
          </q-col>
        </q-row>
      </q-col>
    </q-row>
  </div>
</template>

<script setup lang="ts">
import { PropType, ref, computed, onBeforeMount, onMounted } from 'vue';



// old imports
// 旧インポート
import Sortable from "sortablejs";
import { cloneDeep } from "lodash";

// emits 
const emits = defineEmits<{
  (event: 'addRadioSectionOption'): void;
  (event: 'updateRadioSectionOption', ...payload: any[]): void;
  (event: 'removeRadioSectionOption', payload: any): void;
  (event: 'moveRadioSectionOption', ...payload: any[]): void;
}>();

// refs

const itemSVMDrag = ref<HTMLElement | null>(null);
const hover = ref<any>(false);

// props
const props = defineProps({
  item: { type: Object as PropType<any>, required: true },
  isActive: { type: Boolean as PropType<boolean>, required: true },
  isEditMode: { type: Boolean as PropType<boolean>, required: true },
  originalSectionOptionsIndex: { type: Number as PropType<number>, required: true },
  editIndex: { type: Number as PropType<number>, required: false },
  hasCountVaccinesItem: {
    type: Boolean as PropType<boolean>,
    default: false,
    required: false,
  }
});

// data
const itemKey = ref<any>(null);
const cols = ref<any>({
  col1: 7,
  col2: 5,
});

// methods
const setupDrag = (): void => {
  let list = itemSVMDrag.value;
  const _self = this;
  if (list) {
    Sortable.create(list, {
      handle: ".radio-drag-handle", // Use handle so user can select text
      animation: 150,
      chosenClass: "white",
      onEnd({ newIndex, oldIndex }) {
        emits("moveRadioSectionOption", newIndex, oldIndex);
      },
    });
  }
};
const addRadioSectionOption = (): void => {
  emits("addRadioSectionOption");

  let lastSectionOption = props.item.sectionOptions[props.item.sectionOptions.length - 1];
  lastSectionOption.groupheader.value = 20;
};
const updateRadioSectionOption = (index: number, key: any, value: any): void => {
  emits("updateRadioSectionOption", index, key, value);
};
const removeRadioSectionOption = (index: number) => {
  emits("removeRadioSectionOption", index);
};
const setDefaultValue = (value: any): void => {
  if (props.item.default === value) {
    props.item.default = null;
    props.item.input = null;
  } else {
    props.item.default = value;
    props.item.input = value;
  }
};
const isDefaultValue = (value: any): boolean => {
  return props.item.default === value;
};
const isReadOnly = (index: number) => {
  const isNotEditableIndex = props.editIndex !== index && index <= props.originalSectionOptionsIndex && props.editIndex !== -1;
  return props.isEditMode && !props.item.isNewItem && isNotEditableIndex;
};
const isShowRemoveButton = (index: number): boolean => {
  if (props.isEditMode) {
    return props.item.isNewItem ? isMultipleOptions.value : index > props.originalSectionOptionsIndex;
  } else {
    return isMultipleOptions.value;
  }
};

// computed
const isMultipleOptions = computed((): boolean => {
  return props.item.sectionOptions.length > 1;
});
const isShowAddOption = computed((): boolean => {
  const isPreviousVaccineMaker = props.item.type === 'previousVaccineMaker';
  if (!props.isActive || isPreviousVaccineMaker) {
    return false;
  }

  const originalSectionOptionsLength = props.originalSectionOptionsIndex + 1;
  if (props.isEditMode) {
    return props.item.isNewItem ? true : props.item.sectionOptions.length === originalSectionOptionsLength;
  }

  return true;
});

// hooks

onBeforeMount(() => {
  if (props.item.attributes.hasAllowedDays) {
    cols.value.col1 = 7;
  } else {
    cols.value.col1 = 12;
  }
});


onMounted(() => {
  itemKey.value = cloneDeep(itemKey.value);
  setupDrag();
});

</script>

<style lang="less" scoped>
.radio-drag-handle {
  cursor: move;
}
</style>
