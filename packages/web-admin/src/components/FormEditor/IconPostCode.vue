<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <svg
    version="1.1"
    id="_x32_"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    x="0px"
    y="0px"
    viewBox="0 0 512 512"
    style="padding: 2px; width: 24px; height: 24px; opacity: 0.87; align-item: center; justify-content: center"
    xml:space="preserve"
  >
    <g>
      <rect x="2.823" class="st0" width="506.354" height="108.506" style="fill: rgb(75, 75, 75)"></rect>
      <polygon
        class="st0"
        points="2.823,307.432 201.75,307.432 201.75,512 310.25,512 310.25,307.432 509.177,307.432 509.177,198.927 
            2.823,198.927 	"
        style="fill: rgb(75, 75, 75)"
      ></polygon>
    </g>
  </svg>
</template>
