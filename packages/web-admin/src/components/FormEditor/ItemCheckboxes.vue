<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-row class="row q-mb-md q-mt-md q-col-gutter-md full-width">
      <q-col class="col-8 full-width">
        <ul ref="itemCheckBoxDrag" class="form-editor-drag">
          <li v-for="(option, index) in props.item.options" :key="option + index" @mouseenter="hover = index"
            @mouseleave="hover = false" class="flex items-center self-start">
            <span v-if="isEditMode === false" class="col-auto">
              <q-icon :class="`${hover === index && isActive ? '' : 'invisible'} checkbox-drag-handle`" name="mdi-drag"
                size="sm"></q-icon>
            </span>
            <q-icon class="col-2 cursor-pointer" @click="toggleDefaultValue(option)"
              :name="isDefaultValue(option) ? 'mdi-checkbox-marked' : 'mdi-checkbox-blank-outline'" size="sm">
              <q-tooltip anchor="center right" self="center left" :offset="[2, 2]">
                <span>新規入力時の初期値</span>
              </q-tooltip></q-icon>
            <q-input :key="option + index" class="deactive q-pt-sm q-pl-sm" hide-details :model-value="option"
              @change="(value: any) => onChangeOption(value, index)" :readonly="isReadOnly(index) ">
            </q-input>



            <q-btn class="col-2 flex rounded" size="sm" flat rounded @click="removeOption(index)"
              v-if="isShowRemoveButton(index)">
              <q-icon name="mdi-close" size="sm" class="self-end" color="grey"></q-icon>
            </q-btn>

          </li>
        </ul>
        <q-row v-if="isShowAddOption" :class="`row ${ !isMultipleOptions ? 'q-mt-sm' : ''}`">
          <q-col>
            <div class="q-pt-sm">
              <q-btn @click="addOption" flat :ripple="false" class="q-pl-none">
                <q-icon name="mdi-checkbox-blank-outline" size="sm" left></q-icon>
                <span :style="`color: var(--q-primary)`">選択肢を追加</span></q-btn>
            </div>
          </q-col>
        </q-row>
      </q-col>
    </q-row>
    <template v-if="props.isActive">
      <q-separator class="q-my-md"></q-separator>
      <q-item-label header class="q-pl-lg">回答の検証を設定（任意）</q-item-label>
      <Selection :item="props.item" :isEditMode="props.isEditMode" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { PropType, computed, onMounted, ref } from 'vue';

// old imports
// 旧インポート
import Sortable from "sortablejs";
import Selection from "@/components/FormEditor/ValidatorComponents/Selection.vue";

// emits 
const emits = defineEmits<{
  (event: 'addOption'): void;
  (event: 'removeOption', payload: any): void;
  (event: 'updateOption', ...payload: any[]): void;
  (event: 'moveOption', ...payload: any[]): void;
}>();

// refs

const itemCheckBoxDrag = ref<HTMLElement | null>(null);

const hover = ref<any>(false);

// props
const props = defineProps({
  item: { type: Object as PropType<any>, required: true },
  isActive: { type: Boolean as PropType<boolean>, required: true },
  isEditMode: { type: Boolean as PropType<boolean>, required: true },
  editIndex: { type: Number as PropType<number>, required: false },
  originalOptionsIndex: { type: Number as PropType<number>, required: true }
});

// methods
const setupDrag = (): void => {
  let list = itemCheckBoxDrag.value;
  if (list) {
    Sortable.create(list, {
      handle: ".checkbox-drag-handle", // Use handle so user can select text
      animation: 150,
      chosenClass: "white",
      onEnd({ newIndex, oldIndex }) {
        emits("moveOption", newIndex, oldIndex);
      },
    });
  }
};
const addOption = (): void => {
  emits("addOption");
};
const removeOption = (index: number): void => {
  emits("removeOption", index);
};
const onChangeOption = (value: any, index: number): void => {
  emits("updateOption", index, value);
};
const toggleDefaultValue = (value: any): void => {
  let _index = props.item.default ? props.item.default.findIndex((obj: any) => obj === value) : -1;
  if (_index >= 0) {
    props.item.default.splice(_index, 1);
  } else if (Array.isArray(props.item.default)) {
    props.item.default.push(value);
  } else {
    props.item.default = [...value];
  }
};
const isDefaultValue = (value: any): any => {
  return props.item.default && Array.isArray(props.item.default) && props.item.default.includes(value);
};
const isReadOnly = (index: number) => {
  const isNotEditableIndex = props.editIndex !== index && props.originalOptionsIndex >= index && props.editIndex !== -1;
  //console.log(props.item);
  return props.isEditMode && !props.item.isNewItem && isNotEditableIndex;
};
const isShowRemoveButton = (index: number): boolean => {
  if (props.isEditMode) {
    return props.item.isNewItem ? isMultipleOptions.value : index > props.originalOptionsIndex;
  } else {
    return isMultipleOptions.value;
  }
};

// computed
const isMultipleOptions = computed((): boolean => {
  return props.item.options.length > 1;
});
const isShowAddOption = computed((): boolean => {
  if (!props.isActive) {
    return false;
  }

  const originalOptionsLength = props.originalOptionsIndex + 1;
  if (props.isEditMode) {
    return props.item.isNewItem ? true : props.item.options.length === originalOptionsLength;
  }

  return true;
});

// hooks

onMounted(() => {
  setupDrag();
});

</script>

<style lang="less" scoped>
.checkbox-drag-handle {
  cursor: move;
}
</style>
