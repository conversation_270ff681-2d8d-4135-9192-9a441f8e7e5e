<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-dialog v-model="show" style="max-width: 670px;" eager>
    <q-card class="q-pa-sm primary-top-border" style="max-width: 670px;">
      <q-toolbar flat style="height: 75px" class="q-px-sm">
        <q-toolbar-title class="text-bold" style="font-size:26px;">キー設定</q-toolbar-title>
      </q-toolbar>
      <KeySettingModalInformation :isMemberForm="isMemberForm" />
      <q-space></q-space>
      <q-separator></q-separator>
      <ul ref="moveSectionsDrag" class="form-editor-drag">
        <li v-for="(question, index) in reorderQuestions" :key="question.itemKey" class="q-px-sm primary-border-left">
          <Alert class="q-py-none q-my-none q-pl-none" dense colored-border border="left">
            <q-row class="text-center justify-between row" no-gutters>
              <q-col>
                <q-item two-line class="q-px-none" left>
                  <q-item-section style="max-width: 250px;">
                    <q-item-label class="text-truncate flex justify-left" style="font-size:14px;">
                      {{
                        question.title || "無題質問"
                      }}
                    </q-item-label>
                    <q-item-label class="text-sub-1 text-blue-grey-8 flex justify-left">質問 {{ index + 1 }} / {{ reorderQuestions.length
                      }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-col>
              <q-col class="q-mt-none items-center">
                <div class="row q-col-gutter-xs" style="min-width:380px;">
                  <div class="col-auto">
                    <q-checkbox v-if="isMemberForm &&
                    reorderQuestions[index].type != 'memberNumber' &&
                    !reorderQuestions[index].isAdminItem" label="重複キー" color="primary" hide-details
                    class="q-mt-none q-mr-md" v-model="reorderQuestions[index].duplicateKeyCheck"
                    :disable="isEditMode || (!reorderQuestions[index].isRequired.value && !reorderQuestions[index].isRequiredForUser.value)"
                    @change="handleChangeDuplicateKeyCheck(index)"></q-checkbox>
                  <span class="q-pt-xs flex items-center q-mr-md q-pl-sm"
                    style="font-size:14px; color:rgba(0,0,0,.38) !important;" v-else-if="isMemberForm"><q-icon
                      name="mdi-checkbox-blank-off" size="sm"></q-icon>重複キー</span>
                  </div>

                  <div class="col-auto">
                    <q-checkbox label="インデックスキー" color="primary" hide-details class="q-mt-none q-mr-md" v-if="(reorderQuestions[index].type == 'text' ||
                    reorderQuestions[index].type == 'number' ||
                    reorderQuestions[index].type == 'postcode' ||
                    reorderQuestions[index].type == 'address') &&
                    reorderQuestions[index].isIndexable"
                    :disable="isEditMode && !reorderQuestions[index].isNewItem || (isEditMode && isMemberForm)"
                    v-model="reorderQuestions[index].isIndexable.value"></q-checkbox>
                  <span class="q-pt-xs flex items-center q-mr-md"
                    style="font-size:14px; color:rgba(0,0,0,.38) !important;" v-else><q-icon
                    class="q-pr-sm q-pl-sm"  
                    name="mdi-checkbox-blank-off" size="sm" ></q-icon>インデックスキー</span>
                  </div>

                  <div class="col-auto">
                    <q-checkbox label="照会キー" color="primary" hide-details class="q-mt-none"
                      v-if="
                        !isMemberForm &&
                        reorderQuestions[index].type !== 'groupheader' &&
                        reorderQuestions[index].type !== 'guide' &&
                        reorderQuestions[index].type !== 'linkbutton' &&
                        reorderQuestions[index].type !== 'reservation' &&
                        reorderQuestions[index].type !== 'countVaccines'&&
                        reorderQuestions[index].type !== 'selectProducts' &&
                        reorderQuestions[index].type !== 'selectPayType' &&
                        reorderQuestions[index].isSearchable
                      "
                      v-model="reorderQuestions[index].isSearchable.value"
                      :disable="!isAppending || (isEditMode&& !reorderQuestions[index].isNewItem) || reorderQuestions[index].isAdminItem || reorderQuestions[index].type=='previousVaccineMaker'|| reorderQuestions[index].type=='previousVaccineDate'||!reorderQuestions[index].isRequired.value"
                      @change="handleChangeIsSearchableItem( index )"></q-checkbox>
                  <span class="q-pt-xs flex items-center" style="font-size:14px;color:rgba(0,0,0,.38)!important;"
                    v-else-if="!isMemberForm"><q-icon 
                    class="q-pr-sm"
                    name="mdi-checkbox-blank-off" size="sm"></q-icon>照会キー</span>
                  </div>

                  <div class="col-auto">
                    <q-checkbox label="個人情報含む" color="primary" hide-details class="q-mt-none"
                      v-if="!isMemberForm && isPersonalInformationCandidate(reorderQuestions[index])"
                      v-model="reorderQuestions[index].isPersonalInfo.value"></q-checkbox>
                    <span class="q-pt-sm q-pl-sm flex items-center" style="font-size:14px;color:rgba(0,0,0,.38)!important;"
                      v-else-if="!isMemberForm"><q-icon 
                      class="q-pr-sm"
                      name="mdi-checkbox-blank-off" size="sm"></q-icon>個人情報含む</span>
                  </div>
                  
                  
                  
                </div>
              </q-col>
            </q-row>
          </Alert>
          <q-separator></q-separator>
        </li>
      </ul>
      <q-separator></q-separator>
      <q-card-actions class="q-pa-lg flex justify-end">
        <q-row no-gutters>
          <q-space></q-space>
          <q-btn color="blue-grey" outline class="q-px-md q-mr-md" style="height: 44px" elevation="0"
            @click="show = false">
            帳票作成に戻る
          </q-btn>
          <q-btn color="primary" class="q-px-md" style="height: 44px" elevation="0" @click="updateModal($event)" :disable="reorderQuestions.filter(v => v.isIndexable.value === true).length > 2">
            保存
          </q-btn>
        </q-row>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { PropType, ref, computed, watch, nextTick, onMounted, } from 'vue';

import { useFormEditorStore } from '@stores/modules/formEditor/index';
import { useQuasar } from 'quasar';

// old imports
// 旧インポート
import { cloneDeep } from "lodash";
import { UPDATE_SURVEY_SCHEMA } from "@stores/mutation-types";
import ValidateSurveyForm from "@/model/Form/ValidateSurveyForm";
import KeySettingModalInformation from "./KeySettingModalInformation.vue";
import { INIT_ITEM_ADDITIONAL_PROPS } from '@stores/modules/formEditor/formEditor.constants'
import { ITEM_TYPE } from '@stores/modules/formEditor/formEditor.constants'

import { usePermissionHelper } from '@/mixins/PermissionHelper';

const formEditorStore = useFormEditorStore();

const { hasActionPermission, showActionPermissionError } = usePermissionHelper();

// emits 
const emits = defineEmits<{
  (event: 'close'): void;
  (event: 'onUpdateTeams'): void;
}>();
const $q = useQuasar();

// props
const props = defineProps({
  visible: Boolean as PropType<boolean>,
  close: Function,
  questions: [] as PropType<any>,
  isAppending: Boolean as PropType<boolean>,
  itemTypes: [] as PropType<any>,
});

// data
const reorderQuestions = ref<any>([]);
const isMember = ref<boolean>(false);
const isEditM = ref<boolean>(false);
const resetKeyFlg = ref<boolean>(false);

// methods
const updateSurveySchema = formEditorStore.updateSurveySchema;
const onSaveReorderQuestions = (): void => {
  updateSurveySchema(reorderQuestions.value);
  show.value = false;
};
const isPersonalInformationCandidate = (item: any): boolean => {
  return (
    item.type === 'text' ||
    item.type === 'email' ||
    item.type === 'phone' ||
    item.type === 'number' ||
    item.type === 'postcode' ||
    item.type === 'address' ||
    item.type === 'textarea' ||
    item.type === 'birthday'
  );
};
const fillEmptyItems = (item: any): any => {
  if (!item.isPersonalInfo) {
    item.isPersonalInfo = { ...INIT_ITEM_ADDITIONAL_PROPS.isPersonalInfo };
  }
  return item;
};
const updateModal = (event: any): any => {
  if (!hasActionPermission("click", "backendRequest")) {
    showActionPermissionError();
    event.stopPropagation();
    return;
  }
  nextTick(async () => {
    let chkErr: any = validateData();
    if (chkErr !== null) {
      $q.notify({ message: chkErr, type: "negative" });
      return;
    }
    chkErr = await ValidateSurveyForm.checkItem(formEditorStore.step1SurveyFormat);
    if (chkErr !== null) {
      $q.notify({ message: chkErr, type: "negative" });
      return;
    } else {
      event.stopPropagation();
      updateSurveySchema(reorderQuestions.value);
      show.value = false;
      emits('onUpdateTeams');
    }
  });
};
const validateData = (): string | null => {
  let questions_amount = reorderQuestions.value.length;
  if (questions_amount === 0) {
    return "質問は１個以上を登録して下さい。";
  }
  if (questions_amount > 100) {
    return "質問は100個以下を登録して下さい。";
  }
  if (checkNumberOfIndexableQuestions(reorderQuestions.value) > 2) {
    if (existedMemberNumber(reorderQuestions.value)) {
      return "活性されたインデックスキーオプションの質問は1個制限です。";
    }
    return "活性されたインデックスキーオプションの質問は2個制限です。";
  }
  if (checkNumberOfIndexAndSearchQuestions(reorderQuestions.value) > 1) {
    return "インデックスキーと照会キーの両方がONのアイテム数は、帳票内で1個制限です。";
  }
  return null;
};
const checkNumberOfIndexableQuestions = (surveySchema: any): any => {
  return surveySchema.filter((obj: any) => (
    obj.type == 'text' ||
    obj.type == 'number' ||
    obj.type == 'postcode' ||
    obj.type == 'address')
    && obj.isIndexable && obj.isIndexable == true).length;
};
const checkNumberOfIndexAndSearchQuestions = (surveySchema: any): any => {
  return surveySchema.filter((obj: any) => (
    obj.type == 'text' ||
    obj.type == 'number' ||
    obj.type == 'postcode' ||
    obj.type == 'address')
    && obj.isIndexable && obj.isIndexable == true && obj.isSearchable && obj.isSearchable.value == true).length;
};
const existedMemberNumber = (surveySchema: any): boolean => {
  var found = false;
  for (var i = 0; i < surveySchema.length; i++) {
    if (surveySchema[i].type && surveySchema[i].type === "memberNumber") {
      found = true;
      break;
    }
  }
  return found;
};
const isTextType = (localType: any): any => {
  try {
    let textTypeChildren = props.itemTypes.find((obj: { title: string; }) => obj.title == "記述式").childs.map((obj: { value: any; }) => obj.value);
    return textTypeChildren.includes(localType);
  } catch (err: any) {
    return false;
  }
};
const handleChangeIsSearchableItem = (index: number): void => {
  if (reorderQuestions.value[index].isSearchable.value) {
    reorderQuestions.value[index].isRequired.value = true;
  }
};
const handleChangeDuplicateKeyCheck = (index: number): void => {
  if (reorderQuestions.value[index].duplicateKeyCheck) {
    reorderQuestions.value[index].isRequired.value = true;
  }
};

// computed
const isEditMode = computed(() => formEditorStore.isEditMode);
const formType = computed(() => formEditorStore.formType);
const isMemberForm = computed<boolean>(() => {
  return formEditorStore.formType === "member" ? true : false;
});
const show = computed({
  get() {
    return props.visible;
  },
  set(value: any) {
    if (!value) {
      emits("close");
    }
  },
});

// watch
watch(() => props.questions, (val) => {
  if (val) {
    reorderQuestions.value = cloneDeep(val).map((v: any) => fillEmptyItems(v))
  }
},
  { immediate: true, deep: true }
);

watch(() => props.visible, (val) => {
  if (!val) {
    updateSurveySchema(reorderQuestions.value);
  }
});
</script>

<style lang="less" scoped>
.primary-border-left {
  border-left: 6px solid var(--q-primary);
  border-radius: 4px;
}

.text-sub-1 {
  font-size: 12px;
}

.primary-top-border {
  border-top: 6px solid var(--q-primary);
  border-radius: 4px;
}
</style>