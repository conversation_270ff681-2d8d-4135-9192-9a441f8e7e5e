<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-row no-gutters class="mb-8 pl-6">
      <q-col cols="12" md="8">
        <!-- TODO: Add Postal Validator -->
        <q-input
          type="text"
          hide-details="true"
          :disable="!isActive"
          v-model="item.default"
          placeholder="郵便番号・デフォルト（任意）"
          :rules="validatePostalCode"
          :maxlength="item.length.limitation || 7"
          counter
        ></q-input>
      </q-col>
    </q-row>
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';

// props
const props = defineProps({
  item: { type: Object as PropType<any>, required: true },
  isActive: { type: Boolean as PropType<boolean>, required: true }
});

// methods

const validatePostalCode = [
  (v: string) => !!v || '郵便番号を入力してください',
  (v: string) => {
    const postalCodeRegex = /^\d{7}$/;
    return postalCodeRegex.test(v) || '郵便番号の形式が正しくありません';
  }
];
</script>