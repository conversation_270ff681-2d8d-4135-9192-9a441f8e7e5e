<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-row no-gutters class="q-mb-sm q-mt-md">
      <ul ref="itemRadioDrag" class="form-editor-drag">
        <li v-for="(option, index) in item.payTypeOptions" :key="option.payType" class="row items-center"
          @mouseenter="hoverIndex = index" @mouseleave="hoverIndex = null">
          <div v-if="isEditMode === false" class="col-auto items-center">
            <div class="row q-px-none">
              <q-icon v-if="isActive" class="radio-drag-handle q-px-xs col-2" name="mdi-drag" size="21px"
              :style="{ opacity: hoverIndex === index ? 1 : 0 }"></q-icon>
              <q-btn @click="setDefaultValue(option.input)" class="col-4 q-mr-xs" style="width: 25px"
                :color="isDefaultValue(option.input) ? 'primary' : ''" :icon="getIconName(option.input)" flat
                :ripple="false" >
                <q-tooltip right>
                  <span>新規入力時の初期値</span>
                </q-tooltip>
              </q-btn>
            </div>

          </div>
          <div style="width: 25px" class="q-mr-sm" v-else> 
            <q-btn @click="setDefaultValue(option.input)" style="width: 25px"
                :disable="isEditMode"
                :color="isDefaultValue(option.input) ? 'primary' : ''" :icon="getIconName(option.input)" flat
                :ripple="false" >
                <q-tooltip right>
                  <span>新規入力時の初期値</span>
                </q-tooltip>
              </q-btn>
          </div>
          <q-input :key="option.payType" class="deactive q-mt-xs col-8" hide-details :model-value="option.input"
            :readonly="isEditMode" @update:model-value="(value: any) => onChangeOption(value, index)">
          </q-input>
        </li>
      </ul>
    </q-row>
  </div>
</template>

<script setup lang="ts">
import { PropType, onMounted, ref } from 'vue';

// old imports
// 旧インポート
import { Option } from '@/model/Form/SelectPayTypeItem';
import Sortable from "sortablejs";

const hoverIndex = ref<number | null>(null);

// emits 
const emits = defineEmits<{
  (event: 'updatePayTypeOption', ...payload: any[]): void;
  (event: 'moveOption', ...payload: any[]): void;
}>();

// props
const props = defineProps({
  item: {
    type: Object as PropType<{
      default: string,
      input: string,
      payTypeOptions: Option[],
    }>, required: true
  },
  isActive: { type: Boolean as PropType<boolean>, required: true },
  isEditMode: { type: Boolean as PropType<boolean>, required: true }
});

// refs

const itemRadioDrag = ref();

// methods
const setupDrag = (): void => {
  let list = itemRadioDrag.value;
  if (list) {
    Sortable.create(list, {
      handle: ".radio-drag-handle", // Use handle so user can select text
      animation: 150,
      chosenClass: "white",
      onEnd({ newIndex, oldIndex }) {
        emits("moveOption", { newIndex, oldIndex });
      },
    });
  }
};

const isDefaultValue = (value: any): boolean => {
  return props.item.default === value;
};

const onChangeOption = (value: any, index: number): void => {
  emits("updatePayTypeOption", index, value);
};

const setDefaultValue = (value: any): void => {
  if (props.item.default === value) {
    props.item.default = "";
  } else {
    props.item.default = value;
  }
};

const getIconName = (value: any): string => {
  return isDefaultValue(value) ? 'mdi-radiobox-marked' : 'mdi-radiobox-blank';
};

onMounted(() => {
  setupDrag();
});

</script>

<style lang="less" scoped>
.radio-drag-handle {
  cursor: move;
}
</style>
