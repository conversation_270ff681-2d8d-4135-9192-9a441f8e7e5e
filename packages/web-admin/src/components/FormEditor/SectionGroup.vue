<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <PageBreak
      :index="index"
      :section="section"
      @chgTemplateParent="chgTemplateParent"
      :duplicateFlg="duplicateFlg"
    />

    <ul :id="`list-items-${props.section?.key}`" class="form-editor-drag">
      <li v-for="(item, index) in props.section?.surveySchema" :key="item.itemKey">
        <Item
          :item="item"
          :sectionKey="props.section?.key"
          :section="cloneSection"
          :initialItem="initialItem[index] || {}"
          :editedItemKey="editedItemKey"
          @updateEditItemKey="updateEditItemKey"
          ref="inputForm"
        />
      </li>
    </ul>

    <LinkMemberSurveyForms v-if="isMemberForm" :sectionKey="props.section?.key" />
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from "lodash";
import PageBreak from "@components/FormEditor/PageBreak.vue";
import LinkMemberSurveyForms from "@components/FormEditor/LinkMemberSurveyForms.vue";
import Item from "@components/FormEditor/Item.vue";
import Sortable from "sortablejs";
import "@assets/styles/form-editor.less";
import { computed, onBeforeMount, onMounted, reactive, Ref, ref } from "vue";
import { useRoute } from "vue-router";
import { useFormsStore } from "@/stores/modules/forms";
import { useFormEditorStore } from "@stores/modules/formEditor";
import { ITEM_TYPE } from "@/stores/modules/formEditor/formEditor.constants";

import { useQuasar } from "quasar";

const $q = useQuasar();

interface LocalState {
  editedItemKey: any;
  initialItem: any;
  cloneSection: any;
}

const route = useRoute();

const props = defineProps({
  section: Object,
  index: Number,
  duplicateFlg: Boolean,
});

const formsStore = useFormsStore();
const formEditorStore = useFormEditorStore();

const editedItemKey = ref("");
const initialItem = ref();
const cloneSection = ref<any>({});

const isMemberForm = computed(() => {
  return formEditorStore.formType === "member";
});

const formType = computed(() => {
  return route.params.formType || "survey";
});

const moveQuestion = (newIndex: number, oldIndex: number) => {
  //remove old position
  let dragSection = props.section?.surveySchema.splice(oldIndex, 1);
  //move to new index
  props.section?.surveySchema.splice(newIndex, 0, dragSection[0]);
};

const chgTemplateParent = (template: any) => {
  formEditorStore.initFormSchema({ template, formType });
};

const updateEditItemKey = (itemKey: string) => {
  editedItemKey.value = itemKey;
};

onBeforeMount(async () => {
  initialItem.value = cloneDeep(props.section?.surveySchema);
  cloneSection.value = cloneDeep(props.section);
  //await formEditorStore.fetchPaymentServiceList(); // Not implemented yet.
});

onMounted(() => {
  const table = document.getElementById(`list-items-${props.section?.key}`);
  if(!table) return;
  const sortable = Sortable.create(table, {
    group: "section-group",
    handle: ".item-drag-handle",
    animation: 150,
    chosenClass: "grey",
    onEnd: ({ newIndex, oldIndex }: any) => {
      moveQuestion(newIndex, oldIndex);
    },
  });

  // groupheaderList
  cloneSection.value = cloneDeep(props.section);
  cloneSection.value.groupheaderList = {};
  cloneSection.value.surveySchema.forEach((element: any) => {
    if (element.type === ITEM_TYPE.GROUPHEADER) {
      if (!cloneSection.value.groupheaderList[element.itemKey]) {
        cloneSection.value.groupheaderList[element.itemKey] = element.title;
      }
    }
  });
  cloneSection.value.radioGroupHeaderList = cloneDeep(cloneSection.value.groupheaderList);
});
</script>
