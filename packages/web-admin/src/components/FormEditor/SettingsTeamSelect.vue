<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <template v-if="isMemberForm">
      <q-row justify="center">
        <div fluid class="px-2 py-2">
          <q-col cols="12" class="referSettingFixedMsg blue-grey--text text--darken-4">
            連携帳票の閲覧権限設定を参照してください。<br />
            連携帳票のいずれかに対して閲覧権限を持つユーザーがこの帳票の閲覧権限を持ちます。
          </q-col>
        </div>
      </q-row>
    </template>
    <template v-else>
      <q-row justify="center">
        <div fluid class="px-6 py-1">
          <q-list three-line class="pb-4">
            <q-item class="px-0">
              <q-item-section class="py-0">
                <q-col cols="12" class="px-0 py-0">
                  <div class="body-2 text-blue-grey-6 text-bold q-mb-sm">帳票権限設定

                    <q-icon name="mdi-information-outline">
                      <q-tooltip anchor="bottom right" class="blue-grey--text text-bold" style="font-size: 15px;">
                        この帳票を閲覧できるチームをクリックして選択してください。複数選択可能です。
                      </q-tooltip>
                    </q-icon>

                  </div>
                  <q-select use-input v-model="currentTeam" :options="teamList" option-label="teamName"
                    option-value="teamId" :error-messages="validateCurrentTeam" dense multiple outlined hide-details
                    :placeholder="!currentTeam.length ? 'チームを選択' : ''" @filter="updateFilter">
                    <template v-slot:option="{ itemProps, opt, selected, toggleOption }">
                      <q-item v-bind="itemProps">
                        <q-item-section side>
                          <q-checkbox :model-value="selected" @update:model-value="toggleOption(opt)" />
                        </q-item-section>
                        <q-item-section>
                          <q-item-label>{{ opt.teamName }}</q-item-label>
                        </q-item-section>
                      </q-item>
                    </template>
                  </q-select>
                </q-col>
              </q-item-section>
            </q-item>
          </q-list>
          <q-separator class="q-my-md"></q-separator>
          
          <SetCalendarForBunruiForm v-if="hasBunruiItem" :surveyTeams="selectedTeams" :sectionKey="props.sectionKey"
            @catchCpParent="catchCpParent" />

          <q-card-actions class="px-0 mb-0">
            <q-space></q-space>
            <q-btn :stlye="`height: 44px; width: 63px;`" class="q-ma-md" elevation="0" color="primary" :disable="disableUpdate" @click="
                hasActionPermission('hideButton', 'FormEditor_TeamSelectDialog_updateTeamSetting')
                  ? ''
                  : onUpdateTeamSetting()
              ">保存</q-btn>
          </q-card-actions>
        </div>
      </q-row>
    </template>
  </div>
</template>

<script setup lang="ts">
import { PropType, ref, computed, onMounted, watch } from 'vue';

import { useFormEditorStore } from '@stores/modules/formEditor/index';
import { useUsersStore } from '@stores/modules/users/index';
import { useFormsStore } from '@stores/modules/forms/index';
import { useQuasar } from 'quasar';

import { usePermissionHelper } from '@/mixins/PermissionHelper';

// old imports
// 旧インポート
import { cloneDeep, update } from "lodash";
import SetCalendarForBunruiForm from "./SetCalendarForBunruiForm/Index.vue";
import { useMemberStore } from '@/stores/modules/member';
import { stringTruncate } from '@/utils/stringUtils';

const formEditorStore = useFormEditorStore();
const usersStore = useUsersStore();
const memberStore = useMemberStore();
const formsStore = useFormsStore();

const { hasActionPermission } = usePermissionHelper();

interface TeamDictionary {
  [key: string]: string;
}

// emits 
const emits = defineEmits<{
  (event: 'isTeamSelectInputProperlyAfter', payload: any): void;
  (event: 'close'): void;
}>();
const $q = useQuasar();

// props
const props = defineProps({
  visible: Boolean as PropType<boolean>,
  sectionKey: String as PropType<string>
});

// data
const currentTeam = ref<any>([]);
const teamSelectRule = ref<any>([(v: string) => (v && v.length >= 1) || "1つ以上のチームを選択してください。"]);
const disableUpdate = ref<boolean>(false);
const categoriesPermissions = ref<any>([]);

const filterTerm = ref<string>("");

// methods
const updateSurveyTeams = formEditorStore.updateFormSchemaTeamList;
const updateFormSchemaElement = formEditorStore.updateFormSchemaElement;
const fetchTeamList = usersStore.fetchTeamList;
const updateSurveyConfig = formsStore.updateSurveyConfig;
const updateMemberConfig = memberStore.updateMemberSurveyConfig;
const fetchSurveyConfigs = formsStore.fetchSurveyConfigs;
const fetchSurveyConfigsById = formsStore.fetchSurveyConfigsById;
const catchCpParent = async (cp: any): Promise<void> => {
  categoriesPermissions.value = cp;
};
const onUpdateTeamSetting = async (): Promise<void> => {
  updateSurveyTeams({
    key: props.sectionKey,
    surveyTeams: currentTeam.value,
  });
 updateFormSchemaElement({
    name: "categoriesPermissions",
    categoriesPermissions: categoriesPermissions.value,
  });
  await onSurveyRegisterConfirm();
};
const onSurveyRegisterConfirm = async (): Promise<void> => {
  disableUpdate.value = true;
  let updateFormSchema = cloneDeep(formSchema.value);
  updateFormSchema[0].surveyTeams = currentTeam.value;
  updateFormSchema[0].categoriesPermissions = categoriesPermissions.value;
  let _result = undefined;
  await fetchSurveyConfigsById(formSchema.value[0].surveyId).then(response => {
    updateFormSchema[0].updatedAt = response.data.updatedAt;
  })
  if (isMemberForm.value) {
    _result = await updateMemberConfig(updateFormSchema[0]);
  } else {
    _result = await updateSurveyConfig(updateFormSchema[0]);
  }

  if (_result) {
    $q.notify({ message: "設定を保存しました", type: "positive", icon: "mdi-check-circle" });
    emits("isTeamSelectInputProperlyAfter", true);
  } else {
    if (formsStore.updateSurveyConfigError) {
      $q.notify({ message: formsStore.updateSurveyConfigError, type: "negtive", icon: "mdi-alert-circle" });
    }
  }
  disableUpdate.value = false;
};
const fetchTeamListData = async () => {
  await fetchTeamList();
};
const getBytesLength = (string: string): number => {
  return new TextEncoder().encode(string).length;
};
const ruleLinkedTeams = (currentTeams: any): any => {
  // Rule for checking if required teams from linked survey configs are not selected
  // Only applicable for member forms
  let result: any = true;
  disableUpdate.value = false;

  if (isMemberForm.value) {

    let requiredTeams = [];

    for (const teamId in linkedFormsTeams.value) {
      let hasTeam = currentTeams.some((team: any) => {
        return team.teamId === teamId;
      });

      if (!hasTeam) {
        requiredTeams.push(linkedFormsTeams.value[teamId]);
      }
    }

    if (requiredTeams.length > 0) {
      result = "連携された帳票からチームも選択する必要があります。 不足しているチームは「" + requiredTeams.join(', ') + "」。";
      disableUpdate.value = true;
    }
  }

  return result;
};

const updateFilter = (val: string, update: any) => {
  update(() => {
    filterTerm.value = val;
  }); 
};

// computed
const updateDeliveryMessage = computed(() => formEditorStore.updateFormSchemaDeliveryMessage);
const formSchema = computed(() => formEditorStore.formSchema);
const teamListData = computed(() => usersStore.teamList);
const isMemberForm = computed((state) => {
  return formEditorStore.formType === "member" ? true : false;
});
const isEditMode = computed(() => formEditorStore.isEditMode);
const surveyConfigs = computed(() => formsStore.formConfigs);

const show = computed({
  get() {
    return props.visible;
  },
  set(value: any) {
    if (!value) {
      emits("close");
    }
  },
});
const selectedTeams = computed(() => {
    let result = [];
    if (currentTeam.value && Array.isArray(currentTeam.value) && currentTeam.value.length > 0) {
      currentTeam.value.forEach((team) => {
        teamList.value.forEach((fullTeamItem: any) => {
          if (team.teamId === fullTeamItem.teamId) {
            result.push(team);
            return;
          }
        });
      });
    }

    if (result.length === 0) {
      result = teamList.value;
    }

    return result;
});



const teamList = computed((): any => {
  const tList = teamListData.value.map((x: any) => {
    let teamName = x.teamName;
    if (teamName !== undefined && getBytesLength(teamName) > 50) {
      teamName = stringTruncate(x.teamName, 50).concat("...");
    }
    return { teamId: x.teamId, teamName: teamName };
  });

  const finalT = filterTerm.value.length > 0 ? tList.filter((team: any) => {
    return team.teamName.toLowerCase().indexOf(filterTerm.value.toLowerCase()) > -1;
  }) : tList;

  return finalT;
});
const hasBunruiItem = computed(() => {
  let result = false;
  if (
    formSchema.value &&
    formSchema.value[0] &&
    formSchema.value[0].surveySchema &&
    Array.isArray(formSchema.value[0].surveySchema)
  ) {
    formSchema.value[0].surveySchema.forEach((schema: any) => {
      if (schema.type === "reservation") {
        result = true;
        return;
      }
    });
  }

  return result;
});
const linkedForms = computed((): Array<any> => {
  let linked_forms = [];

  // Get linked IDs from state then filter list of formConfigs
  // Only applicble for member forms
  if (isMemberForm.value) {
    //let linkedFormIDs = linkedFormIDs.value;

    linked_forms = surveyConfigs.value.filter((config: any) => {
      return linkedFormIDs.value.has(config.surveyId);
    });
  }

  return linked_forms;
});
const linkedFormIDs = computed((): Set<any> => {
  return formEditorStore.step1SurveyFormat().linkedForms.value ? new Set(formEditorStore.step1SurveyFormat().linkedForms.value) : new Set();
});
const linkedFormsTeams = computed((): TeamDictionary => {
  // Returns a dictionary of all teams set for linked survey configs
  // Only applicable for member type forms
  // Dictionary key is team ID, mapped value is team name
  let linkedTeams = {} as TeamDictionary;

  if (isMemberForm.value) {
    for (const form of linkedForms.value) {
      if ("surveyTeams" in form) {
        for (const team of form.surveyTeams) {
          linkedTeams[team.teamId] = team.teamName;
        }
      }
    }
  }
  return linkedTeams;
});
const validateCurrentTeam = computed((): any => {
  let returnValue = ruleLinkedTeams(
    currentTeam.value
  );
  if (returnValue === true) {
    returnValue = '';
  }
  return returnValue;
});
const previousPageTab = computed((): string => {
  return isMemberForm.value ? "member-form-list" : "form-list";
});
const previousPageLocation = computed((): any => {
  return {
    name: "FormCreatePage",
    params: { tab: previousPageTab.value },
  };
});

watch(() => formSchema.value, async (newVal) => {
  categoriesPermissions.value = !newVal[0] ? [] : newVal[0].categoriesPermissions;
  // console.log("Update categoriesPermissions", categoriesPermissions.value);
}, { deep: true });

// hooks

onMounted(async () => {
  currentTeam.value = formSchema.value && formSchema.value.length > 0 && "surveyTeams" in formSchema.value[0] ? formSchema.value[0].surveyTeams : [];
  currentTeam.value = currentTeam.value.filter((team: any) => team.teamId !== "Administrator"); // Admin team gets auto added, so no need to show it
  if (!teamListData.value.length) {
    await fetchTeamListData();
  }
  categoriesPermissions.value = cloneDeep(formSchema.value[0].categoriesPermissions);
});

</script>

<style>
.referSettingFixedMsg {
  font-size: 14px;
}
</style>