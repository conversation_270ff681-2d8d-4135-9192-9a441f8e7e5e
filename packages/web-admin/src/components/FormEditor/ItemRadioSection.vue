<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-row class="row q-mb-sm q-mt-md">
      <q-col class="col-12">
        <ul ref="itemRadioDrag" class="form-editor-drag">
          <li v-for="(option, index) in options" :key="option + index" class="row"
            @mouseenter="hover = index" @mouseleave="hover = false">
            <span style="width: 15px" v-if="isEditMode === false" class="q-mx-none q-px-none q-py-none flex items-center">
              <q-icon v-if="hover === index && isActive" class="radio-drag-handle q-px-none" name="mdi-drag"
                size="sm"></q-icon>
            </span>
            <q-btn @click="setDefaultValue(option)" class="q-py-none q-px-sm" :disable="attributes['isSesshuKaisuu']"
              flat :ripple="false">
              <q-icon :name="`${isDefaultValue(option) ? 'mdi-radiobox-marked' : 'mdi-radiobox-blank'}`" size="sm"
                class="self-center"></q-icon>
              <q-tooltip anchor="center right" self="center left" :offset="[2, 2]">
                <span>新規入力時の初期値</span>
              </q-tooltip>
            </q-btn>

            <q-input :key="index + '/' + option.option.value" class="col-4 deactive q-py-none" hide-details
              :model-value="option.option.value" :readonly="isReadOnly(index)" :disable="attributes['isSesshuKaisuu']"
              @change="(value: any) => updateRadioSectionOption(index, 'option', value)">
            </q-input>

            <q-btn @click="removeRadioSectionOption(index)" v-if="isShowRemoveButton(index)" flat>
              <q-icon name="mdi-close" size="sm" class="self-end" color="grey"></q-icon>
            </q-btn>


            <q-select 
              class="col-4 q-py-none q-px-sm" 
              use-input 
              v-model="option.groupheader.value"
              :options="optionize(radioGroupHeaderList)" 
              label="グループヘッダ"
              @update:model-value="(value: any) => updateRadioSectionOption(index, 'groupheader', value)"
            ></q-select>

          </li>
        </ul>
        <q-row v-if="isShowAddOption" :class="{ 'q-mt-sm': !isMultipleOptions }">
          <q-col>
            <div class="q-pt-sm q-pl-sm">
              <q-btn @click="addRadioSectionOption" flat :ripple="false" class="q-pl-none">
                <q-icon name="mdi-radiobox-blank" size="sm" left></q-icon>
                <span :style="`color: var(--q-primary)`">選択肢を追加</span></q-btn>
            </div>
          </q-col>
        </q-row>
      </q-col>
    </q-row>
  </div>

</template>

<script setup lang="ts">
import { PropType, ref, computed, onMounted } from 'vue';



// old imports
// 旧インポート
import Sortable from "sortablejs";
import { cloneDeep } from "lodash";

// emits 
const emits = defineEmits<{
  (event: 'addRadioSectionOption'): void;
  (event: 'updateRadioSectionOption', ...payload: any[]): void;
  (event: 'removeRadioSectionOption', payload: any): void;
  (event: 'moveRadioSectionOption', ...payload: any[]): void;
}>();

const hover = ref<any>(false);

// props
const props = defineProps({
  item: { type: Object as PropType<any>, required: true },
  isActive: { type: Boolean as PropType<boolean>, required: true },
  isEditMode: { type: Boolean as PropType<boolean>, required: true },
  originalSectionOptionsIndex: { type: Number as PropType<number>, required: true },
  editIndex: { type: Number as PropType<number>, required: false },
  radioGroupHeaderList: { type: Object as PropType<any>, required: false },
  attributes: { type: Object as PropType<any>, required: false }
});

// data
const itemRadioDrag = ref<any>(null);
const itemKey = ref<string>("");
const options = ref<any>([]);

// methods

const noData = () => {
  return [{ label: 'データはありません', value: '', disabled: true }];
}

const setupDrag = () => {
  let list = itemRadioDrag.value; 
  const _self = this;
  if (list) {
    Sortable.create(list, {
      handle: ".radio-drag-handle", // Use handle so user can select text
      animation: 150,
      chosenClass: "white",
      onEnd({ newIndex, oldIndex }) {
        emits("moveRadioSectionOption", newIndex, oldIndex);
      },
    });
  }
};
const addRadioSectionOption = () => {
  emits("addRadioSectionOption");
  options.value = cloneDeep(props.item.sectionOptions);
};
const updateRadioSectionOption = (index: any, key: any, value: any) => {
  emits("updateRadioSectionOption", index, key, value);
  options.value = cloneDeep(props.item.sectionOptions);
};
const removeRadioSectionOption = (index: any) => {
  emits("removeRadioSectionOption", index);
  options.value = cloneDeep(props.item.sectionOptions);
};
const setDefaultValue = (value: any) => {
  if (props.item.default === value) {
    props.item.default = null;
    props.item.input = null;
  } else {
    props.item.default = value;
    props.item.input = value;
  }
};
const isDefaultValue = (value: any) => {
  return props.item.default === value;
};
const isReadOnly = (index: number) => {
  const isNotEditableIndex = props.editIndex !== index && index <= props.originalSectionOptionsIndex && props.editIndex !== -1;
  return props.isEditMode && !props.item.isNewItem && isNotEditableIndex;
};
const isShowRemoveButton = (index: number): boolean => {
  if (props.attributes['isSesshuKaisuu']) {
    return false;
  }

  if (props.isEditMode) {
    return props.item.isNewItem ? isMultipleOptions.value : index > props.originalSectionOptionsIndex;
  } else {
    return isMultipleOptions.value;
  }
};

const optionize = (options: any) => {
  if(!options) {
    return noData();
  }
  options = Object.values(options);
  return options.map((option: any) => {
    return { label: option, value: option };
  });
};

// computed
const isMultipleOptions = computed((): boolean => {
  return props.item.sectionOptions.length > 1;
});
const isShowAddOption = computed((): boolean => {
  if (!props.isActive || props.attributes['isSesshuKaisuu']) {
    return false;
  }

  const originalSectionOptionsLength = props.originalSectionOptionsIndex + 1;
  if (props.isEditMode) {
    return props.item.isNewItem ? true : props.item.sectionOptions.length === originalSectionOptionsLength;
  }

  return true;
});

// hooks

onMounted(() => {
  itemKey.value = cloneDeep(itemKey.value);
  options.value = cloneDeep(props.item.sectionOptions);
  setupDrag();
});

</script>

<style lang="less" scoped>
.radio-drag-handle {
  cursor: move;
}
</style>
