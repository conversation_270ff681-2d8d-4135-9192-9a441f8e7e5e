<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-card flat :color="'primary'" dense>
      <div class="row q-pa-md q-ma-md">
        <div style="position:relative;" class="col-12">
          <div class="subtitle d-flex align-center">帳票への連携</div>
          <div class="row my-5 q-col-gutter-md justify-space-between">
            <div class="col-10">
              <q-select use-input v-model="selectedSurvey" 
                :options="surveyOptions" outline
                option-label="text" option-value="value"
                dense hide-details 
                class="bg-white heightCtrl" placeholder="連携する帳票を選択">
              </q-select>
            </div>
            <div class="col-2">
              <q-btn color="primary" class="full-width" :disable="surveyAlreadyLinked" @click="onLinkSurvey">
                <q-icon left name="mdi-link-variant"></q-icon>連携
              </q-btn>
            </div>
          </div>
          <q-table 
            :columns="linkedFormsHeaders" :rows="linkedForms" selection="multiple"
            v-model:selected="selectedLinkedForms" 
            row-key="value"
            @update:selected="console.log($event)"
            color="primary"
            class="footerCtrl q-my-md" flat :items-per-page="5" :footer-props="{
              'items-per-page-options': [5, 10, 20],
            }"></q-table>
          <q-btn color="red-7" class="px-6 mr-2" :disable="selectedLinkedForms.length === 0" @click="onUnlinkFormClick">
            <q-icon name="mdi-link-variant-off" left></q-icon>
            連携削除
          </q-btn>
        </div>
      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { PropType, ref, computed, onMounted, toRaw, } from 'vue';
import { useFormEditorStore } from '@/stores/modules/formEditor';
import { useFormsStore } from '@/stores/modules/forms';
import { cloneDeep } from 'lodash';

// old imports
// 旧インポート
/*import { UPDATE_MEMBER_SCHEMA_LINKED_FORMS } from "@/store/mutation-types";*/

const formEditorStore = useFormEditorStore();
const formsStore = useFormsStore();


// props
const props = defineProps({
  sectionKey: String as PropType<string>
});

// data
const selectedSurvey: any = ref(null);
const selectedLinkedForms = ref([]);
const linkedFormsHeaders = ref<any>([
  {
    label: "帳票名",
    field: "text",
    name: "text",
    width: "55%",
    align: "left",
    headerClasses: "text-weight-bold"
  },
  {
    label: "ID",
    field: "value",
    name: "value",
    width: "40%",
    align: "left",
    headerClasses: "text-weight-bold"
  },
]);

const testData = ref([
  { text: "帳票1", value: "1" },
  { text: "帳票2", value: "2" },
  { text: "帳票3", value: "3" },
  { text: "帳票4", value: "4" },
  { text: "帳票5", value: "5" },
])

// methods
const updateLinkedForms = formEditorStore.updateMemberSchemaLinkedForms;
const onLinkSurvey = (): void => {
  // Add selected survey id to list of linked forms
  let currentFormIDs = toRaw(linkedFormIDs.value);
  currentFormIDs.add(toRaw(selectedSurvey.value.value));
  selectedSurvey.value = [];

  updateLinkedForms({
    key: props.sectionKey,
    linkedForms: Array.from(currentFormIDs),
  });
};
const onUnlinkFormClick = (): void => {
  // Remove selected form IDs from list of linked surveys
  let currentFormIDs = toRaw(linkedFormIDs.value);
  for (let linkedForm of selectedLinkedForms.value) {
    currentFormIDs.delete(linkedForm.value);
  }

  selectedLinkedForms.value = [];

  updateLinkedForms({
    key: props.sectionKey,
    linkedForms: Array.from(currentFormIDs),
  });
};

// computed
const surveyConfigs = computed(() => formsStore.surveyConfigsList);
const linkedForms = computed((): any => {
  // Get linked IDs from state then filter list of formConfigs
  const copyFormIDs = cloneDeep(linkedFormIDs.value);
  const surveyConfigsCopy = cloneDeep(surveyConfigs.value);
  let linkFormIDs = Array.from(copyFormIDs);
  
  const rows = surveyConfigsCopy.filter((config: any) => {
    return linkFormIDs.find((id: any) => id == config.surveyId);
  }).map((config: any) => {
    return {
      text: config.surveyTitle,
      value: config.surveyId,
    };
  });

  return rows;
});

const linkedFormIDs = computed((): Set<any> => {
  return formEditorStore.step1SurveyFormat.linkedForms ? new Set(formEditorStore.step1SurveyFormat.linkedForms) : new Set();
});
const surveyOptions = computed((): any => {
  // Filter survey configs that are already linked to another member form
  // Valid survey configs have no linked forms or the linked form is the current one being edited.
  const validOptions = surveyConfigs.value
    .filter((obj: any) => {
      return (
        !linkedFormIDs.value.has(obj.surveyId) &&
        (!obj.memberFormId ||
          (formEditorStore.step1SurveyFormat.surveyId && formEditorStore.step1SurveyFormat.surveyId == obj.memberFormId)) &&
        obj.usePayment !== 1
      );
    })
    .map((obj: any) => {
      return {
        value: obj.surveyId,
        text:
          obj.surveyTitle !== undefined && obj.surveyTitle.length <= 50
            ? obj.surveyTitle
            : obj.surveyTitle.substring(0, 50).concat("..."),
      };
    });
  return validOptions;
});

const surveyAlreadyLinked = computed((): any => {
  // Check if selected survey is already added to list of linked forms
  return linkedFormIDs.value.has(selectedSurvey.value) || !selectedSurvey.value;
});

onMounted(async () => {
  await formsStore.fetchFormConfigs();
});
</script>

<style scoped>
.subtitle {
  font-size: 17px;
  font-weight: 700;
}
</style>
<style>
.v-autocomplete.heightCtrl fieldset {
  height: 41px;
}

.v-autocomplete.heightCtrl {
  height: 36px;
}

.footerCtrl .v-data-table-header th {
  font-size: 14px !important;
  color: #607D8B !important;
}

.footerCtrl .v-data-footer {
  padding-top: 24px;
  font-size: 14px;
}
</style>