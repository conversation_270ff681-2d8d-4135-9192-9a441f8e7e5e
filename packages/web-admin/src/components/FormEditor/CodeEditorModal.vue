<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-dialog v-model="show" maximized>
    <q-bar color="primary" dark height="5"> </q-bar>
    <q-card>
      <q-toolbar flat>
        <q-toolbar-title>フォームのコード</q-toolbar-title>
        <q-space></q-space>
        <q-btn round dense flat icon="close" @click="show = false"></q-btn>
      </q-toolbar>
      <q-separator></q-separator>
      <q-card-section>
        <codemirror ref="cmFormEditor" v-model="yamlFormatCode" :options="cmOptions" />
      </q-card-section>
      <q-separator></q-separator>
      <q-card-actions class="pa-4">
        <q-space></q-space>
        <q-btn color="primary" class="px-8" flat @click="test">
          <q-icon name="save"></q-icon>
          保存
        </q-btn>
        <q-btn color="grey-2" class="ml-3" flat @click="show = false">
          <q-icon name="cancel"></q-icon>
          キャンセル
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { defineComponent, ref, watch, onMounted, computed } from 'vue';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import { codemirror } from 'vue-codemirror';
import YAML from 'yamljs';
import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/darcula.css';
import 'codemirror/mode/yaml/yaml.js';
import 'codemirror/mode/javascript/javascript';

const TAB_SIZE = 4;

interface LocalState {
  localSections: any;
  cmOptions: any;
}

const props = defineProps({
  visible: Boolean,
  close: Function,
  sections: Array,
});

const show = ref(props.visible);
const localSections = ref(cloneDeep(props.sections));
const cmFormEditor = ref(null);
const cmOptions = {
  tabSize: TAB_SIZE,
  mode: {
  name: 'text/x-yaml',
  },
  theme: 'darcula',
  lineNumbers: true,
  line: true,
  fixedGutter: false,
  autoRefresh: true,
  // more CodeMirror options...
};

const jsonFormatCode = computed(() => {
  return JSON.stringify(localSections.value, null, TAB_SIZE);
});

const yamlFormatCode = computed(() => {
  const safeSections = JSON.parse(JSON.stringify(localSections.value));
  return YAML.stringify(safeSections, 4);
});

const updateFormSchema = () => {
  // Implement your logic here
};

const test = () => {
  // Implement your logic here
};

watch(
  () => props.sections,
  (val) => {
  localSections.value = cloneDeep(val);
  },
  { deep: true, immediate: true }
);

watch(show, (val) => {
  if(val && cmFormEditor) {
    (cmFormEditor as codemirror).refresh();
  }
});

onMounted(() => {
  localSections.value = cloneDeep(props.sections);
});
</script>
