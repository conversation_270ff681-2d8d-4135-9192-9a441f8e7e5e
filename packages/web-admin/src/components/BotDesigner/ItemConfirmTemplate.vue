<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <div class="row tw-py-4">
      <label class="col-auto">テキスト</label>
      <q-input
        v-model="params.text"
        class="col-12"
        auto-grow
        outlined
        dense
        rows="5"
        counter
        :rules="[rules.validTextLength, rules.validTextChar]"
        hide-details="auto"
        type="textarea"
        @update:model-value="onChangeValue($event, 'text')"
        @keydown.meta.a.prevent.stop="selectAll"
      >
        <template v-slot:counter>
          <div>{{`文字数：${params.text.length} / 240`}}</div>
        </template>
      </q-input>
      <span style="font-size: 12px; color: #646464"
        >確認型テンプレートメッセージには高さに制限があり、240文字以内であっても完全に表示されない可能性があります。実環境で表示を確認してください。</span
      >
    </div>

    <div class="row">
      <div class="col">
        <div v-for="(action, index) in 2" :key="action">
          <ActionProperty
            of="template"
            :number="action"
            :firstAction="action == 1"
            :lastAction="action == 2"
            :premadeMessage="isSpecialPremadeTalk"
            :action="getActionByNumber(index)"
            :isValidAction="isValidActionList[index]"
            @validateAction="validateAction($event, index)"
            @resetAction="resetAction($event, index)"
            @moveAction="moveAction($event, index)"
            v-bind:ref="actionRef"
            :botMessages="botMessages"
            :specialTalk="specialTalk"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from "lodash";
import ActionProperty from "@/components/BotDesigner/CommonProperties/ActionProperty.vue";
import { computed, nextTick, onBeforeUnmount, onMounted, ref, watch } from "vue";

// interface LocalState {
//   rules: any;
//   textSections: Array<string>;
//   isValidText: any;
//   isValidActionList: Array<boolean>;
// }

const props = defineProps<{
  params: any,
  canSave?: boolean,
  branchIndex?: number,
  botMessages?: any[],
  specialTalk?: boolean,
}>();

// emit
const emit = defineEmits(['updateSaveStatus', 'updateParams']);

// watch
watch(
  () => props.canSave,
  (val) => {
    if (val) {
      for (const section of textSections.value) {
        isValidText.value[section] = true;
      }
      for (const actionIndex in 2 as any) {
        isValidActionList.value[actionIndex] = true;
      }
    }
  }
);

watch(
  () => props.branchIndex,
  (val) => {
    // this.branchIndex = val;
    scrollToAction(val);
  }
);

// data
const rules = ref({
  validTextLength: (value) => {
    if (value.length <= 0) {
      return "必須";
    }

    if (value.length > 240) {
      return "テキストの最大長は240文字です";
    }

    return true;
  },
  validTextChar: (value) => {
    for (var ch of value) {
      if (ch !== "\n" && ch !== " " && ch !== "　") {
        return true;
      }
    }
    return "空白または改行のみは保存出来ません";
  },
});
const textSections = ref(["text"]);
const isValidText = ref({
  text: true,
});
const isValidActionList = ref([true, true]);
const actionRef = ref();

onMounted(() => {
  if (!Number.isInteger(props.branchIndex)) {
    return;
  }
  scrollToAction(props.branchIndex);
  reportValidation();
});

onBeforeUnmount(() => {
  emit("updateSaveStatus", { key: `ItemConfirmTemplate`, value: true });
});

// computed
const isSpecialPremadeTalk = computed((): any => {
  return "specialScenarioTalk" in props.params ? props.params["specialScenarioTalk"] : null;
});

// methods
const scrollToAction = (branchIndex: any): void => {
  if (!Number.isInteger(branchIndex)) {
    return;
  }

  // const targetRef = this.$refs[`action.${branchIndex}`];
  if (actionRef.value && actionRef.value.length > 0) {
    nextTick(() =>
    actionRef.value[0].$el.scrollIntoView({
        behavior: "smooth",
        block: "start",
        inline: "nearest",
      })
    );
  }
};

const onChangeValue = (event: any, keyValue: any): void => {
  if (textSections.value.includes(keyValue)) {
    validateText(event, keyValue);
    reportValidation();
  }
  emit("updateParams", { key: keyValue, value: event });
};

const getActionByNumber = (number: any): any => {
  let situation = "Right";
  if (number == 0) {
    situation = "Left";
  }
  let actionObj = props.params[`action${situation}`];
  return actionObj;
};

const validateText = (event: any, keyValue: any): void => {
  if (typeof event !== "undefined" && event.length > 0 && event.length <= 240) {
    for (var ch of event) {
      if (ch !== "\n" && ch !== " " && ch !== "　") {
        isValidText.value[keyValue] = true;
        return;
      }
    }
  }
  isValidText.value[keyValue] = false;
};

const validateAction = (value: any, index: any): void => {
  isValidActionList.value[index] = value;
  reportValidation();
};

const reportValidation = (): void => {
  for (const section of textSections.value) {
    if (!isValidText.value[section]) {
      emit("updateSaveStatus", { key: `ItemConfirmTemplate`, value: false });
      return;
    }
  }
  for (var i = 0; i < 2; i++) {
    if (!isValidActionList.value[i]) {
      emit("updateSaveStatus", { key: `ItemConfirmTemplate`, value: false });
      return;
    }
  }
  emit("updateSaveStatus", { key: `ItemConfirmTemplate`, value: true });
};

const resetAction = (oldAction: any, actionToResetIndex: number): void => {
  if (actionToResetIndex === 0) {
    props.params.actionLeft = cloneDeep(oldAction);
  } else {
    props.params.actionRight = cloneDeep(oldAction);
  }
};

const moveAction = (movePositionUp: boolean, indexToMove: number): void => {
  //In the case of confirm, there are only two actions. 
  //Literally just need to switch them
  const tempPosition = cloneDeep(props.params.actionRight);
  props.params.actionRight = cloneDeep(props.params.actionLeft);
  props.params.actionLeft = cloneDeep(tempPosition);
};

// select all text hotkey support
const selectAll = (event: any): void => {
  if ((event.metaKey || event.ctrlKey) && event.key === "a") {
    event.target.select();
  }
};
</script>