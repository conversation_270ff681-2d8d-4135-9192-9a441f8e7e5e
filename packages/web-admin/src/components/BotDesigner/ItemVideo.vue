<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.edit-image-file-row {
  display: inline;
}
</style>

<template>
  <div class="tw-py-4">
    <div class="row">
      <div class="col">
        <div class="row">
          <div class="col">
            <q-toggle v-model="editOriginalFiles" label="動画を変更する"> </q-toggle>
          </div>
        </div>
        <div v-if="editOriginalFiles">
          <label>動画</label>
          <div class="select-image-type-row row">
            <q-select
              v-model="params.originalContentLocal"
              :items="contentOptions"
              single-line
              outlined
              dense
              hide-details
            >
            </q-select>
          </div>
          <div class="edit-image-file-row row">
            <q-input
              v-if="!params.originalContentLocal"
              outlined
              single-line
              hide-details
              dense
              label="URL"
              v-model="tempOriginalUrl"
              @change="onChangeValue($event, 'originalContentUrl')"
            >
            </q-input>
            <div v-else>
              <q-file
                v-model="fileModels[0]"
                @change="fileDataChanged($event, 1)"
                label="新しい動画を選択"
                accept=".mp4"
                :rules="[rules.fileTypeOriginal, rules.fileSizeOriginal]"
              >
              </q-file>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col">
        <label>現在プレビュー画像</label>
        <div class="row">
          <div class="col tw-flex tw-items-center tw-justify-center">
            <img
              v-bind:src="params.previewImageUrl + '?x-request=html'"
              height="150px"
              onclick="window.open(this.src)"
            />
          </div>
        </div>
        <div class="row">
          <div class="col">
            <q-toggle v-model="editPreviewFiles" label="プレビュー画像を変更する"> </q-toggle>
          </div>
        </div>
        <div v-if="editPreviewFiles">
          <label>動画</label>
          <div class="select-image-type-row row">
            <q-select
              v-model="params.previewImageLocal"
              :items="contentOptions"
              single-line
              outlined
              dense
              hide-details
            >
            </q-select>
          </div>
          <div class="edit-image-file-row row">
            <q-input
              v-if="!params.previewImageLocal"
              outlined
              single-line
              hide-details
              dense
              label="URL"
              v-model="tempPreviewUrl"
              @change="onChangeValue($event, 'previewImageUrl')"
            >
            </q-input>
            <div v-else>
              <q-file
                v-model="fileModels[1]"
                @change="fileDataChanged($event, 2)"
                label="新しい画像を選択"
                accept=".jpg,.jpeg,.png"
                :rules="[rules.fileTypePreview, rules.fileSizePreview]"
              >
              </q-file>
            </div>
          </div>
          <div class="row">
            <div class="col tw-flex tw-items-center tw-justify-center">
              <img
                v-if="!params.previewImageLocal && tempPreviewUrl.value"
                v-bind:src="tempPreviewUrl.value + '?x-request=html'"
                height="150px"
                onclick="window.open(this.src)"
              />
              <img
                v-if="params.previewImageLocal && tempPreviewFile.value"
                v-bind:src="tempPreviewFile.value"
                height="150px"
                onclick="window.open(this.src)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from "lodash";
import { useQuasar } from "quasar";
import { ref, watch } from "vue";

// interface LocalState {
//   fileModels: Array<any>;
//   allowedContentType: Array<any>;
//   contentOptions: Array<any>;
//   sizeLimitOriginal: number;
//   allowedContentTypePreview: Array<string>;
//   sizeLimitPreview: number;
//   editOriginalFiles: boolean;
//   editPreviewFiles: boolean;
//   tempOriginalUrl: any;
//   tempOriginalFile: any;
//   validTempOriginalUrl: boolean;
//   validTempOriginalFile: boolean;
//   tempPreviewUrl: any;
//   tempPreviewFile: any;
//   validTempPreviewUrl: boolean;
//   validTempPreviewFile: boolean;
//   rules: any;
//   isValidSize: Array<boolean>;
//   isValidType: Array<boolean>;
//   originalContentUrlBackUp: any;
// }

// quasar
const $q = useQuasar();

// props
const props = defineProps<{
  params: any,
  canSave?: boolean,
}>();

// emit
const emit = defineEmits(['updateParams', 'fileVideoDataUpdate', 'updateSaveStatus']);

// watch
watch(
  () => props.params,
  (value) => {
    fileModels.value = [undefined, undefined];
    editOriginalFiles.value = false;
    tempOriginalUrl.value = null;
    tempOriginalFile.value = null;
    validTempOriginalUrl.value = false;
    validTempOriginalFile.value = false;
    editPreviewFiles.value = false;
    tempPreviewUrl.value = null;
    tempPreviewFile.value = null;
    validTempPreviewUrl.value = false;
    validTempPreviewFile.value = false;
    originalContentUrlBackUp.value = cloneDeep(value.originalContentUrl);
  }
);

watch(
  () => props.params.originalContentLocal,
  (value) => {
    if (tempOriginalUrl.value == null) {
      validTempOriginalUrl.value = false;
    }
    if (tempOriginalFile.value == null) {
      validTempOriginalFile.value = false;
    }
    reportValidation();
  }
);

watch(
  () => props.params.previewImageLocal,
  (value) => {
    if (tempPreviewUrl.value == null) {
      validTempPreviewUrl.value = false;
    }
    if (tempPreviewFile.value == null) {
      validTempPreviewFile.value = false;
    }
    reportValidation();
  }
);

watch(
  () => editOriginalFiles.value,
  (value) => {
    if (value === false) {
      props.params.originalContentUrl = originalContentUrlBackUp.value;
      tempOriginalUrl.value = null;
      fileModels.value[0] = undefined;
    }
    emit("updateParams", { key: "editingOriginalVideo", value: value });
    reportValidation();
  }
);

watch(
  () => editPreviewFiles.value,
  (value) => {
    emit("updateParams", { key: "editingPreviewImage", value: value });
    reportValidation();
  }
);

watch(
  () => props.canSave,
  (value) => {
    if (value) {
      for (var i = 0; i < 2; i++) {
        isValidSize.value[i] = true;
        isValidType.value[i] = true;
      }
    }
  }
);

// data
const fileModels = ref([undefined, undefined]);
const allowedContentType = ref(["video/mp4"]);
const contentOptions = ref([
  { text: "URL", value: false },
  { text: "ローカルファイル", value: true },
]);
const sizeLimitOriginal = ref(200000000);
const allowedContentTypePreview = ref(["image/jpg", "image/jpeg", "image/png"]);
const  sizeLimitPreview = ref(1000000);
const editOriginalFiles = ref(false);
const editPreviewFiles = ref(false);
const tempOriginalUrl = ref(null);
const tempOriginalFile = ref(null);
const validTempOriginalUrl = ref(false);
const validTempOriginalFile = ref(false);
const tempPreviewUrl = ref(null);
const tempPreviewFile = ref(null);
const validTempPreviewUrl = ref(false);
const validTempPreviewFile = ref(false);
const rules = ref({
  fileSizeOriginal: (file) =>
    file === undefined || !(file.size > 200000000) || "200MB以下の動画ファイルをアップロードして下さい",
  fileSizePreview: (file) =>
    file === undefined || !(file.size > 1000000) || "1MB以下の画像ファイルをアップロードして下さい",
  fileTypeOriginal: (file) => file === undefined || file.type === "video/mp4" || "動画は .mp4 しか出来ません",
  fileTypePreview: (file) =>
    file === undefined ||
    file.type === "image/jpeg" ||
    file.type === "image/png" ||
    "画像は .jpg .jpeg .png しか出来ません",
});
const isValidSize = ref([true, true]);
const isValidType = ref([true, true]);
const originalContentUrlBackUp = ref(cloneDeep((this as any).params.originalContentUrl) || {});

// methods
const onChangeValue = (event: any, keyValue: any): void => {
  if (keyValue == "originalContentUrl") {
    if (event !== "") {
      const response = fetch(event, {
        method: "HEAD",
      })
        .then((response) => {
          if (response.status == 200 && allowedContentType.value.includes(response.headers.get("Content-Type"))) {
            if (Number(response.headers.get("Content-Length")) > sizeLimitOriginal.value) {
              props.params.originalContentUrl = originalContentUrlBackUp.value;
              tempOriginalUrl.value = null;
              validTempOriginalUrl.value = false;
              reportValidation();
              $q.notify({ message: "ファイルサイズは200MB以下にしてください。", type: "error" });
            } else {
              props.params.originalContentUrl = tempOriginalUrl.value;
              validTempOriginalUrl.value = true;
              reportValidation();
              emit("updateParams", { key: "tempOriginalVideo", value: event });
            }
          } else {
            props.params.originalContentUrl = originalContentUrlBackUp.value;
            tempOriginalUrl.value = null;
            validTempOriginalUrl.value = false;
            reportValidation();
            $q.notify({ message: "URL形式が正しくありません。", type: "error" });
          }
          return response;
        })
        .catch((error) => {
          props.params.originalContentUrl = originalContentUrlBackUp.value;
          tempOriginalUrl.value = null;
          validTempOriginalUrl.value = false;
          reportValidation();
          $q.notify({
            message: "このURLの内容を読み込めない。URLの内容をダウンロードしてLBDでローカルファイル使ってください。",
            type: "error",
          });
          return error;
        });
    } else {
      props.params.originalContentUrl = originalContentUrlBackUp.value;
      validTempOriginalUrl.value = false;
      reportValidation();
    }
  } else if (keyValue == "previewImageUrl") {
    if (event !== "") {
      const response = fetch(event, {
        method: "HEAD",
      })
        .then((response) => {
          if (
            response.status == 200 &&
            allowedContentTypePreview.value.includes(response.headers.get("Content-Type"))
          ) {
            if (Number(response.headers.get("Content-Length")) > sizeLimitPreview.value) {
              tempPreviewUrl.value = null;
              validTempPreviewUrl.value = false;
              reportValidation();
              $q.notify({ message: "ファイルサイズは1MB以下にしてください。", type: "error" });
            } else {
              validTempPreviewUrl.value = true;
              reportValidation();
              emit("updateParams", { key: "tempPreviewImage", value: event });
            }
          } else {
            tempPreviewUrl.value = null;
            validTempPreviewUrl.value = false;
            reportValidation();
            $q.notify({ message: "URL形式が正しくありません。", type: "error" });
          }
          return response;
        })
        .catch((error) => {
          tempPreviewUrl.value = null;
          validTempPreviewUrl.value = false;
          reportValidation();
          $q.notify({
            message: "このURLの内容を読み込めない。URLの内容をダウンロードしてLBDでローカルファイル使ってください。",
            type: "error",
          });
          return error;
        });
    } else {
      validTempPreviewUrl.value = false;
      reportValidation();
    }
  } else {
    emit("updateParams", { key: keyValue, value: event });
  }
};

const fileDataChanged = (event: any, index: any): void => {
  if (index - 1 === 0) {
    isValidSize.value[index - 1] = !(event !== undefined && event.size > 200000000);
    isValidType.value[index - 1] = !(event !== undefined && event.type !== "video/mp4");
    if (event) {
      let reader = new FileReader();
      reader.onload = (e) => {
        tempOriginalFile.value = e.target.result;
        props.params.originalContentUrl = tempOriginalFile.value;
        validTempOriginalFile.value = true;
        emit("updateParams", { key: "tempOriginalVideo", value: event });
        reportValidation();
      };
      reader.readAsDataURL(event);
    } else {
      props.params.originalContentUrl = originalContentUrlBackUp.value;
      tempOriginalFile.value = null;
      validTempOriginalFile.value = false;
    }
  } else {
    isValidSize.value[index - 1] = !(event !== undefined && event.size > 1000000);
    isValidType.value[index - 1] = !(
      event !== undefined &&
      event.type !== "image/jpeg" &&
      event.type !== "image/png"
    );
    if (event) {
      let reader = new FileReader();
      reader.onload = (e) => {
        tempPreviewFile.value = e.target.result;
        validTempPreviewFile.value = true;
        emit("updateParams", { key: "tempPreviewImage", value: event });
        reportValidation();
      };
      reader.readAsDataURL(event);
    } else {
      tempPreviewFile.value = null;
      validTempPreviewFile.value = false;
    }
  }
  reportValidation();

  fileModels.value[index - 1] = event;
  emit("fileVideoDataUpdate", fileModels.value);
};

const reportValidation = (): void => {
  for (const item of isValidSize.value) {
    if (!item) {
      emit("updateSaveStatus", { key: `ItemVideo`, value: false });
      return;
    }
  }

  for (const item of isValidType.value) {
    if (!item) {
      emit("updateSaveStatus", { key: `ItemVideo`, value: false });
      return;
    }
  }

  if (editOriginalFiles.value && props.params.originalContentLocal) {
    if (!validTempOriginalFile.value) {
      emit("updateSaveStatus", { key: `ItemVideo`, value: false });
      return;
    }
  }
  if (editOriginalFiles.value && !props.params.originalContentLocal) {
    if (!validTempOriginalUrl.value) {
      emit("updateSaveStatus", { key: `ItemVideo`, value: false });
      return;
    }
  }

  if (editPreviewFiles.value && props.params.previewImageLocal) {
    if (!validTempPreviewFile.value) {
      emit("updateSaveStatus", { key: `ItemVideo`, value: false });
      return;
    }
  }
  if (editPreviewFiles.value && !props.params.previewImageLocal) {
    if (!validTempPreviewUrl.value) {
      emit("updateSaveStatus", { key: `ItemVideo`, value: false });
      return;
    }
  }
  emit("updateSaveStatus", { key: `ItemVideo`, value: true });
};
</script>