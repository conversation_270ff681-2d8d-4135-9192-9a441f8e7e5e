<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.composite-message-padding table .text-start {
  padding: 0 6px !important;
}

.v-data-footer {
  border-top: 0px !important;
  justify-content: flex-end !important;
}

.q-table:last-child {
  border: 1px lightgray solid !important;
  border-radius: 5px !important;
}
</style>
<template>
  <div>
    <div class="row tw-py-4">
      <div class="composite-message-padding col">
        <span>編集するメッセージを5つまで選択します。</span>
        <q-table v-model:selected="selectedMessages" :rows-per-page-options="[20]" :rows="filteredMessages"
          :columns="headers" row-key="dataId" flat bordered hide-bottom hide-header selection="multiple"
          class="q-pa-sm" @update:selected="runValidation">
          <template v-slot:top="item">
            <div class="row items-center">
              <div style="font-size: 12px;" class="col-4 flex-center">
                  選択数： {{ selectedMessages.length }} / 5
              </div>
                <div class="col-6 tw-flex q-ml-md">
                  <q-pagination v-model="item.pagination.page" :max="item.pagesNumber" input color="grey"
                    :boundary-links="false" />
                </div>
              </div>
          </template>

          <template v-slot:body="props">
            <q-tr :props="props" @click="props.selected = !props.selected">
              <q-td v-for="col in props.cols" :key="col.name" :props="props" class="row">
                <q-checkbox v-model="props.selected" />
                <div class="flex items-center">
                  <q-icon size="sm" v-if="col.name === 'nameLBD'" :name="getIconFromItemType(props.row.dataType)" />
                  <span class="q-ml-md">{{ col.name === 'nameLBD' && !_isNullOrEmpty(props.row.nameLBD) ?
                    props.row.nameLBD : props.row[col.name] }}</span>
                </div>
              </q-td>
            </q-tr>
          </template>
        </q-table>
      </div>
    </div>
    <div class="row">
      <div class="col">
        <MessagePreview :messages="selectedMessages" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { BOT_ITEM_TYPES } from "@/stores/modules/scenarios/scenarios.constants";
import MessagePreview from "@/pages/admin/ScenarioSettingsDetail/components/MessagePreview.vue";
import { isNullOrEmpty } from "@/utils/stringUtils";
import { computed, onBeforeMount, onBeforeUnmount, onMounted, ref, watch } from "vue";
import { useScenariosStore } from "@/stores/modules/scenarios";

// store
const scenariosStore = useScenariosStore();

// props
const props = defineProps<{
  params: any,
  messagesToDisplay?: any[],
}>();

// emit
const emit = defineEmits(['updateMessages', 'updateSaveStatus']);

// data
const selectedIndex = ref(0);
const selectedMessages = ref<any[]>([]);
const supportedMessages = ref([
  "text",
  "sticker",
  "buttons",
  "imagemap",
  "carousel",
  "bubbleFlex",
  "carouselFlex",
  "image",
  "audio",
  "video",
  "confirm",
  "location",
]);
const headers = ref([
  {
    name: "nameLBD",
    field: "名前",
    label: "名前",
    sortable: true,
    width: "30%",
  },
]);
const rules = ref({
  validTextLength: (value) => value.length > 0 || "必須",
  validTextChar: (value) => {
    for (let ch of value) {
      if (ch !== "\n" && ch !== " " && ch !== "　") {
        return true;
      }
    }
    return "空白または改行のみは保存出来ません";
  },
});

// watch
watch(
  () => selectedMessages.value,
  (newVal) => {
    let listOfMessagePostbackIds = [];
    newVal.forEach((message) => {
      listOfMessagePostbackIds.push(message.dataId);
    });
    emit("updateMessages", listOfMessagePostbackIds);
  }
);

watch(
  () => props.params,
  () => {
    selectedMessages.value = alreadySelectedMessages.value;
  }
);

// computed
const scenarioMessages = computed(() => scenariosStore.scenarioMessages);

const alreadySelectedMessages = computed((): Array<any> => {
  let result = [];
  if (props.params && props.params.messages) {
    props.params.messages.forEach((messageId) => {
      let msg = !isSpecialPremadeTalk()
        ? filteredMessages.value.find((message) => {
          return message.dataId === messageId;
        })
        : scenarioMessages.value.find((message) => {
          return message.dataId === messageId;
        });
      if (msg) {
        result.push(msg);
      }
    });
  }
  return result;
});

const filteredMessages = computed((): any => {
  if (props.messagesToDisplay) {
    return props.messagesToDisplay.filter((message) => {
      return supportedMessages.value.includes(message.dataType);
    });
  }
  if (scenarioMessages.value) {
    return scenarioMessages.value.filter((message) => {
      return supportedMessages.value.includes(message.dataType) && !("specialScenarioTalk" in message.params) && (message.dataId !== props.params.dataId);
    });
  } else {
    return [];
  }
});

// methods
const isSpecialPremadeTalk = (): boolean => {
  return "params" in props.params && "specialScenarioTalk" in props.params.params;
};

const typeTitle = (value: any): any => {
  return BOT_ITEM_TYPES[value] ? BOT_ITEM_TYPES[value].text : "";
};

const runValidation = (): void => {
  setTimeout(() => reportValidation(), 100);
};

const reportValidation = (): void => {
  if (selectedMessages.value.some(message => message.dataId === props.params.dataId)) {
    console.error("Cannot select the same message twice");
    return;
  }

  emit("updateSaveStatus", {
    key: `ItemCompositeMessage`,
    value: selectedMessages.value.length > 0 && selectedMessages.value.length < 6,
  });
};

const getIconFromItemType = (dataType: string): string => {
  return dataType in BOT_ITEM_TYPES && BOT_ITEM_TYPES[dataType].icon ? BOT_ITEM_TYPES[dataType].icon : "";
};

const _isNullOrEmpty = (name: string): boolean => {
  return isNullOrEmpty(name);
};

onBeforeMount(() => {
  selectedMessages.value = alreadySelectedMessages.value;
});

onMounted(() => {
  reportValidation();
  emit("updateSaveStatus", { key: `ItemCompositeMessage`, value: false });
});

onBeforeUnmount(() => {
  emit("updateSaveStatus", { key: `ItemCompositeMessage`, value: true });
});
</script>

<style scoped>
.v-data-footer {
  padding: inherit !important;
  padding-right: 1.5em !important;
  margin-right: 0px !important;
}

th:first-child {
  z-index: 100000001 !important;
}

th>tr {
  z-index: 100000000 !important;
}

.composite-message-edit-display {
  display: contents !important;
}
</style>