<style scoped>
.change-thumbnail-check label {
  margin-bottom: 0px !important;
}
.caption-text {
  font-size: 12px !important;
  color: #646464 !important;
}
</style>

<template>
  <div>
    <div class="row">
      <div class="column full-width">
        <label>画像タイプ</label>
        <q-select 
          v-model="buttonTemplateUrl" 
          :options="buttonTemplateUrlOptions"
          option-value="value"
          option-label="text"
          outlined single-line hide-details 
          dense
          emit-value
          map-options
        >
        </q-select>
      </div>
    </div>

    <div v-if="buttonTemplateUrl">
      <div class="row tw-py-4">
        <div class="column full-width">
          <label>画像の表示形式</label>
            <q-select 
              outlined hide-details 
              :options="templateImageOptions" 
              option-label="text"
              option-value="value"
              v-model="params.imageSize" 
              dense
              emit-value
              map-options
              @update:model-value="onChangeValue($event, 'imageSize')"
            >
            </q-select>
            <span class="caption-text">＊カバー: 画像領域全体に画像を表示します。画像領域に収まらない部分は切り詰められます。
            <br/>＊コンテイン: 画像領域に画像全体を表示します。縦長の画像では左右に、横長の画像では上下に余白が表示されます。</span>
        </div>
      </div>
      <div class="row">
        <div>
          <q-checkbox class="change-thumbnail-check" v-model="editImageFile" hide-details style="margin-top: 0;" label="サムネイル画像を変更する"/>
        </div>
      </div>
      <div v-if="editImageFile">
        <div class="row tw-py-4 items-center">
          <div class="column col-3" style="padding-right: 0">
            <div class="text-grey-7" style="padding: 0" v-text="'タイプ'"></div>
          </div>
          <div class="column col-9 q-my-xs">
            <q-select 
              v-model="newImageLocal" 
              :options="imageOptions" 
              option-label="text"
              option-value="value"
              emit-value
              map-options
              single-line 
              outlined 
              dense 
              hide-details> 
            </q-select>
          </div>
        </div>
        <div class="row tw-py-2 items-center">
          <div class="column col-3" style="padding-right: 0">
            <div class="text-grey-7" style="padding: 0" v-text="'ファイル'"></div>
          </div>
          <div class="items-center col-9">
            <q-input v-if="!newImageLocal"
              v-model="tempThumbnailUrl"
              @blur="onChangeValue(tempThumbnailUrl, 'thumbnailImageUrl')"
              outlined
              dense
              placeholder="URLを入力"
            />
            <q-file v-else
              v-model="tempThumbnailFile"
              @change="fileDataChanged($event)"
              prepend-icon=""
              prepend-inner-icon="mdi-import"
              hide-details="auto"
              outlined
              dense
              placeholder="ファイルを選択"
              class="q-pa-none"
              accept=".jpg,.jpeg,.png"
              @click:clear="emit('updateSaveStatus', { key: `itemButtonTemplate`, value: false })"
              @update:model-value="onChangeThumbnailFile($event)"
              :rules="[isValidFileType, isValidFileSize]"
            >
            </q-file>
          </div>
        </div>
      </div>
    </div>

    <q-form ref="titleTextForm">
      <div class="row tw-py-4">
        <div class="column full-width">
          <label>タイトル</label>
          <q-input
            outlined
            single-line
            dense
            v-model="params.title"
            hide-details="auto"
            :rules="[isValidTitleLength, isValidTextChar]"
            @update:model-value="onChangeValue($event, 'title')"
          >
          </q-input>
        </div>
      </div>
      <div class="row full-width">
        <div class="column">
          <label>テキスト</label>
          <q-input
            auto-grow
            outlined
            single-line
            dense
            rows="5"
            counter
            hide-details="auto"
            :counter-value="(value) => `文字数：${value.length} / ${maxTextLength}`"
            v-model="params.text"
            :rules="[isValidTextLength, isValidTextChar]"
            @update:model-value="onChangeValue($event, 'text')"
            @keydown.meta.a.prevent.stop="selectAll"
            type="textarea"
          >
          </q-input>
          <span class="caption-text"
          >ボタン型テンプレートメッセージには高さに制限があり、{{maxTextLength}}文字以内であっても完全に表示されない可能性があります。実際の環境で表示を確認してください。</span
          >
        </div>
      </div>
    </q-form>

    <div class="row">
      <div class="column full-width">
        <label>アクション数</label>
        <q-select 
          outlined single-line hide-details 
          :options="actions" 
          option-label="text"
          option-value="value"
          v-model="params.actionCount" 
          dense
        ></q-select>

        <div v-for="(action, index) in params.actionCount" :key="`divide_${action}`">
          <q-separator class="tw-my-4"></q-separator>
          <ActionProperty
            of="template"
            :key="action"
            :number="action"
            :firstAction="action == 1"
            :lastAction="action == params.actionCount"
            :premadeMessage="isPremadeMessage"
            :action="getActionByNumber(index)"
            :isValidAction="isValidActionList[index]"
            @validateAction="validateAction($event, index)"
            @resetAction="resetAction($event, index)"
            @moveAction="moveAction($event, index)"
            ref="actionRef"
            v-bind:class="'actions.' + index"
            :botMessages="botMessages"
            :specialTalk="specialTalk"
            :dataId="dataId"
            :isDamageReportCategory="isDamageReportCategory"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ActionProperty from "@/components/BotDesigner/CommonProperties/ActionProperty.vue";
import { cloneDeep } from "lodash";
import { useQuasar } from "quasar";
import { computed, nextTick, onBeforeMount, onMounted, onUnmounted, ref, watch } from "vue";
import defaultLogo from "@/assets/images/logo.png";

// quasar
const $q = useQuasar();

// proos
const props = defineProps<{
  params: any,
  branchIndex?: number,
  canSave?: boolean,
  botMessages?: any[],
  specialTalk?: boolean,
  dataId?: string,
  targetDataField?: string
}>();

const isDamageReportCategory = computed(() => {
  return props.targetDataField === 'category'
});
// emit
const emit = defineEmits(['updateParams', 'updateSaveStatus']);

// data
const actions = ref([1, 2, 3, 4]);
const editImageFile = ref(false);
const newImageLocal = ref(false);
const imageOptions = ref([
  { text: "URL", value: false },
  { text: "ローカルファイル", value: true },
]);
const tempThumbnailFile = ref(null);
const validTempThumbnailFile = ref(false);
const validTempThumbnailUrl = ref(false);
const tempThumbnailFileDisplay = ref(null);
const tempThumbnail = ref(props.params ? cloneDeep(props.params.thumbnailImageUrl) : null);
const tempThumbnailUrl = ref(null);
const isPremadeMessage = ref(null);
const buttonTemplateUrlOptions = ref([
  { text: "画像あり", value: true },
  { text: "画像なし", value: false },
]);
const templateImageOptions = ref([
  { text: "カバー", value: 'cover' },
  { text: "コンテイン", value: 'contain' },
]);
const buttonTemplateUrl = ref(null);
const textSections = ref(["thumbnailImageUrl", "title", "text", "imageSize"]);
const isValidText = ref({
  thumbnailImageUrl: true,
  title: true,
  text: true,
  imageSize: true,
});
const urlSections = ref(["thumbnailImageUrl"]);
const isValidUrl = ref({
  thumbnailImageUrl: true,
});
const isValidActionList = ref([true, true, true, true]);
const allowedContentType = ref(["image/jpg", "image/jpeg", "image/png"]);
const sizeLimitThumbnail = ref(10000000);
const thumbnailImageUrlOriginal = ref(null);
const titleTextForm = ref();
const actionRef = ref();

// watch
watch(
  () => buttonTemplateUrl.value,
  (val) => {
    var updateValue = val ? tempThumbnail.value : "";
    //tempThumbnail.value = val ? props.params.thumbnailImageUrl : "none";
    if (!val) {
      editImageFile.value = false;
      emit("updateParams", { key: "editingThumbnailImage", value: false });
    }

    validateText(updateValue, "thumbnailImageUrl");
    if (val) {
      validateUrl(updateValue, "thumbnailImageUrl");
      reportValidation();
    } else {
      for (const section of urlSections.value) {
        isValidUrl.value[section] = true;
      }
      reportValidation();
    }
    emit("updateParams", { key: "thumbnailImageUrl", value: updateValue });
  }
);

watch(
  () => tempThumbnail.value,
  (val) => {
    validateText(val, "thumbnailImageUrl");
    reportValidation();
  }
);

watch(
  () => editImageFile.value,
  (val) => {
    if (val === false) {
      props.params.thumbnailImageUrl = thumbnailImageUrlOriginal.value;
    }
    emit("updateParams", { key: "editingThumbnailImage", value: val });
    tempThumbnailUrl.value = null;
    validTempThumbnailUrl.value = false;
    tempThumbnailFile.value = null;
    validTempThumbnailFile.value = false;
    reportValidation();
  }
);

watch(
  () => newImageLocal.value,
  (val) => {
    emit("updateParams", { key: "editingThumbnailImageLocal", value: val });
    if (tempThumbnailUrl.value == null) {
      validTempThumbnailUrl.value = false;
    }
    if (tempThumbnailFile.value == null) {
      validTempThumbnailFile.value = false;
    }
    reportValidation();
  }
);

watch(
  () => props.params,
  (val) => {
    tempThumbnail.value = val.thumbnailImageUrl;
    buttonTemplateUrl.value = isUrlSet();
    isPremadeMessage.value = isSpecialPremadeTalk();
    editImageFile.value = false;
    newImageLocal.value = false;
    tempThumbnailFile.value = null;
    tempThumbnailFileDisplay.value = null;
    validTempThumbnailFile.value = false;
    validTempThumbnailUrl.value = false;
    thumbnailImageUrlOriginal.value = cloneDeep(val.thumbnailImageUrl);
    setDefaultValues();
  }
);

watch(
  () => props.canSave,
  (val) => {
    if (val) {
      for (const section of textSections.value) {
        isValidText.value[section] = true;
      }
      for (const section of urlSections.value) {
        isValidUrl.value[section] = true;
      }
      for (const actionIndex in props.params.actionCount) {
        isValidActionList.value[actionIndex] = true;
      }
    }
  }
);

watch(
  () => props.params.actionCount,
  (newVal, oldVal) => {
    if (newVal > oldVal && isPremadeMessage.value) {
      for (var x = oldVal; x < newVal; x++) {
        var tempAction = getActionByNumber(x);
        tempAction["type"] = "postback";
      }
    }
    reportValidation();
  }
);

watch(
  () => props.branchIndex,
  (val) => {
    // props.branchIndex = val;
    scrollToAction(val);
  }
);

// computed
const maxTextLength = computed(() => {
  if (
      !buttonTemplateUrl.value &&
      (!props.params.title || props.params.title === "")
  ) {
    return 160;
  }
  return 60;
});

onBeforeMount(() => {
  setDefaultValues();
  if (!Number.isInteger(props.branchIndex)) {
    return;
  }
  scrollToAction(props.branchIndex);
  reportValidation();
});

// TODO Uncomment if needed
// onMounted(() => {
  // emit("updateSaveStatus", { key: `ItemButtonTemplate`, value: false });
  // reportValidation();
// });

onUnmounted(() => {
  emit("updateSaveStatus", { key: `ItemButtonTemplate`, value: true });
});

// methods
const setDefaultValues = (): void => {
  if (props.params && !props.params.imageSize) {
    props.params.imageSize = 'cover';
  }
  buttonTemplateUrl.value = isUrlSet();
};

const scrollToAction = (branchIndex: any): void => {
  if (!Number.isInteger(branchIndex)) {
    return;
  }

  nextTick(() => {
    // const targetRef = this.$refs[`action.${branchIndex}`];
    if (actionRef.value && actionRef.value.length > 0) {
      actionRef.value[0].$el.scrollIntoView({
        behavior: "smooth",
        block: "start",
        inline: "nearest",
      });
    }
  });
};

const isSpecialPremadeTalk = (): any => {
  return "specialScenarioTalk" in props.params ? props.params["specialScenarioTalk"] : null;
};

const fileDataChanged = (event: any): void => {
  if (event) {
    var reader = new FileReader();
    reader.onload = (e) => {
      tempThumbnailFileDisplay.value = e.target.result;
      validTempThumbnailFile.value = true;
      props.params.thumbnailImageUrl = e.target.result;
      emit("updateParams", { key: "tempThumbnailUrl", value: event });
      if (
        event.size <= 10000000 &&
        (event.type === "image/jpg" || event.type === "image/jpeg" || event.type === "image/png")
      ) {
        reportValidation();
      }
    };
    reader.readAsDataURL(event);
  } else {
    props.params.thumbnailImageUrl = thumbnailImageUrlOriginal.value;

    tempThumbnailFileDisplay.value = null;
    validTempThumbnailFile.value = false;
  }
};

const onChangeThumbnailFile = (file: any): void => {
  if (file) {
    const reader = new FileReader();
    reader.onload = (e) => {
      tempThumbnailFileDisplay.value = e.target.result;
      validTempThumbnailFile.value = true;
      props.params.thumbnailImageUrl = e.target.result;
      emit("updateParams", { key: "tempThumbnailUrl", value: file });
      if (file.size > sizeLimitThumbnail.value) {
        props.params.thumbnailImageUrl = defaultLogo;
        tempThumbnailFileDisplay.value = null;
        validTempThumbnailFile.value = false;
        tempThumbnailFile.value = null;
        reportValidation();
        $q.notify({ message: "ファイルサイズは10MB以下にしてください。", type: "error", color: "negative", icon: "error" });
      } else {
        reportValidation();
      }
    };
    reader.readAsDataURL(file);
  } else {
    props.params.thumbnailImageUrl = defaultLogo;
    tempThumbnailFileDisplay.value = null;
    validTempThumbnailFile.value = false;
    tempThumbnailFile.value = null;
    reportValidation();
  }
};

const onChangeValue = (event: any, keyValue: any): void => {
  nextTick(() => {
    // NOTE: タイトルとテキストで相互にvalidateし合う必要があるので、親formのvalidateを叩き直す
    titleTextForm.value.validate();
  });
  if (textSections.value.includes(keyValue)) {
    validateText(event, keyValue);
    reportValidation();
  }
  if (urlSections.value.includes(keyValue)) {
    validateUrl(event, keyValue);
    reportValidation();
  }

  // NOTE: goes unused since we're using onChangeThumbnailFile instead.
  if (keyValue == "thumbnailImageUrl") {
    const response = fetch(event, {
      method: "HEAD",
      cache: "no-cache",
    })
      .then((response) => {
        if (response.status == 200 && allowedContentType.value.includes(response.headers.get("Content-Type"))) {
          if (Number(response.headers.get("Content-Length")) > sizeLimitThumbnail.value) {
            props.params.thumbnailImageUrl = thumbnailImageUrlOriginal.value;
            tempThumbnailUrl.value = null;
            validTempThumbnailUrl.value = false;
            reportValidation();
            $q.notify({ message: "ファイルサイズは10MB以下にしてください。", type: "error" });
          } else {
            props.params.thumbnailImageUrl = tempThumbnailUrl.value;
            validTempThumbnailUrl.value = true;
            reportValidation();
            emit("updateParams", { key: "tempThumbnailUrl", value: event });
          }
        } else {
          props.params.thumbnailImageUrl = thumbnailImageUrlOriginal.value;
          tempThumbnailUrl.value = null;
          validTempThumbnailUrl.value = false;
          reportValidation();
          $q.notify({ message: "URL形式が正しくありません。", type: "error" });
        }
        return response;
      })
      .catch((error) => {
        props.params.thumbnailImageUrl = thumbnailImageUrlOriginal.value;
        tempThumbnailUrl.value = null;
        validTempThumbnailUrl.value = false;
        reportValidation();
        $q.notify({
          message: "このURLの内容を読み込めない。URLの内容をダウンロードしてLBDでローカルファイル使ってください。",
          type: "error",
        });
        return error;
      });
  } else {
    emit("updateParams", { key: keyValue, value: event });
  }
};

const getActionByNumber = (number: any): any => {
  let actionObj = props.params[`actions.${number}`];
  return actionObj;
};

const isUrlSet = (): boolean => {
  if (props.params) {
    return props.params["thumbnailImageUrl"] != "none" && props.params["thumbnailImageUrl"] !== "";
  } else {
    return false;
  }
};

const validateText = (event: any, keyValue: any): void => {
  if (keyValue == "thumbnailImageUrl" && !buttonTemplateUrl.value) {
    isValidText.value[keyValue] = true;
    return;
  }
  if (typeof event === "string") {
    if (keyValue === "text" || keyValue === "title") {
      const title = keyValue === "title" ? event : props.params.title;
      const text = keyValue === "text" ? event : props.params.text;
      isValidText.value["title"] = isValidTextChar(title) === true && isValidTitleLength(title) === true;
      isValidText.value["text"] = isValidTextChar(text) === true && isValidTextLength(text) === true;
      return;
    }

    isValidText.value[keyValue] = isValidTextChar(event) === true;
    return;
  }

  isValidText.value[keyValue] = false;
};

const validateUrl = (event: any, keyValue: any): void => {
  if (urlSections.value.includes(keyValue)) {
    if (event && (event.startsWith("https://") || event.startsWith("http://"))) {
      isValidUrl.value[keyValue] = true;
    } else {
      isValidUrl.value[keyValue] = false;
    }
  }
};

const validateAction = (value: any, index: any): void => {
  isValidActionList.value[index] = value;
  reportValidation();
};

const reportValidation = (): void => {
  if (isValidText.value["title"] === false) {
    //console.error("[Buttons] Title validation failed");  
    emit("updateSaveStatus", { key: `ItemButtonTemplate`, value: false });
    return;
  }
  if (isValidText.value["text"] === false) {
    //console.error("[Buttons] Text validation failed");
    emit("updateSaveStatus", { key: `ItemButtonTemplate`, value: false });
    return;
  }
  for (const section of textSections.value) {
    if (!isValidText.value[section] && !newImageLocal.value) {
      //console.error(`[Buttons] Text section validation failed: ${section}`);
      emit("updateSaveStatus", { key: `ItemButtonTemplate`, value: false });
      return;
    }
  }
  for (const section of urlSections.value) {
    if (!isValidUrl.value[section] && !newImageLocal.value) {
      //console.error(`[Buttons] URL section validation failed: ${section}`);
      emit("updateSaveStatus", { key: `ItemButtonTemplate`, value: false });
      return;
    }
  }
  for (var i = 0; i < props.params.actionCount; i++) {
    if (!isValidActionList.value[i]) {
      //console.error(`[Buttons] Action validation failed at index: ${i}`);
      emit("updateSaveStatus", { key: `ItemButtonTemplate`, value: false });
      return;
    }
  }
  if (editImageFile.value && newImageLocal.value) {
    if (!validTempThumbnailFile.value) {
      //console.error("[Buttons] Thumbnail file validation failed");
      emit("updateSaveStatus", { key: `ItemButtonTemplate`, value: false });
      return;
    }
  }
  if (editImageFile.value && !newImageLocal.value) {
    if (!validTempThumbnailUrl.value) {
      //console.error("[Buttons] Thumbnail URL validation failed");
      emit("updateSaveStatus", { key: `ItemButtonTemplate`, value: false });
      return;
    }
  }
  emit("updateSaveStatus", { key: `ItemButtonTemplate`, value: true });
};

const isValidTitleLength = (value: any): any => {
  let result_val: any = true;

  if (value !== undefined && value.length > 40) {
    result_val = "タイトルの最大長は40文字です";
  }

  return result_val;
};

const isValidTextLength = (value: any): any => {
  let return_val: any = true;
  // Text is required for button template
  // If title or image is present, max size is 60
  if (value !== undefined) {
    if (value.length <= 0) {
      return_val = "必須";
    } else if (value.length > maxTextLength.value) {
      return_val = `テキストの最大長は${maxTextLength.value}文字です`;
    }
  } else {
    return_val = "必須";
  }

  return return_val;
};

const isValidTextChar = (value: any): any => {
  if (value === undefined || value.length <= 0) {
    return true;
  }

  for (var ch of value) {
    if (ch !== "\n" && ch !== " " && ch !== "　") {
      return true;
    }
  }
  return "空白または改行のみは保存出来ません";
};

const isValidFileSize = (value: any): any => {
  let return_val: any = true;
  if (value !== null && value.size > 10000000) {
    return_val = "10MB以下の画像ファイルをアップロードして下さい";
  }

  return return_val;
};

const isValidFileType = (value: any): any => {
  let return_val: any = true;
  let validTypeExtensions = ["image/jpg", "image/jpeg", "image/png"];
  if (value !== null && !validTypeExtensions.includes(value.type)) {
    return_val = "画像は .jpg .jpeg .png しか出来ません";
  }
  return return_val;
};

const resetAction = (oldAction: any, actionToResetIndex: number): void => {
  props.params['actions.' + actionToResetIndex] = cloneDeep(oldAction);
};

const moveAction = (movePositionUp: boolean, indexToMove: number): void => {
  let positionDelta: number = movePositionUp ? 1 : -1;
  const originalAction = cloneDeep(props.params['actions.' + indexToMove]);
  props.params['actions.' + indexToMove] = cloneDeep(props.params['actions.' + (indexToMove + positionDelta)]);
  props.params['actions.' + (indexToMove + positionDelta)] = cloneDeep(originalAction);
};

// select all text hotkey support
const selectAll = (event: any): void => {
  if ((event.metaKey || event.ctrlKey) && event.key === "a") {
    event.target.select();
  }
};
</script>