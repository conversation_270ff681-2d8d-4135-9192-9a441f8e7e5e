<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
@font-face {
  font-family: lucidaConsole;
  src: url("../../assets/fonts/LUCON.TTF");
}
</style>

<template>
  <div class="tw-py-4">
    <label>JSONデータ</label>
    <h4 v-show="errorText">
      <span class="text-red"><q-icon name="mdi-alert"></q-icon>JSONに誤字があるため保存できません!</span>
    </h4>
    <q-input
      v-model="inputJson"
      outlined
      background-color="grey lighten-2"
      rows="15"
      ref="textareaRef"
      @update:model-value="onChangeText"
    ></q-input>
  </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref } from 'vue';

// interface LocalState {
//   errorText: boolean;
// }

// props
const props = defineProps<{
  params: any,
}>();

// emit
const emit = defineEmits(['updateModelParams', 'updateSaveStatus']);

// data
const errorText = ref(false);
const textareaRef = ref();
const inputJson = ref(JSON.stringify(props.params, undefined, 4));

// methods
const onChangeText = (value: any): void => {
  if (validateJSON(value)) {
    emit("updateModelParams", JSON.parse(value));
  }
  reportValidation(value);
};

const reportValidation = (value: any): void => {
  let target = value;
  if (target === "") {
    target = textareaRef.value.$el.value();
  }
  emit("updateSaveStatus", { key: `ItemJsonTemplate`, value: validateJSON(target) });
};

const validateJSON = (value: any): boolean => {
  try {
    if (value) {
      JSON.parse(value);
    }
    errorText.value = false;
    return true;
  } catch (error: any) {
    errorText.value = true;
  }
};

onMounted(() => {
  textareaRef.value.input.style.cssText = "white-space:nowrap;font-family:lucidaConsole, monospace;";
  reportValidation(undefined);
});

onBeforeUnmount(() => {
  emit("updateSaveStatus", { key: `ItemJsonTemplate`, value: true });
});
</script>