<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.column-display-carousel {
  background-color: #F2F2F2 !important;
  border-radius: 5px !important;
  margin-bottom: 1em !important;
  padding: 0.5em !important;
}
.caption-text {
  font-size: 12px !important;
  color: #646464 !important;
}
</style>

<template>
  <div>
    <div class="row tw-py-4">
      <label class="col-auto">列数</label>
      <q-select 
        v-model="params.columnCount" 
        :options="columns" 
        option-label="text"
        option-value="value"
        class="col-12"
        outlined hide-details 
        dense
      >
      </q-select>
    </div>
    <div class="row tw-py-4">
      <label class="col-auto">アクション数</label>
      <q-select 
        v-model="params.actionCount" 
        :options="actions" 
        option-label="text"
        option-value="value"
        class="col-12"
        outlined hide-details 
        dense
      >
      </q-select>
    </div>

    <div class="row tw-py-4">
      <label class="col-auto">サムネイル画像を使用する</label>
      <q-select 
        v-model="params.useThumbnailImage" 
        :options="useThumbnailImage"
        outlined hide-details 
        option-label="text"
        option-value="value"
        map-options emit-value
        class="col-12"
        dense
        @update:model-value="(e)=>onChangeValueThumbnail(e)"
      >
      </q-select>
    </div>
    <div v-if="params.useThumbnailImage" class="row tw-py-4">
      <label class="col-auto">画像の表示形式</label>
      <q-select 
        v-model="params.imageSize" 
        :options="templateImageOptions"
        option-label="text"
        option-value="value"
        outlined hide-details 
        map-options emit-value
        class="col-12"
        dense
        @update:model-value="onChangeValue($event, 'imageSize')"
      >
      </q-select>
      <span class="caption-text">＊カバー: 画像領域全体に画像を表示します。画像領域に収まらない部分は切り詰められます。
      <br/>＊コンテイン: 画像領域に画像全体を表示します。縦長の画像では左右に、横長の画像では上下に余白が表示されます。</span>
    </div>
    <div class="row tw-py-4">
      <label class="col-auto">タイトルを使用する</label>
      <q-select 
        v-model="params.useTitle" 
        :options="useThumbnailImage" 
        option-label="text"
        option-value="value"
        class="col-12"
        map-options emit-value
        outlined hide-details 
        dense
      >
      </q-select>
    </div>
    <div v-for="ind in params.columnCount" :key="`row_${ind}`">
      <div class="column-display-carousel">
        <div class="row">
          <div class="col-6">
            <q-icon @click="setExpandColumn(ind - 1)" :name="expandColumn[ind - 1] ? 'mdi-chevron-down' : 'mdi-chevron-right'"></q-icon>
            <label>列{{ ind }}</label>
          </div>
        </div>
        <div v-if="expandColumn[ind - 1]">
          <div v-if="params.useThumbnailImage">
            <div class="row tw-py-4">
              <div class="col-atuo">サムネイル画像</div>
              <q-checkbox
                v-model="editThumbnailFile[`${ind - 1}`]"
                label="サムネイル画像を変更する"
                class="col-12 text-grey-8"
                @change="editingThumbnailImage($event, ind - 1)"
              >
              </q-checkbox>
            </div>
          </div>

          <div v-if="editThumbnailFile[`${ind - 1}`] && params.useThumbnailImage">
            <div class="row tw-py-4 tw-items-center">
              <div class="col-3" style="padding-right: 0">
                <div style="padding: 0" v-text="'タイプ'"/>
              </div>
              <q-select
                v-model="newImageLocal[ind - 1]"
                :options="imageOptions"
                option-label="text"
                option-value="value"
                class="col-9"
                outlined
                dense
                background-color="white"
                map-options emit-value
                @update:model-value="thumbnailImageLocal($event, ind - 1)"
              >
              </q-select>
            </div>
            <div class="row tw-py-4 tw-items-center">
              <div class="col-3" style="padding-right: 0">
                <div style="padding: 0" v-text="'ファイル'"/>
              </div>
              <q-input
                v-if="newImageLocal[ind - 1]"
                outlined
                single-line
                hide-details
                background-color="white"
                dense
                v-model="tempThumbnailUrl[`${ind - 1}`]"
                class="col-9"
                :label-slot="tempThumbnailUrl[`${ind - 1}`] ? false : true"
                @blur="onChangeValue(tempThumbnailUrl[`${ind - 1}`], `thumbnail.${ind - 1}`)"
              >
                <template v-slot:label>
                  https://example.com
                </template>
              </q-input>
              <q-file
                v-else
                v-model="tempThumbnailFile[`${ind - 1}`]"
                @update:model-value="inputThumbnailFile($event, ind - 1)"
                prepend-icon=""
                prepend-inner-icon="mdi-import"
                hide-details="auto"
                outlined
                dense
                background-color="white"
                placeholder="ファイルを選択"
                accept=".jpg,.jpeg,.png"
                class="col-9"
              >
              </q-file>
            </div>
          </div>

          <div v-if="params.useTitle" class="row tw-py-4">
            <label class="col-auto">タイトル</label>
            <q-input
              v-model="params[`title.${ind - 1}`]"
              class="col-12 tw-pb-0"
              outlined
              dense
              hide-details="auto"
              background-color="white"
              @update:model-value="onChangeValue($event, `title.${ind - 1}`)"
              :rules="[rules.validTextLength, rules.validTextChar]"
            >
            </q-input>
          </div>

          <div class="row tw-py-4">
            <label class="col-auto">テキスト</label>
            <q-input
              v-model="params[`text.${ind - 1}`]"
              class="col-12 tw-pb-0"
              outlined
              dense
              hide-details="auto"
              bg-color="white"
              @update:model-value="onChangeValue($event, `text.${ind - 1}`)"
              :rules="[rules.validTextLength, rules.validTextChar]"
            >
            </q-input>
          </div>

          <div v-for="(action, index) in params.actionCount" :key="action">
            <ActionProperty
              of="template"
              :number="action"
              :firstAction="action == 1"
              :lastAction="action == params.actionCount"
              :premadeMessage="isSpecialPremadeTalk()"
              :action="getActionByNumber(ind, index)"
              @validateAction="validateAction($event)"
              @resetAction="resetAction($event, (ind - 1), index)"
              @moveAction="moveAction($event, (ind - 1), index)"
              v-bind:ref="actionRef"
              :botMessages="botMessages"
              :specialTalk="specialTalk"
              :isDamageReportCategory="isDamageReportCategory"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ActionProperty from "@/components/BotDesigner/CommonProperties/ActionProperty.vue";
import { cloneDeep } from "lodash";
import { useQuasar } from "quasar";
import { computed, nextTick, onBeforeMount, onMounted, ref, watch } from "vue";

// quasar
const $q = useQuasar();

// props
const props = defineProps<{
  params: any,
  show?: boolean,
  branchIndex?: number,
  botMessages?: any[],
  specialTalk?: boolean,
  webAppId?: string,
  targetDataField?: string
}>();

const isDamageReportCategory = computed(() => {
  return props.targetDataField === 'category'
});
// emit
const emit = defineEmits(['updateSaveStatus', 'updateParams']);

// watch
watch(
  () => props.params,
  (val) => {
    displayThumbnails.value = setDisplayThumbnails();
    isPremadeMessage.value = isSpecialPremadeTalk();
    actionsAreValid.value = true;
    editThumbnailFile.value = [false, false, false, false, false, false, false, false, false, false];
    newImageLocal.value = Array(10).fill(false);
    expandColumn.value = [true, true, true, true, true, true, true, true, true, true];
    tempThumbnailUrl.value = [null, null, null, null, null, null, null, null, null, null];
    tempThumbnailFile.value = [null, null, null, null, null, null, null, null, null, null];
    tempThumbnailFileDisplay.value = [null, null, null, null, null, null, null, null, null, null];
    paramsOriginal.value = cloneDeep(val);
    setDefaultValues();
  }
);

watch(
  () => props.show,
  (val) => {
    if (val) {
      for (let i = 0; i < 10; i++) {
        isValidText.value["title." + i.toString()] = true;
      }
      for (let i = 0; i < 10; i++) {
        isValidText.value["text." + i.toString()] = true;
      }
    }
  }
);

watch(
  props.params.useTitle,
  (val) => {
    reportValidation();
  }
);

watch(
  props.params.useThumbnailImage,
  (val) => {
    reportValidation();
  }
);

watch(
  props.params.columnCount,
  (val) => {
    reportValidation();
  }
);

watch(
  props.params.actionCount,
  (newVal, oldVal) => {
    if (newVal > oldVal && isPremadeMessage.value) {
      for (var col = 1; col <= props.params.columnCount; col++) {
        for (var x = oldVal; x < newVal; x++) {
          var tempAction = getActionByNumber(col, x);
          tempAction["type"] = "postback";
        }
      }
    }
  }
);

watch(
  () => props.branchIndex,
  (val) => {
    // props.branchIndex = val;
    scrollToAction(val);
  }
);

// data
const temp = ref([]);
const columns = ref([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
const editThumbnailFile = ref([false, false, false, false, false, false, false, false, false, false]);
const newImageLocal = ref(Array(props.params.columnCount).fill({ text: "URL", value: 'url' }));
const expandColumn = ref([true, true, true, true, true, true, true, true, true, true]);
const tempThumbnailUrl = ref([null, null, null, null, null, null, null, null, null, null]);
const tempThumbnailFile = ref([null, null, null, null, null, null, null, null, null, null]);
const tempThumbnailFileDisplay = ref([null, null, null, null, null, null, null, null, null, null]);
const imageOptions = ref([
  { text: "URL", value: true },
  { text: "ローカルファイル", value: false },
]);
const actions = ref([1, 2, 3]);
const useThumbnailImage = ref([
  { text: "はい", value: true },
  { text: "いいえ", value: false },
]);
const templateImageOptions = ref([
  { text: "カバー", value: 'cover' },
  { text: "コンテイン", value: 'contain' },
]);
const actionsAreValid = ref(true);
const allowedContentType = ref(["image/jpg", "image/jpeg", "image/png"]);
const displayThumbnails = ref(null);
const isPremadeMessage = ref(null);
const sizeLimitThumbnail = ref(1000000);
const rules = ref({
  validTextLength: (value) => value.length > 0 || "必須",
  validTextChar: (value) => {
    for (var ch of value) {
      if (ch !== "\n" && ch !== " " && ch !== "　") {
        return true;
      }
    }
    return "空白または改行のみは保存出来ません";
  },
});
const isValidText = ref(temp);
const paramsOriginal = ref({});
const actionRef = ref();

// hooks
onMounted(() => {
  setDefaultValues();
  if (!Number.isInteger(props.branchIndex)) {
    return;
  }
  scrollToAction(props.branchIndex);
  reportValidation();
});

onBeforeMount(() => {
  emit("updateSaveStatus", { key: `ItemCarouselTemplate`, value: false });
});

onBeforeMount(() => {
  setDefaultValues();
  displayThumbnails.value = setDisplayThumbnails();

  for (var i = 0; i < 10; i++) {
    temp.value["title." + i.toString()] = true;
    temp.value["text." + i.toString()] = true;
  }
});

const onChangeValueThumbnail = (e) => {
  props.params.useThumbnailImage = e
  reportValidation();
}
// methods
const setDefaultValues = (): void => {
  if(props.params && !props.params.imageSize) {
    props.params.imageSize = 'cover';
  }
};

const scrollToAction = (branchIndex: any): void => {
  if (!Number.isInteger(branchIndex)) {
    return;
  }

  // const targetRef = this.$refs[`action.${branchIndex}`];
  if (actionRef.value && actionRef.value.length > 0) {
    nextTick(() =>
      actionRef.value[0].$el.scrollIntoView({
        behavior: "smooth",
        block: "start",
        inline: "nearest",
      })
    );
  }
};

const isSpecialPremadeTalk = (): any => {
  return "specialScenarioTalk" in props.params ? props.params["specialScenarioTalk"] : null;
};

const onChangeValue = (event: any, keyValue: any): void => {
  if (keyValue.includes("thumbnail.")) {
    var thumbnailNumber = keyValue.split(".")[1];
    const response = fetch(event, {
      method: "HEAD",
      cache: "no-cache",
    })
      .then((response) => {
        if (response.status == 200 && allowedContentType.value.includes(response.headers.get("Content-Type"))) {
          if (Number(response.headers.get("Content-Length")) > sizeLimitThumbnail.value) {
            displayThumbnails.value["thumbnail." + thumbnailNumber] = props.params["thumbnail." + thumbnailNumber];
            $q.notify({ message: "ファイルサイズは1MB以下にしてください。", type: "error" });
          } else {
            emit("updateParams", { key: keyValue, value: event });
          }
        } else {
          displayThumbnails.value["thumbnail." + thumbnailNumber] = props.params["thumbnail." + thumbnailNumber];
          $q.notify({ message: "URL形式が正しくありません。", type: "error" });
        }
        return response;
      })
      .catch((error) => {
        displayThumbnails.value["thumbnail." + thumbnailNumber] = props.params["thumbnail." + thumbnailNumber];
        $q.notify({
          message: "このURLの内容を読み込めない。URLの内容をダウンロードしてLBDでローカルファイル使ってください。",
          type: "error",
        });
        return error;
      });
  } else {
    if (keyValue.startsWith("title.") || keyValue.startsWith("text.")) {
      validateText(event, keyValue);
    }
    reportValidation();
    emit("updateParams", { key: keyValue, value: event });
  }
};

const getActionByNumber = (index: any, number: any): number => {
  let actionObj = props.params[`action.${index - 1}.${number}`];
  return actionObj;
};

const setDisplayThumbnails = (): any => {
  var result = {};
  for (var x = 0; x < 10; x++) {
    result["thumbnail." + x] = props.params["thumbnail." + x];
  }
  return result;
};

const validateText = (event: any, keyValue: any): void => {
  if (event.length > 0) {
    for (var ch of event) {
      if (ch !== "\n" && ch !== " " && ch !== "　") {
        isValidText.value[keyValue] = true;
        return;
      }
    }
  }
  isValidText.value[keyValue] = false;
};

const validateAction = (value: any): void => {
  actionsAreValid.value = value;
  reportValidation();
};

const editingThumbnailImage = (event: any, index: any): void => {
  if (event === false) {
    props.params["thumbnail." + index] = paramsOriginal.value["thumbnail." + index];
    tempThumbnailUrl.value[index] = null;
    tempThumbnailFile.value[index] = null; 
  }
  emit("updateParams", { key: "editingThumbnailImage." + index, value: event });
  reportValidation();
};

const thumbnailImageLocal = (event: any, index: any): void => {
  emit("updateParams", { key: "thumbnailImageEditLocal." + index, value: event });
  reportValidation();
};

const inputThumbnailUrl = (event: any, index: any): void => {
  emit("updateParams", { key: "tempThumbnailUrl." + index, value: event });
  checkValidImage(event, index);
  reportValidation();
};

const inputThumbnailFile = (event: any, index: any): void => {
  emit("updateParams", { key: "tempThumbnailFile." + index, value: event });
  checkValidFile(event, index);
  reportValidation();
};

const checkValidFile = (file: any, columnIndex: any): void => {
  if (file) {
    var reader = new FileReader();
    reader.onload = (e) => {
      tempThumbnailFileDisplay.value[columnIndex] = e.target.result;
      props.params["thumbnail." + columnIndex] = tempThumbnailFileDisplay.value[columnIndex];
      reportValidation();
    };
    if (file.size > sizeLimitThumbnail.value || (file.type != "image/jpeg" && file.type != "image/png")) {
      props.params["thumbnail." + columnIndex] = paramsOriginal.value["thumbnail." + columnIndex];
      tempThumbnailFileDisplay.value[columnIndex] = null;
      tempThumbnailFile.value[columnIndex] = null;
      $q.notify({
        message: "ファイルサイズが1MB以上で、ファイル形式がJPEGまたはPNGではない。このファイルは使用できません。",
        type: "error",
        color: "negative",
        icon: "error",
      });
      reportValidation();
    } else {
      reader.readAsDataURL(file);
    }
  } else {
    props.params["thumbnail." + columnIndex] = paramsOriginal.value["thumbnail." + columnIndex];
    tempThumbnailFileDisplay.value[columnIndex] = null;
    tempThumbnailFile.value[columnIndex] = null;
    reportValidation();
  }
};

const checkValidImage = (url: any, columnIndex: any): void => {
  const response = fetch(url, {
    method: "HEAD",
    cache: "no-cache",
  })
    .then((response) => {
      if (response.status == 200 && allowedContentType.value.includes(response.headers.get("Content-Type"))) {
        if (Number(response.headers.get("Content-Length")) > sizeLimitThumbnail.value) {
          props.params["thumbnail." + columnIndex] = paramsOriginal.value["thumbnail." + columnIndex];
          tempThumbnailUrl.value[columnIndex] = null;
          reportValidation();
          $q.notify({ message: "ファイルサイズは1MB以下にしてください。", type: "error" });
          return response;
        }
      } else {
        props.params["thumbnail." + columnIndex] = paramsOriginal.value["thumbnail." + columnIndex];
        tempThumbnailUrl.value[columnIndex] = null;
        reportValidation();
        $q.notify({ message: "URL形式が正しくありません。", type: "error" });
        return response;
      }
      props.params["thumbnail." + columnIndex] = url;
      return response;
    })
    .catch((error) => {
      props.params["thumbnail." + columnIndex] = paramsOriginal.value["thumbnail." + columnIndex];
      tempThumbnailUrl.value[columnIndex] = null;
      reportValidation();
      $q.notify({
        message: "このURLの内容を読み込めない。URLの内容をダウンロードしてLBDでローカルファイル使ってください。",
        type: "error",
      });
      return error;
    });
};

const reportValidation = (): void => {
  for (var i = 0; i < props.params.columnCount; i++) {
    if (props.params.useTitle && !isValidText.value["title." + i.toString()]) {
      emit("updateSaveStatus", { key: `ItemCarouselTemplate`, value: false });
      return;
    }
    if (!isValidText.value["text." + i.toString()]) {
      emit("updateSaveStatus", { key: `ItemCarouselTemplate`, value: false });
      return;
    }

    if (props.params.useThumbnailImage) {
      if (newImageLocal.value[i]) {
        if (tempThumbnailFile.value[i] == null) {
          emit("updateSaveStatus", { key: `ItemCarouselTemplate`, value: false });
          return;
        }
      } else {
        if (
          props.params["thumbnail." + i] === "" &&
          (tempThumbnailUrl.value[i] === null || tempThumbnailUrl.value[i] === "")
        ) {
          emit("updateSaveStatus", { key: `ItemCarouselTemplate`, value: false });
          return;
        }
      }
    }
  }

  if (!actionsAreValid.value) {
    emit("updateSaveStatus", { key: `ItemCarouselTemplate`, value: false });
    return;
  }
  emit("updateSaveStatus", { key: `ItemCarouselTemplate`, value: true });
};

const setExpandColumn = (indexOfColumn: number): void => {
  expandColumn.value[indexOfColumn] = !expandColumn.value[indexOfColumn];
};

const resetAction = (oldAction: any, columnIndex: number, actionToResetIndex: number): void => {
  // console.log("in reset action");
  // console.log(columnIndex);
  // console.log(actionToResetIndex);
  props.params['action.' + columnIndex + '.' + actionToResetIndex] = cloneDeep(oldAction);
};

const moveAction = (movePositionUp: boolean, columnIndex: number, indexToMove: number): void => {
  let positionDelta: number = movePositionUp ? 1 : -1;
  const originalAction = cloneDeep(props.params['action.' + columnIndex + '.' + indexToMove]);
  props.params['action.' + columnIndex + '.' + indexToMove] =
    cloneDeep(props.params['action.' + columnIndex + '.' + (indexToMove + positionDelta)]);
  props.params['action.' + columnIndex + '.' + (indexToMove + positionDelta)] = cloneDeep(originalAction);
};
</script>