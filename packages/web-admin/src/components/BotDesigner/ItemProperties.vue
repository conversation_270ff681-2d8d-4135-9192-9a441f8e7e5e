<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-layout v-if="show" class="tw-fixed tw-top-0 tw-right-0 tw-z-50">
      <q-drawer
        v-model="show"
        side="right"
        class="tw-z-10"
        :width="500"
      >
          <!-- ref="property-drawer"
          transition="scroll-x-transition"
          :style="`width: calc(${35}% + 38px); z-index: 8`" -->
        <q-card :max-width="maxWidthDialog" style="flex-direction: row; overflow: hidden" 
        :class="`tw-flex tw-h-full ${isMenuVertical ? 'tw-pt-14' : ''}`">
          <div class="tw-flex tw-items-center">
            <div class="drawer-shrink-toggle-icon">
              <q-btn style="min-width: 0; padding: 0 0.5em" flat rounded @click="onClickOutside">
                <q-icon name="mdi-chevron-right"></q-icon>
              </q-btn>
            </div>
          </div>
          <div style="flex: 1; display: flex; height: 100%; flex-direction: column; overflow: hidden" class="tw-p-3">
            <q-toolbar>
              <q-space/>
              <q-btn style="margin-right: 1em" outline color="primary" @click="closeModal"> キャンセル </q-btn>
              <q-btn
                :disable="!canSave"
                color="primary"
                @click="
                  hasActionPermission('click', 'backendRequest') ? onUpdateItemProperties() : showActionPermissionError()
                "
                :style="
                  hasActionPermission('hideButton', 'Components_BotDesigner_ItemProperties_Save')
                    ? hideButtonPermissionStyle()
                    : ''
                "
              >
                適用する
              </q-btn>
            </q-toolbar>
            <div id="drawer-content" style="flex: 1; overflow-y: auto; padding-right: 1em;">
              <div class="tw-py-3">
                <label>メッセージID</label>
                <div class="row">
                  <div class="col-8" style="padding-top: 0">
                    <q-input v-model="modelLocal.dataId" outlined hide-details dense disable style="background-color: lightgrey" >
                    </q-input>
                  </div>
                  <div class="col tw-ml-4">
                    <q-btn color="primary" @click="copyIdToClipboard">コピー</q-btn>
                  </div>
                </div>
              </div>
              <div class="row tw-py-3">
                <div class="col">
                  <label>メッセージ名</label>
                  <q-input
                    outlined
                    dense
                    hide-details="auto"
                    v-model="modelLocal.nameLBD"
                    @update:model-value="onChangeName($event)"
                    :rules="[rules.validTextLength]"
                  >
                  </q-input>
                </div>
              </div>
              <div class="row" v-if="isCustomDamageReportTalk">
                <q-col class="col-12">
                      <DamageReportFieldRequired
                        :data-id="modelLocal.dataId"
                        v-model:targetDataField="modelLocal.targetDataField"
                        v-model:targetDataOrder="modelLocal.targetDataOrder"
                        v-model:parentDataField="modelLocal.parentDataField" 
                        :dataType="modelLocal.dataType">
                      </DamageReportFieldRequired>
                </q-col>
              </div>
              <div class="row">
                <div class="col">
                  <div v-if="modelLocal.dataType === 'text'">
                    <ItemTextMessage
                      :params="modelLocal.params"
                      @updateParams="updateParams"
                      @updateSaveStatus="updateSaveStatus"
                    />
                  </div>
                  <div v-if="modelLocal.dataType === 'buttons'">
                    <ItemButtonTemplate
                      :params="modelLocal.params"
                      @updateParams="updateParams"
                      :canSave="canSave"
                      :specialTalk="isSpecialTalk"
                      @updateSaveStatus="updateSaveStatus"
                      :dataId="modelLocal.dataId"
                      :botMessages="messages"
                      :targetDataField="modelLocal.targetDataField"
                    />
                  </div>
                  <div v-if="modelLocal.dataType === 'richmenu'">
                    <ItemRichMenu :params="modelLocal.params" @updateSaveStatus="updateSaveStatus" :specialTalk="isSpecialTalk" />
                  </div>
                  <div v-if="modelLocal.dataType === 'imagemap'">
                    <ItemImageMapMessage
                      :webAppId="modelLocal.dataId"
                      :params="modelLocal.params"
                      @updateParams="updateModelParams"
                      @fileImageMapDataUpdate="fileImageMapDataUpdate"
                      :canSave="canSave"
                      @updateSaveStatus="updateSaveStatus"
                    />
                  </div>
                  <div v-if="modelLocal.dataType === 'sticker'">
                    <ItemSticker
                      :webAppId="modelLocal.dataId"
                      :params="modelLocal.params"
                      @updateParams="updateParams"
                      @updateSaveStatus="updateSaveStatus"
                    />
                  </div>
                  <div v-if="modelLocal.dataType === 'location'">
                    <ItemLocation
                      :webAppId="modelLocal.dataId"
                      :params="modelLocal.params"
                      @updateParams="updateParams"
                      :canSave="canSave"
                      @updateSaveStatus="updateSaveStatus"
                    />
                  </div>
                  <div v-if="modelLocal.dataType === 'confirm'">
                    <ItemConfirmTemplate
                      :webAppId="modelLocal.dataId"
                      :params="modelLocal.params"
                      @updateParams="updateParams"
                      :canSave="canSave"
                      :specialTalk="isSpecialTalk"
                      @updateSaveStatus="updateSaveStatus"
                      :botMessages="messages"
                    />
                  </div>
                  <div v-if="modelLocal.dataType === 'image'">
                    <ItemImage
                      :webAppId="modelLocal.dataId"
                      :params="modelLocal.params"
                      @updateParams="updateParams"
                      @fileImageDataUpdate="fileImageDataUpdate"
                      :canSave="canSave"
                      @updateSaveStatus="updateSaveStatus"
                    />
                  </div>
                  <div v-if="modelLocal.dataType === 'audio'">
                    <ItemAudio
                      :webAppId="modelLocal.dataId"
                      :params="modelLocal.params"
                      @updateParams="updateParams"
                      @fileAudioDataUpdate="fileAudioDataUpdate"
                      :canSave="canSave"
                      @updateSaveStatus="updateSaveStatus"
                    />
                  </div>
                  <div v-if="modelLocal.dataType === 'video'">
                    <ItemVideo
                      :webAppId="modelLocal.dataId"
                      :params="modelLocal.params"
                      @updateParams="updateParams"
                      @fileVideoDataUpdate="fileVideoDataUpdate"
                      :canSave="canSave"
                      @updateSaveStatus="updateSaveStatus"
                    />
                  </div>
                  <div v-if="modelLocal.dataType === 'carousel'">
                    <ItemCarouselTemplate
                      :webAppId="modelLocal.dataId"
                      :params="modelLocal.params"
                      :show="show"
                      :specialTalk="isSpecialTalk"
                      :botMessages="messages"
                      @updateParams="updateParams"
                      @updateSaveStatus="updateSaveStatus"
                      :targetDataField="modelLocal.targetDataField"
                    />
                  </div>
                  <div v-if="modelLocal.dataType === 'compositeMessage'">
                    <ItemCompositeMessage
                      :params="modelLocal"
                      @updateMessages="updateMessages"
                      @updateSaveStatus="updateSaveStatus"
                    />
                  </div>
                  <div v-if="modelLocal.dataType === 'bubbleFlex'">
                    <ItemBubbleFlex
                      :params="modelLocal.params"
                      @updateModelParams="updateModelParams"
                      @updateSaveStatus="updateSaveStatus"
                    />
                  </div>
                  <div v-if="modelLocal.dataType === 'carouselFlex'">
                    <ItemCarouselFlex
                      :params="modelLocal.params"
                      @updateModelBubbles="updateModelBubbles"
                      @updateSaveStatus="updateSaveStatus"
                    />
                  </div>
                  <div v-if="modelLocal.dataType === 'jsonTemplate'">
                    <ItemJsonTemplate
                      :params="modelLocal.params"
                      @updateModelParams="updateModelParams"
                      @updateSaveStatus="updateSaveStatus"
                    />
                  </div>
                  <div v-if="modelLocal.dataType === '__INITIAL__'">
                    <div class="tw-my-5 tw-pl-6">ノードのタイプをシナリオマップで選択してください。</div>
                  </div>
                  <div v-if="modelLocal.dataType === 'default'">
                    <div class="tw-my-5 tw-pl-6">{{ modelLocal.dataType }} は未対応です。</div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div v-if="!hidePreviewDisplay.includes(modelLocal.dataType)" class="col">
                  <MessagePreview :messages="[modelLocal]" />
                </div>
              </div>
            </div>
          </div>
        </q-card>
      </q-drawer>
    </q-layout>
  
    <q-dialog v-model="showLeaveConfirm" persistent width="500">
      <q-card>
          <q-card-section class="text-h6">メッセージの編集を終了してもよろしいですか？</q-card-section>
          <q-card-section class="text-grey-7 tw-pt-0">行った変更は破棄されます。</q-card-section>
          <q-separator />
          <q-card-actions class="tw-p-4" align="right">
            <div>
              <q-space/>
              <q-btn
                color="primary"
                @click="closeModal"
              >
                はい
              </q-btn>
              <q-btn outline color="primary" @click="cancelLeave" class="tw-ml-2">
                キャンセル
              </q-btn>
            </div>
          </q-card-actions>
        </q-card>
    </q-dialog>
  
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, ref, watch } from "vue";
import ItemTextMessage from "@/components/BotDesigner/ItemTextMessage.vue";
import ItemImageMapMessage from "@/components/BotDesigner/ItemImageMapMessage.vue";
import ItemRichMenu from "@/components/BotDesigner/ItemRichMenu.vue";
import ItemButtonTemplate from "@/components/BotDesigner/ItemButtonTemplate.vue";
import ItemSticker from "@/components/BotDesigner/ItemSticker.vue";
import ItemLocation from "@/components/BotDesigner/ItemLocation.vue";
import ItemConfirmTemplate from "@/components/BotDesigner/ItemConfirmTemplate.vue";
import ItemImage from "@/components/BotDesigner/ItemImage.vue";
import ItemAudio from "@/components/BotDesigner/ItemAudio.vue";
import ItemVideo from "@/components/BotDesigner/ItemVideo.vue";
import ItemCarouselTemplate from "@/components/BotDesigner/ItemCarouselTemplate.vue";
import ItemCompositeMessage from "@/components/BotDesigner/ItemCompositeMessage.vue";
import MessagePreview from "@/pages/admin/ScenarioSettingsDetail/components/MessagePreview.vue";
import ItemBubbleFlex from "@/components/BotDesigner/ItemBubbleFlex.vue";
import ItemCarouselFlex from "@/components/BotDesigner/ItemCarouselFlex.vue";
import ItemJsonTemplate from "@/components/BotDesigner/ItemJsonTemplate.vue";
import { cloneDeep } from "lodash";
import { SPECIAL_TALK_TYPES } from "@/stores/modules/scenarios/scenarios.constants";
import { useQuasar } from "quasar";
import { useScenariosStore } from "@/stores/modules/scenarios";
import { usePermissionHelper } from "@/mixins/PermissionHelper";
import { IDamageReportStorePlugins } from "@/stores/modules/scenarios/scenarios.damage-report.plugin";
import DamageReportFieldRequired from "@/components/BotDesigner/DamageReportFieldRequired.vue";
import { useSettingsStore } from "@/stores/modules/settings";
// quasar
const $q = useQuasar();

// store with plugin typed
const scenarioStoreUnTyped = useScenariosStore();
export type ScenarioStoreWithPlugin = typeof scenarioStoreUnTyped & IDamageReportStorePlugins;
const scenarioStore = scenarioStoreUnTyped as ScenarioStoreWithPlugin

// props
const props = defineProps<{
  visible: boolean,
  model: any,
  messages: any[],
  scenarioId: string,
  versionId: string,
  talkName: string,
  parentClicked: boolean,
  isDamageReportTalk: any
}>();

const setitngsStore = useSettingsStore();

// emit
const emit = defineEmits(['close', 'updateSaveState', 'updateSaveStatus']);

// data
const isMenuVertical = ref(setitngsStore.commonSettings.menuStyle === "vertical");
const modelLocal = ref<any>({});
const hidePreviewDisplay = ref<string[]>(["jsonTemplate", "compositeMessage"]);
const originalModel = ref<any>({});
const imageMapFiles = ref<any[]>([]);
const audioFiles = ref<any[]>([]);
const imageFiles = ref<any[]>([]);
const videoFiles = ref<any[]>([]);
const canSave = ref<boolean>(false);
const contentRendered = ref<boolean>(false);
const showLeaveConfirm = ref<boolean>(false);
const rules = ref<any>({
  validTextLength: (value) => {
    if (value && value.length > 400) {
      return "400文字の制限";
    } else {
      return true;
    }
  },
});

// watch
watch(
  () => props.model,
  (value) => {
    canSave.value = false;
    modelLocal.value = cloneDeep(value);
  }
);

watch(
  () => props.visible,
  (value) => {
    canSave.value = false;
    if (value) {
      originalModel.value = cloneDeep(modelLocal.value);
      setTimeout(function(){ contentRendered.value = true; }, 100);
    }
  }
);

watch(
  () => props.parentClicked,
  () => {
    onClickOutside();
  }
);

// computed
const show = computed({
  get() {
    setTimeout(() => {
      canSave.value = false; 
    }, 100);
    return props.visible;
  },
  set(value) {
    if (!value) {
      emit("close");
    }
  },
});

const maxWidthDialog = computed((): number => {
  if (
    modelLocal.value.dataType == "compositeMessage" ||
    modelLocal.value.dataType == "carouselFlex" ||
    modelLocal.value.dataType == "bubbleFlex"
  ) {
    return 1500;
  }
  return 800;
});

const isSpecialTalk = computed((): boolean => {
  if (!modelLocal.value.params || !modelLocal.value.params.specialScenarioTalk) {
    return false;
  }
  return SPECIAL_TALK_TYPES.includes(modelLocal.value.params.specialScenarioTalk);
});

// methods
const { updateScenarioData } = scenarioStore;
const { hasActionPermission, showActionPermissionError, hideButtonPermissionStyle } = usePermissionHelper();

const closeModal = (): void => {
  contentRendered.value = false;
  show.value = false;
  showLeaveConfirm.value = false;
  modelLocal.value = originalModel.value;
  emit("updateSaveState", false);
};

const onUpdateItemProperties = (): void => {
  if (modelLocal.value.nameLBD && modelLocal.value.nameLBD.length == 0) {
    delete modelLocal.value.nameLBD;
  }
  var payload: any = {
    model: modelLocal.value,
    scenario: {
      scenarioId: props.scenarioId,
      versionId: props.versionId,
    },
  };
  switch (props.model.dataType) {
    case "imagemap":
      payload["files"] = imageMapFiles.value;
      break;
    case "audio":
      payload["files"] = audioFiles.value;
      break;
    case "image":
      payload["files"] = imageFiles.value;
      break;
    case "video":
      payload["files"] = videoFiles.value;
      break;
    default:
  }
  if (props.isDamageReportTalk && props.isDamageReportTalk?.dataId !== "DAMAGE_REPORT_TALK") {
    payload.talkMessages = scenarioStore.scenarioTalks.find(msg => msg.dataType === "talk" && msg.params.name === props.talkName)
    scenarioStore.UpdateDamageReportScenarioData(payload)
  }
  else {
    scenarioStore.updateScenarioData(payload);
  }

  closeModal();
  emit("close");
  emit("updateSaveStatus", { value: false });
};

const updateModelParams = (value: any): void => {
  modelLocal.value.params = value;
};

const updateModelBubbles = (value: any): void => {
  modelLocal.value.params.bubbleParam = value;
};

const updateParams = ({ key, value }) => {
  if (key === "__REPLACE__") {
    modelLocal.value.params = value;
  } else {
    modelLocal.value.params[key] = value;
  }
};

const updateMessages = (value: any): void => {
  modelLocal.value.messages = value;
};

const fileImageMapDataUpdate = (value: any): void => {
  imageMapFiles.value = value;
};

const fileAudioDataUpdate = (value: any): void => {
  audioFiles.value = value;
};

const fileImageDataUpdate = (value: any): void => {
  imageFiles.value = value;
};

const fileVideoDataUpdate = (value: any): void => {
  videoFiles.value = value;
};

const updateSaveStatus = ({ value }: any): void => {
  canSave.value = value;
};

const onChangeName = (event: any): void => {
  updateSaveStatus({ value: event.length <= 400 });
};

const copyIdToClipboard = (): void => {
  navigator.clipboard.writeText(modelLocal.value.dataId)
    .then(() => { $q.notify({ message: "クリップボードにコピーしました。" }); });
};

const cancelLeave = (): void => {
  setTimeout(function(){ showLeaveConfirm.value = false; }, 100);
};

const onClickOutside = (): void => {
  if(show.value && contentRendered.value && !showLeaveConfirm.value){
    showLeaveConfirm.value = true;
  }
};

// hooks
onBeforeMount(() => {
  modelLocal.value = cloneDeep(props.model) || {};
});

// #region damage report
const scenarioTalks = computed(() => scenarioStore.scenarioTalks);

const damageReportTalkVersion = computed((): any => {
  return scenarioTalks.value?.filter(x => x.versionOf === 'damage-report' || x.dataId === 'DAMAGE_REPORT_TALK')?.map(x => {
    return {
      ...x,
      value: x.dataId,
      text: x.params?.name,
      versionId: x.versionId
    }
  });
})
const isCustomDamageReportTalk = computed(() => {
  return props.talkName!= "損傷報告"  && !!damageReportTalkVersion.value.find(x=>x.text === props.talkName)
})
// #endregion
</script>

<style lang="less">
.bot-designer-item-properties {
  label {
    margin-bottom: 5px;

    display: inline-block;
  }
}
</style>