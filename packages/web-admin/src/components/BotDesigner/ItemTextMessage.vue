<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div class="row">
    <label>テキスト</label>
    <q-input
      v-model="params.text"
      class="col-12"
      auto-grow
      outlined
      hide-details="auto"
      rows="5"
      :rules="[rules.validTextLength, rules.validTextChar]"
      @update:model-value="onChangeText"
      ref="textareaRef"
      id="action.0"
      type="textarea"
    ></q-input>
  </div>
</template>

<script setup lang="ts">
import { nextTick, onBeforeMount, onBeforeUnmount, ref, watch } from 'vue';

// props
const props = withDefaults(defineProps<{
  params: any,
  branchIndex?: number,
}>(),
{
  params: {
    text: '',
  }
}
);

// emit
const emit = defineEmits(['updateSaveStatus', 'updateParams']);

// watch
watch(
  () => props.branchIndex,
  (val) => {
    // TODO: propsにデータ入れる場合はemitを使用
    // props.branchIndex = val;
    scrollToAction(val);
  }
);

// data
const textareaRef = ref();
const rules = ref<any>({
  validTextLength: (value) => {
    if (value.length <= 0) {
      return "必須";
    }

    if (value.length > 5000) {
      return "テキストの最大長は5000文字です";
    }

    return true;
  },
  validTextChar: (value) => {
    for (var ch of value) {
      if (ch !== "\n" && ch !== " " && ch !== "　") {
        return true;
      }
    }
    return "空白または改行のみは保存出来ません";
  },
});

// hooks
onBeforeMount(() => {
  if (!Number.isInteger(props.branchIndex)) {
    return;
  }
  scrollToAction(props.branchIndex);
  validateText(props.params.text);
});

onBeforeUnmount(() => {
  emit("updateSaveStatus", { key: `ItemTextMessage`, value: true });
});

// methods
const scrollToAction = (branchIndex: any): void => {
  if (!Number.isInteger(branchIndex)) {
    return;
  }

  // const targetRef = this.$refs[`action.${branchIndex}`];
  if (textareaRef.value && textareaRef.value.length > 0) {
    nextTick(() =>
      textareaRef.value[0].$el.scrollIntoView({
        behavior: "smooth",
        block: "start",
        inline: "nearest",
      })
    );
  }
};

const onChangeText = (value: any): void => {
  validateText(value);
  emit("updateParams", { key: "text", value: value });
};

const validateText = (value: any): void => {
  if (value.length > 0 && value.length <= 5000) {
    for (var ch of value) {
      if (ch !== "\n" && ch !== " " && ch !== "　") {
        emit("updateSaveStatus", { key: `ItemTextMessage`, value: true });
        return;
      }
    }
  }
  emit("updateSaveStatus", { key: `ItemTextMessage`, value: false });
};
</script>