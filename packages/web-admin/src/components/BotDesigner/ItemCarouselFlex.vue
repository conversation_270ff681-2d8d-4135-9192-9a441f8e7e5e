<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <div class="row">
      <div v-if="!isPremadeMessage" class="col">
        <p class="tw-font-semibold text-center">
          カルセルフレックスメッセージ編集するメッセージを10っこまで選択します。選択数：{{ checkedMessages.length }}
        </p>
      </div>
      <div v-if="isPremadeMessage" class="col">
        <p class="tw-font-semibold text-center">カルセルフレックスメッセージ内容</p>
      </div>
    </div>
    <div v-if="!isPremadeMessage" class="row">
      <q-table
        v-model="checkedMessages"
        :columns="headers"
        :rows="filteredMessages"
        :items-per-page="5"
        show-select
        fixed-header
        :height="400"
        row-key="dataId"
      >
        <!-- <template v-slot:header.data-table-select="{ props, on }">
          <v-simple-checkbox
            :value="props.value"
            @update:model-value="on.input($event), reportValidation($event)"
          ></v-simple-checkbox>
        </template>
        <template v-slot:item.data-table-select="{ isSelected, select }">
          <v-simple-checkbox :value="isSelected" @update:model-value="reportValidation($event), select($event)"></v-simple-checkbox>
        </template>
        <template v-slot:item.dataType="{ item }">
          <div>{{ typeTitle(item.dataType) }}</div>
        </template> -->
      </q-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useScenariosStore } from "@/stores/modules/scenarios";
import { BOT_ITEM_TYPES } from "@/stores/modules/scenarios/scenarios.constants";
import { computed, onBeforeMount, onBeforeUnmount, onMounted, ref, watch } from "vue";

// interface LocalState {
//   checkedMessages: any;
//   isPremadeMessage: any;
//   headers: Array<any>;
// }

// store
const scenariosStore = useScenariosStore();

// props
const props = defineProps<{
  params: any,
}>();

// emit
const emit = defineEmits(['updateModelBubbles', 'updateSaveStatus']);

// watch
watch(
  () => checkedMessages.value,
  (newVal) => {
    let listOfMessagePostbackIds = [];
    newVal.forEach((message) => {
      listOfMessagePostbackIds.push(message.dataId);
    });
    emit("updateModelBubbles", listOfMessagePostbackIds);
  }
);

watch(
  () => props.params,
  () => {
    checkedMessages.value = alreadySelectedMessages.value;
    isPremadeMessage.value = isSpecialPremadeTalk();
  }
);

// data
const checkedMessages = ref();
const isPremadeMessage = ref();
const headers = ref([
  {
    label: "メッセージID",
    field: "メッセージID",
    sortable: false,
    name: "dataId",
    width: "30%",
  },
  {
    label: "名前",
    field: "名前",
    sortable: true,
    name: "nameLBD",
    width: "30%",
  },
  {
    label: "種別",
    field: "種別",
    sortable: true,
    name: "dataType",
    width: "30%",
  },
]);

// computed
const scenarioMessages = computed(() => scenariosStore.scenarioMessages);
const alreadySelectedMessages = computed((): Array<any> => {
  let result = [];
  if (props.params) {
    props.params.bubbleParam.forEach((messageId) => {
      let msg = !isSpecialPremadeTalk()
        ? filteredMessages.value.find((message) => {
            return message.dataId === messageId;
          })
        : scenarioMessages.value.find((message) => {
            return message.dataId === messageId;
          });
      if (msg) result.push(msg);
    });
  }
  return result;
});

const filteredMessages = computed((): any => {
  if (scenarioMessages.value) {
    return scenarioMessages.value.filter((message) => {
      return message.dataType == "bubbleFlex" && !("specialScenarioTalk" in message.params);
    });
  } else return [];
});

// methods
const isSpecialPremadeTalk = (): boolean => {
  return "params" in props.params && "specialScenarioTalk" in props.params.params;
};

const typeTitle = (value: any): any => {
  return BOT_ITEM_TYPES[value] ? BOT_ITEM_TYPES[value].text : "";
};

const runValidation = (): void => {
  setTimeout(() => reportValidation(), 100);
};

const reportValidation = (): void => {
  emit("updateSaveStatus", {
    key: `ItemCarouselFlex`,
    value: checkedMessages.value.length > 0 && checkedMessages.value.length < 11,
  });
};

// hooks
onBeforeMount(() => {
  checkedMessages.value = alreadySelectedMessages.value;
});

onMounted(() => {
  reportValidation();
  isPremadeMessage.value = isSpecialPremadeTalk();
});

onBeforeUnmount(() => {
  emit("updateSaveStatus", { key: `ItemCarouselFlex`, value: true });
});
</script>

<style scoped>
.v-data-footer {
  padding: inherit !important;
  padding-right: 1.5em !important;
  margin-right: 0px !important;
}
th:first-child {
  z-index: 100000001 !important;
}
th > tr {
  z-index: 100000000 !important;
}
.composite-message-edit-display {
  display: contents !important;
}
</style>