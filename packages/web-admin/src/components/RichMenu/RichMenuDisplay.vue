<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
.richmenu-content {
  padding-top: 0px;
}
.richmenu-subtitle {
  padding: 0 0 0 0;
}
.richmenu-subtitle-line1 {
  margin-bottom: 0px;
}
.richmenu-action-item {
  display: list-item;
  list-style-type: disc;
}
.rich-menu-image-display {
  width: inherit;
  height: inherit;
  padding: 0.5em;
  padding-left: 1em;
}
.activate-button-row {
  padding: 0.5em;
}
.activate-button {
  width: 80%;
}
.richmenu-image-column {
  margin-left: 1em !important;
  margin-right: 1em !important;
}
.richmenu-canvas-content {
    display:inline-block !important;
}
.original-image {
  position:absolute;
  z-index:1;
}
.richmenu-canvas{
  position:relative;
  z-index:2;
}
.richmenu-settings-link{
  color: var(--v-info-base) !important;
  text-decoration: underline !important;
}
</style>

<template>
  <div>
    <div v-if="richMenu && richMenu.richMenuId" :id="`richmenu-container-${richMenu.richMenuId}-${richMenuTitle}`" class="tw-p-4">
      <span v-if="richMenuTitle" class="text-h6 black--text">{{ richMenuTitle }}モード</span>
      <div class="richmenu-content">
        <div class="row">
          <div :id="`richmenu-image-column-${richMenu.richMenuId}-${richMenuTitle}`" class="richmenu-canvas-content richmenu-image-column col col-md-4">
            <img class="original-image" :src="richMenuImageUrl + richMenu.richMenuId" alt="" :width="renderedImageWidth" :height="renderedImageHeight"/>
            <canvas class="richmenu-canvas" :id="`richmenu-canvas-${richMenu.richMenuId}-${richMenuTitle}`" @click="openImageSource()"></canvas>
          </div>
          <div class="col col-md-6">
            <div class="row tw-p-4">
              <div class="col col-md-3"> タイトル </div>
              <div class="col">
                {{ richMenu.name }}
              </div>
            </div>
            <div class="row tw-p-4">
              <div class="col col-md-3"> アクション </div>
              <div class="col">
                <RichMenuAreas :areas="richMenu.areas" />
              </div>
            </div>
          </div>
          <div class="col col-md-1" v-if="displayActions">
            <q-btn
              class="activate-button"
              :color="'primary'"
              @click="activateClicked"
              :disable="(richMenuTitle == '通常' && !bosaiActive) || (richMenuTitle == '災害時' && bosaiActive)"
              :style="
                hasActionPermission('hideButton', 'Components_RichMenuDisplay_Activate')
                  ? hideButtonPermissionStyle()
                  : ''
              " 
            >
              適用
            </q-btn>
          </div>
        </div>
      </div>
      <ImageDisplayModal :visible="showImageDisplay" :imgSrc="richMenuImageUrl + richMenu.richMenuId" @close="showImageDisplay = false" />
    </div>
    <div v-else class="tw-p-3">
      <Alert
        type="info"
        color-bg="blue-1"
        color-text="blue"
      >
        {{ richMenuTitle }}モードのリッチメニューが設定されていません。
        <a class="richmenu-settings-link" href="/scenario-settings">シナリオ設定のリッチメニュー</a>から設定してください。
      </Alert>
    </div>
    <TalkSelectorModal :visible="showTalkSelector" :mode="richMenuTitle" :environment="environment" @activateTalk="activateTalk" @close="showTalkSelector = false"/>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { RICH_MENU_WIDTH, RICH_MENU_HEIGHT_LARGE, RICH_MENU_HEIGHT_SHORT } from "@/constants/scenario.constants";
import Alert from '../common/Alert.vue';
import RichMenuAreas from "@/components/RichMenu/RichMenuAreas.vue";
import ImageDisplayModal from "@/components/common/ImageDisplayModal.vue";
import TalkSelectorModal from "@/pages/admin/BosaiSettings/components/TalkSelectorModal.vue";
import { usePermissionHelper } from '@/mixins/PermissionHelper';

// emits 
const emits = defineEmits<{
  (event: 'activateClicked', payload: any): void;
}>();

// quasar
const $q = useQuasar();

// props
const props = withDefaults(defineProps<
  {
    richMenu: any,
    richMenuTitle?: string,
    titleColor: string,
    environment: string,
    bosaiMode: boolean,
    bosaiActive: boolean,
    displayActions?: boolean
  }>(),
  {
    richMenuTitle: '',
    displayActions: true,
  }
);

// data
const richMenuImageUrl = ref<string>("https://" + import.meta.env.VITE_SCENARIO_CLOUDFRONT_DOMAIN_NAME + "/resources/richmenus/");
const originalImageWidth = ref<number>(0);
const originalImageHeight = ref<number>(0);
const renderedImageWidth = ref<number>(0);
const renderedImageHeight = ref<number>(0);
const areaTagMargin = ref<number>(5);
const areaTagSize = ref<number>(15);
const showImageDisplay = ref<boolean>(false);
const showTalkSelector = ref<boolean>(false);

// methods
const { hasActionPermission, hideButtonPermissionStyle } = usePermissionHelper();

const openImageSource = (): void => {
  showImageDisplay.value = true;
};

const activateClicked = (): void => {
  if (props.bosaiMode) {
    showTalkSelector.value = true;
  } else {
    $q.dialog({
      title: "モード切り替え確認",
      message: props.richMenuTitle + "モードに切り替えてよろしいですか?",
      // type: "warning",
      ok: { label: 'はい' },
    }).onOk(async () => {
      emits("activateClicked", { environment: props.environment, activeSetting: props.bosaiMode });
    });
  }
};

const activateTalk = (talkId: any): void => {
  emits("activateClicked", { environment: props.environment, activeSetting: props.bosaiMode, selectedTalk: talkId.dataId });
};

const getRichMenuDisplayAreas = (): Array<any> => {
  const areas:any[] = [];
  if (!props.richMenu || !props.richMenu.areas || !Array.isArray(props.richMenu.areas)) {
    return areas;
  }
  let index = 1;
  let yAdjust = props.richMenu.size.height === RICH_MENU_HEIGHT_LARGE ? (renderedImageHeight.value / RICH_MENU_HEIGHT_LARGE) : (renderedImageHeight.value / RICH_MENU_HEIGHT_SHORT);
  let xAdjust  = (renderedImageWidth.value / RICH_MENU_WIDTH);
  for (const area of props.richMenu.areas) {
    areas.push({
      id: index,
      selected: true,
      resizable: false,
      editable: false,
      y: Math.ceil(yAdjust * area.bounds.y),
      height: Math.ceil(yAdjust * area.bounds.height),
      x: Math.ceil(xAdjust * area.bounds.x),
      width: Math.ceil(xAdjust * area.bounds.width),
      z: index,
    });
    index++;
  }
  return areas;
};

const runSetup = async (): Promise<void> => {
  var canvas: any = document.getElementById("richmenu-canvas-" + props.richMenu.richMenuId + "-" + props.richMenuTitle);
  var ctx = canvas.getContext("2d");

  var background = new Image();
  background.onload = function(){
    originalImageWidth.value = background.width;
    originalImageHeight.value = background.height;
    var elem = document.getElementById("richmenu-image-column-" + props.richMenu.richMenuId + "-" + props.richMenuTitle);
    renderedImageWidth.value = elem? elem.getBoundingClientRect().width : 200;

    const newToOriginalRatio = originalImageWidth.value !== 0 ? (renderedImageWidth.value / originalImageWidth.value) : 1;
    renderedImageHeight.value = originalImageHeight.value * newToOriginalRatio;

    canvas.width = renderedImageWidth.value;
    canvas.height = renderedImageHeight.value;

    const areasToDraw = getRichMenuDisplayAreas();

    for (const area of areasToDraw) {
      //Draw bounds
      ctx.beginPath();
      ctx.lineWidth = "2";
      ctx.strokeStyle = "#00b900";
      ctx.rect(
        area.x === 0 ? area.x + 2 : area.x, 
        area.y === 0 ? area.y + 2 : area.y, 
        area.x + area.width > canvas.width ? area.width - 3 : area.width, 
        area.y + area.height > canvas.height ? area.height - 3 : area.height,
      );
      ctx.stroke();
      //Draw id square
      ctx.fillStyle = '#00b900';
      ctx.fillRect(area.x + areaTagMargin.value, area.y + areaTagMargin.value, areaTagSize.value, areaTagSize.value);
      //Draw id tag
      ctx.font = '10pt sans-serif';
      ctx.fillStyle = 'white';
      ctx.fillText(area.id, area.x + areaTagMargin.value * 1.75, area.y + areaTagMargin.value * 3.5);
    }
  };
  background.src = richMenuImageUrl.value + props.richMenu.richMenuId;
};

// hooks
onMounted(() => {
  if (props.richMenu && props.richMenu.richMenuId) {
    runSetup();
  }
});
</script>
