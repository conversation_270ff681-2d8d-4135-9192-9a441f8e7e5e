<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<style scoped>
  .richmenu-action-item {
    display: flex !important;
  }
  .richmenu-action-list {
    list-style-type: none;
  }
  .richmenu-action-item-box {
    float: left !important;
  }
  .richmenu-action-item-content {
    display: inline-block !important;
    margin-right: 0.5em !important;
    vertical-align: top !important;
  }
  .list-index-item {
    display: inline !important;
    color: white;
    background-color: #00b900;
    padding-left: 0.25em;
    padding-right: 0.25em;
  }
  .remove-padding {
    padding-top: 0 !important;
    padding-left: 0 !important;
  }
  .richmenu-action-text {
    display: inline !important;
    word-break: break-all !important;
  }
</style>

<template>
  <q-page-container class="remove-padding">
    <ol class="richmenu-action-list remove-padding">
      <li class="richmenu-action-item row tw-py-1" v-for="(area, index) in areas" :key="index">
        <div class="richmenu-action-item-box col-1 col-md-1 col-sm-2 tw-mr-1">
          <span class="richmenu-action-item-content list-index-item">{{ index + 1 }}</span>
        </div>
        <div class="col">
          <div class="richmenu-action-item-content" v-html="getRichMenuActionDisplay(area)"></div>
          <span class="richmenu-action-item-content richmenu-action-text">{{ getRichMenuActionContents(area) }}</span>
        </div>
      </li>
    </ol>
  </q-page-container>
</template>
<script setup lang="ts">
import { SVG_ICON_CONSTANTS } from "@/stores/modules/scenarios/scenarios.constants";

// props
const props = defineProps<{
  areas: any[],
}>();

// methods
const getRichMenuActionDisplay = (area: any): any => {
  return SVG_ICON_CONSTANTS[area.action.type];
};

const getRichMenuActionContents = (area: any): any => {
  switch(area.action.type) {
    case 'message':
      return area.action.text;
    case 'uri':
      return area.action.uri;
    case 'postback':
      return area.action.data;
    case 'richmenuswitch':
      return area.action.richMenuName;
    default:
      return '不明'
  }
};
</script>
