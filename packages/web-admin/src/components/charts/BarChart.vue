<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <Chart :data="chartData" :options="chartOptions"></Chart>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue';
import Chart from './BarChartBase.vue';
import { isEmpty } from 'lodash';

// props
const props = defineProps<{
  dataSet: any,
  labels: any[],
}>();

// data
// TODO 値表示されない時用に作成 本来なら空の値が入るはず - honda
const data = {
  datasets: [
    {
      label: "データ",
      backgroundColor: "#BBEEBB",
    },
  ],
}

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  aspectRatio: 1, // Adjust the aspect ratio to make the chart taller
  scales: {
    x: {
      stacked: true,
      display: true,
      ticks: {
        callback: (value: any, index: number) => {
          const label = props.labels[index];
          if (label.length > 10) {
            return label.substr(0, 10) + "...";
          } else {
            return label;
          }
        },
        autoSkip: false, // Ensure all labels are shown
      },
    },
    y: {
      stacked: true,
      beginAtZero: true,
    },
  },
  plugins:{
    legend: {
      position: "bottom",
    }
  },
};

const chartData = computed(() => {
  return {
    labels: props.labels,
    datasets: !isEmpty(props.dataSet) ? props.dataSet.datasets : data.datasets,
  };
});

onMounted(() => {
  //console.log('data -> ' + props.dataSet.datasets);
  //console.log('label -> ' + props.labels);
})
</script>
