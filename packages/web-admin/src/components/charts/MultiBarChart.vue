<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <BarChartBase :data="dataSet.length ? dataSet : data" :options="options" />
</template>

<script setup lang="ts">
import { ref  } from 'vue';
import BarChartBase from './BarChartBase.vue';

// emits 
const emits = defineEmits<{
  (event: 'onBarClick', payload: any): void;
}>();

// props
const props = defineProps<{
  dataSet: any,
  labels: any[],
}>();

// data
// TODO 値表示されない時用に作成 本来なら空の値があるはず - honda
const data = {
  datasets: [
    {
      label: "LIFF別起動数",
      backgroundColor: "#BBEEBB",
    },
  ],
}

const options = ref<any>({
  onClick: (event: any, activeElements: any) => { handleClick(event, activeElements) },
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    x: {
      stacked: true,
      display: false,
      ticks: {
        callback: (value: string) => {
          if (value.length > 10) {
            return value.substring(0, 10) + "...";
          } else {
            return value;
          }
        },
      },
    },
    y: {
      stacked: true,
      beginAtZero: true,
    }
  },
  plugins: {
    legend: {
      position: "bottom",
    },
  },
});

// methods
const handleClick = (event: any, array: any): void => {
  if (array[0]) emits("onBarClick", array[0]._index);
};
</script>
