<template>
  <div>
    <Pie :data="data" :options="options"/>
  </div>
</template>

<script setup lang="ts">
// @types/chart.jsの型付けを使用するためにimportしてます。
import type { ChartData, ChartOptions } from 'chart.js';

// 今回はRadar-chartを作成するので、import。
// 他にも{ Bar }など、種類があります。
import { Pie } from 'vue-chartjs'

import { Chart as ChartJS, Tooltip, Legend, ArcElement } from "chart.js";
ChartJS.register(<PERSON>lt<PERSON>, Legend, ArcElement);

defineProps<{
  data: any,
  options: any,
}>();
</script>