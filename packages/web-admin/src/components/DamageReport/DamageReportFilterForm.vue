<template>
  <q-card flat bordered class="tw-rounded-lg tw-mt-2 tw-p-4">
    <div class="row tw--mx-2 tw-gap-y-4">
      <div class="col-12 col-lg-6 col tw-px-2">
        <div class="tw-font-bold">{{ t('form.field.User') }}</div>
        <q-input lazy-rules outlined dense v-model="filterFormData.userName" />
      </div>
      <div class="col-12 col-lg-3 col tw-px-2">
        <div class="tw-font-bold">{{ t('form.field.Status') }}</div>
        <q-select
          outlined
          dense
          clearable
          emit-value
          map-options
          v-model="filterFormData.status"
          :options="statusOptions"
        />
      </div>
      <div class="col-12 col-lg-3 col tw-px-2">
        <div class="tw-font-bold">{{ t('form.field.Category') }}</div>
        <q-input outlined dense clearable v-model="filterFormData.category" />
      </div>
      <div class="col-12 col-lg-3 col tw-px-2">
        <div class="tw-font-bold">{{ t('form.field.Subject') }}</div>
        <q-input outlined dense clearable v-model="filterFormData.subject" />
      </div>
      <div class="col-12 col-lg-3 col tw-px-2">
        <div class="tw-font-bold">{{ t('form.field.Scenario') }}</div>
        <q-select
          outlined
          dense
          clearable
          emit-value
          option-value="value"
          option-label="label"
          map-options
          :loading="loadings.scenarioOptionsLoading"
          v-model="filterFormData.scenarioId"
          :options="scenarioOptions"
        >
          <template v-slot:option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section>
                <q-item-label>{{ scope.opt.label }}</q-item-label>
                <q-item-label caption>{{
                  scope.opt.production
                    ? t('select.scenario.production.true')
                    : ''
                }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
        </q-select>
      </div>
      <div class="col-12 col-lg-6 col tw-px-2">
        <div class="tw-font-bold">{{ t('form.field.ModifiedAt') }}</div>
        <div class="tw-flex tw-gap-1">
          <q-input
            class="tw-flex-grow"
            dense
            mask="date"
            placeholder="YYYY/MM/DD"
            hide-bottom-space
            outlined
            v-model="filterFormData.modifiedAtFrom"
            @change="changeDate('modifiedAtFrom')"
          >
            <template v-slot:prepend>
              <q-icon name="event" class="cursor-pointer">
                <q-popup-proxy
                  cover
                  transition-show="scale"
                  transition-hide="scale"
                >
                  <q-date
                    v-model="filterFormData.modifiedAtFrom"
                    minimal
                    :options="optionsDateModifiedAtFrom"
                  >
                    <div class="row items-center justify-end">
                      <q-btn v-close-popup label="Close" color="primary" flat />
                    </div>
                  </q-date>
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>
          <div class="tw-w-2 tw-flex tw-items-center"><span>~</span></div>
          <q-input
            class="tw-flex-grow"
            dense
            mask="date"
            placeholder="YYYY/MM/DD"
            hide-bottom-space
            outlined
            v-model="filterFormData.modifiedAtTo"
            @change="changeDate('modifiedAtTo')"
          >
            <template v-slot:prepend>
              <q-icon name="event" class="cursor-pointer">
                <q-popup-proxy
                  cover
                  transition-show="scale"
                  transition-hide="scale"
                >
                  <q-date
                    v-model="filterFormData.modifiedAtTo"
                    :options="optionsDateModifiedAtTo"
                    minimal
                  >
                    <div class="row items-center justify-end">
                      <q-btn v-close-popup label="Close" color="primary" flat />
                    </div>
                  </q-date>
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>
        </div>
      </div>
      <div class="col-12 tw-px-2 justify-end row tw-gap-x-4">
        <q-btn outline class="ml-auto" color="negative" @click="clearFilter">
          <div style="line-height: normal">{{ t('btn.Filter.ClearAll') }}</div>
        </q-btn>
        <q-btn color="primary" @click="submitFilter">
          <div style="line-height: normal">
            {{ t('btn.Filter.SearchWithFilter') }}
          </div>
        </q-btn>
      </div>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { useDamageReportStore } from '../../stores/modules/damage-report'
import { storeToRefs } from 'pinia'

import { useI18n } from 'vue-i18n'
import { onBeforeMount, ref, watch } from 'vue'
const { t, locale } = useI18n()

const reportStore = useDamageReportStore()
const { filterFormData, loadings } = storeToRefs(reportStore)

const emit = defineEmits(['submitFilter'])

onBeforeMount(() => {
  reportStore.buildOptionsFromScenario()
})
// #region form options data
const { scenarioOptions, statusOptions } = storeToRefs(reportStore)

function submitFilter() {
  emit('submitFilter', filterFormData.value)
}
function clearFilter() {
  filterFormData.value = {}
}
function optionsDateModifiedAtFrom(date) {
  if (filterFormData.value.modifiedAtTo) {
    return date <= filterFormData.value.modifiedAtTo
  }
  return date <= dayjs().format('YYYY/MM/DD')
}
function optionsDateModifiedAtTo(date) {
  if (filterFormData.value.modifiedAtFrom) {
    return (
      date >= filterFormData.value.modifiedAtFrom &&
      date <= dayjs().format('YYYY/MM/DD')
    )
  } else {
    return date <= dayjs().format('YYYY/MM/DD')
  }
}
function changeDate(date) {
  if (filterFormData.value[date] === '') {
    return
  }

  const mapValidateFunc = {
    modifiedAtFrom: optionsDateModifiedAtFrom,
    modifiedAtTo: optionsDateModifiedAtTo,
  }
  let dateobj = dayjs(filterFormData.value[date], 'YYYY/MM/DD', true)
  if (
    !dateobj.isValid() ||
    !mapValidateFunc[date](filterFormData.value[date])
  ) {
    if (date === 'modifiedAtFrom') {
      if (
        filterFormData.value['modifiedAtTo'] &&
        filterFormData.value['modifiedAtFrom'] >
          filterFormData.value['modifiedAtTo']
      ) {
        filterFormData.value['modifiedAtFrom'] =
          filterFormData.value['modifiedAtTo']
      } else {
        filterFormData.value['modifiedAtFrom'] = dayjs().format('YYYY/MM/DD')
      }
    } else if (date === 'modifiedAtTo') {
      if (
        filterFormData.value['modifiedAtFrom'] &&
        filterFormData.value['modifiedAtFrom'] >
          filterFormData.value['modifiedAtTo']
      ) {
        filterFormData.value['modifiedAtTo'] =
          filterFormData.value['modifiedAtFrom']
      } else {
        filterFormData.value['modifiedAtTo'] = dayjs().format('YYYY/MM/DD')
      }
    }
  }
}
</script>
