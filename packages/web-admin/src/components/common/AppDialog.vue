<template>
  <!-- <v-snackbar v-model="visible" :timeout="timeout">
    <v-icon left color="yellow accent-4" v-if="type === 'error'">mdi-alert-outline</v-icon>
    {{ text }}
    <v-btn color="white" text @click="visible = false">閉じる</v-btn>
  </v-snackbar> -->
  <q-dialog v-model="visible" content-class="q-pa-md" persistent>
    <q-card>
      <q-card-section>
        <q-system-bar :color="dialogColor" dark height="10"></q-system-bar>
        <q-list>
          <q-item>
            <q-item-section avatar>
              <q-icon v-if="type === 'info'" size="2rem" :color="dialogColor" name="mdi-alert-circle-outline"></q-icon>
              <q-icon v-if="type === 'warning'" size="2rem" :color="dialogColor" name="mdi-alert-outline"></q-icon>
              <q-icon v-if="type === 'confirm'" size="2rem" :color="dialogColor" name="mdi-help-circle-outline"></q-icon>
              <q-icon v-if="type === 'error'" size="2rem" :color="dialogColor" name="mdi-alert-outline"></q-icon>
            </q-item-section>

            <q-item-section>
              <q-item-label>{{ title }}</q-item-label>
              <div class="mt-2 text-body-2 break-line text--disabled">
                {{ text }}
              </div>
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>

      <q-separator></q-separator>

      <q-card-actions v-if="!hideBtn">
        <q-space></q-space>
        <q-btn v-if="!hideBtnConfirm" color="primary" dark @click="confirm">
          {{ btnConfirmTitle }}
        </q-btn>
        <q-btn v-if="!hideBtnCancel" color="grey-7" outlined @click="hide">
          {{ btnCancelTitle }}
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, ref } from "vue";
import eventBus from "@utils/eventBus";

const DIALOG_COLOR = {
  info: "primary",
  warning: "warning",
  confirm: "primary",
  error: "error"
};

interface LocalState {
  visible: boolean;
  title: string;
  text: string;
  type: string;
  btnConfirmTitle: string;
  btnCancelTitle: string;
  hideBtn: boolean;
  hideBtnConfirm: boolean;
  hideBtnCancel: boolean;
}

// data 
const visible = ref(false);
const title = ref("");
const text = ref("");
const type = ref<keyof typeof DIALOG_COLOR>("info");
const btnConfirmTitle = ref("OK");
const btnCancelTitle = ref("キャンセル");
const hideBtn = ref(false);
const hideBtnConfirm = ref(false);
const hideBtnCancel = ref(false);

// mounted
onBeforeMount(() => {
  // here we need to listen for emited events
  // we declared those events inside our plugin
  eventBus.on("show", (params: any) => {
    show(params);
  });
});

// computed
const dialogColor = computed((): any => {
  return DIALOG_COLOR[type.value] || DIALOG_COLOR.info;
});

// methods
const hide = (): void => {
  visible.value = false;
};

const confirm = (): void => {
  // TODO onConfirm()不明なため一旦コメント
  // we must check if this.onConfirm is function
  // if (typeof onConfirm === "function") {
  //   // run passed function and then close the modal
  //   onConfirm();
  //   hide();
  // } else {
  //   // we only close the modal
  //   hide();
  // }

  hide();
};

const show = (params: any): void => {
  // making modal visible
  visible.value = true;
  text.value = params.text;
  type.value = params.type || "info";
  title.value = params.title;
  hideBtn.value = params.hideBtn;
  hideBtnConfirm.value = params.hideBtnConfirm;
  hideBtnCancel.value = params.hideBtnCancel;
  (btnConfirmTitle.value = params.btnConfirmTitle || "OK");
  (btnCancelTitle.value = params.btnCancelTitle || "キャンセル");
  // TODO
  // (this.onConfirm = params.onConfirm);
};
</script>

<style lang="less">
.dialog-text {
  white-space: pre !important;
  display: block;
  line-height: 1.5 !important;
}
.break-line {
  white-space: pre-line !important;
}
</style>
