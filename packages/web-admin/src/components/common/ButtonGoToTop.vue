<template>
  <div class="tw-relative row justify-end">
    <q-btn 
      class="tw-fixed tw-bottom-2 tw-right-2"
      round
      color="primary"
      size="lg"
      icon="mdi-arrow-collapse-up" 
      @click="scrollToTop"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref } from "vue";
  import { scroll } from 'quasar'

  // data
  const scrollTopOptions = ref({
    duration: 777,
    easing: "easeInOutCubic",
  });

  // methods
  const scrollToTop = () => {
    const scrollTarget = document.documentElement || document.body;
    scroll.setVerticalScrollPosition(scrollTarget, 0, scrollTopOptions.value.duration);
  }         
</script>

<style></style>
