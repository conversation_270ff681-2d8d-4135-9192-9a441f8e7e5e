<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <v-layout :id="ref" v-resize.quiet="onResize" class="wrap">
    <!-- FORM-BASE TOP SLOT -->
    <slot :name="getFormTopSlot()" />

    <template v-for="(obj, index) in flatCombinedArraySorted">
      <!-- Tooltip Wrapper -->
      <q-tooltip :key="index" :disabled="!obj.schema.tooltip" v-bind="getShorthandTooltip(obj.schema.tooltip)">
        <template v-slot:activator="{ on }">
          <v-flex
            v-show="!obj.schema.hidden"
            :key="index"
            v-intersect="(entries, observer) => onIntersect(entries, observer, obj)"
            v-touch="{
              left: () => onSwipe('left', obj),
              right: () => onSwipe('right', obj),
              up: () => onSwipe('up', obj),
              down: () => onSwipe('down', obj),
            }"
            :class="getClassName(obj)"
            @mouseenter="onEvent($event, obj)"
            @mouseleave="onEvent($event, obj)"
            v-on="on"
          >
            <!-- slot on top of type  -> <div slot="slot-bottom-type-[propertyName]"> -->
            <slot :name="getTypeTopSlot(obj)" />
            <!-- slot on top of key  -> <q-btn slot="slot-bottom-key-[propertyName]"> -->
            <slot :name="getKeyTopSlot(obj)" />
            <!-- slot replaces complete item of defined TYPE -> <q-btn slot="slot-item-type-[propertyName]">-->
            <slot :name="getTypeItemSlot(obj)">
              <!-- slot replaces complete item of defined KEY -> <div slot="slot-item-key-[propertyName]">-->
              <slot :name="getKeyItemSlot(obj)">
                <!-- RADIO -->
                <q-radio
                  v-if="obj.schema.type === 'radio'"
                  v-bind="bindSchema(obj)"
                  :value="setValue(obj)"
                  @change="onInput($event, obj)"
                >
                  <q-radio
                    v-for="(o, idx) in obj.schema.options"
                    :key="idx"
                    v-bind="bindSchema(obj)"
                    :label="sanitizeOptions(o).label"
                    :value="sanitizeOptions(o).value"
                  />
                </q-radio>
                <!-- END RADIO -->

                <!-- DATE, TIME, COLOR MENU -->
                <q-menu
                  v-else-if="isDateTimeColorExtension(obj)"
                  :close-on-content-click="true"
                  transition="scale-transition"
                  offset-y
                  :nudge-right="32"
                  max-width="290px"
                  min-width="290px"
                >
                  <template v-slot:activator="{ on }">
                    <q-input v-on="on" v-bind="bindSchema(obj)" type="text" readonly :value="setValue(obj)">
                      <!-- @input="onInput($event, obj)" -->
                    </q-input>
                  </template>
                  <div
                    :is="mapTypeToComponent(obj.schema.ext)"
                    v-bind="bindSchema(obj)"
                    :type="checkExtensionType(obj)"
                    :value="setValue(obj)"
                    @input="onInput($event, obj)"
                  ></div>
                </q-menu>
                <!-- END DATE, TIME, COLOR MENU -->

                <!-- ARRAY -->
                <template v-else-if="obj.schema.type === 'array'">
                  <div
                    v-for="(item, idx) in setValue(obj)"
                    :key="getKeyForArray(obj, item, idx)"
                    v-bind="bindSchema(obj)"
                    :value="setValue(obj)"
                  >
                    <slot :name="getKeyArraySlot(obj)" :item="item">
                      <v-form-base
                        :id="`${id}-${obj.key}-${idx}`"
                        :value="item"
                        :schema="obj.schema.schema"
                        :class="`${id}-${obj.key}`"
                      />
                    </slot>
                  </div>
                </template>
                <!-- END ARRAY -->

                <!-- CARD -->
                <q-card v-else-if="obj.schema.type === 'card'" v-bind="bindSchema(obj)">
                  <v-card-title v-if="obj.schema.title">{{ obj.schema.title }}</v-card-title>
                  <v-card-subtitle v-if="obj.schema.subtitle">{{ obj.schema.subtitle }}</v-card-subtitle>
                  <v-form-base :value="setValue(obj)" :schema="obj.schema.schema" />
                </q-card>
                <!-- END CARD -->

                <!-- TREEVIEW -->
                <v-treeview
                  v-else-if="obj.schema.type === treeview"
                  v-model="obj.schema.model"
                  :items="setValue(obj)"
                  :active.sync="obj.schema.model"
                  :open.sync="obj.schema.open"
                  v-bind="bindSchema(obj)"
                  @update:open="onEvent({ type: 'click' }, obj, 'open')"
                  @update:active="onEvent({ type: 'click' }, obj, 'selected')"
                />
                <!-- END TREEVIEW -->

                <!-- LIST -->
                <template v-else-if="obj.schema.type === list">
                  <q-toolbar v-if="obj.schema.label" v-bind="bindSchema(obj)" dark>
                    <q-toolbar-title>{{ obj.schema.label }}</q-toolbar-title>
                  </q-toolbar>
                  <q-list>
                    <v-list-item-group v-model="obj.schema.model" v-bind="bindSchema(obj)" light>
                      <v-list-item v-for="(item, idx) in setValue(obj)" :key="idx" @click="onEvent($event, obj, list)">
                        <v-list-item-icon>
                          <v-icon>{{ obj.schema.icon }}</v-icon>
                        </v-list-item-icon>
                        <v-list-item-content>
                          <v-list-item-title>{{ obj.schema.item ? item[obj.schema.item] : item }}</v-list-item-title>
                        </v-list-item-content>
                      </v-list-item>
                    </v-list-item-group>
                  </q-list>
                </template>
                <!-- END LIST -->

                <!-- CHECKBOX | SWITCH -->
                <div
                  :is="mapTypeToComponent(obj.schema.type)"
                  v-else-if="obj.schema.type === 'switch' || obj.schema.type === 'checkbox'"
                  :input-value="setValue(obj)"
                  v-bind="bindSchema(obj)"
                  @change="onInput($event, obj)"
                />
                <!-- END CHECKBOX | SWITCH -->

                <!-- FILE -->
                <q-uploader
                  v-else-if="obj.schema.type === 'file'"
                  :value="setValue(obj)"
                  v-bind="bindSchema(obj)"
                  @focus="onEvent($event, obj)"
                  @blur="onEvent($event, obj)"
                  @change="onInput($event, obj)"
                />
                <!-- END FILE -->

                <!-- ICON -->
                <q-icon
                  v-else-if="obj.schema.type === 'icon'"
                  v-bind="bindSchema(obj)"
                  @click="onEvent($event, obj)"
                >{{ getIconValue(obj) }}</q-icon>
                <!-- END ICON -->

                <!-- IMG -->
                <q-img
                  v-else-if="obj.schema.type === 'img'"
                  :src="getImageSource(obj)"
                  v-bind="bindSchema(obj)"
                  @click="onEvent($event, obj)"
                />
                <!-- END IMG -->

                <!-- BTN-TOGGLE -->
                <q-btn-group
                  v-else-if="obj.schema.type === 'btn-toggle'"
                  v-bind="bindSchema(obj)"
                  color=""
                  :value="setValue(obj)"
                  @change="onInput($event, obj)"
                >
                  <q-btn
                    v-for="(b, idx) in obj.schema.options"
                    :key="idx"
                    v-bind="bindSchema(obj)"
                    :value="sanitizeOptions(b).value"
                  >
                    <q-icon :dark="obj.schema.dark">
                      {{ sanitizeOptions(b).icon }}
                    </q-icon>
                    {{ sanitizeOptions(b).label }}
                  </q-btn>
                </q-btn-group>
                <!-- END BTN-TOGGLE -->

                <!-- BTN -->
                <q-btn
                  v-else-if="obj.schema.type === 'btn'"
                  v-bind="bindSchema(obj)"
                  @click="onEvent($event, obj, button)"
                >
                  <q-icon v-if="obj.schema.iconLeft" left :dark="obj.schema.dark">
                    {{ obj.schema.iconLeft }}
                  </q-icon>
                  {{ setValue(obj) }}
                  <q-icon v-if="obj.schema.iconCenter" :dark="obj.schema.dark">
                    {{ obj.schema.iconCenter }}
                  </q-icon>
                  {{ obj.schema.label }}
                  <q-icon v-if="obj.schema.iconRight" right :dark="obj.schema.dark">
                    {{ obj.schema.iconRight }}
                  </q-icon>
                </q-btn>
                <!-- END BTN -->

                <!-- MASK v-text-field use this Section - https://vuejs-tips.github.io/vue-the-mask/  -->
                <q-input
                  v-else-if="obj.schema.mask"
                  v-mask="obj.schema.mask"
                  v-bind="bindSchema(obj)"
                  :value="setValue(obj)"
                  @focus="onEvent($event, obj)"
                  @blur="onEvent($event, obj)"
                  @click:append="onEvent($event, obj, append)"
                  @click:append-outer="onEvent($event, obj, appendOuter)"
                  @click:clear="onEvent($event, obj, clear)"
                  @click:prepend="onEvent($event, obj, prepend)"
                  @click:prepend-inner="onEvent($event, obj, prependInner)"
                  @input="onInput($event, obj)"
                />
                <!-- END MASK -->

                <!-- DEFAULT all other Types -> typeToComponent -->
                <div
                  :is="mapTypeToComponent(obj.schema.type)"
                  v-else
                  v-bind="bindSchema(obj)"
                  :type="checkExtensionType(obj)"
                  :value="setValue(obj)"
                  @focus="onEvent($event, obj)"
                  @blur="onEvent($event, obj)"
                  @click:append="onEvent($event, obj, append)"
                  @click:append-outer="onEvent($event, obj, appendOuter)"
                  @click:clear="onEvent($event, obj, clear)"
                  @click:prepend="onEvent($event, obj, prepend)"
                  @click:prepend-inner="onEvent($event, obj, prependInner)"
                  @input="onInput($event, obj)"
                />
                <!-- END DEFAULT -->
              </slot>
            </slot>

            <!-- slot at bottom of item  -> <div slot="slot-bottom-key-[deep-nested-key-name]"> -->
            <slot :name="getTypeBottomSlot(obj)" />
            <slot :name="getKeyBottomSlot(obj)" />
          </v-flex>

          <!-- schema.spacer:true - push next item to the right and fill space between items -->
          <q-space v-if="obj.schema.spacer" :key="`s-${index}`" />
        </template>
        <!-- slot for TOOLTIP - inspect css.vue for details -->
        <slot name="slot-tooltip" :obj="obj">
          <span>{{ getShorthandTooltipLabel(obj.schema.tooltip) }}</span>
        </slot>
      </q-tooltip>
    </template>
    <!-- FORM-BASE BOTTOM SLOT -->
    <slot :name="getFormBottomSlot()" />
  </v-layout>
</template>

<script setup lang="ts">
import { PropType,  computed, onBeforeMount } from 'vue';



// old imports
// 旧インポート
/*import { get, isPlainObject, isFunction, isString, isEmpty, orderBy, delay } from "lodash";
import { mask } from "vue-the-mask";*/


// props
const props = defineProps({
  id: {
    type: String as PropType<string>,
    default: defaultID as PropType<defaultid>
  },
  flex: {
    type: [Object, Number, String],
    default: () => flexDefault
  },
  value: {
    type: [Object, Array],
    default: () => null
  },
  model: {
    //  use value or model für presenting data
    type: [Object, Array],
    default: () => null
  },
  schema: {
    type: [Object, Array],
    default: () => ({})
  }
});

// methods
const mapTypeToComponent = (type: string|number): any => {
      // map ie. schema:{ type:'password', ... } to specific vuetify-control or default to v-text-field'
      return typeToComponent[type] ? typeToComponent[type] : `v-${type}`;
    };
const isDateTimeColorExtension = (obj: { schema: { ext: string; }; }): boolean => {
      return "date_time_color".includes(obj.schema.ext);
    };
const bindSchema = (obj: { schema: any; }): any => {
      return obj.schema;
    };
const checkExtensionType = (obj: { schema: { ext: any; type: any; }; }): any => {
      // For native <INPUT> type use ext
      // { type:'text, ext:'date', ...} -> use native Input Type 'Date' instead of Date-Picker
      return obj.schema.ext || obj.schema.type;
    };
const getKeyForArray = (obj: { schema: { key: any; }; }, item: { [x: string]: any; }, index: number): any => {
      // IMPORTANT if you want to add or remove items in type:'array'
      // more Info ->
      // https://forum.vuejs.org/t/after-splicing-an-object-wrong-item-disappears-from-view/9247/4
      // https://stackoverflow.com/questions/45655090/vue-array-splice-removing-wrong-item-from-list

      // create for iteration v-for an uniqe key from each object in array using index.value and time.hash
      // or define your key index.value by defining a key property
      // MODEL
      // arrayTasks: { trace:'100', label:'A', ... }
      // SCHEMA
      // arrayTasks: { type:'array', schema:{ ... } }                           -> KEY index_time.hash  0_1587498241149
      // arrayTasks: { type:'array', key:'trace', schema:{ ... } }              -> KEY trace            100
      // arrayTasks: { type:'array', key:['trace','label'], schema:{ ... } }    -> KEY trace_label      100_A

      // IMPORTANT! Key should not contain an editable prop, because of new iteration on any change

      const k = obj.schema.key;
      return k
        ? Array.isArray(k)
          ? k.map((i) => item[i]).join("_")
          : item[k]
        : !isNaN(index.value)
        ? `${index.value}_${Date.now()}`
        : index.value;
    };
const getImageSource = (obj: { schema: { src: any; base: any; tail: any; }; value: any; }): any => {
      // if exist get source from src otherwise join schema.base & value & schema.tail
      return obj.schema.src ? obj.schema.src : `${obj.schema.base}${obj.value}${obj.schema.tail}`;
    };
const getIconValue = (obj: { schema: { label: any; }; }): any => {
      // icon: try schema.label or if undefined use value
      return obj.schema.label ? obj.schema.label : setValue(obj);
    };
const getShorthandTooltip = (schemaTooltip: any): any => {
      // check if tooltip is typeof string ->  shorthand { bottom:true, label: obj.schema.tooltip} otherwise take original object
      return isString(schemaTooltip) ? { bottom: true, label: schemaTooltip } : schemaTooltip;
    };
const getShorthandTooltipLabel = (schemaTooltip: { label: any; }): any => {
      // check if tooltip is typeof string ->  return Label
      return isString(schemaTooltip) ? schemaTooltip : schemaTooltip && schemaTooltip.label;
    };
const getFormTopSlot = (): string => {
      return id + "-top";
    };
const getFormBottomSlot = (): string => {
      return id + "-bottom";
    };
const getKeyArraySlot = (obj: any): any => {
      // get Key specific name by replacing '.' with '-' and prepending 'slot-item'  -> 'slot-ARRAY-key-address-city'
      return getKeyClassNameWithAppendix(obj, arraySlotAppendix + "-key");
    };
const getKeyItemSlot = (obj: any): any => {
      // get Key specific name by replacing '.' with '-' and prepending 'slot-item'  -> 'slot-item-key-address-city'
      return getKeyClassNameWithAppendix(obj, itemSlotAppendix + "-key");
    };
const getKeyTopSlot = (obj: any): any => {
      // get Key specific name by replacing '.' with '-' and prepending 'slot-top'  -> 'slot-top-key-address-city'
      return getKeyClassNameWithAppendix(obj, topSlotAppendix + "-key");
    };
const getKeyBottomSlot = (obj: any): any => {
      // get Key specific name by replacing '.' with '-' and prepending 'slot-bottom'  -> 'slot-bottom-key-address-city'
      return getKeyClassNameWithAppendix(obj, bottomSlotAppendix + "-key");
    };
const getTypeItemSlot = (obj: any): any => {
      // get Type specific slot name  -> 'slot-item-type-radio'
      return getTypeClassNameWithAppendix(obj, itemSlotAppendix + "-type");
    };
const getTypeTopSlot = (obj: any): any => {
      // get Type specific slot name  -> 'slot-top-type-radio'
      return getTypeClassNameWithAppendix(obj, topSlotAppendix + "-type");
    };
const getTypeBottomSlot = (obj: any): any => {
      // get Type specific slot name  -> 'slot-bottom-type-radio'
      return getTypeClassNameWithAppendix(obj, bottomSlotAppendix + "-type");
    };
const getPropertyClassNameWithAppendix = (obj: { key: string; }, appendix: string): string => {
      // get PROP specific name by app-/prepending 'appendix-' and replacing '.' with '-' in nested key path  -> 'controls switch'
      return obj.key
        ? obj.key
            .split(pathDelimiter)
            .map((s: any) => `${appendix ? appendix + classKeyDelimiter : ""}${s}`)
            .join(" ")
        : "";
    };
const getPropertyClassName = (obj: any): any => {
      return getPropertyClassNameWithAppendix(obj, propertyClassAppendix);
    };
const getKeyClassNameWithAppendix = (obj: { key: string; }, appendix: string): string => {
      // get KEY specific name by app-/prepending 'appendix-' and replacing '.' with '-' in nested key path  -> 'top-slot-address-city'
      return `${appendix ? appendix + classKeyDelimiter : ""}${obj.key.replace(/\./g, "-")}`;
    };
const getKeyClassName = (obj: any): any => {
      return getKeyClassNameWithAppendix(obj, keyClassAppendix);
    };
const getTypeClassNameWithAppendix = (obj: { schema: { type: any; }; }, appendix: string): string => {
      // get TYPE specific class name by prepending '-type' -> 'type-checkbox'
      return `${appendix + classKeyDelimiter}${obj.schema.type}`;
    };
const getTypeClassName = (obj: any): any => {
      return getTypeClassNameWithAppendix(obj, typeClassAppendix);
    };
const getFlexGridClassName = (obj: { schema: { flex: any; }; }): string => {
      const keysToGridClassName = (key: { [x: string]: string; }) =>
        Object.keys(key)
          .map((k) => k + key[k])
          .join(" "); //  { xs:12, md:6, lg:4  } => 'xs12 md6 lg4'
      // schema.flex overrules property flex!
      // get FLEX class from schema.flex ->  schema:{ flex:{ xs:12, md:4  } || flex: 4 } // flex: 4 -> is shorthand for -> flex:{ xs:4 }
      if (obj.schema.flex)
        return isPlainObject(obj.schema.flex) ? keysToGridClassName(obj.schema.flex) : `xs${obj.schema.flex}`;
      // take property flex: <v-form-base :flex="{xs:12,sm:6}" .... />  or  <v-form-base flex="6" .... />
      return isPlainObject(flex) ? keysToGridClassName(flex) : `xs${flex}`;
    };
const getOffsetGridClassName = (obj: { schema: { offset: any; }; }): string => {
      // get OFFSET-FLEX class from schema.offset ->  schema:{ offset:{ xs:12, md:4  } || offset: 4 } // offset: 4 -> is shorthand for -> offset:{ xs:4 }
      const keysToOffsetClassName = (key: { [x: string]: any; }) =>
        Object.keys(key)
          .map((k) => `offset-${k}${key[k]}`)
          .join(" "); //  { xs:12, md:6, lg:4  } => 'xs12 md6 lg4'
      return obj.schema.offset
        ? isPlainObject(obj.schema.offset)
          ? keysToOffsetClassName(obj.schema.offset)
          : `offset-xs${obj.schema.offset}`
        : "";
    };
const getOrderGridClassName = (obj: { schema: { order: any; }; }): string => {
      // get ORDER-FLEX class from schema.order ->  schema:{ order:{ xs:12, md:4  } || order: 4 } // order: 4 -> is shorthand for -> order:{ xs:4 }
      const keysToOrderClassName = (key: { [x: string]: any; }) =>
        Object.keys(key)
          .map((k) => `order-${k}${key[k]}`)
          .join(" "); //  { xs:12, md:6, lg:4  } => 'xs12 md6 lg4'
      return obj.schema.order
        ? isPlainObject(obj.schema.order)
          ? keysToOrderClassName(obj.schema.order)
          : `order-xs${obj.schema.order}`
        : "";
    };
const getGridClassName = (obj: any): string => {
      // combine Flex, Offset, Order into a classname
      return `${getFlexGridClassName(obj)} ${getOffsetGridClassName(obj)} ${getOrderGridClassName(obj)}`;
    };
const getClassName = (obj: any): string => {
      // combines all into a single classname
      // class => ie. 'item type-checkbox key-address-zip prop-adress prop-zip xs12 md6 offset-xs0'
      return `${itemClassAppendix} ${getTypeClassName(obj)} ${getKeyClassName(
        obj
      )} ${getPropertyClassName(obj)} ${getGridClassName(obj)}`;
    };
const toCtrl = (params: { obj: { schema: { toCtrl: (arg0: any) => any; }; }; value: any; }): any => {
      // manipulate value going to control, toCtrl-function must return a (modified) value
      // schema:{ name: { type:'text', toCtrl: ( {value} ) value && value.toUpperCase, ... }, ... }
      return isFunction(params.obj.schema.toCtrl) ? params.obj.schema.toCtrl(params) : params.value;
    };
const fromCtrl = (params: { obj: { schema: { fromCtrl: (arg0: any) => any; }; }; value: any; }): any => {
      // manipulate updated value from control, fromCtrl-function must return a (modified) value
      // schema:{ name: { type:'text', fromCtrl: ( {value} ) value && value.toUpperCase, ... }, ... }
      return isFunction(params.obj.schema.fromCtrl) ? params.obj.schema.fromCtrl(params) : params.value;
    };
const sanitizeOptions = (b: any): any => {
      return isString(b) ? { value: b, label: b } : b;
    };
const setValue = (obj: { value: any; }): any => {
      // Control gets a Value
      return toCtrl({ value: obj.value, obj, data: storeStateData.value, schema: storeStateSchema.value });
    };
const onInput = (value: any, obj: { schema: { type: string; }; key: any; value: any; }): void => {
      // Value after change in Control
      value = fromCtrl({ value, obj, data: storeStateData.value, schema: storeStateSchema.value });
      // harmonize undefined or empty strings => null, because clearable resets to null and not to empty string!
      value = !value || value === "" ? null : value;
      // if schema type is number convert to number
      value = obj.schema.type === "number" ? Number(value) : value;
      // update deep nested prop(key) with value
      setObjectByPath(storeStateData.value, obj.key, value);

      emitValue("input", {
        on: "input",
        id: ref.value,
        index: index.value,
        params: { index: index.value, lastValue: obj.value },
        key: obj.key,
        value,
        obj,
        data: storeStateData.value,
        schema: storeStateSchema.value,
      });
    };
const onEvent = (event: { type: any; srcElement?: any; }, obj: { schema: { model: any; open: any; }; key: any; value: any; }, tag: string = undefined): void => {
      delay(() => {
        const text = event && event.srcElement && event.srcElement.innerText;
        const model = obj.schema.model;
        const open = obj.schema.open;
        const index = index.value;

        emitValue(event.type, {
          on: event.type,
          id: ref.value,
          index,
          params: { text, tag, model, open, index },
          key: obj.key,
          value: obj.value,
          obj,
          event,
          data: storeStateData.value,
          schema: storeStateSchema.value,
          parent: parent.value,
        }),
          onEventDelay;
      });
    };
const onIntersect = (entries: { isIntersecting: any; }[], observer: any, obj: { key: any; value: any; }): void => {
      const isIntersecting = entries[0].isIntersecting;
      const index: any = index.value;
      emitValue("intersect", {
        on: "intersect",
        id: ref.value,
        index,
        key: obj.key,
        value: obj.value,
        obj,
        params: { isIntersecting, entries, observer },
        data: storeStateData.value,
        schema: storeStateSchema.value,
      });
    };
const onSwipe = (tag: string, obj: { key: any; value: any; }): void => {
      emitValue("swipe", {
        on: "swipe",
        id: ref.value,
        key: obj.key,
        value: obj.value,
        obj,
        params: { tag },
        data: storeStateData.value,
        schema: storeStateSchema.value,
      });
    };
const onResize = (event: any): void => {
      emitValue("resize", {
        on: "resize",
        id: ref.value,
        params: { x: window.innerWidth, y: window.innerHeight },
        event,
        data: storeStateData.value,
        schema: storeStateSchema.value,
      });
    };
const emitValue = (emit: string, val: any): void => {
      parent.value.$emit(getEventName(emit), val); // listen to specific event only
      if (change.indexOf(emit) > -1) parent.value.$emit(getEventName("change"), val); // listen only to 'input|click'
      if (watch.indexOf(emit) > -1) parent.value.$emit(getEventName("watch"), val); // listen to 'focus|input|click|blur'
      if (mouse.indexOf(emit) > -1) parent.value.$emit(getEventName("mouse"), val); // listen to 'mouseenter|mouseleave  '
      if (display.indexOf(emit) > -1) parent.value.$emit(getEventName("display"), val); // listen to 'resize|swipe|intersect'
      parent.value.$emit(getEventName("update"), val); // listen to all events
    };
const getEventName = (eventName: any): any => {
      return parent.value.id !== defaultID ? `${eventName}:${parent.value.id}` : eventName;
    };
const setObjectByPath = (object: { [x: string]: any; }, path: string, value: any): void => {
      // resolves chained keys (like 'user.address.street') on an object and set the value
      let pathArray = path.split(pathDelimiter);
      pathArray.forEach((p: string|number, ix: number) => {
        if (ix === pathArray.length - 1) $set(object, p, value);
        object = object[p];
      });
    };
const updateArrayFromState = (data: any, schema: any): void => {
      flatCombinedArray.forEach((obj: { value: any; key: any; schema: any; }) => {
        obj.value = get(data, obj.key, null); // get - lodash
        obj.schema = get(schema, obj.key, null); // get - lodash
      });
    };
const sanitizeShorthandType = (key: any, schema: any): any => {
      // check if schema is typeof string ->  shorthand { type: obj } otherwise take original value
      return isString(schema) ? { type: schema, label: key } : schema;
    };
const flattenObjects = (dat: { [x: string]: any; }, sch: { [x: string]: any; }): any => {
      let data = {};
      let schema = {};

      // Organize Formular using Schema not Data
      Object.keys(sch).forEach((key) => {
        // convert string type to object
        sch[key] = sanitizeShorthandType(key, sch[key]);

        if (
          (!Array.isArray(dat[key]) &&
            dat[key] &&
            typeof dat[key] === "object" &&
            sch[key] &&
            sch[key].type !== groupingType) ||
          (Array.isArray(dat[key]) && Array.isArray(sch[key]))
        ) {
          let { data: flatData, schema: flatSchema } = flattenObjects(dat[key], sch[key]);
          Object.keys(flatData).forEach((ii) => {
            data[key + pathDelimiter + ii] = flatData[ii];
            schema[key + pathDelimiter + ii] = flatSchema[ii];
          });
        } else {
          data[key] = dat[key];
          schema[key] = sch[key];
        }
      });
      return { data, schema };
    };
const combineObjectsToArray = ({ data, schema }): any => {
      let arr = [];
      Object.keys(data).forEach((key) => {
        if (!isPlainObject(schema[key])) {
          console.warn(
            `From Schema:`,
            schema,
            ` the Prop '${key}' must be a string with value of type { type:[stringvalue] } or a plainobject with at least { type:'text'} definition.  Schema Prop '${key}' will be ignored!`
          );
          return;
        }
        arr.push({ key, value: data[key], schema: schema[key] });
      });
      return arr;
    };
const flattenAndCombineToArray = (data: any, schema: any): any => {
      // flatten nested structure of both objects 'data' & 'schema' ...
      let flattenedObjects = flattenObjects(data, schema);
      // ... and combine them to an array
      return combineObjectsToArray(flattenedObjects);
    };
const autogenerateSchema = (value: any): void => {
      // generate a minimal default schema from value
      let schema = JSON.stringify(value, (key, val) => (val === undefined ? null : val));
      schema = JSON.parse(schema, (key, val) => {
        if (val === null || val === undefined) return defaultSchemaIfValueIsNullOrUndefined(key);
        if (typeof val === "string") return defaultSchemaIfValueIsString(key);
        if (typeof val === "number") return defaultSchemaIfValueIsNumber(key);
        if (typeof val === "boolean") return defaultSchemaIfValueIsBoolean(key);
        return val;
      });
      // assign root props to avoid manipulating prop: schema
      Object.keys(schema).forEach((key) => (schema[key] = schema[key]));
    };

// computed
const ref = computed((): any => {
      return id;
    });
const parent = computed((): any => {
      let p = this;
      while (p.id.startsWith(p.$parent.value.$parent.value.id + "-")) {
        p = p.$parent.value.$parent.value;
      }
      return p;
    });
const index = computed((): any => {
      const m = ref.value && ref.value.match(/\d+/g);
      return m ? m.map(Number) : [];
    });
const flatCombinedArraySorted = computed((): any => {
      return orderBy(flatCombinedArray, ["schema.sort"], [orderDirection]);
    });
const storeStateData = computed((): any => {
      updateArrayFromState(valueIntern, schema);
      return valueIntern;
    });
const storeStateSchema = computed((): any => {
      updateArrayFromState(valueIntern, schema);
      return schema;
    });

// hooks

onBeforeMount(() => {
  // use <formbase :model="myData" />  - check for legacy code <formbase :value="myData" />
  valueIntern = value || model;
  // break if no model found, but don't break if model is empty object (can filled by editing)
  if (!valueIntern) throw `No 'model' definition found. Use '<formbase :model="myData" />' `;
  // no schema defined or empty -> autogenerate basic schema
  if (isEmpty(schema)) autogenerateSchema(valueIntern);
  // create flatted working array from schema and value
  flatCombinedArray = flattenAndCombineToArray(storeStateData.value, storeStateSchema.value);
});

</script>
