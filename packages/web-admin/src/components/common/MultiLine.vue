<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <div
      class="multiline"
      v-for="(slot, index) in ( $slots.default ? $slots.default() : [] )"
      :key="index"
      v-html="formatText(slot)"
    />
  </div>
</template>

<script setup lang="ts">
// methods
const formatText = (slot: any) => {
  return slot.text?.replace(/。/g, '。<br>').trim() || "";
};
</script>

<style scoped>
.multiline {
  max-width: 100%;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  overflow: hidden;
}
</style>
