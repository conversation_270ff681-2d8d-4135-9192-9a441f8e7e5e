<template>
  <div class="row tw-flex tw-items-center">
    <div class="col-8">
      <q-select
        v-model="selectedSurvey"
        :options="localSurveyOptions"
        background-color="white"
        outlined
        dense
        hide-details
        use-input
        hide-selected
        fill-input
        @filter="filter"
      >
      </q-select>
    </div>
    <div class="tw-px-2 col-4">
      <q-btn dense class="tw-mx-3 text-white" color="blue-grey" 
      @click="reloadSurveyConfigs" :loading="isFetching" no-wrap>
        <q-icon left :name="'mdi-cached'"></q-icon>
        データ更新
      </q-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { useDataManagementStore } from "@/stores/modules/dataManagement";
import { useMemberStore } from "@/stores/modules/member";
import { useQuasar } from "quasar";
import { useFormsStore } from "@/stores/modules/forms";

// quasar
const $q = useQuasar();

// store
const dataManagementStore = useDataManagementStore();
const memberStore = useMemberStore();
const formsStore = useFormsStore();

// emit
const emit = defineEmits(['clearCacheRefreshMemberSurveyResultsDataEvent']);

const localSurveyOptions = ref();

// computed
const surveyConfigs = computed(() => memberStore.memberConfigs);
const isFetching = computed(() => memberStore.isFetchingMemberConfigs);
const selectedSurveyStore = computed(() => dataManagementStore.selectedSurvey);
const allColumns = computed(() => dataManagementStore.allColumns);
const dataTableOptionsStore = computed(() => dataManagementStore.dataTableOptions);

const selectedSurvey = computed({
  get() {
    const selectedSurveyId = selectedSurveyStore.value ? selectedSurveyStore.value.surveyId : null;
    return surveyOptions.value.find((obj) => obj.value === selectedSurveyId);
  },
  set(selectedSurveyObj: { label: string; value: string }) {
    let formSelectedObj = surveyConfigs.value.find((obj) => obj.surveyId === selectedSurveyObj.value);
    dataManagementStore.selectFormToWork(formSelectedObj);
    dataManagementStore.updateDataTableOptionsDataManagement({
      ...dataTableOptionsStore.value,
      page: 1,
    });
  },
});

const surveyOptions = computed((): any => {
  return surveyConfigs.value.map((obj: any) => {
    return {
      value: obj.surveyId,
      label:
        obj.surveyTitle !== undefined && obj.surveyTitle.length <= 90
          ? obj.surveyTitle
          : obj.surveyTitle.substring(0, 90).concat("..."),
    };
  });
});

// watch
watch(
  () => selectedSurveyStore.value,
  (newVal) => {
    if (newVal) {
      reloadDisplayColumns();
    }
  }
);

// mounted
onMounted(async () => {
  let _result = await memberStore.fetchAllMemberFormConfigs();
  if (_result) dataManagementStore.selectFormToWork(surveyConfigs.value[0]);

  localSurveyOptions.value = surveyOptions.value;
});

// methods
const reloadDisplayColumns = (): void => {
  const cols = JSON.parse(JSON.stringify(allColumns.value));
  dataManagementStore.updateDisplayColumns(cols);
};

const reloadSurveyConfigs = async (): Promise<void> => {
  let _result = await memberStore.fetchAllMemberFormConfigs();
  if (_result) {
    emit("clearCacheRefreshMemberSurveyResultsDataEvent");
    $q.notify({ message: "会員リストをリロードしました。" });
  } else
    $q.notify({
      type: "error",
      message: "エラーが発生しました。",
    });
};

const filter = (val: any, update: any) => {
  update(() => {
    if (val === "") {
      localSurveyOptions.value = surveyOptions.value;
    } else {
      localSurveyOptions.value = surveyOptions.value.filter((item: any) => {
        return item.label.toLowerCase().indexOf(val.toLowerCase()) > -1;
      });
    }
  });
};
</script>