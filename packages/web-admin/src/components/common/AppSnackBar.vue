<template>
  <v-snackbar v-model="visible" :timeout="type === 'error' ? -1 : timeout">
    <v-icon left color="yellow accent-4" v-if="type === 'error' || type === 'warning'">mdi-alert-outline</v-icon>
    {{ text }}
    <template v-slot:action="{ attrs }">
      <v-btn color="white" text v-bind="attrs" @click="visible = false">閉じる</v-btn>
    </template>
  </v-snackbar>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from "vue";
import eventBus from '@utils/eventBus';

// data
const visible = ref(false);
const title = ref("");
const text = ref("");
const timeout = ref(3000);
const type = ref("info");

// mounted
onBeforeMount(() => {
  // here we need to listen for emited events
  // we declared those events inside our plugin
  eventBus.on("show", (params: any) => {
    show(params);
  });
  eventBus.on("hide", () => {
    hide();
  });
});

// methods
const hide = (): void => {
  visible.value = false;
};

// TODO onConfirmが不明　一旦コメント
// const confirm = (): void => {
//   // we must check if this.onConfirm is function
//   if (typeof this.onConfirm === "function") {
//     // run passed function and then close the modal
//     this.onConfirm();
//     hide();
//   } else {
//     // we only close the modal
//     hide();
//   }
// };

const show = (params: any): void => {
  // making modal visible
  visible.value = true;
  text.value = params.text;
  type.value = params.type || "info";
};
</script>
