<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <q-select
      v-model="langSelected"
      :items="isoLangOptions"
      menu-props="auto"
      label="言語"
      hide-details
      outlined
      dense
      background-color="white"
      @change="onChange"
    ></q-select>
  </div>
</template>

<script setup lang="ts">
import {ref,  computed,  } from 'vue';



// old imports
// 旧インポート
/*import { IsoLangs } from "@/constants/index";*/

// emits 
const emits = defineEmits<{
  (event: 'onChange', payload: any): void;
}>();

// data
const langSelected = ref(null);

// methods
const onChange = (val: any): void => {
      emits("onChange", val);
    };

// computed
const isoLangs = computed((): any => {
      return IsoLangs;
    });
const isoLangOptions = computed((): any => {
      let _options = [];
      for (let [key, value] of Object.entries(isoLangs.value)) {
        _options.push({
          value: key,
          text: (value as any).name + ` (${(value as any).nativeName})`,
        });
      }
      return _options;
    });
</script>