<template>
  <q-card flat :bordered="border" :class="bgColor + ' ' + textColor" :style="berStyle">
    <q-card-section class="tw-p-2">
      <div class="row tw-flex tw-justify-center">
        <div>
          <q-icon :name="iconName" size="md"></q-icon>
        </div>
        <div class="col tw-ml-4">
          <div class="text-h6">{{ title }}</div>
          <div :class="title ? '' : 'tw-mt-2'">{{ message }}</div>
          <slot></slot>
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
  type: string,
  message?: string,
  title?: string,
  colorText?: string,
  colorBg?: string,
  icon?: string,
  bar?: {
    place: 'left' | 'right' | 'top' | 'bottom',
    color: string,
  },
  border?: boolean,
}>();

const iconName = computed(() => {
  if (props.icon) {
    return props.icon;
  }

  switch (props.type) {
    case 'success':
      return 'mdi-check-circle';
    
    case 'info':
      return 'mdi-information';

    case 'warning':
      return 'mdi-alert-circle';

    case 'error':
      return 'mdi-close-circle';

    default:
      return '';
  }
});

const textColor = computed(() => {
 
  return props.colorText ? 'text-' + props.colorText : 'text-white';
});

const bgColor = computed(() => {
  return props.colorBg ? 'bg-' + props.colorBg : 'bg-' + getTypeColor();
});

const getTypeColor = () => {
  switch (props.type) {
    case 'success':
      return 'positive';
    
    case 'info':
      return 'info';

    case 'warning':
      return 'warning';

    case 'error':
      return 'negative';

    default:
      return 'primary';
  }
};

const berStyle = computed(() => {
  if (props.bar) {
    return 'border-' + props.bar.place + ': solid 10px ' + props.bar.color;
  }
  return '';
});

</script>