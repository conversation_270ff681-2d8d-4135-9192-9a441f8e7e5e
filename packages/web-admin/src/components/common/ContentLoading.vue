<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div>
    <div class="row tw-flex tw-justify-center">
      <div class="col-auto">
        <q-spinner-puff
          color="primary"
          :size="size"
        />
      </div>
    </div>
    <div class="text-center tw-my-1 text-primary">{{ text }}</div>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: "ContentLoading",
});

interface Props {
  size?: number;
  text?: string;
}

// props
withDefaults(defineProps<Props>(), {
  size: 35, 
  text: 'ロード中...'
});
</script>