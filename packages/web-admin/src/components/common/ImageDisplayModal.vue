<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <q-dialog scrollable max-width="800" v-model="show" transition="slide-y-transition">
    <q-card class="full-width">
      <q-img :src="getImageSource" class="full-width"></q-img>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed } from "vue";

defineOptions({ name: "ImageDisplayModal"});

// props emit
const props = defineProps<{
  visible: boolean,
  imgSrc: string,
}>();

const emit = defineEmits(['close']);

// computed
const show = computed({
  get() {
    return props.visible;
  },
  set(value) {
    if (!value) {
      emit("close");
    }
  },
});

const getImageSource = computed(() => {
    return props.imgSrc;
});
</script>
