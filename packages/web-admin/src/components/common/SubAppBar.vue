<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div
    class="sub-app-bar-container"
  >
    <q-toolbar
      class="sub-app-bar justify-between"
      :style="props.full ? 'flex-direction: row;' : ''"
      absolute
      min-width="100%"
      width="100%"
      min-height="64px"
      height="auto"
      ref="subAppBar"
      flat
    >
      <div :style="`${(dense ? 'padding: 0 !important;' : '')} width: 100%;` ">
        <slot/>
      </div>
    </q-toolbar>
    <div
      class="sub-app-bar-spacer"
      :style="'width: 100%; height: ' + spacerHeight + 'px;'"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';

interface LocalState {
  spacerHeight: number;
  resizeObserver: ResizeObserver | null;
}

const props = defineProps({
    full: Boolean,
    followContainer: Boolean,
    dense: Boolean,
});

const subAppBar = ref<any | null>(null);

const spacerHeight = ref(0);
let resizeObserver: ResizeObserver | null = null;

const onUpdateSubAppBar = (): void => {
  if (!subAppBar.value) {
    spacerHeight.value = 0;
  }
  // 更新され続けてしまうので一旦0を代入します　honda
  //spacerHeight.value = 0;
  spacerHeight.value = subAppBar.value.clientHeight + 16;
};

onMounted(() => {
  if (subAppBar.value) {
    resizeObserver = new ResizeObserver(() => {
      onUpdateSubAppBar();
    });
    resizeObserver.observe(subAppBar.value.$el);
    onUpdateSubAppBar();
  }
});

onBeforeUnmount(() => {
  if (resizeObserver && subAppBar.value) {
    resizeObserver.unobserve(subAppBar.value.$el);
    resizeObserver.disconnect();
  }
});
</script>

<style scoped>
.sub-app-bar {
  display: flex;
  left: 0;

  min-height: 64px;
  min-width: 100%;
  width: 100%;

  padding: 12px;

  background-color: var(--q-bg);
}

.sub-app-bar-spacer {
  position: relative;
  top: 0;
}

</style>

