<!--
Copyright 2025 PlayNext Lab Inc.

PlayNext Lab Inc. licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
-->
<template>
  <div v-if="dataLocal">
    <ContentLoading v-if="isLoading" :size="50" text="" />
    <vue-mermaid
      v-else
      class="tw-m-3"
      :nodes="dataLocal"
      :chartId="chartId ?? ''"
      :type="chartId ? 'graph LR' : 'graph TD'"
      @nodeClick="onNodeClick"
    ></vue-mermaid>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { cloneDeep, debounce } from "lodash";

// components
import VueMermaid from "@/components/common/VueMermaid.vue";
import ContentLoading from "../common/ContentLoading.vue";

// props
const props = defineProps<{
  dataSource: any[],
  chartId: string | null,
}>();

const emit = defineEmits(['nodeClick']);

// data
const componentKey = ref(0);
const isLoading = ref(false);
const dataLocal = ref<any[]>([]);

// computed
const parseCode = computed((): string => {
  let _parseCode = ["graph TD", "userInput([ユーザーテキスト])", "richMenu([リッチメニュー])"];

  if (dataLocal.value) {
    dataLocal.value.forEach((node: { id: any; name: any; }) => {
      _parseCode.push(`${node.id}["${node.name}"]`);
    });
  }

  // console.log("parseCode -> _parseCode", _parseCode.join("\n"));
  return _parseCode.join("\n");
});

// watch
watch(
  () => props.dataSource,
  (newVal) => {
    isLoading.value = true;

    // TODO: setupがわからない - honda
    // this.setup(newVal);
  },
  { immediate: true,
    deep: true,
  }
);

// methods
// TODO: - honda
// const mermaidをここからでは呼び出せない load = (code: string): void => {
//   if (code) {
//     var container = mermaid;
//     if (container) {
//       container.removeAttribute("data-processed");
//       container.replaceChild(document.createTextNode(code), container.firstChild);
//     }
//     try {
//       // mermaid can be exported via VueMermaid
//       // @ts-ignore
//       mermaid.init(code, container); // eslint-disable-line no-undef
//       console.log("load -> code", code);
//     } catch (error: any) {
//       console.error(error);
//     }
//   }
// };

const debouncedFunction = debounce(function (val: any) {
  dataLocal.value = cloneDeep(val);
  isLoading.value = false;
}, 500);

const onNodeClick = (nodeId: any): void => {
  let _node = dataLocal.value.find((obj: any) => obj.id == nodeId);
  if (_node && _node.uri) {
    openInNewTab(_node.uri);
  } else {
    emit("nodeClick", nodeId);
  }
};

const openInNewTab = (url: any): void => {
  let win = window.open(url, "_blank");
  win?.focus();
};
</script>