<template>
  <div
    class="tw-relative tw-mx-auto tw-border-gray-800 tw-bg-gray-800 tw-border-[13px] tw-rounded-[2.5rem] tw-h-[90vh] tw-max-h-[700px] tw-min-h-[600px] tw-w-[350px] tw-shadow-xl"
  >
    <div
      class="tw-w-[108px] tw-h-[25px] tw-bg-gray-800 tw-top-1.5 tw-rounded-[1rem] tw-left-1/2 -tw-translate-x-1/2 tw-absolute tw-z-50"
    />
    <div
      class="tw-h-[46px] tw-w-[3px] tw-bg-gray-800 tw-absolute -tw-left-[16px] tw-top-[124px] tw-rounded-l-lg"
    />
    <div
      class="tw-h-[46px] tw-w-[3px] tw-bg-gray-800 tw-absolute -tw-left-[16px] tw-top-[178px] tw-rounded-l-lg"
    />
    <div
      class="tw-h-[64px] tw-w-[3px] tw-bg-gray-800 tw-absolute -right-[16px] tw-top-[142px] tw-rounded-r-lg"
    />
    <div
      v-show="loading"
      class="tw-rounded-[2rem] tw-relative tw-overflow-hidden tw-w-[324px] tw-h-full tw-bg-[#06C755]"
    >
      <slot />
    </div>
    <div
      v-show="!loading"
      class="tw-rounded-[2rem] tw-relative tw-overflow-hidden tw-w-[324px] tw-h-full"
      :class="[
        bgColor
          ? bgColor
          : `tw-bg-cover tw-bg-no-repeat tw-bg-[url('src/assets/images/line-bg.jpg')]`,
      ]"
    >
      <div class="tw-h-full tw-relative tw-flex tw-flex-col tw-pt-10 tw-pb-0">
        <div class="tw-h-10 tw-absolute tw-top-0 left-0 tw-w-full">
          <div
            class="tw-flex tw-flex-inline tw-items-center tw-justify-between tw-pl-8 tw-pr-6 tw-py-2.5"
          >
            <div class="tw-text-sm tw-font-semibold">
              {{ now }}
            </div>
            <div class="tw-flex tw-flex-row tw-items-center tw-space-x-2">
              <UIcon name="i-fa-solid-signal" />
              <UIcon name="i-fa-solid-wifi" />
              <UIcon name="i-fa-solid-battery-three-quarters" class="text-lg" />
            </div>
          </div>
        </div>
        <slot />
        <div class="tw-h-6 tw-absolute tw-bottom-0 tw-left-0 tw-w-full">
          <div
            class="tw-w-2/5 tw-h-[5px] tw-bg-gray-800 tw-top-2.5 tw-rounded-[1rem] tw-left-1/2 -tw-translate-x-1/2 tw-absolute tw-z-50"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import dayjs from 'dayjs';

defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  bgColor: {
    type: String,
    default: null,
  },
});

const now = ref(dayjs().format('HH:mm'));

setInterval(() => {
  now.value = dayjs().format('HH:mm');
}, 1000);
</script>
