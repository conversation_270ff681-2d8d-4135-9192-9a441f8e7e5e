<template>
  <div class="tw-grid tw-grid-cols-12 tw-max-h-[calc(100vh-160px)] tw-gap-4">
    <div class="tw-col-span-4">
      <div class="q-px-md">
        <div class="tw-w-full tw-mb-4">
          <q-file
            v-model="imageMapImageFile"
            class="tw-w-full"
            :label="!imageMapImageFile ? 'クリックして画像を開く' : '画像'"
            filled
            counter
            dense
            clearable
            accept="image/png, image/jpeg, image/jpg"
          >
            <template v-slot:prepend>
              <q-icon name="attach_file" />
            </template>

            <template v-slot:hint>
              画像形式：JPG, JPEG, PNG | 画像サイズ：10MB以下
              <p class="tw-my-3 tw-text-red-400">
                {{ localState.hasError ? localState.errorMessage : "" }}
              </p>
            </template>
          </q-file>
        </div>
        <q-scroll-area ref="scrollArea" style="height: 450px">
          <div class="hidden-scrollbar tw-overflow-auto tw-flex tw-flex-col tw-gap-4">
            <div v-for="(action, index) in actions" :key="index" :id="action?.id">
              <MessageSimulatorMessageImageMapAction
                :modelValue="action"
                :imageMapImageHeight="localState.imageMapImageHeight"
                :no="index + 1"
                @deleteArea="deleteArea"
              />
            </div>
          </div>
        </q-scroll-area>
      </div>
    </div>
    <ImageMultiSelectorArea :width="imageMapImageWidthBase" :file="imageMapImageFile" :initActions="props.modelValue?.actions" :base-url="props.modelValue?.baseUrl"/>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash';
import { storeToRefs } from 'pinia';
import { MessageContents } from '@/types/index';
import { useMessageSimulatorStore } from '@/stores/modules/message-simulator';
import ImageMultiSelectorArea from '../ImageMultiSelector/ImageMultiSelectorArea.vue';
import MessageSimulatorMessageImageMapAction from './MessageSimulatorMessageImageMapAction.vue';
import { computed, ref, watch } from 'vue';

const messageSimulatorStore = useMessageSimulatorStore();

const {
  message,
  imgSrc,
  imgType,
  actions,
  imageMapImageHeight,
  selectedAction,
  imageMapImageWidthBase
} = storeToRefs(messageSimulatorStore);

interface LocalState {
  hidePreviewDisplay: Array<string>;
  originalParams: any;
  imageMapFiles: Array<any>;
  visible: boolean;
  lastDeletedId: any;
  editFiles: boolean;
  editFocus: boolean;
  isEditing: boolean;
  displayAreas: Array<any>;
  imageMap: any;
  hasError: boolean;
  errorMessage: string;
  // imageMapImageFile: any;
  imageMapImageUrl: any;
  imageMapImageHeight: any;
  fileModels: any;
  actionTypes: Array<any>;
  urlRegex: any;
  onSave: boolean;
}

const props = defineProps<{
  modelValue: MessageContents;
}>();

const emits = defineEmits(['update:modelValue']);

const scrollArea = ref(null);

const imageMapImageFile = computed({
  get: () => props.modelValue?.file,
  set: (value) => {
    const uploadedFile = value;

    if (uploadedFile) {
      const fileType = uploadedFile.type;
      const fileSize = uploadedFile.size;
      if (
        fileType !== 'image/png' &&
        fileType !== 'image/jpg' &&
        fileType !== 'image/jpeg'
      ) {
        localState.value.hasError = true;
        localState.value.errorMessage = `ファイル形式：『${fileType}』がPNG・JPG・JPEGではありません。`;
      } else {
        localState.value.errorMessage = '';
      }
      // console.log('file size: ', fileSize);
      if (fileSize > 10000000) {
        localState.value.hasError = true;
        localState.value.errorMessage = `ファイルサイズ：『${
          fileSize / 1000000
        }MB』が10MB以上です。`;
      } else {
        localState.value.errorMessage = '';
      }

      if (localState.value.errorMessage !== '' || localState.value.errorMessage !== '') {
        return;
      }
      else{
        updateFile(value);
      }
    }
    // console.log('🚀 ~ value:', value);
    emits('update:modelValue', {
      ...props.modelValue,
      file: value,
    });
  },
});

const areaMaxBound = 2147483647;
const params = computed(() => cloneDeep(props.modelValue) || {}) as any;

const imageMapPreviewImageHeight = computed(() => {
  // (700 / 1040) * this.imageMapImageHeight;
  return (imageMapImageWidthBase.value / 1040) * localState.value?.imageMapImageHeight;
});

const localState = ref<LocalState>({
  hidePreviewDisplay: ['jsonTemplate', 'compositeMessage'],
  originalParams: cloneDeep(params.value) || {},
  imageMapFiles: [],
  visible: true,
  lastDeletedId: null,
  editFiles: false,
  editFocus: false,
  isEditing: false,
  displayAreas: [],
  imageMap: {},
  hasError: false,
  errorMessage: '',
  // imageMapImageFile: null,
  imageMapImageUrl: null,
  imageMapImageHeight: params.value?.autoDetectedBaseHeight || 0,
  fileModels: undefined,
  actionTypes: [
    { value: 'message', text: 'メッセージアクション' },
    { value: 'uri', text: 'URIアクション' },
  ],
  // eslint-disable-next-line
  urlRegex: /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/,
  onSave: false,
});

watch(
  () => actions.value,
  (val) => {
    if (val) {
      emits('update:modelValue', {
        ...props.modelValue,
        altText: '新着メッセージがあります。',
        baseSize: {
          width: imageMapImageWidthBase.value,
          height: imageMapImageHeight.value,
        },
        actions: val,
      });
    }
  },
  {
    deep: true,
  }
);

watch(
  () => selectedAction.value,
  (newValue, oldValue) => {
    // scroll to the selected action
    if (newValue) {
      const element = document.getElementById(newValue?.id ?? '');
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  },
  {
    deep: true,
    immediate: true,
  }
);

function deleteArea(id: any): void {
  messageSimulatorStore.removeAction(id);
}

function updateFile(value: any): void {
  localState.value.imageMap.areas = [];
  const reader = new FileReader();
  reader.onload = (e) => {
    const img: any = new Image();
    localState.value.imageMapImageUrl = e.target?.result;
    imgSrc.value = e.target?.result?.toString() ?? '';
    message.value.contents.type = 'imagemap';
    if (typeof e?.target?.result === 'string') {
      const fileType = e?.target?.result.split(':')[1].split(';')[0]; // should be image/png or jpg or jpeg
      imgType.value = fileType;
    }
    img.onload = () => {
      if (!localState.value.imageMap.size) {
        localState.value.imageMap.size = {};
      }

      if (localState.value.imageMap.size) {
        localState.value.imageMap.size.height = Math.ceil(
          img.height * (1040 / img.width)
        );
        localState.value.imageMapImageHeight = Math.ceil(
          img.height * (1040 / img.width)
        );
        imageMapImageHeight.value = localState.value.imageMapImageHeight;
      }
    };
    img.src = e.target?.result as string;
  };
  reader.readAsDataURL(value);
}
</script>

<style>
.nonDrag {
  -webkit-user-drag: none;
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}
.rich-menu-properties-col {
  height: calc(100vh - 150px);

  padding-top: 0px;
  overflow: scroll;
  background-color: #f2f2f2;
}

.image-select-area {
  padding-top: 0px;
  background-color: #688bbc;
}

.active-rich-menu-action {
  border-left: medium solid #00b900 !important;
}

.rich-menu-action-col {
  padding-top: 0.1em;
  padding-bottom: 0.1em;
}

.title-row {
  background-color: lightgray;
  padding-left: 1em;
}

.image-select-area {
  overflow: hidden;
}

.selected-image-area {
  margin: 1.5em;
}

.caption {
  margin-bottom: 0em;
  margin-left: 0.5em;
  padding-left: 0.5em;
}

.image-instructions {
  margin-bottom: 1em;
}
</style>

<style lang="scss">
.c-crop {
  display: inline-block;
  * {
    box-sizing: border-box;
  }
  img {
    pointer-events: none;
  }
  .c-crop--hide_main {
    width: 0;
    height: 0;
    overflow: hidden;
  }
}
.original-image {
  position: absolute;
}
.select-areas {
  &--overlay {
    overflow: hidden;
    position: absolute;
  }
  &--delete_area {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAQAAAC1+jfqAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAAmJLR0QAAKqNIzIAAAAJcEhZcwAADdcAAA3XAUIom3gAAAAHdElNRQfjCB0SCQuXtRLQAAABRklEQVQoz4XRMUiUcQCG8V//O6MhuuEwI4VDDg9ubDCC+ILzIgcFySEnP2wOkqDRMffAa+3wpqDBSRAPp6MlC+yTFsnS0EzBursp8ECHS3AIetYXXnjfhy5B2SuJlpZPKkaEbnAJDJh33w/v7SLntpvq5uz5G69IPFWUlZGRVTQrsaK/W74o8UiftHPS+kxJVIWUkucWLHvilkO/kfdY5K2OaR+DSQfqjrWNmzFkyIxxbcdWHZpMi7xzpGNJxl29KGhY0nFk3b0gZ0cH22q2lJVtqdnGiW9ywX8Idg3qQV6sYM2aglgePQbtpDXc0avpoUhDDbFIy0vXDWuk/BH76avIpje++OW7lGs+mzBqnqAqMfWPoza9FlJOfVAy5kTTqcuuuCpnwqx9z7S7svq98MDBBVk31M3Zv7hmRMWGpqYNC0rnus8AXqJjvC9MrWIAAAAldEVYdGRhdGU6Y3JlYXRlADIwMTktMDgtMjlUMTY6MDk6MTErMDI6MDDV30hTAAAAJXRFWHRkYXRlOm1vZGlmeQAyMDE5LTA4LTI5VDE2OjA5OjExKzAyOjAwpILw7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAAASUVORK5CYII=);
    cursor: pointer;
    height: 16px;
    width: 16px;
  }
}
.delete-area {
  position: absolute;
  cursor: pointer;
  padding: 5px;
}
</style>
