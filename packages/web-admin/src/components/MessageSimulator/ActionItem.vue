<template>
    <div class="tw-flex" >
        <q-select
        class="tw-flex-1"
        v-model="imageMapActions[props.i].actionType"
        dense
        outlined
        :options="actionTypes"
        emit-value
        map-options
        />
        <q-input class="tw-pl-1 tw-flex-1 tw-mx-3" v-model="imageMapActions[props.i].text" />
        <div>
            <q-btn color="red" @click="messageSimulatorStore.deleteImageMapAction(props.i)" >削除</q-btn>
        </div>
    </div>

</template>

<script setup lang="ts">
import { useSegmentsStore } from '../../stores/modules/segments'
import { storeToRefs } from 'pinia'
import { onMounted, ref } from 'vue'
import { useMessageSimulatorStore } from '../../stores/modules/message-simulator'
const messageSimulatorStore = useMessageSimulatorStore()
const segmentssStore = useSegmentsStore()
const { createDelivery } = storeToRefs(segmentssStore)
const { message, imageMapActions } = storeToRefs(messageSimulatorStore)

const actionTypes = ['メッセージアクション', 'URIアクション']

const props = defineProps({
  text: { type: String, required: true },
  i: { type: Number, required: true },
})

const deleteActionItem = () => {
  messageSimulatorStore.deleteImageMapAction(props.i)
}

</script>
