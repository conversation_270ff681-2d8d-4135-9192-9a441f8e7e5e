<template>
  <div>
    <div>メッセージ</div>
    <q-input
      ref="messageInputElm"
      outlined
      autogrow
      placeholder="テキストを入力"
      counter
      maxlength="3000"
      v-model="text"
    />
    <div>絵文字</div>
    <div class="tw-flex tw-flex-col tw-gap-1 tw-border tw-border-gray-300 tw-rounded-md">
      <p class="tw-text-sm tw-p-3 tw-bg-gray-100">
        {{
          `<<$プロダクトID_絵文字ID$>>の文字列は絵文字埋め込み文字で、送信時に選択した絵文字に置き換えられます。`
        }}
        <br />
        実際の送信内容はプレビューを確認してください。なお、プロダクトID、絵文字IDについては
        <a
          class="text-primary !tw-underline"
          href="https://developers.line.biz/ja/docs/messaging-api/emoji-list/"
          target="_blank"
          >こちら</a
        >
        を参照してください。
      </p>
      <p class="tw-text-xs tw-text-red-400 tw-mx-2">
        {{ textOverflowError ? textOverflowError : "" }}
      </p>
      <q-separator />

      <div class="tw-px-2 tw-pb-2">
        <MessageSimulatorMessageEmoji @selectEmoji="onSelectEmoji" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { MessageContents } from '@/types/index';
import MessageSimulatorMessageEmoji from './MessageSimulatorMessageEmoji.vue';
import { computed, ref } from 'vue';
const props = defineProps<{
  modelValue: MessageContents;
}>();
const emits = defineEmits(['update:modelValue']);

const messageInputElm = ref();
const text = computed({
  get: () => props.modelValue.text,
  set: (value) => emits('update:modelValue', { ...props.modelValue, text: value }),
});

const textOverflowError = ref<string>('');

const onSelectEmoji = (emoji: string) => {
  const currentCursorPosition = messageInputElm.value.getNativeElement()?.selectionStart;
  // text.value += emoji
  if (text?.value?.length) {
    const newLen = text?.value?.length + emoji.length;
    if (newLen > 3000) {
      textOverflowError.value = 'メッセージが3000文字以下です。';
      return;
    } else {
      textOverflowError.value = '';
    }
  }
  // get cursor position
  if (currentCursorPosition === undefined) {
    text.value += emoji;
    return;
  }
  const textValue = text.value;
  const newTextValue =
    textValue?.slice(0, currentCursorPosition) +
    emoji +
    textValue?.slice(currentCursorPosition)
  text.value = newTextValue;

  // focus on input
  messageInputElm.value.getNativeElement()?.focus();
};
</script>
