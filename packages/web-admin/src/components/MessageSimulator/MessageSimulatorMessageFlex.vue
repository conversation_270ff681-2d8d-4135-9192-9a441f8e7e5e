<template>
  <div class="tw-flex tw-flex-col tw-gap-4">
    <div class="tw-w-full tw-flex tw-flex-row tw-gap-4 tw-items-center">
      <q-banner dense rounded class="bg-yellow-1 text-black tw-flex-1">
        <div class="">
          <a
            href="https://developers.line.biz/flex-simulator"
            target="_blank"
            class="text-primary !tw-underline"
          >
            Flex Message Simulator
          </a>
          を使用して、テンプレートのJSONを作成することができます。<br />
          フレックスメッセージのサンプルは<a
            href="https://docs.line-smartcity.org/flex-sample"
            target="_blank"
            class="text-primary !tw-underline"
          >
            こちら </a
          >を参照してください。
        </div>
      </q-banner>
      <div class="tw-flex tw-flex-wrap">
        <div>テンプレート</div>
        <q-select
          lazy-rules
          v-model="msgTemplate"
          dense
          outlined
          placeholder="帳票"
          :options="FLEXMESSAGE_TEMPLATE_ITEMS"
          option-value="value"
          option-label="text"
          class="tw-max-w-xs tw-w-full"
        />
      </div>
    </div>

    <div>
      <div>
        <div>メッセージJSON</div>
      </div>
      <!-- <q-input outlined autogrow placeholder="JSONを入力" v-model="msgJson"/> -->
      <JsonEditorVue
        mode="text"
        :mainMenuBar="false"
        v-model="msgJson"
        v-bind="{
          /* local props & attrs */
        }"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { MessageContents } from '@/types/index';
import { FLEXMESSAGE_TEMPLATE_ITEMS } from '@/stores/modules/segments/segments.constants';
import { computed } from 'vue';
import JsonEditorVue from 'json-editor-vue';

const props = defineProps<{
  modelValue: MessageContents;
}>();

const emits = defineEmits(['update:modelValue']);

let msgStr= '{}';
let altText= 'リッチメッセージ'

const msgTemplate = computed({
  get: () => props.modelValue.template,
  set: (value) => {
    const isJson = true;
    emits('update:modelValue', {
      ...props.modelValue,
      template: value,
      contents: value?.value,
      altText: altText,
      isJson,
    });
  },
});

const msgJson = computed({
  get: () => { 
    let result;
    if (props.modelValue.isJson) {
      result = props.modelValue.contents ?? '{}';
    }
    else {
      result = msgStr;
    }
    return result;
  },
  set: (value) => {
    try {
      let json;
      if (typeof(value) === "string"){
        json = JSON.parse(value);
      }
      const isJson = true;
      emits('update:modelValue', {
        ...props.modelValue,
        contents:json,
        altText: altText,
        isJson,
      });
    } catch (e) {
      const isJson = false;
      if (typeof(value) === "string"){
        msgStr = value;
      }
      emits('update:modelValue', {
        ...props.modelValue,
        isJson,
        altText: altText,
      });
    }
  },
});
</script>
