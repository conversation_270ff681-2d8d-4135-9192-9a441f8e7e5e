<template>
  <q-card
    class=""
    :flat="!modelValue.selected"
    bordered
    :class="{
      'tw-border-l-green-500': modelValue.selected,
      'tw-border-l-4': modelValue.selected,
    }"
  >
    <div
      class="tw-text-sm tw-font-semibold tw-px-3 tw-pb-2 tw-pt-4 tw-flex tw-flex-row tw-justify-between tw-items-center"
    >
      アクション {{ no }}
      <q-btn
        size="sm"
        rounded
        color="red"
        label="削除"
        @click="deleteArea(modelValue.id)"
      />
    </div>
    <q-separator />

    <div class="tw-grid tw-grid-cols-4 tw-py-2 tw-mt-2 tw-px-3 tw-gap-4">
      <q-input filled v-model.number="area.x" :dense="true" type="number" min="0">
        <template v-slot:prepend>
          <div class="tw-text-base">X:</div>
        </template>
      </q-input>
      <q-input
        filled
        v-model.number="area.y"
        stack-label
        :dense="true"
        type="number"
        min="0"
      >
        <template v-slot:prepend>
          <div class="tw-text-base">Y:</div>
        </template>
      </q-input>
      <q-input
        filled
        v-model.number="area.width"
        stack-label
        :dense="true"
        type="number"
        :min="0"
        @change="
          () => {
            if (area.width > imageMapImageWidthBase) {
              area.width = imageMapImageWidthBase;
            }
          }
        "
      >
        <template v-slot:prepend>
          <div class="tw-text-base">横:</div>
        </template>
      </q-input>
      <q-input
        filled
        v-model.number="area.height"
        stack-label
        :dense="true"
        type="number"
        min="0"
        @change="
          () => {
            if (area.height > imageMapImageHeight) {
              area.height = imageMapImageHeight;
            }
          }
        "
      >
        <template v-slot:prepend> <div class="tw-text-base">縦:</div> </template>
      </q-input>
    </div>
    <q-list bordered class="tw-mb-2">
      <q-expansion-item
        dense
        dense-toggle
        expand-separator
        icon="settings_suggest"
        label="クイック調整"
      >
        <q-card>
          <q-card-section>
            <div class="tw-grid tw-grid-cols-12">
              <div class="tw-col-span-4 tw-border-r tw-border-r-gray-200 tw-pr-5 tw-mr-5">
                <div class="tw-mb-2 tw-text-sm tw-font-semibold">位置</div>
                <div class="tw-grid tw-grid-cols-3 tw-gap-1">
                  <q-btn
                    size="md"
                    dense
                    icon="north_west"
                    @click="moveTo(modelValue.id, 0, 0)"
                  />
                  <q-btn
                    size="md"
                    dense
                    icon="north"
                    @click="moveTo(modelValue.id, area.x, 0)"
                  />
                  <q-btn
                    size="md"
                    dense
                    icon="north_east"
                    @click="moveTo(modelValue.id, imageMapImageWidthBase - area.width, 0)"
                  />
                  <q-btn
                    size="md"
                    dense
                    icon="west"
                    @click="moveTo(modelValue.id, 0, area.y)"
                  />
                  <q-btn
                    size="md"
                    dense
                    icon="filter_center_focus"
                    @click="
                      moveTo(
                        modelValue.id,
                        350 - area.width / 2,
                        (imageMapImageHeight - area.height) / 2
                      )
                    "
                  />
                  <q-btn
                    size="md"
                    dense
                    icon="east"
                    @click="
                      moveTo(modelValue.id, imageMapImageWidthBase - area.width, area.y)
                    "
                  />
                  <q-btn
                    size="md"
                    dense
                    icon="south_west"
                    @click="moveTo(modelValue.id, 0, imageMapImageHeight - area.height)"
                  />
                  <q-btn
                    size="md"
                    dense
                    icon="south"
                    @click="
                      moveTo(modelValue.id, area.x, imageMapImageHeight - area.height)
                    "
                  />
                  <q-btn
                    size="md"
                    dense
                    icon="south_east"
                    @click="
                      moveTo(
                        modelValue.id,
                        imageMapImageWidthBase - area.width,
                        imageMapImageHeight - area.height
                      )
                    "
                  />
                </div>
              </div>
              <div class="tw-col-span-8">
                <div class="tw-mb-2 tw-text-sm tw-font-semibold">サイズ</div>
                <div class="tw-flex tw-gap-2 tw-flex-wrap">
                  <q-btn
                    v-for="size in sizeOptions"
                    :key="size.label"
                    size="md"
                    dense
                    class="tw-px-2.5"
                    @click="resizeTo(modelValue.id, size.value.width, size.value.height)"
                    :label="size.label"
                  />
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </q-expansion-item>
    </q-list>

    <div class="tw-text-xs tw-px-3 tw-text-gray-500">
      画像サイズは 横: {{ imageMapImageWidthBase }} 縦:
      {{ imageMapImageHeight }} です。<br />
      範囲外の値を入力した場合や、範囲が重なっている場合は意図しない動作になる可能性があります。
    </div>
    <div class="tw-px-3 tw-py-3 tw-flex tw-flex-col tw-gap-4">
      <q-select
        filled
        v-model="action.type"
        :options="actionTypes"
        label="タイプ"
        stack-label
        :dense="true"
        :options-dense="true"
        emit-value
        map-options
      />
      <q-input
        v-if="action.type === 'message'"
        filled
        v-model="action.text"
        label="テキスト"
        stack-label
        :dense="true"
        maxlength="400"
        counter
        :rules="[
          (v) => !!v || '必須',
          (v) => v.length <= 400 || '400文字以内で入力してください',
        ]"
      />
      <q-input
        v-if="action.type === 'uri'"
        filled
        v-model="action.linkUri"
        label="URL"
        stack-label
        :dense="true"
        :rules="[(v) => !!v || '必須', (v) => checkValidUrl(v) || 'URLが無効です']"
      />
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { cloneDeep, isNumber } from 'lodash'
import { computed, ref, watch } from 'vue'
import { useMessageSimulatorStore } from '../../stores/modules/message-simulator'
const messageSimulatorStore = useMessageSimulatorStore()
const { imageMapsEditor, imageMapImageHeight, imageMapImageWidthBase } = storeToRefs(
  messageSimulatorStore
)

const props = defineProps<{
  modelValue: any;
  no: number;
}>()

const emit = defineEmits(['update:modelValue', 'deleteArea'])

const area = computed({
  get: () => props.modelValue?.area || {},
  set: (value) => {
    emit('update:modelValue', {
      ...props.modelValue,
      area: value,
    })
  },
})

const action = computed({
  get: () => props.modelValue || {},
  set: (value) => {
    emit('update:modelValue', {
      ...props.modelValue,
      value,
    })
  },
})

const actionTypes = [
  { value: 'message', label: 'メッセージアクション' },
  { value: 'uri', label: 'URIアクション' },
]

const checkValidUrl = (url: string): boolean => {
  const urlRegex = /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/
  const regex = new RegExp(urlRegex)
  return url.match(regex) !== null
}

function deleteArea(areaId: any): void {
  emit('deleteArea', areaId)
}

watch(
  () => props.modelValue,
  (newValue) => {
    // console.log('🚀 ~ newValue:', newValue)
    const component = imageMapsEditor.value?.getComponentById(newValue.id)
    if (component) {
      // move component and resize component
      component.move(
        newValue.area.x - component.dim.x,
        newValue.area.y - component.dim.y
      )
      component.resize(
        newValue.area.width + newValue.area.x,
        newValue.area.height + newValue.area.y
      )
    }
  },
  {
    deep: true,
    immediate: true,
  }
)

const moveTo = (id: string, x: number, y: number) => {
  const component = imageMapsEditor.value?.getComponentById(id)
  if (component) {
    component.move(x - component.dim.x, y - component.dim.y)
    messageSimulatorStore.updateActionArea(id, {
      x,
      y,
      width: area.value.width,
      height: area.value.height,
    })
  }
}

const sizeOptions = computed(() => {
  return [
    {
      label: '全幅',
      value: {
        width: imageMapImageWidthBase.value,
        height: area.value.height,
      },
    },
    {
      label: '1/2幅',
      value: {
        width: 350,
        height: area.value.height,
      },
    },
    {
      label: '1/3幅',
      value: {
        width: 233,
        height: area.value.height,
      },
    },
    {
      label: '1/4幅',
      value: {
        width: 175,
        height: area.value.height,
      },
    },
    {
      label: '1/5幅',
      value: {
        width: 140,
        height: area.value.height,
      },
    },
    {
      label: '全高',
      value: {
        width: area.value.width,
        height: imageMapImageHeight.value,
      },
    },
    {
      label: '1/2高',
      value: {
        width: area.value.width,
        height: imageMapImageHeight.value / 2,
      },
    },
    {
      label: '1/3高',
      value: {
        width: area.value.width,
        height: imageMapImageHeight.value / 3,
      },
    },
    {
      label: '1/4高',
      value: {
        width: area.value.width,
        height: imageMapImageHeight.value / 4,
      },
    },
    {
      label: '1/5高',
      value: {
        width: area.value.width,
        height: imageMapImageHeight.value / 5,
      },
    },
  ]
})

const resizeTo = (id: string, width: number, height: number) => {
  const component = imageMapsEditor.value?.getComponentById(id)
  if (component) {
    component.resize(width, height)
    messageSimulatorStore.updateActionArea(id, {
      x: area.value.x,
      y: area.value.y,
      width,
      height,
    })
  }
}
</script>
