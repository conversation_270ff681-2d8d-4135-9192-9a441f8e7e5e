<template>
  <div
    class="tw-whitespace-break-spaces tw-break-all tw-border tw-p-2 tw-bg-gray-50 tw-rounded-2xl tw-text-xs"
    v-html="messageTextHtml"
  ></div>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import emojis from "src/assets/emojis.json";
const props = defineProps<{
  message: string;
}>();

const isValidEmoji = (emojiId: string) => {
  return emojis?.paths?.some((emoji) => emoji?.path?.includes(emojiId));
};

const messageTextHtml = computed(() => {
  let msgHtml = props.message?.contents?.text;
  // check if msg include emoji <<$5ac1bfd5040ab15980c9b435_008$>>
  const emojiRegex = /<<\$[a-zA-Z0-9_]+\$>>/g;
  const emojiMatches = msgHtml.match(emojiRegex);
  if (emojiMatches) {
    emojiMatches.forEach((emoji) => {
      const emojiId = emoji.replace("<<$", "").replace("$>>", "");
      if (isValidEmoji(emojiId)) {
        msgHtml = msgHtml.replace(
          emoji,
          `<img src="/emojis/${emojiId}.png" alt="emoji" class="tw-w-6 tw-h-6 tw-inline-block" />`
        );
      }
    });
  }
  return msgHtml;
});
</script>
