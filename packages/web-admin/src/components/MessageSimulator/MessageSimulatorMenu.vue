<template>
  <div>
    <q-tabs
      v-model="message.contents.type"
      class="text-grey-8 bg-grey-3"
      active-color="primary"
      indicator-color="primary"
      align="left"
    >
      <q-tab
        v-for="msgType in messageTypes"
        :key="msgType.id"
        :name="msgType.id"
        :icon="msgType.icon"
      />
    </q-tabs>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { useMessageSimulatorStore } from '../../stores/modules/message-simulator';
const messageSimulatorStore = useMessageSimulatorStore();

const { message, messageTypes } = storeToRefs(messageSimulatorStore);
</script>
