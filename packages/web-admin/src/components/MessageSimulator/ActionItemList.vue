<template>
    <div>
        <p>プロパティ</p>
        <div v-for="(action, i) in imageMapActions" class="" :key="action.text">
            <ActionItem :i="i" text="action.text" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { onMounted, ref } from 'vue'
import { useMessageSimulatorStore } from '../../stores/modules/message-simulator'
import ActionItem from './ActionItem.vue'

const messageSimulatorStore = useMessageSimulatorStore()

const { message, imageMapActions } = storeToRefs(messageSimulatorStore)

// 'メッセージアクション', 'URIアクション'
</script>
