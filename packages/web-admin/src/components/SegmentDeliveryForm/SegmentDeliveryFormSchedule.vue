<template>
  <div>
    <div class="tw-mb-1">配信日時</div>
    <div class="tw-flex tw-flex-col">
      <q-radio
        v-if="props.instant"
        v-model="createDelivery.settings.distributionType"
        :val="'instant'"
        label="今すぐ配信"
      />
      <div v-if="!props.instant" class="tw-flex tw-flex-row tw-gap-4 tw-items-center">
        <q-radio
          v-model="createDelivery.settings.distributionType"
          :val="'postponed'"
          label="一回のみ配信"
        />
        <div v-if="createDelivery.settings.distributionType === 'postponed'">
          <q-input dense filled v-model="oneDateTime">
            <template v-slot:prepend>
              <q-icon name="event" class="cursor-pointer">
                <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                  <q-date
                    v-model="oneDateTime"
                    mask="YYYY-MM-DD HH:mm"
                    :options="isValidDateTime"
                  >
                    <div class="row items-center justify-end">
                      <q-btn v-close-popup label="Close" color="primary" flat />
                    </div>
                  </q-date>
                </q-popup-proxy>
              </q-icon>
            </template>

            <template v-slot:append>
              <q-icon name="access_time" class="cursor-pointer">
                <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                  <q-time v-model="oneDateTime" mask="YYYY-MM-DD HH:mm" format24h>
                    <div class="row items-center justify-end">
                      <q-btn v-close-popup label="Close" color="primary" flat />
                    </div>
                  </q-time>
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>
        </div>
      </div>
      <div v-if="!props.instant" class="tw-flex tw-flex-row tw-gap-4 tw-items-center">
        <q-radio
          v-model="createDelivery.settings.distributionType"
          :val="'repeating'"
          label="詳しく配信条件を設定する"
        />
      </div>
    </div>
    <q-list
      v-if="createDelivery.settings.distributionType === 'repeating'"
      bordered
      class="tw-bg-gray-50 tw-mt-3 tw-rounded-lg tw-border-dashed tw-ml-3"
    >
      <q-expansion-item
        v-model="isExpandSetting"
        icon="tune"
        label="繰り返し指定"
        :caption="isInitiallyRepeating ? '設定済み' : '未設定'"
      >
        <q-card class="tw-bg-gray-50 tw-border-t">
          <q-card-section class="q-gutter-sm">
            <div class="tw-grid tw-grid-cols-3 tw-gap-4">
              <div>
                <div>開始日</div>
                <BaseDatePickerInput
                  v-model="createDelivery.settings.repeatingSettings.fromDate"
                  :isFromDate="true"
                  dense
                />
              </div>
              <div>
                <div>終了日</div>
                <BaseDatePickerInput
                  v-model="createDelivery.settings.repeatingSettings.toDate"
                  :isToDate="true"
                  dense
                />
              </div>
              <div>
                <div>配信時間</div>
                <BaseTimePickerInput
                  v-model="createDelivery.settings.repeatingSettings.fromTime"
                  dense
                />
              </div>
            </div>
            <div>
              <div>配信区分</div>
              <div>
                <q-option-group
                  v-model="createDelivery.settings.repeatingSettings.period"
                  :options="distributionCategories"
                  color="primary"
                  inline
                />
              </div>
            </div>
            <div v-if="createDelivery.settings.repeatingSettings.period === 'Weekly'">
              <div>曜日</div>

              <BaseCheckboxButtonGroup
                class="tw-mt-2 tw-ml-2"
                v-model="createDelivery.settings.repeatingSettings.daysOfWeek"
                :options="daysOfWeekOptions"
              />
            </div>
            <div v-if="createDelivery.settings.repeatingSettings.period === 'Monthly'">
              <div>日付</div>

              <BaseCheckboxButtonGroup
                class="tw-mt-2 tw-ml-2"
                v-model="createDelivery.settings.repeatingSettings.daysOfMonth"
                :options="daysOfMonthOptions"
              />
            </div>
            <div
              v-if="createDelivery.settings.repeatingSettings.period === 'LastDay'"
              class="tw-text-sm tw-text-gray-500"
            >
              ❊ 期間が月末の場合は、日付の月しか指定できません。
            </div>
            <div v-if="createDelivery.settings.repeatingSettings.period === 'Custom'">
              <SegmentDeliveryFormScheduleCustom />
            </div>
            <q-separator />
            <div>
              <q-checkbox
                v-model="createDelivery.settings.repeatingSettings.withExclude"
                label="除外日を設定する"
              />
              <div v-show="createDelivery.settings.repeatingSettings.withExclude">
                <p class="tw-mt-2 tw-mb-3">除外日設定</p>
                <div>曜日</div>
                <BaseCheckboxButtonGroup
                  class="tw-mt-2 tw-ml-2"
                  v-model="createDelivery.settings.repeatingSettings.exclude.daysOfWeek"
                  :options="daysOfWeekOptions"
                />
                <div class="tw-mt-5 tw-mb-3">カレンダーの設定</div>
                <q-date
                  v-model="createDelivery.settings.repeatingSettings.exclude.dates"
                  mask="YYYY-MM-DD"
                  multiple
                  :options="isValidDate"
                />
              </div>
            </div>
            <div>
              <SegmentDelivery />
            </div>
          </q-card-section>
        </q-card>
      </q-expansion-item>
    </q-list>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onBeforeMount } from 'vue';
import { useSegmentsStore } from '@/stores/modules/segments';
import { storeToRefs } from 'pinia';
import dayjs from 'dayjs';
import BaseDatePickerInput from '../base/BaseDatePickerInput.vue';
import BaseTimePickerInput from '../base/BaseTimePickerInput.vue';
import BaseCheckboxButtonGroup from '../base/BaseCheckboxButtonGroup.vue';
import SegmentDeliveryFormScheduleCustom from './SegmentDeliveryFormScheduleCustom.vue';

const props = defineProps<{
  instant: boolean;
}>();

const segmentssStore = useSegmentsStore();
const { createDelivery } = storeToRefs(segmentssStore);
const isExpandSetting = ref(false);
const oneDateTime = ref(dayjs().format('YYYY-MM-DD') + ' 23:00');
const isInitiallyRepeating = computed(() => {
  return segmentssStore.originalDistributionType === 'repeating';
});

watch(oneDateTime, (newDateTime) => {
  createDelivery.value.settings.postponedSettings.date = newDateTime.split(' ')[0];
  createDelivery.value.settings.postponedSettings.time = newDateTime.split(' ')[1];
/*   console.log(
    createDelivery.value.settings.postponedSettings,
    '   ',
    newDateTime.split(' ')
  ); */
});

watch(
  () => props.instant,
  (newVal) => {
    // console.log('🚀 ~ watch ~ newVal:', newVal);

    if (newVal) {
      createDelivery.value.settings.distributionType = 'instant';
    } else {
      createDelivery.value.settings.distributionType = 'postponed';
    }
  }
);

const distributionCategories = ref([
  { label: '毎日', value: 'Daily' },
  { label: '毎週', value: 'Weekly' },
  { label: '毎月', value: 'Monthly' },
  { label: '月末', value: 'LastDay' },
  { label: 'カスタム', value: 'Custom' },
]);

const daysOfWeekOptions = ref([
  { label: '月', value: 1 },
  { label: '火', value: 2 },
  { label: '水', value: 3 },
  { label: '木', value: 4 },
  { label: '金', value: 5 },
  { label: '土', value: 6 },
  { label: '日', value: 7 },
]);

const daysOfMonthOptions = computed(() => {
  return Array.from({ length: 31 }, (_, i) => i + 1).map((day) => ({
    label: day.toString() + '日',
    value: day,
  }));
});

const isValidDateTime = (datetime: string) => {
  const date = datetime.split(' ')[0];
  return segmentssStore.isValidDate(date);
};

const isValidDate = (date: string) => {
  return segmentssStore.isValidDate(date);
};

onBeforeMount(() => {
  createDelivery.value.settings.repeatingSettings = {
    fromDate: dayjs().format('YYYY-MM-DD'),
      toDate: dayjs().add(1, 'month').format('YYYY-MM-DD'),
      fromTime: '23:00',
      period: 'Daily', // Daily, Weekly, Monthly, LastDay, Custom
      withExclude: false,
      exclude: {
        daysOfWeek: [], // list of number 1-7 representing Monday to Sunday
        dates: [], // list of YYYY-MM-dd
      },
      daysOfWeek: [], // period=='Weekly' list of number 1-7 representing Monday to Sunday
      daysOfMonth: [], // period=='Monthly' list of days 1-31, when the month doesn't have this date -> don't send
      custom: {
        type: 'skip', // skip OR numberedDayOfWeek OR dates
        skip: {
          period: 'days', // days OR weeks OR months
          length: 1, // minimum 1. must be integer. max limit: 365 for days, 52 for weeks, 12 for months
        },
        numberedDayOfWeek: [
          {
            number: 1,
            dayOfWeek: [],
          },
          {
            number: 2,
            dayOfWeek: [],
          },
          {
            number: 3,
            dayOfWeek: [],
          },
          {
            number: 4,
            dayOfWeek: [],
          },
          {
            number: 5,
            dayOfWeek: [],
          },
        ], // list of objects {dayOfWeek: 1-7(1=Monday...5=Friday...), number: 1-5(which week 1=第1...)}
        dates: [], // list of YYYY-MM-dd
      },
  };
});

onMounted(() => {
  // in case it is a new delivery, reset the distribution type
  segmentssStore.originalDistributionType = undefined;

  if (props.instant) {
    createDelivery.value.settings.distributionType = 'instant';
  } else {
    createDelivery.value.settings.distributionType = 'postponed';
  }

  if (
    createDelivery.value.settings.distributionType === 'postponed' &&
    createDelivery.value.settings?.postponedSettings?.date
  ) {
    oneDateTime.value =
      createDelivery.value.settings.postponedSettings.date +
      ' ' +
      createDelivery.value.settings.postponedSettings.time;
  }
});

watch(
  () => createDelivery.value.settings.postponedSettings,
  () => {
    if (
      createDelivery.value.settings.distributionType === 'postponed' &&
      createDelivery.value.settings?.postponedSettings?.date
    ) {
      oneDateTime.value =
        createDelivery.value.settings.postponedSettings.date +
        ' ' +
        createDelivery.value.settings.postponedSettings.time;
    }
  }
);
</script>