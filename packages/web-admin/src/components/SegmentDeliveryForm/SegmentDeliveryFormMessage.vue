<template>
  <div>
    <div class="tw-mb-2">メッセージ</div>
    <div>
      <q-list v-if="createDelivery?.messages?.length" bordered class="rounded-borders">
        <div v-for="message in createDelivery?.messages" :key="message.id">
          <q-item class="tw-w-full tw-flex-1 tw-flex-row tw-items-center">
            <q-item-section avatar top>
              <q-icon
                :name="getMessageTypeObject(message?.contents?.type)?.icon"
                color="black"
                size="30px"
              />
            </q-item-section>

            <q-item-section top class="col-3 gt-sm">
              <q-item-label>
                {{ getMessageTypeObject(message?.contents?.type)?.name }}
              </q-item-label>
            </q-item-section>

            <q-item-section side class="tw-ms-auto">
              <div class="text-grey-8 q-gutter-xs">
                <q-btn
                  class="gt-xs"
                  flat
                  dense
                  round
                  icon="delete"
                  @click="onDeleteMessage(message)"
                />
                <q-btn
                  class="gt-xs"
                  flat
                  dense
                  round
                  icon="mode_edit"
                  @click="onEditMessage(message)"
                />
                <!-- <q-btn flat dense round icon="drag_indicator" /> -->
              </div>
            </q-item-section>
          </q-item>
          <q-separator spaced />
        </div>
      </q-list>
    </div>
    <div class="tw-mt-2 tw-pl-3">
      <q-btn
        color="white"
        text-color="black"
        icon="add_circle_outline"
        label="追加"
        @click="onAddNewMessage"
        :disable="segmentssStore.createDelivery.messages?.length >= 5"
      />
    </div>

    <MessageSimulatorModal
      :isOpen="isOpenMessageSimulator"
      @close="isOpenMessageSimulator = false"
      @confirm="updateOrAddMessage"
    />
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash';
import { ref } from 'vue';
import { useSegmentsStore } from '@/stores/modules/segments';
import { storeToRefs } from 'pinia';
import MessageSimulatorModal from '../MessageSimulator/MessageSimulatorModal.vue';
import { Message } from '@/types/index';
import { useMessageSimulatorStore } from '@stores/modules/message-simulator';
const messageSimulatorStore = useMessageSimulatorStore();

const { getMessageTypeObject, message } = storeToRefs(messageSimulatorStore);

const segmentssStore = useSegmentsStore();
const { createDelivery } = storeToRefs(segmentssStore);

const isOpenMessageSimulator = ref(false);
const onAddNewMessage = () => {
  isOpenMessageSimulator.value = true;
};

const updateOrAddMessage = (message: Message) => {
  // console.log('🚀 ~ updateOrAddMessage ~ message:', message);
  // check if createDelivery.messages has message.id
  const index = createDelivery.value.messages?.findIndex((m) => m.id === message.id);
  if (index === -1) {
    createDelivery.value.messages.push(message);
  } else {
    createDelivery.value.messages[index] = message;
  }
  isOpenMessageSimulator.value = false;
};

const onEditMessage = (_message: Message) => {
  message.value = cloneDeep(_message);
  isOpenMessageSimulator.value = true;
};

const onDeleteMessage = (_message: Message) => {
  const index = createDelivery.value.messages.findIndex((m) => m.id === _message.id);
  createDelivery.value.messages.splice(index, 1);
};
</script>
