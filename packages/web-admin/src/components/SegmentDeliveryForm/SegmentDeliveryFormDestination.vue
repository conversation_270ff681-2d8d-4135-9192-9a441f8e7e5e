<template>
  <div>
    <div class="tw-mb-1">配信先</div>
    <div class="tw-flex tw-flex-col">
      <q-radio
        v-model="createDelivery.settings.surveyConditions.pickAll"
        :val="true"
        label="すべての友だち"
      />
      <q-radio
        v-model="createDelivery.settings.surveyConditions.pickAll"
        :val="false"
        label="配信先対象を設定"
      />
    </div>
    <q-list
      v-if="!createDelivery.settings.surveyConditions.pickAll"
      bordered
      class="tw-bg-gray-50 tw-mt-3 tw-rounded-lg tw-border-dashed tw-ml-3"
    >
      <q-expansion-item
        v-model="isExpandTargetSetting"
        icon="tune"
        label="絞り込み"
        :caption="
          createDelivery.settings.surveyConditions.conditions.length
            ? '設定済み'
            : '未設定'
        "
      >
        <q-card class="tw-bg-gray-50 tw-border-t">
          <q-card-section>
            <div class="tw-text-sm tw-font-semibold tw-mb-3">配信先対象の設定</div>
            <div>
              <div>帳票</div>
              <q-select
                v-model="createDelivery.settings.surveyConditions.surveyId"
                :rules="rules.deliverySurveyRule"
                class="tw-bg-white tw-pb-0"
                dense
                outlined
                use-input
                :options="surveyConfigsListFiltered"
                option-value="surveyId"
                option-label="surveyTitle"
                emit-value
                map-options
                :loading="loadings['fetchSurveyConfigsList']"
                :disabled="loadings['fetchSurveyConfigsList']"
                input-debounce="0"
                @filter="filterSurvey"
              />

              <div class="tw-mt-6">
                <q-card flat bordered class="tw-rounded-lg">
                  <q-table
                    :rows="selectedSurveyConfigSelecatbleSchema"
                    :columns="surveyTableColumns"
                    row-key="name"
                    no-data-label="データがありません"
                    class="tw-shadow-none"
                    :loading="loadings['fetchSurveyConfig']"
                    :pagination="{ rowsPerPage: 10 }"
                    top
                  >
                    <template v-slot:body-cell-options="props">
                      <q-td :props="props" class="!tw-whitespace-break-spaces">
                        <q-select
                          :model-value="
                            surveyConditions?.find((c: any) => c.itemKey === props.row.itemKey)?.conditionValues || []
                          "
                          @update:model-value="onUpdateCondition(props, $event)"
                          lazy-rules
                          dense
                          outlined
                          placeholder="帳票"
                          :options="props.row.options"
                          :multiple="props.row.type === 'checkboxes'"
                          class="tw-max-w-xs"
                        />
                      </q-td>
                    </template>
                  </q-table>
                </q-card>
                <div class="tw-py-4 tw-flex tw-flex-row tw-justify-between">
                  <q-btn
                    flat
                    color="white"
                    icon="rotate_left"
                    text-color="primary"
                    label="配信先対象をリセット"
                    @click="resetSurveyConditions"
                  />
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </q-expansion-item>
    </q-list>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash';
import { ref, onMounted, watch, onUnmounted } from 'vue';
import { useSegmentsStore } from '@/stores/modules/segments';
import { storeToRefs } from 'pinia';
import { useFormsStore } from '@/stores/modules/forms';

const formsStore = useFormsStore();
const {
  surveyConfigsListTypeSorted,
  loadings,
  selectedSurveyConfig,
  selectedSurveyConfigSelecatbleSchema,
  surveyConfigsListFiltered,
} = storeToRefs(formsStore);

const segmentssStore = useSegmentsStore();
const { createDelivery } = storeToRefs(segmentssStore);
const isExpandTargetSetting = ref(false);

const surveyConditions = ref();

const rules = ref({
  deliverySurveyRule: [(v: any) => (!!v && !createDelivery.value.settings.surveyConditions.pickAll) || '帳票を選択してください。'],
});

const onUpdateCondition = (props: any, value: any) => {
  const index = surveyConditions.value.findIndex((c: any) => c.itemKey === props.row.itemKey);
  let _value = value;
  // check if value is not an array, then convert it to an array
  if (!Array.isArray(value)) {
    _value = [value];
  }
  if (index === -1) {
    surveyConditions.value.push({
      itemKey: props.row.itemKey,
      conditionValues: _value,
      title: props.row.title,
      blockType: props.row.type,
    });
  } else {
    surveyConditions.value[index].conditionValues = _value;
  }

  onConfirmSurveyConditions();
};

const resetSurveyConditions = () => {
  surveyConditions.value = [];
};

const onConfirmSurveyConditions = async () => {
  createDelivery.value.settings.surveyConditions.conditions = cloneDeep(
    surveyConditions.value
  );
  const survConf = surveyConfigsListTypeSorted.value.find(
    (sc) => sc.surveyId === createDelivery.value.settings.surveyConditions.surveyId
  );
  if (survConf !== undefined) {
    createDelivery.value.settings.surveyConditions.surveyTitle = survConf.surveyTitle;
  }
  // const resp = await segmentssStore.getTargetStatistics()
};

onMounted(() => {
  formsStore.fetchSurveyConfigsList('list');
  if (createDelivery.value.settings.surveyConditions?.surveyId) {
    isExpandTargetSetting.value = true;
    surveyConditions.value = createDelivery.value.settings.surveyConditions.conditions;
  }
});

const filterSurvey = (val: any, update: any) => {
  update(() => {
    if (val === '') {
      surveyConfigsListFiltered.value = surveyConfigsListTypeSorted.value.filter((item) => 
        item.surveyStatus === "enable" && item.isAppending?.value !== true
      );
    }
    else {
      const needle = val.toLowerCase();
      const _filtered = surveyConfigsListTypeSorted.value.filter(
        (item) => item.surveyTitle.toLowerCase().indexOf(needle) > -1 
          && item.surveyStatus === "enable" 
          && item.isAppending?.value !== true
      );
      surveyConfigsListFiltered.value = _filtered;
      return _filtered;
    }
  });
};

const surveyTableColumns = [
  {
    name: 'itemKey',
    field: 'itemKey',
    label: 'アイテムキー',
    align: 'left' as 'left' | 'right' | 'center',
    sortable: true,
    classes: 'tw-w-0',
  },
  {
    name: 'title',
    field: 'title',
    label: 'タイトル',
    align: 'left' as 'left' | 'right' | 'center',
    sortable: true,
    classes: 'tw-w-0',
  },
  {
    name: 'type',
    field: 'type',
    label: 'ブロック種別',
    align: 'left' as 'left' | 'right' | 'center',
    sortable: true,
    classes: 'tw-w-0',
  },
  {
    name: 'options',
    field: 'options',
    label: '配信条件',
    align: 'left' as 'left' | 'right' | 'center',
    sortable: false,
  },
];

watch(
  () => createDelivery.value.settings.surveyConditions?.surveyId,
  async (selectedSurveyId, oldSelectedSurveyId) => {
    if (selectedSurveyId) {
      await formsStore.fetchSurveyConfig(selectedSurveyId);
      if (oldSelectedSurveyId !== selectedSurveyId && oldSelectedSurveyId !== undefined) {
        surveyConditions.value = createDelivery.value.settings?.surveyConditions?.conditions;
      } else {
        surveyConditions.value = [];
      }

      isExpandTargetSetting.value = true;
    }
  },
  {
    immediate: true,
    deep: true,
  }
);

onUnmounted(() => {
  selectedSurveyConfig.value = {};
});
</script>
