<template>
  <div>
    <div>
      <div>カスタムの種類</div>
      <q-option-group
        v-model="createDelivery.settings.repeatingSettings.custom.type"
        :options="customTypeOptions"
        color="primary"
        inline
      />
    </div>
    <div v-if="createDelivery.settings.repeatingSettings.custom.type === 'skip'">
      <div>隔週の設定</div>
      <div class="tw-flex tw-justify-between">
        <q-option-group
          v-model="createDelivery.settings.repeatingSettings.custom.skip.period"
          :options="biweeklyOptions"
          color="primary"
          inline
        />
        <q-input
          class="tw-self-baseline"
          type="number"
          v-model="periodNum"
          min="1"
          max="365"
          @update:model-value="updatePeriodNum"
        />
      </div>
    </div>
    <div
      v-else-if="
        createDelivery.settings.repeatingSettings.custom.type === 'numberedDayOfWeek'
      "
    >
      <div>曜日の設定</div>
      <div class="tw-flex tw-flex-col tw-gap-2">
        <div
          v-for="(dayOfWeek, index) in createDelivery.settings.repeatingSettings.custom
            .numberedDayOfWeek"
          class="tw-flex tw-flex-row tw-gap-2 tw-items-baseline"
          :key="dayOfWeek.number"
        >
          <div class="tw-h-full">{{ `第${dayOfWeek.number}` }}</div>
          <BaseCheckboxButtonGroup
            class="tw-mt-2 tw-ml-2"
            v-model="
              createDelivery.settings.repeatingSettings.custom.numberedDayOfWeek[index]
                .dayOfWeek
            "
            :options="createFiveWeekOptions()"
          />
        </div>
      </div>
    </div>
    <div v-else-if="createDelivery.settings.repeatingSettings.custom.type === 'dates'">
      <div>カレンダーの設定</div>
      <q-date mask="YYYY-MM-DD" v-model="createDelivery.settings.repeatingSettings.custom.dates" multiple :options="isValidDate" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { useSegmentsStore } from '@/stores/modules/segments';
import { storeToRefs } from 'pinia';
import BaseCheckboxButtonGroup from '../base/BaseCheckboxButtonGroup.vue';
import { cloneDeep } from 'lodash';

const segmentssStore = useSegmentsStore();
const { createDelivery } = storeToRefs(segmentssStore);
const periodNum = ref(1);

const customTypeOptions = ref([
  { label: '隔週', value: 'skip' },
  { label: '曜日', value: 'numberedDayOfWeek' },
  { label: 'カレンダー', value: 'dates' },
]);

const biweeklyOptions = ref([
  { label: '日', value: 'days' },
  { label: '週', value: 'weeks' },
  { label: '月', value: 'months' },
]);

const createFiveWeekOptions = () => {
  return [
    { label: '月', value: 1 },
    { label: '火', value: 2 },
    { label: '水', value: 3 },
    { label: '木', value: 4 },
    { label: '金', value: 5 },
    { label: '土', value: 6 },
    { label: '日', value: 7 },
  ];
};

// initial value from store 
onMounted(async () => {
  periodNum.value = cloneDeep(createDelivery.value.settings.repeatingSettings.custom.skip.length);
});

// upd just in case there is a change in the settings
watch(
  [createDelivery],
  ([createDelivery]) => {
    if (createDelivery.settings.repeatingSettings.custom.type === 'skip') {
      periodNum.value = createDelivery.settings.repeatingSettings.custom.skip.length;
    }
  },
  { immediate: true, deep: true },
);

const updatePeriodNum = (value: number) => {
  createDelivery.value.settings.repeatingSettings.custom.skip.length = Number(value);
};

const isValidDate = (date: string) => {
  return segmentssStore.isValidDate(date);
};
</script>
