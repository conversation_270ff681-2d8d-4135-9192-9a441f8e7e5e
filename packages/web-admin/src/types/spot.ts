export type SpotAttributeDefinition = {
    id: string,
    label: string,
    attributeName: string,
    required: boolean
};

export type SpotAttributeValue = {
    id: string,
    value: string
};

export type SpotGroupTemplate = {
    id: string,
    name: string
};

export type SpotScenarioTriggerSettings = {
    locationSearchDistance: number,
    locationSearchText: string,
    locationSearchNotFoundText: string,
    listDisplayContinueText: string
}

export type SpotGroup = {
    partitionKey: string,
    sortKey: null | string,
    groupName: null | string,
    createdBy: null | string,
    updatedAt: null | string,
    updatedBy: null | string,
    spotTemplate: SpotGroupTemplate,
    scenarioTriggerSettings: SpotScenarioTriggerSettings,
    attributes: Array<SpotAttributeDefinition>,
    urlAttribute: SpotAttributeDefinition,
    tags: string[]
};

export type Spot = {
    attributes: Array<SpotAttributeValue>,
    urlAttribute: SpotAttributeValue,
    tags: string[],
    image: string,
    csvSource: string,
    latitude: null | number,
    longitude: null | number
};

export type SpotRoute = {
    listName: string,
    spots: Array<Spot>
}