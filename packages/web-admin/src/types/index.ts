export interface OptionsModel {
  value: string | null,
  text: string,
}

export interface ErrorModel {
    message: string;
    name: string;
    code: number | string;
}

export interface ScenarioMindmapSpecialTalk {
    listName: string,
    talkName: string,
    value: {
        eventType: string;
        scenario: string;
        talkName: string;
        mindmapId: number,
        type: string,
        value: string,
        postbackId: string, 
    }[],
}

export interface ScenarioTalk {
    value: string,
    scenario: string,
    dataId: string,
    params: {
        name: string,
        editor: string,
        messages: {
            messageId: number,
            sender: string,
        }[],
    },
    versionId?: string,
    versionOf?: string,
    versionActive?: string,
    dataType?: string
}

export interface ScenarioTextmap {
  textMapping: {
    [key: string]: any,
  },
}

export interface ScenarioMessage {
    talk: string,
    dataId: string,
    params: {
      [key: string]: any,
    },
    dataType: string,
    userInput: any,
}

export interface FetchPaymentServiceListPayload  {
    fields?: string | null;
    lastEvaluatedKey?: string | null;
    reservationServiceType?: 0 | 1 | 2;
}

export interface FetchProductCategoryList {
    lastEvaluatedKey?: string | null,
    serviceId: string,
}

export interface ScenarioDistributionTalks {
    environment: string,
    talks: string[],
}

export interface ReminderConfiguration {
  categoryId: string,
  fromDate: string,
  id: string,
  reminderType: string,
  surveyId: string,
  toDate: string,
}

export interface SearchSettings {
    properties: {
      value: string,
    }[],
}

export interface FormSchema {
    isAppending: {
        value: string;
    },
    key: string,
    surveyTitle: string,
}

export interface SelectedSurvey {
    surveyTitle: string,
    surveyType: string,
    surveyId: string,
    memberFormId: string,
    usePayment: number,
    isAppending: {
        value: string,
    },
    isDeleteAllEnabled: {
        value: string;
    }
    surveySchema: {
        type: string,
        input: string,
        default: string,
        itemKey: string,
        title: string,
        sectionOptions: [],
        options: [],
        selectedLargeCategory: {
            name: string;
        },
        selectedMediumCategory: {
            name: string;
        },
    }[],
}

export interface CommonSettings {
    logoUrl: string,
    menuStyle: string,
    maxHeightLogo: string,
    maxWidthLogo: string,
    primaryColor: string,
    tenant: string,
    themes: {
        primaryColor: {
            color: string;
        },
        secondaryColor: {
            color: string;
        },
        infoColor: {
            color: string;
        },
        warningColor: {
            color: string;
        },
        errorColor: {
            color: string;
        },
        successColor: {
            color: string;
        },
        contentBgColor: {
            color: string;
        },
        headerBgColor: {
            color: string;
        },
        accentColor: {
            color: string;
        },
    },
}

export interface PermissionsFromTeam {
    isAdministrator: boolean,
    groups: string[],
    permissions: any[],
    teamIds: any[],
}

export interface CognitoUser {
    username: string;
    signInUserSession: { 
        idToken: any; 
        accessToken: any; 
        refreshToken: any;
    };
}

export interface PublicConfiguration {
    versions: {
        general: string,
    },
}

export interface PolicySettings {
    minLength: number;
    numbers: boolean;
    specialSymbols: boolean;
    uppercase: boolean;
    lowercase: boolean;
    validityDays: string;
}

export interface GridCols {
    label: number;
    input: number;
    custom: 4;
    full: 12;
}

export interface UserDetail {
    userId: string;
    userName: string;
    enabled: boolean;
    email: string;
    status: string;
    updatedAt: string;
    createdAt: string;
}
export interface UserGroups {
    groupName: string;
}

export interface TeamList {
    teamName: string;
    teamId: string;
}

export interface UserDitail {
    userName: string;
}

export interface AllCalendarCategories {
    id: string;
}

export interface AllCategoriesTree {
    tree: any[];
}

export interface CalendarDisplaySettings {
    tag1: string;
    tag2: string;
    tag3: string;
}

export interface InputCalender {
    date: string | null;
    placeholder?: string;
    isMinimal?: boolean;
    isOutline?: boolean;
    label?: string;
    limit?: {
       dateBefore: any;
       dateAfter: any;
    };
    rule?: any;
}

export interface SelectedSurveyResults {
    userId: string,
    partitionKey: string,
}

export interface DisplaySettings {
    headers: {
        name: string,
        label: string,
        field: string,
        align: 'left' | 'right' | 'center',
    }[],
    itemsPerPage: number,
}

export interface CalendarValue {
    label: string,
    key: string,
    selectValues: {
        startDate: string,
        startTime: string,
        selectDate: string,
        selectTime: string,
        selectYoubi: string,
        selectKey: string,
        confirmTime: string,
        activeComaIndex: number,
        defaultDate: string,
    },
}

export interface ReservationItemInfo {
    id: number,
    data: {
        cost: number,
        categoryId: string,
        itemId: string,
        sortKey: string,
        itemName: string,
    }[],
}

export interface AllCalendars {
    sortKey: string,
    comaList: {
        start: string,
        end: string,
    }[],
}

export interface PaymentService {
    sortKey: string,
    serviceSortKey: string,
}

export interface ColumnModel {
    value: string,
    text: string,
}

export interface CalendarsList {
    sortKey: string,
    reservationStart: number,
    reservationControlType: number,
    startDate: string,
    calendarOff: number,
}

export interface ScenariosList {
    scenarioId: string,
    versions: [],
}

export interface ActiveScenario {
    scenarioId: string,
    versions: {
        [key: string]:{
            displayVersionName: string,
            updateDate: string,
            specialTalks: {
                [key: string]: { value: string, },
            }
        }
    },
}

export interface ActiveScenarioData {
    activeScenarioId: string,
    envMapping: any,
    richMenus: any,
    scenarioId: string,
    bosaiMode: any,
    bosaiSearchRadius: any,
    bosaiOpenShelterOnly: boolean,
    // {
    //     production: string,
    //     sandbox: string,
    // }[],
}

export interface RichMenu {
    size: {
        width: number,
        height: number | null,
    },
    selected: boolean,
    name: string,
    chatBarText: string,
    areas: any,
    richMenuId?: string,
}

export interface DisplayingCategory {
    calendarId: string,
    id: string,
}

export interface TargetDate {
    date: string,
    time: string,
    youbi: string,
    key: string,
    confirmTime: string,
    activeComaIndex: number,
    defaultDate: string,
    fullDate: string,
    year: number,
    displayDate: string,
    day: number,
}

export interface ReminderExecutionHistories {

}

export interface SurveyConfigs {
  surveyId: string,
  surveyTitle: string,
  surveySchema: {
    type: string,
    selectedLargeCategory: {
      name: string,
    },
    selectedMediumCategory: {
      name: string,
    },
  }[],
}

////////////////////////
// {
//   "createdByName": "Admin",
//   "deliveryStartedAs": "auto-repeating",
//   "deliveriesCount": 0,
//   "createdAt": "2023-09-01T15:00:00Z",
//   "configurationId": "test-repeating-config",
//   "createdBy": "admin",
//   "state": "NOT_STARTED",
//   "distributionName": "Test repeating delivery for",
//   "distributionType": "repeating",
//   "id": "73e63a19-cbe1-4d53-890c-fd20c59232df",
//   "targetsCount": 0,
//   "targetsSelectionType": "broadcast"
// }
export interface DeliveryRow {
    id: string
    distributionName: string
    distributionType: string
    deliveryStartedAs: string
    deliveriesCount: number
    targetsCount: number
    targetsSelectionType: string
    state: string
    createdAt: string
    createdBy: string
    createdByName: string
  }
  
  export interface LoadHistoryPayload {
    fromDate: string
    toDate: string
    limit?: number
    continueToken?: string
  }
  
  export interface ListExternalConfigs {
    surveyId?: string
    limit?: number
    enabled?: boolean
    continueToken?: string
  }
  
  export interface TalkConfig {
    targetSelectionType: string
    createdAt: string
    updatedBy: string
    updatedAt: string
    enabled: boolean
    distributionName: string
    distributionType: string
    id: string
    isDraft: boolean
    environment?: string
    talkName?: string
    mailTriggerSettings: {
      condition: {
        bodyExtractionCondition: string
        bodyTest: string
        subjectTest: string
        subjectExtractionCondition: string
      }
      content: {
        bodyChangeCondition: string
        bodyTest: string
      }
    }
    talkSettings: {
      environment: string
      talkId: string
      talkName: string
      useDisasterRichmenu: boolean
    }
  }
  export interface MailConfig {
    targetSelectionType: string
    createdAt: string
    updatedBy: string
    updatedAt: string
    enabled: boolean
    distributionName: string
    distributionType: string
    id: string
    isDraft: boolean
    surveyConditions?: {
      conditions: Array<MailCondition>
      surveyId: string
    }
    mailTriggerSettings: {
      condition: {
        bodyExtractionCondition: string
        bodyTest: string
        subjectTest: string
        subjectExtractionCondition: string
      }
      content: {
        bodyChangeCondition: string
        bodyTest: string
      }
    }
  }
  interface MailCondition {
    conditionValues: Array<string>
    itemKey: string
  }
  export interface MailFilterCondition {
    distributionName: string
    surveyId: string
    enabled: boolean | undefined
  }
  
  export interface TalkFilterCondition {
    distributionName: string
    talkName: string
    enabled: boolean | undefined
  }
  
  export interface EditConfig {
    settings: {
      targetSelectionType: string
      createdAt: string
      updatedBy: string
      updatedAt: string
      enabled: boolean
      distributionName: string
      distributionType: string
      id: string
      isDraft: boolean
      mailTriggerSettings: {
        condition: {
          bodyExtractionCondition: string
          bodyTest: string
          subjectTest: string
          subjectExtractionCondition: string
        }
        content: {
          bodyChangeCondition: string
          bodyTest: string
        }
      }
      surveyConditions?: {
        conditions: Array<MailCondition>
        surveyId: string
      }
      talkSettings?: {
        environment: string
        talkId: string
        talkName: string
        useDisasterRichmenu: boolean
      }
      repeatingSettings?: {
        fromDate: string
        toDate: string
        fromTime: string
        period: string
        withExclude: boolean
        exclude: {
          daysOfWeek: Array<string>
          dates: Array<string>
        }
        daysOfWeek: Array<string>
        daysOfMonth: Array<string>
        custom: {
          type: string
          skip: {
            period: string
            length: number
          }
          numberedDayOfWeek: Array<any>
          dates: Array<string>
        }
      }
    }
    messages: [
      {
        contents: {
          type: string
          text: string
        }
        id: string
      },
    ]
  }
  export interface ActionItem {
    actionType: 'メッセージアクション' | 'URIアクション'
    text: string
    x: number
    y: number
    width: number
    height: number
  }
  
  export interface Action {
    type: string
    text?: string
    linkUri?: string
    area: {
      height: number
      width: number
      x: number
      y: number
    },
    selected?: boolean
    id?: string
  }
  
  export interface MessageContents {
    type: string
    text?: string
    template?: any
  
    // flex-message
    json?: object
    isJson?: boolean
  
    // image
    originalContentUrl?: string
    previewImageUrl?: string
  
    // image map
    altText?: string
    baseUrl?: string
    actions?: Array<Action>
    baseSize?: {
      height: number
      width: number
    },
    file?: any
  }
  
  export interface Message {
    id: string
    contents: MessageContents
  }
  
  export interface DeliveryCustomNumberedDayOfWeek {
    number: number
    dayOfWeek: number
  }
  
  export interface DeliveryCustomNumberedDayOfWeekFrontend {
    number: number
    dayOfWeek: number[]
  }
  
  export interface DeliveryRepeatSettings {
    fromDate: string
    toDate: string
    fromTime: string
    period: string
    withExclude: boolean
    exclude: {
      daysOfWeek: number[],
      dates: string[],
    };
    daysOfWeek: number[]
    daysOfMonth: number[]
    custom: {
      type: string
      skip: {
        period: string
        length: number
      }
      numberedDayOfWeek: any[]
      dates?: string[]
    };
  }
  
  export interface DeliverySettings {
    targetSelectionType: string
    distributionName: string
    distributionType: string
    repeatingSettings: DeliveryRepeatSettings
    postponedSettings: {
      date: string
      time: string
    }
    surveyConditions: {
      surveyId: string
      pickAll: boolean
      conditions: any[]
      surveyTitle: string,
    }
    id?: number
    updatedAt?: string
  }
  
  export interface CreateDelivery {
    messages: any[],
    settings: DeliverySettings
  }
  
  export interface DeliveryObj {
    messages: any[]
    settings: DeliverySettings
  }
  
  export interface DateRelativeReminderSettingsItem {
    dateItemKey: string,
    reminderType: string,
    settings: [
      {
        daysAfter: number,
        messages: [
          {
            text?: string,
            file?: any,
            error?: {
              value: boolean,
              message: string,
            },
            originalContentUrl?: string,
            previewImageUrl?: string,
            type: 'text' | 'image',
          }
        ],
        monthsAfter: number,
        reminderLocalId: string,
        yearsAfter: number,
      }
    ],
    surveyId: string,
  }