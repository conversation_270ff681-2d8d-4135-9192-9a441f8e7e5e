/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { API } from 'aws-amplify'
import { DamageReportChatApiName } from './amplify.service'


/**
*
* @param {*} payload : {gid: line-user-id as string}
*/

export const getMessages = async (payload) => {
  return await API.post(DamageReportChatApiName, '/admin/chat/list-message', {
    body: payload,
  });
};

export const getGroup = async (payload) => {
  return API.get(DamageReportChatApiName, '/admin/chat/group', {
    queryStringParameters: payload,
  });
};

/**
* @param {*} payload : {
  "id": "U092f6795be76deb44efe82eb8dbc17d2"  as line-user-id (string),
  "gid":"U092f6795be76deb44efe82eb8dbc17d2" as line-user-id (string),
  "role":"line-user" as string,
}
*/
export const createGroup = async (payload) => {
  return await API.post(DamageReportChatApiName, '/admin/chat/group', {
    queryStringParameters: payload,
  });
};

export const updateUnReadMessage = async (payload) => {
  return await API.put(DamageReportChatApiName, '/admin/chat/unread', {
    body: payload,
  });
};

/**
* description: uploadFile || uploadImage
*
* @params {*} payload : {expiresIn: number, key: string, file_name: string, method: 'GET' | 'PUT', content_type?: 'image' | 'file'}
*/

export const getPresignedUrl = async (payload) => {
  return await API.post(DamageReportChatApiName, '/admin/utils/presigned-chat', {
    body: payload,
  });
};


export const pushMsgChatBot = async (payload) => {
  return await API.post(DamageReportChatApiName, '/admin/message/push', {
    body: payload,
  });
};

interface BatchGroupCheckPayload {
  gid: string[],
  uid: string
}

export const GetBatchGroupAdminReadStatus = async (payload: BatchGroupCheckPayload) => {
  return API.post(DamageReportChatApiName, '/admin/chat/batch-group', {
    body: payload,
  });
};