import { API } from "aws-amplify";
import { PlatformAdminApi, Prefix } from "./amplify.service";

export const GetCategoriesTrees = async () => {
  return await API.get(PlatformAdminApi, "/survey/calendars/categories_tree?include_display_settings", {});
};
export const GetAllCalendars = async () => {
  return await API.get(PlatformAdminApi, "/survey/calendars/get_calendars", {});
};

export const GetAllCalendarCategories = async () => {
  return await API.get(PlatformAdminApi, "/survey/calendars/categories", {});
};

export const CreateCalendarCommaCSV = async (calendarId) => {
  try {
    let _response = await API.post(
      PlatformAdminApi,
      "/survey/calendars/create_comma_csv/" + encodeURIComponent(calendarId),
      {}
    );

    return _response;
  } catch (error: any) {
    throw error;
  }
};

export const ImportComaCSV = async (calendarId, comaValues) => {
  return await API.post(PlatformAdminApi, "/survey/calendars/import_comma/" + encodeURIComponent(calendarId), {
    body: {
      coma: comaValues,
    },
  });
};

export const GetCalendarInfo = async (id) => {
  return await API.get(PlatformAdminApi, "/survey/calendars/info/" + encodeURIComponent(id), {});
};

export const UpdateCalendarCategory = async (payload) => {
  return await API.post(PlatformAdminApi, "/survey/calendars/info/" + encodeURIComponent(payload.sortKey), {
    body: payload,
  });
};

export const GetCalendarSchedules = async (start_day, end_day, id) => {
  return await API.get(PlatformAdminApi, "/survey/calendars/schedules/" + encodeURIComponent(id), {
    queryStringParameters: {
      start_day: start_day,
      end_day: end_day,
    },
  });
};

export const UpdatedCalendarSchedules = async (bodyInfo, id) => {
  return await API.post(PlatformAdminApi, "/survey/calendars/schedules/" + encodeURIComponent(id), {
    body: {
      schedule: bodyInfo,
    },
  });
};

export const DeleteCalendar = async (id) => {
  return await API.del(PlatformAdminApi, "/survey/calendars/info/" + encodeURIComponent(id), {});
};

export const ImportCsvCalendarSchedules = async (category, schedule, coma) => {
  let _responseCalendar:any = null;
  let calendarIdParaPath = category.calendarId ? "/" + encodeURIComponent(category.calendarId) : "";
  // カレンダーが存在しない場合、新規カレンダーを作成する
  if (!category.calendarId) {
    _responseCalendar = await API.post(PlatformAdminApi, "/survey/calendars/info" + calendarIdParaPath, {
      body: {
        name: category.name,
        category_id: category.id,
      },
    });
    calendarIdParaPath = _responseCalendar.id ? "/" + encodeURIComponent(_responseCalendar.id) : "";
  }

  let _responseSchedule = await API.post(
    PlatformAdminApi,
    "/survey/calendars/schedules" + calendarIdParaPath + "?mode=check_before",
    {
      body: {
        schedule: schedule,
        coma: coma,
      },
    }
  );

  return {
    responseCalendar: _responseCalendar,
    responseSchedule: _responseSchedule,
  };
};

export const UpdateDayOff = async (payload) => {
  return await API.post(
    PlatformAdminApi,
    "/survey/calendars/update_day_off/" + encodeURIComponent(payload.calendarId),
    {
      body: payload,
    }
  );
};

export const UpdateCalendarOff = async (payload) => {
  return await API.post(
    PlatformAdminApi,
    "/survey/calendars/update_calendar_off/" + encodeURIComponent(payload.calendarId),
    {
      body: payload,
    }
  );
};

export const DownloadCalendarCsv = async (payload) => {
  return await API.get(
    PlatformAdminApi,
    "/survey/calendars/download_calendar_csv/" + encodeURIComponent(payload.categoryId),
    {
      queryStringParameters: {
        startDay: payload.startDay,
        endDay: payload.endDay,
      },
    }
  );
};

/**
 * ユーザーの予約状況をチェック
 */
export const checkReservation = async (payload) => {
  try {
    return await API.post(PlatformAdminApi, "/" + Prefix + "/survey/admin/results/checkReservation", {
      body: payload,
    });
  } catch (err: any) {
    return err;
  }
};

export const getReservationControlInfo = async (calendarId) => {
  try {
    return await API.get(PlatformAdminApi, "/survey/calendars/control_info/" + encodeURIComponent(calendarId), {});
  } catch (err: any) {
    return err;
  }
};

export const upsertReservationControlInfo = async (params) => {
  try {
    return await API.post(PlatformAdminApi, "/survey/calendars/control_info", {
      body: params,
    });
  } catch (err: any) {
    return err;
  }
};

export const getReservationItemInfo = async (categoryId) => {
  try {
    return await API.get(PlatformAdminApi, "/survey/calendars/item_info/" + encodeURIComponent(categoryId), {});
  } catch (err: any) {
    return err;
  }
};

export const upsertReservationItemInfo = async (params) => {
  try {
    return await API.post(PlatformAdminApi, "/survey/calendars/item_info", {
      body: params,
    });
  } catch (err: any) {
    return err;
  }
};

export const GetSingleCategory = async (category_id) => {
  try {
    const params = {
      queryStringParameters: {},
    };
    let result = await API.get(PlatformAdminApi, "/survey/calendars/categories/" + encodeURIComponent(category_id), params);
    return result;
  } catch (err: any) {
    // alert(err);
    return err;
  }
};

export const UpdateCategoriesOrder = async (categoriesTree) => {
  return await API.put(PlatformAdminApi, "/survey/calendars/categories_order", {
      body: categoriesTree,
    }
  );
};

export const GetReservationPaymentItemsInfo = async (payload) => {
  return await API.get(
    PlatformAdminApi,
    `/calendars/reservation-payment-items/${encodeURIComponent(payload.categoryId)}`,
    {
      queryStringParameters: {
        include_deleted: payload.isIncludeDeleted ? true : false,
      }
    }
  )
}

export const upsertCalenderPaymentItems = async (payload) => {
  try {
    return await API.post(PlatformAdminApi, "/calendars/calender-payment-items", {
      body: payload,
    });
  } catch (err: any) {
    return err;
  }
};
