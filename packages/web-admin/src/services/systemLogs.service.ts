import { API } from "aws-amplify";
import { PlatformAdminApi } from "./amplify.service";
const Prefix = 'platform/system'

const SearchSystemLogs = async (startDate, endDate) => {
  const init: any = {
    queryStringParameters: {},
  };
  if (startDate) {
    init.queryStringParameters.start = startDate;
  }
  if (endDate) {
    init.queryStringParameters.end = endDate;
  }

  return await API.get(PlatformAdminApi, "/" + Prefix + "/system-logs", init);
};

interface IFrontendActionLog {
  type: 'download-system-log',
  path: string;
  method: 'GET' | 'PUT' | 'POST' | 'DELETE' | 'PUT',
  params?: {
    parameter1?: string | number;
    parameter2?: string | number;
    parameter3?: string | number;
    parameter4?: string | number;
    parameter5?: string | number;
  };
}
interface ITalkLogsActionLog {
  type: 'talklog-system-log',
  path: string;
  method: 'GET' | 'PUT' | 'POST' | 'DELETE' | 'PUT',
  params?: {
    parameter1?: string | number;
    parameter2?: string | number;
    parameter3?: string | number;
    parameter4?: string | number;
    parameter5?: string | number;
  };
}

const UploadFrontendActionLog = async(payload: IFrontendActionLog): Promise<void> => {
  return await API.post(PlatformAdminApi, "/" + Prefix + "/system-logs/record",
    {
      body: payload,
    }
  );
}

const UploadFrontendTalkLog = async(payload: ITalkLogsActionLog): Promise<void> => {
  return await API.post(PlatformAdminApi, "/" + Prefix + "/system-logs/record",
    {
      body: payload,
    }
  );
}
export {
  SearchSystemLogs,
  UploadFrontendActionLog,
  UploadFrontendTalkLog,
  IFrontendActionLog,
  ITalkLogsActionLog
}
