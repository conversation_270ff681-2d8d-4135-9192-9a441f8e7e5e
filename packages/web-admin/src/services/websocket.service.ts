import { Auth } from 'aws-amplify';
import ReconnectingWebSocket from 'reconnecting-websocket';
import { CHAT_EVENTS, WS_EVENT } from '../constants/chat.constants';

class WebSocketService {
  endpoint_ws_chat: string | undefined;
  listeners: unknown;
  token: string;
  send: string;
  websocketUrl: string;
  socket: ReconnectingWebSocket;
  constructor() {
    this.endpoint_ws_chat = import.meta.env.VITE_DAMAGE_REPORT_CHAT_WS_ENDPOINT;
    this.listeners = {};
  }

  async initWebSocket() {
    this.token = await this.getTokensCogito();
    this.send = 'manager';
    this.websocketUrl = `${this.endpoint_ws_chat}?token=${this.token}&from=${this.send}`;
    this.socket = new ReconnectingWebSocket(this.websocketUrl, [], {
      maxRetries: 3
    });
    this.socket.addEventListener('open', this.handleOpen.bind(this));
    this.socket.addEventListener('message', this.handleMessage.bind(this));
    this.socket.addEventListener('close', this.handleClose.bind(this));
    this.socket.addEventListener('error', this.handleError.bind(this));
  }

  async getTokensCogito() {
    const token = (await Auth.currentSession()).getIdToken().getJwtToken()
    return token
  }

  sendMessage(message) {
    try {
      if (this.socket) {
        this.socket.send(JSON.stringify(message));
      } else {
        console.error('WebSocket connection is not open.');
      }
    } catch (error) {
      console.error('Error sending message via WebSocket:', error);
    }
  }

  on(event, callback) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }

  off(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(listener => listener !== callback);
    }
  }

  emit(event, payload) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(listener => {
        listener(payload);
      });
    }
  }

  handleOpen(event) {
    console.log('WebSocket connection opened:', event);
  }

  handleMessage(event) {
    try {
      const message = JSON.parse(event.data);
      console.log('WebSocket message parsed:', message);
      if (message.event_name === CHAT_EVENTS.SEND_MESSAGE) {
        this.emit(CHAT_EVENTS.SEND_MESSAGE, message.payload);
        return
      } else if (message.event_name === WS_EVENT.READ_MESSAGE) {
        this.emit(CHAT_EVENTS.READ_MESSAGE, message.payload);
        return;
      }

      this.emit(CHAT_EVENTS.NOTIFY_TYPE, message);


    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }

  handleClose(event) {
    console.log('WebSocket connection closed:', event);
  }

  handleError(event) {
    console.error('WebSocket error:', event);
  }

  close() {
    this.socket?.close();
  }
}

export default WebSocketService;
