import { API } from "aws-amplify";
import { ScenarioApiName } from "./amplify.service";

const ReadFileAsync = async (file) => {
  if (typeof file === "string" && file.startsWith("data:")) {
    const innerResp = await fetch(file);
    return innerResp.arrayBuffer();
  }
  return new Promise((resolve, reject) => {
    var reader = new FileReader();

    reader.onload = () => {
      resolve(reader.result);
    };

    reader.onerror = reject;

    reader.readAsArrayBuffer(file);
  });
};

const CLOUDFRONT_DIST = import.meta.env.VITE_SCENARIO_CLOUDFRONT_DOMAIN_NAME;
const GET_PRESIGNED_URL = "/scenario/api/presignedURL";
export const UploadImageToS3 = async (fileData, url, folname) => {
  var errorGettingPresignedURL = false;

  let contentType = fileData.type;
  if (typeof fileData === "string" && fileData.startsWith("data:")) {
    contentType = fileData
      .replace(/^data:/, "")
      .split(";")
      .shift();
  }

  const presignedUrlResponse = await API.get(ScenarioApiName, GET_PRESIGNED_URL, {
    queryStringParameters: {
      folderName: folname,
      objectName: url,
      contentType: contentType,
    },
  })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      console.error(error);
      errorGettingPresignedURL = true;
      return error;
    });
  if (errorGettingPresignedURL) {
    throw presignedUrlResponse;
  }
  if (presignedUrlResponse.result === "ERROR") {
    throw new Error(presignedUrlResponse.exception);
  }
  var s3UploadUrl = presignedUrlResponse.url;
  var s3UploadFields = presignedUrlResponse.fields;
  var errorReadingFile = false;
  const blob: any = await ReadFileAsync(fileData)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      errorReadingFile = true;
      return error;
    });
  if (errorReadingFile) {
    throw blob;
  }

  var errorImportingToS3 = false;
  const form = new FormData();
  for (const field in s3UploadFields) {
    form.append(field, s3UploadFields[field]);
  }
  form.append("file", new Blob([blob]));
  const s3Response = await fetch(s3UploadUrl, {
    method: "POST",
    body: form,
  })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      errorImportingToS3 = true;
      return error;
    });
  if (errorImportingToS3) {
    throw s3Response;
  }
  s3Response["cloudFrontDist"] = CLOUDFRONT_DIST;
  return s3Response;
};

// SERVICE list for SightseeingSpot
const GET_SPOT_GROUP_LIST = "/spot/spotGroups";
export const getSpotList= async () => {
  const response = await API.get(ScenarioApiName, GET_SPOT_GROUP_LIST)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
};

const SPOT_GROUP_PREVIEW = "/spot/spotGroups/preview"
export const getSpotGroupPreview = async (payload) => {
    const response = await API.post(ScenarioApiName, SPOT_GROUP_PREVIEW, {
      body: payload
    })
      .then((response) => {
        return response;
      })
      .catch((error) => {
        throw new Error(error);
      });
  
    return response;
  };

const POST_SPOT_GROUP_LIST = "/spot/spotGroups";
export const postSpotGroupList= async (payload) => {
  const response = await API.post(ScenarioApiName, POST_SPOT_GROUP_LIST,{
    body: payload
  })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
};

export const getSpotGroup= async (id) => {
  const response = await API.get(ScenarioApiName, POST_SPOT_GROUP_LIST + '/' + id)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
};

export const updateSpotGroup= async (id, payload) => {
  const response = await API.put(ScenarioApiName, POST_SPOT_GROUP_LIST + '/' + id,{
    body: payload
  })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
};


export const deleteSpotGroup= async (id) => {
  const response = await API.del(ScenarioApiName, POST_SPOT_GROUP_LIST + '/' + id)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
};

const POST_SPOT_LIST = '/spot/spots'
export const createSpot = async(payload) => {
  const response = await API.post(ScenarioApiName, POST_SPOT_LIST,{
    body: payload
  })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
}

export const listSpots = async(spotGroupId) => {
  const response = await API.get(ScenarioApiName, POST_SPOT_LIST,{
    queryStringParameters: {
      spotGroupId: spotGroupId
    }
  })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
}

export const getSpotbyId = async(id, spotGroupId) => {
  const response = await API.get(ScenarioApiName, POST_SPOT_LIST + '/' + id,{
    queryStringParameters: {
      spotGroupId: spotGroupId
    }
  })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
}

export const updateSpotbyId = async(id, payload) => {
  const response = await API.put(ScenarioApiName, POST_SPOT_LIST + '/' + id,{
    body: payload
  })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
}

const DELETE_SPOT_LIST = '/spot/spots/delete'
export const deleteSpots = async(payload) => {
  const response = await API.post(ScenarioApiName, DELETE_SPOT_LIST ,{
    body: payload
  })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
}


const POST_CSV_FILE = '/spot/spots/import/csv'
export const uploadCSV = async(payload) => {
  const response = await API.post(ScenarioApiName, POST_CSV_FILE ,{
    body: payload
  })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
}

export const uploadUpdateCSV = async(payload) => {
  const updatePayload = { ...payload, isUpdate: true };
  const response = await API.post(ScenarioApiName, POST_CSV_FILE ,{
    body: updatePayload
  })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
}

const GET_EXPORT_CSV = '/spot/spots/export/csv'
export const exportSpotsCSV = async(spotGroupId) => {
  const response = await API.get(ScenarioApiName, GET_EXPORT_CSV, {
    queryStringParameters: {
      spotGroupId: spotGroupId
    }
  })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
}

const GET_CSV_STATUS = '/spot/spots/import/csv/status/'
export const getCsvStatus = async(id) => {
  const response = await API.get(ScenarioApiName, GET_CSV_STATUS + '/' + id)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
}

const UPLOAD_BULK_IMAGES = '/spot/spots/import/images'
export const uploadBulkImages = async(payload) => {
  const response = await API.post(ScenarioApiName, UPLOAD_BULK_IMAGES , {
    body: payload
  })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
}

const CREATE_ROUTE = '/spot/spotLists'
export const createRoute = async(payload) => {
  const response = await API.post(ScenarioApiName, CREATE_ROUTE + '/' + payload.spotGroupId , {
    body: payload
  })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
}

export const listRoutes = async(spotGroupId) => {
  const response = await API.get(ScenarioApiName, CREATE_ROUTE + '/' + spotGroupId )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
}

export const getRoutebyId = async(spotGroupId, spotId) => {
  const response = await API.get(ScenarioApiName, CREATE_ROUTE + '/' + spotGroupId + '/' + spotId )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
}

export const updateRoute = async(payload) => {
  const response = await API.put(ScenarioApiName, CREATE_ROUTE + '/' + payload.spotGroupId , {
    body: payload
  })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
}

export const deleteRoutes = async(groupId, payload) => {
  const response = await API.post(ScenarioApiName, CREATE_ROUTE + '/' + groupId  + '/delete', {
    body: payload
  })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw new Error(error);
    });

  return response;
}
