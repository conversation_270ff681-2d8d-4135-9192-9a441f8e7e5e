import { API } from "aws-amplify";
import { ApiName } from "./amplify.service";
import { PlatformAdminApi } from "./amplify.service";
import { generateUUID } from "../utils/uuidUtils";


export const UploadSurveyImageToS3 = async (payload) => {
  var headerFlg = false;
  const itemImgsChk = payload.surveySchema.filter(obj => obj.imageUrl && JSON.stringify(obj.imageUrl.file) === '{}');
  let itemImgs = itemImgsChk != undefined ? itemImgsChk : [];
  if (payload.headerImageUrl && JSON.stringify(payload.headerImageUrl.file) === '{}') {
    itemImgs.push({ 'imageUrl': { 'file': null } });
    itemImgs[itemImgs.length - 1].imageUrl.file = payload.headerImageUrl.file;
    headerFlg = true;
  }
  if (itemImgs.length != 0) {
    for (let i = 0; itemImgs.length > i; i++) {
      let s3ToUrl = await upLoadLoopS3(itemImgs[i]);
      if ((itemImgs.length - 1) == i && headerFlg) {
        payload.headerImageUrl = s3ToUrl;
      } else {
        itemImgs[i].imageUrl = s3ToUrl;
      }
    }
  }
};

export const upLoadLoopS3 = async (imgs) => {
  const GET_PRESIGNED_URL = "/survey/admin/configs/img";
  let folname = 'resources';
  let url = generateUUID();
  let contentType = imgs.imageUrl.file.type;

  // S3バケットの証明付きURLを取得
  const presignedUrlResponse = await API.get(PlatformAdminApi, GET_PRESIGNED_URL, {
    queryStringParameters: {
      folderName: folname,
      key: url,
      contentType: contentType,
    },
  });

  const s3ToUrl = presignedUrlResponse.objUrl + folname + '/' + url;
  var readError = false;
delete imgs.imageUrl.file.headerUrl;
  const blob: any = await ReadFileAsync(imgs.imageUrl.file)
  .then((response) => {
    return response;
  })
  .catch((error) => {
    readError = true;
    return error;
  });
  if (readError) {
    throw blob;
  }

  // blobでファイルアップロード
  var uploadError = false;
  var s3UploadUrl = presignedUrlResponse.urlLink;
  let s3Response = await fetch(s3UploadUrl, {
    method: "PUT",
    headers: {
      "Content-Type": contentType,
    },
    body: blob,
  })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      uploadError = true;
      return error;
    });
  if (uploadError) {
    throw s3Response;
  }
  return s3ToUrl;
};

export const ReadFileAsync = async (file) => {
  return new Promise((resolve, reject) => {
    var reader = new FileReader();
    reader.onload = () => {
      resolve(reader.result);
    };
    reader.onerror = reject;
    reader.readAsArrayBuffer(file);
  });
};

