import { API } from "aws-amplify";
import { axiosRetry } from "../services/helpers/axios-retry";
import { PlatformAdminApi, Prefix } from "./amplify.service";
import { SET_PARTIAL_DATA_OF_SELECTED_FORM } from "../stores/mutation-types";
import {
  SET_AUTO_RESERVATION_RESULT,
  REPLACE_IN_SURVEY_RESULTS_LIST,
} from "../stores/modules/surveyResults/mutations-types";

import { MULTIPLE_REQUEST_MESSAGE } from "../utils/errorMessageUtils";
import _ from 'lodash';

// ---------------------------- New Code ---------------------------- //
const BASE_PATH = "/" + Prefix;
const API_NAME = PlatformAdminApi;
class SurveyResultsService {
  async get(endpoint, params = null) {
    if (params) {
      return await API.get(API_NAME, BASE_PATH + endpoint, {
        queryStringParameters: params,
      });
    } else {
      return await API.get(API_NAME, BASE_PATH + endpoint, {});
    }
  }

  async post(endpoint, params = null) {
    return await API.post(PlatformAdminApi, BASE_PATH + endpoint, {
      body: params,
    });
  }

  async put(endpoint, params = null) {
    return await API.put(PlatformAdminApi, BASE_PATH + endpoint, {
      body: params,
    });
  }

  async delete(endpoint) {
    return await API.del(PlatformAdminApi, BASE_PATH + endpoint, {});
  }
}

export { SurveyResultsService };

// ---------------------------- Old Code ---------------------------- //
let cacheItems: any = {};

const processTryCatchError = (error, result: any = null) => {
  if (result === undefined || result === null) {
    result = {};
  }

  if (error.response) {
    if (
      error.response.status == 409 &&
      error.response.data &&
      error.response.data.code &&
      error.response.data.code === "temporary_locked"
    ) {
      result.errorMessage = MULTIPLE_REQUEST_MESSAGE;
    }
  }

  if (!(result.errorMessage && result.errorMessage === MULTIPLE_REQUEST_MESSAGE)) {
    result.errorMessage = error;
  }

  return result;
};

const getItems = async (configs, filter, commit, calendarCategories, reservationItemInfo, originalPayload) => {
  let surveyId = configs.surveyId;
  let shouldUseFilter = Object.values(filter).filter((s) => !_.isEmpty(s)).length > 0;

  //console.log("shouldUseFilter", shouldUseFilter);

  let items = [];
  let lastEvaluatedKey;
  let reachedMaxDisplayCount = false;
  do {
    let bodyParams = {
      lastEvaluatedKey: lastEvaluatedKey || 0,
    };
    if (shouldUseFilter) {
      bodyParams = {
        ...filter,
        ...bodyParams,
      };
    }
    let _response = await API.post(
      PlatformAdminApi,
      configs.surveyType === "normal" ? `/survey/admin/results/${surveyId}` : `/survey/admin/member-results/${surveyId}`,
      {
        body: bodyParams,
      }
    );
    const data = _response.data;
    if (data && data.items && data.items.length > 0) {
      items = items.concat(data.items);
      lastEvaluatedKey = data.lastEvaluatedKey;
    } else {
      break;
    }

    let payload = {
      configs: configs,
      results: items,
      commonSearchCriteria: filter,
      calendarCategories: calendarCategories,
      reservationItemInfo: reservationItemInfo,
      originalPayload: originalPayload,
      commit: commit,
    };
  } while (lastEvaluatedKey && !reachedMaxDisplayCount);
  return items;
};

export const ClearCacheItemsSurveyResults = () => {
  cacheItems = {};
};

const updateCacheItem = (items) => {
  if (!Array.isArray(cacheItems.items) || !Array.isArray(items) || items.length < 1) return;
  const partitionKey = items[0].partitionKey;
  const newItems:any[] = [];

  for (let i = 0; i < cacheItems.items.length; i++) {
    const item = cacheItems.items[i];
    if (item.partitionKey != partitionKey) {
      newItems.push(item);
    }
  }

  const updatedAt = Math.floor(Date.now() / 1000);
  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    item.updatedAt = updatedAt;
    newItems.push(item);
  }

  cacheItems.items = newItems;
};

const updateCacheItemAdditionType = (items, surveyId) => {
  if (!Array.isArray(cacheItems[surveyId]) || !Array.isArray(items) || items.length < 1) return;
  const partitionKey = items[0].partitionKey;
  const newItems:any[] = [];

  for (let i = 0; i < cacheItems[surveyId].length; i++) {
    const item = cacheItems[surveyId][i];
    if (item.partitionKey != partitionKey) {
      newItems.push(item);
    }
  }

  const updatedAt = Math.floor(Date.now() / 1000);
  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    item.updatedAt = updatedAt;
    newItems.push(item);
  }
  cacheItems[surveyId] = newItems;
};

export const getAllCalendarCategories = async () => {
  try {
    const result = await API.get(PlatformAdminApi, Prefix + "/survey/calendars/categories", {});
    return result;
  } catch (error: any) {
    console.error(error);
    throw error;
  }
};

const getSearchedAndSortedList = async (
  configs,
  searchCriteria,
  commit,
  calendarCategories,
  reservationItemInfo,
  originalPayload
) => {
  await getItems(configs, searchCriteria, commit, calendarCategories, reservationItemInfo, originalPayload);
};

export const GetPartialSurveyResults = async (payload) => {
  const {
    sortBy,
    sortDesc,
    searchKeyword,
    filterCommon,
    filterDate,
    commit,
    calendarCategories,
    reservationItemInfo,
  } = payload;

  try {
    if (payload.survey) {
      const searchCriteria = { filterCommon, filterDate, searchKeyword, sortBy, sortDesc };
      // console.log("filterCommon", filterCommon);
      await getSearchedAndSortedList(
        payload.survey,
        searchCriteria,
        commit,
        calendarCategories,
        reservationItemInfo,
        payload
      );
    }
  } catch (error: any) {
    throw error;
  }
};

export const GetPartialMemberSurveyResults = async (payload) => {
  const { sortBy, sortDesc, searchKeyword, filterCommon, filterDate, commit, calendarCategories, uniqueSearchId } = payload;

  try {
    if (payload.survey) {
      const searchCriteria = { filterCommon, filterDate, searchKeyword, sortBy, sortDesc };
      // console.log("filterCommon", filterCommon);
      return {
          results: await getItems(payload.survey, searchCriteria, commit, calendarCategories, {}, payload),
          configs: payload.survey,
          commonSearchCriteria: searchCriteria,
          originalPayload: payload,
        };
      }
  } catch (error: any) {
    throw error;
  }
  return [];
};

export const GetSelectedSurveyResults = async (payload) => {
  try {
    let _response = await API.get(
      PlatformAdminApi,
      `/survey/admin/results/${payload.survey.surveyId}/update_from/${payload.updateFrom}`,
      {}
    );

    const results:any[] = [];
    if (_response.result === "OK") {
      const { data } = _response;
      for (const user in data) {
        const row: any = {
          id: user,
        };
        row.createdAt = data[user][0].createdAt;
        row.updatedAt = data[user][0].updatedAt;
        row.check = data[user][0].check || "未対応";
        data[user].map((question) => {
          let _questionConfig = payload.survey.surveySchema.find((obj) => obj.itemKey === question.itemKey);
          if (_questionConfig && _questionConfig.type === "checkboxes") {
            if (!row[question.itemKey]) row[question.itemKey] = [];
            if (question.value) {
              row[question.itemKey].push(question.value);
            }
          } else {
            row[question.itemKey] = question.value;
          }
        });
        _.size(row) > 0 ? results.push(row) : null;
      }
    }
    return { results: results, count: results.length };
  } catch (error: any) {
    return error;
  }
};

export const UpdateMemberSurveyResult = async (payload) => {
  let defaultBodyPayLoadItem: any = [
    {
      surveyId: payload.surveyId,
      userId: payload.userId,
      check: payload.check,
      itemKey: null,
      title: null,
      value: null,
      userSearchKey: null,
      partitionKey: payload.partitionKey,
      sortKey: null,
      updatedAt: payload.surveyConfig.updatedAt,
      createdAt: payload.surveyConfig.createdAt,
    },
  ];

  let _bodyPayload: any = [];

  try {
    const { surveyId, userId, originalData, check, surveyResults, surveyConfig } = payload;

    let itemKeyToItemType = {};
    let checkboxesPayload: any = [];
    for (let i = 0; i < payload.surveyConfig.surveySchema.length; ++i) {
      let item = payload.surveyConfig.surveySchema[i];
      itemKeyToItemType[item.itemKey] = item.type;

      if (item.type === "checkboxes") {
        for (const option of item.options) {
          checkboxesPayload.push({
            itemKey: item.itemKey,
            sortKey: `${item.itemKey}#${option}`,
            title: item.title,
            userSearchKey: `${item.itemKey}#${option}#`,
            value: "",
          });
        }
      }
    }

    _bodyPayload = payload.surveyResults.map((item) => {
      if (itemKeyToItemType[item.itemKey] === "checkboxes") {
        const index = checkboxesPayload.map((o) => o.userSearchKey).indexOf(`${item.itemKey}#${item.value}#`);

        if (index > -1) {
          checkboxesPayload.splice(index, 1);
        }
      }
      return {
        ...item,
        userSearchKey:
          itemKeyToItemType[item.itemKey] === "checkboxes"
            ? `${item.itemKey}#${item.value}#${item.value}`
            : `${item.itemKey}#${item.value}`,
        partitionKey: payload.partitionKey,
        sortKey: `${item.itemKey}#${item.value}`,
        userId: userId,
        updatedAt: originalData.updatedAt,
        createdAt: originalData.createdAt,
        surveyId: surveyId,
        check: check,
      };
    });

    for (let item of checkboxesPayload) {
      _bodyPayload.push({
        itemKey: item.itemKey,
        title: item.title,
        value: item.value,
        userSearchKey: item.userSearchKey,
        partitionKey: payload.partitionKey,
        sortKey: item.sortKey,
        userId: userId,
        updatedAt: originalData.updatedAt,
        createdAt: originalData.createdAt,
        surveyId: surveyId,
        check: check,
      });
    }

    if (_bodyPayload.length == 0) {
      let surveySchema = payload.surveyConfig.surveySchema || payload.surveySchema || null;
      let item = { itemKey: null, title: null };
      if (surveySchema && surveySchema.length > 0) {
        item = {
          itemKey: surveySchema[0].itemKey,
          title: surveySchema[0].title,
        };
      }

      defaultBodyPayLoadItem = {
        ...item,
        value: "",
        userSearchKey: `${item.itemKey}#`,
        partitionKey: payload.partitionKey,
        sortKey: `${item.itemKey}#`,
        userId: userId,
        updatedAt: originalData.updatedAt,
        createdAt: originalData.createdAt,
        surveyId: surveyId,
        check: check,
      };
      _bodyPayload = [{ ...defaultBodyPayLoadItem }];
    }

    let _response = await axiosRetry(() =>
      API.put(PlatformAdminApi, `/survey/admin/member-results`, {
        body: _bodyPayload,
      })
    );
    let result: any = { id: userId };
    if (_response.result === "OK") {
      updateCacheItem(_bodyPayload);
      const { data } = _response;
      return parseDataToObject(data, surveyConfig);
    } else {
      ClearCacheItemsSurveyResults();
      result.errorMessage = _response.errorMessage;
      if (_response.result !== undefined) {
        result.result = _response.result;
      }
      if (_response.needRefresh !== undefined && _response.needRefresh !== null) {
        result.needRefresh = _response.needRefresh;
      }
      if (_response.alreadyUpdated) {
        result.data = parseDataToObject(_response.data, surveyConfig);
      }
      return result;
    }
  } catch (error: any) {
    let result = {}; // parseDataToObject(_bodyPayload, payload.surveyConfig);

    processTryCatchError(error, result);

    return result;
  }
};

export const UpdateSurveyResult = async (payload) => {
  let defaultBodyPayLoadItem: any = [
    {
      surveyId: payload.surveyId,
      userId: payload.userId,
      check: payload.check,
      itemKey: null,
      title: null,
      value: null,
      userSearchKey: null,
      partitionKey: payload.partitionKey,
      sortKey: null,
      updatedAt: payload.surveyConfig.updatedAt,
      createdAt: payload.surveyConfig.createdAt,
    },
  ];

  let _bodyPayload: any = [];

  try {
    const { surveyId, userId, originalData, check, surveyResults, surveyConfig } = payload;

    let itemKeyToItemType = {};
    let checkboxesPayload: any = [];
    for (let i = 0; i < payload.surveyConfig.surveySchema.length; ++i) {
      let item = payload.surveyConfig.surveySchema[i];
      itemKeyToItemType[item.itemKey] = item.type;

      if (item.type === "checkboxes") {
        for (const option of item.options) {
          checkboxesPayload.push({
            itemKey: item.itemKey,
            sortKey: `${item.itemKey}#${option}`,
            title: item.title,
            userSearchKey: `${item.itemKey}#${option}#`,
            value: "",
          });
        }
      }
    }

    _bodyPayload = payload.surveyResults.map((item) => {
      if (itemKeyToItemType[item.itemKey] === "checkboxes") {
        const index = checkboxesPayload.map((o) => o.userSearchKey).indexOf(`${item.itemKey}#${item.value}#`);

        if (index > -1) {
          checkboxesPayload.splice(index, 1);
        }
      }
      return {
        ...item,
        userSearchKey:
          itemKeyToItemType[item.itemKey] === "checkboxes"
            ? `${item.itemKey}#${item.value}#${item.value}`
            : `${item.itemKey}#${item.value}`,
        partitionKey: payload.partitionKey,
        sortKey: `${item.itemKey}#${item.value}`,
        userId: userId,
        updatedAt: originalData.updatedAt,
        createdAt: originalData.createdAt,
        surveyId: surveyId,
        check: check,
      };
    });

    for (let item of checkboxesPayload) {
      _bodyPayload.push({
        itemKey: item.itemKey,
        title: item.title,
        value: item.value,
        userSearchKey: item.userSearchKey,
        partitionKey: payload.partitionKey,
        sortKey: item.sortKey,
        userId: userId,
        updatedAt: originalData.updatedAt,
        createdAt: originalData.createdAt,
        surveyId: surveyId,
        check: check,
      });
    }

    if (_bodyPayload.length == 0) {
      let surveySchema = payload.surveyConfig.surveySchema || payload.surveySchema || null;
      let item = { itemKey: null, title: null };
      if (surveySchema && surveySchema.length > 0) {
        item = {
          itemKey: surveySchema[0].itemKey,
          title: surveySchema[0].title,
        };
      }

      defaultBodyPayLoadItem = {
        ...item,
        value: "",
        userSearchKey: `${item.itemKey}#`,
        partitionKey: payload.partitionKey,
        sortKey: `${item.itemKey}#`,
        userId: userId,
        updatedAt: originalData.updatedAt,
        createdAt: originalData.createdAt,
        surveyId: surveyId,
        check: check,
      };
      _bodyPayload = [{ ...defaultBodyPayLoadItem }];
    }

    let _response = await axiosRetry(() =>
      API.put(PlatformAdminApi, `/survey/admin/results`, {
        body: _bodyPayload,
      })
    );
    let result: any = { id: userId };
    if (_response.result === "OK") {
      updateCacheItem(_bodyPayload);
      const { data } = _response;
      return parseDataToObject(data, surveyConfig);
    } else {
      ClearCacheItemsSurveyResults();
      result.errorMessage = _response.errorMessage;
      if (_response.result !== undefined) {
        result.result = _response.result;
      }
      if (_response.needRefresh !== undefined && _response.needRefresh !== null) {
        result.needRefresh = _response.needRefresh;
      }
      if (_response.alreadyUpdated) {
        result.data = parseDataToObject(_response.data, surveyConfig);
      }
      return result;
    }
  } catch (error: any) {
    let result = {}; // parseDataToObject(_bodyPayload, payload.surveyConfig);

    processTryCatchError(error, result);

    return result;
  }
};

export const CheckReservable = async (payload) => {
  try {
    let _response = await API.post(PlatformAdminApi, `/survey/admin/results/check_reservable`, {
      body: payload,
    });
    if (_response.result === "WARNING") {
      return {
        check: false,
        warningMessage: _response.warningMessage,
      };
    }
    return { check: true };
  } catch (error: any) {
    console.error(error);
    return { check: true };
  }
};

export const UpdateSurveyResultAdditionType = async (payload, commit) => {
  payload["target"] = payload["target"] || false;
  payload["autoReservation"] = payload["autoReservation"] || false;

  let defaultBodyPayLoadItem: any = [
    {
      surveyId: payload.surveyId,
      userId: payload.userId,
      check: payload.check,
      target: payload.target,
      note: payload.note,
      answerCode: payload.answerCode,
      itemKey: null,
      title: null,
      value: null,
      userSearchKey: null,
      partitionKey: payload.partitionKey,
      sortKey: null,
      updatedAt: payload.surveyConfig.updatedAt,
      createdAt: payload.surveyConfig.createdAt,
    },
  ];

  let _bodyPayload: any = [];

  try {
    const { surveyId, userId, originalData, check, note, answerCode, target, surveyResults, surveyConfig } = payload;

    let itemKeyToItemType = {};
    let checkboxesPayload: any = [];
    for (let i = 0; i < payload.surveyConfig.surveySchema.length; ++i) {
      let item = payload.surveyConfig.surveySchema[i];
      itemKeyToItemType[item.itemKey] = item.type;

      if (item.type === "checkboxes") {
        for (const option of item.options) {
          checkboxesPayload.push({
            itemKey: item.itemKey,
            sortKey: `${item.itemKey}#${option}`,
            title: item.title,
            userSearchKey: `${item.itemKey}#${option}#`,
            value: "",
          });
        }
      }
    }

    _bodyPayload = payload.surveyResults.map((item) => {
      if (itemKeyToItemType[item.itemKey] === "checkboxes") {
        const index = checkboxesPayload.map((o) => o.userSearchKey).indexOf(`${item.itemKey}#${item.value}#`);

        if (index > -1) {
          checkboxesPayload.splice(index, 1);
        }
      }
      let _reservationKeyNew:any = null;
      let _reservationKeyOld:any = null;
      if (itemKeyToItemType[item.itemKey] === "reservation") {
        _reservationKeyNew = payload.bunrui_id && payload.bunrui_id.new ? payload.bunrui_id.new : null; // 選択した予約日時
        _reservationKeyOld = payload.bunrui_id && payload.bunrui_id.old ? payload.bunrui_id.old : null; // 元々の予約日時

        // 一時的な予約ありから予約なし設定対策チェック
        // -START-
        if (_reservationKeyNew && _reservationKeyOld &&
          _reservationKeyNew.indexOf('|') < 0 && _reservationKeyOld.indexOf('|') > 0) {
          throw '予約情報が不足しています。予約日時指定を行なってください。';
        }
        // -END-
      }

      const ret = {
        ...item,
        userSearchKey:
          itemKeyToItemType[item.itemKey] === "checkboxes"
            ? `${item.itemKey}#${item.value}#${item.value}`
            : `${item.itemKey}#${item.value}`,
        partitionKey: payload.partitionKey,
        sortKey: `${item.itemKey}#${item.value}`,
        userId: userId,
        updatedAt: originalData.updatedAt,
        createdAt: originalData.createdAt,
        surveyId: surveyId,
        check: check,
        note: note,
        answerCode: answerCode,
        target: payload["target"] || false,
      };
      if (_reservationKeyNew) {
        ret.reservationKeyNew = _reservationKeyNew;
        ret.reservationKeyOld = _reservationKeyOld;
      }
      return ret;
    });

    for (let item of checkboxesPayload) {
      _bodyPayload.push({
        itemKey: item.itemKey,
        title: item.title,
        value: item.value,
        userSearchKey: item.userSearchKey,
        partitionKey: payload.partitionKey,
        sortKey: item.sortKey,
        userId: userId,
        updatedAt: originalData.updatedAt,
        createdAt: originalData.createdAt,
        surveyId: surveyId,
        check: check,
        note: note,
        answerCode: answerCode,
        target: payload["target"] || false,
      });
    }

    if (_bodyPayload.length == 0) {
      let surveySchema = payload.surveyConfig.surveySchema || payload.surveySchema || null;
      let item = { itemKey: null, title: null };
      if (surveySchema && surveySchema.length > 0) {
        item = {
          itemKey: surveySchema[0].itemKey,
          title: surveySchema[0].title,
        };
      }

      defaultBodyPayLoadItem = {
        ...item,
        value: "",
        userSearchKey: `${item.itemKey}#`,
        partitionKey: payload.partitionKey,
        sortKey: `${item.itemKey}#`,
        userId: userId,
        updatedAt: originalData.updatedAt,
        createdAt: originalData.createdAt,
        surveyId: surveyId,
        check: check,
        note: note,
        answerCode: answerCode,
        target: payload["target"] || false,
      };
      _bodyPayload = [{ ...defaultBodyPayLoadItem }];
    }

    let _response = await axiosRetry(() =>
      API.put(PlatformAdminApi, `/survey/admin/results`, {
        body: {
          data: _bodyPayload,
          autoReservation: payload["autoReservation"],
        },
      })
    );

    let result: any = { id: userId };
    if (_response.result === "OK") {
      updateCacheItemAdditionType(_bodyPayload, surveyId);
      const { data } = _response;
      if (data.target === undefined) {
        data.target = payload.target || false;
      }
      return { ...parseDataToObject(data, surveyConfig), _response, paymentData: _response.paymentData };
    } else {
      ClearCacheItemsSurveyResults();
      result.errorMessage = _response.errorMessage;
      if (_response.result !== undefined) {
        result.result = _response.result;
      }
      if (_response.needRefresh !== undefined && _response.needRefresh !== null) {
        result.needRefresh = _response.needRefresh;
      }
      if (_response.alreadyUpdated) {
        if (_response.data.target === undefined) {
          _response.data.target = payload.target || false;
        }
        result.data = parseDataToObject(_response.data, surveyConfig);
      }
      return result;
    }
  } catch (error: any) {
    let result = {}; // parseDataToObject(_bodyPayload, payload.surveyConfig);

    processTryCatchError(error, result);

    return result;
  }
};

export const ResetLineUserId = async (payload) => {
  let _response = await API.post(PlatformAdminApi, `/survey/admin/results/reset-user-id`, {
    body: payload,
  });
  return _response;
};

const EditSurveyResultData = async (payload) => {
  let _bodyPayload: any = [];
  let itemKeyToItemType = {};
  let checkboxesPayload: any = [];

  for (let i = 0; i < payload.surveyConfig.surveySchema.length; ++i) {
    let item = payload.surveyConfig.surveySchema[i];
    itemKeyToItemType[item.itemKey] = item.type;

    if (item.type === "checkboxes") {
      for (const option of item.options) {
        checkboxesPayload.push({
          itemKey: item.itemKey,
          sortKey: `${item.itemKey}#${option}`,
          title: item.title,
          userSearchKey: `${item.itemKey}#${option}#`,
          value: "",
        });
      }
    }
  }

  _bodyPayload = payload.surveyResults.map((item) => {
    if (itemKeyToItemType[item.itemKey] === "checkboxes") {
      const index = checkboxesPayload.map((o) => o.userSearchKey).indexOf(`${item.itemKey}#${item.value}#`);
      if (index > -1) {
        checkboxesPayload.splice(index, 1);
      }
    }
    return {
      ...item,
      userSearchKey:
        itemKeyToItemType[item.itemKey] === "checkboxes"
          ? `${item.itemKey}#${item.value}#${item.value}`
          : `${item.itemKey}#${item.value}`,
      sortKey: `${item.itemKey}#${item.value}`,
      surveyId: payload.surveyId,
      note: payload.note,
    };
  });

  for (let item of checkboxesPayload) {
    _bodyPayload.push({
      itemKey: item.itemKey,
      title: item.title,
      value: item.value,
      userSearchKey: item.userSearchKey,
      sortKey: item.sortKey,
      surveyId: payload.surveyId,
      note: payload.note,
    });
  }

  return _bodyPayload;
};

const SurveyResultDataDefault = async (payload) => {
  let _bodyPayload: any = [];
  let surveySchema = payload.surveyConfig.surveySchema || payload.surveySchema || null;
  let item = { itemKey: null, title: null };
  if (surveySchema && surveySchema.length > 0) {
    item = {
      itemKey: surveySchema[0].itemKey,
      title: surveySchema[0].title,
    };
  }

  let defaultBodyPayLoadItem = {
    ...item,
    value: "",
    userSearchKey: `${item.itemKey}#`,
    sortKey: `${item.itemKey}#`,
    surveyId: payload.surveyId,
    note: payload.note,
  };
  _bodyPayload = [{ ...defaultBodyPayLoadItem }];

  return _bodyPayload;
};

const create = async (payload, path, commit) => {
  try {
    let _bodyPayload = await EditSurveyResultData(payload);

    if (_bodyPayload.length == 0) {
      _bodyPayload = await SurveyResultDataDefault(payload);
    }

    let _response = await axiosRetry(() =>
      API.post(PlatformAdminApi, path, {
        body: {
          data: _bodyPayload,
          autoReservation: payload["autoReservation"],
          paymentData: payload.paymentData,
        },
      })
    );

    let result: any = {};
    if (_response.result === "OK") {
      const { data, paymentData } = _response;

      return { ...parseDataToObject(data, payload.surveyConfig), _response, paymentResult: paymentData };
    } else {
      result.errorMessage = _response.errorMessage;
      if (_response.result !== undefined) {
        result.result = _response.result;
      }
      if (_response.needRefresh !== undefined && _response.needRefresh !== null) {
        result.needRefresh = _response.needRefresh;
      }
      return result;
    }
  } catch (error: any) {
    let result = {}; // parseDataToObject(_bodyPayload, payload.surveyConfig);
    processTryCatchError(error, result);
    return result;
  }
};

export const CreateSurveyResult = async (payload, commit) => {
  const path = `/survey/admin/results/${payload.surveyConfig.surveyId}/putItem`;
  return await create(payload, path, commit);
};

export const CreateMemberSurveyResult = async (payload, commit) => {
  const path = `/survey/admin/member-results/${payload.surveyConfig.surveyId}/putItem`;
  return await create(payload, path, commit);
};

export const CheckSaibanExist = async (payload) => {
  const path = `/survey/admin/member-results/${payload.surveyId}/checkSaibanExist`;

  let response = await API.get(PlatformAdminApi, path, {
    queryStringParameters: payload,
  });

  return response;
};

export const CheckSaibanExistForUpdate = async (payload) => {
  const path = `/member-results/${payload.surveyId}/checkSaibanExistForUpdate`;

  let response = await API.get(PlatformAdminApi, path, {
    queryStringParameters: payload,
  });

  return response;
};

const parseDataToObject = (data, surveyConfig) => {
  let result: any = {};
  result.id = data[0].userId;
  result.userId = data[0].userId;
  result.partitionKey = data[0].partitionKey;
  result.createdAt = data[0].createdAt;
  result.updatedAt = data[0].updatedAt;
  result.check = data[0].check || "未対応";
  result.target = data.target || false;
  if (surveyConfig && surveyConfig.isAppending && surveyConfig.isAppending.value == true) {
    result.note = data[0].note || "";
    result.answerCode = data[0].answerCode || "";
  }
  data.map((question) => {
    let _questionConfig = surveyConfig.surveySchema.find((obj) => obj.itemKey === question.itemKey);
    if (_questionConfig && _questionConfig.type === "checkboxes") {
      if (!result[question.itemKey]) result[question.itemKey] = [];
      if (question.value) {
        result[question.itemKey].push(question.value);
      }
    } else {
      result[question.itemKey] = question.value;
    }
  });
  return result;
};

export const DeleteSurveyResults = async (payload) => {
  const { page, itemsPerPage, sortBy, sortDesc, searchKeyword, filterCommon, filterDate, allColumns } = payload;

  let apiEndPointPath = payload.survey.surveyType !== "normal" ? "/survey/admin/member-results/" : "/survey/admin/results/";

  let surveyId = payload.survey.surveyId;

  try {
    let _response = await API.post(PlatformAdminApi, `${apiEndPointPath}${surveyId}/delete_survey_result`, {
      body: {
        page,
        itemsPerPage,
        sortBy,
        sortDesc,
        searchKeyword,
        filterCommon,
        filterDate,
        allColumns,
      },
    });

    // delete/reset cache
    cacheItems = {};

    const { data, totalCount } = _response;

    if (_response.result === "OK") {
      return { data: data, count: totalCount };
    }
  } catch (error: any) {
    return error;
  }
};

export const DeleteAllSurveyResults = async (payload) => {
  const { page, itemsPerPage, sortBy, sortDesc, searchKeyword, filterCommon, filterDate, surveysToDelete } = payload;
  try {
    let _response = await API.post(
      PlatformAdminApi,
      `/survey/admin/results/${payload.survey.surveyId}/delete_all_survey_results`,
      {
        body: {
          page,
          itemsPerPage,
          sortBy,
          sortDesc,
          searchKeyword,
          filterCommon,
          filterDate,
          surveysToDelete,
        },
      }
    );

    // delete/reset cache
    cacheItems = {};

    const { data, totalCount } = _response;

    if (_response.result === "OK") {
      return { data: data, count: totalCount };
    }
  } catch (error: any) {
    console.error(error);
    return error;
  }
};

export const CreateSurveyResultsCSV = async (payload) => {
  const { isMember, survey, page, itemsPerPage, sortBy, sortDesc, searchKeyword, filterCommon, filterDate, headers } = payload;
  try {
    let _response = await axiosRetry(() =>
      API.post(PlatformAdminApi, `/survey/admin/${isMember ? "member-" : ""}results/${payload.survey.surveyId}/create_csv`, {
        body: {
          page,
          itemsPerPage,
          sortBy,
          sortDesc,
          searchKeyword,
          filterCommon,
          filterDate,
          headers,
          survey,
          isMember
        },
      })
    );

    const { data } = _response;

    if (_response.result === "OK") {
      return { data: data };
    }
  } catch (error: any) {
    return error;
  }
};

export const CreateSurveyResultsCSVAppending = async (payload) => {
  const { headers, appendingData } = payload;

  try {
    const endpointName = (payload.survey.surveyType !== "member") ? "survey" : "member";
    const surveyId = payload.survey.surveyId;
    let _response = await axiosRetry(() =>
      API.post(PlatformAdminApi, `/survey/admin/${endpointName === "member" ? "member-" : ""}results/${surveyId}/create_csv_appending`, {
        params: {
          surveyId: surveyId,
        },
        body: {
          headers,
          appendingData,
        },
      })
    );

    const { data } = _response;

    if (_response.result === "OK") {
      return { data: data };
    }
  } catch (error: any) {
    return error;
  }
};

const SEND_SURVEY_UNICAST_ENDPOINT = "/distribution/message";
export const SendSurveyUnicast = async (payload) => {
  return await API.post(PlatformAdminApi, SEND_SURVEY_UNICAST_ENDPOINT, {
    body: {
      lineUid: payload.userId,
      messages: payload.messages,
    }
  });
};

export const GetImportCsvAppendingPresignedUrl = async (payload) => {
  const IMPORT_APPEND_TYPE_PRESIGNED_URL = "/survey/admin/results/" + payload.surveyId + "/getImportCsvAppendingPresignedUrl";
  const response = await API.get(PlatformAdminApi, IMPORT_APPEND_TYPE_PRESIGNED_URL, {
    queryStringParameters: {
      surveyId: payload.surveyId,
      bucketKey: payload.bucketKey,
      contentType: payload.contentType,
    },
  });
  // console.log("GetImportCsvAppendingPresignedUrl", response);
  return response;
};

const ReadFileAsync = async (file) => {
  return new Promise((resolve, reject) => {
    var reader = new FileReader();

    reader.onload = () => {
      resolve(reader.result);
    };

    reader.onerror = reject;

    reader.readAsArrayBuffer(file);
  });
};

export const UploadImportCsvAppendingToS3 = async (payload) => {
  const fileToUpload = payload.file;

  var errorReadingFile = false;
  const blob: any = await ReadFileAsync(fileToUpload)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      errorReadingFile = true;
      return error;
    });
  if (errorReadingFile) {
    throw blob;
  }

  let s3UploadError = false;
  let s3Response = await fetch(payload.presignedUrl, {
    method: "PUT",
    headers: {
      "Content-Type": payload.contentType,
    },
    body: blob,
  })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      s3UploadError = true;
      return error;
    });

  if (s3UploadError) throw s3Response; // to be caught as an error

  return s3Response; // to be returned as a success response
};

export const runValidationAndTransformationOnImportCsv = async (payload) => {
  const ASYNC_VALIDATE_APPENDING_SURVEY_RESULTS_CSV_ENDPOINT =
    "/survey/admin/results/" + payload.surveyId + "/validateCsvAppendingInvoker";
  return await axiosRetry(() =>
    API.post(PlatformAdminApi, ASYNC_VALIDATE_APPENDING_SURVEY_RESULTS_CSV_ENDPOINT, {
      body: {
        surveyId: payload.surveyId,
        bucketKey: payload.bucketKey,
        isSJISEncoding: payload.isSJISEncoding,
        surveyConfig: payload.survey,
      },
    })
  );
};

export const AsyncImportCsvAppendingInvoker = async (payload) => {
  const ASYNC_IMPORT_APPENDING_SURVEY_RESULTS_CSV_ENDPOINT =
    "/survey/admin/results/" + payload.surveyId + "/asyncImportCsvAppendingInvoker";
  return await axiosRetry(() =>
    API.post(PlatformAdminApi, ASYNC_IMPORT_APPENDING_SURVEY_RESULTS_CSV_ENDPOINT, {
      queryStringParameters: {
        surveyId: payload.surveyId,
        bucketKey: payload.bucketKey,
        fileName: payload.fileName,
      },
    })
  );
};

export const UploadImportedAppendTypeSurveyResultsCsv = async (payload) => {
  ClearCacheItemsSurveyResults();
  const IMPORT_APPEND_TYPE_SURVEY_RESULTS_CSV_ENDPOINT =
    "/survey/admin/results/" + payload.surveyId + "/import_csv_appending";
  return await axiosRetry(() =>
    API.post(PlatformAdminApi, IMPORT_APPEND_TYPE_SURVEY_RESULTS_CSV_ENDPOINT, {
      params: {
        surveyId: payload.surveyId,
      },
      body: {
        toUpdate: payload.toUpdate,
        toCreate: payload.toCreate,
      },
    })
  );
};

const ASYNC_MULTIPLE_UNICAST_INVOKER_ENDPOINT = "/distribution/instant/selected";
const ASYNC_MULTIPLE_UNICAST_INVOKER_ENDPOINT_FILTER = "/distribution/instant/filter";
/**
 * Start an instant delivery for selected targets or selected by filter.
 * Payload struct:
 *  - messages: Array of LINE messages structs
 *  - searchCondition: filter object, contains filterCommon, filterDate, filterCategories etc
 *  - surveyConfig: copy of SurveyConfig struct
 *  - title
 *  - type: searchCondition OR target
 */
export const AsyncMultipleUnicastInvoker = async (payload) => {
  const endpointPath = payload.type === 'searchCondition'
      ? ASYNC_MULTIPLE_UNICAST_INVOKER_ENDPOINT_FILTER
      : ASYNC_MULTIPLE_UNICAST_INVOKER_ENDPOINT
  return await API.post(PlatformAdminApi, endpointPath, {
    body: {
      ...payload,
    },
  });
};

export const FetchHomeNotificationData = async (payload) => {
  const FETCH_HOME_NOTIFICATION_DATA_ENDPOINT = "/survey/admin/home-notification/homeNotificationData/" + payload.surveyId;
  return await API.get(PlatformAdminApi, FETCH_HOME_NOTIFICATION_DATA_ENDPOINT, {
    params: payload,
  });
};

export const FetchReservationPaymentInfo = async (categoryId: string): Promise<any> => {
  return await API.get(PlatformAdminApi, `/calendars/reservation-payment-products/${encodeURIComponent(categoryId)}`, {});
}
