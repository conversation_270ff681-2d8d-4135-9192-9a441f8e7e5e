/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
import { API } from 'aws-amplify'
import { DamageReportApiName } from './amplify.service'

// #region scenario edit
export const CreateDamageReportTalkVersion = async (payload) => {
  return API.post(DamageReportApiName, `/scenario/damage-report/create-talk-version`, {
    body: payload
  })
}

export const SetDamageReportTalkActive = async (payload) => {
  return API.post(DamageReportApiName, `/scenario/damage-report/set-talk-active`, {
    body: payload
  })
}
export const SaveDamageReportTalkActive = async (payload) => {
  return API.post(DamageReportApiName, `/scenario/damage-report/save-talk`, {
    body: payload
  })
}

export const GetAllScenarioDataOfDR = async (scenarioId,  dataId) => {
  return API.get(DamageReportApiName, `/scenario/damage-report/talk-messages/${scenarioId}?dataId=${dataId}`, {})
}

export const DeleteDamageReportScenario = async (payload: {
  deleteItems: any[]
}) => {
  return API.del(DamageReportApiName, `/scenario/version`, {
    body: payload
  })
}
export const DeleteDamageReportScenarioTalkVersion = async (payload: {
  scenario: string
  versionId: string
}) => {
  return API.del(DamageReportApiName, `/scenario/damage-report/talk-version`, {
    body: payload
  })
}
// #endregion

// #region CRUD
interface QueryPayload {
  "lastEvaluatedKey"?: string // uuid
  "pageSize"?: number
}
export const GetAllDamageReports = async (queryPayload?: QueryPayload) => {
  return API.post(DamageReportApiName, `/admin/damage-report/query`, queryPayload ? { body: queryPayload } : {})
}

interface FilterPayload {
  "fromDate"?: string //date
  "toDate"?: string //date
  "userName"?: string
  "status"?: string
  "subject"?: string
  "scenarioId"?: string
  "lastEvaluatedKey"?: string //uuid
  "pageSize"?: number //
  "category"?: string
}
export const QueryFilterDamageReports = async (filterPayload: FilterPayload) => {
  return API.post(DamageReportApiName, `/admin/damage-report/filter`, {
    body: filterPayload
  })
}
export const CreateDamageReports = async (reportData) => {
  return API.post(DamageReportApiName, "/admin/damage-report", {
    body: reportData,
  })
}
export const GetDamageReportsByID = async (id: string) => {
  return API.get(DamageReportApiName, `/admin/damage-report/${id}`, {})
}
export const UpdateDamageReportsByID = async (id: string, reportData) => {
  return API.put(DamageReportApiName, `/admin/damage-report/${id}`, {
    body: reportData,
  })
}
export const DeleteDamageReportsByID = async (id: string) => {
  return API.del(DamageReportApiName, `/admin/damage-report/${id}`, {})
}

export const GetTalkRelationship = async (payload: {
  scenario: string
  talkDrVersionId: string
}) => {
  return API.post(DamageReportApiName, `/admin/utils/get-talk-relationship`, {
    body: payload
  })
}

export const GetScenarioFormConfig = async (payload: {
  scenario: string
  talkDrVersionId: string
}) => {
  return API.post(DamageReportApiName, `/admin/utils/get-scenario-config-form`, {
    body: payload
  })
}
// #endregion

// #region S3
interface PreSignedUploadPayload {
  "type": "image" | "file"
  "fileName": string
}
interface PreSignedDownloadPayload {
  fullKey: string
}

const getPresignedUploadUrl = (payload: PreSignedUploadPayload[]) => {
  return API.post(DamageReportApiName, "/admin/utils/upload-files", {
    body: payload
  })
}
export const UploadImagesS3 = async (files: HTMLInputElement["files"]) => {
  if (!files) return "No file!"

  let payload: PreSignedUploadPayload[] = []
  for (const file of files) {
    payload.push({
      type: "image",
      fileName: file.name
    })
  }

  const signedURL = await getPresignedUploadUrl(payload)
    .catch((error) => {
      return error
    })

  if (!Array.isArray(signedURL)) {
    return "Error getting presigned url"
  }

  let promises: Promise<any>[] = []
  let uploadResult: unknown[] = []
  for (let i = 0; i < files.length; i++) {
    let data = new FormData()
    for (const [key, val] of Object.entries(signedURL[i].fields)) {
      data.append(key, val as any)
    }
    data.append('Content-Type', files[i].type)
    data.append('file', files[i])
    promises.push(fetch(signedURL[i].url, {
      method: 'POST',
      body: data,
    })
      .then((response) => {
        if (response.ok) {
          uploadResult[i] = {
            key: signedURL[i].fullKey
          }
        }
      })
      .catch((error) => {
        return error
      }))
  }
  await Promise.all(promises)
  return uploadResult
}
const getPresignedDownloadUrl = (payload: PreSignedDownloadPayload[]) => {
  return API.post(DamageReportApiName, "/admin/utils/signed-files", {
    body: payload
  })
}
export const GetSignedImagesInfo = (payload: PreSignedDownloadPayload[]) => {
  return getPresignedDownloadUrl(payload)
}
// #endregion

// #region other
export const ExportReportToCSV = async (payload: {
  filter: FilterPayload,
  settings: any
}) => {
  return API.post(DamageReportApiName, `/admin/damage-report/export-csv`, {
    body: payload
  })
}
