import { API } from "aws-amplify";
import { PlatformAdminApi, Prefix } from "./amplify.service";
import { ReminderConfiguration } from "@/types/index";

// S3への画像ファイルアップロード
export const getPresignedUrlOfReminderImage = async (payload) => {
  return await API.get(PlatformAdminApi, '/' + Prefix + '/dist/reservation-reminders/presignedURL', {
    queryStringParameters: {
      key: payload.key,
      contentType: payload.contentType,
    },
  });
};

const ReadFileAsync = async (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      resolve(reader.result);
    };
    reader.onerror = reject;
    reader.readAsArrayBuffer(file);
  });
};

export const UploadReminderImageToS3 = async ({ file, contentType, presignedUrl }) => {
  const blob: any = await ReadFileAsync(file)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      console.error(error);
      return error;
    });

  const s3Response =
    await fetch(presignedUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': contentType,
      },
      body: blob,
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      console.error(error)
      return error;
    });
  return s3Response;
};

// リマインド配信の実行ログを取得(予約リマインド配信・日付リマインド配信共通で使用可能)
export const getExecutionHistories = async (payload: any) => {
  return await API.get(PlatformAdminApi, '/' + Prefix + '/dist/reservation-reminders/history', {
    queryStringParameters: {
      month: payload.month,
      partitionKey: payload.partitionKey,
      sortKey: payload.sortKey,
    }
  });
};

// 予約リマインド配信設定を取得
export const getReminderConfiguration = async (payload) => {
  return await API.get(PlatformAdminApi, '/' + Prefix + '/dist/reservation-reminders/settings/appointment', {
    queryStringParameters: {
      surveyId: payload.surveyId,
      categoryId: payload.categoryId,
      type: payload.type,
    }
  });
}

// リマインド配信設定を更新(予約リマインド配信・日付リマインド配信共通で使用可能)
export const updateReminderConfiguration = async (payload: ReminderConfiguration) => {
  return await API.put(PlatformAdminApi, '/' + Prefix + '/dist/reservation-reminders/settings', {
    body: payload,
  });
};

// リマインド配信設定を作成(予約リマインド配信・日付リマインド配信共通で使用可能)
export const  createReminderConfiguration = async (payload) => {
  return await API.post(PlatformAdminApi, '/' + Prefix + '/dist/reservation-reminders/settings', {
    body: payload,
  });
};

// 予約リマインド配信を削除
export const deleteReservationReminderConfiguration = async (configurationId) => {
  return await API.del(
    PlatformAdminApi, '/' + Prefix + '/dist/reservation-reminders/settings/' + encodeURIComponent(configurationId), {});
};


// 日付リマインド配信設定を削除
export const deleteDateReminders = async (reminderIds) => {
  return await API.post(PlatformAdminApi, '/' + Prefix + '/dist/reservation-reminders/reminders/delete', {
    body: reminderIds,
  });
};

// 日付リマインドを検索
export const findDateReminders = async (surveyId: string, dateItemKey: string) => {
  const basePath = '/' + Prefix + '/dist/reservation-reminders/settings/date_relative';
  return await API.get(PlatformAdminApi, basePath, {
    queryStringParameters: {
      surveyId,
      dateItemKey
    }
  });
}

