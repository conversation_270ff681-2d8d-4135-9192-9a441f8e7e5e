import { defineAbility } from "@casl/ability";
import {
  ACCESS_TAB_PERMISSION,
  HIDE_BUTTON_PERMISSION,
  DISABLE_BUTTON_PERMISSION,
} from "../constants/permission.constants";

export default defineAbility((can, cannot) => {
  can("access", "signin");
});

export const GetUserAbilities = async (userGroups: any) => {
  const defaultAbilities = {
    administrators: {
      access: [],
      hideButton: [],
      disableButton: [],
      click: ["backendRequest"],
    },
    admins: {
      access: [],
      hideButton: [],
      disableButton: [],
      click: ["backendRequest"],
    },
    members: {
      access: [],
      hideButton: [],
      disableButton: [],
      click: ["backendRequest"],
    },
    guests: {
      access: [],
      hideButton: [],
      disableButton: [],
      click: ["backendRequest"],
    },
    operators: {
      access: [],
      hideButton: [],
      disableButton: [],
      click: ["backendRequest"],
    },
  } as any;

  for (const [k, v] of Object.entries(ACCESS_TAB_PERMISSION)) {
    for (const role of v) {
      defaultAbilities[role]["access"].push(k);
    }
  }
  for (const [k, v] of Object.entries(HIDE_BUTTON_PERMISSION)) {
    for (const role of v) {
      defaultAbilities[role]["hideButton"].push(k);
    }
  }
  for (const [k, v] of Object.entries(DISABLE_BUTTON_PERMISSION)) {
    for (const role of v) {
      defaultAbilities[role]["disableButton"].push(k);
    }
  }

  defaultAbilities.administrators = {
    access: [...defaultAbilities.administrators.access, ...defaultAbilities.admins.access],
    hideButton: [...defaultAbilities.administrators.hideButton, ...defaultAbilities.admins.hideButton],
    disableButton: [...defaultAbilities.administrators.disableButton, ...defaultAbilities.admins.disableButton],
    click: [...defaultAbilities.administrators.click, ...defaultAbilities.admins.click],
  };

  if (
    userGroups.some((value: any) => {
      return value.indexOf("Administrator:admins") >= 0;
    })
  ) {
    return defaultAbilities.administrators;
  }
  if (
    userGroups.some((value: any) => {
      return value.indexOf("admins") >= 0;
    })
  ) {
    return defaultAbilities.admins;
  }
  if (
    userGroups.some((value: any) => {
      return value.indexOf("members") >= 0;
    })
  ) {
    return defaultAbilities.members;
  }
  if (
    userGroups.some((value: any) => {
      return value.indexOf("guests") >= 0;
    })
  ) {
    return defaultAbilities.guests;
  }
  if (
    userGroups.some((value: any) => {
      return value.indexOf("operators") >= 0;
    })
  ) {
    return defaultAbilities.operators;
  }
  return {};
};
