import fs from 'fs';
import path from 'path';
import ts from 'typescript';

import { default as generate } from '@babel/generator';
import * as babel from '@babel/core';
import * as parser from '@babel/parser';
import generator from '@babel/generator';

import * as t from '@babel/types';


const MAX_RETRIES = 4;
let retries = 0;
const rootFolder = 'web-admin';

export const findRoot = (currentDir: string = process.cwd()): string | null => {
    if(retries >= MAX_RETRIES) {
        return null;
    }
    
    // Check if the current directory is the target folder
    if (path.basename(currentDir) === rootFolder) {
        return currentDir;
    }

    // Check if we've reached the root of the filesystem
    const parentDir = path.resolve(currentDir, '..');
    if (currentDir === parentDir) {
        return null; // Stop if we've reached the root of the filesystem
    }

    // Check if "web-admin" exists in the current directory
    const targetPath = path.join(currentDir, rootFolder);
    if (fs.existsSync(targetPath) && fs.lstatSync(targetPath).isDirectory()) {
        return targetPath;
    }

    retries++;
    // Recursively traverse up to the parent directory
    return findRoot(parentDir);
}

// Helper function to copy directories recursively
export const copyDirectorySync = (srcDir: string, destDir: string) => {
    if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true }); // Create the destination directory
    }

    const entries = fs.readdirSync(srcDir, { withFileTypes: true });

    for (let entry of entries) {
        const srcPath = path.join(srcDir, entry.name);
        const destPath = path.join(destDir, entry.name);

        if (entry.isDirectory()) {
            copyDirectorySync(srcPath, destPath); // Recursively copy subdirectories
        } else {
            fs.copyFileSync(srcPath, destPath); // Copy files
        }
    }
}

export function toCamelCase(str) {
    return str
        .toLowerCase() // Convert the string to lowercase
        .split('_') // Split the string by underscores
        .map((word, index) => {
            // Capitalize the first letter of each word except the first one
            if (index === 0) return word; // Keep the first word in lowercase
            return word.charAt(0).toUpperCase() + word.slice(1); // Capitalize the first letter
        })
        .join(''); // Join the words back together
}

export function parsePath(dir: string): string[] {
    if(!fs.existsSync(dir)) {
        return [];
    }
    if(dir.endsWith('.vue')) {
        return [dir];
    }
    const result: string[] = [];
    
    // Read the contents of the directory
    const files = fs.readdirSync(dir);
    for (const file of files) {
        const fullPath = path.join(dir, file); // Use path.join for cross-platform compatibility
        
        if (fs.lstatSync(fullPath).isDirectory()) {
            // If it's a directory, recursively parse it for .vue files
            result.push(...parsePath(fullPath));
        } else if (file.endsWith('.vue')) {
            // If it's a .vue file, add it to the result
            result.push(fullPath);
        }
    }
    
    return result;
}

export function extractFunctionsFromSection(sectionContent: string): string[] {
    // Wrap the code in a dummy object to parse it correctly
    const sourceFile = ts.createSourceFile('temp.ts', `const obj = ${sectionContent}`, ts.ScriptTarget.Latest, true);
    const methods: string[] = [];

    let depth = 0; // Track the depth of the nested functions

    function visit(node: ts.Node) {
        // Increase depth when encountering a new function/method
        if (ts.isMethodDeclaration(node) || ts.isPropertyAssignment(node) || ts.isFunctionExpression(node) || ts.isArrowFunction(node)) {
            depth++;
        }

        // Only capture top-level functions (depth 1 indicates direct methods inside the dummy object)
        if (depth === 1 && (ts.isMethodDeclaration(node) || ts.isPropertyAssignment(node))) {
            const functionText = node.getText(sourceFile);
            methods.push(functionText);
        }

        // Recursively visit child nodes
        ts.forEachChild(node, visit);

        // Decrease depth after processing the node
        if (ts.isMethodDeclaration(node) || ts.isPropertyAssignment(node) || ts.isFunctionExpression(node) || ts.isArrowFunction(node)) {
            depth--;
        }
    }

    ts.forEachChild(sourceFile, visit);
    return methods;
}

export const mapProperties = (properties) =>{
    const props = {};
    properties.forEach(prop => {
      if (!prop.key) {
        return;
      }
      const key = prop.key.name; // Get property name

      
      if (t.isTSTypeLiteral(prop.typeAnnotation.typeAnnotation)) {
        // Handle nested structure by mapping inner properties
        const nestedProps = mapProperties(prop.typeAnnotation.typeAnnotation.members);
        props[key] = "Array"; // Return key-value pair
      } else  {
        const type = generate(prop.typeAnnotation).code.substring(2); // Extract type as string

        props[key] = type.toLowerCase() != "any" ? type[0].toUpperCase() + type.substring(1) : "any"; // Return key-value pair
      }
    });
    return props;
  }

export function formatCode(code: string): string {
    // Parse the code into an AST
    const ast = parser.parse(code, {
        sourceType: 'module',
        plugins: ['jsx', 'typescript'], // Add other plugins as needed
    });

    // Generate code from the AST
    const { code: formattedCode } = generator(ast, {
        retainLines: true, // Option to retain line numbers
        // other options you may want to set
    });

    return formattedCode;
}