{
  "files": ["src/types.d.ts"],
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*", "src/globals.d.ts"],
  "compilerOptions": {
    "types": ["@casl/ability", "node", "vite/client"],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@utils/*": ["./src/utils/*"],
      "@components/*": ["./src/components/*"],
      "@pages/*": ["./src/pages/*"],
      "@stores/*": ["./src/stores/*"],
      "@assets/*": ["./src/assets/*"],
      "@services/*": ["./src/services/*"],
      "@constants/*": ["./src/constants/*"],
    },
    "downlevelIteration": true,
    "lib": ["es2019", "dom"],
    "esModuleInterop": true
  },
}
