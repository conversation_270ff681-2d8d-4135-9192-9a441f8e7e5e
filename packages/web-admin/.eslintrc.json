{
  "root": true,
  "env": {
    "node": true
  },
  "plugins": [
    "@typescript-eslint"
  ],
  "parser": "vue-eslint-parser",
  "parserOptions": {
    "parser": "@typescript-eslint/parser"
  },
  "extends": [
    "plugin:vue/essential",
    "plugin:@typescript-eslint/eslint-recommended",
    "plugin:@typescript-eslint/recommended",
    "eslint:recommended"
    // "@vue/typescript"
  ],
  "rules": {
    "no-unused-vars": 0,
    "semi": ["warn", "always"],
    "max-depth": [
      1,
      5
    ],
    "max-statements": [
      1,
      20
    ],
    "prefer-const": "off",
    "no-var": "off",
    "no-case-declarations": "off",
    "no-prototype-builtins": "off",
    "no-useless-escape": "off",
    "no-irregular-whitespace": "off",
    "no-useless-catch": "off",
    "no-control-regex": "off",
    "no-self-assign": "warn",
    "no-empty": "warn",
    "no-inner-declarations": "warn",
    "no-undef": "warn",
    "vue/valid-v-slot": "warn",
    "vue/no-mutating-props": "warn",
    "vue/no-unused-vars": "warn",
    "vue/no-useless-template-attributes": "warn",
    "vue/multi-word-component-names": "warn",
    "vue/no-v-model-argument": "off",
    "@typescript-eslint/no-this-alias": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/no-unused-vars": "off",
    "@typescript-eslint/no-empty-function": "off",
    "@typescript-eslint/no-var-requires": "off",
    "@typescript-eslint/ban-ts-comment": "off",
    "@typescript-eslint/no-empty-interface": "off"
  },
  "overrides": [
    {
      "files": [
        "**/__tests__/*.{j,t}s?(x)",
        "**/tests/unit/**/*.spec.{j,t}s?(x)"
      ],
      "env": {
        "jest": true
      },
      "rules": {
        "@typescript-eslint/ban-ts-comment": "off"
      }
    }
  ]
}