#!/usr/bin/env bash
# 実行方法：./publish.sh prod <profile name (default is fch-dev)>
# Git Action -> publish.sh prod S3_BUCKET CLOUDFRONT_DIST ACCESS_KEY SECRET_ACCESS_KEY


D_ENV=${1:-'dev'} # 引数設定されてなければ 'dev' を設定する
if [ $# -eq 2 ]; then 
  D_PROFILE=${2:-'fcs-dev'} 
else
  D_PROFILE=""
fi
# Install in Git Action too
echo "D_ENV:${D_ENV}"
npm run build:${D_ENV}

if [[ $? != 0 ]]; then
    exit 1
fi

if [[ ${D_ENV} != "prod" ]]; then
  echo "Can deploy only production environment build"
  exit 0
fi

read_var() {
    if [ $# -eq 2 ]; then
      VAR=$(grep $1 $2 | xargs)
    else
      VAR=$(grep $1 $2 $3 | xargs)
    fi
    IFS="=" read -ra VAR <<< "$VAR"
    echo "${VAR[1]}"
}

if [ $# -gt 2 ]; then
  # FOR GITHUB ACTION SCRIPT
  VUE_APP_DEPLOY_AWS_BUCKET=${2}
  AWS_CLOUDFRONT_DISTRIBUTION_ID=${3}

  echo "aws s3 rm s3://${VUE_APP_DEPLOY_AWS_BUCKET}/ --recursive "
  aws s3 rm s3://${VUE_APP_DEPLOY_AWS_BUCKET}/ --recursive 

  echo "aws s3 sync ./dist s3://${VUE_APP_DEPLOY_AWS_BUCKET}/ --exclude *.css.map --exclude *.js.map "
  aws s3 sync ./dist s3://${VUE_APP_DEPLOY_AWS_BUCKET}/ --exclude "*.css.map" --exclude "*.js.map" 

  echo "aws cloudfront create-invalidation --distribution-id ${AWS_CLOUDFRONT_DISTRIBUTION_ID} --paths /* "
  aws cloudfront create-invalidation --distribution-id ${AWS_CLOUDFRONT_DISTRIBUTION_ID} --paths "/*" 

else
  echo "read from .env"
  VUE_APP_DEPLOY_AWS_BUCKET=$(read_var "ADMIN_WEB_BUCKET" ".env")
  AWS_CLOUDFRONT_DISTRIBUTION_ID=$(read_var "AWS_CLOUDFRONT_DISTRIBUTION_ID" ".env")


  aws s3 rm s3://${VUE_APP_DEPLOY_AWS_BUCKET}/ --recursive ILE} --profile ${D_PROFILE}

  aws s3 sync ./dist s3://${VUE_APP_DEPLOY_AWS_BUCKET}/ --exclude "*.css.map" --exclude "*.js.map"  --profile ${D_PROFILE}

  aws cloudfront create-invalidation --distribution-id ${AWS_CLOUDFRONT_DISTRIBUTION_ID} --paths "/*" --profile ${D_PROFILE}
fi

echo "Using bucket ${VUE_APP_DEPLOY_AWS_BUCKET}"
echo "Using CloudFront Distribution ${AWS_CLOUDFRONT_DISTRIBUTION_ID}"
echo "Using profile ${D_PROFILE}"