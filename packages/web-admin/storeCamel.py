import os
import re
import shutil

# Global array to store file paths
index_files = []

def search_index_files(directory, backup=False):
    index_files.clear()
    for root, dirs, files in os.walk(directory):
        if "stores" in root.split(os.path.sep):
            for file in files:
                if backup:
                    if file == "index.ts.bk":
                        index_files.append(os.path.join(root, file))
                else:
                    if file == "index.ts":
                        index_files.append(os.path.join(root, file))

def replaceVuexFunctions():
    print("-> Vuex関数探し中...")
    search_index_files(os.path.dirname(os.path.abspath(__file__)))
    print("-> 元のVuex関数をバックアップ中...")
    result = backupIndexFiles()
    if not result:
        print("\033[91m--> バックアップに失敗しました...終了します...")
        return
    
    print("-> Vuex関数を置換中...")
    for i, file_path in enumerate(index_files):
        print(f"\033[92m--> 置換 ({i}/{len(index_files)}): {file_path}\033[0m")
        with open(file_path, 'r') as file:
            file_data = file.read()

        lines = file_data.splitlines(keepends=True)
        section = ""  # Flag to indicate if "// actions" or "// mutations" has been found

        for i in range(len(lines)):
            line = lines[i]
            # Check for "// actions" and "// mutations" comments
            if re.search(r"//\s*mutations?", line):
                print("-> mutation")
                section = "mutations"
            elif re.search(r"//\s*actions?", line):
                print("-> action")
                section = "actions"
                
            # Replace all function names like "[FUNCTION_HERE_OK]" with camel case
            lines[i] = re.sub(
                r'\[([A-Z_]+)\]', 
                lambda match: ''.join([match.group(1).split('_')[0].lower()] + [word.capitalize() for word in match.group(1).split('_')[1:]]), 
                line
            )
            # Replace all function calls like "this.[FUNCTION_HERE_OK]" with camel case
            lines[i] = re.sub(
                r'this\.([A-Z_]+)', 
                lambda match: 'this.' + match.group(1).split('_')[0].lower() + ''.join(word.capitalize() for word in match.group(1).split('_')[1:]), 
                lines[i]  # Ensure we use lines[i] here
            )
            
            if section == "actions":
                # Replace all functions calls like "this.functionName = value;" with "this.functionName(value);"
                lines[i] = re.sub(
                    r'this\.([a-zA-Z_][\w]*)\s*=\s*(.*?)\s*;', 
                    lambda match: f'this.{match.group(1)}({match.group(2)});', 
                    lines[i]
                )

            # Replace function definitions without checking for control statements
            lines[i] = re.sub(
                r'([a-zA-Z_][\w]*)\(state,\s*([\w\s/]+)\)\s*{',
                lambda match: f'{match.group(1)}({match.group(2)}: any) {{',
                lines[i]
            )

            # Replace function definitions excluding those linked to control statements
            lines[i] = re.sub(
                r'(?<!\w)([a-zA-Z_][\w]*)\s*\(\s*([\w\s]+)\s*\)\s*{(?!\s*(if|while|catch|for)\s*\()',
                lambda match: f'{match.group(1)}({match.group(2)}) {{',
                lines[i]
            )

            # Replace all function calls like "state.functionName" with "this.functionName"
            lines[i] = re.sub(
                r'state\.([a-zA-Z_][\w]*)', 
                lambda match: f'this.{match.group(1)}', 
                lines[i]
            )

        # Join lines back into a single string
        file_data = ''.join(lines)

        # Write the modified data back to the file
        with open(file_path, 'w') as file:
            file.write(file_data)

    print("-> 置換完了")

def rollBack():
    print("-> ロールバック中...")
    search_index_files(os.path.dirname(os.path.abspath(__file__)), True)
    for i, file_path in enumerate(index_files):
        if file_path.endswith('.bk'):
            print(f"\033[94m--> ロールバック: {file_path}\033[0m")
            new_file_path = file_path[:-3]
            os.rename(file_path, new_file_path)
            #os.remove(file_path + '.bk')

def backupIndexFiles():
    search_index_files(os.path.dirname(os.path.abspath(__file__)))
    if len(index_files) == 0:
        print("\033[91m--> Indexファイルが見つかりません")
        return False
    for i, file_path in enumerate(index_files):
        backup_file_path = file_path + '.bk'
        print(f"\033[94m--> バックアップ ({i}/{len(index_files)}): {backup_file_path}\033[0m")
        shutil.copy2(file_path, backup_file_path)
    return True

def deleteAllBackups():
    search_index_files(os.path.dirname(os.path.abspath(__file__)), True)
    if len(index_files) == 0:
        print("\033[91m-> Indexファイルが見つかりません\033[0m")
        return
    for i, file_path in enumerate(index_files):
        if file_path.endswith('.bk'):
            print(f"\033[91m--> 削除: {file_path}")
            os.remove(file_path)

def display_menu():
    print("\033[1m--- Vuex関数置換ツール ---\033[0m")
    print("メニュー:")
    print("1. Vuex関数を置換")
    print("2. バックアップ")
    print("3. ロールバック")
    print("4. すべてのバックアップを削除")
    print("5. 出口")

def get_user_choice():
    choice = input("\033[92m入力: \033[0m")
    return int(choice)

def interactive_menu():
    while True:
        display_menu()
        choice = get_user_choice()
        
        if choice == 1:
            replaceVuexFunctions()
        elif choice == 2:
            backupIndexFiles()
        elif choice == 3:
            rollBack()
        elif choice == 4:
            deleteAllBackups()
        elif choice == 5:
            exit()
        else:
            print("Invalid option")

# Call the interactive menu function
interactive_menu()