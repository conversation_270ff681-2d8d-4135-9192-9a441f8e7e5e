{"name": "@oss/web-admin", "version": "0.0.1", "description": "Line smart city", "productName": "LINE OSS", "author": "PNL", "private": true, "scripts": {"lint": "eslint --ext .js,.ts,.vue ./", "lint-check": "eslint --ext .js,.ts,.vue --quiet", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build:local": "quasar build", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "format": "prettier .  --write", "migrate": "tsx migration/vue_script_upg.ts", "build": "node src/generate_dotenv.js && yarn run build:prod", "build:analyze": "cross-env BUNDLE_ANALYZE=true yarn run build", "build:prod": "quasar build", "deploy": "./publish.sh prod"}, "dependencies": {"@babel/parser": "^7.25.7", "@babel/traverse": "^7.25.7", "@casl/ability": "^6.7.1", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/vue-fontawesome": "^3.0.8", "@overlapmedia/imagemapper": "^2.0.6", "@quasar/extras": "^1.16.15", "@vueuse/core": "^10.2.1", "amazon-cognito-identity-js": "^6.3.3", "aws-amplify": "^5.3.8", "axios": "^1.2.1", "chardet": "^2.0.0", "chart.js": "^4.4.2", "compressorjs": "^1.2.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "decamelize": "^6.0.0", "epic-spinners": "^2.0.0", "iconv-lite": "^0.6.3", "jschardet": "^3.1.3", "json-editor-vue": "^0.15.0", "jszip": "^3.10.1", "less": "^4.2.1", "lodash": "^4.17.21", "luxon": "^3.5.0", "markdown-it": "^14.1.0", "md5": "^2.3.0", "mermaid": "^11.4.1", "mitt": "^3.0.1", "panzoom": "^9.4.3", "papaparse": "^5.4.1", "pinia": "^2.0.11", "quasar": "^2.17.5", "sass": "^1.83.0", "sortablejs": "^1.13.0", "tsx": "^4.19.1", "uuidv4": "^6.2.13", "vue": "^3.5.13", "vue-chartjs": "^5.3.1", "vue-codemirror": "^4.0.6", "vue-i18n": "^10.0.1", "vue-pdf-embed": "^1.1.6", "vue-router": "^4.0.0", "vue3-toastify": "^0.2.1", "yamljs": "^0.3.0"}, "devDependencies": {"@babel/preset-env": "^7.22.9", "@babel/preset-react": "^7.22.5", "@babel/preset-typescript": "^7.25.7", "@oss/web-shared": "workspace:packages/web-shared", "@quasar/app-vite": "^1.9.5", "@quasar/vite-plugin": "^1.7.0", "@storybook/addon-actions": "^7.1.1", "@storybook/addon-controls": "^7.1.1", "@storybook/addon-essentials": "^7.1.1", "@storybook/addon-interactions": "^7.1.1", "@storybook/addon-links": "^7.1.1", "@storybook/addon-mdx-gfm": "^7.1.1", "@storybook/addon-styling": "^1.3.5", "@storybook/blocks": "^7.1.1", "@storybook/testing-library": "^0.2.0", "@storybook/vue3": "^7.1.1", "@storybook/vue3-webpack5": "^7.1.1", "@types/chart.js": "^2.9.41", "@types/luxon": "^3.4.2", "@types/md5": "^2.3.5", "@types/node": "^12.20.55", "@types/papaparse": "^5.3.14", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "@vitejs/plugin-vue": "^2.3.4", "@vue/compiler-sfc": "^3.5.11", "@vue/eslint-config-typescript": "^12.0.0", "autoprefixer": "^10.4.20", "css-loader": "^6.8.1", "deep-equal": "^2.2.3", "eslint": "^8.10.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.19.1", "eslint-plugin-n": "^15.0.0", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-quasar": "^1.1.0", "eslint-plugin-storybook": "^0.6.13", "eslint-plugin-vue": "^9.28.0", "file-saver": "^2.0.5", "install-peers": "^1.0.4", "postcss": "^8.4.47", "prettier": "3.0.1", "raw-loader": "^4.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "reconnecting-websocket": "^4.4.0", "storybook": "^7.1.1", "tailwindcss": "^3.4.11", "typescript": "^5.1.6", "vite-plugin-raw": "^1.0.3", "vue-advanced-chat": "^2.1.0", "vue-i18n": "^11.0.1", "vue3-google-map": "^0.21.0"}, "engines": {"node": "^18 || ^16 || ^14.19", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}