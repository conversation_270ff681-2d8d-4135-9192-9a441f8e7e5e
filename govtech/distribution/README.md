# 配信一括登録スクリプト（手動や外部）

## 検証環境について

| 環境名                     | 分類 | AWS Profile |
|------------------------------|------|------|
| dev-oss    | 開発 / 検証  | dev-oss     |
| city-xyz  | 本番  | pnl  |
## CSV ファイル
### ファイル名とディレクト

| ディレクト                     | 内容 | 備考 |
|------------------------------|------|------|
| /govtech/distribution/csv    | 登録 / 削除用の CSV     |      |
| /govtech/distribution/log    | 自動生成された登録のログ（エラー確認用）      |      |
| /govtech/distribution/script | 登録 / 削除の実行スクリプト   |      |
| /govtech/distribution/script/payload | 自動生成された登録のリクエストパラメータ     |      |

<em>log と payload のファイル名は `<importId>` に基づいて名付けています。　</em>

### ヘッダー
以下のヘッダーのありなしを確認：
- distributionName	
- condition[<設定項目>]	
- messages[<1...5>]		
- distributionType	
- recurringSettings[fromDate]	
- recurringSettings[toDate]	
- recurringSettings[fromTime]	
- recurringSettings[period]	
- recurringSettings[daysOfWeek]	
- recurringSettings[daysOfMonth]	
- recurringSettings[withExclude]	
- exclude[daysOfWeek]	
- exclude[dates]	
- customType	
- customParam

## 登録スクリプト実行（ターミナル）

1. 登録用の CSV を `/govtech/distribution/csv` に配置（ファイル名は自由）
2. ディレクト `/govtech/distribution/script` に移動
3. 以下のいずれを実行

| スクリプト                 | オプション | 用途 | 備考 | 
|--------------------------|--------|------|------|
| distribution-manual.sh | create | 手動配信一括登録 |  |  
| distribution-outer.sh  | create | 外部配信一括登録 |  | 

例：

```
// 手動配信一括登録
$ ./distribution-manual.sh create

// 外部配信一括登録
$ ./distribution-manual.sh create
```


4. OSS 管理画面の `username` と `password` を入力してログイン
5. OSS 管理画面の帳票作成一覧から一括登録の帳票を選択し、`/forms/survey/d/xyz` の URL における `xyz` をコピーし、帳票 ID `surveyId` を入力
6. 登録用の CSV ファイル名を入力（`.csv` が必要）
7. エンターキーを押して登録プロセスが開始
8. `<importId>.log` や `<importId>.json` ファイルが自動的に生成されます

<em>ネットの都合により、ネットワークエラーが発生する場合があるので、1000件以上のデータの登録は分割（500件ごと）で行うのを推薦します。</em>

<em>データを分割すればするほど、ネットワークエラーの発生率が低くなります。</em>

<em>登録が完了しましたら、LSC 管理画面の配信一覧もしくは DynamoDB で確認できます。</em>

<em>誤って登録してしまった場合は、一括削除スクリプトを実行してください（削除スクリプト実行を参考）。</em>


## 削除スクリプト実行（ターミナル）
1. DynamoDB の `<stageName>-distribution-configs` テーブルで、、以下の条件でクエリ（検索）を行う

使用するインデックス：`gsi-importId-id`

| Attribute name                     | Type | Condition | Value |
|------------------------------|------|------|------|
| importId    | String     |   Equal to   | 削除するデータの `importId` |
| createdBy    | String      | Equal to    | スクリプトの実行者、一般的に `lsc-maintenance` |

<em> `createdBy` の検索条件がないと、システム内部に生成されたデータ（`system` など）が出されますので、要注意です。 </em>

2. DynamoDB から検索された結果を全件をチェックし、`Actions` → `Download results to CSV` で削除用の CSV をダウンロードします

3. 削除用の CSV を `/govtech/distribution/csv` に配置（ファイル名は自由）
4. ディレクトリ `/govtech/distribution/script` に移動
5. 以下のいずれかを実行

| スクリプト                 | オプション | 用途 | 備考 | 
|--------------------------|--------|------|------|
| distribution-manual.sh | delete | 手動配信一括削除 |  | 
| distribution-outer.sh  | delete | 外部配信一括削除 | 未対応 | 


例：

```
// 手動配信一括削除
$ ./distribution-manual.sh delete

// 手動配信一括削除
$ ./distribution-manual.sh delete
```

6. OSS 管理画面の `username` と `password` を入力してログイン
7. 削除用の CSV ファイル名を入力（`.csv` が必要）
8. エンターキーを押して削除プロセスが開始
9. 生成されるファイルは特にない

<em>削除が完了しましたら、LSC 管理画面の配信一覧もしくは DynamoDB で確認できます。</em>


## 注意事項
- CSV ヘッダーは完全一致（大文字と小文字）です。
- 存在しない設定項目を `condition` に入れてしまった場合は、エラーが発生します。
- 各行文字データに半角全角スペースを入れたかどうかを要確認、`ホゲ` が `ホ　ゲ` になっていたら必ず `ホゲ` にしてください。
- CSV ファイルのエンドコードは必ず「UTF-8 の BOM 付き」、「JIS」などは NG。
- 不正なログイン情報もしくは CSV ファイルが見つからない場合は、スクリプトが停止されます。
- 登録中の際にエラーが発生したとしても、スクリプトが停止されず継続します、要するに成功したデータを一括削除して再登録が必要です。

## 今後の対応（TODO）
- 「手動配信一括更新」と「外部配信一括削除や更新」
- コードに  `TODO` を書いているもの
- 重複コードがあるので、共通化やリファクタリングする
- 1000件以上のデータを登録や削除の際に、ネットワークエラーの解消