import * as helper from "./helper.js";
import { v4 as uuidv4 } from 'uuid';
import { COMMA_DELIMITER_REGEX, SEMICOLON_DELIMITER_REGEX } from "./constants.js";

class Processor {

  constructor(importId, parsedCSV, surveyConfigs) {
    this.importId = importId;
    this.csvFields = parsedCSV.meta.fields;
    this.surveyConfigs = surveyConfigs ? surveyConfigs.data : undefined;
    this.csvData = parsedCSV.data;
  }

  extractField = (key) => {
    return this.csvFields.filter(e => e.includes(key));
  }

  extractCustomWeek = (str) => {
    return parseInt(str.replace("w=", ""));
  }

  extractCustomNumber = (str) => {
    return parseInt(str.replace("n=", ""));
  }

  getIds = () => {
    let id = this.extractField("id");
    let values = this.csvData.map(e => e[id]);
    return values;
  }
  
  processName = () => {
    let distributionName = this.extractField("distributionName");
    let values = this.csvData.map(e => e[distributionName]);
    return values;
  }
  
  processSurveyConditions = () => {
    let conditions = this.extractField("condition");
    return conditions.map((condition) => {
      let title = condition.replace("condition", "").replace("[", "").replace("]", "");
      let schemas = this.surveyConfigs.surveySchema.filter(schema => schema.type !== "choicegroupheader" && schema.type !== "groupheader");
      let { itemKey, type } = schemas.find(schema => schema.title === title);

      let values = this.csvData.map(e => e[condition]).map(v => {
        return {
          itemKey: itemKey,
          title: title,
          blockType: type,
          conditionValues: helper.truncateEmptyConditionValues(type, v.split(COMMA_DELIMITER_REGEX))
        };
      });
      return { conditions: values };
    });
  }
  
  processMessages = () => {
    let messages = this.extractField("messages");
    return messages.map(message => {
      let values = this.csvData.map(e => e[message]).map(v => {
        return { id: uuidv4(), contents: { text: v, type: 'text' }}
      });
      return values;
    });
  }
  
  processDistributionType = () => {
    let distributionType = this.extractField("distributionType");
    let values = this.csvData.map(e => e[distributionType]);
    return values;
  }
  
  processScheduledStartAt = () => {
    let scheduledStartAt = this.extractField("scheduledStartAt");
    let values = this.csvData.map(e => {
      let formattedDate = helper.convertToUnixTimestamp(e[scheduledStartAt]);
      return formattedDate;
    });
    return values;
  }
  
  processRecurringSettings = () => {
    let recurringSettings = this.extractField("recurringSettings");

    let group = recurringSettings.map(recurringSetting => {
      let setting = recurringSetting.replace("recurringSettings", "").replace("[", "").replace("]", "");
      let values = this.csvData.map(e => e[recurringSetting]).map(v => {
        return v;
      });

      return { [setting]: values }
    });

    return Object.assign({}, ...group);
  }
  
  processExclude = () => {
    let excludes = this.extractField("exclude");
    let group = excludes.map(exclude => {
      let setting = exclude.replace("exclude", "").replace("[", "").replace("]", "");
      let values = this.csvData.map(e => e[exclude]).map(v => {
        return v;
      });
      return { [setting]: values }
    });

    return Object.assign({}, ...group);
  }

  processCustom = () => {
    let customTypes = this.csvData.map(e => e["customType"]);
    let customParams = this.csvData.map(e => e["customParam"]).map((v, index) => {
      if (customTypes[index] === "skip") {
        let params = v.split(SEMICOLON_DELIMITER_REGEX).map(v => v.trim());
        let period = params[0];
        let length = Number(params[1]);
        return {
          type: customTypes[index],
          skip: {
            period: period,
            length: length
          }
        };
      } else if (customTypes[index] === "numberedDayOfWeek") {
        let params = v.split(SEMICOLON_DELIMITER_REGEX).map(v => v.trim());
        let values = params.map((param) => {
          let numberedDayOfWeek = param.split(COMMA_DELIMITER_REGEX);
          if (numberedDayOfWeek.length >= 2) {
            let dayOfWeek = this.extractCustomWeek(numberedDayOfWeek[0]);
            let number = this.extractCustomNumber(numberedDayOfWeek[1]);
            return {
              dayOfWeek: dayOfWeek,
              number: number
            };
          }
        });
        return {
          type: customTypes[index],
          numberedDayOfWeek: values,
          skip: { // 必要ないと思うけど、実際の作成画面のリクエストで使用されているためskipも含める
            period: "days",
            length: 1
          }
        };
      } else if (customTypes[index] === "dates") {
        let params = v.split(SEMICOLON_DELIMITER_REGEX).map(v => v.trim());
        let values = params.map(param => helper.convertToHyphenYYYYMMDD(param));
        return {
          type: customTypes[index],
          dates: values,
          skip: {
            period: "days",
            length: 1
          }
        };
      }

      return helper.checkHasValue(customTypes[index]) ? {
        type: customTypes[index],
        skip: helper.truncateNumberedDayOfWeek(values)
      } : undefined;
    });

    return customParams;
  }
  
  buildPayload = () => {
    let payload = [];
    
    let distributionName = this.processName();
    let surveyConditions = this.processSurveyConditions();
    let messages = this.processMessages();
    let distributionType = this.processDistributionType();
    let recurringSettings = this.processRecurringSettings();
    let excludes = this.processExclude();
    let customs = this.processCustom();
    const nowTimeStamp = new Date().toISOString();
  
    this.csvData.map((e, index) => {
      let obj = {};
      
      obj["settings"] = {
        importId: this.importId,
        targetSelectionType: "surveyConditions",
        distributionName: distributionName[index],
        distributionType: distributionType[index],
        postponedSettings: distributionType[index] === "postponed" ? {
          date: helper.convertToHyphenYYYYMMDD(recurringSettings.fromDate[index]),
          time: helper.convertToHHmm(recurringSettings.fromTime[index]),
        } : undefined,
        repeatingSettings: distributionType[index] === "repeating" ? {
          fromDate: helper.convertToHyphenYYYYMMDD(recurringSettings.fromDate[index]),
          toDate: helper.convertToHyphenYYYYMMDD(recurringSettings.toDate[index]),
          fromTime: helper.convertToHHmm(recurringSettings.fromTime[index]),
          period: recurringSettings.period[index],
          withExclude: helper.convertStrToBoolean(recurringSettings.withExclude[index]),
          daysOfWeek: helper.truncateEmptyDays(recurringSettings.daysOfWeek[index].split(COMMA_DELIMITER_REGEX).map(Number)),
          daysOfMonth: helper.truncateEmptyDays(recurringSettings.daysOfMonth[index].split(COMMA_DELIMITER_REGEX).map(Number)),
          exclude: {
            daysOfWeek: helper.truncateEmptyDays(excludes.daysOfWeek[index].split(COMMA_DELIMITER_REGEX).map(Number)) || [],
            dates: helper.truncateEmptyDates(excludes.dates[index].split(COMMA_DELIMITER_REGEX).map(date => helper.checkHasValue(date) ? helper.convertToHyphenYYYYMMDD(date) : "")),
          },
          custom: customs[index]
        } : undefined,
        surveyConditions: {
          surveyId: this.surveyConfigs.surveyId,
          surveyTitle: this.surveyConfigs.surveyTitle,
          conditions: surveyConditions.map(surveyCondition => surveyCondition.conditions[index]),
          pickAll: false,
        },
        messageIds: messages.map(message => message[index].id),
        createdAt: nowTimeStamp,
        updatedAt: nowTimeStamp,
        isDraft: false,
      }

      obj["messages"] = helper.truncateMessages(messages.map(message => message[index]));


      payload.push(obj);
    })

    return payload;
  }
};

export { Processor };
