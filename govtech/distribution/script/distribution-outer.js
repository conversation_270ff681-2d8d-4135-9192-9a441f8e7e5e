import { Auth } from "aws-amplify";
import inquirer from "inquirer";
import fs from "fs";
import <PERSON> from "papaparse";
import chalk from "chalk";
import _ from "lodash";

import { AmplifyService } from "./config.js";
import { Processor } from "./distribution-outer-processor.js";
import * as helper from "./helper.js";
import * as logger from "./logger.js";

import { getSurveyConfigs, createDistributionOuter, /* deleteDistributionOuter*/ getAllDistributionOuter } from "./api-service.js"
import { CSV_EXTENSION, API_SUCCESS, API_ERROR,
  MAX_PROCESS_CHUNKS, DEFAULT_SUCCESS_MSG, DEFAULT_ERROR_MSG } from "./constants.js";

let importId = helper.getCurrentDatetime() + "#" + helper.generateUUID();

const batchInsert = async (chunks) => {
  var requests = chunks;
  return await Promise.allSettled(requests.map(request => 
    createDistributionOuter(request)
      .catch(error => ({ name: request.name, error}))
    ));
}

// TODO: Batch delete distribution outer
/* const batchDelete = async (ids) => {
  return await Promise.allSettled(ids.map(id => 
    deleteDistributionOuter({
      distributionDeliveryId: id
    }).catch(error => error)
  ));
} */

const promptSignIn = async () => {
  let credential = await inquirer
    .prompt([
      {
        name: "username",
        message: "Username: "
      },
      {
        type: 'password',
        mask: '*',
        name: "password",
        message: "Passord: ",
      }
    ])
    .then(credential => {
      return credential;
    });

  if (credential) {
    return await Auth.signIn(credential.username, credential.password);
  }
}

const promptSurveyId = async () => {
  let surveyId = await inquirer
    .prompt([
      {
        name: "id",
        message: "Enter survey ID: "
      },
    ])
    .then(surveyId => {
      return surveyId;
    });
  
  if (surveyId) {
    return surveyId.id;
  }
}

const promptCSVFilename = async () => {
  let csv = await inquirer
    .prompt([
      {
        name: "filename",
        message: "Enter CSV filename: "
      },
    ])
    .then(csv => {
      return csv;
    });
  
  if (csv) {
    return csv.filename;
  }
}

const readCSV = async (file) => {
  return new Promise((resolve, reject) => {
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      delimiter: ',',
      complete(result) {
        resolve(result);
      },
      error() {
        reject("An error has occured while reading CSV file.");
      },
    });
  });
}

const execute = async (action, filename, surveyId) => {

  let filedata = fs.readFileSync("../csv/" + filename, "utf8");
  let parsedCSV = await readCSV(filedata);
  let surveyConfigs = surveyId ? await getSurveyConfigs(surveyId) : undefined;
  let processor = new Processor(importId, parsedCSV, surveyConfigs);

  if (action === "create") {
    let payload = processor.buildPayload();
    fs.writeFile("../script/payload/" + importId + ".json", JSON.stringify(payload, null, 2), "utf8", function (error) {
      if (error) {
        logger.printInfo("An error has occured while writing JSON file.");
        console.error(error)
      }
    });

    let chunks = _.chunk(payload, MAX_PROCESS_CHUNKS);
    let promises = [];
    let report = [];

    logger.printInfo("----------------------------------------------------------------------");
    logger.printInfo(chalk.cyan("Import ID: " + importId));
    logger.printInfo(chalk.cyan("Number of processes: " + chunks.length));
    logger.printInfo(chalk.cyan("Number of data: " + payload.length));
    logger.printInfo("----------------------------------------------------------------------");
    
    chunks.map((chunk, index) => {
      let promise = new Promise((resolve, reject) => {
        try {
            batchInsert(chunk).then(res => {
            logger.printInfo(chalk.yellow("Process " + parseInt(index + 1) + " has been completed"));
            resolve(res);
          })
        } catch(error) {
          reject();
        }
      });
      promises.push(promise);
    })

    Promise.all(promises).then(res => {
      let successCount = 0;
      let errorCount = 0;
      logger.printInfo(chalk.yellow("--- All processes has been completed --- "));
      let output = res.flat();
      output.map(e => {
        switch(e.status) {
          case "fulfilled":
            let succeedName = e.value && e.value.distributionName ? e.value.distributionName : undefined;
            let createdId = e.value && e.value.id ? e.value.id : undefined;
            if (!e.value.error) {
              successCount++;
              report.push(helper.getCurrentDatetimePretty() + " [" + API_SUCCESS + "] -" + " [" + succeedName + "] " + DEFAULT_SUCCESS_MSG + " [ID: " + createdId + "]");
            } else {
              errorCount++
              report.push(helper.getCurrentDatetimePretty() + " [" + API_ERROR + "] - " + JSON.stringify(e.value));
            }
            break;
          case "rejected":
            errorCount++;
            let failedName = e.reason ? e.reason.name : undefined;
            let error = e.reason ? e.reason.error : DEFAULT_ERROR_MSG
            report.push(helper.getCurrentDatetimePretty() + " [" + API_ERROR + "] -" + " [" + failedName + "] " + error);
            break;
        }
      })

      report.push("Import ID: " + importId);
      report.push("Number of processes: " + chunks.length);
      report.push("Number of data: " + payload.length);
      report.push("Succeeded: " + successCount);
      report.push("Failed: " + errorCount);
      logger.printInfo(chalk.cyan("Succeeded: " + successCount));
      logger.printInfo(chalk.red("Failed: " + errorCount));
      logger.printInfo("----------------------------------------------------------------------");
      logger.writeLogFile(importId, report);
    })

  /*  let res = undefined;
    let distributionIds = [];
 
    do {
      res = await getAllDistributionOuter(res);
      if (res.items.length > 0) {
       // console.log(res.items)
        res.items.map(distribution => {
          distributionIds.push(distribution.id);
        })
      }
    } while (res.lastEvaluatedKey);
 
    console.log(distributionIds.length); */

  } else if (action === "delete") {
  /*  let ids = processor.getIds();
    batchDelete(ids).then(res => {
      logger.printInfo("----------------------------------------------------------------------");
      logger.printInfo(chalk.cyan("Number of data: " + res.length));
      logger.printInfo(chalk.yellow("--- All data has been deleted --- "));
      logger.printInfo("----------------------------------------------------------------------");
    }) */
  }
}

const main = async () => {
  let authenticated = await promptSignIn();
  if (authenticated) {
    logger.printInfo("--- Login successful ---")

    let action = process.argv[2] ? process.argv[2] : undefined;
    let surveyId = undefined;

    if (action === "create") {
      surveyId = await promptSurveyId();
    }

    let filename = await promptCSVFilename();
    execute(action, filename, surveyId);
  }
}

AmplifyService.configure();
main();