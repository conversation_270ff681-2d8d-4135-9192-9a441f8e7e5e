import _ from "lodash";
import { v4 as uuidv4 } from "uuid";
import moment from "moment";

const generateUUID = () => {
  return uuidv4();
};

const checkHasValue = (value) => {
  return value !== undefined && value !== null && (typeof value === "string" ? value !== "" : true) && !_.isNaN(value);
}

const getCurrentDatetime = () => {
  return moment(new Date()).format("YYYYMMDDHHmmss");
}

const getCurrentDatetimePretty = () => {
  return moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
}

const convertToUnixTimestamp = (date) => {
  return moment(new Date(date)).unix();
}

const convertToHyphenYYYYMMDD = (date) => {
  return moment(new Date(date)).format("YYYY-MM-DD");
}

const convertToHHmm = (time) => {
  return moment(time, "HH:mm").format("HH:mm");
}

const convertStrToBoolean = (str) => {
  return (str === "TRUE" || str === "true") ? true : (str === "FALSE" || str === "false") ? false : false;
}

const truncateMessages = (array) => {
  return array.filter(e => checkHasValue(e.contents.text));
}

const truncateEmptyDays = (array) => {
  return !array.every(e => e === 0) ? array : undefined;
}

const truncateEmptyDates = (array) => {
  return array.every(e => checkHasValue(e)) ? array : [];
}

const truncateEmptyConditionValues = (type, array) => {
  if (type === "checkboxes" || type === "suggest") {
    return array.every(e => checkHasValue(e)) ? array : [];
  } else if (type === "radio" || type === "dropdown") {
    return array.every(e => checkHasValue(e)) ? [array[0].trimStart().trimEnd()] : [""];
  }  
}

const truncateNumberedDayOfWeek = (array) => {
  return array.filter(e => checkHasValue(e));
}

export { generateUUID, checkHasValue, getCurrentDatetime,
  getCurrentDatetimePretty, convertToUnixTimestamp, convertToHyphenYYYYMMDD, 
  convertToHHmm, convertStrToBoolean, truncateMessages, truncateEmptyDays, 
  truncateEmptyDates, truncateEmptyConditionValues, truncateNumberedDayOfWeek };
