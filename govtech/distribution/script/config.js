import { Amplify, Auth } from "aws-amplify";

export const PLATFORM_ADMIN_API = "PLATFORM_ADMIN_API";

import path from "path";
import dotenv from "dotenv";

const __dirname = path.dirname(".env");
dotenv.config({ __dirname});

export const AmplifyService = {
  configure() {
    Amplify.configure({
      Auth: {
        region: process.env.REGION,
        userPoolId: process.env.USER_POOL_ID,
        userPoolWebClientId: process.env.USER_POOL_WEB_CLIENT_ID,
      },
      API: {
        endpoints: [
          {
            name: PLATFORM_ADMIN_API,
            endpoint: process.env.ENDPOINT_URL,
            custom_header: async () => {
              try {
                return {
                  Authorization: `Bearer ${(await Auth.currentSession()).getIdToken().getJwtToken()}`,
                };
              } catch (e) {
                return {};
              }
            },
          }
        ],
      },
    });
  },
};