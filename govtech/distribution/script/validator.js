import _ from "lodash";

const checkRequiredHeaders = (surveyConfigs, parsedCSV) => {
  let requiredHeaders = [
    "name",
    "messages[1]",
    "messages[2]",
    "startType",
    "scheduledStartAt",
    "recurringSettings[fromDate]",
    "recurringSettings[toDate]",
    "recurringSettings[fromTime]",
    "recurringSettings[period]",
    "recurringSettings[daysOfWeek]",
    "recurringSettings[daysOfMonth]",
    "recurringSettings[withExclude]",
    "exclude[daysOfWeek]",
    "exclude[dates]",
    "customType",
    "customParams"
  ];


  let conditionHeaders = [];

  let csvHeaders = parsedCSV.meta.fields;

  let titles = surveyConfigs.data.surveySchema.filter(schema => schema.type !== "choicegroupheader" && schema.type !== "groupheader").map(schema => schema.title);
  titles.map(title => {
    conditionHeaders.push("condition[" + title + "]");
  })

  requiredHeaders.push(...conditionHeaders)

  csvHeaders.sort((a, b) => a.localeCompare(b));
  requiredHeaders.sort((a, b) => a.localeCompare(b));

  console.log(csvHeaders)
  console.log(requiredHeaders)

  console.log(_.isEqual(requiredHeaders, csvHeaders));
}

export { checkRequiredHeaders };
