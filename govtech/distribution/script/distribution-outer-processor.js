import * as helper from "./helper.js";

import { COMMA_DELIMITER_REGEX, SEMICOLON_DELIMITER_REGEX } from "./constants.js";
class Processor {

  constructor(importId, parsedCSV, surveyConfigs) {
    this.importId = importId;
    this.csvFields = parsedCSV.meta.fields;
    this.surveyConfigs = surveyConfigs ? surveyConfigs.data : undefined;
    this.csvData = parsedCSV.data;
  }

  extractField = (key) => {
    return this.csvFields.filter(e => e.includes(key));
  }

  getIds = () => {
    let id = this.extractField("id");
    let values = this.csvData.map(e => e[id]);
    return values;
  }
  
  processName = () => {
    let distributionName = this.extractField("distributionName");
    let values = this.csvData.map(e => e[distributionName]);
    return values;
  }
  
  processSurveyConditions = () => {
    let conditions = this.extractField("condition");    
    return conditions.map((condition) => {
      let title = condition.replace("condition", "").replace("[", "").replace("]", "");
      let schemas = this.surveyConfigs.surveySchema.filter(schema => schema.type !== "choicegroupheader" && schema.type !== "groupheader");
      let { itemKey, type } = schemas.find(schema => schema.title === title);

      let values = this.csvData.map(e => e[condition]).map(v => {
        return {
          itemKey: itemKey,
          title: title,
          blockType: type,
          conditionValues: helper.truncateEmptyConditionValues(type, v.split(COMMA_DELIMITER_REGEX))
        };
      });
      return { conditions: values };
    });
  }

  processSubjectExtractionCondition = () => {
    let subjectExtractionCondition = this.extractField("subjectExtractionCondition");
    let values = this.csvData.map(e => e[subjectExtractionCondition]);
    let obj = {};
    obj[subjectExtractionCondition] = values;
    return values;
  }

  processBodyExtractionCondition = () => {
    let bodyExtractionCondition = this.extractField("bodyExtractionCondition");
    let values = this.csvData.map(e => e[bodyExtractionCondition]);
    let obj = {};
    obj[bodyExtractionCondition] = values;
    return values;
  }

  processBodyChangeCondition = () => {
    let bodyChangeCondition = this.extractField("bodyChangeCondition");
    let values = this.csvData.map(e => e[bodyChangeCondition]);
    let obj = {};
    obj[bodyChangeCondition] = values;
    return values;
  }
  
  buildPayload = () => {
    let payload = [];
    
    let distributionNames = this.processName();
    let surveyConditions = this.processSurveyConditions();

    this.csvData.map((e, index) => {
      let obj = {};
      
      obj["settings"] = {
        importId: this.importId,
        distributionName: distributionNames[index],
        surveyConditions: {
          surveyId: this.surveyConfigs.surveyId,
          // surveyTitle: this.surveyConfigs.surveyTitle,
          conditions: surveyConditions.map(surveyCondition => surveyCondition.conditions[index])
        },
        enabled: e.enabled ? e.enabled.toLowerCase().trim() === 'true' : true,
        distributionType: "external",
        isDraft: false,
        mailTriggerSettings: {
          condition: {
            bodyExtractionCondition: e.bodyExtractionCondition || '',
            bodyTest: "",
            subjectExtractionCondition: e.subjectExtractionCondition || '',
            subjectTest: ""
          },
          content: {
            bodyChangeCondition: e.bodyChangeCondition || '',
            bodyTest: ""
          }
        },
        targetSelectionType: "surveyConditions"
      }

      payload.push(obj);
    })

    return payload;
  }
};

export { Processor };
