<a name="readme-top"></a>

<div align="center">
<h1>🈚  OSS化  🆓</h1>
<h3 align="center">LINEスマートシティの後継するもの</h3>

<a href="https://www.notion.so/playnext-lab/OSS-076d131b954b42769920925939cdcdaf?pvs=4"><strong>➡️ Explore the docs (Notion) »</strong></a>
</div>

# Prerequisites
* Node 18
* AWS account and AWS CLI v2 installed (`.aws/credentials` and `.aws/config` files existed) 

# Local development environment

## Backend development

You will need an AWS account (personal or shared) and access credentials, that are written in `.aws/credentials` file. You can provide AWS credentials to SST in following ways:

- create profile in  `.aws/credentials`
- use [Leapp](https://docs.leapp.cloud/latest/), that will control `.aws/credentials` profiles for you

```shell
set $stage dev # for [dev] stage
cp deployment-configs/oss.example.yaml deployment-configs/oss-$stage.local.yaml # on initial deployment only
yarn
yarn dev --stage=$stage --profile {AWS profile name}
```
To start the admin webapp in development mode
```shell
cd packages/web-admin
yarn serve
```

That will deploy an infrastructure and start the SST development mode.

<aside>
💡 If this is the first time you are installing CDK application into your account, you need additional permissions to have bootstrapping process creating necessary resources. It’s not a problem if you use credentials of an existed IAM user, but can lead to access denied errors, when receiving credentials via AssumeRole operation. Use credentials of existed IAM user on the first deployment of SST/CDK application.

</aside>

<aside>
💡 Leapp by default uses STS AssumeRole and obtains temporary credentials, that will not work on initial deployment. You should write IAM user’s credentials directly into .aws/credentials or start Leapp session with MFA configured

</aside>

## Admin Webapp

Get required parameters
1. Admin API Url
   1. use `admniApiUrl` output value from CDK output (in console)
   2. or use `/sst/oss/{stage}/Api/admin_api/url` parameter value from Parameter Store
1. Cognito User Pool 1) user pool ID 2) user pool client ID
   1. use `userPoolId` and `userPoolClientId` output values from CDK output (in console)
   2. or [go to AWS Console](https://ap-northeast-1.console.aws.amazon.com/cognito/v2/idp/user-pools?region=ap-northeast-1) and refer settings of `{stage}-oss-admins_pool` user pool
   
```shell
cd packages/web-admin
cp .env.example .env
# fill in required parameters in .env
yarn
yarn serve
```

#  Development environment deployment


### 🔴Setup required configuration parameters

> No any required parameters yet



### 🔴Run deployment script
```shell
set $stage dev # for [dev] environment
cp deployment-configs/oss.example.yaml deployment-configs/oss-$stage.local.yaml # on initial deployment only
yarn
yarn deploy --stage=$stage --profile {AWS profile name}

# Case: Deploying with a specified Node.js version
$ nvm exec {node version} yarn deploy --stage=$stage --profile {AWS profile name}
# EX: Deploying with a specified Node.js version 18
$ nvm exec 18 yarn deploy --stage=$stage --profile {AWS profile name}
```

### 🔴Add initial admin user
1. [Go to AWS Console page](https://ap-northeast-1.console.aws.amazon.com/cognito/v2/idp/user-pools?region=ap-northeast-1)
1. Find `{stage}-oss-admins_pool`
1. Create new group `Administrator:admins`
1. Add new user and assign the group to it.

### 🔴Find admin webapp URL
1. From console output
    1. Find `admniApiUrl` parameter value: this will be the admin webapp access URL
1. From AWS Console (AWS System Manager Parameter Store)
    1. Login the AWS Console and [go to this page](https://ap-northeast-1.console.aws.amazon.com/systems-manager/parameters/?region=ap-northeast-1&tab=Table)
    1. Filter parameters with `/sst/oss/{stage}`
    1. Find `StaticSite/web_admin/url` - this will be the admin webapp access URL

#  観光機能について

- View: [spot.md](./spot.md)

#  Data migration

- View: [infra/stacks/migration/manual/README.md](./infra/stacks/migration/manual/README.md)