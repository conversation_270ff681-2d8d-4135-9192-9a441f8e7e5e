import { SSTConfig } from "sst";
import * as adminIntegration from "./infra/stacks/ApiIntegration.js";
import * as apiReception from "./infra/stacks/ApiReception.js";
import * as apiAdmin from "./infra/stacks/ApiSurveyAdmin.js";
import * as bosai from "./infra/stacks/Bosai.js";
import * as damageReport from "./infra/stacks/damage-report/stack.js";
import * as distributions from "./infra/stacks/Distributions.js";
import * as dns from "./infra/stacks/Dns.js";
import * as glue from "./infra/stacks/migration/glue.js";
import * as platform from "./infra/stacks/Platform.js";
import * as scenario from "./infra/stacks/Scenario.js";
import * as shareResource from "./infra/stacks/ShareResources.js";
import * as statistics from "./infra/stacks/Statistics.js";
import * as survey from "./infra/stacks/Survey.js";
import * as vpc from "./infra/stacks/Vpc.js";
import * as webResources from "./infra/stacks/WebResources.js";

import { config } from "./infra/config.js";
import { applyDefaults } from "./infra/lambda-defaults.js";
import { getCurrentProfile, setupAwsContext } from "./infra/resources/aws-clients.js";
import payment from "./infra/stacks/Payment.js";

// some comment
export default {
  config(_input) {
    return {
      name: "oss",
      region: "ap-northeast-1",
    };
  },
  async stacks(app) {
    const profile = getCurrentProfile()
    console.log(`Using profile ${profile}`)
    setupAwsContext(profile, app.region)
    config.reset({})
    config.resetContext(app)
    await config.preprocess();

    applyDefaults(app);
    app.stack(dns.Stack, { id: 'dns'})
    app.stack(platform.Stack, { id: 'platform'})
    app.stack(bosai.Stack, { id: 'bosai'})
    app.stack(survey.FullStack, { id: 'survey'});

    if (config.isUsingPayment()) {
      app.stack(vpc.Stack, { id: 'vpc'});
      app.stack(payment, { id: 'payment' });
    }

    app.stack(shareResource.ShareStack, {id: 'share-resources'});

    app.stack(scenario.Stack, { id: 'scenario'});
    app.stack(damageReport.Stack, {id: 'damage-report'});

    app.stack(statistics.Stack, { id: 'statistics'})
    app.stack(distributions.Stack, { id: 'distributions'});
    app.stack(apiReception.Stack, { id: 'api-reception'});
    app.stack(apiAdmin.Stack, { id: 'api-surveyApi'})
    app.stack(adminIntegration.Stack, { id: 'admin-integration'});

    app.stack(webResources.Stack, { id: 'web-resources'});
    if (config.isUsingMigration()) {
      app.stack(glue.Stack, { id: 'glue-migration-job'})
    }
  }
} satisfies SSTConfig;
