deployment:
  isDevelopmentEnvironment: false
  secretName: 'stg-oss-secret'
  environmentUniqueKey: 'ossstg'
  initialUser:
    username: 'admin' # required
    password: 'Pnl2023!' #required
global:
  # members tab
  memberTab: "1"
  sesRegion: 'ap-northeast-1'
  baseDomain: 'stg-oss.line-smartcity.com'
  mailDomain: 'stg-oss.line-smartcity.com'
  # This is the DEV environment, so change this to STG once it is setup
  googleMapApiKey: 'AIzaSyDbycamzrDFPpCk40rDp4U1L_Jf0JIXivE' # google map API key for damage report map feature
  line:
    messagingAccessToken: 'NE7b1CnPkId4BP5/lKMZ9A9UI4f88Gt2LzzcrmCwPeE+qHOp4mXUmMu5zyPE2McGkx9wL0dDTr+eH0/916y7p2FPB13cFFoyif3T2/m2Ah4ZMrFAjrDtaO5/xfAlS/SebBg/V/yIu3z8RIqQO2mU4QdB04t89/1O/w1cDnyilFU='
    loginChannelId: '2006133521'
    liffId: '2006133521-RlqN1Bz3'
    loginChannelSecret: 'a21b833656acf861e3bc100f09d13728'
    messagingChannelId: '2006133473'
    messagingChannelSecret: 'c4c3e75193bf077f6221db30c9f70cb6'
    # This is the DEV environment, so change this to STG once it is setup
    liffIdDamage: '2006133521-0wRJqYGN' # required - Damage report Liff app - Liff id to use LINE login
webAdmin:
  skipBuild: false
webAdminNew:
  skipBuild: false
  deploy: true
webReception:
  skipBuild: false
  apiUrl: ''
apiReception:
  skipBuild: false
  devSkipAuth: false
apiAdmin:
  devSkipAuth: false
webDamageReport:
  skipBuild: false
scenario:
  fuzzySearchEnabled: "0"
  chatbotForward: '0'
  chatbotForwardUrl: 'xxxx.com'
  devSkipAuth: false
  damage:
    emailReport1: ''
    emailReport2: ''
    emailReport3: ''
    emailReport4: ''
  sandbox:
    chatbotForward: '0'
    chatbotForwardUrl: 'xxxx.com'
    messagingChannelId: '2006133477'
    messagingChannelSecret: 'fcd09036c1cf579740f0525211d4afdc'
    messagingAccessToken: 'xfIsoUK7fADXYApgt8PuojeY8Mb/gedXygJAYMEvP0i2cSRPqvERZ0qTwLV1Vmc5tpZCrGgciP3k137EhRxfa4UxKFYTg+cTJH55hTlfbgNMdDHfVoHol3lskO2YTyul9pTCh6Krk8Gaz7cSEjyB6wdB04t89/1O/w1cDnyilFU='
  spot:
    use: "1"
distributions:
  emailTrigger:
    recipientAddress: '<EMAIL>'
databases:
  surveyResults: ''
  surveyConfigs: ''
  lineUsers: ''
  surveyCalendars: ''
  surveyLogs: ''
  surveyResultsHistory: ''
buckets:
  surveyStorage: ''
  surveyPublicResources: ''
adminUsersPool:
  poolId: ''
  clientId: ''
statistics:
  schedule: '0 17 * * ? *' # cron
# This is DEV environment, so change this to STG once it is setup
payment: # here we have parameters of the SBP test environment, no any secrets
  use: "1"
  merchant-id: '30132' # VITE_PAYMENT_MERCHANT_ID
  web-payment-url: 'https://stbfep.sps-system.com/f01/FepBuyInfoReceive.do' # VITE_PAYMENT_LINK_TYPE_ENDPOINT_URL
  endpoint: '' # PAYMENT_API_TYPE_ENDPOINT_URL
  api:
    id: '30132103' # API ACCESS ID, PAYMENT_API_AUTH_ID
    pass: 'ed679e1c9f90c2ab96b25d5c580b58e25192eb5d' # API password, PAYMENT_API_AUTH_PASS
    key: '644da9995cac43695d6b3fcbc89787872fbc8b5c' # API key, VUE_APP_PAYMENT_API_KEY
migration:
  use: "1"