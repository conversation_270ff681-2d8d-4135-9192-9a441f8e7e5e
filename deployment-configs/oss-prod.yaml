deployment:
  isDevelopmentEnvironment: true
global:
  line:
    loginRedirectUrl: ''
    loginChannelSecret: ''
    messagingAccessToken: ''
    loginChannelId: ''
webAdmin:
  apiUrl: ''
  skipBuild: false
webAdminNew:
  skipBuild: false
  deploy: true
webReception:
  apiUrl: ''
apiReception:
  devSkipAuth: false
apiAdmin:
  devSkipAuth: false
databases:
  surveyResults: ''
  surveyConfigs: ''
  lineUsers: ''
  surveyCalendars: ''
  surveyLogs: ''
  surveyResultsHistory: ''
buckets:
  surveyStorage: ''
  surveyPublicResources: ''
adminUsersPool:
  poolId: ''
  clientId: ''
migration:
  use: true
