deployment:
  isDevelopmentEnvironment: true
  secretName: 'lsc-oss'
  environmentUniqueKey: 'lscossdev'
#  initialUser:
#    username: 'lsc-maintenance'
#    password: 'N3tLgZ9i'
#    email: '<EMAIL>'
global:
  sesRegion: 'ap-northeast-1'
  baseDomain: 'lsc-dev-oss.line-smartcity.com'
  mailDomain: 'lsc-dev-oss.line-smartcity.com'
  line:
    messagingAccessToken: 'NZEIfjKHqKLADMhCBu+JWOj76D9600u4+pUP3FsCbj6NnwVURPP5i/+Y/LZxSa1+STA3btOpJNqmwg7iviiWCXybXWaBrgqJkDJpoPG7q5yyrphEgi9JvCYi02lTwjKx99gSAVLNW/XZsojXLZLnbgdB04t89/1O/w1cDnyilFU='
    loginChannelId: '2001019681'
    liffId: '2001019681-gDoN1BBb'
    loginChannelSecret: '07172eff27679b920eabb9457436bbc0'
    messagingChannelId: '2004229418'
    messagingChannelSecret: '7c02d9e329af8b3806ef43b13f5ac20a'
webAdminNew:
  deploy: true
webAdmin:
  skipBuild: false
webReception:
  skipBuild: false
apiReception:
  devSkipAuth: false
apiAdmin:
  devSkipAuth: false
scenario:
  fuzzySearchEnabled: "0"
  chatbotForward: 'xxx'
  chatbotForwardUrl: 'xxxxx.com'
  devSkipAuth: false
  damage:
    emailReport1: ''
    emailReport2: ''
    emailReport3: ''
    emailReport4: ''
  sandbox:
    fuzzySearchEnabled: "0"
    chatbotForward: '0'
    chatbotForwardUrl: 'xxxx.com'
    messagingChannelId: '2004229418'
    messagingChannelSecret: '7c02d9e329af8b3806ef43b13f5ac20a'
    messagingAccessToken: 'NZEIfjKHqKLADMhCBu+JWOj76D9600u4+pUP3FsCbj6NnwVURPP5i/+Y/LZxSa1+STA3btOpJNqmwg7iviiWCXybXWaBrgqJkDJpoPG7q5yyrphEgi9JvCYi02lTwjKx99gSAVLNW/XZsojXLZLnbgdB04t89/1O/w1cDnyilFU='
distributions:
  emailTrigger:
    recipientAddress: '<EMAIL>'
databases:
  surveyResults: ''
  surveyConfigs: ''
  lineUsers: ''
  surveyCalendars: ''
  surveyLogs: ''
  surveyResultsHistory: ''
buckets:
  surveyStorage: ''
adminUsersPool:
  poolId: ''
  clientId: ''
statistics:
  schedule: '0 17 * * ? *' # cron
migration:
  use: true
