deployment:
  isDevelopmentEnvironment: false
global:
  memberTab: "1"
  sesRegion: 'ap-northeast-1'
  baseDomain: '${env_name}.gov.line-smartcity.com'
  googleMapApiKey: '' # google map API key for damage report map feature
  line:
    messagingAccessToken: ''
    loginChannelId: ''
    liffId: ''
    loginChannelSecret: ''
    messagingChannelId: ''
    messagingChannelSecret: ''
    liffIdDamage: '' # required - Damage report Liff app - Liff id to use LINE login
webAdmin:
  skipBuild: false
webReception:
  skipBuild: false
apiReception:
  skipBuild: false
  devSkipAuth: false
apiAdmin:
  devSkipAuth: false
scenario:
  fuzzySearchEnabled: "0"
  chatbotForward: '0'
  chatbotForwardUrl: ''
  devSkipAuth: false
  damage:
    emailReport1: ''
    emailReport2: ''
    emailReport3: ''
    emailReport4: ''
  sandbox:
    chatbotForward: '0'
    chatbotForwardUrl: ''
    messagingChannelId: ''
    messagingChannelSecret: ''
    messagingAccessToken: ''
  spot:
    use: "0"
distributions:
  emailTrigger:
    recipientAddress: 'receiver@${env_name}.gov.line-smartcity.com'
statistics:
  schedule: '0 17 * * ? *' # cron
payment: # here we have parameters of the SBP test environment, no any secrets
  use: "0"
  merchant-id: '30132' # VITE_PAYMENT_MERCHANT_ID
  web-payment-url: 'https://stbfep.sps-system.com/f01/FepBuyInfoReceive.do' # VITE_PAYMENT_LINK_TYPE_ENDPOINT_URL
  endpoint: 'https://stbfep.sps-system.com/api/xmlapi.do' # PAYMENT_API_TYPE_ENDPOINT_URL
  api:
    id: '30132103' # API ACCESS ID, PAYMENT_API_AUTH_ID
    pass: 'ed679e1c9f90c2ab96b25d5c580b58e25192eb5d' # API password, PAYMENT_API_AUTH_PASS
    key: '644da9995cac43695d6b3fcbc89787872fbc8b5c' # API key, VUE_APP_PAYMENT_API_KEY
migration:
  use: "1"
