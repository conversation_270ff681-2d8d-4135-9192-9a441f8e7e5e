deployment:
  isDevelopmentEnvironment: true
  secretName: 'dev-oss-newsecret'
  environmentUniqueKey: 'xxIld32'
  initialUser:
    username: 'admin' # required
    password: 'Pnl2023!' #required
global:
  memberTab: "1"
  sesRegion: 'ap-northeast-1'
  baseDomain: 'dev-oss.line-smartcity.com'
  mailDomain: 'dev-oss.line-smartcity.com'
  googleMapApiKey: 'AIzaSyCGrQV1lz7kBt61n7zDjDJn97YoQRaJvUI' # google map API key for damage report map feature
  line:
    messagingAccessToken: 'P74j1E5cc/dKv2j7rdnuD0YteoldvN9Ol6ibq0L3TkVQk5UG8r8dusMkUj9TTHE8CqEBTxUVON4NKOXcXNRw58WJY2R4Alep3WI8A7zVZEhKGEuHqf/jQzYgYBYLxBOMoAWqexuVK7nT5LtigCz9HgdB04t89/1O/w1cDnyilFU='
    loginChannelId: '2000726509'
    liffId: '2000726509-lobjrjjW'
    loginChannelSecret: '18bc82b7b38ef5debe3732ad447c92f9'
    messagingChannelId: '2000716116'
    messagingChannelSecret: '2033ef513615ca96a95a08e4cc9dd208'
    liffIdDamage: '2000726509-gLB0Y00p' # required - Damage report Liff app - Liff id to use LINE login
webAdmin:
  skipBuild: false
webAdminNew:
  skipBuild: false
  deploy: true
  baseWebappUrl: https://d34ytf9ha4ixq3.cloudfront.net
webReception:
  skipBuild: false
  apiUrl: ''
  host: https://d3g5t1kcnau148.cloudfront.net
apiReception:
  skipBuild: false
  devSkipAuth: false
apiAdmin:
  devSkipAuth: false
scenario:
  fuzzySearchEnabled: "0"
  chatbotForward: '0'
  chatbotForwardUrl: 'xxxx.com'
  devSkipAuth: false
  damage:
    emailReport1: ''
    emailReport2: ''
    emailReport3: ''
    emailReport4: ''
  sandbox:
    chatbotForward: '0'
    chatbotForwardUrl: 'xxxx.com'
    messagingChannelId: '2000716108'
    messagingChannelSecret: '9c23e7949740389aa19c46c999114b1f'
    messagingAccessToken: 'DIix/bodRtmpdhPDKmtfWQ9FXQ5MYSNaaBBEkhybTSX7BigGkn0HLETPQtXkht3D5cGwPSlgYlQRN4ojz5IL5TgMC8Urp2YRwzqP+2rqR9/2nPKixY9Ob8mA0ZmMupZCjh4J8tdxSdUBN2VNVQmfkQdB04t89/1O/w1cDnyilFU='
  spot:
    use: "1"
distributions:
  emailTrigger:
    recipientAddress: '<EMAIL>'
databases:
  surveyResults: ''
  surveyConfigs: ''
  lineUsers: ''
  surveyCalendars: ''
  surveyLogs: ''
  surveyResultsHistory: ''
buckets:
  surveyStorage: ''
  surveyPublicResources: ''
adminUsersPool:
  poolId: ''
  clientId: ''
statistics:
  schedule: '0 17 * * ? *' # cron
payment: # here we have parameters of the SBP test environment, no any secrets
  use: "1"
  merchant-id: '30132' # VITE_PAYMENT_MERCHANT_ID
  web-payment-url: 'https://stbfep.sps-system.com/f01/FepBuyInfoReceive.do' # VITE_PAYMENT_LINK_TYPE_ENDPOINT_URL
  endpoint: 'https://stbfep.sps-system.com/api/xmlapi.do' # PAYMENT_API_TYPE_ENDPOINT_URL
  api:
    id: '30132103' # API ACCESS ID, PAYMENT_API_AUTH_ID
    pass: 'ed679e1c9f90c2ab96b25d5c580b58e25192eb5d' # API password, PAYMENT_API_AUTH_PASS
    key: '644da9995cac43695d6b3fcbc89787872fbc8b5c' # API key, VUE_APP_PAYMENT_API_KEY
migration:
  use: "1"
