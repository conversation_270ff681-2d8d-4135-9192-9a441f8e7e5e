/*
 * Copyright 2025 PlayNext Lab Inc.
 *
 * PlayNext Lab Inc. licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

const map = {
    distribution: {
        token: {
            setting: 'DISTRIBUTION_MSG_CHANNEL_ACCESS_TOKEN',
            env: 'LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
        },
        secret: {
            setting: 'DISTRIBUTION_MSG_CHANNEL_SECRET',
            env: 'LINEMESSAGING_CHANNEL_SECRET',
        },
    },
    scenario: {
        api: {
            sandboxToken: {
                setting: 'SB_LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
                env: 'SB_LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
            },
            prodToken: {
                setting: 'LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
                env: 'LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
            },
        },
        sandbox: {
            token: {
                setting: 'SB_LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
                env: 'LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
            },
            id: {
                setting: 'SB_LINEMESSAGING_CHANNEL_ID',
                env: 'LINEMESSAGING_CHANNEL_ID',
            },
            secret: {
                setting: 'SB_LINEMESSAGING_CHANNEL_SECRET',
                env: 'LINEMESSAGING_CHANNEL_SECRET',
            },
            fuzzySearch: {
                setting: 'SB_CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH',
                env: 'FUZZY_SEARCH_ENABLED'
            },
            forward: {
                setting: 'SB_CHATBOT_FORWARD',
                env: 'CHATBOT_FORWARD'
            }
        },
        prod: {
            token: {
                setting: 'LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
                env: 'LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
            },
            id: {
                setting: 'LINEMESSAGING_CHANNEL_ID',
                env: 'LINEMESSAGING_CHANNEL_ID',
            },
            secret: {
                setting: 'LINEMESSAGING_CHANNEL_SECRET',
                env: 'LINEMESSAGING_CHANNEL_SECRET',
            },
            fuzzySearch: {
                setting: 'CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH',
                env: 'FUZZY_SEARCH_ENABLED'
            },
            forward: {
                setting: 'CHATBOT_FORWARD',
                env: 'CHATBOT_FORWARD'
            }
        },
        both: {
            damageReportEmail1: {
                setting: 'EMAIL_CHATBOT_DAMAGE_REPORT1',
                env: 'EMAIL_CHATBOT_DAMAGE_REPORT1',
            },
            damageReportEmail2: {
                setting: 'EMAIL_CHATBOT_DAMAGE_REPORT2',
                env: 'EMAIL_CHATBOT_DAMAGE_REPORT2',
            },
            damageReportEmail3: {
                setting: 'EMAIL_CHATBOT_DAMAGE_REPORT3',
                env: 'EMAIL_CHATBOT_DAMAGE_REPORT3',
            },
            damageReportEmail4: {
                setting: 'EMAIL_CHATBOT_DAMAGE_REPORT4',
                env: 'EMAIL_CHATBOT_DAMAGE_REPORT4',
            },
            sesEmailDomain: {
                setting: 'SES_EMAIL_DOMAIN',
                env: 'SES_EMAIL_DOMAIN',
            },
        },
        forward: {
            prod: {
                setting: 'CHATBOT_FORWARD_URL',
                env: 'CHATBOT_FORWARD_URL'
            },
            sandbox: {
                setting: 'SB_CHATBOT_FORWARD_URL',
                env: 'CHATBOT_FORWARD_URL'
            },
        }
    },
    platform: {
        sbToken: {
            setting: 'SB_LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
            env: 'SB_LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
        },
        prodToken: {
            setting: 'LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
            env: 'LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
        },
    },
    bi: {
        biApiChannelToken: {
            setting: 'LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
            env: 'CHANNEL_ACCESS_TOKEN',
        },
        biLogChannelToken: {
            setting: 'LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
            env: 'LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
        },
    },
    survey: {
        lineloginChannelId: {
            setting: 'SURVEY_LINELOGIN_CHANNEL_ID',
            env: 'LINELOGIN_CHANNEL_ID',
        },
        lineloginClientSecret: {
            setting: 'SURVEY_LINELOGIN_CHANNEL_SECRET',
            env: 'LINELOGIN_CLIENT_SECRET',
        },
        prodToken: {
            setting: 'LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
            env: 'LINEMESSAGING_CHANNEL_ACCESS_TOKEN',
        },
    },
    lambdas: {
        platform: {
            admin: 'AdminFunction',
        },
        survey: {
            accessor: 'SurveyAccessor',
        },
        bi: {
            biLog: 'BILogFunction',
            biApi: 'BiApiFunction',
        },
        scenario: {
            chatbotProd: 'ChatBotFunction',
            chatbotSandbox: 'ChatBotFunctionSandbox',
            api: 'ScenarioApiFunction',
            log: 'ScenarioLogFunction',
            forwardProd: 'WebhookForwardFunction',
            forwardSandbox: 'WebhookForwardFunctionSandbox',
        },
        distribution: {
            manager: 'distribution-manager',
            worker: 'distribution-worker',
        },
    },
} as const;

export type SecMap = typeof map

export default map;
