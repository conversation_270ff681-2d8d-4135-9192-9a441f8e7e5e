import {StackContext, Bucket, Function, Cron} from "sst/constructs";
import * as cdk from 'aws-cdk-lib';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as firehose from 'aws-cdk-lib/aws-kinesisfirehose';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as logs from 'aws-cdk-lib/aws-logs';

import {config} from "../../config.js";
import {BlockPublicAccess} from "aws-cdk-lib/aws-s3";

export function addActionLogsCollectionInfra(
    ctx: StackContext,
    accessLogsBucket: s3.Bucket
) {
    const { stack } = ctx

    const actionLogsBucket = new s3.Bucket(stack, 'ActionLogsBucket', {
        encryption: s3.BucketEncryption.S3_MANAGED, // Use AES256 encryption
        removalPolicy: config.getRemovalPolicy(),
        blockPublicAccess: BlockPublicAccess.BLOCK_ALL,
        publicReadAccess: false,
        serverAccessLogsBucket: accessLogsBucket,
        autoDeleteObjects: config.getAutodeleteOption(),
        serverAccessLogsPrefix: 'PlatformActionLogs/PlatformActionLogs',
    });

    // IAM role for Firehose
    const firehoseRole = new iam.Role(stack, 'FirehoseDeliveryRole', {
        assumedBy: new iam.ServicePrincipal('firehose.amazonaws.com'),
        externalIds: [ctx.app.account], // Equivalent of `sts:ExternalId` with the account ID
    });

    // Grant Firehose permissions on the ActionLogsBucket
    actionLogsBucket.grantReadWrite(firehoseRole);

    // Define CloudWatch Log Group for Firehose logging
    const firehoseLogGroup = new logs.LogGroup(stack, 'FirehoseLogGroup', {
        logGroupName: `/aws/actionlogs/${cdk.Stack.of(stack).stackName}`,
        retention: logs.RetentionDays.ONE_WEEK,
        removalPolicy: cdk.RemovalPolicy.DESTROY, // Adjust this to retain logs if necessary
    });

    // Define the Firehose delivery stream
    const deliveryStream = new firehose.CfnDeliveryStream(stack, 'ActionLogsDeliveryStream', {
        deliveryStreamType: 'DirectPut',
        s3DestinationConfiguration: {
            bucketArn: actionLogsBucket.bucketArn,
            roleArn: firehoseRole.roleArn,
            prefix: 'platform-action-logs/logs/',
            errorOutputPrefix: 'platform-action-logs/errors/',
            bufferingHints: {
                intervalInSeconds: 60,
                sizeInMBs: 1,
            },
            cloudWatchLoggingOptions: {
                enabled: true,
                logGroupName: firehoseLogGroup.logGroupName,
                logStreamName: 'PlatformLogsS3Delivery',
            },
        },
    });

    const putActionLogPutRecordPermission = new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
            'firehose:PutRecord',
            'firehose:PutRecordBatch',
        ],
        resources: [deliveryStream.attrArn],
    })

    const policy = new iam.ManagedPolicy(stack, 'ActionLogPutRecordPolicy', {
        statements: [putActionLogPutRecordPermission],
    });

    // Lambda 関数の作成
    const actionLogsCollectorFunction = new Function(stack, "ActionLogsCollectorFunction", {
        functionName: `${stack.stackName}-${ctx.app.name}-action-logs-collector`,
        handler: "packages/services/action_logs/reporter/src/handler.handler",
        memorySize: 3008,
        timeout: 900,
        runtime: "nodejs18.x",
        environment: {
            DEST_BUCKET: actionLogsBucket.bucketName,
            DEST_BUCKET_PREFIX: 'platform-action-logs/reports/',
            SOURCE_BUCKET: actionLogsBucket.bucketName,
            SOURCE_BUCKET_PREFIX: 'platform-action-logs/logs/',
        },
        permissions: [
            's3',
            'dynamodb',
            'lambda:InvokeFunction'
        ]
    });

    // Cron ジョブの作成
    const actionLogsCollectorFunctionScheduler = new Cron(stack, "ActionLogsCollectorFunctionScheduler", {
        schedule: "cron(10 15 * * ? *)", 
        job: actionLogsCollectorFunction
    });

    return {
        actionLogsBucket,
        actionLogsDeliveryStream: deliveryStream,
        putActionLogPutRecordPermission: policy,
        putActionLogPutRecordStatement: putActionLogPutRecordPermission,
        actionLogsCollectorFunction,
        actionLogsCollectorFunctionScheduler
    }
}