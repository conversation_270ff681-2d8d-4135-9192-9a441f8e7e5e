import { Duration } from "aws-cdk-lib";
import { Queue, StackContext } from "sst/constructs";

export function ShareStack({ app, stack }: StackContext) {
    // queues
    const queueName = `${stack.stackName}-create-dr-queue`;
    const createDamageReportQueue = new Queue(stack, queueName, {
        cdk: {
            queue: {
                queueName,
                visibilityTimeout: Duration.seconds(60)
            }
        }
    });

    stack.addOutputs({
        createDamageReportQueueUrl: createDamageReportQueue.queueUrl
    })
    return {
        createDamageReportQueue: createDamageReportQueue,
    }
}