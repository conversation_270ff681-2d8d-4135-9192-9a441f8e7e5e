import {StackContext} from "sst/constructs";
import {BlockPublicAccess, Bucket, HttpMethods} from 'aws-cdk-lib/aws-s3'
import {config} from "../../config.js";
import {dynamo} from "../../infra-helpers.js";
import {aws_cloudfront, Duration} from "aws-cdk-lib";
import {S3Origin} from "aws-cdk-lib/aws-cloudfront-origins";

export function createScenarioTables(ctx: StackContext) {
    const tableBuilder = dynamo.newTable(ctx.stack)
    const userSession = tableBuilder( 'userSession', {
        fields: {
            userId: "string",
            environment: "string"
        },
        primaryIndex: {
            partitionKey: "userId",
            sortKey: "environment",
        },
    });
    const chatBotScenario = tableBuilder('chatBotScenario', {
        fields: {
            scenarioId: "string",
        },
        primaryIndex: {
            partitionKey: "scenarioId"
        }
    });

    const chatBotScenarioDataScenarioDataTypeIndex = "scenario-dataType-index";
    // const chatBotScenarioDataDataTypeDataIdIndex = "dataType-dataId-index";
    const chatBotScenarioDataGsiDataTypeDataIdIndex = "gsi-dataType-dataId-index";
    const chatBotScenarioData = tableBuilder('chatBotScenarioData', {
        fields: {
            scenario: "string",
            dataId: "string",
            dataType: "string"
        },
        primaryIndex: {
            partitionKey: "scenario",
            sortKey: "dataId"
        },
        globalIndexes: {
            [chatBotScenarioDataScenarioDataTypeIndex]: {
                partitionKey: "scenario",
                sortKey: "dataType",
                projection: "all"
            },
            // TODO: Cannot perform more than one GSI creation or deletion in a single update.
            // chatBotScenarioDataDataTypeDataIdIndex will be deleted later.
            // https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/GSI.OnlineOps.html#GSI.OnlineOps.Creating
            // [chatBotScenarioDataDataTypeDataIdIndex]: {
            //     partitionKey: "scenario",
            //     sortKey: "dataType",
            //     projection: "all"
            // },
            [chatBotScenarioDataGsiDataTypeDataIdIndex]: {
                partitionKey: "dataType",
                sortKey: "dataId",
                projection: "all"
            },
        }
    })
    const scenarioLogs = tableBuilder('scenarioLogs', {
        fields: {
            "logType": "string",
            "createdAt": "number",
            "sourceUserId": "string",
            "endpointName": "string",
            "scenarioName": "string",
            "sourceType": "string"
        },
        primaryIndex: {
            partitionKey: "logType",
            sortKey: "createdAt"
        },
        globalIndexes: {
            "logType-sourceUserId-index": {
                partitionKey: "logType",
                sortKey: "sourceUserId",
                projection: "all"
            },
            "logType-endpointName-index": {
                partitionKey: "logType",
                sortKey: "endpointName",
                projection: "all"
            },
            "logType-scenarioName-index": {
                partitionKey: "logType",
                sortKey: "scenarioName",
                projection: "all"
            },
            "sourceType-createdAt-index": {
                partitionKey: "sourceType",
                sortKey: "createdAt",
                projection: "all"
            },
        }
    })

    const scenarioData = tableBuilder('scenarioData', {
        fields: {
            id: "string",
            typeId: "string",
        },
        primaryIndex: {
          partitionKey: "id",
          sortKey: "typeId"
        },
    })

    const stampTitle = tableBuilder('stampTitle', {
        fields: {
            "lineUserId": "string",
            "typeId": "string",
        },
        primaryIndex: {
            partitionKey: "lineUserId",
            sortKey: "typeId"
        }
    })

    return {
        userSession,
        scenarioLogs,
        chatBotScenario,
        chatBotScenarioData,
        scenarioData,
        stampTitle,
        indexs: {
            chatBotScenarioDataScenarioDataTypeIndex,
            chatBotScenarioDataGsiDataTypeDataIdIndex
        }
    }
}


export function createPublicBucket(ctx: StackContext) {
    const bucket = new Bucket(ctx.stack, 'chatbot-resources', {
        removalPolicy: config.getRemovalPolicy(),
        blockPublicAccess: BlockPublicAccess.BLOCK_ALL,
        autoDeleteObjects: config.getAutodeleteOption(),
        publicReadAccess: false,
        cors: [{
            allowedOrigins: ['*'],
            allowedMethods: [HttpMethods.GET, HttpMethods.POST, HttpMethods.PUT],
            maxAge: Duration.days(365).toSeconds(),
        }]
    })

    const oid = new aws_cloudfront.OriginAccessIdentity(ctx.stack, 'chatbot-resources-oids');
    const distribution = new aws_cloudfront.Distribution(ctx.stack, 'chatbot-resources-cf', {
        comment:'Chatbot Resources',
        defaultBehavior: {
            origin: new S3Origin(bucket, { originAccessIdentity: oid }),
            originRequestPolicy: aws_cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN,
            responseHeadersPolicy: aws_cloudfront.ResponseHeadersPolicy.CORS_ALLOW_ALL_ORIGINS_WITH_PREFLIGHT,
        }
    })

    return {
        bucket, distribution
    }
}
