import {Bucket, EventBus, Function, Queue, StackContext, use} from "sst/constructs";
import {config} from "../config.js";
import {dynamo} from "../infra-helpers.js";
import {BUCKETS, DATABASES} from "../constants.js";
import * as SURVEY_TABLES from "./survey/tables.js"
import {Duration} from 'aws-cdk-lib'
import {CanonicalUserPrincipal, Effect, PolicyStatement} from "aws-cdk-lib/aws-iam";
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront'
import * as s3 from 'aws-cdk-lib/aws-s3'
import {HttpMethods} from 'aws-cdk-lib/aws-s3'
import {Stack as Platform} from './Platform';

export function FullStack({app, stack}: StackContext) {
    const platform = use(Platform);

    const searchFunction = new Function(stack, `${stack.stackName}-search`, {
        functionName: `${stack.stackName}-search`,
        handler: 'packages/services/survey/function-search.request',
        environment: {
            LOG_EVENT_BUS: platform.logEventBus.eventBusName
        },
        retryAttempts: 0,
        maxEventAge: Duration.seconds(60)
    });
    searchFunction.attachPermissions(["lambda"])

    const surveyResultTable = dynamo.newTable(stack)(DATABASES.SURVEY_RESULTS, SURVEY_TABLES.SURVEY_RESULTS);
    const surveyConfigsTable = dynamo.newTable(stack)(DATABASES.SURVEY_CONFIGS, SURVEY_TABLES.SURVEY_CONFIGS);
    const surveyCalendarsTable = dynamo.newTable(stack)(DATABASES.SURVEY_CALENDARS, SURVEY_TABLES.SURVEY_CALENDARS);
    const lineUsersTable = dynamo.newTable(stack)(DATABASES.LINE_USERS, SURVEY_TABLES.LINE_USERS);
    const surveyResultsHistoryTable = dynamo.newTable(stack)(DATABASES.SURVEY_RESULTS_HISTORY, SURVEY_TABLES.SURVEY_RESULTS_HISTORY);
    const memberSurveyTable = dynamo.newTable(stack)(DATABASES.MEMBER_CONFIGS, SURVEY_TABLES.MEMBER_CONFIGS);
    const memberResultTable = dynamo.newTable(stack)(DATABASES.MEMBER_RESULTS, SURVEY_TABLES.MEMBER_RESULTS);
    const memberResultHistoryTable = dynamo.newTable(stack)(DATABASES.MEMBER_RESULTS_HISTORY, SURVEY_TABLES.MEMBER_RESULTS_HISTORY);

    dynamo.allowWrite(searchFunction, surveyResultTable.tableName)
    dynamo.allowRead(searchFunction, surveyConfigsTable.tableName)
    dynamo.allowRead(searchFunction, memberSurveyTable.tableName)
    dynamo.allowRead(searchFunction, memberResultTable.tableName)
    dynamo.allowRead(searchFunction, memberResultHistoryTable.tableName)
    dynamo.allowRead(searchFunction, surveyCalendarsTable.tableName)
    searchFunction.addEnvironment('TABLE_SURVEY_RESULTS', surveyResultTable.tableName);
    searchFunction.addEnvironment('TABLE_SURVEY_CONFIGS', surveyConfigsTable.tableName);
    searchFunction.addEnvironment('TABLE_MEMBER_CONFIGS', memberSurveyTable.tableName);
    searchFunction.addEnvironment('TABLE_MEMBER_RESULTS', memberResultTable.tableName);
    searchFunction.addEnvironment('TABLE_MEMBER_RESULTS_HISTORY', memberResultHistoryTable.tableName);
    searchFunction.addEnvironment('TABLE_SURVEY_CALENDARS', surveyCalendarsTable.tableName);

    const eventBus = new EventBus(stack, 'surveyBus');

    const resultsBucket = new Bucket(stack, 'private-resources');
    searchFunction.addEnvironment('S3_BUCKET_NAME', resultsBucket.bucketName);
    searchFunction.attachPermissions([resultsBucket]);

    const originIdentity = new cloudfront.OriginAccessIdentity(stack,
        `${stack.stackName}-publicresources-oid`, {comment: 'Read from survey public resources'})

    const existedBucket = config.getBucketName(BUCKETS.SURVEY_PUBLIC_RESOURCES);
    const publicResourcesBucket = existedBucket ?
        s3.Bucket.fromBucketName(stack, 'public-resources', existedBucket) :
        new s3.Bucket(stack, 'public-resources', {
            blockPublicAccess: {
                blockPublicAcls: false,
                blockPublicPolicy: false,
                ignorePublicAcls: false,
                restrictPublicBuckets: false
            },
            objectOwnership: s3.ObjectOwnership.OBJECT_WRITER,
            autoDeleteObjects: config.getAutodeleteOption(),
            cors: [{
                allowedOrigins: ['*'],
                allowedHeaders: ['*'],
                allowedMethods: [HttpMethods.GET, HttpMethods.PUT],
            }],
            removalPolicy: config.getRemovalPolicy(),
        })
    if (!existedBucket) {
        publicResourcesBucket.addToResourcePolicy(new PolicyStatement({
            actions: ["s3:GetObject"],
            effect: Effect.ALLOW,
            resources: [`${publicResourcesBucket.bucketArn}/*`],
            principals: [
                new CanonicalUserPrincipal(originIdentity.cloudFrontOriginAccessIdentityS3CanonicalUserId)
            ]
        }))
        publicResourcesBucket.grantPublicAccess()
    }

    const reservationFlowStarter = new Function(stack, 'survey-reservationStarter', {
        handler: 'packages/services/survey/function-reservation-support.startReservationFlow'
    })

    const reservationQueue = new Queue(stack, 'survey-reservationQueue', {
        consumer: {
            function: reservationFlowStarter,
            cdk: {
                eventSource: {
                    batchSize: 1,
                    maxConcurrency: 50
                }

            }
        },
        cdk: {
            queue: {
                visibilityTimeout: Duration.seconds(900),
                fifo: true,
            }
        }
    })

    const dlq = new Queue(stack, 'survey-dlq', {
        cdk: {
            queue: {
                visibilityTimeout: Duration.seconds(900),
                fifo: true,
            }
        }
    })

    const asyncOperationsQueue = new Queue(stack, 'survey-async-ops', {
        cdk: {
            queue: {
                fifo: true,
                visibilityTimeout: Duration.seconds(900),
                receiveMessageWaitTime: Duration.seconds(20),
                deadLetterQueue: {
                    queue: dlq.cdk.queue,
                    maxReceiveCount: 5
                }
            }
        }
    })

    stack.addOutputs({
        publicResourcesUrl: publicResourcesBucket.bucketWebsiteUrl,
        publicResourcesOID: originIdentity.originAccessIdentityId
    })

    return {
        searchFunction,
        surveyResultTableName: surveyResultTable.tableName,
        surveyConfigsTableName: surveyConfigsTable.tableName,
        memberSurveyTableName: memberSurveyTable.tableName,
        memberResultTableName: memberResultTable.tableName,
        memberResultHistoryTableName: memberResultHistoryTable.tableName,
        surveyCalendarsTableName: surveyCalendarsTable.tableName,
        surveyResultsHistoryTableName: surveyResultsHistoryTable.tableName,
        lineUsersTableName: lineUsersTable.tableName,
        surveyCalendarsTable,
        surveyConfigsTable,
        surveyResultTable,
        asyncOperationsQueue,
        storageBucket: resultsBucket.bucketName,
        publicResourcesBucket,
        reservationQueue,
        publicResourcesOID: originIdentity.originAccessIdentityId,
        eventBus,
    }
}
