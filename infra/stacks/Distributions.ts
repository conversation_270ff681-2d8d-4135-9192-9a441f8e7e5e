import {Bucket, EventBus, Function, StackContext, use} from "sst/constructs";
import {addBucketUniqueSuffix, createPublicBucket, dynamo} from "../infra-helpers.js";
import {BUCKETS, DATABASES} from "../constants.js";
import * as TABLES from "./distributions/tables.js";
import {config} from "../config.js";
import {createStepMachine} from "./distributions/flow-machine.js";
import {FullStack as Survey} from './Survey.js'
import {Stack as Scenario} from './Scenario.js'
import {Effect, PolicyStatement, Role, ServicePrincipal} from "aws-cdk-lib/aws-iam";
import {CfnScheduleGroup} from "aws-cdk-lib/aws-scheduler";
import * as sqs from "aws-cdk-lib/aws-sqs";
import {RemovalPolicy} from "aws-cdk-lib";
import {addMailTrigger} from "./distributions/email-trigger.js";

export function Stack(ctx: StackContext) {
    const {stack, app} = ctx
    // import resources from other stacks
    const {searchFunction, storageBucket, surveyConfigsTableName, surveyCalendarsTableName} = use(Survey)
    const { chatbotFunction, chatbotSandboxFunction } = use(Scenario)


    // add storage buckets: 1 public for images in LINE messages, and 1 private for storing emails and logs
    const fileResources = createPublicBucket(ctx, BUCKETS.DISTRIBUTION_RESOURCES, {
        bucketName: app.logicalPrefixedName(addBucketUniqueSuffix(`distribution-public-resources`)),
        distributionComment: `${stack.stackName} distribution resources`,
    })
    const privateStorage = new Bucket(ctx.stack, 'distribution-private-storage', {
        name: app.logicalPrefixedName(addBucketUniqueSuffix('distribution-private')),
        cdk: {
            bucket: {
                autoDeleteObjects: config.getAutodeleteOption(),
                removalPolicy: config.getRemovalPolicy(),
            }
        }
    })
    const privateStoragePutObjectPolicy = new PolicyStatement({
        effect: Effect.ALLOW,
        actions: ['s3:PutObject'],
        principals: [new ServicePrincipal('ses.amazonaws.com')],
        resources: [`${privateStorage.bucketArn}/*`],
        conditions: {
            StringEquals: {
                'AWS:SourceAccount': app.account
            },
            StringLike: {
                'AWS:SourceArn': 'arn:aws:ses:*'
            },
        },
    });
    privateStorage.cdk.bucket.addToResourcePolicy(privateStoragePutObjectPolicy);

    ctx.stack.addOutputs({
        resourcesBucket: fileResources.bucket.bucketName,
        resourcesCloudFront: fileResources.distribution.distributionId,
        resourcesDomainName: fileResources.accessDomainName,
    })

    // Async interaction infrastructure
    // add Dead Letters Queue, that we will use as a fallback for async interactions
    const dlqQueue = new sqs.Queue(stack, app.logicalPrefixedName('distributions-dlq'), {
        queueName: app.logicalPrefixedName('distribution-dlq'),
        removalPolicy: RemovalPolicy.DESTROY,
    })
    // bus for stack's internal events
    const eventBus = new EventBus(ctx.stack, 'distributions-bus', {})

    // role and group for AWS Scheduler
    const eventBusInvokePermission = new PolicyStatement({
        effect: Effect.ALLOW,
        actions: ['events:PutEvents', "sqs:SendMessage"],
        resources: [eventBus.eventBusArn, dlqQueue.queueArn]
    });
    const schedulerRole = new Role(ctx.stack, 'scheduler-role', {
        assumedBy: new ServicePrincipal(ServicePrincipal.servicePrincipalName("scheduler.amazonaws.com")),
    });
    schedulerRole.addToPolicy(eventBusInvokePermission);
    new CfnScheduleGroup(ctx.stack, 'postponed-schedules-group', {
        name: ctx.app.logicalPrefixedName('distribution-postponed')
    })
    new CfnScheduleGroup(ctx.stack, 'repeating-schedules-group', {
        name: ctx.app.logicalPrefixedName('distribution-repeating')
    })
    new CfnScheduleGroup(ctx.stack, 'reminders-schedules-group', {
        name: ctx.app.logicalPrefixedName('reminders')
    })



    // new tables
    const configsDatabase = dynamo.newTable(ctx.stack)(`configs`, {
        cdk: {
            table: {
                tableName: `${ctx.stack.stage}-${ctx.app.name}-distribution-configs`
            }
        },
        fields: {
            partitionKey: 'string',
            sortKey: 'string',
            gsiListingPk: 'string',
            gsiListingSk: 'string',
            importId: 'string',
            id: 'string'
        },
        primaryIndex: {
            partitionKey: 'partitionKey',
            sortKey: 'sortKey'
        },
        globalIndexes: {
            'gsi-listing': {
                partitionKey: 'gsiListingPk',
                sortKey: 'gsiListingSk',
                projection: 'all'
            },
            'gsi-importId-id': {
                partitionKey: 'importId',
                sortKey: 'id',
                projection: 'all'
            }
        },
    })

    const historyDatabase = dynamo.newTable(ctx.stack)(`history`, {
        cdk: {
            table: {
                tableName: `${ctx.stack.stage}-${ctx.app.name}-distribution-history`
            }
        },
        fields: {
            partitionKey: 'string',
            sortKey: 'string',
            gsiListingPk: 'string',
            gsiListingSk: 'string',
            state: 'string',
            startedAt: 'string',
        },
        primaryIndex: {
            partitionKey: 'partitionKey',
            sortKey: 'sortKey'
        },
        globalIndexes: {
            'gsi-listing': {
                partitionKey: 'gsiListingPk',
                sortKey: 'gsiListingSk',
                projection: 'all'
            },
            'gsi-state-startedAt': {
                partitionKey: 'state',
                sortKey: 'startedAt',
                projection: 'all'
            }
        },
    })

    // emails handling infra
    // handler function
    const emailTrigger = new Function(ctx.stack, app.logicalPrefixedName('distribution-mailtrigger'), {
        handler: 'packages/services/distribution/external/function-mail-trigger.handler',
        environment: {
            DATABASE_CONFIGS: configsDatabase.tableName,
            EVENTBUS_DISTRIBUTIONS: eventBus.eventBusName,
            BUCKET_PRIVATE_STORAGE: privateStorage.bucketName,
            LAMBDA_CHATBOT_PROD: chatbotFunction.functionName,
            LAMBDA_CHATBOT_SANDBOX: chatbotSandboxFunction.functionName,
        }
    })
    // connector infra
    addMailTrigger(ctx, {
        recipientEmail: config.getString('distributions.emailTrigger.recipientAddress'),
        s3ToStore: privateStorage.cdk.bucket,
        triggerLambda: emailTrigger,
        dlq: dlqQueue,
    })


    // ===== REMINDERS =====
    // reminders table
    const remindersTable = dynamo.useExistedOrCreateNew(
        ctx.stack, DATABASES.DISTRIBUTION_REMINDERS, TABLES.REMINDERS
    )
    // reminders messages storage
    const resourcesTable = dynamo.useExistedOrCreateNew(
        ctx.stack, DATABASES.DISTRIBUTION_RESOURCES, TABLES.RESOURCES
    )
    // [reminders realm] <=> [delivery engine] adapter function
    const remindersAdapterFunction = new Function(stack, 'reminders-adapter', {
        functionName: ctx.app.logicalPrefixedName('distribution-reminders-adapter'),
        handler: 'packages/services/distribution/reminders/function-reminders-engine-adapter.handler',
        environment: {
            DATABASE_CONFIGS: configsDatabase.tableName,
            DATABASE_HISTORY: historyDatabase.tableName,
            DATABASE_MESSAGES: resourcesTable.tableName,
            DATABASE_REMINDERS: remindersTable.tableName,
            EVENTBUS_DISTRIBUTIONS: eventBus.eventBusName,
            DATABASE_CALENDARS: surveyCalendarsTableName,
            DATABASE_SURVEY_CONFIGS: surveyConfigsTableName,
        },
        permissions: ["events", "dynamodb"]
    })
    schedulerRole.addToPolicy(new PolicyStatement({
        actions: ['lambda:InvokeFunction'],
        resources: [remindersAdapterFunction.functionArn]
    }))
    // old table (TODO get rid of it)
    const configsResultsTable = dynamo.useExistedOrCreateNew(
        ctx.stack, DATABASES.DISTRIBUTION_CONFIGS_RESULTS, TABLES.CONFIGS_RESULTS);
    // reminders api
    const restApiOld = new Function(ctx.stack, `${ctx.stack.stackName}-restApi`, {
        functionName: `${ctx.stack.stackName}-restApi`,
        handler: 'packages/services/distribution/function-rest-api.main',
        environment: {
            TABLE_DISTRIBUTION_RESOURCES: resourcesTable.tableName,
            // TABLE_DISTRIBUTION_HISTORY: stateTable.tableName,
            BUCKET_DIST_RESOURCES: fileResources.bucket.bucketName,
            DATABASE_REMINDERS: remindersTable.tableName,
            DATABASE_MESSAGES: resourcesTable.tableName,
            SB_LINEMESSAGING_CHANNEL_ACCESS_TOKEN: config.getString('scenario.sandbox.messagingAccessToken'),
            // TABLE_SEGMENT_DELIVERY: configsResultsTable.tableName,
            RESOURCES_CLOUDFRONT_DOMAIN: fileResources.accessDomainName,
            BUCKET_REMINDERS_RESOURCES: fileResources.bucket.bucketName,
            DATABASE_CONFIGS: configsDatabase.tableName,
            DATABASE_HISTORY: historyDatabase.tableName,
            LINE_MESSAGING_ACCESS_TOKEN: config.getString('global.line.messagingAccessToken'),
            EVENTBUS_DISTRIBUTIONS: eventBus.eventBusName,
            EVENTBUS_DISTRIBUTIONS_ARN: eventBus.eventBusArn,
            DAMAGE_REPORT_LIFF_ID: config.getString('global.line.liffIdDamage'),
            TABLE_SURVEY_CONFIGS: surveyConfigsTableName

        },
        permissions: ["dynamodb", "s3"]
    });
    restApiOld.addEnvironment("LAMBDA_REMINDERS_ADAPTER", remindersAdapterFunction.functionArn)
    // ===============

    // delivery engine
    const {simpleFlowMachine} = createStepMachine(ctx, {
        historyTable: historyDatabase.tableName,
        configsTable: configsDatabase.tableName,
        lineMessagingAccessToken: config.getString('global.line.messagingAccessToken'),
        searchResultsBucketName: storageBucket,
        searchFunctionName: searchFunction.functionName,
        eventBusName: eventBus.eventBusName
    })
    // the engine runner
    const dispatcherFn = new Function(ctx.stack, 'dispatcher', {
        functionName: `${ctx.stack.stackName}-dispatcher`,
        handler: 'packages/services/distribution/dispatcher/function-dispatcher.handler',
        permissions: ["states:*", "dynamodb", "events"],
        environment: {
            DATABASE_CONFIGS: configsDatabase.tableName,
            DATABASE_HISTORY: historyDatabase.tableName,
            MACHINE_ARN_SIMPLE: simpleFlowMachine.stateMachineArn,
            MACHINE_ARN_SEARCH: simpleFlowMachine.stateMachineArn,
            EVENTBUS_DISTRIBUTIONS: eventBus.eventBusName,
            EVENTBUS_DISTRIBUTIONS_ARN: eventBus.eventBusArn
        }
    })

    // new control API
    const restApiNew = new Function(ctx.stack, `control-api`, {
        functionName: `${ctx.stack.stackName}-control-api`,
        handler: 'packages/services/distribution/function-rest-api.handler',
        permissions: ["dynamodb", "s3", 'events'],
        environment: {
            DATABASE_CONFIGS: configsDatabase.tableName,
            DATABASE_HISTORY: historyDatabase.tableName,
            LINE_MESSAGING_ACCESS_TOKEN: config.getString('global.line.messagingAccessToken'),
            EVENTBUS_DISTRIBUTIONS: eventBus.eventBusName,
            EVENTBUS_DISTRIBUTIONS_ARN: eventBus.eventBusArn,
            BUCKET_DIST_RESOURCES: fileResources.bucket.bucketName,
            RESOURCES_CLOUDFRONT_DOMAIN: fileResources.accessDomainName
        }
    })

    // connect resources to the bus and to the scheduler
    function connectScheduler(f: Function) {
        f.addEnvironment('SCHEDULER_ROLE_ARN', schedulerRole.roleArn)
        f.addEnvironment('SYSTEM_DLQ_ARN', dlqQueue.queueArn)
        f.addToRolePolicy(new PolicyStatement({
            actions: ['iam:PassRole'],
            effect: Effect.ALLOW,
            resources: [schedulerRole.roleArn],
        }))
        f.attachPermissions(["events"])
    }

    connectScheduler(restApiNew)
    connectScheduler(restApiOld)
    connectScheduler(dispatcherFn)

    eventBus.addRules(ctx.stack, {
        'handlerRunConfiguration': {
            pattern: {
                detailType: ['run-configuration']
            },
            targets: {
                dispatcherFn
            },
        }
    })
    eventBus.addRules(ctx.stack, {
        'reminderAdapterDeliveryFinished': {
            pattern: {
                detailType: ['delivery-finished'],
                detail: {
                    distributionType: ['reminder']
                }
            },
            targets: {
                remindersAdapterFunction
            },
        }
    })

    return {
        restApi: restApiOld,
        restApiNew,
        configsResultsTable,
        remindersTable,
        resourcesTable,
        domainName: fileResources.accessDomainName,
    }
}
