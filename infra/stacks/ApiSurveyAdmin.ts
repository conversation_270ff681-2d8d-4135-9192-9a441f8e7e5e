import {Function, StackContext, use} from "sst/constructs";
import * as Survey from './Survey.js'
import * as Platform from './Platform.js'
import Payment from './Payment.js'
import {dynamo} from "../infra-helpers.js";

import {config} from '../config.js'

export function Stack(ctx: StackContext) {
    const {stack, app} = ctx;

    const {
        surveyResultTableName,
        surveyResultsHistoryTableName,
        surveyCalendarsTableName,
        surveyConfigsTableName,
        memberSurveyTableName,
        memberResultTableName,
        memberResultHistoryTableName,
        searchFunction,
        storageBucket,
        publicResourcesBucket,
    } = use(Survey.FullStack)
    const {previousPlatformTable, dnsResources, actionLogsStream, actionLogsStreamWritePermissionStatement} = use(Platform.Stack)

    const surveyAdminFunction = new Function(stack, `${stack.stackName}-admin`, {
        handler: 'packages/services/api-admin-survey/function-api.surveyAdmin',
        functionName: `${stack.stackName}-admin`
    })
    const calendarsAdminFunction = new Function(stack, `${stack.stackName}-calendars`, {
        handler: 'packages/services/api-admin-survey/function-api.calendarsAdmin',
        functionName: `${stack.stackName}-calendars`
    })
    const surveySearchFunction = new Function(stack, `${stack.stackName}-search`, {
        handler: 'packages/services/api-admin-survey/function-api.surveySearch',
        functionName: `${stack.stackName}-search`
    })
    const surveyResultsExportImport = new Function(stack, `${stack.stackName}-export-import`, {
        handler: 'packages/services/api-admin-survey/function-async.exportImportAsyncRunner',
        functionName: `${stack.stackName}-export-import`
    })

    const memberSurveyFunction = new Function(stack, `${stack.stackName}-member-survey`, {
        handler: 'packages/services/members/function-api.memberSurvey',
        functionName: `${stack.stackName}-member-survey`,
    });

    const functions = [
        surveyAdminFunction, calendarsAdminFunction, surveySearchFunction, surveyResultsExportImport, memberSurveyFunction
    ]
    functions.forEach(f => {
        f.addEnvironment('TABLE_SURVEY_RESULTS', surveyResultTableName)
        f.addEnvironment('TABLE_SURVEY_CALENDARS', surveyCalendarsTableName)
        f.addEnvironment('TABLE_SURVEY_CONFIGS', surveyConfigsTableName)
        f.addEnvironment('TABLE_MEMBER_CONFIGS', memberSurveyTableName)
        f.addEnvironment('TABLE_MEMBER_RESULTS', memberResultTableName)
        f.addEnvironment('TABLE_MEMBER_RESULTS_HISTORY', memberResultHistoryTableName)
        f.addEnvironment('TABLE_SURVEY_RESULTS_HISTORY', surveyResultsHistoryTableName)
        f.addEnvironment('TABLE_PLATFORM_OLD_TABLE', previousPlatformTable)
        f.addEnvironment('RESOURCES_BUCKET', storageBucket)
        f.addEnvironment('VUE_APP_LIFF_WEB_URL', `https://${dnsResources.domainNames.liff.web}`)
        f.addEnvironment('BUCKET_SURVEYIMG_RESOURCES', publicResourcesBucket.bucketName)
        f.addEnvironment('FUNCTION_SURVEY_SEARCH', searchFunction.functionName)
        f.attachPermissions(["s3", "lambda", "dynamodb"])

        f.addToRolePolicy(actionLogsStreamWritePermissionStatement)
        f.addEnvironment('ACTION_LOGS_DELIVERY_STREAM', actionLogsStream.ref)
    })
    surveyAdminFunction.addEnvironment('FUNCTION_ASYNC_JOBS_HANDLER', surveyResultsExportImport.functionName)

    if (config.isUsingPayment()) {
        const { gatewayFunction, paymentResultTable, paymentConfigTable } = use(Payment)
        functions.forEach(f => {
            f.addEnvironment('PAYMENT_GATEWAY_LAMBDA_NAME', gatewayFunction.functionName)
            f.attachPermissions([gatewayFunction])
            f.addEnvironment('TABLE_PAYMENT_RESULTS', paymentResultTable.tableName)
            f.addEnvironment('TABLE_PAYMENT_CONFIGS', paymentConfigTable.tableName)
        })
    }

    return {
        surveyAdminFunction, memberSurveyFunction, calendarsAdminFunction, surveySearchFunction
    }
}
