import { StackContext } from "sst/constructs";
import * as ses from 'aws-cdk-lib/aws-ses';
import { Function } from 'aws-cdk-lib/aws-lambda';
import * as actions from 'aws-cdk-lib/aws-ses-actions';
import { IHostedZone, MxRecord } from "aws-cdk-lib/aws-route53";
import { IBucket } from "aws-cdk-lib/aws-s3";
import { aws_sns, aws_sns_subscriptions } from "aws-cdk-lib";
import { IQueue } from "aws-cdk-lib/aws-sqs";
import { AwsCustomResource, AwsCustomResourcePolicy, PhysicalResourceId } from 'aws-cdk-lib/custom-resources';
import { Effect, PolicyStatement } from "aws-cdk-lib/aws-iam";
import { config } from "../../config.js";

export function addEmailIdentity(ctx: StackContext, id: string, props: {
    hostedZone: IHostedZone
}) {
    const identity = new ses.EmailIdentity(ctx.stack, id, {
        identity: ses.Identity.publicHostedZone(props.hostedZone)
    })
    new MxRecord(ctx.stack, `${id}-mxrecord`, {
        recordName: props.hostedZone.zoneName,
        zone: props.hostedZone,
        values: [{ hostName: `inbound-smtp.${ctx.app.region}.amazonaws.com`, priority: 10 }],
        comment: 'MX record for SES email receiving'
    })
    return { identity }
}

export function addMailTrigger(ctx: StackContext, props: {
    recipientEmail: string,
    s3ToStore: IBucket,
    triggerLambda: Function,
    dlq: IQueue
}) {
    const snsTopic = new aws_sns.Topic(ctx.stack, 'dist-ses-recipient-topic', {
        topicName: ctx.app.logicalPrefixedName('received-emails')
    })
    snsTopic.addSubscription(new aws_sns_subscriptions.LambdaSubscription(props.triggerLambda, {
        deadLetterQueue: props.dlq
    }))

    // if we try to add the RecieptRuleSet in the first deployment, it will fail,
    // because it can't not put a test email into the bucket
    if (!config.isFirstDeployment()) {
        const ruleSet = new ses.ReceiptRuleSet(ctx.stack, 'dist-ses-recipient-rules');
        ruleSet.addRule('ReceiveTriggerMessages', {
            recipients: [props.recipientEmail],
            enabled: true,
            actions: [
                new actions.S3({
                    objectKeyPrefix: `emails/${props.recipientEmail}`,
                    bucket: props.s3ToStore,
                    topic: snsTopic,
                })
            ]
        })

        new AwsCustomResource(ctx.stack, 'ActivateSesRuleSetResource', {
            onCreate: {
                service: 'SES',
                action: 'setActiveReceiptRuleSet',
                parameters: {
                    RuleSetName: ruleSet.receiptRuleSetName,
                },
                physicalResourceId: PhysicalResourceId.of(ruleSet.receiptRuleSetName)
            },
            onDelete: {
                service: 'SES',
                action: 'setActiveReceiptRuleSet',
                parameters: {
                },
            },
            policy: AwsCustomResourcePolicy.fromStatements([new PolicyStatement({
                actions: ['ses:SetActiveReceiptRuleSet'],
                resources: ['*'],
                effect: Effect.ALLOW,
            })])
        });

    }

}
