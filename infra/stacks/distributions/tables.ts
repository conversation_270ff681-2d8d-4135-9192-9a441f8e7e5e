import {TableProps} from "sst/constructs";

export const RESOURCES: TableProps = {
    fields: {
        primaryKey: "string",
        type: "string"
    },
    primaryIndex: {
        partitionKey: 'primaryKey',
    },
    globalIndexes: {
        "type-index": {
            partitionKey: "type",
            projection: "all"
        }
    },
}

export const REMINDERS: TableProps = {
    fields: {
        partitionKey: "string",
        sortKey: "string"
    },
    primaryIndex: {
        partitionKey: 'partitionKey',
        sortKey: 'sortKey'
    },
    globalIndexes: {
    },
    timeToLiveAttribute: "expireAt"
}

export const CONFIGS_RESULTS: TableProps = {
    fields: {
        id: "string",
        type: "string",
        state: "string",
        scheduledStartAt: "number"
    },
    primaryIndex: {
        partitionKey: 'id',
        sortKey: 'type'
    },
    globalIndexes: {
        "state-scheduledStartAt-index": {
            partitionKey: "state",
            sortKey: "scheduledStartAt",
            projection: ["startType", "recurringSettings", "name"]
        }
    },
}

export const STATE: TableProps = {
    fields: {
        partitionKey: "string",
        sortKey: "string",
        status: "string"
    },
    primaryIndex: {
        partitionKey: 'partitionKey',
        sortKey: 'sortKey'
    },
    globalIndexes: {
        "status-index": {
            partitionKey: "status",
            projection: "all"
        }
    },
    timeToLiveAttribute: "expireAt"
}
