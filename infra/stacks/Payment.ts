import {Function, StackContext, use} from "sst/constructs";
import {Effect, PolicyStatement} from "aws-cdk-lib/aws-iam";
import {config} from "../config.js";
import { FullStack as Survey } from "./Survey.js";
import {dynamo} from "../infra-helpers.js";
import {ApiRouteProps} from "sst/constructs/Api";
import * as Platform from "./Platform.js";
import {Stack as Vpc} from "./Vpc.js";

export default function payment({app, stack}: StackContext)  {
    const {dnsResources, logEventBus, resourcesBucket} = use(Platform.Stack);
    const survey = use(Survey);
    const { vpc } = use(Vpc);

    const paymentConfigTable = dynamo.newTable(stack)('paymentConfigs', {
        fields: {
            partitionKey: "string",
            sortKey: "string"
        },
        primaryIndex: {
            partitionKey: 'partitionKey',
            sortKey: 'sortKey'
        }
    });

    const paymentResultTable = dynamo.newTable(stack)('paymentResults', {
        fields: {
            partitionKey: "string",
            sortKey: "string"
        },
        primaryIndex: {
            partitionKey: 'partitionKey',
            sortKey: 'sortKey'
        },
        globalIndexes: {
            'sortKey-index': {
                partitionKey: 'sortKey',
                projection: 'all'
            }
        }
    });

    const gatewayFunction = new Function(stack, 'gateway-function', {
        handler: 'packages/services/payment/gateway/handler.handler',
        memorySize: 1024,
        timeout: 900,
        enableLiveDev: true,
        vpc,
        permissions: [
            'lambda',
            new PolicyStatement({
                actions: ['dynamodb:*'],
                resources: [
                    `arn:aws:dynamodb:${app.region}:${app.account}:table/`,
                    paymentResultTable.tableArn
                ]
            })
        ],
        environment: {
            DATABASE_PAYMENT_RESULTS: paymentResultTable.tableName,
            VUE_APP_PAYMENT_MERCHANT_ID: config.getString("payment.merchant-id"),
            PAYMENT_API_AUTH_ID: config.getString("payment.api.id"),
            PAYMENT_API_AUTH_PASS: config.getString("payment.api.pass"),
            VUE_APP_PAYMENT_API_KEY: config.getString("payment.api.key"),
            PAYMENT_API_TYPE_ENDPOINT_URL: config.getString("payment.endpoint"),
        }
    });

    const temporaryResourceFunction = new Function(stack, 'temporary-results', {
        handler: 'packages/services/payment/temporary-results/handler.handler',
        memorySize: 1024,
        timeout: 900,
        vpc: undefined, // TODO
        permissions: [
            'lambda',
            new PolicyStatement({
                actions: ['dynamodb:*'],
                resources: [
                    survey.surveyCalendarsTable.tableArn, survey.surveyConfigsTable.tableArn, survey.surveyResultTable.tableArn
                ]
            })
        ],
        environment: {
            TABLE_SURVEY_RESULTS: survey.surveyResultTableName,
            VITE_USE_PAYMENT: '1'
        }
    });

    const receptionApiFunction = new Function(stack, 'reception-api', {
        handler: 'packages/services/payment/api/function-reception.main',
        memorySize: 1024,
        timeout: 900,
        permissions: [
            'lambda',
            new PolicyStatement({
                actions: ['dynamodb:*'],
                resources: [
                    paymentResultTable.tableArn, paymentConfigTable.tableArn, survey.surveyResultTable.tableArn
                ]
            }),
            new PolicyStatement({
                effect: Effect.ALLOW,
                actions: ["lambda:InvokeAsync", "lambda:InvokeFunction"],
                resources: [gatewayFunction.functionArn]
            })
        ],
        environment: {
            TABLE_SURVEY_RESULTS: survey.surveyResultTableName,
            TABLE_SURVEY_CONFIGS: survey.surveyConfigsTableName,
            TABLE_PAYMENT_RESULTS: paymentResultTable.tableName,
            TABLE_PAYMENT_CONFIGS: paymentConfigTable.tableName,
            TABLE_SURVEY_RESULTS_HISTORY: survey.surveyResultsHistoryTableName,
            PAYMENT_GATEWAY_LAMBDA_NAME: gatewayFunction.functionName,
            VUE_APP_LIFF_WEB_URL: `https://${dnsResources.domainNames.liff.web}`,
            VUE_APP_PAYMENT_MERCHANT_ID: config.getString('payment.merchant-id'),
            VUE_APP_PAYMENT_API_KEY: config.getString('payment.api.key'),
            LINEMESSAGING_CHANNEL_ACCESS_TOKEN: config.getString('global.line.messagingAccessToken'),
            DEVELOPMENT_MODE: config.getString('deployment.isDevelopmentEnvironment'),
            LINELOGIN_CHANNEL_ID: config.getString('global.line.loginChannelId'),
            LOG_EVENT_BUS: logEventBus.eventBusName,
            TABLE_SURVEY_CALENDARS: survey.surveyCalendarsTableName,
            TABLE_LINE_USERS: survey.lineUsersTableName,
        }
    });

    const adminApi = new Function(stack, 'admin-api', {
        handler: 'packages/services/payment/api/function-admin.main',
        memorySize: 1024,
        timeout: 900,
        permissions: [
            'lambda',
            new PolicyStatement({
                actions: ['dynamodb:*'],
                resources: [
                    paymentResultTable.tableArn,
                    paymentConfigTable.tableArn,
                    survey.surveyResultTable.tableArn,
                    survey.surveyCalendarsTable.tableArn
                ]
            }),
            new PolicyStatement({
                actions: ["s3:*"],
                resources: [
                    resourcesBucket.bucketArn,
                    resourcesBucket.bucketArn + '/*'
                ]
            }),
            new PolicyStatement({
                effect: Effect.ALLOW,
                actions: ["lambda:InvokeAsync", "lambda:InvokeFunction"],
                resources: [gatewayFunction.functionArn]
            })
        ],
        environment: {
            TABLE_SURVEY_RESULTS: survey.surveyResultTableName,
            TABLE_PAYMENT_RESULTS: paymentResultTable.tableName,
            TABLE_PAYMENT_CONFIGS: paymentConfigTable.tableName,
            PAYMENT_GATEWAY_LAMBDA_NAME: gatewayFunction.functionName,
            TABLE_SURVEY_CALENDARS: survey.surveyCalendarsTableName,
            RESOURCES_BUCKET: resourcesBucket.bucketName,
            DEVELOPMENT_MODE: config.getString('deployment.isDevelopmentEnvironment'),
            LOG_EVENT_BUS: logEventBus.eventBusName
        }
    });

    function registerRoutes(func: Function, routes: string[]): Record<string, ApiRouteProps<any>> {
        const result: Record<string, ApiRouteProps<any>> = {};
        for (const route of routes) {
            if (route.startsWith('OPTIONS')) {
                result[route] = { function: func, authorizer: 'none' };
            } else {
                result[route] = func;
            }
        }

        return result;
    }

    function addReceptionRoutes(): Record<string, ApiRouteProps<any>> {
        return registerRoutes(receptionApiFunction, [
            'ANY /survey/api/v1/payment/configs/{proxy+}',
            'ANY /survey/api/v1/payment/results/{proxy+}',

            'ANY /payment/configs/{proxy+}',
            'ANY /payment/results/{proxy+}'
        ].map(route => {
            return [route, route.replace('ANY', 'OPTIONS')]
        }).flat());
    }

    function getAdminRoutes(): Record<string, ApiRouteProps<any>> {
        return registerRoutes(adminApi, [
            'ANY /v1/calendars/{proxy+}',
            'ANY /calendars/{proxy+}',

            'ANY /v1/payment/configs/{proxy+}',
            'ANY /payment/configs/{proxy+}',

            'ANY /v1/payment/results/{proxy+}',
            'ANY /payment/results/{proxy+}'
        ].map(route => {
            return [route, route.replace('ANY', 'OPTIONS')]
        }).flat());
    }

    return {
        gatewayFunction,
        temporaryResourceFunction,
        paymentConfigTable,
        paymentResultTable,
        receptionApiFunction,
        adminApi,
        addReceptionRoutes,
        getAdminRoutes
    }
}