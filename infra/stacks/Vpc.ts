import {StackContext} from "sst/constructs";
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { RemovalPolicy } from "aws-cdk-lib";


export function Stack({stack, app}: StackContext) {
    const vpc = new ec2.Vpc(stack, 'vpc', {        
        maxAzs: 1, // Provide availability across 2 AZs
        natGateways: 0, // Specify number of NAT Gateways
        subnetConfiguration: [
            {
                cidrMask: 24,
                name: 'public-subnet',
                subnetType: ec2.SubnetType.PUBLIC, // Public subnet for NAT Gateway
            },
            {
                cidrMask: 24,
                name: 'private-subnet',
                subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS, // Private subnet for Lambda
            },
        ],        
    });
    vpc.applyRemovalPolicy(RemovalPolicy.DESTROY);
    // create Elastic IP address and new NAT Gateway
    const eip = new ec2.CfnEIP(stack, 'ExternalIp')
    eip.applyRemovalPolicy(RemovalPolicy.DESTROY);
    
    const ngw = new ec2.CfnNatGateway(stack, 'NatGateway', {
        tags: [{key: 'Name', value: `${app.stage}-${app.name}-natgw`}],
        allocationId: eip.attrAllocationId,
        subnetId: vpc.publicSubnets[0].subnetId,
    })
    ngw.applyRemovalPolicy(RemovalPolicy.DESTROY);

    // Add a default route into the private subnet's route table
    const privateSubnet = vpc.privateSubnets[0]
    new ec2.CfnRoute(stack, 'PrivateSubnetRoute', {
        routeTableId: privateSubnet.routeTable.routeTableId,
        destinationCidrBlock: '0.0.0.0/0',
        natGatewayId: ngw.attrNatGatewayId,
    })

    vpc.addGatewayEndpoint('DynamoDBEndpoint', {service: ec2.GatewayVpcEndpointAwsService.DYNAMODB});

    new ssm.StringParameter(stack, 'IP Address', {
        parameterName: `/${app.name}/${app.stage}/payment/outboundIpAddress`, // The name of the parameter
        stringValue: eip.attrPublicIp,
        description: 'The public Elastic IP address of the NAT Gateway',
    });


    stack.addOutputs({
        vpcCidrBlock: vpc.vpcCidrBlock,
        fixedIp: eip.attrPublicIp,
    })

    return {
        vpc,
    }

}