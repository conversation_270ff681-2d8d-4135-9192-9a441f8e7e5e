import { Stack, Function, App, use, Api } from "sst/constructs";
import * as dns from '../Dns.js';
import * as platform from '../Platform.js';

export function createAPIGateway(stack: Stack, app: App, scenarioTables: { [key: string]: string }, indexs: { [key: string]: string }) {
    const platformStack = use(platform.Stack);
    const dnsStack = use(dns.Stack);

    //  pass options cors
    const preflightResponder = new Function(stack, `${stack.stackName}-preflight`, {
        functionName: `${stack.stackName}-preflight`,
        handler: 'packages/services/damage-report/functions/function-preflight.handler',
        memorySize: 128,
        timeout: 5,
        nodejs: {
            format: 'esm',
            install: []
        }
    });

    // scenario api handler
    const scenarioFunction = new Function(stack, "scenarioAdminFunction", {
        functionName: `${stack.stackName}-scenario-api`,
        handler: "packages/services/damage-report/api-scenario/function-api.apiHandler",
        environment: {
            TABLE_CHATBOT_SCENARIO_DATA: scenarioTables.TABLE_CHATBOT_SCENARIO_DATA,
            TABLE_CHATBOT_SCENARIO_DATA_DATATYPE_DATAID_INDEX: indexs.chatBotScenarioDataGsiDataTypeDataIdIndex,
            TABLE_CHATBOT_SCENARIO_DATA_SCENARIO_DATATYPE_INDEX: indexs.chatBotScenarioDataScenarioDataTypeIndex,
            TABLE_CHATBOT_SCENARIO: scenarioTables.TABLE_CHATBOT_SCENARIO,
            TABLE_CHATBOT_USER_SESSION: scenarioTables.TABLE_CHATBOT_USER_SESSION,
            TABLE_SCENARIO_DATA: scenarioTables.TABLE_SCENARIO_DATA,
        }
    });

    // admin api handler
    const adminFunction = new Function(stack, "damageReportAdminFunction", {
        functionName: `${stack.stackName}-admin-api`,
        handler: "packages/services/damage-report/api-admin/function-api.apiHandler",
        environment: {
            TABLE_CHATBOT_SCENARIO_DATA: scenarioTables.TABLE_CHATBOT_SCENARIO_DATA
        }
    });

    // liff api handler
    const liffFunction = new Function(stack, "damageReportLiffFunction", {
        functionName: `${stack.stackName}-liff-api`,
        handler: "packages/services/damage-report/api-liff/function-api.apiHandler",
        environment: {
            TABLE_CHATBOT_SCENARIO_DATA: scenarioTables.TABLE_CHATBOT_SCENARIO_DATA
        }
    });

    // Liff auth
    const liffAuthFunction = new Function(stack, "damageReportLiffAuthFunction", {
        functionName: `${stack.stackName}-damage-liff-auth`,
        handler: "packages/services/damage-report/functions/liff-auth.handler",
        memorySize: 256,
        timeout: 5,
        nodejs: {
            format: 'esm',
            install: []
        }
    });

    // api gateway admin
    const damageApi = new Api(stack, "damage-report-api", {
        accessLog: true,
        cors: true,
        customDomain: {
            domainName: dnsStack.domainNames.damageReport.api,
            hostedZone: dnsStack.hostedZone.zoneName
        },
        authorizers: {
            'adminPool': {
                userPool: {
                    id: platformStack.userPoolId,
                    clientIds: [platformStack.userPoolClientId],
                    region: app.region
                },
                type: 'user_pool'
            },
            'liffVerify': {
                type: "lambda",
                function: liffAuthFunction,
                resultsCacheTtl: "5 minutes"
            }
        },
        defaults: {
            authorizer: "adminPool"
        },
        routes: {
            "ANY /scenario/{proxy+}": scenarioFunction,
            "OPTIONS /scenario/{proxy+}": { function: preflightResponder, authorizer: 'none' },

            "ANY /admin/{proxy+}": adminFunction,
            "OPTIONS /admin/{proxy+}": { function: preflightResponder, authorizer: 'none' },

            "ANY /liff/{proxy+}": { function: liffFunction, authorizer: 'liffVerify' },
            "OPTIONS /liff/{proxy+}": { function: preflightResponder, authorizer: 'none' },
        }
    });

    return {
        damageApi,
        adminFunction,
        liffFunction,
        scenarioFunction,
        damageApiUrl: damageApi.customDomainUrl || damageApi.url
    }
}