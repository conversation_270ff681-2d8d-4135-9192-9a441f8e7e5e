import esbuildPluginTsc from 'esbuild-plugin-tsc';
import { StackContext, use } from "sst/constructs";
import { config } from "../../config.js";
import * as dns from '../Dns.js';
import * as scenario from '../Scenario.js';
import * as shareResource from '../ShareResources.js';
import { createAPIGateway } from './api-gateway.js';
import { InfraChatNotify } from "./chat-notify/infra.js";
import { consumeCreateDamageReportQueue, createDynamoDB, createJobs, createStorage, createWebStatic } from "./resources.js";

export function Stack({ app, stack }: StackContext) {
    const shareResourceStack = use(shareResource.ShareStack);
    const dnsStack = use(dns.Stack);
    const scenarioStack = use(scenario.Stack);

    // s3
    const { damageReportMainStorage } = createStorage(stack, app);

    // dynamodb
    const {
        damageReportData,
        groupIdCreatedAtIndex,
        groupIdUpdatedAtIndex,
        uidUpdatedAtIndex,
        damageReportGeo,
        damageReportGeoHashIndex,
        cacheDamageReportTable
    } = createDynamoDB(stack);

    // default functions
    stack.setDefaultFunctionProps({
        runtime: "nodejs18.x",
        timeout: 29,
        memorySize: 512,
        nodejs: {
            esbuild: {
                plugins: [esbuildPluginTsc({
                    tsconfigPath: 'packages/services/damage-report/tsconfig.json',
                    force: true,
                })],
                // not include these package in bundle
                external: [
                    '@aws-sdk/client-dynamodb',
                    '@aws-sdk/client-s3',
                    '@aws-sdk/s3-request-presigner',
                    '@aws-sdk/lib-dynamodb',
                    '@aws-sdk/util-dynamodb',
                    '@aws-sdk/client-apigatewaymanagementapi'
                ]
            },
            format: 'esm',
            // move these packages to node module
            install: [
                'class-transformer',
                'class-validator',
                'reflect-metadata',
                'date-fns',
                'lambda-api',
                '@aws-sdk/client-sts',
                'lodash',
                'luxon',
                'moment',
                'moment-timezone',
                'uuid',
                'semver',
                'safe-buffer',
                'jsonwebtoken',
                "nodes2ts",
                "long",
                '@fast-csv/format'
            ]
        },
        environment: {
            'DAMAGE_REPORT_DATA_TABLE': damageReportData.tableName,
            'DAMAGE_REPORT_DATA_TABLE_INDEX_GROUPID_CREATED': groupIdCreatedAtIndex,
            'DAMAGE_REPORT_DATA_TABLE_INDEX_GROUPID_UPDATED': groupIdUpdatedAtIndex,
            'DAMAGE_REPORT_DATA_TABLE_INDEX_UID_UPDATED': uidUpdatedAtIndex,
            'DAMAGE_REPORT_GEO_TABLE': damageReportGeo.tableName,
            'DAMAGE_REPORT_GEO_INDEX_GEOHASH': damageReportGeoHashIndex,
            'MAIN_DAMAGE_REPORT_STORAGE': damageReportMainStorage.bucketName,
            'LINELOGIN_CHANNEL_ID': config.getString("global.line.loginChannelId"),
            'CACHE_TOTAL_DR_TABLE': cacheDamageReportTable.tableName
        },
        permissions: ['dynamodb', 's3', 'execute-api']
    })

    // damage report api
    const { damageApi, adminFunction, liffFunction, damageApiUrl } = createAPIGateway(stack, app, scenarioStack.tables, scenarioStack.tableIndexs);

    // sqs
    const { functionConsumeDamageReportCreate } = consumeCreateDamageReportQueue(stack, shareResourceStack.createDamageReportQueue, damageReportData.tableName);

    // websocket - chat - notify
    const infraChatNotify = new InfraChatNotify(stack, app);
    const resultInfraChat = infraChatNotify.createResources();

    const websocketChatNotify = resultInfraChat.websocket.customDomainUrl || resultInfraChat.websocket.url;

    // website - liff 
    const liffDamageUrl = createWebStatic(stack, dnsStack, damageApiUrl, websocketChatNotify);

    // stream dynamodb
    damageReportData.addConsumers(stack, {
        totalConsumer: {
            function: {
                functionName: `${stack.stackName}-consume-total-dr-stream`,
                handler: "packages/services/damage-report/functions/consume-dr-stream-total.handler",
                memorySize: 256,
                timeout: 20,
                nodejs: {
                    esbuild: {
                        external: [
                            '@aws-sdk/client-dynamodb',
                            '@aws-sdk/client-s3',
                            '@aws-sdk/s3-request-presigner',
                            '@aws-sdk/lib-dynamodb',
                            '@aws-sdk/util-dynamodb',
                            '@aws-sdk/client-apigatewaymanagementapi'
                        ]
                    }
                },
                environment: {
                    'CACHE_TOTAL_DR_TABLE': cacheDamageReportTable.tableName
                },
                permissions: ['dynamodb', 'execute-api']
            }
        }
    });
    const consumeTotalDrFunction = damageReportData.getFunction('totalConsumer')
    const arrFunctionAddEnv = [
        adminFunction,
        liffFunction,
        functionConsumeDamageReportCreate
    ]
    if (consumeTotalDrFunction) arrFunctionAddEnv.push(consumeTotalDrFunction)   
    infraChatNotify.addEnvToFunction(arrFunctionAddEnv);

    // jobs
    createJobs(stack);

    const result = {
        damageApiUrl: damageApi.url,
        damageApiCustomUrl: damageApi.customDomainUrl,
        mainS3DamageStorage: damageReportMainStorage.bucketName,
        liffDamageUrl,
        websocketChatNotify
    }

    stack.addOutputs(result);

    return result
}