import { App, Function, Stack, WebSocketApi } from "sst/constructs";
import { infraDynamo, ReturnDatabaseInfra } from "./database";
import { createWebSocket } from "./websocket";

export class InfraChatNotify {
    private resultTable!: ReturnDatabaseInfra;
    private websocketApi!: WebSocketApi;
    constructor(public stack: Stack, public app: App) { }

    createResources() {
        const resultTable = infraDynamo(this.stack);
        const websocket = createWebSocket(this.stack, resultTable);

        this.resultTable = resultTable;
        this.websocketApi = websocket;
        return {
            tables: resultTable,
            websocket
        }
    }
    /**
     * Add chat env to function handler
     * @param functions 
     */
    addEnvToFunction(functions: Function[]) {
        functions.forEach(func => {
            func.addEnvironment("CHAT_MESSAGE_TABLE", this.resultTable.chatMessageTable.tableName);
            func.addEnvironment("CHAT_MESSAGE_TABLE_TIMESTAMP_INDEX", this.resultTable.chatMessageTable.index);

            func.addEnvironment("CHAT_MEMBER_TABLE", this.resultTable.chatMemberTable.tableName);
            func.addEnvironment("CHAT_MEMBER_TABLE_GID_INDEX", this.resultTable.chatMemberTable.index);

            func.addEnvironment("WS_CONNECTION_TABLE", this.resultTable.wsTable.tableName);
            func.addEnvironment("WS_CONNECTION_ID_INDEX", this.resultTable.wsTable.index);

            func.addEnvironment("CHAT_CONNECTION_GROUP_TABLE", this.resultTable.connectionGroupTable);

            func.addEnvironment("APIG_WS_ENDPOINT", this.websocketApi.url);
        })
    }
}