import {StackContext} from "sst/constructs";
import {HostedZone} from "aws-cdk-lib/aws-route53";
import {Certificate, CertificateValidation, DnsValidatedCertificate} from "aws-cdk-lib/aws-certificatemanager";
import {getDnsNames} from "./platform/dns.js";
import {config} from "../config.js";


export function Stack(ctx: StackContext) {
    const { app, stack } = ctx
    const baseDomainName = config.getString('global.baseDomain')
    const hostedZone = HostedZone.fromLookup(ctx.stack, 'MainHostedZone', {
        domainName: baseDomainName,
    });

    const globalCertificate = new DnsValidatedCertificate(stack, 'DomainCertification', {
        domainName: baseDomainName,
        hostedZone,
        region: 'us-east-1',
        subjectAlternativeNames: [`*.${baseDomainName}`],
    })

    const domainNames = getDnsNames()

    return {
        baseDomainName,
        hostedZone,
        domainNames,
        globalCertificate,
    }
}
