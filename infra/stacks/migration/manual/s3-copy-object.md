# S3 coy object の実行 - Development environment

## 観光機能(optional) - development environment

### Bucket name

| Source S3 bucket name                                          | Destination S3 bucket name                              |
| :------------------------------------------------------------- | :------------------------------------------------------ |
| pnl-stg-spot-scenario-st-chatbotimportedresources-9rd6yqqmzi3y | spot-oss-scenario-chatbotresourcesf53bbf29-wppl0a7ytryv |

### Run command - development environment

```bash
aws glue --profile pnl-lsc-stg start-job-run --job-name 'spot-oss-glue-migration-job-s3CopyObjectJob' --arguments='--source_bucket=pnl-stg-spot-scenario-st-chatbotimportedresources-9rd6yqqmzi3y,--dest_bucket=spot-oss-scenario-chatbotresourcesf53bbf29-wppl0a7ytryv'
```
