# Check item count - Development environment

- Get item count in source table and dest table on development environment

- distribution-configs should be check data in AWS Console

## Table name - development environment

| Source table name                                                   | Destination table name          |
| :------------------------------------------------------------------ | :------------------------------ |
| lsc-dev-survey-static-SurveyResults-1IXUWNFPTJBBC                   | phuong-oss-surveyResults        |
| lsc-dev-platform-static-TableAdminDatabase-KY0NX6DINPPU             | phuong-oss-platformOldTable     |
| lsc-dev-scenario-static-TableStampTitle-1N88F243PGLM1               | phuong-oss-stampTitle           |
| lsc-dev-scenario-static-TableUserSession-FUH65H2YVDIB               | phuong-oss-userSession          |
| lsc-dev-survey-static-LineUsers-13Y29X3GBUNSJ                       | phuong-oss-lineUsers            |
| lsc-dev-survey-static-SurveyConfigs-1JS2ROL44LIVM                   | phuong-oss-surveyConfigs        |
| lsc-dev-survey-static-SurveyResults-1IXUWNFPTJBBC                   | phuong-oss-surveyResults        |
| lsc-dev-survey-static-SurveyResultsHistory-7K6NY5PYP0FJ             | phuong-oss-surveyResultsHistory |
| lsc-dev-survey-static-SurveyLogs-R3PMA9XJE71O                       | phuong-oss-surveyLogs           |
| lsc-dev-survey-static-SurveyCalendars-RV4VF4V85S3E                  | phuong-oss-surveyCalendars      |
| lsc-dev-distribution-static-ReservationRemindersTable-1IHWP9UPZ61QV | phuong-oss-distReminders        |
| lsc-dev-scenario-static-TableScenarioData-1WYRSYA885U2Y             | phuong-oss-scenarioData         |
| lsc-dev-scenario-static-TableChatBotScenario-97P441QB4IHE           | phuong-oss-chatBotScenario      |
| lsc-dev-scenario-static-TableChatBotScenarioData-SBN0385BE49T       | phuong-oss-chatBotScenarioData  |
| lsc-dev-scenario-static-TableScenarioLogs-J6I13ENL8PP3              | phuong-oss-scenarioLogs         |
| lsc-dev-bosai-static-TableBosaiShelter-1SDCC5QBGPQYW                | phuong-oss-bosaiShelter         |

## Run job command - development environment

```bash
aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-countItemsInTableJob" --arguments='--src_table_name=lsc-dev-platform-static-TableAdminDatabase-KY0NX6DINPPU,--dest_table_name=phuong-oss-platformOldTable'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-countItemsInTableJob" --arguments='--src_table_name=lsc-dev-scenario-static-TableStampTitle-1N88F243PGLM1,--dest_table_name=phuong-oss-stampTitle'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-countItemsInTableJob" --arguments='--src_table_name=lsc-dev-scenario-static-TableUserSession-FUH65H2YVDIB,--dest_table_name=phuong-oss-userSession'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-countItemsInTableJob" --arguments='--src_table_name=lsc-dev-survey-static-LineUsers-13Y29X3GBUNSJ,--dest_table_name=phuong-oss-lineUsers'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-countItemsInTableJob" --arguments='--src_table_name=lsc-dev-survey-static-SurveyConfigs-1JS2ROL44LIVM,--dest_table_name=phuong-oss-surveyConfigs'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-countItemsInTableJob" --arguments='--src_table_name=lsc-dev-survey-static-SurveyResults-1IXUWNFPTJBBC,--dest_table_name=phuong-oss-surveyResults'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-countItemsInTableJob" --arguments='--src_table_name=lsc-dev-survey-static-SurveyResultsHistory-7K6NY5PYP0FJ,--dest_table_name=phuong-oss-surveyResultsHistory'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-countItemsInTableJob" --arguments='--src_table_name=lsc-dev-survey-static-SurveyLogs-R3PMA9XJE71O,--dest_table_name=phuong-oss-surveyLogs'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-countItemsInTableJob" --arguments='--src_table_name=lsc-dev-survey-static-SurveyCalendars-RV4VF4V85S3E,--dest_table_name=phuong-oss-surveyCalendars'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-countItemsInTableJob" --arguments='--src_table_name=lsc-dev-distribution-static-ReservationRemindersTable-1IHWP9UPZ61QV,--dest_table_name=phuong-oss-distReminders'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-countItemsInTableJob" --arguments='--src_table_name=lsc-dev-scenario-static-TableScenarioData-1WYRSYA885U2Y,--dest_table_name=phuong-oss-scenarioData'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-countItemsInTableJob" --arguments='--src_table_name=lsc-dev-scenario-static-TableChatBotScenario-97P441QB4IHE,--dest_table_name=phuong-oss-chatBotScenario'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-countItemsInTableJob" --arguments='--src_table_name=lsc-dev-scenario-static-TableChatBotScenarioData-SBN0385BE49T,--dest_table_name=phuong-oss-chatBotScenarioData'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-countItemsInTableJob" --arguments='--src_table_name=lsc-dev-scenario-static-TableScenarioLogs-J6I13ENL8PP3,--dest_table_name=phuong-oss-scenarioLogs'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-countItemsInTableJob" --arguments='--src_table_name=lsc-dev-bosai-static-TableBosaiShelter-1SDCC5QBGPQYW,--dest_table_name=phuong-oss-bosaiShelter'
```
