# Migration Cognito User - Development environment

## Cognito User pool - development environment

| Source User pool         | Destination User pool    |
| :----------------------- | :----------------------- |
| ap-northeast-1_acibkPoDb | ap-northeast-1_BhyfmXO6B |

## Run job command - development environment

- Case auto send email and auto generate password:

```bash
aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-migrationCognitoUserJob" --arguments='--src_user_pool_id=ap-northeast-1_acibkPoDb,--dest_user_pool_id=ap-northeast-1_BhyfmXO6B'
```

- Case not send email and manual set password:

```bash
aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-migrationCognitoUserJob" --arguments='--src_user_pool_id=ap-northeast-1_acibkPoDb,--dest_user_pool_id=ap-northeast-1_BhyfmXO6B,--temp_pwd=Playnext2024!,--is_not_send_email=true'
```
