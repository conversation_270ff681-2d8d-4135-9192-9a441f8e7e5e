# Delete data in OSS table - Development environment

> [!CAUTION]
> Using when you want to delete data from table OSS, not used for LSC table

## Table name - development environment

| Table name                      |
| :------------------------------ |
| phuong-oss-distribution-configs |
| phuong-oss-surveyResults        |
| phuong-oss-platformOldTable     |
| phuong-oss-stampTitle           |
| phuong-oss-userSession          |
| phuong-oss-lineUsers            |
| phuong-oss-surveyConfigs        |
| phuong-oss-surveyResultsHistory |
| phuong-oss-surveyLogs           |
| phuong-oss-surveyCalendars      |
| phuong-oss-distReminders        |
| phuong-oss-scenarioData         |
| phuong-oss-chatBotScenario      |
| phuong-oss-chatBotScenarioData  |
| phuong-oss-scenarioLogs         |
| phuong-oss-bosaiShelter         |
| phuong-oss-scenarioSpotData     |

## Run job command - development environment

```bash
aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-deleteTableDataJob" --arguments='--table_name=phuong-oss-distribution-configs'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-deleteTableDataJob" --arguments='--table_name=phuong-oss-surveyResults'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-deleteTableDataJob" --arguments='--table_name=phuong-oss-platformOldTable'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-deleteTableDataJob" --arguments='--table_name=phuong-oss-stampTitle'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-deleteTableDataJob" --arguments='--table_name=phuong-oss-userSession'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-deleteTableDataJob" --arguments='--table_name=phuong-oss-lineUsers'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-deleteTableDataJob" --arguments='--table_name=phuong-oss-surveyConfigs'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-deleteTableDataJob" --arguments='--table_name=phuong-oss-surveyResultsHistory'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-deleteTableDataJob" --arguments='--table_name=phuong-oss-surveyLogs'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-deleteTableDataJob" --arguments='--table_name=phuong-oss-surveyCalendars'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-deleteTableDataJob" --arguments='--table_name=phuong-oss-distReminders'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-deleteTableDataJob" --arguments='--table_name=phuong-oss-scenarioData'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-deleteTableDataJob" --arguments='--table_name=phuong-oss-chatBotScenario'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-deleteTableDataJob" --arguments='--table_name=phuong-oss-chatBotScenarioData'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-deleteTableDataJob" --arguments='--table_name=phuong-oss-scenarioLogs'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-deleteTableDataJob" --arguments='--table_name=phuong-oss-bosaiShelter'

aws glue --profile lsc-dev-phuong start-job-run --job-name "phuong-oss-glue-migration-job-deleteTableDataJob" --arguments='--table_name=phuong-oss-scenarioSpotData'
```
