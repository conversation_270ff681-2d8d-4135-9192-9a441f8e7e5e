import { StackContext } from "sst/constructs";
import { Effect, PolicyStatement, ManagedPolicy, Role, ServicePrincipal } from "aws-cdk-lib/aws-iam";
import {
    aws_iam
} from 'aws-cdk-lib';
import * as glue_alpha from '@aws-cdk/aws-glue-alpha';

export function Stack(ctx: StackContext) {
    const { stack, app } = ctx

    // create rule for glue
    const glueServiceRoleMigrationPolicy = new ManagedPolicy(
        stack,
        `${stack.stackName}-glueServiceRoleMigrationPolicy`,
        {
            managedPolicyName: `${stack.stackName}-glueServiceRoleMigrationPolicy`,
            description: "glueServiceRoleMigrationPolicy",
            statements: [
                new PolicyStatement({
                    effect: Effect.ALLOW,
                    actions: [
                        'cognito-idp:*',
                    ],
                    resources: ["*"],
                }),
                new PolicyStatement({
                    effect: Effect.ALLOW,
                    actions: [
                        's3:*',
                    ],
                    resources: ["*"],
                }),
                new PolicyStatement({
                    effect: Effect.ALLOW,
                    actions: [
                        'dynamodb:*',
                    ],
                    resources: ["*"],
                }),
                new PolicyStatement({
                    effect: Effect.ALLOW,
                    actions: [
                        'cloudwatch:*',
                    ],
                    resources: ["*"],
                }),
            ],
        }
    );

    const glueServiceRoleMigrationRole = new Role(
        stack,
        `${stack.stackName}-glueServiceRoleMigrationRole`,
        {
            roleName: `${stack.stackName}-glueMigrate`,
            assumedBy: new ServicePrincipal('glue.amazonaws.com'),
        }
    );
    glueServiceRoleMigrationRole.addManagedPolicy(glueServiceRoleMigrationPolicy);
    glueServiceRoleMigrationRole.addManagedPolicy(aws_iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSGlueServiceRole'));

    // ----------------------------Migration Cognito group and user Start---------------------------------------------
    // create glue job for migration Cognito group and user
    const migrationCognitoUserJob = new glue_alpha.Job(stack, `${stack.stackName}-migrationCognitoUserJob`,
        {
            jobName: `${stack.stackName}-migrationCognitoUserJob`,
            role: glueServiceRoleMigrationRole,
            maxConcurrentRuns: 200,
            workerCount: 10,
            workerType: glue_alpha.WorkerType.G_1X,
            executable: glue_alpha.JobExecutable.pythonEtl({
                glueVersion: glue_alpha.GlueVersion.V4_0,
                pythonVersion: glue_alpha.PythonVersion.THREE,
                script: glue_alpha.Code.fromAsset('infra/stacks/migration/script/migration-cognito-user.py'),

            }),
            defaultArguments: {
                '--job-bookmark-option': 'job-bookmark-disable',
                '--job-language': 'python',
                '--enable-metrics': 'true',
                '--enable-continuous-cloudwatch-log': 'true',
            },
            continuousLogging: { enabled: true },
            enableProfilingMetrics: true,
        });

    // delete all Cognito group and user
    const deleteCognitoUserAndGroupJob = new glue_alpha.Job(stack, `${stack.stackName}-deleteCognitoUserAndGroupJob`,
        {
            jobName: `${stack.stackName}-deleteCognitoUserAndGroupJob`,
            role: glueServiceRoleMigrationRole,
            maxConcurrentRuns: 200,
            workerCount: 10,
            workerType: glue_alpha.WorkerType.G_1X,
            executable: glue_alpha.JobExecutable.pythonEtl({
                glueVersion: glue_alpha.GlueVersion.V4_0,
                pythonVersion: glue_alpha.PythonVersion.THREE,
                script: glue_alpha.Code.fromAsset('infra/stacks/migration/script/cognito-delete-all-user-and-group.py'),

            }),
            defaultArguments: {
                '--job-bookmark-option': 'job-bookmark-disable',
                '--job-language': 'python',
                '--enable-metrics': 'true',
                '--enable-continuous-cloudwatch-log': 'true',
            },
            continuousLogging: { enabled: true },
            enableProfilingMetrics: true,
        });
    // ----------------------------Migration Cognito group and user End---------------------------------------------

    // ----------------------------配信データ migration job Start---------------------------------------------
    // DynamodbMigration 配信データの移行の実行: メール配信
    const dataMigrationSegmentDeliveryMailJob = new glue_alpha.Job(stack, `${stack.stackName}-dataMigrationSegmentDeliveryMailJob`,
        {
            jobName: `${stack.stackName}-dataMigrationSegmentDeliveryMailJob`,
            role: glueServiceRoleMigrationRole,
            maxConcurrentRuns: 200,
            workerCount: 10,
            workerType: glue_alpha.WorkerType.G_1X,
            executable: glue_alpha.JobExecutable.pythonEtl({
                glueVersion: glue_alpha.GlueVersion.V4_0,
                pythonVersion: glue_alpha.PythonVersion.THREE,
                script: glue_alpha.Code.fromAsset('infra/stacks/migration/script/dynamodb-segment-delivery-mail.py'),

            }),
            defaultArguments: {
                '--job-bookmark-option': 'job-bookmark-disable',
                '--job-language': 'python',
                '--enable-metrics': 'true',
                '--enable-continuous-cloudwatch-log': 'true',
                '--enable-continuous-log-filter': 'false',
            },
            continuousLogging: { enabled: true },
            enableProfilingMetrics: true,
        });

    // DynamodbMigration 配信データの移行の実行: トーク配信
    const dataMigrationSegmentDeliveryTalkJob = new glue_alpha.Job(stack, `${stack.stackName}-dataMigrationSegmentDeliveryTalkJob`,
        {
            jobName: `${stack.stackName}-dataMigrationSegmentDeliveryTalkJob`,
            role: glueServiceRoleMigrationRole,
            maxConcurrentRuns: 200,
            workerCount: 10,
            workerType: glue_alpha.WorkerType.G_1X,
            executable: glue_alpha.JobExecutable.pythonEtl({
                glueVersion: glue_alpha.GlueVersion.V4_0,
                pythonVersion: glue_alpha.PythonVersion.THREE,
                script: glue_alpha.Code.fromAsset('infra/stacks/migration/script/dynamodb-segment-delivery-talk.py'),

            }),
            defaultArguments: {
                '--job-bookmark-option': 'job-bookmark-disable',
                '--job-language': 'python',
                '--enable-metrics': 'true',
                '--enable-continuous-cloudwatch-log': 'true',
                '--enable-continuous-log-filter': 'false',
            },
            continuousLogging: { enabled: true },
            enableProfilingMetrics: true,
        });

    // DynamodbMigration 配信データの移行の実行: 予約配信
    const dataMigrationSegmentDeliveryPostponedJob = new glue_alpha.Job(stack, `${stack.stackName}-dataMigrationSegmentDeliveryPostponedJob`,
        {
            jobName: `${stack.stackName}-dataMigrationSegmentDeliveryPostponedJob`,
            role: glueServiceRoleMigrationRole,
            maxConcurrentRuns: 200,
            workerCount: 10,
            workerType: glue_alpha.WorkerType.G_1X,
            executable: glue_alpha.JobExecutable.pythonEtl({
                glueVersion: glue_alpha.GlueVersion.V4_0,
                pythonVersion: glue_alpha.PythonVersion.THREE,
                script: glue_alpha.Code.fromAsset('infra/stacks/migration/script/dynamodb-segment-delivery-postponed.py'),

            }),
            defaultArguments: {
                '--job-bookmark-option': 'job-bookmark-disable',
                '--job-language': 'python',
                '--enable-metrics': 'true',
                '--enable-continuous-cloudwatch-log': 'true',
                '--enable-continuous-log-filter': 'false',
            },
            continuousLogging: { enabled: true },
            enableProfilingMetrics: true,
        });

    // DynamodbMigration 配信データの移行の実行: 繰り返し配信
    const dataMigrationSegmentDeliveryRepeatingJob = new glue_alpha.Job(stack, `${stack.stackName}-dataMigrationSegmentDeliveryRepeatingJob`,
        {
            jobName: `${stack.stackName}-dataMigrationSegmentDeliveryRepeatingJob`,
            role: glueServiceRoleMigrationRole,
            maxConcurrentRuns: 200,
            workerCount: 10,
            workerType: glue_alpha.WorkerType.G_1X,
            executable: glue_alpha.JobExecutable.pythonEtl({
                glueVersion: glue_alpha.GlueVersion.V4_0,
                pythonVersion: glue_alpha.PythonVersion.THREE,
                script: glue_alpha.Code.fromAsset('infra/stacks/migration/script/dynamodb-segment-delivery-repeating.py'),

            }),
            defaultArguments: {
                '--job-bookmark-option': 'job-bookmark-disable',
                '--job-language': 'python',
                '--enable-metrics': 'true',
                '--enable-continuous-cloudwatch-log': 'true',
                '--enable-continuous-log-filter': 'false',
            },
            continuousLogging: { enabled: true },
            enableProfilingMetrics: true,
        });
    // ----------------------------配信データ migration job End---------------------------------------------

    // DynamodbMigration using ETL Connector Job
    const dataMigrationETLJob = new glue_alpha.Job(stack, `${stack.stackName}-dataMigrationETLJob`,
        {
            jobName: `${stack.stackName}-dataMigrationETLJob`,
            role: glueServiceRoleMigrationRole,
            maxConcurrentRuns: 200,
            workerCount: 10,
            workerType: glue_alpha.WorkerType.G_1X,
            executable: glue_alpha.JobExecutable.pythonEtl({
                glueVersion: glue_alpha.GlueVersion.V4_0,
                pythonVersion: glue_alpha.PythonVersion.THREE,
                script: glue_alpha.Code.fromAsset('infra/stacks/migration/script/migration-etl-connector.py'),

            }),
            defaultArguments: {
                '--job-bookmark-option': 'job-bookmark-disable',
                '--job-language': 'python',
                '--enable-metrics': 'true',
                '--enable-continuous-cloudwatch-log': 'true',
                '--enable-continuous-log-filter': 'false',
            },
            continuousLogging: { enabled: true },
            enableProfilingMetrics: true,
        });

    // DynamodbMigration using boto3
    const dataMigrationBoto3Job = new glue_alpha.Job(stack, `${stack.stackName}-dataMigrationBoto3Job`,
        {
            jobName: `${stack.stackName}-dataMigrationBoto3Job`,
            role: glueServiceRoleMigrationRole,
            maxConcurrentRuns: 200,
            workerCount: 10,
            workerType: glue_alpha.WorkerType.G_1X,
            executable: glue_alpha.JobExecutable.pythonEtl({
                glueVersion: glue_alpha.GlueVersion.V4_0,
                pythonVersion: glue_alpha.PythonVersion.THREE,
                script: glue_alpha.Code.fromAsset('infra/stacks/migration/script/migration-boto3.py'),

            }),
            defaultArguments: {
                '--job-bookmark-option': 'job-bookmark-disable',
                '--job-language': 'python',
                '--enable-metrics': 'true',
                '--enable-continuous-cloudwatch-log': 'true',
                '--enable-continuous-log-filter': 'false',
            },
            continuousLogging: { enabled: true },
            enableProfilingMetrics: true,
        });

    // count items dynamodb table
    const countItemsInTableJob = new glue_alpha.Job(stack, `${stack.stackName}-countItemsInTableJob`,
        {
            jobName: `${stack.stackName}-countItemsInTableJob`,
            role: glueServiceRoleMigrationRole,
            maxConcurrentRuns: 200,
            workerCount: 10,
            workerType: glue_alpha.WorkerType.G_1X,
            executable: glue_alpha.JobExecutable.pythonEtl({
                glueVersion: glue_alpha.GlueVersion.V4_0,
                pythonVersion: glue_alpha.PythonVersion.THREE,
                script: glue_alpha.Code.fromAsset('infra/stacks/migration/script/table-count.py'),

            }),
            defaultArguments: {
                '--job-bookmark-option': 'job-bookmark-disable',
                '--job-language': 'python',
                '--enable-metrics': 'true',
                '--enable-continuous-cloudwatch-log': 'true',
                '--enable-continuous-log-filter': 'false',
            },
            continuousLogging: { enabled: true },
            enableProfilingMetrics: true,
        });

    // count items dynamodb table
    const deleteTableDataJob = new glue_alpha.Job(stack, `${stack.stackName}-deleteTableDataJob`,
        {
            jobName: `${stack.stackName}-deleteTableDataJob`,
            role: glueServiceRoleMigrationRole,
            maxConcurrentRuns: 200,
            workerCount: 10,
            workerType: glue_alpha.WorkerType.G_1X,
            executable: glue_alpha.JobExecutable.pythonEtl({
                glueVersion: glue_alpha.GlueVersion.V4_0,
                pythonVersion: glue_alpha.PythonVersion.THREE,
                script: glue_alpha.Code.fromAsset('infra/stacks/migration/script/delete-data-in-table.py'),

            }),
            defaultArguments: {
                '--job-bookmark-option': 'job-bookmark-disable',
                '--job-language': 'python',
                '--enable-metrics': 'true',
                '--enable-continuous-cloudwatch-log': 'true',
                '--enable-continuous-log-filter': 'false',
            },
            continuousLogging: { enabled: true },
            enableProfilingMetrics: true,
        });

    // S3 bucket copy file
    const s3CopyObjectJob = new glue_alpha.Job(stack, `${stack.stackName}-s3CopyObjectJob`,
        {
            jobName: `${stack.stackName}-s3CopyObjectJob`,
            role: glueServiceRoleMigrationRole,
            maxConcurrentRuns: 200,
            workerCount: 10,
            workerType: glue_alpha.WorkerType.G_1X,
            executable: glue_alpha.JobExecutable.pythonEtl({
                glueVersion: glue_alpha.GlueVersion.V4_0,
                pythonVersion: glue_alpha.PythonVersion.THREE,
                script: glue_alpha.Code.fromAsset('infra/stacks/migration/script/s3_copy_object.py'),

            }),
            defaultArguments: {
                '--job-bookmark-option': 'job-bookmark-disable',
                '--job-language': 'python',
                '--enable-metrics': 'true',
                '--enable-continuous-cloudwatch-log': 'true',
                '--enable-continuous-log-filter': 'false',
            },
            continuousLogging: { enabled: true },
            enableProfilingMetrics: true,
        });

    return {
        glueServiceRoleMigrationRole, migrationCognitoUserJob, deleteCognitoUserAndGroupJob,
        dataMigrationSegmentDeliveryMailJob, dataMigrationSegmentDeliveryTalkJob,
        dataMigrationSegmentDeliveryPostponedJob, dataMigrationSegmentDeliveryRepeatingJob,
        dataMigrationETLJob, dataMigrationBoto3Job,
        countItemsInTableJob, deleteTableDataJob,
        s3CopyObjectJob
    }
}
