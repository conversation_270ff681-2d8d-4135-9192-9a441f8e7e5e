# Using ETL connector

import sys
import boto3
from awsglue.job import Job
from awsglue.utils import getResolvedOptions
from awsglue.context import GlueContext
from pyspark.context import SparkContext

region = "ap-northeast-1"
sc = SparkContext()
glueContext = GlueContext(sc)
spark = glueContext.spark_session
logger = glueContext.get_logger()

args = getResolvedOptions(
    sys.argv,
    [
        "JOB_NAME",
        "src_table_name",
        "dest_table_name",
    ],
)

# Optional arguments
optional_args = {"stack_name": None, "survey_endpoint_url": None}
for key in optional_args:
    if f"--{key}" in sys.argv:
        optional_args[key] = getResolvedOptions(sys.argv, [key])[key]
stack_name = optional_args["stack_name"]
survey_endpoint_url = optional_args["survey_endpoint_url"]

job = Job(glueContext)
job.init(args["JOB_NAME"], args)

src_table_name = args["src_table_name"]
dest_table_name = args["dest_table_name"]

logger.info(f"src_table_name: {src_table_name}")
logger.info(f"dest_table_name: {dest_table_name}")

# Get index keys from DynamoDB table
def get_index_keys(table_name):
    dynamodb = boto3.client("dynamodb", region_name=region)
    table_desc = dynamodb.describe_table(TableName=table_name)["Table"]
    gsi = table_desc.get("GlobalSecondaryIndexes", [])
    lsi = table_desc.get("LocalSecondaryIndexes", [])
    all_indexes = gsi + lsi

    partition_keys = []
    sort_keys = []
    for index in all_indexes:
        for schema in index.get("KeySchema", []):
            attr = schema["AttributeName"]
            if schema["KeyType"] == "HASH" and attr not in partition_keys:
                partition_keys.append(attr)
            elif schema["KeyType"] == "RANGE" and attr not in sort_keys:
                sort_keys.append(attr)

    return partition_keys + sort_keys

index_attribute_names = get_index_keys(src_table_name)
logger.info(f"Detected index keys: {index_attribute_names}")

# Clean up null/empty index attributes
# def remove_empty_index_attributes(record):
#     for key in index_attribute_names:
#         if key in record and record[key] in [None, ""]:
#             logger.info(f"Removing empty attribute '{key}' from record: {record}")
#             record.pop(key)
#     return record

# Load source data
dyf = glueContext.create_dynamic_frame_from_options(
    connection_type="dynamodb",
    connection_options={
        "dynamodb.region": region,
        "dynamodb.input.tableName": src_table_name,  # dynamodb table source name
    },
)

# Clean up null/empty index attributes
# dyf = dyf.map(f=remove_empty_index_attributes)
dyf = dyf.map(
    f=lambda record: {
        k: v
        for k, v in record.items()
        if k not in index_attribute_names or v not in [None, ""]
    }
)

# Optional endpoint URL replacement
if "surveyconfigs" in src_table_name.lower():
    def transform_endpoint(record):
        if stack_name or survey_endpoint_url and "endpointURL" in record and record["endpointURL"]:
            if survey_endpoint_url:
                record["endpointURL"] = survey_endpoint_url
            elif stack_name:
                record["endpointURL"] = f"https://liff-web.{stack_name}.gov.line-smartcity.com/"
        return record

    dyf = dyf.map(f=transform_endpoint)

dyf.show()

# Write to destination
glueContext.write_dynamic_frame_from_options(
    frame=dyf,
    connection_type="dynamodb",
    connection_options={
        "dynamodb.region": region,
        "dynamodb.output.tableName": dest_table_name,  # dynamodb table target name
    },
)

job.commit()
