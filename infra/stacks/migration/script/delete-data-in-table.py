# Using batch_writer for delete all data in  dynamodb table
import time
import sys
import boto3
from awsglue.utils import getResolvedOptions
from awsglue.context import GlueContext
from pyspark.context import SparkContext

sc = SparkContext()
glueContext = GlueContext(sc)
logger = glueContext.get_logger()

dynamo = boto3.resource("dynamodb")

args = getResolvedOptions(
    sys.argv,
    [
        "JOB_NAME",
        "table_name",
    ],
)

tableName = args["table_name"]
logger.info(f"tableName: {tableName}")


def truncateTable():
    table = dynamo.Table(tableName)

    # get the table keys
    tableKeyNames = [key.get("AttributeName") for key in table.key_schema]

    # Only retrieve the keys for each item in the table (minimize data transfer)
    projectionExpression = ", ".join("#" + key for key in tableKeyNames)
    expressionAttrNames = {"#" + key: key for key in tableKeyNames}

    counter = 0
    page = table.scan(
        ProjectionExpression=projectionExpression,
        ExpressionAttributeNames=expressionAttrNames,
    )
    with table.batch_writer() as batch:
        while page["Count"] > 0:
            counter += page["Count"]
            # Delete items in batches
            for itemKeys in page["Items"]:
                batch.delete_item(Key=itemKeys)
            # Fetch the next page
            if "LastEvaluatedKey" in page:
                page = table.scan(
                    ProjectionExpression=projectionExpression,
                    ExpressionAttributeNames=expressionAttrNames,
                    ExclusiveStartKey=page["LastEvaluatedKey"],
                )
            else:
                break
    logger.info(f"Deleted table {tableName} with items:  {counter}")


if __name__ == "__main__":
    start = time.time()
    truncateTable()
    logger.info(f"Done in: {time.time() - start}")
