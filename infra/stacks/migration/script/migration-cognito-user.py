import sys
import boto3
import time
from awsglue.utils import getResolvedOptions
from awsglue.context import GlueContext
from pyspark.context import SparkContext

sc = SparkContext()
glueContext = GlueContext(sc)
logger = glueContext.get_logger()

argv = sys.argv

logger.info("argv: {}".format(argv))
arguments = ["JOB_NAME", "src_user_pool_id", "dest_user_pool_id"]

if "--{}".format("temp_pwd") in sys.argv:
    arguments.append("temp_pwd")

if "--{}".format("is_not_send_email") in sys.argv:
    arguments.append("is_not_send_email")

logger.info("arguments: {}".format(arguments))
args = getResolvedOptions(sys.argv, arguments)


src_user_pool_id = args["src_user_pool_id"]
dest_user_pool_id = args["dest_user_pool_id"]
temp_pwd = args["temp_pwd"] if "temp_pwd" in args else None
is_not_send_email = args["is_not_send_email"] if "is_not_send_email" in args else None

logger.info(f"src_user_pool_id: {src_user_pool_id}")
logger.info(f"dest_user_pool_id: {dest_user_pool_id}")
logger.info(f"temp_pwd: {temp_pwd}")
logger.info(f"is_not_send_email: {is_not_send_email}")

cognitoClient = boto3.client("cognito-idp")
groupPaginator = cognitoClient.get_paginator("list_groups")
userPaginator = cognitoClient.get_paginator("list_users")
usersInGroupPaginator = cognitoClient.get_paginator("list_users_in_group")


def listGroups(userPoolId):
    arr = []
    response_iterator = groupPaginator.paginate(UserPoolId=userPoolId)
    for page in response_iterator:
        arr.extend(page["Groups"])
        time.sleep(1)
    return arr


def listUsersInGroup(userPoolId, groupName):
    arr = []
    response_iterator = usersInGroupPaginator.paginate(
        UserPoolId=userPoolId, GroupName=groupName
    )
    for page in response_iterator:
        arr.extend(page["Users"])
        time.sleep(1)
    return arr


def listUsers(userPoolId):
    arr = []
    response_iterator = userPaginator.paginate(UserPoolId=userPoolId)
    for page in response_iterator:
        arr.extend(page["Users"])
        time.sleep(1)
    return arr


def createGroup(group):
    groupName = group["GroupName"]
    response = cognitoClient.create_group(
        GroupName=groupName,
        UserPoolId=dest_user_pool_id,
    )
    return response


def createUser(user):
    userName = user["Username"]
    attributes = user["Attributes"]
    email = None
    for attribute in attributes:
        if attribute["Name"] == "email":
            email = attribute["Value"]
            break

    input = {
        "UserPoolId": dest_user_pool_id,
        "Username": userName,
        "UserAttributes": [
            {"Name": "email", "Value": email},
        ],
    }

    if temp_pwd:
        input["TemporaryPassword"] = temp_pwd
    if is_not_send_email == "true":
        input["MessageAction"] = "SUPPRESS"

    response = cognitoClient.admin_create_user(**input)
    return response


def addUserToGroup(group):
    groupName = group["GroupName"]
    srcUsers = listUsersInGroup(src_user_pool_id, groupName)
    destUsers = listUsersInGroup(dest_user_pool_id, groupName)
    for srcUser in srcUsers:
        isExisting = False
        userName = srcUser["Username"]
        for destUser in destUsers:
            if userName == destUser["Username"]:
                isExisting = True
                break
        if isExisting:
            logger.info(f"User name ({userName}) already exits in group : {groupName}")
            continue
        response = cognitoClient.admin_add_user_to_group(
            UserPoolId=dest_user_pool_id, Username=userName, GroupName=groupName
        )
        time.sleep(0.05)


def migrationGroup(srcGroups, destGroups):
    for srcGroup in srcGroups:
        isExisting = False
        srcGroupName = srcGroup["GroupName"]
        for destGroup in destGroups:
            if srcGroupName == destGroup["GroupName"]:
                isExisting = True
                break

        if isExisting:
            logger.info(f"Group name already exits : {srcGroupName}")
            continue

        createGroup(srcGroup)
        time.sleep(0.1)


def migrationUser(srcUsers, destUsers):
    for srcUser in srcUsers:
        isExisting = False
        userName = srcUser["Username"]
        for destUser in destUsers:
            if userName == destUser["Username"]:
                isExisting = True
                break

        if isExisting:
            logger.info(f"User name already exits : {userName}")
            continue

        createUser(srcUser)
        time.sleep(0.05)


def migrationAddUserToGroup(groups):
    for group in groups:
        addUserToGroup(group)


start = time.time()
srcGroups = listGroups(src_user_pool_id)
time.sleep(1)
destGroups = listGroups(dest_user_pool_id)
time.sleep(1)
srcUsers = listUsers(src_user_pool_id)
time.sleep(1)
destUsers = listUsers(dest_user_pool_id)
time.sleep(1)
migrationGroup(srcGroups, destGroups)
time.sleep(1)
migrationUser(srcUsers, destUsers)
time.sleep(1)
migrationAddUserToGroup(srcGroups)
logger.info(f"Done in: {time.time() - start}")
