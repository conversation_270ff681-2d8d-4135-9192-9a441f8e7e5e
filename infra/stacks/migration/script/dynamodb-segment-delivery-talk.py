# データ移行
# トーク配信
import sys
import time
import datetime
import boto3
from awsglue.utils import getResolvedOptions
from awsglue.context import GlueContext
from pyspark.context import SparkContext

sc = SparkContext()
glueContext = GlueContext(sc)
logger = glueContext.get_logger()

dynamo = boto3.resource("dynamodb")

args = getResolvedOptions(
    sys.argv,
    [
        "JOB_NAME",
        "src_segment_delivery_table_name",
        "dest_table_name",
    ],
)

src_segment_delivery_table_name = args["src_segment_delivery_table_name"]
dest_table_name = args["dest_table_name"]

srcSegmentDeliveryTable = dynamo.Table(src_segment_delivery_table_name)
destTable = dynamo.Table(dest_table_name)

logger.info(f"src_segment_delivery_table_name: {src_segment_delivery_table_name}")
logger.info(f"dest_table_name: {dest_table_name}")


def createConfigItem(srcItem):
    createdAt = datetime.datetime.fromtimestamp(float(srcItem["createdAt"])).strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
    updatedAt = datetime.datetime.fromtimestamp(float(srcItem["updatedAt"])).strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
    newItem = {
        "partitionKey": srcItem["id"],
        "sortKey": "default",
        "gsiListingPk": "talk",
        "gsiListingSk": "all",
        "id": srcItem["id"],
        "enabled": srcItem["enabled"],
        "distributionName": srcItem["name"],
        "distributionType": "talk",
        "targetSelectionType": "scenario",
        "mailTriggerSettings": srcItem["distributionCondition"],
        "talkSettings": {
            "environment": srcItem["environment"],
            "talkName": srcItem["talkName"],
            "talkId": srcItem["talkId"],
            "bosaiTrigger": srcItem["bosaiTrigger"] if "bosaiTrigger" in srcItem else False,
            "useDisasterRichmenu": srcItem["useDisasterRichmenu"] if "useDisasterRichmenu" in srcItem else False,
        },
        "createdAt": createdAt,
        "updatedAt": updatedAt,
    }
    
    if "createdBy" in srcItem:
        newItem["createdBy"] = srcItem["createdBy"]

    return newItem


def migrate():
    counterConfig = 0

    # filter
    filter_expression = "#type = :type_value AND attribute_exists(talkName)"
    expression_attribute_names = {"#type": "type"}
    expression_attribute_values = {":type_value": "distributionConfig"}

    options = {
        "FilterExpression": filter_expression,
        "ExpressionAttributeNames": expression_attribute_names,
        "ExpressionAttributeValues": expression_attribute_values,
    }

    page = srcSegmentDeliveryTable.scan(**options)
    with destTable.batch_writer() as batch:
        while True:
            counterConfig += page["Count"]
            for item in page["Items"]:
                logger.info("config id: {}".format(item["id"]))
                newItem = createConfigItem(item)
                batch.put_item(newItem)

            if "LastEvaluatedKey" in page:
                # Fetch the next page
                optionsNext = options.copy()
                optionsNext["ExclusiveStartKey"] = page["LastEvaluatedKey"]
                page = srcSegmentDeliveryTable.scan(**optionsNext)
            else:
                break

    logger.info(f"Total トーク配信 added: {counterConfig}")


start = time.time()
migrate()
logger.info(f"Done in: {time.time() - start}")
