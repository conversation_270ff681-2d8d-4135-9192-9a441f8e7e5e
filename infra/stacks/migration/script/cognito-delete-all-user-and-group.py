import sys
import boto3
import time
from awsglue.utils import getResolvedOptions
from awsglue.context import GlueContext
from pyspark.context import SparkContext

# list of group that do not delete
excluded_groups = []
# list of user that do not delete
excluded_users = ["pnl-maintenance"]

sc = SparkContext()
glueContext = GlueContext(sc)
logger = glueContext.get_logger()

argv = sys.argv

logger.info("argv: {}".format(argv))
arguments = ["JOB_NAME", "user_pool_id"]

logger.info("argv: {}".format(argv))

args = getResolvedOptions(sys.argv, arguments)
user_pool_id = args["user_pool_id"]

# Optional arguments
optional_args = {"delete_user_group": "true"}
for key in optional_args:
    if f"--{key}" in sys.argv:
        optional_args[key] = getResolvedOptions(sys.argv, [key])[key]
delete_user_group = optional_args["delete_user_group"] # false: do not delete the Cognito user group; true: delete it

cognitoClient = boto3.client("cognito-idp")
groupPaginator = cognitoClient.get_paginator("list_groups")
userPaginator = cognitoClient.get_paginator("list_users")


def listGroups():
    arr = []
    response_iterator = groupPaginator.paginate(UserPoolId=user_pool_id)
    for page in response_iterator:
        arr.extend(page["Groups"])
        time.sleep(1)
    return arr


def listUsers():
    arr = []
    response_iterator = userPaginator.paginate(UserPoolId=user_pool_id)
    for page in response_iterator:
        arr.extend(page["Users"])
        time.sleep(1)
    return arr


def deleteUser(user):
    userName = user["Username"]
    response = cognitoClient.admin_delete_user(
        UserPoolId=user_pool_id, Username=userName
    )
    return response


def deleteGroup(group):
    groupName = group["GroupName"]
    response = cognitoClient.delete_group(GroupName=groupName, UserPoolId=user_pool_id)
    return response


def deleteAllUser():
    users = listUsers()
    for user in users:
        userName = user["Username"]
        if userName in excluded_users:
            logger.info("Skip deleting user: {}".format(userName))
            continue
        deleteUser(user)
        time.sleep(0.1)


def deleteAllGroup():
    groups = listGroups()
    for group in groups:
        groupName = group["GroupName"]
        if groupName in excluded_groups:
            logger.info("Skip deleting group: {}".format(groupName))
            continue
        deleteGroup(group)
        time.sleep(0.1)


start = time.time()
deleteAllUser()
time.sleep(1)
if delete_user_group == "true":
    deleteAllGroup()
print("Done in: ", time.time() - start)
