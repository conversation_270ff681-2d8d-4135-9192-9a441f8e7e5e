# データ移行
# 予約配信
import sys
import time
import datetime
import boto3
from awsglue.utils import getResolvedOptions
from awsglue.context import GlueContext
from pyspark.context import SparkContext

sc = SparkContext()
glueContext = GlueContext(sc)
logger = glueContext.get_logger()

dynamo = boto3.resource("dynamodb")

args = getResolvedOptions(
    sys.argv,
    [
        "JOB_NAME",
        "src_segment_delivery_table_name",
        "src_distribution_resources_table_name",
        "dest_table_name",
    ],
)

src_segment_delivery_table_name = args["src_segment_delivery_table_name"]
src_distribution_resources_table_name = args["src_distribution_resources_table_name"]
dest_table_name = args["dest_table_name"]

srcSegmentDeliveryTable = dynamo.Table(src_segment_delivery_table_name)
srcDistributionResourcesTable = dynamo.Table(src_distribution_resources_table_name)
destTable = dynamo.Table(dest_table_name)

logger.info(f"src_segment_delivery_table_name: {src_segment_delivery_table_name}")
logger.info(f"src_distribution_resources_table_name: {src_distribution_resources_table_name}")
logger.info(f"dest_table_name: {dest_table_name}")


def createConfigItem(srcItem):
    createdAt = datetime.datetime.fromtimestamp(float(srcItem["createdAt"])).strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
    updatedAt = datetime.datetime.fromtimestamp(float(srcItem["updatedAt"])).strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
    newItem = {
        "partitionKey": srcItem["id"],
        "sortKey": "default",
        "id": srcItem["id"],
        "distributionName": srcItem["name"],
        "targetSelectionType": "surveyConditions",
        "surveyConditions": srcItem["surveyConditions"],
        "messageIds": srcItem["messages"],
        "createdAt": createdAt,
        "updatedAt": updatedAt,
    }

    if srcItem["surveyConditions"] is not None:
        if "surveyId" in srcItem["surveyConditions"] and srcItem["surveyConditions"]["surveyId"]:
            newItem["gsiListingSk"] = srcItem["surveyConditions"]["surveyId"]

    if "scheduledStartAt" in srcItem:
        dt = datetime.datetime.fromtimestamp(float(srcItem["scheduledStartAt"]))
        newItem["postponedSettings"] = {
            "date": dt.strftime("%Y-%m-%d"),  # "2024-03-19" # UTC date
            "time": dt.strftime("%H:%M"),  # "03:00" # UTC time
        }

    if srcItem["state"] == "DRAFT":
        newItem["gsiListingPk"] = "draft"
        newItem["distributionType"] = "draft"
        newItem["isDraft"] = True
    else:
        newItem["gsiListingPk"] = "postponed"
        newItem["distributionType"] = "postponed"
        
    if "gsiListingSk" not in newItem:
        newItem["gsiListingSk"] = "all"
        
    if "createdBy" in srcItem:
        newItem["createdBy"] = srcItem["createdBy"]
        
    return newItem

def createMessageItem(srcItem):
    newMessages = []
    if "messages" in srcItem:
        for messageId in srcItem["messages"]:
            response = srcDistributionResourcesTable.get_item(Key={'primaryKey': messageId})
            item = { **response["Item"], "id": response["Item"].pop("primaryKey") }
            newMessages.append(item)

    newItem = {
        "partitionKey": srcItem["id"],
        "sortKey": "messages",
        "messages": newMessages,
    }
    return newItem

def migrate():
    counterConfig = 0
    counterMsg = 0

    # filter
    filter_expression = "#startType = :start_type_value AND (#state = :state_value_scheduled OR #state = :state_value_draft)"
    expression_attribute_names = {"#startType": "startType", "#state": "state"}
    expression_attribute_values = {
        ":start_type_value": "ONETIME",
        ":state_value_scheduled": "SCHEDULED",
        ":state_value_draft": "DRAFT",
    }

    options = {
        "FilterExpression": filter_expression,
        "ExpressionAttributeNames": expression_attribute_names,
        "ExpressionAttributeValues": expression_attribute_values,
    }

    page = srcSegmentDeliveryTable.scan(**options)
    with destTable.batch_writer() as batch:
        while True:
            counterConfig += page["Count"]
            for item in page["Items"]:
                # 予約配信
                logger.info("config id: {}".format(item["id"]))
                
                newItem = createConfigItem(item)
                batch.put_item(newItem)
                # 配信メッセージ
                newMsgItem = createMessageItem(item)
                batch.put_item(newMsgItem)
                counterMsg += len(newMsgItem["messages"])
                logger.info("    messages length: {}".format(len(newMsgItem["messages"])))
                
            if "LastEvaluatedKey" in page:
                # Fetch the next page
                optionsNext = options.copy()
                optionsNext["ExclusiveStartKey"] = page["LastEvaluatedKey"]
                page = srcSegmentDeliveryTable.scan(**optionsNext)
            else:
                break

    logger.info(f"Total 予約配信 added: {counterConfig}")
    logger.info(f"Total 予約配信のメッセージ : {counterMsg}")


start = time.time()
migrate()
logger.info(f"Done in: {time.time() - start}")
