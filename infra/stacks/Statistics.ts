import {ApiRouteProps, Bucket, Cron, Function, StackContext, use} from "sst/constructs";
import {config} from "../config.js";
import {dynamo} from "../infra-helpers.js";
import { Stack as Platform} from  "./Platform.js"
import { Stack as <PERSON>ena<PERSON> } from "./Scenario.js"
import { FullStack as Survey } from "./Survey.js"
import { Stack as AwsStack } from "aws-cdk-lib";
import * as logs from "@oss/shared/lib/platform/LogEvents";
import {Effect, PolicyStatement} from "aws-cdk-lib/aws-iam";

export function Stack(ctx: StackContext) {
    const platform = use(Platform)

    const staticResources = createStaticResources(ctx.stack, platform);
    const dynamicResources = createDynamicResources(ctx.stack, platform, staticResources);

    return {
        ...staticResources,
        ...dynamicResources
    }
}

function createStaticResources(stack: AwsStack, platform: ReturnType<typeof Platform>) {
    const table = dynamo.newTable(stack)('statisticsLog', {
        fields: {
            "DataType": "string",
            "Date": "string",
        },
        primaryIndex: {
            partitionKey: "DataType",
            sortKey: "Date"
        }
    })

    const bucket = new Bucket(stack, 'statisticLogsBucket', {
        blockPublicACLs: true,
        cdk: {
            bucket: {
                removalPolicy: config.getRemovalPolicy(),
                serverAccessLogsBucket: platform.accessLogBucket,
                serverAccessLogsPrefix: 'StatisticLogsBucket/StatisticLogsBucket'
            }
        }
    })

    return {
        bucket,
        table,
        ...createModuleLogTables(stack)
    }
}

function createDynamicResources(stack: AwsStack, platform: ReturnType<typeof Platform>, statics: ReturnType<typeof createStaticResources>) {
    const scenario = use(Scenario)
    const survey = use(Survey)

    const scheduledLogFunction = new Function(stack, 'statisticsAggregationFunction', {
        runtime: 'nodejs18.x',
        handler: 'packages/services/statistics/cron/handler.main',
        permissions: [
            'lambda',
            new PolicyStatement({
                actions: ['dynamodb:*'],
                resources: [statics.table.tableArn]
            }),
            new PolicyStatement({
                actions: ['s3:*'],
                resources: [statics.bucket.bucketArn]
            }),
            new PolicyStatement({
                effect: Effect.ALLOW,
                actions: ["lambda:InvokeAsync", "lambda:InvokeFunction"],
                resources: ['*']
            })
        ],
        environment: {
            TABLE_BI_LOGS: statics.table.tableName,
            BI_LOG_BUCKET: statics.bucket.bucketName,
            TABLE_SURVEY_LOGS: statics.surveyLogs.tableName,
            TABLE_SURVEY_CONFIGS: survey.surveyConfigsTableName,
            TABLE_SURVEY_RESULTS: survey.surveyResultTableName,
            // TABLE_DISTRIBUTION_LOGS: distribution.resourcesTable,
            // TABLE_SEGMENT_DELIVERY: '',
            TABLE_CHAT_BOT_SCENARIO_LOGS: scenario.scenarioLogs.tableName,
            LINE_MESSAGING_CHANNEL_ACCESS_TOKEN: config.getString('global.line.messagingAccessToken')
        },
        timeout: 15 * 60, // 15 minutes, maximum
        memorySize: 2048
    });

    const logHandlerFunction = new Function(stack, 'logHandlerFunction', {
        runtime: 'nodejs18.x',
        handler: 'packages/services/statistics/event/handler.main',
        memorySize: 2048,
        timeout: 60,
        environment: {
            TABLE_CHAT_BOT_SCENARIO_LOGS: scenario.scenarioLogs.tableName,
            TABLE_SURVEY_LOGS: statics.surveyLogs.tableName,
            TABLE_DISTRIBUTION_LOGS: statics.distributionLogs.tableName,
            // TABLE_SEGMENT_DELIVERY: ''
        }
    });

    const statisticsApiFunction = new Function(stack, 'statisticsApiFunction', {
        runtime: 'nodejs18.x',
        handler: 'packages/services/statistics/rest/handler.main',
        memorySize: 2048,
        timeout: 30,
        permissions: [
            'lambda',
            new PolicyStatement({
                actions: ['dynamodb:*'],
                resources: [statics.table.tableArn]
            })
        ],
        environment: {
            LINE_MESSAGING_CHANNEL_ACCESS_TOKEN: config.getString('global.line.messagingAccessToken'),
            TABLE_BI_LOGS: statics.table.tableName,
            TABLE_SEGMENT_DELIVERY: '', // TODO
            TABLE_SURVEY_CONFIGS: survey.surveyConfigsTableName,
        }
    })

    platform.logEventBus.addRules(stack, {
        statisticLogHandler: {
            pattern: {
                source: [logs.LOG_SOURCE],
                detailType: Object.keys(logs.LogDetail)
            },
            targets: { logHandlerFunction }
        }
    });

    new Cron(stack, 'statisticsAggregationJob', {
        job: scheduledLogFunction,
        schedule: `cron(${config.getString('statistics.schedule')})`
    })

    function registerRoutes(): Record<string, ApiRouteProps<string>> {
        return {
            'OPTIONS /bi/lineAPI/getFriendsList': {
                function: statisticsApiFunction,
                authorizer: 'none'
            },
            'GET /bi/lineAPI/getFriendsList': {
                function: statisticsApiFunction
            },

            'OPTIONS /bi/lineAPI/getMessageDelivered': {
                function: statisticsApiFunction,
                authorizer: 'none'
            },
            'GET /bi/lineAPI/getMessageDelivered': {
                function: statisticsApiFunction
            },

            'OPTIONS /bi/lineAPI/getDemoGraphic': {
                function: statisticsApiFunction,
                authorizer: 'none'
            },
            'GET /bi/lineAPI/getDemoGraphic': {
                function: statisticsApiFunction
            },

            'OPTIONS /bi/survey/getSurveyLogs': {
                function: statisticsApiFunction,
                authorizer: 'none'
            },
            'GET /bi/survey/getSurveyLogs': {
                function: statisticsApiFunction
            },

            'OPTIONS /bi/survey/getSurveyDetailLogs': {
                function: statisticsApiFunction,
                authorizer: 'none'
            },
            'GET /bi/survey/getSurveyDetailLogs': {
                function: statisticsApiFunction
            },

            'OPTIONS /bi/segment/getSegmentLogs': {
                function: statisticsApiFunction,
                authorizer: 'none'
            },
            'GET /bi/segment/getSegmentLogs': {
                function: statisticsApiFunction
            },

            'OPTIONS /bi/scenario/getRichmenuLogs': {
                function: statisticsApiFunction,
                authorizer: 'none'
            },
            'GET /bi/scenario/getRichmenuLogs': {
                function: statisticsApiFunction
            },

            'OPTIONS /bi/survey/getSurveyConfigs': {
                function: statisticsApiFunction,
                authorizer: 'none'
            },
            'GET /bi/survey/getSurveyConfigs': {
                function: statisticsApiFunction
            },
        }
    }

    return {
        logHandlerFunction,
        statisticsApiFunction,
        registerRoutes
    };
}

function createModuleLogTables(stack: AwsStack) {
    const surveyLogs = dynamo.newTable(stack)('surveyLogs', {
        fields: {
            "logType": "string",
            "createdAt": "number",
            "action": "string",
            "surveyId": "string",
        },
        primaryIndex: {
            partitionKey: "logType",
            sortKey: "createdAt"
        },
        globalIndexes: {
            'action-createdAt-index': {
                partitionKey: 'action',
                sortKey: 'createdAt',
                projection: 'all'
            },
            'surveyId-createdAt-index': {
                partitionKey: 'surveyId',
                sortKey: 'createdAt',
                projection: 'all'
            }
        }
    });

    const distributionLogs = dynamo.newTable(stack)('distributionLogs', {
        fields: {
            "partitionKey": "string",
            "sortKey": "string"
        },
        primaryIndex: {
            partitionKey: "partitionKey",
            sortKey: "sortKey"
        }
    });

    return {
        surveyLogs, distributionLogs
    }
}