import { Cognito, EventBus, Function, StackContext, use } from "sst/constructs";
import { AccountRecovery } from "aws-cdk-lib/aws-cognito";
import { Duration, RemovalPolicy } from "aws-cdk-lib";
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { config } from "../config.js";
import { DATABASES } from "../constants.js";
import * as S3AWS from 'aws-cdk-lib/aws-s3'
import { BlockPublicAccess, Bucket as S3Bucket } from 'aws-cdk-lib/aws-s3'
import { createPublicBucket, dynamo } from "../infra-helpers.js";
import { Stack as Dns } from './Dns.js'
import { getSecretName } from "../resources/secret-manager-support.js";
import * as sqs from 'aws-cdk-lib/aws-sqs';
import { addEmailIdentity } from "./distributions/email-trigger.js";
import { addCognitoGroup, UserPoolUser } from "./platform/cognito.js";
import { addActionLogsCollectionInfra } from "./platform/action-logs-collect.js";
import * as iam from 'aws-cdk-lib/aws-iam';

export function Stack(ctx: StackContext) {
    const { stack, app } = ctx;

    const dnsResources = use(Dns)

    addEmailIdentity(ctx, 'main-email-identity', { hostedZone: dnsResources.hostedZone })

    const cognito = new Cognito(stack, `admins_pool`, {
        login: ['username'],
        identityPoolFederation: false,
        cdk: {
            id: 'admins_pool',
            userPoolClient: {
                idTokenValidity: Duration.days(1),
                refreshTokenValidity: Duration.days(30),
            },
            userPool: {
                autoVerify: { email: true },
                accountRecovery: AccountRecovery.EMAIL_ONLY,
            }
        },
    });
    cognito.cdk.userPool.applyRemovalPolicy(RemovalPolicy.DESTROY)

    new ssm.StringParameter(stack, 'AdminsPoolId', {
        parameterName: `/${app.name}/${app.stage}/Platform/AdminsPoolId`, 
        stringValue: cognito.userPoolId,
        description: 'Platform: admin user pool ID',
    });

    addCognitoGroup(stack, cognito.userPoolId, 'Administrator:admins')
    addCognitoGroup(stack, cognito.userPoolId, 'Administrator:members')
    addCognitoGroup(stack, cognito.userPoolId, 'Administrator:guests')
    addCognitoGroup(stack, cognito.userPoolId, 'Administrator:operators')

    stack.addOutputs({
        userPoolId: cognito.userPoolId,
        userPoolClientId: cognito.userPoolClientId
    })

    const accessManagementApi = new Function(stack, `${stack.stackName}-accessApi`, {
        functionName: `${stack.stackName}-access`,
        handler: 'packages/services/platform/function-api.accessApi',
        environment: {
            'API_CORS_ORIGIN': `*`,
            'VUE_APP_AMPLIFY_AUTH_USER_POOL_ID': cognito.userPoolId,
        },
    });
    const systemManagementApi = new Function(stack, `${stack.stackName}-settingsApi`, {
        functionName: `${stack.stackName}-settings`,
        handler: 'packages/services/platform/function-api.settingsApi',
        environment: {
            'VUE_APP_AMPLIFY_AUTH_USER_POOL_ID': cognito.userPoolId,
        },
        permissions: ['lambda']
    });
    const publicApi = new Function(stack, `${stack.stackName}-publicApi`, {
        functionName: `${stack.stackName}-public`,
        handler: 'packages/services/platform/function-api.publicApi',
        environment: {},
    });

    const tagGetResourcesPolicy = new iam.PolicyStatement({
            effect: iam.Effect.ALLOW,
            actions: ['tag:GetResources'],
            resources: ['*'],
      });
      systemManagementApi.addToRolePolicy(tagGetResourcesPolicy);

    // Databases

    // we have to keep an old table, until we can migrate to the new one
    let previousPlatformTable = config.getDatabaseName(DATABASES.PLATFORM_OLD_TABLE);
    if (!previousPlatformTable) {
        const table = dynamo.newTable(stack)(DATABASES.PLATFORM_OLD_TABLE, {
            fields: {
                partitionKey: "string",
                teamId: "string",
                teamName: "string"
            },
            primaryIndex: {
                partitionKey: "partitionKey"
            },
            globalIndexes: {
                "teamId-teamName-index": {
                    partitionKey: "teamId",
                    sortKey: "teamName",
                    projection: "all"
                }
            }
        });
        previousPlatformTable = table.tableName;
    }

    // use new table for new features
    const platformTable = dynamo.newTable(stack)(DATABASES.PLATFORM_DATABASE, {
        fields: {
            partitionKey: "string",
            sortKey: "string"
        },
        primaryIndex: {
            partitionKey: "partitionKey",
            sortKey: "sortKey"
        }
    })

    const resources = createPublicBucket(ctx, 'resources', {
        originPath: '/public',
        distributionComment: `${ctx.app.stage}-${ctx.app.name} platform resources (including public)`,
        domainName: dnsResources.domainNames.resources.public,
        hostedZone: dnsResources.hostedZone,
        certificate: dnsResources.globalCertificate,
    })

    const accessLogBucket = new S3AWS.Bucket(stack, 'accessLogs', {
        removalPolicy: config.getRemovalPolicy(),
    })
    const actionLogs = addActionLogsCollectionInfra(ctx, accessLogBucket);

    function connect(f: Function) {
        f.addEnvironment('TABLE_PLATFORM_OLD_TABLE', previousPlatformTable)
        f.addEnvironment('ADMIN_USERPOOL_ID', cognito.userPoolId)
        f.addEnvironment('ADMIN_USERPOOL_CLIENT_ID', cognito.userPoolClientId)
        f.addEnvironment('RESOURCES_BUCKET', resources.bucket.bucketName)
        f.addEnvironment('SECRET_ID', getSecretName(ctx.app))
        f.addEnvironment('LOG_BUCKET', actionLogs.actionLogsBucket.bucketName)
        f.addEnvironment('LOG_BUCKET_PREFIX', 'platform-action-logs/reports/')

        f.addToRolePolicy(actionLogs.putActionLogPutRecordStatement)
        f.addEnvironment('ACTION_LOGS_DELIVERY_STREAM', actionLogs.actionLogsDeliveryStream.ref)

    }

    [accessManagementApi, systemManagementApi, publicApi].forEach(connect);
    accessManagementApi.addEnvironment('TABLE_PLATFORM_OLD_TABLE', previousPlatformTable)

    const logEventBus = new EventBus(stack, 'logs-bus', {});

    const systemDLQ = new sqs.Queue(stack, 'system-dlq', {
        removalPolicy: RemovalPolicy.DESTROY,
    })

    return {
        systemDLQ,
        userPoolClientId: cognito.userPoolClientId,
        userPoolId: cognito.userPoolId,
        publicApi,
        accessLogBucket,
        systemManagementApi,
        accessManagementApi,
        previousPlatformTable,
        platformTable,
        dnsResources,
        actionLogsBucket: actionLogs.actionLogsBucket,
        resourcesBucket: resources.bucket,
        logEventBus,
        actionLogsStream: actionLogs.actionLogsDeliveryStream,
        actionLogsStreamWritePermissionStatement: actionLogs.putActionLogPutRecordStatement,
    }
}
