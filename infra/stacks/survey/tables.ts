import {TableProps} from "sst/constructs";

export const SURVEY_RESULTS: TableProps = {
    fields: {
        partitionKey: "string",
        sortKey: "string",
        updatedAt: "number",
        userId: "string",
        surveyId: "string",
        userSearchKey: "string",
        indexSearchKey:"string",
        indexId: "string",
        orderId: "string"
    },
    primaryIndex: {
        partitionKey: "partitionKey",
        sortKey: "sortKey",
    },
    globalIndexes: {
        "surveyId-index": {
            partitionKey: "surveyId",
            projection: ["userSearchKey", "partitionKey", "sortKey", "surveyId", "userId"]
        },
        "surveyId-partitionKey-index": {
            partitionKey: "surveyId",
            sortKey: "partitionKey",
            projection: "all"
        },
        "surveyId-sortKey-index": {
            partitionKey: "surveyId",
            sortKey: "sortKey",
            projection: "all"
        },
        "indexId-indexSearchKey-index": {
            partitionKey: "indexId",
            sortKey: "indexSearchKey",
            projection: "all"
        },
        "surveyId-updatedAt-index": {
            partitionKey: "surveyId",
            sortKey: "updatedAt",
            projection: "all",
        },
        "surveyId-userSearchKey-index": {
            partitionKey: "surveyId",
            sortKey: "userSearchKey",
            projection: ["userId"]
        },
        "userId-surveyId-index": {
            partitionKey: "userId",
            sortKey: "surveyId",
            projection: "all"
        },
        "surveyId-orderId-index": {
            partitionKey: "surveyId",
            sortKey: "orderId",
            projection: "all"
        }
    }
}

export const MEMBER_RESULTS: TableProps = {
    fields: {
        partitionKey: "string",
        sortKey: "string",
        updatedAt: "number",
        userId: "string",
        surveyId: "string",
        userSearchKey: "string",
        indexSearchKey:"string",
        indexId: "string"
    },
    primaryIndex: {
        partitionKey: "partitionKey",
        sortKey: "sortKey",
    },
    globalIndexes: {
        "surveyId-index": {
            partitionKey: "surveyId",
            projection: ["userSearchKey", "partitionKey", "sortKey", "surveyId", "userId"]
        },
        "surveyId-partitionKey-index": {
            partitionKey: "surveyId",
            sortKey: "partitionKey",
            projection: "all"
        },
        "surveyId-sortKey-index": {
            partitionKey: "surveyId",
            sortKey: "sortKey",
            projection: "all"
        },
        "indexId-indexSearchKey-index": {
            partitionKey: "indexId",
            sortKey: "indexSearchKey",
            projection: "all"
        },
        "surveyId-updatedAt-index": {
            partitionKey: "surveyId",
            sortKey: "updatedAt",
            projection: "all",
        },
        "surveyId-userSearchKey-index": {
            partitionKey: "surveyId",
            sortKey: "userSearchKey",
            projection: ["userId"]
        },
        "userId-surveyId-index": {
            partitionKey: "userId",
            sortKey: "surveyId",
            projection: "all"
        }
    }
}

export const SURVEY_CONFIGS: TableProps = {
    fields: {
        surveyId: "string",
    },
    primaryIndex: {
        partitionKey: "surveyId"
    },
    globalIndexes: {
        "surveyId-ligtweight-index": {
            partitionKey: "surveyId",
            projection: "memberFormId, endpointURL, isDisplay, createdAt, updatedBy, surveyType, isAppending, createdBy, isDeleteAllEnabled, updatedAt, isEditDisabled, surveyTeams, description, surveyStatus, surveyTitle".split(',').map(s => s.trim()),
        }
    }
}

export const MEMBER_CONFIGS: TableProps = {
    fields: {
        surveyId: "string",
        surveyTitle: "string",
        description: "string",
        endOfSurveyMessage: "string",
        type: "string",
        surveyStatus: "string",
        surveyType: "string",
        memberSurveySubId: "number",
        lastResultNumber: "number",
        headerImageUrl: "string",
        usePayment: "number"
    },
    primaryIndex: {
        partitionKey: "surveyId"
    },
    globalIndexes: {
        "surveyId-ligtweight-index": {
            partitionKey: "surveyId",
            projection: "memberFormId, endpointURL, isDisplay, createdAt, updatedBy, surveyType, isAppending, createdBy, isDeleteAllEnabled, updatedAt, isEditDisabled, surveyTeams, description, surveyStatus, surveyTitle, surveySchema, linkedForms".split(',').map(s => s.trim()),
        }
    }
}

export const SURVEY_RESULTS_HISTORY: TableProps = {
    fields: {
        surveyResultId: "string",
        createdAt: "number"
    },
    primaryIndex: {
        partitionKey: "surveyResultId",
        sortKey: "createdAt"
    },
}

export const MEMBER_RESULTS_HISTORY: TableProps = {
    fields: {
        surveyResultId: "string",
        createdAt: "number"
    },
    primaryIndex: {
        partitionKey: "surveyResultId",
        sortKey: "createdAt"
    },
}

export const LINE_USERS: TableProps = {
    fields: {
        partitionKey: "string",
        sortKey: "string",
        transactionKey: "string",
        status: "string"
    },
    primaryIndex: {
        partitionKey: "partitionKey",
        sortKey: "sortKey"
    },
    globalIndexes: {
        "transactionKey-status-index": {
            partitionKey: "transactionKey",
            sortKey: "status",
        }
    }
}

export const SURVEY_CALENDARS: TableProps = {
    fields: {
        partitionKey: "string",
        sortKey: "string",
        serviceProductId: "string",
    },
    primaryIndex: {
        partitionKey: "partitionKey",
        sortKey: "sortKey"
    },
    globalIndexes: {
        'serviceProductId-sortKey-index': {
            partitionKey: "serviceProductId",
            sortKey: "sortKey",
            projection: "all"
        }
    }
}
