import { StackContext } from "sst/constructs";
import { dynamo } from "../../infra-helpers.js";

export function createScenarioSpotDataTable(ctx: StackContext) {
    const tableBuilder = dynamo.newTable(ctx.stack)

    const scenarioSpotDataTable = tableBuilder('scenarioSpotData', {
        fields: {
            partitionKey: "string",
            sortKey: "string",
            latLngGeohash: "string",
        },
        primaryIndex: {
            partitionKey: "partitionKey",
            sortKey: "sortKey",
        },
        globalIndexes: {
            "partitionKey-latLngGeohash-index": {
                partitionKey: "partitionKey",
                sortKey: "latLngGeohash",
                projection: "all"
            },
        },
    });
    return {
        scenarioSpotDataTable,
    }
}
