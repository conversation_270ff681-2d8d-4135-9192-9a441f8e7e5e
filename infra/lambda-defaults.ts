import {App} from "sst/constructs";
import {config} from "./config.js";
import {aws_iam, RemovalPolicy} from "aws-cdk-lib";

export const REGIONAL_SERVICES: string[] = [
    "cognito-idp", "lambda", "ssm", "events", "secretsmanager", "logs", "scheduler",
]


export function applyDefaults(app: App) {
    const isDev = config.isDevEnv()
    app.setDefaultFunctionProps({
        runtime: "nodejs18.x",
        timeout: 900,
        memorySize: 2048,
        logRetention: isDev ? "infinite" : "three_months",
        environment: {
            NODE_OPTIONS: "--enable-source-maps",
            DEPLOY_ENV: `${app.stage}-${app.name}`,
            ENV_NAME: `${app.stage}-${app.name}`,
            OSS_ENV_NAME: app.stage,
        },
        nodejs: {
            format: 'esm',
            sourcemap: true,
        },
        tracing: isDev ? "disabled" : "active"
    })
    if (config.isSstDev()) {
        app.addDefaultFunctionEnv({'IS_SST_DEV': 'true'})
    }
    app.addDefaultFunctionPermissions([new aws_iam.PolicyStatement({
        actions: ['*'],
        effect: aws_iam.Effect.ALLOW,
        resources: REGIONAL_SERVICES.map(s => `arn:aws:${s}:${app.region}:*:*`),
        // conditions: {
        //     "StringEquals": {"aws:ResourceTag/sst:app": app.name, "aws:ResourceTag/sst:stage": app.stage}
        // }
    })])
    app.addDefaultFunctionPermissions([new aws_iam.PolicyStatement({
        actions: ['*'],
        effect: aws_iam.Effect.ALLOW,
        resources: [
            `arn:aws:s3:::*`,
            `arn:aws:s3:::*/*`,
            `arn:aws:states:::*` // Step Functions
        ],
        // conditions: {
        //     "StringEquals": {"aws:ResourceTag/sst:app": app.name, "aws:ResourceTag/sst:stage": app.stage}
        // }
    })])
    app.addDefaultFunctionPermissions([new aws_iam.PolicyStatement({
        actions: ['*'],
        effect: aws_iam.Effect.ALLOW,
        resources: [
            `arn:aws:dynamodb:*:*:table/*`,
            ],
        // conditions: {
        //     "StringEquals": {"aws:ResourceTag/sst:app": app.name, "aws:ResourceTag/sst:stage": app.stage}
        // }
    })])
    app.addDefaultFunctionEnv({
        ENV_MODE: isDev ? 'development' : 'production',
        ENVIRONMENT_TYPE: isDev ? 'development' : 'production',
        ENVIRONMENT_NAME: `${app.stage}-${app.name}`
    })
    app.setDefaultRemovalPolicy(RemovalPolicy.DESTROY)
}
