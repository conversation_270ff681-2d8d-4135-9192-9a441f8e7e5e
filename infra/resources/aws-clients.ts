import {fromEnv, fromIni} from "@aws-sdk/credential-providers";
import * as ssm from "@aws-sdk/client-ssm"
import * as sm from "@aws-sdk/client-secrets-manager"
import {AwsCredentialIdentityProvider} from "@aws-sdk/types";
import parser from 'minimist'

let profile = 'default'
let region = 'ap-northeast-1'
let provider: AwsCredentialIdentityProvider | undefined;

function createCredentialsProvider(profile: string) {
    if (process.env.CI === 'true') {
        console.log('CI environment, using credentials from env')
        provider = fromEnv()
    } else {
        provider = fromIni({profile: profile});
    }
    return provider
}

export function getCurrentProfile() {
    let profile: string = parser(process.argv.slice(2)).profile
    if (!profile) {
        profile = process.env.AWS_PROFILE
    }
    return profile || 'default'
}

export function setupAwsContext(_profile: string, _region?: string) {
    profile = _profile;
    if (_region) {
        region = _region;
    }
    createCredentialsProvider(_profile);
}

export function createParametersStoreClient() {
    return new ssm.SSMClient({ credentials: provider, region })
}
export function createSecretsManagerClient() {
    return new sm.SecretsManagerClient({ credentials: provider, region } as any)
}
