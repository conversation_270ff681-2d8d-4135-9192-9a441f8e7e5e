import { DeploymentConfig } from "../config.js";
import { getDnsNames } from "../stacks/platform/dns.js";

export const SecretToConfigMapping = {

    'LINEMESSAGING_CHANNEL_ACCESS_TOKEN': 'global.line.messagingAccessToken',
    'LINEMESSAGING_CHANNEL_ID': 'global.line.messagingChannelId',
    'LINEMESSAGING_CHANNEL_SECRET': 'global.line.messagingChannelSecret',
    'SB_LINEMESSAGING_CHANNEL_ACCESS_TOKEN': 'scenario.sandbox.messagingAccessToken',
    'SB_LINEMESSAGING_CHANNEL_ID': 'scenario.sandbox.messagingChannelId',
    'SB_LINEMESSAGING_CHANNEL_SECRET': 'scenario.sandbox.messagingChannelSecret',
    'SURVEY_LINELOGIN_CHANNEL_ID': 'global.line.loginChannelId',
    'SURVEY_LINELOGIN_CHANNEL_SECRET': 'global.line.loginChannelSecret',
    'CHATBOT_FORWARD': 'scenario.chatbotForward',
    'SURVEY_LIFF_ID': 'global.line.liffId',
    'DAMAGE_LIFF_ID': 'global.line.liffIdDamage',
    'SB_CHATBOT_FORWARD': 'scenario.sandbox.chatbotForward',
    'CHATBOT_FORWARD_URL': 'scenario.chatbotForwardUrl',
    'SB_CHATBOT_FORWARD_URL': 'scenario.sandbox.chatbotForwardUrl',
    'CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH': 'scenario.fuzzySearchEnabled',
    'SB_CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH': 'scenario.sandbox.fuzzySearchEnabled',
    'EMAIL_CHATBOT_DAMAGE_REPORT1': 'scenario.damage.emailReport1',
    'EMAIL_CHATBOT_DAMAGE_REPORT2': 'scenario.damage.emailReport2',
    'EMAIL_CHATBOT_DAMAGE_REPORT3': 'scenario.damage.emailReport3',
    'EMAIL_CHATBOT_DAMAGE_REPORT4': 'scenario.damage.emailReport4',
    'VITE_MEMBER_TAB': 'global.memberTab',
    'VITE_USE_PAYMENT': 'payment.use',
    'VITE_IS_USING_SPOT': 'scenario.spot.use',
    'PAYMENT_API_ENDPOINT': 'payment.endpoint',
    'PAYMENT_MERCHANT_ID': 'payment.merchant-id',
    'PAYMENT_WEB_URL': 'payment.web-payment-url',
    'PAYMENT_API_ID': 'payment.api.id',
    'PAYMENT_API_PASS': 'payment.api.pass',
    'PAYMENT_API_KEY': 'payment.api.key',
    'VITE_GOOGLE_MAP_API_KEY': 'global.googleMapApiKey',
}

export function fillInInitialSecrets(config: DeploymentConfig) {
    const domainNames = getDnsNames();
    const obj = {
        'LINEMESSAGING_CHANNEL_ACCESS_TOKEN': config.getString('global.line.messagingAccessToken'),
        'LINEMESSAGING_CHANNEL_ID': config.getString('global.line.messagingChannelId'),
        'LINEMESSAGING_CHANNEL_SECRET': config.getString('global.line.messagingChannelSecret'),
        'SB_LINEMESSAGING_CHANNEL_ACCESS_TOKEN': config.getString('scenario.sandbox.messagingAccessToken'),
        'SB_LINEMESSAGING_CHANNEL_ID': config.getString('scenario.sandbox.messagingChannelId'),
        'SB_LINEMESSAGING_CHANNEL_SECRET': config.getString('scenario.sandbox.messagingChannelSecret'),
        'SURVEY_LIFF_ID': config.getString('global.line.liffId'),
        'DAMAGE_LIFF_ID': config.getString('global.line.liffIdDamage'),
        'CHATBOT_FORWARD': config.getString('scenario.chatbotForward'),
        'SB_CHATBOT_FORWARD': config.getString('scenario.sandbox.chatbotForward'),
        'CHATBOT_FORWARD_URL': config.getString('scenario.chatbotForwardUrl'),
        'SURVEY_LINELOGIN_CHANNEL_ID': config.getString('global.line.loginChannelId'),
        'SURVEY_LINELOGIN_CHANNEL_SECRET': config.getString('global.line.loginChannelSecret'),
        'SB_CHATBOT_FORWARD_URL': config.getString('scenario.sandbox.chatbotForwardUrl'),
        'CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH': config.getString('scenario.sandbox.fuzzySearchEnabled'),
        'SB_CHATBOT_TRASH_SEPARATION_FUZZY_SEARCH': config.getString('scenario.sandbox.fuzzySearchEnabled'),
        'VUE_APP_AMPLIFY_SCENARIO_API_ENDPOINT_URL': `https://${domainNames.scenario.api}`,
        'EMAIL_CHATBOT_DAMAGE_REPORT1': config.getString('scenario.damage.emailReport1'),
        'EMAIL_CHATBOT_DAMAGE_REPORT2': config.getString('scenario.damage.emailReport2'),
        'EMAIL_CHATBOT_DAMAGE_REPORT3': config.getString('scenario.damage.emailReport3'),
        'EMAIL_CHATBOT_DAMAGE_REPORT4': config.getString('scenario.damage.emailReport4'),
        'VITE_MEMBER_TAB': config.getBoolean('global.memberTab') ? '1' : '0',
        'VITE_USE_PAYMENT': config.getBoolean('payment.use') ? '1' : '0',
        'VITE_IS_USING_SPOT': config.getBoolean('scenario.spot.use') ? '1' : '0',
        'PAYMENT_API_ENDPOINT': config.getString('payment.endpoint'),
        'PAYMENT_MERCHANT_ID': config.getString('payment.merchant-id'),
        'PAYMENT_WEB_URL': config.getString('payment.web-payment-url'),
        'PAYMENT_API_ID': config.getString('payment.api.id'),
        'PAYMENT_API_PASS': config.getString('payment.api.pass'),
        'PAYMENT_API_KEY': config.getString('payment.api.key'),
        'VITE_GOOGLE_MAP_API_KEY': config.getString('global.googleMapApiKey'),
    }
    return obj
}

