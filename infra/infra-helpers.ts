import {Function, FunctionProps, StackContext, Table, TableProps} from 'sst/constructs'
import {aws_cloudfront, aws_dynamodb, aws_iam, Duration, RemovalPolicy, Stack} from "aws-cdk-lib";
import {Construct} from "constructs";
import {config} from "./config.js";
import {BlockPublicAccess, Bucket, HttpMethods} from "aws-cdk-lib/aws-s3";
import {S3Origin} from "aws-cdk-lib/aws-cloudfront-origins";
import {AaaaRecord, ARecord, ARecordProps, IHostedZone, RecordTarget} from "aws-cdk-lib/aws-route53";
import {CloudFrontTarget} from "aws-cdk-lib/aws-route53-targets";
import {Certificate, DnsValidatedCertificate} from "aws-cdk-lib/aws-certificatemanager";

function dynamoAllowWrite(labmda: Function, ...tableNames: string[]) {
    labmda.attachPermissions([new aws_iam.PolicyStatement({
        actions: [
            "dynamodb:*"
        ],
        effect: aws_iam.Effect.ALLOW,
        resources: tableNames.map(name => {
            const arn = dynamo.getTableArn(labmda.stack, name);
            return [arn, arn + '/*']
        }).flat()
    })])
}

function dynamoAllowRead(labmda: Function, ...tableNames: string[]) {
    labmda.attachPermissions([new aws_iam.PolicyStatement({
        actions: [
            "dynamodb:*"
        ],
        effect: aws_iam.Effect.ALLOW,
        resources: tableNames.map(name => {
            const arn = dynamo.getTableArn(labmda.stack, name);
            return [arn, arn + '/*']
        }).flat()
    })])
}

function dynamodbTable(c: Construct) {
    return function (id: string, props: TableProps) {
        const table = new Table(c, id, props);
        table.cdk.table.applyRemovalPolicy(RemovalPolicy.DESTROY)
        return table;
    }
}

function useExistedOrCreateNew(construct: Construct, tableId: string, newTableProps: TableProps) {
    const existedDbName = config.getDatabaseName(tableId);
    if (existedDbName) {
        return aws_dynamodb.Table.fromTableName(construct, tableId, existedDbName);
    }
    const table = new Table(construct, tableId, newTableProps);
    table.cdk.table.applyRemovalPolicy(RemovalPolicy.DESTROY)
    return table.cdk.table
}

export const dynamo = {
    allowWrite: dynamoAllowWrite,
    allowRead: dynamoAllowRead,
    newTable: dynamodbTable,
    useExistedOrCreateNew,
    getTableArn: (stack: Stack, tableName: string) => {
        return `arn:aws:dynamodb:${stack.region}:${stack.account}:table/${tableName}`
    }
}

export function createPublicBucket(ctx: StackContext, id: string, params: {
    bucketName?: string,
    distributionComment?: string,
    originPath?: string,
    domainName?: string,
    hostedZone?: IHostedZone,
    certificate?: Certificate,
} = {}) {
    const bucket = new Bucket(ctx.stack, id, {
        bucketName: params.bucketName,
        removalPolicy: config.getRemovalPolicy(),
        blockPublicAccess: BlockPublicAccess.BLOCK_ALL,
        autoDeleteObjects: config.getAutodeleteOption(),
        publicReadAccess: false,
        cors: [{
            allowedOrigins: ['*'],
            allowedHeaders: ['*'],
            allowedMethods: [HttpMethods.GET, HttpMethods.POST, HttpMethods.PUT],
            maxAge: Duration.days(365).toSeconds(),
        }]
    })

    const oid = new aws_cloudfront.OriginAccessIdentity(ctx.stack, `${id}-oids`);
    const distribution = new aws_cloudfront.Distribution(ctx.stack, `${id}-cf`, {
        comment: params.distributionComment || `Distribution for id=[${id}] bucket`,
        domainNames: params.domainName ? [params.domainName] : undefined,
        certificate: params.certificate,
        defaultBehavior: {
            origin: new S3Origin(bucket, {
                originAccessIdentity: oid, originPath: params.originPath
            }),
            originRequestPolicy: aws_cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN,
            responseHeadersPolicy: aws_cloudfront.ResponseHeadersPolicy.CORS_ALLOW_ALL_ORIGINS_WITH_PREFLIGHT,
        }
    })
    if (params.domainName && params.hostedZone) {
        const recordProps: ARecordProps = {
            zone: params.hostedZone,
            recordName: params.domainName,
            target: RecordTarget.fromAlias(new CloudFrontTarget(distribution))
        }
        new ARecord(ctx.stack, `${id}-AltARecord`, recordProps)
        new AaaaRecord(ctx.stack, `${id}-AltAAAARecord`, recordProps)
    }
    const accessDomainName = params.domainName || distribution.domainName
    return {
        bucket, distribution,
        accessDomainName
    }
}

export function addBucketUniqueSuffix(to: string) {
    const key = config.getEnvUniqueKey().toLowerCase()
    if (key) {
        return `${to}-${key}`
    } else {
        return to
    }
}

function alphanum(length = 5) {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() *
            charactersLength));
    }
    return result;
}


export const EventBuses = {
    prefixRule(prefix: string) {
        return {prefix} as never as string
    }
}

export function definePythonFunction(stack: Construct, id: string, props: FunctionProps) {
    if (!config.mockPythonFunctions()) {
        return new Function(stack, id, {
            runtime: "python3.11",
            architecture: "x86_64",
            enableLiveDev: false,
            ...props,
        })
    }

    return new Function(stack, id, {
        ...props,
        runtime: 'nodejs18.x',
        handler: 'infra/resources/function-mock-lambda.handler',
        python: undefined,
        container: undefined,
    })
}
