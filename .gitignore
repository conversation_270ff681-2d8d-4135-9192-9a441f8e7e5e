# dependencies
node_modules

# sst
.sst
.build

# opennext
.open-next

# misc
.DS_Store

# Common .env file
.env
# local env files
.env*.local

deployment-configs/*.local.yaml
deployment-configs/*-local.yaml
deployment-configs/oss-frankpnl.yaml

/private

.pnp.*

.idea/

**/.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

venv
__pycache__
dist

*.local.*
.local/

cdk.context.json

# Quasar folder (auto-generated)
.quasar/

start_local.sh

# autogenerated in dev mode
.sst.config.*.mjs
