name: Reusable workflow to get role to assume
on:
 # This workflow is reusable and can be called from other workflows
  workflow_call:
    inputs:
      env_name:
        required: true
        type: string
        description: Environment name (SST stage)
      role_arn:
        required: false
        type: string
        description: Role ARN to assume
    outputs:
      role_arn:
        description: Role ARN to assume
        value: ${{ jobs.shared-assume-role-job.outputs.result }}

  # Triggered by manual dispatch
  # This is used to run the workflow manually from the GitHub UI
  # and to pass inputs to the workflow
  workflow_dispatch:
    inputs:
      env_name:
        required: true
        description: Environment name
      role_arn:
        required: false
        description: Role ARN to assume

jobs:
  shared-assume-role-job:
    name: Prepare assume role
    runs-on: ubuntu-latest
    outputs:
      result: ${{ steps.print-results.outputs.role_arn }}
    steps:
      - name: debug inputs
        run: |
          echo "::debug::env: ${{ inputs.env_name }}"
          echo "::debug::role_arn: ${{ inputs.role_arn }}"
      - uses: actions/checkout@v2
        if: ${{ inputs.role_arn }}
      - name: Configure PNL AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.PNL_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.PNL_AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-northeast-1
      - name: Gets Role ARN
        id: get-role-step
        if: |
          inputs.role_arn == null
        run: |                    
          role=$(aws dynamodb query --table-name lsc-management-table \
                  --key-condition-expression "envName = :name" \
                  --expression-attribute-values  '{":name":{"S":"${{ inputs.env_name }}"}}' \
                  | jq -r '.Items[0].roleArn.S')
          echo "::debug::roleArn: $role"
          echo "key=$role" >> $GITHUB_OUTPUT
      - name: Put assume role item to DB
        id: put-assume-role-item-to-db
        if: ${{ inputs.role_arn }}
        run: |
          id="$(cut -d':' -f 5 <<< ${{ inputs.role_arn }})"
          role="${{ inputs.role_arn }}"
          aws dynamodb update-item \
            --table-name lsc-management-table \
            --key '{ "envName": {"S": "${{ inputs.env_name }}" }}' \
            --update-expression "SET accountId = :a, roleArn = :r" \
            --expression-attribute-values '{":a":{"S":"'"$id"'"}, ":r":{"S":"${{ inputs.role_arn }}"}}' 
          echo "::debug::roleArn: $role"
          echo "key=$role" >> $GITHUB_OUTPUT
      - name: Configure AWS Credentials
        if: ${{ inputs.role_arn }}
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.PNL_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.PNL_AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-northeast-1
          role-to-assume: ${{ inputs.role_arn }}
          role-duration-seconds: 3600
          role-session-name: GitHub-Actions-PNL
      - name: Update Role session duration
        if: ${{ inputs.role_arn }}
        run: |
          role_name=$(echo "${{ inputs.role_arn }}" | sed 's#.*/##')
          aws iam update-role --role-name $role_name --max-session-duration 10800
      - name: Print results
        id: print-results
        run: |
          echo "Role ARN: ${{ steps.get-role-step.outputs.key || steps.put-assume-role-item-to-db.outputs.key }}"
          echo "role_arn=${{ steps.get-role-step.outputs.key || steps.put-assume-role-item-to-db.outputs.key }}" >> $GITHUB_OUTPUT
