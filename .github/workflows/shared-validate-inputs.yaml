name: Reusable workflow for validation
on:
  workflow_call:
    inputs:
      env_name:
        required: true
        type: string
        description: Environment name (SST stage)
env:
  ACTOR_WHITELIST: ${{ secrets.ACTOR_WHITELIST }}
jobs:
  shared-validate-inputs:
    name: Validate
    runs-on: ubuntu-latest
    steps:
      - name: Authorize an actor
        if: ${{ ! contains(env.ACTOR_WHITELIST, github.actor) }}
        run: |
          echo "::group::実行権限エラー"
          echo "::error ::ユーザーはこのワークフローを実行する権限がありません。"
          echo "::endgroup::"
          exit 1
      - name: Validate env name
        run: |
          if [[ ! "${{ inputs.env_name }}" =~ ^[a-z0-9][a-z0-9-]+$ ]]; then
            echo "::group::入力値エラー"
            echo "::error ::環境名の形式が正しくありません。アルファベット小文字・数字・ハイフンのみ利用できます。"
            echo "::endgroup::"
            exit 1
          fi
