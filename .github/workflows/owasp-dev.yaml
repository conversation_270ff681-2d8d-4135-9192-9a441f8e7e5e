name: OWASP ZAP Scan
on:
  workflow_dispatch:
    inputs:
      cmd_options:
        description: 'Additional command lines options for the full scan script'
        required: false
        default: ''
      target:
        description: 'The target URL to scan'
        required: true
        default: 'https://liff-web.dev-oss.line-smartcity.com'
defaults:
  run:
    shell: bash  
jobs:
  zap-scan:
    runs-on: ubuntu-latest
    name: ZAP Scan
    steps:
      - name: <PERSON><PERSON>
        uses: zaproxy/action-full-scan@v0.12.0
        with:
          target: '${{ github.event.inputs.target }}'
          cmd_options: '-d ${{ github.event.inputs.cmd_options }}'