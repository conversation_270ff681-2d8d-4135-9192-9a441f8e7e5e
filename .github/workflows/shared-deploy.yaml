name: Reusable deploy workflow

on:
  workflow_call:
    inputs:
      env_name:
        required: true
        type: string
        description: Environment name (SST stage)
      env_stage:
        required: false
        default: dev
        type: string
        description: Environment stage (dev | stg | prod)
      version:
        required: false
        type: string      
      role_to_assume:
        required: false
        type: string
        description: Role ARN to assume
      additional_env_list:
        required: false
        type: string
        description: Additional environment variables to set
jobs:
  shared-deploy-job:
    name: Deploy
    runs-on: ubuntu-latest
    steps:
      - name: debug inputs
        run: |
          echo "env: ${{ inputs.env_name }}"
          echo "version: ${{ inputs.version }}"
          echo "env_stage: ${{ inputs.env_stage }}"
          echo "role_to_assume: ${{ inputs.role_to_assume }}"
      - name: clean disk space # do clean GHA container extra space
        run: |
          sudo rm -rf /usr/share/dotnet
          sudo rm -rf /opt/ghc
          sudo rm -rf "/usr/local/share/boost"
      - name: Checkout From Ref
        if: ${{ !inputs.version }}
        uses: actions/checkout@v2
        with:
          ref: ${{ github.ref }}
      - name: Checkout From Tag
        if: ${{ inputs.version }}
        uses: actions/checkout@v2
        with:
          ref: ${{ inputs.version }}
      - name: Configure the credential with the inputted ARN # 初回構築時にSecretが反映されないことがあるため、入力値でAssumeRoleを行う
        if: ${{ github.event.inputs.role_arn }}
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.PNL_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.PNL_AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-northeast-1
          role-to-assume: ${{ github.event.inputs.role_arn }}
          role-duration-seconds: 7200
          role-session-name: GitHub-Actions-PNL
      - name: Configure AWS Credentials
        if: ${{ ! github.event.inputs.role_arn }}
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.PNL_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.PNL_AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-northeast-1
          role-to-assume: ${{ inputs.role_to_assume }}
          role-duration-seconds: 7200
          role-session-name: GitHub-Actions-PNL
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 'lts/hydrogen'
      - name: Yarn install
        run: yarn
      - name: Check if environment is deployed
        id: check-state
        run: |
          PARAMETER_NAME="/oss/${{ inputs.env_name }}/isEnvironmentDeployed"
          PARAMETER_VALUE=$(aws ssm get-parameter --name "$PARAMETER_NAME" --query "Parameter.Value" --output text 2>/dev/null || echo "0")
          if [ "$PARAMETER_VALUE" == "1" ]; then
            echo "is-deployed=1" >> $GITHUB_OUTPUT
          else
            echo "is-deployed=0" >> $GITHUB_OUTPUT
          fi
      - name: Debug - Print is-deployed output
        run: echo "is-deployed=${{ steps.check-state.outputs.is-deployed }}"
      - name: Deploy
        run: |
          export NODE_OPTIONS="--max-old-space-size=4096"
          export IS_ENV_UPDATE="${{ steps.check-state.outputs.is-deployed }}"
          ${{ inputs.additional_env_list }} yarn run deploy --stage ${{ inputs.env_name }}
      - name: Second Deploy Task
        if: ${{ steps.check-state.outputs.is-deployed == '0' }}
        run: |
          echo "Running second deploy task for a new environment."
          export NODE_OPTIONS="--max-old-space-size=4096"
          export IS_ENV_UPDATE="0"
          export SKIP_FRONT=true
          ${{ inputs.additional_env_list }} yarn run deploy --stage ${{ inputs.env_name }}

