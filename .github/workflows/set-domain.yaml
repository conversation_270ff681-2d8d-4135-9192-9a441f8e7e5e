name: 'ドメイン名設定　[商用環境]'

on:
  workflow_dispatch:
    inputs:
      env_name:
        description: "Environment name (required)"
        required: true
      role_arn:
        description: 'Wikiに記載の「Role ARN」を入力 (初期構築の場合は必須)'
        required: false
      root_domain:
        description: "Root domain (default gov.line-smartcity.com)"
        required: false
        default: "gov.line-smartcity.com"

jobs:
  assume-role:
    uses: ./.github/workflows/shared-assume-role.yaml
    secrets: inherit
    with:
      env_name: ${{ github.event.inputs.env_name }}
      role_arn: ${{ github.event.inputs.role_arn }}

  validate-inputs:
    uses: ./.github/workflows/shared-validate-inputs.yaml
    secrets: inherit
    with:
      env_name: ${{ github.event.inputs.env_name }}

  set-parameters:
    runs-on: ubuntu-latest
    needs:
      - validate-inputs
      - assume-role
    steps:
      # Step 0: Print parameters
      - name: Print parameters
        run: |
          echo "assume role arn: ${{ needs.assume-role.outputs.role_arn || github.event.inputs.role_arn }}"
          echo "registred domain: "${{ inputs.env_name }}.${{ github.event.inputs.root_domain }}""
      # Step 1: Configure AWS Credentials
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.PNL_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.PNL_AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-northeast-1
          role-to-assume: ${{ needs.assume-role.outputs.role_arn || github.event.inputs.role_arn }}
          role-duration-seconds: 7200
          role-session-name: GitHub-Actions-PNL

      # Step 2: Checkout the repository
      - name: Checkout code
        uses: actions/checkout@v3

      # Step 3: Set up Node.js
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'

      # Step 4: Install dependencies
      - name: Install dependencies
        run: yarn install
        working-directory: scripts

      # Step 5: Run the secrets update script
      - name: Run secrets-update script
        env:
          ENV_NAME: ${{ github.event.inputs.env_name }}
          BASE_DOMAIN: "${{ inputs.env_name }}.${{ github.event.inputs.root_domain }}"
        run: yarn ts-node secrets-update.ts
        working-directory: scripts

  register_domain:
    uses: ./.github/workflows/shared-route53.yaml
    secrets: inherit
    needs:
      - validate-inputs
      - assume-role
    with:
      env_name: ${{ github.event.inputs.env_name }}
      setup_domain: true
      root_domain: ${{ github.event.inputs.root_domain }}
      role_to_assume: ${{ needs.assume-role.outputs.role_arn }}