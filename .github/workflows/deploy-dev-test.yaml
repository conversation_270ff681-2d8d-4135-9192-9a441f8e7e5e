on:
  workflow_dispatch:
permissions:
  id-token: write
  contents: read
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          role-to-assume: arn:aws:iam::868600483802:role/github-actions-oidc-role
          aws-region: ap-northeast-1
      - name: Checkout
        uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 16.20.2
      - run: node -v
      - name: Install packages for web-shared
        run: cd packages/web-shared && yarn
      - name: Install packages for web-admin
        run: cd packages/web-admin && yarn && yarn build
      - name: Install packages for web-reception
        run: cd packages/web-reception && yarn && yarn build
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18.6.0
      - name: Yarn install
        run: node -v && yarn
