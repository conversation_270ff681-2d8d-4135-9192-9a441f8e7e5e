name: Reusable workflow to find latest tag
on:
  workflow_call:
    inputs:
      regex:
        required: false
        default: '^v\d{1,2}\.\d{1,2}\.\d{1,2}$'
        type: string
        description: Regex pattern to match a tag
    outputs:
      version:
        value: ${{ jobs.shared-find-version.outputs.version }}
jobs:
  shared-find-version:
    name: Find latest tag and set the version
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.set_version.outputs.version }}
    steps:
      - name: Find tag
        id: latest_tag
        uses: oprypin/find-latest-tag@v1
        with:
          repository: playnext-lab/line-oss
          regex: ${{ inputs.regex }}
      - name: Set version to the latest or to the inputted
        id: set_version
        run: |
          if [[ "${{ github.event.inputs.version }}" == "latest" ]]; then
            echo "version=${{ steps.latest_tag.outputs.tag }}" >> $GITHUB_OUTPUT
          elif [[ -z "${{ github.event.inputs.version }}" || "${{ github.event.inputs.version }}" == "" ]]; then
            echo "version=${{ github.ref_name }}" >> $GITHUB_OUTPUT
          else
            echo "version=${{ github.event.inputs.version }}" >> $GITHUB_OUTPUT
          fi
      - run: echo "::debug::${{ steps.set_version.outputs.version }}"