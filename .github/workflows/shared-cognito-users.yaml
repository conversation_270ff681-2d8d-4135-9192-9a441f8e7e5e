name: Reusable Cognito users workflow for the admin setting
on:
  workflow_call:
    inputs:
      env_name:
        required: true
        type: string
        description: Environment name
      role_to_assume:
        required: false
        type: string
        description: Role ARN to assume
env:
  PROD_USERNAME: pnl-maintenance
jobs:
  shared-cognito-users-job-prod:
    name: Create Cognito users for production
    runs-on: ubuntu-latest
    steps:
      - name: debug inputs
        run: |
          echo "::debug::env: ${{ inputs.env_name }}"
          echo "::debug::role_to_assume: ${{ inputs.role_to_assume }}"
      - name: Configure the credential with the inputted ARN # 初回構築時にSecretが反映されないことがあるため、入力値でAssumeRoleを行う
        if: ${{ github.event.inputs.role_arn }}
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.PNL_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.PNL_AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-northeast-1
          role-to-assume: ${{ github.event.inputs.role_arn }}
          role-duration-seconds: 900
      - name: Configure AWS Credentials
        if: ${{ ! github.event.inputs.role_arn }}
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.PNL_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.PNL_AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-northeast-1
          role-to-assume: ${{ inputs.role_to_assume }}
          role-duration-seconds: 900
    
      - name: Add initial admin user
        run: |
          UserPoolId=$(aws ssm get-parameter --name "/oss/${{ inputs.env_name }}/Platform/AdminsPoolId" --with-decryption --query "Parameter.Value" --output text)
          echo "::debug::UserPoolId: $UserPoolId"
          if [ -z "$UserPoolId" ]; then
            echo "Error: UserPoolId is not found. Exiting with code 1."
            exit 1
          fi

          ExistingUser=$(aws cognito-idp list-users --user-pool-id $UserPoolId --filter "username=\"${{ env.PROD_USERNAME }}\"" --query "Users[?Username=='${{ env.PROD_USERNAME }}']" --output text)
          if [ -n "$ExistingUser" ]; then
            echo "User ${{ env.PROD_USERNAME }} already exists in the user pool. Exiting with code 0."
            echo "::debug::User ${{ env.PROD_USERNAME }} is already created."
            exit 0
          fi
          
          aws cognito-idp update-user-pool --admin-create-user-config InviteMessageTemplate='{EmailSubject="[${{ inputs.env_name }}] OSS管理画面の初期パスワード", EmailMessage="Your username is {username} and temporary password is {####}.  https://operator-web.${{ inputs.env_name }}.gov.line-smartcity.com/signin"}' --user-pool-id $UserPoolId
          aws cognito-idp admin-create-user --username ${{ env.PROD_USERNAME }} \
            --user-attributes Name=email,Value=${{ secrets.OSS_MAINTENANCE_USER_EMAIL }} Name=email_verified,Value=true \
            --user-pool-id $UserPoolId \
            --temporary-password ${{ secrets.OSS_TEST_USER_TMP_PASS }};

          aws cognito-idp admin-add-user-to-group --username ${{ env.PROD_USERNAME }} \
          --group-name Administrator:admins --user-pool-id $UserPoolId
          echo "::debug::User creation status: ${{ env.PROD_USERNAME }} created successfully."