on:
  push:
    paths-ignore:
      - '.gitignore'
      - '.vscode/**'
      - '**.md'
      - '**.http'
      - '**.test.ts'
      - '**.spec.ts'
    branches:
      - staging
permissions:
  id-token: write
  contents: read
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          role-to-assume: arn:aws:iam::590183665435:role/github-actions-oidc-role
          aws-region: ap-northeast-1
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 'lts/hydrogen'
      - name: Checkout
        uses: actions/checkout@v3
      - name: Yarn install
        run: yarn
      - name: Install packages for web-admin
        run: cd packages/web-admin && yarn
      - name: Install packages for web-reception
        run: cd packages/web-reception && yarn
      - name: Install packages for web-shared
        run: cd packages/web-shared && yarn
      - name: Deploy
        run: |
          export NODE_OPTIONS="--max-old-space-size=4096"
          yarn run deploy --stage stg
