name: Reusable update management db workflow
on:
  workflow_call:
    inputs:
      env_name:
        required: true
        type: string
        description: Environment name
      version:
        required: false
        type: string
        description: Version deployed
      target_module:
        required: false
        type: string
        default: all
        description: Module name deployed
  workflow_dispatch:
    inputs:
      env_name:
        required: true
        description: Environment name
      version:
        required: false
        type: string
        description: Version deployed
      target_module:
        required: false
        type: string
        default: all
        description: Module name deployed
jobs:
  shared-upadte-db:
    name: Update Management DB 
    runs-on: ubuntu-latest
    steps:
      - name: debug inputs
        run: |
          echo "::debug::env: ${{ inputs.env_name }}"
          echo "::debug::version: ${{ inputs.version }}"
          echo "::debug::target_module: ${{ inputs.target_module }}"
      - name: Checkout From Ref
        if: ${{ !inputs.version }}
        uses: actions/checkout@v2
        with:
          ref: ${{ github.ref }}
      - name: Checkout From Tag
        if: ${{ inputs.version }}
        uses: actions/checkout@v2
        with:
          ref: ${{ inputs.version }}
      - name: Get Git SHA
        run: |
          sha="$(git rev-parse HEAD)"
          echo "GIT_SHA=$sha" >> "$GITHUB_ENV"
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.PNL_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.PNL_AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-northeast-1
      - name: Update Management DB
        env:
          TARGET_MODULE: ${{ inputs.target_module }}
        run: |
          # all is a reserved word in DynamoDB
          if [ "$TARGET_MODULE" = "all" ]; then
            module="allModules"
          else
            module="$TARGET_MODULE"
          fi
          deployed_at="$(TZ='Asia/Tokyo' date +'%Y-%m-%dT%H:%M:%S')"

          set +e
          # Check if `oss` map exists
          aws dynamodb query --table-name lsc-management-table \
            --key-condition-expression "envName = :name" \
            --expression-attribute-values  '{":name":{"S":"${{ inputs.env_name }}"}}' \
            | jq -e '.Items[0].oss'
          
          # oss exists
          if [ $? -eq 0 ]; then
            update_expression="SET oss.$module = :l"
            attribute_values='
            {
              ":l":
                         {
                           "M":
                             {
                               "gitRef":{"S":"${{ inputs.version }}"},
                               "gitSha":{"S":"${{ env.GIT_SHA }}"},
                               "deployedAt":{"S":"'"$deployed_at"'"}
                             }
                         }
            }'
          # oss doesn't exist
          else
            update_expression="SET oss = :l"
            attribute_values='
              {
                ":l":
                   {
                     "M":
                       { 
                         "'"$module"'": 
                           {
                             "M":
                               {
                                 "gitRef":{"S":"${{ inputs.version }}"},
                                 "gitSha":{"S":"${{ env.GIT_SHA }}"},
                                 "deployedAt":{"S":"'"$deployed_at"'"}
                               }
                           }
                       }
                   }
              }'
          fi
          set -e
          aws dynamodb update-item \
              --table-name lsc-management-table \
              --key '{ "envName": {"S": "${{ inputs.env_name }}"}}' \
              --update-expression "$update_expression" \
              --expression-attribute-values "$attribute_values"
